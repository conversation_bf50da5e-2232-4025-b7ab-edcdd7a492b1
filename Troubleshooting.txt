


Set Permissions on Ethereum Sepolia Contracts:

Using forge script ./script/Deployer.s.sol:<ScriptName> --rpc-url 
$SEPOLIA_RPC_URL --account updraft --broadcast --sig 
"<FUNCTION_SIGNATURE>(<ARGS>)" <ARG_VALUES>:

    * This step involves calling specific functions within a permission-setting script (e.g., SetPermissions in Deployer.s.sol) 
      to grant roles and set CCIP admin configurations. Details on potential script modifications for this step are 
      covered in the "Troubleshooting Deployment" section.





Troubleshooting Deployment: Handling Errors and Script Refinements

Testnet deployments can sometimes encounter issues due to network congestion, gas price fluctuations, or nonce 
management complexities.

Error Encountered: "future transaction tries to replace pending"
During an initial execution of the Bash script, you might encounter an error like Context: server returned an error 
response: error code -32000: future transaction tries to replace pending. This typically occurs when deploying to Sepolia 
using forge script.

Reasoning:
This error often points to nonce management issues. When a Foundry script executes multiple transactions within a 
single vm.startBroadcast() / vm.stopBroadcast() block, or if multiple script executions happen in rapid succession, the underlying transaction nonces might conflict, especially on a busy testnet like Sepolia. High gas fees or general network congestion can exacerbate this.

Solution: Splitting Script Logic for Robustness:
To mitigate nonce-related issues on Sepolia, it's advisable to break down complex Foundry scripts that perform multiple 
state-changing operations into smaller, more granular script calls.

  1.Refactor Solidity Script (Deployer.s.sol):
    Instead of having a single function in your SetPermissions script contract that performs multiple permissioning calls, 
    split it into distinct public functions. For example:

```solidity

// In script/Deployer.s.sol
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;
​
import "forge-std/Script.sol";
import {IRelayToken} from "../src/token/RelayToken.sol"; // Adjust path as needed
// ... other necessary imports ...
​
contract SetPermissions is Script {
    // ... (Helper functions to get network details, private key, etc.) ...
​
    function grantRole(address token, address pool) public {
        // ... (Get network details for Sepolia) ...
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY"); // Or use keystore logic
        vm.startBroadcast(deployerPrivateKey);
​
        IRelayToken(token).grantMintAndBurnRole(address(pool));
​
        vm.stopBroadcast();
    }
​
    function setAdminAndPool(address token, address pool) public { // Renamed for clarity
        // ... (Get network details for Sepolia) ...
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY"); // Or use keystore logic
        vm.startBroadcast(deployerPrivateKey);
​
        // Example: Replace with your actual CCIP admin and pool setting logic
        // IRegistryModuleOwnerCustom(REGISTRY_MODULE_OWNER_CUSTOM_ADDRESS).registerAdminViaOwner(TOKEN_ADMIN_REGISTRY_ADDRESS);
        // ITokenAdminRegistry(TOKEN_ADMIN_REGISTRY_ADDRESS).acceptAdminRole();
        // ITokenAdminRegistry(TOKEN_ADMIN_REGISTRY_ADDRESS).setPool(token, pool);
​
        vm.stopBroadcast();
    }
}
```

2.Modify Bash Script (bridgeToZksync.sh):
 Update the Bash script to call these new, specific functions individually using the --sig flag in forge script. 
 This ensures each logical permissioning step is a separate on-chain transaction, reducing the chance of nonce conflicts.

```bash
# Example modification for Sepolia permission setting in bridgeToZksync.sh
# (Assuming SEPOLIA_REBASE_TOKEN_ADDRESS and SEPOLIA_POOL_ADDRESS are set)
​
echo "Granting MintAndBurn role on Sepolia RebaseToken to Pool..."
forge script ./script/Deployer.s.sol:SetPermissions \
    --rpc-url $SEPOLIA_RPC_URL \
    --account updraft \
    --broadcast \
    --sig "grantRole(address,address)" \
    $SEPOLIA_REBASE_TOKEN_ADDRESS $SEPOLIA_POOL_ADDRESS
​
echo "Setting Admin and Pool on Sepolia TokenAdminRegistry..."
forge script ./script/Deployer.s.sol:SetPermissions \
    --rpc-url $SEPOLIA_RPC_URL \
    --account updraft \
    --broadcast \
    --sig "setAdminAndPool(address,address)" \
    $SEPOLIA_REBASE_TOKEN_ADDRESS $SEPOLIA_POOL_ADDRESS
```
This approach provides more control and makes troubleshooting easier if one specific step fails.