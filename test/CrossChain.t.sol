// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Test, console} from "forge-std/Test.sol";
import {CCIPLocalSimulatorFork, Register} from "@chainlink-local/src/ccip/CCIPLocalSimulatorFork.sol";
import {IERC20} from
    "@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol";
import {IERC20 as OpenZeppelinIERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import {RegistryModuleOwnerCustom} from
    "lib/ccip/contracts/src/v0.8/ccip/tokenAdminRegistry/RegistryModuleOwnerCustom.sol";
import {TokenAdminRegistry} from "lib/ccip/contracts/src/v0.8/ccip/tokenAdminRegistry/TokenAdminRegistry.sol";
import {TokenPool} from "lib/chainlink-ccip/chains/evm/contracts/pools/TokenPool.sol";
import {RateLimiter} from "lib/chainlink-ccip/chains/evm/contracts/libraries/RateLimiter.sol";
import {Client} from "lib/chainlink-ccip/chains/evm/contracts/libraries/Client.sol";
import {IRouterClient} from "lib/chainlink-ccip/chains/evm/contracts/interfaces/IRouterClient.sol";

import {RebaseToken} from "../src/RebaseToken.sol";
import {RebaseTokenPool} from "../src/RebaseTokenPool.sol";
import {Vault} from "../src/Vault.sol";
import {IRebaseToken} from "../src/Interfaces/IRebaseToken.sol";

contract CrossChainTest is Test {
    address owner = makeAddr("owner");
    address user = makeAddr("user");
    uint256 SEND_VALUE = 1e5;

    // Helper function to convert OpenZeppelin IERC20 to Chainlink IERC20
    // What the function does:
    // 1. Takes an address parameter representing a token contract
    // 2. Casts the address to Chainlink's IERC20 interface type
    // 3. Returns the properly typed IERC20 interface for use with TokenPool
    // 4. Solves type compatibility issues between different IERC20 implementations
    // 5. Enables seamless interaction between RebaseToken and RebaseTokenPool
    function toChainlinkIERC20(address tokenAddress) internal pure returns (IERC20) {
        return IERC20(tokenAddress);
    }

    uint256 sepoliaFork;
    uint256 arbSepoliaFork;

    CCIPLocalSimulatorFork ccipLocalSimulatorFork;

    // Store the contract in storage
    RebaseToken sepoliaToken;
    RebaseToken arbSepoliaToken;

    Vault vault;

    RebaseTokenPool sepoliaPool;
    RebaseTokenPool arbSepoliaPool;
    // Register.NetworkDetails - this ia a type
    Register.NetworkDetails sepoliaNetworkDetails; // Empty struct, waiting for data
    Register.NetworkDetails arbSepoliaNetworkDetails; // Empty struct, waiting for data

    // Test setup function that initializes the cross-chain testing environment
    // What the function does:
    // 1. Create and configure two blockchain forks (Sepolia and Arbitrum Sepolia)
    // 2. Deploy CCIP Local Simulator for cross-chain message simulation
    // 3. Make simulator persistent across both forks for shared state
    // 4. Deploy RebaseToken and RebaseTokenPool contracts on both chains
    // 5. Deploy Vault contract on Sepolia for token management
    // 6. Grant mint/burn permissions to pools and vault contracts
    // 7. Register token admin roles in CCIP token registry on both chains
    // 8. Link tokens to their respective pools in the admin registry
    // 9. Configure cross-chain routing between the two token pools
    // 10. Set up bidirectional token pool configurations for seamless bridging
    function setUp() public {
        address[] memory allowlist = new address[](0);
        // 1. Create and select the initial (source) fork (Sepolia)
        // This uses the "sepolia" alias defined in foundry.toml
        sepoliaFork = vm.createSelectFork("sepolia-eth"); // doc

        // 2. Create the destination fork (Arbitrum Sepolia) but don't select it yet
        // This uses the "arb-sepolia" alias defined in foundry.toml
        arbSepoliaFork = vm.createFork("arb-sepolia"); // doc

        // 3. Deploy the CCIP Local Simulator contract
        ccipLocalSimulatorFork = new CCIPLocalSimulatorFork();

        // 4. Make the simulator's address persistent across all active forks
        vm.makePersistent(address(ccipLocalSimulatorFork)); // doc

        // 5.Retrieve and set up the network details for the chains
        // This includes (such as chain selector, router address, link address, wrapped native address, or CCIP test tokens)
        sepoliaNetworkDetails = ccipLocalSimulatorFork.getNetworkDetails(block.chainid);
        vm.startPrank(owner);

        sepoliaToken = new RebaseToken();
        // we need to do intermediate casting to an address before casting to IRebaseToken
        vault = new Vault(IRebaseToken(address(sepoliaToken)));

        sepoliaPool = new RebaseTokenPool(
            toChainlinkIERC20(address(sepoliaToken)), // Convert to chainlink IERC20
            18, // decimals for rebase token
            allowlist,
            sepoliaNetworkDetails.rmnProxyAddress,
            sepoliaNetworkDetails.routerAddress
        );

        // Set pool on the token contract for permissions on Sepolia
        sepoliaToken.grantMintAndBurnRole(address(sepoliaPool));
        sepoliaToken.grantMintAndBurnRole(address(vault));
        // Claim role on Sepolia
        RegistryModuleOwnerCustom(sepoliaNetworkDetails.registryModuleOwnerCustomAddress).registerAdminViaOwner(
            address(sepoliaToken)
        );

        // Accept role on Sepolia
        TokenAdminRegistry(sepoliaNetworkDetails.tokenAdminRegistryAddress).acceptAdminRole(address(sepoliaToken));
        // Link token to pool in the token admin registry on Sepolia
        // This links our RebaseToken instances to their corresponding RebaseTokenPool instances on both the Sepolia
        // and Arbitrum Sepolia forks.
        TokenAdminRegistry(sepoliaNetworkDetails.tokenAdminRegistryAddress).setPool(
            address(sepoliaToken), address(sepoliaPool)
        );

        vm.stopPrank();

        // 2. Deploy and configure on Arbitrum Sepolia
        // We must switch the active EVM context to the Arbitrum Sepolia fork we created earlier
        vm.selectFork(arbSepoliaFork); // doc
        arbSepoliaNetworkDetails = ccipLocalSimulatorFork.getNetworkDetails(block.chainid);
        vm.startPrank(owner); // Impersonate owner for deployment on Arbitrum Sepolia
        arbSepoliaToken = new RebaseToken();
        // Deploy RebaseToken on Arbitrum Sepolia
        arbSepoliaPool = new RebaseTokenPool(
            toChainlinkIERC20(address(arbSepoliaToken)), // Convert to chainlink IERC20
            18, // decimals for rebase token
            allowlist,
            arbSepoliaNetworkDetails.rmnProxyAddress,
            arbSepoliaNetworkDetails.routerAddress
        );

        // Set pool on the token contract for permissions on Arbitrum
        arbSepoliaToken.grantMintAndBurnRole(address(arbSepoliaPool));
        // Claim role on Arbitrum
        RegistryModuleOwnerCustom(arbSepoliaNetworkDetails.registryModuleOwnerCustomAddress).registerAdminViaOwner(
            address(arbSepoliaToken)
        );
        // Accept role on Arbitrum
        TokenAdminRegistry(arbSepoliaNetworkDetails.tokenAdminRegistryAddress).acceptAdminRole(address(arbSepoliaToken));
        // Link token to pool in the token admin registry on Arbitrum
        TokenAdminRegistry(arbSepoliaNetworkDetails.tokenAdminRegistryAddress).setPool(
            address(arbSepoliaToken), address(arbSepoliaPool)
        );
        vm.stopPrank();

        configureTokenPool(
            sepoliaFork,
            TokenPool(address(sepoliaPool)),
            TokenPool(address(arbSepoliaPool)),
            IRebaseToken(address(arbSepoliaToken)),
            arbSepoliaNetworkDetails
        );
        configureTokenPool(
            arbSepoliaFork,
            TokenPool(address(arbSepoliaPool)),
            TokenPool(address(sepoliaPool)),
            IRebaseToken(address(sepoliaToken)),
            sepoliaNetworkDetails
        );
    }

    // This function will encapsulate the logic for calling applyChainUpdates on a specified local pool.
    // Configures a token pool for cross-chain operations with a remote chain
    // What the function does:
    // 1. Switch to the specified fork to execute configuration
    // 2. Create array of remote pool addresses for the target chain
    // 3. Encode the remote token address for cross-chain identification
    // 4. Set up rate limiter configurations (disabled for testing)
    // 5. Create ChainUpdate struct with all remote chain parameters
    // 6. Apply chain updates to establish cross-chain routing rules
    // 7. Enable the local pool to communicate with the remote pool
    // 8. Configure inbound and outbound token transfer parameters
    function configureTokenPool(
        uint256 fork,
        TokenPool localPool,
        TokenPool remotePool,
        IRebaseToken remoteToken,
        Register.NetworkDetails memory remoteNetworkDetails
    ) public {
        // The first thing that we need to do is make sure that we are on the correct fork.
        vm.selectFork(fork);
        vm.startPrank(owner);
        bytes[] memory remotePoolAddresses = new bytes[](1);
        remotePoolAddresses[0] = abi.encode(address(remotePool));

        TokenPool.ChainUpdate[] memory chainsToAdd = new TokenPool.ChainUpdate[](1);
        chainsToAdd[0] = TokenPool.ChainUpdate({
            remoteChainSelector: remoteNetworkDetails.chainSelector,
            remotePoolAddresses: remotePoolAddresses,
            remoteTokenAddress: abi.encode(address(remoteToken)),
            outboundRateLimiterConfig: RateLimiter.Config({isEnabled: false, capacity: 0, rate: 0}),
            inboundRateLimiterConfig: RateLimiter.Config({isEnabled: false, capacity: 0, rate: 0})
        });

        uint64[] memory remoteChainSelectorsToRemove = new uint64[](0);
        // To able to call applyChainUpdates, we need to make this sure this ia of type TokenPool.
        TokenPool(localPool).applyChainUpdates(remoteChainSelectorsToRemove, chainsToAdd);
        vm.stopPrank();
    }

    // Main function to execute and verify cross-chain token bridging
    // What the function does:
    // 1. Execute the bridge transaction on the source chain
    // 2. Capture the user's interest rate before bridging
    // 3. Verify successful token burning on source chain
    // 4. Switch to destination chain and process the cross-chain message
    // 5. Verify token minting on destination chain with correct amount
    // 6. Confirm that user's interest rate is preserved across chains
    // 7. Validate the complete cross-chain rebase token transfer flow
    function bridgeTokens(
        uint256 amountToBridge,
        uint256 localFork, // Source chain fork ID
        uint256 remoteFork, // Destination chain fork ID
        Register.NetworkDetails memory localNetworkDetails, // Struct with source chain info
        Register.NetworkDetails memory remoteNetworkDetails, // Struct with dest. chain info
        RebaseToken localToken, // Source token contract instance
        RebaseToken remoteToken // Destination token contract instance
    ) public {
        // Execute the bridge on source chain
        _executeBridgeOnSourceChain(amountToBridge, localFork, localNetworkDetails, remoteNetworkDetails, localToken);

        // Verify the bridge on destination chain
        _verifyBridgeOnDestinationChain(amountToBridge, localFork, remoteFork, localToken, remoteToken);
    }

    // Executes the source chain portion of the cross-chain bridge operation
    // What the function does:
    // 1. Switch to the source chain fork context
    // 2. Create CCIP token amount array with the bridge amount
    // 3. Construct EVM2AnyMessage with user address and token data
    // 4. Calculate CCIP fees for the cross-chain transaction
    // 5. Request LINK tokens from faucet to pay for fees
    // 6. Approve LINK tokens for router to pay transaction fees
    // 7. Approve rebase tokens for router to execute the bridge
    // 8. Record user's token balance before the bridge operation
    // 9. Execute the CCIP send transaction to initiate cross-chain transfer
    // 10. Verify that tokens were properly deducted from user's balance
    // 11. Return the user's current interest rate for verification on destination
    function _executeBridgeOnSourceChain(
        uint256 amountToBridge,
        uint256 localFork,
        Register.NetworkDetails memory localNetworkDetails,
        Register.NetworkDetails memory remoteNetworkDetails,
        RebaseToken localToken
    ) internal {
        // Switches Foundry's execution context to the source chain.
        vm.selectFork(localFork);

        // Create token amounts array
        Client.EVMTokenAmount[] memory tokenAmounts = new Client.EVMTokenAmount[](1);
        tokenAmounts[0] = Client.EVMTokenAmount({token: address(localToken), amount: amountToBridge});

        // Create CCIP message
        Client.EVM2AnyMessage memory message = Client.EVM2AnyMessage({
            receiver: abi.encode(address(user)),
            data: "",
            tokenAmounts: tokenAmounts,
            feeToken: localNetworkDetails.linkAddress,
            extraArgs: Client._argsToBytes(Client.GenericExtraArgsV2({gasLimit: 500_000, allowOutOfOrderExecution: false})) // Increase gas limit for RebaseToken mint
        });

        // Get fee and fund user
        uint256 fee =
            IRouterClient(localNetworkDetails.routerAddress).getFee(remoteNetworkDetails.chainSelector, message);
        // Mints LINK tokens to a user for fee payments
        ccipLocalSimulatorFork.requestLinkFromFaucet(user, fee);

        // Approve tokens and fees
        vm.prank(user);
        IERC20(localNetworkDetails.linkAddress).approve(localNetworkDetails.routerAddress, fee);
        vm.prank(user);
        IERC20(address(localToken)).approve(localNetworkDetails.routerAddress, amountToBridge);

        // Check balance before and execute bridge
        uint256 balanceBefore = IERC20(address(localToken)).balanceOf(user);
        console.log("Local balance before bridge: %d", balanceBefore);

        vm.prank(user);
        IRouterClient(localNetworkDetails.routerAddress).ccipSend(remoteNetworkDetails.chainSelector, message);

        uint256 sourceBalanceAfterBridge = IERC20(address(localToken)).balanceOf(user);
        console.log("Local balance after bridge: %d", sourceBalanceAfterBridge);

        // Verify balance change
        assertEq(sourceBalanceAfterBridge, balanceBefore - amountToBridge, "Local balance incorrect after send");
    }

    // Verifies the destination chain portion of the cross-chain bridge operation
    // What the function does:
    // 1. Switch to the destination chain fork context
    // 2. Fast-forward time by 20 minutes to simulate realistic cross-chain delay
    // 3. Record user's token balance before message processing
    // 4. Trigger the CCIP message processing on the destination chain
    // 5. Verify that the correct amount of tokens were minted to the user
    // 6. Confirm that the user's interest rate matches the source chain rate
    // 7. Validate that the rebase token's cross-chain state preservation works correctly
    function _verifyBridgeOnDestinationChain(
        uint256 amountToBridge,
        uint256 localFork,
        uint256 remoteFork,
        RebaseToken localToken,
        RebaseToken remoteToken
    ) internal {
        uint256 expectedInterestRate = localToken.getInterestRate();

        vm.selectFork(remoteFork);
        // Pretend it takes 20 minutes to bridge the tokens
        // fast-forward block.timestamp, mimicking network latency and finalization times
        vm.warp(block.timestamp + 20 minutes); // Fast-forward time

        // get initial balance on Arbitrum
        uint256 balanceBefore = IERC20(address(remoteToken)).balanceOf(user);
        console.log("Remote balance before bridge: %d", balanceBefore);
        vm.selectFork(localFork); // in the latest version of chainlink-local, it assumes you are currently on the local fork before calling switchChainAndRouteMessage
        // Simulates message routing and execution on the target forkId, including handling vm.selectFork
        ccipLocalSimulatorFork.switchChainAndRouteMessage(remoteFork);

        console.log("Remote user interest rate: %d", remoteToken.getUserInterestRate(user));
        uint256 destBalance = IERC20(address(remoteToken)).balanceOf(user);
        console.log("Remote balance after bridge: %d", destBalance);

        // Verify balance increase and interest rate preservation
        assertEq(destBalance, balanceBefore + amountToBridge, "Remote balance incorrect after receive");
        assertEq(remoteToken.getUserInterestRate(user), expectedInterestRate, "Interest rate incorrect after receive");
    }

    function testBridgeAllTokens() public {
        // 1. Deposit into Vault on Sepolia
        vm.selectFork(sepoliaFork);
        vm.deal(user, SEND_VALUE);
        vm.prank(user);
        // To send ETH (msg.value) with a contract call in Foundry:
        // Cast contract instance to address, then to payable, then back to contract type.
        Vault(payable(address(vault))).deposit{value: SEND_VALUE}(); // doc
        vm.stopPrank();
        assertEq(sepoliaToken.balanceOf(user), SEND_VALUE, "User Sepolia token balance after deposit incorrect");

        // 2. Bridge Tokens: Sepolia -> Arbitrum Sepolia
        bridgeTokens(
            SEND_VALUE,
            sepoliaFork,
            arbSepoliaFork,
            sepoliaNetworkDetails,
            arbSepoliaNetworkDetails,
            sepoliaToken,
            arbSepoliaToken
        );
        // Assertions for this step are within bridgeTokens

        // 3. Bridge All Tokens Back: Arbitrum Sepolia -> Sepolia
        vm.selectFork(arbSepoliaFork);
        vm.warp(block.timestamp + 20 minutes); // Advance time on Arbitrum Sepolia before bridging back
        bridgeTokens(
            arbSepoliaToken.balanceOf(user),
            arbSepoliaFork,
            sepoliaFork,
            arbSepoliaNetworkDetails,
            sepoliaNetworkDetails,
            arbSepoliaToken,
            sepoliaToken
        );

        // Final state check: User on Sepolia should have their initial deposit back
        // (minus any very small precision differences if applicable to tokenomics, or fees not covered by faucet)
        // Note: Exact final balance might depend on tokenomics if any fees were burnt from principal.
    }
}

// Consider extending your test suite with scenarios like:
//     Bridging partial amounts of tokens.
//     Multiple consecutive bridge operations in both directions.
//     Testing failure cases (e.g., insufficient fees, invalid receiver).
//     Interactions with different users.
