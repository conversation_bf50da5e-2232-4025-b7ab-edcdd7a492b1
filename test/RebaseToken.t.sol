// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {Test, console} from "forge-std/Test.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {IAccessControl} from "@openzeppelin/contracts/access/AccessControl.sol";
import {RebaseToken} from "../src/RebaseToken.sol";
import {Vault} from "../src/Vault.sol";
import {IRebaseToken} from "../src/Interfaces/IRebaseToken.sol";

contract RebaseTokenTest is Test {
    RebaseToken public rebaseToken;
    Vault public vault;

    address public owner = makeAddr("owner");
    address public user = makeAddr("user");
    uint256 public SEND_VALUE = 1e5;

    function setUp() public {
        vm.startPrank(owner);
        rebaseToken = new RebaseToken();
        vault = new Vault(IRebaseToken(address(rebaseToken)));
        rebaseToken.grantMintAndBurnRole(address(vault));
        // (bool success,) = payable(address(vault)).call{value: 1e18}(""); // deposit 1 eth
        vm.stopPrank();
    }
    
    // helper function to add rewards to the vault
    function addRewardsToVault(uint256 rewardAmount) public {
        (bool success,) = payable(address(vault)).call{value: rewardAmount}("");
        vm.assume(success);
    }

    ////////////////////////////////////////////////////////////////////////////////////////
    // Deposit
    ////////////////////////////////////////////////////////////////////////////////////////

    function testDepositLinear(uint256 amount) public {
        // vm.assume(amount > 1e5); // alternate way to assume
        amount = bound(amount, 1e5, type(uint96).max);
        // 1. deposit
        vm.startPrank(user);
        vm.deal(user, amount);
        vault.deposit{value: amount}();
        // 2. check our rebase token balance
        uint256 startBalance = rebaseToken.balanceOf(user);
        console.log("startBalance", startBalance);
        assertEq(startBalance, amount);
        // 3. warp the time and check the balance again
        vm.warp(block.timestamp + 1 hours);
        uint256 middleBalance = rebaseToken.balanceOf(user);
        assertGt(middleBalance, startBalance);
        // 4. warp the time again by the same amount and check the balance again
        vm.warp(block.timestamp + 1 hours);
        uint256 endBalance = rebaseToken.balanceOf(user);
        assertGt(endBalance, middleBalance);
        // 5. check that the rebase token balance is linear
        assertApproxEqAbs(endBalance - middleBalance, middleBalance - startBalance, 1);
        // assertEq(endBalance - middleBalance, middleBalance - startBalance);

        // Option: Log the values to understand the difference
        console.log("First period growth:", middleBalance - startBalance);
        console.log("Second period growth:", endBalance - middleBalance);
        console.log("Difference:", (endBalance - middleBalance) - (middleBalance - startBalance));
        console.log("Current interest rate:", rebaseToken.getInterestRate());
        console.log("Current interest rate in percentage:", rebaseToken.getInterestRate() / 1e18);

        vm.stopPrank();
    }

    ////////////////////////////////////////////////////////////////////////////////////////
    // Redeem
    ////////////////////////////////////////////////////////////////////////////////////////
    function testCannotRedeemZero() public {
        vm.expectRevert(Vault.Vault__RedeemAmountMustBeGreaterThanZero.selector);
        vm.prank(user);
        vault.redeem(0);
    }

    function testRedeemStraightAway(uint256 amount) public {
        amount = bound(amount, 1e5, type(uint96).max);
        // 1.deposit
        vm.startPrank(user);
        vm.deal(user, amount);
        vault.deposit{value: amount}();
        assertEq(rebaseToken.balanceOf(user), amount);
        // 2. redeem
        vault.redeem(type(uint256).max);
        uint256 balance = rebaseToken.balanceOf(user);
        console.log("User balance: %d", balance);
        assertEq(balance, 0);
        vm.stopPrank();
    }

    function testRedeemAfterTimeHasPassed(uint256 depositAmount, uint256 time) public {
        time = bound(time, 1000 seconds, type(uint32).max); // maximum time of 2.5 * 10^21 years!!!
        depositAmount = bound(depositAmount, 1e5, type(uint96).max); // this is an Ether value of max 2^78 which is crazy
        // 1. deposit
        // this technically doesn`t count as a transaction,so we don`t need to worry about pranking for that
        vm.deal(user, depositAmount);
        vm.prank(user);
        vault.deposit{value: depositAmount}();

        // 2. warp the time
        // we want to warp the time by an amount equal to the time
        vm.warp(block.timestamp + time);
        // we want to check the balance of the user after the time has passed
        // This is just a read function. so we don`t need to worry about pranking for that
        uint256 balanceAfterSomeTime = rebaseToken.balanceOf(user);
        console.log("balanceAfterSomeTime", balanceAfterSomeTime);

        // 2(b) Add the rewards to the vault
        // - we need to deal the owner some ether to add rewards to the vault because otherwise they`re not going
        //   to have any ether to add rewards to the vault
        // - before redemption, `addRewardsToVault` is called. The `rewardAmount` is calculated as
        //   `balanceAfterSomeTime - depositAmount` to ensure enough funds are available for the increased balance due to interest.

        vm.deal(owner, balanceAfterSomeTime - depositAmount);
        vm.prank(owner);
        addRewardsToVault(balanceAfterSomeTime - depositAmount);
        console.log("Added rewards to vault", balanceAfterSomeTime - depositAmount);
        // 3. redeem
        vm.prank(user);
        vault.redeem(type(uint256).max);
        uint256 endBalance = rebaseToken.balanceOf(user);
        console.log("endBalance", endBalance);

        // 4. check user balance
        uint256 ethBalance = address(user).balance;
        console.log("User balance: %d", ethBalance);

        assertEq(ethBalance, balanceAfterSomeTime);
        assertEq(endBalance, 0);
        // check they earned some interest
        assertGt(ethBalance, depositAmount);
    }

    function testRedeemFailsWithMockContract() public {
        // 1. Setup: deposit tokens for the user
        uint256 depositAmount = 1e18;
        vm.deal(user, depositAmount);
        vm.prank(user);
        vault.deposit{value: depositAmount}();

        // 2. Mock the ETH transfer to fail
        // This uses Foundry's vm.mockCall to make the low-level call return false
        vm.mockCall(
            user,
            abi.encodeWithSignature(""), // empty signature for the call function
            abi.encode(false, "") // return false to simulate failure
        );

        // 3. Try to redeem tokens - should fail with the custom error
        vm.prank(user);
        vm.expectRevert(Vault.Vault__ReedemFailed.selector);
        vault.redeem(rebaseToken.balanceOf(user));

        // 4. Clear the mock to avoid affecting other tests
        vm.clearMockedCalls();
    }

    ////////////////////////////////////////////////////////////////////////////////////////
    // transfer
    ////////////////////////////////////////////////////////////////////////////////////////

    function testFuzzTransfer(uint256 amount, uint256 amountToSend) public {
        amount = bound(amount, 1e5 + 1e5, type(uint96).max);
        amountToSend = bound(amountToSend, 1e5, amount - 1e3);
        // 1. deposit
        vm.deal(user, amount);
        vm.prank(user);
        vault.deposit{value: amount}();

        address user2 = makeAddr("user2");
        uint256 userBalance = rebaseToken.balanceOf(user);
        uint256 user2Balance = rebaseToken.balanceOf(user2);
        assertEq(userBalance, amount);
        assertEq(user2Balance, 0);

        // owner reduces the interest rate
        vm.prank(owner);
        rebaseToken.setInterestRate(4e10);

        // 2. transfer to another user
        vm.prank(user);
        rebaseToken.transfer(user2, amountToSend);
        uint256 userBalanceAfterTransfer = rebaseToken.balanceOf(user);
        uint256 user2BalanceAfterTransfer = rebaseToken.balanceOf(user2);
        assertEq(userBalanceAfterTransfer, userBalance - amountToSend);
        assertEq(user2BalanceAfterTransfer, amountToSend);

        // 3. check the interest rate has been inherited (5e10 not 4e10)
        assertEq(rebaseToken.getUserInterestRate(user), 5e10);
        assertEq(rebaseToken.getUserInterestRate(user2), 5e10);

        // After some time has passed, check the balance of the two users has increased
        vm.warp(block.timestamp + 1 days);
        uint256 userBalanceAfterWarp = rebaseToken.balanceOf(user);
        uint256 user2BalanceAfterWarp = rebaseToken.balanceOf(user2);
        // check their interest rates are as expected
        // since user two hadn't minted before, their interest rate should be the same as in the contract
        uint256 user2InterestRate = rebaseToken.getUserInterestRate(user2);
        assertEq(user2InterestRate, 5e10);
        // since user had minted before, their interest rate should be the previous interest rate
        uint256 userInterestRate = rebaseToken.getUserInterestRate(user);
        assertEq(userInterestRate, 5e10);

        assertGt(userBalanceAfterWarp, userBalanceAfterTransfer);
        assertGt(user2BalanceAfterWarp, user2BalanceAfterTransfer);
    }

    function testFuzzTransferFrom(uint256 amount, uint256 amountToSend, uint256 approvalAmount) public {
        // Bound inputs to reasonable values
        amount = bound(amount, 1e5 + 1e5, type(uint96).max);
        amountToSend = bound(amountToSend, 1e5, amount);
        approvalAmount = bound(approvalAmount, amountToSend, type(uint256).max);

        // 1. Setup: deposit tokens for the first user
        vm.deal(user, amount);
        vm.prank(user);
        vault.deposit{value: amount}();

        // 2. Create a second user who will receive the tokens
        address user2 = makeAddr("user2");

        // 3. Approve the test contract to spend user's tokens
        vm.prank(user);
        rebaseToken.approve(address(this), approvalAmount);

        // 4. Record balances before transfer
        uint256 userBalanceBefore = rebaseToken.balanceOf(user);
        uint256 user2BalanceBefore = rebaseToken.balanceOf(user2);
        uint256 userInterestRateBefore = rebaseToken.getUserInterestRate(user);

        // 5. Execute transferFrom
        bool success = rebaseToken.transferFrom(user, user2, amountToSend);

        // 6. Verify transfer was successful
        assertTrue(success);

        // 7. Check balances after transfer
        assertEq(rebaseToken.balanceOf(user), userBalanceBefore - amountToSend);
        assertEq(rebaseToken.balanceOf(user2), user2BalanceBefore + amountToSend);

        // 8. Verify interest rate inheritance
        assertEq(rebaseToken.getUserInterestRate(user2), userInterestRateBefore);
    }

    function testTransferFromWithMaxUint() public {
        // 1. Setup: deposit tokens for the first user
        uint256 depositAmount = 1e18;
        vm.deal(user, depositAmount);
        vm.prank(user);
        vault.deposit{value: depositAmount}();

        // 2. Create a second user who will receive the tokens
        address user2 = makeAddr("user2");

        // 3. Approve the test contract to spend user's tokens
        vm.prank(user);
        rebaseToken.approve(address(this), type(uint256).max);

        // 4. Record balances before transfer
        uint256 userBalanceBefore = rebaseToken.balanceOf(user);
        console.log("User balance before transfer:", userBalanceBefore);

        // 5. Execute transferFrom with type(uint256).max to test lines 162-163
        bool success = rebaseToken.transferFrom(user, user2, type(uint256).max);

        // 6. Verify transfer was successful
        assertTrue(success);

        // 7. Check that the entire balance was transferred
        assertEq(rebaseToken.balanceOf(user), 0, "User should have 0 balance after max transfer");
        assertEq(rebaseToken.balanceOf(user2), userBalanceBefore, "Recipient should receive entire balance");

        assertEq(
            rebaseToken.allowance(user, address(this)),
            type(uint256).max,
            "Allowance should remain at max value for max uint approvals"
        );
    }

    function testTransferFromWithMaxUintAfterTimeElapsed() public {
        // 1. Setup: deposit tokens for the first user
        uint256 depositAmount = 1e18;
        vm.deal(user, depositAmount);
        vm.prank(user);
        vault.deposit{value: depositAmount}();

        // 2. Warp time to accumulate interest
        vm.warp(block.timestamp + 1 days);

        // 3. Create a second user who will receive the tokens
        address user2 = makeAddr("user2");

        // 4. Approve the test contract to spend user's tokens
        vm.prank(user);
        rebaseToken.approve(address(this), type(uint256).max);

        // 5. Record balances before transfer
        uint256 userBalanceBefore = rebaseToken.balanceOf(user);
        console.log("User balance before transfer (with interest):", userBalanceBefore);

        // 6. Execute transferFrom with type(uint256).max
        bool success = rebaseToken.transferFrom(user, user2, type(uint256).max);

        // 7. Verify transfer was successful
        assertTrue(success);

        // 8. Check that the entire balance including accrued interest was transferred
        assertEq(rebaseToken.balanceOf(user), 0, "User should have 0 balance after max transfer");
        assertEq(
            rebaseToken.balanceOf(user2), userBalanceBefore, "Recipient should receive entire balance with interest"
        );
    }

    ////////////////////////////////////////////////////////////////////////////////////////
    // setInterestRate
    ////////////////////////////////////////////////////////////////////////////////////////

    function testFuzzSetInterestRate(uint256 newInterestRate) public {
        // bound the interest rate to be less than the current interest rate
        newInterestRate = bound(newInterestRate, 0, rebaseToken.getInterestRate() - 1);
        // Update the interest rate
        vm.startPrank(owner);
        rebaseToken.setInterestRate(newInterestRate);
        uint256 interestRate = rebaseToken.getInterestRate();
        assertEq(interestRate, newInterestRate);
        vm.stopPrank();

        // check that if someone deposits, this is their new interest rate
        vm.startPrank(user);
        vm.deal(user, SEND_VALUE);
        vault.deposit{value: SEND_VALUE}();
        uint256 userInterestRate = rebaseToken.getUserInterestRate(user);

        console.log("userInterestRate", userInterestRate);
        vm.stopPrank();
        assertEq(userInterestRate, newInterestRate);
    }

    // This test ensures that only the `owner` (as defined by OpenZeppelin's `Ownable`) can call `setInterestRate`
    function testFuzzCannotSetInterestRate(uint256 newInterestrate) public {
        vm.prank(user);
        vm.expectPartialRevert(bytes4(Ownable.OwnableUnauthorizedAccount.selector));
        rebaseToken.setInterestRate(newInterestrate);
    }

    function testFuzzInterestRateCanOnlyDecrease(uint256 newInterestrate) public {
        uint256 initialInterestRate = rebaseToken.getInterestRate();
        newInterestrate = bound(newInterestrate, initialInterestRate, type(uint96).max);
        vm.prank(owner);
        vm.expectPartialRevert(bytes4(RebaseToken.RebaseToken__InterestRateCanOnlyDecrease.selector));
        rebaseToken.setInterestRate(newInterestrate);
        assertEq(rebaseToken.getInterestRate(), initialInterestRate);
    }

    ////////////////////////////////////////////////////////////////////////////////////////
    // mint and burn
    ////////////////////////////////////////////////////////////////////////////////////////

    function testCannnotCallMintAndBurn() public {
        vm.deal(user, 1e18);
        vm.prank(user); // User does not have MINT_AND_BURN_ROLE
        vault.deposit{value: 1e18}();

        // Get the interest rate outside of prank to avoid issues
        uint256 currentInterestRate = rebaseToken.getInterestRate();

        // Test that user cannot call mint function directly
        vm.prank(user);
        vm.expectPartialRevert(bytes4(IAccessControl.AccessControlUnauthorizedAccount.selector));
        rebaseToken.mint(user, 100, currentInterestRate);

        // Test that user cannot call burn function directly
        vm.prank(user);
        vm.expectPartialRevert(bytes4(IAccessControl.AccessControlUnauthorizedAccount.selector));
        rebaseToken.burn(user, 1e18); // Assuming user has some balance to burn for this part
    }

    ////////////////////////////////////////////////////////////////////////////////////////
    // Getters
    ////////////////////////////////////////////////////////////////////////////////////////

    // Verifies that the `principleBalanceOf(address _user)` function correctly returns the original amount
    // of tokens minted or deposited by a user, excluding any accrued interest.
    // This test confirms that the contract accurately tracks the user's base contribution, distinct from their
    // interest-adjusted balance.
    function testGetPrincipalAmount(uint256 amount) public {
        amount = bound(amount, 1e5, type(uint96).max);
        vm.deal(user, amount);
        vm.prank(user);
        vault.deposit{value: amount}();
        assertEq(rebaseToken.principalBalanceOf(user), amount);

        vm.warp(block.timestamp + 1 hours);
        assertEq(rebaseToken.principalBalanceOf(user), amount);
    }

    function testGetRebaseTokenAddress() public view {
        assertEq(vault.getRebaseTokenAddress(), address(rebaseToken));
    }
}

// 1. дополнение к testRedeemAfterTimeHasPassed:
// If you have higher precision, then it means that you are more likely to have overflow and therefore you need to
// restrict the inputs.Or you have a lower precision, wich means it`s ore likely you`re going to have that kind
// of rounding issue, but then you dont`t have to restrict your inputs.

// 2. дополнение к testCannotSetInterestRate касаемо m.expectPartialRevert
// Importantly, when dealing with custom errors that may or may not have arguments, or standard errors
// from libraries like OpenZeppelin, `vm.expectRevert()` might not be sufficient if it expects an exact revert
// data match. `vm.expectPartialRevert(bytes4(Ownable.OwnableUnauthorizedAccount.selector))` is more robust as
// it checks for the correct error selector, ignoring any arguments.
