{"abi": [{"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addRemotePool", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "remotePoolAddress", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "applyAllowListUpdates", "inputs": [{"name": "removes", "type": "address[]", "internalType": "address[]"}, {"name": "adds", "type": "address[]", "internalType": "address[]"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "applyChainUpdates", "inputs": [{"name": "remoteChainSelectorsToRemove", "type": "uint64[]", "internalType": "uint64[]"}, {"name": "chainsToAdd", "type": "tuple[]", "internalType": "struct TokenPool.ChainUpdate[]", "components": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "remotePoolAddresses", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "remoteTokenAddress", "type": "bytes", "internalType": "bytes"}, {"name": "outboundRateLimiterConfig", "type": "tuple", "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}, {"name": "inboundRateLimiterConfig", "type": "tuple", "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAllowList", "inputs": [], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getAllowListEnabled", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentInboundRateLimiterState", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct RateLimiter.TokenBucket", "components": [{"name": "tokens", "type": "uint128", "internalType": "uint128"}, {"name": "lastUpdated", "type": "uint32", "internalType": "uint32"}, {"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}], "stateMutability": "view"}, {"type": "function", "name": "getCurrentOutboundRateLimiterState", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct RateLimiter.TokenBucket", "components": [{"name": "tokens", "type": "uint128", "internalType": "uint128"}, {"name": "lastUpdated", "type": "uint32", "internalType": "uint32"}, {"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}], "stateMutability": "view"}, {"type": "function", "name": "getRateLimitAdmin", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getRemotePools", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "", "type": "bytes[]", "internalType": "bytes[]"}], "stateMutability": "view"}, {"type": "function", "name": "getRemoteToken", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "function", "name": "getRmnProxy", "inputs": [], "outputs": [{"name": "rmnProxy", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getRouter", "inputs": [], "outputs": [{"name": "router", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getSupportedChains", "inputs": [], "outputs": [{"name": "", "type": "uint64[]", "internalType": "uint64[]"}], "stateMutability": "view"}, {"type": "function", "name": "getToken", "inputs": [], "outputs": [{"name": "token", "type": "address", "internalType": "contract IERC20"}], "stateMutability": "view"}, {"type": "function", "name": "getTokenDecimals", "inputs": [], "outputs": [{"name": "decimals", "type": "uint8", "internalType": "uint8"}], "stateMutability": "view"}, {"type": "function", "name": "isRemotePool", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "remotePoolAddress", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isSupportedChain", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isSupportedToken", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "lockOrBurn", "inputs": [{"name": "lockOrBurnIn", "type": "tuple", "internalType": "struct Pool.LockOrBurnInV1", "components": [{"name": "receiver", "type": "bytes", "internalType": "bytes"}, {"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "originalSender", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "localToken", "type": "address", "internalType": "address"}]}], "outputs": [{"name": "lockOrBurnOut", "type": "tuple", "internalType": "struct Pool.LockOrBurnOutV1", "components": [{"name": "dest<PERSON>okenAddress", "type": "bytes", "internalType": "bytes"}, {"name": "destPoolData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "releaseOrMint", "inputs": [{"name": "releaseOrMintIn", "type": "tuple", "internalType": "struct Pool.ReleaseOrMintInV1", "components": [{"name": "originalSender", "type": "bytes", "internalType": "bytes"}, {"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}, {"name": "localToken", "type": "address", "internalType": "address"}, {"name": "sourcePoolAddress", "type": "bytes", "internalType": "bytes"}, {"name": "sourcePoolData", "type": "bytes", "internalType": "bytes"}, {"name": "offchainTokenData", "type": "bytes", "internalType": "bytes"}]}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct Pool.ReleaseOrMintOutV1", "components": [{"name": "destinationAmount", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeRemotePool", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "remotePoolAddress", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setChainRateLimiterConfig", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "outboundConfig", "type": "tuple", "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}, {"name": "inboundConfig", "type": "tuple", "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setChainRateLimiterConfigs", "inputs": [{"name": "remoteChainSelectors", "type": "uint64[]", "internalType": "uint64[]"}, {"name": "outboundConfigs", "type": "tuple[]", "internalType": "struct RateLimiter.Config[]", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}, {"name": "inboundConfigs", "type": "tuple[]", "internalType": "struct RateLimiter.Config[]", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRateLimitAdmin", "inputs": [{"name": "rateLimitAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setRouter", "inputs": [{"name": "newRouter", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "pure"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "AllowListAdd", "inputs": [{"name": "sender", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AllowListRemove", "inputs": [{"name": "sender", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Burned", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ChainAdded", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "indexed": false, "internalType": "uint64"}, {"name": "remoteToken", "type": "bytes", "indexed": false, "internalType": "bytes"}, {"name": "outboundRateLimiterConfig", "type": "tuple", "indexed": false, "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}, {"name": "inboundRateLimiterConfig", "type": "tuple", "indexed": false, "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}], "anonymous": false}, {"type": "event", "name": "ChainConfigured", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "indexed": false, "internalType": "uint64"}, {"name": "outboundRateLimiterConfig", "type": "tuple", "indexed": false, "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}, {"name": "inboundRateLimiterConfig", "type": "tuple", "indexed": false, "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}], "anonymous": false}, {"type": "event", "name": "ChainRemoved", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Config<PERSON><PERSON><PERSON>", "inputs": [{"name": "config", "type": "tuple", "indexed": false, "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}], "anonymous": false}, {"type": "event", "name": "Locked", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Minted", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferRequested", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RateLimitAdminSet", "inputs": [{"name": "rateLimitAdmin", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Released", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "RemotePoolAdded", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "indexed": true, "internalType": "uint64"}, {"name": "remotePoolAddress", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "RemotePoolRemoved", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "indexed": true, "internalType": "uint64"}, {"name": "remotePoolAddress", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "event", "name": "RouterUpdated", "inputs": [{"name": "old<PERSON>outer", "type": "address", "indexed": false, "internalType": "address"}, {"name": "newRouter", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AllowListNotEnabled", "inputs": []}, {"type": "error", "name": "CallerIsNotARampOnRouter", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "CannotTransferToSelf", "inputs": []}, {"type": "error", "name": "ChainAlreadyExists", "inputs": [{"name": "chainSelector", "type": "uint64", "internalType": "uint64"}]}, {"type": "error", "name": "ChainNotAllowed", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}]}, {"type": "error", "name": "CursedByRMN", "inputs": []}, {"type": "error", "name": "DisabledNonZeroRateLimit", "inputs": [{"name": "config", "type": "tuple", "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}]}, {"type": "error", "name": "InvalidDecimalArgs", "inputs": [{"name": "expected", "type": "uint8", "internalType": "uint8"}, {"name": "actual", "type": "uint8", "internalType": "uint8"}]}, {"type": "error", "name": "InvalidRateLimitRate", "inputs": [{"name": "rateLimiterConfig", "type": "tuple", "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}]}, {"type": "error", "name": "InvalidRemoteChainDecimals", "inputs": [{"name": "sourcePoolData", "type": "bytes", "internalType": "bytes"}]}, {"type": "error", "name": "InvalidRemotePoolForChain", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "remotePoolAddress", "type": "bytes", "internalType": "bytes"}]}, {"type": "error", "name": "InvalidSourcePoolAddress", "inputs": [{"name": "sourcePoolAddress", "type": "bytes", "internalType": "bytes"}]}, {"type": "error", "name": "InvalidToken", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "MismatchedArrayLengths", "inputs": []}, {"type": "error", "name": "MustBeProposedOwner", "inputs": []}, {"type": "error", "name": "NonExistentChain", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}]}, {"type": "error", "name": "OnlyCallableByOwner", "inputs": []}, {"type": "error", "name": "OverflowDetected", "inputs": [{"name": "remoteDecimals", "type": "uint8", "internalType": "uint8"}, {"name": "localDecimals", "type": "uint8", "internalType": "uint8"}, {"name": "remoteAmount", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "OwnerCannotBeZero", "inputs": []}, {"type": "error", "name": "PoolAlreadyAdded", "inputs": [{"name": "remoteChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "remotePoolAddress", "type": "bytes", "internalType": "bytes"}]}, {"type": "error", "name": "RateLimitMustBeDisabled", "inputs": []}, {"type": "error", "name": "SenderNotAllowed", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Unauthorized", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZeroAddressNotAllowed", "inputs": []}], "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 27}