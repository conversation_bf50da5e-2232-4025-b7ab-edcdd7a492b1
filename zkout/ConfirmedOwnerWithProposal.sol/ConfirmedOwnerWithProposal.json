{"abi": [{"type": "constructor", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}, {"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferRequested", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}], "bytecode": {"object": "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", "object_format": "raw", "missing_libraries": []}, "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "hash": "0100004fee165cd256445db5d8fa3e7c67f37f372561499344f9528d12309e00", "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 8}