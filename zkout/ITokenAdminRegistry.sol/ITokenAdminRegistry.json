{"abi": [{"type": "function", "name": "acceptAdminRole", "inputs": [{"name": "localToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getPool", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proposeAdministrator", "inputs": [{"name": "localToken", "type": "address", "internalType": "address"}, {"name": "administrator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPool", "inputs": [{"name": "localToken", "type": "address", "internalType": "address"}, {"name": "pool", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferAdminRole", "inputs": [{"name": "localToken", "type": "address", "internalType": "address"}, {"name": "newAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}], "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 3}