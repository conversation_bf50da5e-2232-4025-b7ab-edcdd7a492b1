{"abi": [{"type": "function", "name": "EVM_EXTRA_ARGS_V1_TAG", "inputs": [], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "view"}, {"type": "function", "name": "GENERIC_EXTRA_ARGS_V2_TAG", "inputs": [], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "view"}, {"type": "function", "name": "SVM_ACCOUNT_BYTE_SIZE", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "SVM_EXTRA_ARGS_MAX_ACCOUNTS", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "SVM_EXTRA_ARGS_V1_TAG", "inputs": [], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "view"}, {"type": "function", "name": "SVM_MESSAGING_ACCOUNTS_OVERHEAD", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "SVM_TOKEN_TRANSFER_DATA_OVERHEAD", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}], "bytecode": {"object": "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", "object_format": "raw", "missing_libraries": []}, "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "hash": "010000259a2badf823dfe6e83e43571ea7f3e2d06bf34f251049a25e878f4bae", "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 22}