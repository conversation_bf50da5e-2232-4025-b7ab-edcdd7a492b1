{"abi": [{"type": "function", "name": "acceptAdminRole", "inputs": [{"name": "localToken", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addRegistryModule", "inputs": [{"name": "module", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getAllConfiguredTokens", "inputs": [{"name": "startIndex", "type": "uint64", "internalType": "uint64"}, {"name": "maxCount", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "tokens", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getPool", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getPools", "inputs": [{"name": "tokens", "type": "address[]", "internalType": "address[]"}], "outputs": [{"name": "", "type": "address[]", "internalType": "address[]"}], "stateMutability": "view"}, {"type": "function", "name": "getTokenConfig", "inputs": [{"name": "token", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct TokenAdminRegistry.TokenConfig", "components": [{"name": "administrator", "type": "address", "internalType": "address"}, {"name": "pendingAdministrator", "type": "address", "internalType": "address"}, {"name": "tokenPool", "type": "address", "internalType": "address"}]}], "stateMutability": "view"}, {"type": "function", "name": "isAdministrator", "inputs": [{"name": "localToken", "type": "address", "internalType": "address"}, {"name": "administrator", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isRegistryModule", "inputs": [{"name": "module", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "proposeAdministrator", "inputs": [{"name": "localToken", "type": "address", "internalType": "address"}, {"name": "administrator", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeRegistryModule", "inputs": [{"name": "module", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setPool", "inputs": [{"name": "localToken", "type": "address", "internalType": "address"}, {"name": "pool", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferAdminRole", "inputs": [{"name": "localToken", "type": "address", "internalType": "address"}, {"name": "newAdmin", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "typeAndVersion", "inputs": [], "outputs": [{"name": "", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "event", "name": "AdministratorTransferRequested", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "currentAdmin", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newAdmin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "AdministratorTransferred", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newAdmin", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferRequested", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PoolSet", "inputs": [{"name": "token", "type": "address", "indexed": true, "internalType": "address"}, {"name": "previousPool", "type": "address", "indexed": true, "internalType": "address"}, {"name": "newPool", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RegistryModuleAdded", "inputs": [{"name": "module", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RegistryModuleRemoved", "inputs": [{"name": "module", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "AlreadyRegistered", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "InvalidTokenPoolToken", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OnlyAdministrator", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OnlyPendingAdministrator", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}, {"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OnlyRegistryModuleOrOwner", "inputs": [{"name": "sender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ZeroAddress", "inputs": []}], "bytecode": {"object": "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", "object_format": "raw", "missing_libraries": []}, "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "hash": "0100017b217a32af0222c408fff050fc34d9a15aeb495c9b517c71de25abbf32", "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 6}