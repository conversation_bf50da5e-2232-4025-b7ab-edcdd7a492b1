{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferRequested", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "CannotTransferToSelf", "inputs": []}, {"type": "error", "name": "MustBeProposedOwner", "inputs": []}, {"type": "error", "name": "OnlyCallableByOwner", "inputs": []}, {"type": "error", "name": "OwnerCannotBeZero", "inputs": []}], "bytecode": {"object": "0000008003000039000000400030043f00000001002001900000002d0000c13d00000060021002700000002102200197000000040020008c000000530000413d000000000301043b000000e003300270000000260030009c0000004a0000613d000000270030009c000000410000613d000000280030009c000000530000c13d0000000001000416000000000001004b000000530000c13d000000000100041a00000029021001970000000006000411000000000026004b000000550000c13d0000000102000039000000000302041a0000002204300197000000000464019f000000000042041b0000002201100197000000000010041b00000000010004140000002905300197000000210010009c0000002101008041000000c0011002100000002b011001c70000800d0200003900000003030000390000003004000041007f007a0000040f0000000100200190000000530000613d0000000001000019000000800001042e0000000001000416000000000001004b000000530000c13d0000000001000411000000000001004b000000370000c13d0000002401000041000000800010043f000000250100004100000081000104300000000102000039000000000302041a0000002203300197000000000113019f000000000012041b0000002001000039000001000010044300000120000004430000002301000041000000800001042e0000000001000416000000000001004b000000530000c13d0000000101000039000000000101041a0000002901100197000000800010043f0000002e01000041000000800001042e0000000003000416000000000003004b000000530000c13d000000240020008c000000530000413d0000000401100370000000000101043b000000290010009c000000590000a13d000000000100001900000081000104300000002f01000041000000800010043f000000250100004100000081000104300000000102000039000000000202041a00000029022001970000000005000411000000000025004b000000660000c13d0000002906100197000000000056004b0000006a0000c13d0000002d01000041000000800010043f000000250100004100000081000104300000002a01000041000000800010043f00000025010000410000008100010430000000000200041a0000002202200197000000000112019f000000000010041b0000000001000414000000210010009c0000002101008041000000c0011002100000002b011001c70000800d0200003900000003030000390000002c04000041007f007a0000040f00000001002001900000002b0000c13d000000530000013d0000007d002104210000000102000039000000000001042d0000000002000019000000000001042d0000007f00000432000000800001042e00000081000104300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ffffffffffffffffffffffffffffffff000000000000000000000000000000000000000000000002000000000000000000000000000000400000010000000000000000009b15e16f00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000400000080000000000000000000000000000000000000000000000000000000000000000000000000f2fde38b000000000000000000000000000000000000000000000000000000008da5cb5b0000000000000000000000000000000000000000000000000000000079ba5097000000000000000000000000ffffffffffffffffffffffffffffffffffffffff2b5c74de000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000ed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278dad89dca00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000080000000000000000002b543c6000000000000000000000000000000000000000000000000000000008be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e00000000000000000000000000000000000000000000000000000000000000000000000000000000000a2646970667358221220a2e576e60f64b615101700006e94a3c81e15405130f39da3dd4ad868730bc7aa64736f6c6378247a6b736f6c633a312e352e31353b736f6c633a302e382e32343b6c6c766d3a312e302e320055", "object_format": "raw", "missing_libraries": []}, "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "hash": "0100003556092234de633b8659cebe7bacb52f21e9c30e7200a43501b6a0194f", "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 29}