{"abi": [{"type": "constructor", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}, {"name": "pending<PERSON><PERSON>er", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "acceptOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "to", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferRequested", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "to", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "error", "name": "CannotTransferToSelf", "inputs": []}, {"type": "error", "name": "MustBeProposedOwner", "inputs": []}, {"type": "error", "name": "OnlyCallableByOwner", "inputs": []}, {"type": "error", "name": "OwnerCannotBeZero", "inputs": []}], "bytecode": {"object": "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", "object_format": "raw", "missing_libraries": []}, "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "hash": "010000477ad1b01f1fba992b71605a3bc31db0a52934c714a0c3021b1f61b6e0", "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 28}