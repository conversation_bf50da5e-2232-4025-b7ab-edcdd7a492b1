{"abi": [{"type": "function", "name": "executeSingleMessage", "inputs": [{"name": "message", "type": "tuple", "internalType": "struct InternalPreV1dot6.EVM2EVMMessage", "components": [{"name": "sourceChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "sender", "type": "address", "internalType": "address"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "sequenceNumber", "type": "uint64", "internalType": "uint64"}, {"name": "gasLimit", "type": "uint256", "internalType": "uint256"}, {"name": "strict", "type": "bool", "internalType": "bool"}, {"name": "nonce", "type": "uint64", "internalType": "uint64"}, {"name": "feeToken", "type": "address", "internalType": "address"}, {"name": "feeTokenAmount", "type": "uint256", "internalType": "uint256"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "tokenAmounts", "type": "tuple[]", "internalType": "struct Client.EVMTokenAmount[]", "components": [{"name": "token", "type": "address", "internalType": "address"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}, {"name": "sourceTokenData", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "messageId", "type": "bytes32", "internalType": "bytes32"}]}, {"name": "offchainTokenData", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "tokenGasOverrides", "type": "uint32[]", "internalType": "uint32[]"}], "outputs": [], "stateMutability": "nonpayable"}], "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 35}