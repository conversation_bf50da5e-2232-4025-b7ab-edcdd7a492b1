{"abi": [{"type": "function", "name": "executeSingleMessage", "inputs": [{"name": "message", "type": "tuple", "internalType": "struct Internal.Any2EVMRampMessage", "components": [{"name": "header", "type": "tuple", "internalType": "struct Internal.RampMessageHeader", "components": [{"name": "messageId", "type": "bytes32", "internalType": "bytes32"}, {"name": "sourceChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "destChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "sequenceNumber", "type": "uint64", "internalType": "uint64"}, {"name": "nonce", "type": "uint64", "internalType": "uint64"}]}, {"name": "sender", "type": "bytes", "internalType": "bytes"}, {"name": "data", "type": "bytes", "internalType": "bytes"}, {"name": "receiver", "type": "address", "internalType": "address"}, {"name": "gasLimit", "type": "uint256", "internalType": "uint256"}, {"name": "tokenAmounts", "type": "tuple[]", "internalType": "struct Internal.Any2EVMTokenTransfer[]", "components": [{"name": "sourcePoolAddress", "type": "bytes", "internalType": "bytes"}, {"name": "dest<PERSON>okenAddress", "type": "address", "internalType": "address"}, {"name": "destGasAmount", "type": "uint32", "internalType": "uint32"}, {"name": "extraData", "type": "bytes", "internalType": "bytes"}, {"name": "amount", "type": "uint256", "internalType": "uint256"}]}]}, {"name": "offchainTokenData", "type": "bytes[]", "internalType": "bytes[]"}, {"name": "tokenGasOverrides", "type": "uint32[]", "internalType": "uint32[]"}], "outputs": [], "stateMutability": "nonpayable"}], "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 35}