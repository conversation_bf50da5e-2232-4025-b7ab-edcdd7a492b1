{"abi": [{"type": "function", "name": "getOffRamps", "inputs": [], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct IRouterFork.OffRamp[]", "components": [{"name": "sourceChainSelector", "type": "uint64", "internalType": "uint64"}, {"name": "offRamp", "type": "address", "internalType": "address"}]}], "stateMutability": "view"}, {"type": "function", "name": "getOnRamp", "inputs": [{"name": "destChainSelector", "type": "uint64", "internalType": "uint64"}], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}], "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 35}