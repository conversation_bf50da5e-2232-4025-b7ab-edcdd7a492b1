{"abi": [{"type": "function", "name": "isBlessed", "inputs": [{"name": "taggedRoot", "type": "tuple", "internalType": "struct IRMN.TaggedRoot", "components": [{"name": "commitStore", "type": "address", "internalType": "address"}, {"name": "root", "type": "bytes32", "internalType": "bytes32"}]}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isCursed", "inputs": [{"name": "subject", "type": "bytes16", "internalType": "bytes16"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isCursed", "inputs": [], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}], "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 19}