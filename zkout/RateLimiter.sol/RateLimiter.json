{"abi": [{"type": "event", "name": "Config<PERSON><PERSON><PERSON>", "inputs": [{"name": "config", "type": "tuple", "indexed": false, "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}], "anonymous": false}, {"type": "event", "name": "TokensConsumed", "inputs": [{"name": "tokens", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AggregateValueMaxCapacityExceeded", "inputs": [{"name": "capacity", "type": "uint256", "internalType": "uint256"}, {"name": "requested", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "AggregateValueRateLimitReached", "inputs": [{"name": "minWaitInSeconds", "type": "uint256", "internalType": "uint256"}, {"name": "available", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "BucketOverfilled", "inputs": []}, {"type": "error", "name": "DisabledNonZeroRateLimit", "inputs": [{"name": "config", "type": "tuple", "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}]}, {"type": "error", "name": "InvalidRateLimitRate", "inputs": [{"name": "rateLimiterConfig", "type": "tuple", "internalType": "struct RateLimiter.Config", "components": [{"name": "isEnabled", "type": "bool", "internalType": "bool"}, {"name": "capacity", "type": "uint128", "internalType": "uint128"}, {"name": "rate", "type": "uint128", "internalType": "uint128"}]}]}, {"type": "error", "name": "OnlyCallableByAdminOrOwner", "inputs": []}, {"type": "error", "name": "RateLimitMustBeDisabled", "inputs": []}, {"type": "error", "name": "TokenMaxCapacityExceeded", "inputs": [{"name": "capacity", "type": "uint256", "internalType": "uint256"}, {"name": "requested", "type": "uint256", "internalType": "uint256"}, {"name": "tokenAddress", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "TokenRateLimitReached", "inputs": [{"name": "minWaitInSeconds", "type": "uint256", "internalType": "uint256"}, {"name": "available", "type": "uint256", "internalType": "uint256"}, {"name": "tokenAddress", "type": "address", "internalType": "address"}]}], "bytecode": {"object": "0000000100200190000000100000613d0000008001000039000000400010043f0000000001000416000000000001004b000000100000c13d00000140000004430000000001000410000001600010044300000020010000390000010000100443000000010100003900000120001004430000000601000041000000130001042e000000000100001900000014000104300000001200000432000000130001042e000000140001043000000000000000000000000000000000000000000000000000000002000000000000000000000000000000800000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a2646970667358221220cf7733c70e59cc40fbc67d16c70f1bbb4d22653b125d88dac275159a89b075dd64736f6c6378247a6b736f6c633a312e352e31353b736f6c633a302e382e32343b6c6c766d3a312e302e320055", "object_format": "raw", "missing_libraries": []}, "storageLayout": {"storage": [], "types": {}}, "userdoc": {}, "devdoc": {}, "hash": "0100000bbafb72ee9f2062b171cb2c45e6c5440c6fbd3c0868466d093f6c60fe", "factoryDependencies": {}, "factoryDependenciesUnlinked": [], "id": 26}