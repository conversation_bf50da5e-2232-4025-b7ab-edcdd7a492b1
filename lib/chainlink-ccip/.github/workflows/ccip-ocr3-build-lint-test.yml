name: "Build lint and test CCIP-OCR3"

on:
  pull_request:
  push:
    branches:
      - 'main'

jobs:
  build-lint-test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: .
    steps:
      - name: Checkout repository
        uses: actions/checkout@9bb56186c3b09b4f86b1c65136769dd318469633 # v4.1.2
        with:
          fetch-depth: 0
      - name: Determine Go version
        id: go_version
        run: echo "GO_VERSION=$(cat go.mod |grep "^go"|cut -d' ' -f 2)" >> $GITHUB_ENV
      - name: Setup Go ${{ env.GO_VERSION }}
        uses: actions/setup-go@0c52d547c9bc32b1aa3301fd7a9cb496313a4491 # v5.0.0
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Display Go version
        run: go version
      - name: Build
        run: make
      - name: Install linter
        run: |
          make install-golangcilint
      - name: Run linter
        run: make lint
      - name: Run protobuf linter
        run: |
          make install_buf
          make proto-lint
      - name: Run tests
        run: TEST_COUNT=20 COVERAGE_FILE=coverage.out make test
      - name: Generate coverage report
        if: github.event_name == 'pull_request'
        run: |
          total=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
          echo "coverage=$total" >> $GITHUB_ENV
      - name: Coverage on target branch and proto breaking change check
        if: github.event_name == 'pull_request'
        run: |
          git fetch origin ${{ github.base_ref }}
          git branch -a
          make proto-check-breaking COMPARE_AGAINST_BRANCH=origin/${{ github.base_ref }}
          git checkout ${{ github.base_ref }}
          TEST_COUNT=1 COVERAGE_FILE=coverage_target.out make test
          total=$(go tool cover -func=coverage_target.out | grep total | awk '{print $3}')
          echo "coverage_target=$total" >> $GITHUB_ENV
      - name: Remove previous coverage comments
        uses: actions/github-script@v6
        if: github.event_name == 'pull_request'
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { owner, repo, number: issue_number } = context.issue;
            const comments = await github.rest.issues.listComments({
              owner,
              repo,
              issue_number
            });
            const coverageCommentPrefix = "| Metric |";
            for (const comment of comments.data) {
              if (comment.body.startsWith(coverageCommentPrefix)) {
                await github.rest.issues.deleteComment({
                  owner,
                  repo,
                  comment_id: comment.id
                });
              }
            }
      - name: Display coverage in PR comment
        uses: actions/github-script@v6
        if: github.event_name == 'pull_request'
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const coverage = process.env.coverage;
            const coverage_target = process.env.coverage_target;
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `| Metric | \`${{ github.head_ref }}\` | \`${{ github.base_ref }}\` |\n|--|--|--|\n| **Coverage** | ${coverage} | ${coverage_target} |`
            });
