name: Solana Verified Build

on:
  workflow_dispatch:
    inputs:
      sha:
        description: 'SHA to build (can be short)'
        required: true
        type: string
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest-8cores-32GB
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: Get Long and Short SHAs
      id: get_sha
      run: |
        FULL_SHA=$(git rev-parse ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.sha || github.sha }})
        echo "short_sha=${FULL_SHA:0:12}" >> $GITHUB_OUTPUT
        echo "full_sha=$FULL_SHA" >> $GITHUB_OUTPUT
    - uses: actions/checkout@v4
      with:
        ref: ${{ steps.get_sha.outputs.full_sha }}
        fetch-depth: 0
    - uses: actions-rust-lang/setup-rust-toolchain@9399c7bb15d4c7d47b27263d024f0a4978346ba4 # v1
    - name: Install Solana Verify
      run: |
        cargo install solana-verify
    - name: Cache cargo target dir
      id: cache-target
      uses: actions/cache@v4 # v4
      with:
        path: chains/solana/contracts/target/deploy/*.so
        key: ${{ runner.os }}-solana-contract-verified-${{ hashFiles('chains/solana/contracts/**/*.rs', 'chains/solana/contracts/**/Cargo.lock') }}
    - name: Build Verified Artifacts
      if: steps.cache-target.outputs.cache-hit != 'true'
      run: |
        cd chains/solana/contracts
        solana-verify build
    - name: Generate release files
      run: |
        cd chains/solana/contracts
        tar cfvz artifacts.tar.gz target/deploy/*.so target/idl/*
    - name: Publish Release
      uses: softprops/action-gh-release@c95fe1489396fe8a9eb87c0abf8aa5b2ef267fda # v2.2.1
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        tag_name: solana-artifacts-localtest-${{ steps.get_sha.outputs.short_sha }}
        target_commitish: ${{ steps.get_sha.outputs.full_sha }}
        files: |
          chains/solana/contracts/artifacts.tar.gz
