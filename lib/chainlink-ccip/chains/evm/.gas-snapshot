BurnFromMintTokenPool_lockOrBurn:test_PoolBurn() (gas: 238950)
BurnFromMintTokenPool_lockOrBurn:test_setup() (gas: 24178)
BurnMintTokenPool_lockOrBurn:test_PoolBurn() (gas: 236872)
BurnMintTokenPool_lockOrBurn:test_Setup() (gas: 17819)
BurnMintTokenPool_releaseOrMint:test_PoolMint() (gas: 102527)
BurnMintWithLockReleaseFlagTokenPool_lockOrBurn:test_LockOrBurn_CorrectReturnData() (gas: 237292)
BurnMintWithLockReleaseFlagTokenPool_releaseOrMint:test_releaseOrMint_EmptySourcePoolData() (gas: 102089)
BurnMintWithLockReleaseFlagTokenPool_releaseOrMint:test_releaseOrMint_LockReleaseFlagInSourcePoolData() (gas: 102202)
BurnMintWithLockReleaseFlagTokenPool_releaseOrMint_e2eTest:test_releaseOrMint_SourcePoolDataFromHybridUSDCPool() (gas: 231702)
BurnToAddressMintTokenPool_lockOrBurn:test_LockOrBurn() (gas: 235440)
BurnWithFromMintTokenPool_lockOrBurn:test_PoolBurn() (gas: 239012)
BurnWithFromMintTokenPool_lockOrBurn:test_Setup() (gas: 24169)
CCIPClientExample_sanity:test_ImmutableExamples() (gas: 2090431)
CCIPHome__validateConfig:test__validateConfig() (gas: 300016)
CCIPHome__validateConfig:test__validateConfigLessTransmittersThanSigners() (gas: 332965)
CCIPHome__validateConfig:test__validateConfigSmallerFChain() (gas: 459322)
CCIPHome_applyChainConfigUpdates:test_applyChainConfigUpdates_addChainConfigs() (gas: 350127)
CCIPHome_applyChainConfigUpdates:test_applyChainConfigUpdates_removeChainConfigs() (gas: 282241)
CCIPHome_applyChainConfigUpdates:test_getPaginatedCCIPHomes() (gas: 373692)
CCIPHome_beforeCapabilityConfigSet:test_beforeCapabilityConfigSet() (gas: 1455733)
CCIPHome_constructor:test_constructor() (gas: 3547489)
CCIPHome_getAllConfigs:test_getAllConfigs() (gas: 2772793)
CCIPHome_getCapabilityConfiguration:test_getCapabilityConfiguration() (gas: 9073)
CCIPHome_getConfigDigests:test_getConfigDigests() (gas: 2547587)
CCIPHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive_multiplePlugins() (gas: 5113791)
CCIPHome_revokeCandidate:test_revokeCandidate() (gas: 30647)
CCIPHome_setCandidate:test_setCandidate() (gas: 1365392)
CCIPHome_supportsInterface:test_supportsInterface() (gas: 9885)
CCTPMessageTransmitterProxy_configureAllowedCallers:test_configureAllowedCallers() (gas: 65308)
CCTPMessageTransmitterProxy_getAllowedCallers:test_configureAllowedCallers() (gas: 75076)
CCTPMessageTransmitterProxy_getCCTPTransmitter:test_getCCTPTransmitter() (gas: 10536)
CCTPMessageTransmitterProxy_receiveMesssage:test_receiveMesssage() (gas: 31304)
DefensiveExampleTest:test_HappyPath() (gas: 200535)
DefensiveExampleTest:test_Recovery() (gas: 424996)
DonIDClaimerTest:test_ClaimMultipleDONIds() (gas: 44547)
DonIDClaimerTest:test_ClaimNextDONId() (gas: 24388)
DonIDClaimerTest:test_Constructor() (gas: 635695)
DonIDClaimerTest:test_RevokeThenReauthorizeDeployer() (gas: 31507)
DonIDClaimerTest:test_SetAuthorizedDeployer() (gas: 70028)
DonIDClaimerTest:test_SetAuthorizedDeployerRevoked() (gas: 26503)
DonIDClaimerTest:test_SyncNextDONIdWithOffset() (gas: 27741)
E2E:test_E2E_3MessagesMMultiOffRampSuccess_gas() (gas: 1520825)
ERC165CheckerReverting_supportsInterfaceReverting:test__supportsInterfaceReverting() (gas: 10517)
EtherSenderReceiverTest_ccipReceive:test_ccipReceive_fallbackToWethTransfer() (gas: 96964)
EtherSenderReceiverTest_ccipReceive:test_ccipReceive_happyPath() (gas: 49797)
EtherSenderReceiverTest_ccipReceive:test_ccipReceive_wrongToken() (gas: 17460)
EtherSenderReceiverTest_ccipReceive:test_ccipReceive_wrongTokenAmount() (gas: 15748)
EtherSenderReceiverTest_ccipSend:test_ccipSend_feeToken() (gas: 145087)
EtherSenderReceiverTest_ccipSend:test_ccipSend_native() (gas: 80451)
EtherSenderReceiverTest_ccipSend:test_ccipSend_nativeExcess() (gas: 80616)
EtherSenderReceiverTest_ccipSend:test_ccipSend_weth() (gas: 96167)
EtherSenderReceiverTest_constructor:test_constructor() (gas: 17582)
EtherSenderReceiverTest_getFee:test_getFee() (gas: 27482)
EtherSenderReceiverTest_validateFeeToken:test_validateFeeToken_valid_feeToken() (gas: 16700)
EtherSenderReceiverTest_validateFeeToken:test_validateFeeToken_valid_native() (gas: 16611)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_dataOverwrittenToMsgSender() (gas: 25455)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_emptyDataOverwrittenToMsgSender() (gas: 25372)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_invalidTokenAmounts() (gas: 17955)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_tokenOverwrittenToWeth() (gas: 25327)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_validMessage_extraArgs() (gas: 26347)
FactoryBurnMintERC20_approve:test_Approve() (gas: 55786)
FactoryBurnMintERC20_burn:test_BasicBurn() (gas: 172448)
FactoryBurnMintERC20_burnFrom:test_BurnFrom() (gas: 58290)
FactoryBurnMintERC20_burnFromAlias:test_BurnFrom() (gas: 58264)
FactoryBurnMintERC20_constructor:test_Constructor() (gas: 1450297)
FactoryBurnMintERC20_decreaseApproval:test_DecreaseApproval() (gas: 31420)
FactoryBurnMintERC20_getCCIPAdmin:test_getCCIPAdmin() (gas: 12740)
FactoryBurnMintERC20_getCCIPAdmin:test_setCCIPAdmin() (gas: 23852)
FactoryBurnMintERC20_grantMintAndBurnRoles:test_GrantMintAndBurnRoles() (gas: 121216)
FactoryBurnMintERC20_grantRole:test_GrantBurnAccess() (gas: 53386)
FactoryBurnMintERC20_grantRole:test_GrantMany() (gas: 961708)
FactoryBurnMintERC20_grantRole:test_GrantMintAccess() (gas: 94181)
FactoryBurnMintERC20_increaseApproval:test_IncreaseApproval() (gas: 44421)
FactoryBurnMintERC20_mint:test_BasicMint() (gas: 149826)
FactoryBurnMintERC20_supportsInterface:test_SupportsInterface() (gas: 11539)
FactoryBurnMintERC20_transfer:test_Transfer() (gas: 42505)
FeeQuoter_applyDestChainConfigUpdates:test_applyDestChainConfigUpdates() (gas: 229374)
FeeQuoter_applyDestChainConfigUpdates:test_applyDestChainConfigUpdatesZeroInput() (gas: 12426)
FeeQuoter_applyFeeTokensUpdates:test_ApplyFeeTokensUpdates() (gas: 162691)
FeeQuoter_applyPremiumMultiplierWeiPerEthUpdates:test_applyPremiumMultiplierWeiPerEthUpdatesMultipleTokens() (gas: 54925)
FeeQuoter_applyPremiumMultiplierWeiPerEthUpdates:test_applyPremiumMultiplierWeiPerEthUpdatesSingleToken() (gas: 45386)
FeeQuoter_applyPremiumMultiplierWeiPerEthUpdates:test_applyPremiumMultiplierWeiPerEthUpdatesZeroInput() (gas: 12468)
FeeQuoter_applyTokenTransferFeeConfigUpdates:test_ApplyTokenTransferFeeConfig() (gas: 88868)
FeeQuoter_applyTokenTransferFeeConfigUpdates:test_ApplyTokenTransferFeeZeroInput() (gas: 13240)
FeeQuoter_constructor:test_Setup() (gas: 5674570)
FeeQuoter_convertTokenAmount:test_ConvertTokenAmount() (gas: 68417)
FeeQuoter_getDataAvailabilityCost:test_EmptyMessageCalculatesDataAvailabilityCost() (gas: 98943)
FeeQuoter_getDataAvailabilityCost:test_SimpleMessageCalculatesDataAvailabilityCost() (gas: 21505)
FeeQuoter_getDataAvailabilityCost:test_SimpleMessageCalculatesDataAvailabilityCostUnsupportedDestChainSelector() (gas: 14860)
FeeQuoter_getTokenAndGasPrices:test_GetFeeTokenAndGasPrices() (gas: 73123)
FeeQuoter_getTokenAndGasPrices:test_StalenessCheckDisabled() (gas: 113569)
FeeQuoter_getTokenAndGasPrices:test_ZeroGasPrice() (gas: 110656)
FeeQuoter_getTokenPrice:test_GetTokenPriceFromFeed() (gas: 68180)
FeeQuoter_getTokenPrice:test_GetTokenPrice_LocalMoreRecent() (gas: 33502)
FeeQuoter_getTokenPrices:test_GetTokenPrices() (gas: 78578)
FeeQuoter_getTokenTransferCost:test_CustomTokenBpsFee() (gas: 34596)
FeeQuoter_getTokenTransferCost:test_FeeTokenBpsFee() (gas: 32375)
FeeQuoter_getTokenTransferCost:test_LargeTokenTransferChargesMaxFeeAndGas() (gas: 25445)
FeeQuoter_getTokenTransferCost:test_MixedTokenTransferFee() (gas: 91800)
FeeQuoter_getTokenTransferCost:test_NoTokenTransferChargesZeroFee() (gas: 17840)
FeeQuoter_getTokenTransferCost:test_SmallTokenTransferChargesMinFeeAndGas() (gas: 25252)
FeeQuoter_getTokenTransferCost:test_ZeroAmountTokenTransferChargesMinFeeAndGas() (gas: 25275)
FeeQuoter_getTokenTransferCost:test_ZeroFeeConfigChargesMinFee() (gas: 37833)
FeeQuoter_getTokenTransferCost:test_getTokenTransferCost_selfServeUsesDefaults() (gas: 26969)
FeeQuoter_getValidatedFee:test_getValidatedFee_Aptos() (gas: 60473)
FeeQuoter_getValidatedFee:test_getValidatedFee_EmptyMessage() (gas: 85619)
FeeQuoter_getValidatedFee:test_getValidatedFee_HighGasMessage() (gas: 243514)
FeeQuoter_getValidatedFee:test_getValidatedFee_MessageWithDataAndTokenTransfer() (gas: 144401)
FeeQuoter_getValidatedFee:test_getValidatedFee_SVM() (gas: 62422)
FeeQuoter_getValidatedFee:test_getValidatedFee_SingleTokenMessage() (gas: 115795)
FeeQuoter_getValidatedFee:test_getValidatedFee_ZeroDataAvailabilityMultiplier() (gas: 66576)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPrice() (gas: 58838)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeed() (gas: 65048)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedErc20Above18Decimals() (gas: 1897657)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedErc20Below18Decimals() (gas: 1897699)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedFeedAt0Decimals() (gas: 1877755)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedFeedAt18Decimals() (gas: 1897497)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedFlippedDecimals() (gas: 1897633)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedMaxInt224Value() (gas: 1897467)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedOverStalenessPeriod() (gas: 65166)
FeeQuoter_getValidatedTokenPrice:test_StaleFeeToken() (gas: 61787)
FeeQuoter_onReport:test_OnReport_SkipPriceUpdateWhenStaleUpdateReceived() (gas: 52653)
FeeQuoter_onReport:test_onReport() (gas: 88986)
FeeQuoter_onReport:test_onReport_withKeystoneForwarderContract() (gas: 122680)
FeeQuoter_parseSVMExtraArgsFromBytes:test_SVMExtraArgsV1() (gas: 23277)
FeeQuoter_parseSVMExtraArgsFromBytes:test_SVMExtraArgsV1TagSelector() (gas: 3157)
FeeQuoter_processChainFamilySelector:test_processChainFamilySelector_Aptos() (gas: 22716)
FeeQuoter_processChainFamilySelector:test_processChainFamilySelector_EVM() (gas: 22640)
FeeQuoter_processChainFamilySelector:test_processChainFamilySelector_SVM_NoTokenTransfer() (gas: 24235)
FeeQuoter_processChainFamilySelector:test_processChainFamilySelector_SVM_WithTokenTransfer() (gas: 25411)
FeeQuoter_processMessageArgs:test_processMessageArgs_WitEVMExtraArgsV2() (gas: 28782)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithConvertedTokenAmount() (gas: 32783)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithEVMExtraArgsV1() (gas: 28413)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithEmptyEVMExtraArgs() (gas: 26286)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithLinkTokenAmount() (gas: 22463)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithSVMExtraArgsV1() (gas: 63166)
FeeQuoter_processPoolReturnData:test_processPoolReturnData() (gas: 73431)
FeeQuoter_resolveGasLimitForDestination:test_EVMExtraArgsDefault() (gas: 17135)
FeeQuoter_resolveGasLimitForDestination:test_EVMExtraArgsV1() (gas: 16202)
FeeQuoter_resolveGasLimitForDestination:test_EVMExtraArgsV1TagSelector() (gas: 3169)
FeeQuoter_resolveGasLimitForDestination:test_EVMExtraArgsV2() (gas: 16328)
FeeQuoter_resolveGasLimitForDestination:test_EVMExtraArgsV2TagSelector() (gas: 3168)
FeeQuoter_supportsInterface:test_SupportsInterface() (gas: 13352)
FeeQuoter_updatePrices:test_OnlyGasPrice() (gas: 23868)
FeeQuoter_updatePrices:test_OnlyTokenPrice() (gas: 28673)
FeeQuoter_updatePrices:test_UpdatableByAuthorizedCaller() (gas: 74645)
FeeQuoter_updatePrices:test_UpdateMultiplePrices() (gas: 145958)
FeeQuoter_updateTokenPriceFeeds:test_FeedNotUpdated() (gas: 52495)
FeeQuoter_updateTokenPriceFeeds:test_FeedUnset() (gas: 66418)
FeeQuoter_updateTokenPriceFeeds:test_MultipleFeedUpdate() (gas: 93559)
FeeQuoter_updateTokenPriceFeeds:test_SingleFeedUpdate() (gas: 53171)
FeeQuoter_updateTokenPriceFeeds:test_ZeroFeeds() (gas: 12471)
FeeQuoter_validateDestFamilyAddress:test_validateDestFamilyAddress_Aptos() (gas: 6903)
FeeQuoter_validateDestFamilyAddress:test_validateDestFamilyAddress_EVMs() (gas: 6839)
FeeQuoter_validateDestFamilyAddress:test_validateDestFamilyAddress_SVM() (gas: 6780)
HybridLockReleaseUSDCTokenPool_lockOrBurn:test_PrimaryMechanism() (gas: 130321)
HybridLockReleaseUSDCTokenPool_lockOrBurn:test_onLockReleaseMechanism() (gas: 140191)
HybridLockReleaseUSDCTokenPool_lockOrBurn:test_onLockReleaseMechanism_thenSwitchToPrimary() (gas: 202932)
HybridLockReleaseUSDCTokenPool_releaseOrMint:test_OnLockReleaseMechanism() (gas: 206305)
HybridLockReleaseUSDCTokenPool_releaseOrMint:test_incomingMessageWithPrimaryMechanism() (gas: 265499)
HybridLockReleaseUSDCTokenPool_releaseOrMint_E2ETest:test_releaseOrMint_E2E() (gas: 374077)
LockReleaseTokenPool_canAcceptLiquidity:test_CanAcceptLiquidity() (gas: 3222607)
LockReleaseTokenPool_lockOrBurn:test_LockOrBurnWithAllowList() (gas: 72828)
LockReleaseTokenPool_releaseOrMint:test_ReleaseOrMint() (gas: 217898)
LockReleaseTokenPool_setRebalancer:test_SetRebalancer() (gas: 18183)
LockReleaseTokenPool_supportsInterface:test_SupportsInterface() (gas: 10251)
LockReleaseTokenPool_transferLiquidity:test_transferLiquidity() (gas: 83263)
MerkleMultiProofTest:test_CVE_2023_34459() (gas: 5456)
MerkleMultiProofTest:test_MerkleRoot256() (gas: 396915)
MerkleMultiProofTest:test_MerkleRootSingleLeaf() (gas: 3684)
MerkleMultiProofTest:test_SpecSync_gas() (gas: 34152)
MockRouterTest:test_ccipSendWithEVMExtraArgsV1() (gas: 110081)
MockRouterTest:test_ccipSendWithEVMExtraArgsV2() (gas: 132594)
MockRouterTest:test_ccipSendWithLinkFeeTokenAndValidMsgValue() (gas: 126679)
MockRouterTest:test_ccipSendWithSufficientNativeFeeTokens() (gas: 44038)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_MultipleConfigs() (gas: 317373)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_MultipleConfigsBothLanes() (gas: 134278)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_SingleConfig() (gas: 76755)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_SingleConfigOutbound() (gas: 76797)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_UpdateExistingConfig() (gas: 54084)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_UpdateExistingConfigWithNoDifference() (gas: 38924)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_ZeroConfigs() (gas: 12505)
MultiAggregateRateLimiter_constructor:test_Constructor() (gas: 2100225)
MultiAggregateRateLimiter_constructor:test_ConstructorNoAuthorizedCallers() (gas: 1984394)
MultiAggregateRateLimiter_getTokenBucket:test_GetTokenBucket() (gas: 30888)
MultiAggregateRateLimiter_getTokenBucket:test_Refill() (gas: 48378)
MultiAggregateRateLimiter_getTokenValue:test_GetTokenValue() (gas: 17594)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithDifferentTokensOnDifferentChains() (gas: 211396)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithDisabledRateLimitToken() (gas: 58810)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithNoTokens() (gas: 17918)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithRateLimitDisabled() (gas: 45460)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithRateLimitReset() (gas: 77677)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithTokens() (gas: 50923)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithTokensOnDifferentChains() (gas: 309989)
MultiAggregateRateLimiter_onOutboundMessage:test_RateLimitValueDifferentLanes() (gas: 51567)
MultiAggregateRateLimiter_onOutboundMessage:test_ValidateMessageWithNoTokens() (gas: 19379)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithDifferentTokensOnDifferentChains() (gas: 210948)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithDisabledRateLimitToken() (gas: 60522)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithRateLimitDisabled() (gas: 47112)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithRateLimitReset() (gas: 78418)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithTokens() (gas: 52633)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithTokensOnDifferentChains() (gas: 309747)
MultiAggregateRateLimiter_updateRateLimitTokens:test_UpdateRateLimitTokensMultipleChains() (gas: 281364)
MultiAggregateRateLimiter_updateRateLimitTokens:test_UpdateRateLimitTokensSingleChain() (gas: 255770)
MultiAggregateRateLimiter_updateRateLimitTokens:test_UpdateRateLimitTokens_AddsAndRemoves() (gas: 205543)
MultiAggregateRateLimiter_updateRateLimitTokens:test_UpdateRateLimitTokens_RemoveNonExistentToken() (gas: 29151)
MultiOCR3Base_setOCR3Configs:test_SetConfigIgnoreSigners() (gas: 512335)
MultiOCR3Base_setOCR3Configs:test_SetConfigWithSigners() (gas: 829238)
MultiOCR3Base_setOCR3Configs:test_SetConfigWithSignersMismatchingTransmitters() (gas: 680660)
MultiOCR3Base_setOCR3Configs:test_SetConfigWithoutSigners() (gas: 457485)
MultiOCR3Base_setOCR3Configs:test_SetConfigsZeroInput() (gas: 12437)
MultiOCR3Base_setOCR3Configs:test_SetMultipleConfigs() (gas: 2142785)
MultiOCR3Base_setOCR3Configs:test_UpdateConfigSigners() (gas: 861909)
MultiOCR3Base_setOCR3Configs:test_UpdateConfigTransmittersWithoutSigners() (gas: 476109)
MultiOCR3Base_transmit:test_TransmitSigners_gas() (gas: 33559)
MultiOCR3Base_transmit:test_TransmitWithoutSignatureVerification_gas() (gas: 18638)
NonceManager_applyPreviousRampsUpdates:test_MultipleRampsUpdates() (gas: 123617)
NonceManager_applyPreviousRampsUpdates:test_PreviousRampAlreadySet_overrideAllowed() (gas: 45935)
NonceManager_applyPreviousRampsUpdates:test_SingleRampUpdate() (gas: 66937)
NonceManager_applyPreviousRampsUpdates:test_ZeroInput() (gas: 12123)
NonceManager_getInboundNonce:test_getInboundNonce_NoPrevOffRampForChain() (gas: 183800)
NonceManager_getInboundNonce:test_getInboundNonce_Upgraded() (gas: 150988)
NonceManager_getInboundNonce:test_getInboundNonce_UpgradedNonceNewSenderStartsAtZero() (gas: 187293)
NonceManager_getInboundNonce:test_getInboundNonce_UpgradedNonceStartsAtV1Nonce() (gas: 254946)
NonceManager_getInboundNonce:test_getInboundNonce_UpgradedOffRampNonceSkipsIfMsgInFlight() (gas: 218982)
NonceManager_getInboundNonce:test_getInboundNonce_UpgradedSenderNoncesReadsPreviousRamp() (gas: 60396)
NonceManager_getIncrementedOutboundNonce:test_getIncrementedOutboundNonce() (gas: 37974)
NonceManager_getIncrementedOutboundNonce:test_incrementInboundNonce() (gas: 38746)
NonceManager_getIncrementedOutboundNonce:test_incrementInboundNonce_SkippedIncorrectNonce() (gas: 23739)
NonceManager_getIncrementedOutboundNonce:test_incrementNoncesInboundAndOutbound() (gas: 71886)
NonceManager_getOutboundNonce:test_getOutboundNonce_Upgrade() (gas: 113957)
NonceManager_getOutboundNonce:test_getOutboundNonce_UpgradeNonceNewSenderStartsAtZero() (gas: 178713)
NonceManager_getOutboundNonce:test_getOutboundNonce_UpgradeNonceStartsAtV1Nonce() (gas: 217308)
NonceManager_getOutboundNonce:test_getOutboundNonce_UpgradeSenderNoncesReadsPreviousRamp() (gas: 154048)
OffRampWithMessageTransformer_executeSingleReport:test_executeSingleReport() (gas: 307163)
OffRampWithMessageTransformer_setMessageTransformer:test_setMessageTransformer() (gas: 701156)
OffRamp_applySourceChainConfigUpdates:test_AddMultipleChains() (gas: 629252)
OffRamp_applySourceChainConfigUpdates:test_AddNewChain() (gas: 167412)
OffRamp_applySourceChainConfigUpdates:test_ApplyZeroUpdates() (gas: 16647)
OffRamp_applySourceChainConfigUpdates:test_ReplaceExistingChain() (gas: 182429)
OffRamp_applySourceChainConfigUpdates:test_ReplaceExistingChainOnRamp() (gas: 169667)
OffRamp_applySourceChainConfigUpdates:test_allowNonOnRampUpdateAfterLaneIsUsed() (gas: 285940)
OffRamp_batchExecute:test_MultipleReportsDifferentChains() (gas: 340753)
OffRamp_batchExecute:test_MultipleReportsDifferentChainsSkipCursedChain() (gas: 175818)
OffRamp_batchExecute:test_MultipleReportsSameChain() (gas: 284041)
OffRamp_batchExecute:test_MultipleReportsSkipDuplicate() (gas: 166960)
OffRamp_batchExecute:test_SingleReport() (gas: 154466)
OffRamp_batchExecute:test_Unhealthy() (gas: 546691)
OffRamp_commit:test_OnlyGasPriceUpdates() (gas: 114706)
OffRamp_commit:test_OnlyTokenPriceUpdates() (gas: 114638)
OffRamp_commit:test_PriceSequenceNumberCleared() (gas: 358677)
OffRamp_commit:test_ReportAndPriceUpdate() (gas: 166998)
OffRamp_commit:test_ReportOnlyRootSuccess_gas() (gas: 141950)
OffRamp_commit:test_RootWithRMNDisabled() (gas: 159871)
OffRamp_commit:test_StaleReportWithRoot() (gas: 237218)
OffRamp_commit:test_ValidPriceUpdateThenStaleReportWithRoot() (gas: 211061)
OffRamp_constructor:test_Constructor() (gas: 6390407)
OffRamp_execute:test_LargeBatch() (gas: 3537164)
OffRamp_execute:test_MultipleReports() (gas: 306127)
OffRamp_execute:test_MultipleReportsWithPartialValidationFailures() (gas: 369525)
OffRamp_execute:test_SingleReport() (gas: 173680)
OffRamp_executeSingleMessage:test_executeSingleMessage_NoTokens() (gas: 55466)
OffRamp_executeSingleMessage:test_executeSingleMessage_NonContract() (gas: 20470)
OffRamp_executeSingleMessage:test_executeSingleMessage_NonContractWithTokens() (gas: 230396)
OffRamp_executeSingleMessage:test_executeSingleMessage_WithMessageInterceptor() (gas: 90556)
OffRamp_executeSingleMessage:test_executeSingleMessage_WithTokens() (gas: 265188)
OffRamp_executeSingleReport:test_InvalidSourcePoolAddress() (gas: 462330)
OffRamp_executeSingleReport:test_ReceiverError() (gas: 181139)
OffRamp_executeSingleReport:test_SingleMessageNoTokens() (gas: 215123)
OffRamp_executeSingleReport:test_SingleMessageNoTokensOtherChain() (gas: 249635)
OffRamp_executeSingleReport:test_SingleMessageNoTokensUnordered() (gas: 195098)
OffRamp_executeSingleReport:test_SingleMessageToNonCCIPReceiver() (gas: 244226)
OffRamp_executeSingleReport:test_SingleMessagesNoTokensSuccess_gas() (gas: 139549)
OffRamp_executeSingleReport:test_SkippedIncorrectNonce() (gas: 58625)
OffRamp_executeSingleReport:test_SkippedIncorrectNonceStillExecutes() (gas: 399329)
OffRamp_executeSingleReport:test_TwoMessagesWithTokensAndGE() (gas: 575812)
OffRamp_executeSingleReport:test_TwoMessagesWithTokensSuccess_gas() (gas: 524300)
OffRamp_executeSingleReport:test_Unhealthy() (gas: 542445)
OffRamp_executeSingleReport:test_WithCurseOnAnotherSourceChain() (gas: 450406)
OffRamp_executeSingleReport:test__execute_SkippedAlreadyExecutedMessage() (gas: 163261)
OffRamp_executeSingleReport:test__execute_SkippedAlreadyExecutedMessageUnordered() (gas: 133646)
OffRamp_getExecutionState:test_FillExecutionState() (gas: 3938744)
OffRamp_getExecutionState:test_GetDifferentChainExecutionState() (gas: 121157)
OffRamp_getExecutionState:test_GetExecutionState() (gas: 89992)
OffRamp_manuallyExecute:test_manuallyExecute() (gas: 212654)
OffRamp_manuallyExecute:test_manuallyExecute_DoesNotRevertIfUntouched() (gas: 165998)
OffRamp_manuallyExecute:test_manuallyExecute_LowGasLimit() (gas: 479604)
OffRamp_manuallyExecute:test_manuallyExecute_ReentrancyFails() (gas: 2230097)
OffRamp_manuallyExecute:test_manuallyExecute_WithGasOverride() (gas: 213226)
OffRamp_manuallyExecute:test_manuallyExecute_WithMultiReportGasOverride() (gas: 753517)
OffRamp_manuallyExecute:test_manuallyExecute_WithPartialMessages() (gas: 347154)
OffRamp_releaseOrMintSingleToken:test__releaseOrMintSingleToken() (gas: 94629)
OffRamp_releaseOrMintTokens:test_releaseOrMintTokens() (gas: 161157)
OffRamp_releaseOrMintTokens:test_releaseOrMintTokens_WithGasOverride() (gas: 163001)
OffRamp_releaseOrMintTokens:test_releaseOrMintTokens_destDenominatedDecimals() (gas: 174254)
OffRamp_setDynamicConfig:test_SetDynamicConfig() (gas: 24372)
OffRamp_setDynamicConfig:test_SetDynamicConfigWithInterceptor() (gas: 46421)
OffRamp_trialExecute:test_trialExecute() (gas: 268978)
OffRamp_trialExecute:test_trialExecute_RateLimitError() (gas: 120733)
OffRamp_trialExecute:test_trialExecute_RevertsWhen_SenderIsGasEstimator_InsufficientGasForToCompleteTx() (gas: 67421)
OffRamp_trialExecute:test_trialExecute_SenderIsNotGasEstimator_CallWithExactGasReverts() (gas: 24640)
OffRamp_trialExecute:test_trialExecute_TokenHandlingErrorIsCaught() (gas: 132021)
OffRamp_trialExecute:test_trialExecute_TokenPoolIsNotAContract() (gas: 286715)
OnRampTokenPoolReentrancy:test_OnRampTokenPoolReentrancy() (gas: 252502)
OnRampWithMessageTransformer_executeSingleMessage:test_forwardFromRouter() (gas: 130493)
OnRampWithMessageTransformer_setMessageTransformer:test_setMessageTransformer() (gas: 701204)
OnRamp_applyAllowlistUpdates:test_applyAllowlistUpdates() (gas: 325996)
OnRamp_applyAllowlistUpdates:test_applyAllowlistUpdates_InvalidAllowListRequestDisabledAllowListWithAdds() (gas: 17190)
OnRamp_applyDestChainConfigUpdates:test_ApplyDestChainConfigUpdates() (gas: 65874)
OnRamp_constructor:test_Constructor() (gas: 2723399)
OnRamp_forwardFromRouter:test_ForwardFromRouter() (gas: 154087)
OnRamp_forwardFromRouter:test_ForwardFromRouterExtraArgsV2() (gas: 154922)
OnRamp_forwardFromRouter:test_ForwardFromRouterExtraArgsV2AllowOutOfOrderTrue() (gas: 124123)
OnRamp_forwardFromRouter:test_ForwardFromRouterSuccessCustomExtraArgs() (gas: 154486)
OnRamp_forwardFromRouter:test_ForwardFromRouterSuccessEmptyExtraArgs() (gas: 152741)
OnRamp_forwardFromRouter:test_ForwardFromRouterSuccessLegacyExtraArgs() (gas: 154727)
OnRamp_forwardFromRouter:test_ForwardFromRouter_ConfigurableSourceRouter() (gas: 146563)
OnRamp_forwardFromRouter:test_ForwardFromRouter_EVM_WithTokenTransfer() (gas: 235234)
OnRamp_forwardFromRouter:test_ForwardFromRouter_SVM_WithTokenTransfer() (gas: 219241)
OnRamp_forwardFromRouter:test_ShouldIncrementNonceOnlyOnOrdered() (gas: 213075)
OnRamp_forwardFromRouter:test_ShouldIncrementSeqNumAndNonce() (gas: 239453)
OnRamp_forwardFromRouter:test_ShouldStoreLinkFees() (gas: 155732)
OnRamp_forwardFromRouter:test_forwardFromRouter_WithInterception() (gas: 283126)
OnRamp_getFee:test_EmptyMessage() (gas: 101181)
OnRamp_getFee:test_GetFeeOfZeroForTokenMessage() (gas: 89847)
OnRamp_getFee:test_SingleTokenMessage() (gas: 116499)
OnRamp_getTokenPool:test_GetTokenPool() (gas: 35404)
OnRamp_setDynamicConfig:test_setDynamicConfig() (gas: 56650)
OnRamp_withdrawFeeTokens:test_WithdrawFeeTokens() (gas: 125835)
PingPong_ccipReceive:test_CcipReceive() (gas: 172822)
PingPong_setOutOfOrderExecution:test_OutOfOrderExecution() (gas: 20372)
PingPong_setPaused:test_Pausing() (gas: 17766)
PingPong_startPingPong:test_StartPingPong_With_OOO() (gas: 152116)
PingPong_startPingPong:test_StartPingPong_With_Sequenced_Ordered() (gas: 177703)
RMNHome_getConfigDigests:test_getConfigDigests() (gas: 1081176)
RMNHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive() (gas: 1086556)
RMNHome_revokeCandidate:test_revokeCandidate() (gas: 28085)
RMNHome_setCandidate:test_setCandidate() (gas: 590250)
RMNHome_setDynamicConfig:test_setDynamicConfig() (gas: 105498)
RMNProxy_constructor:test_Constructor() (gas: 302031)
RMNProxy_isCursed:test_IsCursed_GlobalCurseSubject() (gas: 89809)
RMNProxy_setARM:test_SetARM() (gas: 16599)
RMNProxy_setARM:test_SetARMzero() (gas: 11275)
RMNRemote_constructor:test_constructor() (gas: 8410)
RMNRemote_curse:test_curse() (gas: 149422)
RMNRemote_global_curses:test_isCursed_globalCurseSubject() (gas: 71707)
RMNRemote_isBlessed:test_isBlessed() (gas: 17588)
RMNRemote_setConfig:test_setConfig_addSigner_removeSigner() (gas: 994169)
RMNRemote_uncurse:test_uncurse() (gas: 40136)
RMNRemote_verify_withConfigSet:test_verify() (gas: 86470)
RateLimiter_constructor:test_Constructor() (gas: 19820)
RateLimiter_consume:test_ConsumeAggregateValue() (gas: 31633)
RateLimiter_consume:test_ConsumeTokens() (gas: 20369)
RateLimiter_consume:test_ConsumeUnlimited() (gas: 40923)
RateLimiter_consume:test_Refill() (gas: 37562)
RateLimiter_currentTokenBucketState:test_CurrentTokenBucketState() (gas: 39126)
RateLimiter_currentTokenBucketState:test_Refill() (gas: 47182)
RateLimiter_setTokenBucketConfig:test_SetRateLimiterConfig() (gas: 38645)
RegistryModuleOwnerCustom_registerAccessControlDefaultAdmin:test_registerAccessControlDefaultAdmin() (gas: 130641)
RegistryModuleOwnerCustom_registerAdminViaGetCCIPAdmin:test_registerAdminViaGetCCIPAdmin() (gas: 130136)
RegistryModuleOwnerCustom_registerAdminViaOwner:test_registerAdminViaOwner() (gas: 129941)
Router_applyRampUpdates:test_applyRampUpdates_OffRampUpdatesWithRouting() (gas: ********)
Router_applyRampUpdates:test_applyRampUpdates_OnRampDisable() (gas: 56445)
Router_ccipSend:test_CCIPSendLinkFeeNoTokenSuccess_gas() (gas: 128906)
Router_ccipSend:test_CCIPSendLinkFeeOneTokenSuccess_gas() (gas: 215974)
Router_ccipSend:test_InvalidMsgValue() (gas: 27856)
Router_ccipSend:test_NativeFeeToken() (gas: 189944)
Router_ccipSend:test_NativeFeeTokenInsufficientValue() (gas: 62959)
Router_ccipSend:test_NativeFeeTokenOverpay() (gas: 191339)
Router_ccipSend:test_NativeFeeTokenZeroValue() (gas: 55051)
Router_ccipSend:test_NonLinkFeeToken() (gas: 224030)
Router_ccipSend:test_WrappedNativeFeeToken() (gas: 192161)
Router_ccipSend:test_ccipSend_nativeFeeNoTokenSuccess_gas() (gas: 138063)
Router_ccipSend:test_ccipSend_nativeFeeOneTokenSuccess_gas() (gas: 225174)
Router_constructor:test_Constructor() (gas: 13170)
Router_getArmProxy:test_getArmProxy() (gas: 10573)
Router_getFee:test_GetFeeSupportedChain() (gas: 52630)
Router_recoverTokens:test_RecoverTokens() (gas: 52686)
Router_routeMessage:test_routeMessage_AutoExec() (gas: 41838)
Router_routeMessage:test_routeMessage_ExecutionEvent() (gas: 157241)
Router_routeMessage:test_routeMessage_ManualExec() (gas: 34884)
SiloedLockReleaseTokenPool_getAvailableTokens:test_getAvailableTokens_SiloedChain() (gas: 75798)
SiloedLockReleaseTokenPool_getAvailableTokens:test_getAvailableTokens_UnsiloedChain() (gas: 79119)
SiloedLockReleaseTokenPool_lockOrBurn:test_lockOrBurn_SiloedFunds() (gas: 77195)
SiloedLockReleaseTokenPool_lockOrBurn:test_lockOrBurn_UnsiloedFunds() (gas: 76425)
SiloedLockReleaseTokenPool_provideLiquidity:test_provideLiquidity() (gas: 94247)
SiloedLockReleaseTokenPool_provideSiloedLiquidity:test_provideSiloedLiquidity() (gas: 84925)
SiloedLockReleaseTokenPool_releaseOrMint:test_ReleaseOrMint_RevertsWhen_InsufficientLiquidity_SiloedChain() (gas: 110216)
SiloedLockReleaseTokenPool_releaseOrMint:test_ReleaseOrMint_RevertsWhen_InsufficientLiquidity_UnsiloedChain() (gas: 113535)
SiloedLockReleaseTokenPool_releaseOrMint:test_ReleaseOrMint_SiloedChain() (gas: 262739)
SiloedLockReleaseTokenPool_releaseOrMint:test_ReleaseOrMint_UnsiloedChain() (gas: 263827)
SiloedLockReleaseTokenPool_setRebalancer:test_setRebalancer_UnsiloedChains() (gas: 23652)
SiloedLockReleaseTokenPool_setRebalancer:test_setSiloRebalancer() (gas: 27421)
SiloedLockReleaseTokenPool_updateSiloDesignations:test_updateSiloDesignations() (gas: 139625)
SiloedLockReleaseTokenPool_withdrawLiqudity:test_withdrawLiquidity_SiloedFunds() (gas: 73138)
SiloedLockReleaseTokenPool_withdrawLiqudity:test_withdrawLiquidity_UnsiloedFunds_LegacyFunctionSelector() (gas: 74648)
TokenAdminRegistry_acceptAdminRole:test_acceptAdminRole() (gas: 44236)
TokenAdminRegistry_addRegistryModule:test_addRegistryModule() (gas: 67093)
TokenAdminRegistry_getAllConfiguredTokens:test_getAllConfiguredTokens_outOfBounds() (gas: 11363)
TokenAdminRegistry_getPool:test_getPool() (gas: 17679)
TokenAdminRegistry_getPools:test_getPools() (gas: 40271)
TokenAdminRegistry_isAdministrator:test_isAdministrator() (gas: 106335)
TokenAdminRegistry_proposeAdministrator:test_proposeAdministrator_module() (gas: 113043)
TokenAdminRegistry_proposeAdministrator:test_proposeAdministrator_owner() (gas: 107992)
TokenAdminRegistry_proposeAdministrator:test_proposeAdministrator_reRegisterWhileUnclaimed() (gas: 116200)
TokenAdminRegistry_removeRegistryModule:test_removeRegistryModule() (gas: 54757)
TokenAdminRegistry_setPool:test_setPool() (gas: 36207)
TokenAdminRegistry_setPool:test_setPool_ZeroAddressRemovesPool() (gas: 30852)
TokenAdminRegistry_transferAdminRole:test_transferAdminRole() (gas: 49558)
TokenPoolFactory_createTokenPool:test_createTokenPoolLockRelease_ExistingToken_predict() (gas: 12646379)
TokenPoolFactory_createTokenPool:test_createTokenPool_BurnFromMintTokenPool() (gas: 6526762)
TokenPoolFactory_createTokenPool:test_createTokenPool_ExistingRemoteToken_AndPredictPool() (gas: 13480657)
TokenPoolFactory_createTokenPool:test_createTokenPool_RemoteTokenHasDifferentDecimals() (gas: 13488141)
TokenPoolFactory_createTokenPool:test_createTokenPool_WithNoExistingRemoteContracts_predict() (gas: 13819502)
TokenPoolFactory_createTokenPool:test_createTokenPool_WithNoExistingTokenOnRemoteChain() (gas: 6315855)
TokenPoolFactory_createTokenPool:test_createTokenPool_WithRemoteTokenAndRemotePool() (gas: 6523352)
TokenPoolWithAllowList_applyAllowListUpdates:test_SetAllowList() (gas: 178482)
TokenPoolWithAllowList_applyAllowListUpdates:test_SetAllowListSkipsZero() (gas: 23580)
TokenPoolWithAllowList_getAllowList:test_GetAllowList() (gas: 23908)
TokenPoolWithAllowList_getAllowListEnabled:test_GetAllowListEnabled() (gas: 8386)
TokenPoolWithAllowList_setRouter:test_SetRouter() (gas: 24994)
TokenPool_addRemotePool:test_addRemotePool() (gas: 157121)
TokenPool_addRemotePool:test_addRemotePool_MultipleActive() (gas: 453937)
TokenPool_applyChainUpdates:test_applyChainUpdates() (gas: 592354)
TokenPool_applyChainUpdates:test_applyChainUpdates_UpdatesRemotePoolHashes() (gas: 1077690)
TokenPool_calculateLocalAmount:test_calculateLocalAmount() (gas: 93680)
TokenPool_constructor:test_constructor() (gas: 21930)
TokenPool_constructor:test_constructor_DecimalCallFails() (gas: 2836717)
TokenPool_getRemotePool:test_getRemotePools() (gas: 330476)
TokenPool_onlyOffRamp:test_onlyOffRamp() (gas: 94348)
TokenPool_onlyOnRamp:test_onlyOnRamp() (gas: 49257)
TokenPool_parseRemoteDecimals:test_parseRemoteDecimals() (gas: 14030)
TokenPool_parseRemoteDecimals:test_parseRemoteDecimals_NoDecimalsDefaultsToLocalDecimals() (gas: 9705)
TokenPool_removeRemotePool:test_removeRemotePool() (gas: 188402)
TokenPool_setRateLimitAdmin:test_SetRateLimitAdmin() (gas: 37630)
USDCBridgeMigrator_BurnLockedUSDC:test_PrimaryMechanism() (gas: 130484)
USDCBridgeMigrator_BurnLockedUSDC:test_lockOrBurn_then_BurnInCCTPMigration() (gas: 304002)
USDCBridgeMigrator_BurnLockedUSDC:test_onLockReleaseMechanism() (gas: 140258)
USDCBridgeMigrator_BurnLockedUSDC:test_onLockReleaseMechanism_thenSwitchToPrimary() (gas: 203295)
USDCBridgeMigrator_cancelMigrationProposal:test_cancelExistingCCTPMigrationProposal() (gas: 56135)
USDCBridgeMigrator_provideLiquidity:test_PrimaryMechanism() (gas: 130503)
USDCBridgeMigrator_provideLiquidity:test_lockOrBurn_then_BurnInCCTPMigration() (gas: 304002)
USDCBridgeMigrator_provideLiquidity:test_onLockReleaseMechanism() (gas: 140347)
USDCBridgeMigrator_provideLiquidity:test_onLockReleaseMechanism_thenSwitchToPrimary() (gas: 203296)
USDCBridgeMigrator_releaseOrMint:test_OnLockReleaseMechanism() (gas: 206338)
USDCBridgeMigrator_releaseOrMint:test_incomingMessageWithPrimaryMechanism() (gas: 265552)
USDCBridgeMigrator_releaseOrMint:test_unstickManualTxAfterMigration_destChain() (gas: 142808)
USDCBridgeMigrator_releaseOrMint:test_unstickManualTxAfterMigration_homeChain() (gas: 510650)
USDCBridgeMigrator_updateChainSelectorMechanism:test_PrimaryMechanism() (gas: 130484)
USDCBridgeMigrator_updateChainSelectorMechanism:test_lockOrBurn_then_BurnInCCTPMigration() (gas: 303984)
USDCBridgeMigrator_updateChainSelectorMechanism:test_onLockReleaseMechanism() (gas: 140347)
USDCBridgeMigrator_updateChainSelectorMechanism:test_onLockReleaseMechanism_thenSwitchToPrimary() (gas: 203277)
USDCTokenPool_constructor:test_constructor() (gas: 3192321)
USDCTokenPool_lockOrBurn:test_LockOrBurn() (gas: 128129)
USDCTokenPool_releaseOrMint:test_ReleaseOrMintRealTx() (gas: 265265)
USDCTokenPool_supportsInterface:test_SupportsInterface() (gas: 10108)