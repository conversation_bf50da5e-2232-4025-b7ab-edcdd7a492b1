#!/usr/bin/env bash

# IMPORTANT: Ensure hardhat-abi-exporter package is installed and `hardhat compile` is run before running this script.
#
# This script copies abi json files generated by hardhat-abi-exporter from `../abi/src/v0.x/` to `../abi/v0.x/`
# then converts them from .json to .abi

SCRIPTPATH="$( cd "$(dirname "$0")" >/dev/null 2>&1 ; cd .. && pwd -P )"
TARGET="abi"

# For each version directory in src, copy the json files to the correct location
versions=( v0.8 )
for version in "${versions[@]}"
do
  rm -rf $SCRIPTPATH/$TARGET/$version
  mkdir $SCRIPTPATH/$TARGET/$version
  find $SCRIPTPATH/$TARGET/src/$version -type f -name "*.json" -exec cp {} $SCRIPTPATH/$TARGET/$version/ \;
done

# Remove the original src abis
rm -rf $SCRIPTPATH/$TARGET/src
