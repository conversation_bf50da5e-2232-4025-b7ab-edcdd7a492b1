// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package burn_with_from_mint_token_pool

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/interfaces/IPool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IRMN.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IRouter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Client.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Pool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/RateLimiter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/pools/BurnMintTokenPoolAbstract.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/pools/BurnWithFromMintTokenPool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/pools/TokenPool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/draft-IERC20Permit.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/utils/SafeERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Address.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IPool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\n\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\\\";\\n\\n/// @notice Shared public interface for multiple V1 pool types.\\n/// Each pool type handles a different child token model e.g. lock/unlock, mint/burn.\\ninterface IPoolV1 is IERC165 {\\n  /// @notice Lock tokens into the pool or burn the tokens.\\n  /// @param lockOrBurnIn Encoded data fields for the processing of tokens on the source chain.\\n  /// @return lockOrBurnOut Encoded data fields for the processing of tokens on the destination chain.\\n  function lockOrBurn(\\n    Pool.LockOrBurnInV1 calldata lockOrBurnIn\\n  ) external returns (Pool.LockOrBurnOutV1 memory lockOrBurnOut);\\n\\n  /// @notice Releases or mints tokens to the receiver address.\\n  /// @param releaseOrMintIn All data required to release or mint tokens.\\n  /// @return releaseOrMintOut The amount of tokens released or minted on the local chain, denominated\\n  /// in the local token's decimals.\\n  /// @dev The offramp asserts that the balanceOf of the receiver has been incremented by exactly the number\\n  /// of tokens that is returned in ReleaseOrMintOutV1.destinationAmount. If the amounts do not match, the tx reverts.\\n  function releaseOrMint(\\n    Pool.ReleaseOrMintInV1 calldata releaseOrMintIn\\n  ) external returns (Pool.ReleaseOrMintOutV1 memory);\\n\\n  /// @notice Checks whether a remote chain is supported in the token pool.\\n  /// @param remoteChainSelector The selector of the remote chain.\\n  /// @return true if the given chain is a permissioned remote chain.\\n  function isSupportedChain(\\n    uint64 remoteChainSelector\\n  ) external view returns (bool);\\n\\n  /// @notice Returns if the token pool supports the given token.\\n  /// @param token The address of the token.\\n  /// @return true if the token is supported by the pool.\\n  function isSupportedToken(\\n    address token\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/IRMN.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This interface contains the only RMN-related functions that might be used on-chain by other CCIP contracts.\\ninterface IRMN {\\n  /// @notice A Merkle root tagged with the address of the commit store contract it is destined for.\\n  struct TaggedRoot {\\n    address commitStore;\\n    bytes32 root;\\n  }\\n\\n  /// @notice Callers MUST NOT cache the return value as a blessed tagged root could become unblessed.\\n  function isBlessed(\\n    TaggedRoot calldata taggedRoot\\n  ) external view returns (bool);\\n\\n  /// @notice Iff there is an active global or legacy curse, this function returns true.\\n  function isCursed() external view returns (bool);\\n\\n  /// @notice Iff there is an active global curse, or an active curse for `subject`, this function returns true.\\n  /// @param subject To check whether a particular chain is cursed, set to bytes16(uint128(chainSelector)).\\n  function isCursed(\\n    bytes16 subject\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/IRouter.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\n\\ninterface IRouter {\\n  error OnlyOffRamp();\\n\\n  /// @notice Route the message to its intended receiver contract.\\n  /// @param message Client.Any2EVMMessage struct.\\n  /// @param gasForCallExactCheck of params for exec.\\n  /// @param gasLimit set of params for exec.\\n  /// @param receiver set of params for exec.\\n  /// @dev if the receiver is a contracts that signals support for CCIP execution through EIP-165.\\n  /// the contract is called. If not, only tokens are transferred.\\n  /// @return success A boolean value indicating whether the ccip message was received without errors.\\n  /// @return retBytes A bytes array containing return data form CCIP receiver.\\n  /// @return gasUsed the gas used by the external customer call. Does not include any overhead.\\n  function routeMessage(\\n    Client.Any2EVMMessage calldata message,\\n    uint16 gasForCallExactCheck,\\n    uint256 gasLimit,\\n    address receiver\\n  ) external returns (bool success, bytes memory retBytes, uint256 gasUsed);\\n\\n  /// @notice Returns the configured onramp for a specific destination chain.\\n  /// @param destChainSelector The destination chain Id to get the onRamp for.\\n  /// @return onRampAddress The address of the onRamp.\\n  function getOnRamp(\\n    uint64 destChainSelector\\n  ) external view returns (address onRampAddress);\\n\\n  /// @notice Return true if the given offRamp is a configured offRamp for the given source chain.\\n  /// @param sourceChainSelector The source chain selector to check.\\n  /// @param offRamp The address of the offRamp to check.\\n  function isOffRamp(uint64 sourceChainSelector, address offRamp) external view returns (bool isOffRamp);\\n}\\n\"},\"contracts/libraries/Client.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n// End consumer library.\\nlibrary Client {\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct EVMTokenAmount {\\n    address token; // token address on the local chain.\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  struct Any2EVMMessage {\\n    bytes32 messageId; // MessageId corresponding to ccipSend on source.\\n    uint64 sourceChainSelector; // Source chain selector.\\n    bytes sender; // abi.decode(sender) if coming from an EVM chain.\\n    bytes data; // payload sent in original message.\\n    EVMTokenAmount[] destTokenAmounts; // Tokens and their amounts in their destination chain representation.\\n  }\\n\\n  // If extraArgs is empty bytes, the default is 200k gas limit.\\n  struct EVM2AnyMessage {\\n    bytes receiver; // abi.encode(receiver address) for dest EVM chains.\\n    bytes data; // Data payload.\\n    EVMTokenAmount[] tokenAmounts; // Token transfers.\\n    address feeToken; // Address of feeToken. address(0) means you will send msg.value.\\n    bytes extraArgs; // Populate this with _argsToBytes(EVMExtraArgsV2).\\n  }\\n\\n  // Tag to indicate only a gas limit. Only usable for EVM as destination chain.\\n  bytes4 public constant EVM_EXTRA_ARGS_V1_TAG = 0x97a657c9;\\n\\n  struct EVMExtraArgsV1 {\\n    uint256 gasLimit;\\n  }\\n\\n  function _argsToBytes(\\n    EVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(EVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n\\n  // Tag to indicate a gas limit (or dest chain equivalent processing units) and Out Of Order Execution. This tag is\\n  // available for multiple chain families. If there is no chain family specific tag, this is the default available\\n  // for a chain.\\n  // Note: not available for Solana VM based chains.\\n  bytes4 public constant GENERIC_EXTRA_ARGS_V2_TAG = 0x181dcf10;\\n\\n  /// @param gasLimit: gas limit for the callback on the destination chain.\\n  /// @param allowOutOfOrderExecution: if true, it indicates that the message can be executed in any order relative to\\n  /// other messages from the same sender. This value's default varies by chain. On some chains, a particular value is\\n  /// enforced, meaning if the expected value is not set, the message request will revert.\\n  /// @dev Fully compatible with the previously existing EVMExtraArgsV2.\\n  struct GenericExtraArgsV2 {\\n    uint256 gasLimit;\\n    bool allowOutOfOrderExecution;\\n  }\\n\\n  // Extra args tag for chains that use the Solana VM.\\n  bytes4 public constant SVM_EXTRA_ARGS_V1_TAG = 0x1f3b3aba;\\n\\n  struct SVMExtraArgsV1 {\\n    uint32 computeUnits;\\n    uint64 accountIsWritableBitmap;\\n    bool allowOutOfOrderExecution;\\n    bytes32 tokenReceiver;\\n    // Additional accounts needed for execution of CCIP receiver. Must be empty if message.receiver is zero.\\n    // Token transfer related accounts are specified in the token pool lookup table on SVM.\\n    bytes32[] accounts;\\n  }\\n\\n  /// @dev The maximum number of accounts that can be passed in SVMExtraArgs.\\n  uint256 public constant SVM_EXTRA_ARGS_MAX_ACCOUNTS = 64;\\n\\n  /// @dev The expected static payload size of a token transfer when Borsh encoded and submitted to SVM.\\n  /// TokenPool extra data and offchain data sizes are dynamic, and should be accounted for separately.\\n  uint256 public constant SVM_TOKEN_TRANSFER_DATA_OVERHEAD = (4 + 32) // source_pool\\n    + 32 // token_address\\n    + 4 // gas_amount\\n    + 4 // extra_data overhead\\n    + 32 // amount\\n    + 32 // size of the token lookup table account\\n    + 32 // token-related accounts in the lookup table, over-estimated to 32, typically between 11 - 13\\n    + 32 // token account belonging to the token receiver, e.g ATA, not included in the token lookup table\\n    + 32 // per-chain token pool config, not included in the token lookup table\\n    + 32 // per-chain token billing config, not always included in the token lookup table\\n    + 32; // OffRamp pool signer PDA, not included in the token lookup table\\n\\n  /// @dev Number of overhead accounts needed for message execution on SVM.\\n  /// @dev These are message.receiver, and the OffRamp Signer PDA specific to the receiver.\\n  uint256 public constant SVM_MESSAGING_ACCOUNTS_OVERHEAD = 2;\\n\\n  /// @dev The size of each SVM account address in bytes.\\n  uint256 public constant SVM_ACCOUNT_BYTE_SIZE = 32;\\n\\n  function _argsToBytes(\\n    GenericExtraArgsV2 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(GENERIC_EXTRA_ARGS_V2_TAG, extraArgs);\\n  }\\n\\n  function _svmArgsToBytes(\\n    SVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(SVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n}\\n\"},\"contracts/libraries/Pool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This library contains various token pool functions to aid constructing the return data.\\nlibrary Pool {\\n  // The tag used to signal support for the pool v1 standard.\\n  // bytes4(keccak256(\\\"CCIP_POOL_V1\\\"))\\n  bytes4 public constant CCIP_POOL_V1 = 0xaff2afbf;\\n\\n  // The number of bytes in the return data for a pool v1 releaseOrMint call.\\n  // This should match the size of the ReleaseOrMintOutV1 struct.\\n  uint16 public constant CCIP_POOL_V1_RET_BYTES = 32;\\n\\n  // The default max number of bytes in the return data for a pool v1 lockOrBurn call.\\n  // This data can be used to send information to the destination chain token pool. Can be overwritten\\n  // in the TokenTransferFeeConfig.destBytesOverhead if more data is required.\\n  uint32 public constant CCIP_LOCK_OR_BURN_V1_RET_BYTES = 32;\\n\\n  struct LockOrBurnInV1 {\\n    bytes receiver; //  The recipient of the tokens on the destination chain, abi encoded.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the destination chain.\\n    address originalSender; // ─────╯ The original sender of the tx on the source chain.\\n    uint256 amount; //  The amount of tokens to lock or burn, denominated in the source token's decimals.\\n    address localToken; //  The address on this chain of the token to lock or burn.\\n  }\\n\\n  struct LockOrBurnOutV1 {\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes destPoolData;\\n  }\\n\\n  struct ReleaseOrMintInV1 {\\n    bytes originalSender; //          The original sender of the tx on the source chain.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the source chain.\\n    address receiver; // ───────────╯ The recipient of the tokens on the destination chain.\\n    uint256 amount; //                The amount of tokens to release or mint, denominated in the source token's decimals.\\n    address localToken; //            The address on this chain of the token to release or mint.\\n    /// @dev WARNING: sourcePoolAddress should be checked prior to any processing of funds. Make sure it matches the\\n    /// expected pool address for the given remoteChainSelector.\\n    bytes sourcePoolAddress; //       The address of the source pool, abi encoded in the case of EVM chains.\\n    bytes sourcePoolData; //          The data received from the source pool to process the release or mint.\\n    /// @dev WARNING: offchainTokenData is untrusted data.\\n    bytes offchainTokenData; //       The offchain data to process the release or mint.\\n  }\\n\\n  struct ReleaseOrMintOutV1 {\\n    // The number of tokens released or minted on the destination chain, denominated in the local token's decimals.\\n    // This value is expected to be equal to the ReleaseOrMintInV1.amount in the case where the source and destination\\n    // chain have the same number of decimals.\\n    uint256 destinationAmount;\\n  }\\n}\\n\"},\"contracts/libraries/RateLimiter.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\n/// @notice Implements Token Bucket rate limiting.\\n/// @dev uint128 is safe for rate limiter state.\\n/// - For USD value rate limiting, it can adequately store USD value in 18 decimals.\\n/// - For ERC20 token amount rate limiting, all tokens that will be listed will have at most a supply of uint128.max\\n/// tokens, and it will therefore not overflow the bucket. In exceptional scenarios where tokens consumed may be larger\\n/// than uint128, e.g. compromised issuer, an enabled RateLimiter will check and revert.\\nlibrary RateLimiter {\\n  error BucketOverfilled();\\n  error OnlyCallableByAdminOrOwner();\\n  error TokenMaxCapacityExceeded(uint256 capacity, uint256 requested, address tokenAddress);\\n  error TokenRateLimitReached(uint256 minWaitInSeconds, uint256 available, address tokenAddress);\\n  error AggregateValueMaxCapacityExceeded(uint256 capacity, uint256 requested);\\n  error AggregateValueRateLimitReached(uint256 minWaitInSeconds, uint256 available);\\n  error InvalidRateLimitRate(Config rateLimiterConfig);\\n  error DisabledNonZeroRateLimit(Config config);\\n  error RateLimitMustBeDisabled();\\n\\n  event TokensConsumed(uint256 tokens);\\n  event ConfigChanged(Config config);\\n\\n  struct TokenBucket {\\n    uint128 tokens; // ──────╮ Current number of tokens that are in the bucket.\\n    uint32 lastUpdated; //   │ Timestamp in seconds of the last token refill, good for 100+ years.\\n    bool isEnabled; // ──────╯ Indication whether the rate limiting is enabled or not.\\n    uint128 capacity; // ────╮ Maximum number of tokens that can be in the bucket.\\n    uint128 rate; // ────────╯ Number of tokens per second that the bucket is refilled.\\n  }\\n\\n  struct Config {\\n    bool isEnabled; // Indication whether the rate limiting should be enabled.\\n    uint128 capacity; // ────╮ Specifies the capacity of the rate limiter.\\n    uint128 rate; //  ───────╯ Specifies the rate of the rate limiter.\\n  }\\n\\n  /// @notice _consume removes the given tokens from the pool, lowering the rate tokens allowed to be\\n  /// consumed for subsequent calls.\\n  /// @param requestTokens The total tokens to be consumed from the bucket.\\n  /// @param tokenAddress The token to consume capacity for, use 0x0 to indicate aggregate value capacity.\\n  /// @dev Reverts when requestTokens exceeds bucket capacity or available tokens in the bucket.\\n  /// @dev emits removal of requestTokens if requestTokens is \\u003e 0.\\n  function _consume(TokenBucket storage s_bucket, uint256 requestTokens, address tokenAddress) internal {\\n    // If there is no value to remove or rate limiting is turned off, skip this step to reduce gas usage.\\n    if (!s_bucket.isEnabled || requestTokens == 0) {\\n      return;\\n    }\\n\\n    uint256 tokens = s_bucket.tokens;\\n    uint256 capacity = s_bucket.capacity;\\n    uint256 timeDiff = block.timestamp - s_bucket.lastUpdated;\\n\\n    if (timeDiff != 0) {\\n      if (tokens \\u003e capacity) revert BucketOverfilled();\\n\\n      // Refill tokens when arriving at a new block time.\\n      tokens = _calculateRefill(capacity, tokens, timeDiff, s_bucket.rate);\\n\\n      s_bucket.lastUpdated = uint32(block.timestamp);\\n    }\\n\\n    if (capacity \\u003c requestTokens) {\\n      // Token address 0 indicates consuming aggregate value rate limit capacity.\\n      if (tokenAddress == address(0)) revert AggregateValueMaxCapacityExceeded(capacity, requestTokens);\\n      revert TokenMaxCapacityExceeded(capacity, requestTokens, tokenAddress);\\n    }\\n    if (tokens \\u003c requestTokens) {\\n      uint256 rate = s_bucket.rate;\\n      // Wait required until the bucket is refilled enough to accept this value, round up to next higher second.\\n      // Consume is not guaranteed to succeed after wait time passes if there is competing traffic.\\n      // This acts as a lower bound of wait time.\\n      uint256 minWaitInSeconds = ((requestTokens - tokens) + (rate - 1)) / rate;\\n\\n      if (tokenAddress == address(0)) revert AggregateValueRateLimitReached(minWaitInSeconds, tokens);\\n      revert TokenRateLimitReached(minWaitInSeconds, tokens, tokenAddress);\\n    }\\n    tokens -= requestTokens;\\n\\n    // Downcast is safe here, as tokens is not larger than capacity.\\n    s_bucket.tokens = uint128(tokens);\\n    emit TokensConsumed(requestTokens);\\n  }\\n\\n  /// @notice Gets the token bucket with its values for the block it was requested at.\\n  /// @return The token bucket.\\n  function _currentTokenBucketState(\\n    TokenBucket memory bucket\\n  ) internal view returns (TokenBucket memory) {\\n    // We update the bucket to reflect the status at the exact time of the call. This means we might need to refill a\\n    // part of the bucket based on the time that has passed since the last update.\\n    bucket.tokens =\\n      uint128(_calculateRefill(bucket.capacity, bucket.tokens, block.timestamp - bucket.lastUpdated, bucket.rate));\\n    bucket.lastUpdated = uint32(block.timestamp);\\n    return bucket;\\n  }\\n\\n  /// @notice Sets the rate limited config.\\n  /// @param s_bucket The token bucket.\\n  /// @param config The new config.\\n  function _setTokenBucketConfig(TokenBucket storage s_bucket, Config memory config) internal {\\n    // First update the bucket to make sure the proper rate is used for all the time up until the config change.\\n    uint256 timeDiff = block.timestamp - s_bucket.lastUpdated;\\n    if (timeDiff != 0) {\\n      s_bucket.tokens = uint128(_calculateRefill(s_bucket.capacity, s_bucket.tokens, timeDiff, s_bucket.rate));\\n\\n      s_bucket.lastUpdated = uint32(block.timestamp);\\n    }\\n\\n    s_bucket.tokens = uint128(_min(config.capacity, s_bucket.tokens));\\n    s_bucket.isEnabled = config.isEnabled;\\n    s_bucket.capacity = config.capacity;\\n    s_bucket.rate = config.rate;\\n\\n    emit ConfigChanged(config);\\n  }\\n\\n  /// @notice Validates the token bucket config.\\n  function _validateTokenBucketConfig(Config memory config, bool mustBeDisabled) internal pure {\\n    if (config.isEnabled) {\\n      if (config.rate \\u003e= config.capacity || config.rate == 0) {\\n        revert InvalidRateLimitRate(config);\\n      }\\n      if (mustBeDisabled) {\\n        revert RateLimitMustBeDisabled();\\n      }\\n    } else {\\n      if (config.rate != 0 || config.capacity != 0) {\\n        revert DisabledNonZeroRateLimit(config);\\n      }\\n    }\\n  }\\n\\n  /// @notice Calculate refilled tokens.\\n  /// @param capacity bucket capacity.\\n  /// @param tokens current bucket tokens.\\n  /// @param timeDiff block time difference since last refill.\\n  /// @param rate bucket refill rate.\\n  /// @return the value of tokens after refill.\\n  function _calculateRefill(\\n    uint256 capacity,\\n    uint256 tokens,\\n    uint256 timeDiff,\\n    uint256 rate\\n  ) private pure returns (uint256) {\\n    return _min(capacity, tokens + timeDiff * rate);\\n  }\\n\\n  /// @notice Return the smallest of two integers.\\n  /// @param a first int.\\n  /// @param b second int.\\n  /// @return smallest.\\n  function _min(uint256 a, uint256 b) internal pure returns (uint256) {\\n    return a \\u003c b ? a : b;\\n  }\\n}\\n\"},\"contracts/pools/BurnMintTokenPoolAbstract.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IBurnMintERC20} from \\\"@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\\\";\\n\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\nimport {TokenPool} from \\\"./TokenPool.sol\\\";\\n\\nabstract contract BurnMintTokenPoolAbstract is TokenPool {\\n  /// @notice Contains the specific burn call for a pool.\\n  /// @dev overriding this method allows us to create pools with different burn signatures\\n  /// without duplicating the underlying logic.\\n  function _burn(\\n    uint256 amount\\n  ) internal virtual;\\n\\n  /// @notice Burn the token in the pool\\n  /// @dev The _validateLockOrBurn check is an essential security check\\n  function lockOrBurn(\\n    Pool.LockOrBurnInV1 calldata lockOrBurnIn\\n  ) external virtual override returns (Pool.LockOrBurnOutV1 memory) {\\n    _validateLockOrBurn(lockOrBurnIn);\\n\\n    _burn(lockOrBurnIn.amount);\\n\\n    emit Burned(msg.sender, lockOrBurnIn.amount);\\n\\n    return Pool.LockOrBurnOutV1({\\n      destTokenAddress: getRemoteToken(lockOrBurnIn.remoteChainSelector),\\n      destPoolData: _encodeLocalDecimals()\\n    });\\n  }\\n\\n  /// @notice Mint tokens from the pool to the recipient\\n  /// @dev The _validateReleaseOrMint check is an essential security check\\n  function releaseOrMint(\\n    Pool.ReleaseOrMintInV1 calldata releaseOrMintIn\\n  ) public virtual override returns (Pool.ReleaseOrMintOutV1 memory) {\\n    _validateReleaseOrMint(releaseOrMintIn);\\n\\n    // Calculate the local amount\\n    uint256 localAmount =\\n      _calculateLocalAmount(releaseOrMintIn.amount, _parseRemoteDecimals(releaseOrMintIn.sourcePoolData));\\n\\n    // Mint to the receiver\\n    IBurnMintERC20(address(i_token)).mint(releaseOrMintIn.receiver, localAmount);\\n\\n    emit Minted(msg.sender, releaseOrMintIn.receiver, localAmount);\\n\\n    return Pool.ReleaseOrMintOutV1({destinationAmount: localAmount});\\n  }\\n}\\n\"},\"contracts/pools/BurnWithFromMintTokenPool.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\nimport {IBurnMintERC20} from \\\"@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\\\";\\n\\nimport {BurnMintTokenPoolAbstract} from \\\"./BurnMintTokenPoolAbstract.sol\\\";\\nimport {TokenPool} from \\\"./TokenPool.sol\\\";\\n\\nimport {SafeERC20} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/utils/SafeERC20.sol\\\";\\n\\n/// @notice This pool mints and burns a 3rd-party token.\\n/// @dev Pool whitelisting mode is set in the constructor and cannot be modified later.\\n/// It either accepts any address as originalSender, or only accepts whitelisted originalSender.\\n/// The only way to change whitelisting mode is to deploy a new pool.\\n/// If that is expected, please make sure the token's burner/minter roles are adjustable.\\n/// @dev This contract is a variant of BurnMintTokenPool that uses `burn(from, amount)`.\\ncontract BurnWithFromMintTokenPool is BurnMintTokenPoolAbstract, ITypeAndVersion {\\n  using SafeERC20 for IBurnMintERC20;\\n\\n  constructor(\\n    IBurnMintERC20 token,\\n    uint8 localTokenDecimals,\\n    address[] memory allowlist,\\n    address rmnProxy,\\n    address router\\n  ) TokenPool(token, localTokenDecimals, allowlist, rmnProxy, router) {\\n    // Some tokens allow burning from the sender without approval, but not all do.\\n    // To be safe, we approve the pool to burn from the pool.\\n    token.safeIncreaseAllowance(address(this), type(uint256).max);\\n  }\\n\\n  /// @inheritdoc BurnMintTokenPoolAbstract\\n  function _burn(\\n    uint256 amount\\n  ) internal virtual override {\\n    IBurnMintERC20(address(i_token)).burn(address(this), amount);\\n  }\\n\\n  function typeAndVersion() external pure virtual override returns (string memory) {\\n    return \\\"BurnWithFromMintTokenPool 1.5.1\\\";\\n  }\\n}\\n\"},\"contracts/pools/TokenPool.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IPoolV1} from \\\"../interfaces/IPool.sol\\\";\\nimport {IRMN} from \\\"../interfaces/IRMN.sol\\\";\\nimport {IRouter} from \\\"../interfaces/IRouter.sol\\\";\\n\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\nimport {RateLimiter} from \\\"../libraries/RateLimiter.sol\\\";\\nimport {Ownable2StepMsgSender} from \\\"@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\\\";\\n\\nimport {IERC20} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\nimport {IERC20Metadata} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\\\";\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\\\";\\nimport {EnumerableSet} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @notice Base abstract class with common functions for all token pools.\\n/// A token pool serves as isolated place for holding tokens and token specific logic\\n/// that may execute as tokens move across the bridge.\\n/// @dev This pool supports different decimals on different chains but using this feature could impact the total number\\n/// of tokens in circulation. Since all of the tokens are locked/burned on the source, and a rounded amount is\\n/// minted/released on the destination, the number of tokens minted/released could be less than the number of tokens\\n/// burned/locked. This is because the source chain does not know about the destination token decimals. This is not a\\n/// problem if the decimals are the same on both chains.\\n///\\n/// Example:\\n/// Assume there is a token with 6 decimals on chain A and 3 decimals on chain B.\\n/// - 1.234567 tokens are burned on chain A.\\n/// - 1.234    tokens are minted on chain B.\\n/// When sending the 1.234 tokens back to chain A, you will receive 1.234000 tokens on chain A, effectively losing\\n/// 0.000567 tokens.\\n/// In the case of a burnMint pool on chain A, these funds are burned in the pool on chain A.\\n/// In the case of a lockRelease pool on chain A, these funds accumulate in the pool on chain A.\\nabstract contract TokenPool is IPoolV1, Ownable2StepMsgSender {\\n  using EnumerableSet for EnumerableSet.Bytes32Set;\\n  using EnumerableSet for EnumerableSet.AddressSet;\\n  using EnumerableSet for EnumerableSet.UintSet;\\n  using RateLimiter for RateLimiter.TokenBucket;\\n\\n  error CallerIsNotARampOnRouter(address caller);\\n  error ZeroAddressNotAllowed();\\n  error SenderNotAllowed(address sender);\\n  error AllowListNotEnabled();\\n  error NonExistentChain(uint64 remoteChainSelector);\\n  error ChainNotAllowed(uint64 remoteChainSelector);\\n  error CursedByRMN();\\n  error ChainAlreadyExists(uint64 chainSelector);\\n  error InvalidSourcePoolAddress(bytes sourcePoolAddress);\\n  error InvalidToken(address token);\\n  error Unauthorized(address caller);\\n  error PoolAlreadyAdded(uint64 remoteChainSelector, bytes remotePoolAddress);\\n  error InvalidRemotePoolForChain(uint64 remoteChainSelector, bytes remotePoolAddress);\\n  error InvalidRemoteChainDecimals(bytes sourcePoolData);\\n  error MismatchedArrayLengths();\\n  error OverflowDetected(uint8 remoteDecimals, uint8 localDecimals, uint256 remoteAmount);\\n  error InvalidDecimalArgs(uint8 expected, uint8 actual);\\n\\n  event Locked(address indexed sender, uint256 amount);\\n  event Burned(address indexed sender, uint256 amount);\\n  event Released(address indexed sender, address indexed recipient, uint256 amount);\\n  event Minted(address indexed sender, address indexed recipient, uint256 amount);\\n  event ChainAdded(\\n    uint64 remoteChainSelector,\\n    bytes remoteToken,\\n    RateLimiter.Config outboundRateLimiterConfig,\\n    RateLimiter.Config inboundRateLimiterConfig\\n  );\\n  event ChainConfigured(\\n    uint64 remoteChainSelector,\\n    RateLimiter.Config outboundRateLimiterConfig,\\n    RateLimiter.Config inboundRateLimiterConfig\\n  );\\n  event ChainRemoved(uint64 remoteChainSelector);\\n  event RemotePoolAdded(uint64 indexed remoteChainSelector, bytes remotePoolAddress);\\n  event RemotePoolRemoved(uint64 indexed remoteChainSelector, bytes remotePoolAddress);\\n  event AllowListAdd(address sender);\\n  event AllowListRemove(address sender);\\n  event RouterUpdated(address oldRouter, address newRouter);\\n  event RateLimitAdminSet(address rateLimitAdmin);\\n\\n  struct ChainUpdate {\\n    uint64 remoteChainSelector; // Remote chain selector\\n    bytes[] remotePoolAddresses; // Address of the remote pool, ABI encoded in the case of a remote EVM chain.\\n    bytes remoteTokenAddress; // Address of the remote token, ABI encoded in the case of a remote EVM chain.\\n    RateLimiter.Config outboundRateLimiterConfig; // Outbound rate limited config, meaning the rate limits for all of the onRamps for the given chain\\n    RateLimiter.Config inboundRateLimiterConfig; // Inbound rate limited config, meaning the rate limits for all of the offRamps for the given chain\\n  }\\n\\n  struct RemoteChainConfig {\\n    RateLimiter.TokenBucket outboundRateLimiterConfig; // Outbound rate limited config, meaning the rate limits for all of the onRamps for the given chain\\n    RateLimiter.TokenBucket inboundRateLimiterConfig; // Inbound rate limited config, meaning the rate limits for all of the offRamps for the given chain\\n    bytes remoteTokenAddress; // Address of the remote token, ABI encoded in the case of a remote EVM chain.\\n    EnumerableSet.Bytes32Set remotePools; // Set of remote pool hashes, ABI encoded in the case of a remote EVM chain.\\n  }\\n\\n  /// @dev The bridgeable token that is managed by this pool. Pools could support multiple tokens at the same time if\\n  /// required, but this implementation only supports one token.\\n  IERC20 internal immutable i_token;\\n  /// @dev The number of decimals of the token managed by this pool.\\n  uint8 internal immutable i_tokenDecimals;\\n  /// @dev The address of the RMN proxy\\n  address internal immutable i_rmnProxy;\\n  /// @dev The immutable flag that indicates if the pool is access-controlled.\\n  bool internal immutable i_allowlistEnabled;\\n  /// @dev A set of addresses allowed to trigger lockOrBurn as original senders.\\n  /// Only takes effect if i_allowlistEnabled is true.\\n  /// This can be used to ensure only token-issuer specified addresses can move tokens.\\n  EnumerableSet.AddressSet internal s_allowlist;\\n  /// @dev The address of the router\\n  IRouter internal s_router;\\n  /// @dev A set of allowed chain selectors. We want the allowlist to be enumerable to\\n  /// be able to quickly determine (without parsing logs) who can access the pool.\\n  /// @dev The chain selectors are in uint256 format because of the EnumerableSet implementation.\\n  EnumerableSet.UintSet internal s_remoteChainSelectors;\\n  mapping(uint64 remoteChainSelector =\\u003e RemoteChainConfig) internal s_remoteChainConfigs;\\n  /// @notice A mapping of hashed pool addresses to their unhashed form. This is used to be able to find the actually\\n  /// configured pools and not just their hashed versions.\\n  mapping(bytes32 poolAddressHash =\\u003e bytes poolAddress) internal s_remotePoolAddresses;\\n  /// @notice The address of the rate limiter admin.\\n  /// @dev Can be address(0) if none is configured.\\n  address internal s_rateLimitAdmin;\\n\\n  constructor(IERC20 token, uint8 localTokenDecimals, address[] memory allowlist, address rmnProxy, address router) {\\n    if (address(token) == address(0) || router == address(0) || rmnProxy == address(0)) revert ZeroAddressNotAllowed();\\n    i_token = token;\\n    i_rmnProxy = rmnProxy;\\n\\n    try IERC20Metadata(address(token)).decimals() returns (uint8 actualTokenDecimals) {\\n      if (localTokenDecimals != actualTokenDecimals) {\\n        revert InvalidDecimalArgs(localTokenDecimals, actualTokenDecimals);\\n      }\\n    } catch {\\n      // The decimals function doesn't exist, which is possible since it's optional in the ERC20 spec. We skip the check and\\n      // assume the supplied token decimals are correct.\\n    }\\n    i_tokenDecimals = localTokenDecimals;\\n\\n    s_router = IRouter(router);\\n\\n    // Pool can be set as permissioned or permissionless at deployment time only to save hot-path gas.\\n    i_allowlistEnabled = allowlist.length \\u003e 0;\\n    if (i_allowlistEnabled) {\\n      _applyAllowListUpdates(new address[](0), allowlist);\\n    }\\n  }\\n\\n  /// @inheritdoc IPoolV1\\n  function isSupportedToken(\\n    address token\\n  ) public view virtual returns (bool) {\\n    return token == address(i_token);\\n  }\\n\\n  /// @notice Gets the IERC20 token that this pool can lock or burn.\\n  /// @return token The IERC20 token representation.\\n  function getToken() public view returns (IERC20 token) {\\n    return i_token;\\n  }\\n\\n  /// @notice Get RMN proxy address\\n  /// @return rmnProxy Address of RMN proxy\\n  function getRmnProxy() public view returns (address rmnProxy) {\\n    return i_rmnProxy;\\n  }\\n\\n  /// @notice Gets the pool's Router\\n  /// @return router The pool's Router\\n  function getRouter() public view returns (address router) {\\n    return address(s_router);\\n  }\\n\\n  /// @notice Sets the pool's Router\\n  /// @param newRouter The new Router\\n  function setRouter(\\n    address newRouter\\n  ) public onlyOwner {\\n    if (newRouter == address(0)) revert ZeroAddressNotAllowed();\\n    address oldRouter = address(s_router);\\n    s_router = IRouter(newRouter);\\n\\n    emit RouterUpdated(oldRouter, newRouter);\\n  }\\n\\n  /// @notice Signals which version of the pool interface is supported\\n  function supportsInterface(\\n    bytes4 interfaceId\\n  ) public pure virtual override returns (bool) {\\n    return interfaceId == Pool.CCIP_POOL_V1 || interfaceId == type(IPoolV1).interfaceId\\n      || interfaceId == type(IERC165).interfaceId;\\n  }\\n\\n  // ================================================================\\n  // │                         Validation                           │\\n  // ================================================================\\n\\n  /// @notice Validates the lock or burn input for correctness on\\n  /// - token to be locked or burned\\n  /// - RMN curse status\\n  /// - allowlist status\\n  /// - if the sender is a valid onRamp\\n  /// - rate limit status\\n  /// @param lockOrBurnIn The input to validate.\\n  /// @dev This function should always be called before executing a lock or burn. Not doing so would allow\\n  /// for various exploits.\\n  function _validateLockOrBurn(\\n    Pool.LockOrBurnInV1 calldata lockOrBurnIn\\n  ) internal {\\n    if (!isSupportedToken(lockOrBurnIn.localToken)) revert InvalidToken(lockOrBurnIn.localToken);\\n    if (IRMN(i_rmnProxy).isCursed(bytes16(uint128(lockOrBurnIn.remoteChainSelector)))) revert CursedByRMN();\\n    _checkAllowList(lockOrBurnIn.originalSender);\\n\\n    _onlyOnRamp(lockOrBurnIn.remoteChainSelector);\\n    _consumeOutboundRateLimit(lockOrBurnIn.remoteChainSelector, lockOrBurnIn.amount);\\n  }\\n\\n  /// @notice Validates the release or mint input for correctness on\\n  /// - token to be released or minted\\n  /// - RMN curse status\\n  /// - if the sender is a valid offRamp\\n  /// - if the source pool is valid\\n  /// - rate limit status\\n  /// @param releaseOrMintIn The input to validate.\\n  /// @dev This function should always be called before executing a release or mint. Not doing so would allow\\n  /// for various exploits.\\n  function _validateReleaseOrMint(\\n    Pool.ReleaseOrMintInV1 calldata releaseOrMintIn\\n  ) internal {\\n    if (!isSupportedToken(releaseOrMintIn.localToken)) revert InvalidToken(releaseOrMintIn.localToken);\\n    if (IRMN(i_rmnProxy).isCursed(bytes16(uint128(releaseOrMintIn.remoteChainSelector)))) revert CursedByRMN();\\n    _onlyOffRamp(releaseOrMintIn.remoteChainSelector);\\n\\n    // Validates that the source pool address is configured on this pool.\\n    if (!isRemotePool(releaseOrMintIn.remoteChainSelector, releaseOrMintIn.sourcePoolAddress)) {\\n      revert InvalidSourcePoolAddress(releaseOrMintIn.sourcePoolAddress);\\n    }\\n\\n    _consumeInboundRateLimit(releaseOrMintIn.remoteChainSelector, releaseOrMintIn.amount);\\n  }\\n\\n  // ================================================================\\n  // │                      Token decimals                          │\\n  // ================================================================\\n\\n  /// @notice Gets the IERC20 token decimals on the local chain.\\n  function getTokenDecimals() public view virtual returns (uint8 decimals) {\\n    return i_tokenDecimals;\\n  }\\n\\n  function _encodeLocalDecimals() internal view virtual returns (bytes memory) {\\n    return abi.encode(i_tokenDecimals);\\n  }\\n\\n  function _parseRemoteDecimals(\\n    bytes memory sourcePoolData\\n  ) internal view virtual returns (uint8) {\\n    // Fallback to the local token decimals if the source pool data is empty. This allows for backwards compatibility.\\n    if (sourcePoolData.length == 0) {\\n      return i_tokenDecimals;\\n    }\\n    if (sourcePoolData.length != 32) {\\n      revert InvalidRemoteChainDecimals(sourcePoolData);\\n    }\\n    uint256 remoteDecimals = abi.decode(sourcePoolData, (uint256));\\n    if (remoteDecimals \\u003e type(uint8).max) {\\n      revert InvalidRemoteChainDecimals(sourcePoolData);\\n    }\\n    return uint8(remoteDecimals);\\n  }\\n\\n  /// @notice Calculates the local amount based on the remote amount and decimals.\\n  /// @param remoteAmount The amount on the remote chain.\\n  /// @param remoteDecimals The decimals of the token on the remote chain.\\n  /// @return The local amount.\\n  /// @dev This function protects against overflows. If there is a transaction that hits the overflow check, it is\\n  /// probably incorrect as that means the amount cannot be represented on this chain. If the local decimals have been\\n  /// wrongly configured, the token issuer could redeploy the pool with the correct decimals and manually re-execute the\\n  /// CCIP tx to fix the issue.\\n  function _calculateLocalAmount(uint256 remoteAmount, uint8 remoteDecimals) internal view virtual returns (uint256) {\\n    if (remoteDecimals == i_tokenDecimals) {\\n      return remoteAmount;\\n    }\\n    if (remoteDecimals \\u003e i_tokenDecimals) {\\n      uint8 decimalsDiff = remoteDecimals - i_tokenDecimals;\\n      if (decimalsDiff \\u003e 77) {\\n        // This is a safety check to prevent overflow in the next calculation.\\n        revert OverflowDetected(remoteDecimals, i_tokenDecimals, remoteAmount);\\n      }\\n      // Solidity rounds down so there is no risk of minting more tokens than the remote chain sent.\\n      return remoteAmount / (10 ** decimalsDiff);\\n    }\\n\\n    // This is a safety check to prevent overflow in the next calculation.\\n    // More than 77 would never fit in a uint256 and would cause an overflow. We also check if the resulting amount\\n    // would overflow.\\n    uint8 diffDecimals = i_tokenDecimals - remoteDecimals;\\n    if (diffDecimals \\u003e 77 || remoteAmount \\u003e type(uint256).max / (10 ** diffDecimals)) {\\n      revert OverflowDetected(remoteDecimals, i_tokenDecimals, remoteAmount);\\n    }\\n\\n    return remoteAmount * (10 ** diffDecimals);\\n  }\\n\\n  // ================================================================\\n  // │                     Chain permissions                        │\\n  // ================================================================\\n\\n  /// @notice Gets the pool address on the remote chain.\\n  /// @param remoteChainSelector Remote chain selector.\\n  /// @dev To support non-evm chains, this value is encoded into bytes\\n  function getRemotePools(\\n    uint64 remoteChainSelector\\n  ) public view returns (bytes[] memory) {\\n    bytes32[] memory remotePoolHashes = s_remoteChainConfigs[remoteChainSelector].remotePools.values();\\n\\n    bytes[] memory remotePools = new bytes[](remotePoolHashes.length);\\n    for (uint256 i = 0; i \\u003c remotePoolHashes.length; ++i) {\\n      remotePools[i] = s_remotePoolAddresses[remotePoolHashes[i]];\\n    }\\n\\n    return remotePools;\\n  }\\n\\n  /// @notice Checks if the pool address is configured on the remote chain.\\n  /// @param remoteChainSelector Remote chain selector.\\n  /// @param remotePoolAddress The address of the remote pool.\\n  function isRemotePool(uint64 remoteChainSelector, bytes calldata remotePoolAddress) public view returns (bool) {\\n    return s_remoteChainConfigs[remoteChainSelector].remotePools.contains(keccak256(remotePoolAddress));\\n  }\\n\\n  /// @notice Gets the token address on the remote chain.\\n  /// @param remoteChainSelector Remote chain selector.\\n  /// @dev To support non-evm chains, this value is encoded into bytes\\n  function getRemoteToken(\\n    uint64 remoteChainSelector\\n  ) public view returns (bytes memory) {\\n    return s_remoteChainConfigs[remoteChainSelector].remoteTokenAddress;\\n  }\\n\\n  /// @notice Adds a remote pool for a given chain selector. This could be due to a pool being upgraded on the remote\\n  /// chain. We don't simply want to replace the old pool as there could still be valid inflight messages from the old\\n  /// pool. This function allows for multiple pools to be added for a single chain selector.\\n  /// @param remoteChainSelector The remote chain selector for which the remote pool address is being added.\\n  /// @param remotePoolAddress The address of the new remote pool.\\n  function addRemotePool(uint64 remoteChainSelector, bytes calldata remotePoolAddress) external onlyOwner {\\n    if (!isSupportedChain(remoteChainSelector)) revert NonExistentChain(remoteChainSelector);\\n\\n    _setRemotePool(remoteChainSelector, remotePoolAddress);\\n  }\\n\\n  /// @notice Removes the remote pool address for a given chain selector.\\n  /// @dev All inflight txs from the remote pool will be rejected after it is removed. To ensure no loss of funds, there\\n  /// should be no inflight txs from the given pool.\\n  function removeRemotePool(uint64 remoteChainSelector, bytes calldata remotePoolAddress) external onlyOwner {\\n    if (!isSupportedChain(remoteChainSelector)) revert NonExistentChain(remoteChainSelector);\\n\\n    if (!s_remoteChainConfigs[remoteChainSelector].remotePools.remove(keccak256(remotePoolAddress))) {\\n      revert InvalidRemotePoolForChain(remoteChainSelector, remotePoolAddress);\\n    }\\n\\n    emit RemotePoolRemoved(remoteChainSelector, remotePoolAddress);\\n  }\\n\\n  /// @inheritdoc IPoolV1\\n  function isSupportedChain(\\n    uint64 remoteChainSelector\\n  ) public view returns (bool) {\\n    return s_remoteChainSelectors.contains(remoteChainSelector);\\n  }\\n\\n  /// @notice Get list of allowed chains\\n  /// @return list of chains.\\n  function getSupportedChains() public view returns (uint64[] memory) {\\n    uint256[] memory uint256ChainSelectors = s_remoteChainSelectors.values();\\n    uint64[] memory chainSelectors = new uint64[](uint256ChainSelectors.length);\\n    for (uint256 i = 0; i \\u003c uint256ChainSelectors.length; ++i) {\\n      chainSelectors[i] = uint64(uint256ChainSelectors[i]);\\n    }\\n\\n    return chainSelectors;\\n  }\\n\\n  /// @notice Sets the permissions for a list of chains selectors. Actual senders for these chains\\n  /// need to be allowed on the Router to interact with this pool.\\n  /// @param remoteChainSelectorsToRemove A list of chain selectors to remove.\\n  /// @param chainsToAdd A list of chains and their new permission status \\u0026 rate limits. Rate limits\\n  /// are only used when the chain is being added through `allowed` being true.\\n  /// @dev Only callable by the owner\\n  function applyChainUpdates(\\n    uint64[] calldata remoteChainSelectorsToRemove,\\n    ChainUpdate[] calldata chainsToAdd\\n  ) external virtual onlyOwner {\\n    for (uint256 i = 0; i \\u003c remoteChainSelectorsToRemove.length; ++i) {\\n      uint64 remoteChainSelectorToRemove = remoteChainSelectorsToRemove[i];\\n      // If the chain doesn't exist, revert\\n      if (!s_remoteChainSelectors.remove(remoteChainSelectorToRemove)) {\\n        revert NonExistentChain(remoteChainSelectorToRemove);\\n      }\\n\\n      // Remove all remote pool hashes for the chain\\n      bytes32[] memory remotePools = s_remoteChainConfigs[remoteChainSelectorToRemove].remotePools.values();\\n      for (uint256 j = 0; j \\u003c remotePools.length; ++j) {\\n        s_remoteChainConfigs[remoteChainSelectorToRemove].remotePools.remove(remotePools[j]);\\n      }\\n\\n      delete s_remoteChainConfigs[remoteChainSelectorToRemove];\\n\\n      emit ChainRemoved(remoteChainSelectorToRemove);\\n    }\\n\\n    for (uint256 i = 0; i \\u003c chainsToAdd.length; ++i) {\\n      ChainUpdate memory newChain = chainsToAdd[i];\\n      RateLimiter._validateTokenBucketConfig(newChain.outboundRateLimiterConfig, false);\\n      RateLimiter._validateTokenBucketConfig(newChain.inboundRateLimiterConfig, false);\\n\\n      if (newChain.remoteTokenAddress.length == 0) {\\n        revert ZeroAddressNotAllowed();\\n      }\\n\\n      // If the chain already exists, revert\\n      if (!s_remoteChainSelectors.add(newChain.remoteChainSelector)) {\\n        revert ChainAlreadyExists(newChain.remoteChainSelector);\\n      }\\n\\n      RemoteChainConfig storage remoteChainConfig = s_remoteChainConfigs[newChain.remoteChainSelector];\\n\\n      remoteChainConfig.outboundRateLimiterConfig = RateLimiter.TokenBucket({\\n        rate: newChain.outboundRateLimiterConfig.rate,\\n        capacity: newChain.outboundRateLimiterConfig.capacity,\\n        tokens: newChain.outboundRateLimiterConfig.capacity,\\n        lastUpdated: uint32(block.timestamp),\\n        isEnabled: newChain.outboundRateLimiterConfig.isEnabled\\n      });\\n      remoteChainConfig.inboundRateLimiterConfig = RateLimiter.TokenBucket({\\n        rate: newChain.inboundRateLimiterConfig.rate,\\n        capacity: newChain.inboundRateLimiterConfig.capacity,\\n        tokens: newChain.inboundRateLimiterConfig.capacity,\\n        lastUpdated: uint32(block.timestamp),\\n        isEnabled: newChain.inboundRateLimiterConfig.isEnabled\\n      });\\n      remoteChainConfig.remoteTokenAddress = newChain.remoteTokenAddress;\\n\\n      for (uint256 j = 0; j \\u003c newChain.remotePoolAddresses.length; ++j) {\\n        _setRemotePool(newChain.remoteChainSelector, newChain.remotePoolAddresses[j]);\\n      }\\n\\n      emit ChainAdded(\\n        newChain.remoteChainSelector,\\n        newChain.remoteTokenAddress,\\n        newChain.outboundRateLimiterConfig,\\n        newChain.inboundRateLimiterConfig\\n      );\\n    }\\n  }\\n\\n  /// @notice Adds a pool address to the allowed remote token pools for a particular chain.\\n  /// @param remoteChainSelector The remote chain selector for which the remote pool address is being added.\\n  /// @param remotePoolAddress The address of the new remote pool.\\n  function _setRemotePool(uint64 remoteChainSelector, bytes memory remotePoolAddress) internal {\\n    if (remotePoolAddress.length == 0) {\\n      revert ZeroAddressNotAllowed();\\n    }\\n\\n    bytes32 poolHash = keccak256(remotePoolAddress);\\n\\n    // Check if the pool already exists.\\n    if (!s_remoteChainConfigs[remoteChainSelector].remotePools.add(poolHash)) {\\n      revert PoolAlreadyAdded(remoteChainSelector, remotePoolAddress);\\n    }\\n\\n    // Add the pool to the mapping to be able to un-hash it later.\\n    s_remotePoolAddresses[poolHash] = remotePoolAddress;\\n\\n    emit RemotePoolAdded(remoteChainSelector, remotePoolAddress);\\n  }\\n\\n  // ================================================================\\n  // │                        Rate limiting                         │\\n  // ================================================================\\n\\n  /// @dev The inbound rate limits should be slightly higher than the outbound rate limits. This is because many chains\\n  /// finalize blocks in batches. CCIP also commits messages in batches: the commit plugin bundles multiple messages in\\n  /// a single merkle root.\\n  /// Imagine the following scenario.\\n  /// - Chain A has an inbound and outbound rate limit of 100 tokens capacity and 1 token per second refill rate.\\n  /// - Chain B has an inbound and outbound rate limit of 100 tokens capacity and 1 token per second refill rate.\\n  ///\\n  /// At time 0:\\n  /// - Chain A sends 100 tokens to Chain B.\\n  /// At time 5:\\n  /// - Chain A sends 5 tokens to Chain B.\\n  /// At time 6:\\n  /// The epoch that contains blocks [0-5] is finalized.\\n  /// Both transactions will be included in the same merkle root and become executable at the same time. This means\\n  /// the token pool on chain B requires a capacity of 105 to successfully execute both messages at the same time.\\n  /// The exact additional capacity required depends on the refill rate and the size of the source chain epochs and the\\n  /// CCIP round time. For simplicity, a 5-10% buffer should be sufficient in most cases.\\n\\n  /// @notice Sets the rate limiter admin address.\\n  /// @dev Only callable by the owner.\\n  /// @param rateLimitAdmin The new rate limiter admin address.\\n  function setRateLimitAdmin(\\n    address rateLimitAdmin\\n  ) external onlyOwner {\\n    s_rateLimitAdmin = rateLimitAdmin;\\n    emit RateLimitAdminSet(rateLimitAdmin);\\n  }\\n\\n  /// @notice Gets the rate limiter admin address.\\n  function getRateLimitAdmin() external view returns (address) {\\n    return s_rateLimitAdmin;\\n  }\\n\\n  /// @notice Consumes outbound rate limiting capacity in this pool\\n  function _consumeOutboundRateLimit(uint64 remoteChainSelector, uint256 amount) internal {\\n    s_remoteChainConfigs[remoteChainSelector].outboundRateLimiterConfig._consume(amount, address(i_token));\\n  }\\n\\n  /// @notice Consumes inbound rate limiting capacity in this pool\\n  function _consumeInboundRateLimit(uint64 remoteChainSelector, uint256 amount) internal {\\n    s_remoteChainConfigs[remoteChainSelector].inboundRateLimiterConfig._consume(amount, address(i_token));\\n  }\\n\\n  /// @notice Gets the token bucket with its values for the block it was requested at.\\n  /// @return The token bucket.\\n  function getCurrentOutboundRateLimiterState(\\n    uint64 remoteChainSelector\\n  ) external view returns (RateLimiter.TokenBucket memory) {\\n    return s_remoteChainConfigs[remoteChainSelector].outboundRateLimiterConfig._currentTokenBucketState();\\n  }\\n\\n  /// @notice Gets the token bucket with its values for the block it was requested at.\\n  /// @return The token bucket.\\n  function getCurrentInboundRateLimiterState(\\n    uint64 remoteChainSelector\\n  ) external view returns (RateLimiter.TokenBucket memory) {\\n    return s_remoteChainConfigs[remoteChainSelector].inboundRateLimiterConfig._currentTokenBucketState();\\n  }\\n\\n  /// @notice Sets multiple chain rate limiter configs.\\n  /// @param remoteChainSelectors The remote chain selector for which the rate limits apply.\\n  /// @param outboundConfigs The new outbound rate limiter config, meaning the onRamp rate limits for the given chain.\\n  /// @param inboundConfigs The new inbound rate limiter config, meaning the offRamp rate limits for the given chain.\\n  function setChainRateLimiterConfigs(\\n    uint64[] calldata remoteChainSelectors,\\n    RateLimiter.Config[] calldata outboundConfigs,\\n    RateLimiter.Config[] calldata inboundConfigs\\n  ) external {\\n    if (msg.sender != s_rateLimitAdmin \\u0026\\u0026 msg.sender != owner()) revert Unauthorized(msg.sender);\\n    if (remoteChainSelectors.length != outboundConfigs.length || remoteChainSelectors.length != inboundConfigs.length) {\\n      revert MismatchedArrayLengths();\\n    }\\n\\n    for (uint256 i = 0; i \\u003c remoteChainSelectors.length; ++i) {\\n      _setRateLimitConfig(remoteChainSelectors[i], outboundConfigs[i], inboundConfigs[i]);\\n    }\\n  }\\n\\n  /// @notice Sets the chain rate limiter config.\\n  /// @param remoteChainSelector The remote chain selector for which the rate limits apply.\\n  /// @param outboundConfig The new outbound rate limiter config, meaning the onRamp rate limits for the given chain.\\n  /// @param inboundConfig The new inbound rate limiter config, meaning the offRamp rate limits for the given chain.\\n  function setChainRateLimiterConfig(\\n    uint64 remoteChainSelector,\\n    RateLimiter.Config memory outboundConfig,\\n    RateLimiter.Config memory inboundConfig\\n  ) external {\\n    if (msg.sender != s_rateLimitAdmin \\u0026\\u0026 msg.sender != owner()) revert Unauthorized(msg.sender);\\n\\n    _setRateLimitConfig(remoteChainSelector, outboundConfig, inboundConfig);\\n  }\\n\\n  function _setRateLimitConfig(\\n    uint64 remoteChainSelector,\\n    RateLimiter.Config memory outboundConfig,\\n    RateLimiter.Config memory inboundConfig\\n  ) internal {\\n    if (!isSupportedChain(remoteChainSelector)) revert NonExistentChain(remoteChainSelector);\\n    RateLimiter._validateTokenBucketConfig(outboundConfig, false);\\n    s_remoteChainConfigs[remoteChainSelector].outboundRateLimiterConfig._setTokenBucketConfig(outboundConfig);\\n    RateLimiter._validateTokenBucketConfig(inboundConfig, false);\\n    s_remoteChainConfigs[remoteChainSelector].inboundRateLimiterConfig._setTokenBucketConfig(inboundConfig);\\n    emit ChainConfigured(remoteChainSelector, outboundConfig, inboundConfig);\\n  }\\n\\n  // ================================================================\\n  // │                           Access                             │\\n  // ================================================================\\n\\n  /// @notice Checks whether remote chain selector is configured on this contract, and if the msg.sender\\n  /// is a permissioned onRamp for the given chain on the Router.\\n  function _onlyOnRamp(\\n    uint64 remoteChainSelector\\n  ) internal view {\\n    if (!isSupportedChain(remoteChainSelector)) revert ChainNotAllowed(remoteChainSelector);\\n    if (!(msg.sender == s_router.getOnRamp(remoteChainSelector))) revert CallerIsNotARampOnRouter(msg.sender);\\n  }\\n\\n  /// @notice Checks whether remote chain selector is configured on this contract, and if the msg.sender\\n  /// is a permissioned offRamp for the given chain on the Router.\\n  function _onlyOffRamp(\\n    uint64 remoteChainSelector\\n  ) internal view {\\n    if (!isSupportedChain(remoteChainSelector)) revert ChainNotAllowed(remoteChainSelector);\\n    if (!s_router.isOffRamp(remoteChainSelector, msg.sender)) revert CallerIsNotARampOnRouter(msg.sender);\\n  }\\n\\n  // ================================================================\\n  // │                          Allowlist                           │\\n  // ================================================================\\n\\n  function _checkAllowList(\\n    address sender\\n  ) internal view {\\n    if (i_allowlistEnabled) {\\n      if (!s_allowlist.contains(sender)) {\\n        revert SenderNotAllowed(sender);\\n      }\\n    }\\n  }\\n\\n  /// @notice Gets whether the allowlist functionality is enabled.\\n  /// @return true is enabled, false if not.\\n  function getAllowListEnabled() external view returns (bool) {\\n    return i_allowlistEnabled;\\n  }\\n\\n  /// @notice Gets the allowed addresses.\\n  /// @return The allowed addresses.\\n  function getAllowList() external view returns (address[] memory) {\\n    return s_allowlist.values();\\n  }\\n\\n  /// @notice Apply updates to the allow list.\\n  /// @param removes The addresses to be removed.\\n  /// @param adds The addresses to be added.\\n  function applyAllowListUpdates(address[] calldata removes, address[] calldata adds) external onlyOwner {\\n    _applyAllowListUpdates(removes, adds);\\n  }\\n\\n  /// @notice Internal version of applyAllowListUpdates to allow for reuse in the constructor.\\n  function _applyAllowListUpdates(address[] memory removes, address[] memory adds) internal {\\n    if (!i_allowlistEnabled) revert AllowListNotEnabled();\\n\\n    for (uint256 i = 0; i \\u003c removes.length; ++i) {\\n      address toRemove = removes[i];\\n      if (s_allowlist.remove(toRemove)) {\\n        emit AllowListRemove(toRemove);\\n      }\\n    }\\n    for (uint256 i = 0; i \\u003c adds.length; ++i) {\\n      address toAdd = adds[i];\\n      if (toAdd == address(0)) {\\n        continue;\\n      }\\n      if (s_allowlist.add(toAdd)) {\\n        emit AllowListAdd(toAdd);\\n      }\\n    }\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IERC20} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\n\\ninterface IBurnMintERC20 is IERC20 {\\n  /// @notice Mints new tokens for a given address.\\n  /// @param account The address to mint the new tokens to.\\n  /// @param amount The number of tokens to be minted.\\n  /// @dev this function increases the total supply.\\n  function mint(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from the sender.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burnFrom(address account, uint256 amount) external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.6.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20 {\\n  /**\\n   * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n   * another (`to`).\\n   *\\n   * Note that `value` may be zero.\\n   */\\n  event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n  /**\\n   * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n   * a call to {approve}. `value` is the new allowance.\\n   */\\n  event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n  /**\\n   * @dev Returns the amount of tokens in existence.\\n   */\\n  function totalSupply() external view returns (uint256);\\n\\n  /**\\n   * @dev Returns the amount of tokens owned by `account`.\\n   */\\n  function balanceOf(address account) external view returns (uint256);\\n\\n  /**\\n   * @dev Moves `amount` tokens from the caller's account to `to`.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transfer(address to, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Returns the remaining number of tokens that `spender` will be\\n   * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n   * zero by default.\\n   *\\n   * This value changes when {approve} or {transferFrom} are called.\\n   */\\n  function allowance(address owner, address spender) external view returns (uint256);\\n\\n  /**\\n   * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n   * that someone may use both the old and the new allowance by unfortunate\\n   * transaction ordering. One possible solution to mitigate this race\\n   * condition is to first reduce the spender's allowance to 0 and set the\\n   * desired value afterwards:\\n   * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n   *\\n   * Emits an {Approval} event.\\n   */\\n  function approve(address spender, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Moves `amount` tokens from `from` to `to` using the\\n   * allowance mechanism. `amount` is then deducted from the caller's\\n   * allowance.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transferFrom(address from, address to, uint256 amount) external returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC20/extensions/IERC20Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC20.sol\\\";\\n\\n/**\\n * @dev Interface for the optional metadata functions from the ERC20 standard.\\n *\\n * _Available since v4.1._\\n */\\ninterface IERC20Metadata is IERC20 {\\n  /**\\n   * @dev Returns the name of the token.\\n   */\\n  function name() external view returns (string memory);\\n\\n  /**\\n   * @dev Returns the symbol of the token.\\n   */\\n  function symbol() external view returns (string memory);\\n\\n  /**\\n   * @dev Returns the decimals places of the token.\\n   */\\n  function decimals() external view returns (uint8);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/draft-IERC20Permit.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC20/extensions/draft-IERC20Permit.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 Permit extension allowing approvals to be made via signatures, as defined in\\n * https://eips.ethereum.org/EIPS/eip-2612[EIP-2612].\\n *\\n * Adds the {permit} method, which can be used to change an account's ERC20 allowance (see {IERC20-allowance}) by\\n * presenting a message signed by the account. By not relying on {IERC20-approve}, the token holder account doesn't\\n * need to send a transaction, and thus is not required to hold Ether at all.\\n */\\ninterface IERC20Permit {\\n  /**\\n   * @dev Sets `value` as the allowance of `spender` over ``owner``'s tokens,\\n   * given ``owner``'s signed approval.\\n   *\\n   * IMPORTANT: The same issues {IERC20-approve} has related to transaction\\n   * ordering also apply here.\\n   *\\n   * Emits an {Approval} event.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   * - `deadline` must be a timestamp in the future.\\n   * - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner`\\n   * over the EIP712-formatted function arguments.\\n   * - the signature must use ``owner``'s current nonce (see {nonces}).\\n   *\\n   * For more information on the signature format, see the\\n   * https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP\\n   * section].\\n   */\\n  function permit(\\n    address owner,\\n    address spender,\\n    uint256 value,\\n    uint256 deadline,\\n    uint8 v,\\n    bytes32 r,\\n    bytes32 s\\n  ) external;\\n\\n  /**\\n   * @dev Returns the current nonce for `owner`. This value must be\\n   * included whenever a signature is generated for {permit}.\\n   *\\n   * Every successful call to {permit} increases ``owner``'s nonce by one. This\\n   * prevents a signature from being used multiple times.\\n   */\\n  function nonces(address owner) external view returns (uint256);\\n\\n  /**\\n   * @dev Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}.\\n   */\\n  // solhint-disable-next-line func-name-mixedcase\\n  function DOMAIN_SEPARATOR() external view returns (bytes32);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/utils/SafeERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (token/ERC20/utils/SafeERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC20.sol\\\";\\nimport \\\"../extensions/draft-IERC20Permit.sol\\\";\\nimport \\\"../../../utils/Address.sol\\\";\\n\\n/**\\n * @title SafeERC20\\n * @dev Wrappers around ERC20 operations that throw on failure (when the token\\n * contract returns false). Tokens that return no value (and instead revert or\\n * throw on failure) are also supported, non-reverting calls are assumed to be\\n * successful.\\n * To use this library you can add a `using SafeERC20 for IERC20;` statement to your contract,\\n * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.\\n */\\nlibrary SafeERC20 {\\n  using Address for address;\\n\\n  function safeTransfer(IERC20 token, address to, uint256 value) internal {\\n    _callOptionalReturn(token, abi.encodeWithSelector(token.transfer.selector, to, value));\\n  }\\n\\n  function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {\\n    _callOptionalReturn(token, abi.encodeWithSelector(token.transferFrom.selector, from, to, value));\\n  }\\n\\n  /**\\n   * @dev Deprecated. This function has issues similar to the ones found in\\n   * {IERC20-approve}, and its usage is discouraged.\\n   *\\n   * Whenever possible, use {safeIncreaseAllowance} and\\n   * {safeDecreaseAllowance} instead.\\n   */\\n  function safeApprove(IERC20 token, address spender, uint256 value) internal {\\n    // safeApprove should only be called when setting an initial allowance,\\n    // or when resetting it to zero. To increase and decrease it, use\\n    // 'safeIncreaseAllowance' and 'safeDecreaseAllowance'\\n    require(\\n      (value == 0) || (token.allowance(address(this), spender) == 0),\\n      \\\"SafeERC20: approve from non-zero to non-zero allowance\\\"\\n    );\\n    _callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, value));\\n  }\\n\\n  function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n    uint256 newAllowance = token.allowance(address(this), spender) + value;\\n    _callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\\n  }\\n\\n  function safeDecreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n    unchecked {\\n      uint256 oldAllowance = token.allowance(address(this), spender);\\n      require(oldAllowance \\u003e= value, \\\"SafeERC20: decreased allowance below zero\\\");\\n      uint256 newAllowance = oldAllowance - value;\\n      _callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\\n    }\\n  }\\n\\n  function safePermit(\\n    IERC20Permit token,\\n    address owner,\\n    address spender,\\n    uint256 value,\\n    uint256 deadline,\\n    uint8 v,\\n    bytes32 r,\\n    bytes32 s\\n  ) internal {\\n    uint256 nonceBefore = token.nonces(owner);\\n    token.permit(owner, spender, value, deadline, v, r, s);\\n    uint256 nonceAfter = token.nonces(owner);\\n    require(nonceAfter == nonceBefore + 1, \\\"SafeERC20: permit did not succeed\\\");\\n  }\\n\\n  /**\\n   * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n   * on the return value: the return value is optional (but if data is returned, it must not be false).\\n   * @param token The token targeted by the call.\\n   * @param data The call data (encoded using abi.encode or one of its variants).\\n   */\\n  function _callOptionalReturn(IERC20 token, bytes memory data) private {\\n    // We need to perform a low level call here, to bypass Solidity's return data size checking mechanism, since\\n    // we're implementing it ourselves. We use {Address-functionCall} to perform this call, which verifies that\\n    // the target address contains contract code and also asserts for success in the low-level call.\\n\\n    bytes memory returndata = address(token).functionCall(data, \\\"SafeERC20: low-level call failed\\\");\\n    if (returndata.length \\u003e 0) {\\n      // Return data is optional\\n      require(abi.decode(returndata, (bool)), \\\"SafeERC20: ERC20 operation did not succeed\\\");\\n    }\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Address.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/Address.sol)\\n\\npragma solidity ^0.8.1;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary Address {\\n  /**\\n   * @dev Returns true if `account` is a contract.\\n   *\\n   * [IMPORTANT]\\n   * ====\\n   * It is unsafe to assume that an address for which this function returns\\n   * false is an externally-owned account (EOA) and not a contract.\\n   *\\n   * Among others, `isContract` will return false for the following\\n   * types of addresses:\\n   *\\n   *  - an externally-owned account\\n   *  - a contract in construction\\n   *  - an address where a contract will be created\\n   *  - an address where a contract lived, but was destroyed\\n   * ====\\n   *\\n   * [IMPORTANT]\\n   * ====\\n   * You shouldn't rely on `isContract` to protect against flash loan attacks!\\n   *\\n   * Preventing calls from contracts is highly discouraged. It breaks composability, breaks support for smart wallets\\n   * like Gnosis Safe, and does not provide security since it can be circumvented by calling from a contract\\n   * constructor.\\n   * ====\\n   */\\n  function isContract(address account) internal view returns (bool) {\\n    // This method relies on extcodesize/address.code.length, which returns 0\\n    // for contracts in construction, since the code is only stored at the end\\n    // of the constructor execution.\\n\\n    return account.code.length \\u003e 0;\\n  }\\n\\n  /**\\n   * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n   * `recipient`, forwarding all available gas and reverting on errors.\\n   *\\n   * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n   * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n   * imposed by `transfer`, making them unable to receive funds via\\n   * `transfer`. {sendValue} removes this limitation.\\n   *\\n   * https://diligence.consensys.net/posts/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n   *\\n   * IMPORTANT: because control is transferred to `recipient`, care must be\\n   * taken to not create reentrancy vulnerabilities. Consider using\\n   * {ReentrancyGuard} or the\\n   * https://solidity.readthedocs.io/en/v0.5.11/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n   */\\n  function sendValue(address payable recipient, uint256 amount) internal {\\n    require(address(this).balance \\u003e= amount, \\\"Address: insufficient balance\\\");\\n\\n    (bool success, ) = recipient.call{value: amount}(\\\"\\\");\\n    require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n  }\\n\\n  /**\\n   * @dev Performs a Solidity function call using a low level `call`. A\\n   * plain `call` is an unsafe replacement for a function call: use this\\n   * function instead.\\n   *\\n   * If `target` reverts with a revert reason, it is bubbled up by this\\n   * function (like regular Solidity function calls).\\n   *\\n   * Returns the raw returned data. To convert to the expected return value,\\n   * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].\\n   *\\n   * Requirements:\\n   *\\n   * - `target` must be a contract.\\n   * - calling `target` with `data` must not revert.\\n   *\\n   * _Available since v3.1._\\n   */\\n  function functionCall(address target, bytes memory data) internal returns (bytes memory) {\\n    return functionCallWithValue(target, data, 0, \\\"Address: low-level call failed\\\");\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`], but with\\n   * `errorMessage` as a fallback revert reason when `target` reverts.\\n   *\\n   * _Available since v3.1._\\n   */\\n  function functionCall(address target, bytes memory data, string memory errorMessage) internal returns (bytes memory) {\\n    return functionCallWithValue(target, data, 0, errorMessage);\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n   * but also transferring `value` wei to `target`.\\n   *\\n   * Requirements:\\n   *\\n   * - the calling contract must have an ETH balance of at least `value`.\\n   * - the called Solidity function must be `payable`.\\n   *\\n   * _Available since v3.1._\\n   */\\n  function functionCallWithValue(address target, bytes memory data, uint256 value) internal returns (bytes memory) {\\n    return functionCallWithValue(target, data, value, \\\"Address: low-level call with value failed\\\");\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCallWithValue-address-bytes-uint256-}[`functionCallWithValue`], but\\n   * with `errorMessage` as a fallback revert reason when `target` reverts.\\n   *\\n   * _Available since v3.1._\\n   */\\n  function functionCallWithValue(\\n    address target,\\n    bytes memory data,\\n    uint256 value,\\n    string memory errorMessage\\n  ) internal returns (bytes memory) {\\n    require(address(this).balance \\u003e= value, \\\"Address: insufficient balance for call\\\");\\n    (bool success, bytes memory returndata) = target.call{value: value}(data);\\n    return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n   * but performing a static call.\\n   *\\n   * _Available since v3.3._\\n   */\\n  function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {\\n    return functionStaticCall(target, data, \\\"Address: low-level static call failed\\\");\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n   * but performing a static call.\\n   *\\n   * _Available since v3.3._\\n   */\\n  function functionStaticCall(\\n    address target,\\n    bytes memory data,\\n    string memory errorMessage\\n  ) internal view returns (bytes memory) {\\n    (bool success, bytes memory returndata) = target.staticcall(data);\\n    return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n   * but performing a delegate call.\\n   *\\n   * _Available since v3.4._\\n   */\\n  function functionDelegateCall(address target, bytes memory data) internal returns (bytes memory) {\\n    return functionDelegateCall(target, data, \\\"Address: low-level delegate call failed\\\");\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n   * but performing a delegate call.\\n   *\\n   * _Available since v3.4._\\n   */\\n  function functionDelegateCall(\\n    address target,\\n    bytes memory data,\\n    string memory errorMessage\\n  ) internal returns (bytes memory) {\\n    (bool success, bytes memory returndata) = target.delegatecall(data);\\n    return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n  }\\n\\n  /**\\n   * @dev Tool to verify that a low level call to smart-contract was successful, and revert (either by bubbling\\n   * the revert reason or using the provided one) in case of unsuccessful call or if target was not a contract.\\n   *\\n   * _Available since v4.8._\\n   */\\n  function verifyCallResultFromTarget(\\n    address target,\\n    bool success,\\n    bytes memory returndata,\\n    string memory errorMessage\\n  ) internal view returns (bytes memory) {\\n    if (success) {\\n      if (returndata.length == 0) {\\n        // only check isContract if the call was successful and the return data is empty\\n        // otherwise we already know that it was a contract\\n        require(isContract(target), \\\"Address: call to non-contract\\\");\\n      }\\n      return returndata;\\n    } else {\\n      _revert(returndata, errorMessage);\\n    }\\n  }\\n\\n  /**\\n   * @dev Tool to verify that a low level call was successful, and revert if it wasn't, either by bubbling the\\n   * revert reason or using the provided one.\\n   *\\n   * _Available since v4.3._\\n   */\\n  function verifyCallResult(\\n    bool success,\\n    bytes memory returndata,\\n    string memory errorMessage\\n  ) internal pure returns (bytes memory) {\\n    if (success) {\\n      return returndata;\\n    } else {\\n      _revert(returndata, errorMessage);\\n    }\\n  }\\n\\n  function _revert(bytes memory returndata, string memory errorMessage) private pure {\\n    // Look for revert reason and bubble it up if present\\n    if (returndata.length \\u003e 0) {\\n      // The easiest way to bubble the revert reason is using memory via assembly\\n      /// @solidity memory-safe-assembly\\n      assembly {\\n        let returndata_size := mload(returndata)\\n        revert(add(32, returndata), returndata_size)\\n      }\\n    } else {\\n      revert(errorMessage);\\n    }\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```solidity\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n    // To implement this library for multiple types with as little code\\n    // repetition as possible, we write it in terms of a generic Set type with\\n    // bytes32 values.\\n    // The Set implementation uses private functions, and user-facing\\n    // implementations (such as AddressSet) are just wrappers around the\\n    // underlying Set.\\n    // This means that we can only create new EnumerableSets for types that fit\\n    // in bytes32.\\n\\n    struct Set {\\n        // Storage of set values\\n        bytes32[] _values;\\n        // Position is the index of the value in the `values` array plus 1.\\n        // Position 0 is used to mean a value is not in the set.\\n        mapping(bytes32 value =\\u003e uint256) _positions;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function _add(Set storage set, bytes32 value) private returns (bool) {\\n        if (!_contains(set, value)) {\\n            set._values.push(value);\\n            // The value is stored at length-1, but we add 1 to all indexes\\n            // and use 0 as a sentinel value\\n            set._positions[value] = set._values.length;\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function _remove(Set storage set, bytes32 value) private returns (bool) {\\n        // We cache the value's position to prevent multiple reads from the same storage slot\\n        uint256 position = set._positions[value];\\n\\n        if (position != 0) {\\n            // Equivalent to contains(set, value)\\n            // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n            // the array, and then remove the last element (sometimes called as 'swap and pop').\\n            // This modifies the order of the array, as noted in {at}.\\n\\n            uint256 valueIndex = position - 1;\\n            uint256 lastIndex = set._values.length - 1;\\n\\n            if (valueIndex != lastIndex) {\\n                bytes32 lastValue = set._values[lastIndex];\\n\\n                // Move the lastValue to the index where the value to delete is\\n                set._values[valueIndex] = lastValue;\\n                // Update the tracked position of the lastValue (that was just moved)\\n                set._positions[lastValue] = position;\\n            }\\n\\n            // Delete the slot where the moved value was stored\\n            set._values.pop();\\n\\n            // Delete the tracked position for the deleted slot\\n            delete set._positions[value];\\n\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n        return set._positions[value] != 0;\\n    }\\n\\n    /**\\n     * @dev Returns the number of values on the set. O(1).\\n     */\\n    function _length(Set storage set) private view returns (uint256) {\\n        return set._values.length;\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n        return set._values[index];\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function _values(Set storage set) private view returns (bytes32[] memory) {\\n        return set._values;\\n    }\\n\\n    // Bytes32Set\\n\\n    struct Bytes32Set {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _add(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _remove(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n        return _contains(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(Bytes32Set storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n        return _at(set._inner, index);\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        bytes32[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // AddressSet\\n\\n    struct AddressSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(AddressSet storage set, address value) internal returns (bool) {\\n        return _add(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(AddressSet storage set, address value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(AddressSet storage set, address value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(AddressSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n        return address(uint160(uint256(_at(set._inner, index))));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(AddressSet storage set) internal view returns (address[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        address[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // UintSet\\n\\n    struct UintSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _add(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(UintSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n        return uint256(_at(set._inner, index));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(UintSet storage set) internal view returns (uint256[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        uint256[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n}\\n\"}}}"
