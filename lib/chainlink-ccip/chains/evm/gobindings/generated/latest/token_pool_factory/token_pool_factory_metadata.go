// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package token_pool_factory

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/interfaces/IGetCCIPAdmin.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IOwner.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IPool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IRMN.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IRouter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/ITokenAdminRegistry.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Client.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Pool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/RateLimiter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/pools/TokenPool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/tokenAdminRegistry/RegistryModuleOwnerCustom.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/tokenAdminRegistry/TokenPoolFactory/FactoryBurnMintERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/tokenAdminRegistry/TokenPoolFactory/TokenPoolFactory.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/ERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Context.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/access/AccessControl.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/access/IAccessControl.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/Context.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/Create2.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/Errors.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/ERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IGetCCIPAdmin.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IGetCCIPAdmin {\\n  /// @notice Returns the admin of the token.\\n  /// @dev This method is named to never conflict with existing methods.\\n  function getCCIPAdmin() external view returns (address);\\n}\\n\"},\"contracts/interfaces/IOwner.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwner {\\n  /// @notice Returns the owner of the contract.\\n  /// @dev This method is named to match with the OpenZeppelin Ownable contract.\\n  function owner() external view returns (address);\\n}\\n\"},\"contracts/interfaces/IPool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\n\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\\\";\\n\\n/// @notice Shared public interface for multiple V1 pool types.\\n/// Each pool type handles a different child token model e.g. lock/unlock, mint/burn.\\ninterface IPoolV1 is IERC165 {\\n  /// @notice Lock tokens into the pool or burn the tokens.\\n  /// @param lockOrBurnIn Encoded data fields for the processing of tokens on the source chain.\\n  /// @return lockOrBurnOut Encoded data fields for the processing of tokens on the destination chain.\\n  function lockOrBurn(\\n    Pool.LockOrBurnInV1 calldata lockOrBurnIn\\n  ) external returns (Pool.LockOrBurnOutV1 memory lockOrBurnOut);\\n\\n  /// @notice Releases or mints tokens to the receiver address.\\n  /// @param releaseOrMintIn All data required to release or mint tokens.\\n  /// @return releaseOrMintOut The amount of tokens released or minted on the local chain, denominated\\n  /// in the local token's decimals.\\n  /// @dev The offramp asserts that the balanceOf of the receiver has been incremented by exactly the number\\n  /// of tokens that is returned in ReleaseOrMintOutV1.destinationAmount. If the amounts do not match, the tx reverts.\\n  function releaseOrMint(\\n    Pool.ReleaseOrMintInV1 calldata releaseOrMintIn\\n  ) external returns (Pool.ReleaseOrMintOutV1 memory);\\n\\n  /// @notice Checks whether a remote chain is supported in the token pool.\\n  /// @param remoteChainSelector The selector of the remote chain.\\n  /// @return true if the given chain is a permissioned remote chain.\\n  function isSupportedChain(\\n    uint64 remoteChainSelector\\n  ) external view returns (bool);\\n\\n  /// @notice Returns if the token pool supports the given token.\\n  /// @param token The address of the token.\\n  /// @return true if the token is supported by the pool.\\n  function isSupportedToken(\\n    address token\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/IRMN.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This interface contains the only RMN-related functions that might be used on-chain by other CCIP contracts.\\ninterface IRMN {\\n  /// @notice A Merkle root tagged with the address of the commit store contract it is destined for.\\n  struct TaggedRoot {\\n    address commitStore;\\n    bytes32 root;\\n  }\\n\\n  /// @notice Callers MUST NOT cache the return value as a blessed tagged root could become unblessed.\\n  function isBlessed(\\n    TaggedRoot calldata taggedRoot\\n  ) external view returns (bool);\\n\\n  /// @notice Iff there is an active global or legacy curse, this function returns true.\\n  function isCursed() external view returns (bool);\\n\\n  /// @notice Iff there is an active global curse, or an active curse for `subject`, this function returns true.\\n  /// @param subject To check whether a particular chain is cursed, set to bytes16(uint128(chainSelector)).\\n  function isCursed(\\n    bytes16 subject\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/IRouter.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\n\\ninterface IRouter {\\n  error OnlyOffRamp();\\n\\n  /// @notice Route the message to its intended receiver contract.\\n  /// @param message Client.Any2EVMMessage struct.\\n  /// @param gasForCallExactCheck of params for exec.\\n  /// @param gasLimit set of params for exec.\\n  /// @param receiver set of params for exec.\\n  /// @dev if the receiver is a contracts that signals support for CCIP execution through EIP-165.\\n  /// the contract is called. If not, only tokens are transferred.\\n  /// @return success A boolean value indicating whether the ccip message was received without errors.\\n  /// @return retBytes A bytes array containing return data form CCIP receiver.\\n  /// @return gasUsed the gas used by the external customer call. Does not include any overhead.\\n  function routeMessage(\\n    Client.Any2EVMMessage calldata message,\\n    uint16 gasForCallExactCheck,\\n    uint256 gasLimit,\\n    address receiver\\n  ) external returns (bool success, bytes memory retBytes, uint256 gasUsed);\\n\\n  /// @notice Returns the configured onramp for a specific destination chain.\\n  /// @param destChainSelector The destination chain Id to get the onRamp for.\\n  /// @return onRampAddress The address of the onRamp.\\n  function getOnRamp(\\n    uint64 destChainSelector\\n  ) external view returns (address onRampAddress);\\n\\n  /// @notice Return true if the given offRamp is a configured offRamp for the given source chain.\\n  /// @param sourceChainSelector The source chain selector to check.\\n  /// @param offRamp The address of the offRamp to check.\\n  function isOffRamp(uint64 sourceChainSelector, address offRamp) external view returns (bool isOffRamp);\\n}\\n\"},\"contracts/interfaces/ITokenAdminRegistry.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.0;\\n\\ninterface ITokenAdminRegistry {\\n  /// @notice Returns the pool for the given token.\\n  function getPool(\\n    address token\\n  ) external view returns (address);\\n\\n  /// @notice Proposes an administrator for the given token as pending administrator.\\n  /// @param localToken The token to register the administrator for.\\n  /// @param administrator The administrator to register.\\n  function proposeAdministrator(address localToken, address administrator) external;\\n\\n  /// @notice Accepts the administrator role for a token.\\n  /// @param localToken The token to accept the administrator role for.\\n  /// @dev This function can only be called by the pending administrator.\\n  function acceptAdminRole(\\n    address localToken\\n  ) external;\\n\\n  /// @notice Sets the pool for a token. Setting the pool to address(0) effectively delists the token\\n  /// from CCIP. Setting the pool to any other address enables the token on CCIP.\\n  /// @param localToken The token to set the pool for.\\n  /// @param pool The pool to set for the token.\\n  function setPool(address localToken, address pool) external;\\n\\n  /// @notice Transfers the administrator role for a token to a new address with a 2-step process.\\n  /// @param localToken The token to transfer the administrator role for.\\n  /// @param newAdmin The address to transfer the administrator role to. Can be address(0) to cancel\\n  /// a pending transfer.\\n  /// @dev The new admin must call `acceptAdminRole` to accept the role.\\n  function transferAdminRole(address localToken, address newAdmin) external;\\n}\\n\"},\"contracts/libraries/Client.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n// End consumer library.\\nlibrary Client {\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct EVMTokenAmount {\\n    address token; // token address on the local chain.\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  struct Any2EVMMessage {\\n    bytes32 messageId; // MessageId corresponding to ccipSend on source.\\n    uint64 sourceChainSelector; // Source chain selector.\\n    bytes sender; // abi.decode(sender) if coming from an EVM chain.\\n    bytes data; // payload sent in original message.\\n    EVMTokenAmount[] destTokenAmounts; // Tokens and their amounts in their destination chain representation.\\n  }\\n\\n  // If extraArgs is empty bytes, the default is 200k gas limit.\\n  struct EVM2AnyMessage {\\n    bytes receiver; // abi.encode(receiver address) for dest EVM chains.\\n    bytes data; // Data payload.\\n    EVMTokenAmount[] tokenAmounts; // Token transfers.\\n    address feeToken; // Address of feeToken. address(0) means you will send msg.value.\\n    bytes extraArgs; // Populate this with _argsToBytes(EVMExtraArgsV2).\\n  }\\n\\n  // Tag to indicate only a gas limit. Only usable for EVM as destination chain.\\n  bytes4 public constant EVM_EXTRA_ARGS_V1_TAG = 0x97a657c9;\\n\\n  struct EVMExtraArgsV1 {\\n    uint256 gasLimit;\\n  }\\n\\n  function _argsToBytes(\\n    EVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(EVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n\\n  // Tag to indicate a gas limit (or dest chain equivalent processing units) and Out Of Order Execution. This tag is\\n  // available for multiple chain families. If there is no chain family specific tag, this is the default available\\n  // for a chain.\\n  // Note: not available for Solana VM based chains.\\n  bytes4 public constant GENERIC_EXTRA_ARGS_V2_TAG = 0x181dcf10;\\n\\n  /// @param gasLimit: gas limit for the callback on the destination chain.\\n  /// @param allowOutOfOrderExecution: if true, it indicates that the message can be executed in any order relative to\\n  /// other messages from the same sender. This value's default varies by chain. On some chains, a particular value is\\n  /// enforced, meaning if the expected value is not set, the message request will revert.\\n  /// @dev Fully compatible with the previously existing EVMExtraArgsV2.\\n  struct GenericExtraArgsV2 {\\n    uint256 gasLimit;\\n    bool allowOutOfOrderExecution;\\n  }\\n\\n  // Extra args tag for chains that use the Solana VM.\\n  bytes4 public constant SVM_EXTRA_ARGS_V1_TAG = 0x1f3b3aba;\\n\\n  struct SVMExtraArgsV1 {\\n    uint32 computeUnits;\\n    uint64 accountIsWritableBitmap;\\n    bool allowOutOfOrderExecution;\\n    bytes32 tokenReceiver;\\n    // Additional accounts needed for execution of CCIP receiver. Must be empty if message.receiver is zero.\\n    // Token transfer related accounts are specified in the token pool lookup table on SVM.\\n    bytes32[] accounts;\\n  }\\n\\n  /// @dev The maximum number of accounts that can be passed in SVMExtraArgs.\\n  uint256 public constant SVM_EXTRA_ARGS_MAX_ACCOUNTS = 64;\\n\\n  /// @dev The expected static payload size of a token transfer when Borsh encoded and submitted to SVM.\\n  /// TokenPool extra data and offchain data sizes are dynamic, and should be accounted for separately.\\n  uint256 public constant SVM_TOKEN_TRANSFER_DATA_OVERHEAD = (4 + 32) // source_pool\\n    + 32 // token_address\\n    + 4 // gas_amount\\n    + 4 // extra_data overhead\\n    + 32 // amount\\n    + 32 // size of the token lookup table account\\n    + 32 // token-related accounts in the lookup table, over-estimated to 32, typically between 11 - 13\\n    + 32 // token account belonging to the token receiver, e.g ATA, not included in the token lookup table\\n    + 32 // per-chain token pool config, not included in the token lookup table\\n    + 32 // per-chain token billing config, not always included in the token lookup table\\n    + 32; // OffRamp pool signer PDA, not included in the token lookup table\\n\\n  /// @dev Number of overhead accounts needed for message execution on SVM.\\n  /// @dev These are message.receiver, and the OffRamp Signer PDA specific to the receiver.\\n  uint256 public constant SVM_MESSAGING_ACCOUNTS_OVERHEAD = 2;\\n\\n  /// @dev The size of each SVM account address in bytes.\\n  uint256 public constant SVM_ACCOUNT_BYTE_SIZE = 32;\\n\\n  function _argsToBytes(\\n    GenericExtraArgsV2 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(GENERIC_EXTRA_ARGS_V2_TAG, extraArgs);\\n  }\\n\\n  function _svmArgsToBytes(\\n    SVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(SVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n}\\n\"},\"contracts/libraries/Pool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This library contains various token pool functions to aid constructing the return data.\\nlibrary Pool {\\n  // The tag used to signal support for the pool v1 standard.\\n  // bytes4(keccak256(\\\"CCIP_POOL_V1\\\"))\\n  bytes4 public constant CCIP_POOL_V1 = 0xaff2afbf;\\n\\n  // The number of bytes in the return data for a pool v1 releaseOrMint call.\\n  // This should match the size of the ReleaseOrMintOutV1 struct.\\n  uint16 public constant CCIP_POOL_V1_RET_BYTES = 32;\\n\\n  // The default max number of bytes in the return data for a pool v1 lockOrBurn call.\\n  // This data can be used to send information to the destination chain token pool. Can be overwritten\\n  // in the TokenTransferFeeConfig.destBytesOverhead if more data is required.\\n  uint32 public constant CCIP_LOCK_OR_BURN_V1_RET_BYTES = 32;\\n\\n  struct LockOrBurnInV1 {\\n    bytes receiver; //  The recipient of the tokens on the destination chain, abi encoded.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the destination chain.\\n    address originalSender; // ─────╯ The original sender of the tx on the source chain.\\n    uint256 amount; //  The amount of tokens to lock or burn, denominated in the source token's decimals.\\n    address localToken; //  The address on this chain of the token to lock or burn.\\n  }\\n\\n  struct LockOrBurnOutV1 {\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes destPoolData;\\n  }\\n\\n  struct ReleaseOrMintInV1 {\\n    bytes originalSender; //          The original sender of the tx on the source chain.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the source chain.\\n    address receiver; // ───────────╯ The recipient of the tokens on the destination chain.\\n    uint256 amount; //                The amount of tokens to release or mint, denominated in the source token's decimals.\\n    address localToken; //            The address on this chain of the token to release or mint.\\n    /// @dev WARNING: sourcePoolAddress should be checked prior to any processing of funds. Make sure it matches the\\n    /// expected pool address for the given remoteChainSelector.\\n    bytes sourcePoolAddress; //       The address of the source pool, abi encoded in the case of EVM chains.\\n    bytes sourcePoolData; //          The data received from the source pool to process the release or mint.\\n    /// @dev WARNING: offchainTokenData is untrusted data.\\n    bytes offchainTokenData; //       The offchain data to process the release or mint.\\n  }\\n\\n  struct ReleaseOrMintOutV1 {\\n    // The number of tokens released or minted on the destination chain, denominated in the local token's decimals.\\n    // This value is expected to be equal to the ReleaseOrMintInV1.amount in the case where the source and destination\\n    // chain have the same number of decimals.\\n    uint256 destinationAmount;\\n  }\\n}\\n\"},\"contracts/libraries/RateLimiter.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\n/// @notice Implements Token Bucket rate limiting.\\n/// @dev uint128 is safe for rate limiter state.\\n/// - For USD value rate limiting, it can adequately store USD value in 18 decimals.\\n/// - For ERC20 token amount rate limiting, all tokens that will be listed will have at most a supply of uint128.max\\n/// tokens, and it will therefore not overflow the bucket. In exceptional scenarios where tokens consumed may be larger\\n/// than uint128, e.g. compromised issuer, an enabled RateLimiter will check and revert.\\nlibrary RateLimiter {\\n  error BucketOverfilled();\\n  error OnlyCallableByAdminOrOwner();\\n  error TokenMaxCapacityExceeded(uint256 capacity, uint256 requested, address tokenAddress);\\n  error TokenRateLimitReached(uint256 minWaitInSeconds, uint256 available, address tokenAddress);\\n  error AggregateValueMaxCapacityExceeded(uint256 capacity, uint256 requested);\\n  error AggregateValueRateLimitReached(uint256 minWaitInSeconds, uint256 available);\\n  error InvalidRateLimitRate(Config rateLimiterConfig);\\n  error DisabledNonZeroRateLimit(Config config);\\n  error RateLimitMustBeDisabled();\\n\\n  event TokensConsumed(uint256 tokens);\\n  event ConfigChanged(Config config);\\n\\n  struct TokenBucket {\\n    uint128 tokens; // ──────╮ Current number of tokens that are in the bucket.\\n    uint32 lastUpdated; //   │ Timestamp in seconds of the last token refill, good for 100+ years.\\n    bool isEnabled; // ──────╯ Indication whether the rate limiting is enabled or not.\\n    uint128 capacity; // ────╮ Maximum number of tokens that can be in the bucket.\\n    uint128 rate; // ────────╯ Number of tokens per second that the bucket is refilled.\\n  }\\n\\n  struct Config {\\n    bool isEnabled; // Indication whether the rate limiting should be enabled.\\n    uint128 capacity; // ────╮ Specifies the capacity of the rate limiter.\\n    uint128 rate; //  ───────╯ Specifies the rate of the rate limiter.\\n  }\\n\\n  /// @notice _consume removes the given tokens from the pool, lowering the rate tokens allowed to be\\n  /// consumed for subsequent calls.\\n  /// @param requestTokens The total tokens to be consumed from the bucket.\\n  /// @param tokenAddress The token to consume capacity for, use 0x0 to indicate aggregate value capacity.\\n  /// @dev Reverts when requestTokens exceeds bucket capacity or available tokens in the bucket.\\n  /// @dev emits removal of requestTokens if requestTokens is \\u003e 0.\\n  function _consume(TokenBucket storage s_bucket, uint256 requestTokens, address tokenAddress) internal {\\n    // If there is no value to remove or rate limiting is turned off, skip this step to reduce gas usage.\\n    if (!s_bucket.isEnabled || requestTokens == 0) {\\n      return;\\n    }\\n\\n    uint256 tokens = s_bucket.tokens;\\n    uint256 capacity = s_bucket.capacity;\\n    uint256 timeDiff = block.timestamp - s_bucket.lastUpdated;\\n\\n    if (timeDiff != 0) {\\n      if (tokens \\u003e capacity) revert BucketOverfilled();\\n\\n      // Refill tokens when arriving at a new block time.\\n      tokens = _calculateRefill(capacity, tokens, timeDiff, s_bucket.rate);\\n\\n      s_bucket.lastUpdated = uint32(block.timestamp);\\n    }\\n\\n    if (capacity \\u003c requestTokens) {\\n      // Token address 0 indicates consuming aggregate value rate limit capacity.\\n      if (tokenAddress == address(0)) revert AggregateValueMaxCapacityExceeded(capacity, requestTokens);\\n      revert TokenMaxCapacityExceeded(capacity, requestTokens, tokenAddress);\\n    }\\n    if (tokens \\u003c requestTokens) {\\n      uint256 rate = s_bucket.rate;\\n      // Wait required until the bucket is refilled enough to accept this value, round up to next higher second.\\n      // Consume is not guaranteed to succeed after wait time passes if there is competing traffic.\\n      // This acts as a lower bound of wait time.\\n      uint256 minWaitInSeconds = ((requestTokens - tokens) + (rate - 1)) / rate;\\n\\n      if (tokenAddress == address(0)) revert AggregateValueRateLimitReached(minWaitInSeconds, tokens);\\n      revert TokenRateLimitReached(minWaitInSeconds, tokens, tokenAddress);\\n    }\\n    tokens -= requestTokens;\\n\\n    // Downcast is safe here, as tokens is not larger than capacity.\\n    s_bucket.tokens = uint128(tokens);\\n    emit TokensConsumed(requestTokens);\\n  }\\n\\n  /// @notice Gets the token bucket with its values for the block it was requested at.\\n  /// @return The token bucket.\\n  function _currentTokenBucketState(\\n    TokenBucket memory bucket\\n  ) internal view returns (TokenBucket memory) {\\n    // We update the bucket to reflect the status at the exact time of the call. This means we might need to refill a\\n    // part of the bucket based on the time that has passed since the last update.\\n    bucket.tokens =\\n      uint128(_calculateRefill(bucket.capacity, bucket.tokens, block.timestamp - bucket.lastUpdated, bucket.rate));\\n    bucket.lastUpdated = uint32(block.timestamp);\\n    return bucket;\\n  }\\n\\n  /// @notice Sets the rate limited config.\\n  /// @param s_bucket The token bucket.\\n  /// @param config The new config.\\n  function _setTokenBucketConfig(TokenBucket storage s_bucket, Config memory config) internal {\\n    // First update the bucket to make sure the proper rate is used for all the time up until the config change.\\n    uint256 timeDiff = block.timestamp - s_bucket.lastUpdated;\\n    if (timeDiff != 0) {\\n      s_bucket.tokens = uint128(_calculateRefill(s_bucket.capacity, s_bucket.tokens, timeDiff, s_bucket.rate));\\n\\n      s_bucket.lastUpdated = uint32(block.timestamp);\\n    }\\n\\n    s_bucket.tokens = uint128(_min(config.capacity, s_bucket.tokens));\\n    s_bucket.isEnabled = config.isEnabled;\\n    s_bucket.capacity = config.capacity;\\n    s_bucket.rate = config.rate;\\n\\n    emit ConfigChanged(config);\\n  }\\n\\n  /// @notice Validates the token bucket config.\\n  function _validateTokenBucketConfig(Config memory config, bool mustBeDisabled) internal pure {\\n    if (config.isEnabled) {\\n      if (config.rate \\u003e= config.capacity || config.rate == 0) {\\n        revert InvalidRateLimitRate(config);\\n      }\\n      if (mustBeDisabled) {\\n        revert RateLimitMustBeDisabled();\\n      }\\n    } else {\\n      if (config.rate != 0 || config.capacity != 0) {\\n        revert DisabledNonZeroRateLimit(config);\\n      }\\n    }\\n  }\\n\\n  /// @notice Calculate refilled tokens.\\n  /// @param capacity bucket capacity.\\n  /// @param tokens current bucket tokens.\\n  /// @param timeDiff block time difference since last refill.\\n  /// @param rate bucket refill rate.\\n  /// @return the value of tokens after refill.\\n  function _calculateRefill(\\n    uint256 capacity,\\n    uint256 tokens,\\n    uint256 timeDiff,\\n    uint256 rate\\n  ) private pure returns (uint256) {\\n    return _min(capacity, tokens + timeDiff * rate);\\n  }\\n\\n  /// @notice Return the smallest of two integers.\\n  /// @param a first int.\\n  /// @param b second int.\\n  /// @return smallest.\\n  function _min(uint256 a, uint256 b) internal pure returns (uint256) {\\n    return a \\u003c b ? a : b;\\n  }\\n}\\n\"},\"contracts/pools/TokenPool.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IPoolV1} from \\\"../interfaces/IPool.sol\\\";\\nimport {IRMN} from \\\"../interfaces/IRMN.sol\\\";\\nimport {IRouter} from \\\"../interfaces/IRouter.sol\\\";\\n\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\nimport {RateLimiter} from \\\"../libraries/RateLimiter.sol\\\";\\nimport {Ownable2StepMsgSender} from \\\"@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\\\";\\n\\nimport {IERC20} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\nimport {IERC20Metadata} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\\\";\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\\\";\\nimport {EnumerableSet} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @notice Base abstract class with common functions for all token pools.\\n/// A token pool serves as isolated place for holding tokens and token specific logic\\n/// that may execute as tokens move across the bridge.\\n/// @dev This pool supports different decimals on different chains but using this feature could impact the total number\\n/// of tokens in circulation. Since all of the tokens are locked/burned on the source, and a rounded amount is\\n/// minted/released on the destination, the number of tokens minted/released could be less than the number of tokens\\n/// burned/locked. This is because the source chain does not know about the destination token decimals. This is not a\\n/// problem if the decimals are the same on both chains.\\n///\\n/// Example:\\n/// Assume there is a token with 6 decimals on chain A and 3 decimals on chain B.\\n/// - 1.234567 tokens are burned on chain A.\\n/// - 1.234    tokens are minted on chain B.\\n/// When sending the 1.234 tokens back to chain A, you will receive 1.234000 tokens on chain A, effectively losing\\n/// 0.000567 tokens.\\n/// In the case of a burnMint pool on chain A, these funds are burned in the pool on chain A.\\n/// In the case of a lockRelease pool on chain A, these funds accumulate in the pool on chain A.\\nabstract contract TokenPool is IPoolV1, Ownable2StepMsgSender {\\n  using EnumerableSet for EnumerableSet.Bytes32Set;\\n  using EnumerableSet for EnumerableSet.AddressSet;\\n  using EnumerableSet for EnumerableSet.UintSet;\\n  using RateLimiter for RateLimiter.TokenBucket;\\n\\n  error CallerIsNotARampOnRouter(address caller);\\n  error ZeroAddressNotAllowed();\\n  error SenderNotAllowed(address sender);\\n  error AllowListNotEnabled();\\n  error NonExistentChain(uint64 remoteChainSelector);\\n  error ChainNotAllowed(uint64 remoteChainSelector);\\n  error CursedByRMN();\\n  error ChainAlreadyExists(uint64 chainSelector);\\n  error InvalidSourcePoolAddress(bytes sourcePoolAddress);\\n  error InvalidToken(address token);\\n  error Unauthorized(address caller);\\n  error PoolAlreadyAdded(uint64 remoteChainSelector, bytes remotePoolAddress);\\n  error InvalidRemotePoolForChain(uint64 remoteChainSelector, bytes remotePoolAddress);\\n  error InvalidRemoteChainDecimals(bytes sourcePoolData);\\n  error MismatchedArrayLengths();\\n  error OverflowDetected(uint8 remoteDecimals, uint8 localDecimals, uint256 remoteAmount);\\n  error InvalidDecimalArgs(uint8 expected, uint8 actual);\\n\\n  event Locked(address indexed sender, uint256 amount);\\n  event Burned(address indexed sender, uint256 amount);\\n  event Released(address indexed sender, address indexed recipient, uint256 amount);\\n  event Minted(address indexed sender, address indexed recipient, uint256 amount);\\n  event ChainAdded(\\n    uint64 remoteChainSelector,\\n    bytes remoteToken,\\n    RateLimiter.Config outboundRateLimiterConfig,\\n    RateLimiter.Config inboundRateLimiterConfig\\n  );\\n  event ChainConfigured(\\n    uint64 remoteChainSelector,\\n    RateLimiter.Config outboundRateLimiterConfig,\\n    RateLimiter.Config inboundRateLimiterConfig\\n  );\\n  event ChainRemoved(uint64 remoteChainSelector);\\n  event RemotePoolAdded(uint64 indexed remoteChainSelector, bytes remotePoolAddress);\\n  event RemotePoolRemoved(uint64 indexed remoteChainSelector, bytes remotePoolAddress);\\n  event AllowListAdd(address sender);\\n  event AllowListRemove(address sender);\\n  event RouterUpdated(address oldRouter, address newRouter);\\n  event RateLimitAdminSet(address rateLimitAdmin);\\n\\n  struct ChainUpdate {\\n    uint64 remoteChainSelector; // Remote chain selector\\n    bytes[] remotePoolAddresses; // Address of the remote pool, ABI encoded in the case of a remote EVM chain.\\n    bytes remoteTokenAddress; // Address of the remote token, ABI encoded in the case of a remote EVM chain.\\n    RateLimiter.Config outboundRateLimiterConfig; // Outbound rate limited config, meaning the rate limits for all of the onRamps for the given chain\\n    RateLimiter.Config inboundRateLimiterConfig; // Inbound rate limited config, meaning the rate limits for all of the offRamps for the given chain\\n  }\\n\\n  struct RemoteChainConfig {\\n    RateLimiter.TokenBucket outboundRateLimiterConfig; // Outbound rate limited config, meaning the rate limits for all of the onRamps for the given chain\\n    RateLimiter.TokenBucket inboundRateLimiterConfig; // Inbound rate limited config, meaning the rate limits for all of the offRamps for the given chain\\n    bytes remoteTokenAddress; // Address of the remote token, ABI encoded in the case of a remote EVM chain.\\n    EnumerableSet.Bytes32Set remotePools; // Set of remote pool hashes, ABI encoded in the case of a remote EVM chain.\\n  }\\n\\n  /// @dev The bridgeable token that is managed by this pool. Pools could support multiple tokens at the same time if\\n  /// required, but this implementation only supports one token.\\n  IERC20 internal immutable i_token;\\n  /// @dev The number of decimals of the token managed by this pool.\\n  uint8 internal immutable i_tokenDecimals;\\n  /// @dev The address of the RMN proxy\\n  address internal immutable i_rmnProxy;\\n  /// @dev The immutable flag that indicates if the pool is access-controlled.\\n  bool internal immutable i_allowlistEnabled;\\n  /// @dev A set of addresses allowed to trigger lockOrBurn as original senders.\\n  /// Only takes effect if i_allowlistEnabled is true.\\n  /// This can be used to ensure only token-issuer specified addresses can move tokens.\\n  EnumerableSet.AddressSet internal s_allowlist;\\n  /// @dev The address of the router\\n  IRouter internal s_router;\\n  /// @dev A set of allowed chain selectors. We want the allowlist to be enumerable to\\n  /// be able to quickly determine (without parsing logs) who can access the pool.\\n  /// @dev The chain selectors are in uint256 format because of the EnumerableSet implementation.\\n  EnumerableSet.UintSet internal s_remoteChainSelectors;\\n  mapping(uint64 remoteChainSelector =\\u003e RemoteChainConfig) internal s_remoteChainConfigs;\\n  /// @notice A mapping of hashed pool addresses to their unhashed form. This is used to be able to find the actually\\n  /// configured pools and not just their hashed versions.\\n  mapping(bytes32 poolAddressHash =\\u003e bytes poolAddress) internal s_remotePoolAddresses;\\n  /// @notice The address of the rate limiter admin.\\n  /// @dev Can be address(0) if none is configured.\\n  address internal s_rateLimitAdmin;\\n\\n  constructor(IERC20 token, uint8 localTokenDecimals, address[] memory allowlist, address rmnProxy, address router) {\\n    if (address(token) == address(0) || router == address(0) || rmnProxy == address(0)) revert ZeroAddressNotAllowed();\\n    i_token = token;\\n    i_rmnProxy = rmnProxy;\\n\\n    try IERC20Metadata(address(token)).decimals() returns (uint8 actualTokenDecimals) {\\n      if (localTokenDecimals != actualTokenDecimals) {\\n        revert InvalidDecimalArgs(localTokenDecimals, actualTokenDecimals);\\n      }\\n    } catch {\\n      // The decimals function doesn't exist, which is possible since it's optional in the ERC20 spec. We skip the check and\\n      // assume the supplied token decimals are correct.\\n    }\\n    i_tokenDecimals = localTokenDecimals;\\n\\n    s_router = IRouter(router);\\n\\n    // Pool can be set as permissioned or permissionless at deployment time only to save hot-path gas.\\n    i_allowlistEnabled = allowlist.length \\u003e 0;\\n    if (i_allowlistEnabled) {\\n      _applyAllowListUpdates(new address[](0), allowlist);\\n    }\\n  }\\n\\n  /// @inheritdoc IPoolV1\\n  function isSupportedToken(\\n    address token\\n  ) public view virtual returns (bool) {\\n    return token == address(i_token);\\n  }\\n\\n  /// @notice Gets the IERC20 token that this pool can lock or burn.\\n  /// @return token The IERC20 token representation.\\n  function getToken() public view returns (IERC20 token) {\\n    return i_token;\\n  }\\n\\n  /// @notice Get RMN proxy address\\n  /// @return rmnProxy Address of RMN proxy\\n  function getRmnProxy() public view returns (address rmnProxy) {\\n    return i_rmnProxy;\\n  }\\n\\n  /// @notice Gets the pool's Router\\n  /// @return router The pool's Router\\n  function getRouter() public view returns (address router) {\\n    return address(s_router);\\n  }\\n\\n  /// @notice Sets the pool's Router\\n  /// @param newRouter The new Router\\n  function setRouter(\\n    address newRouter\\n  ) public onlyOwner {\\n    if (newRouter == address(0)) revert ZeroAddressNotAllowed();\\n    address oldRouter = address(s_router);\\n    s_router = IRouter(newRouter);\\n\\n    emit RouterUpdated(oldRouter, newRouter);\\n  }\\n\\n  /// @notice Signals which version of the pool interface is supported\\n  function supportsInterface(\\n    bytes4 interfaceId\\n  ) public pure virtual override returns (bool) {\\n    return interfaceId == Pool.CCIP_POOL_V1 || interfaceId == type(IPoolV1).interfaceId\\n      || interfaceId == type(IERC165).interfaceId;\\n  }\\n\\n  // ================================================================\\n  // │                         Validation                           │\\n  // ================================================================\\n\\n  /// @notice Validates the lock or burn input for correctness on\\n  /// - token to be locked or burned\\n  /// - RMN curse status\\n  /// - allowlist status\\n  /// - if the sender is a valid onRamp\\n  /// - rate limit status\\n  /// @param lockOrBurnIn The input to validate.\\n  /// @dev This function should always be called before executing a lock or burn. Not doing so would allow\\n  /// for various exploits.\\n  function _validateLockOrBurn(\\n    Pool.LockOrBurnInV1 calldata lockOrBurnIn\\n  ) internal {\\n    if (!isSupportedToken(lockOrBurnIn.localToken)) revert InvalidToken(lockOrBurnIn.localToken);\\n    if (IRMN(i_rmnProxy).isCursed(bytes16(uint128(lockOrBurnIn.remoteChainSelector)))) revert CursedByRMN();\\n    _checkAllowList(lockOrBurnIn.originalSender);\\n\\n    _onlyOnRamp(lockOrBurnIn.remoteChainSelector);\\n    _consumeOutboundRateLimit(lockOrBurnIn.remoteChainSelector, lockOrBurnIn.amount);\\n  }\\n\\n  /// @notice Validates the release or mint input for correctness on\\n  /// - token to be released or minted\\n  /// - RMN curse status\\n  /// - if the sender is a valid offRamp\\n  /// - if the source pool is valid\\n  /// - rate limit status\\n  /// @param releaseOrMintIn The input to validate.\\n  /// @dev This function should always be called before executing a release or mint. Not doing so would allow\\n  /// for various exploits.\\n  function _validateReleaseOrMint(\\n    Pool.ReleaseOrMintInV1 calldata releaseOrMintIn\\n  ) internal {\\n    if (!isSupportedToken(releaseOrMintIn.localToken)) revert InvalidToken(releaseOrMintIn.localToken);\\n    if (IRMN(i_rmnProxy).isCursed(bytes16(uint128(releaseOrMintIn.remoteChainSelector)))) revert CursedByRMN();\\n    _onlyOffRamp(releaseOrMintIn.remoteChainSelector);\\n\\n    // Validates that the source pool address is configured on this pool.\\n    if (!isRemotePool(releaseOrMintIn.remoteChainSelector, releaseOrMintIn.sourcePoolAddress)) {\\n      revert InvalidSourcePoolAddress(releaseOrMintIn.sourcePoolAddress);\\n    }\\n\\n    _consumeInboundRateLimit(releaseOrMintIn.remoteChainSelector, releaseOrMintIn.amount);\\n  }\\n\\n  // ================================================================\\n  // │                      Token decimals                          │\\n  // ================================================================\\n\\n  /// @notice Gets the IERC20 token decimals on the local chain.\\n  function getTokenDecimals() public view virtual returns (uint8 decimals) {\\n    return i_tokenDecimals;\\n  }\\n\\n  function _encodeLocalDecimals() internal view virtual returns (bytes memory) {\\n    return abi.encode(i_tokenDecimals);\\n  }\\n\\n  function _parseRemoteDecimals(\\n    bytes memory sourcePoolData\\n  ) internal view virtual returns (uint8) {\\n    // Fallback to the local token decimals if the source pool data is empty. This allows for backwards compatibility.\\n    if (sourcePoolData.length == 0) {\\n      return i_tokenDecimals;\\n    }\\n    if (sourcePoolData.length != 32) {\\n      revert InvalidRemoteChainDecimals(sourcePoolData);\\n    }\\n    uint256 remoteDecimals = abi.decode(sourcePoolData, (uint256));\\n    if (remoteDecimals \\u003e type(uint8).max) {\\n      revert InvalidRemoteChainDecimals(sourcePoolData);\\n    }\\n    return uint8(remoteDecimals);\\n  }\\n\\n  /// @notice Calculates the local amount based on the remote amount and decimals.\\n  /// @param remoteAmount The amount on the remote chain.\\n  /// @param remoteDecimals The decimals of the token on the remote chain.\\n  /// @return The local amount.\\n  /// @dev This function protects against overflows. If there is a transaction that hits the overflow check, it is\\n  /// probably incorrect as that means the amount cannot be represented on this chain. If the local decimals have been\\n  /// wrongly configured, the token issuer could redeploy the pool with the correct decimals and manually re-execute the\\n  /// CCIP tx to fix the issue.\\n  function _calculateLocalAmount(uint256 remoteAmount, uint8 remoteDecimals) internal view virtual returns (uint256) {\\n    if (remoteDecimals == i_tokenDecimals) {\\n      return remoteAmount;\\n    }\\n    if (remoteDecimals \\u003e i_tokenDecimals) {\\n      uint8 decimalsDiff = remoteDecimals - i_tokenDecimals;\\n      if (decimalsDiff \\u003e 77) {\\n        // This is a safety check to prevent overflow in the next calculation.\\n        revert OverflowDetected(remoteDecimals, i_tokenDecimals, remoteAmount);\\n      }\\n      // Solidity rounds down so there is no risk of minting more tokens than the remote chain sent.\\n      return remoteAmount / (10 ** decimalsDiff);\\n    }\\n\\n    // This is a safety check to prevent overflow in the next calculation.\\n    // More than 77 would never fit in a uint256 and would cause an overflow. We also check if the resulting amount\\n    // would overflow.\\n    uint8 diffDecimals = i_tokenDecimals - remoteDecimals;\\n    if (diffDecimals \\u003e 77 || remoteAmount \\u003e type(uint256).max / (10 ** diffDecimals)) {\\n      revert OverflowDetected(remoteDecimals, i_tokenDecimals, remoteAmount);\\n    }\\n\\n    return remoteAmount * (10 ** diffDecimals);\\n  }\\n\\n  // ================================================================\\n  // │                     Chain permissions                        │\\n  // ================================================================\\n\\n  /// @notice Gets the pool address on the remote chain.\\n  /// @param remoteChainSelector Remote chain selector.\\n  /// @dev To support non-evm chains, this value is encoded into bytes\\n  function getRemotePools(\\n    uint64 remoteChainSelector\\n  ) public view returns (bytes[] memory) {\\n    bytes32[] memory remotePoolHashes = s_remoteChainConfigs[remoteChainSelector].remotePools.values();\\n\\n    bytes[] memory remotePools = new bytes[](remotePoolHashes.length);\\n    for (uint256 i = 0; i \\u003c remotePoolHashes.length; ++i) {\\n      remotePools[i] = s_remotePoolAddresses[remotePoolHashes[i]];\\n    }\\n\\n    return remotePools;\\n  }\\n\\n  /// @notice Checks if the pool address is configured on the remote chain.\\n  /// @param remoteChainSelector Remote chain selector.\\n  /// @param remotePoolAddress The address of the remote pool.\\n  function isRemotePool(uint64 remoteChainSelector, bytes calldata remotePoolAddress) public view returns (bool) {\\n    return s_remoteChainConfigs[remoteChainSelector].remotePools.contains(keccak256(remotePoolAddress));\\n  }\\n\\n  /// @notice Gets the token address on the remote chain.\\n  /// @param remoteChainSelector Remote chain selector.\\n  /// @dev To support non-evm chains, this value is encoded into bytes\\n  function getRemoteToken(\\n    uint64 remoteChainSelector\\n  ) public view returns (bytes memory) {\\n    return s_remoteChainConfigs[remoteChainSelector].remoteTokenAddress;\\n  }\\n\\n  /// @notice Adds a remote pool for a given chain selector. This could be due to a pool being upgraded on the remote\\n  /// chain. We don't simply want to replace the old pool as there could still be valid inflight messages from the old\\n  /// pool. This function allows for multiple pools to be added for a single chain selector.\\n  /// @param remoteChainSelector The remote chain selector for which the remote pool address is being added.\\n  /// @param remotePoolAddress The address of the new remote pool.\\n  function addRemotePool(uint64 remoteChainSelector, bytes calldata remotePoolAddress) external onlyOwner {\\n    if (!isSupportedChain(remoteChainSelector)) revert NonExistentChain(remoteChainSelector);\\n\\n    _setRemotePool(remoteChainSelector, remotePoolAddress);\\n  }\\n\\n  /// @notice Removes the remote pool address for a given chain selector.\\n  /// @dev All inflight txs from the remote pool will be rejected after it is removed. To ensure no loss of funds, there\\n  /// should be no inflight txs from the given pool.\\n  function removeRemotePool(uint64 remoteChainSelector, bytes calldata remotePoolAddress) external onlyOwner {\\n    if (!isSupportedChain(remoteChainSelector)) revert NonExistentChain(remoteChainSelector);\\n\\n    if (!s_remoteChainConfigs[remoteChainSelector].remotePools.remove(keccak256(remotePoolAddress))) {\\n      revert InvalidRemotePoolForChain(remoteChainSelector, remotePoolAddress);\\n    }\\n\\n    emit RemotePoolRemoved(remoteChainSelector, remotePoolAddress);\\n  }\\n\\n  /// @inheritdoc IPoolV1\\n  function isSupportedChain(\\n    uint64 remoteChainSelector\\n  ) public view returns (bool) {\\n    return s_remoteChainSelectors.contains(remoteChainSelector);\\n  }\\n\\n  /// @notice Get list of allowed chains\\n  /// @return list of chains.\\n  function getSupportedChains() public view returns (uint64[] memory) {\\n    uint256[] memory uint256ChainSelectors = s_remoteChainSelectors.values();\\n    uint64[] memory chainSelectors = new uint64[](uint256ChainSelectors.length);\\n    for (uint256 i = 0; i \\u003c uint256ChainSelectors.length; ++i) {\\n      chainSelectors[i] = uint64(uint256ChainSelectors[i]);\\n    }\\n\\n    return chainSelectors;\\n  }\\n\\n  /// @notice Sets the permissions for a list of chains selectors. Actual senders for these chains\\n  /// need to be allowed on the Router to interact with this pool.\\n  /// @param remoteChainSelectorsToRemove A list of chain selectors to remove.\\n  /// @param chainsToAdd A list of chains and their new permission status \\u0026 rate limits. Rate limits\\n  /// are only used when the chain is being added through `allowed` being true.\\n  /// @dev Only callable by the owner\\n  function applyChainUpdates(\\n    uint64[] calldata remoteChainSelectorsToRemove,\\n    ChainUpdate[] calldata chainsToAdd\\n  ) external virtual onlyOwner {\\n    for (uint256 i = 0; i \\u003c remoteChainSelectorsToRemove.length; ++i) {\\n      uint64 remoteChainSelectorToRemove = remoteChainSelectorsToRemove[i];\\n      // If the chain doesn't exist, revert\\n      if (!s_remoteChainSelectors.remove(remoteChainSelectorToRemove)) {\\n        revert NonExistentChain(remoteChainSelectorToRemove);\\n      }\\n\\n      // Remove all remote pool hashes for the chain\\n      bytes32[] memory remotePools = s_remoteChainConfigs[remoteChainSelectorToRemove].remotePools.values();\\n      for (uint256 j = 0; j \\u003c remotePools.length; ++j) {\\n        s_remoteChainConfigs[remoteChainSelectorToRemove].remotePools.remove(remotePools[j]);\\n      }\\n\\n      delete s_remoteChainConfigs[remoteChainSelectorToRemove];\\n\\n      emit ChainRemoved(remoteChainSelectorToRemove);\\n    }\\n\\n    for (uint256 i = 0; i \\u003c chainsToAdd.length; ++i) {\\n      ChainUpdate memory newChain = chainsToAdd[i];\\n      RateLimiter._validateTokenBucketConfig(newChain.outboundRateLimiterConfig, false);\\n      RateLimiter._validateTokenBucketConfig(newChain.inboundRateLimiterConfig, false);\\n\\n      if (newChain.remoteTokenAddress.length == 0) {\\n        revert ZeroAddressNotAllowed();\\n      }\\n\\n      // If the chain already exists, revert\\n      if (!s_remoteChainSelectors.add(newChain.remoteChainSelector)) {\\n        revert ChainAlreadyExists(newChain.remoteChainSelector);\\n      }\\n\\n      RemoteChainConfig storage remoteChainConfig = s_remoteChainConfigs[newChain.remoteChainSelector];\\n\\n      remoteChainConfig.outboundRateLimiterConfig = RateLimiter.TokenBucket({\\n        rate: newChain.outboundRateLimiterConfig.rate,\\n        capacity: newChain.outboundRateLimiterConfig.capacity,\\n        tokens: newChain.outboundRateLimiterConfig.capacity,\\n        lastUpdated: uint32(block.timestamp),\\n        isEnabled: newChain.outboundRateLimiterConfig.isEnabled\\n      });\\n      remoteChainConfig.inboundRateLimiterConfig = RateLimiter.TokenBucket({\\n        rate: newChain.inboundRateLimiterConfig.rate,\\n        capacity: newChain.inboundRateLimiterConfig.capacity,\\n        tokens: newChain.inboundRateLimiterConfig.capacity,\\n        lastUpdated: uint32(block.timestamp),\\n        isEnabled: newChain.inboundRateLimiterConfig.isEnabled\\n      });\\n      remoteChainConfig.remoteTokenAddress = newChain.remoteTokenAddress;\\n\\n      for (uint256 j = 0; j \\u003c newChain.remotePoolAddresses.length; ++j) {\\n        _setRemotePool(newChain.remoteChainSelector, newChain.remotePoolAddresses[j]);\\n      }\\n\\n      emit ChainAdded(\\n        newChain.remoteChainSelector,\\n        newChain.remoteTokenAddress,\\n        newChain.outboundRateLimiterConfig,\\n        newChain.inboundRateLimiterConfig\\n      );\\n    }\\n  }\\n\\n  /// @notice Adds a pool address to the allowed remote token pools for a particular chain.\\n  /// @param remoteChainSelector The remote chain selector for which the remote pool address is being added.\\n  /// @param remotePoolAddress The address of the new remote pool.\\n  function _setRemotePool(uint64 remoteChainSelector, bytes memory remotePoolAddress) internal {\\n    if (remotePoolAddress.length == 0) {\\n      revert ZeroAddressNotAllowed();\\n    }\\n\\n    bytes32 poolHash = keccak256(remotePoolAddress);\\n\\n    // Check if the pool already exists.\\n    if (!s_remoteChainConfigs[remoteChainSelector].remotePools.add(poolHash)) {\\n      revert PoolAlreadyAdded(remoteChainSelector, remotePoolAddress);\\n    }\\n\\n    // Add the pool to the mapping to be able to un-hash it later.\\n    s_remotePoolAddresses[poolHash] = remotePoolAddress;\\n\\n    emit RemotePoolAdded(remoteChainSelector, remotePoolAddress);\\n  }\\n\\n  // ================================================================\\n  // │                        Rate limiting                         │\\n  // ================================================================\\n\\n  /// @dev The inbound rate limits should be slightly higher than the outbound rate limits. This is because many chains\\n  /// finalize blocks in batches. CCIP also commits messages in batches: the commit plugin bundles multiple messages in\\n  /// a single merkle root.\\n  /// Imagine the following scenario.\\n  /// - Chain A has an inbound and outbound rate limit of 100 tokens capacity and 1 token per second refill rate.\\n  /// - Chain B has an inbound and outbound rate limit of 100 tokens capacity and 1 token per second refill rate.\\n  ///\\n  /// At time 0:\\n  /// - Chain A sends 100 tokens to Chain B.\\n  /// At time 5:\\n  /// - Chain A sends 5 tokens to Chain B.\\n  /// At time 6:\\n  /// The epoch that contains blocks [0-5] is finalized.\\n  /// Both transactions will be included in the same merkle root and become executable at the same time. This means\\n  /// the token pool on chain B requires a capacity of 105 to successfully execute both messages at the same time.\\n  /// The exact additional capacity required depends on the refill rate and the size of the source chain epochs and the\\n  /// CCIP round time. For simplicity, a 5-10% buffer should be sufficient in most cases.\\n\\n  /// @notice Sets the rate limiter admin address.\\n  /// @dev Only callable by the owner.\\n  /// @param rateLimitAdmin The new rate limiter admin address.\\n  function setRateLimitAdmin(\\n    address rateLimitAdmin\\n  ) external onlyOwner {\\n    s_rateLimitAdmin = rateLimitAdmin;\\n    emit RateLimitAdminSet(rateLimitAdmin);\\n  }\\n\\n  /// @notice Gets the rate limiter admin address.\\n  function getRateLimitAdmin() external view returns (address) {\\n    return s_rateLimitAdmin;\\n  }\\n\\n  /// @notice Consumes outbound rate limiting capacity in this pool\\n  function _consumeOutboundRateLimit(uint64 remoteChainSelector, uint256 amount) internal {\\n    s_remoteChainConfigs[remoteChainSelector].outboundRateLimiterConfig._consume(amount, address(i_token));\\n  }\\n\\n  /// @notice Consumes inbound rate limiting capacity in this pool\\n  function _consumeInboundRateLimit(uint64 remoteChainSelector, uint256 amount) internal {\\n    s_remoteChainConfigs[remoteChainSelector].inboundRateLimiterConfig._consume(amount, address(i_token));\\n  }\\n\\n  /// @notice Gets the token bucket with its values for the block it was requested at.\\n  /// @return The token bucket.\\n  function getCurrentOutboundRateLimiterState(\\n    uint64 remoteChainSelector\\n  ) external view returns (RateLimiter.TokenBucket memory) {\\n    return s_remoteChainConfigs[remoteChainSelector].outboundRateLimiterConfig._currentTokenBucketState();\\n  }\\n\\n  /// @notice Gets the token bucket with its values for the block it was requested at.\\n  /// @return The token bucket.\\n  function getCurrentInboundRateLimiterState(\\n    uint64 remoteChainSelector\\n  ) external view returns (RateLimiter.TokenBucket memory) {\\n    return s_remoteChainConfigs[remoteChainSelector].inboundRateLimiterConfig._currentTokenBucketState();\\n  }\\n\\n  /// @notice Sets multiple chain rate limiter configs.\\n  /// @param remoteChainSelectors The remote chain selector for which the rate limits apply.\\n  /// @param outboundConfigs The new outbound rate limiter config, meaning the onRamp rate limits for the given chain.\\n  /// @param inboundConfigs The new inbound rate limiter config, meaning the offRamp rate limits for the given chain.\\n  function setChainRateLimiterConfigs(\\n    uint64[] calldata remoteChainSelectors,\\n    RateLimiter.Config[] calldata outboundConfigs,\\n    RateLimiter.Config[] calldata inboundConfigs\\n  ) external {\\n    if (msg.sender != s_rateLimitAdmin \\u0026\\u0026 msg.sender != owner()) revert Unauthorized(msg.sender);\\n    if (remoteChainSelectors.length != outboundConfigs.length || remoteChainSelectors.length != inboundConfigs.length) {\\n      revert MismatchedArrayLengths();\\n    }\\n\\n    for (uint256 i = 0; i \\u003c remoteChainSelectors.length; ++i) {\\n      _setRateLimitConfig(remoteChainSelectors[i], outboundConfigs[i], inboundConfigs[i]);\\n    }\\n  }\\n\\n  /// @notice Sets the chain rate limiter config.\\n  /// @param remoteChainSelector The remote chain selector for which the rate limits apply.\\n  /// @param outboundConfig The new outbound rate limiter config, meaning the onRamp rate limits for the given chain.\\n  /// @param inboundConfig The new inbound rate limiter config, meaning the offRamp rate limits for the given chain.\\n  function setChainRateLimiterConfig(\\n    uint64 remoteChainSelector,\\n    RateLimiter.Config memory outboundConfig,\\n    RateLimiter.Config memory inboundConfig\\n  ) external {\\n    if (msg.sender != s_rateLimitAdmin \\u0026\\u0026 msg.sender != owner()) revert Unauthorized(msg.sender);\\n\\n    _setRateLimitConfig(remoteChainSelector, outboundConfig, inboundConfig);\\n  }\\n\\n  function _setRateLimitConfig(\\n    uint64 remoteChainSelector,\\n    RateLimiter.Config memory outboundConfig,\\n    RateLimiter.Config memory inboundConfig\\n  ) internal {\\n    if (!isSupportedChain(remoteChainSelector)) revert NonExistentChain(remoteChainSelector);\\n    RateLimiter._validateTokenBucketConfig(outboundConfig, false);\\n    s_remoteChainConfigs[remoteChainSelector].outboundRateLimiterConfig._setTokenBucketConfig(outboundConfig);\\n    RateLimiter._validateTokenBucketConfig(inboundConfig, false);\\n    s_remoteChainConfigs[remoteChainSelector].inboundRateLimiterConfig._setTokenBucketConfig(inboundConfig);\\n    emit ChainConfigured(remoteChainSelector, outboundConfig, inboundConfig);\\n  }\\n\\n  // ================================================================\\n  // │                           Access                             │\\n  // ================================================================\\n\\n  /// @notice Checks whether remote chain selector is configured on this contract, and if the msg.sender\\n  /// is a permissioned onRamp for the given chain on the Router.\\n  function _onlyOnRamp(\\n    uint64 remoteChainSelector\\n  ) internal view {\\n    if (!isSupportedChain(remoteChainSelector)) revert ChainNotAllowed(remoteChainSelector);\\n    if (!(msg.sender == s_router.getOnRamp(remoteChainSelector))) revert CallerIsNotARampOnRouter(msg.sender);\\n  }\\n\\n  /// @notice Checks whether remote chain selector is configured on this contract, and if the msg.sender\\n  /// is a permissioned offRamp for the given chain on the Router.\\n  function _onlyOffRamp(\\n    uint64 remoteChainSelector\\n  ) internal view {\\n    if (!isSupportedChain(remoteChainSelector)) revert ChainNotAllowed(remoteChainSelector);\\n    if (!s_router.isOffRamp(remoteChainSelector, msg.sender)) revert CallerIsNotARampOnRouter(msg.sender);\\n  }\\n\\n  // ================================================================\\n  // │                          Allowlist                           │\\n  // ================================================================\\n\\n  function _checkAllowList(\\n    address sender\\n  ) internal view {\\n    if (i_allowlistEnabled) {\\n      if (!s_allowlist.contains(sender)) {\\n        revert SenderNotAllowed(sender);\\n      }\\n    }\\n  }\\n\\n  /// @notice Gets whether the allowlist functionality is enabled.\\n  /// @return true is enabled, false if not.\\n  function getAllowListEnabled() external view returns (bool) {\\n    return i_allowlistEnabled;\\n  }\\n\\n  /// @notice Gets the allowed addresses.\\n  /// @return The allowed addresses.\\n  function getAllowList() external view returns (address[] memory) {\\n    return s_allowlist.values();\\n  }\\n\\n  /// @notice Apply updates to the allow list.\\n  /// @param removes The addresses to be removed.\\n  /// @param adds The addresses to be added.\\n  function applyAllowListUpdates(address[] calldata removes, address[] calldata adds) external onlyOwner {\\n    _applyAllowListUpdates(removes, adds);\\n  }\\n\\n  /// @notice Internal version of applyAllowListUpdates to allow for reuse in the constructor.\\n  function _applyAllowListUpdates(address[] memory removes, address[] memory adds) internal {\\n    if (!i_allowlistEnabled) revert AllowListNotEnabled();\\n\\n    for (uint256 i = 0; i \\u003c removes.length; ++i) {\\n      address toRemove = removes[i];\\n      if (s_allowlist.remove(toRemove)) {\\n        emit AllowListRemove(toRemove);\\n      }\\n    }\\n    for (uint256 i = 0; i \\u003c adds.length; ++i) {\\n      address toAdd = adds[i];\\n      if (toAdd == address(0)) {\\n        continue;\\n      }\\n      if (s_allowlist.add(toAdd)) {\\n        emit AllowListAdd(toAdd);\\n      }\\n    }\\n  }\\n}\\n\"},\"contracts/tokenAdminRegistry/RegistryModuleOwnerCustom.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IGetCCIPAdmin} from \\\"../interfaces/IGetCCIPAdmin.sol\\\";\\nimport {IOwner} from \\\"../interfaces/IOwner.sol\\\";\\nimport {ITokenAdminRegistry} from \\\"../interfaces/ITokenAdminRegistry.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {AccessControl} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/access/AccessControl.sol\\\";\\n\\ncontract RegistryModuleOwnerCustom is ITypeAndVersion {\\n  error CanOnlySelfRegister(address admin, address token);\\n  error RequiredRoleNotFound(address msgSender, bytes32 role, address token);\\n  error AddressZero();\\n\\n  event AdministratorRegistered(address indexed token, address indexed administrator);\\n\\n  string public constant override typeAndVersion = \\\"RegistryModuleOwnerCustom 1.6.0\\\";\\n\\n  // The TokenAdminRegistry contract\\n  ITokenAdminRegistry internal immutable i_tokenAdminRegistry;\\n\\n  constructor(\\n    address tokenAdminRegistry\\n  ) {\\n    if (tokenAdminRegistry == address(0)) {\\n      revert AddressZero();\\n    }\\n    i_tokenAdminRegistry = ITokenAdminRegistry(tokenAdminRegistry);\\n  }\\n\\n  /// @notice Registers the admin of the token using the `getCCIPAdmin` method.\\n  /// @param token The token to register the admin for.\\n  /// @dev The caller must be the admin returned by the `getCCIPAdmin` method.\\n  function registerAdminViaGetCCIPAdmin(\\n    address token\\n  ) external {\\n    _registerAdmin(token, IGetCCIPAdmin(token).getCCIPAdmin());\\n  }\\n\\n  /// @notice Registers the admin of the token using the `owner` method.\\n  /// @param token The token to register the admin for.\\n  /// @dev The caller must be the admin returned by the `owner` method.\\n  function registerAdminViaOwner(\\n    address token\\n  ) external {\\n    _registerAdmin(token, IOwner(token).owner());\\n  }\\n\\n  /// @notice Registers the admin of the token using OZ's AccessControl DEFAULT_ADMIN_ROLE.\\n  /// @param token The token to register the admin for.\\n  /// @dev The caller must have the DEFAULT_ADMIN_ROLE as defined by the contract itself.\\n  function registerAccessControlDefaultAdmin(\\n    address token\\n  ) external {\\n    bytes32 defaultAdminRole = AccessControl(token).DEFAULT_ADMIN_ROLE();\\n    if (!AccessControl(token).hasRole(defaultAdminRole, msg.sender)) {\\n      revert RequiredRoleNotFound(msg.sender, defaultAdminRole, token);\\n    }\\n\\n    _registerAdmin(token, msg.sender);\\n  }\\n\\n  /// @notice Registers the admin of the token to msg.sender given that the\\n  /// admin is equal to msg.sender.\\n  /// @param token The token to register the admin for.\\n  /// @param admin The caller must be the admin.\\n  function _registerAdmin(address token, address admin) internal {\\n    if (admin != msg.sender) {\\n      revert CanOnlySelfRegister(admin, token);\\n    }\\n\\n    i_tokenAdminRegistry.proposeAdministrator(token, admin);\\n\\n    emit AdministratorRegistered(token, admin);\\n  }\\n}\\n\"},\"contracts/tokenAdminRegistry/TokenPoolFactory/FactoryBurnMintERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.24;\\n\\nimport {IGetCCIPAdmin} from \\\"../../interfaces/IGetCCIPAdmin.sol\\\";\\nimport {IOwnable} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\\\";\\nimport {IBurnMintERC20} from \\\"@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\\\";\\n\\nimport {Ownable2StepMsgSender} from \\\"@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\\\";\\n\\nimport {ERC20} from \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/ERC20.sol\\\";\\nimport {IERC20} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\nimport {ERC20Burnable} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/ERC20Burnable.sol\\\";\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\\\";\\nimport {EnumerableSet} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @notice A basic ERC20 compatible token contract with burn and minting roles.\\n/// @dev The constructor has been modified to support the deployment pattern used by a factory contract.\\n/// @dev The total supply can be limited during deployment.\\ncontract FactoryBurnMintERC20 is IBurnMintERC20, IGetCCIPAdmin, IERC165, ERC20Burnable, Ownable2StepMsgSender {\\n  using EnumerableSet for EnumerableSet.AddressSet;\\n\\n  error SenderNotMinter(address sender);\\n  error SenderNotBurner(address sender);\\n  error MaxSupplyExceeded(uint256 supplyAfterMint);\\n\\n  event MintAccessGranted(address minter);\\n  event BurnAccessGranted(address burner);\\n  event MintAccessRevoked(address minter);\\n  event BurnAccessRevoked(address burner);\\n  event CCIPAdminTransferred(address indexed previousAdmin, address indexed newAdmin);\\n\\n  /// @dev The number of decimals for the token\\n  uint8 internal immutable i_decimals;\\n\\n  /// @dev The maximum supply of the token, 0 if unlimited\\n  uint256 internal immutable i_maxSupply;\\n\\n  /// @dev the CCIPAdmin can be used to register with the CCIP token admin registry, but has no other special powers,\\n  /// and can only be transferred by the owner.\\n  address internal s_ccipAdmin;\\n\\n  /// @dev the allowed minter addresses\\n  EnumerableSet.AddressSet internal s_minters;\\n  /// @dev the allowed burner addresses\\n  EnumerableSet.AddressSet internal s_burners;\\n\\n  /// @dev the underscores in parameter names are used to suppress compiler warnings about shadowing ERC20 functions\\n  constructor(\\n    string memory name,\\n    string memory symbol,\\n    uint8 decimals_,\\n    uint256 maxSupply_,\\n    uint256 preMint,\\n    address newOwner\\n  ) ERC20(name, symbol) {\\n    i_decimals = decimals_;\\n    i_maxSupply = maxSupply_;\\n\\n    s_ccipAdmin = newOwner;\\n\\n    // Mint the initial supply to the new Owner, saving gas by not calling if the mint amount is zero\\n    if (preMint != 0) _mint(newOwner, preMint);\\n\\n    // Grant the deployer the minter and burner roles. This contract is expected to be deployed by a factory\\n    // contract that will transfer ownership to the correct address after deployment, so granting minting and burning\\n    // privileges here saves gas by not requiring two transactions.\\n    grantMintRole(newOwner);\\n    grantBurnRole(newOwner);\\n  }\\n\\n  /// @inheritdoc IERC165\\n  function supportsInterface(\\n    bytes4 interfaceId\\n  ) public pure virtual override returns (bool) {\\n    return interfaceId == type(IERC20).interfaceId || interfaceId == type(IBurnMintERC20).interfaceId\\n      || interfaceId == type(IERC165).interfaceId || interfaceId == type(IOwnable).interfaceId\\n      || interfaceId == type(IGetCCIPAdmin).interfaceId;\\n  }\\n\\n  // ================================================================\\n  // │                            ERC20                             │\\n  // ================================================================\\n\\n  /// @dev Returns the number of decimals used in its user representation.\\n  function decimals() public view virtual override returns (uint8) {\\n    return i_decimals;\\n  }\\n\\n  /// @dev Returns the max supply of the token, 0 if unlimited.\\n  function maxSupply() public view virtual returns (uint256) {\\n    return i_maxSupply;\\n  }\\n\\n  /// @dev Uses OZ ERC20 _transfer to disallow sending to address(0).\\n  /// @dev Disallows sending to address(this)\\n  function _transfer(address from, address to, uint256 amount) internal virtual override validAddress(to) {\\n    super._transfer(from, to, amount);\\n  }\\n\\n  /// @dev Uses OZ ERC20 _approve to disallow approving for address(0).\\n  /// @dev Disallows approving for address(this)\\n  function _approve(address owner, address spender, uint256 amount) internal virtual override validAddress(spender) {\\n    super._approve(owner, spender, amount);\\n  }\\n\\n  /// @dev Exists to be backwards compatible with the older naming convention.\\n  /// @param spender the account being approved to spend on the users' behalf.\\n  /// @param subtractedValue the amount being removed from the approval.\\n  /// @return success Bool to return if the approval was successfully decreased.\\n  function decreaseApproval(address spender, uint256 subtractedValue) external returns (bool success) {\\n    return decreaseAllowance(spender, subtractedValue);\\n  }\\n\\n  /// @dev Exists to be backwards compatible with the older naming convention.\\n  /// @param spender the account being approved to spend on the users' behalf.\\n  /// @param addedValue the amount being added to the approval.\\n  function increaseApproval(address spender, uint256 addedValue) external {\\n    increaseAllowance(spender, addedValue);\\n  }\\n\\n  // ================================================================\\n  // │                      Burning \\u0026 minting                       │\\n  // ================================================================\\n\\n  /// @inheritdoc ERC20Burnable\\n  /// @dev Uses OZ ERC20 _burn to disallow burning from address(0).\\n  /// @dev Decreases the total supply.\\n  function burn(\\n    uint256 amount\\n  ) public override(IBurnMintERC20, ERC20Burnable) onlyBurner {\\n    super.burn(amount);\\n  }\\n\\n  /// @inheritdoc IBurnMintERC20\\n  /// @dev Alias for BurnFrom for compatibility with the older naming convention.\\n  /// @dev Uses burnFrom for all validation \\u0026 logic.\\n  function burn(address account, uint256 amount) public virtual override {\\n    burnFrom(account, amount);\\n  }\\n\\n  /// @inheritdoc ERC20Burnable\\n  /// @dev Uses OZ ERC20 _burn to disallow burning from address(0).\\n  /// @dev Decreases the total supply.\\n  function burnFrom(address account, uint256 amount) public override(IBurnMintERC20, ERC20Burnable) onlyBurner {\\n    super.burnFrom(account, amount);\\n  }\\n\\n  /// @inheritdoc IBurnMintERC20\\n  /// @dev Uses OZ ERC20 _mint to disallow minting to address(0).\\n  /// @dev Disallows minting to address(this)\\n  /// @dev Increases the total supply.\\n  function mint(address account, uint256 amount) external override onlyMinter validAddress(account) {\\n    if (i_maxSupply != 0 \\u0026\\u0026 totalSupply() + amount \\u003e i_maxSupply) revert MaxSupplyExceeded(totalSupply() + amount);\\n\\n    _mint(account, amount);\\n  }\\n\\n  // ================================================================\\n  // │                            Roles                             │\\n  // ================================================================\\n\\n  /// @notice grants both mint and burn roles to `burnAndMinter`.\\n  /// @dev calls public functions so this function does not require\\n  /// access controls. This is handled in the inner functions.\\n  function grantMintAndBurnRoles(\\n    address burnAndMinter\\n  ) external {\\n    grantMintRole(burnAndMinter);\\n    grantBurnRole(burnAndMinter);\\n  }\\n\\n  /// @notice Grants mint role to the given address.\\n  /// @dev only the owner can call this function.\\n  function grantMintRole(\\n    address minter\\n  ) public onlyOwner {\\n    if (s_minters.add(minter)) {\\n      emit MintAccessGranted(minter);\\n    }\\n  }\\n\\n  /// @notice Grants burn role to the given address.\\n  /// @dev only the owner can call this function.\\n  /// @param burner the address to grant the burner role to\\n  function grantBurnRole(\\n    address burner\\n  ) public onlyOwner {\\n    if (s_burners.add(burner)) {\\n      emit BurnAccessGranted(burner);\\n    }\\n  }\\n\\n  /// @notice Revokes mint role for the given address.\\n  /// @dev only the owner can call this function.\\n  /// @param minter the address to revoke the mint role from.\\n  function revokeMintRole(\\n    address minter\\n  ) external onlyOwner {\\n    if (s_minters.remove(minter)) {\\n      emit MintAccessRevoked(minter);\\n    }\\n  }\\n\\n  /// @notice Revokes burn role from the given address.\\n  /// @dev only the owner can call this function\\n  /// @param burner the address to revoke the burner role from\\n  function revokeBurnRole(\\n    address burner\\n  ) external onlyOwner {\\n    if (s_burners.remove(burner)) {\\n      emit BurnAccessRevoked(burner);\\n    }\\n  }\\n\\n  /// @notice Returns all permissioned minters\\n  function getMinters() external view returns (address[] memory) {\\n    return s_minters.values();\\n  }\\n\\n  /// @notice Returns all permissioned burners\\n  function getBurners() external view returns (address[] memory) {\\n    return s_burners.values();\\n  }\\n\\n  /// @notice Returns the current CCIPAdmin\\n  function getCCIPAdmin() external view returns (address) {\\n    return s_ccipAdmin;\\n  }\\n\\n  /// @notice Transfers the CCIPAdmin role to a new address\\n  /// @dev only the owner can call this function, NOT the current ccipAdmin, and 1-step ownership transfer is used.\\n  /// @param newAdmin The address to transfer the CCIPAdmin role to. Setting to address(0) is a valid way to revoke\\n  /// the role\\n  function setCCIPAdmin(\\n    address newAdmin\\n  ) public onlyOwner {\\n    address currentAdmin = s_ccipAdmin;\\n\\n    s_ccipAdmin = newAdmin;\\n\\n    emit CCIPAdminTransferred(currentAdmin, newAdmin);\\n  }\\n\\n  // ================================================================\\n  // │                            Access                            │\\n  // ================================================================\\n\\n  /// @notice Checks whether a given address is a minter for this token.\\n  /// @return true if the address is allowed to mint.\\n  function isMinter(\\n    address minter\\n  ) public view returns (bool) {\\n    return s_minters.contains(minter);\\n  }\\n\\n  /// @notice Checks whether a given address is a burner for this token.\\n  /// @return true if the address is allowed to burn.\\n  function isBurner(\\n    address burner\\n  ) public view returns (bool) {\\n    return s_burners.contains(burner);\\n  }\\n\\n  /// @notice Checks whether the msg.sender is a permissioned minter for this token\\n  /// @dev Reverts with a SenderNotMinter if the check fails\\n  modifier onlyMinter() {\\n    if (!isMinter(msg.sender)) revert SenderNotMinter(msg.sender);\\n    _;\\n  }\\n\\n  /// @notice Checks whether the msg.sender is a permissioned burner for this token\\n  /// @dev Reverts with a SenderNotBurner if the check fails\\n  modifier onlyBurner() {\\n    if (!isBurner(msg.sender)) revert SenderNotBurner(msg.sender);\\n    _;\\n  }\\n\\n  /// @notice Check if recipient is valid (not this contract address).\\n  /// @param recipient the account we transfer/approve to.\\n  /// @dev Reverts with an empty revert to be compatible with the existing link token when\\n  /// the recipient is this contract address.\\n  modifier validAddress(\\n    address recipient\\n  ) virtual {\\n    // solhint-disable-next-line reason-string, gas-custom-errors\\n    if (recipient == address(this)) revert();\\n    _;\\n  }\\n}\\n\"},\"contracts/tokenAdminRegistry/TokenPoolFactory/TokenPoolFactory.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {ITokenAdminRegistry} from \\\"../../interfaces/ITokenAdminRegistry.sol\\\";\\nimport {IOwnable} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {RateLimiter} from \\\"../../libraries/RateLimiter.sol\\\";\\nimport {TokenPool} from \\\"../../pools/TokenPool.sol\\\";\\nimport {RegistryModuleOwnerCustom} from \\\"../RegistryModuleOwnerCustom.sol\\\";\\nimport {FactoryBurnMintERC20} from \\\"./FactoryBurnMintERC20.sol\\\";\\n\\nimport {Create2} from \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/Create2.sol\\\";\\n\\n/// @notice A contract for deploying new tokens and token pools, and configuring them with the token admin registry\\n/// @dev At the end of the transaction, the ownership transfer process will begin, but the user must accept the\\n/// ownership transfer in a separate transaction.\\n/// @dev The address prediction mechanism is only capable of deploying and predicting addresses for EVM based chains.\\n/// adding compatibility for other chains will require additional offchain computation.\\ncontract TokenPoolFactory is ITypeAndVersion {\\n  using Create2 for bytes32;\\n\\n  event RemoteChainConfigUpdated(uint64 indexed remoteChainSelector, RemoteChainConfig remoteChainConfig);\\n\\n  error InvalidZeroAddress();\\n\\n  /// @notice The type of pool to deploy. Types may be expanded in future versions\\n  enum PoolType {\\n    BURN_MINT,\\n    LOCK_RELEASE\\n  }\\n\\n  /// @dev This struct will only ever exist in memory and as calldata, and therefore does not need to be efficiently packed for storage. The struct is used to pass information to the create2 address generation function.\\n  struct RemoteTokenPoolInfo {\\n    uint64 remoteChainSelector; // The CCIP specific selector for the remote chain\\n    bytes remotePoolAddress; // The address of the remote pool to either deploy or use as is. If empty, address\\n    // will be predicted\\n    bytes remotePoolInitCode; // Remote pool creation code if it needs to be deployed, without constructor params\\n    // appended to the end.\\n    RemoteChainConfig remoteChainConfig; // The addresses of the remote RMNProxy, Router, factory, and token\\n    // decimals which are needed for determining the remote address\\n    PoolType poolType; // The type of pool to deploy, either Burn/Mint or Lock/Release\\n    bytes remoteTokenAddress; // EVM address for remote token. If empty, the address will be predicted\\n    bytes remoteTokenInitCode; // The init code to be deployed on the remote chain and includes constructor params\\n    RateLimiter.Config rateLimiterConfig; // Token Pool rate limit. Values will be applied on incoming an outgoing messages\\n  }\\n\\n  // solhint-disable-next-line gas-struct-packing\\n  struct RemoteChainConfig {\\n    address remotePoolFactory; // The factory contract on the remote chain which will make the deployment\\n    address remoteRouter; // The router on the remote chain\\n    address remoteRMNProxy; // The RMNProxy contract on the remote chain\\n    uint8 remoteTokenDecimals; // The number of decimals for the token on the remote chain\\n  }\\n\\n  string public constant typeAndVersion = \\\"TokenPoolFactory 1.5.1\\\";\\n\\n  ITokenAdminRegistry private immutable i_tokenAdminRegistry;\\n  RegistryModuleOwnerCustom private immutable i_registryModuleOwnerCustom;\\n\\n  address private immutable i_rmnProxy;\\n  address private immutable i_ccipRouter;\\n\\n  /// @notice Construct the TokenPoolFactory\\n  /// @param tokenAdminRegistry The address of the token admin registry\\n  /// @param tokenAdminModule The address of the token admin module which can register the token via ownership module\\n  /// @param rmnProxy The address of the RMNProxy contract token pools will be deployed with\\n  /// @param ccipRouter The address of the CCIPRouter contract token pools will be deployed with\\n  constructor(\\n    ITokenAdminRegistry tokenAdminRegistry,\\n    RegistryModuleOwnerCustom tokenAdminModule,\\n    address rmnProxy,\\n    address ccipRouter\\n  ) {\\n    if (\\n      address(tokenAdminRegistry) == address(0) || address(tokenAdminModule) == address(0) || rmnProxy == address(0)\\n        || ccipRouter == address(0)\\n    ) revert InvalidZeroAddress();\\n\\n    i_tokenAdminRegistry = ITokenAdminRegistry(tokenAdminRegistry);\\n    i_registryModuleOwnerCustom = RegistryModuleOwnerCustom(tokenAdminModule);\\n    i_rmnProxy = rmnProxy;\\n    i_ccipRouter = ccipRouter;\\n  }\\n\\n  // ================================================================\\n  // │                   Top-Level Deployment                       │\\n  // ================================================================\\n\\n  /// @notice Deploys a token and token pool with the given token information and configures it with remote token pools\\n  /// @dev The token and token pool are deployed in the same transaction, and the token pool is configured with the\\n  /// remote token pools. The token pool is then set in the token admin registry. Ownership of the everything is transferred\\n  /// to the msg.sender, but must be accepted in a separate transaction due to 2-step ownership transfer.\\n  /// @param remoteTokenPools An array of remote token pools info to be used in the pool's applyChainUpdates function\\n  /// or to be predicted if the pool has not been deployed yet on the remote chain\\n  /// @param localTokenDecimals The amount of decimals to be used in the new token. Since decimals() is not part of the\\n  /// the ERC20 standard, and thus cannot be certain to exist, the amount must be supplied via user input.\\n  /// @param tokenInitCode The creation code for the token, which includes the constructor parameters already appended\\n  /// @param tokenPoolInitCode The creation code for the token pool, without the constructor parameters appended\\n  /// @param salt The salt to be used in the create2 deployment of the token and token pool to ensure a unique address\\n  /// @return token The address of the token that was deployed\\n  /// @return pool The address of the token pool that was deployed\\n  function deployTokenAndTokenPool(\\n    RemoteTokenPoolInfo[] calldata remoteTokenPools,\\n    uint8 localTokenDecimals,\\n    bytes memory tokenInitCode,\\n    bytes calldata tokenPoolInitCode,\\n    bytes32 salt\\n  ) external returns (address, address) {\\n    // Ensure a unique deployment between senders even if the same input parameter is used to prevent\\n    // DOS/front running attacks\\n    salt = keccak256(abi.encodePacked(salt, msg.sender));\\n\\n    // Deploy the token. The constructor parameters are already provided in the tokenInitCode\\n    address token = Create2.deploy(0, salt, tokenInitCode);\\n\\n    // Deploy the token pool\\n    address pool =\\n      _createTokenPool(token, localTokenDecimals, remoteTokenPools, tokenPoolInitCode, salt, PoolType.BURN_MINT);\\n\\n    // Grant the mint and burn roles to the pool for the token\\n    FactoryBurnMintERC20(token).grantMintAndBurnRoles(pool);\\n\\n    // Set the token pool for token in the token admin registry since this contract is the token and pool owner\\n    _setTokenPoolInTokenAdminRegistry(token, pool);\\n\\n    // Begin the 2 step ownership transfer of the newly deployed token to the msg.sender\\n    IOwnable(token).transferOwnership(msg.sender);\\n\\n    return (token, pool);\\n  }\\n\\n  /// @notice Deploys a token pool with an existing ERC20 token\\n  /// @dev Since the token already exists, this contract is not the owner and therefore cannot configure the\\n  /// token pool in the token admin registry in the same transaction. The user must invoke the calls to the\\n  /// tokenAdminRegistry manually\\n  /// @dev since the token already exists, the owner must grant the mint and burn roles to the pool manually\\n  /// @param token The address of the existing token to be used in the token pool\\n  /// @param localTokenDecimals The amount of decimals used in the existing token. Since decimals() is not part of the\\n  /// the ERC20 standard, and thus cannot be certain to exist, the amount must be supplied via user input.\\n  /// @param remoteTokenPools An array of remote token pools info to be used in the pool's applyChainUpdates function\\n  /// @param tokenPoolInitCode The creation code for the token pool\\n  /// @param salt The salt to be used in the create2 deployment of the token pool\\n  /// @return poolAddress The address of the token pool that was deployed\\n  function deployTokenPoolWithExistingToken(\\n    address token,\\n    uint8 localTokenDecimals,\\n    RemoteTokenPoolInfo[] calldata remoteTokenPools,\\n    bytes calldata tokenPoolInitCode,\\n    bytes32 salt,\\n    PoolType poolType\\n  ) external returns (address poolAddress) {\\n    // Ensure a unique deployment between senders even if the same input parameter is used to prevent\\n    // DOS/front running attacks\\n    salt = keccak256(abi.encodePacked(salt, msg.sender));\\n\\n    // create the token pool and return the address\\n    return _createTokenPool(token, localTokenDecimals, remoteTokenPools, tokenPoolInitCode, salt, poolType);\\n  }\\n\\n  // ================================================================\\n  // │                Pool Deployment/Configuration                 │\\n  // ================================================================\\n\\n  /// @notice Deploys a token pool with the given token information and remote token pools\\n  /// @param token The token to be used in the token pool\\n  /// @param remoteTokenPools An array of remote token pools info to be used in the pool's applyChainUpdates function\\n  /// @param tokenPoolInitCode The creation code for the token pool\\n  /// @param salt The salt to be used in the create2 deployment of the token pool\\n  /// @return poolAddress The address of the token pool that was deployed\\n  function _createTokenPool(\\n    address token,\\n    uint8 localTokenDecimals,\\n    RemoteTokenPoolInfo[] calldata remoteTokenPools,\\n    bytes calldata tokenPoolInitCode,\\n    bytes32 salt,\\n    PoolType poolType\\n  ) private returns (address) {\\n    // Create an array of chain updates to apply to the token pool\\n    TokenPool.ChainUpdate[] memory chainUpdates = new TokenPool.ChainUpdate[](remoteTokenPools.length);\\n\\n    RemoteTokenPoolInfo memory remoteTokenPool;\\n    for (uint256 i = 0; i \\u003c remoteTokenPools.length; ++i) {\\n      remoteTokenPool = remoteTokenPools[i];\\n\\n      // If the user provides an empty byte string, indicated no token has already been deployed,\\n      // then the address of the token needs to be predicted. Otherwise the address provided will be used.\\n      if (remoteTokenPool.remoteTokenAddress.length == 0) {\\n        // The user must provide the initCode for the remote token, so its address can be predicted correctly. It's\\n        // provided in the remoteTokenInitCode field for the remoteTokenPool\\n        remoteTokenPool.remoteTokenAddress = abi.encode(\\n          salt.computeAddress(\\n            keccak256(remoteTokenPool.remoteTokenInitCode), remoteTokenPool.remoteChainConfig.remotePoolFactory\\n          )\\n        );\\n      }\\n\\n      // If the user provides an empty byte string parameter, indicating the pool has not been deployed yet,\\n      // the address of the pool should be predicted. Otherwise use the provided address.\\n      if (remoteTokenPool.remotePoolAddress.length == 0) {\\n        // Address is predicted based on the init code hash and the deployer, so the hash must first be computed\\n        // using the initCode and a concatenated set of constructor parameters.\\n        bytes32 remotePoolInitcodeHash = _generatePoolInitcodeHash(\\n          remoteTokenPool.remotePoolInitCode,\\n          remoteTokenPool.remoteChainConfig,\\n          abi.decode(remoteTokenPool.remoteTokenAddress, (address)),\\n          remoteTokenPool.poolType\\n        );\\n\\n        // Abi encode the computed remote address so it can be used as bytes in the chain update\\n        remoteTokenPool.remotePoolAddress =\\n          abi.encode(salt.computeAddress(remotePoolInitcodeHash, remoteTokenPool.remoteChainConfig.remotePoolFactory));\\n      }\\n\\n      bytes[] memory remotePoolAddresses = new bytes[](1);\\n      remotePoolAddresses[0] = remoteTokenPool.remotePoolAddress;\\n\\n      chainUpdates[i] = TokenPool.ChainUpdate({\\n        remoteChainSelector: remoteTokenPool.remoteChainSelector,\\n        remotePoolAddresses: remotePoolAddresses,\\n        remoteTokenAddress: remoteTokenPool.remoteTokenAddress,\\n        outboundRateLimiterConfig: remoteTokenPool.rateLimiterConfig,\\n        inboundRateLimiterConfig: remoteTokenPool.rateLimiterConfig\\n      });\\n    }\\n\\n    // Construct the initArgs for the token pool using the immutable contracts for CCIP on the local chain\\n    bytes memory tokenPoolInitArgs;\\n    if (poolType == PoolType.BURN_MINT) {\\n      tokenPoolInitArgs = abi.encode(token, localTokenDecimals, new address[](0), i_rmnProxy, i_ccipRouter);\\n    } else if (poolType == PoolType.LOCK_RELEASE) {\\n      // Lock/Release pools have an additional boolean constructor parameter that must be accounted for, acceptLiquidity,\\n      // which is set to true by default in this case. Users wishing to set it to false must deploy the pool manually.\\n      tokenPoolInitArgs = abi.encode(token, localTokenDecimals, new address[](0), i_rmnProxy, true, i_ccipRouter);\\n    }\\n\\n    // Construct the deployment code from the initCode and the initArgs and then deploy\\n    address poolAddress = Create2.deploy(0, salt, abi.encodePacked(tokenPoolInitCode, tokenPoolInitArgs));\\n\\n    // Apply the chain updates to the token pool\\n    TokenPool(poolAddress).applyChainUpdates(new uint64[](0), chainUpdates);\\n\\n    // Begin the 2 step ownership transfer of the token pool to the msg.sender.\\n    IOwnable(poolAddress).transferOwnership(address(msg.sender)); // 2 step ownership transfer\\n\\n    return poolAddress;\\n  }\\n\\n  /// @notice Generates the hash of the init code the pool will be deployed with\\n  /// @dev The init code hash is used with Create2 to predict the address of the pool on the remote chain\\n  /// @dev ABI-encoding limitations prevent arbitrary constructor parameters from being used, so pool type must be\\n  /// restricted to those with known types in the constructor. This function should be updated if new pool types are needed.\\n  /// @param initCode The init code of the pool\\n  /// @param remoteChainConfig The remote chain config for the pool\\n  /// @param remoteTokenAddress The address of the remote token\\n  /// @param poolType The type of pool to deploy\\n  /// @return bytes32 hash of the init code to be used in the deterministic address calculation\\n  function _generatePoolInitcodeHash(\\n    bytes memory initCode,\\n    RemoteChainConfig memory remoteChainConfig,\\n    address remoteTokenAddress,\\n    PoolType poolType\\n  ) private pure returns (bytes32) {\\n    if (poolType == PoolType.BURN_MINT) {\\n      return keccak256(\\n        abi.encodePacked(\\n          initCode,\\n          // constructor(address token, uint8 localTokenDecimals, address[] allowlist, address rmnProxy, address router)\\n          abi.encode(\\n            remoteTokenAddress,\\n            remoteChainConfig.remoteTokenDecimals,\\n            new address[](0),\\n            remoteChainConfig.remoteRMNProxy,\\n            remoteChainConfig.remoteRouter\\n          )\\n        )\\n      );\\n    } else {\\n      // if poolType is PoolType.LOCK_RELEASE, but may be expanded in future versions\\n      return keccak256(\\n        abi.encodePacked(\\n          initCode,\\n          // constructor(address token, uint8 localTokenDecimals, address[] allowList, address rmnProxy, bool acceptLiquidity, address router)\\n          abi.encode(\\n            remoteTokenAddress,\\n            remoteChainConfig.remoteTokenDecimals,\\n            new address[](0),\\n            remoteChainConfig.remoteRMNProxy,\\n            true,\\n            remoteChainConfig.remoteRouter\\n          )\\n        )\\n      );\\n    }\\n  }\\n\\n  /// @notice Sets the token pool address in the token admin registry for a newly deployed token pool.\\n  /// @dev this function should only be called when the token is deployed by this contract as well, otherwise\\n  /// the token pool will not be able to be set in the token admin registry, and this function will revert.\\n  /// @param token The address of the token to set the pool for\\n  /// @param pool The address of the pool to set in the token admin registry\\n  function _setTokenPoolInTokenAdminRegistry(address token, address pool) private {\\n    i_registryModuleOwnerCustom.registerAdminViaOwner(token);\\n    i_tokenAdminRegistry.acceptAdminRole(token);\\n    i_tokenAdminRegistry.setPool(token, pool);\\n\\n    // Begin the 2 admin transfer process which must be accepted in a separate tx.\\n    i_tokenAdminRegistry.transferAdminRole(token, msg.sender);\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IERC20} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\n\\ninterface IBurnMintERC20 is IERC20 {\\n  /// @notice Mints new tokens for a given address.\\n  /// @param account The address to mint the new tokens to.\\n  /// @param amount The number of tokens to be minted.\\n  /// @dev this function increases the total supply.\\n  function mint(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from the sender.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burnFrom(address account, uint256 amount) external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/ERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (token/ERC20/ERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC20.sol\\\";\\nimport \\\"./extensions/IERC20Metadata.sol\\\";\\nimport \\\"../../utils/Context.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC20} interface.\\n *\\n * This implementation is agnostic to the way tokens are created. This means\\n * that a supply mechanism has to be added in a derived contract using {_mint}.\\n * For a generic mechanism see {ERC20PresetMinterPauser}.\\n *\\n * TIP: For a detailed writeup see our guide\\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\\n * to implement supply mechanisms].\\n *\\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\\n * instead returning `false` on failure. This behavior is nonetheless\\n * conventional and does not conflict with the expectations of ERC20\\n * applications.\\n *\\n * Additionally, an {Approval} event is emitted on calls to {transferFrom}.\\n * This allows applications to reconstruct the allowance for all accounts just\\n * by listening to said events. Other implementations of the EIP may not emit\\n * these events, as it isn't required by the specification.\\n *\\n * Finally, the non-standard {decreaseAllowance} and {increaseAllowance}\\n * functions have been added to mitigate the well-known issues around setting\\n * allowances. See {IERC20-approve}.\\n */\\ncontract ERC20 is Context, IERC20, IERC20Metadata {\\n  mapping(address =\\u003e uint256) private _balances;\\n\\n  mapping(address =\\u003e mapping(address =\\u003e uint256)) private _allowances;\\n\\n  uint256 private _totalSupply;\\n\\n  string private _name;\\n  string private _symbol;\\n\\n  /**\\n   * @dev Sets the values for {name} and {symbol}.\\n   *\\n   * The default value of {decimals} is 18. To select a different value for\\n   * {decimals} you should overload it.\\n   *\\n   * All two of these values are immutable: they can only be set once during\\n   * construction.\\n   */\\n  constructor(string memory name_, string memory symbol_) {\\n    _name = name_;\\n    _symbol = symbol_;\\n  }\\n\\n  /**\\n   * @dev Returns the name of the token.\\n   */\\n  function name() public view virtual override returns (string memory) {\\n    return _name;\\n  }\\n\\n  /**\\n   * @dev Returns the symbol of the token, usually a shorter version of the\\n   * name.\\n   */\\n  function symbol() public view virtual override returns (string memory) {\\n    return _symbol;\\n  }\\n\\n  /**\\n   * @dev Returns the number of decimals used to get its user representation.\\n   * For example, if `decimals` equals `2`, a balance of `505` tokens should\\n   * be displayed to a user as `5.05` (`505 / 10 ** 2`).\\n   *\\n   * Tokens usually opt for a value of 18, imitating the relationship between\\n   * Ether and Wei. This is the value {ERC20} uses, unless this function is\\n   * overridden;\\n   *\\n   * NOTE: This information is only used for _display_ purposes: it in\\n   * no way affects any of the arithmetic of the contract, including\\n   * {IERC20-balanceOf} and {IERC20-transfer}.\\n   */\\n  function decimals() public view virtual override returns (uint8) {\\n    return 18;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-totalSupply}.\\n   */\\n  function totalSupply() public view virtual override returns (uint256) {\\n    return _totalSupply;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-balanceOf}.\\n   */\\n  function balanceOf(address account) public view virtual override returns (uint256) {\\n    return _balances[account];\\n  }\\n\\n  /**\\n   * @dev See {IERC20-transfer}.\\n   *\\n   * Requirements:\\n   *\\n   * - `to` cannot be the zero address.\\n   * - the caller must have a balance of at least `amount`.\\n   */\\n  function transfer(address to, uint256 amount) public virtual override returns (bool) {\\n    address owner = _msgSender();\\n    _transfer(owner, to, amount);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-allowance}.\\n   */\\n  function allowance(address owner, address spender) public view virtual override returns (uint256) {\\n    return _allowances[owner][spender];\\n  }\\n\\n  /**\\n   * @dev See {IERC20-approve}.\\n   *\\n   * NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on\\n   * `transferFrom`. This is semantically equivalent to an infinite approval.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   */\\n  function approve(address spender, uint256 amount) public virtual override returns (bool) {\\n    address owner = _msgSender();\\n    _approve(owner, spender, amount);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-transferFrom}.\\n   *\\n   * Emits an {Approval} event indicating the updated allowance. This is not\\n   * required by the EIP. See the note at the beginning of {ERC20}.\\n   *\\n   * NOTE: Does not update the allowance if the current allowance\\n   * is the maximum `uint256`.\\n   *\\n   * Requirements:\\n   *\\n   * - `from` and `to` cannot be the zero address.\\n   * - `from` must have a balance of at least `amount`.\\n   * - the caller must have allowance for ``from``'s tokens of at least\\n   * `amount`.\\n   */\\n  function transferFrom(address from, address to, uint256 amount) public virtual override returns (bool) {\\n    address spender = _msgSender();\\n    _spendAllowance(from, spender, amount);\\n    _transfer(from, to, amount);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev Atomically increases the allowance granted to `spender` by the caller.\\n   *\\n   * This is an alternative to {approve} that can be used as a mitigation for\\n   * problems described in {IERC20-approve}.\\n   *\\n   * Emits an {Approval} event indicating the updated allowance.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   */\\n  function increaseAllowance(address spender, uint256 addedValue) public virtual returns (bool) {\\n    address owner = _msgSender();\\n    _approve(owner, spender, allowance(owner, spender) + addedValue);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev Atomically decreases the allowance granted to `spender` by the caller.\\n   *\\n   * This is an alternative to {approve} that can be used as a mitigation for\\n   * problems described in {IERC20-approve}.\\n   *\\n   * Emits an {Approval} event indicating the updated allowance.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   * - `spender` must have allowance for the caller of at least\\n   * `subtractedValue`.\\n   */\\n  function decreaseAllowance(address spender, uint256 subtractedValue) public virtual returns (bool) {\\n    address owner = _msgSender();\\n    uint256 currentAllowance = allowance(owner, spender);\\n    require(currentAllowance \\u003e= subtractedValue, \\\"ERC20: decreased allowance below zero\\\");\\n    unchecked {\\n      _approve(owner, spender, currentAllowance - subtractedValue);\\n    }\\n\\n    return true;\\n  }\\n\\n  /**\\n   * @dev Moves `amount` of tokens from `from` to `to`.\\n   *\\n   * This internal function is equivalent to {transfer}, and can be used to\\n   * e.g. implement automatic token fees, slashing mechanisms, etc.\\n   *\\n   * Emits a {Transfer} event.\\n   *\\n   * Requirements:\\n   *\\n   * - `from` cannot be the zero address.\\n   * - `to` cannot be the zero address.\\n   * - `from` must have a balance of at least `amount`.\\n   */\\n  function _transfer(address from, address to, uint256 amount) internal virtual {\\n    require(from != address(0), \\\"ERC20: transfer from the zero address\\\");\\n    require(to != address(0), \\\"ERC20: transfer to the zero address\\\");\\n\\n    _beforeTokenTransfer(from, to, amount);\\n\\n    uint256 fromBalance = _balances[from];\\n    require(fromBalance \\u003e= amount, \\\"ERC20: transfer amount exceeds balance\\\");\\n    unchecked {\\n      _balances[from] = fromBalance - amount;\\n      // Overflow not possible: the sum of all balances is capped by totalSupply, and the sum is preserved by\\n      // decrementing then incrementing.\\n      _balances[to] += amount;\\n    }\\n\\n    emit Transfer(from, to, amount);\\n\\n    _afterTokenTransfer(from, to, amount);\\n  }\\n\\n  /** @dev Creates `amount` tokens and assigns them to `account`, increasing\\n   * the total supply.\\n   *\\n   * Emits a {Transfer} event with `from` set to the zero address.\\n   *\\n   * Requirements:\\n   *\\n   * - `account` cannot be the zero address.\\n   */\\n  function _mint(address account, uint256 amount) internal virtual {\\n    require(account != address(0), \\\"ERC20: mint to the zero address\\\");\\n\\n    _beforeTokenTransfer(address(0), account, amount);\\n\\n    _totalSupply += amount;\\n    unchecked {\\n      // Overflow not possible: balance + amount is at most totalSupply + amount, which is checked above.\\n      _balances[account] += amount;\\n    }\\n    emit Transfer(address(0), account, amount);\\n\\n    _afterTokenTransfer(address(0), account, amount);\\n  }\\n\\n  /**\\n   * @dev Destroys `amount` tokens from `account`, reducing the\\n   * total supply.\\n   *\\n   * Emits a {Transfer} event with `to` set to the zero address.\\n   *\\n   * Requirements:\\n   *\\n   * - `account` cannot be the zero address.\\n   * - `account` must have at least `amount` tokens.\\n   */\\n  function _burn(address account, uint256 amount) internal virtual {\\n    require(account != address(0), \\\"ERC20: burn from the zero address\\\");\\n\\n    _beforeTokenTransfer(account, address(0), amount);\\n\\n    uint256 accountBalance = _balances[account];\\n    require(accountBalance \\u003e= amount, \\\"ERC20: burn amount exceeds balance\\\");\\n    unchecked {\\n      _balances[account] = accountBalance - amount;\\n      // Overflow not possible: amount \\u003c= accountBalance \\u003c= totalSupply.\\n      _totalSupply -= amount;\\n    }\\n\\n    emit Transfer(account, address(0), amount);\\n\\n    _afterTokenTransfer(account, address(0), amount);\\n  }\\n\\n  /**\\n   * @dev Sets `amount` as the allowance of `spender` over the `owner` s tokens.\\n   *\\n   * This internal function is equivalent to `approve`, and can be used to\\n   * e.g. set automatic allowances for certain subsystems, etc.\\n   *\\n   * Emits an {Approval} event.\\n   *\\n   * Requirements:\\n   *\\n   * - `owner` cannot be the zero address.\\n   * - `spender` cannot be the zero address.\\n   */\\n  function _approve(address owner, address spender, uint256 amount) internal virtual {\\n    require(owner != address(0), \\\"ERC20: approve from the zero address\\\");\\n    require(spender != address(0), \\\"ERC20: approve to the zero address\\\");\\n\\n    _allowances[owner][spender] = amount;\\n    emit Approval(owner, spender, amount);\\n  }\\n\\n  /**\\n   * @dev Updates `owner` s allowance for `spender` based on spent `amount`.\\n   *\\n   * Does not update the allowance amount in case of infinite allowance.\\n   * Revert if not enough allowance is available.\\n   *\\n   * Might emit an {Approval} event.\\n   */\\n  function _spendAllowance(address owner, address spender, uint256 amount) internal virtual {\\n    uint256 currentAllowance = allowance(owner, spender);\\n    if (currentAllowance != type(uint256).max) {\\n      require(currentAllowance \\u003e= amount, \\\"ERC20: insufficient allowance\\\");\\n      unchecked {\\n        _approve(owner, spender, currentAllowance - amount);\\n      }\\n    }\\n  }\\n\\n  /**\\n   * @dev Hook that is called before any transfer of tokens. This includes\\n   * minting and burning.\\n   *\\n   * Calling conditions:\\n   *\\n   * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n   * will be transferred to `to`.\\n   * - when `from` is zero, `amount` tokens will be minted for `to`.\\n   * - when `to` is zero, `amount` of ``from``'s tokens will be burned.\\n   * - `from` and `to` are never both zero.\\n   *\\n   * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n   */\\n  function _beforeTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n\\n  /**\\n   * @dev Hook that is called after any transfer of tokens. This includes\\n   * minting and burning.\\n   *\\n   * Calling conditions:\\n   *\\n   * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n   * has been transferred to `to`.\\n   * - when `from` is zero, `amount` tokens have been minted for `to`.\\n   * - when `to` is zero, `amount` of ``from``'s tokens have been burned.\\n   * - `from` and `to` are never both zero.\\n   *\\n   * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n   */\\n  function _afterTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.6.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20 {\\n  /**\\n   * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n   * another (`to`).\\n   *\\n   * Note that `value` may be zero.\\n   */\\n  event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n  /**\\n   * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n   * a call to {approve}. `value` is the new allowance.\\n   */\\n  event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n  /**\\n   * @dev Returns the amount of tokens in existence.\\n   */\\n  function totalSupply() external view returns (uint256);\\n\\n  /**\\n   * @dev Returns the amount of tokens owned by `account`.\\n   */\\n  function balanceOf(address account) external view returns (uint256);\\n\\n  /**\\n   * @dev Moves `amount` tokens from the caller's account to `to`.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transfer(address to, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Returns the remaining number of tokens that `spender` will be\\n   * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n   * zero by default.\\n   *\\n   * This value changes when {approve} or {transferFrom} are called.\\n   */\\n  function allowance(address owner, address spender) external view returns (uint256);\\n\\n  /**\\n   * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n   * that someone may use both the old and the new allowance by unfortunate\\n   * transaction ordering. One possible solution to mitigate this race\\n   * condition is to first reduce the spender's allowance to 0 and set the\\n   * desired value afterwards:\\n   * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n   *\\n   * Emits an {Approval} event.\\n   */\\n  function approve(address spender, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Moves `amount` tokens from `from` to `to` using the\\n   * allowance mechanism. `amount` is then deducted from the caller's\\n   * allowance.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transferFrom(address from, address to, uint256 amount) external returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.5.0) (token/ERC20/extensions/ERC20Burnable.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../ERC20.sol\\\";\\nimport \\\"../../../utils/Context.sol\\\";\\n\\n/**\\n * @dev Extension of {ERC20} that allows token holders to destroy both their own\\n * tokens and those that they have an allowance for, in a way that can be\\n * recognized off-chain (via event analysis).\\n */\\nabstract contract ERC20Burnable is Context, ERC20 {\\n  /**\\n   * @dev Destroys `amount` tokens from the caller.\\n   *\\n   * See {ERC20-_burn}.\\n   */\\n  function burn(uint256 amount) public virtual {\\n    _burn(_msgSender(), amount);\\n  }\\n\\n  /**\\n   * @dev Destroys `amount` tokens from `account`, deducting from the caller's\\n   * allowance.\\n   *\\n   * See {ERC20-_burn} and {ERC20-allowance}.\\n   *\\n   * Requirements:\\n   *\\n   * - the caller must have allowance for ``accounts``'s tokens of at least\\n   * `amount`.\\n   */\\n  function burnFrom(address account, uint256 amount) public virtual {\\n    _spendAllowance(account, _msgSender(), amount);\\n    _burn(account, amount);\\n  }\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC20/extensions/IERC20Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC20.sol\\\";\\n\\n/**\\n * @dev Interface for the optional metadata functions from the ERC20 standard.\\n *\\n * _Available since v4.1._\\n */\\ninterface IERC20Metadata is IERC20 {\\n  /**\\n   * @dev Returns the name of the token.\\n   */\\n  function name() external view returns (string memory);\\n\\n  /**\\n   * @dev Returns the symbol of the token.\\n   */\\n  function symbol() external view returns (string memory);\\n\\n  /**\\n   * @dev Returns the decimals places of the token.\\n   */\\n  function decimals() external view returns (uint8);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Context.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Context.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract Context {\\n  function _msgSender() internal view virtual returns (address) {\\n    return msg.sender;\\n  }\\n\\n  function _msgData() internal view virtual returns (bytes calldata) {\\n    return msg.data;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n  // To implement this library for multiple types with as little code\\n  // repetition as possible, we write it in terms of a generic Set type with\\n  // bytes32 values.\\n  // The Set implementation uses private functions, and user-facing\\n  // implementations (such as AddressSet) are just wrappers around the\\n  // underlying Set.\\n  // This means that we can only create new EnumerableSets for types that fit\\n  // in bytes32.\\n\\n  struct Set {\\n    // Storage of set values\\n    bytes32[] _values;\\n    // Position of the value in the `values` array, plus 1 because index 0\\n    // means a value is not in the set.\\n    mapping(bytes32 =\\u003e uint256) _indexes;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function _add(Set storage set, bytes32 value) private returns (bool) {\\n    if (!_contains(set, value)) {\\n      set._values.push(value);\\n      // The value is stored at length-1, but we add 1 to all indexes\\n      // and use 0 as a sentinel value\\n      set._indexes[value] = set._values.length;\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function _remove(Set storage set, bytes32 value) private returns (bool) {\\n    // We read and store the value's index to prevent multiple reads from the same storage slot\\n    uint256 valueIndex = set._indexes[value];\\n\\n    if (valueIndex != 0) {\\n      // Equivalent to contains(set, value)\\n      // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n      // the array, and then remove the last element (sometimes called as 'swap and pop').\\n      // This modifies the order of the array, as noted in {at}.\\n\\n      uint256 toDeleteIndex = valueIndex - 1;\\n      uint256 lastIndex = set._values.length - 1;\\n\\n      if (lastIndex != toDeleteIndex) {\\n        bytes32 lastValue = set._values[lastIndex];\\n\\n        // Move the last value to the index where the value to delete is\\n        set._values[toDeleteIndex] = lastValue;\\n        // Update the index for the moved value\\n        set._indexes[lastValue] = valueIndex; // Replace lastValue's index to valueIndex\\n      }\\n\\n      // Delete the slot where the moved value was stored\\n      set._values.pop();\\n\\n      // Delete the index for the deleted slot\\n      delete set._indexes[value];\\n\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n    return set._indexes[value] != 0;\\n  }\\n\\n  /**\\n   * @dev Returns the number of values on the set. O(1).\\n   */\\n  function _length(Set storage set) private view returns (uint256) {\\n    return set._values.length;\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n    return set._values[index];\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function _values(Set storage set) private view returns (bytes32[] memory) {\\n    return set._values;\\n  }\\n\\n  // Bytes32Set\\n\\n  struct Bytes32Set {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _add(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _remove(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n    return _contains(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(Bytes32Set storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n    return _at(set._inner, index);\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    bytes32[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // AddressSet\\n\\n  struct AddressSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(AddressSet storage set, address value) internal returns (bool) {\\n    return _add(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(AddressSet storage set, address value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(AddressSet storage set, address value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(AddressSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n    return address(uint160(uint256(_at(set._inner, index))));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(AddressSet storage set) internal view returns (address[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    address[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // UintSet\\n\\n  struct UintSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _add(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(UintSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n    return uint256(_at(set._inner, index));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(UintSet storage set) internal view returns (uint256[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    uint256[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/access/AccessControl.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (access/AccessControl.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IAccessControl} from \\\"./IAccessControl.sol\\\";\\nimport {Context} from \\\"../utils/Context.sol\\\";\\nimport {ERC165} from \\\"../utils/introspection/ERC165.sol\\\";\\n\\n/**\\n * @dev Contract module that allows children to implement role-based access\\n * control mechanisms. This is a lightweight version that doesn't allow enumerating role\\n * members except through off-chain means by accessing the contract event logs. Some\\n * applications may benefit from on-chain enumerability, for those cases see\\n * {AccessControlEnumerable}.\\n *\\n * Roles are referred to by their `bytes32` identifier. These should be exposed\\n * in the external API and be unique. The best way to achieve this is by\\n * using `public constant` hash digests:\\n *\\n * ```solidity\\n * bytes32 public constant MY_ROLE = keccak256(\\\"MY_ROLE\\\");\\n * ```\\n *\\n * Roles can be used to represent a set of permissions. To restrict access to a\\n * function call, use {hasRole}:\\n *\\n * ```solidity\\n * function foo() public {\\n *     require(hasRole(MY_ROLE, msg.sender));\\n *     ...\\n * }\\n * ```\\n *\\n * Roles can be granted and revoked dynamically via the {grantRole} and\\n * {revokeRole} functions. Each role has an associated admin role, and only\\n * accounts that have a role's admin role can call {grantRole} and {revokeRole}.\\n *\\n * By default, the admin role for all roles is `DEFAULT_ADMIN_ROLE`, which means\\n * that only accounts with this role will be able to grant or revoke other\\n * roles. More complex role relationships can be created by using\\n * {_setRoleAdmin}.\\n *\\n * WARNING: The `DEFAULT_ADMIN_ROLE` is also its own admin: it has permission to\\n * grant and revoke this role. Extra precautions should be taken to secure\\n * accounts that have been granted it. We recommend using {AccessControlDefaultAdminRules}\\n * to enforce additional security measures for this role.\\n */\\nabstract contract AccessControl is Context, IAccessControl, ERC165 {\\n    struct RoleData {\\n        mapping(address account =\\u003e bool) hasRole;\\n        bytes32 adminRole;\\n    }\\n\\n    mapping(bytes32 role =\\u003e RoleData) private _roles;\\n\\n    bytes32 public constant DEFAULT_ADMIN_ROLE = 0x00;\\n\\n    /**\\n     * @dev Modifier that checks that an account has a specific role. Reverts\\n     * with an {AccessControlUnauthorizedAccount} error including the required role.\\n     */\\n    modifier onlyRole(bytes32 role) {\\n        _checkRole(role);\\n        _;\\n    }\\n\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n        return interfaceId == type(IAccessControl).interfaceId || super.supportsInterface(interfaceId);\\n    }\\n\\n    /**\\n     * @dev Returns `true` if `account` has been granted `role`.\\n     */\\n    function hasRole(bytes32 role, address account) public view virtual returns (bool) {\\n        return _roles[role].hasRole[account];\\n    }\\n\\n    /**\\n     * @dev Reverts with an {AccessControlUnauthorizedAccount} error if `_msgSender()`\\n     * is missing `role`. Overriding this function changes the behavior of the {onlyRole} modifier.\\n     */\\n    function _checkRole(bytes32 role) internal view virtual {\\n        _checkRole(role, _msgSender());\\n    }\\n\\n    /**\\n     * @dev Reverts with an {AccessControlUnauthorizedAccount} error if `account`\\n     * is missing `role`.\\n     */\\n    function _checkRole(bytes32 role, address account) internal view virtual {\\n        if (!hasRole(role, account)) {\\n            revert AccessControlUnauthorizedAccount(account, role);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the admin role that controls `role`. See {grantRole} and\\n     * {revokeRole}.\\n     *\\n     * To change a role's admin, use {_setRoleAdmin}.\\n     */\\n    function getRoleAdmin(bytes32 role) public view virtual returns (bytes32) {\\n        return _roles[role].adminRole;\\n    }\\n\\n    /**\\n     * @dev Grants `role` to `account`.\\n     *\\n     * If `account` had not been already granted `role`, emits a {RoleGranted}\\n     * event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must have ``role``'s admin role.\\n     *\\n     * May emit a {RoleGranted} event.\\n     */\\n    function grantRole(bytes32 role, address account) public virtual onlyRole(getRoleAdmin(role)) {\\n        _grantRole(role, account);\\n    }\\n\\n    /**\\n     * @dev Revokes `role` from `account`.\\n     *\\n     * If `account` had been granted `role`, emits a {RoleRevoked} event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must have ``role``'s admin role.\\n     *\\n     * May emit a {RoleRevoked} event.\\n     */\\n    function revokeRole(bytes32 role, address account) public virtual onlyRole(getRoleAdmin(role)) {\\n        _revokeRole(role, account);\\n    }\\n\\n    /**\\n     * @dev Revokes `role` from the calling account.\\n     *\\n     * Roles are often managed via {grantRole} and {revokeRole}: this function's\\n     * purpose is to provide a mechanism for accounts to lose their privileges\\n     * if they are compromised (such as when a trusted device is misplaced).\\n     *\\n     * If the calling account had been revoked `role`, emits a {RoleRevoked}\\n     * event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must be `callerConfirmation`.\\n     *\\n     * May emit a {RoleRevoked} event.\\n     */\\n    function renounceRole(bytes32 role, address callerConfirmation) public virtual {\\n        if (callerConfirmation != _msgSender()) {\\n            revert AccessControlBadConfirmation();\\n        }\\n\\n        _revokeRole(role, callerConfirmation);\\n    }\\n\\n    /**\\n     * @dev Sets `adminRole` as ``role``'s admin role.\\n     *\\n     * Emits a {RoleAdminChanged} event.\\n     */\\n    function _setRoleAdmin(bytes32 role, bytes32 adminRole) internal virtual {\\n        bytes32 previousAdminRole = getRoleAdmin(role);\\n        _roles[role].adminRole = adminRole;\\n        emit RoleAdminChanged(role, previousAdminRole, adminRole);\\n    }\\n\\n    /**\\n     * @dev Attempts to grant `role` to `account` and returns a boolean indicating if `role` was granted.\\n     *\\n     * Internal function without access restriction.\\n     *\\n     * May emit a {RoleGranted} event.\\n     */\\n    function _grantRole(bytes32 role, address account) internal virtual returns (bool) {\\n        if (!hasRole(role, account)) {\\n            _roles[role].hasRole[account] = true;\\n            emit RoleGranted(role, account, _msgSender());\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Attempts to revoke `role` to `account` and returns a boolean indicating if `role` was revoked.\\n     *\\n     * Internal function without access restriction.\\n     *\\n     * May emit a {RoleRevoked} event.\\n     */\\n    function _revokeRole(bytes32 role, address account) internal virtual returns (bool) {\\n        if (hasRole(role, account)) {\\n            _roles[role].hasRole[account] = false;\\n            emit RoleRevoked(role, account, _msgSender());\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/access/IAccessControl.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (access/IAccessControl.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev External interface of AccessControl declared to support ERC165 detection.\\n */\\ninterface IAccessControl {\\n    /**\\n     * @dev The `account` is missing a role.\\n     */\\n    error AccessControlUnauthorizedAccount(address account, bytes32 neededRole);\\n\\n    /**\\n     * @dev The caller of a function is not the expected one.\\n     *\\n     * NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\\n     */\\n    error AccessControlBadConfirmation();\\n\\n    /**\\n     * @dev Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole`\\n     *\\n     * `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite\\n     * {RoleAdminChanged} not being emitted signaling this.\\n     */\\n    event RoleAdminChanged(bytes32 indexed role, bytes32 indexed previousAdminRole, bytes32 indexed newAdminRole);\\n\\n    /**\\n     * @dev Emitted when `account` is granted `role`.\\n     *\\n     * `sender` is the account that originated the contract call, an admin role\\n     * bearer except when using {AccessControl-_setupRole}.\\n     */\\n    event RoleGranted(bytes32 indexed role, address indexed account, address indexed sender);\\n\\n    /**\\n     * @dev Emitted when `account` is revoked `role`.\\n     *\\n     * `sender` is the account that originated the contract call:\\n     *   - if using `revokeRole`, it is the admin role bearer\\n     *   - if using `renounceRole`, it is the role bearer (i.e. `account`)\\n     */\\n    event RoleRevoked(bytes32 indexed role, address indexed account, address indexed sender);\\n\\n    /**\\n     * @dev Returns `true` if `account` has been granted `role`.\\n     */\\n    function hasRole(bytes32 role, address account) external view returns (bool);\\n\\n    /**\\n     * @dev Returns the admin role that controls `role`. See {grantRole} and\\n     * {revokeRole}.\\n     *\\n     * To change a role's admin, use {AccessControl-_setRoleAdmin}.\\n     */\\n    function getRoleAdmin(bytes32 role) external view returns (bytes32);\\n\\n    /**\\n     * @dev Grants `role` to `account`.\\n     *\\n     * If `account` had not been already granted `role`, emits a {RoleGranted}\\n     * event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must have ``role``'s admin role.\\n     */\\n    function grantRole(bytes32 role, address account) external;\\n\\n    /**\\n     * @dev Revokes `role` from `account`.\\n     *\\n     * If `account` had been granted `role`, emits a {RoleRevoked} event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must have ``role``'s admin role.\\n     */\\n    function revokeRole(bytes32 role, address account) external;\\n\\n    /**\\n     * @dev Revokes `role` from the calling account.\\n     *\\n     * Roles are often managed via {grantRole} and {revokeRole}: this function's\\n     * purpose is to provide a mechanism for accounts to lose their privileges\\n     * if they are compromised (such as when a trusted device is misplaced).\\n     *\\n     * If the calling account had been granted `role`, emits a {RoleRevoked}\\n     * event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must be `callerConfirmation`.\\n     */\\n    function renounceRole(bytes32 role, address callerConfirmation) external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/Context.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract Context {\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    function _contextSuffixLength() internal view virtual returns (uint256) {\\n        return 0;\\n    }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/Create2.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/Create2.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {Errors} from \\\"./Errors.sol\\\";\\n\\n/**\\n * @dev Helper to make usage of the `CREATE2` EVM opcode easier and safer.\\n * `CREATE2` can be used to compute in advance the address where a smart\\n * contract will be deployed, which allows for interesting new mechanisms known\\n * as 'counterfactual interactions'.\\n *\\n * See the https://eips.ethereum.org/EIPS/eip-1014#motivation[EIP] for more\\n * information.\\n */\\nlibrary Create2 {\\n    /**\\n     * @dev There's no code to deploy.\\n     */\\n    error Create2EmptyBytecode();\\n\\n    /**\\n     * @dev Deploys a contract using `CREATE2`. The address where the contract\\n     * will be deployed can be known in advance via {computeAddress}.\\n     *\\n     * The bytecode for a contract can be obtained from Solidity with\\n     * `type(contractName).creationCode`.\\n     *\\n     * Requirements:\\n     *\\n     * - `bytecode` must not be empty.\\n     * - `salt` must have not been used for `bytecode` already.\\n     * - the factory must have a balance of at least `amount`.\\n     * - if `amount` is non-zero, `bytecode` must have a `payable` constructor.\\n     */\\n    function deploy(uint256 amount, bytes32 salt, bytes memory bytecode) internal returns (address addr) {\\n        if (address(this).balance \\u003c amount) {\\n            revert Errors.InsufficientBalance(address(this).balance, amount);\\n        }\\n        if (bytecode.length == 0) {\\n            revert Create2EmptyBytecode();\\n        }\\n        assembly (\\\"memory-safe\\\") {\\n            addr := create2(amount, add(bytecode, 0x20), mload(bytecode), salt)\\n            // if no address was created, and returndata is not empty, bubble revert\\n            if and(iszero(addr), not(iszero(returndatasize()))) {\\n                let p := mload(0x40)\\n                returndatacopy(p, 0, returndatasize())\\n                revert(p, returndatasize())\\n            }\\n        }\\n        if (addr == address(0)) {\\n            revert Errors.FailedDeployment();\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the address where a contract will be stored if deployed via {deploy}. Any change in the\\n     * `bytecodeHash` or `salt` will result in a new destination address.\\n     */\\n    function computeAddress(bytes32 salt, bytes32 bytecodeHash) internal view returns (address) {\\n        return computeAddress(salt, bytecodeHash, address(this));\\n    }\\n\\n    /**\\n     * @dev Returns the address where a contract will be stored if deployed via {deploy} from a contract located at\\n     * `deployer`. If `deployer` is this contract's address, returns the same value as {computeAddress}.\\n     */\\n    function computeAddress(bytes32 salt, bytes32 bytecodeHash, address deployer) internal pure returns (address addr) {\\n        assembly (\\\"memory-safe\\\") {\\n            let ptr := mload(0x40) // Get free memory pointer\\n\\n            // |                   | ↓ ptr ...  ↓ ptr + 0x0B (start) ...  ↓ ptr + 0x20 ...  ↓ ptr + 0x40 ...   |\\n            // |-------------------|---------------------------------------------------------------------------|\\n            // | bytecodeHash      |                                                        CCCCCCCCCCCCC...CC |\\n            // | salt              |                                      BBBBBBBBBBBBB...BB                   |\\n            // | deployer          | 000000...0000AAAAAAAAAAAAAAAAAAA...AA                                     |\\n            // | 0xFF              |            FF                                                             |\\n            // |-------------------|---------------------------------------------------------------------------|\\n            // | memory            | 000000...00FFAAAAAAAAAAAAAAAAAAA...AABBBBBBBBBBBBB...BBCCCCCCCCCCCCC...CC |\\n            // | keccak(start, 85) |            ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ |\\n\\n            mstore(add(ptr, 0x40), bytecodeHash)\\n            mstore(add(ptr, 0x20), salt)\\n            mstore(ptr, deployer) // Right-aligned with 12 preceding garbage bytes\\n            let start := add(ptr, 0x0b) // The hashed data starts at the final garbage byte which we will set to 0xff\\n            mstore8(start, 0xff)\\n            addr := and(keccak256(start, 85), 0xffffffffffffffffffffffffffffffffffffffff)\\n        }\\n    }\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/Errors.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Collection of common custom errors used in multiple contracts\\n *\\n * IMPORTANT: Backwards compatibility is not guaranteed in future versions of the library.\\n * It is recommended to avoid relying on the error API for critical functionality.\\n */\\nlibrary Errors {\\n    /**\\n     * @dev The ETH balance of the account is not enough to perform the operation.\\n     */\\n    error InsufficientBalance(uint256 balance, uint256 needed);\\n\\n    /**\\n     * @dev A call to an address target failed. The target may have reverted.\\n     */\\n    error FailedCall();\\n\\n    /**\\n     * @dev The deployment failed.\\n     */\\n    error FailedDeployment();\\n\\n    /**\\n     * @dev A necessary precompile is missing.\\n     */\\n    error MissingPrecompile(address);\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/ERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/introspection/ERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC165} from \\\"./IERC165.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC165} interface.\\n *\\n * Contracts that want to implement ERC165 should inherit from this contract and override {supportsInterface} to check\\n * for the additional interface id that will be supported. For example:\\n *\\n * ```solidity\\n * function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n *     return interfaceId == type(MyInterface).interfaceId || super.supportsInterface(interfaceId);\\n * }\\n * ```\\n */\\nabstract contract ERC165 is IERC165 {\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual returns (bool) {\\n        return interfaceId == type(IERC165).interfaceId;\\n    }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```solidity\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n    // To implement this library for multiple types with as little code\\n    // repetition as possible, we write it in terms of a generic Set type with\\n    // bytes32 values.\\n    // The Set implementation uses private functions, and user-facing\\n    // implementations (such as AddressSet) are just wrappers around the\\n    // underlying Set.\\n    // This means that we can only create new EnumerableSets for types that fit\\n    // in bytes32.\\n\\n    struct Set {\\n        // Storage of set values\\n        bytes32[] _values;\\n        // Position is the index of the value in the `values` array plus 1.\\n        // Position 0 is used to mean a value is not in the set.\\n        mapping(bytes32 value =\\u003e uint256) _positions;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function _add(Set storage set, bytes32 value) private returns (bool) {\\n        if (!_contains(set, value)) {\\n            set._values.push(value);\\n            // The value is stored at length-1, but we add 1 to all indexes\\n            // and use 0 as a sentinel value\\n            set._positions[value] = set._values.length;\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function _remove(Set storage set, bytes32 value) private returns (bool) {\\n        // We cache the value's position to prevent multiple reads from the same storage slot\\n        uint256 position = set._positions[value];\\n\\n        if (position != 0) {\\n            // Equivalent to contains(set, value)\\n            // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n            // the array, and then remove the last element (sometimes called as 'swap and pop').\\n            // This modifies the order of the array, as noted in {at}.\\n\\n            uint256 valueIndex = position - 1;\\n            uint256 lastIndex = set._values.length - 1;\\n\\n            if (valueIndex != lastIndex) {\\n                bytes32 lastValue = set._values[lastIndex];\\n\\n                // Move the lastValue to the index where the value to delete is\\n                set._values[valueIndex] = lastValue;\\n                // Update the tracked position of the lastValue (that was just moved)\\n                set._positions[lastValue] = position;\\n            }\\n\\n            // Delete the slot where the moved value was stored\\n            set._values.pop();\\n\\n            // Delete the tracked position for the deleted slot\\n            delete set._positions[value];\\n\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n        return set._positions[value] != 0;\\n    }\\n\\n    /**\\n     * @dev Returns the number of values on the set. O(1).\\n     */\\n    function _length(Set storage set) private view returns (uint256) {\\n        return set._values.length;\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n        return set._values[index];\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function _values(Set storage set) private view returns (bytes32[] memory) {\\n        return set._values;\\n    }\\n\\n    // Bytes32Set\\n\\n    struct Bytes32Set {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _add(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _remove(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n        return _contains(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(Bytes32Set storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n        return _at(set._inner, index);\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        bytes32[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // AddressSet\\n\\n    struct AddressSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(AddressSet storage set, address value) internal returns (bool) {\\n        return _add(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(AddressSet storage set, address value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(AddressSet storage set, address value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(AddressSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n        return address(uint160(uint256(_at(set._inner, index))));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(AddressSet storage set) internal view returns (address[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        address[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // UintSet\\n\\n    struct UintSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _add(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(UintSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n        return uint256(_at(set._inner, index));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(UintSet storage set) internal view returns (uint256[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        uint256[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n}\\n\"}}}"
