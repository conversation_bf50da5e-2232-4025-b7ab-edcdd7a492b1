// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package fee_quoter

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":8000},\"outputSelection\":{\"contracts/FeeQuoter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IFeeQuoter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IPriceRegistry.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Client.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Internal.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/MerkleMultiProof.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Pool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/USDPriceWith18Decimals.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/keystone/KeystoneFeedsPermissionHandler.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/keystone/interfaces/IReceiver.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/keystone/lib/KeystoneFeedDefaultMetadataLib.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/AuthorizedCallers.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/AggregatorV3Interface.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/interfaces/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/FeeQuoter.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IFeeQuoter} from \\\"./interfaces/IFeeQuoter.sol\\\";\\nimport {IPriceRegistry} from \\\"./interfaces/IPriceRegistry.sol\\\";\\nimport {IReceiver} from \\\"@chainlink/contracts/src/v0.8/keystone/interfaces/IReceiver.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {Client} from \\\"./libraries/Client.sol\\\";\\nimport {Internal} from \\\"./libraries/Internal.sol\\\";\\nimport {Pool} from \\\"./libraries/Pool.sol\\\";\\nimport {USDPriceWith18Decimals} from \\\"./libraries/USDPriceWith18Decimals.sol\\\";\\nimport {KeystoneFeedsPermissionHandler} from \\\"@chainlink/contracts/src/v0.8/keystone/KeystoneFeedsPermissionHandler.sol\\\";\\nimport {KeystoneFeedDefaultMetadataLib} from\\n  \\\"@chainlink/contracts/src/v0.8/keystone/lib/KeystoneFeedDefaultMetadataLib.sol\\\";\\nimport {AuthorizedCallers} from \\\"@chainlink/contracts/src/v0.8/shared/access/AuthorizedCallers.sol\\\";\\nimport {AggregatorV3Interface} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/AggregatorV3Interface.sol\\\";\\n\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/interfaces/IERC165.sol\\\";\\nimport {EnumerableSet} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @notice The FeeQuoter contract responsibility is to:\\n///   - Store the current gas price in USD for a given destination chain.\\n///   - Store the price of a token in USD allowing the owner or priceUpdater to update this value.\\n///   - Manage chain specific fee calculations.\\n/// The authorized callers in the contract represent the fee price updaters.\\ncontract FeeQuoter is AuthorizedCallers, IFeeQuoter, ITypeAndVersion, IReceiver, KeystoneFeedsPermissionHandler {\\n  using EnumerableSet for EnumerableSet.AddressSet;\\n  using USDPriceWith18Decimals for uint224;\\n  using KeystoneFeedDefaultMetadataLib for bytes;\\n\\n  error TokenNotSupported(address token);\\n  error FeeTokenNotSupported(address token);\\n  error StaleGasPrice(uint64 destChainSelector, uint256 threshold, uint256 timePassed);\\n  error DataFeedValueOutOfUint224Range();\\n  error InvalidDestBytesOverhead(address token, uint32 destBytesOverhead);\\n  error MessageGasLimitTooHigh();\\n  error MessageComputeUnitLimitTooHigh();\\n  error DestinationChainNotEnabled(uint64 destChainSelector);\\n  error ExtraArgOutOfOrderExecutionMustBeTrue();\\n  error InvalidExtraArgsTag();\\n  error InvalidExtraArgsData();\\n  error SourceTokenDataTooLarge(address token);\\n  error InvalidDestChainConfig(uint64 destChainSelector);\\n  error MessageFeeTooHigh(uint256 msgFeeJuels, uint256 maxFeeJuelsPerMsg);\\n  error InvalidStaticConfig();\\n  error MessageTooLarge(uint256 maxSize, uint256 actualSize);\\n  error UnsupportedNumberOfTokens(uint256 numberOfTokens, uint256 maxNumberOfTokensPerMsg);\\n  error InvalidFeeRange(uint256 minFeeUSDCents, uint256 maxFeeUSDCents);\\n  error InvalidChainFamilySelector(bytes4 chainFamilySelector);\\n  error InvalidTokenReceiver();\\n  error TooManySVMExtraArgsAccounts(uint256 numAccounts, uint256 maxAccounts);\\n  error InvalidSVMExtraArgsWritableBitmap(uint64 accountIsWritableBitmap, uint256 numAccounts);\\n\\n  event FeeTokenAdded(address indexed feeToken);\\n  event FeeTokenRemoved(address indexed feeToken);\\n  event UsdPerUnitGasUpdated(uint64 indexed destChain, uint256 value, uint256 timestamp);\\n  event UsdPerTokenUpdated(address indexed token, uint256 value, uint256 timestamp);\\n  event PriceFeedPerTokenUpdated(address indexed token, TokenPriceFeedConfig priceFeedConfig);\\n  event TokenTransferFeeConfigUpdated(\\n    uint64 indexed destChainSelector, address indexed token, TokenTransferFeeConfig tokenTransferFeeConfig\\n  );\\n  event TokenTransferFeeConfigDeleted(uint64 indexed destChainSelector, address indexed token);\\n  event PremiumMultiplierWeiPerEthUpdated(address indexed token, uint64 premiumMultiplierWeiPerEth);\\n  event DestChainConfigUpdated(uint64 indexed destChainSelector, DestChainConfig destChainConfig);\\n  event DestChainAdded(uint64 indexed destChainSelector, DestChainConfig destChainConfig);\\n\\n  /// @dev Contains token price configuration used in both the keystone price updates and the price feed fallback logic.\\n  struct TokenPriceFeedConfig {\\n    address dataFeedAddress; // ─╮ Price feed contract. Can be address(0) to indicate no feed is configured.\\n    uint8 tokenDecimals; //      │ Decimals of the token, used for both keystone and price feed decimal multiplications.\\n    bool isEnabled; // ──────────╯ Whether the token is configured to receive keystone and/or price feed updates.\\n  }\\n\\n  /// @dev Token price data feed update.\\n  struct TokenPriceFeedUpdate {\\n    address sourceToken; // Source token to update feed for.\\n    TokenPriceFeedConfig feedConfig; // Feed config update data.\\n  }\\n\\n  /// @dev Struct that contains the static configuration.\\n  /// RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct StaticConfig {\\n    uint96 maxFeeJuelsPerMsg; // ─╮ Maximum fee that can be charged for a message.\\n    address linkToken; // ────────╯ LINK token address.\\n    // The amount of time a token price can be stale before it is considered invalid. Gas price staleness is configured\\n    // per dest chain.\\n    uint32 tokenPriceStalenessThreshold;\\n  }\\n\\n  /// @dev The struct representing the received CCIP feed report from keystone IReceiver.onReport().\\n  struct ReceivedCCIPFeedReport {\\n    address token; //       Token address.\\n    uint224 price; // ────╮ Price of the token in USD with 18 decimals.\\n    uint32 timestamp; // ─╯ Timestamp of the price update.\\n  }\\n\\n  /// @dev Struct to hold the fee \\u0026 validation configs for a destination chain.\\n  struct DestChainConfig {\\n    bool isEnabled; // ─────────────────────────╮ Whether this destination chain is enabled.\\n    uint16 maxNumberOfTokensPerMsg; //          │ Maximum number of distinct ERC20 tokens transferred per message.\\n    uint32 maxDataBytes; //                     │ Maximum data payload size in bytes.\\n    uint32 maxPerMsgGasLimit; //                │ Maximum gas limit for messages targeting EVMs.\\n    uint32 destGasOverhead; //                  │ Gas charged on top of the gasLimit to cover destination chain costs.\\n    uint8 destGasPerPayloadByteBase; //         │ Default dest-chain gas charged each byte of `data` payload.\\n    uint8 destGasPerPayloadByteHigh; //         │ High dest-chain gas charged each byte of `data` payload, used to account for eip-7623.\\n    uint16 destGasPerPayloadByteThreshold; //   │ The value at which the billing switches from destGasPerPayloadByteBase to destGasPerPayloadByteHigh.\\n    uint32 destDataAvailabilityOverheadGas; //  │ Data availability gas charged for overhead costs e.g. for OCR.\\n    uint16 destGasPerDataAvailabilityByte; //   │ Gas units charged per byte of message data that needs availability.\\n    uint16 destDataAvailabilityMultiplierBps; //│ Multiplier for data availability gas, multiples of bps, or 0.0001.\\n    bytes4 chainFamilySelector; //              │ Selector that identifies the destination chain's family. Used to determine the correct validations to perform for the dest chain.\\n    bool enforceOutOfOrder; // ─────────────────╯ Whether to enforce the allowOutOfOrderExecution extraArg value to be true.\\n    // The following three properties are defaults, they can be overridden by setting the TokenTransferFeeConfig for a token.\\n    uint16 defaultTokenFeeUSDCents; // ────╮ Default token fee charged per token transfer.\\n    uint32 defaultTokenDestGasOverhead; // │ Default gas charged to execute a token transfer on the destination chain.\\n    uint32 defaultTxGasLimit; //           │ Default gas limit for a tx.\\n    uint64 gasMultiplierWeiPerEth; //      │ Multiplier for gas costs, 1e18 based so 11e17 = 10% extra cost.\\n    uint32 gasPriceStalenessThreshold; //  │ The amount of time a gas price can be stale before it is considered invalid (0 means disabled).\\n    uint32 networkFeeUSDCents; // ─────────╯ Flat network fee to charge for messages, multiples of 0.01 USD.\\n  }\\n\\n  /// @dev Struct to hold the configs and its destination chain selector. Same as DestChainConfig but with the\\n  /// destChainSelector so that an array of these can be passed in the constructor and applyDestChainConfigUpdates.\\n  /// solhint-disable gas-struct-packing\\n  struct DestChainConfigArgs {\\n    uint64 destChainSelector; // Destination chain selector.\\n    DestChainConfig destChainConfig; // Config to update for the chain selector.\\n  }\\n\\n  /// @dev Struct with transfer fee configuration for token transfers.\\n  struct TokenTransferFeeConfig {\\n    uint32 minFeeUSDCents; // ───╮ Minimum fee to charge per token transfer, multiples of 0.01 USD.\\n    uint32 maxFeeUSDCents; //    │ Maximum fee to charge per token transfer, multiples of 0.01 USD.\\n    uint16 deciBps; //           │ Basis points charged on token transfers, multiples of 0.1bps, or 1e-5.\\n    uint32 destGasOverhead; //   │ Gas charged to execute the token transfer on the destination chain.\\n    //                           │ Data availability bytes that are returned from the source pool and sent to the dest\\n    uint32 destBytesOverhead; // │ pool. Must be \\u003e= Pool.CCIP_LOCK_OR_BURN_V1_RET_BYTES. Set as multiple of 32 bytes.\\n    bool isEnabled; // ──────────╯ Whether this token has custom transfer fees.\\n  }\\n\\n  /// @dev Struct with token transfer fee configurations for a token, same as TokenTransferFeeConfig but with the token\\n  /// address included.\\n  struct TokenTransferFeeConfigSingleTokenArgs {\\n    address token; // Token address.\\n    TokenTransferFeeConfig tokenTransferFeeConfig; // Struct to hold the transfer fee configuration for token transfers.\\n  }\\n\\n  /// @dev Struct with args for setting the token transfer fee configurations for a destination chain and a set of tokens.\\n  struct TokenTransferFeeConfigArgs {\\n    uint64 destChainSelector; // Destination chain selector.\\n    TokenTransferFeeConfigSingleTokenArgs[] tokenTransferFeeConfigs; // Array of token transfer fee configurations.\\n  }\\n\\n  /// @dev Struct with a pair of destination chain selector and token address so that an array of these can be passed in\\n  /// the applyTokenTransferFeeConfigUpdates function to remove the token transfer fee configuration for a token.\\n  struct TokenTransferFeeConfigRemoveArgs {\\n    uint64 destChainSelector; // ─╮ Destination chain selector.\\n    address token; // ────────────╯ Token address.\\n  }\\n\\n  /// @dev Struct with fee token configuration for a token.\\n  struct PremiumMultiplierWeiPerEthArgs {\\n    address token; // // ──────────────────╮ Token address.\\n    uint64 premiumMultiplierWeiPerEth; // ─╯ Multiplier for destination chain specific premiums.\\n  }\\n\\n  /// @dev The base decimals for cost calculations.\\n  uint256 public constant FEE_BASE_DECIMALS = 36;\\n  /// @dev The decimals that Keystone reports prices in.\\n  uint256 public constant KEYSTONE_PRICE_DECIMALS = 18;\\n\\n  string public constant override typeAndVersion = \\\"FeeQuoter 1.6.0\\\";\\n\\n  /// @dev The gas price per unit of gas for a given destination chain, in USD with 18 decimals. Multiple gas prices can\\n  /// be encoded into the same value. Each price takes {Internal.GAS_PRICE_BITS} bits. For example, if Optimism is the\\n  /// destination chain, gas price can include L1 base fee and L2 gas price. Logic to parse the price components is\\n  ///  chain-specific, and should live in OnRamp.\\n  /// @dev Price of 1e18 is 1 USD. Examples:\\n  ///     Very Expensive:   1 unit of gas costs 1 USD                  -\\u003e 1e18.\\n  ///     Expensive:        1 unit of gas costs 0.1 USD                -\\u003e 1e17.\\n  ///     Cheap:            1 unit of gas costs 0.000001 USD           -\\u003e 1e12.\\n  mapping(uint64 destChainSelector =\\u003e Internal.TimestampedPackedUint224 price) private\\n    s_usdPerUnitGasByDestChainSelector;\\n\\n  /// @dev The price, in USD with 18 decimals, per 1e18 of the smallest token denomination.\\n  /// @dev Price of 1e18 represents 1 USD per 1e18 token amount.\\n  ///     1 USDC = 1.00 USD per full token, each full token is 1e6 units -\\u003e 1 * 1e18 * 1e18 / 1e6 = 1e30.\\n  ///     1 ETH = 2,000 USD per full token, each full token is 1e18 units -\\u003e 2000 * 1e18 * 1e18 / 1e18 = 2_000e18.\\n  ///     1 LINK = 5.00 USD per full token, each full token is 1e18 units -\\u003e 5 * 1e18 * 1e18 / 1e18 = 5e18.\\n  mapping(address token =\\u003e Internal.TimestampedPackedUint224 price) private s_usdPerToken;\\n\\n  /// @dev Stores the price data feed configurations per token.\\n  mapping(address token =\\u003e TokenPriceFeedConfig dataFeedAddress) private s_usdPriceFeedsPerToken;\\n\\n  /// @dev The multiplier for destination chain specific premiums that can be set by the owner or fee admin.\\n  mapping(address token =\\u003e uint64 premiumMultiplierWeiPerEth) private s_premiumMultiplierWeiPerEth;\\n\\n  /// @dev The destination chain specific fee configs.\\n  mapping(uint64 destChainSelector =\\u003e DestChainConfig destChainConfig) internal s_destChainConfigs;\\n\\n  /// @dev The token transfer fee config that can be set by the owner or fee admin.\\n  mapping(uint64 destChainSelector =\\u003e mapping(address token =\\u003e TokenTransferFeeConfig tranferFeeConfig)) private\\n    s_tokenTransferFeeConfig;\\n\\n  /// @dev Maximum fee that can be charged for a message. This is a guard to prevent massively overcharging due to\\n  /// misconfiguration.\\n  uint96 internal immutable i_maxFeeJuelsPerMsg;\\n  /// @dev The link token address.\\n  address internal immutable i_linkToken;\\n\\n  /// @dev Subset of tokens which prices tracked by this registry which are fee tokens.\\n  EnumerableSet.AddressSet private s_feeTokens;\\n  /// @dev The amount of time a token price can be stale before it is considered invalid.\\n  uint32 private immutable i_tokenPriceStalenessThreshold;\\n\\n  constructor(\\n    StaticConfig memory staticConfig,\\n    address[] memory priceUpdaters,\\n    address[] memory feeTokens,\\n    TokenPriceFeedUpdate[] memory tokenPriceFeeds,\\n    TokenTransferFeeConfigArgs[] memory tokenTransferFeeConfigArgs,\\n    PremiumMultiplierWeiPerEthArgs[] memory premiumMultiplierWeiPerEthArgs,\\n    DestChainConfigArgs[] memory destChainConfigArgs\\n  ) AuthorizedCallers(priceUpdaters) {\\n    if (\\n      staticConfig.linkToken == address(0) || staticConfig.maxFeeJuelsPerMsg == 0\\n        || staticConfig.tokenPriceStalenessThreshold == 0\\n    ) {\\n      revert InvalidStaticConfig();\\n    }\\n\\n    i_linkToken = staticConfig.linkToken;\\n    i_maxFeeJuelsPerMsg = staticConfig.maxFeeJuelsPerMsg;\\n    i_tokenPriceStalenessThreshold = staticConfig.tokenPriceStalenessThreshold;\\n\\n    _applyFeeTokensUpdates(new address[](0), feeTokens);\\n    _updateTokenPriceFeeds(tokenPriceFeeds);\\n    _applyDestChainConfigUpdates(destChainConfigArgs);\\n    _applyPremiumMultiplierWeiPerEthUpdates(premiumMultiplierWeiPerEthArgs);\\n    _applyTokenTransferFeeConfigUpdates(tokenTransferFeeConfigArgs, new TokenTransferFeeConfigRemoveArgs[](0));\\n  }\\n\\n  // ================================================================\\n  // │                     Price calculations                       │\\n  // ================================================================\\n\\n  /// @inheritdoc IPriceRegistry\\n  function getTokenPrice(\\n    address token\\n  ) public view override returns (Internal.TimestampedPackedUint224 memory) {\\n    Internal.TimestampedPackedUint224 memory tokenPrice = s_usdPerToken[token];\\n\\n    // If the token price is not stale, return it.\\n    if (block.timestamp - tokenPrice.timestamp \\u003c i_tokenPriceStalenessThreshold) {\\n      return tokenPrice;\\n    }\\n\\n    // When we have a stale price we should check if there is a more up to date source. If not, return the stale price.\\n    TokenPriceFeedConfig memory priceFeedConfig = s_usdPriceFeedsPerToken[token];\\n    if (!priceFeedConfig.isEnabled || priceFeedConfig.dataFeedAddress == address(0)) {\\n      return tokenPrice;\\n    }\\n\\n    // If the token price feed is set, retrieve the price from the feed.\\n    Internal.TimestampedPackedUint224 memory feedPrice = _getTokenPriceFromDataFeed(priceFeedConfig);\\n\\n    // We check if the feed price isn't more stale than the stored price. Return the most recent one.\\n    return feedPrice.timestamp \\u003e= tokenPrice.timestamp ? feedPrice : tokenPrice;\\n  }\\n\\n  /// @notice Get the `tokenPrice` for a given token, checks if the price is valid.\\n  /// @param token The token to get the price for.\\n  /// @return tokenPrice The tokenPrice for the given token if it exists and is valid.\\n  function getValidatedTokenPrice(\\n    address token\\n  ) external view returns (uint224) {\\n    return _getValidatedTokenPrice(token);\\n  }\\n\\n  /// @notice Get the `tokenPrice` for an array of tokens.\\n  /// @param tokens The tokens to get prices for.\\n  /// @return tokenPrices The tokenPrices for the given tokens.\\n  function getTokenPrices(\\n    address[] calldata tokens\\n  ) external view returns (Internal.TimestampedPackedUint224[] memory) {\\n    uint256 length = tokens.length;\\n    Internal.TimestampedPackedUint224[] memory tokenPrices = new Internal.TimestampedPackedUint224[](length);\\n    for (uint256 i = 0; i \\u003c length; ++i) {\\n      tokenPrices[i] = getTokenPrice(tokens[i]);\\n    }\\n    return tokenPrices;\\n  }\\n\\n  /// @notice Returns the token price data feed configuration.\\n  /// @param token The token to retrieve the feed config for.\\n  /// @return tokenPriceFeedConfig The token price data feed config (if feed address is 0, the feed config is disabled).\\n  function getTokenPriceFeedConfig(\\n    address token\\n  ) external view returns (TokenPriceFeedConfig memory) {\\n    return s_usdPriceFeedsPerToken[token];\\n  }\\n\\n  /// @notice Get an encoded `gasPrice` for a given destination chain ID.\\n  /// The 224-bit result encodes necessary gas price components.\\n  /// - On L1 chains like Ethereum or Avax, the only component is the gas price.\\n  /// - On Optimistic Rollups, there are two components - the L2 gas price, and L1 base fee for data availability.\\n  /// - On future chains, there could be more or differing price components.\\n  /// PriceRegistry does not contain chain-specific logic to parse destination chain price components.\\n  /// @param destChainSelector The destination chain to get the price for.\\n  /// @return gasPrice The encoded gasPrice for the given destination chain ID.\\n  /// @dev Does not validate if the chain is enabled\\n  function getDestinationChainGasPrice(\\n    uint64 destChainSelector\\n  ) external view returns (Internal.TimestampedPackedUint224 memory) {\\n    return s_usdPerUnitGasByDestChainSelector[destChainSelector];\\n  }\\n\\n  /// @notice Gets the fee token price and the gas price, both denominated in dollars.\\n  /// @param token The source token to get the price for.\\n  /// @param destChainSelector The destination chain to get the gas price for.\\n  /// @return tokenPrice The price of the feeToken in 1e18 dollars per base unit.\\n  /// @return gasPriceValue The price of gas in 1e18 dollars per base unit.\\n  function getTokenAndGasPrices(\\n    address token,\\n    uint64 destChainSelector\\n  ) external view returns (uint224 tokenPrice, uint224 gasPriceValue) {\\n    if (!s_destChainConfigs[destChainSelector].isEnabled) revert DestinationChainNotEnabled(destChainSelector);\\n    return (\\n      _getValidatedTokenPrice(token),\\n      _getValidatedGasPrice(destChainSelector, s_destChainConfigs[destChainSelector].gasPriceStalenessThreshold)\\n    );\\n  }\\n\\n  /// @notice Convert a given token amount to target token amount.\\n  /// @dev this function assumes that no more than 1e59 dollars are sent as payment.\\n  /// If more is sent, the multiplication of feeTokenAmount and feeTokenValue will overflow.\\n  /// Since there isn't even close to 1e59 dollars in the world economy this is safe.\\n  /// @param fromToken The given token address.\\n  /// @param fromTokenAmount The given token amount.\\n  /// @param toToken The target token address.\\n  /// @return toTokenAmount The target token amount.\\n  function convertTokenAmount(\\n    address fromToken,\\n    uint256 fromTokenAmount,\\n    address toToken\\n  ) public view returns (uint256) {\\n    /// Example:\\n    /// fromTokenAmount:   1e18      // 1 ETH\\n    /// ETH:               2_000e18\\n    /// LINK:              5e18\\n    /// return:            1e18 * 2_000e18 / 5e18 = 400e18 (400 LINK)\\n    return (fromTokenAmount * _getValidatedTokenPrice(fromToken)) / _getValidatedTokenPrice(toToken);\\n  }\\n\\n  /// @notice Gets the token price for a given token and reverts if the token is not supported.\\n  /// @param token The address of the token to get the price for.\\n  /// @return tokenPriceValue The token price.\\n  function _getValidatedTokenPrice(\\n    address token\\n  ) internal view returns (uint224) {\\n    Internal.TimestampedPackedUint224 memory tokenPrice = getTokenPrice(token);\\n    // Token price must be set at least once.\\n    if (tokenPrice.timestamp == 0 || tokenPrice.value == 0) revert TokenNotSupported(token);\\n    return tokenPrice.value;\\n  }\\n\\n  /// @notice Gets the token price from a data feed address, rebased to the same units as s_usdPerToken.\\n  /// @param priceFeedConfig token data feed configuration with valid data feed address (used to retrieve price \\u0026 timestamp).\\n  /// @return tokenPrice data feed price answer rebased to s_usdPerToken units, with latest block timestamp.\\n  function _getTokenPriceFromDataFeed(\\n    TokenPriceFeedConfig memory priceFeedConfig\\n  ) internal view returns (Internal.TimestampedPackedUint224 memory tokenPrice) {\\n    AggregatorV3Interface dataFeedContract = AggregatorV3Interface(priceFeedConfig.dataFeedAddress);\\n    (\\n      // uint80 roundID\\n      ,\\n      int256 dataFeedAnswer,\\n      // uint startedAt\\n      ,\\n      uint256 updatedAt,\\n      // uint80 answeredInRound\\n    ) = dataFeedContract.latestRoundData();\\n\\n    if (dataFeedAnswer \\u003c 0) {\\n      revert DataFeedValueOutOfUint224Range();\\n    }\\n    uint224 rebasedValue =\\n      _calculateRebasedValue(dataFeedContract.decimals(), priceFeedConfig.tokenDecimals, uint256(dataFeedAnswer));\\n\\n    // Data feed staleness is unchecked to decouple the FeeQuoter from data feed delay issues.\\n    return Internal.TimestampedPackedUint224({value: rebasedValue, timestamp: uint32(updatedAt)});\\n  }\\n\\n  /// @dev Gets the fee token price and the gas price, both denominated in dollars.\\n  /// @param destChainSelector The destination chain to get the gas price for.\\n  /// @param gasPriceStalenessThreshold The amount of time a gas price can be stale before it is considered invalid.\\n  /// @return gasPriceValue The price of gas in 1e18 dollars per base unit.\\n  function _getValidatedGasPrice(\\n    uint64 destChainSelector,\\n    uint32 gasPriceStalenessThreshold\\n  ) private view returns (uint224 gasPriceValue) {\\n    Internal.TimestampedPackedUint224 memory gasPrice = s_usdPerUnitGasByDestChainSelector[destChainSelector];\\n    // If the staleness threshold is 0, we consider the gas price to be always valid.\\n    if (gasPriceStalenessThreshold != 0) {\\n      // We do allow a gas price of 0, but no stale or unset gas prices.\\n      uint256 timePassed = block.timestamp - gasPrice.timestamp;\\n      if (timePassed \\u003e gasPriceStalenessThreshold) {\\n        revert StaleGasPrice(destChainSelector, gasPriceStalenessThreshold, timePassed);\\n      }\\n    }\\n\\n    return gasPrice.value;\\n  }\\n\\n  // ================================================================\\n  // │                         Fee tokens                           │\\n  // ================================================================\\n\\n  /// @inheritdoc IPriceRegistry\\n  function getFeeTokens() external view returns (address[] memory) {\\n    return s_feeTokens.values();\\n  }\\n\\n  /// @notice Add and remove tokens from feeTokens set.\\n  /// @param feeTokensToRemove The addresses of the tokens which are no longer considered feeTokens.\\n  /// @param feeTokensToAdd The addresses of the tokens which are now considered fee tokens and can be used\\n  /// to calculate fees.\\n  function applyFeeTokensUpdates(\\n    address[] memory feeTokensToRemove,\\n    address[] memory feeTokensToAdd\\n  ) external onlyOwner {\\n    _applyFeeTokensUpdates(feeTokensToRemove, feeTokensToAdd);\\n  }\\n\\n  /// @notice Add and remove tokens from feeTokens set.\\n  /// @param feeTokensToRemove The addresses of the tokens which are no longer considered feeTokens.\\n  /// @param feeTokensToAdd The addresses of the tokens which are now considered fee tokens.\\n  /// and can be used to calculate fees.\\n  function _applyFeeTokensUpdates(address[] memory feeTokensToRemove, address[] memory feeTokensToAdd) private {\\n    for (uint256 i = 0; i \\u003c feeTokensToRemove.length; ++i) {\\n      if (s_feeTokens.remove(feeTokensToRemove[i])) {\\n        emit FeeTokenRemoved(feeTokensToRemove[i]);\\n      }\\n    }\\n    for (uint256 i = 0; i \\u003c feeTokensToAdd.length; ++i) {\\n      if (s_feeTokens.add(feeTokensToAdd[i])) {\\n        emit FeeTokenAdded(feeTokensToAdd[i]);\\n      }\\n    }\\n  }\\n\\n  // ================================================================\\n  // │                       Price updates                          │\\n  // ================================================================\\n\\n  /// @inheritdoc IPriceRegistry\\n  function updatePrices(\\n    Internal.PriceUpdates calldata priceUpdates\\n  ) external override {\\n    // The caller must be a fee updater.\\n    _validateCaller();\\n\\n    uint256 tokenUpdatesLength = priceUpdates.tokenPriceUpdates.length;\\n\\n    for (uint256 i = 0; i \\u003c tokenUpdatesLength; ++i) {\\n      Internal.TokenPriceUpdate memory update = priceUpdates.tokenPriceUpdates[i];\\n      s_usdPerToken[update.sourceToken] =\\n        Internal.TimestampedPackedUint224({value: update.usdPerToken, timestamp: uint32(block.timestamp)});\\n      emit UsdPerTokenUpdated(update.sourceToken, update.usdPerToken, block.timestamp);\\n    }\\n\\n    uint256 gasUpdatesLength = priceUpdates.gasPriceUpdates.length;\\n\\n    for (uint256 i = 0; i \\u003c gasUpdatesLength; ++i) {\\n      Internal.GasPriceUpdate memory update = priceUpdates.gasPriceUpdates[i];\\n      s_usdPerUnitGasByDestChainSelector[update.destChainSelector] =\\n        Internal.TimestampedPackedUint224({value: update.usdPerUnitGas, timestamp: uint32(block.timestamp)});\\n      emit UsdPerUnitGasUpdated(update.destChainSelector, update.usdPerUnitGas, block.timestamp);\\n    }\\n  }\\n\\n  /// @notice Updates the USD token price feeds for given tokens.\\n  /// @param tokenPriceFeedUpdates Token price feed updates to apply.\\n  function updateTokenPriceFeeds(\\n    TokenPriceFeedUpdate[] memory tokenPriceFeedUpdates\\n  ) external onlyOwner {\\n    _updateTokenPriceFeeds(tokenPriceFeedUpdates);\\n  }\\n\\n  /// @notice Updates the USD token price feeds for given tokens.\\n  /// @param tokenPriceFeedUpdates Token price feed updates to apply.\\n  function _updateTokenPriceFeeds(\\n    TokenPriceFeedUpdate[] memory tokenPriceFeedUpdates\\n  ) private {\\n    for (uint256 i; i \\u003c tokenPriceFeedUpdates.length; ++i) {\\n      TokenPriceFeedUpdate memory update = tokenPriceFeedUpdates[i];\\n      address sourceToken = update.sourceToken;\\n      TokenPriceFeedConfig memory tokenPriceFeedConfig = update.feedConfig;\\n\\n      s_usdPriceFeedsPerToken[sourceToken] = tokenPriceFeedConfig;\\n      emit PriceFeedPerTokenUpdated(sourceToken, tokenPriceFeedConfig);\\n    }\\n  }\\n\\n  /// @notice Signals which version of the pool interface is supported\\n  function supportsInterface(\\n    bytes4 interfaceId\\n  ) public pure override returns (bool) {\\n    return interfaceId == type(IReceiver).interfaceId || interfaceId == type(IFeeQuoter).interfaceId\\n      || interfaceId == type(ITypeAndVersion).interfaceId || interfaceId == type(IERC165).interfaceId;\\n  }\\n\\n  /// @inheritdoc IReceiver\\n  /// @notice Handles the report containing price feeds and updates the internal price storage.\\n  /// @dev This function is called to process incoming price feed data.\\n  /// @param metadata Arbitrary metadata associated with the report (not used in this implementation).\\n  /// @param report Encoded report containing an array of `ReceivedCCIPFeedReport` structs.\\n  function onReport(bytes calldata metadata, bytes calldata report) external {\\n    (bytes10 workflowName, address workflowOwner, bytes2 reportName) = metadata._extractMetadataInfo();\\n\\n    _validateReportPermission(msg.sender, workflowOwner, workflowName, reportName);\\n\\n    ReceivedCCIPFeedReport[] memory feeds = abi.decode(report, (ReceivedCCIPFeedReport[]));\\n\\n    for (uint256 i = 0; i \\u003c feeds.length; ++i) {\\n      TokenPriceFeedConfig memory feedConfig = s_usdPriceFeedsPerToken[feeds[i].token];\\n\\n      // If the token is not enabled we revert the entire report as that indicates some type of misconfiguration.\\n      if (!feedConfig.isEnabled) {\\n        revert TokenNotSupported(feeds[i].token);\\n      }\\n      // Keystone reports prices in USD with 18 decimals, so we passing it as 18 in the _calculateRebasedValue function.\\n      uint224 rebasedValue =\\n        _calculateRebasedValue(uint8(KEYSTONE_PRICE_DECIMALS), feedConfig.tokenDecimals, feeds[i].price);\\n\\n      // If the feed timestamp is older than the current stored price, skip the update.\\n      // We do not revert Keystone price feeds deliberately.\\n      if (feeds[i].timestamp \\u003c s_usdPerToken[feeds[i].token].timestamp) {\\n        continue;\\n      }\\n\\n      // Update the token price with the new value and timestamp.\\n      s_usdPerToken[feeds[i].token] =\\n        Internal.TimestampedPackedUint224({value: rebasedValue, timestamp: feeds[i].timestamp});\\n      emit UsdPerTokenUpdated(feeds[i].token, rebasedValue, feeds[i].timestamp);\\n    }\\n  }\\n\\n  // ================================================================\\n  // │                       Fee quoting                            │\\n  // ================================================================\\n\\n  /// @inheritdoc IFeeQuoter\\n  /// @dev The function should always validate message.extraArgs, message.receiver and family-specific configs.\\n  function getValidatedFee(\\n    uint64 destChainSelector,\\n    Client.EVM2AnyMessage calldata message\\n  ) external view returns (uint256 feeTokenAmount) {\\n    DestChainConfig memory destChainConfig = s_destChainConfigs[destChainSelector];\\n    if (!destChainConfig.isEnabled) revert DestinationChainNotEnabled(destChainSelector);\\n    if (!s_feeTokens.contains(message.feeToken)) revert FeeTokenNotSupported(message.feeToken);\\n\\n    uint256 numberOfTokens = message.tokenAmounts.length;\\n    uint256 gasLimit = _validateMessageAndResolveGasLimitForDestination(destChainSelector, destChainConfig, message);\\n\\n    // The below call asserts that feeToken is a supported token.\\n    uint224 feeTokenPrice = _getValidatedTokenPrice(message.feeToken);\\n    uint224 packedGasPrice = _getValidatedGasPrice(destChainSelector, destChainConfig.gasPriceStalenessThreshold);\\n\\n    // Calculate premiumFee in USD with 18 decimals precision first.\\n    // If message-only and no token transfers, a flat network fee is charged.\\n    // If there are token transfers, premiumFee is calculated from token transfer fee.\\n    // If there are both token transfers and message, premiumFee is only calculated from token transfer fee.\\n    uint256 premiumFeeUSDWei = 0;\\n    uint32 tokenTransferGas = 0;\\n    uint32 tokenTransferBytesOverhead = 0;\\n    if (numberOfTokens \\u003e 0) {\\n      (premiumFeeUSDWei, tokenTransferGas, tokenTransferBytesOverhead) = _getTokenTransferCost(\\n        destChainConfig.defaultTokenFeeUSDCents,\\n        destChainConfig.defaultTokenDestGasOverhead,\\n        destChainSelector,\\n        message.feeToken,\\n        feeTokenPrice,\\n        message.tokenAmounts\\n      );\\n    } else {\\n      // Convert USD cents with 2 decimals to 18 decimals.\\n      premiumFeeUSDWei = uint256(destChainConfig.networkFeeUSDCents) * 1e16;\\n    }\\n    // Apply the premium multiplier for the fee token, making it 36 decimals\\n    premiumFeeUSDWei *= s_premiumMultiplierWeiPerEth[message.feeToken];\\n\\n    // Calculate data availability cost in USD with 36 decimals. Data availability cost exists on rollups that need to\\n    // post transaction calldata onto another storage layer, e.g. Eth mainnet, incurring additional storage gas costs.\\n    uint256 dataAvailabilityCostUSD36Decimals = 0;\\n\\n    // Only calculate data availability cost if data availability multiplier is non-zero.\\n    // The multiplier should be set to 0 if destination chain does not charge data availability cost.\\n    if (destChainConfig.destDataAvailabilityMultiplierBps \\u003e 0) {\\n      dataAvailabilityCostUSD36Decimals = _getDataAvailabilityCost(\\n        destChainConfig,\\n        // Parse the data availability gas price stored in the higher-order 112 bits of the encoded gas price.\\n        uint112(packedGasPrice \\u003e\\u003e Internal.GAS_PRICE_BITS),\\n        message.data.length,\\n        numberOfTokens,\\n        tokenTransferBytesOverhead\\n      );\\n    }\\n\\n    // Calculate the calldata, taking into account EIP-7623. We charge destGasPerPayloadByteBase for the calldata cost\\n    // up to destGasPerPayloadByteThreshold, even when the total calldata length exceeds the threshold. This is safe\\n    // because we also charge for execution gas on top of this. When correct values are chosen, the execution gas we\\n    // charge is always higher than the difference between the base and high calldata costs for the first\\n    // destGasPerPayloadByteThreshold bytes. Since we don't pay for execution gas in EIP-7623, this execution gas is\\n    // effectively used to cover the higher calldata costs for the first destGasPerPayloadByteThreshold bytes.\\n    // The threshold should be adjusted based on expected execution cost and, potentially, to discourage large payloads.\\n    // Example: 16 base, 40 high, 100k execution cost. 100k/(40-16) = max 4.16kb as the threshold. Take 4kb threshold.\\n    // Calldata length = 5000\\n    // Our calculations: 1000 * 40 + 4000 * 16 = 104k calldata cost + 100k execution cost = 204k calculated cost.\\n    // Actual cost: 5000 * 40 = 200k\\n    // The difference is 4k in favour of CCIP. The lower the threshold, the more premium is charged for large payloads.\\n    uint256 calldataLength = message.data.length + tokenTransferBytesOverhead;\\n    uint256 destCallDataCost = calldataLength * destChainConfig.destGasPerPayloadByteBase;\\n    if (calldataLength \\u003e destChainConfig.destGasPerPayloadByteThreshold) {\\n      destCallDataCost = destChainConfig.destGasPerPayloadByteBase * destChainConfig.destGasPerPayloadByteThreshold\\n        + (calldataLength - destChainConfig.destGasPerPayloadByteThreshold) * destChainConfig.destGasPerPayloadByteHigh;\\n    }\\n\\n    // We add the destination chain CCIP overhead (commit, exec), the token transfer gas, the calldata cost and the msg\\n    // gas limit to get the total gas the tx costs to execute on the destination chain.\\n    uint256 totalDestChainGas = destChainConfig.destGasOverhead + tokenTransferGas + destCallDataCost + gasLimit;\\n\\n    // Total USD fee is in 36 decimals, feeTokenPrice is in 18 decimals USD for 1e18 smallest token denominations.\\n    // The result is the fee in the feeTokens smallest denominations (e.g. wei for ETH).\\n    // uint112(packedGasPrice) = executionGasPrice\\n    return (\\n      totalDestChainGas * uint112(packedGasPrice) * destChainConfig.gasMultiplierWeiPerEth + premiumFeeUSDWei\\n        + dataAvailabilityCostUSD36Decimals\\n    ) / feeTokenPrice;\\n  }\\n\\n  /// @notice Sets the fee configuration for a token.\\n  /// @param premiumMultiplierWeiPerEthArgs Array of PremiumMultiplierWeiPerEthArgs structs.\\n  function applyPremiumMultiplierWeiPerEthUpdates(\\n    PremiumMultiplierWeiPerEthArgs[] memory premiumMultiplierWeiPerEthArgs\\n  ) external onlyOwner {\\n    _applyPremiumMultiplierWeiPerEthUpdates(premiumMultiplierWeiPerEthArgs);\\n  }\\n\\n  /// @dev Sets the fee config.\\n  /// @param premiumMultiplierWeiPerEthArgs The multiplier for destination chain specific premiums.\\n  function _applyPremiumMultiplierWeiPerEthUpdates(\\n    PremiumMultiplierWeiPerEthArgs[] memory premiumMultiplierWeiPerEthArgs\\n  ) internal {\\n    for (uint256 i = 0; i \\u003c premiumMultiplierWeiPerEthArgs.length; ++i) {\\n      address token = premiumMultiplierWeiPerEthArgs[i].token;\\n      uint64 premiumMultiplierWeiPerEth = premiumMultiplierWeiPerEthArgs[i].premiumMultiplierWeiPerEth;\\n      s_premiumMultiplierWeiPerEth[token] = premiumMultiplierWeiPerEth;\\n\\n      emit PremiumMultiplierWeiPerEthUpdated(token, premiumMultiplierWeiPerEth);\\n    }\\n  }\\n\\n  /// @notice Gets the fee configuration for a token.\\n  /// @param token The token to get the fee configuration for.\\n  /// @return premiumMultiplierWeiPerEth The multiplier for destination chain specific premiums.\\n  function getPremiumMultiplierWeiPerEth(\\n    address token\\n  ) external view returns (uint64 premiumMultiplierWeiPerEth) {\\n    return s_premiumMultiplierWeiPerEth[token];\\n  }\\n\\n  /// @notice Returns the token transfer cost parameters.\\n  /// A basis point fee is calculated from the USD value of each token transfer.\\n  /// For each individual transfer, this fee is between [minFeeUSD, maxFeeUSD].\\n  /// Total transfer fee is the sum of each individual token transfer fee.\\n  /// @dev Assumes that tokenAmounts are validated to be listed tokens elsewhere.\\n  /// @dev Splitting one token transfer into multiple transfers is discouraged, as it will result in a transferFee\\n  /// equal or greater than the same amount aggregated/de-duped.\\n  /// @param defaultTokenFeeUSDCents the default token fee in USD cents.\\n  /// @param defaultTokenDestGasOverhead the default token destination gas overhead.\\n  /// @param destChainSelector the destination chain selector.\\n  /// @param feeToken address of the feeToken.\\n  /// @param feeTokenPrice price of feeToken in USD with 18 decimals.\\n  /// @param tokenAmounts token transfers in the message.\\n  /// @return tokenTransferFeeUSDWei total token transfer bps fee in USD with 18 decimals.\\n  /// @return tokenTransferGas total execution gas of the token transfers.\\n  /// @return tokenTransferBytesOverhead additional token transfer data passed to destination, e.g. USDC attestation.\\n  function _getTokenTransferCost(\\n    uint256 defaultTokenFeeUSDCents,\\n    uint32 defaultTokenDestGasOverhead,\\n    uint64 destChainSelector,\\n    address feeToken,\\n    uint224 feeTokenPrice,\\n    Client.EVMTokenAmount[] calldata tokenAmounts\\n  ) internal view returns (uint256 tokenTransferFeeUSDWei, uint32 tokenTransferGas, uint32 tokenTransferBytesOverhead) {\\n    uint256 numberOfTokens = tokenAmounts.length;\\n\\n    for (uint256 i = 0; i \\u003c numberOfTokens; ++i) {\\n      Client.EVMTokenAmount memory tokenAmount = tokenAmounts[i];\\n      TokenTransferFeeConfig memory transferFeeConfig = s_tokenTransferFeeConfig[destChainSelector][tokenAmount.token];\\n\\n      // If the token has no specific overrides configured, we use the global defaults.\\n      if (!transferFeeConfig.isEnabled) {\\n        tokenTransferFeeUSDWei += defaultTokenFeeUSDCents * 1e16;\\n        tokenTransferGas += defaultTokenDestGasOverhead;\\n        tokenTransferBytesOverhead += Pool.CCIP_LOCK_OR_BURN_V1_RET_BYTES;\\n        continue;\\n      }\\n\\n      uint256 bpsFeeUSDWei = 0;\\n      // Only calculate bps fee if ratio is greater than 0. Ratio of 0 means no bps fee for a token.\\n      // Useful for when the FeeQuoter cannot return a valid price for the token.\\n      if (transferFeeConfig.deciBps \\u003e 0) {\\n        uint224 tokenPrice = 0;\\n        if (tokenAmount.token != feeToken) {\\n          tokenPrice = _getValidatedTokenPrice(tokenAmount.token);\\n        } else {\\n          tokenPrice = feeTokenPrice;\\n        }\\n\\n        // Calculate token transfer value, then apply fee ratio.\\n        // ratio represents multiples of 0.1bps, or 1e-5.\\n        bpsFeeUSDWei = (tokenPrice._calcUSDValueFromTokenAmount(tokenAmount.amount) * transferFeeConfig.deciBps) / 1e5;\\n      }\\n\\n      tokenTransferGas += transferFeeConfig.destGasOverhead;\\n      tokenTransferBytesOverhead += transferFeeConfig.destBytesOverhead;\\n\\n      // Bps fees should be kept within range of [minFeeUSD, maxFeeUSD].\\n      // Convert USD values with 2 decimals to 18 decimals.\\n      uint256 minFeeUSDWei = uint256(transferFeeConfig.minFeeUSDCents) * 1e16;\\n      if (bpsFeeUSDWei \\u003c minFeeUSDWei) {\\n        tokenTransferFeeUSDWei += minFeeUSDWei;\\n        continue;\\n      }\\n\\n      uint256 maxFeeUSDWei = uint256(transferFeeConfig.maxFeeUSDCents) * 1e16;\\n      if (bpsFeeUSDWei \\u003e maxFeeUSDWei) {\\n        tokenTransferFeeUSDWei += maxFeeUSDWei;\\n        continue;\\n      }\\n\\n      // In the case where bpsFeeUSDWei, minFeeUSDWei, and maxFeeUSDWei are all 0, we skip the fee. This is intended\\n      // to allow for a fee of 0 to be set.\\n      tokenTransferFeeUSDWei += bpsFeeUSDWei;\\n    }\\n\\n    return (tokenTransferFeeUSDWei, tokenTransferGas, tokenTransferBytesOverhead);\\n  }\\n\\n  /// @notice calculates the rebased value for 1e18 smallest token denomination.\\n  /// @param dataFeedDecimal decimal of the data feed.\\n  /// @param tokenDecimal decimal of the token.\\n  /// @param feedValue value of the data feed.\\n  /// @return rebasedValue rebased value.\\n  function _calculateRebasedValue(\\n    uint8 dataFeedDecimal,\\n    uint8 tokenDecimal,\\n    uint256 feedValue\\n  ) internal pure returns (uint224 rebasedValue) {\\n    // Rebase formula for units in smallest token denomination: usdValue * (1e18 * 1e18) / 1eTokenDecimals.\\n    // feedValue * (10 ** (18 - feedDecimals)) * (10 ** (18 - erc20Decimals))\\n    // feedValue * (10 ** ((18 - feedDecimals) + (18 - erc20Decimals)))\\n    // feedValue * (10 ** (36 - feedDecimals - erc20Decimals))\\n    // feedValue * (10 ** (36 - (feedDecimals + erc20Decimals)))\\n    // feedValue * (10 ** (36 - excessDecimals))\\n    // If excessDecimals \\u003e 36 =\\u003e flip it to feedValue / (10 ** (excessDecimals - 36)).\\n    uint8 excessDecimals = dataFeedDecimal + tokenDecimal;\\n    uint256 rebasedVal;\\n\\n    if (excessDecimals \\u003e FEE_BASE_DECIMALS) {\\n      rebasedVal = feedValue / (10 ** (excessDecimals - FEE_BASE_DECIMALS));\\n    } else {\\n      rebasedVal = feedValue * (10 ** (FEE_BASE_DECIMALS - excessDecimals));\\n    }\\n\\n    if (rebasedVal \\u003e type(uint224).max) {\\n      revert DataFeedValueOutOfUint224Range();\\n    }\\n\\n    return uint224(rebasedVal);\\n  }\\n\\n  /// @notice Returns the estimated data availability cost of the message.\\n  /// @dev To save on gas, we use a single destGasPerDataAvailabilityByte value for both zero and non-zero bytes.\\n  /// @param destChainConfig the config configured for the destination chain selector.\\n  /// @param dataAvailabilityGasPrice USD per data availability gas in 18 decimals.\\n  /// @param messageDataLength length of the data field in the message.\\n  /// @param numberOfTokens number of distinct token transfers in the message.\\n  /// @param tokenTransferBytesOverhead additional token transfer data passed to destination, e.g. USDC attestation.\\n  /// @return dataAvailabilityCostUSD36Decimal total data availability cost in USD with 36 decimals.\\n  function _getDataAvailabilityCost(\\n    DestChainConfig memory destChainConfig,\\n    uint112 dataAvailabilityGasPrice,\\n    uint256 messageDataLength,\\n    uint256 numberOfTokens,\\n    uint32 tokenTransferBytesOverhead\\n  ) internal pure returns (uint256 dataAvailabilityCostUSD36Decimal) {\\n    // dataAvailabilityLengthBytes sums up byte lengths of fixed message fields and dynamic message fields.\\n    // Fixed message fields do account for the offset and length slot of the dynamic fields.\\n    uint256 dataAvailabilityLengthBytes = Internal.MESSAGE_FIXED_BYTES + messageDataLength\\n      + (numberOfTokens * Internal.MESSAGE_FIXED_BYTES_PER_TOKEN) + tokenTransferBytesOverhead;\\n\\n    // destDataAvailabilityOverheadGas is a separate config value for flexibility to be updated independently of message\\n    // cost. Its value is determined by CCIP lane implementation, e.g. the overhead data posted for OCR.\\n    uint256 dataAvailabilityGas = (dataAvailabilityLengthBytes * destChainConfig.destGasPerDataAvailabilityByte)\\n      + destChainConfig.destDataAvailabilityOverheadGas;\\n\\n    // dataAvailabilityGasPrice is in 18 decimals, destDataAvailabilityMultiplierBps is in 4 decimals.\\n    // We pad 14 decimals to bring the result to 36 decimals, in line with token bps and execution fee.\\n    return ((dataAvailabilityGas * dataAvailabilityGasPrice) * destChainConfig.destDataAvailabilityMultiplierBps) * 1e14;\\n  }\\n\\n  /// @notice Gets the transfer fee config for a given token.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param token The token address.\\n  /// @return tokenTransferFeeConfig The transfer fee config for the token.\\n  function getTokenTransferFeeConfig(\\n    uint64 destChainSelector,\\n    address token\\n  ) external view returns (TokenTransferFeeConfig memory tokenTransferFeeConfig) {\\n    return s_tokenTransferFeeConfig[destChainSelector][token];\\n  }\\n\\n  /// @notice Sets the transfer fee config.\\n  /// @dev only callable by the owner or admin.\\n  function applyTokenTransferFeeConfigUpdates(\\n    TokenTransferFeeConfigArgs[] memory tokenTransferFeeConfigArgs,\\n    TokenTransferFeeConfigRemoveArgs[] memory tokensToUseDefaultFeeConfigs\\n  ) external onlyOwner {\\n    _applyTokenTransferFeeConfigUpdates(tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs);\\n  }\\n\\n  /// @notice internal helper to set the token transfer fee config.\\n  function _applyTokenTransferFeeConfigUpdates(\\n    TokenTransferFeeConfigArgs[] memory tokenTransferFeeConfigArgs,\\n    TokenTransferFeeConfigRemoveArgs[] memory tokensToUseDefaultFeeConfigs\\n  ) internal {\\n    for (uint256 i = 0; i \\u003c tokenTransferFeeConfigArgs.length; ++i) {\\n      TokenTransferFeeConfigArgs memory tokenTransferFeeConfigArg = tokenTransferFeeConfigArgs[i];\\n      uint64 destChainSelector = tokenTransferFeeConfigArg.destChainSelector;\\n\\n      for (uint256 j = 0; j \\u003c tokenTransferFeeConfigArg.tokenTransferFeeConfigs.length; ++j) {\\n        TokenTransferFeeConfig memory tokenTransferFeeConfig =\\n          tokenTransferFeeConfigArg.tokenTransferFeeConfigs[j].tokenTransferFeeConfig;\\n        address token = tokenTransferFeeConfigArg.tokenTransferFeeConfigs[j].token;\\n\\n        if (tokenTransferFeeConfig.minFeeUSDCents \\u003e= tokenTransferFeeConfig.maxFeeUSDCents) {\\n          revert InvalidFeeRange(tokenTransferFeeConfig.minFeeUSDCents, tokenTransferFeeConfig.maxFeeUSDCents);\\n        }\\n\\n        if (tokenTransferFeeConfig.destBytesOverhead \\u003c Pool.CCIP_LOCK_OR_BURN_V1_RET_BYTES) {\\n          revert InvalidDestBytesOverhead(token, tokenTransferFeeConfig.destBytesOverhead);\\n        }\\n\\n        s_tokenTransferFeeConfig[destChainSelector][token] = tokenTransferFeeConfig;\\n\\n        emit TokenTransferFeeConfigUpdated(destChainSelector, token, tokenTransferFeeConfig);\\n      }\\n    }\\n\\n    // Remove the custom fee configs for the tokens that are in the tokensToUseDefaultFeeConfigs array.\\n    for (uint256 i = 0; i \\u003c tokensToUseDefaultFeeConfigs.length; ++i) {\\n      uint64 destChainSelector = tokensToUseDefaultFeeConfigs[i].destChainSelector;\\n      address token = tokensToUseDefaultFeeConfigs[i].token;\\n      delete s_tokenTransferFeeConfig[destChainSelector][token];\\n      emit TokenTransferFeeConfigDeleted(destChainSelector, token);\\n    }\\n  }\\n\\n  // ================================================================\\n  // │             Validations \\u0026 message processing                 │\\n  // ================================================================\\n\\n  /// @notice Validates that the destAddress matches the expected format of the family.\\n  /// @param chainFamilySelector Tag to identify the target family.\\n  /// @param destAddress Dest address to validate.\\n  /// @dev precondition - assumes the family tag is correct and validated.\\n  function _validateDestFamilyAddress(\\n    bytes4 chainFamilySelector,\\n    bytes memory destAddress,\\n    uint256 gasLimit\\n  ) internal pure {\\n    if (chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_EVM) {\\n      return Internal._validateEVMAddress(destAddress);\\n    }\\n    if (chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_SVM) {\\n      // SVM addresses don't have a precompile space at the first X addresses, instead we validate that if the gasLimit\\n      // is non-zero, the address must not be 0x0.\\n      return Internal._validate32ByteAddress(destAddress, gasLimit \\u003e 0 ? 1 : 0);\\n    }\\n    if (\\n      chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_APTOS\\n        || chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_SUI\\n    ) {\\n      return Internal._validate32ByteAddress(destAddress, Internal.APTOS_PRECOMPILE_SPACE);\\n    }\\n    revert InvalidChainFamilySelector(chainFamilySelector);\\n  }\\n\\n  /// @notice Parse and validate the SVM specific Extra Args Bytes.\\n  function _parseSVMExtraArgsFromBytes(\\n    bytes calldata extraArgs,\\n    uint256 maxPerMsgGasLimit,\\n    bool enforceOutOfOrder\\n  ) internal pure returns (Client.SVMExtraArgsV1 memory svmExtraArgs) {\\n    if (extraArgs.length == 0) {\\n      revert InvalidExtraArgsData();\\n    }\\n\\n    bytes4 tag = bytes4(extraArgs[:4]);\\n    if (tag != Client.SVM_EXTRA_ARGS_V1_TAG) {\\n      revert InvalidExtraArgsTag();\\n    }\\n\\n    svmExtraArgs = abi.decode(extraArgs[4:], (Client.SVMExtraArgsV1));\\n\\n    if (enforceOutOfOrder \\u0026\\u0026 !svmExtraArgs.allowOutOfOrderExecution) {\\n      revert ExtraArgOutOfOrderExecutionMustBeTrue();\\n    }\\n\\n    if (svmExtraArgs.computeUnits \\u003e maxPerMsgGasLimit) {\\n      revert MessageComputeUnitLimitTooHigh();\\n    }\\n\\n    return svmExtraArgs;\\n  }\\n\\n  /// @dev Convert the extra args bytes into a struct with validations against the dest chain config.\\n  /// @param extraArgs The extra args bytes.\\n  /// @return genericExtraArgs The GenericExtraArgs struct.\\n  function _parseGenericExtraArgsFromBytes(\\n    bytes calldata extraArgs,\\n    uint32 defaultTxGasLimit,\\n    uint256 maxPerMsgGasLimit,\\n    bool enforceOutOfOrder\\n  ) internal pure returns (Client.GenericExtraArgsV2 memory) {\\n    // Since GenericExtraArgs are simply a superset of EVMExtraArgsV1, we can parse them as such. For Aptos, this\\n    // technically means EVMExtraArgsV1 are processed like they would be valid, but they will always fail on the\\n    // allowedOutOfOrderExecution check below.\\n    Client.GenericExtraArgsV2 memory parsedExtraArgs =\\n      _parseUnvalidatedEVMExtraArgsFromBytes(extraArgs, defaultTxGasLimit);\\n\\n    if (parsedExtraArgs.gasLimit \\u003e maxPerMsgGasLimit) revert MessageGasLimitTooHigh();\\n\\n    // If the chain enforces out of order execution, the extra args must allow it, otherwise revert. We cannot assume\\n    // the user intended to use OOO on any chain that requires it as it may lead to unexpected behavior. Therefore we\\n    // revert instead of assuming the user intended to use OOO.\\n    if (enforceOutOfOrder \\u0026\\u0026 !parsedExtraArgs.allowOutOfOrderExecution) {\\n      revert ExtraArgOutOfOrderExecutionMustBeTrue();\\n    }\\n\\n    return parsedExtraArgs;\\n  }\\n\\n  /// @dev Convert the extra args bytes into a struct.\\n  /// @param extraArgs The extra args bytes.\\n  /// @param defaultTxGasLimit default tx gas limit to use in the absence of extra args.\\n  /// @return EVMExtraArgsV2 the extra args struct populated with either the given args or default values.\\n  function _parseUnvalidatedEVMExtraArgsFromBytes(\\n    bytes calldata extraArgs,\\n    uint64 defaultTxGasLimit\\n  ) private pure returns (Client.GenericExtraArgsV2 memory) {\\n    if (extraArgs.length == 0) {\\n      // If extra args are empty, generate default values.\\n      return Client.GenericExtraArgsV2({gasLimit: defaultTxGasLimit, allowOutOfOrderExecution: false});\\n    }\\n\\n    bytes4 extraArgsTag = bytes4(extraArgs);\\n    bytes memory argsData = extraArgs[4:];\\n\\n    if (extraArgsTag == Client.GENERIC_EXTRA_ARGS_V2_TAG) {\\n      return abi.decode(argsData, (Client.GenericExtraArgsV2));\\n    } else if (extraArgsTag == Client.EVM_EXTRA_ARGS_V1_TAG) {\\n      // EVMExtraArgsV1 originally included a second boolean (strict) field which has been deprecated.\\n      // Clients may still include it but it will be ignored.\\n      return Client.GenericExtraArgsV2({gasLimit: abi.decode(argsData, (uint256)), allowOutOfOrderExecution: false});\\n    }\\n    revert InvalidExtraArgsTag();\\n  }\\n\\n  /// @notice Validate the forwarded message to ensure it matches the configuration limits (message length, number of\\n  /// tokens) and family-specific expectations (address format).\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param destChainConfig The destination chain config.\\n  /// @param message The message to validate.\\n  /// @return gasLimit The gas limit to use for the message.\\n  function _validateMessageAndResolveGasLimitForDestination(\\n    uint64 destChainSelector,\\n    DestChainConfig memory destChainConfig,\\n    Client.EVM2AnyMessage calldata message\\n  ) internal view returns (uint256 gasLimit) {\\n    uint256 dataLength = message.data.length;\\n    uint256 numberOfTokens = message.tokenAmounts.length;\\n\\n    // Check that payload is formed correctly.\\n    if (dataLength \\u003e uint256(destChainConfig.maxDataBytes)) {\\n      revert MessageTooLarge(uint256(destChainConfig.maxDataBytes), dataLength);\\n    }\\n    if (numberOfTokens \\u003e uint256(destChainConfig.maxNumberOfTokensPerMsg)) {\\n      revert UnsupportedNumberOfTokens(numberOfTokens, destChainConfig.maxNumberOfTokensPerMsg);\\n    }\\n\\n    // resolve gas limit and validate chainFamilySelector\\n    if (\\n      destChainConfig.chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_EVM\\n        || destChainConfig.chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_APTOS\\n        || destChainConfig.chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_SUI\\n    ) {\\n      gasLimit = _parseGenericExtraArgsFromBytes(\\n        message.extraArgs,\\n        destChainConfig.defaultTxGasLimit,\\n        destChainConfig.maxPerMsgGasLimit,\\n        destChainConfig.enforceOutOfOrder\\n      ).gasLimit;\\n\\n      _validateDestFamilyAddress(destChainConfig.chainFamilySelector, message.receiver, gasLimit);\\n    } else if (destChainConfig.chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_SVM) {\\n      Client.SVMExtraArgsV1 memory svmExtraArgsV1 = _parseSVMExtraArgsFromBytes(\\n        message.extraArgs, destChainConfig.maxPerMsgGasLimit, destChainConfig.enforceOutOfOrder\\n      );\\n\\n      gasLimit = svmExtraArgsV1.computeUnits;\\n\\n      _validateDestFamilyAddress(destChainConfig.chainFamilySelector, message.receiver, gasLimit);\\n\\n      uint256 accountsLength = svmExtraArgsV1.accounts.length;\\n      // The max payload size for SVM is heavily dependent on the accounts passed into extra args and the number of\\n      // tokens. Below, token and account overhead will count towards maxDataBytes.\\n      uint256 svmExpandedDataLength = dataLength;\\n\\n      // This abi.decode is safe because the address is validated above.\\n      if (abi.decode(message.receiver, (uint256)) == 0) {\\n        // When message receiver is zero, CCIP receiver is not invoked on SVM.\\n        // There should not be additional accounts specified for the receiver.\\n        if (accountsLength \\u003e 0) {\\n          revert TooManySVMExtraArgsAccounts(accountsLength, 0);\\n        }\\n      } else {\\n        // The messaging accounts needed for CCIP receiver on SVM are:\\n        // message receiver, offramp PDA signer,\\n        // plus remaining accounts specified in SVM extraArgs. Each account is 32 bytes.\\n        svmExpandedDataLength +=\\n          ((accountsLength + Client.SVM_MESSAGING_ACCOUNTS_OVERHEAD) * Client.SVM_ACCOUNT_BYTE_SIZE);\\n      }\\n\\n      if (numberOfTokens \\u003e 0 \\u0026\\u0026 svmExtraArgsV1.tokenReceiver == bytes32(0)) {\\n        revert InvalidTokenReceiver();\\n      }\\n      if (accountsLength \\u003e Client.SVM_EXTRA_ARGS_MAX_ACCOUNTS) {\\n        revert TooManySVMExtraArgsAccounts(accountsLength, Client.SVM_EXTRA_ARGS_MAX_ACCOUNTS);\\n      }\\n      if (svmExtraArgsV1.accountIsWritableBitmap \\u003e\\u003e accountsLength != 0) {\\n        revert InvalidSVMExtraArgsWritableBitmap(svmExtraArgsV1.accountIsWritableBitmap, accountsLength);\\n      }\\n\\n      svmExpandedDataLength += (numberOfTokens * Client.SVM_TOKEN_TRANSFER_DATA_OVERHEAD);\\n\\n      // The token destBytesOverhead can be very different per token so we have to take it into account as well.\\n      for (uint256 i = 0; i \\u003c numberOfTokens; ++i) {\\n        uint256 destBytesOverhead =\\n          s_tokenTransferFeeConfig[destChainSelector][message.tokenAmounts[i].token].destBytesOverhead;\\n\\n        // Pools get Pool.CCIP_LOCK_OR_BURN_V1_RET_BYTES by default, but if an override is set we use that instead.\\n        if (destBytesOverhead \\u003e 0) {\\n          svmExpandedDataLength += destBytesOverhead;\\n        } else {\\n          svmExpandedDataLength += Pool.CCIP_LOCK_OR_BURN_V1_RET_BYTES;\\n        }\\n      }\\n\\n      if (svmExpandedDataLength \\u003e uint256(destChainConfig.maxDataBytes)) {\\n        revert MessageTooLarge(uint256(destChainConfig.maxDataBytes), svmExpandedDataLength);\\n      }\\n    } else {\\n      revert InvalidChainFamilySelector(destChainConfig.chainFamilySelector);\\n    }\\n\\n    return gasLimit;\\n  }\\n\\n  /// @inheritdoc IFeeQuoter\\n  /// @dev precondition - onRampTokenTransfers and sourceTokenAmounts lengths must be equal.\\n  function processMessageArgs(\\n    uint64 destChainSelector,\\n    address feeToken,\\n    uint256 feeTokenAmount,\\n    bytes calldata extraArgs,\\n    bytes calldata messageReceiver\\n  )\\n    external\\n    view\\n    returns (\\n      uint256 msgFeeJuels,\\n      bool isOutOfOrderExecution,\\n      bytes memory convertedExtraArgs,\\n      bytes memory tokenReceiver\\n    )\\n  {\\n    // Convert feeToken to link if not already in link.\\n    if (feeToken == i_linkToken) {\\n      msgFeeJuels = feeTokenAmount;\\n    } else {\\n      msgFeeJuels = convertTokenAmount(feeToken, feeTokenAmount, i_linkToken);\\n    }\\n\\n    if (msgFeeJuels \\u003e i_maxFeeJuelsPerMsg) revert MessageFeeTooHigh(msgFeeJuels, i_maxFeeJuelsPerMsg);\\n\\n    (convertedExtraArgs, isOutOfOrderExecution, tokenReceiver) =\\n      _processChainFamilySelector(destChainSelector, messageReceiver, extraArgs);\\n\\n    return (msgFeeJuels, isOutOfOrderExecution, convertedExtraArgs, tokenReceiver);\\n  }\\n\\n  /// @notice Parses the extra Args based on the chain family selector. Isolated into a separate function\\n  /// as it was the only way to prevent a stack too deep error, and makes future chain family additions easier.\\n  // solhint-disable-next-line chainlink-solidity/explicit-returns\\n  function _processChainFamilySelector(\\n    uint64 destChainSelector,\\n    bytes calldata messageReceiver,\\n    bytes calldata extraArgs\\n  ) internal view returns (bytes memory validatedExtraArgs, bool allowOutOfOrderExecution, bytes memory tokenReceiver) {\\n    // Since this function is called after getFee, which already validates the params, no validation is necessary.\\n    DestChainConfig memory destChainConfig = s_destChainConfigs[destChainSelector];\\n    // EVM and Aptos both use the same GenericExtraArgs, with EVM also supporting EVMExtraArgsV1 which is handled inside\\n    // the generic function.\\n    if (\\n      destChainConfig.chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_EVM\\n        || destChainConfig.chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_APTOS\\n        || destChainConfig.chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_SUI\\n    ) {\\n      Client.GenericExtraArgsV2 memory parsedExtraArgs =\\n        _parseUnvalidatedEVMExtraArgsFromBytes(extraArgs, destChainConfig.defaultTxGasLimit);\\n\\n      return (Client._argsToBytes(parsedExtraArgs), parsedExtraArgs.allowOutOfOrderExecution, messageReceiver);\\n    }\\n    if (destChainConfig.chainFamilySelector == Internal.CHAIN_FAMILY_SELECTOR_SVM) {\\n      // If extraArgs passes the parsing it's valid and can be returned unchanged.\\n      // ExtraArgs are required on SVM, meaning the supplied extraArgs are either invalid and we would have reverted\\n      // or we have valid extraArgs and we can return them without having to re-encode them.\\n      return (\\n        extraArgs,\\n        true,\\n        abi.encode(\\n          _parseSVMExtraArgsFromBytes(extraArgs, destChainConfig.maxPerMsgGasLimit, destChainConfig.enforceOutOfOrder)\\n            .tokenReceiver\\n        )\\n      );\\n    }\\n    revert InvalidChainFamilySelector(destChainConfig.chainFamilySelector);\\n  }\\n\\n  /// @inheritdoc IFeeQuoter\\n  function processPoolReturnData(\\n    uint64 destChainSelector,\\n    Internal.EVM2AnyTokenTransfer[] calldata onRampTokenTransfers,\\n    Client.EVMTokenAmount[] calldata sourceTokenAmounts\\n  ) external view returns (bytes[] memory destExecDataPerToken) {\\n    bytes4 chainFamilySelector = s_destChainConfigs[destChainSelector].chainFamilySelector;\\n    destExecDataPerToken = new bytes[](onRampTokenTransfers.length);\\n    for (uint256 i = 0; i \\u003c onRampTokenTransfers.length; ++i) {\\n      address sourceToken = sourceTokenAmounts[i].token;\\n\\n      // Since the DON has to pay for the extraData to be included on the destination chain, we cap the length of the\\n      // extraData. This prevents gas bomb attacks on the NOPs. As destBytesOverhead accounts for both.\\n      // extraData and offchainData, this caps the worst case abuse to the number of bytes reserved for offchainData.\\n      uint256 destPoolDataLength = onRampTokenTransfers[i].extraData.length;\\n      if (destPoolDataLength \\u003e Pool.CCIP_LOCK_OR_BURN_V1_RET_BYTES) {\\n        if (destPoolDataLength \\u003e s_tokenTransferFeeConfig[destChainSelector][sourceToken].destBytesOverhead) {\\n          revert SourceTokenDataTooLarge(sourceToken);\\n        }\\n      }\\n\\n      // We pass '1' here so that SVM validation requires a non-zero token address.\\n      // The 'gasLimit' parameter isn't actually used for gas in this context; it simply\\n      // signals that the address must not be zero on SVM.\\n      _validateDestFamilyAddress(chainFamilySelector, onRampTokenTransfers[i].destTokenAddress, 1);\\n      FeeQuoter.TokenTransferFeeConfig memory tokenTransferFeeConfig =\\n        s_tokenTransferFeeConfig[destChainSelector][sourceToken];\\n\\n      uint32 destGasAmount = tokenTransferFeeConfig.isEnabled\\n        ? tokenTransferFeeConfig.destGasOverhead\\n        : s_destChainConfigs[destChainSelector].defaultTokenDestGasOverhead;\\n\\n      // The user will be billed either the default or the override, so we send the exact amount that we billed for\\n      // to the destination chain to be used for the token releaseOrMint and transfer.\\n      destExecDataPerToken[i] = abi.encode(destGasAmount);\\n    }\\n    return destExecDataPerToken;\\n  }\\n\\n  // ================================================================\\n  // │                           Configs                            │\\n  // ================================================================\\n\\n  /// @notice Returns the configured config for the dest chain selector.\\n  /// @param destChainSelector Destination chain selector to fetch config for.\\n  /// @return destChainConfig Config for the destination chain.\\n  function getDestChainConfig(\\n    uint64 destChainSelector\\n  ) external view returns (DestChainConfig memory) {\\n    return s_destChainConfigs[destChainSelector];\\n  }\\n\\n  /// @notice Updates the destination chain specific config.\\n  /// @param destChainConfigArgs Array of source chain specific configs.\\n  function applyDestChainConfigUpdates(\\n    DestChainConfigArgs[] memory destChainConfigArgs\\n  ) external onlyOwner {\\n    _applyDestChainConfigUpdates(destChainConfigArgs);\\n  }\\n\\n  /// @notice Internal version of applyDestChainConfigUpdates.\\n  function _applyDestChainConfigUpdates(\\n    DestChainConfigArgs[] memory destChainConfigArgs\\n  ) internal {\\n    for (uint256 i = 0; i \\u003c destChainConfigArgs.length; ++i) {\\n      DestChainConfigArgs memory destChainConfigArg = destChainConfigArgs[i];\\n      uint64 destChainSelector = destChainConfigArgs[i].destChainSelector;\\n      DestChainConfig memory destChainConfig = destChainConfigArg.destChainConfig;\\n\\n      // destChainSelector must be non-zero, defaultTxGasLimit must be set, must be less than maxPerMsgGasLimit\\n      if (\\n        destChainSelector == 0 || destChainConfig.defaultTxGasLimit == 0\\n          || destChainConfig.defaultTxGasLimit \\u003e destChainConfig.maxPerMsgGasLimit\\n          || (\\n            destChainConfig.chainFamilySelector != Internal.CHAIN_FAMILY_SELECTOR_EVM\\n              \\u0026\\u0026 destChainConfig.chainFamilySelector != Internal.CHAIN_FAMILY_SELECTOR_SVM\\n              \\u0026\\u0026 destChainConfig.chainFamilySelector != Internal.CHAIN_FAMILY_SELECTOR_APTOS\\n              \\u0026\\u0026 destChainConfig.chainFamilySelector != Internal.CHAIN_FAMILY_SELECTOR_SUI\\n          )\\n      ) {\\n        revert InvalidDestChainConfig(destChainSelector);\\n      }\\n\\n      // If the chain family selector is zero, it indicates that the chain was never configured and we\\n      // are adding a new chain.\\n      if (s_destChainConfigs[destChainSelector].chainFamilySelector == 0) {\\n        emit DestChainAdded(destChainSelector, destChainConfig);\\n      } else {\\n        emit DestChainConfigUpdated(destChainSelector, destChainConfig);\\n      }\\n\\n      s_destChainConfigs[destChainSelector] = destChainConfig;\\n    }\\n  }\\n\\n  /// @notice Returns the static FeeQuoter config.\\n  /// @dev RMN depends on this function, if updated, please notify the RMN maintainers.\\n  /// @return staticConfig The static configuration.\\n  function getStaticConfig() external view returns (StaticConfig memory) {\\n    return StaticConfig({\\n      maxFeeJuelsPerMsg: i_maxFeeJuelsPerMsg,\\n      linkToken: i_linkToken,\\n      tokenPriceStalenessThreshold: i_tokenPriceStalenessThreshold\\n    });\\n  }\\n}\\n\"},\"contracts/interfaces/IFeeQuoter.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {IPriceRegistry} from \\\"./IPriceRegistry.sol\\\";\\n\\ninterface IFeeQuoter is IPriceRegistry {\\n  /// @notice Validates the ccip message \\u0026 returns the fee.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param message The message to get quote for.\\n  /// @return feeTokenAmount The amount of fee token needed for the fee, in smallest denomination of the fee token.\\n  function getValidatedFee(\\n    uint64 destChainSelector,\\n    Client.EVM2AnyMessage calldata message\\n  ) external view returns (uint256 feeTokenAmount);\\n\\n  /// @notice Converts the extraArgs to the latest version and returns the converted message fee in juels.\\n  /// @notice Validates pool return data.\\n  /// @param destChainSelector destination chain selector to process, must be a configured valid chain.\\n  /// @param feeToken token address used to pay for message fees, must be a configured valid fee token.\\n  /// @param feeTokenAmount Fee token amount.\\n  /// @param extraArgs Message extra args that were passed in by the client.\\n  /// @param messageReceiver Message receiver address in bytes from EVM2AnyMessage.receiver\\n  /// @return msgFeeJuels message fee in juels.\\n  /// @return isOutOfOrderExecution true if the message should be executed out of order.\\n  /// @return convertedExtraArgs extra args converted to the latest family-specific args version.\\n  /// @return tokenReceiver token receiver address in bytes on destination chain\\n  function processMessageArgs(\\n    uint64 destChainSelector,\\n    address feeToken,\\n    uint256 feeTokenAmount,\\n    bytes calldata extraArgs,\\n    bytes calldata messageReceiver\\n  )\\n    external\\n    view\\n    returns (\\n      uint256 msgFeeJuels,\\n      bool isOutOfOrderExecution,\\n      bytes memory convertedExtraArgs,\\n      bytes memory tokenReceiver\\n    );\\n\\n  /// @notice Validates pool return data.\\n  /// @param destChainSelector Destination chain selector to which the token amounts are sent to.\\n  /// @param onRampTokenTransfers Token amounts with populated pool return data.\\n  /// @param sourceTokenAmounts Token amounts originally sent in a Client.EVM2AnyMessage message.\\n  /// @return destExecDataPerToken Destination chain execution data.\\n  function processPoolReturnData(\\n    uint64 destChainSelector,\\n    Internal.EVM2AnyTokenTransfer[] calldata onRampTokenTransfers,\\n    Client.EVMTokenAmount[] calldata sourceTokenAmounts\\n  ) external view returns (bytes[] memory destExecDataPerToken);\\n}\\n\"},\"contracts/interfaces/IPriceRegistry.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\n\\ninterface IPriceRegistry {\\n  /// @notice Update the price for given tokens and gas prices for given chains.\\n  /// @param priceUpdates The price updates to apply.\\n  function updatePrices(\\n    Internal.PriceUpdates memory priceUpdates\\n  ) external;\\n\\n  /// @notice Get the `tokenPrice` for a given token.\\n  /// @param token The token to get the price for.\\n  /// @return tokenPrice The tokenPrice for the given token.\\n  function getTokenPrice(\\n    address token\\n  ) external view returns (Internal.TimestampedPackedUint224 memory);\\n\\n  /// @notice Get the `tokenPrice` for a given token, checks if the price is valid.\\n  /// @param token The token to get the price for.\\n  /// @return tokenPrice The tokenPrice for the given token if it exists and is valid.\\n  function getValidatedTokenPrice(\\n    address token\\n  ) external view returns (uint224);\\n\\n  /// @notice Get the `tokenPrice` for an array of tokens.\\n  /// @param tokens The tokens to get prices for.\\n  /// @return tokenPrices The tokenPrices for the given tokens.\\n  function getTokenPrices(\\n    address[] calldata tokens\\n  ) external view returns (Internal.TimestampedPackedUint224[] memory);\\n\\n  /// @notice Get an encoded `gasPrice` for a given destination chain ID.\\n  /// The 224-bit result encodes necessary gas price components.\\n  /// On L1 chains like Ethereum or Avax, the only component is the gas price.\\n  /// On Optimistic Rollups, there are two components - the L2 gas price, and L1 base fee for data availability.\\n  /// On future chains, there could be more or differing price components.\\n  /// PriceRegistry does not contain chain-specific logic to parse destination chain price components.\\n  /// @param destChainSelector The destination chain to get the price for.\\n  /// @return gasPrice The encoded gasPrice for the given destination chain ID.\\n  function getDestinationChainGasPrice(\\n    uint64 destChainSelector\\n  ) external view returns (Internal.TimestampedPackedUint224 memory);\\n\\n  /// @notice Gets the fee token price and the gas price, both denominated in dollars.\\n  /// @param token The source token to get the price for.\\n  /// @param destChainSelector The destination chain to get the gas price for.\\n  /// @return tokenPrice The price of the feeToken in 1e18 dollars per base unit.\\n  /// @return gasPrice The price of gas in 1e18 dollars per base unit.\\n  function getTokenAndGasPrices(\\n    address token,\\n    uint64 destChainSelector\\n  ) external view returns (uint224 tokenPrice, uint224 gasPrice);\\n\\n  /// @notice Convert a given token amount to target token amount.\\n  /// @param fromToken The given token address.\\n  /// @param fromTokenAmount The given token amount.\\n  /// @param toToken The target token address.\\n  /// @return toTokenAmount The target token amount.\\n  function convertTokenAmount(\\n    address fromToken,\\n    uint256 fromTokenAmount,\\n    address toToken\\n  ) external view returns (uint256 toTokenAmount);\\n\\n  /// @notice Get the list of fee tokens.\\n  /// @return feeTokens The tokens set as fee tokens.\\n  function getFeeTokens() external view returns (address[] memory);\\n}\\n\"},\"contracts/libraries/Client.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n// End consumer library.\\nlibrary Client {\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct EVMTokenAmount {\\n    address token; // token address on the local chain.\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  struct Any2EVMMessage {\\n    bytes32 messageId; // MessageId corresponding to ccipSend on source.\\n    uint64 sourceChainSelector; // Source chain selector.\\n    bytes sender; // abi.decode(sender) if coming from an EVM chain.\\n    bytes data; // payload sent in original message.\\n    EVMTokenAmount[] destTokenAmounts; // Tokens and their amounts in their destination chain representation.\\n  }\\n\\n  // If extraArgs is empty bytes, the default is 200k gas limit.\\n  struct EVM2AnyMessage {\\n    bytes receiver; // abi.encode(receiver address) for dest EVM chains.\\n    bytes data; // Data payload.\\n    EVMTokenAmount[] tokenAmounts; // Token transfers.\\n    address feeToken; // Address of feeToken. address(0) means you will send msg.value.\\n    bytes extraArgs; // Populate this with _argsToBytes(EVMExtraArgsV2).\\n  }\\n\\n  // Tag to indicate only a gas limit. Only usable for EVM as destination chain.\\n  bytes4 public constant EVM_EXTRA_ARGS_V1_TAG = 0x97a657c9;\\n\\n  struct EVMExtraArgsV1 {\\n    uint256 gasLimit;\\n  }\\n\\n  function _argsToBytes(\\n    EVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(EVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n\\n  // Tag to indicate a gas limit (or dest chain equivalent processing units) and Out Of Order Execution. This tag is\\n  // available for multiple chain families. If there is no chain family specific tag, this is the default available\\n  // for a chain.\\n  // Note: not available for Solana VM based chains.\\n  bytes4 public constant GENERIC_EXTRA_ARGS_V2_TAG = 0x181dcf10;\\n\\n  /// @param gasLimit: gas limit for the callback on the destination chain.\\n  /// @param allowOutOfOrderExecution: if true, it indicates that the message can be executed in any order relative to\\n  /// other messages from the same sender. This value's default varies by chain. On some chains, a particular value is\\n  /// enforced, meaning if the expected value is not set, the message request will revert.\\n  /// @dev Fully compatible with the previously existing EVMExtraArgsV2.\\n  struct GenericExtraArgsV2 {\\n    uint256 gasLimit;\\n    bool allowOutOfOrderExecution;\\n  }\\n\\n  // Extra args tag for chains that use the Solana VM.\\n  bytes4 public constant SVM_EXTRA_ARGS_V1_TAG = 0x1f3b3aba;\\n\\n  struct SVMExtraArgsV1 {\\n    uint32 computeUnits;\\n    uint64 accountIsWritableBitmap;\\n    bool allowOutOfOrderExecution;\\n    bytes32 tokenReceiver;\\n    // Additional accounts needed for execution of CCIP receiver. Must be empty if message.receiver is zero.\\n    // Token transfer related accounts are specified in the token pool lookup table on SVM.\\n    bytes32[] accounts;\\n  }\\n\\n  /// @dev The maximum number of accounts that can be passed in SVMExtraArgs.\\n  uint256 public constant SVM_EXTRA_ARGS_MAX_ACCOUNTS = 64;\\n\\n  /// @dev The expected static payload size of a token transfer when Borsh encoded and submitted to SVM.\\n  /// TokenPool extra data and offchain data sizes are dynamic, and should be accounted for separately.\\n  uint256 public constant SVM_TOKEN_TRANSFER_DATA_OVERHEAD = (4 + 32) // source_pool\\n    + 32 // token_address\\n    + 4 // gas_amount\\n    + 4 // extra_data overhead\\n    + 32 // amount\\n    + 32 // size of the token lookup table account\\n    + 32 // token-related accounts in the lookup table, over-estimated to 32, typically between 11 - 13\\n    + 32 // token account belonging to the token receiver, e.g ATA, not included in the token lookup table\\n    + 32 // per-chain token pool config, not included in the token lookup table\\n    + 32 // per-chain token billing config, not always included in the token lookup table\\n    + 32; // OffRamp pool signer PDA, not included in the token lookup table\\n\\n  /// @dev Number of overhead accounts needed for message execution on SVM.\\n  /// @dev These are message.receiver, and the OffRamp Signer PDA specific to the receiver.\\n  uint256 public constant SVM_MESSAGING_ACCOUNTS_OVERHEAD = 2;\\n\\n  /// @dev The size of each SVM account address in bytes.\\n  uint256 public constant SVM_ACCOUNT_BYTE_SIZE = 32;\\n\\n  function _argsToBytes(\\n    GenericExtraArgsV2 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(GENERIC_EXTRA_ARGS_V2_TAG, extraArgs);\\n  }\\n\\n  function _svmArgsToBytes(\\n    SVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(SVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n}\\n\"},\"contracts/libraries/Internal.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {MerkleMultiProof} from \\\"../libraries/MerkleMultiProof.sol\\\";\\n\\n/// @notice Library for CCIP internal definitions common to multiple contracts.\\n/// @dev The following is a non-exhaustive list of \\\"known issues\\\" for CCIP:\\n/// - We could implement yield claiming for Blast. This is not worth the custom code path on non-blast chains.\\n/// - uint32 is used for timestamps, which will overflow in 2106. This is not a concern for the current use case, as we\\n/// expect to have migrated to a new version by then.\\nlibrary Internal {\\n  error InvalidEVMAddress(bytes encodedAddress);\\n  error Invalid32ByteAddress(bytes encodedAddress);\\n\\n  /// @dev We limit return data to a selector plus 4 words. This is to avoid malicious contracts from returning\\n  /// large amounts of data and causing repeated out-of-gas scenarios.\\n  uint16 internal constant MAX_RET_BYTES = 4 + 4 * 32;\\n  /// @dev The expected number of bytes returned by the balanceOf function.\\n  uint256 internal constant MAX_BALANCE_OF_RET_BYTES = 32;\\n\\n  /// @dev The address used to send calls for gas estimation.\\n  /// You only need to use this address if the minimum gas limit specified by the user is not actually enough to execute the\\n  /// given message and you're attempting to estimate the actual necessary gas limit\\n  address public constant GAS_ESTIMATION_SENDER = address(0xC11C11C11C11C11C11C11C11C11C11C11C11C1);\\n\\n  /// @notice A collection of token price and gas price updates.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct PriceUpdates {\\n    TokenPriceUpdate[] tokenPriceUpdates;\\n    GasPriceUpdate[] gasPriceUpdates;\\n  }\\n\\n  /// @notice Token price in USD.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct TokenPriceUpdate {\\n    address sourceToken; // Source token.\\n    uint224 usdPerToken; // 1e18 USD per 1e18 of the smallest token denomination.\\n  }\\n\\n  /// @notice Gas price for a given chain in USD, its value may contain tightly packed fields.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct GasPriceUpdate {\\n    uint64 destChainSelector; // Destination chain selector.\\n    uint224 usdPerUnitGas; // 1e18 USD per smallest unit (e.g. wei) of destination chain gas.\\n  }\\n\\n  /// @notice A timestamped uint224 value that can contain several tightly packed fields.\\n  struct TimestampedPackedUint224 {\\n    uint224 value; // ────╮ Value in uint224, packed.\\n    uint32 timestamp; // ─╯ Timestamp of the most recent price update.\\n  }\\n\\n  /// @dev Gas price is stored in 112-bit unsigned int. uint224 can pack 2 prices.\\n  /// When packing L1 and L2 gas prices, L1 gas price is left-shifted to the higher-order bits.\\n  /// Using uint8 type, which cannot be higher than other bit shift operands, to avoid shift operand type warning.\\n  uint8 public constant GAS_PRICE_BITS = 112;\\n\\n  struct SourceTokenData {\\n    // The source pool address, abi encoded. This value is trusted as it was obtained through the onRamp. It can be\\n    // relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint32 destGasAmount; // The amount of gas available for the releaseOrMint and balanceOf calls on the offRamp\\n  }\\n\\n  /// @notice Report that is submitted by the execution DON at the execution phase, including chain selector data.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct ExecutionReport {\\n    uint64 sourceChainSelector; // Source chain selector for which the report is submitted.\\n    Any2EVMRampMessage[] messages;\\n    // Contains a bytes array for each message, each inner bytes array contains bytes per transferred token.\\n    bytes[][] offchainTokenData;\\n    bytes32[] proofs;\\n    uint256 proofFlagBits;\\n  }\\n\\n  /// @dev Any2EVMRampMessage struct has 10 fields, including 3 variable unnested arrays, sender, data and tokenAmounts.\\n  /// Each variable array takes 1 more slot to store its length.\\n  /// When abi encoded, excluding array contents, Any2EVMMessage takes up a fixed number of 13 slots, 32 bytes each.\\n  /// Assume 1 slot for sender\\n  /// For structs that contain arrays, 1 more slot is added to the front, reaching a total of 14.\\n  /// The fixed bytes does not cover struct data (this is represented by MESSAGE_FIXED_BYTES_PER_TOKEN)\\n  uint256 public constant MESSAGE_FIXED_BYTES = 32 * 15;\\n\\n  /// @dev Any2EVMTokensTransfer struct bytes length\\n  /// 0x20\\n  /// sourcePoolAddress_offset\\n  /// destTokenAddress\\n  /// destGasAmount\\n  /// extraData_offset\\n  /// amount\\n  /// sourcePoolAddress_length\\n  /// sourcePoolAddress_content // assume 1 slot\\n  /// extraData_length // contents billed separately\\n  uint256 public constant MESSAGE_FIXED_BYTES_PER_TOKEN = 32 * (4 + (3 + 2));\\n\\n  bytes32 internal constant ANY_2_EVM_MESSAGE_HASH = keccak256(\\\"Any2EVMMessageHashV1\\\");\\n  bytes32 internal constant EVM_2_ANY_MESSAGE_HASH = keccak256(\\\"EVM2AnyMessageHashV1\\\");\\n\\n  /// @dev Used to hash messages for multi-lane family-agnostic OffRamps.\\n  /// OnRamp hash(EVM2AnyMessage) != Any2EVMRampMessage.messageId.\\n  /// OnRamp hash(EVM2AnyMessage) != OffRamp hash(Any2EVMRampMessage).\\n  /// @param original OffRamp message to hash.\\n  /// @param metadataHash Hash preimage to ensure global uniqueness.\\n  /// @return hashedMessage hashed message as a keccak256.\\n  function _hash(Any2EVMRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.header.messageId,\\n            original.receiver,\\n            original.header.sequenceNumber,\\n            original.gasLimit,\\n            original.header.nonce\\n          )\\n        ),\\n        keccak256(original.sender),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts))\\n      )\\n    );\\n  }\\n\\n  function _hash(EVM2AnyRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.sender,\\n            original.header.sequenceNumber,\\n            original.header.nonce,\\n            original.feeToken,\\n            original.feeTokenAmount\\n          )\\n        ),\\n        keccak256(original.receiver),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts)),\\n        keccak256(original.extraArgs)\\n      )\\n    );\\n  }\\n\\n  /// @dev We disallow the first 1024 addresses to avoid calling into a range known for hosting precompiles. Calling\\n  /// into precompiles probably won't cause any issues, but to be safe we can disallow this range. It is extremely\\n  /// unlikely that anyone would ever be able to generate an address in this range. There is no official range of\\n  /// precompiles, but EIP-7587 proposes to reserve the range 0x100 to 0x1ff. Our range is more conservative, even\\n  /// though it might not be exhaustive for all chains, which is OK. We also disallow the zero address, which is a\\n  /// common practice.\\n  uint256 public constant EVM_PRECOMPILE_SPACE = 1024;\\n\\n  // According to the Aptos docs, the first 0xa addresses are reserved for precompiles.\\n  // https://github.com/aptos-labs/aptos-core/blob/main/aptos-move/framework/aptos-framework/doc/account.md#function-create_framework_reserved_account-1\\n  uint256 public constant APTOS_PRECOMPILE_SPACE = 0x0b;\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// EVM address space. If it isn't it will revert with an InvalidEVMAddress error, which we can catch and handle\\n  /// more gracefully than a revert from abi.decode.\\n  function _validateEVMAddress(\\n    bytes memory encodedAddress\\n  ) internal pure {\\n    if (encodedAddress.length != 32) revert InvalidEVMAddress(encodedAddress);\\n    uint256 encodedAddressUint = abi.decode(encodedAddress, (uint256));\\n    if (encodedAddressUint \\u003e type(uint160).max || encodedAddressUint \\u003c EVM_PRECOMPILE_SPACE) {\\n      revert InvalidEVMAddress(encodedAddress);\\n    }\\n  }\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// bounds of [minValue, uint256.max]. If it isn't it will revert with an Invalid32ByteAddress error.\\n  function _validate32ByteAddress(bytes memory encodedAddress, uint256 minValue) internal pure {\\n    if (encodedAddress.length != 32) revert Invalid32ByteAddress(encodedAddress);\\n    if (minValue \\u003e 0) {\\n      if (abi.decode(encodedAddress, (uint256)) \\u003c minValue) {\\n        revert Invalid32ByteAddress(encodedAddress);\\n      }\\n    }\\n  }\\n\\n  /// @notice Enum listing the possible message execution states within the offRamp contract.\\n  /// UNTOUCHED never executed.\\n  /// IN_PROGRESS currently being executed, used a replay protection.\\n  /// SUCCESS successfully executed. End state.\\n  /// FAILURE unsuccessfully executed, manual execution is now enabled.\\n  /// @dev RMN depends on this enum, if changing, please notify the RMN maintainers.\\n  enum MessageExecutionState {\\n    UNTOUCHED,\\n    IN_PROGRESS,\\n    SUCCESS,\\n    FAILURE\\n  }\\n\\n  /// @notice CCIP OCR plugin type, used to separate execution \\u0026 commit transmissions and configs.\\n  enum OCRPluginType {\\n    Commit,\\n    Execution\\n  }\\n\\n  /// @notice Family-agnostic header for OnRamp \\u0026 OffRamp messages.\\n  /// The messageId is not expected to match hash(message), since it may originate from another ramp family.\\n  struct RampMessageHeader {\\n    bytes32 messageId; // Unique identifier for the message, generated with the source chain's encoding scheme (i.e. not necessarily abi.encoded).\\n    uint64 sourceChainSelector; // ─╮ the chain selector of the source chain, note: not chainId.\\n    uint64 destChainSelector; //    │ the chain selector of the destination chain, note: not chainId.\\n    uint64 sequenceNumber; //       │ sequence number, not unique across lanes.\\n    uint64 nonce; // ───────────────╯ nonce for this lane for this sender, not unique across senders/lanes.\\n  }\\n\\n  struct EVM2AnyTokenTransfer {\\n    // The source pool EVM address. This value is trusted as it was obtained through the onRamp. It can be relied\\n    // upon by the destination pool to validate the source pool.\\n    address sourcePoolAddress;\\n    // The EVM address of the destination token.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n    // Destination chain data used to execute the token transfer on the destination chain. For an EVM destination, it\\n    // consists of the amount of gas available for the releaseOrMint and transfer calls made by the offRamp.\\n    bytes destExecData;\\n  }\\n\\n  struct Any2EVMTokenTransfer {\\n    // The source pool EVM address encoded to bytes. This value is trusted as it is obtained through the onRamp. It can\\n    // be relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    address destTokenAddress; // ─╮ Address of destination token\\n    uint32 destGasAmount; // ─────╯ The amount of gas available for the releaseOrMint and transfer calls on the offRamp.\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  /// @notice Family-agnostic message routed to an OffRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage), hash(Any2EVMRampMessage) != messageId due to encoding\\n  /// and parameter differences.\\n  struct Any2EVMRampMessage {\\n    RampMessageHeader header; // Message header.\\n    bytes sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    address receiver; // receiver address on the destination chain.\\n    uint256 gasLimit; // user supplied maximum gas amount available for dest chain execution.\\n    Any2EVMTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  /// @notice Family-agnostic message emitted from the OnRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage) due to encoding \\u0026 parameter differences.\\n  /// messageId = hash(EVM2AnyRampMessage) using the source EVM chain's encoding format.\\n  struct EVM2AnyRampMessage {\\n    RampMessageHeader header; // Message header.\\n    address sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    bytes receiver; // receiver address on the destination chain.\\n    bytes extraArgs; // destination-chain specific extra args, such as the gasLimit for EVM chains.\\n    address feeToken; // fee token.\\n    uint256 feeTokenAmount; // fee token amount.\\n    uint256 feeValueJuels; // fee amount in Juels.\\n    EVM2AnyTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector EVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_EVM = 0x2812d52c;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SVM = 0x1e10bdc4;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector APTOS\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_APTOS = 0xac77ffec;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SUI\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SUI = 0xc4e05953;\\n\\n  /// @dev Holds a merkle root and interval for a source chain so that an array of these can be passed in the CommitReport.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  /// @dev inefficient struct packing intentionally chosen to maintain order of specificity. Not a storage struct so impact is minimal.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct MerkleRoot {\\n    uint64 sourceChainSelector; // Remote source chain selector that the Merkle Root is scoped to\\n    bytes onRampAddress; //        Generic onRamp address, to support arbitrary sources; for EVM, use abi.encode\\n    uint64 minSeqNr; // ─────────╮ Minimum sequence number, inclusive\\n    uint64 maxSeqNr; // ─────────╯ Maximum sequence number, inclusive\\n    bytes32 merkleRoot; //         Merkle root covering the interval \\u0026 source chain messages\\n  }\\n}\\n\"},\"contracts/libraries/MerkleMultiProof.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nlibrary MerkleMultiProof {\\n  /// @notice Leaf domain separator, should be used as the first 32 bytes of a leaf's preimage.\\n  bytes32 internal constant LEAF_DOMAIN_SEPARATOR = 0x0000000000000000000000000000000000000000000000000000000000000000;\\n  /// @notice Internal domain separator, should be used as the first 32 bytes of an internal node's preimage.\\n  bytes32 internal constant INTERNAL_DOMAIN_SEPARATOR =\\n    0x0000000000000000000000000000000000000000000000000000000000000001;\\n\\n  uint256 internal constant MAX_NUM_HASHES = 256;\\n\\n  error InvalidProof();\\n  error LeavesCannotBeEmpty();\\n\\n  /// @notice Computes the root based on provided pre-hashed leaf nodes in leaves, internal nodes  in proofs, and using\\n  /// proofFlagBits' i-th bit to determine if an element of proofs or one of the previously computed leafs or internal\\n  /// nodes will be used for the i-th hash.\\n  /// @param leaves Should be pre-hashed and the first 32 bytes of a leaf's preimage should match LEAF_DOMAIN_SEPARATOR.\\n  /// @param proofs Hashes to be used instead of a leaf hash when the proofFlagBits indicates a proof should be used.\\n  /// @param proofFlagBits A single uint256 of which each bit indicates whether a leaf or a proof needs to be used in\\n  /// a hash operation.\\n  /// @dev the maximum number of hash operations it set to 256. Any input that would require more than 256 hashes to get\\n  /// to a root will revert.\\n  /// @dev For given input `leaves` = [a,b,c] `proofs` = [D] and `proofFlagBits` = 5\\n  ///     totalHashes = 3 + 1 - 1 = 3\\n  ///  ** round 1 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 0) \\u0026 1 = true\\n  ///    hashes[0] = hashPair(a, b)\\n  ///    (leafPos, hashPos, proofPos) = (2, 0, 0);\\n  ///\\n  ///  ** round 2 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 1) \\u0026 1 = false\\n  ///    hashes[1] = hashPair(D, c)\\n  ///    (leafPos, hashPos, proofPos) = (3, 0, 1);\\n  ///\\n  ///  ** round 3 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 2) \\u0026 1 = true\\n  ///    hashes[2] = hashPair(hashes[0], hashes[1])\\n  ///    (leafPos, hashPos, proofPos) = (3, 2, 1);\\n  ///\\n  ///    i = 3 and no longer \\u003c totalHashes. The algorithm is done\\n  ///    return hashes[totalHashes - 1] = hashes[2]; the last hash we computed.\\n  // We mark this function as internal to force it to be inlined in contracts that use it, but semantically it is public.\\n  function _merkleRoot(\\n    bytes32[] memory leaves,\\n    bytes32[] memory proofs,\\n    uint256 proofFlagBits\\n  ) internal pure returns (bytes32) {\\n    unchecked {\\n      uint256 leavesLen = leaves.length;\\n      uint256 proofsLen = proofs.length;\\n      if (leavesLen == 0) revert LeavesCannotBeEmpty();\\n      if (!(leavesLen \\u003c= MAX_NUM_HASHES + 1 \\u0026\\u0026 proofsLen \\u003c= MAX_NUM_HASHES + 1)) revert InvalidProof();\\n      uint256 totalHashes = leavesLen + proofsLen - 1;\\n      if (!(totalHashes \\u003c= MAX_NUM_HASHES)) revert InvalidProof();\\n      if (totalHashes == 0) {\\n        return leaves[0];\\n      }\\n      bytes32[] memory hashes = new bytes32[](totalHashes);\\n      (uint256 leafPos, uint256 hashPos, uint256 proofPos) = (0, 0, 0);\\n\\n      for (uint256 i = 0; i \\u003c totalHashes; ++i) {\\n        // Checks if the bit flag signals the use of a supplied proof or a leaf/previous hash.\\n        bytes32 a;\\n        if (proofFlagBits \\u0026 (1 \\u003c\\u003c i) == (1 \\u003c\\u003c i)) {\\n          // Use a leaf or a previously computed hash.\\n          if (leafPos \\u003c leavesLen) {\\n            a = leaves[leafPos++];\\n          } else {\\n            a = hashes[hashPos++];\\n          }\\n        } else {\\n          // Use a supplied proof.\\n          a = proofs[proofPos++];\\n        }\\n\\n        // The second part of the hashed pair is never a proof as hashing two proofs would result in a\\n        // hash that can already be computed offchain.\\n        bytes32 b;\\n        if (leafPos \\u003c leavesLen) {\\n          b = leaves[leafPos++];\\n        } else {\\n          b = hashes[hashPos++];\\n        }\\n\\n        if (!(hashPos \\u003c= i)) revert InvalidProof();\\n\\n        hashes[i] = _hashPair(a, b);\\n      }\\n      if (!(hashPos == totalHashes - 1 \\u0026\\u0026 leafPos == leavesLen \\u0026\\u0026 proofPos == proofsLen)) revert InvalidProof();\\n      // Return the last hash.\\n      return hashes[totalHashes - 1];\\n    }\\n  }\\n\\n  /// @notice Hashes two bytes32 objects in their given order, prepended by the INTERNAL_DOMAIN_SEPARATOR.\\n  function _hashInternalNode(bytes32 left, bytes32 right) private pure returns (bytes32 hash) {\\n    return keccak256(abi.encode(INTERNAL_DOMAIN_SEPARATOR, left, right));\\n  }\\n\\n  /// @notice Hashes two bytes32 objects. The order is taken into account, using the lower value first.\\n  function _hashPair(bytes32 a, bytes32 b) private pure returns (bytes32) {\\n    return a \\u003c b ? _hashInternalNode(a, b) : _hashInternalNode(b, a);\\n  }\\n}\\n\"},\"contracts/libraries/Pool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This library contains various token pool functions to aid constructing the return data.\\nlibrary Pool {\\n  // The tag used to signal support for the pool v1 standard.\\n  // bytes4(keccak256(\\\"CCIP_POOL_V1\\\"))\\n  bytes4 public constant CCIP_POOL_V1 = 0xaff2afbf;\\n\\n  // The number of bytes in the return data for a pool v1 releaseOrMint call.\\n  // This should match the size of the ReleaseOrMintOutV1 struct.\\n  uint16 public constant CCIP_POOL_V1_RET_BYTES = 32;\\n\\n  // The default max number of bytes in the return data for a pool v1 lockOrBurn call.\\n  // This data can be used to send information to the destination chain token pool. Can be overwritten\\n  // in the TokenTransferFeeConfig.destBytesOverhead if more data is required.\\n  uint32 public constant CCIP_LOCK_OR_BURN_V1_RET_BYTES = 32;\\n\\n  struct LockOrBurnInV1 {\\n    bytes receiver; //  The recipient of the tokens on the destination chain, abi encoded.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the destination chain.\\n    address originalSender; // ─────╯ The original sender of the tx on the source chain.\\n    uint256 amount; //  The amount of tokens to lock or burn, denominated in the source token's decimals.\\n    address localToken; //  The address on this chain of the token to lock or burn.\\n  }\\n\\n  struct LockOrBurnOutV1 {\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes destPoolData;\\n  }\\n\\n  struct ReleaseOrMintInV1 {\\n    bytes originalSender; //          The original sender of the tx on the source chain.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the source chain.\\n    address receiver; // ───────────╯ The recipient of the tokens on the destination chain.\\n    uint256 amount; //                The amount of tokens to release or mint, denominated in the source token's decimals.\\n    address localToken; //            The address on this chain of the token to release or mint.\\n    /// @dev WARNING: sourcePoolAddress should be checked prior to any processing of funds. Make sure it matches the\\n    /// expected pool address for the given remoteChainSelector.\\n    bytes sourcePoolAddress; //       The address of the source pool, abi encoded in the case of EVM chains.\\n    bytes sourcePoolData; //          The data received from the source pool to process the release or mint.\\n    /// @dev WARNING: offchainTokenData is untrusted data.\\n    bytes offchainTokenData; //       The offchain data to process the release or mint.\\n  }\\n\\n  struct ReleaseOrMintOutV1 {\\n    // The number of tokens released or minted on the destination chain, denominated in the local token's decimals.\\n    // This value is expected to be equal to the ReleaseOrMintInV1.amount in the case where the source and destination\\n    // chain have the same number of decimals.\\n    uint256 destinationAmount;\\n  }\\n}\\n\"},\"contracts/libraries/USDPriceWith18Decimals.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.0;\\n\\nlibrary USDPriceWith18Decimals {\\n  /// @notice Takes a price in USD, with 18 decimals per 1e18 token amount, and amount of the smallest token\\n  /// denomination, calculates the value in USD with 18 decimals.\\n  /// @param tokenPrice The USD price of the token.\\n  /// @param tokenAmount Amount of the smallest token denomination.\\n  /// @return USD value with 18 decimals.\\n  /// @dev this function assumes that no more than 1e59 US dollar worth of token is passed in. If more is sent, this\\n  /// function will overflow and revert. Since there isn't even close to 1e59 dollars, this is ok for all legit tokens.\\n  function _calcUSDValueFromTokenAmount(uint224 tokenPrice, uint256 tokenAmount) internal pure returns (uint256) {\\n    /// LINK Example:\\n    /// tokenPrice:         8e18 -\\u003e $8/LINK, as 1e18 token amount is 1 LINK, worth 8 USD, or 8e18 with 18 decimals\\n    /// tokenAmount:        2e18 -\\u003e 2 LINK\\n    /// result:             8e18 * 2e18 / 1e18 -\\u003e 16e18 with 18 decimals = $16\\n\\n    /// USDC Example:\\n    /// tokenPrice:         1e30 -\\u003e $1/USDC, as 1e18 token amount is 1e12 USDC, worth 1e12 USD, or 1e30 with 18 decimals\\n    /// tokenAmount:        5e6  -\\u003e 5 USDC\\n    /// result:             1e30 * 5e6 / 1e18 -\\u003e 5e18 with 18 decimals = $5\\n    return (tokenPrice * tokenAmount) / 1e18;\\n  }\\n\\n  /// @notice Takes a price in USD, with 18 decimals per 1e18 token amount, and USD value with 18 decimals, calculates\\n  /// amount of the smallest token denomination.\\n  /// @param tokenPrice The USD price of the token.\\n  /// @param usdValue USD value with 18 decimals.\\n  /// @return Amount of the smallest token denomination.\\n  function _calcTokenAmountFromUSDValue(uint224 tokenPrice, uint256 usdValue) internal pure returns (uint256) {\\n    /// LINK Example:\\n    /// tokenPrice:          8e18 -\\u003e $8/LINK, as 1e18 token amount is 1 LINK, worth 8 USD, or 8e18 with 18 decimals\\n    /// usdValue:           16e18 -\\u003e $16\\n    /// result:             16e18 * 1e18 / 8e18 -\\u003e 2e18 = 2 LINK\\n\\n    /// USDC Example:\\n    /// tokenPrice:         1e30 -\\u003e $1/USDC, as 1e18 token amount is 1e12 USDC, worth 1e12 USD, or 1e30 with 18 decimals\\n    /// usdValue:           5e18 -\\u003e $5\\n    /// result:             5e18 * 1e18 / 1e30 -\\u003e 5e6 = 5 USDC\\n    return (usdValue * 1e18) / tokenPrice;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/keystone/KeystoneFeedsPermissionHandler.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.19;\\n\\nimport {Ownable2StepMsgSender} from \\\"../shared/access/Ownable2StepMsgSender.sol\\\";\\n\\n/// @title Keystone Feeds Permission Handler\\n/// @notice This contract is designed to manage and validate permissions for accessing specific reports within a decentralized system.\\n/// @dev The contract uses mappings to keep track of report permissions associated with a unique report ID.\\nabstract contract KeystoneFeedsPermissionHandler is Ownable2StepMsgSender {\\n  /// @notice Holds the details for permissions of a report\\n  /// @dev Workflow names and report names are stored as bytes to optimize for gas efficiency.\\n  struct Permission {\\n    address forwarder; // ─────╮ The address of the forwarder (20 bytes)\\n    bytes10 workflowName; //   │ The name of the workflow in bytes10\\n    bytes2 reportName; // ─────╯ The name of the report in bytes2\\n    address workflowOwner; // ─╮ The address of the workflow owner (20 bytes)\\n    bool isAllowed; // ────────╯ Whether the report is allowed or not (1 byte)\\n  }\\n\\n  /// @notice Event emitted when report permissions are set\\n  event ReportPermissionSet(bytes32 indexed reportId, Permission permission);\\n\\n  /// @notice Error to be thrown when an unauthorized access attempt is made\\n  error ReportForwarderUnauthorized(address forwarder, address workflowOwner, bytes10 workflowName, bytes2 reportName);\\n\\n  /// @dev Mapping from a report ID to a boolean indicating whether the report is allowed or not\\n  mapping(bytes32 reportId =\\u003e bool isAllowed) internal s_allowedReports;\\n\\n  /// @notice Sets permissions for multiple reports\\n  /// @param permissions An array of Permission structs for which to set permissions\\n  /// @dev Emits a ReportPermissionSet event for each permission set\\n  function setReportPermissions(Permission[] memory permissions) external onlyOwner {\\n    for (uint256 i; i \\u003c permissions.length; ++i) {\\n      _setReportPermission(permissions[i]);\\n    }\\n  }\\n\\n  /// @dev Internal function to set a single report permission\\n  /// @param permission The Permission struct containing details about the permission to set\\n  /// @dev Emits a ReportPermissionSet event\\n  function _setReportPermission(Permission memory permission) internal {\\n    bytes32 reportId = _createReportId(\\n      permission.forwarder,\\n      permission.workflowOwner,\\n      permission.workflowName,\\n      permission.reportName\\n    );\\n    s_allowedReports[reportId] = permission.isAllowed;\\n    emit ReportPermissionSet(reportId, permission);\\n  }\\n\\n  /// @dev Internal view function to validate if a report is allowed for a given set of details\\n  /// @param forwarder The address of the forwarder\\n  /// @param workflowOwner The address of the workflow owner\\n  /// @param workflowName The name of the workflow in bytes10\\n  /// @param reportName The name of the report in bytes2\\n  /// @dev Reverts with Unauthorized if the report is not allowed\\n  function _validateReportPermission(\\n    address forwarder,\\n    address workflowOwner,\\n    bytes10 workflowName,\\n    bytes2 reportName\\n  ) internal view {\\n    bytes32 reportId = _createReportId(forwarder, workflowOwner, workflowName, reportName);\\n    if (!s_allowedReports[reportId]) {\\n      revert ReportForwarderUnauthorized(forwarder, workflowOwner, workflowName, reportName);\\n    }\\n  }\\n\\n  /// @notice Generates a unique report ID based on the provided parameters.\\n  /// @dev The report ID is computed using the Keccak-256 hash function over the encoded parameters.\\n  /// @param forwarder The address of the forwarder associated with the report.\\n  /// @param workflowOwner The address of the owner of the workflow.\\n  /// @param workflowName The name of the workflow, represented as a 10-byte value.\\n  /// @param reportName The name of the report, represented as a 2-byte value.\\n  /// @return reportId The computed unique report ID as a bytes32 value.\\n  function _createReportId(\\n    address forwarder,\\n    address workflowOwner,\\n    bytes10 workflowName,\\n    bytes2 reportName\\n  ) internal pure returns (bytes32 reportId) {\\n    return keccak256(abi.encode(forwarder, workflowOwner, workflowName, reportName));\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/keystone/interfaces/IReceiver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IERC165} from \\\"../../vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\\\";\\n\\n/// @title IReceiver - receives keystone reports\\n/// @notice Implementations must support the IReceiver interface through ERC165.\\ninterface IReceiver is IERC165 {\\n  /// @notice Handles incoming keystone reports.\\n  /// @dev If this function call reverts, it can be retried with a higher gas\\n  /// limit. The receiver is responsible for discarding stale reports.\\n  /// @param metadata Report's metadata.\\n  /// @param report Workflow report.\\n  function onReport(bytes calldata metadata, bytes calldata report) external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/keystone/lib/KeystoneFeedDefaultMetadataLib.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nlibrary KeystoneFeedDefaultMetadataLib {\\n  /**\\n   * Metadata Layout:\\n   *\\n   * +-------------------------------+--------------------+---------------------+---------------+\\n   * | 32 bytes (length prefix)      | 32 bytes           | 10 bytes            | 20 bytes      | 2 bytes        |\\n   * | (Not used in function)        | workflow_cid       | workflow_name       | workflow_owner| report_name    |\\n   * +-------------------------------+--------------------+---------------------+---------------+----------------+\\n   * |                               |                    |                     |               |                |\\n   * |          (Offset 0)           |     (Offset 32)    |     (Offset 64)     |  (Offset 74)  |  (Offset 94)   |\\n   * +-------------------------------+--------------------+---------------------+---------------+----------------+\\n   * @dev used to slice metadata bytes into workflowName, workflowOwner and report name\\n   */\\n  function _extractMetadataInfo(\\n    bytes memory metadata\\n  ) internal pure returns (bytes10 workflowName, address workflowOwner, bytes2 reportName) {\\n    // (first 32 bytes contain length of the byte array)\\n    // workflow_cid             // offset 32, size 32\\n    // workflow_name            // offset 64, size 10\\n    // workflow_owner           // offset 74, size 20\\n    // report_name              // offset 94, size  2\\n    assembly {\\n      // no shifting needed for bytes10 type\\n      workflowName := mload(add(metadata, 64))\\n      // shift right by 12 bytes to get the actual value\\n      workflowOwner := shr(mul(12, 8), mload(add(metadata, 74)))\\n      // no shifting needed for bytes2 type\\n      reportName := mload(add(metadata, 94))\\n    }\\n    return (workflowName, workflowOwner, reportName);\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/AuthorizedCallers.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2StepMsgSender} from \\\"./Ownable2StepMsgSender.sol\\\";\\nimport {EnumerableSet} from \\\"../../vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @title The AuthorizedCallers contract\\n/// @notice A contract that manages multiple authorized callers. Enables restricting access to certain functions to a set of addresses.\\ncontract AuthorizedCallers is Ownable2StepMsgSender {\\n  using EnumerableSet for EnumerableSet.AddressSet;\\n\\n  event AuthorizedCallerAdded(address caller);\\n  event AuthorizedCallerRemoved(address caller);\\n\\n  error UnauthorizedCaller(address caller);\\n  error ZeroAddressNotAllowed();\\n\\n  /// @notice Update args for changing the authorized callers\\n  struct AuthorizedCallerArgs {\\n    address[] addedCallers;\\n    address[] removedCallers;\\n  }\\n\\n  /// @dev Set of authorized callers\\n  EnumerableSet.AddressSet internal s_authorizedCallers;\\n\\n  /// @param authorizedCallers the authorized callers to set\\n  constructor(address[] memory authorizedCallers) {\\n    _applyAuthorizedCallerUpdates(\\n      AuthorizedCallerArgs({addedCallers: authorizedCallers, removedCallers: new address[](0)})\\n    );\\n  }\\n\\n  /// @return authorizedCallers Returns all authorized callers\\n  function getAllAuthorizedCallers() external view returns (address[] memory) {\\n    return s_authorizedCallers.values();\\n  }\\n\\n  /// @notice Updates the list of authorized callers\\n  /// @param authorizedCallerArgs Callers to add and remove. Removals are performed first.\\n  function applyAuthorizedCallerUpdates(AuthorizedCallerArgs memory authorizedCallerArgs) external onlyOwner {\\n    _applyAuthorizedCallerUpdates(authorizedCallerArgs);\\n  }\\n\\n  /// @notice Updates the list of authorized callers\\n  /// @param authorizedCallerArgs Callers to add and remove. Removals are performed first.\\n  function _applyAuthorizedCallerUpdates(AuthorizedCallerArgs memory authorizedCallerArgs) internal {\\n    address[] memory removedCallers = authorizedCallerArgs.removedCallers;\\n    for (uint256 i = 0; i \\u003c removedCallers.length; ++i) {\\n      address caller = removedCallers[i];\\n\\n      if (s_authorizedCallers.remove(caller)) {\\n        emit AuthorizedCallerRemoved(caller);\\n      }\\n    }\\n\\n    address[] memory addedCallers = authorizedCallerArgs.addedCallers;\\n    for (uint256 i = 0; i \\u003c addedCallers.length; ++i) {\\n      address caller = addedCallers[i];\\n\\n      if (caller == address(0)) {\\n        revert ZeroAddressNotAllowed();\\n      }\\n\\n      s_authorizedCallers.add(caller);\\n      emit AuthorizedCallerAdded(caller);\\n    }\\n  }\\n\\n  /// @notice Checks the sender and reverts if it is anyone other than a listed authorized caller.\\n  function _validateCaller() internal view {\\n    if (!s_authorizedCallers.contains(msg.sender)) {\\n      revert UnauthorizedCaller(msg.sender);\\n    }\\n  }\\n\\n  /// @notice Checks the sender and reverts if it is anyone other than a listed authorized caller.\\n  modifier onlyAuthorizedCallers() {\\n    _validateCaller();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/AggregatorV3Interface.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n// solhint-disable-next-line interface-starts-with-i\\ninterface AggregatorV3Interface {\\n  function decimals() external view returns (uint8);\\n\\n  function description() external view returns (string memory);\\n\\n  function version() external view returns (uint256);\\n\\n  function getRoundData(\\n    uint80 _roundId\\n  ) external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound);\\n\\n  function latestRoundData()\\n    external\\n    view\\n    returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n  // To implement this library for multiple types with as little code\\n  // repetition as possible, we write it in terms of a generic Set type with\\n  // bytes32 values.\\n  // The Set implementation uses private functions, and user-facing\\n  // implementations (such as AddressSet) are just wrappers around the\\n  // underlying Set.\\n  // This means that we can only create new EnumerableSets for types that fit\\n  // in bytes32.\\n\\n  struct Set {\\n    // Storage of set values\\n    bytes32[] _values;\\n    // Position of the value in the `values` array, plus 1 because index 0\\n    // means a value is not in the set.\\n    mapping(bytes32 =\\u003e uint256) _indexes;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function _add(Set storage set, bytes32 value) private returns (bool) {\\n    if (!_contains(set, value)) {\\n      set._values.push(value);\\n      // The value is stored at length-1, but we add 1 to all indexes\\n      // and use 0 as a sentinel value\\n      set._indexes[value] = set._values.length;\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function _remove(Set storage set, bytes32 value) private returns (bool) {\\n    // We read and store the value's index to prevent multiple reads from the same storage slot\\n    uint256 valueIndex = set._indexes[value];\\n\\n    if (valueIndex != 0) {\\n      // Equivalent to contains(set, value)\\n      // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n      // the array, and then remove the last element (sometimes called as 'swap and pop').\\n      // This modifies the order of the array, as noted in {at}.\\n\\n      uint256 toDeleteIndex = valueIndex - 1;\\n      uint256 lastIndex = set._values.length - 1;\\n\\n      if (lastIndex != toDeleteIndex) {\\n        bytes32 lastValue = set._values[lastIndex];\\n\\n        // Move the last value to the index where the value to delete is\\n        set._values[toDeleteIndex] = lastValue;\\n        // Update the index for the moved value\\n        set._indexes[lastValue] = valueIndex; // Replace lastValue's index to valueIndex\\n      }\\n\\n      // Delete the slot where the moved value was stored\\n      set._values.pop();\\n\\n      // Delete the index for the deleted slot\\n      delete set._indexes[value];\\n\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n    return set._indexes[value] != 0;\\n  }\\n\\n  /**\\n   * @dev Returns the number of values on the set. O(1).\\n   */\\n  function _length(Set storage set) private view returns (uint256) {\\n    return set._values.length;\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n    return set._values[index];\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function _values(Set storage set) private view returns (bytes32[] memory) {\\n    return set._values;\\n  }\\n\\n  // Bytes32Set\\n\\n  struct Bytes32Set {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _add(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _remove(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n    return _contains(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(Bytes32Set storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n    return _at(set._inner, index);\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    bytes32[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // AddressSet\\n\\n  struct AddressSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(AddressSet storage set, address value) internal returns (bool) {\\n    return _add(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(AddressSet storage set, address value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(AddressSet storage set, address value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(AddressSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n    return address(uint160(uint256(_at(set._inner, index))));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(AddressSet storage set) internal view returns (address[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    address[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // UintSet\\n\\n  struct UintSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _add(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(UintSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n    return uint256(_at(set._inner, index));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(UintSet storage set) internal view returns (uint256[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    uint256[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/interfaces/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC165} from \\\"../utils/introspection/IERC165.sol\\\";\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```solidity\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n    // To implement this library for multiple types with as little code\\n    // repetition as possible, we write it in terms of a generic Set type with\\n    // bytes32 values.\\n    // The Set implementation uses private functions, and user-facing\\n    // implementations (such as AddressSet) are just wrappers around the\\n    // underlying Set.\\n    // This means that we can only create new EnumerableSets for types that fit\\n    // in bytes32.\\n\\n    struct Set {\\n        // Storage of set values\\n        bytes32[] _values;\\n        // Position is the index of the value in the `values` array plus 1.\\n        // Position 0 is used to mean a value is not in the set.\\n        mapping(bytes32 value =\\u003e uint256) _positions;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function _add(Set storage set, bytes32 value) private returns (bool) {\\n        if (!_contains(set, value)) {\\n            set._values.push(value);\\n            // The value is stored at length-1, but we add 1 to all indexes\\n            // and use 0 as a sentinel value\\n            set._positions[value] = set._values.length;\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function _remove(Set storage set, bytes32 value) private returns (bool) {\\n        // We cache the value's position to prevent multiple reads from the same storage slot\\n        uint256 position = set._positions[value];\\n\\n        if (position != 0) {\\n            // Equivalent to contains(set, value)\\n            // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n            // the array, and then remove the last element (sometimes called as 'swap and pop').\\n            // This modifies the order of the array, as noted in {at}.\\n\\n            uint256 valueIndex = position - 1;\\n            uint256 lastIndex = set._values.length - 1;\\n\\n            if (valueIndex != lastIndex) {\\n                bytes32 lastValue = set._values[lastIndex];\\n\\n                // Move the lastValue to the index where the value to delete is\\n                set._values[valueIndex] = lastValue;\\n                // Update the tracked position of the lastValue (that was just moved)\\n                set._positions[lastValue] = position;\\n            }\\n\\n            // Delete the slot where the moved value was stored\\n            set._values.pop();\\n\\n            // Delete the tracked position for the deleted slot\\n            delete set._positions[value];\\n\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n        return set._positions[value] != 0;\\n    }\\n\\n    /**\\n     * @dev Returns the number of values on the set. O(1).\\n     */\\n    function _length(Set storage set) private view returns (uint256) {\\n        return set._values.length;\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n        return set._values[index];\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function _values(Set storage set) private view returns (bytes32[] memory) {\\n        return set._values;\\n    }\\n\\n    // Bytes32Set\\n\\n    struct Bytes32Set {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _add(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _remove(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n        return _contains(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(Bytes32Set storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n        return _at(set._inner, index);\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        bytes32[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // AddressSet\\n\\n    struct AddressSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(AddressSet storage set, address value) internal returns (bool) {\\n        return _add(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(AddressSet storage set, address value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(AddressSet storage set, address value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(AddressSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n        return address(uint160(uint256(_at(set._inner, index))));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(AddressSet storage set) internal view returns (address[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        address[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // UintSet\\n\\n    struct UintSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _add(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(UintSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n        return uint256(_at(set._inner, index));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(UintSet storage set) internal view returns (uint256[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        uint256[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n}\\n\"}}}"
