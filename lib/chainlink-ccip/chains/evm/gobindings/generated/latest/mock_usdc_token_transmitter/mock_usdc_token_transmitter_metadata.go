// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package mock_usdc_token_transmitter

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/pools/USDC/IMessageTransmitter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/test/mocks/MockE2EUSDCTransmitter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/test/mocks/interfaces/IMessageTransmitterWithRelay.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IGetCCIPAdmin.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/BurnMintERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/access/AccessControl.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/access/IAccessControl.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/ERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Context.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Strings.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/ERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/math/Math.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/pools/USDC/IMessageTransmitter.sol\":{\"content\":\"/*\\n * Copyright (c) 2022, Circle Internet Financial Limited.\\n *\\n * Licensed under the Apache License, Version 2.0 (the \\\"License\\\");\\n * you may not use this file except in compliance with the License.\\n * You may obtain a copy of the License at\\n *\\n * http://www.apache.org/licenses/LICENSE-2.0\\n *\\n * Unless required by applicable law or agreed to in writing, software\\n * distributed under the License is distributed on an \\\"AS IS\\\" BASIS,\\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n * See the License for the specific language governing permissions and\\n * limitations under the License.\\n */\\npragma solidity ^0.8.0;\\n\\ninterface IMessageTransmitter {\\n  /// @notice Unlocks USDC tokens on the destination chain\\n  /// @param message The original message on the source chain\\n  ///     * Message format:\\n  ///     * Field                 Bytes      Type       Index\\n  ///     * version               4          uint32     0\\n  ///     * sourceDomain          4          uint32     4\\n  ///     * destinationDomain     4          uint32     8\\n  ///     * nonce                 8          uint64     12\\n  ///     * sender                32         bytes32    20\\n  ///     * recipient             32         bytes32    52\\n  ///     * destinationCaller     32         bytes32    84\\n  ///     * messageBody           dynamic    bytes      116\\n  /// param attestation A valid attestation is the concatenated 65-byte signature(s) of\\n  /// exactly `thresholdSignature` signatures, in increasing order of attester address.\\n  /// ***If the attester addresses recovered from signatures are not in increasing order,\\n  /// signature verification will fail.***\\n  /// If incorrect number of signatures or duplicate signatures are supplied,\\n  /// signature verification will fail.\\n  function receiveMessage(bytes calldata message, bytes calldata attestation) external returns (bool success);\\n\\n  /// Returns domain of chain on which the contract is deployed.\\n  /// @dev immutable\\n  function localDomain() external view returns (uint32);\\n\\n  /// Returns message format version.\\n  /// @dev immutable\\n  function version() external view returns (uint32);\\n}\\n\"},\"contracts/test/mocks/MockE2EUSDCTransmitter.sol\":{\"content\":\"/*\\n * Copyright (c) 2022, Circle Internet Financial Limited.\\n *\\n * Licensed under the Apache License, Version 2.0 (the \\\"License\\\");\\n * you may not use this file except in compliance with the License.\\n * You may obtain a copy of the License at\\n *\\n * http://www.apache.org/licenses/LICENSE-2.0\\n *\\n * Unless required by applicable law or agreed to in writing, software\\n * distributed under the License is distributed on an \\\"AS IS\\\" BASIS,\\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n * See the License for the specific language governing permissions and\\n * limitations under the License.\\n */\\npragma solidity ^0.8.0;\\n\\nimport {IMessageTransmitterWithRelay} from \\\"./interfaces/IMessageTransmitterWithRelay.sol\\\";\\n\\nimport {BurnMintERC20} from \\\"@chainlink/contracts/src/v0.8/shared/token/ERC20/BurnMintERC20.sol\\\";\\n\\n// solhint-disable\\ncontract MockE2EUSDCTransmitter is IMessageTransmitterWithRelay {\\n  // Indicated whether the receiveMessage() call should succeed.\\n  bool public s_shouldSucceed;\\n  uint32 private immutable i_version;\\n  uint32 private immutable i_localDomain;\\n  // Next available nonce from this source domain\\n  uint64 public nextAvailableNonce;\\n\\n  BurnMintERC20 internal immutable i_token;\\n\\n  /**\\n   * @notice Emitted when a new message is dispatched\\n   * @param message Raw bytes of message\\n   */\\n  event MessageSent(bytes message);\\n\\n  constructor(uint32 _version, uint32 _localDomain, address token) {\\n    i_version = _version;\\n    i_localDomain = _localDomain;\\n    s_shouldSucceed = true;\\n\\n    i_token = BurnMintERC20(token);\\n  }\\n\\n  /// @param message The original message on the source chain\\n  ///     * Message format:\\n  ///     * Field                 Bytes      Type       Index\\n  ///     * version               4          uint32     0\\n  ///     * sourceDomain          4          uint32     4\\n  ///     * destinationDomain     4          uint32     8\\n  ///     * nonce                 8          uint64     12\\n  ///     * sender                32         bytes32    20\\n  ///     * recipient             32         bytes32    52\\n  ///     * destinationCaller     32         bytes32    84\\n  ///     * messageBody           dynamic    bytes      116\\n  function receiveMessage(bytes calldata message, bytes calldata) external returns (bool success) {\\n    // The receiver of the funds is the _mintRecipient in the following encoded format\\n    //   function _formatMessage(\\n    //    uint32 _version,             4\\n    //    bytes32 _burnToken,         32\\n    //    bytes32 _mintRecipient,     32, first 12 empty for EVM addresses\\n    //    uint256 _amount,\\n    //    bytes32 _messageSender\\n    //  ) internal pure returns (bytes memory) {\\n    //    return abi.encodePacked(_version, _burnToken, _mintRecipient, _amount, _messageSender);\\n    //  }\\n    address recipient = address(bytes20(message[116 + 36 + 12:116 + 36 + 12 + 20]));\\n    // We always mint 1 token to not complicate the test.\\n    i_token.mint(recipient, 1);\\n\\n    return s_shouldSucceed;\\n  }\\n\\n  function setShouldSucceed(\\n    bool shouldSucceed\\n  ) external {\\n    s_shouldSucceed = shouldSucceed;\\n  }\\n\\n  function version() external view returns (uint32) {\\n    return i_version;\\n  }\\n\\n  function localDomain() external view returns (uint32) {\\n    return i_localDomain;\\n  }\\n\\n  /**\\n   * This is based on similar function in https://github.com/circlefin/evm-cctp-contracts/blob/master/src/MessageTransmitter.sol\\n   * @notice Send the message to the destination domain and recipient\\n   * @dev Increment nonce, format the message, and emit `MessageSent` event with message information.\\n   * @param destinationDomain Domain of destination chain\\n   * @param recipient Address of message recipient on destination chain as bytes32\\n   * @param messageBody Raw bytes content of message\\n   * @return nonce reserved by message\\n   */\\n  function sendMessage(\\n    uint32 destinationDomain,\\n    bytes32 recipient,\\n    bytes calldata messageBody\\n  ) external returns (uint64) {\\n    bytes32 _emptyDestinationCaller = bytes32(0);\\n    uint64 _nonce = _reserveAndIncrementNonce();\\n    bytes32 _messageSender = bytes32(uint256(uint160((msg.sender))));\\n\\n    _sendMessage(destinationDomain, recipient, _emptyDestinationCaller, _messageSender, _nonce, messageBody);\\n\\n    return _nonce;\\n  }\\n\\n  /**\\n   * @notice Send the message to the destination domain and recipient, for a specified `destinationCaller` on the\\n   * destination domain.\\n   * @dev Increment nonce, format the message, and emit `MessageSent` event with message information.\\n   * WARNING: if the `destinationCaller` does not represent a valid address, then it will not be possible\\n   * to broadcast the message on the destination domain. This is an advanced feature, and the standard\\n   * sendMessage() should be preferred for use cases where a specific destination caller is not required.\\n   * @param destinationDomain Domain of destination chain\\n   * @param recipient Address of message recipient on destination domain as bytes32\\n   * @param destinationCaller caller on the destination domain, as bytes32\\n   * @param messageBody Raw bytes content of message\\n   * @return nonce reserved by message\\n   */\\n  function sendMessageWithCaller(\\n    uint32 destinationDomain,\\n    bytes32 recipient,\\n    bytes32 destinationCaller,\\n    bytes calldata messageBody\\n  ) external returns (uint64) {\\n    require(destinationCaller != bytes32(0), \\\"Destination caller must be nonzero\\\");\\n\\n    uint64 _nonce = _reserveAndIncrementNonce();\\n    bytes32 _messageSender = bytes32(uint256(uint160((msg.sender))));\\n\\n    _sendMessage(destinationDomain, recipient, destinationCaller, _messageSender, _nonce, messageBody);\\n\\n    return _nonce;\\n  }\\n\\n  /**\\n   * Reserve and increment next available nonce\\n   * @return nonce reserved\\n   */\\n  function _reserveAndIncrementNonce() internal returns (uint64) {\\n    uint64 _nonceReserved = nextAvailableNonce;\\n    nextAvailableNonce = nextAvailableNonce + 1;\\n    return _nonceReserved;\\n  }\\n\\n  /**\\n   * @notice Send the message to the destination domain and recipient. If `_destinationCaller` is not equal to bytes32(0),\\n   * the message can only be received on the destination chain when called by `_destinationCaller`.\\n   * @dev Format the message and emit `MessageSent` event with message information.\\n   * @param _destinationDomain Domain of destination chain\\n   * @param _recipient Address of message recipient on destination domain as bytes32\\n   * @param _destinationCaller caller on the destination domain, as bytes32\\n   * @param _sender message sender, as bytes32\\n   * @param _nonce nonce reserved for message\\n   * @param _messageBody Raw bytes content of message\\n   */\\n  function _sendMessage(\\n    uint32 _destinationDomain,\\n    bytes32 _recipient,\\n    bytes32 _destinationCaller,\\n    bytes32 _sender,\\n    uint64 _nonce,\\n    bytes calldata _messageBody\\n  ) internal {\\n    require(_recipient != bytes32(0), \\\"Recipient must be nonzero\\\");\\n    // serialize message\\n    bytes memory _message = abi.encodePacked(\\n      i_version, i_localDomain, _destinationDomain, _nonce, _sender, _recipient, _destinationCaller, _messageBody\\n    );\\n\\n    // Emit MessageSent event\\n    emit MessageSent(_message);\\n  }\\n}\\n\"},\"contracts/test/mocks/interfaces/IMessageTransmitterWithRelay.sol\":{\"content\":\"/*\\n * Copyright (c) 2022, Circle Internet Financial Limited.\\n *\\n * Licensed under the Apache License, Version 2.0 (the \\\"License\\\");\\n * you may not use this file except in compliance with the License.\\n * You may obtain a copy of the License at\\n *\\n * http://www.apache.org/licenses/LICENSE-2.0\\n *\\n * Unless required by applicable law or agreed to in writing, software\\n * distributed under the License is distributed on an \\\"AS IS\\\" BASIS,\\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n * See the License for the specific language governing permissions and\\n * limitations under the License.\\n */\\npragma solidity ^0.8.0;\\n\\nimport {IMessageTransmitter} from \\\"../../../pools/USDC/IMessageTransmitter.sol\\\";\\n\\n// This follows https://github.com/circlefin/evm-cctp-contracts/blob/master/src/interfaces/IMessageTransmitter.sol\\ninterface IMessageTransmitterWithRelay is IMessageTransmitter {\\n  /**\\n   * @notice Sends an outgoing message from the source domain.\\n   * @dev Increment nonce, format the message, and emit `MessageSent` event with message information.\\n   * @param destinationDomain Domain of destination chain\\n   * @param recipient Address of message recipient on destination domain as bytes32\\n   * @param messageBody Raw bytes content of message\\n   * @return nonce reserved by message\\n   */\\n  function sendMessage(\\n    uint32 destinationDomain,\\n    bytes32 recipient,\\n    bytes calldata messageBody\\n  ) external returns (uint64);\\n\\n  /**\\n   * @notice Sends an outgoing message from the source domain, with a specified caller on the\\n   * destination domain.\\n   * @dev Increment nonce, format the message, and emit `MessageSent` event with message information.\\n   * WARNING: if the `destinationCaller` does not represent a valid address as bytes32, then it will not be possible\\n   * to broadcast the message on the destination domain. This is an advanced feature, and the standard\\n   * sendMessage() should be preferred for use cases where a specific destination caller is not required.\\n   * @param destinationDomain Domain of destination chain\\n   * @param recipient Address of message recipient on destination domain as bytes32\\n   * @param destinationCaller caller on the destination domain, as bytes32\\n   * @param messageBody Raw bytes content of message\\n   * @return nonce reserved by message\\n   */\\n  function sendMessageWithCaller(\\n    uint32 destinationDomain,\\n    bytes32 recipient,\\n    bytes32 destinationCaller,\\n    bytes calldata messageBody\\n  ) external returns (uint64);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IGetCCIPAdmin.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IGetCCIPAdmin {\\n  /// @notice Returns the admin of the token.\\n  /// @dev This method is named to never conflict with existing methods.\\n  function getCCIPAdmin() external view returns (address);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/BurnMintERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IGetCCIPAdmin} from \\\"../../../shared/interfaces/IGetCCIPAdmin.sol\\\";\\nimport {IBurnMintERC20} from \\\"../../../shared/token/ERC20/IBurnMintERC20.sol\\\";\\n\\nimport {AccessControl} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/access/AccessControl.sol\\\";\\nimport {IAccessControl} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/access/IAccessControl.sol\\\";\\nimport {ERC20} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/ERC20.sol\\\";\\nimport {IERC20} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\nimport {ERC20Burnable} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/ERC20Burnable.sol\\\";\\nimport {IERC165} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\\\";\\n\\n/// @notice A basic ERC20 compatible token contract with burn and minting roles.\\n/// @dev The total supply can be limited during deployment.\\ncontract BurnMintERC20 is IBurnMintERC20, IGetCCIPAdmin, IERC165, ERC20Burnable, AccessControl {\\n  error MaxSupplyExceeded(uint256 supplyAfterMint);\\n  error InvalidRecipient(address recipient);\\n\\n  event CCIPAdminTransferred(address indexed previousAdmin, address indexed newAdmin);\\n\\n  /// @dev The number of decimals for the token\\n  uint8 internal immutable i_decimals;\\n\\n  /// @dev The maximum supply of the token, 0 if unlimited\\n  uint256 internal immutable i_maxSupply;\\n\\n  /// @dev the CCIPAdmin can be used to register with the CCIP token admin registry, but has no other special powers,\\n  /// and can only be transferred by the owner.\\n  address internal s_ccipAdmin;\\n\\n  bytes32 public constant MINTER_ROLE = keccak256(\\\"MINTER_ROLE\\\");\\n  bytes32 public constant BURNER_ROLE = keccak256(\\\"BURNER_ROLE\\\");\\n\\n  /// @dev the underscores in parameter names are used to suppress compiler warnings about shadowing ERC20 functions\\n  constructor(\\n    string memory name,\\n    string memory symbol,\\n    uint8 decimals_,\\n    uint256 maxSupply_,\\n    uint256 preMint\\n  ) ERC20(name, symbol) {\\n    i_decimals = decimals_;\\n    i_maxSupply = maxSupply_;\\n\\n    s_ccipAdmin = msg.sender;\\n\\n    // Mint the initial supply to the new Owner, saving gas by not calling if the mint amount is zero\\n    if (preMint != 0) _mint(msg.sender, preMint);\\n\\n    // Set up the owner as the initial minter and burner\\n    _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);\\n  }\\n\\n  /// @inheritdoc IERC165\\n  function supportsInterface(bytes4 interfaceId) public pure virtual override(AccessControl, IERC165) returns (bool) {\\n    return\\n      interfaceId == type(IERC20).interfaceId ||\\n      interfaceId == type(IBurnMintERC20).interfaceId ||\\n      interfaceId == type(IERC165).interfaceId ||\\n      interfaceId == type(IAccessControl).interfaceId ||\\n      interfaceId == type(IGetCCIPAdmin).interfaceId;\\n  }\\n\\n  // ================================================================\\n  // │                            ERC20                             │\\n  // ================================================================\\n\\n  /// @dev Returns the number of decimals used in its user representation.\\n  function decimals() public view virtual override returns (uint8) {\\n    return i_decimals;\\n  }\\n\\n  /// @dev Returns the max supply of the token, 0 if unlimited.\\n  function maxSupply() public view virtual returns (uint256) {\\n    return i_maxSupply;\\n  }\\n\\n  /// @dev Uses OZ ERC20 _transfer to disallow sending to address(0).\\n  /// @dev Disallows sending to address(this)\\n  function _transfer(address from, address to, uint256 amount) internal virtual override {\\n    if (to == address(this)) revert InvalidRecipient(to);\\n\\n    super._transfer(from, to, amount);\\n  }\\n\\n  /// @dev Uses OZ ERC20 _approve to disallow approving for address(0).\\n  /// @dev Disallows approving for address(this)\\n  function _approve(address owner, address spender, uint256 amount) internal virtual override {\\n    if (spender == address(this)) revert InvalidRecipient(spender);\\n\\n    super._approve(owner, spender, amount);\\n  }\\n\\n  // ================================================================\\n  // │                      Burning \\u0026 minting                       │\\n  // ================================================================\\n\\n  /// @inheritdoc ERC20Burnable\\n  /// @dev Uses OZ ERC20 _burn to disallow burning from address(0).\\n  /// @dev Decreases the total supply.\\n  function burn(uint256 amount) public override(IBurnMintERC20, ERC20Burnable) onlyRole(BURNER_ROLE) {\\n    super.burn(amount);\\n  }\\n\\n  /// @inheritdoc IBurnMintERC20\\n  /// @dev Alias for BurnFrom for compatibility with the older naming convention.\\n  /// @dev Uses burnFrom for all validation \\u0026 logic.\\n  function burn(address account, uint256 amount) public virtual override {\\n    burnFrom(account, amount);\\n  }\\n\\n  /// @inheritdoc ERC20Burnable\\n  /// @dev Uses OZ ERC20 _burn to disallow burning from address(0).\\n  /// @dev Decreases the total supply.\\n  function burnFrom(\\n    address account,\\n    uint256 amount\\n  ) public override(IBurnMintERC20, ERC20Burnable) onlyRole(BURNER_ROLE) {\\n    super.burnFrom(account, amount);\\n  }\\n\\n  /// @inheritdoc IBurnMintERC20\\n  /// @dev Uses OZ ERC20 _mint to disallow minting to address(0).\\n  /// @dev Disallows minting to address(this)\\n  /// @dev Increases the total supply.\\n  function mint(address account, uint256 amount) external override onlyRole(MINTER_ROLE) {\\n    if (account == address(this)) revert InvalidRecipient(account);\\n    if (i_maxSupply != 0 \\u0026\\u0026 totalSupply() + amount \\u003e i_maxSupply) revert MaxSupplyExceeded(totalSupply() + amount);\\n\\n    _mint(account, amount);\\n  }\\n\\n  // ================================================================\\n  // │                            Roles                             │\\n  // ================================================================\\n\\n  /// @notice grants both mint and burn roles to `burnAndMinter`.\\n  /// @dev calls public functions so this function does not require\\n  /// access controls. This is handled in the inner functions.\\n  function grantMintAndBurnRoles(address burnAndMinter) external {\\n    grantRole(MINTER_ROLE, burnAndMinter);\\n    grantRole(BURNER_ROLE, burnAndMinter);\\n  }\\n\\n  /// @notice Returns the current CCIPAdmin\\n  function getCCIPAdmin() external view returns (address) {\\n    return s_ccipAdmin;\\n  }\\n\\n  /// @notice Transfers the CCIPAdmin role to a new address\\n  /// @dev only the owner can call this function, NOT the current ccipAdmin, and 1-step ownership transfer is used.\\n  /// @param newAdmin The address to transfer the CCIPAdmin role to. Setting to address(0) is a valid way to revoke\\n  /// the role\\n  function setCCIPAdmin(address newAdmin) external onlyRole(DEFAULT_ADMIN_ROLE) {\\n    address currentAdmin = s_ccipAdmin;\\n\\n    s_ccipAdmin = newAdmin;\\n\\n    emit CCIPAdminTransferred(currentAdmin, newAdmin);\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IERC20} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\n\\ninterface IBurnMintERC20 is IERC20 {\\n  /// @notice Mints new tokens for a given address.\\n  /// @param account The address to mint the new tokens to.\\n  /// @param amount The number of tokens to be minted.\\n  /// @dev this function increases the total supply.\\n  function mint(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from the sender.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burnFrom(address account, uint256 amount) external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/access/AccessControl.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (access/AccessControl.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IAccessControl.sol\\\";\\nimport \\\"../utils/Context.sol\\\";\\nimport \\\"../utils/Strings.sol\\\";\\nimport \\\"../utils/introspection/ERC165.sol\\\";\\n\\n/**\\n * @dev Contract module that allows children to implement role-based access\\n * control mechanisms. This is a lightweight version that doesn't allow enumerating role\\n * members except through off-chain means by accessing the contract event logs. Some\\n * applications may benefit from on-chain enumerability, for those cases see\\n * {AccessControlEnumerable}.\\n *\\n * Roles are referred to by their `bytes32` identifier. These should be exposed\\n * in the external API and be unique. The best way to achieve this is by\\n * using `public constant` hash digests:\\n *\\n * ```\\n * bytes32 public constant MY_ROLE = keccak256(\\\"MY_ROLE\\\");\\n * ```\\n *\\n * Roles can be used to represent a set of permissions. To restrict access to a\\n * function call, use {hasRole}:\\n *\\n * ```\\n * function foo() public {\\n *     require(hasRole(MY_ROLE, msg.sender));\\n *     ...\\n * }\\n * ```\\n *\\n * Roles can be granted and revoked dynamically via the {grantRole} and\\n * {revokeRole} functions. Each role has an associated admin role, and only\\n * accounts that have a role's admin role can call {grantRole} and {revokeRole}.\\n *\\n * By default, the admin role for all roles is `DEFAULT_ADMIN_ROLE`, which means\\n * that only accounts with this role will be able to grant or revoke other\\n * roles. More complex role relationships can be created by using\\n * {_setRoleAdmin}.\\n *\\n * WARNING: The `DEFAULT_ADMIN_ROLE` is also its own admin: it has permission to\\n * grant and revoke this role. Extra precautions should be taken to secure\\n * accounts that have been granted it.\\n */\\nabstract contract AccessControl is Context, IAccessControl, ERC165 {\\n    struct RoleData {\\n        mapping(address =\\u003e bool) members;\\n        bytes32 adminRole;\\n    }\\n\\n    mapping(bytes32 =\\u003e RoleData) private _roles;\\n\\n    bytes32 public constant DEFAULT_ADMIN_ROLE = 0x00;\\n\\n    /**\\n     * @dev Modifier that checks that an account has a specific role. Reverts\\n     * with a standardized message including the required role.\\n     *\\n     * The format of the revert reason is given by the following regular expression:\\n     *\\n     *  /^AccessControl: account (0x[0-9a-f]{40}) is missing role (0x[0-9a-f]{64})$/\\n     *\\n     * _Available since v4.1._\\n     */\\n    modifier onlyRole(bytes32 role) {\\n        _checkRole(role);\\n        _;\\n    }\\n\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n        return interfaceId == type(IAccessControl).interfaceId || super.supportsInterface(interfaceId);\\n    }\\n\\n    /**\\n     * @dev Returns `true` if `account` has been granted `role`.\\n     */\\n    function hasRole(bytes32 role, address account) public view virtual override returns (bool) {\\n        return _roles[role].members[account];\\n    }\\n\\n    /**\\n     * @dev Revert with a standard message if `_msgSender()` is missing `role`.\\n     * Overriding this function changes the behavior of the {onlyRole} modifier.\\n     *\\n     * Format of the revert message is described in {_checkRole}.\\n     *\\n     * _Available since v4.6._\\n     */\\n    function _checkRole(bytes32 role) internal view virtual {\\n        _checkRole(role, _msgSender());\\n    }\\n\\n    /**\\n     * @dev Revert with a standard message if `account` is missing `role`.\\n     *\\n     * The format of the revert reason is given by the following regular expression:\\n     *\\n     *  /^AccessControl: account (0x[0-9a-f]{40}) is missing role (0x[0-9a-f]{64})$/\\n     */\\n    function _checkRole(bytes32 role, address account) internal view virtual {\\n        if (!hasRole(role, account)) {\\n            revert(\\n                string(\\n                    abi.encodePacked(\\n                        \\\"AccessControl: account \\\",\\n                        Strings.toHexString(account),\\n                        \\\" is missing role \\\",\\n                        Strings.toHexString(uint256(role), 32)\\n                    )\\n                )\\n            );\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the admin role that controls `role`. See {grantRole} and\\n     * {revokeRole}.\\n     *\\n     * To change a role's admin, use {_setRoleAdmin}.\\n     */\\n    function getRoleAdmin(bytes32 role) public view virtual override returns (bytes32) {\\n        return _roles[role].adminRole;\\n    }\\n\\n    /**\\n     * @dev Grants `role` to `account`.\\n     *\\n     * If `account` had not been already granted `role`, emits a {RoleGranted}\\n     * event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must have ``role``'s admin role.\\n     *\\n     * May emit a {RoleGranted} event.\\n     */\\n    function grantRole(bytes32 role, address account) public virtual override onlyRole(getRoleAdmin(role)) {\\n        _grantRole(role, account);\\n    }\\n\\n    /**\\n     * @dev Revokes `role` from `account`.\\n     *\\n     * If `account` had been granted `role`, emits a {RoleRevoked} event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must have ``role``'s admin role.\\n     *\\n     * May emit a {RoleRevoked} event.\\n     */\\n    function revokeRole(bytes32 role, address account) public virtual override onlyRole(getRoleAdmin(role)) {\\n        _revokeRole(role, account);\\n    }\\n\\n    /**\\n     * @dev Revokes `role` from the calling account.\\n     *\\n     * Roles are often managed via {grantRole} and {revokeRole}: this function's\\n     * purpose is to provide a mechanism for accounts to lose their privileges\\n     * if they are compromised (such as when a trusted device is misplaced).\\n     *\\n     * If the calling account had been revoked `role`, emits a {RoleRevoked}\\n     * event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must be `account`.\\n     *\\n     * May emit a {RoleRevoked} event.\\n     */\\n    function renounceRole(bytes32 role, address account) public virtual override {\\n        require(account == _msgSender(), \\\"AccessControl: can only renounce roles for self\\\");\\n\\n        _revokeRole(role, account);\\n    }\\n\\n    /**\\n     * @dev Grants `role` to `account`.\\n     *\\n     * If `account` had not been already granted `role`, emits a {RoleGranted}\\n     * event. Note that unlike {grantRole}, this function doesn't perform any\\n     * checks on the calling account.\\n     *\\n     * May emit a {RoleGranted} event.\\n     *\\n     * [WARNING]\\n     * ====\\n     * This function should only be called from the constructor when setting\\n     * up the initial roles for the system.\\n     *\\n     * Using this function in any other way is effectively circumventing the admin\\n     * system imposed by {AccessControl}.\\n     * ====\\n     *\\n     * NOTE: This function is deprecated in favor of {_grantRole}.\\n     */\\n    function _setupRole(bytes32 role, address account) internal virtual {\\n        _grantRole(role, account);\\n    }\\n\\n    /**\\n     * @dev Sets `adminRole` as ``role``'s admin role.\\n     *\\n     * Emits a {RoleAdminChanged} event.\\n     */\\n    function _setRoleAdmin(bytes32 role, bytes32 adminRole) internal virtual {\\n        bytes32 previousAdminRole = getRoleAdmin(role);\\n        _roles[role].adminRole = adminRole;\\n        emit RoleAdminChanged(role, previousAdminRole, adminRole);\\n    }\\n\\n    /**\\n     * @dev Grants `role` to `account`.\\n     *\\n     * Internal function without access restriction.\\n     *\\n     * May emit a {RoleGranted} event.\\n     */\\n    function _grantRole(bytes32 role, address account) internal virtual {\\n        if (!hasRole(role, account)) {\\n            _roles[role].members[account] = true;\\n            emit RoleGranted(role, account, _msgSender());\\n        }\\n    }\\n\\n    /**\\n     * @dev Revokes `role` from `account`.\\n     *\\n     * Internal function without access restriction.\\n     *\\n     * May emit a {RoleRevoked} event.\\n     */\\n    function _revokeRole(bytes32 role, address account) internal virtual {\\n        if (hasRole(role, account)) {\\n            _roles[role].members[account] = false;\\n            emit RoleRevoked(role, account, _msgSender());\\n        }\\n    }\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/access/IAccessControl.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (access/IAccessControl.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev External interface of AccessControl declared to support ERC165 detection.\\n */\\ninterface IAccessControl {\\n    /**\\n     * @dev Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole`\\n     *\\n     * `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite\\n     * {RoleAdminChanged} not being emitted signaling this.\\n     *\\n     * _Available since v3.1._\\n     */\\n    event RoleAdminChanged(bytes32 indexed role, bytes32 indexed previousAdminRole, bytes32 indexed newAdminRole);\\n\\n    /**\\n     * @dev Emitted when `account` is granted `role`.\\n     *\\n     * `sender` is the account that originated the contract call, an admin role\\n     * bearer except when using {AccessControl-_setupRole}.\\n     */\\n    event RoleGranted(bytes32 indexed role, address indexed account, address indexed sender);\\n\\n    /**\\n     * @dev Emitted when `account` is revoked `role`.\\n     *\\n     * `sender` is the account that originated the contract call:\\n     *   - if using `revokeRole`, it is the admin role bearer\\n     *   - if using `renounceRole`, it is the role bearer (i.e. `account`)\\n     */\\n    event RoleRevoked(bytes32 indexed role, address indexed account, address indexed sender);\\n\\n    /**\\n     * @dev Returns `true` if `account` has been granted `role`.\\n     */\\n    function hasRole(bytes32 role, address account) external view returns (bool);\\n\\n    /**\\n     * @dev Returns the admin role that controls `role`. See {grantRole} and\\n     * {revokeRole}.\\n     *\\n     * To change a role's admin, use {AccessControl-_setRoleAdmin}.\\n     */\\n    function getRoleAdmin(bytes32 role) external view returns (bytes32);\\n\\n    /**\\n     * @dev Grants `role` to `account`.\\n     *\\n     * If `account` had not been already granted `role`, emits a {RoleGranted}\\n     * event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must have ``role``'s admin role.\\n     */\\n    function grantRole(bytes32 role, address account) external;\\n\\n    /**\\n     * @dev Revokes `role` from `account`.\\n     *\\n     * If `account` had been granted `role`, emits a {RoleRevoked} event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must have ``role``'s admin role.\\n     */\\n    function revokeRole(bytes32 role, address account) external;\\n\\n    /**\\n     * @dev Revokes `role` from the calling account.\\n     *\\n     * Roles are often managed via {grantRole} and {revokeRole}: this function's\\n     * purpose is to provide a mechanism for accounts to lose their privileges\\n     * if they are compromised (such as when a trusted device is misplaced).\\n     *\\n     * If the calling account had been granted `role`, emits a {RoleRevoked}\\n     * event.\\n     *\\n     * Requirements:\\n     *\\n     * - the caller must be `account`.\\n     */\\n    function renounceRole(bytes32 role, address account) external;\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/ERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (token/ERC20/ERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC20.sol\\\";\\nimport \\\"./extensions/IERC20Metadata.sol\\\";\\nimport \\\"../../utils/Context.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC20} interface.\\n *\\n * This implementation is agnostic to the way tokens are created. This means\\n * that a supply mechanism has to be added in a derived contract using {_mint}.\\n * For a generic mechanism see {ERC20PresetMinterPauser}.\\n *\\n * TIP: For a detailed writeup see our guide\\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\\n * to implement supply mechanisms].\\n *\\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\\n * instead returning `false` on failure. This behavior is nonetheless\\n * conventional and does not conflict with the expectations of ERC20\\n * applications.\\n *\\n * Additionally, an {Approval} event is emitted on calls to {transferFrom}.\\n * This allows applications to reconstruct the allowance for all accounts just\\n * by listening to said events. Other implementations of the EIP may not emit\\n * these events, as it isn't required by the specification.\\n *\\n * Finally, the non-standard {decreaseAllowance} and {increaseAllowance}\\n * functions have been added to mitigate the well-known issues around setting\\n * allowances. See {IERC20-approve}.\\n */\\ncontract ERC20 is Context, IERC20, IERC20Metadata {\\n  mapping(address =\\u003e uint256) private _balances;\\n\\n  mapping(address =\\u003e mapping(address =\\u003e uint256)) private _allowances;\\n\\n  uint256 private _totalSupply;\\n\\n  string private _name;\\n  string private _symbol;\\n\\n  /**\\n   * @dev Sets the values for {name} and {symbol}.\\n   *\\n   * The default value of {decimals} is 18. To select a different value for\\n   * {decimals} you should overload it.\\n   *\\n   * All two of these values are immutable: they can only be set once during\\n   * construction.\\n   */\\n  constructor(string memory name_, string memory symbol_) {\\n    _name = name_;\\n    _symbol = symbol_;\\n  }\\n\\n  /**\\n   * @dev Returns the name of the token.\\n   */\\n  function name() public view virtual override returns (string memory) {\\n    return _name;\\n  }\\n\\n  /**\\n   * @dev Returns the symbol of the token, usually a shorter version of the\\n   * name.\\n   */\\n  function symbol() public view virtual override returns (string memory) {\\n    return _symbol;\\n  }\\n\\n  /**\\n   * @dev Returns the number of decimals used to get its user representation.\\n   * For example, if `decimals` equals `2`, a balance of `505` tokens should\\n   * be displayed to a user as `5.05` (`505 / 10 ** 2`).\\n   *\\n   * Tokens usually opt for a value of 18, imitating the relationship between\\n   * Ether and Wei. This is the value {ERC20} uses, unless this function is\\n   * overridden;\\n   *\\n   * NOTE: This information is only used for _display_ purposes: it in\\n   * no way affects any of the arithmetic of the contract, including\\n   * {IERC20-balanceOf} and {IERC20-transfer}.\\n   */\\n  function decimals() public view virtual override returns (uint8) {\\n    return 18;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-totalSupply}.\\n   */\\n  function totalSupply() public view virtual override returns (uint256) {\\n    return _totalSupply;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-balanceOf}.\\n   */\\n  function balanceOf(address account) public view virtual override returns (uint256) {\\n    return _balances[account];\\n  }\\n\\n  /**\\n   * @dev See {IERC20-transfer}.\\n   *\\n   * Requirements:\\n   *\\n   * - `to` cannot be the zero address.\\n   * - the caller must have a balance of at least `amount`.\\n   */\\n  function transfer(address to, uint256 amount) public virtual override returns (bool) {\\n    address owner = _msgSender();\\n    _transfer(owner, to, amount);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-allowance}.\\n   */\\n  function allowance(address owner, address spender) public view virtual override returns (uint256) {\\n    return _allowances[owner][spender];\\n  }\\n\\n  /**\\n   * @dev See {IERC20-approve}.\\n   *\\n   * NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on\\n   * `transferFrom`. This is semantically equivalent to an infinite approval.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   */\\n  function approve(address spender, uint256 amount) public virtual override returns (bool) {\\n    address owner = _msgSender();\\n    _approve(owner, spender, amount);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-transferFrom}.\\n   *\\n   * Emits an {Approval} event indicating the updated allowance. This is not\\n   * required by the EIP. See the note at the beginning of {ERC20}.\\n   *\\n   * NOTE: Does not update the allowance if the current allowance\\n   * is the maximum `uint256`.\\n   *\\n   * Requirements:\\n   *\\n   * - `from` and `to` cannot be the zero address.\\n   * - `from` must have a balance of at least `amount`.\\n   * - the caller must have allowance for ``from``'s tokens of at least\\n   * `amount`.\\n   */\\n  function transferFrom(address from, address to, uint256 amount) public virtual override returns (bool) {\\n    address spender = _msgSender();\\n    _spendAllowance(from, spender, amount);\\n    _transfer(from, to, amount);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev Atomically increases the allowance granted to `spender` by the caller.\\n   *\\n   * This is an alternative to {approve} that can be used as a mitigation for\\n   * problems described in {IERC20-approve}.\\n   *\\n   * Emits an {Approval} event indicating the updated allowance.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   */\\n  function increaseAllowance(address spender, uint256 addedValue) public virtual returns (bool) {\\n    address owner = _msgSender();\\n    _approve(owner, spender, allowance(owner, spender) + addedValue);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev Atomically decreases the allowance granted to `spender` by the caller.\\n   *\\n   * This is an alternative to {approve} that can be used as a mitigation for\\n   * problems described in {IERC20-approve}.\\n   *\\n   * Emits an {Approval} event indicating the updated allowance.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   * - `spender` must have allowance for the caller of at least\\n   * `subtractedValue`.\\n   */\\n  function decreaseAllowance(address spender, uint256 subtractedValue) public virtual returns (bool) {\\n    address owner = _msgSender();\\n    uint256 currentAllowance = allowance(owner, spender);\\n    require(currentAllowance \\u003e= subtractedValue, \\\"ERC20: decreased allowance below zero\\\");\\n    unchecked {\\n      _approve(owner, spender, currentAllowance - subtractedValue);\\n    }\\n\\n    return true;\\n  }\\n\\n  /**\\n   * @dev Moves `amount` of tokens from `from` to `to`.\\n   *\\n   * This internal function is equivalent to {transfer}, and can be used to\\n   * e.g. implement automatic token fees, slashing mechanisms, etc.\\n   *\\n   * Emits a {Transfer} event.\\n   *\\n   * Requirements:\\n   *\\n   * - `from` cannot be the zero address.\\n   * - `to` cannot be the zero address.\\n   * - `from` must have a balance of at least `amount`.\\n   */\\n  function _transfer(address from, address to, uint256 amount) internal virtual {\\n    require(from != address(0), \\\"ERC20: transfer from the zero address\\\");\\n    require(to != address(0), \\\"ERC20: transfer to the zero address\\\");\\n\\n    _beforeTokenTransfer(from, to, amount);\\n\\n    uint256 fromBalance = _balances[from];\\n    require(fromBalance \\u003e= amount, \\\"ERC20: transfer amount exceeds balance\\\");\\n    unchecked {\\n      _balances[from] = fromBalance - amount;\\n      // Overflow not possible: the sum of all balances is capped by totalSupply, and the sum is preserved by\\n      // decrementing then incrementing.\\n      _balances[to] += amount;\\n    }\\n\\n    emit Transfer(from, to, amount);\\n\\n    _afterTokenTransfer(from, to, amount);\\n  }\\n\\n  /** @dev Creates `amount` tokens and assigns them to `account`, increasing\\n   * the total supply.\\n   *\\n   * Emits a {Transfer} event with `from` set to the zero address.\\n   *\\n   * Requirements:\\n   *\\n   * - `account` cannot be the zero address.\\n   */\\n  function _mint(address account, uint256 amount) internal virtual {\\n    require(account != address(0), \\\"ERC20: mint to the zero address\\\");\\n\\n    _beforeTokenTransfer(address(0), account, amount);\\n\\n    _totalSupply += amount;\\n    unchecked {\\n      // Overflow not possible: balance + amount is at most totalSupply + amount, which is checked above.\\n      _balances[account] += amount;\\n    }\\n    emit Transfer(address(0), account, amount);\\n\\n    _afterTokenTransfer(address(0), account, amount);\\n  }\\n\\n  /**\\n   * @dev Destroys `amount` tokens from `account`, reducing the\\n   * total supply.\\n   *\\n   * Emits a {Transfer} event with `to` set to the zero address.\\n   *\\n   * Requirements:\\n   *\\n   * - `account` cannot be the zero address.\\n   * - `account` must have at least `amount` tokens.\\n   */\\n  function _burn(address account, uint256 amount) internal virtual {\\n    require(account != address(0), \\\"ERC20: burn from the zero address\\\");\\n\\n    _beforeTokenTransfer(account, address(0), amount);\\n\\n    uint256 accountBalance = _balances[account];\\n    require(accountBalance \\u003e= amount, \\\"ERC20: burn amount exceeds balance\\\");\\n    unchecked {\\n      _balances[account] = accountBalance - amount;\\n      // Overflow not possible: amount \\u003c= accountBalance \\u003c= totalSupply.\\n      _totalSupply -= amount;\\n    }\\n\\n    emit Transfer(account, address(0), amount);\\n\\n    _afterTokenTransfer(account, address(0), amount);\\n  }\\n\\n  /**\\n   * @dev Sets `amount` as the allowance of `spender` over the `owner` s tokens.\\n   *\\n   * This internal function is equivalent to `approve`, and can be used to\\n   * e.g. set automatic allowances for certain subsystems, etc.\\n   *\\n   * Emits an {Approval} event.\\n   *\\n   * Requirements:\\n   *\\n   * - `owner` cannot be the zero address.\\n   * - `spender` cannot be the zero address.\\n   */\\n  function _approve(address owner, address spender, uint256 amount) internal virtual {\\n    require(owner != address(0), \\\"ERC20: approve from the zero address\\\");\\n    require(spender != address(0), \\\"ERC20: approve to the zero address\\\");\\n\\n    _allowances[owner][spender] = amount;\\n    emit Approval(owner, spender, amount);\\n  }\\n\\n  /**\\n   * @dev Updates `owner` s allowance for `spender` based on spent `amount`.\\n   *\\n   * Does not update the allowance amount in case of infinite allowance.\\n   * Revert if not enough allowance is available.\\n   *\\n   * Might emit an {Approval} event.\\n   */\\n  function _spendAllowance(address owner, address spender, uint256 amount) internal virtual {\\n    uint256 currentAllowance = allowance(owner, spender);\\n    if (currentAllowance != type(uint256).max) {\\n      require(currentAllowance \\u003e= amount, \\\"ERC20: insufficient allowance\\\");\\n      unchecked {\\n        _approve(owner, spender, currentAllowance - amount);\\n      }\\n    }\\n  }\\n\\n  /**\\n   * @dev Hook that is called before any transfer of tokens. This includes\\n   * minting and burning.\\n   *\\n   * Calling conditions:\\n   *\\n   * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n   * will be transferred to `to`.\\n   * - when `from` is zero, `amount` tokens will be minted for `to`.\\n   * - when `to` is zero, `amount` of ``from``'s tokens will be burned.\\n   * - `from` and `to` are never both zero.\\n   *\\n   * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n   */\\n  function _beforeTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n\\n  /**\\n   * @dev Hook that is called after any transfer of tokens. This includes\\n   * minting and burning.\\n   *\\n   * Calling conditions:\\n   *\\n   * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n   * has been transferred to `to`.\\n   * - when `from` is zero, `amount` tokens have been minted for `to`.\\n   * - when `to` is zero, `amount` of ``from``'s tokens have been burned.\\n   * - `from` and `to` are never both zero.\\n   *\\n   * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n   */\\n  function _afterTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.6.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20 {\\n  /**\\n   * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n   * another (`to`).\\n   *\\n   * Note that `value` may be zero.\\n   */\\n  event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n  /**\\n   * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n   * a call to {approve}. `value` is the new allowance.\\n   */\\n  event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n  /**\\n   * @dev Returns the amount of tokens in existence.\\n   */\\n  function totalSupply() external view returns (uint256);\\n\\n  /**\\n   * @dev Returns the amount of tokens owned by `account`.\\n   */\\n  function balanceOf(address account) external view returns (uint256);\\n\\n  /**\\n   * @dev Moves `amount` tokens from the caller's account to `to`.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transfer(address to, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Returns the remaining number of tokens that `spender` will be\\n   * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n   * zero by default.\\n   *\\n   * This value changes when {approve} or {transferFrom} are called.\\n   */\\n  function allowance(address owner, address spender) external view returns (uint256);\\n\\n  /**\\n   * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n   * that someone may use both the old and the new allowance by unfortunate\\n   * transaction ordering. One possible solution to mitigate this race\\n   * condition is to first reduce the spender's allowance to 0 and set the\\n   * desired value afterwards:\\n   * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n   *\\n   * Emits an {Approval} event.\\n   */\\n  function approve(address spender, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Moves `amount` tokens from `from` to `to` using the\\n   * allowance mechanism. `amount` is then deducted from the caller's\\n   * allowance.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transferFrom(address from, address to, uint256 amount) external returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.5.0) (token/ERC20/extensions/ERC20Burnable.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../ERC20.sol\\\";\\nimport \\\"../../../utils/Context.sol\\\";\\n\\n/**\\n * @dev Extension of {ERC20} that allows token holders to destroy both their own\\n * tokens and those that they have an allowance for, in a way that can be\\n * recognized off-chain (via event analysis).\\n */\\nabstract contract ERC20Burnable is Context, ERC20 {\\n  /**\\n   * @dev Destroys `amount` tokens from the caller.\\n   *\\n   * See {ERC20-_burn}.\\n   */\\n  function burn(uint256 amount) public virtual {\\n    _burn(_msgSender(), amount);\\n  }\\n\\n  /**\\n   * @dev Destroys `amount` tokens from `account`, deducting from the caller's\\n   * allowance.\\n   *\\n   * See {ERC20-_burn} and {ERC20-allowance}.\\n   *\\n   * Requirements:\\n   *\\n   * - the caller must have allowance for ``accounts``'s tokens of at least\\n   * `amount`.\\n   */\\n  function burnFrom(address account, uint256 amount) public virtual {\\n    _spendAllowance(account, _msgSender(), amount);\\n    _burn(account, amount);\\n  }\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC20/extensions/IERC20Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC20.sol\\\";\\n\\n/**\\n * @dev Interface for the optional metadata functions from the ERC20 standard.\\n *\\n * _Available since v4.1._\\n */\\ninterface IERC20Metadata is IERC20 {\\n  /**\\n   * @dev Returns the name of the token.\\n   */\\n  function name() external view returns (string memory);\\n\\n  /**\\n   * @dev Returns the symbol of the token.\\n   */\\n  function symbol() external view returns (string memory);\\n\\n  /**\\n   * @dev Returns the decimals places of the token.\\n   */\\n  function decimals() external view returns (uint8);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Context.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Context.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract Context {\\n  function _msgSender() internal view virtual returns (address) {\\n    return msg.sender;\\n  }\\n\\n  function _msgData() internal view virtual returns (bytes calldata) {\\n    return msg.data;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Strings.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/Strings.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./math/Math.sol\\\";\\n\\n/**\\n * @dev String operations.\\n */\\nlibrary Strings {\\n  bytes16 private constant _SYMBOLS = \\\"0123456789abcdef\\\";\\n  uint8 private constant _ADDRESS_LENGTH = 20;\\n\\n  /**\\n   * @dev Converts a `uint256` to its ASCII `string` decimal representation.\\n   */\\n  function toString(uint256 value) internal pure returns (string memory) {\\n    unchecked {\\n      uint256 length = Math.log10(value) + 1;\\n      string memory buffer = new string(length);\\n      uint256 ptr;\\n      /// @solidity memory-safe-assembly\\n      assembly {\\n        ptr := add(buffer, add(32, length))\\n      }\\n      while (true) {\\n        ptr--;\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n          mstore8(ptr, byte(mod(value, 10), _SYMBOLS))\\n        }\\n        value /= 10;\\n        if (value == 0) break;\\n      }\\n      return buffer;\\n    }\\n  }\\n\\n  /**\\n   * @dev Converts a `uint256` to its ASCII `string` hexadecimal representation.\\n   */\\n  function toHexString(uint256 value) internal pure returns (string memory) {\\n    unchecked {\\n      return toHexString(value, Math.log256(value) + 1);\\n    }\\n  }\\n\\n  /**\\n   * @dev Converts a `uint256` to its ASCII `string` hexadecimal representation with fixed length.\\n   */\\n  function toHexString(uint256 value, uint256 length) internal pure returns (string memory) {\\n    bytes memory buffer = new bytes(2 * length + 2);\\n    buffer[0] = \\\"0\\\";\\n    buffer[1] = \\\"x\\\";\\n    for (uint256 i = 2 * length + 1; i \\u003e 1; --i) {\\n      buffer[i] = _SYMBOLS[value \\u0026 0xf];\\n      value \\u003e\\u003e= 4;\\n    }\\n    require(value == 0, \\\"Strings: hex length insufficient\\\");\\n    return string(buffer);\\n  }\\n\\n  /**\\n   * @dev Converts an `address` with fixed length of 20 bytes to its not checksummed ASCII `string` hexadecimal representation.\\n   */\\n  function toHexString(address addr) internal pure returns (string memory) {\\n    return toHexString(uint256(uint160(addr)), _ADDRESS_LENGTH);\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/ERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/ERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC165.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC165} interface.\\n *\\n * Contracts that want to implement ERC165 should inherit from this contract and override {supportsInterface} to check\\n * for the additional interface id that will be supported. For example:\\n *\\n * ```solidity\\n * function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n *     return interfaceId == type(MyInterface).interfaceId || super.supportsInterface(interfaceId);\\n * }\\n * ```\\n *\\n * Alternatively, {ERC165Storage} provides an easier to use but more expensive implementation.\\n */\\nabstract contract ERC165 is IERC165 {\\n    /**\\n     * @dev See {IERC165-supportsInterface}.\\n     */\\n    function supportsInterface(bytes4 interfaceId) public view virtual override returns (bool) {\\n        return interfaceId == type(IERC165).interfaceId;\\n    }\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/math/Math.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/math/Math.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Standard math utilities missing in the Solidity language.\\n */\\nlibrary Math {\\n  enum Rounding {\\n    Down, // Toward negative infinity\\n    Up, // Toward infinity\\n    Zero // Toward zero\\n  }\\n\\n  /**\\n   * @dev Returns the largest of two numbers.\\n   */\\n  function max(uint256 a, uint256 b) internal pure returns (uint256) {\\n    return a \\u003e b ? a : b;\\n  }\\n\\n  /**\\n   * @dev Returns the smallest of two numbers.\\n   */\\n  function min(uint256 a, uint256 b) internal pure returns (uint256) {\\n    return a \\u003c b ? a : b;\\n  }\\n\\n  /**\\n   * @dev Returns the average of two numbers. The result is rounded towards\\n   * zero.\\n   */\\n  function average(uint256 a, uint256 b) internal pure returns (uint256) {\\n    // (a + b) / 2 can overflow.\\n    return (a \\u0026 b) + (a ^ b) / 2;\\n  }\\n\\n  /**\\n   * @dev Returns the ceiling of the division of two numbers.\\n   *\\n   * This differs from standard division with `/` in that it rounds up instead\\n   * of rounding down.\\n   */\\n  function ceilDiv(uint256 a, uint256 b) internal pure returns (uint256) {\\n    // (a + b - 1) / b can overflow on addition, so we distribute.\\n    return a == 0 ? 0 : (a - 1) / b + 1;\\n  }\\n\\n  /**\\n   * @notice Calculates floor(x * y / denominator) with full precision. Throws if result overflows a uint256 or denominator == 0\\n   * @dev Original credit to Remco Bloemen under MIT license (https://xn--2-umb.com/21/muldiv)\\n   * with further edits by Uniswap Labs also under MIT license.\\n   */\\n  function mulDiv(uint256 x, uint256 y, uint256 denominator) internal pure returns (uint256 result) {\\n    unchecked {\\n      // 512-bit multiply [prod1 prod0] = x * y. Compute the product mod 2^256 and mod 2^256 - 1, then use\\n      // use the Chinese Remainder Theorem to reconstruct the 512 bit result. The result is stored in two 256\\n      // variables such that product = prod1 * 2^256 + prod0.\\n      uint256 prod0; // Least significant 256 bits of the product\\n      uint256 prod1; // Most significant 256 bits of the product\\n      assembly {\\n        let mm := mulmod(x, y, not(0))\\n        prod0 := mul(x, y)\\n        prod1 := sub(sub(mm, prod0), lt(mm, prod0))\\n      }\\n\\n      // Handle non-overflow cases, 256 by 256 division.\\n      if (prod1 == 0) {\\n        return prod0 / denominator;\\n      }\\n\\n      // Make sure the result is less than 2^256. Also prevents denominator == 0.\\n      require(denominator \\u003e prod1);\\n\\n      ///////////////////////////////////////////////\\n      // 512 by 256 division.\\n      ///////////////////////////////////////////////\\n\\n      // Make division exact by subtracting the remainder from [prod1 prod0].\\n      uint256 remainder;\\n      assembly {\\n        // Compute remainder using mulmod.\\n        remainder := mulmod(x, y, denominator)\\n\\n        // Subtract 256 bit number from 512 bit number.\\n        prod1 := sub(prod1, gt(remainder, prod0))\\n        prod0 := sub(prod0, remainder)\\n      }\\n\\n      // Factor powers of two out of denominator and compute largest power of two divisor of denominator. Always \\u003e= 1.\\n      // See https://cs.stackexchange.com/q/138556/92363.\\n\\n      // Does not overflow because the denominator cannot be zero at this stage in the function.\\n      uint256 twos = denominator \\u0026 (~denominator + 1);\\n      assembly {\\n        // Divide denominator by twos.\\n        denominator := div(denominator, twos)\\n\\n        // Divide [prod1 prod0] by twos.\\n        prod0 := div(prod0, twos)\\n\\n        // Flip twos such that it is 2^256 / twos. If twos is zero, then it becomes one.\\n        twos := add(div(sub(0, twos), twos), 1)\\n      }\\n\\n      // Shift in bits from prod1 into prod0.\\n      prod0 |= prod1 * twos;\\n\\n      // Invert denominator mod 2^256. Now that denominator is an odd number, it has an inverse modulo 2^256 such\\n      // that denominator * inv = 1 mod 2^256. Compute the inverse by starting with a seed that is correct for\\n      // four bits. That is, denominator * inv = 1 mod 2^4.\\n      uint256 inverse = (3 * denominator) ^ 2;\\n\\n      // Use the Newton-Raphson iteration to improve the precision. Thanks to Hensel's lifting lemma, this also works\\n      // in modular arithmetic, doubling the correct bits in each step.\\n      inverse *= 2 - denominator * inverse; // inverse mod 2^8\\n      inverse *= 2 - denominator * inverse; // inverse mod 2^16\\n      inverse *= 2 - denominator * inverse; // inverse mod 2^32\\n      inverse *= 2 - denominator * inverse; // inverse mod 2^64\\n      inverse *= 2 - denominator * inverse; // inverse mod 2^128\\n      inverse *= 2 - denominator * inverse; // inverse mod 2^256\\n\\n      // Because the division is now exact we can divide by multiplying with the modular inverse of denominator.\\n      // This will give us the correct result modulo 2^256. Since the preconditions guarantee that the outcome is\\n      // less than 2^256, this is the final result. We don't need to compute the high bits of the result and prod1\\n      // is no longer required.\\n      result = prod0 * inverse;\\n      return result;\\n    }\\n  }\\n\\n  /**\\n   * @notice Calculates x * y / denominator with full precision, following the selected rounding direction.\\n   */\\n  function mulDiv(uint256 x, uint256 y, uint256 denominator, Rounding rounding) internal pure returns (uint256) {\\n    uint256 result = mulDiv(x, y, denominator);\\n    if (rounding == Rounding.Up \\u0026\\u0026 mulmod(x, y, denominator) \\u003e 0) {\\n      result += 1;\\n    }\\n    return result;\\n  }\\n\\n  /**\\n   * @dev Returns the square root of a number. If the number is not a perfect square, the value is rounded down.\\n   *\\n   * Inspired by Henry S. Warren, Jr.'s \\\"Hacker's Delight\\\" (Chapter 11).\\n   */\\n  function sqrt(uint256 a) internal pure returns (uint256) {\\n    if (a == 0) {\\n      return 0;\\n    }\\n\\n    // For our first guess, we get the biggest power of 2 which is smaller than the square root of the target.\\n    //\\n    // We know that the \\\"msb\\\" (most significant bit) of our target number `a` is a power of 2 such that we have\\n    // `msb(a) \\u003c= a \\u003c 2*msb(a)`. This value can be written `msb(a)=2**k` with `k=log2(a)`.\\n    //\\n    // This can be rewritten `2**log2(a) \\u003c= a \\u003c 2**(log2(a) + 1)`\\n    // → `sqrt(2**k) \\u003c= sqrt(a) \\u003c sqrt(2**(k+1))`\\n    // → `2**(k/2) \\u003c= sqrt(a) \\u003c 2**((k+1)/2) \\u003c= 2**(k/2 + 1)`\\n    //\\n    // Consequently, `2**(log2(a) / 2)` is a good first approximation of `sqrt(a)` with at least 1 correct bit.\\n    uint256 result = 1 \\u003c\\u003c (log2(a) \\u003e\\u003e 1);\\n\\n    // At this point `result` is an estimation with one bit of precision. We know the true value is a uint128,\\n    // since it is the square root of a uint256. Newton's method converges quadratically (precision doubles at\\n    // every iteration). We thus need at most 7 iteration to turn our partial result with one bit of precision\\n    // into the expected uint128 result.\\n    unchecked {\\n      result = (result + a / result) \\u003e\\u003e 1;\\n      result = (result + a / result) \\u003e\\u003e 1;\\n      result = (result + a / result) \\u003e\\u003e 1;\\n      result = (result + a / result) \\u003e\\u003e 1;\\n      result = (result + a / result) \\u003e\\u003e 1;\\n      result = (result + a / result) \\u003e\\u003e 1;\\n      result = (result + a / result) \\u003e\\u003e 1;\\n      return min(result, a / result);\\n    }\\n  }\\n\\n  /**\\n   * @notice Calculates sqrt(a), following the selected rounding direction.\\n   */\\n  function sqrt(uint256 a, Rounding rounding) internal pure returns (uint256) {\\n    unchecked {\\n      uint256 result = sqrt(a);\\n      return result + (rounding == Rounding.Up \\u0026\\u0026 result * result \\u003c a ? 1 : 0);\\n    }\\n  }\\n\\n  /**\\n   * @dev Return the log in base 2, rounded down, of a positive value.\\n   * Returns 0 if given 0.\\n   */\\n  function log2(uint256 value) internal pure returns (uint256) {\\n    uint256 result = 0;\\n    unchecked {\\n      if (value \\u003e\\u003e 128 \\u003e 0) {\\n        value \\u003e\\u003e= 128;\\n        result += 128;\\n      }\\n      if (value \\u003e\\u003e 64 \\u003e 0) {\\n        value \\u003e\\u003e= 64;\\n        result += 64;\\n      }\\n      if (value \\u003e\\u003e 32 \\u003e 0) {\\n        value \\u003e\\u003e= 32;\\n        result += 32;\\n      }\\n      if (value \\u003e\\u003e 16 \\u003e 0) {\\n        value \\u003e\\u003e= 16;\\n        result += 16;\\n      }\\n      if (value \\u003e\\u003e 8 \\u003e 0) {\\n        value \\u003e\\u003e= 8;\\n        result += 8;\\n      }\\n      if (value \\u003e\\u003e 4 \\u003e 0) {\\n        value \\u003e\\u003e= 4;\\n        result += 4;\\n      }\\n      if (value \\u003e\\u003e 2 \\u003e 0) {\\n        value \\u003e\\u003e= 2;\\n        result += 2;\\n      }\\n      if (value \\u003e\\u003e 1 \\u003e 0) {\\n        result += 1;\\n      }\\n    }\\n    return result;\\n  }\\n\\n  /**\\n   * @dev Return the log in base 2, following the selected rounding direction, of a positive value.\\n   * Returns 0 if given 0.\\n   */\\n  function log2(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n    unchecked {\\n      uint256 result = log2(value);\\n      return result + (rounding == Rounding.Up \\u0026\\u0026 1 \\u003c\\u003c result \\u003c value ? 1 : 0);\\n    }\\n  }\\n\\n  /**\\n   * @dev Return the log in base 10, rounded down, of a positive value.\\n   * Returns 0 if given 0.\\n   */\\n  function log10(uint256 value) internal pure returns (uint256) {\\n    uint256 result = 0;\\n    unchecked {\\n      if (value \\u003e= 10 ** 64) {\\n        value /= 10 ** 64;\\n        result += 64;\\n      }\\n      if (value \\u003e= 10 ** 32) {\\n        value /= 10 ** 32;\\n        result += 32;\\n      }\\n      if (value \\u003e= 10 ** 16) {\\n        value /= 10 ** 16;\\n        result += 16;\\n      }\\n      if (value \\u003e= 10 ** 8) {\\n        value /= 10 ** 8;\\n        result += 8;\\n      }\\n      if (value \\u003e= 10 ** 4) {\\n        value /= 10 ** 4;\\n        result += 4;\\n      }\\n      if (value \\u003e= 10 ** 2) {\\n        value /= 10 ** 2;\\n        result += 2;\\n      }\\n      if (value \\u003e= 10 ** 1) {\\n        result += 1;\\n      }\\n    }\\n    return result;\\n  }\\n\\n  /**\\n   * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\\n   * Returns 0 if given 0.\\n   */\\n  function log10(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n    unchecked {\\n      uint256 result = log10(value);\\n      return result + (rounding == Rounding.Up \\u0026\\u0026 10 ** result \\u003c value ? 1 : 0);\\n    }\\n  }\\n\\n  /**\\n   * @dev Return the log in base 256, rounded down, of a positive value.\\n   * Returns 0 if given 0.\\n   *\\n   * Adding one to the result gives the number of pairs of hex symbols needed to represent `value` as a hex string.\\n   */\\n  function log256(uint256 value) internal pure returns (uint256) {\\n    uint256 result = 0;\\n    unchecked {\\n      if (value \\u003e\\u003e 128 \\u003e 0) {\\n        value \\u003e\\u003e= 128;\\n        result += 16;\\n      }\\n      if (value \\u003e\\u003e 64 \\u003e 0) {\\n        value \\u003e\\u003e= 64;\\n        result += 8;\\n      }\\n      if (value \\u003e\\u003e 32 \\u003e 0) {\\n        value \\u003e\\u003e= 32;\\n        result += 4;\\n      }\\n      if (value \\u003e\\u003e 16 \\u003e 0) {\\n        value \\u003e\\u003e= 16;\\n        result += 2;\\n      }\\n      if (value \\u003e\\u003e 8 \\u003e 0) {\\n        result += 1;\\n      }\\n    }\\n    return result;\\n  }\\n\\n  /**\\n   * @dev Return the log in base 10, following the selected rounding direction, of a positive value.\\n   * Returns 0 if given 0.\\n   */\\n  function log256(uint256 value, Rounding rounding) internal pure returns (uint256) {\\n    unchecked {\\n      uint256 result = log256(value);\\n      return result + (rounding == Rounding.Up \\u0026\\u0026 1 \\u003c\\u003c (result * 8) \\u003c value ? 1 : 0);\\n    }\\n  }\\n}\\n\"}}}"
