// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package message_hasher

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/libraries/Client.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Internal.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/MerkleMultiProof.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/test/helpers/MessageHasher.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/libraries/Client.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n// End consumer library.\\nlibrary Client {\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct EVMTokenAmount {\\n    address token; // token address on the local chain.\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  struct Any2EVMMessage {\\n    bytes32 messageId; // MessageId corresponding to ccipSend on source.\\n    uint64 sourceChainSelector; // Source chain selector.\\n    bytes sender; // abi.decode(sender) if coming from an EVM chain.\\n    bytes data; // payload sent in original message.\\n    EVMTokenAmount[] destTokenAmounts; // Tokens and their amounts in their destination chain representation.\\n  }\\n\\n  // If extraArgs is empty bytes, the default is 200k gas limit.\\n  struct EVM2AnyMessage {\\n    bytes receiver; // abi.encode(receiver address) for dest EVM chains.\\n    bytes data; // Data payload.\\n    EVMTokenAmount[] tokenAmounts; // Token transfers.\\n    address feeToken; // Address of feeToken. address(0) means you will send msg.value.\\n    bytes extraArgs; // Populate this with _argsToBytes(EVMExtraArgsV2).\\n  }\\n\\n  // Tag to indicate only a gas limit. Only usable for EVM as destination chain.\\n  bytes4 public constant EVM_EXTRA_ARGS_V1_TAG = 0x97a657c9;\\n\\n  struct EVMExtraArgsV1 {\\n    uint256 gasLimit;\\n  }\\n\\n  function _argsToBytes(\\n    EVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(EVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n\\n  // Tag to indicate a gas limit (or dest chain equivalent processing units) and Out Of Order Execution. This tag is\\n  // available for multiple chain families. If there is no chain family specific tag, this is the default available\\n  // for a chain.\\n  // Note: not available for Solana VM based chains.\\n  bytes4 public constant GENERIC_EXTRA_ARGS_V2_TAG = 0x181dcf10;\\n\\n  /// @param gasLimit: gas limit for the callback on the destination chain.\\n  /// @param allowOutOfOrderExecution: if true, it indicates that the message can be executed in any order relative to\\n  /// other messages from the same sender. This value's default varies by chain. On some chains, a particular value is\\n  /// enforced, meaning if the expected value is not set, the message request will revert.\\n  /// @dev Fully compatible with the previously existing EVMExtraArgsV2.\\n  struct GenericExtraArgsV2 {\\n    uint256 gasLimit;\\n    bool allowOutOfOrderExecution;\\n  }\\n\\n  // Extra args tag for chains that use the Solana VM.\\n  bytes4 public constant SVM_EXTRA_ARGS_V1_TAG = 0x1f3b3aba;\\n\\n  struct SVMExtraArgsV1 {\\n    uint32 computeUnits;\\n    uint64 accountIsWritableBitmap;\\n    bool allowOutOfOrderExecution;\\n    bytes32 tokenReceiver;\\n    // Additional accounts needed for execution of CCIP receiver. Must be empty if message.receiver is zero.\\n    // Token transfer related accounts are specified in the token pool lookup table on SVM.\\n    bytes32[] accounts;\\n  }\\n\\n  /// @dev The maximum number of accounts that can be passed in SVMExtraArgs.\\n  uint256 public constant SVM_EXTRA_ARGS_MAX_ACCOUNTS = 64;\\n\\n  /// @dev The expected static payload size of a token transfer when Borsh encoded and submitted to SVM.\\n  /// TokenPool extra data and offchain data sizes are dynamic, and should be accounted for separately.\\n  uint256 public constant SVM_TOKEN_TRANSFER_DATA_OVERHEAD = (4 + 32) // source_pool\\n    + 32 // token_address\\n    + 4 // gas_amount\\n    + 4 // extra_data overhead\\n    + 32 // amount\\n    + 32 // size of the token lookup table account\\n    + 32 // token-related accounts in the lookup table, over-estimated to 32, typically between 11 - 13\\n    + 32 // token account belonging to the token receiver, e.g ATA, not included in the token lookup table\\n    + 32 // per-chain token pool config, not included in the token lookup table\\n    + 32 // per-chain token billing config, not always included in the token lookup table\\n    + 32; // OffRamp pool signer PDA, not included in the token lookup table\\n\\n  /// @dev Number of overhead accounts needed for message execution on SVM.\\n  /// @dev These are message.receiver, and the OffRamp Signer PDA specific to the receiver.\\n  uint256 public constant SVM_MESSAGING_ACCOUNTS_OVERHEAD = 2;\\n\\n  /// @dev The size of each SVM account address in bytes.\\n  uint256 public constant SVM_ACCOUNT_BYTE_SIZE = 32;\\n\\n  function _argsToBytes(\\n    GenericExtraArgsV2 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(GENERIC_EXTRA_ARGS_V2_TAG, extraArgs);\\n  }\\n\\n  function _svmArgsToBytes(\\n    SVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(SVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n}\\n\"},\"contracts/libraries/Internal.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {MerkleMultiProof} from \\\"../libraries/MerkleMultiProof.sol\\\";\\n\\n/// @notice Library for CCIP internal definitions common to multiple contracts.\\n/// @dev The following is a non-exhaustive list of \\\"known issues\\\" for CCIP:\\n/// - We could implement yield claiming for Blast. This is not worth the custom code path on non-blast chains.\\n/// - uint32 is used for timestamps, which will overflow in 2106. This is not a concern for the current use case, as we\\n/// expect to have migrated to a new version by then.\\nlibrary Internal {\\n  error InvalidEVMAddress(bytes encodedAddress);\\n  error Invalid32ByteAddress(bytes encodedAddress);\\n\\n  /// @dev We limit return data to a selector plus 4 words. This is to avoid malicious contracts from returning\\n  /// large amounts of data and causing repeated out-of-gas scenarios.\\n  uint16 internal constant MAX_RET_BYTES = 4 + 4 * 32;\\n  /// @dev The expected number of bytes returned by the balanceOf function.\\n  uint256 internal constant MAX_BALANCE_OF_RET_BYTES = 32;\\n\\n  /// @dev The address used to send calls for gas estimation.\\n  /// You only need to use this address if the minimum gas limit specified by the user is not actually enough to execute the\\n  /// given message and you're attempting to estimate the actual necessary gas limit\\n  address public constant GAS_ESTIMATION_SENDER = address(0xC11C11C11C11C11C11C11C11C11C11C11C11C1);\\n\\n  /// @notice A collection of token price and gas price updates.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct PriceUpdates {\\n    TokenPriceUpdate[] tokenPriceUpdates;\\n    GasPriceUpdate[] gasPriceUpdates;\\n  }\\n\\n  /// @notice Token price in USD.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct TokenPriceUpdate {\\n    address sourceToken; // Source token.\\n    uint224 usdPerToken; // 1e18 USD per 1e18 of the smallest token denomination.\\n  }\\n\\n  /// @notice Gas price for a given chain in USD, its value may contain tightly packed fields.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct GasPriceUpdate {\\n    uint64 destChainSelector; // Destination chain selector.\\n    uint224 usdPerUnitGas; // 1e18 USD per smallest unit (e.g. wei) of destination chain gas.\\n  }\\n\\n  /// @notice A timestamped uint224 value that can contain several tightly packed fields.\\n  struct TimestampedPackedUint224 {\\n    uint224 value; // ────╮ Value in uint224, packed.\\n    uint32 timestamp; // ─╯ Timestamp of the most recent price update.\\n  }\\n\\n  /// @dev Gas price is stored in 112-bit unsigned int. uint224 can pack 2 prices.\\n  /// When packing L1 and L2 gas prices, L1 gas price is left-shifted to the higher-order bits.\\n  /// Using uint8 type, which cannot be higher than other bit shift operands, to avoid shift operand type warning.\\n  uint8 public constant GAS_PRICE_BITS = 112;\\n\\n  struct SourceTokenData {\\n    // The source pool address, abi encoded. This value is trusted as it was obtained through the onRamp. It can be\\n    // relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint32 destGasAmount; // The amount of gas available for the releaseOrMint and balanceOf calls on the offRamp\\n  }\\n\\n  /// @notice Report that is submitted by the execution DON at the execution phase, including chain selector data.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct ExecutionReport {\\n    uint64 sourceChainSelector; // Source chain selector for which the report is submitted.\\n    Any2EVMRampMessage[] messages;\\n    // Contains a bytes array for each message, each inner bytes array contains bytes per transferred token.\\n    bytes[][] offchainTokenData;\\n    bytes32[] proofs;\\n    uint256 proofFlagBits;\\n  }\\n\\n  /// @dev Any2EVMRampMessage struct has 10 fields, including 3 variable unnested arrays, sender, data and tokenAmounts.\\n  /// Each variable array takes 1 more slot to store its length.\\n  /// When abi encoded, excluding array contents, Any2EVMMessage takes up a fixed number of 13 slots, 32 bytes each.\\n  /// Assume 1 slot for sender\\n  /// For structs that contain arrays, 1 more slot is added to the front, reaching a total of 14.\\n  /// The fixed bytes does not cover struct data (this is represented by MESSAGE_FIXED_BYTES_PER_TOKEN)\\n  uint256 public constant MESSAGE_FIXED_BYTES = 32 * 15;\\n\\n  /// @dev Any2EVMTokensTransfer struct bytes length\\n  /// 0x20\\n  /// sourcePoolAddress_offset\\n  /// destTokenAddress\\n  /// destGasAmount\\n  /// extraData_offset\\n  /// amount\\n  /// sourcePoolAddress_length\\n  /// sourcePoolAddress_content // assume 1 slot\\n  /// extraData_length // contents billed separately\\n  uint256 public constant MESSAGE_FIXED_BYTES_PER_TOKEN = 32 * (4 + (3 + 2));\\n\\n  bytes32 internal constant ANY_2_EVM_MESSAGE_HASH = keccak256(\\\"Any2EVMMessageHashV1\\\");\\n  bytes32 internal constant EVM_2_ANY_MESSAGE_HASH = keccak256(\\\"EVM2AnyMessageHashV1\\\");\\n\\n  /// @dev Used to hash messages for multi-lane family-agnostic OffRamps.\\n  /// OnRamp hash(EVM2AnyMessage) != Any2EVMRampMessage.messageId.\\n  /// OnRamp hash(EVM2AnyMessage) != OffRamp hash(Any2EVMRampMessage).\\n  /// @param original OffRamp message to hash.\\n  /// @param metadataHash Hash preimage to ensure global uniqueness.\\n  /// @return hashedMessage hashed message as a keccak256.\\n  function _hash(Any2EVMRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.header.messageId,\\n            original.receiver,\\n            original.header.sequenceNumber,\\n            original.gasLimit,\\n            original.header.nonce\\n          )\\n        ),\\n        keccak256(original.sender),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts))\\n      )\\n    );\\n  }\\n\\n  function _hash(EVM2AnyRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.sender,\\n            original.header.sequenceNumber,\\n            original.header.nonce,\\n            original.feeToken,\\n            original.feeTokenAmount\\n          )\\n        ),\\n        keccak256(original.receiver),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts)),\\n        keccak256(original.extraArgs)\\n      )\\n    );\\n  }\\n\\n  /// @dev We disallow the first 1024 addresses to avoid calling into a range known for hosting precompiles. Calling\\n  /// into precompiles probably won't cause any issues, but to be safe we can disallow this range. It is extremely\\n  /// unlikely that anyone would ever be able to generate an address in this range. There is no official range of\\n  /// precompiles, but EIP-7587 proposes to reserve the range 0x100 to 0x1ff. Our range is more conservative, even\\n  /// though it might not be exhaustive for all chains, which is OK. We also disallow the zero address, which is a\\n  /// common practice.\\n  uint256 public constant EVM_PRECOMPILE_SPACE = 1024;\\n\\n  // According to the Aptos docs, the first 0xa addresses are reserved for precompiles.\\n  // https://github.com/aptos-labs/aptos-core/blob/main/aptos-move/framework/aptos-framework/doc/account.md#function-create_framework_reserved_account-1\\n  uint256 public constant APTOS_PRECOMPILE_SPACE = 0x0b;\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// EVM address space. If it isn't it will revert with an InvalidEVMAddress error, which we can catch and handle\\n  /// more gracefully than a revert from abi.decode.\\n  function _validateEVMAddress(\\n    bytes memory encodedAddress\\n  ) internal pure {\\n    if (encodedAddress.length != 32) revert InvalidEVMAddress(encodedAddress);\\n    uint256 encodedAddressUint = abi.decode(encodedAddress, (uint256));\\n    if (encodedAddressUint \\u003e type(uint160).max || encodedAddressUint \\u003c EVM_PRECOMPILE_SPACE) {\\n      revert InvalidEVMAddress(encodedAddress);\\n    }\\n  }\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// bounds of [minValue, uint256.max]. If it isn't it will revert with an Invalid32ByteAddress error.\\n  function _validate32ByteAddress(bytes memory encodedAddress, uint256 minValue) internal pure {\\n    if (encodedAddress.length != 32) revert Invalid32ByteAddress(encodedAddress);\\n    if (minValue \\u003e 0) {\\n      if (abi.decode(encodedAddress, (uint256)) \\u003c minValue) {\\n        revert Invalid32ByteAddress(encodedAddress);\\n      }\\n    }\\n  }\\n\\n  /// @notice Enum listing the possible message execution states within the offRamp contract.\\n  /// UNTOUCHED never executed.\\n  /// IN_PROGRESS currently being executed, used a replay protection.\\n  /// SUCCESS successfully executed. End state.\\n  /// FAILURE unsuccessfully executed, manual execution is now enabled.\\n  /// @dev RMN depends on this enum, if changing, please notify the RMN maintainers.\\n  enum MessageExecutionState {\\n    UNTOUCHED,\\n    IN_PROGRESS,\\n    SUCCESS,\\n    FAILURE\\n  }\\n\\n  /// @notice CCIP OCR plugin type, used to separate execution \\u0026 commit transmissions and configs.\\n  enum OCRPluginType {\\n    Commit,\\n    Execution\\n  }\\n\\n  /// @notice Family-agnostic header for OnRamp \\u0026 OffRamp messages.\\n  /// The messageId is not expected to match hash(message), since it may originate from another ramp family.\\n  struct RampMessageHeader {\\n    bytes32 messageId; // Unique identifier for the message, generated with the source chain's encoding scheme (i.e. not necessarily abi.encoded).\\n    uint64 sourceChainSelector; // ─╮ the chain selector of the source chain, note: not chainId.\\n    uint64 destChainSelector; //    │ the chain selector of the destination chain, note: not chainId.\\n    uint64 sequenceNumber; //       │ sequence number, not unique across lanes.\\n    uint64 nonce; // ───────────────╯ nonce for this lane for this sender, not unique across senders/lanes.\\n  }\\n\\n  struct EVM2AnyTokenTransfer {\\n    // The source pool EVM address. This value is trusted as it was obtained through the onRamp. It can be relied\\n    // upon by the destination pool to validate the source pool.\\n    address sourcePoolAddress;\\n    // The EVM address of the destination token.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n    // Destination chain data used to execute the token transfer on the destination chain. For an EVM destination, it\\n    // consists of the amount of gas available for the releaseOrMint and transfer calls made by the offRamp.\\n    bytes destExecData;\\n  }\\n\\n  struct Any2EVMTokenTransfer {\\n    // The source pool EVM address encoded to bytes. This value is trusted as it is obtained through the onRamp. It can\\n    // be relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    address destTokenAddress; // ─╮ Address of destination token\\n    uint32 destGasAmount; // ─────╯ The amount of gas available for the releaseOrMint and transfer calls on the offRamp.\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  /// @notice Family-agnostic message routed to an OffRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage), hash(Any2EVMRampMessage) != messageId due to encoding\\n  /// and parameter differences.\\n  struct Any2EVMRampMessage {\\n    RampMessageHeader header; // Message header.\\n    bytes sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    address receiver; // receiver address on the destination chain.\\n    uint256 gasLimit; // user supplied maximum gas amount available for dest chain execution.\\n    Any2EVMTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  /// @notice Family-agnostic message emitted from the OnRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage) due to encoding \\u0026 parameter differences.\\n  /// messageId = hash(EVM2AnyRampMessage) using the source EVM chain's encoding format.\\n  struct EVM2AnyRampMessage {\\n    RampMessageHeader header; // Message header.\\n    address sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    bytes receiver; // receiver address on the destination chain.\\n    bytes extraArgs; // destination-chain specific extra args, such as the gasLimit for EVM chains.\\n    address feeToken; // fee token.\\n    uint256 feeTokenAmount; // fee token amount.\\n    uint256 feeValueJuels; // fee amount in Juels.\\n    EVM2AnyTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector EVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_EVM = 0x2812d52c;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SVM = 0x1e10bdc4;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector APTOS\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_APTOS = 0xac77ffec;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SUI\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SUI = 0xc4e05953;\\n\\n  /// @dev Holds a merkle root and interval for a source chain so that an array of these can be passed in the CommitReport.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  /// @dev inefficient struct packing intentionally chosen to maintain order of specificity. Not a storage struct so impact is minimal.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct MerkleRoot {\\n    uint64 sourceChainSelector; // Remote source chain selector that the Merkle Root is scoped to\\n    bytes onRampAddress; //        Generic onRamp address, to support arbitrary sources; for EVM, use abi.encode\\n    uint64 minSeqNr; // ─────────╮ Minimum sequence number, inclusive\\n    uint64 maxSeqNr; // ─────────╯ Maximum sequence number, inclusive\\n    bytes32 merkleRoot; //         Merkle root covering the interval \\u0026 source chain messages\\n  }\\n}\\n\"},\"contracts/libraries/MerkleMultiProof.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nlibrary MerkleMultiProof {\\n  /// @notice Leaf domain separator, should be used as the first 32 bytes of a leaf's preimage.\\n  bytes32 internal constant LEAF_DOMAIN_SEPARATOR = 0x0000000000000000000000000000000000000000000000000000000000000000;\\n  /// @notice Internal domain separator, should be used as the first 32 bytes of an internal node's preimage.\\n  bytes32 internal constant INTERNAL_DOMAIN_SEPARATOR =\\n    0x0000000000000000000000000000000000000000000000000000000000000001;\\n\\n  uint256 internal constant MAX_NUM_HASHES = 256;\\n\\n  error InvalidProof();\\n  error LeavesCannotBeEmpty();\\n\\n  /// @notice Computes the root based on provided pre-hashed leaf nodes in leaves, internal nodes  in proofs, and using\\n  /// proofFlagBits' i-th bit to determine if an element of proofs or one of the previously computed leafs or internal\\n  /// nodes will be used for the i-th hash.\\n  /// @param leaves Should be pre-hashed and the first 32 bytes of a leaf's preimage should match LEAF_DOMAIN_SEPARATOR.\\n  /// @param proofs Hashes to be used instead of a leaf hash when the proofFlagBits indicates a proof should be used.\\n  /// @param proofFlagBits A single uint256 of which each bit indicates whether a leaf or a proof needs to be used in\\n  /// a hash operation.\\n  /// @dev the maximum number of hash operations it set to 256. Any input that would require more than 256 hashes to get\\n  /// to a root will revert.\\n  /// @dev For given input `leaves` = [a,b,c] `proofs` = [D] and `proofFlagBits` = 5\\n  ///     totalHashes = 3 + 1 - 1 = 3\\n  ///  ** round 1 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 0) \\u0026 1 = true\\n  ///    hashes[0] = hashPair(a, b)\\n  ///    (leafPos, hashPos, proofPos) = (2, 0, 0);\\n  ///\\n  ///  ** round 2 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 1) \\u0026 1 = false\\n  ///    hashes[1] = hashPair(D, c)\\n  ///    (leafPos, hashPos, proofPos) = (3, 0, 1);\\n  ///\\n  ///  ** round 3 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 2) \\u0026 1 = true\\n  ///    hashes[2] = hashPair(hashes[0], hashes[1])\\n  ///    (leafPos, hashPos, proofPos) = (3, 2, 1);\\n  ///\\n  ///    i = 3 and no longer \\u003c totalHashes. The algorithm is done\\n  ///    return hashes[totalHashes - 1] = hashes[2]; the last hash we computed.\\n  // We mark this function as internal to force it to be inlined in contracts that use it, but semantically it is public.\\n  function _merkleRoot(\\n    bytes32[] memory leaves,\\n    bytes32[] memory proofs,\\n    uint256 proofFlagBits\\n  ) internal pure returns (bytes32) {\\n    unchecked {\\n      uint256 leavesLen = leaves.length;\\n      uint256 proofsLen = proofs.length;\\n      if (leavesLen == 0) revert LeavesCannotBeEmpty();\\n      if (!(leavesLen \\u003c= MAX_NUM_HASHES + 1 \\u0026\\u0026 proofsLen \\u003c= MAX_NUM_HASHES + 1)) revert InvalidProof();\\n      uint256 totalHashes = leavesLen + proofsLen - 1;\\n      if (!(totalHashes \\u003c= MAX_NUM_HASHES)) revert InvalidProof();\\n      if (totalHashes == 0) {\\n        return leaves[0];\\n      }\\n      bytes32[] memory hashes = new bytes32[](totalHashes);\\n      (uint256 leafPos, uint256 hashPos, uint256 proofPos) = (0, 0, 0);\\n\\n      for (uint256 i = 0; i \\u003c totalHashes; ++i) {\\n        // Checks if the bit flag signals the use of a supplied proof or a leaf/previous hash.\\n        bytes32 a;\\n        if (proofFlagBits \\u0026 (1 \\u003c\\u003c i) == (1 \\u003c\\u003c i)) {\\n          // Use a leaf or a previously computed hash.\\n          if (leafPos \\u003c leavesLen) {\\n            a = leaves[leafPos++];\\n          } else {\\n            a = hashes[hashPos++];\\n          }\\n        } else {\\n          // Use a supplied proof.\\n          a = proofs[proofPos++];\\n        }\\n\\n        // The second part of the hashed pair is never a proof as hashing two proofs would result in a\\n        // hash that can already be computed offchain.\\n        bytes32 b;\\n        if (leafPos \\u003c leavesLen) {\\n          b = leaves[leafPos++];\\n        } else {\\n          b = hashes[hashPos++];\\n        }\\n\\n        if (!(hashPos \\u003c= i)) revert InvalidProof();\\n\\n        hashes[i] = _hashPair(a, b);\\n      }\\n      if (!(hashPos == totalHashes - 1 \\u0026\\u0026 leafPos == leavesLen \\u0026\\u0026 proofPos == proofsLen)) revert InvalidProof();\\n      // Return the last hash.\\n      return hashes[totalHashes - 1];\\n    }\\n  }\\n\\n  /// @notice Hashes two bytes32 objects in their given order, prepended by the INTERNAL_DOMAIN_SEPARATOR.\\n  function _hashInternalNode(bytes32 left, bytes32 right) private pure returns (bytes32 hash) {\\n    return keccak256(abi.encode(INTERNAL_DOMAIN_SEPARATOR, left, right));\\n  }\\n\\n  /// @notice Hashes two bytes32 objects. The order is taken into account, using the lower value first.\\n  function _hashPair(bytes32 a, bytes32 b) private pure returns (bytes32) {\\n    return a \\u003c b ? _hashInternalNode(a, b) : _hashInternalNode(b, a);\\n  }\\n}\\n\"},\"contracts/test/helpers/MessageHasher.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nimport {Client} from \\\"../../libraries/Client.sol\\\";\\nimport {Internal} from \\\"../../libraries/Internal.sol\\\";\\n\\n/// @notice MessageHasher is a contract that utility functions to hash an Any2EVMRampMessage\\n/// and encode various preimages for the final hash of the message.\\n/// @dev This is only deployed in tests and is not part of the production contracts.\\ncontract MessageHasher {\\n  function hash(Internal.Any2EVMRampMessage memory message, bytes memory onRamp) public pure returns (bytes32) {\\n    return Internal._hash(\\n      message,\\n      keccak256(\\n        abi.encode(\\n          Internal.ANY_2_EVM_MESSAGE_HASH,\\n          message.header.sourceChainSelector,\\n          message.header.destChainSelector,\\n          keccak256(onRamp)\\n        )\\n      )\\n    );\\n  }\\n\\n  function encodeAny2EVMTokenAmountsHashPreimage(\\n    Internal.Any2EVMTokenTransfer[] memory tokenAmounts\\n  ) public pure returns (bytes memory) {\\n    return abi.encode(tokenAmounts);\\n  }\\n\\n  function encodeEVM2AnyTokenAmountsHashPreimage(\\n    Internal.EVM2AnyTokenTransfer[] memory tokenAmount\\n  ) public pure returns (bytes memory) {\\n    return abi.encode(tokenAmount);\\n  }\\n\\n  function encodeMetadataHashPreimage(\\n    bytes32 any2EVMMessageHash,\\n    uint64 sourceChainSelector,\\n    uint64 destChainSelector,\\n    bytes32 onRampHash\\n  ) public pure returns (bytes memory) {\\n    return abi.encode(any2EVMMessageHash, sourceChainSelector, destChainSelector, onRampHash);\\n  }\\n\\n  function encodeFixedSizeFieldsHashPreimage(\\n    bytes32 messageId,\\n    address receiver,\\n    uint64 sequenceNumber,\\n    uint256 gasLimit,\\n    uint64 nonce\\n  ) public pure returns (bytes memory) {\\n    return abi.encode(messageId, receiver, sequenceNumber, gasLimit, nonce);\\n  }\\n\\n  function encodeFinalHashPreimage(\\n    bytes32 leafDomainSeparator,\\n    bytes32 metaDataHash,\\n    bytes32 fixedSizeFieldsHash,\\n    bytes32 senderHash,\\n    bytes32 dataHash,\\n    bytes32 tokenAmountsHash\\n  ) public pure returns (bytes memory) {\\n    return abi.encode(leafDomainSeparator, metaDataHash, fixedSizeFieldsHash, senderHash, dataHash, tokenAmountsHash);\\n  }\\n\\n  function encodeEVMExtraArgsV1(\\n    Client.EVMExtraArgsV1 memory extraArgs\\n  ) public pure returns (bytes memory) {\\n    return Client._argsToBytes(extraArgs);\\n  }\\n\\n  function encodeEVMExtraArgsV2(\\n    Client.GenericExtraArgsV2 memory extraArgs\\n  ) public pure returns (bytes memory) {\\n    return Client._argsToBytes(extraArgs);\\n  }\\n\\n  function encodeGenericExtraArgsV2(\\n    Client.GenericExtraArgsV2 memory extraArgs\\n  ) public pure returns (bytes memory) {\\n    return Client._argsToBytes(extraArgs);\\n  }\\n\\n  function decodeEVMExtraArgsV1(\\n    uint256 gasLimit\\n  ) public pure returns (Client.EVMExtraArgsV1 memory) {\\n    return Client.EVMExtraArgsV1(gasLimit);\\n  }\\n\\n  function decodeGenericExtraArgsV2(\\n    uint256 gasLimit,\\n    bool allowOutOfOrderExecution\\n  ) public pure returns (Client.GenericExtraArgsV2 memory) {\\n    return Client.GenericExtraArgsV2({gasLimit: gasLimit, allowOutOfOrderExecution: allowOutOfOrderExecution});\\n  }\\n\\n  function decodeEVMExtraArgsV2(\\n    uint256 gasLimit,\\n    bool allowOutOfOrderExecution\\n  ) public pure returns (Client.GenericExtraArgsV2 memory) {\\n    return Client.GenericExtraArgsV2({gasLimit: gasLimit, allowOutOfOrderExecution: allowOutOfOrderExecution});\\n  }\\n\\n  function encodeSVMExtraArgsV1(\\n    Client.SVMExtraArgsV1 memory extraArgs\\n  ) public pure returns (bytes memory) {\\n    return Client._svmArgsToBytes(extraArgs);\\n  }\\n\\n  /// @notice used offchain to decode an encoded SVMExtraArgsV1 struct.\\n  /// @dev The unrolled version fails due to differences in encoding when the accounts[] array\\n  /// is empty or not.\\n  function decodeSVMExtraArgsStruct(\\n    Client.SVMExtraArgsV1 memory extraArgs\\n  )\\n    public\\n    pure\\n    returns (\\n      uint32 computeUnits,\\n      uint64 accountIsWritableBitmap,\\n      bool allowOutOfOrderExecution,\\n      bytes32 tokenReceiver,\\n      bytes32[] memory accounts\\n    )\\n  {\\n    return (\\n      extraArgs.computeUnits,\\n      extraArgs.accountIsWritableBitmap,\\n      extraArgs.allowOutOfOrderExecution,\\n      extraArgs.tokenReceiver,\\n      extraArgs.accounts\\n    );\\n  }\\n}\\n\"}}}"
