// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package usdc_reader_tester

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/test/helpers/USDCReaderTester.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/test/helpers/USDCReaderTester.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\ncontract USDCReaderTester {\\n  event MessageSent(bytes);\\n\\n  // emitMessageSent reflects the logic from Circle's MessageTransmitter emitting MeseageSent(bytes) events\\n  // https://github.com/circlefin/evm-cctp-contracts/blob/****************************************/src/MessageTransmitter.sol#L41\\n  // https://github.com/circlefin/evm-cctp-contracts/blob/****************************************/src/MessageTransmitter.sol#L365\\n  function emitMessageSent(\\n    uint32 version,\\n    uint32 sourceDomain,\\n    uint32 destinationDomain,\\n    bytes32 recipient,\\n    bytes32 destinationCaller,\\n    bytes32 sender,\\n    uint64 nonce,\\n    bytes calldata messageBody\\n  ) external {\\n    bytes memory _message =\\n      _formatMessage(version, sourceDomain, destinationDomain, nonce, sender, recipient, destinationCaller, messageBody);\\n    emit MessageSent(_message);\\n  }\\n\\n  /**\\n   * @notice Returns formatted (packed) message with provided fields\\n   * It's a copy paste of the Message._formatMessage() call in MessageTransmitter.sol\\n   * https://github.com/circlefin/evm-cctp-contracts/blob/****************************************/src/messages/Message.sol#L54C1-L65C9\\n   * Check the chainlink-ccip repo for the offchain implementation of matching this format\\n   * @param _msgVersion the version of the message format\\n   * @param _msgSourceDomain Domain of home chain\\n   * @param _msgDestinationDomain Domain of destination chain\\n   * @param _msgNonce Destination-specific nonce\\n   * @param _msgSender Address of sender on source chain as bytes32\\n   * @param _msgRecipient Address of recipient on destination chain as bytes32\\n   * @param _msgDestinationCaller Address of caller on destination chain as bytes32\\n   * @param _msgRawBody Raw bytes of message body\\n   * @return Formatted message\\n   *\\n   */\\n  function _formatMessage(\\n    uint32 _msgVersion,\\n    uint32 _msgSourceDomain,\\n    uint32 _msgDestinationDomain,\\n    uint64 _msgNonce,\\n    bytes32 _msgSender,\\n    bytes32 _msgRecipient,\\n    bytes32 _msgDestinationCaller,\\n    bytes memory _msgRawBody\\n  ) internal pure returns (bytes memory) {\\n    return abi.encodePacked(\\n      _msgVersion,\\n      _msgSourceDomain,\\n      _msgDestinationDomain,\\n      _msgNonce,\\n      _msgSender,\\n      _msgRecipient,\\n      _msgDestinationCaller,\\n      _msgRawBody\\n    );\\n  }\\n}\\n\"}}}"
