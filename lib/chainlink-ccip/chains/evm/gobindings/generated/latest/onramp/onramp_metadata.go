// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package onramp

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/interfaces/IEVM2AnyOnRampClient.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IFeeQuoter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IMessageInterceptor.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/INonceManager.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IPool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IPriceRegistry.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IRMNRemote.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IRouter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/ITokenAdminRegistry.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Client.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Internal.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/MerkleMultiProof.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Pool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/USDPriceWith18Decimals.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/onRamp/OnRamp.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/draft-IERC20Permit.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/utils/SafeERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Address.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IEVM2AnyOnRampClient.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IPoolV1} from \\\"./IPool.sol\\\";\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\n\\nimport {IERC20} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\n\\ninterface IEVM2AnyOnRampClient {\\n  /// @notice Get the fee for a given ccip message.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param message The message to calculate the cost for.\\n  /// @return fee The calculated fee.\\n  function getFee(uint64 destChainSelector, Client.EVM2AnyMessage calldata message) external view returns (uint256 fee);\\n\\n  /// @notice Get the pool for a specific token.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param sourceToken The source chain token to get the pool for.\\n  /// @return pool Token pool.\\n  function getPoolBySourceToken(uint64 destChainSelector, IERC20 sourceToken) external view returns (IPoolV1);\\n\\n  /// @notice Gets a list of all supported source chain tokens.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @return tokens The addresses of all tokens that this onRamp supports the given destination chain.\\n  function getSupportedTokens(\\n    uint64 destChainSelector\\n  ) external view returns (address[] memory tokens);\\n\\n  /// @notice Send a message to the remote chain.\\n  /// @dev only callable by the Router.\\n  /// @dev approve() must have already been called on the token using the this ramp address as the spender.\\n  /// @dev if the contract is paused, this function will revert.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param message Message struct to send.\\n  /// @param feeTokenAmount Amount of fee tokens for payment.\\n  /// @param originalSender The original initiator of the CCIP request.\\n  /// @return messageId The message id.\\n  function forwardFromRouter(\\n    uint64 destChainSelector,\\n    Client.EVM2AnyMessage memory message,\\n    uint256 feeTokenAmount,\\n    address originalSender\\n  ) external returns (bytes32);\\n}\\n\"},\"contracts/interfaces/IFeeQuoter.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {IPriceRegistry} from \\\"./IPriceRegistry.sol\\\";\\n\\ninterface IFeeQuoter is IPriceRegistry {\\n  /// @notice Validates the ccip message \\u0026 returns the fee.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param message The message to get quote for.\\n  /// @return feeTokenAmount The amount of fee token needed for the fee, in smallest denomination of the fee token.\\n  function getValidatedFee(\\n    uint64 destChainSelector,\\n    Client.EVM2AnyMessage calldata message\\n  ) external view returns (uint256 feeTokenAmount);\\n\\n  /// @notice Converts the extraArgs to the latest version and returns the converted message fee in juels.\\n  /// @notice Validates pool return data.\\n  /// @param destChainSelector destination chain selector to process, must be a configured valid chain.\\n  /// @param feeToken token address used to pay for message fees, must be a configured valid fee token.\\n  /// @param feeTokenAmount Fee token amount.\\n  /// @param extraArgs Message extra args that were passed in by the client.\\n  /// @param messageReceiver Message receiver address in bytes from EVM2AnyMessage.receiver\\n  /// @return msgFeeJuels message fee in juels.\\n  /// @return isOutOfOrderExecution true if the message should be executed out of order.\\n  /// @return convertedExtraArgs extra args converted to the latest family-specific args version.\\n  /// @return tokenReceiver token receiver address in bytes on destination chain\\n  function processMessageArgs(\\n    uint64 destChainSelector,\\n    address feeToken,\\n    uint256 feeTokenAmount,\\n    bytes calldata extraArgs,\\n    bytes calldata messageReceiver\\n  )\\n    external\\n    view\\n    returns (\\n      uint256 msgFeeJuels,\\n      bool isOutOfOrderExecution,\\n      bytes memory convertedExtraArgs,\\n      bytes memory tokenReceiver\\n    );\\n\\n  /// @notice Validates pool return data.\\n  /// @param destChainSelector Destination chain selector to which the token amounts are sent to.\\n  /// @param onRampTokenTransfers Token amounts with populated pool return data.\\n  /// @param sourceTokenAmounts Token amounts originally sent in a Client.EVM2AnyMessage message.\\n  /// @return destExecDataPerToken Destination chain execution data.\\n  function processPoolReturnData(\\n    uint64 destChainSelector,\\n    Internal.EVM2AnyTokenTransfer[] calldata onRampTokenTransfers,\\n    Client.EVMTokenAmount[] calldata sourceTokenAmounts\\n  ) external view returns (bytes[] memory destExecDataPerToken);\\n}\\n\"},\"contracts/interfaces/IMessageInterceptor.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\n\\n/// @notice Interface for plug-in message hook contracts that intercept OffRamp \\u0026 OnRamp messages and perform\\n/// validations / state changes on top of the messages. The interceptor functions are expected to revert\\n/// on validation failures.\\ninterface IMessageInterceptor {\\n  /// @notice Common error that can be thrown on validation failures and used by consumers.\\n  /// @param errorReason abi encoded revert reason.\\n  error MessageValidationError(bytes errorReason);\\n\\n  /// @notice Intercepts \\u0026 validates the given OffRamp message. Reverts on validation failure.\\n  /// @param message to validate.\\n  function onInboundMessage(\\n    Client.Any2EVMMessage memory message\\n  ) external;\\n\\n  /// @notice Intercepts \\u0026 validates the given OnRamp message. Reverts on validation failure.\\n  /// @param destChainSelector remote destination chain selector where the message is being sent to.\\n  /// @param message to validate.\\n  function onOutboundMessage(uint64 destChainSelector, Client.EVM2AnyMessage memory message) external;\\n}\\n\"},\"contracts/interfaces/INonceManager.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice Contract interface that allows managing sender nonces.\\ninterface INonceManager {\\n  /// @notice Increments the outbound nonce for a given sender on a given destination chain.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param sender The sender address.\\n  /// @return incrementedOutboundNonce The new outbound nonce.\\n  function getIncrementedOutboundNonce(uint64 destChainSelector, address sender) external returns (uint64);\\n\\n  /// @notice Increments the inbound nonce for a given sender on a given source chain.\\n  /// @notice The increment is only applied if the resulting nonce matches the expectedNonce.\\n  /// @param sourceChainSelector The destination chain selector.\\n  /// @param expectedNonce The expected inbound nonce.\\n  /// @param sender The encoded sender address.\\n  /// @return incremented True if the nonce was incremented, false otherwise.\\n  function incrementInboundNonce(\\n    uint64 sourceChainSelector,\\n    uint64 expectedNonce,\\n    bytes calldata sender\\n  ) external returns (bool);\\n}\\n\"},\"contracts/interfaces/IPool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\n\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\\\";\\n\\n/// @notice Shared public interface for multiple V1 pool types.\\n/// Each pool type handles a different child token model e.g. lock/unlock, mint/burn.\\ninterface IPoolV1 is IERC165 {\\n  /// @notice Lock tokens into the pool or burn the tokens.\\n  /// @param lockOrBurnIn Encoded data fields for the processing of tokens on the source chain.\\n  /// @return lockOrBurnOut Encoded data fields for the processing of tokens on the destination chain.\\n  function lockOrBurn(\\n    Pool.LockOrBurnInV1 calldata lockOrBurnIn\\n  ) external returns (Pool.LockOrBurnOutV1 memory lockOrBurnOut);\\n\\n  /// @notice Releases or mints tokens to the receiver address.\\n  /// @param releaseOrMintIn All data required to release or mint tokens.\\n  /// @return releaseOrMintOut The amount of tokens released or minted on the local chain, denominated\\n  /// in the local token's decimals.\\n  /// @dev The offramp asserts that the balanceOf of the receiver has been incremented by exactly the number\\n  /// of tokens that is returned in ReleaseOrMintOutV1.destinationAmount. If the amounts do not match, the tx reverts.\\n  function releaseOrMint(\\n    Pool.ReleaseOrMintInV1 calldata releaseOrMintIn\\n  ) external returns (Pool.ReleaseOrMintOutV1 memory);\\n\\n  /// @notice Checks whether a remote chain is supported in the token pool.\\n  /// @param remoteChainSelector The selector of the remote chain.\\n  /// @return true if the given chain is a permissioned remote chain.\\n  function isSupportedChain(\\n    uint64 remoteChainSelector\\n  ) external view returns (bool);\\n\\n  /// @notice Returns if the token pool supports the given token.\\n  /// @param token The address of the token.\\n  /// @return true if the token is supported by the pool.\\n  function isSupportedToken(\\n    address token\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/IPriceRegistry.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\n\\ninterface IPriceRegistry {\\n  /// @notice Update the price for given tokens and gas prices for given chains.\\n  /// @param priceUpdates The price updates to apply.\\n  function updatePrices(\\n    Internal.PriceUpdates memory priceUpdates\\n  ) external;\\n\\n  /// @notice Get the `tokenPrice` for a given token.\\n  /// @param token The token to get the price for.\\n  /// @return tokenPrice The tokenPrice for the given token.\\n  function getTokenPrice(\\n    address token\\n  ) external view returns (Internal.TimestampedPackedUint224 memory);\\n\\n  /// @notice Get the `tokenPrice` for a given token, checks if the price is valid.\\n  /// @param token The token to get the price for.\\n  /// @return tokenPrice The tokenPrice for the given token if it exists and is valid.\\n  function getValidatedTokenPrice(\\n    address token\\n  ) external view returns (uint224);\\n\\n  /// @notice Get the `tokenPrice` for an array of tokens.\\n  /// @param tokens The tokens to get prices for.\\n  /// @return tokenPrices The tokenPrices for the given tokens.\\n  function getTokenPrices(\\n    address[] calldata tokens\\n  ) external view returns (Internal.TimestampedPackedUint224[] memory);\\n\\n  /// @notice Get an encoded `gasPrice` for a given destination chain ID.\\n  /// The 224-bit result encodes necessary gas price components.\\n  /// On L1 chains like Ethereum or Avax, the only component is the gas price.\\n  /// On Optimistic Rollups, there are two components - the L2 gas price, and L1 base fee for data availability.\\n  /// On future chains, there could be more or differing price components.\\n  /// PriceRegistry does not contain chain-specific logic to parse destination chain price components.\\n  /// @param destChainSelector The destination chain to get the price for.\\n  /// @return gasPrice The encoded gasPrice for the given destination chain ID.\\n  function getDestinationChainGasPrice(\\n    uint64 destChainSelector\\n  ) external view returns (Internal.TimestampedPackedUint224 memory);\\n\\n  /// @notice Gets the fee token price and the gas price, both denominated in dollars.\\n  /// @param token The source token to get the price for.\\n  /// @param destChainSelector The destination chain to get the gas price for.\\n  /// @return tokenPrice The price of the feeToken in 1e18 dollars per base unit.\\n  /// @return gasPrice The price of gas in 1e18 dollars per base unit.\\n  function getTokenAndGasPrices(\\n    address token,\\n    uint64 destChainSelector\\n  ) external view returns (uint224 tokenPrice, uint224 gasPrice);\\n\\n  /// @notice Convert a given token amount to target token amount.\\n  /// @param fromToken The given token address.\\n  /// @param fromTokenAmount The given token amount.\\n  /// @param toToken The target token address.\\n  /// @return toTokenAmount The target token amount.\\n  function convertTokenAmount(\\n    address fromToken,\\n    uint256 fromTokenAmount,\\n    address toToken\\n  ) external view returns (uint256 toTokenAmount);\\n\\n  /// @notice Get the list of fee tokens.\\n  /// @return feeTokens The tokens set as fee tokens.\\n  function getFeeTokens() external view returns (address[] memory);\\n}\\n\"},\"contracts/interfaces/IRMNRemote.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\n\\n/// @notice This interface contains the only RMN-related functions that might be used on-chain by other CCIP contracts.\\ninterface IRMNRemote {\\n  /// @notice signature components from RMN nodes.\\n  struct Signature {\\n    bytes32 r;\\n    bytes32 s;\\n  }\\n\\n  /// @notice Verifies signatures of RMN nodes, on dest lane updates as provided in the CommitReport.\\n  /// @param offRampAddress is not inferred by msg.sender, in case the call is made through RMNProxy.\\n  /// @param merkleRoots must be well formed, and is a representation of the CommitReport received from the oracles.\\n  /// @param signatures rmnNodes ECDSA sigs, only r \\u0026 s, must be sorted in ascending order by signer address.\\n  /// @dev Will revert if verification fails.\\n  function verify(\\n    address offRampAddress,\\n    Internal.MerkleRoot[] memory merkleRoots,\\n    Signature[] memory signatures\\n  ) external view;\\n\\n  /// @notice gets the current set of cursed subjects.\\n  /// @return subjects the list of cursed subjects.\\n  function getCursedSubjects() external view returns (bytes16[] memory subjects);\\n\\n  /// @notice If there is an active global or legacy curse, this function returns true.\\n  /// @return bool true if there is an active global curse.\\n  function isCursed() external view returns (bool);\\n\\n  /// @notice If there is an active global curse, or an active curse for `subject`, this function returns true.\\n  /// @param subject To check whether a particular chain is cursed, set to bytes16(uint128(chainSelector)).\\n  /// @return bool true if the provided subject is cured *or* if there is an active global curse.\\n  function isCursed(\\n    bytes16 subject\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/IRouter.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\n\\ninterface IRouter {\\n  error OnlyOffRamp();\\n\\n  /// @notice Route the message to its intended receiver contract.\\n  /// @param message Client.Any2EVMMessage struct.\\n  /// @param gasForCallExactCheck of params for exec.\\n  /// @param gasLimit set of params for exec.\\n  /// @param receiver set of params for exec.\\n  /// @dev if the receiver is a contracts that signals support for CCIP execution through EIP-165.\\n  /// the contract is called. If not, only tokens are transferred.\\n  /// @return success A boolean value indicating whether the ccip message was received without errors.\\n  /// @return retBytes A bytes array containing return data form CCIP receiver.\\n  /// @return gasUsed the gas used by the external customer call. Does not include any overhead.\\n  function routeMessage(\\n    Client.Any2EVMMessage calldata message,\\n    uint16 gasForCallExactCheck,\\n    uint256 gasLimit,\\n    address receiver\\n  ) external returns (bool success, bytes memory retBytes, uint256 gasUsed);\\n\\n  /// @notice Returns the configured onramp for a specific destination chain.\\n  /// @param destChainSelector The destination chain Id to get the onRamp for.\\n  /// @return onRampAddress The address of the onRamp.\\n  function getOnRamp(\\n    uint64 destChainSelector\\n  ) external view returns (address onRampAddress);\\n\\n  /// @notice Return true if the given offRamp is a configured offRamp for the given source chain.\\n  /// @param sourceChainSelector The source chain selector to check.\\n  /// @param offRamp The address of the offRamp to check.\\n  function isOffRamp(uint64 sourceChainSelector, address offRamp) external view returns (bool isOffRamp);\\n}\\n\"},\"contracts/interfaces/ITokenAdminRegistry.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.0;\\n\\ninterface ITokenAdminRegistry {\\n  /// @notice Returns the pool for the given token.\\n  function getPool(\\n    address token\\n  ) external view returns (address);\\n\\n  /// @notice Proposes an administrator for the given token as pending administrator.\\n  /// @param localToken The token to register the administrator for.\\n  /// @param administrator The administrator to register.\\n  function proposeAdministrator(address localToken, address administrator) external;\\n\\n  /// @notice Accepts the administrator role for a token.\\n  /// @param localToken The token to accept the administrator role for.\\n  /// @dev This function can only be called by the pending administrator.\\n  function acceptAdminRole(\\n    address localToken\\n  ) external;\\n\\n  /// @notice Sets the pool for a token. Setting the pool to address(0) effectively delists the token\\n  /// from CCIP. Setting the pool to any other address enables the token on CCIP.\\n  /// @param localToken The token to set the pool for.\\n  /// @param pool The pool to set for the token.\\n  function setPool(address localToken, address pool) external;\\n\\n  /// @notice Transfers the administrator role for a token to a new address with a 2-step process.\\n  /// @param localToken The token to transfer the administrator role for.\\n  /// @param newAdmin The address to transfer the administrator role to. Can be address(0) to cancel\\n  /// a pending transfer.\\n  /// @dev The new admin must call `acceptAdminRole` to accept the role.\\n  function transferAdminRole(address localToken, address newAdmin) external;\\n}\\n\"},\"contracts/libraries/Client.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n// End consumer library.\\nlibrary Client {\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct EVMTokenAmount {\\n    address token; // token address on the local chain.\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  struct Any2EVMMessage {\\n    bytes32 messageId; // MessageId corresponding to ccipSend on source.\\n    uint64 sourceChainSelector; // Source chain selector.\\n    bytes sender; // abi.decode(sender) if coming from an EVM chain.\\n    bytes data; // payload sent in original message.\\n    EVMTokenAmount[] destTokenAmounts; // Tokens and their amounts in their destination chain representation.\\n  }\\n\\n  // If extraArgs is empty bytes, the default is 200k gas limit.\\n  struct EVM2AnyMessage {\\n    bytes receiver; // abi.encode(receiver address) for dest EVM chains.\\n    bytes data; // Data payload.\\n    EVMTokenAmount[] tokenAmounts; // Token transfers.\\n    address feeToken; // Address of feeToken. address(0) means you will send msg.value.\\n    bytes extraArgs; // Populate this with _argsToBytes(EVMExtraArgsV2).\\n  }\\n\\n  // Tag to indicate only a gas limit. Only usable for EVM as destination chain.\\n  bytes4 public constant EVM_EXTRA_ARGS_V1_TAG = 0x97a657c9;\\n\\n  struct EVMExtraArgsV1 {\\n    uint256 gasLimit;\\n  }\\n\\n  function _argsToBytes(\\n    EVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(EVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n\\n  // Tag to indicate a gas limit (or dest chain equivalent processing units) and Out Of Order Execution. This tag is\\n  // available for multiple chain families. If there is no chain family specific tag, this is the default available\\n  // for a chain.\\n  // Note: not available for Solana VM based chains.\\n  bytes4 public constant GENERIC_EXTRA_ARGS_V2_TAG = 0x181dcf10;\\n\\n  /// @param gasLimit: gas limit for the callback on the destination chain.\\n  /// @param allowOutOfOrderExecution: if true, it indicates that the message can be executed in any order relative to\\n  /// other messages from the same sender. This value's default varies by chain. On some chains, a particular value is\\n  /// enforced, meaning if the expected value is not set, the message request will revert.\\n  /// @dev Fully compatible with the previously existing EVMExtraArgsV2.\\n  struct GenericExtraArgsV2 {\\n    uint256 gasLimit;\\n    bool allowOutOfOrderExecution;\\n  }\\n\\n  // Extra args tag for chains that use the Solana VM.\\n  bytes4 public constant SVM_EXTRA_ARGS_V1_TAG = 0x1f3b3aba;\\n\\n  struct SVMExtraArgsV1 {\\n    uint32 computeUnits;\\n    uint64 accountIsWritableBitmap;\\n    bool allowOutOfOrderExecution;\\n    bytes32 tokenReceiver;\\n    // Additional accounts needed for execution of CCIP receiver. Must be empty if message.receiver is zero.\\n    // Token transfer related accounts are specified in the token pool lookup table on SVM.\\n    bytes32[] accounts;\\n  }\\n\\n  /// @dev The maximum number of accounts that can be passed in SVMExtraArgs.\\n  uint256 public constant SVM_EXTRA_ARGS_MAX_ACCOUNTS = 64;\\n\\n  /// @dev The expected static payload size of a token transfer when Borsh encoded and submitted to SVM.\\n  /// TokenPool extra data and offchain data sizes are dynamic, and should be accounted for separately.\\n  uint256 public constant SVM_TOKEN_TRANSFER_DATA_OVERHEAD = (4 + 32) // source_pool\\n    + 32 // token_address\\n    + 4 // gas_amount\\n    + 4 // extra_data overhead\\n    + 32 // amount\\n    + 32 // size of the token lookup table account\\n    + 32 // token-related accounts in the lookup table, over-estimated to 32, typically between 11 - 13\\n    + 32 // token account belonging to the token receiver, e.g ATA, not included in the token lookup table\\n    + 32 // per-chain token pool config, not included in the token lookup table\\n    + 32 // per-chain token billing config, not always included in the token lookup table\\n    + 32; // OffRamp pool signer PDA, not included in the token lookup table\\n\\n  /// @dev Number of overhead accounts needed for message execution on SVM.\\n  /// @dev These are message.receiver, and the OffRamp Signer PDA specific to the receiver.\\n  uint256 public constant SVM_MESSAGING_ACCOUNTS_OVERHEAD = 2;\\n\\n  /// @dev The size of each SVM account address in bytes.\\n  uint256 public constant SVM_ACCOUNT_BYTE_SIZE = 32;\\n\\n  function _argsToBytes(\\n    GenericExtraArgsV2 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(GENERIC_EXTRA_ARGS_V2_TAG, extraArgs);\\n  }\\n\\n  function _svmArgsToBytes(\\n    SVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(SVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n}\\n\"},\"contracts/libraries/Internal.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {MerkleMultiProof} from \\\"../libraries/MerkleMultiProof.sol\\\";\\n\\n/// @notice Library for CCIP internal definitions common to multiple contracts.\\n/// @dev The following is a non-exhaustive list of \\\"known issues\\\" for CCIP:\\n/// - We could implement yield claiming for Blast. This is not worth the custom code path on non-blast chains.\\n/// - uint32 is used for timestamps, which will overflow in 2106. This is not a concern for the current use case, as we\\n/// expect to have migrated to a new version by then.\\nlibrary Internal {\\n  error InvalidEVMAddress(bytes encodedAddress);\\n  error Invalid32ByteAddress(bytes encodedAddress);\\n\\n  /// @dev We limit return data to a selector plus 4 words. This is to avoid malicious contracts from returning\\n  /// large amounts of data and causing repeated out-of-gas scenarios.\\n  uint16 internal constant MAX_RET_BYTES = 4 + 4 * 32;\\n  /// @dev The expected number of bytes returned by the balanceOf function.\\n  uint256 internal constant MAX_BALANCE_OF_RET_BYTES = 32;\\n\\n  /// @dev The address used to send calls for gas estimation.\\n  /// You only need to use this address if the minimum gas limit specified by the user is not actually enough to execute the\\n  /// given message and you're attempting to estimate the actual necessary gas limit\\n  address public constant GAS_ESTIMATION_SENDER = address(0xC11C11C11C11C11C11C11C11C11C11C11C11C1);\\n\\n  /// @notice A collection of token price and gas price updates.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct PriceUpdates {\\n    TokenPriceUpdate[] tokenPriceUpdates;\\n    GasPriceUpdate[] gasPriceUpdates;\\n  }\\n\\n  /// @notice Token price in USD.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct TokenPriceUpdate {\\n    address sourceToken; // Source token.\\n    uint224 usdPerToken; // 1e18 USD per 1e18 of the smallest token denomination.\\n  }\\n\\n  /// @notice Gas price for a given chain in USD, its value may contain tightly packed fields.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct GasPriceUpdate {\\n    uint64 destChainSelector; // Destination chain selector.\\n    uint224 usdPerUnitGas; // 1e18 USD per smallest unit (e.g. wei) of destination chain gas.\\n  }\\n\\n  /// @notice A timestamped uint224 value that can contain several tightly packed fields.\\n  struct TimestampedPackedUint224 {\\n    uint224 value; // ────╮ Value in uint224, packed.\\n    uint32 timestamp; // ─╯ Timestamp of the most recent price update.\\n  }\\n\\n  /// @dev Gas price is stored in 112-bit unsigned int. uint224 can pack 2 prices.\\n  /// When packing L1 and L2 gas prices, L1 gas price is left-shifted to the higher-order bits.\\n  /// Using uint8 type, which cannot be higher than other bit shift operands, to avoid shift operand type warning.\\n  uint8 public constant GAS_PRICE_BITS = 112;\\n\\n  struct SourceTokenData {\\n    // The source pool address, abi encoded. This value is trusted as it was obtained through the onRamp. It can be\\n    // relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint32 destGasAmount; // The amount of gas available for the releaseOrMint and balanceOf calls on the offRamp\\n  }\\n\\n  /// @notice Report that is submitted by the execution DON at the execution phase, including chain selector data.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct ExecutionReport {\\n    uint64 sourceChainSelector; // Source chain selector for which the report is submitted.\\n    Any2EVMRampMessage[] messages;\\n    // Contains a bytes array for each message, each inner bytes array contains bytes per transferred token.\\n    bytes[][] offchainTokenData;\\n    bytes32[] proofs;\\n    uint256 proofFlagBits;\\n  }\\n\\n  /// @dev Any2EVMRampMessage struct has 10 fields, including 3 variable unnested arrays, sender, data and tokenAmounts.\\n  /// Each variable array takes 1 more slot to store its length.\\n  /// When abi encoded, excluding array contents, Any2EVMMessage takes up a fixed number of 13 slots, 32 bytes each.\\n  /// Assume 1 slot for sender\\n  /// For structs that contain arrays, 1 more slot is added to the front, reaching a total of 14.\\n  /// The fixed bytes does not cover struct data (this is represented by MESSAGE_FIXED_BYTES_PER_TOKEN)\\n  uint256 public constant MESSAGE_FIXED_BYTES = 32 * 15;\\n\\n  /// @dev Any2EVMTokensTransfer struct bytes length\\n  /// 0x20\\n  /// sourcePoolAddress_offset\\n  /// destTokenAddress\\n  /// destGasAmount\\n  /// extraData_offset\\n  /// amount\\n  /// sourcePoolAddress_length\\n  /// sourcePoolAddress_content // assume 1 slot\\n  /// extraData_length // contents billed separately\\n  uint256 public constant MESSAGE_FIXED_BYTES_PER_TOKEN = 32 * (4 + (3 + 2));\\n\\n  bytes32 internal constant ANY_2_EVM_MESSAGE_HASH = keccak256(\\\"Any2EVMMessageHashV1\\\");\\n  bytes32 internal constant EVM_2_ANY_MESSAGE_HASH = keccak256(\\\"EVM2AnyMessageHashV1\\\");\\n\\n  /// @dev Used to hash messages for multi-lane family-agnostic OffRamps.\\n  /// OnRamp hash(EVM2AnyMessage) != Any2EVMRampMessage.messageId.\\n  /// OnRamp hash(EVM2AnyMessage) != OffRamp hash(Any2EVMRampMessage).\\n  /// @param original OffRamp message to hash.\\n  /// @param metadataHash Hash preimage to ensure global uniqueness.\\n  /// @return hashedMessage hashed message as a keccak256.\\n  function _hash(Any2EVMRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.header.messageId,\\n            original.receiver,\\n            original.header.sequenceNumber,\\n            original.gasLimit,\\n            original.header.nonce\\n          )\\n        ),\\n        keccak256(original.sender),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts))\\n      )\\n    );\\n  }\\n\\n  function _hash(EVM2AnyRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.sender,\\n            original.header.sequenceNumber,\\n            original.header.nonce,\\n            original.feeToken,\\n            original.feeTokenAmount\\n          )\\n        ),\\n        keccak256(original.receiver),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts)),\\n        keccak256(original.extraArgs)\\n      )\\n    );\\n  }\\n\\n  /// @dev We disallow the first 1024 addresses to avoid calling into a range known for hosting precompiles. Calling\\n  /// into precompiles probably won't cause any issues, but to be safe we can disallow this range. It is extremely\\n  /// unlikely that anyone would ever be able to generate an address in this range. There is no official range of\\n  /// precompiles, but EIP-7587 proposes to reserve the range 0x100 to 0x1ff. Our range is more conservative, even\\n  /// though it might not be exhaustive for all chains, which is OK. We also disallow the zero address, which is a\\n  /// common practice.\\n  uint256 public constant EVM_PRECOMPILE_SPACE = 1024;\\n\\n  // According to the Aptos docs, the first 0xa addresses are reserved for precompiles.\\n  // https://github.com/aptos-labs/aptos-core/blob/main/aptos-move/framework/aptos-framework/doc/account.md#function-create_framework_reserved_account-1\\n  uint256 public constant APTOS_PRECOMPILE_SPACE = 0x0b;\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// EVM address space. If it isn't it will revert with an InvalidEVMAddress error, which we can catch and handle\\n  /// more gracefully than a revert from abi.decode.\\n  function _validateEVMAddress(\\n    bytes memory encodedAddress\\n  ) internal pure {\\n    if (encodedAddress.length != 32) revert InvalidEVMAddress(encodedAddress);\\n    uint256 encodedAddressUint = abi.decode(encodedAddress, (uint256));\\n    if (encodedAddressUint \\u003e type(uint160).max || encodedAddressUint \\u003c EVM_PRECOMPILE_SPACE) {\\n      revert InvalidEVMAddress(encodedAddress);\\n    }\\n  }\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// bounds of [minValue, uint256.max]. If it isn't it will revert with an Invalid32ByteAddress error.\\n  function _validate32ByteAddress(bytes memory encodedAddress, uint256 minValue) internal pure {\\n    if (encodedAddress.length != 32) revert Invalid32ByteAddress(encodedAddress);\\n    if (minValue \\u003e 0) {\\n      if (abi.decode(encodedAddress, (uint256)) \\u003c minValue) {\\n        revert Invalid32ByteAddress(encodedAddress);\\n      }\\n    }\\n  }\\n\\n  /// @notice Enum listing the possible message execution states within the offRamp contract.\\n  /// UNTOUCHED never executed.\\n  /// IN_PROGRESS currently being executed, used a replay protection.\\n  /// SUCCESS successfully executed. End state.\\n  /// FAILURE unsuccessfully executed, manual execution is now enabled.\\n  /// @dev RMN depends on this enum, if changing, please notify the RMN maintainers.\\n  enum MessageExecutionState {\\n    UNTOUCHED,\\n    IN_PROGRESS,\\n    SUCCESS,\\n    FAILURE\\n  }\\n\\n  /// @notice CCIP OCR plugin type, used to separate execution \\u0026 commit transmissions and configs.\\n  enum OCRPluginType {\\n    Commit,\\n    Execution\\n  }\\n\\n  /// @notice Family-agnostic header for OnRamp \\u0026 OffRamp messages.\\n  /// The messageId is not expected to match hash(message), since it may originate from another ramp family.\\n  struct RampMessageHeader {\\n    bytes32 messageId; // Unique identifier for the message, generated with the source chain's encoding scheme (i.e. not necessarily abi.encoded).\\n    uint64 sourceChainSelector; // ─╮ the chain selector of the source chain, note: not chainId.\\n    uint64 destChainSelector; //    │ the chain selector of the destination chain, note: not chainId.\\n    uint64 sequenceNumber; //       │ sequence number, not unique across lanes.\\n    uint64 nonce; // ───────────────╯ nonce for this lane for this sender, not unique across senders/lanes.\\n  }\\n\\n  struct EVM2AnyTokenTransfer {\\n    // The source pool EVM address. This value is trusted as it was obtained through the onRamp. It can be relied\\n    // upon by the destination pool to validate the source pool.\\n    address sourcePoolAddress;\\n    // The EVM address of the destination token.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n    // Destination chain data used to execute the token transfer on the destination chain. For an EVM destination, it\\n    // consists of the amount of gas available for the releaseOrMint and transfer calls made by the offRamp.\\n    bytes destExecData;\\n  }\\n\\n  struct Any2EVMTokenTransfer {\\n    // The source pool EVM address encoded to bytes. This value is trusted as it is obtained through the onRamp. It can\\n    // be relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    address destTokenAddress; // ─╮ Address of destination token\\n    uint32 destGasAmount; // ─────╯ The amount of gas available for the releaseOrMint and transfer calls on the offRamp.\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  /// @notice Family-agnostic message routed to an OffRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage), hash(Any2EVMRampMessage) != messageId due to encoding\\n  /// and parameter differences.\\n  struct Any2EVMRampMessage {\\n    RampMessageHeader header; // Message header.\\n    bytes sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    address receiver; // receiver address on the destination chain.\\n    uint256 gasLimit; // user supplied maximum gas amount available for dest chain execution.\\n    Any2EVMTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  /// @notice Family-agnostic message emitted from the OnRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage) due to encoding \\u0026 parameter differences.\\n  /// messageId = hash(EVM2AnyRampMessage) using the source EVM chain's encoding format.\\n  struct EVM2AnyRampMessage {\\n    RampMessageHeader header; // Message header.\\n    address sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    bytes receiver; // receiver address on the destination chain.\\n    bytes extraArgs; // destination-chain specific extra args, such as the gasLimit for EVM chains.\\n    address feeToken; // fee token.\\n    uint256 feeTokenAmount; // fee token amount.\\n    uint256 feeValueJuels; // fee amount in Juels.\\n    EVM2AnyTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector EVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_EVM = 0x2812d52c;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SVM = 0x1e10bdc4;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector APTOS\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_APTOS = 0xac77ffec;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SUI\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SUI = 0xc4e05953;\\n\\n  /// @dev Holds a merkle root and interval for a source chain so that an array of these can be passed in the CommitReport.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  /// @dev inefficient struct packing intentionally chosen to maintain order of specificity. Not a storage struct so impact is minimal.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct MerkleRoot {\\n    uint64 sourceChainSelector; // Remote source chain selector that the Merkle Root is scoped to\\n    bytes onRampAddress; //        Generic onRamp address, to support arbitrary sources; for EVM, use abi.encode\\n    uint64 minSeqNr; // ─────────╮ Minimum sequence number, inclusive\\n    uint64 maxSeqNr; // ─────────╯ Maximum sequence number, inclusive\\n    bytes32 merkleRoot; //         Merkle root covering the interval \\u0026 source chain messages\\n  }\\n}\\n\"},\"contracts/libraries/MerkleMultiProof.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nlibrary MerkleMultiProof {\\n  /// @notice Leaf domain separator, should be used as the first 32 bytes of a leaf's preimage.\\n  bytes32 internal constant LEAF_DOMAIN_SEPARATOR = 0x0000000000000000000000000000000000000000000000000000000000000000;\\n  /// @notice Internal domain separator, should be used as the first 32 bytes of an internal node's preimage.\\n  bytes32 internal constant INTERNAL_DOMAIN_SEPARATOR =\\n    0x0000000000000000000000000000000000000000000000000000000000000001;\\n\\n  uint256 internal constant MAX_NUM_HASHES = 256;\\n\\n  error InvalidProof();\\n  error LeavesCannotBeEmpty();\\n\\n  /// @notice Computes the root based on provided pre-hashed leaf nodes in leaves, internal nodes  in proofs, and using\\n  /// proofFlagBits' i-th bit to determine if an element of proofs or one of the previously computed leafs or internal\\n  /// nodes will be used for the i-th hash.\\n  /// @param leaves Should be pre-hashed and the first 32 bytes of a leaf's preimage should match LEAF_DOMAIN_SEPARATOR.\\n  /// @param proofs Hashes to be used instead of a leaf hash when the proofFlagBits indicates a proof should be used.\\n  /// @param proofFlagBits A single uint256 of which each bit indicates whether a leaf or a proof needs to be used in\\n  /// a hash operation.\\n  /// @dev the maximum number of hash operations it set to 256. Any input that would require more than 256 hashes to get\\n  /// to a root will revert.\\n  /// @dev For given input `leaves` = [a,b,c] `proofs` = [D] and `proofFlagBits` = 5\\n  ///     totalHashes = 3 + 1 - 1 = 3\\n  ///  ** round 1 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 0) \\u0026 1 = true\\n  ///    hashes[0] = hashPair(a, b)\\n  ///    (leafPos, hashPos, proofPos) = (2, 0, 0);\\n  ///\\n  ///  ** round 2 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 1) \\u0026 1 = false\\n  ///    hashes[1] = hashPair(D, c)\\n  ///    (leafPos, hashPos, proofPos) = (3, 0, 1);\\n  ///\\n  ///  ** round 3 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 2) \\u0026 1 = true\\n  ///    hashes[2] = hashPair(hashes[0], hashes[1])\\n  ///    (leafPos, hashPos, proofPos) = (3, 2, 1);\\n  ///\\n  ///    i = 3 and no longer \\u003c totalHashes. The algorithm is done\\n  ///    return hashes[totalHashes - 1] = hashes[2]; the last hash we computed.\\n  // We mark this function as internal to force it to be inlined in contracts that use it, but semantically it is public.\\n  function _merkleRoot(\\n    bytes32[] memory leaves,\\n    bytes32[] memory proofs,\\n    uint256 proofFlagBits\\n  ) internal pure returns (bytes32) {\\n    unchecked {\\n      uint256 leavesLen = leaves.length;\\n      uint256 proofsLen = proofs.length;\\n      if (leavesLen == 0) revert LeavesCannotBeEmpty();\\n      if (!(leavesLen \\u003c= MAX_NUM_HASHES + 1 \\u0026\\u0026 proofsLen \\u003c= MAX_NUM_HASHES + 1)) revert InvalidProof();\\n      uint256 totalHashes = leavesLen + proofsLen - 1;\\n      if (!(totalHashes \\u003c= MAX_NUM_HASHES)) revert InvalidProof();\\n      if (totalHashes == 0) {\\n        return leaves[0];\\n      }\\n      bytes32[] memory hashes = new bytes32[](totalHashes);\\n      (uint256 leafPos, uint256 hashPos, uint256 proofPos) = (0, 0, 0);\\n\\n      for (uint256 i = 0; i \\u003c totalHashes; ++i) {\\n        // Checks if the bit flag signals the use of a supplied proof or a leaf/previous hash.\\n        bytes32 a;\\n        if (proofFlagBits \\u0026 (1 \\u003c\\u003c i) == (1 \\u003c\\u003c i)) {\\n          // Use a leaf or a previously computed hash.\\n          if (leafPos \\u003c leavesLen) {\\n            a = leaves[leafPos++];\\n          } else {\\n            a = hashes[hashPos++];\\n          }\\n        } else {\\n          // Use a supplied proof.\\n          a = proofs[proofPos++];\\n        }\\n\\n        // The second part of the hashed pair is never a proof as hashing two proofs would result in a\\n        // hash that can already be computed offchain.\\n        bytes32 b;\\n        if (leafPos \\u003c leavesLen) {\\n          b = leaves[leafPos++];\\n        } else {\\n          b = hashes[hashPos++];\\n        }\\n\\n        if (!(hashPos \\u003c= i)) revert InvalidProof();\\n\\n        hashes[i] = _hashPair(a, b);\\n      }\\n      if (!(hashPos == totalHashes - 1 \\u0026\\u0026 leafPos == leavesLen \\u0026\\u0026 proofPos == proofsLen)) revert InvalidProof();\\n      // Return the last hash.\\n      return hashes[totalHashes - 1];\\n    }\\n  }\\n\\n  /// @notice Hashes two bytes32 objects in their given order, prepended by the INTERNAL_DOMAIN_SEPARATOR.\\n  function _hashInternalNode(bytes32 left, bytes32 right) private pure returns (bytes32 hash) {\\n    return keccak256(abi.encode(INTERNAL_DOMAIN_SEPARATOR, left, right));\\n  }\\n\\n  /// @notice Hashes two bytes32 objects. The order is taken into account, using the lower value first.\\n  function _hashPair(bytes32 a, bytes32 b) private pure returns (bytes32) {\\n    return a \\u003c b ? _hashInternalNode(a, b) : _hashInternalNode(b, a);\\n  }\\n}\\n\"},\"contracts/libraries/Pool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This library contains various token pool functions to aid constructing the return data.\\nlibrary Pool {\\n  // The tag used to signal support for the pool v1 standard.\\n  // bytes4(keccak256(\\\"CCIP_POOL_V1\\\"))\\n  bytes4 public constant CCIP_POOL_V1 = 0xaff2afbf;\\n\\n  // The number of bytes in the return data for a pool v1 releaseOrMint call.\\n  // This should match the size of the ReleaseOrMintOutV1 struct.\\n  uint16 public constant CCIP_POOL_V1_RET_BYTES = 32;\\n\\n  // The default max number of bytes in the return data for a pool v1 lockOrBurn call.\\n  // This data can be used to send information to the destination chain token pool. Can be overwritten\\n  // in the TokenTransferFeeConfig.destBytesOverhead if more data is required.\\n  uint32 public constant CCIP_LOCK_OR_BURN_V1_RET_BYTES = 32;\\n\\n  struct LockOrBurnInV1 {\\n    bytes receiver; //  The recipient of the tokens on the destination chain, abi encoded.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the destination chain.\\n    address originalSender; // ─────╯ The original sender of the tx on the source chain.\\n    uint256 amount; //  The amount of tokens to lock or burn, denominated in the source token's decimals.\\n    address localToken; //  The address on this chain of the token to lock or burn.\\n  }\\n\\n  struct LockOrBurnOutV1 {\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes destPoolData;\\n  }\\n\\n  struct ReleaseOrMintInV1 {\\n    bytes originalSender; //          The original sender of the tx on the source chain.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the source chain.\\n    address receiver; // ───────────╯ The recipient of the tokens on the destination chain.\\n    uint256 amount; //                The amount of tokens to release or mint, denominated in the source token's decimals.\\n    address localToken; //            The address on this chain of the token to release or mint.\\n    /// @dev WARNING: sourcePoolAddress should be checked prior to any processing of funds. Make sure it matches the\\n    /// expected pool address for the given remoteChainSelector.\\n    bytes sourcePoolAddress; //       The address of the source pool, abi encoded in the case of EVM chains.\\n    bytes sourcePoolData; //          The data received from the source pool to process the release or mint.\\n    /// @dev WARNING: offchainTokenData is untrusted data.\\n    bytes offchainTokenData; //       The offchain data to process the release or mint.\\n  }\\n\\n  struct ReleaseOrMintOutV1 {\\n    // The number of tokens released or minted on the destination chain, denominated in the local token's decimals.\\n    // This value is expected to be equal to the ReleaseOrMintInV1.amount in the case where the source and destination\\n    // chain have the same number of decimals.\\n    uint256 destinationAmount;\\n  }\\n}\\n\"},\"contracts/libraries/USDPriceWith18Decimals.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.0;\\n\\nlibrary USDPriceWith18Decimals {\\n  /// @notice Takes a price in USD, with 18 decimals per 1e18 token amount, and amount of the smallest token\\n  /// denomination, calculates the value in USD with 18 decimals.\\n  /// @param tokenPrice The USD price of the token.\\n  /// @param tokenAmount Amount of the smallest token denomination.\\n  /// @return USD value with 18 decimals.\\n  /// @dev this function assumes that no more than 1e59 US dollar worth of token is passed in. If more is sent, this\\n  /// function will overflow and revert. Since there isn't even close to 1e59 dollars, this is ok for all legit tokens.\\n  function _calcUSDValueFromTokenAmount(uint224 tokenPrice, uint256 tokenAmount) internal pure returns (uint256) {\\n    /// LINK Example:\\n    /// tokenPrice:         8e18 -\\u003e $8/LINK, as 1e18 token amount is 1 LINK, worth 8 USD, or 8e18 with 18 decimals\\n    /// tokenAmount:        2e18 -\\u003e 2 LINK\\n    /// result:             8e18 * 2e18 / 1e18 -\\u003e 16e18 with 18 decimals = $16\\n\\n    /// USDC Example:\\n    /// tokenPrice:         1e30 -\\u003e $1/USDC, as 1e18 token amount is 1e12 USDC, worth 1e12 USD, or 1e30 with 18 decimals\\n    /// tokenAmount:        5e6  -\\u003e 5 USDC\\n    /// result:             1e30 * 5e6 / 1e18 -\\u003e 5e18 with 18 decimals = $5\\n    return (tokenPrice * tokenAmount) / 1e18;\\n  }\\n\\n  /// @notice Takes a price in USD, with 18 decimals per 1e18 token amount, and USD value with 18 decimals, calculates\\n  /// amount of the smallest token denomination.\\n  /// @param tokenPrice The USD price of the token.\\n  /// @param usdValue USD value with 18 decimals.\\n  /// @return Amount of the smallest token denomination.\\n  function _calcTokenAmountFromUSDValue(uint224 tokenPrice, uint256 usdValue) internal pure returns (uint256) {\\n    /// LINK Example:\\n    /// tokenPrice:          8e18 -\\u003e $8/LINK, as 1e18 token amount is 1 LINK, worth 8 USD, or 8e18 with 18 decimals\\n    /// usdValue:           16e18 -\\u003e $16\\n    /// result:             16e18 * 1e18 / 8e18 -\\u003e 2e18 = 2 LINK\\n\\n    /// USDC Example:\\n    /// tokenPrice:         1e30 -\\u003e $1/USDC, as 1e18 token amount is 1e12 USDC, worth 1e12 USD, or 1e30 with 18 decimals\\n    /// usdValue:           5e18 -\\u003e $5\\n    /// result:             5e18 * 1e18 / 1e30 -\\u003e 5e6 = 5 USDC\\n    return (usdValue * 1e18) / tokenPrice;\\n  }\\n}\\n\"},\"contracts/onRamp/OnRamp.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IEVM2AnyOnRampClient} from \\\"../interfaces/IEVM2AnyOnRampClient.sol\\\";\\nimport {IFeeQuoter} from \\\"../interfaces/IFeeQuoter.sol\\\";\\nimport {IMessageInterceptor} from \\\"../interfaces/IMessageInterceptor.sol\\\";\\nimport {INonceManager} from \\\"../interfaces/INonceManager.sol\\\";\\nimport {IPoolV1} from \\\"../interfaces/IPool.sol\\\";\\nimport {IRMNRemote} from \\\"../interfaces/IRMNRemote.sol\\\";\\nimport {IRouter} from \\\"../interfaces/IRouter.sol\\\";\\nimport {ITokenAdminRegistry} from \\\"../interfaces/ITokenAdminRegistry.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\nimport {USDPriceWith18Decimals} from \\\"../libraries/USDPriceWith18Decimals.sol\\\";\\nimport {Ownable2StepMsgSender} from \\\"@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\\\";\\n\\nimport {IERC20} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\nimport {SafeERC20} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/utils/SafeERC20.sol\\\";\\nimport {EnumerableSet} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @notice The OnRamp is a contract that handles lane-specific fee logic.\\n/// @dev The OnRamp and OffRamp form a cross chain upgradeable unit. Any change to one of them results in an onchain\\n/// upgrade of both contracts.\\ncontract OnRamp is IEVM2AnyOnRampClient, ITypeAndVersion, Ownable2StepMsgSender {\\n  using SafeERC20 for IERC20;\\n  using EnumerableSet for EnumerableSet.AddressSet;\\n  using USDPriceWith18Decimals for uint224;\\n\\n  error CannotSendZeroTokens();\\n  error UnsupportedToken(address token);\\n  error MustBeCalledByRouter();\\n  error RouterMustSetOriginalSender();\\n  error InvalidConfig();\\n  error CursedByRMN(uint64 destChainSelector);\\n  error GetSupportedTokensFunctionalityRemovedCheckAdminRegistry();\\n  error InvalidDestChainConfig(uint64 destChainSelector);\\n  error OnlyCallableByOwnerOrAllowlistAdmin();\\n  error SenderNotAllowed(address sender);\\n  error InvalidAllowListRequest(uint64 destChainSelector);\\n  error ReentrancyGuardReentrantCall();\\n\\n  event ConfigSet(StaticConfig staticConfig, DynamicConfig dynamicConfig);\\n  event DestChainConfigSet(\\n    uint64 indexed destChainSelector, uint64 sequenceNumber, IRouter router, bool allowlistEnabled\\n  );\\n  event FeeTokenWithdrawn(address indexed feeAggregator, address indexed feeToken, uint256 amount);\\n  /// RMN depends on this event, if changing, please notify the RMN maintainers.\\n  event CCIPMessageSent(\\n    uint64 indexed destChainSelector, uint64 indexed sequenceNumber, Internal.EVM2AnyRampMessage message\\n  );\\n  event AllowListAdminSet(address indexed allowlistAdmin);\\n  event AllowListSendersAdded(uint64 indexed destChainSelector, address[] senders);\\n  event AllowListSendersRemoved(uint64 indexed destChainSelector, address[] senders);\\n\\n  /// @dev Struct that contains the static configuration.\\n  /// RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct StaticConfig {\\n    uint64 chainSelector; // ────╮ Source chain selector.\\n    IRMNRemote rmnRemote; // ────╯ RMN remote address.\\n    address nonceManager; //       Nonce manager address.\\n    address tokenAdminRegistry; // Token admin registry address.\\n  }\\n\\n  /// @dev Struct that contains the dynamic configuration\\n  // solhint-disable-next-line gas-struct-packing\\n  struct DynamicConfig {\\n    address feeQuoter; // FeeQuoter address.\\n    bool reentrancyGuardEntered; // Reentrancy protection.\\n    address messageInterceptor; // Optional message interceptor to validate messages. Zero address = no interceptor.\\n    address feeAggregator; // Fee aggregator address.\\n    address allowlistAdmin; // authorized admin to add or remove allowed senders.\\n  }\\n\\n  /// @dev Struct to hold the configs for a single destination chain.\\n  struct DestChainConfig {\\n    // The last used sequence number. This is zero in the case where no messages have yet been sent.\\n    // 0 is not a valid sequence number for any real transaction as this value will be incremented before use.\\n    uint64 sequenceNumber; // ──╮ The last used sequence number.\\n    bool allowlistEnabled; //   │ True if the allowlist is enabled.\\n    IRouter router; // ─────────╯ Local router address  that is allowed to send messages to the destination chain.\\n    EnumerableSet.AddressSet allowedSendersList; // The list of addresses allowed to send messages.\\n  }\\n\\n  /// @dev Same as DestChainConfig but with the destChainSelector so that an array of these can be passed in the\\n  /// constructor and the applyDestChainConfigUpdates function.\\n  // solhint-disable gas-struct-packing\\n  struct DestChainConfigArgs {\\n    uint64 destChainSelector; // ─╮ Destination chain selector.\\n    IRouter router; //            │ Source router address.\\n    bool allowlistEnabled; // ────╯ True if the allowlist is enabled.\\n  }\\n\\n  /// @dev Struct to hold the allowlist configuration args per dest chain.\\n  struct AllowlistConfigArgs {\\n    uint64 destChainSelector; // ──╮ Destination chain selector.\\n    bool allowlistEnabled; // ─────╯ True if the allowlist is enabled.\\n    address[] addedAllowlistedSenders; // list of senders to be added to the allowedSendersList.\\n    address[] removedAllowlistedSenders; // list of senders to be removed from the allowedSendersList.\\n  }\\n\\n  // STATIC CONFIG\\n  string public constant override typeAndVersion = \\\"OnRamp 1.6.0\\\";\\n  /// @dev The chain ID of the source chain that this contract is deployed to.\\n  uint64 private immutable i_chainSelector;\\n  /// @dev The rmn contract.\\n  IRMNRemote private immutable i_rmnRemote;\\n  /// @dev The address of the nonce manager.\\n  address private immutable i_nonceManager;\\n  /// @dev The address of the token admin registry.\\n  address private immutable i_tokenAdminRegistry;\\n\\n  // DYNAMIC CONFIG\\n  /// @dev The dynamic config for the onRamp.\\n  DynamicConfig private s_dynamicConfig;\\n\\n  /// @dev The destination chain specific configs.\\n  mapping(uint64 destChainSelector =\\u003e DestChainConfig destChainConfig) private s_destChainConfigs;\\n\\n  constructor(\\n    StaticConfig memory staticConfig,\\n    DynamicConfig memory dynamicConfig,\\n    DestChainConfigArgs[] memory destChainConfigArgs\\n  ) {\\n    if (\\n      staticConfig.chainSelector == 0 || address(staticConfig.rmnRemote) == address(0)\\n        || staticConfig.nonceManager == address(0) || staticConfig.tokenAdminRegistry == address(0)\\n    ) {\\n      revert InvalidConfig();\\n    }\\n\\n    i_chainSelector = staticConfig.chainSelector;\\n    i_rmnRemote = staticConfig.rmnRemote;\\n    i_nonceManager = staticConfig.nonceManager;\\n    i_tokenAdminRegistry = staticConfig.tokenAdminRegistry;\\n\\n    _setDynamicConfig(dynamicConfig);\\n    _applyDestChainConfigUpdates(destChainConfigArgs);\\n  }\\n\\n  // ================================================================\\n  // │                          Messaging                           │\\n  // ================================================================\\n\\n  /// @notice Gets the next sequence number to be used in the onRamp.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @return nextSequenceNumber The next sequence number to be used.\\n  function getExpectedNextSequenceNumber(\\n    uint64 destChainSelector\\n  ) external view returns (uint64) {\\n    return s_destChainConfigs[destChainSelector].sequenceNumber + 1;\\n  }\\n\\n  /// @inheritdoc IEVM2AnyOnRampClient\\n  function forwardFromRouter(\\n    uint64 destChainSelector,\\n    Client.EVM2AnyMessage calldata message,\\n    uint256 feeTokenAmount,\\n    address originalSender\\n  ) external returns (bytes32) {\\n    // We rely on a reentrancy guard here due to the untrusted calls performed to the pools. This enables some\\n    // optimizations by not following the CEI pattern.\\n    if (s_dynamicConfig.reentrancyGuardEntered) revert ReentrancyGuardReentrantCall();\\n    s_dynamicConfig.reentrancyGuardEntered = true;\\n\\n    DestChainConfig storage destChainConfig = s_destChainConfigs[destChainSelector];\\n\\n    // NOTE: assumes the message has already been validated through the getFee call.\\n    // Validate originalSender is set and allowed. Not validated in `getFee` since it is not user-driven.\\n    if (originalSender == address(0)) revert RouterMustSetOriginalSender();\\n\\n    if (destChainConfig.allowlistEnabled) {\\n      if (!destChainConfig.allowedSendersList.contains(originalSender)) {\\n        revert SenderNotAllowed(originalSender);\\n      }\\n    }\\n\\n    // Router address may be zero intentionally to pause, which should stop all messages.\\n    if (msg.sender != address(destChainConfig.router)) revert MustBeCalledByRouter();\\n\\n    {\\n      // scoped to reduce stack usage\\n      address messageInterceptor = s_dynamicConfig.messageInterceptor;\\n      if (messageInterceptor != address(0)) {\\n        IMessageInterceptor(messageInterceptor).onOutboundMessage(destChainSelector, message);\\n      }\\n    }\\n\\n    Internal.EVM2AnyRampMessage memory newMessage = Internal.EVM2AnyRampMessage({\\n      header: Internal.RampMessageHeader({\\n        // Should be generated after the message is complete.\\n        messageId: \\\"\\\",\\n        sourceChainSelector: i_chainSelector,\\n        destChainSelector: destChainSelector,\\n        // We need the next available sequence number so we increment before we use the value.\\n        sequenceNumber: ++destChainConfig.sequenceNumber,\\n        // Only bump nonce for messages that specify allowOutOfOrderExecution == false. Otherwise, we may block ordered\\n        // message nonces, which is not what we want.\\n        nonce: 0\\n      }),\\n      sender: originalSender,\\n      data: message.data,\\n      extraArgs: \\\"\\\",\\n      receiver: message.receiver,\\n      feeToken: message.feeToken,\\n      feeTokenAmount: feeTokenAmount,\\n      feeValueJuels: 0, // calculated later.\\n      // Should be populated via lock / burn pool calls.\\n      tokenAmounts: new Internal.EVM2AnyTokenTransfer[](message.tokenAmounts.length)\\n    });\\n\\n    // Convert message fee to juels and retrieve converted args.\\n    // Validate pool return data after it is populated (view function - no state changes).\\n    bool isOutOfOrderExecution;\\n    bytes memory tokenReceiver;\\n    (newMessage.feeValueJuels, isOutOfOrderExecution, newMessage.extraArgs, tokenReceiver) = IFeeQuoter(\\n      s_dynamicConfig.feeQuoter\\n    ).processMessageArgs(destChainSelector, message.feeToken, feeTokenAmount, message.extraArgs, message.receiver);\\n\\n    Client.EVMTokenAmount[] memory tokenAmounts = message.tokenAmounts;\\n    // Lock / burn the tokens as last step. TokenPools may not always be trusted.\\n    for (uint256 i = 0; i \\u003c message.tokenAmounts.length; ++i) {\\n      newMessage.tokenAmounts[i] =\\n        _lockOrBurnSingleToken(tokenAmounts[i], destChainSelector, tokenReceiver, originalSender);\\n    }\\n\\n    bytes[] memory destExecDataPerToken = IFeeQuoter(s_dynamicConfig.feeQuoter).processPoolReturnData(\\n      destChainSelector, newMessage.tokenAmounts, tokenAmounts\\n    );\\n    newMessage.header.nonce = isOutOfOrderExecution\\n      ? 0\\n      : INonceManager(i_nonceManager).getIncrementedOutboundNonce(destChainSelector, originalSender);\\n\\n    for (uint256 i = 0; i \\u003c newMessage.tokenAmounts.length; ++i) {\\n      newMessage.tokenAmounts[i].destExecData = destExecDataPerToken[i];\\n    }\\n\\n    newMessage = _postProcessMessage(newMessage);\\n\\n    // Hash only after all fields have been set.\\n    newMessage.header.messageId = Internal._hash(\\n      newMessage,\\n      // Metadata hash preimage to ensure global uniqueness, ensuring 2 identical messages sent to 2 different lanes\\n      // will have a distinct hash.\\n      keccak256(abi.encode(Internal.EVM_2_ANY_MESSAGE_HASH, i_chainSelector, destChainSelector, address(this)))\\n    );\\n\\n    // Emit message request.\\n    // This must happen after any pool events as some tokens (e.g. USDC) emit events that we expect to precede this\\n    // event in the offchain code.\\n    emit CCIPMessageSent(destChainSelector, newMessage.header.sequenceNumber, newMessage);\\n\\n    s_dynamicConfig.reentrancyGuardEntered = false;\\n\\n    return newMessage.header.messageId;\\n  }\\n\\n  /// @notice hook for applying custom logic to the message from the router\\n  /// @param message router message\\n  /// @return transformedMessage modified message\\n  function _postProcessMessage(\\n    Internal.EVM2AnyRampMessage memory message\\n  ) internal virtual returns (Internal.EVM2AnyRampMessage memory transformedMessage) {\\n    return message;\\n  }\\n\\n  /// @notice Uses a pool to lock or burn a token.\\n  /// @param tokenAndAmount Token address and amount to lock or burn.\\n  /// @param destChainSelector Target destination chain selector of the message.\\n  /// @param receiver Message receiver.\\n  /// @param originalSender Message sender.\\n  /// @return evm2AnyTokenTransfer EVM2Any token and amount data.\\n  function _lockOrBurnSingleToken(\\n    Client.EVMTokenAmount memory tokenAndAmount,\\n    uint64 destChainSelector,\\n    bytes memory receiver,\\n    address originalSender\\n  ) internal returns (Internal.EVM2AnyTokenTransfer memory) {\\n    if (tokenAndAmount.amount == 0) revert CannotSendZeroTokens();\\n\\n    IPoolV1 sourcePool = getPoolBySourceToken(destChainSelector, IERC20(tokenAndAmount.token));\\n    // We don't have to check if it supports the pool version in a non-reverting way here because\\n    // if we revert here, there is no effect on CCIP. Therefore we directly call the supportsInterface\\n    // function and not through the ERC165Checker.\\n    if (address(sourcePool) == address(0) || !sourcePool.supportsInterface(Pool.CCIP_POOL_V1)) {\\n      revert UnsupportedToken(tokenAndAmount.token);\\n    }\\n\\n    Pool.LockOrBurnOutV1 memory poolReturnData = sourcePool.lockOrBurn(\\n      Pool.LockOrBurnInV1({\\n        receiver: receiver,\\n        remoteChainSelector: destChainSelector,\\n        originalSender: originalSender,\\n        amount: tokenAndAmount.amount,\\n        localToken: tokenAndAmount.token\\n      })\\n    );\\n\\n    // NOTE: pool data validations are outsourced to the FeeQuoter to handle family-specific logic handling.\\n    return Internal.EVM2AnyTokenTransfer({\\n      sourcePoolAddress: address(sourcePool),\\n      destTokenAddress: poolReturnData.destTokenAddress,\\n      extraData: poolReturnData.destPoolData,\\n      amount: tokenAndAmount.amount,\\n      destExecData: \\\"\\\" // This is set in the processPoolReturnData function.\\n    });\\n  }\\n\\n  // ================================================================\\n  // │                           Config                             │\\n  // ================================================================\\n\\n  /// @notice Returns the static onRamp config.\\n  /// @dev RMN depends on this function, if modified, please notify the RMN maintainers.\\n  /// @return staticConfig the static configuration.\\n  function getStaticConfig() external view returns (StaticConfig memory) {\\n    return StaticConfig({\\n      chainSelector: i_chainSelector,\\n      rmnRemote: i_rmnRemote,\\n      nonceManager: i_nonceManager,\\n      tokenAdminRegistry: i_tokenAdminRegistry\\n    });\\n  }\\n\\n  /// @notice Returns the dynamic onRamp config.\\n  /// @return dynamicConfig the dynamic configuration.\\n  function getDynamicConfig() external view returns (DynamicConfig memory dynamicConfig) {\\n    return s_dynamicConfig;\\n  }\\n\\n  /// @notice Sets the dynamic configuration.\\n  /// @param dynamicConfig The configuration.\\n  function setDynamicConfig(\\n    DynamicConfig memory dynamicConfig\\n  ) external onlyOwner {\\n    _setDynamicConfig(dynamicConfig);\\n  }\\n\\n  /// @notice Internal version of setDynamicConfig to allow for reuse in the constructor.\\n  function _setDynamicConfig(\\n    DynamicConfig memory dynamicConfig\\n  ) internal {\\n    if (\\n      dynamicConfig.feeQuoter == address(0) || dynamicConfig.feeAggregator == address(0)\\n        || dynamicConfig.reentrancyGuardEntered\\n    ) revert InvalidConfig();\\n\\n    s_dynamicConfig = dynamicConfig;\\n\\n    emit ConfigSet(\\n      StaticConfig({\\n        chainSelector: i_chainSelector,\\n        rmnRemote: i_rmnRemote,\\n        nonceManager: i_nonceManager,\\n        tokenAdminRegistry: i_tokenAdminRegistry\\n      }),\\n      dynamicConfig\\n    );\\n  }\\n\\n  /// @notice Updates destination chains specific configs.\\n  /// @param destChainConfigArgs Array of destination chain specific configs.\\n  function applyDestChainConfigUpdates(\\n    DestChainConfigArgs[] memory destChainConfigArgs\\n  ) external onlyOwner {\\n    _applyDestChainConfigUpdates(destChainConfigArgs);\\n  }\\n\\n  /// @notice Internal version of applyDestChainConfigUpdates.\\n  function _applyDestChainConfigUpdates(\\n    DestChainConfigArgs[] memory destChainConfigArgs\\n  ) internal {\\n    for (uint256 i = 0; i \\u003c destChainConfigArgs.length; ++i) {\\n      DestChainConfigArgs memory destChainConfigArg = destChainConfigArgs[i];\\n      uint64 destChainSelector = destChainConfigArgs[i].destChainSelector;\\n\\n      if (destChainSelector == 0) {\\n        revert InvalidDestChainConfig(destChainSelector);\\n      }\\n\\n      DestChainConfig storage destChainConfig = s_destChainConfigs[destChainSelector];\\n      // The router can be zero to pause the destination chain\\n      destChainConfig.router = destChainConfigArg.router;\\n      destChainConfig.allowlistEnabled = destChainConfigArg.allowlistEnabled;\\n\\n      emit DestChainConfigSet(\\n        destChainSelector, destChainConfig.sequenceNumber, destChainConfigArg.router, destChainConfig.allowlistEnabled\\n      );\\n    }\\n  }\\n\\n  /// @notice get ChainConfig configured for the DestinationChainSelector.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @return sequenceNumber The last used sequence number.\\n  /// @return allowlistEnabled boolean indicator to specify if allowlist check is enabled.\\n  /// @return router address of the router.\\n  function getDestChainConfig(\\n    uint64 destChainSelector\\n  ) external view returns (uint64 sequenceNumber, bool allowlistEnabled, address router) {\\n    DestChainConfig storage config = s_destChainConfigs[destChainSelector];\\n    sequenceNumber = config.sequenceNumber;\\n    allowlistEnabled = config.allowlistEnabled;\\n    router = address(config.router);\\n    return (sequenceNumber, allowlistEnabled, router);\\n  }\\n\\n  /// @notice get allowedSenders List configured for the DestinationChainSelector.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @return isEnabled True if allowlist is enabled.\\n  /// @return configuredAddresses This is always populated with the list of allowed senders, even if the allowlist\\n  /// is turned off. This is because the only way to know what addresses are configured is through this function. If\\n  /// it would return an empty list when the allowlist is disabled, it would be impossible to know what addresses are\\n  /// configured.\\n  function getAllowedSendersList(\\n    uint64 destChainSelector\\n  ) external view returns (bool isEnabled, address[] memory configuredAddresses) {\\n    return (\\n      s_destChainConfigs[destChainSelector].allowlistEnabled,\\n      s_destChainConfigs[destChainSelector].allowedSendersList.values()\\n    );\\n  }\\n\\n  // ================================================================\\n  // │                          Allowlist                           │\\n  // ================================================================\\n\\n  /// @notice Updates allowlistConfig for Senders.\\n  /// @dev configuration used to set the list of senders who are authorized to send messages.\\n  /// @param allowlistConfigArgsItems Array of AllowlistConfigArguments where each item is for a destChainSelector.\\n  function applyAllowlistUpdates(\\n    AllowlistConfigArgs[] calldata allowlistConfigArgsItems\\n  ) external {\\n    if (msg.sender != owner()) {\\n      if (msg.sender != s_dynamicConfig.allowlistAdmin) {\\n        revert OnlyCallableByOwnerOrAllowlistAdmin();\\n      }\\n    }\\n\\n    for (uint256 i = 0; i \\u003c allowlistConfigArgsItems.length; ++i) {\\n      AllowlistConfigArgs memory allowlistConfigArgs = allowlistConfigArgsItems[i];\\n\\n      DestChainConfig storage destChainConfig = s_destChainConfigs[allowlistConfigArgs.destChainSelector];\\n      destChainConfig.allowlistEnabled = allowlistConfigArgs.allowlistEnabled;\\n\\n      if (allowlistConfigArgs.addedAllowlistedSenders.length \\u003e 0) {\\n        if (allowlistConfigArgs.allowlistEnabled) {\\n          for (uint256 j = 0; j \\u003c allowlistConfigArgs.addedAllowlistedSenders.length; ++j) {\\n            address toAdd = allowlistConfigArgs.addedAllowlistedSenders[j];\\n            if (toAdd == address(0)) {\\n              revert InvalidAllowListRequest(allowlistConfigArgs.destChainSelector);\\n            }\\n            destChainConfig.allowedSendersList.add(toAdd);\\n          }\\n\\n          emit AllowListSendersAdded(allowlistConfigArgs.destChainSelector, allowlistConfigArgs.addedAllowlistedSenders);\\n        } else {\\n          revert InvalidAllowListRequest(allowlistConfigArgs.destChainSelector);\\n        }\\n      }\\n\\n      for (uint256 j = 0; j \\u003c allowlistConfigArgs.removedAllowlistedSenders.length; ++j) {\\n        destChainConfig.allowedSendersList.remove(allowlistConfigArgs.removedAllowlistedSenders[j]);\\n      }\\n\\n      if (allowlistConfigArgs.removedAllowlistedSenders.length \\u003e 0) {\\n        emit AllowListSendersRemoved(\\n          allowlistConfigArgs.destChainSelector, allowlistConfigArgs.removedAllowlistedSenders\\n        );\\n      }\\n    }\\n  }\\n\\n  // ================================================================\\n  // │                      Tokens and pools                        │\\n  // ================================================================\\n\\n  /// @inheritdoc IEVM2AnyOnRampClient\\n  function getPoolBySourceToken(uint64, /*destChainSelector*/ IERC20 sourceToken) public view returns (IPoolV1) {\\n    return IPoolV1(ITokenAdminRegistry(i_tokenAdminRegistry).getPool(address(sourceToken)));\\n  }\\n\\n  /// @inheritdoc IEVM2AnyOnRampClient\\n  function getSupportedTokens(\\n    uint64 // destChainSelector\\n  ) external pure returns (address[] memory) {\\n    revert GetSupportedTokensFunctionalityRemovedCheckAdminRegistry();\\n  }\\n\\n  // ================================================================\\n  // │                             Fees                             │\\n  // ================================================================\\n\\n  /// @inheritdoc IEVM2AnyOnRampClient\\n  /// @dev getFee MUST revert if the feeToken is not listed in the fee token config, as the router assumes it does.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param message The message to get quote for.\\n  /// @return feeTokenAmount The amount of fee token needed for the fee, in smallest denomination of the fee token.\\n  function getFee(\\n    uint64 destChainSelector,\\n    Client.EVM2AnyMessage calldata message\\n  ) external view returns (uint256 feeTokenAmount) {\\n    if (i_rmnRemote.isCursed(bytes16(uint128(destChainSelector)))) revert CursedByRMN(destChainSelector);\\n\\n    return IFeeQuoter(s_dynamicConfig.feeQuoter).getValidatedFee(destChainSelector, message);\\n  }\\n\\n  /// @notice Withdraws the outstanding fee token balances to the fee aggregator.\\n  /// @param feeTokens The fee tokens to withdraw.\\n  /// @dev This function can be permissionless as it only transfers tokens to the fee aggregator which is a trusted address.\\n  function withdrawFeeTokens(\\n    address[] calldata feeTokens\\n  ) external {\\n    address feeAggregator = s_dynamicConfig.feeAggregator;\\n\\n    for (uint256 i = 0; i \\u003c feeTokens.length; ++i) {\\n      IERC20 feeToken = IERC20(feeTokens[i]);\\n      uint256 feeTokenBalance = feeToken.balanceOf(address(this));\\n\\n      if (feeTokenBalance \\u003e 0) {\\n        feeToken.safeTransfer(feeAggregator, feeTokenBalance);\\n\\n        emit FeeTokenWithdrawn(feeAggregator, address(feeToken), feeTokenBalance);\\n      }\\n    }\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.6.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20 {\\n  /**\\n   * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n   * another (`to`).\\n   *\\n   * Note that `value` may be zero.\\n   */\\n  event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n  /**\\n   * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n   * a call to {approve}. `value` is the new allowance.\\n   */\\n  event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n  /**\\n   * @dev Returns the amount of tokens in existence.\\n   */\\n  function totalSupply() external view returns (uint256);\\n\\n  /**\\n   * @dev Returns the amount of tokens owned by `account`.\\n   */\\n  function balanceOf(address account) external view returns (uint256);\\n\\n  /**\\n   * @dev Moves `amount` tokens from the caller's account to `to`.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transfer(address to, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Returns the remaining number of tokens that `spender` will be\\n   * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n   * zero by default.\\n   *\\n   * This value changes when {approve} or {transferFrom} are called.\\n   */\\n  function allowance(address owner, address spender) external view returns (uint256);\\n\\n  /**\\n   * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n   * that someone may use both the old and the new allowance by unfortunate\\n   * transaction ordering. One possible solution to mitigate this race\\n   * condition is to first reduce the spender's allowance to 0 and set the\\n   * desired value afterwards:\\n   * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n   *\\n   * Emits an {Approval} event.\\n   */\\n  function approve(address spender, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Moves `amount` tokens from `from` to `to` using the\\n   * allowance mechanism. `amount` is then deducted from the caller's\\n   * allowance.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transferFrom(address from, address to, uint256 amount) external returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/draft-IERC20Permit.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC20/extensions/draft-IERC20Permit.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 Permit extension allowing approvals to be made via signatures, as defined in\\n * https://eips.ethereum.org/EIPS/eip-2612[EIP-2612].\\n *\\n * Adds the {permit} method, which can be used to change an account's ERC20 allowance (see {IERC20-allowance}) by\\n * presenting a message signed by the account. By not relying on {IERC20-approve}, the token holder account doesn't\\n * need to send a transaction, and thus is not required to hold Ether at all.\\n */\\ninterface IERC20Permit {\\n  /**\\n   * @dev Sets `value` as the allowance of `spender` over ``owner``'s tokens,\\n   * given ``owner``'s signed approval.\\n   *\\n   * IMPORTANT: The same issues {IERC20-approve} has related to transaction\\n   * ordering also apply here.\\n   *\\n   * Emits an {Approval} event.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   * - `deadline` must be a timestamp in the future.\\n   * - `v`, `r` and `s` must be a valid `secp256k1` signature from `owner`\\n   * over the EIP712-formatted function arguments.\\n   * - the signature must use ``owner``'s current nonce (see {nonces}).\\n   *\\n   * For more information on the signature format, see the\\n   * https://eips.ethereum.org/EIPS/eip-2612#specification[relevant EIP\\n   * section].\\n   */\\n  function permit(\\n    address owner,\\n    address spender,\\n    uint256 value,\\n    uint256 deadline,\\n    uint8 v,\\n    bytes32 r,\\n    bytes32 s\\n  ) external;\\n\\n  /**\\n   * @dev Returns the current nonce for `owner`. This value must be\\n   * included whenever a signature is generated for {permit}.\\n   *\\n   * Every successful call to {permit} increases ``owner``'s nonce by one. This\\n   * prevents a signature from being used multiple times.\\n   */\\n  function nonces(address owner) external view returns (uint256);\\n\\n  /**\\n   * @dev Returns the domain separator used in the encoding of the signature for {permit}, as defined by {EIP712}.\\n   */\\n  // solhint-disable-next-line func-name-mixedcase\\n  function DOMAIN_SEPARATOR() external view returns (bytes32);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/utils/SafeERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (token/ERC20/utils/SafeERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC20.sol\\\";\\nimport \\\"../extensions/draft-IERC20Permit.sol\\\";\\nimport \\\"../../../utils/Address.sol\\\";\\n\\n/**\\n * @title SafeERC20\\n * @dev Wrappers around ERC20 operations that throw on failure (when the token\\n * contract returns false). Tokens that return no value (and instead revert or\\n * throw on failure) are also supported, non-reverting calls are assumed to be\\n * successful.\\n * To use this library you can add a `using SafeERC20 for IERC20;` statement to your contract,\\n * which allows you to call the safe operations as `token.safeTransfer(...)`, etc.\\n */\\nlibrary SafeERC20 {\\n  using Address for address;\\n\\n  function safeTransfer(IERC20 token, address to, uint256 value) internal {\\n    _callOptionalReturn(token, abi.encodeWithSelector(token.transfer.selector, to, value));\\n  }\\n\\n  function safeTransferFrom(IERC20 token, address from, address to, uint256 value) internal {\\n    _callOptionalReturn(token, abi.encodeWithSelector(token.transferFrom.selector, from, to, value));\\n  }\\n\\n  /**\\n   * @dev Deprecated. This function has issues similar to the ones found in\\n   * {IERC20-approve}, and its usage is discouraged.\\n   *\\n   * Whenever possible, use {safeIncreaseAllowance} and\\n   * {safeDecreaseAllowance} instead.\\n   */\\n  function safeApprove(IERC20 token, address spender, uint256 value) internal {\\n    // safeApprove should only be called when setting an initial allowance,\\n    // or when resetting it to zero. To increase and decrease it, use\\n    // 'safeIncreaseAllowance' and 'safeDecreaseAllowance'\\n    require(\\n      (value == 0) || (token.allowance(address(this), spender) == 0),\\n      \\\"SafeERC20: approve from non-zero to non-zero allowance\\\"\\n    );\\n    _callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, value));\\n  }\\n\\n  function safeIncreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n    uint256 newAllowance = token.allowance(address(this), spender) + value;\\n    _callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\\n  }\\n\\n  function safeDecreaseAllowance(IERC20 token, address spender, uint256 value) internal {\\n    unchecked {\\n      uint256 oldAllowance = token.allowance(address(this), spender);\\n      require(oldAllowance \\u003e= value, \\\"SafeERC20: decreased allowance below zero\\\");\\n      uint256 newAllowance = oldAllowance - value;\\n      _callOptionalReturn(token, abi.encodeWithSelector(token.approve.selector, spender, newAllowance));\\n    }\\n  }\\n\\n  function safePermit(\\n    IERC20Permit token,\\n    address owner,\\n    address spender,\\n    uint256 value,\\n    uint256 deadline,\\n    uint8 v,\\n    bytes32 r,\\n    bytes32 s\\n  ) internal {\\n    uint256 nonceBefore = token.nonces(owner);\\n    token.permit(owner, spender, value, deadline, v, r, s);\\n    uint256 nonceAfter = token.nonces(owner);\\n    require(nonceAfter == nonceBefore + 1, \\\"SafeERC20: permit did not succeed\\\");\\n  }\\n\\n  /**\\n   * @dev Imitates a Solidity high-level call (i.e. a regular function call to a contract), relaxing the requirement\\n   * on the return value: the return value is optional (but if data is returned, it must not be false).\\n   * @param token The token targeted by the call.\\n   * @param data The call data (encoded using abi.encode or one of its variants).\\n   */\\n  function _callOptionalReturn(IERC20 token, bytes memory data) private {\\n    // We need to perform a low level call here, to bypass Solidity's return data size checking mechanism, since\\n    // we're implementing it ourselves. We use {Address-functionCall} to perform this call, which verifies that\\n    // the target address contains contract code and also asserts for success in the low-level call.\\n\\n    bytes memory returndata = address(token).functionCall(data, \\\"SafeERC20: low-level call failed\\\");\\n    if (returndata.length \\u003e 0) {\\n      // Return data is optional\\n      require(abi.decode(returndata, (bool)), \\\"SafeERC20: ERC20 operation did not succeed\\\");\\n    }\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Address.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/Address.sol)\\n\\npragma solidity ^0.8.1;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary Address {\\n  /**\\n   * @dev Returns true if `account` is a contract.\\n   *\\n   * [IMPORTANT]\\n   * ====\\n   * It is unsafe to assume that an address for which this function returns\\n   * false is an externally-owned account (EOA) and not a contract.\\n   *\\n   * Among others, `isContract` will return false for the following\\n   * types of addresses:\\n   *\\n   *  - an externally-owned account\\n   *  - a contract in construction\\n   *  - an address where a contract will be created\\n   *  - an address where a contract lived, but was destroyed\\n   * ====\\n   *\\n   * [IMPORTANT]\\n   * ====\\n   * You shouldn't rely on `isContract` to protect against flash loan attacks!\\n   *\\n   * Preventing calls from contracts is highly discouraged. It breaks composability, breaks support for smart wallets\\n   * like Gnosis Safe, and does not provide security since it can be circumvented by calling from a contract\\n   * constructor.\\n   * ====\\n   */\\n  function isContract(address account) internal view returns (bool) {\\n    // This method relies on extcodesize/address.code.length, which returns 0\\n    // for contracts in construction, since the code is only stored at the end\\n    // of the constructor execution.\\n\\n    return account.code.length \\u003e 0;\\n  }\\n\\n  /**\\n   * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n   * `recipient`, forwarding all available gas and reverting on errors.\\n   *\\n   * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n   * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n   * imposed by `transfer`, making them unable to receive funds via\\n   * `transfer`. {sendValue} removes this limitation.\\n   *\\n   * https://diligence.consensys.net/posts/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n   *\\n   * IMPORTANT: because control is transferred to `recipient`, care must be\\n   * taken to not create reentrancy vulnerabilities. Consider using\\n   * {ReentrancyGuard} or the\\n   * https://solidity.readthedocs.io/en/v0.5.11/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n   */\\n  function sendValue(address payable recipient, uint256 amount) internal {\\n    require(address(this).balance \\u003e= amount, \\\"Address: insufficient balance\\\");\\n\\n    (bool success, ) = recipient.call{value: amount}(\\\"\\\");\\n    require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n  }\\n\\n  /**\\n   * @dev Performs a Solidity function call using a low level `call`. A\\n   * plain `call` is an unsafe replacement for a function call: use this\\n   * function instead.\\n   *\\n   * If `target` reverts with a revert reason, it is bubbled up by this\\n   * function (like regular Solidity function calls).\\n   *\\n   * Returns the raw returned data. To convert to the expected return value,\\n   * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].\\n   *\\n   * Requirements:\\n   *\\n   * - `target` must be a contract.\\n   * - calling `target` with `data` must not revert.\\n   *\\n   * _Available since v3.1._\\n   */\\n  function functionCall(address target, bytes memory data) internal returns (bytes memory) {\\n    return functionCallWithValue(target, data, 0, \\\"Address: low-level call failed\\\");\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`], but with\\n   * `errorMessage` as a fallback revert reason when `target` reverts.\\n   *\\n   * _Available since v3.1._\\n   */\\n  function functionCall(address target, bytes memory data, string memory errorMessage) internal returns (bytes memory) {\\n    return functionCallWithValue(target, data, 0, errorMessage);\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n   * but also transferring `value` wei to `target`.\\n   *\\n   * Requirements:\\n   *\\n   * - the calling contract must have an ETH balance of at least `value`.\\n   * - the called Solidity function must be `payable`.\\n   *\\n   * _Available since v3.1._\\n   */\\n  function functionCallWithValue(address target, bytes memory data, uint256 value) internal returns (bytes memory) {\\n    return functionCallWithValue(target, data, value, \\\"Address: low-level call with value failed\\\");\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCallWithValue-address-bytes-uint256-}[`functionCallWithValue`], but\\n   * with `errorMessage` as a fallback revert reason when `target` reverts.\\n   *\\n   * _Available since v3.1._\\n   */\\n  function functionCallWithValue(\\n    address target,\\n    bytes memory data,\\n    uint256 value,\\n    string memory errorMessage\\n  ) internal returns (bytes memory) {\\n    require(address(this).balance \\u003e= value, \\\"Address: insufficient balance for call\\\");\\n    (bool success, bytes memory returndata) = target.call{value: value}(data);\\n    return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n   * but performing a static call.\\n   *\\n   * _Available since v3.3._\\n   */\\n  function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {\\n    return functionStaticCall(target, data, \\\"Address: low-level static call failed\\\");\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n   * but performing a static call.\\n   *\\n   * _Available since v3.3._\\n   */\\n  function functionStaticCall(\\n    address target,\\n    bytes memory data,\\n    string memory errorMessage\\n  ) internal view returns (bytes memory) {\\n    (bool success, bytes memory returndata) = target.staticcall(data);\\n    return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n   * but performing a delegate call.\\n   *\\n   * _Available since v3.4._\\n   */\\n  function functionDelegateCall(address target, bytes memory data) internal returns (bytes memory) {\\n    return functionDelegateCall(target, data, \\\"Address: low-level delegate call failed\\\");\\n  }\\n\\n  /**\\n   * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n   * but performing a delegate call.\\n   *\\n   * _Available since v3.4._\\n   */\\n  function functionDelegateCall(\\n    address target,\\n    bytes memory data,\\n    string memory errorMessage\\n  ) internal returns (bytes memory) {\\n    (bool success, bytes memory returndata) = target.delegatecall(data);\\n    return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n  }\\n\\n  /**\\n   * @dev Tool to verify that a low level call to smart-contract was successful, and revert (either by bubbling\\n   * the revert reason or using the provided one) in case of unsuccessful call or if target was not a contract.\\n   *\\n   * _Available since v4.8._\\n   */\\n  function verifyCallResultFromTarget(\\n    address target,\\n    bool success,\\n    bytes memory returndata,\\n    string memory errorMessage\\n  ) internal view returns (bytes memory) {\\n    if (success) {\\n      if (returndata.length == 0) {\\n        // only check isContract if the call was successful and the return data is empty\\n        // otherwise we already know that it was a contract\\n        require(isContract(target), \\\"Address: call to non-contract\\\");\\n      }\\n      return returndata;\\n    } else {\\n      _revert(returndata, errorMessage);\\n    }\\n  }\\n\\n  /**\\n   * @dev Tool to verify that a low level call was successful, and revert if it wasn't, either by bubbling the\\n   * revert reason or using the provided one.\\n   *\\n   * _Available since v4.3._\\n   */\\n  function verifyCallResult(\\n    bool success,\\n    bytes memory returndata,\\n    string memory errorMessage\\n  ) internal pure returns (bytes memory) {\\n    if (success) {\\n      return returndata;\\n    } else {\\n      _revert(returndata, errorMessage);\\n    }\\n  }\\n\\n  function _revert(bytes memory returndata, string memory errorMessage) private pure {\\n    // Look for revert reason and bubble it up if present\\n    if (returndata.length \\u003e 0) {\\n      // The easiest way to bubble the revert reason is using memory via assembly\\n      /// @solidity memory-safe-assembly\\n      assembly {\\n        let returndata_size := mload(returndata)\\n        revert(add(32, returndata), returndata_size)\\n      }\\n    } else {\\n      revert(errorMessage);\\n    }\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```solidity\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n    // To implement this library for multiple types with as little code\\n    // repetition as possible, we write it in terms of a generic Set type with\\n    // bytes32 values.\\n    // The Set implementation uses private functions, and user-facing\\n    // implementations (such as AddressSet) are just wrappers around the\\n    // underlying Set.\\n    // This means that we can only create new EnumerableSets for types that fit\\n    // in bytes32.\\n\\n    struct Set {\\n        // Storage of set values\\n        bytes32[] _values;\\n        // Position is the index of the value in the `values` array plus 1.\\n        // Position 0 is used to mean a value is not in the set.\\n        mapping(bytes32 value =\\u003e uint256) _positions;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function _add(Set storage set, bytes32 value) private returns (bool) {\\n        if (!_contains(set, value)) {\\n            set._values.push(value);\\n            // The value is stored at length-1, but we add 1 to all indexes\\n            // and use 0 as a sentinel value\\n            set._positions[value] = set._values.length;\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function _remove(Set storage set, bytes32 value) private returns (bool) {\\n        // We cache the value's position to prevent multiple reads from the same storage slot\\n        uint256 position = set._positions[value];\\n\\n        if (position != 0) {\\n            // Equivalent to contains(set, value)\\n            // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n            // the array, and then remove the last element (sometimes called as 'swap and pop').\\n            // This modifies the order of the array, as noted in {at}.\\n\\n            uint256 valueIndex = position - 1;\\n            uint256 lastIndex = set._values.length - 1;\\n\\n            if (valueIndex != lastIndex) {\\n                bytes32 lastValue = set._values[lastIndex];\\n\\n                // Move the lastValue to the index where the value to delete is\\n                set._values[valueIndex] = lastValue;\\n                // Update the tracked position of the lastValue (that was just moved)\\n                set._positions[lastValue] = position;\\n            }\\n\\n            // Delete the slot where the moved value was stored\\n            set._values.pop();\\n\\n            // Delete the tracked position for the deleted slot\\n            delete set._positions[value];\\n\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n        return set._positions[value] != 0;\\n    }\\n\\n    /**\\n     * @dev Returns the number of values on the set. O(1).\\n     */\\n    function _length(Set storage set) private view returns (uint256) {\\n        return set._values.length;\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n        return set._values[index];\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function _values(Set storage set) private view returns (bytes32[] memory) {\\n        return set._values;\\n    }\\n\\n    // Bytes32Set\\n\\n    struct Bytes32Set {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _add(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _remove(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n        return _contains(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(Bytes32Set storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n        return _at(set._inner, index);\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        bytes32[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // AddressSet\\n\\n    struct AddressSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(AddressSet storage set, address value) internal returns (bool) {\\n        return _add(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(AddressSet storage set, address value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(AddressSet storage set, address value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(AddressSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n        return address(uint160(uint256(_at(set._inner, index))));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(AddressSet storage set) internal view returns (address[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        address[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // UintSet\\n\\n    struct UintSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _add(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(UintSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n        return uint256(_at(set._inner, index));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(UintSet storage set) internal view returns (uint256[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        uint256[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n}\\n\"}}}"
