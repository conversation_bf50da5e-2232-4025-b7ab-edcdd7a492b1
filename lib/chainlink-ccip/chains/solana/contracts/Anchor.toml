[toolchain]
anchor_version = "0.29.0"

[features]
seeds = false
skip-lint = false

[programs.localnet]
access_controller = "6KsN58MTnRQ8FfPaXHiFPPFGDRioikj9CdPvPxZJdCjb"
ccip_offramp = "offqSMQWgQud6WJz694LRzkeN5kMYpCHTpXQr3Rkcjm"
ccip_router = "Ccip842gzYHhvdDkSyi2YVCoAWPbYJoApMFzSxQroE9C"
burnmint_token_pool = "41FGToCmdaWa1dgZLKFAjvmx6e6AjVTX7SVRibvsMGVB"
example_ccip_receiver = "48LGpn6tPn5SjTtK2wL9uUx48JUWZdZBv11sboy2orCc"
example_ccip_sender = "4LfBQWYaU6zQZbDyYjX8pbY4qjzrhoumUFYZEZEqMNhJ"
lockrelease_token_pool = "8eqh8wppT9c5rw4ERqNCffvU6cNFJWff9WmkcYtmGiqC"
external_program_cpi_stub = "2zZwzyptLqwFJFEFxjPvrdhiGpH9pJ3MfrrmZX6NTKxm"
fee_quoter = "FeeQPGkKDeRV1MgoYfMH6L8o3KeuYjwUZrgn4LRKfjHi"
mcm = "5vNJx78mz7KVMjhuipyr9jKBKcMrKYGdjGkgE4LUmjKk"
rmn_remote = "RmnXLft1mSEwDgMKu2okYuHkiazxntFFcZFrrcXxYg7"
test_ccip_invalid_receiver = "FmyF3oW69MSAhyPSiZ69C4RKBdCPv5vAFTScisV7Me2j"
test_ccip_receiver = "EvhgrPhTDt4LcSPS2kfJgH6T6XWZ6wT3X9ncDGLT1vui"
test_token_pool = "JuCcZ4smxAYv9QHJ36jshA7pA3FuQ3vQeWLUeAtZduJ"
timelock = "DoajfR5tK24xVw51fWcawUZWhAXD8yrBJVacc13neVQA"
ping_pong_demo = "PPbZmYFf5SPAM9Jhm9mNmYoCwT7icPYVKAfJoMCQovU"

[registry]
url = "https://anchor.projectserum.com"

[provider]
cluster = "Localnet"
wallet = "id.json"
