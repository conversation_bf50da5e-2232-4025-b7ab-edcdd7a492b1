{"version": "1.0.1", "name": "access_controller", "constants": [{"name": "MAX_ADDRS", "type": {"defined": "usize"}, "value": "64"}], "instructions": [{"name": "initialize", "accounts": [{"name": "state", "isMut": true, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": true}], "args": []}, {"name": "transferOwnership", "accounts": [{"name": "state", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}], "args": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}]}, {"name": "acceptOwnership", "accounts": [{"name": "state", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}], "args": []}, {"name": "addAccess", "accounts": [{"name": "state", "isMut": true, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": true}, {"name": "address", "isMut": false, "isSigner": false}], "args": []}, {"name": "removeAccess", "accounts": [{"name": "state", "isMut": true, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": true}, {"name": "address", "isMut": false, "isSigner": false}], "args": []}], "accounts": [{"name": "AccessController", "type": {"kind": "struct", "fields": [{"name": "owner", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "accessList", "type": {"defined": "AccessList"}}]}}], "types": [{"name": "AccessList", "type": {"kind": "struct", "fields": [{"name": "xs", "type": {"array": ["public<PERSON>ey", 64]}}, {"name": "len", "type": "u64"}]}}], "errors": [{"code": 6000, "name": "Unauthorized", "msg": "Unauthorized"}, {"code": 6001, "name": "InvalidInput", "msg": "Invalid input"}, {"code": 6002, "name": "Full", "msg": "Access list is full"}]}