{"version": "0.1.0-dev", "name": "base_token_pool", "instructions": [], "types": [{"name": "BaseConfig", "type": {"kind": "struct", "fields": [{"name": "tokenProgram", "type": "public<PERSON>ey"}, {"name": "mint", "type": "public<PERSON>ey"}, {"name": "decimals", "type": "u8"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "poolTokenAccount", "type": "public<PERSON>ey"}, {"name": "owner", "type": "public<PERSON>ey"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}, {"name": "rateLimitAdmin", "type": "public<PERSON>ey"}, {"name": "routerOnrampAuthority", "type": "public<PERSON>ey"}, {"name": "router", "type": "public<PERSON>ey"}, {"name": "rebalancer", "type": "public<PERSON>ey"}, {"name": "canAcceptLiquidity", "type": "bool"}, {"name": "listEnabled", "type": "bool"}, {"name": "allowList", "type": {"vec": "public<PERSON>ey"}}, {"name": "rmnRemote", "type": "public<PERSON>ey"}]}}, {"name": "BaseChain", "type": {"kind": "struct", "fields": [{"name": "remote", "type": {"defined": "RemoteConfig"}}, {"name": "inboundRateLimit", "type": {"defined": "RateLimitTokenBucket"}}, {"name": "outboundRateLimit", "type": {"defined": "RateLimitTokenBucket"}}]}}, {"name": "RemoteConfig", "type": {"kind": "struct", "fields": [{"name": "poolAddresses", "type": {"vec": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}}, {"name": "tokenAddress", "type": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}, {"name": "decimals", "type": "u8"}]}}, {"name": "Re<PERSON><PERSON><PERSON><PERSON>", "type": {"kind": "struct", "fields": [{"name": "address", "type": "bytes"}]}}, {"name": "LockOrBurnInV1", "type": {"kind": "struct", "fields": [{"name": "receiver", "type": "bytes"}, {"name": "remoteChainSelector", "type": "u64"}, {"name": "originalSender", "type": "public<PERSON>ey"}, {"name": "amount", "type": "u64"}, {"name": "localToken", "type": "public<PERSON>ey"}]}}, {"name": "LockOrBurnOutV1", "type": {"kind": "struct", "fields": [{"name": "dest<PERSON>okenAddress", "type": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}, {"name": "destPoolData", "type": "bytes"}]}}, {"name": "ReleaseOrMintInV1", "type": {"kind": "struct", "fields": [{"name": "originalSender", "type": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}, {"name": "remoteChainSelector", "type": "u64"}, {"name": "receiver", "type": "public<PERSON>ey"}, {"name": "amount", "type": {"array": ["u8", 32]}}, {"name": "localToken", "type": "public<PERSON>ey"}, {"name": "sourcePoolAddress", "docs": ["@dev WARNING: sourcePoolAddress should be checked prior to any processing of funds. Make sure it matches the", "expected pool address for the given remoteChainSelector."], "type": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}, {"name": "sourcePoolData", "type": "bytes"}, {"name": "offchainTokenData", "docs": ["@dev WARNING: offchainTokenData is untrusted data."], "type": "bytes"}]}}, {"name": "ReleaseOrMintOutV1", "type": {"kind": "struct", "fields": [{"name": "destinationAmount", "type": "u64"}]}}, {"name": "RateLimitTokenBucket", "type": {"kind": "struct", "fields": [{"name": "tokens", "type": "u64"}, {"name": "lastUpdated", "type": "u64"}, {"name": "cfg", "type": {"defined": "RateLimitConfig"}}]}}, {"name": "RateLimitConfig", "type": {"kind": "struct", "fields": [{"name": "enabled", "type": "bool"}, {"name": "capacity", "type": "u64"}, {"name": "rate", "type": "u64"}]}}], "events": [{"name": "Burned", "fields": [{"name": "sender", "type": "public<PERSON>ey", "index": false}, {"name": "amount", "type": "u64", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "Minted", "fields": [{"name": "sender", "type": "public<PERSON>ey", "index": false}, {"name": "recipient", "type": "public<PERSON>ey", "index": false}, {"name": "amount", "type": "u64", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "Locked", "fields": [{"name": "sender", "type": "public<PERSON>ey", "index": false}, {"name": "amount", "type": "u64", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "Released", "fields": [{"name": "sender", "type": "public<PERSON>ey", "index": false}, {"name": "recipient", "type": "public<PERSON>ey", "index": false}, {"name": "amount", "type": "u64", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "RemoteChainConfigured", "fields": [{"name": "chainSelector", "type": "u64", "index": false}, {"name": "token", "type": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}, "index": false}, {"name": "previousToken", "type": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}, "index": false}, {"name": "poolAddresses", "type": {"vec": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}, "index": false}, {"name": "previousPoolAddresses", "type": {"vec": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}, "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "RateLimitConfigured", "fields": [{"name": "chainSelector", "type": "u64", "index": false}, {"name": "outboundRateLimit", "type": {"defined": "RateLimitConfig"}, "index": false}, {"name": "inboundRateLimit", "type": {"defined": "RateLimitConfig"}, "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "RemotePoolsAppended", "fields": [{"name": "chainSelector", "type": "u64", "index": false}, {"name": "poolAddresses", "type": {"vec": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}, "index": false}, {"name": "previousPoolAddresses", "type": {"vec": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}, "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "RemoteChainRemoved", "fields": [{"name": "chainSelector", "type": "u64", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "RouterUpdated", "fields": [{"name": "old<PERSON>outer", "type": "public<PERSON>ey", "index": false}, {"name": "newRouter", "type": "public<PERSON>ey", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "OwnershipTransferRequested", "fields": [{"name": "from", "type": "public<PERSON>ey", "index": false}, {"name": "to", "type": "public<PERSON>ey", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "OwnershipTransferred", "fields": [{"name": "from", "type": "public<PERSON>ey", "index": false}, {"name": "to", "type": "public<PERSON>ey", "index": false}, {"name": "mint", "type": "public<PERSON>ey", "index": false}]}, {"name": "TokensConsumed", "fields": [{"name": "tokens", "type": "u64", "index": false}]}, {"name": "Config<PERSON><PERSON><PERSON>", "fields": [{"name": "config", "type": {"defined": "RateLimitConfig"}, "index": false}]}], "errors": [{"code": 6000, "name": "InvalidInitPoolPermissions", "msg": "Pool authority does not match token mint owner"}, {"code": 6001, "name": "InvalidRMNRemoteAddress", "msg": "Invalid RMN Remote Address"}, {"code": 6002, "name": "Unauthorized", "msg": "Unauthorized"}, {"code": 6003, "name": "InvalidInputs", "msg": "Invalid inputs"}, {"code": 6004, "name": "InvalidVersion", "msg": "Invalid state version"}, {"code": 6005, "name": "InvalidPoolCaller", "msg": "Caller is not ramp on router"}, {"code": 6006, "name": "InvalidSender", "msg": "Sender not allowed"}, {"code": 6007, "name": "InvalidSourcePoolAddress", "msg": "Invalid source pool address"}, {"code": 6008, "name": "InvalidToken", "msg": "Invalid token"}, {"code": 6009, "name": "InvalidTokenAmountConversion", "msg": "Invalid token amount conversion"}, {"code": 6010, "name": "AllowlistKeyAlreadyExisted", "msg": "Key already existed in the allowlist"}, {"code": 6011, "name": "AllowlistKeyDidNotExist", "msg": "Key did not exist in the allowlist"}, {"code": 6012, "name": "RemotePoolAddressAlreadyExisted", "msg": "Remote pool address already exists"}, {"code": 6013, "name": "NonemptyPoolAddressesInit", "msg": "Expected empty pool addresses during initialization"}, {"code": 6014, "name": "RLBucketOverfilled", "msg": "RateLimit: bucket overfilled"}, {"code": 6015, "name": "RLMaxCapacityExceeded", "msg": "RateLimit: max capacity exceeded"}, {"code": 6016, "name": "RLRateLimitReached", "msg": "RateLimit: rate limit reached"}, {"code": 6017, "name": "RLInvalidRateLimitRate", "msg": "RateLimit: invalid rate limit rate"}, {"code": 6018, "name": "RLDisabledNonZeroRateLimit", "msg": "RateLimit: disabled non-zero rate limit"}, {"code": 6019, "name": "LiquidityNotAccepted", "msg": "Liquidity not accepted"}]}