[package]
name = "example_ccip_sender"
version = "0.1.0-dev"
description = "Created with Anchor"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "example_ccip_sender"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = ["custom-heap"]
custom-heap = []

[dependencies]
solana-program = "1.17.25" # pin solana to 1.17
anchor-lang = { version = "0.29.0", features = [] }
anchor-spl = "0.29.0"
ccip_router = { version = "0.1.0-dev", path = "../ccip-router", features = ["no-entrypoint"]}
smalloc = "0.1.2"
