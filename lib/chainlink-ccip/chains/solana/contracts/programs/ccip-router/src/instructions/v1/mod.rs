////////////////////////////////////////////////////////////////////////////////
// Public modules, to be exposed via routers to lib.rs as program entrypoints //
////////////////////////////////////////////////////////////////////////////////
pub(super) mod admin;
pub(super) mod onramp;
pub(super) mod token_admin_registry;

//////////////////////////////////////////////////////////////////////////////////
// Private modules, just to be used within the instructions versioned submodule //
//////////////////////////////////////////////////////////////////////////////////
mod fees;
mod messages;
mod pools;
