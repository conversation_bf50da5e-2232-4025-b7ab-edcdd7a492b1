// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package example_ccip_sender

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// CcipSend is the `ccipSend` instruction.
type CcipSend struct {
	DestChainSelector *uint64
	TokenAmounts      *[]SVMTokenAmount
	Data              *[]byte
	FeeToken          *ag_solanago.PublicKey
	TokenIndexes      *[]byte

	// [0] = [] state
	//
	// [1] = [] chainConfig
	//
	// [2] = [WRITE] ccipSender
	//
	// [3] = [WRITE] authorityFeeTokenAta
	//
	// [4] = [SIGNER] authority
	//
	// [5] = [] systemProgram
	//
	// [6] = [] ccipRouter
	//
	// [7] = [] ccipConfig
	//
	// [8] = [WRITE] ccipDestChainState
	//
	// [9] = [WRITE] ccipSenderNonce
	//
	// [10] = [] ccipFeeTokenProgram
	//
	// [11] = [] ccipFeeTokenMint
	//
	// [12] = [WRITE] ccipFeeTokenUserAta
	//
	// [13] = [WRITE] ccipFeeTokenReceiver
	//
	// [14] = [] ccipFeeBillingSigner
	//
	// [15] = [] ccipFeeQuoter
	//
	// [16] = [] ccipFeeQuoterConfig
	//
	// [17] = [] ccipFeeQuoterDestChain
	//
	// [18] = [] ccipFeeQuoterBillingTokenConfig
	//
	// [19] = [] ccipFeeQuoterLinkTokenConfig
	//
	// [20] = [] ccipRmnRemote
	//
	// [21] = [] ccipRmnRemoteCurses
	//
	// [22] = [] ccipRmnRemoteConfig
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewCcipSendInstructionBuilder creates a new `CcipSend` instruction builder.
func NewCcipSendInstructionBuilder() *CcipSend {
	nd := &CcipSend{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 23),
	}
	return nd
}

// SetDestChainSelector sets the "destChainSelector" parameter.
func (inst *CcipSend) SetDestChainSelector(destChainSelector uint64) *CcipSend {
	inst.DestChainSelector = &destChainSelector
	return inst
}

// SetTokenAmounts sets the "tokenAmounts" parameter.
func (inst *CcipSend) SetTokenAmounts(tokenAmounts []SVMTokenAmount) *CcipSend {
	inst.TokenAmounts = &tokenAmounts
	return inst
}

// SetData sets the "data" parameter.
func (inst *CcipSend) SetData(data []byte) *CcipSend {
	inst.Data = &data
	return inst
}

// SetFeeToken sets the "feeToken" parameter.
func (inst *CcipSend) SetFeeToken(feeToken ag_solanago.PublicKey) *CcipSend {
	inst.FeeToken = &feeToken
	return inst
}

// SetTokenIndexes sets the "tokenIndexes" parameter.
func (inst *CcipSend) SetTokenIndexes(tokenIndexes []byte) *CcipSend {
	inst.TokenIndexes = &tokenIndexes
	return inst
}

// SetStateAccount sets the "state" account.
func (inst *CcipSend) SetStateAccount(state ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(state)
	return inst
}

// GetStateAccount gets the "state" account.
func (inst *CcipSend) GetStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetChainConfigAccount sets the "chainConfig" account.
func (inst *CcipSend) SetChainConfigAccount(chainConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(chainConfig)
	return inst
}

// GetChainConfigAccount gets the "chainConfig" account.
func (inst *CcipSend) GetChainConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetCcipSenderAccount sets the "ccipSender" account.
func (inst *CcipSend) SetCcipSenderAccount(ccipSender ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(ccipSender).WRITE()
	return inst
}

// GetCcipSenderAccount gets the "ccipSender" account.
func (inst *CcipSend) GetCcipSenderAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetAuthorityFeeTokenAtaAccount sets the "authorityFeeTokenAta" account.
func (inst *CcipSend) SetAuthorityFeeTokenAtaAccount(authorityFeeTokenAta ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(authorityFeeTokenAta).WRITE()
	return inst
}

// GetAuthorityFeeTokenAtaAccount gets the "authorityFeeTokenAta" account.
func (inst *CcipSend) GetAuthorityFeeTokenAtaAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *CcipSend) SetAuthorityAccount(authority ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *CcipSend) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *CcipSend) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *CcipSend) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetCcipRouterAccount sets the "ccipRouter" account.
func (inst *CcipSend) SetCcipRouterAccount(ccipRouter ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(ccipRouter)
	return inst
}

// GetCcipRouterAccount gets the "ccipRouter" account.
func (inst *CcipSend) GetCcipRouterAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

// SetCcipConfigAccount sets the "ccipConfig" account.
func (inst *CcipSend) SetCcipConfigAccount(ccipConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[7] = ag_solanago.Meta(ccipConfig)
	return inst
}

// GetCcipConfigAccount gets the "ccipConfig" account.
func (inst *CcipSend) GetCcipConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[7]
}

// SetCcipDestChainStateAccount sets the "ccipDestChainState" account.
func (inst *CcipSend) SetCcipDestChainStateAccount(ccipDestChainState ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[8] = ag_solanago.Meta(ccipDestChainState).WRITE()
	return inst
}

// GetCcipDestChainStateAccount gets the "ccipDestChainState" account.
func (inst *CcipSend) GetCcipDestChainStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[8]
}

// SetCcipSenderNonceAccount sets the "ccipSenderNonce" account.
func (inst *CcipSend) SetCcipSenderNonceAccount(ccipSenderNonce ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[9] = ag_solanago.Meta(ccipSenderNonce).WRITE()
	return inst
}

// GetCcipSenderNonceAccount gets the "ccipSenderNonce" account.
func (inst *CcipSend) GetCcipSenderNonceAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[9]
}

// SetCcipFeeTokenProgramAccount sets the "ccipFeeTokenProgram" account.
func (inst *CcipSend) SetCcipFeeTokenProgramAccount(ccipFeeTokenProgram ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[10] = ag_solanago.Meta(ccipFeeTokenProgram)
	return inst
}

// GetCcipFeeTokenProgramAccount gets the "ccipFeeTokenProgram" account.
func (inst *CcipSend) GetCcipFeeTokenProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[10]
}

// SetCcipFeeTokenMintAccount sets the "ccipFeeTokenMint" account.
func (inst *CcipSend) SetCcipFeeTokenMintAccount(ccipFeeTokenMint ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[11] = ag_solanago.Meta(ccipFeeTokenMint)
	return inst
}

// GetCcipFeeTokenMintAccount gets the "ccipFeeTokenMint" account.
func (inst *CcipSend) GetCcipFeeTokenMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[11]
}

// SetCcipFeeTokenUserAtaAccount sets the "ccipFeeTokenUserAta" account.
func (inst *CcipSend) SetCcipFeeTokenUserAtaAccount(ccipFeeTokenUserAta ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[12] = ag_solanago.Meta(ccipFeeTokenUserAta).WRITE()
	return inst
}

// GetCcipFeeTokenUserAtaAccount gets the "ccipFeeTokenUserAta" account.
func (inst *CcipSend) GetCcipFeeTokenUserAtaAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[12]
}

// SetCcipFeeTokenReceiverAccount sets the "ccipFeeTokenReceiver" account.
func (inst *CcipSend) SetCcipFeeTokenReceiverAccount(ccipFeeTokenReceiver ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[13] = ag_solanago.Meta(ccipFeeTokenReceiver).WRITE()
	return inst
}

// GetCcipFeeTokenReceiverAccount gets the "ccipFeeTokenReceiver" account.
func (inst *CcipSend) GetCcipFeeTokenReceiverAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[13]
}

// SetCcipFeeBillingSignerAccount sets the "ccipFeeBillingSigner" account.
func (inst *CcipSend) SetCcipFeeBillingSignerAccount(ccipFeeBillingSigner ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[14] = ag_solanago.Meta(ccipFeeBillingSigner)
	return inst
}

// GetCcipFeeBillingSignerAccount gets the "ccipFeeBillingSigner" account.
func (inst *CcipSend) GetCcipFeeBillingSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[14]
}

// SetCcipFeeQuoterAccount sets the "ccipFeeQuoter" account.
func (inst *CcipSend) SetCcipFeeQuoterAccount(ccipFeeQuoter ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[15] = ag_solanago.Meta(ccipFeeQuoter)
	return inst
}

// GetCcipFeeQuoterAccount gets the "ccipFeeQuoter" account.
func (inst *CcipSend) GetCcipFeeQuoterAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[15]
}

// SetCcipFeeQuoterConfigAccount sets the "ccipFeeQuoterConfig" account.
func (inst *CcipSend) SetCcipFeeQuoterConfigAccount(ccipFeeQuoterConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[16] = ag_solanago.Meta(ccipFeeQuoterConfig)
	return inst
}

// GetCcipFeeQuoterConfigAccount gets the "ccipFeeQuoterConfig" account.
func (inst *CcipSend) GetCcipFeeQuoterConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[16]
}

// SetCcipFeeQuoterDestChainAccount sets the "ccipFeeQuoterDestChain" account.
func (inst *CcipSend) SetCcipFeeQuoterDestChainAccount(ccipFeeQuoterDestChain ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[17] = ag_solanago.Meta(ccipFeeQuoterDestChain)
	return inst
}

// GetCcipFeeQuoterDestChainAccount gets the "ccipFeeQuoterDestChain" account.
func (inst *CcipSend) GetCcipFeeQuoterDestChainAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[17]
}

// SetCcipFeeQuoterBillingTokenConfigAccount sets the "ccipFeeQuoterBillingTokenConfig" account.
func (inst *CcipSend) SetCcipFeeQuoterBillingTokenConfigAccount(ccipFeeQuoterBillingTokenConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[18] = ag_solanago.Meta(ccipFeeQuoterBillingTokenConfig)
	return inst
}

// GetCcipFeeQuoterBillingTokenConfigAccount gets the "ccipFeeQuoterBillingTokenConfig" account.
func (inst *CcipSend) GetCcipFeeQuoterBillingTokenConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[18]
}

// SetCcipFeeQuoterLinkTokenConfigAccount sets the "ccipFeeQuoterLinkTokenConfig" account.
func (inst *CcipSend) SetCcipFeeQuoterLinkTokenConfigAccount(ccipFeeQuoterLinkTokenConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[19] = ag_solanago.Meta(ccipFeeQuoterLinkTokenConfig)
	return inst
}

// GetCcipFeeQuoterLinkTokenConfigAccount gets the "ccipFeeQuoterLinkTokenConfig" account.
func (inst *CcipSend) GetCcipFeeQuoterLinkTokenConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[19]
}

// SetCcipRmnRemoteAccount sets the "ccipRmnRemote" account.
func (inst *CcipSend) SetCcipRmnRemoteAccount(ccipRmnRemote ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[20] = ag_solanago.Meta(ccipRmnRemote)
	return inst
}

// GetCcipRmnRemoteAccount gets the "ccipRmnRemote" account.
func (inst *CcipSend) GetCcipRmnRemoteAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[20]
}

// SetCcipRmnRemoteCursesAccount sets the "ccipRmnRemoteCurses" account.
func (inst *CcipSend) SetCcipRmnRemoteCursesAccount(ccipRmnRemoteCurses ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[21] = ag_solanago.Meta(ccipRmnRemoteCurses)
	return inst
}

// GetCcipRmnRemoteCursesAccount gets the "ccipRmnRemoteCurses" account.
func (inst *CcipSend) GetCcipRmnRemoteCursesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[21]
}

// SetCcipRmnRemoteConfigAccount sets the "ccipRmnRemoteConfig" account.
func (inst *CcipSend) SetCcipRmnRemoteConfigAccount(ccipRmnRemoteConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[22] = ag_solanago.Meta(ccipRmnRemoteConfig)
	return inst
}

// GetCcipRmnRemoteConfigAccount gets the "ccipRmnRemoteConfig" account.
func (inst *CcipSend) GetCcipRmnRemoteConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[22]
}

func (inst CcipSend) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_CcipSend,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst CcipSend) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *CcipSend) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.DestChainSelector == nil {
			return errors.New("DestChainSelector parameter is not set")
		}
		if inst.TokenAmounts == nil {
			return errors.New("TokenAmounts parameter is not set")
		}
		if inst.Data == nil {
			return errors.New("Data parameter is not set")
		}
		if inst.FeeToken == nil {
			return errors.New("FeeToken parameter is not set")
		}
		if inst.TokenIndexes == nil {
			return errors.New("TokenIndexes parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.State is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.ChainConfig is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.CcipSender is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.AuthorityFeeTokenAta is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.CcipRouter is not set")
		}
		if inst.AccountMetaSlice[7] == nil {
			return errors.New("accounts.CcipConfig is not set")
		}
		if inst.AccountMetaSlice[8] == nil {
			return errors.New("accounts.CcipDestChainState is not set")
		}
		if inst.AccountMetaSlice[9] == nil {
			return errors.New("accounts.CcipSenderNonce is not set")
		}
		if inst.AccountMetaSlice[10] == nil {
			return errors.New("accounts.CcipFeeTokenProgram is not set")
		}
		if inst.AccountMetaSlice[11] == nil {
			return errors.New("accounts.CcipFeeTokenMint is not set")
		}
		if inst.AccountMetaSlice[12] == nil {
			return errors.New("accounts.CcipFeeTokenUserAta is not set")
		}
		if inst.AccountMetaSlice[13] == nil {
			return errors.New("accounts.CcipFeeTokenReceiver is not set")
		}
		if inst.AccountMetaSlice[14] == nil {
			return errors.New("accounts.CcipFeeBillingSigner is not set")
		}
		if inst.AccountMetaSlice[15] == nil {
			return errors.New("accounts.CcipFeeQuoter is not set")
		}
		if inst.AccountMetaSlice[16] == nil {
			return errors.New("accounts.CcipFeeQuoterConfig is not set")
		}
		if inst.AccountMetaSlice[17] == nil {
			return errors.New("accounts.CcipFeeQuoterDestChain is not set")
		}
		if inst.AccountMetaSlice[18] == nil {
			return errors.New("accounts.CcipFeeQuoterBillingTokenConfig is not set")
		}
		if inst.AccountMetaSlice[19] == nil {
			return errors.New("accounts.CcipFeeQuoterLinkTokenConfig is not set")
		}
		if inst.AccountMetaSlice[20] == nil {
			return errors.New("accounts.CcipRmnRemote is not set")
		}
		if inst.AccountMetaSlice[21] == nil {
			return errors.New("accounts.CcipRmnRemoteCurses is not set")
		}
		if inst.AccountMetaSlice[22] == nil {
			return errors.New("accounts.CcipRmnRemoteConfig is not set")
		}
	}
	return nil
}

func (inst *CcipSend) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("CcipSend")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=5]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("DestChainSelector", *inst.DestChainSelector))
						paramsBranch.Child(ag_format.Param("     TokenAmounts", *inst.TokenAmounts))
						paramsBranch.Child(ag_format.Param("             Data", *inst.Data))
						paramsBranch.Child(ag_format.Param("         FeeToken", *inst.FeeToken))
						paramsBranch.Child(ag_format.Param("     TokenIndexes", *inst.TokenIndexes))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=23]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("                          state", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("                    chainConfig", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("                     ccipSender", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("           authorityFeeTokenAta", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("                      authority", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("                  systemProgram", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("                     ccipRouter", inst.AccountMetaSlice[6]))
						accountsBranch.Child(ag_format.Meta("                     ccipConfig", inst.AccountMetaSlice[7]))
						accountsBranch.Child(ag_format.Meta("             ccipDestChainState", inst.AccountMetaSlice[8]))
						accountsBranch.Child(ag_format.Meta("                ccipSenderNonce", inst.AccountMetaSlice[9]))
						accountsBranch.Child(ag_format.Meta("            ccipFeeTokenProgram", inst.AccountMetaSlice[10]))
						accountsBranch.Child(ag_format.Meta("               ccipFeeTokenMint", inst.AccountMetaSlice[11]))
						accountsBranch.Child(ag_format.Meta("            ccipFeeTokenUserAta", inst.AccountMetaSlice[12]))
						accountsBranch.Child(ag_format.Meta("           ccipFeeTokenReceiver", inst.AccountMetaSlice[13]))
						accountsBranch.Child(ag_format.Meta("           ccipFeeBillingSigner", inst.AccountMetaSlice[14]))
						accountsBranch.Child(ag_format.Meta("                  ccipFeeQuoter", inst.AccountMetaSlice[15]))
						accountsBranch.Child(ag_format.Meta("            ccipFeeQuoterConfig", inst.AccountMetaSlice[16]))
						accountsBranch.Child(ag_format.Meta("         ccipFeeQuoterDestChain", inst.AccountMetaSlice[17]))
						accountsBranch.Child(ag_format.Meta("ccipFeeQuoterBillingTokenConfig", inst.AccountMetaSlice[18]))
						accountsBranch.Child(ag_format.Meta("   ccipFeeQuoterLinkTokenConfig", inst.AccountMetaSlice[19]))
						accountsBranch.Child(ag_format.Meta("                  ccipRmnRemote", inst.AccountMetaSlice[20]))
						accountsBranch.Child(ag_format.Meta("            ccipRmnRemoteCurses", inst.AccountMetaSlice[21]))
						accountsBranch.Child(ag_format.Meta("            ccipRmnRemoteConfig", inst.AccountMetaSlice[22]))
					})
				})
		})
}

func (obj CcipSend) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `DestChainSelector` param:
	err = encoder.Encode(obj.DestChainSelector)
	if err != nil {
		return err
	}
	// Serialize `TokenAmounts` param:
	err = encoder.Encode(obj.TokenAmounts)
	if err != nil {
		return err
	}
	// Serialize `Data` param:
	err = encoder.Encode(obj.Data)
	if err != nil {
		return err
	}
	// Serialize `FeeToken` param:
	err = encoder.Encode(obj.FeeToken)
	if err != nil {
		return err
	}
	// Serialize `TokenIndexes` param:
	err = encoder.Encode(obj.TokenIndexes)
	if err != nil {
		return err
	}
	return nil
}
func (obj *CcipSend) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `DestChainSelector`:
	err = decoder.Decode(&obj.DestChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `TokenAmounts`:
	err = decoder.Decode(&obj.TokenAmounts)
	if err != nil {
		return err
	}
	// Deserialize `Data`:
	err = decoder.Decode(&obj.Data)
	if err != nil {
		return err
	}
	// Deserialize `FeeToken`:
	err = decoder.Decode(&obj.FeeToken)
	if err != nil {
		return err
	}
	// Deserialize `TokenIndexes`:
	err = decoder.Decode(&obj.TokenIndexes)
	if err != nil {
		return err
	}
	return nil
}

// NewCcipSendInstruction declares a new CcipSend instruction with the provided parameters and accounts.
func NewCcipSendInstruction(
	// Parameters:
	destChainSelector uint64,
	tokenAmounts []SVMTokenAmount,
	data []byte,
	feeToken ag_solanago.PublicKey,
	tokenIndexes []byte,
	// Accounts:
	state ag_solanago.PublicKey,
	chainConfig ag_solanago.PublicKey,
	ccipSender ag_solanago.PublicKey,
	authorityFeeTokenAta ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey,
	ccipRouter ag_solanago.PublicKey,
	ccipConfig ag_solanago.PublicKey,
	ccipDestChainState ag_solanago.PublicKey,
	ccipSenderNonce ag_solanago.PublicKey,
	ccipFeeTokenProgram ag_solanago.PublicKey,
	ccipFeeTokenMint ag_solanago.PublicKey,
	ccipFeeTokenUserAta ag_solanago.PublicKey,
	ccipFeeTokenReceiver ag_solanago.PublicKey,
	ccipFeeBillingSigner ag_solanago.PublicKey,
	ccipFeeQuoter ag_solanago.PublicKey,
	ccipFeeQuoterConfig ag_solanago.PublicKey,
	ccipFeeQuoterDestChain ag_solanago.PublicKey,
	ccipFeeQuoterBillingTokenConfig ag_solanago.PublicKey,
	ccipFeeQuoterLinkTokenConfig ag_solanago.PublicKey,
	ccipRmnRemote ag_solanago.PublicKey,
	ccipRmnRemoteCurses ag_solanago.PublicKey,
	ccipRmnRemoteConfig ag_solanago.PublicKey) *CcipSend {
	return NewCcipSendInstructionBuilder().
		SetDestChainSelector(destChainSelector).
		SetTokenAmounts(tokenAmounts).
		SetData(data).
		SetFeeToken(feeToken).
		SetTokenIndexes(tokenIndexes).
		SetStateAccount(state).
		SetChainConfigAccount(chainConfig).
		SetCcipSenderAccount(ccipSender).
		SetAuthorityFeeTokenAtaAccount(authorityFeeTokenAta).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram).
		SetCcipRouterAccount(ccipRouter).
		SetCcipConfigAccount(ccipConfig).
		SetCcipDestChainStateAccount(ccipDestChainState).
		SetCcipSenderNonceAccount(ccipSenderNonce).
		SetCcipFeeTokenProgramAccount(ccipFeeTokenProgram).
		SetCcipFeeTokenMintAccount(ccipFeeTokenMint).
		SetCcipFeeTokenUserAtaAccount(ccipFeeTokenUserAta).
		SetCcipFeeTokenReceiverAccount(ccipFeeTokenReceiver).
		SetCcipFeeBillingSignerAccount(ccipFeeBillingSigner).
		SetCcipFeeQuoterAccount(ccipFeeQuoter).
		SetCcipFeeQuoterConfigAccount(ccipFeeQuoterConfig).
		SetCcipFeeQuoterDestChainAccount(ccipFeeQuoterDestChain).
		SetCcipFeeQuoterBillingTokenConfigAccount(ccipFeeQuoterBillingTokenConfig).
		SetCcipFeeQuoterLinkTokenConfigAccount(ccipFeeQuoterLinkTokenConfig).
		SetCcipRmnRemoteAccount(ccipRmnRemote).
		SetCcipRmnRemoteCursesAccount(ccipRmnRemoteCurses).
		SetCcipRmnRemoteConfigAccount(ccipRmnRemoteConfig)
}
