// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package external_program_cpi_stub

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// AccountRead is the `accountRead` instruction.
type AccountRead struct {

	// [0] = [] u8Value
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewAccountReadInstructionBuilder creates a new `AccountRead` instruction builder.
func NewAccountReadInstructionBuilder() *AccountRead {
	nd := &AccountRead{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 1),
	}
	return nd
}

// SetU8ValueAccount sets the "u8Value" account.
func (inst *AccountRead) SetU8ValueAccount(u8Value ag_solanago.PublicKey) *AccountRead {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(u8Value)
	return inst
}

// GetU8ValueAccount gets the "u8Value" account.
func (inst *AccountRead) GetU8ValueAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

func (inst AccountRead) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_AccountRead,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst AccountRead) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *AccountRead) Validate() error {
	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.U8Value is not set")
		}
	}
	return nil
}

func (inst *AccountRead) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("AccountRead")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=0]").ParentFunc(func(paramsBranch ag_treeout.Branches) {})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=1]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("u8Value", inst.AccountMetaSlice[0]))
					})
				})
		})
}

func (obj AccountRead) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	return nil
}
func (obj *AccountRead) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	return nil
}

// NewAccountReadInstruction declares a new AccountRead instruction with the provided parameters and accounts.
func NewAccountReadInstruction(
	// Accounts:
	u8Value ag_solanago.PublicKey) *AccountRead {
	return NewAccountReadInstructionBuilder().
		SetU8ValueAccount(u8Value)
}
