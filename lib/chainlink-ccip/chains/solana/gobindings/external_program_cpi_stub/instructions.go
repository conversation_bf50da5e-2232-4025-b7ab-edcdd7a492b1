// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package external_program_cpi_stub

import (
	"bytes"
	"fmt"
	ag_spew "github.com/davecgh/go-spew/spew"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_text "github.com/gagliardetto/solana-go/text"
	ag_treeout "github.com/gagliardetto/treeout"
)

var ProgramID ag_solanago.PublicKey

func SetProgramID(pubkey ag_solanago.PublicKey) {
	ProgramID = pubkey
	ag_solanago.RegisterInstructionDecoder(ProgramID, registryDecodeInstruction)
}

const ProgramName = "ExternalProgramCpiStub"

func init() {
	if !ProgramID.IsZero() {
		ag_solanago.RegisterInstructionDecoder(ProgramID, registryDecodeInstruction)
	}
}

var (
	Instruction_Initialize = ag_binary.TypeID([8]byte{175, 175, 109, 31, 13, 152, 155, 237})

	Instruction_Empty = ag_binary.TypeID([8]byte{214, 44, 4, 247, 12, 41, 217, 110})

	Instruction_U8InstructionData = ag_binary.TypeID([8]byte{17, 175, 156, 253, 91, 173, 26, 228})

	Instruction_StructInstructionData = ag_binary.TypeID([8]byte{132, 84, 80, 47, 117, 198, 94, 67})

	Instruction_AccountRead = ag_binary.TypeID([8]byte{79, 53, 80, 124, 182, 81, 206, 85})

	Instruction_AccountMut = ag_binary.TypeID([8]byte{12, 2, 137, 19, 22, 235, 144, 70})

	// instruction that accepts arbitrarily large instruction data.
	Instruction_BigInstructionData = ag_binary.TypeID([8]byte{250, 215, 200, 174, 42, 217, 129, 182})

	// no-op instruction that does nothing, also can be used to test maximum account references(remaining_accounts)
	Instruction_NoOp = ag_binary.TypeID([8]byte{36, 122, 159, 43, 166, 240, 121, 88})

	Instruction_ComputeHeavy = ag_binary.TypeID([8]byte{114, 240, 76, 252, 181, 175, 211, 78})
)

// InstructionIDToName returns the name of the instruction given its ID.
func InstructionIDToName(id ag_binary.TypeID) string {
	switch id {
	case Instruction_Initialize:
		return "Initialize"
	case Instruction_Empty:
		return "Empty"
	case Instruction_U8InstructionData:
		return "U8InstructionData"
	case Instruction_StructInstructionData:
		return "StructInstructionData"
	case Instruction_AccountRead:
		return "AccountRead"
	case Instruction_AccountMut:
		return "AccountMut"
	case Instruction_BigInstructionData:
		return "BigInstructionData"
	case Instruction_NoOp:
		return "NoOp"
	case Instruction_ComputeHeavy:
		return "ComputeHeavy"
	default:
		return ""
	}
}

type Instruction struct {
	ag_binary.BaseVariant
}

func (inst *Instruction) EncodeToTree(parent ag_treeout.Branches) {
	if enToTree, ok := inst.Impl.(ag_text.EncodableToTree); ok {
		enToTree.EncodeToTree(parent)
	} else {
		parent.Child(ag_spew.Sdump(inst))
	}
}

var InstructionImplDef = ag_binary.NewVariantDefinition(
	ag_binary.AnchorTypeIDEncoding,
	[]ag_binary.VariantType{
		{
			"initialize", (*Initialize)(nil),
		},
		{
			"empty", (*Empty)(nil),
		},
		{
			"u8_instruction_data", (*U8InstructionData)(nil),
		},
		{
			"struct_instruction_data", (*StructInstructionData)(nil),
		},
		{
			"account_read", (*AccountRead)(nil),
		},
		{
			"account_mut", (*AccountMut)(nil),
		},
		{
			"big_instruction_data", (*BigInstructionData)(nil),
		},
		{
			"no_op", (*NoOp)(nil),
		},
		{
			"compute_heavy", (*ComputeHeavy)(nil),
		},
	},
)

func (inst *Instruction) ProgramID() ag_solanago.PublicKey {
	return ProgramID
}

func (inst *Instruction) Accounts() (out []*ag_solanago.AccountMeta) {
	return inst.Impl.(ag_solanago.AccountsGettable).GetAccounts()
}

func (inst *Instruction) Data() ([]byte, error) {
	buf := new(bytes.Buffer)
	if err := ag_binary.NewBorshEncoder(buf).Encode(inst); err != nil {
		return nil, fmt.Errorf("unable to encode instruction: %w", err)
	}
	return buf.Bytes(), nil
}

func (inst *Instruction) TextEncode(encoder *ag_text.Encoder, option *ag_text.Option) error {
	return encoder.Encode(inst.Impl, option)
}

func (inst *Instruction) UnmarshalWithDecoder(decoder *ag_binary.Decoder) error {
	return inst.BaseVariant.UnmarshalBinaryVariant(decoder, InstructionImplDef)
}

func (inst *Instruction) MarshalWithEncoder(encoder *ag_binary.Encoder) error {
	err := encoder.WriteBytes(inst.TypeID.Bytes(), false)
	if err != nil {
		return fmt.Errorf("unable to write variant type: %w", err)
	}
	return encoder.Encode(inst.Impl)
}

func registryDecodeInstruction(accounts []*ag_solanago.AccountMeta, data []byte) (interface{}, error) {
	inst, err := DecodeInstruction(accounts, data)
	if err != nil {
		return nil, err
	}
	return inst, nil
}

func DecodeInstruction(accounts []*ag_solanago.AccountMeta, data []byte) (*Instruction, error) {
	inst := new(Instruction)
	if err := ag_binary.NewBorshDecoder(data).Decode(inst); err != nil {
		return nil, fmt.Errorf("unable to decode instruction: %w", err)
	}
	if v, ok := inst.Impl.(ag_solanago.AccountsSettable); ok {
		err := v.SetAccounts(accounts)
		if err != nil {
			return nil, fmt.Errorf("unable to set accounts for instruction: %w", err)
		}
	}
	return inst, nil
}
