// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ping_pong_demo

import ag_binary "github.com/gagliardetto/binary"

type CounterpartAddress struct {
	Bytes [64]uint8
	Len   uint8
}

func (obj CounterpartAddress) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Bytes` param:
	err = encoder.Encode(obj.Bytes)
	if err != nil {
		return err
	}
	// Serialize `Len` param:
	err = encoder.Encode(obj.Len)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CounterpartAddress) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Bytes`:
	err = decoder.Decode(&obj.Bytes)
	if err != nil {
		return err
	}
	// Deserialize `Len`:
	err = decoder.Decode(&obj.Len)
	if err != nil {
		return err
	}
	return nil
}
