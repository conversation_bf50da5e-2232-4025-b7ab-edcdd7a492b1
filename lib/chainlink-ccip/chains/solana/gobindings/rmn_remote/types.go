// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package rmn_remote

import ag_binary "github.com/gagliardetto/binary"

type CurseSubject struct {
	Value [16]uint8
}

func (obj CurseSubject) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Value` param:
	err = encoder.Encode(obj.Value)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CurseSubject) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Value`:
	err = decoder.Decode(&obj.Value)
	if err != nil {
		return err
	}
	return nil
}

type CodeVersion ag_binary.BorshEnum

const (
	Default_CodeVersion CodeVersion = iota
	V1_CodeVersion
)

func (value CodeVersion) String() string {
	switch value {
	case Default_CodeVersion:
		return "Default"
	case V1_CodeVersion:
		return "V1"
	default:
		return ""
	}
}
