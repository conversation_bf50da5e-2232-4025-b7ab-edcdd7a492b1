// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package mcm

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Initialize a new multisig configuration.
//
// Creates the foundation for a new multisig instance by initializing the core configuration
// PDAs and registering the multisig_id and chain_id. This is the first step in setting up
// a new multisig configuration.
//
// # Parameters
//
// - `ctx`: The context containing the accounts required for initialization:
// - `multisig_config`: PDA that will store the core configuration
// - `root_metadata`: PDA that will store the current root's metadata
// - `expiring_root_and_op_count`: PDA that tracks the current root and operation count
// - `authority`: The deployer who becomes the initial owner
// - `program_data`: Used to validate that the caller is the program's upgrade authority
// - `chain_id`: Network identifier for the chain this configuration is targeting
// - `multisig_id`: A unique, 32-byte identifier (left-padded) for this multisig instance
//
// # Access Control
//
// This instruction can only be called by the program's upgrade authority (typically the deployer).
//
// # Note
//
// After initialization, the owner can transfer ownership through the two-step
// transfer_ownership/accept_ownership process.
type Initialize struct {
	ChainId    *uint64
	MultisigId *[32]uint8

	// [0] = [WRITE] multisigConfig
	//
	// [1] = [WRITE, SIGNER] authority
	//
	// [2] = [] systemProgram
	//
	// [3] = [] program
	//
	// [4] = [] programData
	//
	// [5] = [WRITE] rootMetadata
	//
	// [6] = [WRITE] expiringRootAndOpCount
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewInitializeInstructionBuilder creates a new `Initialize` instruction builder.
func NewInitializeInstructionBuilder() *Initialize {
	nd := &Initialize{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 7),
	}
	return nd
}

// SetChainId sets the "chainId" parameter.
func (inst *Initialize) SetChainId(chainId uint64) *Initialize {
	inst.ChainId = &chainId
	return inst
}

// SetMultisigId sets the "multisigId" parameter.
func (inst *Initialize) SetMultisigId(multisigId [32]uint8) *Initialize {
	inst.MultisigId = &multisigId
	return inst
}

// SetMultisigConfigAccount sets the "multisigConfig" account.
func (inst *Initialize) SetMultisigConfigAccount(multisigConfig ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(multisigConfig).WRITE()
	return inst
}

// GetMultisigConfigAccount gets the "multisigConfig" account.
func (inst *Initialize) GetMultisigConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *Initialize) SetAuthorityAccount(authority ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *Initialize) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *Initialize) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *Initialize) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetProgramAccount sets the "program" account.
func (inst *Initialize) SetProgramAccount(program ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(program)
	return inst
}

// GetProgramAccount gets the "program" account.
func (inst *Initialize) GetProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetProgramDataAccount sets the "programData" account.
func (inst *Initialize) SetProgramDataAccount(programData ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(programData)
	return inst
}

// GetProgramDataAccount gets the "programData" account.
func (inst *Initialize) GetProgramDataAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetRootMetadataAccount sets the "rootMetadata" account.
func (inst *Initialize) SetRootMetadataAccount(rootMetadata ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(rootMetadata).WRITE()
	return inst
}

// GetRootMetadataAccount gets the "rootMetadata" account.
func (inst *Initialize) GetRootMetadataAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetExpiringRootAndOpCountAccount sets the "expiringRootAndOpCount" account.
func (inst *Initialize) SetExpiringRootAndOpCountAccount(expiringRootAndOpCount ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(expiringRootAndOpCount).WRITE()
	return inst
}

// GetExpiringRootAndOpCountAccount gets the "expiringRootAndOpCount" account.
func (inst *Initialize) GetExpiringRootAndOpCountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

func (inst Initialize) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_Initialize,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst Initialize) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *Initialize) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.ChainId == nil {
			return errors.New("ChainId parameter is not set")
		}
		if inst.MultisigId == nil {
			return errors.New("MultisigId parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.MultisigConfig is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.Program is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.ProgramData is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.RootMetadata is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.ExpiringRootAndOpCount is not set")
		}
	}
	return nil
}

func (inst *Initialize) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("Initialize")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=2]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("   ChainId", *inst.ChainId))
						paramsBranch.Child(ag_format.Param("MultisigId", *inst.MultisigId))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=7]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("        multisigConfig", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("             authority", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("         systemProgram", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("               program", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("           programData", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("          rootMetadata", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("expiringRootAndOpCount", inst.AccountMetaSlice[6]))
					})
				})
		})
}

func (obj Initialize) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `ChainId` param:
	err = encoder.Encode(obj.ChainId)
	if err != nil {
		return err
	}
	// Serialize `MultisigId` param:
	err = encoder.Encode(obj.MultisigId)
	if err != nil {
		return err
	}
	return nil
}
func (obj *Initialize) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `ChainId`:
	err = decoder.Decode(&obj.ChainId)
	if err != nil {
		return err
	}
	// Deserialize `MultisigId`:
	err = decoder.Decode(&obj.MultisigId)
	if err != nil {
		return err
	}
	return nil
}

// NewInitializeInstruction declares a new Initialize instruction with the provided parameters and accounts.
func NewInitializeInstruction(
	// Parameters:
	chainId uint64,
	multisigId [32]uint8,
	// Accounts:
	multisigConfig ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey,
	program ag_solanago.PublicKey,
	programData ag_solanago.PublicKey,
	rootMetadata ag_solanago.PublicKey,
	expiringRootAndOpCount ag_solanago.PublicKey) *Initialize {
	return NewInitializeInstructionBuilder().
		SetChainId(chainId).
		SetMultisigId(multisigId).
		SetMultisigConfigAccount(multisigConfig).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram).
		SetProgramAccount(program).
		SetProgramDataAccount(programData).
		SetRootMetadataAccount(rootMetadata).
		SetExpiringRootAndOpCountAccount(expiringRootAndOpCount)
}
