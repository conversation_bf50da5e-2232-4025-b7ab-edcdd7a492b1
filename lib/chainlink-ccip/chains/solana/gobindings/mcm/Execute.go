// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package mcm

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Executes an operation after verifying it's authorized in the current Merkle root.
//
// This function:
// 1. Performs extensive validation checks on the operation
// - Ensures the operation is within the allowed count range
// - Verifies chain ID matches the configured chain
// - Checks the root has not expired
// - Validates the operation's nonce against current state
// 2. Verifies the operation's inclusion in the Merkle tree
// 3. Executes the cross-program invocation with the multisig signer PDA
//
// # Parameters
//
// - `ctx`: Context containing operation accounts and signer information
// - `multisig_id`: Identifier for the multisig instance
// - `chain_id`: Network identifier that must match configuration
// - `nonce`: Operation counter that must match current state
// - `data`: Instruction data to be executed
// - `proof`: Merkle proof for operation verification
//
// # Security Considerations
//
// This instruction implements secure privilege delegation through PDA signing.
// The multisig's signer PDA becomes the authoritative signer for the operation,
// allowing controlled execution of privileged actions while maintaining the
// security guarantees of the Merkle root validation.
type Execute struct {
	MultisigId *[32]uint8
	ChainId    *uint64
	Nonce      *uint64
	Data       *[]byte
	Proof      *[][32]uint8

	// [0] = [WRITE] multisigConfig
	//
	// [1] = [] rootMetadata
	//
	// [2] = [WRITE] expiringRootAndOpCount
	//
	// [3] = [] to
	//
	// [4] = [] multisigSigner
	//
	// [5] = [WRITE, SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewExecuteInstructionBuilder creates a new `Execute` instruction builder.
func NewExecuteInstructionBuilder() *Execute {
	nd := &Execute{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 6),
	}
	return nd
}

// SetMultisigId sets the "multisigId" parameter.
func (inst *Execute) SetMultisigId(multisigId [32]uint8) *Execute {
	inst.MultisigId = &multisigId
	return inst
}

// SetChainId sets the "chainId" parameter.
func (inst *Execute) SetChainId(chainId uint64) *Execute {
	inst.ChainId = &chainId
	return inst
}

// SetNonce sets the "nonce" parameter.
func (inst *Execute) SetNonce(nonce uint64) *Execute {
	inst.Nonce = &nonce
	return inst
}

// SetData sets the "data" parameter.
func (inst *Execute) SetData(data []byte) *Execute {
	inst.Data = &data
	return inst
}

// SetProof sets the "proof" parameter.
func (inst *Execute) SetProof(proof [][32]uint8) *Execute {
	inst.Proof = &proof
	return inst
}

// SetMultisigConfigAccount sets the "multisigConfig" account.
func (inst *Execute) SetMultisigConfigAccount(multisigConfig ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(multisigConfig).WRITE()
	return inst
}

// GetMultisigConfigAccount gets the "multisigConfig" account.
func (inst *Execute) GetMultisigConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetRootMetadataAccount sets the "rootMetadata" account.
func (inst *Execute) SetRootMetadataAccount(rootMetadata ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(rootMetadata)
	return inst
}

// GetRootMetadataAccount gets the "rootMetadata" account.
func (inst *Execute) GetRootMetadataAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetExpiringRootAndOpCountAccount sets the "expiringRootAndOpCount" account.
func (inst *Execute) SetExpiringRootAndOpCountAccount(expiringRootAndOpCount ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(expiringRootAndOpCount).WRITE()
	return inst
}

// GetExpiringRootAndOpCountAccount gets the "expiringRootAndOpCount" account.
func (inst *Execute) GetExpiringRootAndOpCountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetToAccount sets the "to" account.
func (inst *Execute) SetToAccount(to ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(to)
	return inst
}

// GetToAccount gets the "to" account.
func (inst *Execute) GetToAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetMultisigSignerAccount sets the "multisigSigner" account.
func (inst *Execute) SetMultisigSignerAccount(multisigSigner ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(multisigSigner)
	return inst
}

// GetMultisigSignerAccount gets the "multisigSigner" account.
func (inst *Execute) GetMultisigSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *Execute) SetAuthorityAccount(authority ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *Execute) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

func (inst Execute) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_Execute,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst Execute) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *Execute) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.MultisigId == nil {
			return errors.New("MultisigId parameter is not set")
		}
		if inst.ChainId == nil {
			return errors.New("ChainId parameter is not set")
		}
		if inst.Nonce == nil {
			return errors.New("Nonce parameter is not set")
		}
		if inst.Data == nil {
			return errors.New("Data parameter is not set")
		}
		if inst.Proof == nil {
			return errors.New("Proof parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.MultisigConfig is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.RootMetadata is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.ExpiringRootAndOpCount is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.To is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.MultisigSigner is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *Execute) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("Execute")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=5]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("MultisigId", *inst.MultisigId))
						paramsBranch.Child(ag_format.Param("   ChainId", *inst.ChainId))
						paramsBranch.Child(ag_format.Param("     Nonce", *inst.Nonce))
						paramsBranch.Child(ag_format.Param("      Data", *inst.Data))
						paramsBranch.Child(ag_format.Param("     Proof", *inst.Proof))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=6]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("        multisigConfig", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("          rootMetadata", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("expiringRootAndOpCount", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("                    to", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("        multisigSigner", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("             authority", inst.AccountMetaSlice[5]))
					})
				})
		})
}

func (obj Execute) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `MultisigId` param:
	err = encoder.Encode(obj.MultisigId)
	if err != nil {
		return err
	}
	// Serialize `ChainId` param:
	err = encoder.Encode(obj.ChainId)
	if err != nil {
		return err
	}
	// Serialize `Nonce` param:
	err = encoder.Encode(obj.Nonce)
	if err != nil {
		return err
	}
	// Serialize `Data` param:
	err = encoder.Encode(obj.Data)
	if err != nil {
		return err
	}
	// Serialize `Proof` param:
	err = encoder.Encode(obj.Proof)
	if err != nil {
		return err
	}
	return nil
}
func (obj *Execute) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `MultisigId`:
	err = decoder.Decode(&obj.MultisigId)
	if err != nil {
		return err
	}
	// Deserialize `ChainId`:
	err = decoder.Decode(&obj.ChainId)
	if err != nil {
		return err
	}
	// Deserialize `Nonce`:
	err = decoder.Decode(&obj.Nonce)
	if err != nil {
		return err
	}
	// Deserialize `Data`:
	err = decoder.Decode(&obj.Data)
	if err != nil {
		return err
	}
	// Deserialize `Proof`:
	err = decoder.Decode(&obj.Proof)
	if err != nil {
		return err
	}
	return nil
}

// NewExecuteInstruction declares a new Execute instruction with the provided parameters and accounts.
func NewExecuteInstruction(
	// Parameters:
	multisigId [32]uint8,
	chainId uint64,
	nonce uint64,
	data []byte,
	proof [][32]uint8,
	// Accounts:
	multisigConfig ag_solanago.PublicKey,
	rootMetadata ag_solanago.PublicKey,
	expiringRootAndOpCount ag_solanago.PublicKey,
	to ag_solanago.PublicKey,
	multisigSigner ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *Execute {
	return NewExecuteInstructionBuilder().
		SetMultisigId(multisigId).
		SetChainId(chainId).
		SetNonce(nonce).
		SetData(data).
		SetProof(proof).
		SetMultisigConfigAccount(multisigConfig).
		SetRootMetadataAccount(rootMetadata).
		SetExpiringRootAndOpCountAccount(expiringRootAndOpCount).
		SetToAccount(to).
		SetMultisigSignerAccount(multisigSigner).
		SetAuthorityAccount(authority)
}
