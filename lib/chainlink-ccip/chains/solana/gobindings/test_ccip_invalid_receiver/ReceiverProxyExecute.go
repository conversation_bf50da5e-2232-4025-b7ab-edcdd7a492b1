// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package test_ccip_invalid_receiver

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// ReceiverProxyExecute is the `receiverProxyExecute` instruction.
type ReceiverProxyExecute struct {
	Message *Any2SVMMessage

	// [0] = [] testReceiver
	// ··········· CHECK
	//
	// [1] = [] cpiSigner
	// ··········· CHECK
	//
	// [2] = [] offrampProgram
	// ··········· CHECK
	//
	// [3] = [] allowedOfframp
	// ··········· CHECK
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewReceiverProxyExecuteInstructionBuilder creates a new `ReceiverProxyExecute` instruction builder.
func NewReceiverProxyExecuteInstructionBuilder() *ReceiverProxyExecute {
	nd := &ReceiverProxyExecute{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 4),
	}
	return nd
}

// SetMessage sets the "message" parameter.
func (inst *ReceiverProxyExecute) SetMessage(message Any2SVMMessage) *ReceiverProxyExecute {
	inst.Message = &message
	return inst
}

// SetTestReceiverAccount sets the "testReceiver" account.
// CHECK
func (inst *ReceiverProxyExecute) SetTestReceiverAccount(testReceiver ag_solanago.PublicKey) *ReceiverProxyExecute {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(testReceiver)
	return inst
}

// GetTestReceiverAccount gets the "testReceiver" account.
// CHECK
func (inst *ReceiverProxyExecute) GetTestReceiverAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetCpiSignerAccount sets the "cpiSigner" account.
// CHECK
func (inst *ReceiverProxyExecute) SetCpiSignerAccount(cpiSigner ag_solanago.PublicKey) *ReceiverProxyExecute {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(cpiSigner)
	return inst
}

// GetCpiSignerAccount gets the "cpiSigner" account.
// CHECK
func (inst *ReceiverProxyExecute) GetCpiSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetOfframpProgramAccount sets the "offrampProgram" account.
// CHECK
func (inst *ReceiverProxyExecute) SetOfframpProgramAccount(offrampProgram ag_solanago.PublicKey) *ReceiverProxyExecute {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(offrampProgram)
	return inst
}

// GetOfframpProgramAccount gets the "offrampProgram" account.
// CHECK
func (inst *ReceiverProxyExecute) GetOfframpProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetAllowedOfframpAccount sets the "allowedOfframp" account.
// CHECK
func (inst *ReceiverProxyExecute) SetAllowedOfframpAccount(allowedOfframp ag_solanago.PublicKey) *ReceiverProxyExecute {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(allowedOfframp)
	return inst
}

// GetAllowedOfframpAccount gets the "allowedOfframp" account.
// CHECK
func (inst *ReceiverProxyExecute) GetAllowedOfframpAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

func (inst ReceiverProxyExecute) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_ReceiverProxyExecute,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst ReceiverProxyExecute) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *ReceiverProxyExecute) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.Message == nil {
			return errors.New("Message parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.TestReceiver is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.CpiSigner is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.OfframpProgram is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.AllowedOfframp is not set")
		}
	}
	return nil
}

func (inst *ReceiverProxyExecute) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("ReceiverProxyExecute")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("Message", *inst.Message))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=4]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("  testReceiver", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("     cpiSigner", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("offrampProgram", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("allowedOfframp", inst.AccountMetaSlice[3]))
					})
				})
		})
}

func (obj ReceiverProxyExecute) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Message` param:
	err = encoder.Encode(obj.Message)
	if err != nil {
		return err
	}
	return nil
}
func (obj *ReceiverProxyExecute) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Message`:
	err = decoder.Decode(&obj.Message)
	if err != nil {
		return err
	}
	return nil
}

// NewReceiverProxyExecuteInstruction declares a new ReceiverProxyExecute instruction with the provided parameters and accounts.
func NewReceiverProxyExecuteInstruction(
	// Parameters:
	message Any2SVMMessage,
	// Accounts:
	testReceiver ag_solanago.PublicKey,
	cpiSigner ag_solanago.PublicKey,
	offrampProgram ag_solanago.PublicKey,
	allowedOfframp ag_solanago.PublicKey) *ReceiverProxyExecute {
	return NewReceiverProxyExecuteInstructionBuilder().
		SetMessage(message).
		SetTestReceiverAccount(testReceiver).
		SetCpiSignerAccount(cpiSigner).
		SetOfframpProgramAccount(offrampProgram).
		SetAllowedOfframpAccount(allowedOfframp)
}
