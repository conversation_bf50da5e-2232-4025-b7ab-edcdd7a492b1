// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package timelock

import (
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
)

type BlockedSelectors struct {
	Xs  [128][8]uint8
	Len uint64
}

func (obj BlockedSelectors) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Xs` param:
	err = encoder.Encode(obj.Xs)
	if err != nil {
		return err
	}
	// Serialize `Len` param:
	err = encoder.Encode(obj.Len)
	if err != nil {
		return err
	}
	return nil
}

func (obj *BlockedSelectors) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Xs`:
	err = decoder.Decode(&obj.Xs)
	if err != nil {
		return err
	}
	// Deserialize `Len`:
	err = decoder.Decode(&obj.Len)
	if err != nil {
		return err
	}
	return nil
}

type InstructionData struct {
	ProgramId ag_solanago.PublicKey
	Data      []byte
	Accounts  []InstructionAccount
}

func (obj InstructionData) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `ProgramId` param:
	err = encoder.Encode(obj.ProgramId)
	if err != nil {
		return err
	}
	// Serialize `Data` param:
	err = encoder.Encode(obj.Data)
	if err != nil {
		return err
	}
	// Serialize `Accounts` param:
	err = encoder.Encode(obj.Accounts)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InstructionData) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `ProgramId`:
	err = decoder.Decode(&obj.ProgramId)
	if err != nil {
		return err
	}
	// Deserialize `Data`:
	err = decoder.Decode(&obj.Data)
	if err != nil {
		return err
	}
	// Deserialize `Accounts`:
	err = decoder.Decode(&obj.Accounts)
	if err != nil {
		return err
	}
	return nil
}

type InstructionAccount struct {
	Pubkey     ag_solanago.PublicKey
	IsSigner   bool
	IsWritable bool
}

func (obj InstructionAccount) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Pubkey` param:
	err = encoder.Encode(obj.Pubkey)
	if err != nil {
		return err
	}
	// Serialize `IsSigner` param:
	err = encoder.Encode(obj.IsSigner)
	if err != nil {
		return err
	}
	// Serialize `IsWritable` param:
	err = encoder.Encode(obj.IsWritable)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InstructionAccount) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Pubkey`:
	err = decoder.Decode(&obj.Pubkey)
	if err != nil {
		return err
	}
	// Deserialize `IsSigner`:
	err = decoder.Decode(&obj.IsSigner)
	if err != nil {
		return err
	}
	// Deserialize `IsWritable`:
	err = decoder.Decode(&obj.IsWritable)
	if err != nil {
		return err
	}
	return nil
}

type TimelockError ag_binary.BorshEnum

const (
	InvalidInput_TimelockError TimelockError = iota
	Overflow_TimelockError
	InvalidId_TimelockError
	OperationNotFinalized_TimelockError
	OperationAlreadyFinalized_TimelockError
	TooManyInstructions_TimelockError
	OperationAlreadyScheduled_TimelockError
	DelayInsufficient_TimelockError
	OperationNotCancellable_TimelockError
	OperationNotReady_TimelockError
	OperationAlreadyExecuted_TimelockError
	MissingDependency_TimelockError
	InvalidAccessController_TimelockError
	BlockedSelector_TimelockError
	AlreadyBlocked_TimelockError
	SelectorNotFound_TimelockError
	MaxCapacityReached_TimelockError
)

func (value TimelockError) String() string {
	switch value {
	case InvalidInput_TimelockError:
		return "InvalidInput"
	case Overflow_TimelockError:
		return "Overflow"
	case InvalidId_TimelockError:
		return "InvalidId"
	case OperationNotFinalized_TimelockError:
		return "OperationNotFinalized"
	case OperationAlreadyFinalized_TimelockError:
		return "OperationAlreadyFinalized"
	case TooManyInstructions_TimelockError:
		return "TooManyInstructions"
	case OperationAlreadyScheduled_TimelockError:
		return "OperationAlreadyScheduled"
	case DelayInsufficient_TimelockError:
		return "DelayInsufficient"
	case OperationNotCancellable_TimelockError:
		return "OperationNotCancellable"
	case OperationNotReady_TimelockError:
		return "OperationNotReady"
	case OperationAlreadyExecuted_TimelockError:
		return "OperationAlreadyExecuted"
	case MissingDependency_TimelockError:
		return "MissingDependency"
	case InvalidAccessController_TimelockError:
		return "InvalidAccessController"
	case BlockedSelector_TimelockError:
		return "BlockedSelector"
	case AlreadyBlocked_TimelockError:
		return "AlreadyBlocked"
	case SelectorNotFound_TimelockError:
		return "SelectorNotFound"
	case MaxCapacityReached_TimelockError:
		return "MaxCapacityReached"
	default:
		return ""
	}
}

type Role ag_binary.BorshEnum

const (
	Admin_Role Role = iota
	Proposer_Role
	Executor_Role
	Canceller_Role
	Bypasser_Role
)

func (value Role) String() string {
	switch value {
	case Admin_Role:
		return "Admin"
	case Proposer_Role:
		return "Proposer"
	case Executor_Role:
		return "Executor"
	case Canceller_Role:
		return "Canceller"
	case Bypasser_Role:
		return "Bypasser"
	default:
		return ""
	}
}

type OperationState ag_binary.BorshEnum

const (
	Initialized_OperationState OperationState = iota
	Finalized_OperationState
	Scheduled_OperationState
	Done_OperationState
)

func (value OperationState) String() string {
	switch value {
	case Initialized_OperationState:
		return "Initialized"
	case Finalized_OperationState:
		return "Finalized"
	case Scheduled_OperationState:
		return "Scheduled"
	case Done_OperationState:
		return "Done"
	default:
		return ""
	}
}
