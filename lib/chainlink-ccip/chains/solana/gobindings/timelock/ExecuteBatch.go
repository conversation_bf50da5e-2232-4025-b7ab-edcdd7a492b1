// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package timelock

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Executes a scheduled batch of operations after validating readiness and predecessor dependencies.
//
// This function:
// 1. Verifies the operation is ready for execution (delay period has passed)
// 2. Validates that any predecessor operation has been completed
// 3. Executes each instruction in the operation using the timelock signer PDA
// 4. Emits events for each executed instruction
//
// # Parameters
//
// - `ctx`: Context containing operation accounts and signer information
// - `timelock_id`: Identifier for the timelock instance
// - `_id`: Operation ID (used for PDA derivation)
//
// # Security Considerations
//
// This instruction uses PDA signing to create a trusted execution environment.
// The timelock's signer <PERSON><PERSON> will replace any account marked as a signer in the
// original instructions, providing the necessary privileges while maintaining
// security through program derivation.
type ExecuteBatch struct {
	TimelockId *[32]uint8
	Id         *[32]uint8

	// [0] = [WRITE] operation
	//
	// [1] = [] predecessorOperation
	//
	// [2] = [] config
	//
	// [3] = [] timelockSigner
	//
	// [4] = [] roleAccessController
	//
	// [5] = [WRITE, SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewExecuteBatchInstructionBuilder creates a new `ExecuteBatch` instruction builder.
func NewExecuteBatchInstructionBuilder() *ExecuteBatch {
	nd := &ExecuteBatch{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 6),
	}
	return nd
}

// SetTimelockId sets the "timelockId" parameter.
func (inst *ExecuteBatch) SetTimelockId(timelockId [32]uint8) *ExecuteBatch {
	inst.TimelockId = &timelockId
	return inst
}

// SetId sets the "id" parameter.
func (inst *ExecuteBatch) SetId(id [32]uint8) *ExecuteBatch {
	inst.Id = &id
	return inst
}

// SetOperationAccount sets the "operation" account.
func (inst *ExecuteBatch) SetOperationAccount(operation ag_solanago.PublicKey) *ExecuteBatch {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(operation).WRITE()
	return inst
}

// GetOperationAccount gets the "operation" account.
func (inst *ExecuteBatch) GetOperationAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetPredecessorOperationAccount sets the "predecessorOperation" account.
func (inst *ExecuteBatch) SetPredecessorOperationAccount(predecessorOperation ag_solanago.PublicKey) *ExecuteBatch {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(predecessorOperation)
	return inst
}

// GetPredecessorOperationAccount gets the "predecessorOperation" account.
func (inst *ExecuteBatch) GetPredecessorOperationAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetConfigAccount sets the "config" account.
func (inst *ExecuteBatch) SetConfigAccount(config ag_solanago.PublicKey) *ExecuteBatch {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *ExecuteBatch) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetTimelockSignerAccount sets the "timelockSigner" account.
func (inst *ExecuteBatch) SetTimelockSignerAccount(timelockSigner ag_solanago.PublicKey) *ExecuteBatch {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(timelockSigner)
	return inst
}

// GetTimelockSignerAccount gets the "timelockSigner" account.
func (inst *ExecuteBatch) GetTimelockSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetRoleAccessControllerAccount sets the "roleAccessController" account.
func (inst *ExecuteBatch) SetRoleAccessControllerAccount(roleAccessController ag_solanago.PublicKey) *ExecuteBatch {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(roleAccessController)
	return inst
}

// GetRoleAccessControllerAccount gets the "roleAccessController" account.
func (inst *ExecuteBatch) GetRoleAccessControllerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *ExecuteBatch) SetAuthorityAccount(authority ag_solanago.PublicKey) *ExecuteBatch {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *ExecuteBatch) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

func (inst ExecuteBatch) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_ExecuteBatch,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst ExecuteBatch) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *ExecuteBatch) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.TimelockId == nil {
			return errors.New("TimelockId parameter is not set")
		}
		if inst.Id == nil {
			return errors.New("Id parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Operation is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.PredecessorOperation is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.TimelockSigner is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.RoleAccessController is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *ExecuteBatch) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("ExecuteBatch")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=2]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("TimelockId", *inst.TimelockId))
						paramsBranch.Child(ag_format.Param("        Id", *inst.Id))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=6]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("           operation", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("predecessorOperation", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("              config", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("      timelockSigner", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("roleAccessController", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("           authority", inst.AccountMetaSlice[5]))
					})
				})
		})
}

func (obj ExecuteBatch) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `TimelockId` param:
	err = encoder.Encode(obj.TimelockId)
	if err != nil {
		return err
	}
	// Serialize `Id` param:
	err = encoder.Encode(obj.Id)
	if err != nil {
		return err
	}
	return nil
}
func (obj *ExecuteBatch) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `TimelockId`:
	err = decoder.Decode(&obj.TimelockId)
	if err != nil {
		return err
	}
	// Deserialize `Id`:
	err = decoder.Decode(&obj.Id)
	if err != nil {
		return err
	}
	return nil
}

// NewExecuteBatchInstruction declares a new ExecuteBatch instruction with the provided parameters and accounts.
func NewExecuteBatchInstruction(
	// Parameters:
	timelockId [32]uint8,
	id [32]uint8,
	// Accounts:
	operation ag_solanago.PublicKey,
	predecessorOperation ag_solanago.PublicKey,
	config ag_solanago.PublicKey,
	timelockSigner ag_solanago.PublicKey,
	roleAccessController ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *ExecuteBatch {
	return NewExecuteBatchInstructionBuilder().
		SetTimelockId(timelockId).
		SetId(id).
		SetOperationAccount(operation).
		SetPredecessorOperationAccount(predecessorOperation).
		SetConfigAccount(config).
		SetTimelockSignerAccount(timelockSigner).
		SetRoleAccessControllerAccount(roleAccessController).
		SetAuthorityAccount(authority)
}
