// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package timelock

import (
	"fmt"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
)

type Config struct {
	TimelockId                    [32]uint8
	Owner                         ag_solanago.PublicKey
	ProposedOwner                 ag_solanago.PublicKey
	ProposerRoleAccessController  ag_solanago.PublicKey
	ExecutorRoleAccessController  ag_solanago.PublicKey
	CancellerRoleAccessController ag_solanago.PublicKey
	BypasserRoleAccessController  ag_solanago.PublicKey
	MinDelay                      uint64
	BlockedSelectors              BlockedSelectors
}

var ConfigDiscriminator = [8]byte{155, 12, 170, 224, 30, 250, 204, 130}

func (obj Config) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(ConfigDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `TimelockId` param:
	err = encoder.Encode(obj.TimelockId)
	if err != nil {
		return err
	}
	// Serialize `Owner` param:
	err = encoder.Encode(obj.Owner)
	if err != nil {
		return err
	}
	// Serialize `ProposedOwner` param:
	err = encoder.Encode(obj.ProposedOwner)
	if err != nil {
		return err
	}
	// Serialize `ProposerRoleAccessController` param:
	err = encoder.Encode(obj.ProposerRoleAccessController)
	if err != nil {
		return err
	}
	// Serialize `ExecutorRoleAccessController` param:
	err = encoder.Encode(obj.ExecutorRoleAccessController)
	if err != nil {
		return err
	}
	// Serialize `CancellerRoleAccessController` param:
	err = encoder.Encode(obj.CancellerRoleAccessController)
	if err != nil {
		return err
	}
	// Serialize `BypasserRoleAccessController` param:
	err = encoder.Encode(obj.BypasserRoleAccessController)
	if err != nil {
		return err
	}
	// Serialize `MinDelay` param:
	err = encoder.Encode(obj.MinDelay)
	if err != nil {
		return err
	}
	// Serialize `BlockedSelectors` param:
	err = encoder.Encode(obj.BlockedSelectors)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(ConfigDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[155 12 170 224 30 250 204 130]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `TimelockId`:
	err = decoder.Decode(&obj.TimelockId)
	if err != nil {
		return err
	}
	// Deserialize `Owner`:
	err = decoder.Decode(&obj.Owner)
	if err != nil {
		return err
	}
	// Deserialize `ProposedOwner`:
	err = decoder.Decode(&obj.ProposedOwner)
	if err != nil {
		return err
	}
	// Deserialize `ProposerRoleAccessController`:
	err = decoder.Decode(&obj.ProposerRoleAccessController)
	if err != nil {
		return err
	}
	// Deserialize `ExecutorRoleAccessController`:
	err = decoder.Decode(&obj.ExecutorRoleAccessController)
	if err != nil {
		return err
	}
	// Deserialize `CancellerRoleAccessController`:
	err = decoder.Decode(&obj.CancellerRoleAccessController)
	if err != nil {
		return err
	}
	// Deserialize `BypasserRoleAccessController`:
	err = decoder.Decode(&obj.BypasserRoleAccessController)
	if err != nil {
		return err
	}
	// Deserialize `MinDelay`:
	err = decoder.Decode(&obj.MinDelay)
	if err != nil {
		return err
	}
	// Deserialize `BlockedSelectors`:
	err = decoder.Decode(&obj.BlockedSelectors)
	if err != nil {
		return err
	}
	return nil
}

type Operation struct {
	State             OperationState
	Timestamp         uint64
	Id                [32]uint8
	Predecessor       [32]uint8
	Salt              [32]uint8
	TotalInstructions uint32
	Instructions      []InstructionData
}

var OperationDiscriminator = [8]byte{171, 150, 196, 17, 229, 166, 58, 44}

func (obj Operation) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(OperationDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `State` param:
	err = encoder.Encode(obj.State)
	if err != nil {
		return err
	}
	// Serialize `Timestamp` param:
	err = encoder.Encode(obj.Timestamp)
	if err != nil {
		return err
	}
	// Serialize `Id` param:
	err = encoder.Encode(obj.Id)
	if err != nil {
		return err
	}
	// Serialize `Predecessor` param:
	err = encoder.Encode(obj.Predecessor)
	if err != nil {
		return err
	}
	// Serialize `Salt` param:
	err = encoder.Encode(obj.Salt)
	if err != nil {
		return err
	}
	// Serialize `TotalInstructions` param:
	err = encoder.Encode(obj.TotalInstructions)
	if err != nil {
		return err
	}
	// Serialize `Instructions` param:
	err = encoder.Encode(obj.Instructions)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Operation) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(OperationDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[171 150 196 17 229 166 58 44]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `State`:
	err = decoder.Decode(&obj.State)
	if err != nil {
		return err
	}
	// Deserialize `Timestamp`:
	err = decoder.Decode(&obj.Timestamp)
	if err != nil {
		return err
	}
	// Deserialize `Id`:
	err = decoder.Decode(&obj.Id)
	if err != nil {
		return err
	}
	// Deserialize `Predecessor`:
	err = decoder.Decode(&obj.Predecessor)
	if err != nil {
		return err
	}
	// Deserialize `Salt`:
	err = decoder.Decode(&obj.Salt)
	if err != nil {
		return err
	}
	// Deserialize `TotalInstructions`:
	err = decoder.Decode(&obj.TotalInstructions)
	if err != nil {
		return err
	}
	// Deserialize `Instructions`:
	err = decoder.Decode(&obj.Instructions)
	if err != nil {
		return err
	}
	return nil
}
