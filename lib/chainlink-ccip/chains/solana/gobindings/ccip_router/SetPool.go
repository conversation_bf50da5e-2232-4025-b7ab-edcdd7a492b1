// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Sets the pool lookup table for a given token mint.
//
// The administrator of the token admin registry can set the pool lookup table for a given token mint.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for setting the pool.
// * `writable_indexes` - a bit map of the indexes of the accounts in lookup table that are writable
type SetPool struct {
	WritableIndexes *[]byte

	// [0] = [] config
	//
	// [1] = [WRITE] tokenAdminRegistry
	//
	// [2] = [] mint
	//
	// [3] = [] poolLookuptable
	//
	// [4] = [WRITE, SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewSetPoolInstructionBuilder creates a new `SetPool` instruction builder.
func NewSetPoolInstructionBuilder() *SetPool {
	nd := &SetPool{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 5),
	}
	return nd
}

// SetWritableIndexes sets the "writableIndexes" parameter.
func (inst *SetPool) SetWritableIndexes(writableIndexes []byte) *SetPool {
	inst.WritableIndexes = &writableIndexes
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *SetPool) SetConfigAccount(config ag_solanago.PublicKey) *SetPool {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *SetPool) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetTokenAdminRegistryAccount sets the "tokenAdminRegistry" account.
func (inst *SetPool) SetTokenAdminRegistryAccount(tokenAdminRegistry ag_solanago.PublicKey) *SetPool {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(tokenAdminRegistry).WRITE()
	return inst
}

// GetTokenAdminRegistryAccount gets the "tokenAdminRegistry" account.
func (inst *SetPool) GetTokenAdminRegistryAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetMintAccount sets the "mint" account.
func (inst *SetPool) SetMintAccount(mint ag_solanago.PublicKey) *SetPool {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(mint)
	return inst
}

// GetMintAccount gets the "mint" account.
func (inst *SetPool) GetMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetPoolLookuptableAccount sets the "poolLookuptable" account.
func (inst *SetPool) SetPoolLookuptableAccount(poolLookuptable ag_solanago.PublicKey) *SetPool {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(poolLookuptable)
	return inst
}

// GetPoolLookuptableAccount gets the "poolLookuptable" account.
func (inst *SetPool) GetPoolLookuptableAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *SetPool) SetAuthorityAccount(authority ag_solanago.PublicKey) *SetPool {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *SetPool) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

func (inst SetPool) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_SetPool,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst SetPool) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *SetPool) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.WritableIndexes == nil {
			return errors.New("WritableIndexes parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.TokenAdminRegistry is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Mint is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.PoolLookuptable is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *SetPool) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("SetPool")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("WritableIndexes", *inst.WritableIndexes))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=5]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("            config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("tokenAdminRegistry", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("              mint", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("   poolLookuptable", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("         authority", inst.AccountMetaSlice[4]))
					})
				})
		})
}

func (obj SetPool) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `WritableIndexes` param:
	err = encoder.Encode(obj.WritableIndexes)
	if err != nil {
		return err
	}
	return nil
}
func (obj *SetPool) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `WritableIndexes`:
	err = decoder.Decode(&obj.WritableIndexes)
	if err != nil {
		return err
	}
	return nil
}

// NewSetPoolInstruction declares a new SetPool instruction with the provided parameters and accounts.
func NewSetPoolInstruction(
	// Parameters:
	writableIndexes []byte,
	// Accounts:
	config ag_solanago.PublicKey,
	tokenAdminRegistry ag_solanago.PublicKey,
	mint ag_solanago.PublicKey,
	poolLookuptable ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *SetPool {
	return NewSetPoolInstructionBuilder().
		SetWritableIndexes(writableIndexes).
		SetConfigAccount(config).
		SetTokenAdminRegistryAccount(tokenAdminRegistry).
		SetMintAccount(mint).
		SetPoolLookuptableAccount(poolLookuptable).
		SetAuthorityAccount(authority)
}
