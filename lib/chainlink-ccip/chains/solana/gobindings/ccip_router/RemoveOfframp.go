// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Remove an offramp address from the list of offramps allowed by the router, for a
// particular source chain. External users will check this list before accepting
// a `ccip_receive` CPI.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for this operation.
// * `source_chain_selector` - The source chain for the offramp's lane.
// * `offramp` - The offramp's address.
type RemoveOfframp struct {
	SourceChainSelector *uint64
	Offramp             *ag_solanago.PublicKey

	// [0] = [WRITE] allowedOfframp
	//
	// [1] = [] config
	//
	// [2] = [WRITE, SIGNER] authority
	//
	// [3] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewRemoveOfframpInstructionBuilder creates a new `RemoveOfframp` instruction builder.
func NewRemoveOfframpInstructionBuilder() *RemoveOfframp {
	nd := &RemoveOfframp{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 4),
	}
	return nd
}

// SetSourceChainSelector sets the "sourceChainSelector" parameter.
func (inst *RemoveOfframp) SetSourceChainSelector(sourceChainSelector uint64) *RemoveOfframp {
	inst.SourceChainSelector = &sourceChainSelector
	return inst
}

// SetOfframp sets the "offramp" parameter.
func (inst *RemoveOfframp) SetOfframp(offramp ag_solanago.PublicKey) *RemoveOfframp {
	inst.Offramp = &offramp
	return inst
}

// SetAllowedOfframpAccount sets the "allowedOfframp" account.
func (inst *RemoveOfframp) SetAllowedOfframpAccount(allowedOfframp ag_solanago.PublicKey) *RemoveOfframp {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(allowedOfframp).WRITE()
	return inst
}

// GetAllowedOfframpAccount gets the "allowedOfframp" account.
func (inst *RemoveOfframp) GetAllowedOfframpAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigAccount sets the "config" account.
func (inst *RemoveOfframp) SetConfigAccount(config ag_solanago.PublicKey) *RemoveOfframp {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *RemoveOfframp) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *RemoveOfframp) SetAuthorityAccount(authority ag_solanago.PublicKey) *RemoveOfframp {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *RemoveOfframp) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *RemoveOfframp) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *RemoveOfframp {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *RemoveOfframp) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

func (inst RemoveOfframp) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_RemoveOfframp,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst RemoveOfframp) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *RemoveOfframp) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.SourceChainSelector == nil {
			return errors.New("SourceChainSelector parameter is not set")
		}
		if inst.Offramp == nil {
			return errors.New("Offramp parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.AllowedOfframp is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *RemoveOfframp) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("RemoveOfframp")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=2]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("SourceChainSelector", *inst.SourceChainSelector))
						paramsBranch.Child(ag_format.Param("            Offramp", *inst.Offramp))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=4]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("allowedOfframp", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("        config", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("     authority", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta(" systemProgram", inst.AccountMetaSlice[3]))
					})
				})
		})
}

func (obj RemoveOfframp) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `SourceChainSelector` param:
	err = encoder.Encode(obj.SourceChainSelector)
	if err != nil {
		return err
	}
	// Serialize `Offramp` param:
	err = encoder.Encode(obj.Offramp)
	if err != nil {
		return err
	}
	return nil
}
func (obj *RemoveOfframp) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `SourceChainSelector`:
	err = decoder.Decode(&obj.SourceChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `Offramp`:
	err = decoder.Decode(&obj.Offramp)
	if err != nil {
		return err
	}
	return nil
}

// NewRemoveOfframpInstruction declares a new RemoveOfframp instruction with the provided parameters and accounts.
func NewRemoveOfframpInstruction(
	// Parameters:
	sourceChainSelector uint64,
	offramp ag_solanago.PublicKey,
	// Accounts:
	allowedOfframp ag_solanago.PublicKey,
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *RemoveOfframp {
	return NewRemoveOfframpInstructionBuilder().
		SetSourceChainSelector(sourceChainSelector).
		SetOfframp(offramp).
		SetAllowedOfframpAccount(allowedOfframp).
		SetConfigAccount(config).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
