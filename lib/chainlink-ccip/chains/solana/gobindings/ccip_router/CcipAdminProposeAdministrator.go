// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Token Admin Registry //
// Registers the Token Admin Registry via the CCIP Admin
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for registration.
// * `token_admin_registry_admin` - The public key of the token admin registry admin to propose.
type CcipAdminProposeAdministrator struct {
	TokenAdminRegistryAdmin *ag_solanago.PublicKey

	// [0] = [] config
	//
	// [1] = [WRITE] tokenAdminRegistry
	//
	// [2] = [] mint
	//
	// [3] = [WRITE, SIGNER] authority
	//
	// [4] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewCcipAdminProposeAdministratorInstructionBuilder creates a new `CcipAdminProposeAdministrator` instruction builder.
func NewCcipAdminProposeAdministratorInstructionBuilder() *CcipAdminProposeAdministrator {
	nd := &CcipAdminProposeAdministrator{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 5),
	}
	return nd
}

// SetTokenAdminRegistryAdmin sets the "tokenAdminRegistryAdmin" parameter.
func (inst *CcipAdminProposeAdministrator) SetTokenAdminRegistryAdmin(tokenAdminRegistryAdmin ag_solanago.PublicKey) *CcipAdminProposeAdministrator {
	inst.TokenAdminRegistryAdmin = &tokenAdminRegistryAdmin
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *CcipAdminProposeAdministrator) SetConfigAccount(config ag_solanago.PublicKey) *CcipAdminProposeAdministrator {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *CcipAdminProposeAdministrator) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetTokenAdminRegistryAccount sets the "tokenAdminRegistry" account.
func (inst *CcipAdminProposeAdministrator) SetTokenAdminRegistryAccount(tokenAdminRegistry ag_solanago.PublicKey) *CcipAdminProposeAdministrator {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(tokenAdminRegistry).WRITE()
	return inst
}

// GetTokenAdminRegistryAccount gets the "tokenAdminRegistry" account.
func (inst *CcipAdminProposeAdministrator) GetTokenAdminRegistryAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetMintAccount sets the "mint" account.
func (inst *CcipAdminProposeAdministrator) SetMintAccount(mint ag_solanago.PublicKey) *CcipAdminProposeAdministrator {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(mint)
	return inst
}

// GetMintAccount gets the "mint" account.
func (inst *CcipAdminProposeAdministrator) GetMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *CcipAdminProposeAdministrator) SetAuthorityAccount(authority ag_solanago.PublicKey) *CcipAdminProposeAdministrator {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *CcipAdminProposeAdministrator) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *CcipAdminProposeAdministrator) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *CcipAdminProposeAdministrator {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *CcipAdminProposeAdministrator) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

func (inst CcipAdminProposeAdministrator) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_CcipAdminProposeAdministrator,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst CcipAdminProposeAdministrator) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *CcipAdminProposeAdministrator) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.TokenAdminRegistryAdmin == nil {
			return errors.New("TokenAdminRegistryAdmin parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.TokenAdminRegistry is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Mint is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *CcipAdminProposeAdministrator) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("CcipAdminProposeAdministrator")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("TokenAdminRegistryAdmin", *inst.TokenAdminRegistryAdmin))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=5]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("            config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("tokenAdminRegistry", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("              mint", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("         authority", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("     systemProgram", inst.AccountMetaSlice[4]))
					})
				})
		})
}

func (obj CcipAdminProposeAdministrator) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `TokenAdminRegistryAdmin` param:
	err = encoder.Encode(obj.TokenAdminRegistryAdmin)
	if err != nil {
		return err
	}
	return nil
}
func (obj *CcipAdminProposeAdministrator) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `TokenAdminRegistryAdmin`:
	err = decoder.Decode(&obj.TokenAdminRegistryAdmin)
	if err != nil {
		return err
	}
	return nil
}

// NewCcipAdminProposeAdministratorInstruction declares a new CcipAdminProposeAdministrator instruction with the provided parameters and accounts.
func NewCcipAdminProposeAdministratorInstruction(
	// Parameters:
	tokenAdminRegistryAdmin ag_solanago.PublicKey,
	// Accounts:
	config ag_solanago.PublicKey,
	tokenAdminRegistry ag_solanago.PublicKey,
	mint ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *CcipAdminProposeAdministrator {
	return NewCcipAdminProposeAdministratorInstructionBuilder().
		SetTokenAdminRegistryAdmin(tokenAdminRegistryAdmin).
		SetConfigAccount(config).
		SetTokenAdminRegistryAccount(tokenAdminRegistry).
		SetMintAccount(mint).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
