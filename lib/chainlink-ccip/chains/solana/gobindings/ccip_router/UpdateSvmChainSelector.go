// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Updates the SVM chain selector in the router configuration.
//
// This method should only be used if there was an error with the initial configuration or if the solana chain selector changes.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for updating the configuration.
// * `new_chain_selector` - The new chain selector for SVM.
type UpdateSvmChainSelector struct {
	NewChainSelector *uint64

	// [0] = [WRITE] config
	//
	// [1] = [SIGNER] authority
	//
	// [2] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewUpdateSvmChainSelectorInstructionBuilder creates a new `UpdateSvmChainSelector` instruction builder.
func NewUpdateSvmChainSelectorInstructionBuilder() *UpdateSvmChainSelector {
	nd := &UpdateSvmChainSelector{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetNewChainSelector sets the "newChainSelector" parameter.
func (inst *UpdateSvmChainSelector) SetNewChainSelector(newChainSelector uint64) *UpdateSvmChainSelector {
	inst.NewChainSelector = &newChainSelector
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *UpdateSvmChainSelector) SetConfigAccount(config ag_solanago.PublicKey) *UpdateSvmChainSelector {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config).WRITE()
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *UpdateSvmChainSelector) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *UpdateSvmChainSelector) SetAuthorityAccount(authority ag_solanago.PublicKey) *UpdateSvmChainSelector {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *UpdateSvmChainSelector) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *UpdateSvmChainSelector) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *UpdateSvmChainSelector {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *UpdateSvmChainSelector) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst UpdateSvmChainSelector) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_UpdateSvmChainSelector,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst UpdateSvmChainSelector) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *UpdateSvmChainSelector) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.NewChainSelector == nil {
			return errors.New("NewChainSelector parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *UpdateSvmChainSelector) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("UpdateSvmChainSelector")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("NewChainSelector", *inst.NewChainSelector))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("       config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("    authority", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("systemProgram", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj UpdateSvmChainSelector) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `NewChainSelector` param:
	err = encoder.Encode(obj.NewChainSelector)
	if err != nil {
		return err
	}
	return nil
}
func (obj *UpdateSvmChainSelector) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `NewChainSelector`:
	err = decoder.Decode(&obj.NewChainSelector)
	if err != nil {
		return err
	}
	return nil
}

// NewUpdateSvmChainSelectorInstruction declares a new UpdateSvmChainSelector instruction with the provided parameters and accounts.
func NewUpdateSvmChainSelectorInstruction(
	// Parameters:
	newChainSelector uint64,
	// Accounts:
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *UpdateSvmChainSelector {
	return NewUpdateSvmChainSelectorInstructionBuilder().
		SetNewChainSelector(newChainSelector).
		SetConfigAccount(config).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
