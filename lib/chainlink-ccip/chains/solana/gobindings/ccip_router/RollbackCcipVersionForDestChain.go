// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Rolls back the CCIP version for a destination chain.
// This effectively just restores the old version's sequence number of the destination chain state.
// We only support 1 consecutive rollback. If a rollback has occurred for that lane, the version can't
// be rolled back again without bumping the version first.
//
// # Arguments
// * `ctx` - The context containing the accounts required for the rollback.
// * `dest_chain_selector` - The destination chain selector to rollback the version for.
type RollbackCcipVersionForDestChain struct {
	DestChainSelector *uint64

	// [0] = [WRITE] destChainState
	//
	// [1] = [] config
	//
	// [2] = [WRITE, SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewRollbackCcipVersionForDestChainInstructionBuilder creates a new `RollbackCcipVersionForDestChain` instruction builder.
func NewRollbackCcipVersionForDestChainInstructionBuilder() *RollbackCcipVersionForDestChain {
	nd := &RollbackCcipVersionForDestChain{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetDestChainSelector sets the "destChainSelector" parameter.
func (inst *RollbackCcipVersionForDestChain) SetDestChainSelector(destChainSelector uint64) *RollbackCcipVersionForDestChain {
	inst.DestChainSelector = &destChainSelector
	return inst
}

// SetDestChainStateAccount sets the "destChainState" account.
func (inst *RollbackCcipVersionForDestChain) SetDestChainStateAccount(destChainState ag_solanago.PublicKey) *RollbackCcipVersionForDestChain {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(destChainState).WRITE()
	return inst
}

// GetDestChainStateAccount gets the "destChainState" account.
func (inst *RollbackCcipVersionForDestChain) GetDestChainStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigAccount sets the "config" account.
func (inst *RollbackCcipVersionForDestChain) SetConfigAccount(config ag_solanago.PublicKey) *RollbackCcipVersionForDestChain {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *RollbackCcipVersionForDestChain) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *RollbackCcipVersionForDestChain) SetAuthorityAccount(authority ag_solanago.PublicKey) *RollbackCcipVersionForDestChain {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *RollbackCcipVersionForDestChain) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst RollbackCcipVersionForDestChain) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_RollbackCcipVersionForDestChain,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst RollbackCcipVersionForDestChain) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *RollbackCcipVersionForDestChain) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.DestChainSelector == nil {
			return errors.New("DestChainSelector parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.DestChainState is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *RollbackCcipVersionForDestChain) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("RollbackCcipVersionForDestChain")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("DestChainSelector", *inst.DestChainSelector))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("destChainState", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("        config", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("     authority", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj RollbackCcipVersionForDestChain) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `DestChainSelector` param:
	err = encoder.Encode(obj.DestChainSelector)
	if err != nil {
		return err
	}
	return nil
}
func (obj *RollbackCcipVersionForDestChain) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `DestChainSelector`:
	err = decoder.Decode(&obj.DestChainSelector)
	if err != nil {
		return err
	}
	return nil
}

// NewRollbackCcipVersionForDestChainInstruction declares a new RollbackCcipVersionForDestChain instruction with the provided parameters and accounts.
func NewRollbackCcipVersionForDestChainInstruction(
	// Parameters:
	destChainSelector uint64,
	// Accounts:
	destChainState ag_solanago.PublicKey,
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *RollbackCcipVersionForDestChain {
	return NewRollbackCcipVersionForDestChainInstructionBuilder().
		SetDestChainSelector(destChainSelector).
		SetDestChainStateAccount(destChainState).
		SetConfigAccount(config).
		SetAuthorityAccount(authority)
}
