// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package burnmint_token_pool

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Returns the program type (name) and version.
// Used by offchain code to easily determine which program & version is being interacted with.
//
// # Arguments
// * `ctx` - The context
type TypeVersion struct {

	// [0] = [] clock
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewTypeVersionInstructionBuilder creates a new `TypeVersion` instruction builder.
func NewTypeVersionInstructionBuilder() *TypeVersion {
	nd := &TypeVersion{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 1),
	}
	return nd
}

// SetClockAccount sets the "clock" account.
func (inst *TypeVersion) SetClockAccount(clock ag_solanago.PublicKey) *TypeVersion {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(clock)
	return inst
}

// GetClockAccount gets the "clock" account.
func (inst *TypeVersion) GetClockAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

func (inst TypeVersion) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_TypeVersion,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst TypeVersion) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *TypeVersion) Validate() error {
	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Clock is not set")
		}
	}
	return nil
}

func (inst *TypeVersion) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("TypeVersion")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=0]").ParentFunc(func(paramsBranch ag_treeout.Branches) {})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=1]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("clock", inst.AccountMetaSlice[0]))
					})
				})
		})
}

func (obj TypeVersion) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	return nil
}
func (obj *TypeVersion) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	return nil
}

// NewTypeVersionInstruction declares a new TypeVersion instruction with the provided parameters and accounts.
func NewTypeVersionInstruction(
	// Accounts:
	clock ag_solanago.PublicKey) *TypeVersion {
	return NewTypeVersionInstructionBuilder().
		SetClockAccount(clock)
}
