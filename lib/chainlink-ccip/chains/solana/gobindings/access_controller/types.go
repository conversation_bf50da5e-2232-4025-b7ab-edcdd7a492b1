// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package access_controller

import (
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
)

type AccessList struct {
	Xs  [64]ag_solanago.PublicKey
	Len uint64
}

func (obj AccessList) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Xs` param:
	err = encoder.Encode(obj.Xs)
	if err != nil {
		return err
	}
	// Serialize `Len` param:
	err = encoder.Encode(obj.Len)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AccessList) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Xs`:
	err = decoder.Decode(&obj.Xs)
	if err != nil {
		return err
	}
	// Deserialize `Len`:
	err = decoder.Decode(&obj.<PERSON>)
	if err != nil {
		return err
	}
	return nil
}
