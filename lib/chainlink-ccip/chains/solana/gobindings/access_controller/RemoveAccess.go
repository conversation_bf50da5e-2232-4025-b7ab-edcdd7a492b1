// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package access_controller

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// RemoveAccess is the `removeAccess` instruction.
type RemoveAccess struct {

	// [0] = [WRITE] state
	//
	// [1] = [SIGNER] owner
	//
	// [2] = [] address
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewRemoveAccessInstructionBuilder creates a new `RemoveAccess` instruction builder.
func NewRemoveAccessInstructionBuilder() *RemoveAccess {
	nd := &RemoveAccess{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetStateAccount sets the "state" account.
func (inst *RemoveAccess) SetStateAccount(state ag_solanago.PublicKey) *RemoveAccess {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(state).WRITE()
	return inst
}

// GetStateAccount gets the "state" account.
func (inst *RemoveAccess) GetStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetOwnerAccount sets the "owner" account.
func (inst *RemoveAccess) SetOwnerAccount(owner ag_solanago.PublicKey) *RemoveAccess {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(owner).SIGNER()
	return inst
}

// GetOwnerAccount gets the "owner" account.
func (inst *RemoveAccess) GetOwnerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAddressAccount sets the "address" account.
func (inst *RemoveAccess) SetAddressAccount(address ag_solanago.PublicKey) *RemoveAccess {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(address)
	return inst
}

// GetAddressAccount gets the "address" account.
func (inst *RemoveAccess) GetAddressAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst RemoveAccess) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_RemoveAccess,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst RemoveAccess) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *RemoveAccess) Validate() error {
	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.State is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Owner is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Address is not set")
		}
	}
	return nil
}

func (inst *RemoveAccess) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("RemoveAccess")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=0]").ParentFunc(func(paramsBranch ag_treeout.Branches) {})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("  state", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("  owner", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("address", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj RemoveAccess) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	return nil
}
func (obj *RemoveAccess) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	return nil
}

// NewRemoveAccessInstruction declares a new RemoveAccess instruction with the provided parameters and accounts.
func NewRemoveAccessInstruction(
	// Accounts:
	state ag_solanago.PublicKey,
	owner ag_solanago.PublicKey,
	address ag_solanago.PublicKey) *RemoveAccess {
	return NewRemoveAccessInstructionBuilder().
		SetStateAccount(state).
		SetOwnerAccount(owner).
		SetAddressAccount(address)
}
