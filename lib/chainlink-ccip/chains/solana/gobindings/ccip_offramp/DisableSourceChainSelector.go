// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_offramp

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Disables the source chain selector.
//
// The Admin is the only one able to disable the chain selector as source. This method is thought of as an emergency kill-switch.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for disabling the chain selector.
// * `source_chain_selector` - The source chain selector to be disabled.
type DisableSourceChainSelector struct {
	SourceChainSelector *uint64

	// [0] = [WRITE] sourceChain
	//
	// [1] = [] config
	//
	// [2] = [WRITE, SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewDisableSourceChainSelectorInstructionBuilder creates a new `DisableSourceChainSelector` instruction builder.
func NewDisableSourceChainSelectorInstructionBuilder() *DisableSourceChainSelector {
	nd := &DisableSourceChainSelector{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetSourceChainSelector sets the "sourceChainSelector" parameter.
func (inst *DisableSourceChainSelector) SetSourceChainSelector(sourceChainSelector uint64) *DisableSourceChainSelector {
	inst.SourceChainSelector = &sourceChainSelector
	return inst
}

// SetSourceChainAccount sets the "sourceChain" account.
func (inst *DisableSourceChainSelector) SetSourceChainAccount(sourceChain ag_solanago.PublicKey) *DisableSourceChainSelector {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(sourceChain).WRITE()
	return inst
}

// GetSourceChainAccount gets the "sourceChain" account.
func (inst *DisableSourceChainSelector) GetSourceChainAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigAccount sets the "config" account.
func (inst *DisableSourceChainSelector) SetConfigAccount(config ag_solanago.PublicKey) *DisableSourceChainSelector {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *DisableSourceChainSelector) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *DisableSourceChainSelector) SetAuthorityAccount(authority ag_solanago.PublicKey) *DisableSourceChainSelector {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *DisableSourceChainSelector) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst DisableSourceChainSelector) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_DisableSourceChainSelector,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst DisableSourceChainSelector) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *DisableSourceChainSelector) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.SourceChainSelector == nil {
			return errors.New("SourceChainSelector parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.SourceChain is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *DisableSourceChainSelector) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("DisableSourceChainSelector")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("SourceChainSelector", *inst.SourceChainSelector))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("sourceChain", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("     config", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("  authority", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj DisableSourceChainSelector) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `SourceChainSelector` param:
	err = encoder.Encode(obj.SourceChainSelector)
	if err != nil {
		return err
	}
	return nil
}
func (obj *DisableSourceChainSelector) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `SourceChainSelector`:
	err = decoder.Decode(&obj.SourceChainSelector)
	if err != nil {
		return err
	}
	return nil
}

// NewDisableSourceChainSelectorInstruction declares a new DisableSourceChainSelector instruction with the provided parameters and accounts.
func NewDisableSourceChainSelectorInstruction(
	// Parameters:
	sourceChainSelector uint64,
	// Accounts:
	sourceChain ag_solanago.PublicKey,
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *DisableSourceChainSelector {
	return NewDisableSourceChainSelectorInstructionBuilder().
		SetSourceChainSelector(sourceChainSelector).
		SetSourceChainAccount(sourceChain).
		SetConfigAccount(config).
		SetAuthorityAccount(authority)
}
