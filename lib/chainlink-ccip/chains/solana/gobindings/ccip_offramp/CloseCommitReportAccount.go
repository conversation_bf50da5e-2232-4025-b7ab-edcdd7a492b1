// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_offramp

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// CloseCommitReportAccount is the `closeCommitReportAccount` instruction.
type CloseCommitReportAccount struct {
	SourceChainSelector *uint64
	Root                *[]byte

	// [0] = [] config
	//
	// [1] = [WRITE] commitReport
	//
	// [2] = [] referenceAddresses
	//
	// [3] = [] wsolMint
	//
	// [4] = [WRITE] feeTokenReceiver
	//
	// [5] = [] feeBillingSigner
	//
	// [6] = [] tokenProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewCloseCommitReportAccountInstructionBuilder creates a new `CloseCommitReportAccount` instruction builder.
func NewCloseCommitReportAccountInstructionBuilder() *CloseCommitReportAccount {
	nd := &CloseCommitReportAccount{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 7),
	}
	return nd
}

// SetSourceChainSelector sets the "sourceChainSelector" parameter.
func (inst *CloseCommitReportAccount) SetSourceChainSelector(sourceChainSelector uint64) *CloseCommitReportAccount {
	inst.SourceChainSelector = &sourceChainSelector
	return inst
}

// SetRoot sets the "root" parameter.
func (inst *CloseCommitReportAccount) SetRoot(root []byte) *CloseCommitReportAccount {
	inst.Root = &root
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *CloseCommitReportAccount) SetConfigAccount(config ag_solanago.PublicKey) *CloseCommitReportAccount {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *CloseCommitReportAccount) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetCommitReportAccount sets the "commitReport" account.
func (inst *CloseCommitReportAccount) SetCommitReportAccount(commitReport ag_solanago.PublicKey) *CloseCommitReportAccount {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(commitReport).WRITE()
	return inst
}

// GetCommitReportAccount gets the "commitReport" account.
func (inst *CloseCommitReportAccount) GetCommitReportAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetReferenceAddressesAccount sets the "referenceAddresses" account.
func (inst *CloseCommitReportAccount) SetReferenceAddressesAccount(referenceAddresses ag_solanago.PublicKey) *CloseCommitReportAccount {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(referenceAddresses)
	return inst
}

// GetReferenceAddressesAccount gets the "referenceAddresses" account.
func (inst *CloseCommitReportAccount) GetReferenceAddressesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetWsolMintAccount sets the "wsolMint" account.
func (inst *CloseCommitReportAccount) SetWsolMintAccount(wsolMint ag_solanago.PublicKey) *CloseCommitReportAccount {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(wsolMint)
	return inst
}

// GetWsolMintAccount gets the "wsolMint" account.
func (inst *CloseCommitReportAccount) GetWsolMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetFeeTokenReceiverAccount sets the "feeTokenReceiver" account.
func (inst *CloseCommitReportAccount) SetFeeTokenReceiverAccount(feeTokenReceiver ag_solanago.PublicKey) *CloseCommitReportAccount {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(feeTokenReceiver).WRITE()
	return inst
}

// GetFeeTokenReceiverAccount gets the "feeTokenReceiver" account.
func (inst *CloseCommitReportAccount) GetFeeTokenReceiverAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetFeeBillingSignerAccount sets the "feeBillingSigner" account.
func (inst *CloseCommitReportAccount) SetFeeBillingSignerAccount(feeBillingSigner ag_solanago.PublicKey) *CloseCommitReportAccount {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(feeBillingSigner)
	return inst
}

// GetFeeBillingSignerAccount gets the "feeBillingSigner" account.
func (inst *CloseCommitReportAccount) GetFeeBillingSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetTokenProgramAccount sets the "tokenProgram" account.
func (inst *CloseCommitReportAccount) SetTokenProgramAccount(tokenProgram ag_solanago.PublicKey) *CloseCommitReportAccount {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(tokenProgram)
	return inst
}

// GetTokenProgramAccount gets the "tokenProgram" account.
func (inst *CloseCommitReportAccount) GetTokenProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

func (inst CloseCommitReportAccount) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_CloseCommitReportAccount,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst CloseCommitReportAccount) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *CloseCommitReportAccount) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.SourceChainSelector == nil {
			return errors.New("SourceChainSelector parameter is not set")
		}
		if inst.Root == nil {
			return errors.New("Root parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.CommitReport is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.ReferenceAddresses is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.WsolMint is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.FeeTokenReceiver is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.FeeBillingSigner is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.TokenProgram is not set")
		}
	}
	return nil
}

func (inst *CloseCommitReportAccount) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("CloseCommitReportAccount")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=2]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("SourceChainSelector", *inst.SourceChainSelector))
						paramsBranch.Child(ag_format.Param("               Root", *inst.Root))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=7]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("            config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("      commitReport", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("referenceAddresses", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("          wsolMint", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("  feeTokenReceiver", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("  feeBillingSigner", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("      tokenProgram", inst.AccountMetaSlice[6]))
					})
				})
		})
}

func (obj CloseCommitReportAccount) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `SourceChainSelector` param:
	err = encoder.Encode(obj.SourceChainSelector)
	if err != nil {
		return err
	}
	// Serialize `Root` param:
	err = encoder.Encode(obj.Root)
	if err != nil {
		return err
	}
	return nil
}
func (obj *CloseCommitReportAccount) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `SourceChainSelector`:
	err = decoder.Decode(&obj.SourceChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `Root`:
	err = decoder.Decode(&obj.Root)
	if err != nil {
		return err
	}
	return nil
}

// NewCloseCommitReportAccountInstruction declares a new CloseCommitReportAccount instruction with the provided parameters and accounts.
func NewCloseCommitReportAccountInstruction(
	// Parameters:
	sourceChainSelector uint64,
	root []byte,
	// Accounts:
	config ag_solanago.PublicKey,
	commitReport ag_solanago.PublicKey,
	referenceAddresses ag_solanago.PublicKey,
	wsolMint ag_solanago.PublicKey,
	feeTokenReceiver ag_solanago.PublicKey,
	feeBillingSigner ag_solanago.PublicKey,
	tokenProgram ag_solanago.PublicKey) *CloseCommitReportAccount {
	return NewCloseCommitReportAccountInstructionBuilder().
		SetSourceChainSelector(sourceChainSelector).
		SetRoot(root).
		SetConfigAccount(config).
		SetCommitReportAccount(commitReport).
		SetReferenceAddressesAccount(referenceAddresses).
		SetWsolMintAccount(wsolMint).
		SetFeeTokenReceiverAccount(feeTokenReceiver).
		SetFeeBillingSignerAccount(feeBillingSigner).
		SetTokenProgramAccount(tokenProgram)
}
