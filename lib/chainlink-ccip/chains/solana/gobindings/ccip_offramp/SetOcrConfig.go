// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_offramp

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Sets the OCR configuration.
// Only CCIP Admin can set the OCR configuration.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for setting the OCR configuration.
// * `plugin_type` - The type of OCR plugin [0: Commit, 1: Execution].
// * `config_info` - The OCR configuration information.
// * `signers` - The list of signers.
// * `transmitters` - The list of transmitters.
type SetOcrConfig struct {
	PluginType   *OcrPluginType
	ConfigInfo   *Ocr3ConfigInfo
	Signers      *[][20]uint8
	Transmitters *[]ag_solanago.PublicKey

	// [0] = [WRITE] config
	//
	// [1] = [WRITE] state
	//
	// [2] = [SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewSetOcrConfigInstructionBuilder creates a new `SetOcrConfig` instruction builder.
func NewSetOcrConfigInstructionBuilder() *SetOcrConfig {
	nd := &SetOcrConfig{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetPluginType sets the "pluginType" parameter.
func (inst *SetOcrConfig) SetPluginType(pluginType OcrPluginType) *SetOcrConfig {
	inst.PluginType = &pluginType
	return inst
}

// SetConfigInfo sets the "configInfo" parameter.
func (inst *SetOcrConfig) SetConfigInfo(configInfo Ocr3ConfigInfo) *SetOcrConfig {
	inst.ConfigInfo = &configInfo
	return inst
}

// SetSigners sets the "signers" parameter.
func (inst *SetOcrConfig) SetSigners(signers [][20]uint8) *SetOcrConfig {
	inst.Signers = &signers
	return inst
}

// SetTransmitters sets the "transmitters" parameter.
func (inst *SetOcrConfig) SetTransmitters(transmitters []ag_solanago.PublicKey) *SetOcrConfig {
	inst.Transmitters = &transmitters
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *SetOcrConfig) SetConfigAccount(config ag_solanago.PublicKey) *SetOcrConfig {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config).WRITE()
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *SetOcrConfig) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetStateAccount sets the "state" account.
func (inst *SetOcrConfig) SetStateAccount(state ag_solanago.PublicKey) *SetOcrConfig {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(state).WRITE()
	return inst
}

// GetStateAccount gets the "state" account.
func (inst *SetOcrConfig) GetStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *SetOcrConfig) SetAuthorityAccount(authority ag_solanago.PublicKey) *SetOcrConfig {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *SetOcrConfig) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst SetOcrConfig) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_SetOcrConfig,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst SetOcrConfig) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *SetOcrConfig) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.PluginType == nil {
			return errors.New("PluginType parameter is not set")
		}
		if inst.ConfigInfo == nil {
			return errors.New("ConfigInfo parameter is not set")
		}
		if inst.Signers == nil {
			return errors.New("Signers parameter is not set")
		}
		if inst.Transmitters == nil {
			return errors.New("Transmitters parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.State is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *SetOcrConfig) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("SetOcrConfig")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=4]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("  PluginType", *inst.PluginType))
						paramsBranch.Child(ag_format.Param("  ConfigInfo", *inst.ConfigInfo))
						paramsBranch.Child(ag_format.Param("     Signers", *inst.Signers))
						paramsBranch.Child(ag_format.Param("Transmitters", *inst.Transmitters))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("   config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("    state", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("authority", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj SetOcrConfig) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `PluginType` param:
	err = encoder.Encode(obj.PluginType)
	if err != nil {
		return err
	}
	// Serialize `ConfigInfo` param:
	err = encoder.Encode(obj.ConfigInfo)
	if err != nil {
		return err
	}
	// Serialize `Signers` param:
	err = encoder.Encode(obj.Signers)
	if err != nil {
		return err
	}
	// Serialize `Transmitters` param:
	err = encoder.Encode(obj.Transmitters)
	if err != nil {
		return err
	}
	return nil
}
func (obj *SetOcrConfig) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `PluginType`:
	err = decoder.Decode(&obj.PluginType)
	if err != nil {
		return err
	}
	// Deserialize `ConfigInfo`:
	err = decoder.Decode(&obj.ConfigInfo)
	if err != nil {
		return err
	}
	// Deserialize `Signers`:
	err = decoder.Decode(&obj.Signers)
	if err != nil {
		return err
	}
	// Deserialize `Transmitters`:
	err = decoder.Decode(&obj.Transmitters)
	if err != nil {
		return err
	}
	return nil
}

// NewSetOcrConfigInstruction declares a new SetOcrConfig instruction with the provided parameters and accounts.
func NewSetOcrConfigInstruction(
	// Parameters:
	pluginType OcrPluginType,
	configInfo Ocr3ConfigInfo,
	signers [][20]uint8,
	transmitters []ag_solanago.PublicKey,
	// Accounts:
	config ag_solanago.PublicKey,
	state ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *SetOcrConfig {
	return NewSetOcrConfigInstructionBuilder().
		SetPluginType(pluginType).
		SetConfigInfo(configInfo).
		SetSigners(signers).
		SetTransmitters(transmitters).
		SetConfigAccount(config).
		SetStateAccount(state).
		SetAuthorityAccount(authority)
}
