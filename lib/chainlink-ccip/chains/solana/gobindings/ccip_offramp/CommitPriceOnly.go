// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_offramp

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Commits a report to the router, with price updates only.
//
// The method name needs to be commit with Anchor encoding.
//
// This function is called by the OffChain when committing one Report to the SVM Router,
// containing only price updates and no merkle root.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for the commit.
// * `report_context_byte_words` - consists of:
// * report_context_byte_words[0]: ConfigDigest
// * report_context_byte_words[1]: 24 byte padding, 8 byte sequence number
// * `raw_report` - The serialized commit input report containing the price updates,
// with no merkle root.
// * `rs` - slice of R components of signatures
// * `ss` - slice of S components of signatures
// * `raw_vs` - array of V components of signatures
type CommitPriceOnly struct {
	ReportContextByteWords *[2][32]uint8
	RawReport              *[]byte
	Rs                     *[][32]uint8
	Ss                     *[][32]uint8
	RawVs                  *[32]uint8

	// [0] = [] config
	//
	// [1] = [] referenceAddresses
	//
	// [2] = [WRITE, SIGNER] authority
	//
	// [3] = [] systemProgram
	//
	// [4] = [] sysvarInstructions
	//
	// [5] = [] feeBillingSigner
	//
	// [6] = [] feeQuoter
	//
	// [7] = [] feeQuoterAllowedPriceUpdater
	// ··········· so that it can authorize the call made by this offramp
	//
	// [8] = [] feeQuoterConfig
	//
	// [9] = [] rmnRemote
	//
	// [10] = [] rmnRemoteCurses
	//
	// [11] = [] rmnRemoteConfig
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewCommitPriceOnlyInstructionBuilder creates a new `CommitPriceOnly` instruction builder.
func NewCommitPriceOnlyInstructionBuilder() *CommitPriceOnly {
	nd := &CommitPriceOnly{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 12),
	}
	return nd
}

// SetReportContextByteWords sets the "reportContextByteWords" parameter.
func (inst *CommitPriceOnly) SetReportContextByteWords(reportContextByteWords [2][32]uint8) *CommitPriceOnly {
	inst.ReportContextByteWords = &reportContextByteWords
	return inst
}

// SetRawReport sets the "rawReport" parameter.
func (inst *CommitPriceOnly) SetRawReport(rawReport []byte) *CommitPriceOnly {
	inst.RawReport = &rawReport
	return inst
}

// SetRs sets the "rs" parameter.
func (inst *CommitPriceOnly) SetRs(rs [][32]uint8) *CommitPriceOnly {
	inst.Rs = &rs
	return inst
}

// SetSs sets the "ss" parameter.
func (inst *CommitPriceOnly) SetSs(ss [][32]uint8) *CommitPriceOnly {
	inst.Ss = &ss
	return inst
}

// SetRawVs sets the "rawVs" parameter.
func (inst *CommitPriceOnly) SetRawVs(rawVs [32]uint8) *CommitPriceOnly {
	inst.RawVs = &rawVs
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *CommitPriceOnly) SetConfigAccount(config ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *CommitPriceOnly) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetReferenceAddressesAccount sets the "referenceAddresses" account.
func (inst *CommitPriceOnly) SetReferenceAddressesAccount(referenceAddresses ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(referenceAddresses)
	return inst
}

// GetReferenceAddressesAccount gets the "referenceAddresses" account.
func (inst *CommitPriceOnly) GetReferenceAddressesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *CommitPriceOnly) SetAuthorityAccount(authority ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *CommitPriceOnly) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *CommitPriceOnly) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *CommitPriceOnly) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetSysvarInstructionsAccount sets the "sysvarInstructions" account.
func (inst *CommitPriceOnly) SetSysvarInstructionsAccount(sysvarInstructions ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(sysvarInstructions)
	return inst
}

// GetSysvarInstructionsAccount gets the "sysvarInstructions" account.
func (inst *CommitPriceOnly) GetSysvarInstructionsAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetFeeBillingSignerAccount sets the "feeBillingSigner" account.
func (inst *CommitPriceOnly) SetFeeBillingSignerAccount(feeBillingSigner ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(feeBillingSigner)
	return inst
}

// GetFeeBillingSignerAccount gets the "feeBillingSigner" account.
func (inst *CommitPriceOnly) GetFeeBillingSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetFeeQuoterAccount sets the "feeQuoter" account.
func (inst *CommitPriceOnly) SetFeeQuoterAccount(feeQuoter ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(feeQuoter)
	return inst
}

// GetFeeQuoterAccount gets the "feeQuoter" account.
func (inst *CommitPriceOnly) GetFeeQuoterAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

// SetFeeQuoterAllowedPriceUpdaterAccount sets the "feeQuoterAllowedPriceUpdater" account.
// so that it can authorize the call made by this offramp
func (inst *CommitPriceOnly) SetFeeQuoterAllowedPriceUpdaterAccount(feeQuoterAllowedPriceUpdater ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[7] = ag_solanago.Meta(feeQuoterAllowedPriceUpdater)
	return inst
}

// GetFeeQuoterAllowedPriceUpdaterAccount gets the "feeQuoterAllowedPriceUpdater" account.
// so that it can authorize the call made by this offramp
func (inst *CommitPriceOnly) GetFeeQuoterAllowedPriceUpdaterAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[7]
}

// SetFeeQuoterConfigAccount sets the "feeQuoterConfig" account.
func (inst *CommitPriceOnly) SetFeeQuoterConfigAccount(feeQuoterConfig ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[8] = ag_solanago.Meta(feeQuoterConfig)
	return inst
}

// GetFeeQuoterConfigAccount gets the "feeQuoterConfig" account.
func (inst *CommitPriceOnly) GetFeeQuoterConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[8]
}

// SetRmnRemoteAccount sets the "rmnRemote" account.
func (inst *CommitPriceOnly) SetRmnRemoteAccount(rmnRemote ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[9] = ag_solanago.Meta(rmnRemote)
	return inst
}

// GetRmnRemoteAccount gets the "rmnRemote" account.
func (inst *CommitPriceOnly) GetRmnRemoteAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[9]
}

// SetRmnRemoteCursesAccount sets the "rmnRemoteCurses" account.
func (inst *CommitPriceOnly) SetRmnRemoteCursesAccount(rmnRemoteCurses ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[10] = ag_solanago.Meta(rmnRemoteCurses)
	return inst
}

// GetRmnRemoteCursesAccount gets the "rmnRemoteCurses" account.
func (inst *CommitPriceOnly) GetRmnRemoteCursesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[10]
}

// SetRmnRemoteConfigAccount sets the "rmnRemoteConfig" account.
func (inst *CommitPriceOnly) SetRmnRemoteConfigAccount(rmnRemoteConfig ag_solanago.PublicKey) *CommitPriceOnly {
	inst.AccountMetaSlice[11] = ag_solanago.Meta(rmnRemoteConfig)
	return inst
}

// GetRmnRemoteConfigAccount gets the "rmnRemoteConfig" account.
func (inst *CommitPriceOnly) GetRmnRemoteConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[11]
}

func (inst CommitPriceOnly) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_CommitPriceOnly,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst CommitPriceOnly) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *CommitPriceOnly) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.ReportContextByteWords == nil {
			return errors.New("ReportContextByteWords parameter is not set")
		}
		if inst.RawReport == nil {
			return errors.New("RawReport parameter is not set")
		}
		if inst.Rs == nil {
			return errors.New("Rs parameter is not set")
		}
		if inst.Ss == nil {
			return errors.New("Ss parameter is not set")
		}
		if inst.RawVs == nil {
			return errors.New("RawVs parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.ReferenceAddresses is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.SysvarInstructions is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.FeeBillingSigner is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.FeeQuoter is not set")
		}
		if inst.AccountMetaSlice[7] == nil {
			return errors.New("accounts.FeeQuoterAllowedPriceUpdater is not set")
		}
		if inst.AccountMetaSlice[8] == nil {
			return errors.New("accounts.FeeQuoterConfig is not set")
		}
		if inst.AccountMetaSlice[9] == nil {
			return errors.New("accounts.RmnRemote is not set")
		}
		if inst.AccountMetaSlice[10] == nil {
			return errors.New("accounts.RmnRemoteCurses is not set")
		}
		if inst.AccountMetaSlice[11] == nil {
			return errors.New("accounts.RmnRemoteConfig is not set")
		}
	}
	return nil
}

func (inst *CommitPriceOnly) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("CommitPriceOnly")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=5]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("ReportContextByteWords", *inst.ReportContextByteWords))
						paramsBranch.Child(ag_format.Param("             RawReport", *inst.RawReport))
						paramsBranch.Child(ag_format.Param("                    Rs", *inst.Rs))
						paramsBranch.Child(ag_format.Param("                    Ss", *inst.Ss))
						paramsBranch.Child(ag_format.Param("                 RawVs", *inst.RawVs))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=12]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("                      config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("          referenceAddresses", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("                   authority", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("               systemProgram", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("          sysvarInstructions", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("            feeBillingSigner", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("                   feeQuoter", inst.AccountMetaSlice[6]))
						accountsBranch.Child(ag_format.Meta("feeQuoterAllowedPriceUpdater", inst.AccountMetaSlice[7]))
						accountsBranch.Child(ag_format.Meta("             feeQuoterConfig", inst.AccountMetaSlice[8]))
						accountsBranch.Child(ag_format.Meta("                   rmnRemote", inst.AccountMetaSlice[9]))
						accountsBranch.Child(ag_format.Meta("             rmnRemoteCurses", inst.AccountMetaSlice[10]))
						accountsBranch.Child(ag_format.Meta("             rmnRemoteConfig", inst.AccountMetaSlice[11]))
					})
				})
		})
}

func (obj CommitPriceOnly) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `ReportContextByteWords` param:
	err = encoder.Encode(obj.ReportContextByteWords)
	if err != nil {
		return err
	}
	// Serialize `RawReport` param:
	err = encoder.Encode(obj.RawReport)
	if err != nil {
		return err
	}
	// Serialize `Rs` param:
	err = encoder.Encode(obj.Rs)
	if err != nil {
		return err
	}
	// Serialize `Ss` param:
	err = encoder.Encode(obj.Ss)
	if err != nil {
		return err
	}
	// Serialize `RawVs` param:
	err = encoder.Encode(obj.RawVs)
	if err != nil {
		return err
	}
	return nil
}
func (obj *CommitPriceOnly) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `ReportContextByteWords`:
	err = decoder.Decode(&obj.ReportContextByteWords)
	if err != nil {
		return err
	}
	// Deserialize `RawReport`:
	err = decoder.Decode(&obj.RawReport)
	if err != nil {
		return err
	}
	// Deserialize `Rs`:
	err = decoder.Decode(&obj.Rs)
	if err != nil {
		return err
	}
	// Deserialize `Ss`:
	err = decoder.Decode(&obj.Ss)
	if err != nil {
		return err
	}
	// Deserialize `RawVs`:
	err = decoder.Decode(&obj.RawVs)
	if err != nil {
		return err
	}
	return nil
}

// NewCommitPriceOnlyInstruction declares a new CommitPriceOnly instruction with the provided parameters and accounts.
func NewCommitPriceOnlyInstruction(
	// Parameters:
	reportContextByteWords [2][32]uint8,
	rawReport []byte,
	rs [][32]uint8,
	ss [][32]uint8,
	rawVs [32]uint8,
	// Accounts:
	config ag_solanago.PublicKey,
	referenceAddresses ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey,
	sysvarInstructions ag_solanago.PublicKey,
	feeBillingSigner ag_solanago.PublicKey,
	feeQuoter ag_solanago.PublicKey,
	feeQuoterAllowedPriceUpdater ag_solanago.PublicKey,
	feeQuoterConfig ag_solanago.PublicKey,
	rmnRemote ag_solanago.PublicKey,
	rmnRemoteCurses ag_solanago.PublicKey,
	rmnRemoteConfig ag_solanago.PublicKey) *CommitPriceOnly {
	return NewCommitPriceOnlyInstructionBuilder().
		SetReportContextByteWords(reportContextByteWords).
		SetRawReport(rawReport).
		SetRs(rs).
		SetSs(ss).
		SetRawVs(rawVs).
		SetConfigAccount(config).
		SetReferenceAddressesAccount(referenceAddresses).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram).
		SetSysvarInstructionsAccount(sysvarInstructions).
		SetFeeBillingSignerAccount(feeBillingSigner).
		SetFeeQuoterAccount(feeQuoter).
		SetFeeQuoterAllowedPriceUpdaterAccount(feeQuoterAllowedPriceUpdater).
		SetFeeQuoterConfigAccount(feeQuoterConfig).
		SetRmnRemoteAccount(rmnRemote).
		SetRmnRemoteCursesAccount(rmnRemoteCurses).
		SetRmnRemoteConfigAccount(rmnRemoteConfig)
}
