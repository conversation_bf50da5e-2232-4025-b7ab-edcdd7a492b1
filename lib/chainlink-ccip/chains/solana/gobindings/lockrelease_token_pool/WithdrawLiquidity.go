// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package lockrelease_token_pool

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// WithdrawLiquidity is the `withdrawLiquidity` instruction.
type WithdrawLiquidity struct {
	Amount *uint64

	// [0] = [] state
	//
	// [1] = [] tokenProgram
	//
	// [2] = [WRITE] mint
	//
	// [3] = [] poolSigner
	//
	// [4] = [WRITE] poolTokenAccount
	//
	// [5] = [WRITE] remoteTokenAccount
	//
	// [6] = [SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewWithdrawLiquidityInstructionBuilder creates a new `WithdrawLiquidity` instruction builder.
func NewWithdrawLiquidityInstructionBuilder() *WithdrawLiquidity {
	nd := &WithdrawLiquidity{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 7),
	}
	return nd
}

// SetAmount sets the "amount" parameter.
func (inst *WithdrawLiquidity) SetAmount(amount uint64) *WithdrawLiquidity {
	inst.Amount = &amount
	return inst
}

// SetStateAccount sets the "state" account.
func (inst *WithdrawLiquidity) SetStateAccount(state ag_solanago.PublicKey) *WithdrawLiquidity {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(state)
	return inst
}

// GetStateAccount gets the "state" account.
func (inst *WithdrawLiquidity) GetStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetTokenProgramAccount sets the "tokenProgram" account.
func (inst *WithdrawLiquidity) SetTokenProgramAccount(tokenProgram ag_solanago.PublicKey) *WithdrawLiquidity {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(tokenProgram)
	return inst
}

// GetTokenProgramAccount gets the "tokenProgram" account.
func (inst *WithdrawLiquidity) GetTokenProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetMintAccount sets the "mint" account.
func (inst *WithdrawLiquidity) SetMintAccount(mint ag_solanago.PublicKey) *WithdrawLiquidity {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(mint).WRITE()
	return inst
}

// GetMintAccount gets the "mint" account.
func (inst *WithdrawLiquidity) GetMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetPoolSignerAccount sets the "poolSigner" account.
func (inst *WithdrawLiquidity) SetPoolSignerAccount(poolSigner ag_solanago.PublicKey) *WithdrawLiquidity {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(poolSigner)
	return inst
}

// GetPoolSignerAccount gets the "poolSigner" account.
func (inst *WithdrawLiquidity) GetPoolSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetPoolTokenAccountAccount sets the "poolTokenAccount" account.
func (inst *WithdrawLiquidity) SetPoolTokenAccountAccount(poolTokenAccount ag_solanago.PublicKey) *WithdrawLiquidity {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(poolTokenAccount).WRITE()
	return inst
}

// GetPoolTokenAccountAccount gets the "poolTokenAccount" account.
func (inst *WithdrawLiquidity) GetPoolTokenAccountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetRemoteTokenAccountAccount sets the "remoteTokenAccount" account.
func (inst *WithdrawLiquidity) SetRemoteTokenAccountAccount(remoteTokenAccount ag_solanago.PublicKey) *WithdrawLiquidity {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(remoteTokenAccount).WRITE()
	return inst
}

// GetRemoteTokenAccountAccount gets the "remoteTokenAccount" account.
func (inst *WithdrawLiquidity) GetRemoteTokenAccountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *WithdrawLiquidity) SetAuthorityAccount(authority ag_solanago.PublicKey) *WithdrawLiquidity {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *WithdrawLiquidity) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

func (inst WithdrawLiquidity) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_WithdrawLiquidity,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst WithdrawLiquidity) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *WithdrawLiquidity) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.Amount == nil {
			return errors.New("Amount parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.State is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.TokenProgram is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Mint is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.PoolSigner is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.PoolTokenAccount is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.RemoteTokenAccount is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *WithdrawLiquidity) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("WithdrawLiquidity")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("Amount", *inst.Amount))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=7]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("       state", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("tokenProgram", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("        mint", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("  poolSigner", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("   poolToken", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta(" remoteToken", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("   authority", inst.AccountMetaSlice[6]))
					})
				})
		})
}

func (obj WithdrawLiquidity) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Amount` param:
	err = encoder.Encode(obj.Amount)
	if err != nil {
		return err
	}
	return nil
}
func (obj *WithdrawLiquidity) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Amount`:
	err = decoder.Decode(&obj.Amount)
	if err != nil {
		return err
	}
	return nil
}

// NewWithdrawLiquidityInstruction declares a new WithdrawLiquidity instruction with the provided parameters and accounts.
func NewWithdrawLiquidityInstruction(
	// Parameters:
	amount uint64,
	// Accounts:
	state ag_solanago.PublicKey,
	tokenProgram ag_solanago.PublicKey,
	mint ag_solanago.PublicKey,
	poolSigner ag_solanago.PublicKey,
	poolTokenAccount ag_solanago.PublicKey,
	remoteTokenAccount ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *WithdrawLiquidity {
	return NewWithdrawLiquidityInstructionBuilder().
		SetAmount(amount).
		SetStateAccount(state).
		SetTokenProgramAccount(tokenProgram).
		SetMintAccount(mint).
		SetPoolSignerAccount(poolSigner).
		SetPoolTokenAccountAccount(poolTokenAccount).
		SetRemoteTokenAccountAccount(remoteTokenAccount).
		SetAuthorityAccount(authority)
}
