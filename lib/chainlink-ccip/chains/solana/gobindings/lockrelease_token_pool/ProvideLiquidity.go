// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package lockrelease_token_pool

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// ProvideLiquidity is the `provideLiquidity` instruction.
type ProvideLiquidity struct {
	Amount *uint64

	// [0] = [] state
	//
	// [1] = [] tokenProgram
	//
	// [2] = [WRITE] mint
	//
	// [3] = [] poolSigner
	//
	// [4] = [WRITE] poolTokenAccount
	//
	// [5] = [WRITE] remoteTokenAccount
	//
	// [6] = [SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewProvideLiquidityInstructionBuilder creates a new `ProvideLiquidity` instruction builder.
func NewProvideLiquidityInstructionBuilder() *ProvideLiquidity {
	nd := &ProvideLiquidity{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 7),
	}
	return nd
}

// SetAmount sets the "amount" parameter.
func (inst *ProvideLiquidity) SetAmount(amount uint64) *ProvideLiquidity {
	inst.Amount = &amount
	return inst
}

// SetStateAccount sets the "state" account.
func (inst *ProvideLiquidity) SetStateAccount(state ag_solanago.PublicKey) *ProvideLiquidity {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(state)
	return inst
}

// GetStateAccount gets the "state" account.
func (inst *ProvideLiquidity) GetStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetTokenProgramAccount sets the "tokenProgram" account.
func (inst *ProvideLiquidity) SetTokenProgramAccount(tokenProgram ag_solanago.PublicKey) *ProvideLiquidity {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(tokenProgram)
	return inst
}

// GetTokenProgramAccount gets the "tokenProgram" account.
func (inst *ProvideLiquidity) GetTokenProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetMintAccount sets the "mint" account.
func (inst *ProvideLiquidity) SetMintAccount(mint ag_solanago.PublicKey) *ProvideLiquidity {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(mint).WRITE()
	return inst
}

// GetMintAccount gets the "mint" account.
func (inst *ProvideLiquidity) GetMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetPoolSignerAccount sets the "poolSigner" account.
func (inst *ProvideLiquidity) SetPoolSignerAccount(poolSigner ag_solanago.PublicKey) *ProvideLiquidity {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(poolSigner)
	return inst
}

// GetPoolSignerAccount gets the "poolSigner" account.
func (inst *ProvideLiquidity) GetPoolSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetPoolTokenAccountAccount sets the "poolTokenAccount" account.
func (inst *ProvideLiquidity) SetPoolTokenAccountAccount(poolTokenAccount ag_solanago.PublicKey) *ProvideLiquidity {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(poolTokenAccount).WRITE()
	return inst
}

// GetPoolTokenAccountAccount gets the "poolTokenAccount" account.
func (inst *ProvideLiquidity) GetPoolTokenAccountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetRemoteTokenAccountAccount sets the "remoteTokenAccount" account.
func (inst *ProvideLiquidity) SetRemoteTokenAccountAccount(remoteTokenAccount ag_solanago.PublicKey) *ProvideLiquidity {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(remoteTokenAccount).WRITE()
	return inst
}

// GetRemoteTokenAccountAccount gets the "remoteTokenAccount" account.
func (inst *ProvideLiquidity) GetRemoteTokenAccountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *ProvideLiquidity) SetAuthorityAccount(authority ag_solanago.PublicKey) *ProvideLiquidity {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *ProvideLiquidity) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

func (inst ProvideLiquidity) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_ProvideLiquidity,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst ProvideLiquidity) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *ProvideLiquidity) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.Amount == nil {
			return errors.New("Amount parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.State is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.TokenProgram is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Mint is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.PoolSigner is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.PoolTokenAccount is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.RemoteTokenAccount is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *ProvideLiquidity) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("ProvideLiquidity")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("Amount", *inst.Amount))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=7]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("       state", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("tokenProgram", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("        mint", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("  poolSigner", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("   poolToken", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta(" remoteToken", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("   authority", inst.AccountMetaSlice[6]))
					})
				})
		})
}

func (obj ProvideLiquidity) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Amount` param:
	err = encoder.Encode(obj.Amount)
	if err != nil {
		return err
	}
	return nil
}
func (obj *ProvideLiquidity) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Amount`:
	err = decoder.Decode(&obj.Amount)
	if err != nil {
		return err
	}
	return nil
}

// NewProvideLiquidityInstruction declares a new ProvideLiquidity instruction with the provided parameters and accounts.
func NewProvideLiquidityInstruction(
	// Parameters:
	amount uint64,
	// Accounts:
	state ag_solanago.PublicKey,
	tokenProgram ag_solanago.PublicKey,
	mint ag_solanago.PublicKey,
	poolSigner ag_solanago.PublicKey,
	poolTokenAccount ag_solanago.PublicKey,
	remoteTokenAccount ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *ProvideLiquidity {
	return NewProvideLiquidityInstructionBuilder().
		SetAmount(amount).
		SetStateAccount(state).
		SetTokenProgramAccount(tokenProgram).
		SetMintAccount(mint).
		SetPoolSignerAccount(poolSigner).
		SetPoolTokenAccountAccount(poolTokenAccount).
		SetRemoteTokenAccountAccount(remoteTokenAccount).
		SetAuthorityAccount(authority)
}
