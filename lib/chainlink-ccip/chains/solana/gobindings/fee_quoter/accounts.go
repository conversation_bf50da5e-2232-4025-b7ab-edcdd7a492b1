// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package fee_quoter

import (
	"fmt"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
)

type Config struct {
	Version                uint8
	Owner                  ag_solanago.PublicKey
	ProposedOwner          ag_solanago.PublicKey
	MaxFeeJuelsPerMsg      ag_binary.Uint128
	LinkTokenMint          ag_solanago.PublicKey
	LinkTokenLocalDecimals uint8
	Onramp                 ag_solanago.PublicKey
	DefaultCodeVersion     CodeVersion
}

var ConfigDiscriminator = [8]byte{155, 12, 170, 224, 30, 250, 204, 130}

func (obj Config) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(ConfigDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `Owner` param:
	err = encoder.Encode(obj.Owner)
	if err != nil {
		return err
	}
	// Serialize `ProposedOwner` param:
	err = encoder.Encode(obj.ProposedOwner)
	if err != nil {
		return err
	}
	// Serialize `MaxFeeJuelsPerMsg` param:
	err = encoder.Encode(obj.MaxFeeJuelsPerMsg)
	if err != nil {
		return err
	}
	// Serialize `LinkTokenMint` param:
	err = encoder.Encode(obj.LinkTokenMint)
	if err != nil {
		return err
	}
	// Serialize `LinkTokenLocalDecimals` param:
	err = encoder.Encode(obj.LinkTokenLocalDecimals)
	if err != nil {
		return err
	}
	// Serialize `Onramp` param:
	err = encoder.Encode(obj.Onramp)
	if err != nil {
		return err
	}
	// Serialize `DefaultCodeVersion` param:
	err = encoder.Encode(obj.DefaultCodeVersion)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(ConfigDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[155 12 170 224 30 250 204 130]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `Owner`:
	err = decoder.Decode(&obj.Owner)
	if err != nil {
		return err
	}
	// Deserialize `ProposedOwner`:
	err = decoder.Decode(&obj.ProposedOwner)
	if err != nil {
		return err
	}
	// Deserialize `MaxFeeJuelsPerMsg`:
	err = decoder.Decode(&obj.MaxFeeJuelsPerMsg)
	if err != nil {
		return err
	}
	// Deserialize `LinkTokenMint`:
	err = decoder.Decode(&obj.LinkTokenMint)
	if err != nil {
		return err
	}
	// Deserialize `LinkTokenLocalDecimals`:
	err = decoder.Decode(&obj.LinkTokenLocalDecimals)
	if err != nil {
		return err
	}
	// Deserialize `Onramp`:
	err = decoder.Decode(&obj.Onramp)
	if err != nil {
		return err
	}
	// Deserialize `DefaultCodeVersion`:
	err = decoder.Decode(&obj.DefaultCodeVersion)
	if err != nil {
		return err
	}
	return nil
}

type DestChain struct {
	Version       uint8
	ChainSelector uint64
	State         DestChainState
	Config        DestChainConfig
}

var DestChainDiscriminator = [8]byte{77, 18, 241, 132, 212, 54, 218, 16}

func (obj DestChain) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(DestChainDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `ChainSelector` param:
	err = encoder.Encode(obj.ChainSelector)
	if err != nil {
		return err
	}
	// Serialize `State` param:
	err = encoder.Encode(obj.State)
	if err != nil {
		return err
	}
	// Serialize `Config` param:
	err = encoder.Encode(obj.Config)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DestChain) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(DestChainDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[77 18 241 132 212 54 218 16]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `ChainSelector`:
	err = decoder.Decode(&obj.ChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `State`:
	err = decoder.Decode(&obj.State)
	if err != nil {
		return err
	}
	// Deserialize `Config`:
	err = decoder.Decode(&obj.Config)
	if err != nil {
		return err
	}
	return nil
}

type BillingTokenConfigWrapper struct {
	Version uint8
	Config  BillingTokenConfig
}

var BillingTokenConfigWrapperDiscriminator = [8]byte{63, 178, 72, 57, 171, 66, 44, 151}

func (obj BillingTokenConfigWrapper) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(BillingTokenConfigWrapperDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `Config` param:
	err = encoder.Encode(obj.Config)
	if err != nil {
		return err
	}
	return nil
}

func (obj *BillingTokenConfigWrapper) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(BillingTokenConfigWrapperDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[63 178 72 57 171 66 44 151]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `Config`:
	err = decoder.Decode(&obj.Config)
	if err != nil {
		return err
	}
	return nil
}

type PerChainPerTokenConfig struct {
	Version             uint8
	ChainSelector       uint64
	Mint                ag_solanago.PublicKey
	TokenTransferConfig TokenTransferFeeConfig
}

var PerChainPerTokenConfigDiscriminator = [8]byte{183, 88, 20, 99, 246, 46, 51, 230}

func (obj PerChainPerTokenConfig) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(PerChainPerTokenConfigDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `ChainSelector` param:
	err = encoder.Encode(obj.ChainSelector)
	if err != nil {
		return err
	}
	// Serialize `Mint` param:
	err = encoder.Encode(obj.Mint)
	if err != nil {
		return err
	}
	// Serialize `TokenTransferConfig` param:
	err = encoder.Encode(obj.TokenTransferConfig)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PerChainPerTokenConfig) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(PerChainPerTokenConfigDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[183 88 20 99 246 46 51 230]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `ChainSelector`:
	err = decoder.Decode(&obj.ChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `Mint`:
	err = decoder.Decode(&obj.Mint)
	if err != nil {
		return err
	}
	// Deserialize `TokenTransferConfig`:
	err = decoder.Decode(&obj.TokenTransferConfig)
	if err != nil {
		return err
	}
	return nil
}

type AllowedPriceUpdater struct{}

var AllowedPriceUpdaterDiscriminator = [8]byte{51, 136, 222, 161, 38, 7, 184, 190}

func (obj AllowedPriceUpdater) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(AllowedPriceUpdaterDiscriminator[:], false)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AllowedPriceUpdater) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(AllowedPriceUpdaterDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[51 136 222 161 38 7 184 190]",
				fmt.Sprint(discriminator[:]))
		}
	}
	return nil
}
