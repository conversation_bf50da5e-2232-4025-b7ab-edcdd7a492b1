// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package fee_quoter

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Sets the max_fee_juels_per_msg, which is an upper bound on how much can be billed for any message.
// (1 juels = 1e-18 LINK)
//
// Only the admin may set this.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for updating the configuration.
// * `max_fee_juels_per_msg` - The new value for the max_feel_juels_per_msg config.
type SetMaxFeeJuelsPerMsg struct {
	MaxFeeJuelsPerMsg *ag_binary.Uint128

	// [0] = [WRITE] config
	//
	// [1] = [SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewSetMaxFeeJuelsPerMsgInstructionBuilder creates a new `SetMaxFeeJuelsPerMsg` instruction builder.
func NewSetMaxFeeJuelsPerMsgInstructionBuilder() *SetMaxFeeJuelsPerMsg {
	nd := &SetMaxFeeJuelsPerMsg{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 2),
	}
	return nd
}

// SetMaxFeeJuelsPerMsg sets the "maxFeeJuelsPerMsg" parameter.
func (inst *SetMaxFeeJuelsPerMsg) SetMaxFeeJuelsPerMsg(maxFeeJuelsPerMsg ag_binary.Uint128) *SetMaxFeeJuelsPerMsg {
	inst.MaxFeeJuelsPerMsg = &maxFeeJuelsPerMsg
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *SetMaxFeeJuelsPerMsg) SetConfigAccount(config ag_solanago.PublicKey) *SetMaxFeeJuelsPerMsg {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config).WRITE()
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *SetMaxFeeJuelsPerMsg) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *SetMaxFeeJuelsPerMsg) SetAuthorityAccount(authority ag_solanago.PublicKey) *SetMaxFeeJuelsPerMsg {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *SetMaxFeeJuelsPerMsg) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

func (inst SetMaxFeeJuelsPerMsg) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_SetMaxFeeJuelsPerMsg,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst SetMaxFeeJuelsPerMsg) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *SetMaxFeeJuelsPerMsg) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.MaxFeeJuelsPerMsg == nil {
			return errors.New("MaxFeeJuelsPerMsg parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *SetMaxFeeJuelsPerMsg) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("SetMaxFeeJuelsPerMsg")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("MaxFeeJuelsPerMsg", *inst.MaxFeeJuelsPerMsg))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=2]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("   config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("authority", inst.AccountMetaSlice[1]))
					})
				})
		})
}

func (obj SetMaxFeeJuelsPerMsg) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `MaxFeeJuelsPerMsg` param:
	err = encoder.Encode(obj.MaxFeeJuelsPerMsg)
	if err != nil {
		return err
	}
	return nil
}
func (obj *SetMaxFeeJuelsPerMsg) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `MaxFeeJuelsPerMsg`:
	err = decoder.Decode(&obj.MaxFeeJuelsPerMsg)
	if err != nil {
		return err
	}
	return nil
}

// NewSetMaxFeeJuelsPerMsgInstruction declares a new SetMaxFeeJuelsPerMsg instruction with the provided parameters and accounts.
func NewSetMaxFeeJuelsPerMsgInstruction(
	// Parameters:
	maxFeeJuelsPerMsg ag_binary.Uint128,
	// Accounts:
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *SetMaxFeeJuelsPerMsg {
	return NewSetMaxFeeJuelsPerMsgInstructionBuilder().
		SetMaxFeeJuelsPerMsg(maxFeeJuelsPerMsg).
		SetConfigAccount(config).
		SetAuthorityAccount(authority)
}
