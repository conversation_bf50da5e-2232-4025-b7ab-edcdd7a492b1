// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package fee_quoter

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Transfers the ownership of the fee quoter to a new proposed owner.
//
// # Shared func signature with other programs
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for the transfer.
// * `proposed_owner` - The public key of the new proposed owner.
type TransferOwnership struct {
	NewOwner *ag_solanago.PublicKey

	// [0] = [WRITE] config
	//
	// [1] = [SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewTransferOwnershipInstructionBuilder creates a new `TransferOwnership` instruction builder.
func NewTransferOwnershipInstructionBuilder() *TransferOwnership {
	nd := &TransferOwnership{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 2),
	}
	return nd
}

// SetNewOwner sets the "newOwner" parameter.
func (inst *TransferOwnership) SetNewOwner(newOwner ag_solanago.PublicKey) *TransferOwnership {
	inst.NewOwner = &newOwner
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *TransferOwnership) SetConfigAccount(config ag_solanago.PublicKey) *TransferOwnership {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config).WRITE()
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *TransferOwnership) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *TransferOwnership) SetAuthorityAccount(authority ag_solanago.PublicKey) *TransferOwnership {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *TransferOwnership) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

func (inst TransferOwnership) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_TransferOwnership,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst TransferOwnership) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *TransferOwnership) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.NewOwner == nil {
			return errors.New("NewOwner parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *TransferOwnership) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("TransferOwnership")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("NewOwner", *inst.NewOwner))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=2]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("   config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("authority", inst.AccountMetaSlice[1]))
					})
				})
		})
}

func (obj TransferOwnership) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `NewOwner` param:
	err = encoder.Encode(obj.NewOwner)
	if err != nil {
		return err
	}
	return nil
}
func (obj *TransferOwnership) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `NewOwner`:
	err = decoder.Decode(&obj.NewOwner)
	if err != nil {
		return err
	}
	return nil
}

// NewTransferOwnershipInstruction declares a new TransferOwnership instruction with the provided parameters and accounts.
func NewTransferOwnershipInstruction(
	// Parameters:
	newOwner ag_solanago.PublicKey,
	// Accounts:
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *TransferOwnership {
	return NewTransferOwnershipInstructionBuilder().
		SetNewOwner(newOwner).
		SetConfigAccount(config).
		SetAuthorityAccount(authority)
}
