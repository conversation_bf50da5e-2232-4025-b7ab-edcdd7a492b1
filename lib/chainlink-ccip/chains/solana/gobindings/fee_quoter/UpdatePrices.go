// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package fee_quoter

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Updates prices for tokens and gas. This method may only be called by an allowed price updater.
//
// # Arguments
//
// * `ctx` - The context containing the accounts always required for the price updates
// * `token_updates` - Vector of token price updates
// * `gas_updates` - Vector of gas price updates
//
// # Additional accounts
//
// In addition to the fixed amount of accounts defined in the `UpdatePrices` context,
// the following accounts must be provided:
//
// * First, the billing token config accounts for each token whose price is being updated, in the same order
// as the token_updates vector.
// * Then, the dest chain accounts of every chain whose gas price is being updated, in the same order as the
// gas_updates vector.
type UpdatePrices struct {
	TokenUpdates *[]TokenPriceUpdate
	GasUpdates   *[]GasPriceUpdate

	// [0] = [SIGNER] authority
	//
	// [1] = [] allowedPriceUpdater
	// ··········· was added by the owner as an allowed price updater. The constraints enforced guarantee that it is the right PDA
	// ··········· and that it was initialized.
	//
	// [2] = [] config
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewUpdatePricesInstructionBuilder creates a new `UpdatePrices` instruction builder.
func NewUpdatePricesInstructionBuilder() *UpdatePrices {
	nd := &UpdatePrices{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetTokenUpdates sets the "tokenUpdates" parameter.
func (inst *UpdatePrices) SetTokenUpdates(tokenUpdates []TokenPriceUpdate) *UpdatePrices {
	inst.TokenUpdates = &tokenUpdates
	return inst
}

// SetGasUpdates sets the "gasUpdates" parameter.
func (inst *UpdatePrices) SetGasUpdates(gasUpdates []GasPriceUpdate) *UpdatePrices {
	inst.GasUpdates = &gasUpdates
	return inst
}

// SetAuthorityAccount sets the "authority" account.
func (inst *UpdatePrices) SetAuthorityAccount(authority ag_solanago.PublicKey) *UpdatePrices {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *UpdatePrices) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAllowedPriceUpdaterAccount sets the "allowedPriceUpdater" account.
// was added by the owner as an allowed price updater. The constraints enforced guarantee that it is the right PDA
// and that it was initialized.
func (inst *UpdatePrices) SetAllowedPriceUpdaterAccount(allowedPriceUpdater ag_solanago.PublicKey) *UpdatePrices {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(allowedPriceUpdater)
	return inst
}

// GetAllowedPriceUpdaterAccount gets the "allowedPriceUpdater" account.
// was added by the owner as an allowed price updater. The constraints enforced guarantee that it is the right PDA
// and that it was initialized.
func (inst *UpdatePrices) GetAllowedPriceUpdaterAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetConfigAccount sets the "config" account.
func (inst *UpdatePrices) SetConfigAccount(config ag_solanago.PublicKey) *UpdatePrices {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *UpdatePrices) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst UpdatePrices) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_UpdatePrices,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst UpdatePrices) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *UpdatePrices) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.TokenUpdates == nil {
			return errors.New("TokenUpdates parameter is not set")
		}
		if inst.GasUpdates == nil {
			return errors.New("GasUpdates parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.AllowedPriceUpdater is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Config is not set")
		}
	}
	return nil
}

func (inst *UpdatePrices) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("UpdatePrices")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=2]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("TokenUpdates", *inst.TokenUpdates))
						paramsBranch.Child(ag_format.Param("  GasUpdates", *inst.GasUpdates))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("          authority", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("allowedPriceUpdater", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("             config", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj UpdatePrices) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `TokenUpdates` param:
	err = encoder.Encode(obj.TokenUpdates)
	if err != nil {
		return err
	}
	// Serialize `GasUpdates` param:
	err = encoder.Encode(obj.GasUpdates)
	if err != nil {
		return err
	}
	return nil
}
func (obj *UpdatePrices) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `TokenUpdates`:
	err = decoder.Decode(&obj.TokenUpdates)
	if err != nil {
		return err
	}
	// Deserialize `GasUpdates`:
	err = decoder.Decode(&obj.GasUpdates)
	if err != nil {
		return err
	}
	return nil
}

// NewUpdatePricesInstruction declares a new UpdatePrices instruction with the provided parameters and accounts.
func NewUpdatePricesInstruction(
	// Parameters:
	tokenUpdates []TokenPriceUpdate,
	gasUpdates []GasPriceUpdate,
	// Accounts:
	authority ag_solanago.PublicKey,
	allowedPriceUpdater ag_solanago.PublicKey,
	config ag_solanago.PublicKey) *UpdatePrices {
	return NewUpdatePricesInstructionBuilder().
		SetTokenUpdates(tokenUpdates).
		SetGasUpdates(gasUpdates).
		SetAuthorityAccount(authority).
		SetAllowedPriceUpdaterAccount(allowedPriceUpdater).
		SetConfigAccount(config)
}
