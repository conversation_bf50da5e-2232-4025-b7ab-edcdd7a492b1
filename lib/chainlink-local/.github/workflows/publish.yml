name: Manual NPM Publish

on:
  workflow_dispatch:

jobs:
  publish:
    runs-on: ubuntu-latest
    environment: publish

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20.x"
          registry-url: "https://registry.npmjs.org"
          always-auth: true

      - name: Setup npm authentication
        run: echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_CHAINLINK_LOCAL }}" > ~/.npmrc

      - name: Install dependencies
        run: npm ci

      - name: Publish
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_CHAINLINK_LOCAL }}
