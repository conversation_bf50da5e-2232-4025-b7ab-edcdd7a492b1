# Solidity API

## CCIPLocalSimulator

This contract simulates local CCIP (Cross-Chain Interoperability Protocol)
operations for testing and development purposes.

_This contract includes methods to manage supported tokens and configurations
for local simulations._

### CHAIN_SELECTOR

```solidity
uint64 CHAIN_SELECTOR
```

The unique CCIP Chain Selector constant

### i_wrappedNative

```solidity
contract WETH9 i_wrappedNative
```

The wrapped native token instance

### i_linkToken

```solidity
contract LinkToken i_linkToken
```

The LINK token instance

### i_ccipBnM

```solidity
contract BurnMintERC677Helper i_ccipBnM
```

The BurnMintERC677Helper instance for CCIP-BnM token

### i_ccipLnM

```solidity
contract BurnMintERC677Helper i_ccipLnM
```

The BurnMintERC677Helper instance for CCIP-LnM token

### i_mockRouter

```solidity
contract MockCCIPRouter i_mockRouter
```

The mock CCIP router instance

### s_supportedTokens

```solidity
address[] s_supportedTokens
```

The list of supported token addresses

### CCIPLocalSimulator\_\_MsgSenderIsNotTokenOwner

```solidity
error CCIPLocalSimulator__MsgSenderIsNotTokenOwner()
```

### CCIPLocalSimulator\_\_RequiredRoleNotFound

```solidity
error CCIPLocalSimulator__RequiredRoleNotFound(address account, bytes32 role, address token)
```

### constructor

```solidity
constructor() public
```

Constructor to initialize the contract and pre-deployed token instances

### supportNewTokenViaOwner

```solidity
function supportNewTokenViaOwner(address tokenAddress) external
```

Allows user to support any new token, besides CCIP BnM and CCIP LnM, for
cross-chain transfers. Reverts if token does not implement owner() function.
Reverts if the caller is not the token owner.

#### Parameters

| Name         | Type    | Description                                                        |
| ------------ | ------- | ------------------------------------------------------------------ |
| tokenAddress | address | - The address of the token to add to the list of supported tokens. |

### supportNewTokenViaGetCCIPAdmin

```solidity
function supportNewTokenViaGetCCIPAdmin(address tokenAddress) external
```

Allows user to support any new token, besides CCIP BnM and CCIP LnM, for
cross-chain transfers. Reverts if token does not implement getCCIPAdmin()
function. Reverts if the caller is not the token CCIPAdmin.

#### Parameters

| Name         | Type    | Description                                                        |
| ------------ | ------- | ------------------------------------------------------------------ |
| tokenAddress | address | - The address of the token to add to the list of supported tokens. |

### supportNewTokenViaAccessControlDefaultAdmin

```solidity
function supportNewTokenViaAccessControlDefaultAdmin(address tokenAddress) external
```

Allows user to support any new token, besides CCIP BnM and CCIP LnM, for
cross-chain transfers. The caller must have the DEFAULT_ADMIN_ROLE as defined by
the contract itself. Reverts if the caller is not the admin of the token using
OZ's AccessControl DEFAULT_ADMIN_ROLE.

#### Parameters

| Name         | Type    | Description                                                        |
| ------------ | ------- | ------------------------------------------------------------------ |
| tokenAddress | address | - The address of the token to add to the list of supported tokens. |

### isChainSupported

```solidity
function isChainSupported(uint64 chainSelector) public pure returns (bool supported)
```

Checks whether the provided `chainSelector` is supported by the simulator.

#### Parameters

| Name          | Type   | Description                       |
| ------------- | ------ | --------------------------------- |
| chainSelector | uint64 | - The unique CCIP Chain Selector. |

#### Return Values

| Name      | Type | Description                                                      |
| --------- | ---- | ---------------------------------------------------------------- |
| supported | bool | - Returns true if `chainSelector` is supported by the simulator. |

### getSupportedTokens

```solidity
function getSupportedTokens(uint64 chainSelector) external view returns (address[] tokens)
```

Gets a list of token addresses that are supported for cross-chain transfers by
the simulator.

#### Parameters

| Name          | Type   | Description                       |
| ------------- | ------ | --------------------------------- |
| chainSelector | uint64 | - The unique CCIP Chain Selector. |

#### Return Values

| Name   | Type      | Description                                                                                        |
| ------ | --------- | -------------------------------------------------------------------------------------------------- |
| tokens | address[] | - Returns a list of token addresses that are supported for cross-chain transfers by the simulator. |

### requestLinkFromFaucet

```solidity
function requestLinkFromFaucet(address to, uint256 amount) external returns (bool success)
```

Requests LINK tokens from the faucet. The provided amount of tokens are
transferred to provided destination address.

#### Parameters

| Name   | Type    | Description                                        |
| ------ | ------- | -------------------------------------------------- |
| to     | address | - The address to which LINK tokens are to be sent. |
| amount | uint256 | - The amount of LINK tokens to send.               |

#### Return Values

| Name    | Type | Description                                                                   |
| ------- | ---- | ----------------------------------------------------------------------------- |
| success | bool | - Returns `true` if the transfer of tokens was successful, otherwise `false`. |

### configuration

```solidity
function configuration() public view returns (uint64 chainSelector_, contract IRouterClient sourceRouter_, contract IRouterClient destinationRouter_, contract WETH9 wrappedNative_, contract LinkToken linkToken_, contract BurnMintERC677Helper ccipBnM_, contract BurnMintERC677Helper ccipLnM_)
```

Returns configuration details for pre-deployed contracts and services needed for
local CCIP simulations.

#### Return Values

| Name                | Type                          | Description                                                 |
| ------------------- | ----------------------------- | ----------------------------------------------------------- |
| chainSelector\_     | uint64                        | - The unique CCIP Chain Selector.                           |
| sourceRouter\_      | contract IRouterClient        | - The source chain Router contract.                         |
| destinationRouter\_ | contract IRouterClient        | - The destination chain Router contract.                    |
| wrappedNative\_     | contract WETH9                | - The wrapped native token which can be used for CCIP fees. |
| linkToken\_         | contract LinkToken            | - The LINK token.                                           |
| ccipBnM\_           | contract BurnMintERC677Helper | - The ccipBnM token.                                        |
| ccipLnM\_           | contract BurnMintERC677Helper | - The ccipLnM token.                                        |
