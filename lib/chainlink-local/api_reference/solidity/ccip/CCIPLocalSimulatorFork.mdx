# Solidity API

## IRouterFork

### OffRamp

Structure representing an offRamp configuration

#### Parameters

| Name | Type | Description |
| ---- | ---- | ----------- |

```solidity
struct OffRamp {
  uint64 sourceChainSelector;
  address offRamp;
}
```

### getOffRamps

```solidity
function getOffRamps() external view returns (struct IRouterFork.OffRamp[])
```

Gets the list of offRamps

#### Return Values

| Name | Type                         | Description                         |
| ---- | ---------------------------- | ----------------------------------- |
| [0]  | struct IRouterFork.OffRamp[] | offRamps - Array of OffRamp structs |

## IEVM2EVMOffRampFork

### executeSingleMessage

```solidity
function executeSingleMessage(struct Internal.EVM2EVMMessage message, bytes[] offchainTokenData, uint32[] tokenGasOverrides) external
```

Executes a single CCIP message on the offRamp

#### Parameters

| Name              | Type                           | Description                       |
| ----------------- | ------------------------------ | --------------------------------- |
| message           | struct Internal.EVM2EVMMessage | - The CCIP message to be executed |
| offchainTokenData | bytes[]                        | - Additional offchain token data  |
| tokenGasOverrides | uint32[]                       |                                   |

## CCIPLocalSimulatorFork

Works with Foundry only

### CCIPSendRequested

```solidity
event CCIPSendRequested(struct Internal.EVM2EVMMessage message)
```

Event emitted when a CCIP send request is made

#### Parameters

| Name    | Type                           | Description                         |
| ------- | ------------------------------ | ----------------------------------- |
| message | struct Internal.EVM2EVMMessage | - The EVM2EVM message that was sent |

### i_register

```solidity
contract Register i_register
```

The immutable register instance

### LINK_FAUCET

```solidity
address LINK_FAUCET
```

The address of the LINK faucet

### s_processedMessages

```solidity
mapping(bytes32 => bool) s_processedMessages
```

Mapping to track processed messages

### constructor

```solidity
constructor() public
```

Constructor to initialize the contract

### switchChainAndRouteMessage

```solidity
function switchChainAndRouteMessage(uint256 forkId) external
```

To be called after the sending of the cross-chain message (`ccipSend`). Goes
through the list of past logs and looks for the `CCIPSendRequested` event.
Switches to a destination network fork. Routes the sent cross-chain message on
the destination network.

#### Parameters

| Name   | Type    | Description                                                                                                    |
| ------ | ------- | -------------------------------------------------------------------------------------------------------------- |
| forkId | uint256 | - The ID of the destination network fork. This is the returned value of `createFork()` or `createSelectFork()` |

### getNetworkDetails

```solidity
function getNetworkDetails(uint256 chainId) external view returns (struct Register.NetworkDetails)
```

Returns the default values for currently CCIP supported networks. If network is
not present or some of the values are changed, user can manually add new network
details using the `setNetworkDetails` function.

#### Parameters

| Name    | Type    | Description                                                                                            |
| ------- | ------- | ------------------------------------------------------------------------------------------------------ |
| chainId | uint256 | - The blockchain network chain ID. For example 11155111 for Ethereum Sepolia. Not CCIP chain selector. |

#### Return Values

| Name | Type                           | Description                                                                                                                                                                                                                                                                                                                                                                                         |
| ---- | ------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [0]  | struct Register.NetworkDetails | networkDetails - The tuple containing: chainSelector - The unique CCIP Chain Selector. routerAddress - The address of the CCIP Router contract. linkAddress - The address of the LINK token. wrappedNativeAddress - The address of the wrapped native token that can be used for CCIP fees. ccipBnMAddress - The address of the CCIP BnM token. ccipLnMAddress - The address of the CCIP LnM token. |

### setNetworkDetails

```solidity
function setNetworkDetails(uint256 chainId, struct Register.NetworkDetails networkDetails) external
```

If network details are not present or some of the values are changed, user can
manually add new network details using the `setNetworkDetails` function.

#### Parameters

| Name           | Type                           | Description                                                                                                                                                                                                                                                                                                                                                                          |
| -------------- | ------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| chainId        | uint256                        | - The blockchain network chain ID. For example 11155111 for Ethereum Sepolia. Not CCIP chain selector.                                                                                                                                                                                                                                                                               |
| networkDetails | struct Register.NetworkDetails | - The tuple containing: chainSelector - The unique CCIP Chain Selector. routerAddress - The address of the CCIP Router contract. linkAddress - The address of the LINK token. wrappedNativeAddress - The address of the wrapped native token that can be used for CCIP fees. ccipBnMAddress - The address of the CCIP BnM token. ccipLnMAddress - The address of the CCIP LnM token. |

### requestLinkFromFaucet

```solidity
function requestLinkFromFaucet(address to, uint256 amount) external returns (bool success)
```

Requests LINK tokens from the faucet. The provided amount of tokens are
transferred to provided destination address.

#### Parameters

| Name   | Type    | Description                                        |
| ------ | ------- | -------------------------------------------------- |
| to     | address | - The address to which LINK tokens are to be sent. |
| amount | uint256 | - The amount of LINK tokens to send.               |

#### Return Values

| Name    | Type | Description                                                                   |
| ------- | ---- | ----------------------------------------------------------------------------- |
| success | bool | - Returns `true` if the transfer of tokens was successful, otherwise `false`. |
