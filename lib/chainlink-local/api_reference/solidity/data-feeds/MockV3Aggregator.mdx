# Solidity API

## MockV3Aggregator

This contract is a mock implementation of the AggregatorV2V3Interface for
testing purposes.

_This contract interacts with a MockOffchainAggregator to simulate price feeds._

### version

```solidity
uint256 version
```

The version of the aggregator.

### aggregator

```solidity
address aggregator
```

The address of the current aggregator.

### proposedAggregator

```solidity
address proposedAggregator
```

The address of the proposed aggregator.

### constructor

```solidity
constructor(uint8 _decimals, int256 _initialAnswer) public
```

Constructor to initialize the MockV3Aggregator contract with initial parameters.

#### Parameters

| Name            | Type   | Description                                            |
| --------------- | ------ | ------------------------------------------------------ |
| \_decimals      | uint8  | - The number of decimals for the aggregator.           |
| \_initialAnswer | int256 | - The initial answer to be set in the mock aggregator. |

### decimals

```solidity
function decimals() external view returns (uint8)
```

Gets the number of decimals used by the aggregator.

#### Return Values

| Name | Type  | Description                     |
| ---- | ----- | ------------------------------- |
| [0]  | uint8 | uint8 - The number of decimals. |

### getAnswer

```solidity
function getAnswer(uint256 roundId) external view returns (int256)
```

Gets the answer for a specific round ID.

#### Parameters

| Name    | Type    | Description                           |
| ------- | ------- | ------------------------------------- |
| roundId | uint256 | - The round ID to get the answer for. |

#### Return Values

| Name | Type   | Description                                 |
| ---- | ------ | ------------------------------------------- |
| [0]  | int256 | int256 - The answer for the given round ID. |

### getRoundData

```solidity
function getRoundData(uint80 _roundId) external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)
```

Gets the round data for a specific round ID.

_This function should raise "No data present" if no data is available for the
given round ID._

#### Parameters

| Name      | Type   | Description                         |
| --------- | ------ | ----------------------------------- |
| \_roundId | uint80 | - The round ID to get the data for. |

#### Return Values

| Name            | Type    | Description                                      |
| --------------- | ------- | ------------------------------------------------ |
| roundId         | uint80  | - The round ID.                                  |
| answer          | int256  | - The answer for the round.                      |
| startedAt       | uint256 | - The timestamp when the round started.          |
| updatedAt       | uint256 | - The timestamp when the round was updated.      |
| answeredInRound | uint80  | - The round ID in which the answer was computed. |

### latestRoundData

```solidity
function latestRoundData() external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)
```

Gets the latest round data.

_This function should raise "No data present" if no data is available._

#### Return Values

| Name            | Type    | Description                                             |
| --------------- | ------- | ------------------------------------------------------- |
| roundId         | uint80  | - The latest round ID.                                  |
| answer          | int256  | - The latest answer.                                    |
| startedAt       | uint256 | - The timestamp when the latest round started.          |
| updatedAt       | uint256 | - The timestamp when the latest round was updated.      |
| answeredInRound | uint80  | - The round ID in which the latest answer was computed. |

### getTimestamp

```solidity
function getTimestamp(uint256 roundId) external view returns (uint256)
```

Gets the timestamp for a specific round ID.

#### Parameters

| Name    | Type    | Description                              |
| ------- | ------- | ---------------------------------------- |
| roundId | uint256 | - The round ID to get the timestamp for. |

#### Return Values

| Name | Type    | Description                                     |
| ---- | ------- | ----------------------------------------------- |
| [0]  | uint256 | uint256 - The timestamp for the given round ID. |

### latestAnswer

```solidity
function latestAnswer() external view returns (int256)
```

Gets the latest answer from the aggregator.

#### Return Values

| Name | Type   | Description                 |
| ---- | ------ | --------------------------- |
| [0]  | int256 | int256 - The latest answer. |

### latestTimestamp

```solidity
function latestTimestamp() external view returns (uint256)
```

Gets the timestamp of the latest answer from the aggregator.

#### Return Values

| Name | Type    | Description                                   |
| ---- | ------- | --------------------------------------------- |
| [0]  | uint256 | uint256 - The timestamp of the latest answer. |

### latestRound

```solidity
function latestRound() external view returns (uint256)
```

Gets the latest round ID from the aggregator.

#### Return Values

| Name | Type    | Description                    |
| ---- | ------- | ------------------------------ |
| [0]  | uint256 | uint256 - The latest round ID. |

### updateAnswer

```solidity
function updateAnswer(int256 _answer) public
```

Updates the answer in the mock aggregator.

#### Parameters

| Name     | Type   | Description                 |
| -------- | ------ | --------------------------- |
| \_answer | int256 | - The new answer to be set. |

### updateRoundData

```solidity
function updateRoundData(uint80 _roundId, int256 _answer, uint256 _timestamp, uint256 _startedAt) public
```

Updates the round data in the mock aggregator.

#### Parameters

| Name        | Type    | Description                             |
| ----------- | ------- | --------------------------------------- |
| \_roundId   | uint80  | - The round ID to be updated.           |
| \_answer    | int256  | - The new answer to be set.             |
| \_timestamp | uint256 | - The timestamp to be set.              |
| \_startedAt | uint256 | - The timestamp when the round started. |

### proposeAggregator

```solidity
function proposeAggregator(contract AggregatorV2V3Interface _aggregator) external
```

Proposes a new aggregator.

#### Parameters

| Name         | Type                             | Description                               |
| ------------ | -------------------------------- | ----------------------------------------- |
| \_aggregator | contract AggregatorV2V3Interface | - The address of the proposed aggregator. |

### confirmAggregator

```solidity
function confirmAggregator(address _aggregator) external
```

Confirms the proposed aggregator.

#### Parameters

| Name         | Type    | Description                               |
| ------------ | ------- | ----------------------------------------- |
| \_aggregator | address | - The address of the proposed aggregator. |

### description

```solidity
function description() external pure returns (string)
```

Gets the description of the aggregator.

#### Return Values

| Name | Type   | Description                                        |
| ---- | ------ | -------------------------------------------------- |
| [0]  | string | string memory - The description of the aggregator. |
