# Solidity API

## MockOffchainAggregator

This contract is a mock implementation of an offchain aggregator for testing
purposes.

_This contract simulates the behavior of an offchain aggregator and allows for
updating answers and round data._

### decimals

```solidity
uint8 decimals
```

The number of decimals used by the aggregator.

### latestAnswer

```solidity
int256 latestAnswer
```

The latest answer reported by the aggregator.

### latestTimestamp

```solidity
uint256 latestTimestamp
```

The timestamp of the latest answer.

### latestRound

```solidity
uint256 latestRound
```

The latest round ID.

### minAnswer

```solidity
int192 minAnswer
```

The minimum answer the aggregator is allowed to report.

### maxAnswer

```solidity
int192 maxAnswer
```

The maximum answer the aggregator is allowed to report.

### getAnswer

```solidity
mapping(uint256 => int256) getAnswer
```

Mapping to get the answer for a specific round ID.

### getTimestamp

```solidity
mapping(uint256 => uint256) getTimestamp
```

Mapping to get the timestamp for a specific round ID.

### constructor

```solidity
constructor(uint8 _decimals, int256 _initialAnswer) public
```

Constructor to initialize the MockOffchainAggregator contract with initial
parameters.

#### Parameters

| Name            | Type   | Description                                            |
| --------------- | ------ | ------------------------------------------------------ |
| \_decimals      | uint8  | - The number of decimals for the aggregator.           |
| \_initialAnswer | int256 | - The initial answer to be set in the mock aggregator. |

### updateAnswer

```solidity
function updateAnswer(int256 _answer) public
```

Updates the answer in the mock aggregator.

#### Parameters

| Name     | Type   | Description                 |
| -------- | ------ | --------------------------- |
| \_answer | int256 | - The new answer to be set. |

### updateRoundData

```solidity
function updateRoundData(uint80 _roundId, int256 _answer, uint256 _timestamp, uint256 _startedAt) public
```

Updates the round data in the mock aggregator.

#### Parameters

| Name        | Type    | Description                             |
| ----------- | ------- | --------------------------------------- |
| \_roundId   | uint80  | - The round ID to be updated.           |
| \_answer    | int256  | - The new answer to be set.             |
| \_timestamp | uint256 | - The timestamp to be set.              |
| \_startedAt | uint256 | - The timestamp when the round started. |

### getRoundData

```solidity
function getRoundData(uint80 _roundId) external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)
```

Gets the round data for a specific round ID.

#### Parameters

| Name      | Type   | Description                         |
| --------- | ------ | ----------------------------------- |
| \_roundId | uint80 | - The round ID to get the data for. |

#### Return Values

| Name            | Type    | Description                                      |
| --------------- | ------- | ------------------------------------------------ |
| roundId         | uint80  | - The round ID.                                  |
| answer          | int256  | - The answer for the round.                      |
| startedAt       | uint256 | - The timestamp when the round started.          |
| updatedAt       | uint256 | - The timestamp when the round was updated.      |
| answeredInRound | uint80  | - The round ID in which the answer was computed. |

### latestRoundData

```solidity
function latestRoundData() external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)
```

Gets the latest round data.

#### Return Values

| Name            | Type    | Description                                             |
| --------------- | ------- | ------------------------------------------------------- |
| roundId         | uint80  | - The latest round ID.                                  |
| answer          | int256  | - The latest answer.                                    |
| startedAt       | uint256 | - The timestamp when the latest round started.          |
| updatedAt       | uint256 | - The timestamp when the latest round was updated.      |
| answeredInRound | uint80  | - The round ID in which the latest answer was computed. |

### updateMinAndMaxAnswers

```solidity
function updateMinAndMaxAnswers(int192 _minAnswer, int192 _maxAnswer) external
```

Updates the minimum and maximum answers the aggregator can report.

#### Parameters

| Name        | Type   | Description               |
| ----------- | ------ | ------------------------- |
| \_minAnswer | int192 | - The new minimum answer. |
| \_maxAnswer | int192 | - The new maximum answer. |
