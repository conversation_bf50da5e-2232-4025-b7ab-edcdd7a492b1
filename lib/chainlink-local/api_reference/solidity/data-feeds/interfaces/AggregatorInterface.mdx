# Solidity API

## AggregatorInterface

Interface for accessing data from an aggregator contract.

_Provides methods to get the latest data and historical data for specific
rounds._

### latestAnswer

```solidity
function latestAnswer() external view returns (int256)
```

Gets the latest answer from the aggregator.

#### Return Values

| Name | Type   | Description                 |
| ---- | ------ | --------------------------- |
| [0]  | int256 | int256 - The latest answer. |

### latestTimestamp

```solidity
function latestTimestamp() external view returns (uint256)
```

Gets the timestamp of the latest answer from the aggregator.

#### Return Values

| Name | Type    | Description                                   |
| ---- | ------- | --------------------------------------------- |
| [0]  | uint256 | uint256 - The timestamp of the latest answer. |

### latestRound

```solidity
function latestRound() external view returns (uint256)
```

Gets the latest round ID from the aggregator.

#### Return Values

| Name | Type    | Description                    |
| ---- | ------- | ------------------------------ |
| [0]  | uint256 | uint256 - The latest round ID. |

### getAnswer

```solidity
function getAnswer(uint256 roundId) external view returns (int256)
```

Gets the answer for a specific round ID.

#### Parameters

| Name    | Type    | Description                           |
| ------- | ------- | ------------------------------------- |
| roundId | uint256 | - The round ID to get the answer for. |

#### Return Values

| Name | Type   | Description                                 |
| ---- | ------ | ------------------------------------------- |
| [0]  | int256 | int256 - The answer for the given round ID. |

### getTimestamp

```solidity
function getTimestamp(uint256 roundId) external view returns (uint256)
```

Gets the timestamp for a specific round ID.

#### Parameters

| Name    | Type    | Description                              |
| ------- | ------- | ---------------------------------------- |
| roundId | uint256 | - The round ID to get the timestamp for. |

#### Return Values

| Name | Type    | Description                                     |
| ---- | ------- | ----------------------------------------------- |
| [0]  | uint256 | uint256 - The timestamp for the given round ID. |

### AnswerUpdated

```solidity
event AnswerUpdated(int256 current, uint256 roundId, uint256 updatedAt)
```

Emitted when the answer is updated.

#### Parameters

| Name      | Type    | Description                                      |
| --------- | ------- | ------------------------------------------------ |
| current   | int256  | - The updated answer.                            |
| roundId   | uint256 | - The round ID for which the answer was updated. |
| updatedAt | uint256 | - The timestamp when the answer was updated.     |

### NewRound

```solidity
event NewRound(uint256 roundId, address startedBy, uint256 startedAt)
```

Emitted when a new round is started.

#### Parameters

| Name      | Type    | Description                                          |
| --------- | ------- | ---------------------------------------------------- |
| roundId   | uint256 | - The round ID of the new round.                     |
| startedBy | address | - The address of the account that started the round. |
| startedAt | uint256 | - The timestamp when the round was started.          |
