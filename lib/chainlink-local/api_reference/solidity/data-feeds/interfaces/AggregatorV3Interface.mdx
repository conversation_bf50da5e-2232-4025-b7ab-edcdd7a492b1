# Solidity API

## AggregatorV3Interface

Interface for accessing detailed data from an aggregator contract, including
round data and metadata.

_Provides methods to get the latest data, historical data for specific rounds,
and metadata such as decimals and description._

### decimals

```solidity
function decimals() external view returns (uint8)
```

Gets the number of decimals used by the aggregator.

#### Return Values

| Name | Type  | Description                     |
| ---- | ----- | ------------------------------- |
| [0]  | uint8 | uint8 - The number of decimals. |

### description

```solidity
function description() external view returns (string)
```

Gets the description of the aggregator.

#### Return Values

| Name | Type   | Description                                        |
| ---- | ------ | -------------------------------------------------- |
| [0]  | string | string memory - The description of the aggregator. |

### version

```solidity
function version() external view returns (uint256)
```

Gets the version of the aggregator.

#### Return Values

| Name | Type    | Description                              |
| ---- | ------- | ---------------------------------------- |
| [0]  | uint256 | uint256 - The version of the aggregator. |

### getRoundData

```solidity
function getRoundData(uint80 _roundId) external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)
```

Gets the round data for a specific round ID.

_This function should raise "No data present" if no data is available for the
given round ID._

#### Parameters

| Name      | Type   | Description                         |
| --------- | ------ | ----------------------------------- |
| \_roundId | uint80 | - The round ID to get the data for. |

#### Return Values

| Name            | Type    | Description                                      |
| --------------- | ------- | ------------------------------------------------ |
| roundId         | uint80  | - The round ID.                                  |
| answer          | int256  | - The answer for the round.                      |
| startedAt       | uint256 | - The timestamp when the round started.          |
| updatedAt       | uint256 | - The timestamp when the round was updated.      |
| answeredInRound | uint80  | - The round ID in which the answer was computed. |

### latestRoundData

```solidity
function latestRoundData() external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)
```

Gets the latest round data.

_This function should raise "No data present" if no data is available._

#### Return Values

| Name            | Type    | Description                                             |
| --------------- | ------- | ------------------------------------------------------- |
| roundId         | uint80  | - The latest round ID.                                  |
| answer          | int256  | - The latest answer.                                    |
| startedAt       | uint256 | - The timestamp when the latest round started.          |
| updatedAt       | uint256 | - The timestamp when the latest round was updated.      |
| answeredInRound | uint80  | - The round ID in which the latest answer was computed. |
