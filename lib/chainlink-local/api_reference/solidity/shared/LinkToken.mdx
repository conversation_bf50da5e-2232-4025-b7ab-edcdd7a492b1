# Solidity API

## LinkToken

This contract implements the ChainLink Token (LINK) using the ERC677 standard.

_Inherits from the ERC677 token contract and initializes with a fixed total
supply and standard token details._

### constructor

```solidity
constructor() public
```

Constructor to initialize the LinkToken contract with a fixed total supply,
name, and symbol.

_Calls the ERC677 constructor with the name and symbol, and then mints the total
supply to the contract deployer._

### \_onCreate

```solidity
function _onCreate() internal virtual
```

Hook that is called when this contract is created.

_Useful to override constructor behaviour in child contracts (e.g., LINK bridge
tokens). The default implementation mints 10\*\*27 tokens to the contract
deployer._
