# Solidity API

## MockVerifier

### AccessForbidden

```solidity
error AccessForbidden()
```

### InactiveFeed

```solidity
error InactiveFeed(bytes32 feedId)
```

### DigestInactive

```solidity
error DigestInactive(bytes32 feedId, bytes32 configDigest)
```

### BadVerification

```solidity
error BadVerification()
```

### InvalidV

```solidity
error InvalidV()
```

### AlreadyUsedSignature

```solidity
error AlreadyUsedSignature()
```

### InvalidSignatureLength

```solidity
error InvalidSignatureLength()
```

### InvalidS

```solidity
error InvalidS()
```

### N_2

```solidity
uint256 N_2
```

### MOCK_DATA_STREAM_DON_ADDRESS

```solidity
address MOCK_DATA_STREAM_DON_ADDRESS
```

### i_verifierProxy

```solidity
address i_verifierProxy
```

### s_inactiveDigests

```solidity
mapping(bytes32 => mapping(bytes32 => bool)) s_inactiveDigests
```

### s_inactiveFeeds

```solidity
mapping(bytes32 => bool) s_inactiveFeeds
```

### s_verifiedSignatures

```solidity
mapping(bytes => bool) s_verifiedSignatures
```

### ReportVerified

```solidity
event ReportVerified(bytes32 feedId, address sender)
```

### constructor

```solidity
constructor(address verifierProxy) public
```

### verify

```solidity
function verify(bytes signedReport, address sender) external returns (bytes verifierResponse)
```

### deactivateConfig

```solidity
function deactivateConfig(bytes32 feedId, bytes32 configDigest) external
```

### activateConfig

```solidity
function activateConfig(bytes32 feedId, bytes32 configDigest) external
```

### deactivateFeed

```solidity
function deactivateFeed(bytes32 feedId) external
```

### activateFeed

```solidity
function activateFeed(bytes32 feedId) external
```

### supportsInterface

```solidity
function supportsInterface(bytes4 interfaceId) external pure returns (bool)
```

\_Returns true if this contract implements the interface defined by
`interfaceId`. See the corresponding
https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP
section] to learn more about how these ids are created.

This function call must use less than 30 000 gas.\_
