# Solidity API

## DataStreamsLocalSimulator

### s_mockVerifier

```solidity
contract MockVerifier s_mockVerifier
```

### s_mockVerifierProxy

```solidity
contract MockVerifierProxy s_mockVerifierProxy
```

### s_mockFeeManager

```solidity
contract MockFeeManager s_mockFeeManager
```

### s_mockRewardManager

```solidity
contract MockRewardManager s_mockRewardManager
```

### i_wrappedNative

```solidity
contract WETH9 i_wrappedNative
```

The wrapped native token instance

### i_linkToken

```solidity
contract LinkToken i_linkToken
```

The LINK token instance

### constructor

```solidity
constructor() public
```

### requestLinkFromFaucet

```solidity
function requestLinkFromFaucet(address to, uint256 amount) external returns (bool success)
```

Requests LINK tokens from the faucet. The provided amount of tokens are
transferred to provided destination address.

#### Parameters

| Name   | Type    | Description                                        |
| ------ | ------- | -------------------------------------------------- |
| to     | address | - The address to which LINK tokens are to be sent. |
| amount | uint256 | - The amount of LINK tokens to send.               |

#### Return Values

| Name    | Type | Description                                                                   |
| ------- | ---- | ----------------------------------------------------------------------------- |
| success | bool | - Returns `true` if the transfer of tokens was successful, otherwise `false`. |

### configuration

```solidity
function configuration() public view returns (contract WETH9 wrappedNative_, contract LinkToken linkToken_, contract MockVerifier mockVerifier_, contract MockVerifierProxy mockVerifierProxy_, contract MockFeeManager mockFeeManager_, contract MockRewardManager mockRewardManager_)
```

@notice Returns configuration details for pre-deployed contracts and services
needed for local Data Streams simulations.

#### Return Values

| Name                | Type                       | Description                         |
| ------------------- | -------------------------- | ----------------------------------- |
| wrappedNative\_     | contract WETH9             | - The wrapped native token.         |
| linkToken\_         | contract LinkToken         | - The LINK token.                   |
| mockVerifier\_      | contract MockVerifier      | - The mock verifier contract.       |
| mockVerifierProxy\_ | contract MockVerifierProxy | - The mock verifier proxy contract. |
| mockFeeManager\_    | contract MockFeeManager    | - The mock fee manager contract.    |
| mockRewardManager\_ | contract MockRewardManager | - The mock reward manager contract. |
