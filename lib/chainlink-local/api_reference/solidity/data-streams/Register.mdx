# Solidity API

## Register

This contract allows storing and retrieving network details for various chains.

_Stores network details in a mapping based on chain IDs._

### NetworkDetails

```solidity
struct NetworkDetails {
  address verifierProxyAddress;
  address linkAddress;
}
```

### s_networkDetails

```solidity
mapping(uint256 => struct Register.NetworkDetails) s_networkDetails
```

Mapping to store network details based on chain ID.

### constructor

```solidity
constructor() public
```

Constructor to initialize the network details for various chains.

### getNetworkDetails

```solidity
function getNetworkDetails(uint256 chainId) external view returns (struct Register.NetworkDetails networkDetails)
```

Retrieves network details for a given chain ID.

#### Parameters

| Name    | Type    | Description                                   |
| ------- | ------- | --------------------------------------------- |
| chainId | uint256 | - The ID of the chain to get the details for. |

#### Return Values

| Name           | Type                           | Description                                       |
| -------------- | ------------------------------ | ------------------------------------------------- |
| networkDetails | struct Register.NetworkDetails | - The network details for the specified chain ID. |

### setNetworkDetails

```solidity
function setNetworkDetails(uint256 chainId, struct Register.NetworkDetails networkDetails) external
```

Sets the network details for a given chain ID.

#### Parameters

| Name           | Type                           | Description                                              |
| -------------- | ------------------------------ | -------------------------------------------------------- |
| chainId        | uint256                        | - The ID of the chain to set the details for.            |
| networkDetails | struct Register.NetworkDetails | - The network details to set for the specified chain ID. |
