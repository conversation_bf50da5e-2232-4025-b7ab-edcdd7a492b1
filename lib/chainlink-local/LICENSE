Copyright (c) 2018 SmartContract ChainLink Limited SEZC

Portions of this software are licensed as follows:

The MIT License (MIT)

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.


*All content residing under (1) “src/ccip/BurnMintERC677Helper.sol”; (2)
“src/ccip/CCIPLocalSimulator.sol”; (3) “src/ccip/CCIPLocalSimulatorFork.sol”; (4) "src/ccip/Register.sol" are licensed
under “Business Source License 1.1” with a Change Date of May 23, 2027 and
Change License to “MIT License”

* Content outside of the above mentioned directories or restrictions
above is available under the "MIT" license as defined above.
