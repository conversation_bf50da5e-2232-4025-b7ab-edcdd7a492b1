# dependencies
node_modules/
tmp/
.pnp
.pnp.js
tools/bin/abigen
tools/bin/forge_zksync

/chainlink
core/chainlink

# SQLite
tools/clroot/db.sqlite3-shm
tools/clroot/db.sqlite3-wal

# Tooling caches
*.tsbuildinfo
.eslintcache

# Log files
*.log

# misc
.DS_Store
.envrc
.env*
.dbenv
!.github/actions/setup-postgres/.env
.direnv
.idea
.vscode/
*.iml
debug.env
operator_ui/install
.devenv

# codeship
*.aes
dockercfg
env
credentials.env
gcr_creds.env

# DB backups

cl_backup_*.tar.gz

# Test artifacts
core/cmd/TestClient_ImportExportP2PKeyBundle_test_key.json
race.*
*output.txt
/golangci-lint/
.covdata
core/services/job/testdata/wasm/testmodule.wasm
core/services/job/testdata/wasm/testmodule.br
temp-repo

# DB state
./db/
.s.PGSQL.5432.lock

# can be left behind by tests
core/cmd/vrfkey1

# Integration Tests
integration-tests/**/logs/
tests-*.xml
*.test
tmp-manifest-*.yaml
ztarrepo.tar.gz
**/test-ledger/*
__debug_bin*
.test_summary/
db_dumps/
.run.id
integration-tests/**/traces/
integration-tests/**/integration-tests
benchmark_report.csv
benchmark_summary.json
secrets.toml
tmp_laneconfig/
solana_contracts

# goreleaser builds
cosign.*
dist/
MacOSX*

# Test & linter reports
*report.xml
*report.json
*.out
dot_graphs/

contracts/yarn.lock

# Ignore DevSpace cache and log folder
.devspace/
go.work*

# This sometimes shows up for some reason
tools/flakeytests/coverage.txt

# Fuzz tests can create these files
**/testdata/fuzz/*

# Runtime test configuration that might contain secrets
override*.toml

# Python venv
.venv/

ocr_soak_report.csv

vendor/*

*.wasm
contracts/lcov.info
contracts/out/
/cache
