{"name": "@chainlink/contracts", "version": "1.4.0", "description": "Chainlink smart contracts", "author": "Chainlink devs", "repository": "github:smartcontractkit/chainlink-evm", "license": "BUSL-1.1", "private": false, "scripts": {"test": "hardhat test --parallel", "lint": "eslint --ext js,ts .", "prettier:check": "prettier '**/*' --check --ignore-unknown", "prettier:write": "prettier '**/*' --write --ignore-unknown", "size": "hardhat size-contracts", "clean": "hardhat clean", "compile:native": "./scripts/native_solc_compile_all", "compile": "hardhat compile", "coverage": "hardhat coverage", "prepublishOnly": "pnpm compile && ./scripts/prepublish_generate_abi_folder", "publish-beta": "pnpm publish --tag beta", "publish-prod": "pnpm publish --tag latest", "solhint": "solhint --max-warnings 0 \"./src/v0.8/**/*.sol\"", "solhint-test": "solhint --config \".solhint-test.json\"  --ignore-path \".solhintignore-test\" --max-warnings 0 \"./src/v0.8/**/*.sol\""}, "files": ["src/v0.8", "abi/v0.8", "!**/test/**/*", "src/**/test/mocks/**/*.sol", "!src/**/test/mocks/test/*.sol", "src/**/test/helpers/**/*.sol", "foundry.toml", "remappings.txt", ".solhint.json", ".solhint-test.json", ".solhinti<PERSON>re", ".solhintignore-test"], "pnpm": {"_comment": "See https://github.com/ethers-io/ethers.js/discussions/2849#discussioncomment-2696454", "_comment2_logger": "See https://github.com/ethers-io/ethers.js/issues/379 we pin this version since that's what was used in the old yarn lockfile", "overrides": {"@ethersproject/logger": "5.0.6", "@ethersproject/providers@5.7.2>ws": "7.5.10", "@openzeppelin/contracts-upgradeable": "4.9.6", "@openzeppelin/contracts": "4.9.6", "cookie": "^0.7.0"}, "onlyBuiltDependencies": ["// keccak@3.0.4, secp256k1@4.0.4 run node-gyp-build during install", "// core-js@3.4.0 has a postinstall script to print a banner", "// @arbitrum/nitro-contracts@1.1.1 has a postinstall script that runs patch-package. But has no patches.", "secp256k1", "keccak", "core-js", "@arbitrum/nitro-contracts"]}, "engines": {"node": ">=18", "pnpm": ">=10"}, "devDependencies": {"@ethereum-waffle/mock-contract": "^3.4.4", "@nomicfoundation/hardhat-chai-matchers": "^1.0.6", "@nomicfoundation/hardhat-ethers": "^3.0.8", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "@nomicfoundation/hardhat-verify": "^2.0.13", "@typechain/ethers-v5": "^7.2.0", "@typechain/hardhat": "^7.0.0", "@types/cbor": "~5.0.1", "@types/chai": "^4.3.20", "@types/debug": "^4.1.12", "@types/deep-equal-in-any-order": "^1.0.4", "@types/mocha": "^10.0.10", "@types/node": "^20.17.27", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "abi-to-sol": "^0.6.6", "cbor": "^5.2.0", "chai": "^4.5.0", "debug": "^4.4.0", "deep-equal-in-any-order": "^2.0.6", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.5", "ethers": "~5.8.0", "hardhat": "~2.20.1", "hardhat-abi-exporter": "^2.10.1", "hardhat-ignore-warnings": "^0.2.12", "moment": "^2.30.1", "prettier": "^3.5.3", "prettier-plugin-solidity": "^1.4.2", "solhint": "^5.0.5", "solhint-plugin-chainlink-solidity": "github:smartcontractkit/chainlink-solhint-rules#v1.2.1", "solhint-plugin-prettier": "^0.1.0", "ts-node": "^10.9.2", "typechain": "^8.3.2", "typescript": "^5.8.2"}, "dependencies": {"@arbitrum/nitro-contracts": "3.0.0", "@changesets/cli": "~2.28.1", "@changesets/get-github-info": "^0.6.0", "@eth-optimism/contracts": "0.6.0", "@openzeppelin/contracts": "4.9.6", "@openzeppelin/contracts-upgradeable": "4.9.6", "@scroll-tech/contracts": "2.0.0", "@zksync/contracts": "github:matter-labs/era-contracts#446d391d34bdb48255d5f8fef8a8248925fc98b9", "semver": "^7.7.1"}}