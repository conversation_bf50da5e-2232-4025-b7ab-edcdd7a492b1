#!/usr/bin/env bash

set -e

echo " ┌──────────────────────────────────────────────┐"
echo " │         Compiling *all* contracts...         │"
echo " └──────────────────────────────────────────────┘"

SCRIPTPATH="$( cd "$(dirname "$0")" >/dev/null 2>&1 ; pwd -P )"

# For each product we have a native_solc_compile_all_$product script
# These scripts can be run individually, or all together with this script.
# To add new CL products, simply write a native_solc_compile_all_$product script and add it to the list below.
for product in automation functions keystone llo-feeds operatorforwarder shared vrf workflow data-feeds
do
  $SCRIPTPATH/native_solc_compile_all_$product
done
