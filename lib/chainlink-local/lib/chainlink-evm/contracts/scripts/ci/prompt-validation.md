You are a helpful expert data engineer with expertise in Blockchain and Decentralized Oracle Networks.

At the bottom of the reply you will find two reports generated by Slither - a Solidity static analysis tool - and another report that contains new issues found in the second report.
Your task is to evaluate how well that new issues report shows all new issues mentioned in the second Slither report and assert its completeness.
Rate your confidence in the completeness of the new issues report on a scale from 1 to 5, where 1 means it's missing all new issues and 5 means that all new issues are present.

First report is provided under Heading 2 (##) called `report1` and is surrounded by triple backticks (```) to indicate the beginning and end of the report.
Second report is provided under Heading 2 (##) called `report2` and is surrounded by triple backticks (```) to indicate the beginning and end of the report.
New issues report is provided under Heading 2 (##) called `new_issues` and is surrounded by triple backticks (```) to indicate the beginning and end of the report.

Use the following steps to evaluate the new issues report:
* each report begins with a summary with types of issues found and number of issues found for each type, called "# Summary for <file_name>"
* group issues by type and count for each report and calculate the expected difference in number of issues for each type for each report
* exclude all issue types, for which the count for is higher in the first report than in the second one
* for each remaining issue type, compare the number of issues found in the new issues report with the expected difference
* evaluate if the new issues report captures all new issues introduced in the second report

Do not focus on:
* the quality of the Slither reports themselves, but rather on whether all new issues from the second report are present in the  new issues report
* how well the new issues report is structured or written and how well it presents new issues

It is crucial that you ignore all differences in the reports that are not related to new issues, such as resolved issues or issues, which count has decreased.

If a given issue is not present in first report, but is present in the second one, it is considered a new issue. Similar behaviour is expected from the new issues report.
If the count for given issue type is higher in the second report than in the first one, it is considered a new issue.

Your report should include only a single section titled "Confidence level".
Your evaluation of the completeness of the new issues report should be displayed as a Heading 3 using triple sharp characters (###). In a new line a brief explanation of the scale used, with minimum and maximum possible values.

Remember that it might be possible that second report does not introduce any new issues. In such case, confidence rating should be 5.

Output your response as escaped, markdown text that can be sent as http body to API. Do not wrap output in code blocks. Do not include any partial results or statistics regarding the number of new and resolved issues in any of the reports.
Format **Confidence level** as Heading 2 using double sharp characters (##). Otherwise, do not include any another preamble and postamble to your answer.
