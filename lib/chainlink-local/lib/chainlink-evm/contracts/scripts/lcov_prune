#!/bin/bash

if [ "$#" -ne 3 ]; then
    >&2 echo "Usage: $0 <product_name> <input_coverage_file> <output_coverage_file>"
    exit 1
fi

set -e

product_name=$1
input_coverage_file=$2
output_coverage_file=$3

exclusion_list_shared=(
   "*/shared/*"
)

exclusion_list_common=(
  "*/$product_name/test/*"
  "*/vendor/*"
)

all_exclusions=("${exclusion_list_common[@]}")

if [ "$product_name" != "shared" ]; then
  all_exclusions+=("${exclusion_list_shared[@]}")
fi

echo "Excluding the following files for product $product_name:"
for exclusion in "${all_exclusions[@]}"; do
  echo "$exclusion"
done

lcov_command="lcov --remove $input_coverage_file -o $output_coverage_file"

for exclusion in "${all_exclusions[@]}"; do
  lcov_command+=" \"$exclusion\""
done

lcov_command+=" --rc lcov_branch_coverage=1"

eval $lcov_command
