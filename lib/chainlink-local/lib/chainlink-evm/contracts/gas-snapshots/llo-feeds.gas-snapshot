ByteUtilTest:test_readAddress() (gas: 3366)
ByteUtilTest:test_readAddressMultiWord() (gas: 3386)
ByteUtilTest:test_readAddressWithEmptyArray() (gas: 3252)
ByteUtilTest:test_readAddressWithNotEnoughBytes() (gas: 3314)
ByteUtilTest:test_readUint192Max() (gas: 3326)
ByteUtilTest:test_readUint192Min() (gas: 3327)
ByteUtilTest:test_readUint192MultiWord() (gas: 3392)
ByteUtilTest:test_readUint192WithEmptyArray() (gas: 3274)
ByteUtilTest:test_readUint192WithNotEnoughBytes() (gas: 3314)
ByteUtilTest:test_readUint256Max() (gas: 3321)
ByteUtilTest:test_readUint256Min() (gas: 3365)
ByteUtilTest:test_readUint256MultiWord() (gas: 3341)
ByteUtilTest:test_readUint256WithEmptyArray() (gas: 3274)
ByteUtilTest:test_readUint256WithNotEnoughBytes() (gas: 3271)
ByteUtilTest:test_readUint32Max() (gas: 3348)
ByteUtilTest:test_readUint32Min() (gas: 3328)
ByteUtilTest:test_readUint32MultiWord() (gas: 3371)
ByteUtilTest:test_readUint32WithEmptyArray() (gas: 3231)
ByteUtilTest:test_readUint32WithNotEnoughBytes() (gas: 3272)
ByteUtilTest:test_readZeroAddress() (gas: 3343)
ChannelConfigStoreTest:testSetChannelDefinitions() (gas: 46905)
ChannelConfigStoreTest:testSupportsInterface() (gas: 8367)
ChannelConfigStoreTest:testTypeAndVersion() (gas: 9644)
ChannelConfigStoreTest:test_revertsIfCalledByNonOwner() (gas: 11288)
ConfiguratorPromoteStagingConfigTest:test_promotesStagingConfig() (gas: 99047)
ConfiguratorPromoteStagingConfigTest:test_revertsIfCalledByNonOwner() (gas: 10962)
ConfiguratorPromoteStagingConfigTest:test_revertsIfIsGreenProductionDoesNotMatchContractState() (gas: 13423)
ConfiguratorPromoteStagingConfigTest:test_revertsIfNoConfigHasEverBeenSetWithThisConfigId() (gas: 13419)
ConfiguratorPromoteStagingConfigTest:test_revertsIfProductionConfigDigestIsZero() (gas: 68521)
ConfiguratorPromoteStagingConfigTest:test_revertsIfStagingConfigDigestIsZero() (gas: 48258)
ConfiguratorSetProductionConfigTest:test_correctlyUpdatesTheConfig() (gas: 259776)
ConfiguratorSetProductionConfigTest:test_revertsIfCalledByNonOwner() (gas: 266559)
ConfiguratorSetProductionConfigTest:test_revertsIfFaultToleranceIsZero() (gas: 264173)
ConfiguratorSetProductionConfigTest:test_revertsIfNotEnoughSigners() (gas: 95951)
ConfiguratorSetProductionConfigTest:test_revertsIfOnchainConfigIsInvalid() (gas: 60885)
ConfiguratorSetProductionConfigTest:test_revertsIfSetWithTooManySigners() (gas: 107412)
ConfiguratorSetProductionConfigTest:test_supportsHigherVersionsIgnoringExcessOnchainConfig() (gas: 125099)
ConfiguratorSetStagingConfigTest:test_correctlyUpdatesTheConfig() (gas: 266041)
ConfiguratorSetStagingConfigTest:test_revertsIfCalledByNonOwner() (gas: 266528)
ConfiguratorSetStagingConfigTest:test_revertsIfFaultToleranceIsZero() (gas: 264142)
ConfiguratorSetStagingConfigTest:test_revertsIfNotEnoughSigners() (gas: 95920)
ConfiguratorSetStagingConfigTest:test_revertsIfOnchainConfigIsInvalid() (gas: 85217)
ConfiguratorSetStagingConfigTest:test_revertsIfSetWithTooManySigners() (gas: 107392)
ConfiguratorTest:testSupportsInterface() (gas: 8367)
ConfiguratorTest:testTypeAndVersion() (gas: 9683)
DestinationFeeManagerProcessFeeTest:test_DiscountIsAppliedForNative() (gas: 52717)
DestinationFeeManagerProcessFeeTest:test_DiscountIsReturnedForNative() (gas: 52645)
DestinationFeeManagerProcessFeeTest:test_DiscountIsReturnedForNativeWithSurcharge() (gas: 78879)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountCantBeSetToMoreThanMaximum() (gas: 19544)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountIsOverridenByIndividualDiscountLink() (gas: 79509)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountIsOverridenByIndividualDiscountNative() (gas: 82523)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountIsUpdatedAfterBeingSetToZeroLink() (gas: 48409)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountIsUpdatedAfterBeingSetToZeroNative() (gas: 51589)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountWithLink() (gas: 51902)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountWithNative() (gas: 54892)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountWithNativeAndLink() (gas: 83739)
DestinationFeeManagerProcessFeeTest:test_V1PayloadVerifies() (gas: 29358)
DestinationFeeManagerProcessFeeTest:test_V1PayloadVerifiesAndReturnsChange() (gas: 61221)
DestinationFeeManagerProcessFeeTest:test_V2PayloadVerifies() (gas: 123459)
DestinationFeeManagerProcessFeeTest:test_V2PayloadWithoutQuoteFails() (gas: 29703)
DestinationFeeManagerProcessFeeTest:test_V2PayloadWithoutZeroFee() (gas: 77115)
DestinationFeeManagerProcessFeeTest:test_WithdrawERC20() (gas: 72819)
DestinationFeeManagerProcessFeeTest:test_WithdrawNonAdminAddr() (gas: 56357)
DestinationFeeManagerProcessFeeTest:test_WithdrawUnwrappedNative() (gas: 26434)
DestinationFeeManagerProcessFeeTest:test_addVerifier() (gas: 128921)
DestinationFeeManagerProcessFeeTest:test_addVerifierExistingAddress() (gas: 34192)
DestinationFeeManagerProcessFeeTest:test_baseFeeIsAppliedForLink() (gas: 19497)
DestinationFeeManagerProcessFeeTest:test_baseFeeIsAppliedForNative() (gas: 22502)
DestinationFeeManagerProcessFeeTest:test_correctDiscountIsAppliedWhenBothTokensAreDiscounted() (gas: 91133)
DestinationFeeManagerProcessFeeTest:test_discountAIsNotAppliedWhenSetForOtherUsers() (gas: 58864)
DestinationFeeManagerProcessFeeTest:test_discountFeeRoundsDownWhenUneven() (gas: 52919)
DestinationFeeManagerProcessFeeTest:test_discountIsAppliedForLink() (gas: 49730)
DestinationFeeManagerProcessFeeTest:test_discountIsAppliedWith100PercentSurcharge() (gas: 78908)
DestinationFeeManagerProcessFeeTest:test_discountIsNoLongerAppliedAfterRemoving() (gas: 48362)
DestinationFeeManagerProcessFeeTest:test_discountIsNotAppliedForInvalidTokenAddress() (gas: 17516)
DestinationFeeManagerProcessFeeTest:test_discountIsNotAppliedToOtherFeeds() (gas: 56912)
DestinationFeeManagerProcessFeeTest:test_discountIsReturnedForLink() (gas: 49702)
DestinationFeeManagerProcessFeeTest:test_emptyQuoteRevertsWithError() (gas: 12253)
DestinationFeeManagerProcessFeeTest:test_eventIsEmittedAfterSurchargeIsSet() (gas: 41379)
DestinationFeeManagerProcessFeeTest:test_eventIsEmittedIfNotEnoughLink() (gas: 182683)
DestinationFeeManagerProcessFeeTest:test_eventIsEmittedUponWithdraw() (gas: 69080)
DestinationFeeManagerProcessFeeTest:test_feeIsUpdatedAfterDiscountIsRemoved() (gas: 51626)
DestinationFeeManagerProcessFeeTest:test_feeIsUpdatedAfterNewDiscountIsApplied() (gas: 67777)
DestinationFeeManagerProcessFeeTest:test_feeIsUpdatedAfterNewSurchargeIsApplied() (gas: 66960)
DestinationFeeManagerProcessFeeTest:test_feeIsZeroWith100PercentDiscount() (gas: 52073)
DestinationFeeManagerProcessFeeTest:test_getBaseRewardWithLinkQuote() (gas: 19495)
DestinationFeeManagerProcessFeeTest:test_getLinkFeeIsRoundedUp() (gas: 49857)
DestinationFeeManagerProcessFeeTest:test_getLinkRewardIsSameAsFee() (gas: 55762)
DestinationFeeManagerProcessFeeTest:test_getLinkRewardWithNativeQuoteAndSurchargeWithLinkDiscount() (gas: 85050)
DestinationFeeManagerProcessFeeTest:test_getRewardWithLinkDiscount() (gas: 49726)
DestinationFeeManagerProcessFeeTest:test_getRewardWithLinkQuoteAndLinkDiscount() (gas: 49685)
DestinationFeeManagerProcessFeeTest:test_getRewardWithNativeQuote() (gas: 22456)
DestinationFeeManagerProcessFeeTest:test_getRewardWithNativeQuoteAndSurcharge() (gas: 53190)
DestinationFeeManagerProcessFeeTest:test_linkAvailableForPaymentReturnsLinkBalance() (gas: 53194)
DestinationFeeManagerProcessFeeTest:test_nativeSurcharge0Percent() (gas: 33198)
DestinationFeeManagerProcessFeeTest:test_nativeSurcharge100Percent() (gas: 53170)
DestinationFeeManagerProcessFeeTest:test_nativeSurchargeCannotExceed100Percent() (gas: 17152)
DestinationFeeManagerProcessFeeTest:test_nativeSurchargeEventIsEmittedOnUpdate() (gas: 41357)
DestinationFeeManagerProcessFeeTest:test_noFeeIsAppliedWhenReportHasZeroFee() (gas: 51918)
DestinationFeeManagerProcessFeeTest:test_noFeeIsAppliedWhenReportHasZeroFeeAndDiscountAndSurchargeIsSet() (gas: 78108)
DestinationFeeManagerProcessFeeTest:test_nonAdminProxyUserCannotProcessFee() (gas: 24207)
DestinationFeeManagerProcessFeeTest:test_nonAdminUserCanNotSetDiscount() (gas: 19784)
DestinationFeeManagerProcessFeeTest:test_onlyCallableByOwnerReverts() (gas: 15475)
DestinationFeeManagerProcessFeeTest:test_onlyOwnerCanSetGlobalDiscount() (gas: 19929)
DestinationFeeManagerProcessFeeTest:test_payLinkDeficit() (gas: 199893)
DestinationFeeManagerProcessFeeTest:test_payLinkDeficitOnlyCallableByAdmin() (gas: 17348)
DestinationFeeManagerProcessFeeTest:test_payLinkDeficitPaysAllFeesProcessed() (gas: 221286)
DestinationFeeManagerProcessFeeTest:test_payLinkDeficitTwice() (gas: 204214)
DestinationFeeManagerProcessFeeTest:test_poolIdsCannotBeZeroAddress() (gas: 117941)
DestinationFeeManagerProcessFeeTest:test_processFeeAsProxy() (gas: 123797)
DestinationFeeManagerProcessFeeTest:test_processFeeDefaultReportsStillVerifiesWithEmptyQuote() (gas: 29779)
DestinationFeeManagerProcessFeeTest:test_processFeeEmitsEventIfNotEnoughLink() (gas: 167711)
DestinationFeeManagerProcessFeeTest:test_processFeeIfSubscriberIsSelf() (gas: 32597)
DestinationFeeManagerProcessFeeTest:test_processFeeNative() (gas: 180526)
DestinationFeeManagerProcessFeeTest:test_processFeeUsesCorrectDigest() (gas: 125088)
DestinationFeeManagerProcessFeeTest:test_processFeeWithDefaultReportPayloadAndQuoteStillVerifies() (gas: 31856)
DestinationFeeManagerProcessFeeTest:test_processFeeWithDiscountEmitsEvent() (gas: 246054)
DestinationFeeManagerProcessFeeTest:test_processFeeWithInvalidReportVersionFailsToDecode() (gas: 30804)
DestinationFeeManagerProcessFeeTest:test_processFeeWithNoDiscountDoesNotEmitEvent() (gas: 173431)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNative() (gas: 188391)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeLinkAddress() (gas: 138192)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeLinkAddressExcessiveFee() (gas: 163781)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeShortFunds() (gas: 97159)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeWithExcessiveFee() (gas: 195354)
DestinationFeeManagerProcessFeeTest:test_processFeeWithWithCorruptQuotePayload() (gas: 77402)
DestinationFeeManagerProcessFeeTest:test_processFeeWithWithEmptyQuotePayload() (gas: 30040)
DestinationFeeManagerProcessFeeTest:test_processFeeWithWithZeroQuotePayload() (gas: 30090)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroLinkNonZeroNativeWithLinkQuote() (gas: 37638)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroLinkNonZeroNativeWithNativeQuote() (gas: 160403)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkReturnsChange() (gas: 58377)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkWithLinkQuote() (gas: 123795)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkWithNativeQuote() (gas: 40342)
DestinationFeeManagerProcessFeeTest:test_processMultipleLinkReports() (gas: 233892)
DestinationFeeManagerProcessFeeTest:test_processMultipleUnwrappedNativeReports() (gas: 267681)
DestinationFeeManagerProcessFeeTest:test_processMultipleV1Reports() (gas: 81189)
DestinationFeeManagerProcessFeeTest:test_processMultipleWrappedNativeReports() (gas: 250530)
DestinationFeeManagerProcessFeeTest:test_processPoolIdsPassedMismatched() (gas: 98827)
DestinationFeeManagerProcessFeeTest:test_processV1V2V3Reports() (gas: 218606)
DestinationFeeManagerProcessFeeTest:test_processV1V2V3ReportsWithUnwrapped() (gas: 260283)
DestinationFeeManagerProcessFeeTest:test_removeVerifierNonExistentAddress() (gas: 12822)
DestinationFeeManagerProcessFeeTest:test_removeVerifierZeroAaddress() (gas: 10700)
DestinationFeeManagerProcessFeeTest:test_reportWithNoExpiryOrFeeReturnsZero() (gas: 13682)
DestinationFeeManagerProcessFeeTest:test_revertOnSettingAnAddressZeroVerifier() (gas: 10636)
DestinationFeeManagerProcessFeeTest:test_rewardsAreCorrectlySentToEachAssociatedPoolWhenVerifyingInBulk() (gas: 266620)
DestinationFeeManagerProcessFeeTest:test_setDiscountOver100Percent() (gas: 19540)
DestinationFeeManagerProcessFeeTest:test_setRewardManagerZeroAddress() (gas: 10626)
DestinationFeeManagerProcessFeeTest:test_subscriberDiscountEventIsEmittedOnUpdate() (gas: 46285)
DestinationFeeManagerProcessFeeTest:test_surchargeFeeRoundsUpWhenUneven() (gas: 53501)
DestinationFeeManagerProcessFeeTest:test_surchargeIsApplied() (gas: 53426)
DestinationFeeManagerProcessFeeTest:test_surchargeIsAppliedForNativeFeeWithDiscount() (gas: 79315)
DestinationFeeManagerProcessFeeTest:test_surchargeIsNoLongerAppliedAfterRemoving() (gas: 49149)
DestinationFeeManagerProcessFeeTest:test_surchargeIsNotAppliedForLinkFee() (gas: 52223)
DestinationFeeManagerProcessFeeTest:test_surchargeIsNotAppliedWith100PercentDiscount() (gas: 78311)
DestinationFeeManagerProcessFeeTest:test_testRevertIfReportHasExpired() (gas: 14987)
DestinationRewardManagerClaimTest:test_claimAllRecipients() (gas: 277223)
DestinationRewardManagerClaimTest:test_claimMultipleRecipients() (gas: 154387)
DestinationRewardManagerClaimTest:test_claimRewardsWithDuplicatePoolIdsDoesNotPayoutTwice() (gas: 330244)
DestinationRewardManagerClaimTest:test_claimSingleRecipient() (gas: 89047)
DestinationRewardManagerClaimTest:test_claimUnevenAmountRoundsDown() (gas: 315447)
DestinationRewardManagerClaimTest:test_claimUnregisteredPoolId() (gas: 35146)
DestinationRewardManagerClaimTest:test_claimUnregisteredRecipient() (gas: 41205)
DestinationRewardManagerClaimTest:test_eventIsEmittedUponClaim() (gas: 86092)
DestinationRewardManagerClaimTest:test_eventIsNotEmittedUponUnsuccessfulClaim() (gas: 25054)
DestinationRewardManagerClaimTest:test_recipientsClaimMultipleDeposits() (gas: 386925)
DestinationRewardManagerClaimTest:test_singleRecipientClaimMultipleDeposits() (gas: 137775)
DestinationRewardManagerNoRecipientSet:test_claimAllRecipientsAfterRecipientsSet() (gas: 494460)
DestinationRewardManagerPayRecipientsTest:test_addFundsToPoolAsNonOwnerOrFeeManager() (gas: 11517)
DestinationRewardManagerPayRecipientsTest:test_addFundsToPoolAsOwner() (gas: 53965)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipients() (gas: 253082)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipientsFromNonAdminUser() (gas: 20472)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipientsFromRecipientInPool() (gas: 248942)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalInvalidRecipient() (gas: 264510)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalUnregisteredRecipient() (gas: 267995)
DestinationRewardManagerPayRecipientsTest:test_payRecipientWithInvalidPool() (gas: 31111)
DestinationRewardManagerPayRecipientsTest:test_payRecipientsEmptyRecipientList() (gas: 27577)
DestinationRewardManagerPayRecipientsTest:test_payRecipientsWithInvalidPoolId() (gas: 33639)
DestinationRewardManagerPayRecipientsTest:test_paySingleRecipient() (gas: 86982)
DestinationRewardManagerPayRecipientsTest:test_paySubsetOfRecipientsInPool() (gas: 200697)
DestinationRewardManagerRecipientClaimDifferentWeightsTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 280819)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsMultiplePools() (gas: 512553)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsSinglePool() (gas: 283726)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimEmptyPoolWhenSecondPoolContainsFunds() (gas: 293533)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsMultiplePools() (gas: 263107)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsSinglePool() (gas: 154531)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleRecipientMultiplePools() (gas: 132669)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleUniqueRecipient() (gas: 106068)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimUnevenAmountRoundsDown() (gas: 579848)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimUnregisteredRecipient() (gas: 64672)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorAndTotalPoolsEqual() (gas: 13052)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorCannotBeGreaterThanTotalPools() (gas: 12747)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorSingleResult() (gas: 22449)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPools() (gas: 32248)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPoolsWhereAlreadyClaimed() (gas: 148645)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInNoPools() (gas: 21706)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInSinglePool() (gas: 27765)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_recipientsClaimMultipleDeposits() (gas: 391495)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_singleRecipientClaimMultipleDeposits() (gas: 137860)
DestinationRewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 199566)
DestinationRewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmountWithSmallDeposit() (gas: 219439)
DestinationRewardManagerSetRecipientsTest:test_eventIsEmittedUponSetRecipients() (gas: 193870)
DestinationRewardManagerSetRecipientsTest:test_setRecipientContainsDuplicateRecipients() (gas: 128245)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientFromManagerAddress() (gas: 213998)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientFromNonOwnerOrFeeManagerAddress() (gas: 21496)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientTwice() (gas: 195694)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientWeights() (gas: 182793)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroAddress() (gas: 92387)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroWeight() (gas: 193497)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipients() (gas: 187752)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientsIsEmpty() (gas: 89299)
DestinationRewardManagerSetRecipientsTest:test_setSingleRewardRecipient() (gas: 112534)
DestinationRewardManagerSetupTest:test_addFeeManagerExistingAddress() (gas: 35281)
DestinationRewardManagerSetupTest:test_addFeeManagerZeroAddress() (gas: 10603)
DestinationRewardManagerSetupTest:test_addRemoveFeeManager() (gas: 48231)
DestinationRewardManagerSetupTest:test_eventEmittedUponFeeManagerUpdate() (gas: 41559)
DestinationRewardManagerSetupTest:test_eventEmittedUponFeePaid() (gas: 261361)
DestinationRewardManagerSetupTest:test_rejectsZeroLinkAddressOnConstruction() (gas: 59481)
DestinationRewardManagerSetupTest:test_removeFeeManagerNonExistentAddress() (gas: 12778)
DestinationRewardManagerSetupTest:test_setFeeManagerZeroAddress() (gas: 17128)
DestinationRewardManagerUpdateRewardRecipientsMultiplePoolsTest:test_updatePrimaryRecipientWeights() (gas: 376742)
DestinationRewardManagerUpdateRewardRecipientsTest:test_eventIsEmittedUponUpdateRecipients() (gas: 280465)
DestinationRewardManagerUpdateRewardRecipientsTest:test_onlyAdminCanUpdateRecipients() (gas: 19705)
DestinationRewardManagerUpdateRewardRecipientsTest:test_partialUpdateRecipientWeights() (gas: 221040)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateAllRecipientsWithSameAddressAndWeight() (gas: 274265)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsToSubset() (gas: 254188)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithExcessiveWeight() (gas: 259175)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithSameAddressAndWeight() (gas: 149872)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithUnderWeightSet() (gas: 259249)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientWeights() (gas: 372223)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientWithNewZeroAddress() (gas: 270758)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsContainsDuplicateRecipients() (gas: 288531)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentLargerSet() (gas: 407832)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentPartialSet() (gas: 317985)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSet() (gas: 377740)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSetWithInvalidWeights() (gas: 312078)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForLargerSet() (gas: 399655)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForSmallerSet() (gas: 289469)
DestinationVerifierBulkVerifyBillingReport:test_verifyWithBulkLink() (gas: 642577)
DestinationVerifierBulkVerifyBillingReport:test_verifyWithBulkNative() (gas: 643680)
DestinationVerifierBulkVerifyBillingReport:test_verifyWithBulkNativeUnwrapped() (gas: 665238)
DestinationVerifierBulkVerifyBillingReport:test_verifyWithBulkNativeUnwrappedReturnsChange() (gas: 665193)
DestinationVerifierConstructorTest:test_falseIfIsNotCorrectInterface() (gas: 8481)
DestinationVerifierConstructorTest:test_revertsIfInitializedWithEmptyVerifierProxy() (gas: 60885)
DestinationVerifierConstructorTest:test_trueIfIsCorrectInterface() (gas: 9361)
DestinationVerifierConstructorTest:test_typeAndVersion() (gas: 2624729)
DestinationVerifierProxyInitializeVerifierTest:test_correctlySetsTheOwner() (gas: 862226)
DestinationVerifierProxyInitializeVerifierTest:test_correctlySetsVersion() (gas: 9841)
DestinationVerifierProxyInitializeVerifierTest:test_setVerifierCalledByNoOwner() (gas: 17483)
DestinationVerifierProxyInitializeVerifierTest:test_setVerifierOk() (gas: 27685)
DestinationVerifierProxyInitializeVerifierTest:test_setVerifierWhichDoesntHonourInterface() (gas: 16535)
DestinationVerifierSetAccessControllerTest:test_emitsTheCorrectEvent() (gas: 35391)
DestinationVerifierSetAccessControllerTest:test_revertsIfCalledByNonOwner() (gas: 15089)
DestinationVerifierSetAccessControllerTest:test_successfullySetsNewAccessController() (gas: 34939)
DestinationVerifierSetAccessControllerTest:test_successfullySetsNewAccessControllerIsEmpty() (gas: 14995)
DestinationVerifierSetConfigTest:test_NoDonConfigAlreadyExists() (gas: 2877761)
DestinationVerifierSetConfigTest:test_addressesAndWeightsDoNotProduceSideEffectsInDonConfigIds() (gas: 1323263)
DestinationVerifierSetConfigTest:test_donConfigIdIsSameForSignersInDifferentOrder() (gas: 1290458)
DestinationVerifierSetConfigTest:test_removeLatestConfig() (gas: 786139)
DestinationVerifierSetConfigTest:test_removeLatestConfigWhenNoConfigShouldFail() (gas: 12870)
DestinationVerifierSetConfigTest:test_revertsIfCalledByNonOwner() (gas: 174936)
DestinationVerifierSetConfigTest:test_revertsIfDuplicateSigners() (gas: 171284)
DestinationVerifierSetConfigTest:test_revertsIfFaultToleranceIsZero() (gas: 168506)
DestinationVerifierSetConfigTest:test_revertsIfNotEnoughSigners() (gas: 11578)
DestinationVerifierSetConfigTest:test_revertsIfSetWithTooManySigners() (gas: 17928)
DestinationVerifierSetConfigTest:test_revertsIfSignerContainsZeroAddress() (gas: 324013)
DestinationVerifierSetConfigTest:test_setConfigActiveUnknownDonConfigId() (gas: 13102)
DestinationVerifierSetConfigTest:test_setConfigWithActivationTime() (gas: 1088137)
DestinationVerifierSetConfigTest:test_setConfigWithActivationTimeEarlierThanLatestConfigShouldFail() (gas: 1963051)
DestinationVerifierSetConfigTest:test_setConfigWithActivationTimeNoFutureTimeShouldFail() (gas: 259477)
DestinationVerifierSetConfigTest:test_setConfigWithActivationTimeTheSameAsLatestConfigShouldFail() (gas: 1283835)
FeeManagerProcessFeeTest:test_DiscountIsAppliedForNative() (gas: 52645)
FeeManagerProcessFeeTest:test_DiscountIsReturnedForNative() (gas: 52595)
FeeManagerProcessFeeTest:test_DiscountIsReturnedForNativeWithSurcharge() (gas: 78830)
FeeManagerProcessFeeTest:test_V1PayloadVerifies() (gas: 26980)
FeeManagerProcessFeeTest:test_V1PayloadVerifiesAndReturnsChange() (gas: 58910)
FeeManagerProcessFeeTest:test_V2PayloadVerifies() (gas: 116756)
FeeManagerProcessFeeTest:test_V2PayloadWithoutQuoteFails() (gas: 27395)
FeeManagerProcessFeeTest:test_V2PayloadWithoutZeroFee() (gas: 70370)
FeeManagerProcessFeeTest:test_WithdrawERC20() (gas: 72682)
FeeManagerProcessFeeTest:test_WithdrawNonAdminAddr() (gas: 56286)
FeeManagerProcessFeeTest:test_WithdrawUnwrappedNative() (gas: 26387)
FeeManagerProcessFeeTest:test_baseFeeIsAppliedForLink() (gas: 17167)
FeeManagerProcessFeeTest:test_baseFeeIsAppliedForNative() (gas: 20150)
FeeManagerProcessFeeTest:test_correctDiscountIsAppliedWhenBothTokensAreDiscounted() (gas: 91033)
FeeManagerProcessFeeTest:test_discountAIsNotAppliedWhenSetForOtherUsers() (gas: 56556)
FeeManagerProcessFeeTest:test_discountFeeRoundsDownWhenUneven() (gas: 52847)
FeeManagerProcessFeeTest:test_discountIsAppliedForLink() (gas: 49658)
FeeManagerProcessFeeTest:test_discountIsAppliedWith100PercentSurcharge() (gas: 78903)
FeeManagerProcessFeeTest:test_discountIsNoLongerAppliedAfterRemoving() (gas: 46511)
FeeManagerProcessFeeTest:test_discountIsNotAppliedForInvalidTokenAddress() (gas: 17560)
FeeManagerProcessFeeTest:test_discountIsNotAppliedToOtherFeeds() (gas: 54604)
FeeManagerProcessFeeTest:test_discountIsReturnedForLink() (gas: 49608)
FeeManagerProcessFeeTest:test_emptyQuoteRevertsWithError() (gas: 12163)
FeeManagerProcessFeeTest:test_eventIsEmittedAfterSurchargeIsSet() (gas: 41356)
FeeManagerProcessFeeTest:test_eventIsEmittedIfNotEnoughLink() (gas: 173762)
FeeManagerProcessFeeTest:test_eventIsEmittedUponWithdraw() (gas: 69009)
FeeManagerProcessFeeTest:test_feeIsUpdatedAfterDiscountIsRemoved() (gas: 49722)
FeeManagerProcessFeeTest:test_feeIsUpdatedAfterNewDiscountIsApplied() (gas: 67699)
FeeManagerProcessFeeTest:test_feeIsUpdatedAfterNewSurchargeIsApplied() (gas: 64324)
FeeManagerProcessFeeTest:test_feeIsZeroWith100PercentDiscount() (gas: 52045)
FeeManagerProcessFeeTest:test_getBaseRewardWithLinkQuote() (gas: 17165)
FeeManagerProcessFeeTest:test_getLinkFeeIsRoundedUp() (gas: 49829)
FeeManagerProcessFeeTest:test_getLinkRewardIsSameAsFee() (gas: 55618)
FeeManagerProcessFeeTest:test_getLinkRewardWithNativeQuoteAndSurchargeWithLinkDiscount() (gas: 82787)
FeeManagerProcessFeeTest:test_getRewardWithLinkDiscount() (gas: 49676)
FeeManagerProcessFeeTest:test_getRewardWithLinkQuoteAndLinkDiscount() (gas: 49657)
FeeManagerProcessFeeTest:test_getRewardWithNativeQuote() (gas: 20148)
FeeManagerProcessFeeTest:test_getRewardWithNativeQuoteAndSurcharge() (gas: 50838)
FeeManagerProcessFeeTest:test_linkAvailableForPaymentReturnsLinkBalance() (gas: 53192)
FeeManagerProcessFeeTest:test_nativeSurcharge0Percent() (gas: 30848)
FeeManagerProcessFeeTest:test_nativeSurcharge100Percent() (gas: 50863)
FeeManagerProcessFeeTest:test_nativeSurchargeCannotExceed100Percent() (gas: 17175)
FeeManagerProcessFeeTest:test_nativeSurchargeEventIsEmittedOnUpdate() (gas: 41402)
FeeManagerProcessFeeTest:test_noFeeIsAppliedWhenReportHasZeroFee() (gas: 51890)
FeeManagerProcessFeeTest:test_noFeeIsAppliedWhenReportHasZeroFeeAndDiscountAndSurchargeIsSet() (gas: 78081)
FeeManagerProcessFeeTest:test_nonAdminProxyUserCannotProcessFee() (gas: 21895)
FeeManagerProcessFeeTest:test_nonAdminUserCanNotSetDiscount() (gas: 19806)
FeeManagerProcessFeeTest:test_payLinkDeficit() (gas: 194434)
FeeManagerProcessFeeTest:test_payLinkDeficitOnlyCallableByAdmin() (gas: 17413)
FeeManagerProcessFeeTest:test_payLinkDeficitPaysAllFeesProcessed() (gas: 214764)
FeeManagerProcessFeeTest:test_payLinkDeficitTwice() (gas: 198808)
FeeManagerProcessFeeTest:test_processFeeAsProxy() (gas: 117094)
FeeManagerProcessFeeTest:test_processFeeDefaultReportsStillVerifiesWithEmptyQuote() (gas: 27468)
FeeManagerProcessFeeTest:test_processFeeEmitsEventIfNotEnoughLink() (gas: 163211)
FeeManagerProcessFeeTest:test_processFeeIfSubscriberIsSelf() (gas: 30333)
FeeManagerProcessFeeTest:test_processFeeNative() (gas: 173832)
FeeManagerProcessFeeTest:test_processFeeUsesCorrectDigest() (gas: 118385)
FeeManagerProcessFeeTest:test_processFeeWithDefaultReportPayloadAndQuoteStillVerifies() (gas: 29542)
FeeManagerProcessFeeTest:test_processFeeWithDiscountEmitsEvent() (gas: 241359)
FeeManagerProcessFeeTest:test_processFeeWithInvalidReportVersionFailsToDecode() (gas: 28517)
FeeManagerProcessFeeTest:test_processFeeWithNoDiscountDoesNotEmitEvent() (gas: 166759)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNative() (gas: 181697)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeLinkAddress() (gas: 131472)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeLinkAddressExcessiveFee() (gas: 157078)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeShortFunds() (gas: 92641)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeWithExcessiveFee() (gas: 188660)
FeeManagerProcessFeeTest:test_processFeeWithWithCorruptQuotePayload() (gas: 70681)
FeeManagerProcessFeeTest:test_processFeeWithWithEmptyQuotePayload() (gas: 27733)
FeeManagerProcessFeeTest:test_processFeeWithWithZeroQuotePayload() (gas: 27783)
FeeManagerProcessFeeTest:test_processFeeWithZeroLinkNonZeroNativeWithLinkQuote() (gas: 32973)
FeeManagerProcessFeeTest:test_processFeeWithZeroLinkNonZeroNativeWithNativeQuote() (gas: 153731)
FeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkReturnsChange() (gas: 53801)
FeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkWithLinkQuote() (gas: 117005)
FeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkWithNativeQuote() (gas: 35744)
FeeManagerProcessFeeTest:test_processMultipleLinkReports() (gas: 223139)
FeeManagerProcessFeeTest:test_processMultipleUnwrappedNativeReports() (gas: 257002)
FeeManagerProcessFeeTest:test_processMultipleV1Reports() (gas: 74799)
FeeManagerProcessFeeTest:test_processMultipleWrappedNativeReports() (gas: 239807)
FeeManagerProcessFeeTest:test_processV1V2V3Reports() (gas: 207930)
FeeManagerProcessFeeTest:test_processV1V2V3ReportsWithUnwrapped() (gas: 249586)
FeeManagerProcessFeeTest:test_reportWithNoExpiryOrFeeReturnsZero() (gas: 13613)
FeeManagerProcessFeeTest:test_setDiscountOver100Percent() (gas: 19562)
FeeManagerProcessFeeTest:test_subscriberDiscountEventIsEmittedOnUpdate() (gas: 46261)
FeeManagerProcessFeeTest:test_surchargeFeeRoundsUpWhenUneven() (gas: 51215)
FeeManagerProcessFeeTest:test_surchargeIsApplied() (gas: 51096)
FeeManagerProcessFeeTest:test_surchargeIsAppliedForNativeFeeWithDiscount() (gas: 79265)
FeeManagerProcessFeeTest:test_surchargeIsNoLongerAppliedAfterRemoving() (gas: 47076)
FeeManagerProcessFeeTest:test_surchargeIsNotAppliedForLinkFee() (gas: 49938)
FeeManagerProcessFeeTest:test_surchargeIsNotAppliedWith100PercentDiscount() (gas: 78261)
FeeManagerProcessFeeTest:test_testRevertIfReportHasExpired() (gas: 14941)
FeeManagerProcessFeeTestV05:test_DiscountIsAppliedForNative() (gas: 52650)
FeeManagerProcessFeeTestV05:test_DiscountIsReturnedForNative() (gas: 52643)
FeeManagerProcessFeeTestV05:test_DiscountIsReturnedForNativeWithSurcharge() (gas: 78900)
FeeManagerProcessFeeTestV05:test_GlobalDiscountCantBeSetToMoreThanMaximum() (gas: 19501)
FeeManagerProcessFeeTestV05:test_GlobalDiscountIsOverridenByIndividualDiscountLink() (gas: 79464)
FeeManagerProcessFeeTestV05:test_GlobalDiscountIsOverridenByIndividualDiscountNative() (gas: 82478)
FeeManagerProcessFeeTestV05:test_GlobalDiscountIsUpdatedAfterBeingSetToZeroLink() (gas: 48284)
FeeManagerProcessFeeTestV05:test_GlobalDiscountIsUpdatedAfterBeingSetToZeroNative() (gas: 51464)
FeeManagerProcessFeeTestV05:test_GlobalDiscountWithLink() (gas: 51813)
FeeManagerProcessFeeTestV05:test_GlobalDiscountWithNative() (gas: 54759)
FeeManagerProcessFeeTestV05:test_GlobalDiscountWithNativeAndLink() (gas: 83629)
FeeManagerProcessFeeTestV05:test_V1PayloadVerifies() (gas: 26980)
FeeManagerProcessFeeTestV05:test_V1PayloadVerifiesAndReturnsChange() (gas: 58936)
FeeManagerProcessFeeTestV05:test_V2PayloadVerifies() (gas: 119040)
FeeManagerProcessFeeTestV05:test_V2PayloadWithoutQuoteFails() (gas: 27395)
FeeManagerProcessFeeTestV05:test_V2PayloadWithoutZeroFee() (gas: 72654)
FeeManagerProcessFeeTestV05:test_WithdrawERC20() (gas: 72726)
FeeManagerProcessFeeTestV05:test_WithdrawNonAdminAddr() (gas: 56309)
FeeManagerProcessFeeTestV05:test_WithdrawUnwrappedNative() (gas: 26463)
FeeManagerProcessFeeTestV05:test_baseFeeIsAppliedForLink() (gas: 19451)
FeeManagerProcessFeeTestV05:test_baseFeeIsAppliedForNative() (gas: 22456)
FeeManagerProcessFeeTestV05:test_correctDiscountIsAppliedWhenBothTokensAreDiscounted() (gas: 91085)
FeeManagerProcessFeeTestV05:test_discountAIsNotAppliedWhenSetForOtherUsers() (gas: 58840)
FeeManagerProcessFeeTestV05:test_discountFeeRoundsDownWhenUneven() (gas: 52850)
FeeManagerProcessFeeTestV05:test_discountIsAppliedForLink() (gas: 49706)
FeeManagerProcessFeeTestV05:test_discountIsAppliedWith100PercentSurcharge() (gas: 78951)
FeeManagerProcessFeeTestV05:test_discountIsNoLongerAppliedAfterRemoving() (gas: 48341)
FeeManagerProcessFeeTestV05:test_discountIsNotAppliedForInvalidTokenAddress() (gas: 17538)
FeeManagerProcessFeeTestV05:test_discountIsNotAppliedToOtherFeeds() (gas: 56844)
FeeManagerProcessFeeTestV05:test_discountIsReturnedForLink() (gas: 49678)
FeeManagerProcessFeeTestV05:test_emptyQuoteRevertsWithError() (gas: 12162)
FeeManagerProcessFeeTestV05:test_eventIsEmittedAfterSurchargeIsSet() (gas: 41378)
FeeManagerProcessFeeTestV05:test_eventIsEmittedIfNotEnoughLink() (gas: 177182)
FeeManagerProcessFeeTestV05:test_eventIsEmittedUponWithdraw() (gas: 69031)
FeeManagerProcessFeeTestV05:test_feeIsUpdatedAfterDiscountIsRemoved() (gas: 51588)
FeeManagerProcessFeeTestV05:test_feeIsUpdatedAfterNewDiscountIsApplied() (gas: 67751)
FeeManagerProcessFeeTestV05:test_feeIsUpdatedAfterNewSurchargeIsApplied() (gas: 66958)
FeeManagerProcessFeeTestV05:test_feeIsZeroWith100PercentDiscount() (gas: 52049)
FeeManagerProcessFeeTestV05:test_getBaseRewardWithLinkQuote() (gas: 19449)
FeeManagerProcessFeeTestV05:test_getLinkFeeIsRoundedUp() (gas: 49855)
FeeManagerProcessFeeTestV05:test_getLinkRewardIsSameAsFee() (gas: 55692)
FeeManagerProcessFeeTestV05:test_getLinkRewardWithNativeQuoteAndSurchargeWithLinkDiscount() (gas: 85071)
FeeManagerProcessFeeTestV05:test_getRewardWithLinkDiscount() (gas: 49702)
FeeManagerProcessFeeTestV05:test_getRewardWithLinkQuoteAndLinkDiscount() (gas: 49683)
FeeManagerProcessFeeTestV05:test_getRewardWithNativeQuote() (gas: 22432)
FeeManagerProcessFeeTestV05:test_getRewardWithNativeQuoteAndSurcharge() (gas: 53166)
FeeManagerProcessFeeTestV05:test_linkAvailableForPaymentReturnsLinkBalance() (gas: 53192)
FeeManagerProcessFeeTestV05:test_nativeSurcharge0Percent() (gas: 33154)
FeeManagerProcessFeeTestV05:test_nativeSurcharge100Percent() (gas: 53191)
FeeManagerProcessFeeTestV05:test_nativeSurchargeCannotExceed100Percent() (gas: 17219)
FeeManagerProcessFeeTestV05:test_nativeSurchargeEventIsEmittedOnUpdate() (gas: 41424)
FeeManagerProcessFeeTestV05:test_noFeeIsAppliedWhenReportHasZeroFee() (gas: 51894)
FeeManagerProcessFeeTestV05:test_noFeeIsAppliedWhenReportHasZeroFeeAndDiscountAndSurchargeIsSet() (gas: 78129)
FeeManagerProcessFeeTestV05:test_nonAdminProxyUserCannotProcessFee() (gas: 21895)
FeeManagerProcessFeeTestV05:test_nonAdminUserCanNotSetDiscount() (gas: 19806)
FeeManagerProcessFeeTestV05:test_onlyOwnerCanSetGlobalDiscount() (gas: 19842)
FeeManagerProcessFeeTestV05:test_payLinkDeficit() (gas: 196279)
FeeManagerProcessFeeTestV05:test_payLinkDeficitOnlyCallableByAdmin() (gas: 17413)
FeeManagerProcessFeeTestV05:test_payLinkDeficitPaysAllFeesProcessed() (gas: 216836)
FeeManagerProcessFeeTestV05:test_payLinkDeficitTwice() (gas: 200635)
FeeManagerProcessFeeTestV05:test_processFeeAsProxy() (gas: 119378)
FeeManagerProcessFeeTestV05:test_processFeeDefaultReportsStillVerifiesWithEmptyQuote() (gas: 27468)
FeeManagerProcessFeeTestV05:test_processFeeEmitsEventIfNotEnoughLink() (gas: 165495)
FeeManagerProcessFeeTestV05:test_processFeeIfSubscriberIsSelf() (gas: 30333)
FeeManagerProcessFeeTestV05:test_processFeeNative() (gas: 176116)
FeeManagerProcessFeeTestV05:test_processFeeUsesCorrectDigest() (gas: 120669)
FeeManagerProcessFeeTestV05:test_processFeeWithDefaultReportPayloadAndQuoteStillVerifies() (gas: 29542)
FeeManagerProcessFeeTestV05:test_processFeeWithDiscountEmitsEvent() (gas: 241463)
FeeManagerProcessFeeTestV05:test_processFeeWithInvalidReportVersionFailsToDecode() (gas: 28517)
FeeManagerProcessFeeTestV05:test_processFeeWithNoDiscountDoesNotEmitEvent() (gas: 169043)
FeeManagerProcessFeeTestV05:test_processFeeWithUnwrappedNative() (gas: 183981)
FeeManagerProcessFeeTestV05:test_processFeeWithUnwrappedNativeLinkAddress() (gas: 133756)
FeeManagerProcessFeeTestV05:test_processFeeWithUnwrappedNativeLinkAddressExcessiveFee() (gas: 159388)
FeeManagerProcessFeeTestV05:test_processFeeWithUnwrappedNativeShortFunds() (gas: 94925)
FeeManagerProcessFeeTestV05:test_processFeeWithUnwrappedNativeWithExcessiveFee() (gas: 190970)
FeeManagerProcessFeeTestV05:test_processFeeWithWithCorruptQuotePayload() (gas: 72965)
FeeManagerProcessFeeTestV05:test_processFeeWithWithEmptyQuotePayload() (gas: 27733)
FeeManagerProcessFeeTestV05:test_processFeeWithWithZeroQuotePayload() (gas: 27783)
FeeManagerProcessFeeTestV05:test_processFeeWithZeroLinkNonZeroNativeWithLinkQuote() (gas: 35257)
FeeManagerProcessFeeTestV05:test_processFeeWithZeroLinkNonZeroNativeWithNativeQuote() (gas: 156015)
FeeManagerProcessFeeTestV05:test_processFeeWithZeroNativeNonZeroLinkReturnsChange() (gas: 56111)
FeeManagerProcessFeeTestV05:test_processFeeWithZeroNativeNonZeroLinkWithLinkQuote() (gas: 119289)
FeeManagerProcessFeeTestV05:test_processFeeWithZeroNativeNonZeroLinkWithNativeQuote() (gas: 38028)
FeeManagerProcessFeeTestV05:test_processMultipleLinkReports() (gas: 226585)
FeeManagerProcessFeeTestV05:test_processMultipleUnwrappedNativeReports() (gas: 260448)
FeeManagerProcessFeeTestV05:test_processMultipleV1Reports() (gas: 74825)
FeeManagerProcessFeeTestV05:test_processMultipleWrappedNativeReports() (gas: 243227)
FeeManagerProcessFeeTestV05:test_processV1V2V3Reports() (gas: 211066)
FeeManagerProcessFeeTestV05:test_processV1V2V3ReportsWithUnwrapped() (gas: 252722)
FeeManagerProcessFeeTestV05:test_reportWithNoExpiryOrFeeReturnsZero() (gas: 13591)
FeeManagerProcessFeeTestV05:test_setDiscountOver100Percent() (gas: 19584)
FeeManagerProcessFeeTestV05:test_subscriberDiscountEventIsEmittedOnUpdate() (gas: 46261)
FeeManagerProcessFeeTestV05:test_surchargeFeeRoundsUpWhenUneven() (gas: 53500)
FeeManagerProcessFeeTestV05:test_surchargeIsApplied() (gas: 53447)
FeeManagerProcessFeeTestV05:test_surchargeIsAppliedForNativeFeeWithDiscount() (gas: 79313)
FeeManagerProcessFeeTestV05:test_surchargeIsNoLongerAppliedAfterRemoving() (gas: 49165)
FeeManagerProcessFeeTestV05:test_surchargeIsNotAppliedForLinkFee() (gas: 52244)
FeeManagerProcessFeeTestV05:test_surchargeIsNotAppliedWith100PercentDiscount() (gas: 78309)
FeeManagerProcessFeeTestV05:test_testRevertIfReportHasExpired() (gas: 14941)
MultiVerifierBillingTests:test_multipleFeeManagersAndVerifiers() (gas: 4598496)
RewardManagerClaimTest:test_claimAllRecipients() (gas: 277131)
RewardManagerClaimTest:test_claimAllRecipients() (gas: 277131)
RewardManagerClaimTest:test_claimMultipleRecipients() (gas: 154341)
RewardManagerClaimTest:test_claimMultipleRecipients() (gas: 154341)
RewardManagerClaimTest:test_claimRewardsWithDuplicatePoolIdsDoesNotPayoutTwice() (gas: 330086)
RewardManagerClaimTest:test_claimRewardsWithDuplicatePoolIdsDoesNotPayoutTwice() (gas: 330086)
RewardManagerClaimTest:test_claimSingleRecipient() (gas: 89024)
RewardManagerClaimTest:test_claimSingleRecipient() (gas: 89024)
RewardManagerClaimTest:test_claimUnevenAmountRoundsDown() (gas: 315289)
RewardManagerClaimTest:test_claimUnevenAmountRoundsDown() (gas: 315289)
RewardManagerClaimTest:test_claimUnregisteredPoolId() (gas: 35123)
RewardManagerClaimTest:test_claimUnregisteredPoolId() (gas: 35123)
RewardManagerClaimTest:test_claimUnregisteredRecipient() (gas: 41182)
RewardManagerClaimTest:test_claimUnregisteredRecipient() (gas: 41182)
RewardManagerClaimTest:test_eventIsEmittedUponClaim() (gas: 86069)
RewardManagerClaimTest:test_eventIsEmittedUponClaim() (gas: 86069)
RewardManagerClaimTest:test_eventIsNotEmittedUponUnsuccessfulClaim() (gas: 25031)
RewardManagerClaimTest:test_eventIsNotEmittedUponUnsuccessfulClaim() (gas: 25031)
RewardManagerClaimTest:test_recipientsClaimMultipleDeposits() (gas: 386675)
RewardManagerClaimTest:test_recipientsClaimMultipleDeposits() (gas: 386675)
RewardManagerClaimTest:test_singleRecipientClaimMultipleDeposits() (gas: 137663)
RewardManagerClaimTest:test_singleRecipientClaimMultipleDeposits() (gas: 137663)
RewardManagerNoRecipientSet:test_claimAllRecipientsAfterRecipientsSet() (gas: 492113)
RewardManagerNoRecipientSet:test_claimAllRecipientsAfterRecipientsSet() (gas: 492113)
RewardManagerPayRecipientsTest:test_addFundsToPoolAsNonOwnerOrFeeManager() (gas: 11451)
RewardManagerPayRecipientsTest:test_addFundsToPoolAsNonOwnerOrFeeManager() (gas: 11451)
RewardManagerPayRecipientsTest:test_addFundsToPoolAsOwner() (gas: 53912)
RewardManagerPayRecipientsTest:test_addFundsToPoolAsOwner() (gas: 53912)
RewardManagerPayRecipientsTest:test_payAllRecipients() (gas: 250840)
RewardManagerPayRecipientsTest:test_payAllRecipients() (gas: 250840)
RewardManagerPayRecipientsTest:test_payAllRecipientsFromNonAdminUser() (gas: 20475)
RewardManagerPayRecipientsTest:test_payAllRecipientsFromNonAdminUser() (gas: 20475)
RewardManagerPayRecipientsTest:test_payAllRecipientsFromRecipientInPool() (gas: 251064)
RewardManagerPayRecipientsTest:test_payAllRecipientsFromRecipientInPool() (gas: 251064)
RewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalInvalidRecipient() (gas: 262268)
RewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalInvalidRecipient() (gas: 262268)
RewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalUnregisteredRecipient() (gas: 265753)
RewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalUnregisteredRecipient() (gas: 265753)
RewardManagerPayRecipientsTest:test_payRecipientWithInvalidPool() (gas: 28869)
RewardManagerPayRecipientsTest:test_payRecipientWithInvalidPool() (gas: 28869)
RewardManagerPayRecipientsTest:test_payRecipientsEmptyRecipientList() (gas: 25335)
RewardManagerPayRecipientsTest:test_payRecipientsEmptyRecipientList() (gas: 25335)
RewardManagerPayRecipientsTest:test_payRecipientsWithInvalidPoolId() (gas: 31397)
RewardManagerPayRecipientsTest:test_payRecipientsWithInvalidPoolId() (gas: 31397)
RewardManagerPayRecipientsTest:test_paySingleRecipient() (gas: 84740)
RewardManagerPayRecipientsTest:test_paySingleRecipient() (gas: 84740)
RewardManagerPayRecipientsTest:test_paySubsetOfRecipientsInPool() (gas: 198455)
RewardManagerPayRecipientsTest:test_paySubsetOfRecipientsInPool() (gas: 198455)
RewardManagerRecipientClaimDifferentWeightsTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 280727)
RewardManagerRecipientClaimDifferentWeightsTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 280727)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsMultiplePools() (gas: 512369)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsMultiplePools() (gas: 512369)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsSinglePool() (gas: 283634)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsSinglePool() (gas: 283634)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimEmptyPoolWhenSecondPoolContainsFunds() (gas: 293418)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimEmptyPoolWhenSecondPoolContainsFunds() (gas: 293418)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsMultiplePools() (gas: 263015)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsMultiplePools() (gas: 263015)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsSinglePool() (gas: 154485)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsSinglePool() (gas: 154485)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleRecipientMultiplePools() (gas: 132623)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleRecipientMultiplePools() (gas: 132623)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleUniqueRecipient() (gas: 106022)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleUniqueRecipient() (gas: 106022)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimUnevenAmountRoundsDown() (gas: 579532)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimUnevenAmountRoundsDown() (gas: 579532)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimUnregisteredRecipient() (gas: 64626)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimUnregisteredRecipient() (gas: 64626)
RewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorAndTotalPoolsEqual() (gas: 13029)
RewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorAndTotalPoolsEqual() (gas: 13029)
RewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorCannotBeGreaterThanTotalPools() (gas: 12724)
RewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorCannotBeGreaterThanTotalPools() (gas: 12724)
RewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorSingleResult() (gas: 22426)
RewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorSingleResult() (gas: 22426)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPools() (gas: 32225)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPools() (gas: 32225)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPoolsWhereAlreadyClaimed() (gas: 148553)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPoolsWhereAlreadyClaimed() (gas: 148553)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInNoPools() (gas: 21683)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInNoPools() (gas: 21683)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInSinglePool() (gas: 27742)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInSinglePool() (gas: 27742)
RewardManagerRecipientClaimMultiplePoolsTest:test_recipientsClaimMultipleDeposits() (gas: 391245)
RewardManagerRecipientClaimMultiplePoolsTest:test_recipientsClaimMultipleDeposits() (gas: 391245)
RewardManagerRecipientClaimMultiplePoolsTest:test_singleRecipientClaimMultipleDeposits() (gas: 137748)
RewardManagerRecipientClaimMultiplePoolsTest:test_singleRecipientClaimMultipleDeposits() (gas: 137748)
RewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 199454)
RewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 199454)
RewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmountWithSmallDeposit() (gas: 219327)
RewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmountWithSmallDeposit() (gas: 219327)
RewardManagerSetRecipientsTest:test_eventIsEmittedUponSetRecipients() (gas: 191707)
RewardManagerSetRecipientsTest:test_eventIsEmittedUponSetRecipients() (gas: 191707)
RewardManagerSetRecipientsTest:test_setRecipientContainsDuplicateRecipients() (gas: 126082)
RewardManagerSetRecipientsTest:test_setRecipientContainsDuplicateRecipients() (gas: 126082)
RewardManagerSetRecipientsTest:test_setRewardRecipientFromManagerAddress() (gas: 193880)
RewardManagerSetRecipientsTest:test_setRewardRecipientFromManagerAddress() (gas: 193880)
RewardManagerSetRecipientsTest:test_setRewardRecipientFromNonOwnerOrFeeManagerAddress() (gas: 21452)
RewardManagerSetRecipientsTest:test_setRewardRecipientFromNonOwnerOrFeeManagerAddress() (gas: 21452)
RewardManagerSetRecipientsTest:test_setRewardRecipientTwice() (gas: 193368)
RewardManagerSetRecipientsTest:test_setRewardRecipientTwice() (gas: 193368)
RewardManagerSetRecipientsTest:test_setRewardRecipientWeights() (gas: 180630)
RewardManagerSetRecipientsTest:test_setRewardRecipientWeights() (gas: 180630)
RewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroAddress() (gas: 90224)
RewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroAddress() (gas: 90224)
RewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroWeight() (gas: 191334)
RewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroWeight() (gas: 191334)
RewardManagerSetRecipientsTest:test_setRewardRecipients() (gas: 185589)
RewardManagerSetRecipientsTest:test_setRewardRecipients() (gas: 185589)
RewardManagerSetRecipientsTest:test_setRewardRecipientsIsEmpty() (gas: 87136)
RewardManagerSetRecipientsTest:test_setRewardRecipientsIsEmpty() (gas: 87136)
RewardManagerSetRecipientsTest:test_setSingleRewardRecipient() (gas: 110371)
RewardManagerSetRecipientsTest:test_setSingleRewardRecipient() (gas: 110371)
RewardManagerSetupTest:test_eventEmittedUponFeeManagerUpdate() (gas: 21366)
RewardManagerSetupTest:test_eventEmittedUponFeeManagerUpdate() (gas: 21366)
RewardManagerSetupTest:test_eventEmittedUponFeePaid() (gas: 259132)
RewardManagerSetupTest:test_eventEmittedUponFeePaid() (gas: 259132)
RewardManagerSetupTest:test_rejectsZeroLinkAddressOnConstruction() (gas: 59411)
RewardManagerSetupTest:test_rejectsZeroLinkAddressOnConstruction() (gas: 59411)
RewardManagerSetupTest:test_setFeeManagerZeroAddress() (gas: 17105)
RewardManagerSetupTest:test_setFeeManagerZeroAddress() (gas: 17105)
RewardManagerUpdateRewardRecipientsMultiplePoolsTest:test_updatePrimaryRecipientWeights() (gas: 376628)
RewardManagerUpdateRewardRecipientsMultiplePoolsTest:test_updatePrimaryRecipientWeights() (gas: 376628)
RewardManagerUpdateRewardRecipientsTest:test_eventIsEmittedUponUpdateRecipients() (gas: 280509)
RewardManagerUpdateRewardRecipientsTest:test_eventIsEmittedUponUpdateRecipients() (gas: 280509)
RewardManagerUpdateRewardRecipientsTest:test_onlyAdminCanUpdateRecipients() (gas: 19749)
RewardManagerUpdateRewardRecipientsTest:test_onlyAdminCanUpdateRecipients() (gas: 19749)
RewardManagerUpdateRewardRecipientsTest:test_partialUpdateRecipientWeights() (gas: 220972)
RewardManagerUpdateRewardRecipientsTest:test_partialUpdateRecipientWeights() (gas: 220972)
RewardManagerUpdateRewardRecipientsTest:test_updateAllRecipientsWithSameAddressAndWeight() (gas: 274309)
RewardManagerUpdateRewardRecipientsTest:test_updateAllRecipientsWithSameAddressAndWeight() (gas: 274309)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsToSubset() (gas: 254232)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsToSubset() (gas: 254232)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithExcessiveWeight() (gas: 259219)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithExcessiveWeight() (gas: 259219)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithSameAddressAndWeight() (gas: 149916)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithSameAddressAndWeight() (gas: 149916)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithUnderWeightSet() (gas: 259293)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithUnderWeightSet() (gas: 259293)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientWeights() (gas: 372109)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientWeights() (gas: 372109)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientWithNewZeroAddress() (gas: 270802)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientWithNewZeroAddress() (gas: 270802)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsContainsDuplicateRecipients() (gas: 288575)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsContainsDuplicateRecipients() (gas: 288575)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentLargerSet() (gas: 407876)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentLargerSet() (gas: 407876)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentPartialSet() (gas: 318029)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentPartialSet() (gas: 318029)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSet() (gas: 377784)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSet() (gas: 377784)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSetWithInvalidWeights() (gas: 312122)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSetWithInvalidWeights() (gas: 312122)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForLargerSet() (gas: 399699)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForLargerSet() (gas: 399699)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForSmallerSet() (gas: 289513)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForSmallerSet() (gas: 289513)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_correctlyRemovesAMiddleDigest() (gas: 27017)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_correctlyRemovesTheFirstDigest() (gas: 27007)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_correctlyUnsetsDigestsInSequence() (gas: 45102)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_revertsIfCalledByNonOwner() (gas: 15016)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_revertsIfRemovingAnEmptyDigest() (gas: 10907)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_revertsIfRemovingAnNonExistentDigest() (gas: 13381)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTestV05:test_revertsIfCalledByNonOwner() (gas: 14977)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTestV05:test_revertsIfRemovingAnEmptyDigest() (gas: 10891)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTestV05:test_revertsIfRemovingAnNonExistentDigest() (gas: 13195)
VerifierActivateConfigTest:test_revertsIfDigestIsEmpty() (gas: 10962)
VerifierActivateConfigTest:test_revertsIfDigestNotSet() (gas: 13394)
VerifierActivateConfigTest:test_revertsIfNotOwner() (gas: 17182)
VerifierActivateConfigTestV05:test_revertsIfDigestIsEmpty() (gas: 10903)
VerifierActivateConfigTestV05:test_revertsIfDigestNotSet() (gas: 13154)
VerifierActivateConfigTestV05:test_revertsIfNotOwner() (gas: 17123)
VerifierActivateConfigWithDeactivatedConfigTest:test_allowsVerification() (gas: 97175)
VerifierActivateConfigWithDeactivatedConfigTestV05:test_allowsVerification() (gas: 94200)
VerifierActivateFeedTest:test_revertsIfNoFeedExistsActivate() (gas: 13179)
VerifierActivateFeedTest:test_revertsIfNoFeedExistsDeactivate() (gas: 13202)
VerifierActivateFeedTest:test_revertsIfNotOwnerActivateFeed() (gas: 17109)
VerifierActivateFeedTest:test_revertsIfNotOwnerDeactivateFeed() (gas: 17164)
VerifierBillingTests:test_rewardsAreDistributedAccordingToWeights() (gas: 1736225)
VerifierBillingTests:test_rewardsAreDistributedAccordingToWeightsMultipleWeigths() (gas: 4468029)
VerifierBillingTests:test_rewardsAreDistributedAccordingToWeightsUsingHistoricalConfigs() (gas: 2106504)
VerifierBillingTests:test_verifyWithLinkV3Report() (gas: 1593640)
VerifierBillingTests:test_verifyWithNativeERC20() (gas: 1467541)
VerifierBillingTests:test_verifyWithNativeUnwrapped() (gas: 1378718)
VerifierBillingTests:test_verifyWithNativeUnwrappedReturnsChange() (gas: 1385742)
VerifierBulkVerifyBillingReport:test_verifyMultiVersions() (gas: 476595)
VerifierBulkVerifyBillingReport:test_verifyMultiVersionsReturnsVerifiedReports() (gas: 474853)
VerifierBulkVerifyBillingReport:test_verifyWithBulkLink() (gas: 557541)
VerifierBulkVerifyBillingReport:test_verifyWithBulkNative() (gas: 560806)
VerifierBulkVerifyBillingReport:test_verifyWithBulkNativeUnwrapped() (gas: 568629)
VerifierBulkVerifyBillingReport:test_verifyWithBulkNativeUnwrappedReturnsChange() (gas: 575635)
VerifierBulkVerifyBillingReportV05:test_verifyMultiVersions() (gas: 473747)
VerifierBulkVerifyBillingReportV05:test_verifyMultiVersionsReturnsVerifiedReports() (gas: 472005)
VerifierBulkVerifyBillingReportV05:test_verifyWithBulkLink() (gas: 556573)
VerifierBulkVerifyBillingReportV05:test_verifyWithBulkNative() (gas: 559838)
VerifierBulkVerifyBillingReportV05:test_verifyWithBulkNativeUnwrapped() (gas: 567661)
VerifierBulkVerifyBillingReportV05:test_verifyWithBulkNativeUnwrappedReturnsChange() (gas: 574763)
VerifierConstructorTest:test_revertsIfInitializedWithEmptyVerifierProxy() (gas: 59960)
VerifierConstructorTest:test_setsTheCorrectProperties() (gas: 1813269)
VerifierConstructorTestV05:test_revertsIfInitializedWithEmptyVerifierProxy() (gas: 59657)
VerifierConstructorTestV05:test_setsTheCorrectProperties() (gas: 1530840)
VerifierDeactivateFeedWithVerifyTest:test_currentReportAllowsVerification() (gas: 192073)
VerifierDeactivateFeedWithVerifyTest:test_currentReportFailsVerification() (gas: 113388)
VerifierDeactivateFeedWithVerifyTest:test_previousReportAllowsVerification() (gas: 99669)
VerifierDeactivateFeedWithVerifyTest:test_previousReportFailsVerification() (gas: 69943)
VerifierInterfacesTest:test_DestinationContractInterfaces() (gas: 628150)
VerifierProxyAccessControlledVerificationTest:test_proxiesToTheVerifierIfHasAccess() (gas: 208507)
VerifierProxyAccessControlledVerificationTest:test_revertsIfNoAccess() (gas: 112323)
VerifierProxyAccessControlledVerificationTestV05:test_proxiesToTheVerifierIfHasAccess() (gas: 206027)
VerifierProxyAccessControlledVerificationTestV05:test_revertsIfNoAccess() (gas: 112323)
VerifierProxyConstructorTest:test_correctlySetsTheCorrectAccessControllerInterface() (gas: 1485337)
VerifierProxyConstructorTest:test_correctlySetsTheCorrectAccessControllerInterface() (gas: 1485337)
VerifierProxyConstructorTest:test_correctlySetsTheOwner() (gas: 1465483)
VerifierProxyConstructorTest:test_correctlySetsTheOwner() (gas: 1465483)
VerifierProxyConstructorTest:test_correctlySetsVersion() (gas: 9767)
VerifierProxyConstructorTest:test_correctlySetsVersion() (gas: 9767)
VerifierProxyInitializeVerifierTest:test_revertsIfDigestAlreadySet() (gas: 54117)
VerifierProxyInitializeVerifierTest:test_revertsIfNotCorrectVerifier() (gas: 13613)
VerifierProxyInitializeVerifierTest:test_revertsIfNotOwner() (gas: 17168)
VerifierProxyInitializeVerifierTest:test_revertsIfVerifierAlreadyInitialized() (gas: 42053)
VerifierProxyInitializeVerifierTest:test_revertsIfZeroAddress() (gas: 10956)
VerifierProxyInitializeVerifierTest:test_setFeeManagerWhichDoesntHonourIERC165Interface() (gas: 13890)
VerifierProxyInitializeVerifierTest:test_setFeeManagerWhichDoesntHonourInterface() (gas: 16290)
VerifierProxyInitializeVerifierTest:test_setFeeManagerZeroAddress() (gas: 10911)
VerifierProxyInitializeVerifierTest:test_updatesVerifierIfVerifier() (gas: 54086)
VerifierProxyInitializeVerifierTestV05:test_revertsIfDigestAlreadySet() (gas: 48384)
VerifierProxyInitializeVerifierTestV05:test_revertsIfNotCorrectVerifier() (gas: 13613)
VerifierProxyInitializeVerifierTestV05:test_revertsIfNotOwner() (gas: 17168)
VerifierProxyInitializeVerifierTestV05:test_revertsIfVerifierAlreadyInitialized() (gas: 42053)
VerifierProxyInitializeVerifierTestV05:test_revertsIfZeroAddress() (gas: 10956)
VerifierProxyInitializeVerifierTestV05:test_setFeeManagerWhichDoesntHonourIERC165Interface() (gas: 13890)
VerifierProxyInitializeVerifierTestV05:test_setFeeManagerWhichDoesntHonourInterface() (gas: 16290)
VerifierProxyInitializeVerifierTestV05:test_setFeeManagerZeroAddress() (gas: 10911)
VerifierProxyInitializeVerifierTestV05:test_updatesVerifierIfVerifier() (gas: 48356)
VerifierProxySetAccessControllerTest:test_emitsTheCorrectEvent() (gas: 35348)
VerifierProxySetAccessControllerTest:test_emitsTheCorrectEvent() (gas: 35348)
VerifierProxySetAccessControllerTest:test_revertsIfCalledByNonOwner() (gas: 15025)
VerifierProxySetAccessControllerTest:test_revertsIfCalledByNonOwner() (gas: 15025)
VerifierProxySetAccessControllerTest:test_successfullySetsNewAccessController() (gas: 34921)
VerifierProxySetAccessControllerTest:test_successfullySetsNewAccessController() (gas: 34921)
VerifierProxySetAccessControllerTest:test_successfullySetsNewAccessControllerIsEmpty() (gas: 15065)
VerifierProxySetAccessControllerTest:test_successfullySetsNewAccessControllerIsEmpty() (gas: 15065)
VerifierProxyUnsetVerifierTest:test_revertsIfDigestDoesNotExist() (gas: 13149)
VerifierProxyUnsetVerifierTest:test_revertsIfDigestDoesNotExist() (gas: 13149)
VerifierProxyUnsetVerifierTest:test_revertsIfNotAdmin() (gas: 14973)
VerifierProxyUnsetVerifierTest:test_revertsIfNotAdmin() (gas: 14973)
VerifierProxyUnsetVerifierWithPreviouslySetVerifierTest:test_correctlyUnsetsVerifier() (gas: 15555)
VerifierProxyUnsetVerifierWithPreviouslySetVerifierTest:test_correctlyUnsetsVerifier() (gas: 15555)
VerifierProxyUnsetVerifierWithPreviouslySetVerifierTest:test_emitsAnEventAfterUnsettingVerifier() (gas: 17985)
VerifierProxyUnsetVerifierWithPreviouslySetVerifierTest:test_emitsAnEventAfterUnsettingVerifier() (gas: 17985)
VerifierProxyVerifyTest:test_proxiesToTheCorrectVerifier() (gas: 204342)
VerifierProxyVerifyTest:test_revertsIfNoVerifierConfigured() (gas: 117331)
VerifierProxyVerifyTestV05:test_proxiesToTheCorrectVerifier() (gas: 201862)
VerifierProxyVerifyTestV05:test_revertsIfNoVerifierConfigured() (gas: 117331)
VerifierSetAccessControllerTest:test_revertsIfCalledByNonOwner() (gas: 17152)
VerifierSetAccessControllerTest:test_setFeeManagerWhichDoesntHonourInterface() (gas: 16272)
VerifierSetAccessControllerTest:test_successfullySetsNewFeeManager() (gas: 42214)
VerifierSetConfigFromSourceMultipleDigestsTest:test_correctlySetsConfigWhenDigestsAreRemoved() (gas: 542280)
VerifierSetConfigFromSourceMultipleDigestsTest:test_correctlyUpdatesDigestsOnMultipleVerifiersInTheProxy() (gas: 967835)
VerifierSetConfigFromSourceMultipleDigestsTest:test_correctlyUpdatesTheDigestInTheProxy() (gas: 523229)
VerifierSetConfigFromSourceTest:test_revertsIfCalledByNonOwner() (gas: 183217)
VerifierSetConfigTest:test_correctlyUpdatesTheConfig() (gas: 1062416)
VerifierSetConfigTest:test_revertsIfCalledByNonOwner() (gas: 182986)
VerifierSetConfigTest:test_revertsIfDuplicateSigners() (gas: 251595)
VerifierSetConfigTest:test_revertsIfFaultToleranceIsZero() (gas: 176543)
VerifierSetConfigTest:test_revertsIfNotEnoughSigners() (gas: 15842)
VerifierSetConfigTest:test_revertsIfSetWithTooManySigners() (gas: 22182)
VerifierSetConfigTest:test_revertsIfSignerContainsZeroAddress() (gas: 228025)
VerifierSetConfigTestV05:test_correctlyUpdatesTheConfig() (gas: 1022171)
VerifierSetConfigTestV05:test_revertsIfCalledByNonOwner() (gas: 213898)
VerifierSetConfigTestV05:test_revertsIfDuplicateSigners() (gas: 243789)
VerifierSetConfigTestV05:test_revertsIfFaultToleranceIsZero() (gas: 207455)
VerifierSetConfigTestV05:test_revertsIfNotEnoughSigners() (gas: 16407)
VerifierSetConfigTestV05:test_revertsIfSetWithTooManySigners() (gas: 41461)
VerifierSetConfigTestV05:test_revertsIfSignerContainsZeroAddress() (gas: 220308)
VerifierSetConfigWhenThereAreMultipleDigestsTest05:test_configDigestMatchesConfiguratorDigest() (gas: 584372)
VerifierSetConfigWhenThereAreMultipleDigestsTest05:test_correctlySetsConfigWhenDigestsAreRemoved() (gas: 527267)
VerifierSetConfigWhenThereAreMultipleDigestsTest05:test_correctlyUpdatesDigestsOnMultipleVerifiersInTheProxy() (gas: 961409)
VerifierSetConfigWhenThereAreMultipleDigestsTest05:test_correctlyUpdatesTheDigestInTheProxy() (gas: 519967)
VerifierSetConfigWhenThereAreMultipleDigestsTest05:test_incrementalConfigUpdates() (gas: 1424440)
VerifierSetConfigWhenThereAreMultipleDigestsTest05:test_revertsIfDuplicateConfigIsSet() (gas: 545708)
VerifierSetConfigWhenThereAreMultipleDigestsTest:test_correctlySetsConfigWhenDigestsAreRemoved() (gas: 542029)
VerifierSetConfigWhenThereAreMultipleDigestsTest:test_correctlyUpdatesDigestsOnMultipleVerifiersInTheProxy() (gas: 967324)
VerifierSetConfigWhenThereAreMultipleDigestsTest:test_correctlyUpdatesTheDigestInTheProxy() (gas: 522969)
VerifierSupportsInterfaceTest:test_falseIfIsNotCorrectInterface() (gas: 8421)
VerifierSupportsInterfaceTest:test_falseIfIsNotCorrectInterface() (gas: 8421)
VerifierSupportsInterfaceTest:test_trueIfIsCorrectInterface() (gas: 8464)
VerifierSupportsInterfaceTest:test_trueIfIsCorrectInterface() (gas: 8464)
VerifierTestBillingReport:test_verifyWithLink() (gas: 275293)
VerifierTestBillingReport:test_verifyWithNative() (gas: 316326)
VerifierTestBillingReport:test_verifyWithNativeUnwrapped() (gas: 318619)
VerifierTestBillingReport:test_verifyWithNativeUnwrappedReturnsChange() (gas: 325642)
VerifierTestBillingReportV05:test_verifyWithLink() (gas: 275097)
VerifierTestBillingReportV05:test_verifyWithNative() (gas: 316130)
VerifierTestBillingReportV05:test_verifyWithNativeUnwrapped() (gas: 318423)
VerifierTestBillingReportV05:test_verifyWithNativeUnwrappedReturnsChange() (gas: 325542)
VerifierUpdateConfigTest:test_updateConfig() (gas: 673580)
VerifierUpdateConfigTest:test_updateConfigRevertsIfCalledByNonOwner() (gas: 505312)
VerifierUpdateConfigTest:test_updateConfigRevertsIfDigestNotSet() (gas: 92340)
VerifierUpdateConfigTest:test_updateConfigRevertsIfFIsZero() (gas: 500500)
VerifierUpdateConfigTest:test_updateConfigRevertsIfFTooHigh() (gas: 501433)
VerifierUpdateConfigTest:test_updateConfigRevertsIfPrevSignersLengthMismatch() (gas: 526466)
VerifierUpdateConfigTest:test_updateConfigWithDifferentSigners() (gas: 803519)
VerifierVerifyBulkTest:test_revertsVerifyBulkIfNoAccess() (gas: 112855)
VerifierVerifyBulkTest:test_verifyBulkSingleCaseWithSingleConfig() (gas: 745006)
VerifierVerifyBulkTest:test_verifyBulkWithSingleConfigOneVerifyFails() (gas: 698163)
VerifierVerifyMultipleConfigDigestTest:test_canVerifyNewerReportsWithNewerConfigs() (gas: 133961)
VerifierVerifyMultipleConfigDigestTest:test_canVerifyOlderReportsWithOlderConfigs() (gas: 189843)
VerifierVerifyMultipleConfigDigestTest:test_revertsIfAReportIsVerifiedWithAnExistingButIncorrectDigest() (gas: 88216)
VerifierVerifyMultipleConfigDigestTest:test_revertsIfVerifyingWithAnUnsetDigest() (gas: 128073)
VerifierVerifyMultipleConfigDigestTestV05:test_canVerifyNewerReportsWithNewerConfigs() (gas: 131504)
VerifierVerifyMultipleConfigDigestTestV05:test_canVerifyOlderReportsWithOlderConfigs() (gas: 187363)
VerifierVerifyMultipleConfigDigestTestV05:test_revertsIfAReportIsVerifiedWithAnExistingButIncorrectDigest() (gas: 85898)
VerifierVerifyMultipleConfigDigestTestV05:test_revertsIfVerifyingWithAnUnsetDigest() (gas: 123076)
VerifierVerifyMultipleConfigDigestTestV05:test_verifyAfterConfigUpdate() (gas: 906142)
VerifierVerifyMultipleConfigDigestTestV05:test_verifyAfterConfigUpdateWithExistingSigners() (gas: 745636)
VerifierVerifySingleConfigDigestTest:test_emitsAnEventIfReportVerified() (gas: 186934)
VerifierVerifySingleConfigDigestTest:test_returnsThePriceAndBlockNumIfReportVerified() (gas: 189825)
VerifierVerifySingleConfigDigestTest:test_revertsIfConfigDigestNotSet() (gas: 116162)
VerifierVerifySingleConfigDigestTest:test_revertsIfDuplicateSignersHaveSigned() (gas: 182371)
VerifierVerifySingleConfigDigestTest:test_revertsIfMismatchedSignatureLengthV03() (gas: 53108)
VerifierVerifySingleConfigDigestTest:test_revertsIfReportHasUnconfiguredFeedID() (gas: 103987)
VerifierVerifySingleConfigDigestTest:test_revertsIfVerifiedByNonProxy() (gas: 100992)
VerifierVerifySingleConfigDigestTest:test_revertsIfVerifiedWithIncorrectAddresses() (gas: 184100)
VerifierVerifySingleConfigDigestTest:test_revertsIfWrongNumberOfSigners() (gas: 110042)
VerifierVerifySingleConfigDigestTest:test_setsTheCorrectEpoch() (gas: 194615)
VerifierVerifySingleConfigDigestTestV05:test_emitsAnEventIfReportVerified() (gas: 184487)
VerifierVerifySingleConfigDigestTestV05:test_returnsThePriceAndBlockNumIfReportVerified() (gas: 187378)
VerifierVerifySingleConfigDigestTestV05:test_revertsIfConfigDigestNotSet() (gas: 113700)
VerifierVerifySingleConfigDigestTestV05:test_revertsIfDuplicateSignersHaveSigned() (gas: 179780)
VerifierVerifySingleConfigDigestTestV05:test_revertsIfMismatchedSignatureLength() (gas: 50778)
VerifierVerifySingleConfigDigestTestV05:test_revertsIfReportHasUnconfiguredConfigDigest() (gas: 104583)
VerifierVerifySingleConfigDigestTestV05:test_revertsIfVerifiedByNonProxy() (gas: 100947)
VerifierVerifySingleConfigDigestTestV05:test_revertsIfVerifiedWithIncorrectAddresses() (gas: 181509)
VerifierVerifySingleConfigDigestTestV05:test_revertsIfWrongNumberOfSigners() (gas: 107712)
VerifierVerifyTest:test_canVerifyNewerReportsWithNewerConfigs() (gas: 862947)
VerifierVerifyTest:test_canVerifyOlderV3ReportsWithOlderConfigs() (gas: 815914)
VerifierVerifyTest:test_failToVerifyReportIfDupSigners() (gas: 450740)
VerifierVerifyTest:test_failToVerifyReportIfNoSigners() (gas: 426452)
VerifierVerifyTest:test_failToVerifyReportIfNotEnoughSigners() (gas: 434774)
VerifierVerifyTest:test_failToVerifyReportIfSignerNotInConfig() (gas: 456826)
VerifierVerifyTest:test_revertsVerifyIfNoAccess() (gas: 109453)
VerifierVerifyTest:test_rollingOutConfiguration() (gas: 1497118)
VerifierVerifyTest:test_scenarioRollingNewChainWithHistoricConfigs() (gas: 976048)
VerifierVerifyTest:test_verifyFailsWhenReportIsOlderThanConfig() (gas: 2303309)
VerifierVerifyTest:test_verifyReport() (gas: 1434779)
VerifierVerifyTest:test_verifyTooglingActiveFlagsDonConfigs() (gas: 1918758)
Verifier_accessControlledVerify:testVerifyWithAccessControl_gas() (gas: 212077)
Verifier_accessControlledVerifyV05:testVerifyWithAccessControl_gas() (gas: 209597)
Verifier_bulkVerifyWithFee:testBulkVerifyProxyWithLinkFeeSuccess_gas() (gas: 519389)
Verifier_bulkVerifyWithFee:testBulkVerifyProxyWithNativeFeeSuccess_gas() (gas: 542808)
Verifier_bulkVerifyWithFeeV05:testBulkVerifyProxyWithLinkFeeSuccess_gas() (gas: 518413)
Verifier_bulkVerifyWithFeeV05:testBulkVerifyProxyWithNativeFeeSuccess_gas() (gas: 541832)
Verifier_setConfig:testSetConfigSuccess_gas() (gas: 922625)
Verifier_setConfigV05:testSetConfigSuccess_gas() (gas: 895165)
Verifier_verify:testVerifyProxySuccess_gas() (gas: 198742)
Verifier_verify:testVerifySuccess_gas() (gas: 186736)
Verifier_verifyV05:testVerifyProxySuccess_gas() (gas: 196262)
Verifier_verifyV05:testVerifySuccess_gas() (gas: 184256)
Verifier_verifyWithFee:testVerifyProxyWithLinkFeeSuccess_gas() (gas: 238899)
Verifier_verifyWithFee:testVerifyProxyWithNativeFeeSuccess_gas() (gas: 257399)
Verifier_verifyWithFeeV05:testVerifyProxyWithLinkFeeSuccess_gas() (gas: 238703)
Verifier_verifyWithFeeV05:testVerifyProxyWithNativeFeeSuccess_gas() (gas: 257203)