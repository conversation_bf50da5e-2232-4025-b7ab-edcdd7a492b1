## Foundry

Chainlink supports Foundry testing for Solidity based on the Solidity version.

- < 0.8.0 Hardhat tests only
- 0.8.x Hardhat and Foundry tests
- \> 0.9.0 Foundry tests only

New projects should use Foundry but existing ones can keep using Hardhat if they want to; we will not be forcing any teams to rewrite their existing test suite.

## Installation

Please see the [official docs](https://book.getfoundry.sh/getting-started/installation) for the latest installation guide

## CI

The Foundry CI will build, run tests and check the gas usage of all test functions against the version controlled `.gas-snapshot` file.
This snapshot should always be up-to-date and can be generated by running `make snapshot`, and alias for `forge snapshot --match-test _gas`.
We only track gas for tests that have the `_gas` suffix as to not introduce too much noise in the snapshot file.
This will create a snapshot file in the current working dir, please make sure that you are in the root `chainlink` directory.

## Configuration

Foundry config lives in `foundry.toml` in the root, the docs can be found [here](https://book.getfoundry.sh/config/).
The remapping of libraries, both from Foundry and NPM packages, is done in `remapping.txt`.


