WorkflowRegistry.pauseWorkflow
├── when the registry is locked
│   └── it should revert
└── when the registry is not locked
    ├── when the caller is not the workflow owner
    │   └── it should revert
    └── when the caller is the workflow owner
        ├── when the workflow is already paused
        │   └── it should revert
        └── when the workflow is active
            ├── when the donID is not allowed
            │   ├── it should pause the workflow for an authorized address and emit {WorkflowPausedV1}
            │   └── it should pause the workflow for an unauthorized address and emit {WorkflowPausedV1}
            └── when the donID is allowed
                ├── it should pause the workflow for an authorized address and emit {WorkflowPausedV1}
                └── it should pause the workflow for an unauthorized address and emit {WorkflowPausedV1}
