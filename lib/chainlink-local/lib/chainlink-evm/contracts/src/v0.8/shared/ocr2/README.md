# OCR2 Rapid Prototype Contracts

The contracts in this directory are to aid rapid prototyping of OCR2 based products. They abstract OCR2 config and boilerplate code so that specific logic can be implemented and tested quickly. They are not optimized or audited.

Do not use these contracts in production. For actual production contracts, it is expected that most of the logic of these contracts will be folded directly into the application contract.