// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.
package mock_ethusd_aggregator_wrapper

import (
	"context"
	"crypto/rand"
	"fmt"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/zksync-sdk/zksync2-go/accounts"
	"github.com/zksync-sdk/zksync2-go/clients"
	"github.com/zksync-sdk/zksync2-go/types"
)

func DeployMockETHUSDAggregatorZk(deployOpts *accounts.TransactOpts, client *clients.Client, wallet *accounts.Wallet, backend bind.ContractBackend, args ...interface{}) (common.Address, *types.Receipt, *MockETHUSDAggregator, error) {
	var calldata []byte
	if len(args) > 0 {
		abi, err := MockETHUSDAggregatorMetaData.GetAbi()
		if err != nil {
			return common.Address{}, nil, nil, err
		}
		calldata, err = abi.Pack("", args...)
		if err != nil {
			return common.Address{}, nil, nil, err
		}
	}

	salt := make([]byte, 32)
	n, err := rand.Read(salt)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if n != len(salt) {
		return common.Address{}, nil, nil, fmt.Errorf("failed to read random bytes: expected %d, got %d", len(salt), n)
	}

	txHash, err := wallet.Deploy(deployOpts, accounts.Create2Transaction{
		Bytecode: ZkBytecode,
		Calldata: calldata,
		Salt:     salt,
	})
	if err != nil {
		return common.Address{}, nil, nil, err
	}

	receipt, err := client.WaitMined(context.Background(), txHash)
	if err != nil {
		return common.Address{}, nil, nil, err
	}

	address := receipt.ContractAddress
	contract, err := NewMockETHUSDAggregator(address, backend)
	if err != nil {
		return common.Address{}, nil, nil, err
	}

	return address, receipt, contract, nil
}

var ZkBytecode = common.Hex2Bytes("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")
