// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package vrfv2plus_wrapper_arbitrum

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
	"github.com/smartcontractkit/chainlink-evm/gethwrappers/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

var VRFV2PlusWrapperArbitrumMetaData = &bind.MetaData{
	ABI: "[{\"type\":\"constructor\",\"inputs\":[{\"name\":\"_link\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_linkNativeFeed\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_coordinator\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_subId\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"SUBSCRIPTION_ID\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"acceptOwnership\",\"inputs\":[],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"calculateRequestPrice\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"calculateRequestPriceNative\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"checkPaymentMode\",\"inputs\":[{\"name\":\"extraArgs\",\"type\":\"bytes\",\"internalType\":\"bytes\"},{\"name\":\"isLinkMode\",\"type\":\"bool\",\"internalType\":\"bool\"}],\"outputs\":[],\"stateMutability\":\"pure\"},{\"type\":\"function\",\"name\":\"disable\",\"inputs\":[],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"enable\",\"inputs\":[],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"estimateRequestPrice\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_requestGasPriceWei\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"estimateRequestPriceNative\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_requestGasPriceWei\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"getConfig\",\"inputs\":[],\"outputs\":[{\"name\":\"fallbackWeiPerUnitLink\",\"type\":\"int256\",\"internalType\":\"int256\"},{\"name\":\"stalenessSeconds\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"fulfillmentFlatFeeNativePPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"fulfillmentFlatFeeLinkDiscountPPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"wrapperGasOverhead\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadNative\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadLink\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadPerWord\",\"type\":\"uint16\",\"internalType\":\"uint16\"},{\"name\":\"wrapperNativePremiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"wrapperLinkPremiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"keyHash\",\"type\":\"bytes32\",\"internalType\":\"bytes32\"},{\"name\":\"maxNumWords\",\"type\":\"uint8\",\"internalType\":\"uint8\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"lastRequestId\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"link\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"linkNativeFeed\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"onTokenTransfer\",\"inputs\":[{\"name\":\"_sender\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_amount\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"_data\",\"type\":\"bytes\",\"internalType\":\"bytes\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"owner\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"rawFulfillRandomWords\",\"inputs\":[{\"name\":\"requestId\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"randomWords\",\"type\":\"uint256[]\",\"internalType\":\"uint256[]\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"requestRandomWordsInNative\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_requestConfirmations\",\"type\":\"uint16\",\"internalType\":\"uint16\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"extraArgs\",\"type\":\"bytes\",\"internalType\":\"bytes\"}],\"outputs\":[{\"name\":\"requestId\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"payable\"},{\"type\":\"function\",\"name\":\"s_callbacks\",\"inputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[{\"name\":\"callbackAddress\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"requestGasPrice\",\"type\":\"uint64\",\"internalType\":\"uint64\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"s_configured\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"bool\",\"internalType\":\"bool\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"s_disabled\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"bool\",\"internalType\":\"bool\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"s_fulfillmentTxSizeBytes\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"s_vrfCoordinator\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"contractIVRFCoordinatorV2Plus\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"setConfig\",\"inputs\":[{\"name\":\"_wrapperGasOverhead\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_coordinatorGasOverheadNative\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_coordinatorGasOverheadLink\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_coordinatorGasOverheadPerWord\",\"type\":\"uint16\",\"internalType\":\"uint16\"},{\"name\":\"_coordinatorNativePremiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"_coordinatorLinkPremiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"_keyHash\",\"type\":\"bytes32\",\"internalType\":\"bytes32\"},{\"name\":\"_maxNumWords\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"_stalenessSeconds\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_fallbackWeiPerUnitLink\",\"type\":\"int256\",\"internalType\":\"int256\"},{\"name\":\"_fulfillmentFlatFeeNativePPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_fulfillmentFlatFeeLinkDiscountPPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"setCoordinator\",\"inputs\":[{\"name\":\"_vrfCoordinator\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"setFulfillmentTxSize\",\"inputs\":[{\"name\":\"_size\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"transferOwnership\",\"inputs\":[{\"name\":\"to\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"typeAndVersion\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"string\",\"internalType\":\"string\"}],\"stateMutability\":\"pure\"},{\"type\":\"function\",\"name\":\"withdraw\",\"inputs\":[{\"name\":\"_recipient\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"withdrawNative\",\"inputs\":[{\"name\":\"_recipient\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"event\",\"name\":\"ConfigSet\",\"inputs\":[{\"name\":\"wrapperGasOverhead\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadNative\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadLink\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadPerWord\",\"type\":\"uint16\",\"indexed\":false,\"internalType\":\"uint16\"},{\"name\":\"coordinatorNativePremiumPercentage\",\"type\":\"uint8\",\"indexed\":false,\"internalType\":\"uint8\"},{\"name\":\"coordinatorLinkPremiumPercentage\",\"type\":\"uint8\",\"indexed\":false,\"internalType\":\"uint8\"},{\"name\":\"keyHash\",\"type\":\"bytes32\",\"indexed\":false,\"internalType\":\"bytes32\"},{\"name\":\"maxNumWords\",\"type\":\"uint8\",\"indexed\":false,\"internalType\":\"uint8\"},{\"name\":\"stalenessSeconds\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"fallbackWeiPerUnitLink\",\"type\":\"int256\",\"indexed\":false,\"internalType\":\"int256\"},{\"name\":\"fulfillmentFlatFeeNativePPM\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"fulfillmentFlatFeeLinkDiscountPPM\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"CoordinatorSet\",\"inputs\":[{\"name\":\"vrfCoordinator\",\"type\":\"address\",\"indexed\":false,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"Disabled\",\"inputs\":[],\"anonymous\":false},{\"type\":\"event\",\"name\":\"Enabled\",\"inputs\":[],\"anonymous\":false},{\"type\":\"event\",\"name\":\"FallbackWeiPerUnitLinkUsed\",\"inputs\":[{\"name\":\"requestId\",\"type\":\"uint256\",\"indexed\":false,\"internalType\":\"uint256\"},{\"name\":\"fallbackWeiPerUnitLink\",\"type\":\"int256\",\"indexed\":false,\"internalType\":\"int256\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"FulfillmentTxSizeSet\",\"inputs\":[{\"name\":\"size\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"NativeWithdrawn\",\"inputs\":[{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"amount\",\"type\":\"uint256\",\"indexed\":false,\"internalType\":\"uint256\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"OwnershipTransferRequested\",\"inputs\":[{\"name\":\"from\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"OwnershipTransferred\",\"inputs\":[{\"name\":\"from\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"Withdrawn\",\"inputs\":[{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"amount\",\"type\":\"uint256\",\"indexed\":false,\"internalType\":\"uint256\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"WrapperFulfillmentFailed\",\"inputs\":[{\"name\":\"requestId\",\"type\":\"uint256\",\"indexed\":true,\"internalType\":\"uint256\"},{\"name\":\"consumer\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"error\",\"name\":\"FailedToTransferLink\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"IncorrectExtraArgsLength\",\"inputs\":[{\"name\":\"expectedMinimumLength\",\"type\":\"uint16\",\"internalType\":\"uint16\"},{\"name\":\"actualLength\",\"type\":\"uint16\",\"internalType\":\"uint16\"}]},{\"type\":\"error\",\"name\":\"InvalidPremiumPercentage\",\"inputs\":[{\"name\":\"premiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"max\",\"type\":\"uint8\",\"internalType\":\"uint8\"}]},{\"type\":\"error\",\"name\":\"LINKPaymentInRequestRandomWordsInNative\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"LinkAlreadySet\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"LinkDiscountTooHigh\",\"inputs\":[{\"name\":\"flatFeeLinkDiscountPPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"flatFeeNativePPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"}]},{\"type\":\"error\",\"name\":\"NativePaymentInOnTokenTransfer\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"OnlyCoordinatorCanFulfill\",\"inputs\":[{\"name\":\"have\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"want\",\"type\":\"address\",\"internalType\":\"address\"}]},{\"type\":\"error\",\"name\":\"OnlyOwnerOrCoordinator\",\"inputs\":[{\"name\":\"have\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"owner\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"coordinator\",\"type\":\"address\",\"internalType\":\"address\"}]},{\"type\":\"error\",\"name\":\"SubscriptionIdMissing\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"ZeroAddress\",\"inputs\":[]}]",
	Bin: "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",
}

var VRFV2PlusWrapperArbitrumABI = VRFV2PlusWrapperArbitrumMetaData.ABI

var VRFV2PlusWrapperArbitrumBin = VRFV2PlusWrapperArbitrumMetaData.Bin

func DeployVRFV2PlusWrapperArbitrum(auth *bind.TransactOpts, backend bind.ContractBackend, _link common.Address, _linkNativeFeed common.Address, _coordinator common.Address, _subId *big.Int) (common.Address, *types.Transaction, *VRFV2PlusWrapperArbitrum, error) {
	parsed, err := VRFV2PlusWrapperArbitrumMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(VRFV2PlusWrapperArbitrumBin), backend, _link, _linkNativeFeed, _coordinator, _subId)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &VRFV2PlusWrapperArbitrum{address: address, abi: *parsed, VRFV2PlusWrapperArbitrumCaller: VRFV2PlusWrapperArbitrumCaller{contract: contract}, VRFV2PlusWrapperArbitrumTransactor: VRFV2PlusWrapperArbitrumTransactor{contract: contract}, VRFV2PlusWrapperArbitrumFilterer: VRFV2PlusWrapperArbitrumFilterer{contract: contract}}, nil
}

type VRFV2PlusWrapperArbitrum struct {
	address common.Address
	abi     abi.ABI
	VRFV2PlusWrapperArbitrumCaller
	VRFV2PlusWrapperArbitrumTransactor
	VRFV2PlusWrapperArbitrumFilterer
}

type VRFV2PlusWrapperArbitrumCaller struct {
	contract *bind.BoundContract
}

type VRFV2PlusWrapperArbitrumTransactor struct {
	contract *bind.BoundContract
}

type VRFV2PlusWrapperArbitrumFilterer struct {
	contract *bind.BoundContract
}

type VRFV2PlusWrapperArbitrumSession struct {
	Contract     *VRFV2PlusWrapperArbitrum
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type VRFV2PlusWrapperArbitrumCallerSession struct {
	Contract *VRFV2PlusWrapperArbitrumCaller
	CallOpts bind.CallOpts
}

type VRFV2PlusWrapperArbitrumTransactorSession struct {
	Contract     *VRFV2PlusWrapperArbitrumTransactor
	TransactOpts bind.TransactOpts
}

type VRFV2PlusWrapperArbitrumRaw struct {
	Contract *VRFV2PlusWrapperArbitrum
}

type VRFV2PlusWrapperArbitrumCallerRaw struct {
	Contract *VRFV2PlusWrapperArbitrumCaller
}

type VRFV2PlusWrapperArbitrumTransactorRaw struct {
	Contract *VRFV2PlusWrapperArbitrumTransactor
}

func NewVRFV2PlusWrapperArbitrum(address common.Address, backend bind.ContractBackend) (*VRFV2PlusWrapperArbitrum, error) {
	abi, err := abi.JSON(strings.NewReader(VRFV2PlusWrapperArbitrumABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindVRFV2PlusWrapperArbitrum(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrum{address: address, abi: abi, VRFV2PlusWrapperArbitrumCaller: VRFV2PlusWrapperArbitrumCaller{contract: contract}, VRFV2PlusWrapperArbitrumTransactor: VRFV2PlusWrapperArbitrumTransactor{contract: contract}, VRFV2PlusWrapperArbitrumFilterer: VRFV2PlusWrapperArbitrumFilterer{contract: contract}}, nil
}

func NewVRFV2PlusWrapperArbitrumCaller(address common.Address, caller bind.ContractCaller) (*VRFV2PlusWrapperArbitrumCaller, error) {
	contract, err := bindVRFV2PlusWrapperArbitrum(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumCaller{contract: contract}, nil
}

func NewVRFV2PlusWrapperArbitrumTransactor(address common.Address, transactor bind.ContractTransactor) (*VRFV2PlusWrapperArbitrumTransactor, error) {
	contract, err := bindVRFV2PlusWrapperArbitrum(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumTransactor{contract: contract}, nil
}

func NewVRFV2PlusWrapperArbitrumFilterer(address common.Address, filterer bind.ContractFilterer) (*VRFV2PlusWrapperArbitrumFilterer, error) {
	contract, err := bindVRFV2PlusWrapperArbitrum(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumFilterer{contract: contract}, nil
}

func bindVRFV2PlusWrapperArbitrum(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := VRFV2PlusWrapperArbitrumMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _VRFV2PlusWrapperArbitrum.Contract.VRFV2PlusWrapperArbitrumCaller.contract.Call(opts, result, method, params...)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.VRFV2PlusWrapperArbitrumTransactor.contract.Transfer(opts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.VRFV2PlusWrapperArbitrumTransactor.contract.Transact(opts, method, params...)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _VRFV2PlusWrapperArbitrum.Contract.contract.Call(opts, result, method, params...)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.contract.Transfer(opts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.contract.Transact(opts, method, params...)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) SUBSCRIPTIONID(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "SUBSCRIPTION_ID")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) SUBSCRIPTIONID() (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SUBSCRIPTIONID(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) SUBSCRIPTIONID() (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SUBSCRIPTIONID(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) CalculateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "calculateRequestPrice", _callbackGasLimit, _numWords)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) CalculateRequestPrice(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.CalculateRequestPrice(&_VRFV2PlusWrapperArbitrum.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) CalculateRequestPrice(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.CalculateRequestPrice(&_VRFV2PlusWrapperArbitrum.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) CalculateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "calculateRequestPriceNative", _callbackGasLimit, _numWords)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) CalculateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.CalculateRequestPriceNative(&_VRFV2PlusWrapperArbitrum.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) CalculateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.CalculateRequestPriceNative(&_VRFV2PlusWrapperArbitrum.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) CheckPaymentMode(opts *bind.CallOpts, extraArgs []byte, isLinkMode bool) error {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "checkPaymentMode", extraArgs, isLinkMode)

	if err != nil {
		return err
	}

	return err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) CheckPaymentMode(extraArgs []byte, isLinkMode bool) error {
	return _VRFV2PlusWrapperArbitrum.Contract.CheckPaymentMode(&_VRFV2PlusWrapperArbitrum.CallOpts, extraArgs, isLinkMode)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) CheckPaymentMode(extraArgs []byte, isLinkMode bool) error {
	return _VRFV2PlusWrapperArbitrum.Contract.CheckPaymentMode(&_VRFV2PlusWrapperArbitrum.CallOpts, extraArgs, isLinkMode)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) EstimateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "estimateRequestPrice", _callbackGasLimit, _numWords, _requestGasPriceWei)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) EstimateRequestPrice(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.EstimateRequestPrice(&_VRFV2PlusWrapperArbitrum.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) EstimateRequestPrice(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.EstimateRequestPrice(&_VRFV2PlusWrapperArbitrum.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) EstimateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "estimateRequestPriceNative", _callbackGasLimit, _numWords, _requestGasPriceWei)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) EstimateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.EstimateRequestPriceNative(&_VRFV2PlusWrapperArbitrum.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) EstimateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.EstimateRequestPriceNative(&_VRFV2PlusWrapperArbitrum.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) GetConfig(opts *bind.CallOpts) (GetConfig,

	error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "getConfig")

	outstruct := new(GetConfig)
	if err != nil {
		return *outstruct, err
	}

	outstruct.FallbackWeiPerUnitLink = *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)
	outstruct.StalenessSeconds = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.FulfillmentFlatFeeNativePPM = *abi.ConvertType(out[2], new(uint32)).(*uint32)
	outstruct.FulfillmentFlatFeeLinkDiscountPPM = *abi.ConvertType(out[3], new(uint32)).(*uint32)
	outstruct.WrapperGasOverhead = *abi.ConvertType(out[4], new(uint32)).(*uint32)
	outstruct.CoordinatorGasOverheadNative = *abi.ConvertType(out[5], new(uint32)).(*uint32)
	outstruct.CoordinatorGasOverheadLink = *abi.ConvertType(out[6], new(uint32)).(*uint32)
	outstruct.CoordinatorGasOverheadPerWord = *abi.ConvertType(out[7], new(uint16)).(*uint16)
	outstruct.WrapperNativePremiumPercentage = *abi.ConvertType(out[8], new(uint8)).(*uint8)
	outstruct.WrapperLinkPremiumPercentage = *abi.ConvertType(out[9], new(uint8)).(*uint8)
	outstruct.KeyHash = *abi.ConvertType(out[10], new([32]byte)).(*[32]byte)
	outstruct.MaxNumWords = *abi.ConvertType(out[11], new(uint8)).(*uint8)

	return *outstruct, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) GetConfig() (GetConfig,

	error) {
	return _VRFV2PlusWrapperArbitrum.Contract.GetConfig(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) GetConfig() (GetConfig,

	error) {
	return _VRFV2PlusWrapperArbitrum.Contract.GetConfig(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) LastRequestId(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "lastRequestId")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) LastRequestId() (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.LastRequestId(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) LastRequestId() (*big.Int, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.LastRequestId(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) Link(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "link")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) Link() (common.Address, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Link(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) Link() (common.Address, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Link(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) LinkNativeFeed(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "linkNativeFeed")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) LinkNativeFeed() (common.Address, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.LinkNativeFeed(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) LinkNativeFeed() (common.Address, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.LinkNativeFeed(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) Owner() (common.Address, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Owner(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) Owner() (common.Address, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Owner(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) SCallbacks(opts *bind.CallOpts, arg0 *big.Int) (SCallbacks,

	error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "s_callbacks", arg0)

	outstruct := new(SCallbacks)
	if err != nil {
		return *outstruct, err
	}

	outstruct.CallbackAddress = *abi.ConvertType(out[0], new(common.Address)).(*common.Address)
	outstruct.CallbackGasLimit = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.RequestGasPrice = *abi.ConvertType(out[2], new(uint64)).(*uint64)

	return *outstruct, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) SCallbacks(arg0 *big.Int) (SCallbacks,

	error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SCallbacks(&_VRFV2PlusWrapperArbitrum.CallOpts, arg0)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) SCallbacks(arg0 *big.Int) (SCallbacks,

	error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SCallbacks(&_VRFV2PlusWrapperArbitrum.CallOpts, arg0)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) SConfigured(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "s_configured")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) SConfigured() (bool, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SConfigured(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) SConfigured() (bool, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SConfigured(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) SDisabled(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "s_disabled")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) SDisabled() (bool, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SDisabled(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) SDisabled() (bool, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SDisabled(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) SFulfillmentTxSizeBytes(opts *bind.CallOpts) (uint32, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "s_fulfillmentTxSizeBytes")

	if err != nil {
		return *new(uint32), err
	}

	out0 := *abi.ConvertType(out[0], new(uint32)).(*uint32)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) SFulfillmentTxSizeBytes() (uint32, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SFulfillmentTxSizeBytes(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) SFulfillmentTxSizeBytes() (uint32, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SFulfillmentTxSizeBytes(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) SVrfCoordinator(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "s_vrfCoordinator")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) SVrfCoordinator() (common.Address, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SVrfCoordinator(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) SVrfCoordinator() (common.Address, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SVrfCoordinator(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperArbitrum.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) TypeAndVersion() (string, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.TypeAndVersion(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumCallerSession) TypeAndVersion() (string, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.TypeAndVersion(&_VRFV2PlusWrapperArbitrum.CallOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "acceptOwnership")
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) AcceptOwnership() (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.AcceptOwnership(&_VRFV2PlusWrapperArbitrum.TransactOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.AcceptOwnership(&_VRFV2PlusWrapperArbitrum.TransactOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) Disable(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "disable")
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) Disable() (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Disable(&_VRFV2PlusWrapperArbitrum.TransactOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) Disable() (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Disable(&_VRFV2PlusWrapperArbitrum.TransactOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) Enable(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "enable")
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) Enable() (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Enable(&_VRFV2PlusWrapperArbitrum.TransactOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) Enable() (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Enable(&_VRFV2PlusWrapperArbitrum.TransactOpts)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) OnTokenTransfer(opts *bind.TransactOpts, _sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "onTokenTransfer", _sender, _amount, _data)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) OnTokenTransfer(_sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.OnTokenTransfer(&_VRFV2PlusWrapperArbitrum.TransactOpts, _sender, _amount, _data)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) OnTokenTransfer(_sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.OnTokenTransfer(&_VRFV2PlusWrapperArbitrum.TransactOpts, _sender, _amount, _data)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) RawFulfillRandomWords(opts *bind.TransactOpts, requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "rawFulfillRandomWords", requestId, randomWords)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) RawFulfillRandomWords(requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.RawFulfillRandomWords(&_VRFV2PlusWrapperArbitrum.TransactOpts, requestId, randomWords)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) RawFulfillRandomWords(requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.RawFulfillRandomWords(&_VRFV2PlusWrapperArbitrum.TransactOpts, requestId, randomWords)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) RequestRandomWordsInNative(opts *bind.TransactOpts, _callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "requestRandomWordsInNative", _callbackGasLimit, _requestConfirmations, _numWords, extraArgs)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) RequestRandomWordsInNative(_callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.RequestRandomWordsInNative(&_VRFV2PlusWrapperArbitrum.TransactOpts, _callbackGasLimit, _requestConfirmations, _numWords, extraArgs)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) RequestRandomWordsInNative(_callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.RequestRandomWordsInNative(&_VRFV2PlusWrapperArbitrum.TransactOpts, _callbackGasLimit, _requestConfirmations, _numWords, extraArgs)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) SetConfig(opts *bind.TransactOpts, _wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "setConfig", _wrapperGasOverhead, _coordinatorGasOverheadNative, _coordinatorGasOverheadLink, _coordinatorGasOverheadPerWord, _coordinatorNativePremiumPercentage, _coordinatorLinkPremiumPercentage, _keyHash, _maxNumWords, _stalenessSeconds, _fallbackWeiPerUnitLink, _fulfillmentFlatFeeNativePPM, _fulfillmentFlatFeeLinkDiscountPPM)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) SetConfig(_wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SetConfig(&_VRFV2PlusWrapperArbitrum.TransactOpts, _wrapperGasOverhead, _coordinatorGasOverheadNative, _coordinatorGasOverheadLink, _coordinatorGasOverheadPerWord, _coordinatorNativePremiumPercentage, _coordinatorLinkPremiumPercentage, _keyHash, _maxNumWords, _stalenessSeconds, _fallbackWeiPerUnitLink, _fulfillmentFlatFeeNativePPM, _fulfillmentFlatFeeLinkDiscountPPM)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) SetConfig(_wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SetConfig(&_VRFV2PlusWrapperArbitrum.TransactOpts, _wrapperGasOverhead, _coordinatorGasOverheadNative, _coordinatorGasOverheadLink, _coordinatorGasOverheadPerWord, _coordinatorNativePremiumPercentage, _coordinatorLinkPremiumPercentage, _keyHash, _maxNumWords, _stalenessSeconds, _fallbackWeiPerUnitLink, _fulfillmentFlatFeeNativePPM, _fulfillmentFlatFeeLinkDiscountPPM)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) SetCoordinator(opts *bind.TransactOpts, _vrfCoordinator common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "setCoordinator", _vrfCoordinator)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) SetCoordinator(_vrfCoordinator common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SetCoordinator(&_VRFV2PlusWrapperArbitrum.TransactOpts, _vrfCoordinator)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) SetCoordinator(_vrfCoordinator common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SetCoordinator(&_VRFV2PlusWrapperArbitrum.TransactOpts, _vrfCoordinator)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) SetFulfillmentTxSize(opts *bind.TransactOpts, _size uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "setFulfillmentTxSize", _size)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) SetFulfillmentTxSize(_size uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SetFulfillmentTxSize(&_VRFV2PlusWrapperArbitrum.TransactOpts, _size)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) SetFulfillmentTxSize(_size uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.SetFulfillmentTxSize(&_VRFV2PlusWrapperArbitrum.TransactOpts, _size)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "transferOwnership", to)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.TransferOwnership(&_VRFV2PlusWrapperArbitrum.TransactOpts, to)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.TransferOwnership(&_VRFV2PlusWrapperArbitrum.TransactOpts, to)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) Withdraw(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "withdraw", _recipient)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) Withdraw(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Withdraw(&_VRFV2PlusWrapperArbitrum.TransactOpts, _recipient)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) Withdraw(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.Withdraw(&_VRFV2PlusWrapperArbitrum.TransactOpts, _recipient)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactor) WithdrawNative(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.contract.Transact(opts, "withdrawNative", _recipient)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumSession) WithdrawNative(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.WithdrawNative(&_VRFV2PlusWrapperArbitrum.TransactOpts, _recipient)
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumTransactorSession) WithdrawNative(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperArbitrum.Contract.WithdrawNative(&_VRFV2PlusWrapperArbitrum.TransactOpts, _recipient)
}

type VRFV2PlusWrapperArbitrumConfigSetIterator struct {
	Event *VRFV2PlusWrapperArbitrumConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumConfigSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumConfigSet struct {
	WrapperGasOverhead                 uint32
	CoordinatorGasOverheadNative       uint32
	CoordinatorGasOverheadLink         uint32
	CoordinatorGasOverheadPerWord      uint16
	CoordinatorNativePremiumPercentage uint8
	CoordinatorLinkPremiumPercentage   uint8
	KeyHash                            [32]byte
	MaxNumWords                        uint8
	StalenessSeconds                   uint32
	FallbackWeiPerUnitLink             *big.Int
	FulfillmentFlatFeeNativePPM        uint32
	FulfillmentFlatFeeLinkDiscountPPM  uint32
	Raw                                types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterConfigSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumConfigSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumConfigSetIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "ConfigSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumConfigSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumConfigSet)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "ConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseConfigSet(log types.Log) (*VRFV2PlusWrapperArbitrumConfigSet, error) {
	event := new(VRFV2PlusWrapperArbitrumConfigSet)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "ConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumCoordinatorSetIterator struct {
	Event *VRFV2PlusWrapperArbitrumCoordinatorSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumCoordinatorSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumCoordinatorSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumCoordinatorSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumCoordinatorSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumCoordinatorSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumCoordinatorSet struct {
	VrfCoordinator common.Address
	Raw            types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterCoordinatorSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumCoordinatorSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "CoordinatorSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumCoordinatorSetIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "CoordinatorSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchCoordinatorSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumCoordinatorSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "CoordinatorSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumCoordinatorSet)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "CoordinatorSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseCoordinatorSet(log types.Log) (*VRFV2PlusWrapperArbitrumCoordinatorSet, error) {
	event := new(VRFV2PlusWrapperArbitrumCoordinatorSet)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "CoordinatorSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumDisabledIterator struct {
	Event *VRFV2PlusWrapperArbitrumDisabled

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumDisabledIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumDisabled)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumDisabled)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumDisabledIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumDisabledIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumDisabled struct {
	Raw types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterDisabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumDisabledIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "Disabled")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumDisabledIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "Disabled", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchDisabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumDisabled) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "Disabled")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumDisabled)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "Disabled", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseDisabled(log types.Log) (*VRFV2PlusWrapperArbitrumDisabled, error) {
	event := new(VRFV2PlusWrapperArbitrumDisabled)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "Disabled", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumEnabledIterator struct {
	Event *VRFV2PlusWrapperArbitrumEnabled

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumEnabledIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumEnabled)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumEnabled)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumEnabledIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumEnabledIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumEnabled struct {
	Raw types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterEnabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumEnabledIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "Enabled")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumEnabledIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "Enabled", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchEnabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumEnabled) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "Enabled")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumEnabled)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "Enabled", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseEnabled(log types.Log) (*VRFV2PlusWrapperArbitrumEnabled, error) {
	event := new(VRFV2PlusWrapperArbitrumEnabled)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "Enabled", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsedIterator struct {
	Event *VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsedIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed struct {
	RequestId              *big.Int
	FallbackWeiPerUnitLink *big.Int
	Raw                    types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterFallbackWeiPerUnitLinkUsed(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsedIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "FallbackWeiPerUnitLinkUsed")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsedIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "FallbackWeiPerUnitLinkUsed", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchFallbackWeiPerUnitLinkUsed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "FallbackWeiPerUnitLinkUsed")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "FallbackWeiPerUnitLinkUsed", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseFallbackWeiPerUnitLinkUsed(log types.Log) (*VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed, error) {
	event := new(VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "FallbackWeiPerUnitLinkUsed", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumFulfillmentTxSizeSetIterator struct {
	Event *VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumFulfillmentTxSizeSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumFulfillmentTxSizeSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumFulfillmentTxSizeSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet struct {
	Size uint32
	Raw  types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterFulfillmentTxSizeSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumFulfillmentTxSizeSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "FulfillmentTxSizeSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumFulfillmentTxSizeSetIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "FulfillmentTxSizeSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchFulfillmentTxSizeSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "FulfillmentTxSizeSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "FulfillmentTxSizeSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseFulfillmentTxSizeSet(log types.Log) (*VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet, error) {
	event := new(VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "FulfillmentTxSizeSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumNativeWithdrawnIterator struct {
	Event *VRFV2PlusWrapperArbitrumNativeWithdrawn

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumNativeWithdrawnIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumNativeWithdrawn)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumNativeWithdrawn)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumNativeWithdrawnIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumNativeWithdrawnIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumNativeWithdrawn struct {
	To     common.Address
	Amount *big.Int
	Raw    types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterNativeWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperArbitrumNativeWithdrawnIterator, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "NativeWithdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumNativeWithdrawnIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "NativeWithdrawn", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchNativeWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumNativeWithdrawn, to []common.Address) (event.Subscription, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "NativeWithdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumNativeWithdrawn)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "NativeWithdrawn", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseNativeWithdrawn(log types.Log) (*VRFV2PlusWrapperArbitrumNativeWithdrawn, error) {
	event := new(VRFV2PlusWrapperArbitrumNativeWithdrawn)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "NativeWithdrawn", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumOwnershipTransferRequestedIterator struct {
	Event *VRFV2PlusWrapperArbitrumOwnershipTransferRequested

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumOwnershipTransferRequestedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperArbitrumOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumOwnershipTransferRequestedIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumOwnershipTransferRequested)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseOwnershipTransferRequested(log types.Log) (*VRFV2PlusWrapperArbitrumOwnershipTransferRequested, error) {
	event := new(VRFV2PlusWrapperArbitrumOwnershipTransferRequested)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumOwnershipTransferredIterator struct {
	Event *VRFV2PlusWrapperArbitrumOwnershipTransferred

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumOwnershipTransferredIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumOwnershipTransferredIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperArbitrumOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumOwnershipTransferredIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumOwnershipTransferred)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseOwnershipTransferred(log types.Log) (*VRFV2PlusWrapperArbitrumOwnershipTransferred, error) {
	event := new(VRFV2PlusWrapperArbitrumOwnershipTransferred)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumWithdrawnIterator struct {
	Event *VRFV2PlusWrapperArbitrumWithdrawn

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumWithdrawnIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumWithdrawn)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumWithdrawn)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumWithdrawnIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumWithdrawnIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumWithdrawn struct {
	To     common.Address
	Amount *big.Int
	Raw    types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperArbitrumWithdrawnIterator, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "Withdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumWithdrawnIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "Withdrawn", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumWithdrawn, to []common.Address) (event.Subscription, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "Withdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumWithdrawn)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "Withdrawn", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseWithdrawn(log types.Log) (*VRFV2PlusWrapperArbitrumWithdrawn, error) {
	event := new(VRFV2PlusWrapperArbitrumWithdrawn)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "Withdrawn", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperArbitrumWrapperFulfillmentFailedIterator struct {
	Event *VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperArbitrumWrapperFulfillmentFailedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperArbitrumWrapperFulfillmentFailedIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperArbitrumWrapperFulfillmentFailedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed struct {
	RequestId *big.Int
	Consumer  common.Address
	Raw       types.Log
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) FilterWrapperFulfillmentFailed(opts *bind.FilterOpts, requestId []*big.Int, consumer []common.Address) (*VRFV2PlusWrapperArbitrumWrapperFulfillmentFailedIterator, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}
	var consumerRule []interface{}
	for _, consumerItem := range consumer {
		consumerRule = append(consumerRule, consumerItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.FilterLogs(opts, "WrapperFulfillmentFailed", requestIdRule, consumerRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperArbitrumWrapperFulfillmentFailedIterator{contract: _VRFV2PlusWrapperArbitrum.contract, event: "WrapperFulfillmentFailed", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) WatchWrapperFulfillmentFailed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed, requestId []*big.Int, consumer []common.Address) (event.Subscription, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}
	var consumerRule []interface{}
	for _, consumerItem := range consumer {
		consumerRule = append(consumerRule, consumerItem)
	}

	logs, sub, err := _VRFV2PlusWrapperArbitrum.contract.WatchLogs(opts, "WrapperFulfillmentFailed", requestIdRule, consumerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed)
				if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "WrapperFulfillmentFailed", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrumFilterer) ParseWrapperFulfillmentFailed(log types.Log) (*VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed, error) {
	event := new(VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed)
	if err := _VRFV2PlusWrapperArbitrum.contract.UnpackLog(event, "WrapperFulfillmentFailed", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type GetConfig struct {
	FallbackWeiPerUnitLink            *big.Int
	StalenessSeconds                  uint32
	FulfillmentFlatFeeNativePPM       uint32
	FulfillmentFlatFeeLinkDiscountPPM uint32
	WrapperGasOverhead                uint32
	CoordinatorGasOverheadNative      uint32
	CoordinatorGasOverheadLink        uint32
	CoordinatorGasOverheadPerWord     uint16
	WrapperNativePremiumPercentage    uint8
	WrapperLinkPremiumPercentage      uint8
	KeyHash                           [32]byte
	MaxNumWords                       uint8
}
type SCallbacks struct {
	CallbackAddress  common.Address
	CallbackGasLimit uint32
	RequestGasPrice  uint64
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrum) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _VRFV2PlusWrapperArbitrum.abi.Events["ConfigSet"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseConfigSet(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["CoordinatorSet"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseCoordinatorSet(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["Disabled"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseDisabled(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["Enabled"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseEnabled(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["FallbackWeiPerUnitLinkUsed"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseFallbackWeiPerUnitLinkUsed(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["FulfillmentTxSizeSet"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseFulfillmentTxSizeSet(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["NativeWithdrawn"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseNativeWithdrawn(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["OwnershipTransferRequested"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseOwnershipTransferRequested(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["OwnershipTransferred"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseOwnershipTransferred(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["Withdrawn"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseWithdrawn(log)
	case _VRFV2PlusWrapperArbitrum.abi.Events["WrapperFulfillmentFailed"].ID:
		return _VRFV2PlusWrapperArbitrum.ParseWrapperFulfillmentFailed(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (VRFV2PlusWrapperArbitrumConfigSet) Topic() common.Hash {
	return common.HexToHash("0x8aee1a8c131eaf1a5bd30594737b6926a7c5cb29281a97639f6ac93947c8995e")
}

func (VRFV2PlusWrapperArbitrumCoordinatorSet) Topic() common.Hash {
	return common.HexToHash("0xd1a6a14209a385a964d036e404cb5cfb71f4000cdb03c9366292430787261be6")
}

func (VRFV2PlusWrapperArbitrumDisabled) Topic() common.Hash {
	return common.HexToHash("0x75884cdadc4a89e8b545db800057f06ec7f5338a08183c7ba515f2bfdd9fe1e1")
}

func (VRFV2PlusWrapperArbitrumEnabled) Topic() common.Hash {
	return common.HexToHash("0xc0f961051f97b04c496472d11cb6170d844e4b2c9dfd3b602a4fa0139712d484")
}

func (VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed) Topic() common.Hash {
	return common.HexToHash("0x6ca648a381f22ead7e37773d934e64885dcf861fbfbb26c40354cbf0c4662d1a")
}

func (VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet) Topic() common.Hash {
	return common.HexToHash("0x697b48b8b76cebb09a54ec4ff810e8a181c96f65395d51c744db09c115d1d5d0")
}

func (VRFV2PlusWrapperArbitrumNativeWithdrawn) Topic() common.Hash {
	return common.HexToHash("0xc303ca808382409472acbbf899c316cf439f409f6584aae22df86dfa3c9ed504")
}

func (VRFV2PlusWrapperArbitrumOwnershipTransferRequested) Topic() common.Hash {
	return common.HexToHash("0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278")
}

func (VRFV2PlusWrapperArbitrumOwnershipTransferred) Topic() common.Hash {
	return common.HexToHash("0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0")
}

func (VRFV2PlusWrapperArbitrumWithdrawn) Topic() common.Hash {
	return common.HexToHash("0x7084f5476618d8e60b11ef0d7d3f06914655adb8793e28ff7f018d4c76d505d5")
}

func (VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed) Topic() common.Hash {
	return common.HexToHash("0xc551b83c151f2d1c7eeb938ac59008e0409f1c1dc1e2f112449d4d79b4589022")
}

func (_VRFV2PlusWrapperArbitrum *VRFV2PlusWrapperArbitrum) Address() common.Address {
	return _VRFV2PlusWrapperArbitrum.address
}

type VRFV2PlusWrapperArbitrumInterface interface {
	SUBSCRIPTIONID(opts *bind.CallOpts) (*big.Int, error)

	CalculateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error)

	CalculateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error)

	CheckPaymentMode(opts *bind.CallOpts, extraArgs []byte, isLinkMode bool) error

	EstimateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error)

	EstimateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error)

	GetConfig(opts *bind.CallOpts) (GetConfig,

		error)

	LastRequestId(opts *bind.CallOpts) (*big.Int, error)

	Link(opts *bind.CallOpts) (common.Address, error)

	LinkNativeFeed(opts *bind.CallOpts) (common.Address, error)

	Owner(opts *bind.CallOpts) (common.Address, error)

	SCallbacks(opts *bind.CallOpts, arg0 *big.Int) (SCallbacks,

		error)

	SConfigured(opts *bind.CallOpts) (bool, error)

	SDisabled(opts *bind.CallOpts) (bool, error)

	SFulfillmentTxSizeBytes(opts *bind.CallOpts) (uint32, error)

	SVrfCoordinator(opts *bind.CallOpts) (common.Address, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error)

	Disable(opts *bind.TransactOpts) (*types.Transaction, error)

	Enable(opts *bind.TransactOpts) (*types.Transaction, error)

	OnTokenTransfer(opts *bind.TransactOpts, _sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error)

	RawFulfillRandomWords(opts *bind.TransactOpts, requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error)

	RequestRandomWordsInNative(opts *bind.TransactOpts, _callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error)

	SetConfig(opts *bind.TransactOpts, _wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error)

	SetCoordinator(opts *bind.TransactOpts, _vrfCoordinator common.Address) (*types.Transaction, error)

	SetFulfillmentTxSize(opts *bind.TransactOpts, _size uint32) (*types.Transaction, error)

	TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error)

	Withdraw(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error)

	WithdrawNative(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error)

	FilterConfigSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumConfigSetIterator, error)

	WatchConfigSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumConfigSet) (event.Subscription, error)

	ParseConfigSet(log types.Log) (*VRFV2PlusWrapperArbitrumConfigSet, error)

	FilterCoordinatorSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumCoordinatorSetIterator, error)

	WatchCoordinatorSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumCoordinatorSet) (event.Subscription, error)

	ParseCoordinatorSet(log types.Log) (*VRFV2PlusWrapperArbitrumCoordinatorSet, error)

	FilterDisabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumDisabledIterator, error)

	WatchDisabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumDisabled) (event.Subscription, error)

	ParseDisabled(log types.Log) (*VRFV2PlusWrapperArbitrumDisabled, error)

	FilterEnabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumEnabledIterator, error)

	WatchEnabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumEnabled) (event.Subscription, error)

	ParseEnabled(log types.Log) (*VRFV2PlusWrapperArbitrumEnabled, error)

	FilterFallbackWeiPerUnitLinkUsed(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsedIterator, error)

	WatchFallbackWeiPerUnitLinkUsed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed) (event.Subscription, error)

	ParseFallbackWeiPerUnitLinkUsed(log types.Log) (*VRFV2PlusWrapperArbitrumFallbackWeiPerUnitLinkUsed, error)

	FilterFulfillmentTxSizeSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperArbitrumFulfillmentTxSizeSetIterator, error)

	WatchFulfillmentTxSizeSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet) (event.Subscription, error)

	ParseFulfillmentTxSizeSet(log types.Log) (*VRFV2PlusWrapperArbitrumFulfillmentTxSizeSet, error)

	FilterNativeWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperArbitrumNativeWithdrawnIterator, error)

	WatchNativeWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumNativeWithdrawn, to []common.Address) (event.Subscription, error)

	ParseNativeWithdrawn(log types.Log) (*VRFV2PlusWrapperArbitrumNativeWithdrawn, error)

	FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperArbitrumOwnershipTransferRequestedIterator, error)

	WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferRequested(log types.Log) (*VRFV2PlusWrapperArbitrumOwnershipTransferRequested, error)

	FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperArbitrumOwnershipTransferredIterator, error)

	WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferred(log types.Log) (*VRFV2PlusWrapperArbitrumOwnershipTransferred, error)

	FilterWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperArbitrumWithdrawnIterator, error)

	WatchWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumWithdrawn, to []common.Address) (event.Subscription, error)

	ParseWithdrawn(log types.Log) (*VRFV2PlusWrapperArbitrumWithdrawn, error)

	FilterWrapperFulfillmentFailed(opts *bind.FilterOpts, requestId []*big.Int, consumer []common.Address) (*VRFV2PlusWrapperArbitrumWrapperFulfillmentFailedIterator, error)

	WatchWrapperFulfillmentFailed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed, requestId []*big.Int, consumer []common.Address) (event.Subscription, error)

	ParseWrapperFulfillmentFailed(log types.Log) (*VRFV2PlusWrapperArbitrumWrapperFulfillmentFailed, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
