// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package vrfv2plus_wrapper

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
	"github.com/smartcontractkit/chainlink-evm/gethwrappers/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

var VRFV2PlusWrapperMetaData = &bind.MetaData{
	ABI: "[{\"type\":\"constructor\",\"inputs\":[{\"name\":\"_link\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_linkNativeFeed\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_coordinator\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_subId\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"SUBSCRIPTION_ID\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"acceptOwnership\",\"inputs\":[],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"calculateRequestPrice\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"calculateRequestPriceNative\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"checkPaymentMode\",\"inputs\":[{\"name\":\"extraArgs\",\"type\":\"bytes\",\"internalType\":\"bytes\"},{\"name\":\"isLinkMode\",\"type\":\"bool\",\"internalType\":\"bool\"}],\"outputs\":[],\"stateMutability\":\"pure\"},{\"type\":\"function\",\"name\":\"disable\",\"inputs\":[],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"enable\",\"inputs\":[],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"estimateRequestPrice\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_requestGasPriceWei\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"estimateRequestPriceNative\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_requestGasPriceWei\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"getConfig\",\"inputs\":[],\"outputs\":[{\"name\":\"fallbackWeiPerUnitLink\",\"type\":\"int256\",\"internalType\":\"int256\"},{\"name\":\"stalenessSeconds\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"fulfillmentFlatFeeNativePPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"fulfillmentFlatFeeLinkDiscountPPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"wrapperGasOverhead\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadNative\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadLink\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadPerWord\",\"type\":\"uint16\",\"internalType\":\"uint16\"},{\"name\":\"wrapperNativePremiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"wrapperLinkPremiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"keyHash\",\"type\":\"bytes32\",\"internalType\":\"bytes32\"},{\"name\":\"maxNumWords\",\"type\":\"uint8\",\"internalType\":\"uint8\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"lastRequestId\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"link\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"linkNativeFeed\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"onTokenTransfer\",\"inputs\":[{\"name\":\"_sender\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_amount\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"_data\",\"type\":\"bytes\",\"internalType\":\"bytes\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"owner\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"rawFulfillRandomWords\",\"inputs\":[{\"name\":\"requestId\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"randomWords\",\"type\":\"uint256[]\",\"internalType\":\"uint256[]\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"requestRandomWordsInNative\",\"inputs\":[{\"name\":\"_callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_requestConfirmations\",\"type\":\"uint16\",\"internalType\":\"uint16\"},{\"name\":\"_numWords\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"extraArgs\",\"type\":\"bytes\",\"internalType\":\"bytes\"}],\"outputs\":[{\"name\":\"requestId\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"payable\"},{\"type\":\"function\",\"name\":\"s_callbacks\",\"inputs\":[{\"name\":\"\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[{\"name\":\"callbackAddress\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"callbackGasLimit\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"requestGasPrice\",\"type\":\"uint64\",\"internalType\":\"uint64\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"s_configured\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"bool\",\"internalType\":\"bool\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"s_disabled\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"bool\",\"internalType\":\"bool\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"s_fulfillmentTxSizeBytes\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"s_vrfCoordinator\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"contractIVRFCoordinatorV2Plus\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"setConfig\",\"inputs\":[{\"name\":\"_wrapperGasOverhead\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_coordinatorGasOverheadNative\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_coordinatorGasOverheadLink\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_coordinatorGasOverheadPerWord\",\"type\":\"uint16\",\"internalType\":\"uint16\"},{\"name\":\"_coordinatorNativePremiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"_coordinatorLinkPremiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"_keyHash\",\"type\":\"bytes32\",\"internalType\":\"bytes32\"},{\"name\":\"_maxNumWords\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"_stalenessSeconds\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_fallbackWeiPerUnitLink\",\"type\":\"int256\",\"internalType\":\"int256\"},{\"name\":\"_fulfillmentFlatFeeNativePPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"_fulfillmentFlatFeeLinkDiscountPPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"setCoordinator\",\"inputs\":[{\"name\":\"_vrfCoordinator\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"setFulfillmentTxSize\",\"inputs\":[{\"name\":\"_size\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"transferOwnership\",\"inputs\":[{\"name\":\"to\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"typeAndVersion\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"string\",\"internalType\":\"string\"}],\"stateMutability\":\"pure\"},{\"type\":\"function\",\"name\":\"withdraw\",\"inputs\":[{\"name\":\"_recipient\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"withdrawNative\",\"inputs\":[{\"name\":\"_recipient\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"event\",\"name\":\"ConfigSet\",\"inputs\":[{\"name\":\"wrapperGasOverhead\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadNative\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadLink\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"coordinatorGasOverheadPerWord\",\"type\":\"uint16\",\"indexed\":false,\"internalType\":\"uint16\"},{\"name\":\"coordinatorNativePremiumPercentage\",\"type\":\"uint8\",\"indexed\":false,\"internalType\":\"uint8\"},{\"name\":\"coordinatorLinkPremiumPercentage\",\"type\":\"uint8\",\"indexed\":false,\"internalType\":\"uint8\"},{\"name\":\"keyHash\",\"type\":\"bytes32\",\"indexed\":false,\"internalType\":\"bytes32\"},{\"name\":\"maxNumWords\",\"type\":\"uint8\",\"indexed\":false,\"internalType\":\"uint8\"},{\"name\":\"stalenessSeconds\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"fallbackWeiPerUnitLink\",\"type\":\"int256\",\"indexed\":false,\"internalType\":\"int256\"},{\"name\":\"fulfillmentFlatFeeNativePPM\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"},{\"name\":\"fulfillmentFlatFeeLinkDiscountPPM\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"CoordinatorSet\",\"inputs\":[{\"name\":\"vrfCoordinator\",\"type\":\"address\",\"indexed\":false,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"Disabled\",\"inputs\":[],\"anonymous\":false},{\"type\":\"event\",\"name\":\"Enabled\",\"inputs\":[],\"anonymous\":false},{\"type\":\"event\",\"name\":\"FallbackWeiPerUnitLinkUsed\",\"inputs\":[{\"name\":\"requestId\",\"type\":\"uint256\",\"indexed\":false,\"internalType\":\"uint256\"},{\"name\":\"fallbackWeiPerUnitLink\",\"type\":\"int256\",\"indexed\":false,\"internalType\":\"int256\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"FulfillmentTxSizeSet\",\"inputs\":[{\"name\":\"size\",\"type\":\"uint32\",\"indexed\":false,\"internalType\":\"uint32\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"NativeWithdrawn\",\"inputs\":[{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"amount\",\"type\":\"uint256\",\"indexed\":false,\"internalType\":\"uint256\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"OwnershipTransferRequested\",\"inputs\":[{\"name\":\"from\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"OwnershipTransferred\",\"inputs\":[{\"name\":\"from\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"Withdrawn\",\"inputs\":[{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"amount\",\"type\":\"uint256\",\"indexed\":false,\"internalType\":\"uint256\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"WrapperFulfillmentFailed\",\"inputs\":[{\"name\":\"requestId\",\"type\":\"uint256\",\"indexed\":true,\"internalType\":\"uint256\"},{\"name\":\"consumer\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"error\",\"name\":\"FailedToTransferLink\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"IncorrectExtraArgsLength\",\"inputs\":[{\"name\":\"expectedMinimumLength\",\"type\":\"uint16\",\"internalType\":\"uint16\"},{\"name\":\"actualLength\",\"type\":\"uint16\",\"internalType\":\"uint16\"}]},{\"type\":\"error\",\"name\":\"InvalidPremiumPercentage\",\"inputs\":[{\"name\":\"premiumPercentage\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"max\",\"type\":\"uint8\",\"internalType\":\"uint8\"}]},{\"type\":\"error\",\"name\":\"LINKPaymentInRequestRandomWordsInNative\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"LinkAlreadySet\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"LinkDiscountTooHigh\",\"inputs\":[{\"name\":\"flatFeeLinkDiscountPPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"},{\"name\":\"flatFeeNativePPM\",\"type\":\"uint32\",\"internalType\":\"uint32\"}]},{\"type\":\"error\",\"name\":\"NativePaymentInOnTokenTransfer\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"OnlyCoordinatorCanFulfill\",\"inputs\":[{\"name\":\"have\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"want\",\"type\":\"address\",\"internalType\":\"address\"}]},{\"type\":\"error\",\"name\":\"OnlyOwnerOrCoordinator\",\"inputs\":[{\"name\":\"have\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"owner\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"coordinator\",\"type\":\"address\",\"internalType\":\"address\"}]},{\"type\":\"error\",\"name\":\"SubscriptionIdMissing\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"ZeroAddress\",\"inputs\":[]}]",
	Bin: "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",
}

var VRFV2PlusWrapperABI = VRFV2PlusWrapperMetaData.ABI

var VRFV2PlusWrapperBin = VRFV2PlusWrapperMetaData.Bin

func DeployVRFV2PlusWrapper(auth *bind.TransactOpts, backend bind.ContractBackend, _link common.Address, _linkNativeFeed common.Address, _coordinator common.Address, _subId *big.Int) (common.Address, *types.Transaction, *VRFV2PlusWrapper, error) {
	parsed, err := VRFV2PlusWrapperMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(VRFV2PlusWrapperBin), backend, _link, _linkNativeFeed, _coordinator, _subId)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &VRFV2PlusWrapper{address: address, abi: *parsed, VRFV2PlusWrapperCaller: VRFV2PlusWrapperCaller{contract: contract}, VRFV2PlusWrapperTransactor: VRFV2PlusWrapperTransactor{contract: contract}, VRFV2PlusWrapperFilterer: VRFV2PlusWrapperFilterer{contract: contract}}, nil
}

type VRFV2PlusWrapper struct {
	address common.Address
	abi     abi.ABI
	VRFV2PlusWrapperCaller
	VRFV2PlusWrapperTransactor
	VRFV2PlusWrapperFilterer
}

type VRFV2PlusWrapperCaller struct {
	contract *bind.BoundContract
}

type VRFV2PlusWrapperTransactor struct {
	contract *bind.BoundContract
}

type VRFV2PlusWrapperFilterer struct {
	contract *bind.BoundContract
}

type VRFV2PlusWrapperSession struct {
	Contract     *VRFV2PlusWrapper
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type VRFV2PlusWrapperCallerSession struct {
	Contract *VRFV2PlusWrapperCaller
	CallOpts bind.CallOpts
}

type VRFV2PlusWrapperTransactorSession struct {
	Contract     *VRFV2PlusWrapperTransactor
	TransactOpts bind.TransactOpts
}

type VRFV2PlusWrapperRaw struct {
	Contract *VRFV2PlusWrapper
}

type VRFV2PlusWrapperCallerRaw struct {
	Contract *VRFV2PlusWrapperCaller
}

type VRFV2PlusWrapperTransactorRaw struct {
	Contract *VRFV2PlusWrapperTransactor
}

func NewVRFV2PlusWrapper(address common.Address, backend bind.ContractBackend) (*VRFV2PlusWrapper, error) {
	abi, err := abi.JSON(strings.NewReader(VRFV2PlusWrapperABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindVRFV2PlusWrapper(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapper{address: address, abi: abi, VRFV2PlusWrapperCaller: VRFV2PlusWrapperCaller{contract: contract}, VRFV2PlusWrapperTransactor: VRFV2PlusWrapperTransactor{contract: contract}, VRFV2PlusWrapperFilterer: VRFV2PlusWrapperFilterer{contract: contract}}, nil
}

func NewVRFV2PlusWrapperCaller(address common.Address, caller bind.ContractCaller) (*VRFV2PlusWrapperCaller, error) {
	contract, err := bindVRFV2PlusWrapper(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperCaller{contract: contract}, nil
}

func NewVRFV2PlusWrapperTransactor(address common.Address, transactor bind.ContractTransactor) (*VRFV2PlusWrapperTransactor, error) {
	contract, err := bindVRFV2PlusWrapper(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperTransactor{contract: contract}, nil
}

func NewVRFV2PlusWrapperFilterer(address common.Address, filterer bind.ContractFilterer) (*VRFV2PlusWrapperFilterer, error) {
	contract, err := bindVRFV2PlusWrapper(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperFilterer{contract: contract}, nil
}

func bindVRFV2PlusWrapper(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := VRFV2PlusWrapperMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _VRFV2PlusWrapper.Contract.VRFV2PlusWrapperCaller.contract.Call(opts, result, method, params...)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.VRFV2PlusWrapperTransactor.contract.Transfer(opts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.VRFV2PlusWrapperTransactor.contract.Transact(opts, method, params...)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _VRFV2PlusWrapper.Contract.contract.Call(opts, result, method, params...)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.contract.Transfer(opts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.contract.Transact(opts, method, params...)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) SUBSCRIPTIONID(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "SUBSCRIPTION_ID")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) SUBSCRIPTIONID() (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.SUBSCRIPTIONID(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) SUBSCRIPTIONID() (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.SUBSCRIPTIONID(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) CalculateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "calculateRequestPrice", _callbackGasLimit, _numWords)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) CalculateRequestPrice(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.CalculateRequestPrice(&_VRFV2PlusWrapper.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) CalculateRequestPrice(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.CalculateRequestPrice(&_VRFV2PlusWrapper.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) CalculateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "calculateRequestPriceNative", _callbackGasLimit, _numWords)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) CalculateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.CalculateRequestPriceNative(&_VRFV2PlusWrapper.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) CalculateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.CalculateRequestPriceNative(&_VRFV2PlusWrapper.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) CheckPaymentMode(opts *bind.CallOpts, extraArgs []byte, isLinkMode bool) error {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "checkPaymentMode", extraArgs, isLinkMode)

	if err != nil {
		return err
	}

	return err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) CheckPaymentMode(extraArgs []byte, isLinkMode bool) error {
	return _VRFV2PlusWrapper.Contract.CheckPaymentMode(&_VRFV2PlusWrapper.CallOpts, extraArgs, isLinkMode)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) CheckPaymentMode(extraArgs []byte, isLinkMode bool) error {
	return _VRFV2PlusWrapper.Contract.CheckPaymentMode(&_VRFV2PlusWrapper.CallOpts, extraArgs, isLinkMode)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) EstimateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "estimateRequestPrice", _callbackGasLimit, _numWords, _requestGasPriceWei)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) EstimateRequestPrice(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.EstimateRequestPrice(&_VRFV2PlusWrapper.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) EstimateRequestPrice(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.EstimateRequestPrice(&_VRFV2PlusWrapper.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) EstimateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "estimateRequestPriceNative", _callbackGasLimit, _numWords, _requestGasPriceWei)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) EstimateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.EstimateRequestPriceNative(&_VRFV2PlusWrapper.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) EstimateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.EstimateRequestPriceNative(&_VRFV2PlusWrapper.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) GetConfig(opts *bind.CallOpts) (GetConfig,

	error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "getConfig")

	outstruct := new(GetConfig)
	if err != nil {
		return *outstruct, err
	}

	outstruct.FallbackWeiPerUnitLink = *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)
	outstruct.StalenessSeconds = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.FulfillmentFlatFeeNativePPM = *abi.ConvertType(out[2], new(uint32)).(*uint32)
	outstruct.FulfillmentFlatFeeLinkDiscountPPM = *abi.ConvertType(out[3], new(uint32)).(*uint32)
	outstruct.WrapperGasOverhead = *abi.ConvertType(out[4], new(uint32)).(*uint32)
	outstruct.CoordinatorGasOverheadNative = *abi.ConvertType(out[5], new(uint32)).(*uint32)
	outstruct.CoordinatorGasOverheadLink = *abi.ConvertType(out[6], new(uint32)).(*uint32)
	outstruct.CoordinatorGasOverheadPerWord = *abi.ConvertType(out[7], new(uint16)).(*uint16)
	outstruct.WrapperNativePremiumPercentage = *abi.ConvertType(out[8], new(uint8)).(*uint8)
	outstruct.WrapperLinkPremiumPercentage = *abi.ConvertType(out[9], new(uint8)).(*uint8)
	outstruct.KeyHash = *abi.ConvertType(out[10], new([32]byte)).(*[32]byte)
	outstruct.MaxNumWords = *abi.ConvertType(out[11], new(uint8)).(*uint8)

	return *outstruct, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) GetConfig() (GetConfig,

	error) {
	return _VRFV2PlusWrapper.Contract.GetConfig(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) GetConfig() (GetConfig,

	error) {
	return _VRFV2PlusWrapper.Contract.GetConfig(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) LastRequestId(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "lastRequestId")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) LastRequestId() (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.LastRequestId(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) LastRequestId() (*big.Int, error) {
	return _VRFV2PlusWrapper.Contract.LastRequestId(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) Link(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "link")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) Link() (common.Address, error) {
	return _VRFV2PlusWrapper.Contract.Link(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) Link() (common.Address, error) {
	return _VRFV2PlusWrapper.Contract.Link(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) LinkNativeFeed(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "linkNativeFeed")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) LinkNativeFeed() (common.Address, error) {
	return _VRFV2PlusWrapper.Contract.LinkNativeFeed(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) LinkNativeFeed() (common.Address, error) {
	return _VRFV2PlusWrapper.Contract.LinkNativeFeed(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) Owner() (common.Address, error) {
	return _VRFV2PlusWrapper.Contract.Owner(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) Owner() (common.Address, error) {
	return _VRFV2PlusWrapper.Contract.Owner(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) SCallbacks(opts *bind.CallOpts, arg0 *big.Int) (SCallbacks,

	error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "s_callbacks", arg0)

	outstruct := new(SCallbacks)
	if err != nil {
		return *outstruct, err
	}

	outstruct.CallbackAddress = *abi.ConvertType(out[0], new(common.Address)).(*common.Address)
	outstruct.CallbackGasLimit = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.RequestGasPrice = *abi.ConvertType(out[2], new(uint64)).(*uint64)

	return *outstruct, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) SCallbacks(arg0 *big.Int) (SCallbacks,

	error) {
	return _VRFV2PlusWrapper.Contract.SCallbacks(&_VRFV2PlusWrapper.CallOpts, arg0)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) SCallbacks(arg0 *big.Int) (SCallbacks,

	error) {
	return _VRFV2PlusWrapper.Contract.SCallbacks(&_VRFV2PlusWrapper.CallOpts, arg0)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) SConfigured(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "s_configured")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) SConfigured() (bool, error) {
	return _VRFV2PlusWrapper.Contract.SConfigured(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) SConfigured() (bool, error) {
	return _VRFV2PlusWrapper.Contract.SConfigured(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) SDisabled(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "s_disabled")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) SDisabled() (bool, error) {
	return _VRFV2PlusWrapper.Contract.SDisabled(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) SDisabled() (bool, error) {
	return _VRFV2PlusWrapper.Contract.SDisabled(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) SFulfillmentTxSizeBytes(opts *bind.CallOpts) (uint32, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "s_fulfillmentTxSizeBytes")

	if err != nil {
		return *new(uint32), err
	}

	out0 := *abi.ConvertType(out[0], new(uint32)).(*uint32)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) SFulfillmentTxSizeBytes() (uint32, error) {
	return _VRFV2PlusWrapper.Contract.SFulfillmentTxSizeBytes(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) SFulfillmentTxSizeBytes() (uint32, error) {
	return _VRFV2PlusWrapper.Contract.SFulfillmentTxSizeBytes(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) SVrfCoordinator(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "s_vrfCoordinator")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) SVrfCoordinator() (common.Address, error) {
	return _VRFV2PlusWrapper.Contract.SVrfCoordinator(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) SVrfCoordinator() (common.Address, error) {
	return _VRFV2PlusWrapper.Contract.SVrfCoordinator(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _VRFV2PlusWrapper.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) TypeAndVersion() (string, error) {
	return _VRFV2PlusWrapper.Contract.TypeAndVersion(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperCallerSession) TypeAndVersion() (string, error) {
	return _VRFV2PlusWrapper.Contract.TypeAndVersion(&_VRFV2PlusWrapper.CallOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "acceptOwnership")
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) AcceptOwnership() (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.AcceptOwnership(&_VRFV2PlusWrapper.TransactOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.AcceptOwnership(&_VRFV2PlusWrapper.TransactOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) Disable(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "disable")
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) Disable() (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.Disable(&_VRFV2PlusWrapper.TransactOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) Disable() (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.Disable(&_VRFV2PlusWrapper.TransactOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) Enable(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "enable")
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) Enable() (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.Enable(&_VRFV2PlusWrapper.TransactOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) Enable() (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.Enable(&_VRFV2PlusWrapper.TransactOpts)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) OnTokenTransfer(opts *bind.TransactOpts, _sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "onTokenTransfer", _sender, _amount, _data)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) OnTokenTransfer(_sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.OnTokenTransfer(&_VRFV2PlusWrapper.TransactOpts, _sender, _amount, _data)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) OnTokenTransfer(_sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.OnTokenTransfer(&_VRFV2PlusWrapper.TransactOpts, _sender, _amount, _data)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) RawFulfillRandomWords(opts *bind.TransactOpts, requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "rawFulfillRandomWords", requestId, randomWords)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) RawFulfillRandomWords(requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.RawFulfillRandomWords(&_VRFV2PlusWrapper.TransactOpts, requestId, randomWords)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) RawFulfillRandomWords(requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.RawFulfillRandomWords(&_VRFV2PlusWrapper.TransactOpts, requestId, randomWords)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) RequestRandomWordsInNative(opts *bind.TransactOpts, _callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "requestRandomWordsInNative", _callbackGasLimit, _requestConfirmations, _numWords, extraArgs)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) RequestRandomWordsInNative(_callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.RequestRandomWordsInNative(&_VRFV2PlusWrapper.TransactOpts, _callbackGasLimit, _requestConfirmations, _numWords, extraArgs)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) RequestRandomWordsInNative(_callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.RequestRandomWordsInNative(&_VRFV2PlusWrapper.TransactOpts, _callbackGasLimit, _requestConfirmations, _numWords, extraArgs)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) SetConfig(opts *bind.TransactOpts, _wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "setConfig", _wrapperGasOverhead, _coordinatorGasOverheadNative, _coordinatorGasOverheadLink, _coordinatorGasOverheadPerWord, _coordinatorNativePremiumPercentage, _coordinatorLinkPremiumPercentage, _keyHash, _maxNumWords, _stalenessSeconds, _fallbackWeiPerUnitLink, _fulfillmentFlatFeeNativePPM, _fulfillmentFlatFeeLinkDiscountPPM)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) SetConfig(_wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.SetConfig(&_VRFV2PlusWrapper.TransactOpts, _wrapperGasOverhead, _coordinatorGasOverheadNative, _coordinatorGasOverheadLink, _coordinatorGasOverheadPerWord, _coordinatorNativePremiumPercentage, _coordinatorLinkPremiumPercentage, _keyHash, _maxNumWords, _stalenessSeconds, _fallbackWeiPerUnitLink, _fulfillmentFlatFeeNativePPM, _fulfillmentFlatFeeLinkDiscountPPM)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) SetConfig(_wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.SetConfig(&_VRFV2PlusWrapper.TransactOpts, _wrapperGasOverhead, _coordinatorGasOverheadNative, _coordinatorGasOverheadLink, _coordinatorGasOverheadPerWord, _coordinatorNativePremiumPercentage, _coordinatorLinkPremiumPercentage, _keyHash, _maxNumWords, _stalenessSeconds, _fallbackWeiPerUnitLink, _fulfillmentFlatFeeNativePPM, _fulfillmentFlatFeeLinkDiscountPPM)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) SetCoordinator(opts *bind.TransactOpts, _vrfCoordinator common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "setCoordinator", _vrfCoordinator)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) SetCoordinator(_vrfCoordinator common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.SetCoordinator(&_VRFV2PlusWrapper.TransactOpts, _vrfCoordinator)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) SetCoordinator(_vrfCoordinator common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.SetCoordinator(&_VRFV2PlusWrapper.TransactOpts, _vrfCoordinator)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) SetFulfillmentTxSize(opts *bind.TransactOpts, _size uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "setFulfillmentTxSize", _size)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) SetFulfillmentTxSize(_size uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.SetFulfillmentTxSize(&_VRFV2PlusWrapper.TransactOpts, _size)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) SetFulfillmentTxSize(_size uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.SetFulfillmentTxSize(&_VRFV2PlusWrapper.TransactOpts, _size)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "transferOwnership", to)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.TransferOwnership(&_VRFV2PlusWrapper.TransactOpts, to)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.TransferOwnership(&_VRFV2PlusWrapper.TransactOpts, to)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) Withdraw(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "withdraw", _recipient)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) Withdraw(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.Withdraw(&_VRFV2PlusWrapper.TransactOpts, _recipient)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) Withdraw(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.Withdraw(&_VRFV2PlusWrapper.TransactOpts, _recipient)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactor) WithdrawNative(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.contract.Transact(opts, "withdrawNative", _recipient)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperSession) WithdrawNative(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.WithdrawNative(&_VRFV2PlusWrapper.TransactOpts, _recipient)
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperTransactorSession) WithdrawNative(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapper.Contract.WithdrawNative(&_VRFV2PlusWrapper.TransactOpts, _recipient)
}

type VRFV2PlusWrapperConfigSetIterator struct {
	Event *VRFV2PlusWrapperConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperConfigSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperConfigSet struct {
	WrapperGasOverhead                 uint32
	CoordinatorGasOverheadNative       uint32
	CoordinatorGasOverheadLink         uint32
	CoordinatorGasOverheadPerWord      uint16
	CoordinatorNativePremiumPercentage uint8
	CoordinatorLinkPremiumPercentage   uint8
	KeyHash                            [32]byte
	MaxNumWords                        uint8
	StalenessSeconds                   uint32
	FallbackWeiPerUnitLink             *big.Int
	FulfillmentFlatFeeNativePPM        uint32
	FulfillmentFlatFeeLinkDiscountPPM  uint32
	Raw                                types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterConfigSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperConfigSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperConfigSetIterator{contract: _VRFV2PlusWrapper.contract, event: "ConfigSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperConfigSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperConfigSet)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "ConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseConfigSet(log types.Log) (*VRFV2PlusWrapperConfigSet, error) {
	event := new(VRFV2PlusWrapperConfigSet)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "ConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperCoordinatorSetIterator struct {
	Event *VRFV2PlusWrapperCoordinatorSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperCoordinatorSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperCoordinatorSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperCoordinatorSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperCoordinatorSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperCoordinatorSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperCoordinatorSet struct {
	VrfCoordinator common.Address
	Raw            types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterCoordinatorSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperCoordinatorSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "CoordinatorSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperCoordinatorSetIterator{contract: _VRFV2PlusWrapper.contract, event: "CoordinatorSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchCoordinatorSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperCoordinatorSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "CoordinatorSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperCoordinatorSet)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "CoordinatorSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseCoordinatorSet(log types.Log) (*VRFV2PlusWrapperCoordinatorSet, error) {
	event := new(VRFV2PlusWrapperCoordinatorSet)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "CoordinatorSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperDisabledIterator struct {
	Event *VRFV2PlusWrapperDisabled

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperDisabledIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperDisabled)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperDisabled)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperDisabledIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperDisabledIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperDisabled struct {
	Raw types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterDisabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperDisabledIterator, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "Disabled")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperDisabledIterator{contract: _VRFV2PlusWrapper.contract, event: "Disabled", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchDisabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperDisabled) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "Disabled")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperDisabled)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "Disabled", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseDisabled(log types.Log) (*VRFV2PlusWrapperDisabled, error) {
	event := new(VRFV2PlusWrapperDisabled)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "Disabled", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperEnabledIterator struct {
	Event *VRFV2PlusWrapperEnabled

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperEnabledIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperEnabled)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperEnabled)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperEnabledIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperEnabledIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperEnabled struct {
	Raw types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterEnabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperEnabledIterator, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "Enabled")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperEnabledIterator{contract: _VRFV2PlusWrapper.contract, event: "Enabled", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchEnabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperEnabled) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "Enabled")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperEnabled)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "Enabled", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseEnabled(log types.Log) (*VRFV2PlusWrapperEnabled, error) {
	event := new(VRFV2PlusWrapperEnabled)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "Enabled", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperFallbackWeiPerUnitLinkUsedIterator struct {
	Event *VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperFallbackWeiPerUnitLinkUsedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperFallbackWeiPerUnitLinkUsedIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperFallbackWeiPerUnitLinkUsedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed struct {
	RequestId              *big.Int
	FallbackWeiPerUnitLink *big.Int
	Raw                    types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterFallbackWeiPerUnitLinkUsed(opts *bind.FilterOpts) (*VRFV2PlusWrapperFallbackWeiPerUnitLinkUsedIterator, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "FallbackWeiPerUnitLinkUsed")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperFallbackWeiPerUnitLinkUsedIterator{contract: _VRFV2PlusWrapper.contract, event: "FallbackWeiPerUnitLinkUsed", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchFallbackWeiPerUnitLinkUsed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "FallbackWeiPerUnitLinkUsed")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "FallbackWeiPerUnitLinkUsed", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseFallbackWeiPerUnitLinkUsed(log types.Log) (*VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed, error) {
	event := new(VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "FallbackWeiPerUnitLinkUsed", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperFulfillmentTxSizeSetIterator struct {
	Event *VRFV2PlusWrapperFulfillmentTxSizeSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperFulfillmentTxSizeSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperFulfillmentTxSizeSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperFulfillmentTxSizeSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperFulfillmentTxSizeSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperFulfillmentTxSizeSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperFulfillmentTxSizeSet struct {
	Size uint32
	Raw  types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterFulfillmentTxSizeSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperFulfillmentTxSizeSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "FulfillmentTxSizeSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperFulfillmentTxSizeSetIterator{contract: _VRFV2PlusWrapper.contract, event: "FulfillmentTxSizeSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchFulfillmentTxSizeSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperFulfillmentTxSizeSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "FulfillmentTxSizeSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperFulfillmentTxSizeSet)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "FulfillmentTxSizeSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseFulfillmentTxSizeSet(log types.Log) (*VRFV2PlusWrapperFulfillmentTxSizeSet, error) {
	event := new(VRFV2PlusWrapperFulfillmentTxSizeSet)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "FulfillmentTxSizeSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperNativeWithdrawnIterator struct {
	Event *VRFV2PlusWrapperNativeWithdrawn

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperNativeWithdrawnIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperNativeWithdrawn)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperNativeWithdrawn)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperNativeWithdrawnIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperNativeWithdrawnIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperNativeWithdrawn struct {
	To     common.Address
	Amount *big.Int
	Raw    types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterNativeWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperNativeWithdrawnIterator, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "NativeWithdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperNativeWithdrawnIterator{contract: _VRFV2PlusWrapper.contract, event: "NativeWithdrawn", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchNativeWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperNativeWithdrawn, to []common.Address) (event.Subscription, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "NativeWithdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperNativeWithdrawn)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "NativeWithdrawn", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseNativeWithdrawn(log types.Log) (*VRFV2PlusWrapperNativeWithdrawn, error) {
	event := new(VRFV2PlusWrapperNativeWithdrawn)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "NativeWithdrawn", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOwnershipTransferRequestedIterator struct {
	Event *VRFV2PlusWrapperOwnershipTransferRequested

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOwnershipTransferRequestedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOwnershipTransferRequestedIterator{contract: _VRFV2PlusWrapper.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOwnershipTransferRequested)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseOwnershipTransferRequested(log types.Log) (*VRFV2PlusWrapperOwnershipTransferRequested, error) {
	event := new(VRFV2PlusWrapperOwnershipTransferRequested)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOwnershipTransferredIterator struct {
	Event *VRFV2PlusWrapperOwnershipTransferred

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOwnershipTransferredIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOwnershipTransferredIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOwnershipTransferredIterator{contract: _VRFV2PlusWrapper.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOwnershipTransferred)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseOwnershipTransferred(log types.Log) (*VRFV2PlusWrapperOwnershipTransferred, error) {
	event := new(VRFV2PlusWrapperOwnershipTransferred)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperWithdrawnIterator struct {
	Event *VRFV2PlusWrapperWithdrawn

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperWithdrawnIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperWithdrawn)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperWithdrawn)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperWithdrawnIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperWithdrawnIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperWithdrawn struct {
	To     common.Address
	Amount *big.Int
	Raw    types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperWithdrawnIterator, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "Withdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperWithdrawnIterator{contract: _VRFV2PlusWrapper.contract, event: "Withdrawn", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperWithdrawn, to []common.Address) (event.Subscription, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "Withdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperWithdrawn)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "Withdrawn", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseWithdrawn(log types.Log) (*VRFV2PlusWrapperWithdrawn, error) {
	event := new(VRFV2PlusWrapperWithdrawn)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "Withdrawn", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperWrapperFulfillmentFailedIterator struct {
	Event *VRFV2PlusWrapperWrapperFulfillmentFailed

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperWrapperFulfillmentFailedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperWrapperFulfillmentFailed)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperWrapperFulfillmentFailed)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperWrapperFulfillmentFailedIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperWrapperFulfillmentFailedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperWrapperFulfillmentFailed struct {
	RequestId *big.Int
	Consumer  common.Address
	Raw       types.Log
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) FilterWrapperFulfillmentFailed(opts *bind.FilterOpts, requestId []*big.Int, consumer []common.Address) (*VRFV2PlusWrapperWrapperFulfillmentFailedIterator, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}
	var consumerRule []interface{}
	for _, consumerItem := range consumer {
		consumerRule = append(consumerRule, consumerItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.FilterLogs(opts, "WrapperFulfillmentFailed", requestIdRule, consumerRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperWrapperFulfillmentFailedIterator{contract: _VRFV2PlusWrapper.contract, event: "WrapperFulfillmentFailed", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) WatchWrapperFulfillmentFailed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperWrapperFulfillmentFailed, requestId []*big.Int, consumer []common.Address) (event.Subscription, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}
	var consumerRule []interface{}
	for _, consumerItem := range consumer {
		consumerRule = append(consumerRule, consumerItem)
	}

	logs, sub, err := _VRFV2PlusWrapper.contract.WatchLogs(opts, "WrapperFulfillmentFailed", requestIdRule, consumerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperWrapperFulfillmentFailed)
				if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "WrapperFulfillmentFailed", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapperFilterer) ParseWrapperFulfillmentFailed(log types.Log) (*VRFV2PlusWrapperWrapperFulfillmentFailed, error) {
	event := new(VRFV2PlusWrapperWrapperFulfillmentFailed)
	if err := _VRFV2PlusWrapper.contract.UnpackLog(event, "WrapperFulfillmentFailed", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type GetConfig struct {
	FallbackWeiPerUnitLink            *big.Int
	StalenessSeconds                  uint32
	FulfillmentFlatFeeNativePPM       uint32
	FulfillmentFlatFeeLinkDiscountPPM uint32
	WrapperGasOverhead                uint32
	CoordinatorGasOverheadNative      uint32
	CoordinatorGasOverheadLink        uint32
	CoordinatorGasOverheadPerWord     uint16
	WrapperNativePremiumPercentage    uint8
	WrapperLinkPremiumPercentage      uint8
	KeyHash                           [32]byte
	MaxNumWords                       uint8
}
type SCallbacks struct {
	CallbackAddress  common.Address
	CallbackGasLimit uint32
	RequestGasPrice  uint64
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapper) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _VRFV2PlusWrapper.abi.Events["ConfigSet"].ID:
		return _VRFV2PlusWrapper.ParseConfigSet(log)
	case _VRFV2PlusWrapper.abi.Events["CoordinatorSet"].ID:
		return _VRFV2PlusWrapper.ParseCoordinatorSet(log)
	case _VRFV2PlusWrapper.abi.Events["Disabled"].ID:
		return _VRFV2PlusWrapper.ParseDisabled(log)
	case _VRFV2PlusWrapper.abi.Events["Enabled"].ID:
		return _VRFV2PlusWrapper.ParseEnabled(log)
	case _VRFV2PlusWrapper.abi.Events["FallbackWeiPerUnitLinkUsed"].ID:
		return _VRFV2PlusWrapper.ParseFallbackWeiPerUnitLinkUsed(log)
	case _VRFV2PlusWrapper.abi.Events["FulfillmentTxSizeSet"].ID:
		return _VRFV2PlusWrapper.ParseFulfillmentTxSizeSet(log)
	case _VRFV2PlusWrapper.abi.Events["NativeWithdrawn"].ID:
		return _VRFV2PlusWrapper.ParseNativeWithdrawn(log)
	case _VRFV2PlusWrapper.abi.Events["OwnershipTransferRequested"].ID:
		return _VRFV2PlusWrapper.ParseOwnershipTransferRequested(log)
	case _VRFV2PlusWrapper.abi.Events["OwnershipTransferred"].ID:
		return _VRFV2PlusWrapper.ParseOwnershipTransferred(log)
	case _VRFV2PlusWrapper.abi.Events["Withdrawn"].ID:
		return _VRFV2PlusWrapper.ParseWithdrawn(log)
	case _VRFV2PlusWrapper.abi.Events["WrapperFulfillmentFailed"].ID:
		return _VRFV2PlusWrapper.ParseWrapperFulfillmentFailed(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (VRFV2PlusWrapperConfigSet) Topic() common.Hash {
	return common.HexToHash("0x8aee1a8c131eaf1a5bd30594737b6926a7c5cb29281a97639f6ac93947c8995e")
}

func (VRFV2PlusWrapperCoordinatorSet) Topic() common.Hash {
	return common.HexToHash("0xd1a6a14209a385a964d036e404cb5cfb71f4000cdb03c9366292430787261be6")
}

func (VRFV2PlusWrapperDisabled) Topic() common.Hash {
	return common.HexToHash("0x75884cdadc4a89e8b545db800057f06ec7f5338a08183c7ba515f2bfdd9fe1e1")
}

func (VRFV2PlusWrapperEnabled) Topic() common.Hash {
	return common.HexToHash("0xc0f961051f97b04c496472d11cb6170d844e4b2c9dfd3b602a4fa0139712d484")
}

func (VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed) Topic() common.Hash {
	return common.HexToHash("0x6ca648a381f22ead7e37773d934e64885dcf861fbfbb26c40354cbf0c4662d1a")
}

func (VRFV2PlusWrapperFulfillmentTxSizeSet) Topic() common.Hash {
	return common.HexToHash("0x697b48b8b76cebb09a54ec4ff810e8a181c96f65395d51c744db09c115d1d5d0")
}

func (VRFV2PlusWrapperNativeWithdrawn) Topic() common.Hash {
	return common.HexToHash("0xc303ca808382409472acbbf899c316cf439f409f6584aae22df86dfa3c9ed504")
}

func (VRFV2PlusWrapperOwnershipTransferRequested) Topic() common.Hash {
	return common.HexToHash("0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278")
}

func (VRFV2PlusWrapperOwnershipTransferred) Topic() common.Hash {
	return common.HexToHash("0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0")
}

func (VRFV2PlusWrapperWithdrawn) Topic() common.Hash {
	return common.HexToHash("0x7084f5476618d8e60b11ef0d7d3f06914655adb8793e28ff7f018d4c76d505d5")
}

func (VRFV2PlusWrapperWrapperFulfillmentFailed) Topic() common.Hash {
	return common.HexToHash("0xc551b83c151f2d1c7eeb938ac59008e0409f1c1dc1e2f112449d4d79b4589022")
}

func (_VRFV2PlusWrapper *VRFV2PlusWrapper) Address() common.Address {
	return _VRFV2PlusWrapper.address
}

type VRFV2PlusWrapperInterface interface {
	SUBSCRIPTIONID(opts *bind.CallOpts) (*big.Int, error)

	CalculateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error)

	CalculateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error)

	CheckPaymentMode(opts *bind.CallOpts, extraArgs []byte, isLinkMode bool) error

	EstimateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error)

	EstimateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error)

	GetConfig(opts *bind.CallOpts) (GetConfig,

		error)

	LastRequestId(opts *bind.CallOpts) (*big.Int, error)

	Link(opts *bind.CallOpts) (common.Address, error)

	LinkNativeFeed(opts *bind.CallOpts) (common.Address, error)

	Owner(opts *bind.CallOpts) (common.Address, error)

	SCallbacks(opts *bind.CallOpts, arg0 *big.Int) (SCallbacks,

		error)

	SConfigured(opts *bind.CallOpts) (bool, error)

	SDisabled(opts *bind.CallOpts) (bool, error)

	SFulfillmentTxSizeBytes(opts *bind.CallOpts) (uint32, error)

	SVrfCoordinator(opts *bind.CallOpts) (common.Address, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error)

	Disable(opts *bind.TransactOpts) (*types.Transaction, error)

	Enable(opts *bind.TransactOpts) (*types.Transaction, error)

	OnTokenTransfer(opts *bind.TransactOpts, _sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error)

	RawFulfillRandomWords(opts *bind.TransactOpts, requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error)

	RequestRandomWordsInNative(opts *bind.TransactOpts, _callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error)

	SetConfig(opts *bind.TransactOpts, _wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error)

	SetCoordinator(opts *bind.TransactOpts, _vrfCoordinator common.Address) (*types.Transaction, error)

	SetFulfillmentTxSize(opts *bind.TransactOpts, _size uint32) (*types.Transaction, error)

	TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error)

	Withdraw(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error)

	WithdrawNative(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error)

	FilterConfigSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperConfigSetIterator, error)

	WatchConfigSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperConfigSet) (event.Subscription, error)

	ParseConfigSet(log types.Log) (*VRFV2PlusWrapperConfigSet, error)

	FilterCoordinatorSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperCoordinatorSetIterator, error)

	WatchCoordinatorSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperCoordinatorSet) (event.Subscription, error)

	ParseCoordinatorSet(log types.Log) (*VRFV2PlusWrapperCoordinatorSet, error)

	FilterDisabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperDisabledIterator, error)

	WatchDisabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperDisabled) (event.Subscription, error)

	ParseDisabled(log types.Log) (*VRFV2PlusWrapperDisabled, error)

	FilterEnabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperEnabledIterator, error)

	WatchEnabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperEnabled) (event.Subscription, error)

	ParseEnabled(log types.Log) (*VRFV2PlusWrapperEnabled, error)

	FilterFallbackWeiPerUnitLinkUsed(opts *bind.FilterOpts) (*VRFV2PlusWrapperFallbackWeiPerUnitLinkUsedIterator, error)

	WatchFallbackWeiPerUnitLinkUsed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed) (event.Subscription, error)

	ParseFallbackWeiPerUnitLinkUsed(log types.Log) (*VRFV2PlusWrapperFallbackWeiPerUnitLinkUsed, error)

	FilterFulfillmentTxSizeSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperFulfillmentTxSizeSetIterator, error)

	WatchFulfillmentTxSizeSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperFulfillmentTxSizeSet) (event.Subscription, error)

	ParseFulfillmentTxSizeSet(log types.Log) (*VRFV2PlusWrapperFulfillmentTxSizeSet, error)

	FilterNativeWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperNativeWithdrawnIterator, error)

	WatchNativeWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperNativeWithdrawn, to []common.Address) (event.Subscription, error)

	ParseNativeWithdrawn(log types.Log) (*VRFV2PlusWrapperNativeWithdrawn, error)

	FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperOwnershipTransferRequestedIterator, error)

	WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferRequested(log types.Log) (*VRFV2PlusWrapperOwnershipTransferRequested, error)

	FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperOwnershipTransferredIterator, error)

	WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferred(log types.Log) (*VRFV2PlusWrapperOwnershipTransferred, error)

	FilterWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperWithdrawnIterator, error)

	WatchWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperWithdrawn, to []common.Address) (event.Subscription, error)

	ParseWithdrawn(log types.Log) (*VRFV2PlusWrapperWithdrawn, error)

	FilterWrapperFulfillmentFailed(opts *bind.FilterOpts, requestId []*big.Int, consumer []common.Address) (*VRFV2PlusWrapperWrapperFulfillmentFailedIterator, error)

	WatchWrapperFulfillmentFailed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperWrapperFulfillmentFailed, requestId []*big.Int, consumer []common.Address) (event.Subscription, error)

	ParseWrapperFulfillmentFailed(log types.Log) (*VRFV2PlusWrapperWrapperFulfillmentFailed, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
