// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.
package weth9_zksync

import (
	"context"
	"crypto/rand"
	"fmt"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/zksync-sdk/zksync2-go/accounts"
	"github.com/zksync-sdk/zksync2-go/clients"
	"github.com/zksync-sdk/zksync2-go/types"
)

func DeployWETH9ZKSyncZk(deployOpts *accounts.TransactOpts, client *clients.Client, wallet *accounts.Wallet, backend bind.ContractBackend, args ...interface{}) (common.Address, *types.Receipt, *WETH9ZKSync, error) {
	var calldata []byte
	if len(args) > 0 {
		abi, err := WETH9ZKSyncMetaData.GetAbi()
		if err != nil {
			return common.Address{}, nil, nil, err
		}
		calldata, err = abi.Pack("", args...)
		if err != nil {
			return common.Address{}, nil, nil, err
		}
	}

	salt := make([]byte, 32)
	n, err := rand.Read(salt)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if n != len(salt) {
		return common.Address{}, nil, nil, fmt.Errorf("failed to read random bytes: expected %d, got %d", len(salt), n)
	}

	txHash, err := wallet.Deploy(deployOpts, accounts.Create2Transaction{
		Bytecode: ZkBytecode,
		Calldata: calldata,
		Salt:     salt,
	})
	if err != nil {
		return common.Address{}, nil, nil, err
	}

	receipt, err := client.WaitMined(context.Background(), txHash)
	if err != nil {
		return common.Address{}, nil, nil, err
	}

	address := receipt.ContractAddress
	contract, err := NewWETH9ZKSync(address, backend)
	if err != nil {
		return common.Address{}, nil, nil, err
	}

	return address, receipt, contract, nil
}

var ZkBytecode = common.Hex2Bytes("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")
