// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.
package mock_v3_aggregator_contract

import (
	"context"
	"crypto/rand"
	"fmt"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/zksync-sdk/zksync2-go/accounts"
	"github.com/zksync-sdk/zksync2-go/clients"
	"github.com/zksync-sdk/zksync2-go/types"
)

func DeployMockV3AggregatorZk(deployOpts *accounts.TransactOpts, client *clients.Client, wallet *accounts.Wallet, backend bind.ContractBackend, args ...interface{}) (common.Address, *types.Receipt, *MockV3Aggregator, error) {
	var calldata []byte
	if len(args) > 0 {
		abi, err := MockV3AggregatorMetaData.GetAbi()
		if err != nil {
			return common.Address{}, nil, nil, err
		}
		calldata, err = abi.Pack("", args...)
		if err != nil {
			return common.Address{}, nil, nil, err
		}
	}

	salt := make([]byte, 32)
	n, err := rand.Read(salt)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if n != len(salt) {
		return common.Address{}, nil, nil, fmt.Errorf("failed to read random bytes: expected %d, got %d", len(salt), n)
	}

	txHash, err := wallet.Deploy(deployOpts, accounts.Create2Transaction{
		Bytecode: ZkBytecode,
		Calldata: calldata,
		Salt:     salt,
	})
	if err != nil {
		return common.Address{}, nil, nil, err
	}

	receipt, err := client.WaitMined(context.Background(), txHash)
	if err != nil {
		return common.Address{}, nil, nil, err
	}

	address := receipt.ContractAddress
	contract, err := NewMockV3Aggregator(address, backend)
	if err != nil {
		return common.Address{}, nil, nil, err
	}

	return address, receipt, contract, nil
}

var ZkBytecode = common.Hex2Bytes("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")
