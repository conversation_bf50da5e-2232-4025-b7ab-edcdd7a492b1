#!/usr/bin/env bash

set -e

VERSION="0.0.12"
if [ -f "$(dirname $0)/forge_zksync" ] && "$(dirname $0)/forge_zksync" -V | grep -q "1.0.0-foundry-zksync-v${VERSION}"; then
    echo "Correct forge_zksync version already installed."
    exit 0
fi

if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
    ARCH="amd64"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="darwin"
    ARCH=$(uname -m)
else
    echo "Unsupported OS: $OSTYPE"
    exit 1
fi

ARTIFACT_URL="https://github.com/matter-labs/foundry-zksync/releases/download/foundry-zksync-v${VERSION}/foundry_zksync_v${VERSION}_${OS}_${ARCH}.tar.gz"
THIS_DIR="$(realpath "$(dirname $0)")"

if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    wget -qc $ARTIFACT_URL -O -
elif [[ "$OSTYPE" == "darwin"* ]]; then
    curl $ARTIFACT_URL -L -o-
fi | tar -xz -C $THIS_DIR
rm $THIS_DIR/cast
mv $THIS_DIR/forge $THIS_DIR/forge_zksync
