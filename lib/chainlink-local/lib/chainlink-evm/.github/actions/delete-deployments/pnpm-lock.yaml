lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@actions/core':
        specifier: ^1.10.1
        version: 1.10.1
      '@octokit/action':
        specifier: ^6.1.0
        version: 6.1.0
      '@octokit/plugin-retry':
        specifier: ^6.0.1
        version: 6.0.1(@octokit/core@5.0.0)
      '@octokit/plugin-throttling':
        specifier: ^7.0.0
        version: 7.0.0(@octokit/core@5.0.0)
      ts-node:
        specifier: ^10.9.2
        version: 10.9.2(@types/node@20.12.10)(typescript@5.4.5)
    devDependencies:
      '@octokit/types':
        specifier: ^11.1.0
        version: 11.1.0
      '@types/node':
        specifier: ^20.12.10
        version: 20.12.10
      typescript:
        specifier: ^5.4.5
        version: 5.4.5

packages:

  '@actions/core@1.10.1':
    resolution: {integrity: sha512-3lBR9EDAY+iYIpTnTIXmWcNbX3T2kCkAEQGIQx4NVQ0575nk2k3GRZDTPQG+vVtS2izSLmINlxXf0uLtnrTP+g==}

  '@actions/http-client@2.1.1':
    resolution: {integrity: sha512-qhrkRMB40bbbLo7gF+0vu+X+UawOvQQqNAA/5Unx774RS8poaOhThDOG6BGmxvAnxhQnDp2BG/ZUm65xZILTpw==}

  '@cspotcode/source-map-support@0.8.1':
    resolution: {integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==}
    engines: {node: '>=12'}

  '@jridgewell/resolve-uri@3.1.1':
    resolution: {integrity: sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.4.15':
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  '@jridgewell/trace-mapping@0.3.9':
    resolution: {integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==}

  '@octokit/action@6.1.0':
    resolution: {integrity: sha512-lo+nHx8kAV86bxvOVOI3vFjX3gXPd/L7guAUbvs3pUvnR2KC+R7yjBkA1uACt4gYhs4LcWP3AXSGQzsbeN2XXw==}
    engines: {node: '>= 18'}

  '@octokit/auth-action@4.0.0':
    resolution: {integrity: sha512-sMm9lWZdiX6e89YFaLrgE9EFs94k58BwIkvjOtozNWUqyTmsrnWFr/M5LolaRzZ7Kmb5FbhF9hi7FEeE274SoQ==}
    engines: {node: '>= 18'}

  '@octokit/auth-token@4.0.0':
    resolution: {integrity: sha512-tY/msAuJo6ARbK6SPIxZrPBms3xPbfwBrulZe0Wtr/DIY9lje2HeV1uoebShn6mx7SjCHif6EjMvoREj+gZ+SA==}
    engines: {node: '>= 18'}

  '@octokit/core@5.0.0':
    resolution: {integrity: sha512-YbAtMWIrbZ9FCXbLwT9wWB8TyLjq9mxpKdgB3dUNxQcIVTf9hJ70gRPwAcqGZdY6WdJPZ0I7jLaaNDCiloGN2A==}
    engines: {node: '>= 18'}

  '@octokit/endpoint@9.0.0':
    resolution: {integrity: sha512-szrQhiqJ88gghWY2Htt8MqUDO6++E/EIXqJ2ZEp5ma3uGS46o7LZAzSLt49myB7rT+Hfw5Y6gO3LmOxGzHijAQ==}
    engines: {node: '>= 18'}

  '@octokit/graphql@7.0.1':
    resolution: {integrity: sha512-T5S3oZ1JOE58gom6MIcrgwZXzTaxRnxBso58xhozxHpOqSTgDS6YNeEUvZ/kRvXgPrRz/KHnZhtb7jUMRi9E6w==}
    engines: {node: '>= 18'}

  '@octokit/openapi-types@18.0.0':
    resolution: {integrity: sha512-V8GImKs3TeQRxRtXFpG2wl19V7444NIOTDF24AWuIbmNaNYOQMWRbjcGDXV5B+0n887fgDcuMNOmlul+k+oJtw==}

  '@octokit/openapi-types@20.0.0':
    resolution: {integrity: sha512-EtqRBEjp1dL/15V7WiX5LJMIxxkdiGJnabzYx5Apx4FkQIFgAfKumXeYAqqJCj1s+BMX4cPFIFC4OLCR6stlnA==}

  '@octokit/plugin-paginate-rest@9.2.1':
    resolution: {integrity: sha512-wfGhE/TAkXZRLjksFXuDZdmGnJQHvtU/joFQdweXUgzo1XwvBCD4o4+75NtFfjfLK5IwLf9vHTfSiU3sLRYpRw==}
    engines: {node: '>= 18'}
    peerDependencies:
      '@octokit/core': '5'

  '@octokit/plugin-rest-endpoint-methods@10.4.1':
    resolution: {integrity: sha512-xV1b+ceKV9KytQe3zCVqjg+8GTGfDYwaT1ATU5isiUyVtlVAO3HNdzpS4sr4GBx4hxQ46s7ITtZrAsxG22+rVg==}
    engines: {node: '>= 18'}
    peerDependencies:
      '@octokit/core': '5'

  '@octokit/plugin-retry@6.0.1':
    resolution: {integrity: sha512-SKs+Tz9oj0g4p28qkZwl/topGcb0k0qPNX/i7vBKmDsjoeqnVfFUquqrE/O9oJY7+oLzdCtkiWSXLpLjvl6uog==}
    engines: {node: '>= 18'}
    peerDependencies:
      '@octokit/core': '>=5'

  '@octokit/plugin-throttling@7.0.0':
    resolution: {integrity: sha512-KL2k/d0uANc8XqP5S64YcNFCudR3F5AaKO39XWdUtlJIjT9Ni79ekWJ6Kj5xvAw87udkOMEPcVf9xEge2+ahew==}
    engines: {node: '>= 18'}
    peerDependencies:
      '@octokit/core': ^5.0.0

  '@octokit/request-error@5.0.0':
    resolution: {integrity: sha512-1ue0DH0Lif5iEqT52+Rf/hf0RmGO9NWFjrzmrkArpG9trFfDM/efx00BJHdLGuro4BR/gECxCU2Twf5OKrRFsQ==}
    engines: {node: '>= 18'}

  '@octokit/request@8.1.1':
    resolution: {integrity: sha512-8N+tdUz4aCqQmXl8FpHYfKG9GelDFd7XGVzyN8rc6WxVlYcfpHECnuRkgquzz+WzvHTK62co5di8gSXnzASZPQ==}
    engines: {node: '>= 18'}

  '@octokit/types@11.1.0':
    resolution: {integrity: sha512-Fz0+7GyLm/bHt8fwEqgvRBWwIV1S6wRRyq+V6exRKLVWaKGsuy6H9QFYeBVDV7rK6fO3XwHgQOPxv+cLj2zpXQ==}

  '@octokit/types@12.6.0':
    resolution: {integrity: sha512-1rhSOfRa6H9w4YwK0yrf5faDaDTb+yLyBUKOCV4xtCDB5VmIPqd/v9yr9o6SAzOAlRxMiRiCic6JVM1/kunVkw==}

  '@tsconfig/node10@1.0.9':
    resolution: {integrity: sha512-jNsYVVxU8v5g43Erja32laIDHXeoNvFEpX33OK4d6hljo3jDhCBDhx5dhCCTMWUojscpAagGiRkBKxpdl9fxqA==}

  '@tsconfig/node12@1.0.11':
    resolution: {integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==}

  '@tsconfig/node14@1.0.3':
    resolution: {integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==}

  '@tsconfig/node16@1.0.4':
    resolution: {integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==}

  '@types/node@20.12.10':
    resolution: {integrity: sha512-Eem5pH9pmWBHoGAT8Dr5fdc5rYA+4NAovdM4EktRPVAAiJhmWWfQrA0cFhAbOsQdSfIHjAud6YdkbL69+zSKjw==}

  acorn-walk@8.2.0:
    resolution: {integrity: sha512-k+iyHEuPgSw6SbuDpGQM+06HQUa04DZ3o+F6CSzXMvvI5KMvnaEqXe+YVe555R9nn6GPt404fos4wcgpw12SDA==}
    engines: {node: '>=0.4.0'}

  acorn@8.10.0:
    resolution: {integrity: sha512-F0SAmZ8iUtS//m8DmCTA0jlh6TDKkHQyK6xc6V4KDTyZKA9dnvX9/3sRTVQrWm79glUAZbnmmNcdYwUIHWVybw==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  arg@4.1.3:
    resolution: {integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==}

  before-after-hook@2.2.3:
    resolution: {integrity: sha512-NzUnlZexiaH/46WDhANlyR2bXRopNg4F/zuSA3OpZnllCUgRaOF2znDioDWrmbNVsuZk6l9pMquQB38cfBZwkQ==}

  bottleneck@2.19.5:
    resolution: {integrity: sha512-VHiNCbI1lKdl44tGrhNfU3lup0Tj/ZBMJB5/2ZbNXRCPuRCO7ed2mgcK4r17y+KB2EfuYuRaVlwNbAeaWGSpbw==}

  create-require@1.1.1:
    resolution: {integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==}

  deprecation@2.3.1:
    resolution: {integrity: sha512-xmHIy4F3scKVwMsQ4WnVaS8bHOx0DmVwRywosKhaILI0ywMDWPtBSku2HNxRvF7jtwDRsoEwYQSfbxj8b7RlJQ==}

  diff@4.0.2:
    resolution: {integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==}
    engines: {node: '>=0.3.1'}

  is-plain-object@5.0.0:
    resolution: {integrity: sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==}
    engines: {node: '>=0.10.0'}

  make-error@1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  ts-node@10.9.2:
    resolution: {integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==}
    hasBin: true
    peerDependencies:
      '@swc/core': '>=1.2.50'
      '@swc/wasm': '>=1.2.50'
      '@types/node': '*'
      typescript: '>=2.7'
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      '@swc/wasm':
        optional: true

  tunnel@0.0.6:
    resolution: {integrity: sha512-1h/Lnq9yajKY2PEbBadPXj3VxsDDu844OnaAo52UVmIzIvwwtBPIuNvkjuzBlTWpfJyUbG3ez0KSBibQkj4ojg==}
    engines: {node: '>=0.6.11 <=0.7.0 || >=0.7.3'}

  typescript@5.4.5:
    resolution: {integrity: sha512-vcI4UpRgg81oIRUFwR0WSIHKt11nJ7SAVlYNIu+QpqeyXP+gpQJy/Z4+F0aGxSE4MqwjyXvW/TzgkLAx2AGHwQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  undici@6.16.0:
    resolution: {integrity: sha512-HQfVddOTb5PJtfLnJ1Px8bNGyIg/z7WTj1hjUSna1Itsv59Oca9JdclIU08ToNqvWWXjFLRzc9rqjnpfw5UWcQ==}
    engines: {node: '>=18.17'}

  universal-user-agent@6.0.0:
    resolution: {integrity: sha512-isyNax3wXoKaulPDZWHQqbmIx1k2tb9fb3GGDBRxCscfYV2Ch7WxPArBsFEG8s/safwXTT7H4QGhaIkTp9447w==}

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  v8-compile-cache-lib@3.0.1:
    resolution: {integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  yn@3.1.1:
    resolution: {integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==}
    engines: {node: '>=6'}

snapshots:

  '@actions/core@1.10.1':
    dependencies:
      '@actions/http-client': 2.1.1
      uuid: 8.3.2

  '@actions/http-client@2.1.1':
    dependencies:
      tunnel: 0.0.6

  '@cspotcode/source-map-support@0.8.1':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.9

  '@jridgewell/resolve-uri@3.1.1': {}

  '@jridgewell/sourcemap-codec@1.4.15': {}

  '@jridgewell/trace-mapping@0.3.9':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.1
      '@jridgewell/sourcemap-codec': 1.4.15

  '@octokit/action@6.1.0':
    dependencies:
      '@octokit/auth-action': 4.0.0
      '@octokit/core': 5.0.0
      '@octokit/plugin-paginate-rest': 9.2.1(@octokit/core@5.0.0)
      '@octokit/plugin-rest-endpoint-methods': 10.4.1(@octokit/core@5.0.0)
      '@octokit/types': 12.6.0
      undici: 6.16.0

  '@octokit/auth-action@4.0.0':
    dependencies:
      '@octokit/auth-token': 4.0.0
      '@octokit/types': 11.1.0

  '@octokit/auth-token@4.0.0': {}

  '@octokit/core@5.0.0':
    dependencies:
      '@octokit/auth-token': 4.0.0
      '@octokit/graphql': 7.0.1
      '@octokit/request': 8.1.1
      '@octokit/request-error': 5.0.0
      '@octokit/types': 11.1.0
      before-after-hook: 2.2.3
      universal-user-agent: 6.0.0

  '@octokit/endpoint@9.0.0':
    dependencies:
      '@octokit/types': 11.1.0
      is-plain-object: 5.0.0
      universal-user-agent: 6.0.0

  '@octokit/graphql@7.0.1':
    dependencies:
      '@octokit/request': 8.1.1
      '@octokit/types': 11.1.0
      universal-user-agent: 6.0.0

  '@octokit/openapi-types@18.0.0': {}

  '@octokit/openapi-types@20.0.0': {}

  '@octokit/plugin-paginate-rest@9.2.1(@octokit/core@5.0.0)':
    dependencies:
      '@octokit/core': 5.0.0
      '@octokit/types': 12.6.0

  '@octokit/plugin-rest-endpoint-methods@10.4.1(@octokit/core@5.0.0)':
    dependencies:
      '@octokit/core': 5.0.0
      '@octokit/types': 12.6.0

  '@octokit/plugin-retry@6.0.1(@octokit/core@5.0.0)':
    dependencies:
      '@octokit/core': 5.0.0
      '@octokit/request-error': 5.0.0
      '@octokit/types': 12.6.0
      bottleneck: 2.19.5

  '@octokit/plugin-throttling@7.0.0(@octokit/core@5.0.0)':
    dependencies:
      '@octokit/core': 5.0.0
      '@octokit/types': 11.1.0
      bottleneck: 2.19.5

  '@octokit/request-error@5.0.0':
    dependencies:
      '@octokit/types': 11.1.0
      deprecation: 2.3.1
      once: 1.4.0

  '@octokit/request@8.1.1':
    dependencies:
      '@octokit/endpoint': 9.0.0
      '@octokit/request-error': 5.0.0
      '@octokit/types': 11.1.0
      is-plain-object: 5.0.0
      universal-user-agent: 6.0.0

  '@octokit/types@11.1.0':
    dependencies:
      '@octokit/openapi-types': 18.0.0

  '@octokit/types@12.6.0':
    dependencies:
      '@octokit/openapi-types': 20.0.0

  '@tsconfig/node10@1.0.9': {}

  '@tsconfig/node12@1.0.11': {}

  '@tsconfig/node14@1.0.3': {}

  '@tsconfig/node16@1.0.4': {}

  '@types/node@20.12.10':
    dependencies:
      undici-types: 5.26.5

  acorn-walk@8.2.0: {}

  acorn@8.10.0: {}

  arg@4.1.3: {}

  before-after-hook@2.2.3: {}

  bottleneck@2.19.5: {}

  create-require@1.1.1: {}

  deprecation@2.3.1: {}

  diff@4.0.2: {}

  is-plain-object@5.0.0: {}

  make-error@1.3.6: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  ts-node@10.9.2(@types/node@20.12.10)(typescript@5.4.5):
    dependencies:
      '@cspotcode/source-map-support': 0.8.1
      '@tsconfig/node10': 1.0.9
      '@tsconfig/node12': 1.0.11
      '@tsconfig/node14': 1.0.3
      '@tsconfig/node16': 1.0.4
      '@types/node': 20.12.10
      acorn: 8.10.0
      acorn-walk: 8.2.0
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 5.4.5
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1

  tunnel@0.0.6: {}

  typescript@5.4.5: {}

  undici-types@5.26.5: {}

  undici@6.16.0: {}

  universal-user-agent@6.0.0: {}

  uuid@8.3.2: {}

  v8-compile-cache-lib@3.0.1: {}

  wrappy@1.0.2: {}

  yn@3.1.1: {}
