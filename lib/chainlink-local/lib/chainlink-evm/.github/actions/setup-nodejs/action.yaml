name: Setup NodeJS
inputs:
  prod:
    default: "false"
    description: Set to 'true' to do a prod only install
  base-path:
    description: Path to the base of the repo
    required: false
    default: .
description: Setup pnpm for contracts
runs:
  using: composite
  steps:
    - uses: pnpm/action-setup@a3252b78c470c02df07e9d59298aecedc3ccdd6d # v3.0.0
      with:
        version: ^10.0.0

    - uses: actions/setup-node@v4
      with:
        node-version: "20"
        cache: "pnpm"
        cache-dependency-path: "${{ inputs.base-path }}/contracts/pnpm-lock.yaml"

    - if: ${{ inputs.prod == 'false' }}
      name: Install dependencies
      shell: bash
      run: pnpm i
      working-directory: ${{ inputs.base-path }}/contracts

    - if: ${{ inputs.prod == 'true' }}
      name: Install prod dependencies
      shell: bash
      run: pnpm i --prod
      working-directory: ${{ inputs.base-path }}/contracts
