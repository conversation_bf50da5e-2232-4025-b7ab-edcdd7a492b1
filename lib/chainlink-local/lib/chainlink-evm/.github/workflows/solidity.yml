name: Solidity

on:
  merge_group:
  push:

defaults:
  run:
    shell: bash

jobs:
  readonly_changes:
    name: Detect readonly solidity file changes
    runs-on: ubuntu-latest
    outputs:
      changes: ${{ steps.ch.outputs.changes }}
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Detect readonly solidity file changes
        id: ch
        uses: ./.github/actions/detect-solidity-readonly-file-changes

  changes:
    name: Detect changes
    runs-on: ubuntu-latest
    outputs:
      changes: ${{ steps.ch.outputs.changes }}
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Detect changes
        id: ch
        uses: ./.github/actions/detect-solidity-file-changes

  tag-check:
    needs: [changes]
    name: Tag Check
    runs-on: ubuntu-latest
    outputs:
      is-release-core: ${{ steps.release-tag-check-core.outputs.is-release }}
      is-pre-release-core: ${{ steps.release-tag-check-core.outputs.is-pre-release }}
      release-version-core: ${{ steps.release-tag-check-core.outputs.release-version }}
      pre-release-version-core: ${{ steps.release-tag-check-core.outputs.pre-release-version }}
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Check release tag (core)
        id: release-tag-check-core
        uses: smartcontractkit/.github/actions/release-tag-check@c5c4a8186da4218cff6cac8184e47dd3dec69ba3 # release-tag-check@0.1.0
        env:
          # Match semver git tags with a "contracts-" prefix.
          RELEASE_REGEX: '^contracts-v[0-9]+\.[0-9]+\.[0-9]+$'
          PRE_RELEASE_REGEX: '^contracts-v[0-9]+\.[0-9]+\.[0-9]+-(.+)$'
          # Get the version by stripping the "contracts-v" prefix.
          VERSION_PREFIX: "contracts-v"

  prepublish-test:
    needs: [changes, tag-check]
    if: needs.changes.outputs.changes == 'true' || (needs.tag-check.outputs.is-pre-release-core == 'true')
    name: Prepublish Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Setup NodeJS
        uses: ./.github/actions/setup-nodejs
      - name: Install Foundry
        uses: ./.github/actions/install-solidity-foundry
      - name: Run Prepublish test
        working-directory: contracts
        run: pnpm prepublishOnly

  native-compile:
    needs: [changes, tag-check]
    if: needs.changes.outputs.changes == 'true' || needs.tag-check.outputs.is-release-core == 'true' || needs.tag-check.outputs.is-pre-release-core == 'true'
    name: Native Compilation
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
          path: chainlink

      - name: Checkout diff-so-fancy
        uses: actions/checkout@v4
        with:
          persist-credentials: false
          repository: so-fancy/diff-so-fancy
          ref: a673cb4d2707f64d92b86498a2f5f71c8e2643d5 # v1.4.3
          path: diff-so-fancy

      - name: Install diff-so-fancy
        run: echo "$GITHUB_WORKSPACE/diff-so-fancy" >> $GITHUB_PATH

      - name: Setup NodeJS
        uses: ./chainlink/.github/actions/setup-nodejs
        with:
          base-path: "chainlink"
          prod: "true"

      - name: Setup Go
        uses: ./chainlink/.github/actions/setup-go
        with:
          go-version-file: "chainlink/go.mod"

      - name: Install Foundry
        uses: ./chainlink/.github/actions/install-solidity-foundry
        with:
          working-directory: chainlink/contracts

      - name: Run native compile and generate wrappers
        working-directory: ./chainlink/contracts
        run: make wrappers-all

      - name: Run zksync compile and generate wrappers
        working-directory: ./chainlink/contracts
        run: make wrappers-zksync

      - name: Check if Go solidity wrappers are updated
        if: ${{ needs.changes.outputs.changes == 'true' }}
        working-directory: chainlink
        run: |
          git add --all
          git diff --minimal --color --cached --exit-code | diff-so-fancy

  wrapper-result-validation:
    if: ${{ always() }}
    needs: [ changes, native-compile ]
    runs-on: ubuntu-latest
    steps:
      - name: Check if Go solidity wrappers are updated
        if: needs.changes.outputs.changes == 'true' && needs.native-compile.result != 'success'
        run: exit 1

  # The if statements for steps after checkout repo is a workaround for
  # passing required check for PRs that don't have filtered changes.
  lint:
    defaults:
      run:
        working-directory: contracts
    needs: [changes]
    if: needs.changes.outputs.changes == 'true'
    name: Solidity Lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Setup NodeJS
        uses: ./.github/actions/setup-nodejs
      - name: Run pnpm lint
        run: pnpm lint
      - name: Run solhint
        run: pnpm solhint
      - name: Run solhint on tests
        run: pnpm solhint-test

  prettier:
    defaults:
      run:
        working-directory: contracts
    needs: [changes]
    if: needs.changes.outputs.changes == 'true'
    name: Prettier Formatting
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Setup NodeJS
        uses: ./.github/actions/setup-nodejs
      - name: Run prettier check
        run: pnpm prettier:check

  publish-beta:
    name: Publish Beta NPM
    environment: publish-contracts
    needs: [tag-check, changes, lint, prettier, native-compile, prepublish-test]
    if: needs.tag-check.outputs.is-pre-release-core == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Setup NodeJS
        uses: ./.github/actions/setup-nodejs

      - name: Install Foundry
        uses: ./.github/actions/install-solidity-foundry

      - name: Run native compile and generate wrappers
        working-directory: contracts
        run: make wrappers-all

      - name: Version package.json
        working-directory: contracts
        shell: bash
        env:
          IS_PRE_RELEASE_CORE: ${{ needs.tag-check.outputs.is-pre-release-core }}
          PRE_RELEASE_VERSION_CORE: ${{ needs.tag-check.outputs.pre-release-version-core }}
        run: |
          version=""
          if [[ $IS_PRE_RELEASE_CORE == 'true' ]]; then
            version="${PRE_RELEASE_VERSION_CORE}"
          else
            echo "::error::No pre-release version found."
            exit 1
          fi
          echo "Bumping version to ${version}"
          pnpm version "${version}" --no-git-tag-version --no-commit-hooks --no-git-checks

      - name: Publish to NPM (beta)
        uses: smartcontractkit/.github/actions/ci-publish-npm@4b0ab756abcb1760cb82e1e87b94ff431905bffc # ci-publish-npm@0.4.0
        with:
          npm-token: ${{ secrets.NPM_CHAINLINK_EVM }}
          create-github-release: false
          publish-command: "pnpm publish-beta --no-git-checks"
          package-json-directory: contracts

  publish-prod:
    name: Publish Prod NPM
    environment: publish-contracts
    needs: [tag-check, changes, lint, prettier, native-compile, prepublish-test]
    if: needs.tag-check.outputs.is-release-core == 'true'
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Setup NodeJS
        uses: ./.github/actions/setup-nodejs

      - name: Install Foundry
        uses: ./.github/actions/install-solidity-foundry

      - name: Run native compile and generate wrappers
        working-directory: contracts
        run: make wrappers-all

      - name: Validate version
        working-directory: contracts
        shell: bash
        env:
          IS_RELEASE_CORE: ${{ needs.tag-check.outputs.is-release-core }}
          RELEASE_VERSION_CORE: ${{ needs.tag-check.outputs.release-version-core }}
        run: |
          VERSION=""
          if [[ "${IS_RELEASE_CORE}" == 'true' ]]; then
            VERSION="${RELEASE_VERSION_CORE}"
          else
            echo "::error::No release version found."
            exit 1
          fi
          PACKAGE_JSON_VERSION="$(jq -r '.version' package.json)"
          if [[ "${PACKAGE_JSON_VERSION}" != "${VERSION}" ]]; then
            echo "::error::Version mismatch: package.json version (${PACKAGE_JSON_VERSION}) does not match version computed from tag ${VERSION}"
            exit 1
          fi

      - name: Publish to NPM (latest)
        uses: smartcontractkit/.github/actions/ci-publish-npm@4b0ab756abcb1760cb82e1e87b94ff431905bffc # ci-publish-npm@0.4.0
        with:
          npm-token: ${{ secrets.NPM_CHAINLINK_EVM }}
          create-github-release: false
          publish-command: "pnpm publish-prod --no-git-checks"
          package-json-directory: contracts
