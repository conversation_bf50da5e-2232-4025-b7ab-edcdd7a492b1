package testutils

import (
	"testing"
	"time"
)

type Awaiter chan struct{}

func NewAwaiter() Awaiter { return make(Awaiter) }

func (a Awaiter) ItHappened() { close(a) }

func (a Awaiter) AssertHappened(t *testing.T, expected bool) {
	t.<PERSON>er()
	select {
	case <-a:
		if !expected {
			t.<PERSON><PERSON>("It happened")
		}
	default:
		if expected {
			t.<PERSON><PERSON>("It didn't happen")
		}
	}
}

func (a Awaiter) AwaitOrFail(t testing.TB, durationParams ...time.Duration) {
	t.Helper()

	duration := 10 * time.Second
	if len(durationParams) > 0 {
		duration = durationParams[0]
	}

	select {
	case <-a:
	case <-time.After(duration):
		t.<PERSON><PERSON>("Timed out waiting for <PERSON><PERSON><PERSON> to get ItHappened")
	}
}
