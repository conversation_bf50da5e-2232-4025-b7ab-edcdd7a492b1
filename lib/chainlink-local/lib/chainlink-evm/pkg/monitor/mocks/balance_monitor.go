// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	common "github.com/ethereum/go-ethereum/common"
	assets "github.com/smartcontractkit/chainlink-evm/pkg/assets"

	context "context"

	mock "github.com/stretchr/testify/mock"

	types "github.com/smartcontractkit/chainlink-evm/pkg/types"
)

// BalanceMonitor is an autogenerated mock type for the BalanceMonitor type
type BalanceMonitor struct {
	mock.Mock
}

type BalanceMonitor_Expecter struct {
	mock *mock.Mock
}

func (_m *BalanceMonitor) EXPECT() *BalanceMonitor_Expecter {
	return &BalanceMonitor_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with no fields
func (_m *BalanceMonitor) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BalanceMonitor_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type BalanceMonitor_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *BalanceMonitor_Expecter) Close() *BalanceMonitor_Close_Call {
	return &BalanceMonitor_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *BalanceMonitor_Close_Call) Run(run func()) *BalanceMonitor_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *BalanceMonitor_Close_Call) Return(_a0 error) *BalanceMonitor_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BalanceMonitor_Close_Call) RunAndReturn(run func() error) *BalanceMonitor_Close_Call {
	_c.Call.Return(run)
	return _c
}

// GetEthBalance provides a mock function with given fields: _a0
func (_m *BalanceMonitor) GetEthBalance(_a0 common.Address) *assets.Eth {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for GetEthBalance")
	}

	var r0 *assets.Eth
	if rf, ok := ret.Get(0).(func(common.Address) *assets.Eth); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*assets.Eth)
		}
	}

	return r0
}

// BalanceMonitor_GetEthBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEthBalance'
type BalanceMonitor_GetEthBalance_Call struct {
	*mock.Call
}

// GetEthBalance is a helper method to define mock.On call
//   - _a0 common.Address
func (_e *BalanceMonitor_Expecter) GetEthBalance(_a0 interface{}) *BalanceMonitor_GetEthBalance_Call {
	return &BalanceMonitor_GetEthBalance_Call{Call: _e.mock.On("GetEthBalance", _a0)}
}

func (_c *BalanceMonitor_GetEthBalance_Call) Run(run func(_a0 common.Address)) *BalanceMonitor_GetEthBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(common.Address))
	})
	return _c
}

func (_c *BalanceMonitor_GetEthBalance_Call) Return(_a0 *assets.Eth) *BalanceMonitor_GetEthBalance_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BalanceMonitor_GetEthBalance_Call) RunAndReturn(run func(common.Address) *assets.Eth) *BalanceMonitor_GetEthBalance_Call {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with no fields
func (_m *BalanceMonitor) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// BalanceMonitor_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type BalanceMonitor_HealthReport_Call struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *BalanceMonitor_Expecter) HealthReport() *BalanceMonitor_HealthReport_Call {
	return &BalanceMonitor_HealthReport_Call{Call: _e.mock.On("HealthReport")}
}

func (_c *BalanceMonitor_HealthReport_Call) Run(run func()) *BalanceMonitor_HealthReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *BalanceMonitor_HealthReport_Call) Return(_a0 map[string]error) *BalanceMonitor_HealthReport_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BalanceMonitor_HealthReport_Call) RunAndReturn(run func() map[string]error) *BalanceMonitor_HealthReport_Call {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with no fields
func (_m *BalanceMonitor) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// BalanceMonitor_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type BalanceMonitor_Name_Call struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *BalanceMonitor_Expecter) Name() *BalanceMonitor_Name_Call {
	return &BalanceMonitor_Name_Call{Call: _e.mock.On("Name")}
}

func (_c *BalanceMonitor_Name_Call) Run(run func()) *BalanceMonitor_Name_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *BalanceMonitor_Name_Call) Return(_a0 string) *BalanceMonitor_Name_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BalanceMonitor_Name_Call) RunAndReturn(run func() string) *BalanceMonitor_Name_Call {
	_c.Call.Return(run)
	return _c
}

// OnNewLongestChain provides a mock function with given fields: ctx, head
func (_m *BalanceMonitor) OnNewLongestChain(ctx context.Context, head *types.Head) {
	_m.Called(ctx, head)
}

// BalanceMonitor_OnNewLongestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnNewLongestChain'
type BalanceMonitor_OnNewLongestChain_Call struct {
	*mock.Call
}

// OnNewLongestChain is a helper method to define mock.On call
//   - ctx context.Context
//   - head *types.Head
func (_e *BalanceMonitor_Expecter) OnNewLongestChain(ctx interface{}, head interface{}) *BalanceMonitor_OnNewLongestChain_Call {
	return &BalanceMonitor_OnNewLongestChain_Call{Call: _e.mock.On("OnNewLongestChain", ctx, head)}
}

func (_c *BalanceMonitor_OnNewLongestChain_Call) Run(run func(ctx context.Context, head *types.Head)) *BalanceMonitor_OnNewLongestChain_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*types.Head))
	})
	return _c
}

func (_c *BalanceMonitor_OnNewLongestChain_Call) Return() *BalanceMonitor_OnNewLongestChain_Call {
	_c.Call.Return()
	return _c
}

func (_c *BalanceMonitor_OnNewLongestChain_Call) RunAndReturn(run func(context.Context, *types.Head)) *BalanceMonitor_OnNewLongestChain_Call {
	_c.Run(run)
	return _c
}

// Ready provides a mock function with no fields
func (_m *BalanceMonitor) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BalanceMonitor_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type BalanceMonitor_Ready_Call struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *BalanceMonitor_Expecter) Ready() *BalanceMonitor_Ready_Call {
	return &BalanceMonitor_Ready_Call{Call: _e.mock.On("Ready")}
}

func (_c *BalanceMonitor_Ready_Call) Run(run func()) *BalanceMonitor_Ready_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *BalanceMonitor_Ready_Call) Return(_a0 error) *BalanceMonitor_Ready_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BalanceMonitor_Ready_Call) RunAndReturn(run func() error) *BalanceMonitor_Ready_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *BalanceMonitor) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BalanceMonitor_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type BalanceMonitor_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *BalanceMonitor_Expecter) Start(_a0 interface{}) *BalanceMonitor_Start_Call {
	return &BalanceMonitor_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *BalanceMonitor_Start_Call) Run(run func(_a0 context.Context)) *BalanceMonitor_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *BalanceMonitor_Start_Call) Return(_a0 error) *BalanceMonitor_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BalanceMonitor_Start_Call) RunAndReturn(run func(context.Context) error) *BalanceMonitor_Start_Call {
	_c.Call.Return(run)
	return _c
}

// NewBalanceMonitor creates a new instance of BalanceMonitor. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBalanceMonitor(t interface {
	mock.TestingT
	Cleanup(func())
}) *BalanceMonitor {
	mock := &BalanceMonitor{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
