// Code generated by mockery v2.53.3. DO NOT EDIT.

package mocks

import (
	config "github.com/smartcontractkit/chainlink-evm/pkg/config"
	mock "github.com/stretchr/testify/mock"
)

// ChainScopedConfig is an autogenerated mock type for the ChainScopedConfig type
type ChainScopedConfig struct {
	mock.Mock
}

type ChainScopedConfig_Expecter struct {
	mock *mock.Mock
}

func (_m *ChainScopedConfig) EXPECT() *ChainScopedConfig_Expecter {
	return &ChainScopedConfig_Expecter{mock: &_m.Mock}
}

// EVM provides a mock function with no fields
func (_m *ChainScopedConfig) EVM() config.EVM {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for EVM")
	}

	var r0 config.EVM
	if rf, ok := ret.Get(0).(func() config.EVM); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(config.EVM)
		}
	}

	return r0
}

// ChainScopedConfig_EVM_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EVM'
type ChainScopedConfig_EVM_Call struct {
	*mock.Call
}

// EVM is a helper method to define mock.On call
func (_e *ChainScopedConfig_Expecter) EVM() *ChainScopedConfig_EVM_Call {
	return &ChainScopedConfig_EVM_Call{Call: _e.mock.On("EVM")}
}

func (_c *ChainScopedConfig_EVM_Call) Run(run func()) *ChainScopedConfig_EVM_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ChainScopedConfig_EVM_Call) Return(_a0 config.EVM) *ChainScopedConfig_EVM_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ChainScopedConfig_EVM_Call) RunAndReturn(run func() config.EVM) *ChainScopedConfig_EVM_Call {
	_c.Call.Return(run)
	return _c
}

// NewChainScopedConfig creates a new instance of ChainScopedConfig. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewChainScopedConfig(t interface {
	mock.TestingT
	Cleanup(func())
}) *ChainScopedConfig {
	mock := &ChainScopedConfig{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
