# Polygon has a 1s block time and looser finality guarantees than ethereum.
ChainID = '137'
# It is quite common to see re-orgs on polygon go several hundred blocks deep. See: https://polygonscan.com/blocks_forked
FinalityDepth = 500
FinalityTagEnabled = true
LinkContractAddress = '******************************************'
LogPollInterval = '1s'
MinIncomingConfirmations = 5
NoNewHeadsThreshold = '30s'
# Must be set to something large here because Polygon has so many re-orgs that otherwise we are constantly refetching
RPCBlockQueryDelay = 10
RPCDefaultBatchSize = 100
NoNewFinalizedHeadsThreshold = '6m'

[Transactions]
# Matic nodes under high mempool pressure are liable to drop txes, we need to ensure we keep sending them
# Since re-orgs on Polygon can be so large, we need a large safety buffer to allow time for the queue to clear down before we start dropping transactions
MaxQueued = 5000

[GasEstimator]
# Many Polygon RPC providers set a minimum of 30 GWei on mainnet to prevent spam
PriceDefault = '30 gwei'
PriceMax = '115792089237316195423570985008687907853269984665.640564039457584007913129639935 tether'
# Many Polygon RPC providers set a minimum of 30 GWei on mainnet to prevent spam
PriceMin = '30 gwei'
BumpMin = '20 gwei'
# 10s delay since feeds update every minute in volatile situations
BumpThreshold = 5

[GasEstimator.BlockHistory]
BlockHistorySize = 24

[HeadTracker]
# Polygon suffers from a tremendous number of re-orgs, we need to set this to something very large to be conservative enough
HistoryDepth = 2000

[NodePool]
SyncThreshold = 10
