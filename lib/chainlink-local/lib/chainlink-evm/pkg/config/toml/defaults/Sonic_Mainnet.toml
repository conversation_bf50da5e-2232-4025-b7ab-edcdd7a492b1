ChainId = '146'
FinalityDepth = 10
FinalityTagEnabled = false
LinkContractAddress = '0x71052BAe71C25C78E37fD12E5ff1101A71d9018F'
LogPollInterval = "1s"       #1s block rate
MinIncomingConfirmations = 5
RPCBlockQueryDelay = 10
RPCDefaultBatchSize = 100

[GasEstimator]
Mode = 'FeeHistory'
EIP1559DynamicFees = true
BumpPercent = 10

[GasEstimator.FeeHistory]
CacheTimeout = '2s'

[GasEstimator.BlockHistory]
BlockHistorySize = 100

[HeadTracker]
HistoryDepth = 50

[NodePool]
SyncThreshold = 10

[Transactions]
MaxQueued = 500
