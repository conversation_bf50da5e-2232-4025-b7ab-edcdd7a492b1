# Heco uses BSC's settings.
ChainID = '128'
LinkContractAddress = '0x404460C6A5EdE2D891e8297795264fDe62ADBB75'
LogPollInterval = '3s'
NoNewHeadsThreshold = '30s'
RPCBlockQueryDelay = 2

[GasEstimator]
PriceDefault = '5 gwei'
BumpThreshold = 5

[GasEstimator.BlockHistory]
BlockHistorySize = 24

[OCR]
DatabaseTimeout = '2s'
ContractTransmitterTransmitTimeout = '2s'
ObservationGracePeriod = '500ms'

[NodePool]
SyncThreshold = 10
