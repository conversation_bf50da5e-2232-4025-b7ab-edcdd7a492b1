ChainID = '44787'
ChainType = 'celo'
FinalityTagEnabled = true
FinalityDepth = 2750 # mean finality time of ~37 minutes + 500 block buffer
LinkContractAddress = '0x32E08557B14FaD8908025619797221281D439071'
LogPollInterval = '1s' # 1 sec block rate
NoNewHeadsThreshold = '1m'
MinIncomingConfirmations = 1
NoNewFinalizedHeadsThreshold = '45m' # Set slightly higher than mean finality time

[GasEstimator]
EIP1559DynamicFees = true
PriceMin = '5 gwei' # Mean gas price around 5 gwei and celo txns are extremely cheap at ~0.00088 CELO per txn ($0.000058)
PriceMax = '1000 gwei' # DS&A recommendation

[GasEstimator.BlockHistory]
# Default is 8, which leads to bumpy gas prices. In CCIP
# we want to smooth out the gas prices, so we increase the sample size.
BlockHistorySize = 200

[Transactions]
ResendAfterThreshold = '30s'

[HeadTracker]
HistoryDepth = 300

[NodePool]
SyncThreshold = 10 # recommended for OP stack chains

[OCR]
ContractConfirmations = 1 # recommended for OP stack chains