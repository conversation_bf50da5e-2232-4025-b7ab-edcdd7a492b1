ChainID = '420'
ChainType = 'optimismBedrock'
FinalityDepth = 200
LinkContractAddress = '0xdc2CC710e42857672E7907CF474a69B63B93089f'
LogPollInterval = '2s'
NoNewHeadsThreshold = '40s'
MinIncomingConfirmations = 1

[GasEstimator]
EIP1559DynamicFees = true
PriceMin = '1 wei'
BumpMin = '100 wei'

[GasEstimator.BlockHistory]
BlockHistorySize = 60

[GasEstimator.DAOracle]
OracleType = 'opstack'
OracleAddress = '0x420000000000000000000000000000000000000F'

[Transactions]
ResendAfterThreshold = '30s'

[HeadTracker]
HistoryDepth = 300

[NodePool]
SyncThreshold = 10

[OCR]
ContractConfirmations = 1

[OCR2.Automation]
GasLimit = 6500000
