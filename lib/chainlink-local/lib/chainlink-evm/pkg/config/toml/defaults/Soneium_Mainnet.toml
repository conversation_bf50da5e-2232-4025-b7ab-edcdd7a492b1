ChainID = '1868'
ChainType = 'optimismBedrock'
LinkContractAddress = '0x32D8F819C8080ae44375F8d383Ffd39FC642f3Ec'
FinalityDepth = 200
LogPollInterval = '2s'
NoNewHeadsThreshold = '40s'
MinIncomingConfirmations = 1
NoNewFinalizedHeadsThreshold = '120m'                              # Soneium can take upto 2Hours to finalize
FinalityTagEnabled = true

[GasEstimator]
EIP1559DynamicFees = true
PriceMin = '1 wei'
BumpMin = '1 mwei'

[GasEstimator.BlockHistory]
BlockHistorySize = 60

[GasEstimator.DAOracle]
OracleType = 'opstack'
OracleAddress = '0x420000000000000000000000000000000000000F'

[Transactions]
ResendAfterThreshold = '30s'

[HeadTracker]
HistoryDepth = 300

[NodePool]
SyncThreshold = 10

[OCR]
ContractConfirmations = 1

[OCR2.Automation]
GasLimit = 6500000
