ChainID = '1337'
FinalityDepth = 10
MinIncomingConfirmations = 1
MinContractPayment = '100'
NoNewHeadsThreshold = '0s'

[Transactions]
ReaperThreshold = '0s'
ResendAfterThreshold = '0s'

[GasEstimator]
Mode = 'FixedPrice'
PriceMin = '0'
BumpThreshold = 0
FeeCapDefault = '1 gwei'
TipCapDefault = '1 mwei'
PriceDefault = '1 gwei'
PriceMax = '1 gwei'

[HeadTracker]
HistoryDepth = 10
MaxBufferSize = 100
SamplingInterval = '0s'

[NodePool]
EnforceRepeatableRead = false # disable for simulation to prevent failure of tests with manual commit and reorgs

[OCR]
ContractConfirmations = 1
