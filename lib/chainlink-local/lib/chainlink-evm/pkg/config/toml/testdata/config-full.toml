ChainID = '42'
Enabled = false
AutoCreateKey = false
BlockBackfillDepth = 100
BlockBackfillSkip = true
ChainType = 'Optimism'
FinalityDepth = 42
FinalityTagEnabled = true
FlagsContractAddress = '******************************************'
LinkContractAddress = '0x538aAaB4ea120b2bC2fe5D296852D948F07D849e'
LogBackfillBatchSize = 17
LogPollInterval = '1m0s'
LogKeepBlocksDepth = 100000
LogPrunePageSize = 0
BackupLogPollerBlockDelay = 532
MinIncomingConfirmations = 13
MinContractPayment = '9.223372036854775807 link'
NonceAutoSync = true
NoNewHeadsThreshold = '1m0s'
OperatorFactoryAddress = '0xa5B85635Be42F21f94F28034B7DA440EeFF0F418'
LogBroadcasterEnabled = true
RPCDefaultBatchSize = 17
RPCBlockQueryDelay = 10
FinalizedBlockOffset = 16
NoNewFinalizedHeadsThreshold = '1h0m0s'

[Transactions]
Enabled = true
ForwardersEnabled = true
MaxInFlight = 19
MaxQueued = 99
ReaperInterval = '1m0s'
ReaperThreshold = '1m0s'
ResendAfterThreshold = '1h0m0s'

[Transactions.AutoPurge]
Enabled = false
Threshold = 42
MinAttempts = 13
DetectionApiUrl = 'http://example.net'

[Transactions.TransactionManagerV2]
Enabled = false
BlockTime = '42s'
CustomURL = 'http://txs.org'
DualBroadcast = true

[BalanceMonitor]
Enabled = true

[GasEstimator]
Mode = 'SuggestedPrice'
PriceDefault = '9.223372036854775807 ether'
PriceMax = '281.474976710655 micro'
PriceMin = '13 wei'
LimitDefault = 12
LimitMax = 17
LimitMultiplier = '1.234'
LimitTransfer = 100
EstimateLimit = false
BumpMin = '100 wei'
BumpPercent = 10
BumpThreshold = 6
BumpTxDepth = 6
EIP1559DynamicFees = true
FeeCapDefault = '9.223372036854775807 ether'
TipCapDefault = '2 wei'
TipCapMin = '1 wei'

[GasEstimator.LimitJobType]
OCR = 1001
OCR2 = 1006
DR = 1002
VRF = 1003
FM = 1004
Keeper = 1005

[GasEstimator.BlockHistory]
BatchSize = 17
BlockHistorySize = 12
CheckInclusionBlocks = 18
CheckInclusionPercentile = 19
EIP1559FeeCapBufferBlocks = 13
TransactionPercentile = 15

[GasEstimator.FeeHistory]
CacheTimeout = '1s'

[GasEstimator.DAOracle]
OracleType = 'opstack'
OracleAddress = '******************************************'
CustomGasPriceCalldata = '0x1234asdf'

[HeadTracker]
HistoryDepth = 15
MaxBufferSize = 17
SamplingInterval = '1h0m0s'
MaxAllowedFinalityDepth = 1500
FinalityTagBypass = false
PersistenceEnabled = false

[[KeySpecific]]
Key = '******************************************'

[KeySpecific.GasEstimator]
PriceMax = '79.228162514264337593543950335 gether'

[NodePool]
PollFailureThreshold = 5
PollInterval = '1m0s'
SelectionMode = 'HighestHead'
SyncThreshold = 13
LeaseDuration = '0s'
NodeIsSyncingEnabled = true
FinalizedBlockPollInterval = '1s'
EnforceRepeatableRead = true
DeathDeclarationDelay = '1m0s'
NewHeadsPollInterval = '0s'
VerifyChainID = true

[NodePool.Errors]
NonceTooLow = '(: |^)nonce too low'
NonceTooHigh = '(: |^)nonce too high'
ReplacementTransactionUnderpriced = '(: |^)replacement transaction underpriced'
LimitReached = '(: |^)limit reached'
TransactionAlreadyInMempool = '(: |^)transaction already in mempool'
TerminallyUnderpriced = '(: |^)terminally underpriced'
InsufficientEth = '(: |^)insufficient eth'
TxFeeExceedsCap = '(: |^)tx fee exceeds cap'
L2FeeTooLow = '(: |^)l2 fee too low'
L2FeeTooHigh = '(: |^)l2 fee too high'
L2Full = '(: |^)l2 full'
TransactionAlreadyMined = '(: |^)transaction already mined'
Fatal = '(: |^)fatal'
ServiceUnavailable = '(: |^)service unavailable'
TooManyResults = '(: |^)too many results'

[OCR]
ContractConfirmations = 11
ContractTransmitterTransmitTimeout = '1m0s'
DatabaseTimeout = '1s'
DeltaCOverride = '1h0m0s'
DeltaCJitterOverride = '1s'
ObservationGracePeriod = '1s'

[OCR2]
[OCR2.Automation]
GasLimit = 540

[Workflow]
FromAddress = '******************************************'
ForwarderAddress = '******************************************'
GasLimitDefault = 400000

[[Nodes]]
Name = 'foo'
WSURL = 'wss://web.socket/test/foo'
HTTPURL = 'https://foo.web'
HTTPURLExtraWrite = 'https://foo.web/extra'
SendOnly = false
Order = 0
