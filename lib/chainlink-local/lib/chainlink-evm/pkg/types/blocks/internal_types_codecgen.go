//go:build go1.6
// +build go1.6

// Code generated by codecgen - DO NOT EDIT.

package blocks

import (
	"errors"
	pkg2_common "github.com/ethereum/go-ethereum/common"
	pkg1_hexutil "github.com/ethereum/go-ethereum/common/hexutil"
	codec1978 "github.com/ugorji/go/codec"
	"runtime"
	"sort"
	"strconv"
)

const (
	// ----- content types ----
	codecSelferCcUTF81709 = 1
	codecSelferCcRAW1709  = 255
	// ----- value types used ----
	codecSelferValueTypeArray1709     = 10
	codecSelferValueTypeMap1709       = 9
	codecSelferValueTypeString1709    = 6
	codecSelferValueTypeInt1709       = 2
	codecSelferValueTypeUint1709      = 3
	codecSelferValueTypeFloat1709     = 4
	codecSelferValueTypeNil1709       = 1
	codecSelferBitsize1709            = uint8(32 << (^uint(0) >> 63))
	codecSelferDecContainerLenNil1709 = -2147483648
)

var (
	errCodecSelferOnlyMapOrArrayEncodeToStruct1709                = errors.New(`only encoded map or array can be decoded into a struct`)
	_                                              sort.Interface = nil
)

type codecSelfer1709 struct{}

func codecSelfer1709False() bool { return false }
func codecSelfer1709True() bool  { return true }

type codecSelfer1709stringSlice []string

func (p codecSelfer1709stringSlice) Len() int           { return len(p) }
func (p codecSelfer1709stringSlice) Swap(i, j int)      { p[uint(i)], p[uint(j)] = p[uint(j)], p[uint(i)] }
func (p codecSelfer1709stringSlice) Less(i, j int) bool { return p[uint(i)] < p[uint(j)] }

type codecSelfer1709uint64Slice []uint64

func (p codecSelfer1709uint64Slice) Len() int           { return len(p) }
func (p codecSelfer1709uint64Slice) Swap(i, j int)      { p[uint(i)], p[uint(j)] = p[uint(j)], p[uint(i)] }
func (p codecSelfer1709uint64Slice) Less(i, j int) bool { return p[uint(i)] < p[uint(j)] }

type codecSelfer1709int64Slice []int64

func (p codecSelfer1709int64Slice) Len() int           { return len(p) }
func (p codecSelfer1709int64Slice) Swap(i, j int)      { p[uint(i)], p[uint(j)] = p[uint(j)], p[uint(i)] }
func (p codecSelfer1709int64Slice) Less(i, j int) bool { return p[uint(i)] < p[uint(j)] }

type codecSelfer1709float64Slice []float64

func (p codecSelfer1709float64Slice) Len() int           { return len(p) }
func (p codecSelfer1709float64Slice) Swap(i, j int)      { p[uint(i)], p[uint(j)] = p[uint(j)], p[uint(i)] }
func (p codecSelfer1709float64Slice) Less(i, j int) bool { return p[uint(i)] < p[uint(j)] }

func init() {
	if codec1978.GenVersion != 28 {
		_, file, _, _ := runtime.Caller(0)
		ver := strconv.FormatInt(int64(codec1978.GenVersion), 10)
		panic(errors.New("codecgen version mismatch: current: 28, need " + ver + ". Re-generate file: " + file))
	}
	if false { // reference the types, but skip this branch at build/run time
		var _ pkg2_common.Hash
		var _ pkg1_hexutil.Big
	}
}

func (TxType) codecSelferViaCodecgen() {}
func (x TxType) CodecEncodeSelf(e *codec1978.Encoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Encoder(e)
	_, _, _ = h, z, r
	if !z.EncBinary() {
		z.EncTextMarshal(&x)
	} else {
		r.EncodeUint(uint64(x))
	}
}

func (x *TxType) CodecDecodeSelf(d *codec1978.Decoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Decoder(d)
	_, _, _ = h, z, r
	if !z.DecBinary() && z.IsJSONHandle() {
		z.DecJSONUnmarshal(x)
	} else {
		*x = (TxType)(z.C.UintV(r.DecodeUint64(), 8))
	}
}

func (TransactionInternal) codecSelferViaCodecgen() {}
func (x *TransactionInternal) CodecEncodeSelf(e *codec1978.Encoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Encoder(e)
	_, _, _ = h, z, r
	if z.EncBasicHandle().CheckCircularRef {
		z.EncEncode(x)
		return
	}
	if x == nil {
		r.EncodeNil()
	} else {
		yy2arr2 := z.EncBasicHandle().StructToArray
		_ = yy2arr2
		const yyr2 bool = false // struct tag has 'toArray'
		var yyn3 bool = x.GasPrice == nil
		var yyn4 bool = x.Gas == nil
		var yyn5 bool = x.MaxFeePerGas == nil
		var yyn6 bool = x.MaxPriorityFeePerGas == nil
		var yyn7 bool = x.Type == nil
		if yyr2 || yy2arr2 {
			z.EncWriteArrayStart(6)
			if yyn3 {
				z.EncWriteArrayElem()
				r.EncodeNil()
			} else {
				z.EncWriteArrayElem()
				if yyxt9 := z.Extension(x.GasPrice); yyxt9 != nil {
					z.EncExtension(x.GasPrice, yyxt9)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*x.GasPrice)
				} else {
					z.EncFallback(x.GasPrice)
				}
			}
			if yyn4 {
				z.EncWriteArrayElem()
				r.EncodeNil()
			} else {
				z.EncWriteArrayElem()
				yy10 := *x.Gas
				if yyxt11 := z.Extension(yy10); yyxt11 != nil {
					z.EncExtension(yy10, yyxt11)
				} else if !z.EncBinary() {
					z.EncTextMarshal(yy10)
				} else {
					r.EncodeUint(uint64(yy10))
				}
			}
			if yyn5 {
				z.EncWriteArrayElem()
				r.EncodeNil()
			} else {
				z.EncWriteArrayElem()
				if yyxt12 := z.Extension(x.MaxFeePerGas); yyxt12 != nil {
					z.EncExtension(x.MaxFeePerGas, yyxt12)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*x.MaxFeePerGas)
				} else {
					z.EncFallback(x.MaxFeePerGas)
				}
			}
			if yyn6 {
				z.EncWriteArrayElem()
				r.EncodeNil()
			} else {
				z.EncWriteArrayElem()
				if yyxt13 := z.Extension(x.MaxPriorityFeePerGas); yyxt13 != nil {
					z.EncExtension(x.MaxPriorityFeePerGas, yyxt13)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*x.MaxPriorityFeePerGas)
				} else {
					z.EncFallback(x.MaxPriorityFeePerGas)
				}
			}
			if yyn7 {
				z.EncWriteArrayElem()
				r.EncodeNil()
			} else {
				z.EncWriteArrayElem()
				yy14 := *x.Type
				if yyxt15 := z.Extension(yy14); yyxt15 != nil {
					z.EncExtension(yy14, yyxt15)
				} else {
					yy14.CodecEncodeSelf(e)
				}
			}
			z.EncWriteArrayElem()
			yy16 := &x.Hash
			if yyxt17 := z.Extension(yy16); yyxt17 != nil {
				z.EncExtension(yy16, yyxt17)
			} else if !z.EncBinary() {
				z.EncTextMarshal(*yy16)
			} else {
				z.F.EncSliceUint8V(([]uint8)(yy16[:]), e)
			}
			z.EncWriteArrayEnd()
		} else {
			z.EncWriteMapStart(6)
			if z.EncBasicHandle().Canonical {
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"gas\"")
				z.EncWriteMapElemValue()
				if yyn4 {
					r.EncodeNil()
				} else {
					yy18 := *x.Gas
					if yyxt19 := z.Extension(yy18); yyxt19 != nil {
						z.EncExtension(yy18, yyxt19)
					} else if !z.EncBinary() {
						z.EncTextMarshal(yy18)
					} else {
						r.EncodeUint(uint64(yy18))
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"gasPrice\"")
				z.EncWriteMapElemValue()
				if yyn3 {
					r.EncodeNil()
				} else {
					if yyxt20 := z.Extension(x.GasPrice); yyxt20 != nil {
						z.EncExtension(x.GasPrice, yyxt20)
					} else if !z.EncBinary() {
						z.EncTextMarshal(*x.GasPrice)
					} else {
						z.EncFallback(x.GasPrice)
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"hash\"")
				z.EncWriteMapElemValue()
				yy21 := &x.Hash
				if yyxt22 := z.Extension(yy21); yyxt22 != nil {
					z.EncExtension(yy21, yyxt22)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*yy21)
				} else {
					z.F.EncSliceUint8V(([]uint8)(yy21[:]), e)
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"maxFeePerGas\"")
				z.EncWriteMapElemValue()
				if yyn5 {
					r.EncodeNil()
				} else {
					if yyxt23 := z.Extension(x.MaxFeePerGas); yyxt23 != nil {
						z.EncExtension(x.MaxFeePerGas, yyxt23)
					} else if !z.EncBinary() {
						z.EncTextMarshal(*x.MaxFeePerGas)
					} else {
						z.EncFallback(x.MaxFeePerGas)
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"maxPriorityFeePerGas\"")
				z.EncWriteMapElemValue()
				if yyn6 {
					r.EncodeNil()
				} else {
					if yyxt24 := z.Extension(x.MaxPriorityFeePerGas); yyxt24 != nil {
						z.EncExtension(x.MaxPriorityFeePerGas, yyxt24)
					} else if !z.EncBinary() {
						z.EncTextMarshal(*x.MaxPriorityFeePerGas)
					} else {
						z.EncFallback(x.MaxPriorityFeePerGas)
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"type\"")
				z.EncWriteMapElemValue()
				if yyn7 {
					r.EncodeNil()
				} else {
					yy25 := *x.Type
					if yyxt26 := z.Extension(yy25); yyxt26 != nil {
						z.EncExtension(yy25, yyxt26)
					} else {
						yy25.CodecEncodeSelf(e)
					}
				}
			} else {
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"gasPrice\"")
				z.EncWriteMapElemValue()
				if yyn3 {
					r.EncodeNil()
				} else {
					if yyxt27 := z.Extension(x.GasPrice); yyxt27 != nil {
						z.EncExtension(x.GasPrice, yyxt27)
					} else if !z.EncBinary() {
						z.EncTextMarshal(*x.GasPrice)
					} else {
						z.EncFallback(x.GasPrice)
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"gas\"")
				z.EncWriteMapElemValue()
				if yyn4 {
					r.EncodeNil()
				} else {
					yy28 := *x.Gas
					if yyxt29 := z.Extension(yy28); yyxt29 != nil {
						z.EncExtension(yy28, yyxt29)
					} else if !z.EncBinary() {
						z.EncTextMarshal(yy28)
					} else {
						r.EncodeUint(uint64(yy28))
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"maxFeePerGas\"")
				z.EncWriteMapElemValue()
				if yyn5 {
					r.EncodeNil()
				} else {
					if yyxt30 := z.Extension(x.MaxFeePerGas); yyxt30 != nil {
						z.EncExtension(x.MaxFeePerGas, yyxt30)
					} else if !z.EncBinary() {
						z.EncTextMarshal(*x.MaxFeePerGas)
					} else {
						z.EncFallback(x.MaxFeePerGas)
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"maxPriorityFeePerGas\"")
				z.EncWriteMapElemValue()
				if yyn6 {
					r.EncodeNil()
				} else {
					if yyxt31 := z.Extension(x.MaxPriorityFeePerGas); yyxt31 != nil {
						z.EncExtension(x.MaxPriorityFeePerGas, yyxt31)
					} else if !z.EncBinary() {
						z.EncTextMarshal(*x.MaxPriorityFeePerGas)
					} else {
						z.EncFallback(x.MaxPriorityFeePerGas)
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"type\"")
				z.EncWriteMapElemValue()
				if yyn7 {
					r.EncodeNil()
				} else {
					yy32 := *x.Type
					if yyxt33 := z.Extension(yy32); yyxt33 != nil {
						z.EncExtension(yy32, yyxt33)
					} else {
						yy32.CodecEncodeSelf(e)
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"hash\"")
				z.EncWriteMapElemValue()
				yy34 := &x.Hash
				if yyxt35 := z.Extension(yy34); yyxt35 != nil {
					z.EncExtension(yy34, yyxt35)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*yy34)
				} else {
					z.F.EncSliceUint8V(([]uint8)(yy34[:]), e)
				}
			}
			z.EncWriteMapEnd()
		}
	}
}

func (x *TransactionInternal) CodecDecodeSelf(d *codec1978.Decoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Decoder(d)
	_, _, _ = h, z, r
	yyct2 := r.ContainerType()
	if yyct2 == codecSelferValueTypeNil1709 {
		*(x) = TransactionInternal{}
	} else if yyct2 == codecSelferValueTypeMap1709 {
		yyl2 := z.DecReadMapStart()
		if yyl2 == 0 {
		} else {
			x.codecDecodeSelfFromMap(yyl2, d)
		}
		z.DecReadMapEnd()
	} else if yyct2 == codecSelferValueTypeArray1709 {
		yyl2 := z.DecReadArrayStart()
		if yyl2 != 0 {
			x.codecDecodeSelfFromArray(yyl2, d)
		}
		z.DecReadArrayEnd()
	} else {
		panic(errCodecSelferOnlyMapOrArrayEncodeToStruct1709)
	}
}

func (x *TransactionInternal) codecDecodeSelfFromMap(l int, d *codec1978.Decoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Decoder(d)
	_, _, _ = h, z, r
	var yyhl3 bool = l >= 0
	for yyj3 := 0; z.DecContainerNext(yyj3, l, yyhl3); yyj3++ {
		z.DecReadMapElemKey()
		yys3 := r.DecodeStringAsBytes()
		z.DecReadMapElemValue()
		switch string(yys3) {
		case "gasPrice":
			if r.TryNil() {
				if x.GasPrice != nil { // remove the if-true
					x.GasPrice = nil
				}
			} else {
				if x.GasPrice == nil {
					x.GasPrice = new(pkg1_hexutil.Big)
				}
				if yyxt5 := z.Extension(x.GasPrice); yyxt5 != nil {
					z.DecExtension(x.GasPrice, yyxt5)
				} else if !z.DecBinary() && z.IsJSONHandle() {
					z.DecJSONUnmarshal(x.GasPrice)
				} else {
					z.DecFallback(x.GasPrice, false)
				}
			}
		case "gas":
			if r.TryNil() {
				if x.Gas != nil { // remove the if-true
					x.Gas = nil
				}
			} else {
				if x.Gas == nil {
					x.Gas = new(pkg1_hexutil.Uint64)
				}
				if yyxt7 := z.Extension(x.Gas); yyxt7 != nil {
					z.DecExtension(x.Gas, yyxt7)
				} else if !z.DecBinary() && z.IsJSONHandle() {
					z.DecJSONUnmarshal(x.Gas)
				} else {
					*x.Gas = (pkg1_hexutil.Uint64)(r.DecodeUint64())
				}
			}
		case "maxFeePerGas":
			if r.TryNil() {
				if x.MaxFeePerGas != nil { // remove the if-true
					x.MaxFeePerGas = nil
				}
			} else {
				if x.MaxFeePerGas == nil {
					x.MaxFeePerGas = new(pkg1_hexutil.Big)
				}
				if yyxt9 := z.Extension(x.MaxFeePerGas); yyxt9 != nil {
					z.DecExtension(x.MaxFeePerGas, yyxt9)
				} else if !z.DecBinary() && z.IsJSONHandle() {
					z.DecJSONUnmarshal(x.MaxFeePerGas)
				} else {
					z.DecFallback(x.MaxFeePerGas, false)
				}
			}
		case "maxPriorityFeePerGas":
			if r.TryNil() {
				if x.MaxPriorityFeePerGas != nil { // remove the if-true
					x.MaxPriorityFeePerGas = nil
				}
			} else {
				if x.MaxPriorityFeePerGas == nil {
					x.MaxPriorityFeePerGas = new(pkg1_hexutil.Big)
				}
				if yyxt11 := z.Extension(x.MaxPriorityFeePerGas); yyxt11 != nil {
					z.DecExtension(x.MaxPriorityFeePerGas, yyxt11)
				} else if !z.DecBinary() && z.IsJSONHandle() {
					z.DecJSONUnmarshal(x.MaxPriorityFeePerGas)
				} else {
					z.DecFallback(x.MaxPriorityFeePerGas, false)
				}
			}
		case "type":
			if r.TryNil() {
				if x.Type != nil { // remove the if-true
					x.Type = nil
				}
			} else {
				if x.Type == nil {
					x.Type = new(TxType)
				}
				if yyxt13 := z.Extension(x.Type); yyxt13 != nil {
					z.DecExtension(x.Type, yyxt13)
				} else {
					x.Type.CodecDecodeSelf(d)
				}
			}
		case "hash":
			if yyxt15 := z.Extension(x.Hash); yyxt15 != nil {
				z.DecExtension(&x.Hash, yyxt15)
			} else if !z.DecBinary() && z.IsJSONHandle() {
				z.DecJSONUnmarshal(&x.Hash)
			} else {
				z.F.DecSliceUint8N(([]uint8)(x.Hash[:]), d)
			}
		default:
			z.DecStructFieldNotFound(-1, string(yys3))
		} // end switch yys3
	} // end for yyj3
}

func (x *TransactionInternal) codecDecodeSelfFromArray(l int, d *codec1978.Decoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Decoder(d)
	_, _, _ = h, z, r
	var yyj16 int
	var yyb16 bool
	var yyhl16 bool = l >= 0
	yyb16 = !z.DecContainerNext(yyj16, l, yyhl16)
	if yyb16 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if r.TryNil() {
		if x.GasPrice != nil { // remove the if-true
			x.GasPrice = nil
		}
	} else {
		if x.GasPrice == nil {
			x.GasPrice = new(pkg1_hexutil.Big)
		}
		if yyxt18 := z.Extension(x.GasPrice); yyxt18 != nil {
			z.DecExtension(x.GasPrice, yyxt18)
		} else if !z.DecBinary() && z.IsJSONHandle() {
			z.DecJSONUnmarshal(x.GasPrice)
		} else {
			z.DecFallback(x.GasPrice, false)
		}
	}
	yyj16++
	yyb16 = !z.DecContainerNext(yyj16, l, yyhl16)
	if yyb16 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if r.TryNil() {
		if x.Gas != nil { // remove the if-true
			x.Gas = nil
		}
	} else {
		if x.Gas == nil {
			x.Gas = new(pkg1_hexutil.Uint64)
		}
		if yyxt20 := z.Extension(x.Gas); yyxt20 != nil {
			z.DecExtension(x.Gas, yyxt20)
		} else if !z.DecBinary() && z.IsJSONHandle() {
			z.DecJSONUnmarshal(x.Gas)
		} else {
			*x.Gas = (pkg1_hexutil.Uint64)(r.DecodeUint64())
		}
	}
	yyj16++
	yyb16 = !z.DecContainerNext(yyj16, l, yyhl16)
	if yyb16 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if r.TryNil() {
		if x.MaxFeePerGas != nil { // remove the if-true
			x.MaxFeePerGas = nil
		}
	} else {
		if x.MaxFeePerGas == nil {
			x.MaxFeePerGas = new(pkg1_hexutil.Big)
		}
		if yyxt22 := z.Extension(x.MaxFeePerGas); yyxt22 != nil {
			z.DecExtension(x.MaxFeePerGas, yyxt22)
		} else if !z.DecBinary() && z.IsJSONHandle() {
			z.DecJSONUnmarshal(x.MaxFeePerGas)
		} else {
			z.DecFallback(x.MaxFeePerGas, false)
		}
	}
	yyj16++
	yyb16 = !z.DecContainerNext(yyj16, l, yyhl16)
	if yyb16 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if r.TryNil() {
		if x.MaxPriorityFeePerGas != nil { // remove the if-true
			x.MaxPriorityFeePerGas = nil
		}
	} else {
		if x.MaxPriorityFeePerGas == nil {
			x.MaxPriorityFeePerGas = new(pkg1_hexutil.Big)
		}
		if yyxt24 := z.Extension(x.MaxPriorityFeePerGas); yyxt24 != nil {
			z.DecExtension(x.MaxPriorityFeePerGas, yyxt24)
		} else if !z.DecBinary() && z.IsJSONHandle() {
			z.DecJSONUnmarshal(x.MaxPriorityFeePerGas)
		} else {
			z.DecFallback(x.MaxPriorityFeePerGas, false)
		}
	}
	yyj16++
	yyb16 = !z.DecContainerNext(yyj16, l, yyhl16)
	if yyb16 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if r.TryNil() {
		if x.Type != nil { // remove the if-true
			x.Type = nil
		}
	} else {
		if x.Type == nil {
			x.Type = new(TxType)
		}
		if yyxt26 := z.Extension(x.Type); yyxt26 != nil {
			z.DecExtension(x.Type, yyxt26)
		} else {
			x.Type.CodecDecodeSelf(d)
		}
	}
	yyj16++
	yyb16 = !z.DecContainerNext(yyj16, l, yyhl16)
	if yyb16 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if yyxt28 := z.Extension(x.Hash); yyxt28 != nil {
		z.DecExtension(&x.Hash, yyxt28)
	} else if !z.DecBinary() && z.IsJSONHandle() {
		z.DecJSONUnmarshal(&x.Hash)
	} else {
		z.F.DecSliceUint8N(([]uint8)(x.Hash[:]), d)
	}
	yyj16++
	for ; z.DecContainerNext(yyj16, l, yyhl16); yyj16++ {
		z.DecReadArrayElem()
		z.DecStructFieldNotFound(yyj16-1, "")
	}
}

func (x *TransactionInternal) IsCodecEmpty() bool {
	return !(x.Hash != pkg2_common.Hash{} || false)
}

func (BlockInternal) codecSelferViaCodecgen() {}
func (x *BlockInternal) CodecEncodeSelf(e *codec1978.Encoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Encoder(e)
	_, _, _ = h, z, r
	if z.EncBasicHandle().CheckCircularRef {
		z.EncEncode(x)
		return
	}
	if x == nil {
		r.EncodeNil()
	} else {
		yy2arr2 := z.EncBasicHandle().StructToArray
		_ = yy2arr2
		const yyr2 bool = false // struct tag has 'toArray'
		var yyn6 bool = x.BaseFeePerGas == nil
		if yyr2 || yy2arr2 {
			z.EncWriteArrayStart(6)
			z.EncWriteArrayElem()
			r.EncodeString(string(x.Number))
			z.EncWriteArrayElem()
			yy10 := &x.Hash
			if yyxt11 := z.Extension(yy10); yyxt11 != nil {
				z.EncExtension(yy10, yyxt11)
			} else if !z.EncBinary() {
				z.EncTextMarshal(*yy10)
			} else {
				z.F.EncSliceUint8V(([]uint8)(yy10[:]), e)
			}
			z.EncWriteArrayElem()
			yy12 := &x.ParentHash
			if yyxt13 := z.Extension(yy12); yyxt13 != nil {
				z.EncExtension(yy12, yyxt13)
			} else if !z.EncBinary() {
				z.EncTextMarshal(*yy12)
			} else {
				z.F.EncSliceUint8V(([]uint8)(yy12[:]), e)
			}
			if yyn6 {
				z.EncWriteArrayElem()
				r.EncodeNil()
			} else {
				z.EncWriteArrayElem()
				if yyxt14 := z.Extension(x.BaseFeePerGas); yyxt14 != nil {
					z.EncExtension(x.BaseFeePerGas, yyxt14)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*x.BaseFeePerGas)
				} else {
					z.EncFallback(x.BaseFeePerGas)
				}
			}
			z.EncWriteArrayElem()
			if yyxt15 := z.Extension(x.Timestamp); yyxt15 != nil {
				z.EncExtension(x.Timestamp, yyxt15)
			} else if !z.EncBinary() {
				z.EncTextMarshal(x.Timestamp)
			} else {
				r.EncodeUint(uint64(x.Timestamp))
			}
			z.EncWriteArrayElem()
			if x.Transactions == nil {
				r.EncodeNil()
			} else {
				h.encSliceTransactionInternal(([]TransactionInternal)(x.Transactions), e)
			} // end block: if x.Transactions slice == nil
			z.EncWriteArrayEnd()
		} else {
			z.EncWriteMapStart(6)
			if z.EncBasicHandle().Canonical {
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"baseFeePerGas\"")
				z.EncWriteMapElemValue()
				if yyn6 {
					r.EncodeNil()
				} else {
					if yyxt17 := z.Extension(x.BaseFeePerGas); yyxt17 != nil {
						z.EncExtension(x.BaseFeePerGas, yyxt17)
					} else if !z.EncBinary() {
						z.EncTextMarshal(*x.BaseFeePerGas)
					} else {
						z.EncFallback(x.BaseFeePerGas)
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"hash\"")
				z.EncWriteMapElemValue()
				yy18 := &x.Hash
				if yyxt19 := z.Extension(yy18); yyxt19 != nil {
					z.EncExtension(yy18, yyxt19)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*yy18)
				} else {
					z.F.EncSliceUint8V(([]uint8)(yy18[:]), e)
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"number\"")
				z.EncWriteMapElemValue()
				r.EncodeString(string(x.Number))
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"parentHash\"")
				z.EncWriteMapElemValue()
				yy21 := &x.ParentHash
				if yyxt22 := z.Extension(yy21); yyxt22 != nil {
					z.EncExtension(yy21, yyxt22)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*yy21)
				} else {
					z.F.EncSliceUint8V(([]uint8)(yy21[:]), e)
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"timestamp\"")
				z.EncWriteMapElemValue()
				if yyxt23 := z.Extension(x.Timestamp); yyxt23 != nil {
					z.EncExtension(x.Timestamp, yyxt23)
				} else if !z.EncBinary() {
					z.EncTextMarshal(x.Timestamp)
				} else {
					r.EncodeUint(uint64(x.Timestamp))
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"transactions\"")
				z.EncWriteMapElemValue()
				if x.Transactions == nil {
					r.EncodeNil()
				} else {
					h.encSliceTransactionInternal(([]TransactionInternal)(x.Transactions), e)
				} // end block: if x.Transactions slice == nil
			} else {
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"number\"")
				z.EncWriteMapElemValue()
				r.EncodeString(string(x.Number))
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"hash\"")
				z.EncWriteMapElemValue()
				yy26 := &x.Hash
				if yyxt27 := z.Extension(yy26); yyxt27 != nil {
					z.EncExtension(yy26, yyxt27)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*yy26)
				} else {
					z.F.EncSliceUint8V(([]uint8)(yy26[:]), e)
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"parentHash\"")
				z.EncWriteMapElemValue()
				yy28 := &x.ParentHash
				if yyxt29 := z.Extension(yy28); yyxt29 != nil {
					z.EncExtension(yy28, yyxt29)
				} else if !z.EncBinary() {
					z.EncTextMarshal(*yy28)
				} else {
					z.F.EncSliceUint8V(([]uint8)(yy28[:]), e)
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"baseFeePerGas\"")
				z.EncWriteMapElemValue()
				if yyn6 {
					r.EncodeNil()
				} else {
					if yyxt30 := z.Extension(x.BaseFeePerGas); yyxt30 != nil {
						z.EncExtension(x.BaseFeePerGas, yyxt30)
					} else if !z.EncBinary() {
						z.EncTextMarshal(*x.BaseFeePerGas)
					} else {
						z.EncFallback(x.BaseFeePerGas)
					}
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"timestamp\"")
				z.EncWriteMapElemValue()
				if yyxt31 := z.Extension(x.Timestamp); yyxt31 != nil {
					z.EncExtension(x.Timestamp, yyxt31)
				} else if !z.EncBinary() {
					z.EncTextMarshal(x.Timestamp)
				} else {
					r.EncodeUint(uint64(x.Timestamp))
				}
				z.EncWriteMapElemKey()
				z.EncWr().WriteStr("\"transactions\"")
				z.EncWriteMapElemValue()
				if x.Transactions == nil {
					r.EncodeNil()
				} else {
					h.encSliceTransactionInternal(([]TransactionInternal)(x.Transactions), e)
				} // end block: if x.Transactions slice == nil
			}
			z.EncWriteMapEnd()
		}
	}
}

func (x *BlockInternal) CodecDecodeSelf(d *codec1978.Decoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Decoder(d)
	_, _, _ = h, z, r
	yyct2 := r.ContainerType()
	if yyct2 == codecSelferValueTypeNil1709 {
		*(x) = BlockInternal{}
	} else if yyct2 == codecSelferValueTypeMap1709 {
		yyl2 := z.DecReadMapStart()
		if yyl2 == 0 {
		} else {
			x.codecDecodeSelfFromMap(yyl2, d)
		}
		z.DecReadMapEnd()
	} else if yyct2 == codecSelferValueTypeArray1709 {
		yyl2 := z.DecReadArrayStart()
		if yyl2 != 0 {
			x.codecDecodeSelfFromArray(yyl2, d)
		}
		z.DecReadArrayEnd()
	} else {
		panic(errCodecSelferOnlyMapOrArrayEncodeToStruct1709)
	}
}

func (x *BlockInternal) codecDecodeSelfFromMap(l int, d *codec1978.Decoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Decoder(d)
	_, _, _ = h, z, r
	var yyhl3 bool = l >= 0
	for yyj3 := 0; z.DecContainerNext(yyj3, l, yyhl3); yyj3++ {
		z.DecReadMapElemKey()
		yys3 := r.DecodeStringAsBytes()
		z.DecReadMapElemValue()
		switch string(yys3) {
		case "number":
			x.Number = (string)(z.DecStringZC(r.DecodeStringAsBytes()))
		case "hash":
			if yyxt6 := z.Extension(x.Hash); yyxt6 != nil {
				z.DecExtension(&x.Hash, yyxt6)
			} else if !z.DecBinary() && z.IsJSONHandle() {
				z.DecJSONUnmarshal(&x.Hash)
			} else {
				z.F.DecSliceUint8N(([]uint8)(x.Hash[:]), d)
			}
		case "parentHash":
			if yyxt8 := z.Extension(x.ParentHash); yyxt8 != nil {
				z.DecExtension(&x.ParentHash, yyxt8)
			} else if !z.DecBinary() && z.IsJSONHandle() {
				z.DecJSONUnmarshal(&x.ParentHash)
			} else {
				z.F.DecSliceUint8N(([]uint8)(x.ParentHash[:]), d)
			}
		case "baseFeePerGas":
			if r.TryNil() {
				if x.BaseFeePerGas != nil { // remove the if-true
					x.BaseFeePerGas = nil
				}
			} else {
				if x.BaseFeePerGas == nil {
					x.BaseFeePerGas = new(pkg1_hexutil.Big)
				}
				if yyxt10 := z.Extension(x.BaseFeePerGas); yyxt10 != nil {
					z.DecExtension(x.BaseFeePerGas, yyxt10)
				} else if !z.DecBinary() && z.IsJSONHandle() {
					z.DecJSONUnmarshal(x.BaseFeePerGas)
				} else {
					z.DecFallback(x.BaseFeePerGas, false)
				}
			}
		case "timestamp":
			if yyxt12 := z.Extension(x.Timestamp); yyxt12 != nil {
				z.DecExtension(&x.Timestamp, yyxt12)
			} else if !z.DecBinary() && z.IsJSONHandle() {
				z.DecJSONUnmarshal(&x.Timestamp)
			} else {
				x.Timestamp = (pkg1_hexutil.Uint64)(r.DecodeUint64())
			}
		case "transactions":
			h.decSliceTransactionInternal((*[]TransactionInternal)(&x.Transactions), d)
		default:
			z.DecStructFieldNotFound(-1, string(yys3))
		} // end switch yys3
	} // end for yyj3
}

func (x *BlockInternal) codecDecodeSelfFromArray(l int, d *codec1978.Decoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Decoder(d)
	_, _, _ = h, z, r
	var yyj15 int
	var yyb15 bool
	var yyhl15 bool = l >= 0
	yyb15 = !z.DecContainerNext(yyj15, l, yyhl15)
	if yyb15 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	x.Number = (string)(z.DecStringZC(r.DecodeStringAsBytes()))
	yyj15++
	yyb15 = !z.DecContainerNext(yyj15, l, yyhl15)
	if yyb15 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if yyxt18 := z.Extension(x.Hash); yyxt18 != nil {
		z.DecExtension(&x.Hash, yyxt18)
	} else if !z.DecBinary() && z.IsJSONHandle() {
		z.DecJSONUnmarshal(&x.Hash)
	} else {
		z.F.DecSliceUint8N(([]uint8)(x.Hash[:]), d)
	}
	yyj15++
	yyb15 = !z.DecContainerNext(yyj15, l, yyhl15)
	if yyb15 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if yyxt20 := z.Extension(x.ParentHash); yyxt20 != nil {
		z.DecExtension(&x.ParentHash, yyxt20)
	} else if !z.DecBinary() && z.IsJSONHandle() {
		z.DecJSONUnmarshal(&x.ParentHash)
	} else {
		z.F.DecSliceUint8N(([]uint8)(x.ParentHash[:]), d)
	}
	yyj15++
	yyb15 = !z.DecContainerNext(yyj15, l, yyhl15)
	if yyb15 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if r.TryNil() {
		if x.BaseFeePerGas != nil { // remove the if-true
			x.BaseFeePerGas = nil
		}
	} else {
		if x.BaseFeePerGas == nil {
			x.BaseFeePerGas = new(pkg1_hexutil.Big)
		}
		if yyxt22 := z.Extension(x.BaseFeePerGas); yyxt22 != nil {
			z.DecExtension(x.BaseFeePerGas, yyxt22)
		} else if !z.DecBinary() && z.IsJSONHandle() {
			z.DecJSONUnmarshal(x.BaseFeePerGas)
		} else {
			z.DecFallback(x.BaseFeePerGas, false)
		}
	}
	yyj15++
	yyb15 = !z.DecContainerNext(yyj15, l, yyhl15)
	if yyb15 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	if yyxt24 := z.Extension(x.Timestamp); yyxt24 != nil {
		z.DecExtension(&x.Timestamp, yyxt24)
	} else if !z.DecBinary() && z.IsJSONHandle() {
		z.DecJSONUnmarshal(&x.Timestamp)
	} else {
		x.Timestamp = (pkg1_hexutil.Uint64)(r.DecodeUint64())
	}
	yyj15++
	yyb15 = !z.DecContainerNext(yyj15, l, yyhl15)
	if yyb15 {
		z.DecReadArrayEnd()
		return
	}
	z.DecReadArrayElem()
	h.decSliceTransactionInternal((*[]TransactionInternal)(&x.Transactions), d)
	yyj15++
	for ; z.DecContainerNext(yyj15, l, yyhl15); yyj15++ {
		z.DecReadArrayElem()
		z.DecStructFieldNotFound(yyj15-1, "")
	}
}

func (x *BlockInternal) IsCodecEmpty() bool {
	return !(x.Number != "" || x.Hash != pkg2_common.Hash{} || x.ParentHash != pkg2_common.Hash{} || x.Timestamp != 0 || len(x.Transactions) != 0 || false)
}

func (x codecSelfer1709) encSliceTransactionInternal(v []TransactionInternal, e *codec1978.Encoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Encoder(e)
	_, _, _ = h, z, r
	if v == nil {
		r.EncodeNil()
		return
	}
	z.EncWriteArrayStart(len(v))
	for yyv1 := range v {
		z.EncWriteArrayElem()
		yy2 := &v[yyv1]
		if yyxt3 := z.Extension(yy2); yyxt3 != nil {
			z.EncExtension(yy2, yyxt3)
		} else {
			yy2.CodecEncodeSelf(e)
		}
	}
	z.EncWriteArrayEnd()
}

func (x codecSelfer1709) decSliceTransactionInternal(v *[]TransactionInternal, d *codec1978.Decoder) {
	var h codecSelfer1709
	z, r := codec1978.GenHelper().Decoder(d)
	_, _, _ = h, z, r

	yyv1 := *v
	yyh1, yyl1 := z.DecSliceHelperStart()
	var yyc1 bool
	_ = yyc1
	if yyh1.IsNil {
		if yyv1 != nil {
			yyv1 = nil
			yyc1 = true
		}
	} else if yyl1 == 0 {
		if yyv1 == nil {
			yyv1 = []TransactionInternal{}
			yyc1 = true
		} else if len(yyv1) != 0 {
			yyv1 = yyv1[:0]
			yyc1 = true
		}
	} else {
		yyhl1 := yyl1 > 0
		var yyrl1 int
		_ = yyrl1
		if yyhl1 {
			if yyl1 > cap(yyv1) {
				yyrl1 = z.DecInferLen(yyl1, z.DecBasicHandle().MaxInitLen, 72)
				if yyrl1 <= cap(yyv1) {
					yyv1 = yyv1[:yyrl1]
				} else {
					yyv1 = make([]TransactionInternal, yyrl1)
				}
				yyc1 = true
			} else if yyl1 != len(yyv1) {
				yyv1 = yyv1[:yyl1]
				yyc1 = true
			}
		}
		var yyj1 int
		for yyj1 = 0; z.DecContainerNext(yyj1, yyl1, yyhl1); yyj1++ {
			if yyj1 == 0 && yyv1 == nil {
				if yyhl1 {
					yyrl1 = z.DecInferLen(yyl1, z.DecBasicHandle().MaxInitLen, 72)
				} else {
					yyrl1 = 8
				}
				yyv1 = make([]TransactionInternal, yyrl1)
				yyc1 = true
			}
			yyh1.ElemContainerState(yyj1)
			var yydb1 bool
			if yyj1 >= len(yyv1) {
				yyv1 = append(yyv1, TransactionInternal{})
				yyc1 = true
			}
			if yydb1 {
				z.DecSwallow()
			} else {
				if yyxt3 := z.Extension(yyv1[yyj1]); yyxt3 != nil {
					z.DecExtension(&yyv1[yyj1], yyxt3)
				} else {
					yyv1[yyj1].CodecDecodeSelf(d)
				}
			}
		}
		if yyj1 < len(yyv1) {
			yyv1 = yyv1[:yyj1]
			yyc1 = true
		} else if yyj1 == 0 && yyv1 == nil {
			yyv1 = []TransactionInternal{}
			yyc1 = true
		}
	}
	yyh1.End()
	if yyc1 {
		*v = yyv1
	}
}
