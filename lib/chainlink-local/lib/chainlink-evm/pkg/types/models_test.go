package types

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"math/big"
	"os"
	"strconv"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	gethtypes "github.com/ethereum/go-ethereum/core/types"
	pkgerrors "github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	"github.com/smartcontractkit/chainlink-common/pkg/utils/hex"
	"github.com/smartcontractkit/chainlink-evm/pkg/assets"
	"github.com/smartcontractkit/chainlink-evm/pkg/utils"
	ubig "github.com/smartcontractkit/chainlink-evm/pkg/utils/big"
)

func TestHead_NewHead(t *testing.T) {
	t.<PERSON>l()
	tests := []struct {
		input *big.Int
		want  string
	}{
		{big.NewInt(0), "0"},
		{big.NewInt(0xf), "f"},
		{big.NewInt(0x10), "10"},
	}
	for _, test := range tests {
		t.Run(test.want, func(t *testing.T) {
			num := NewHead(test.input, utils.NewHash(), utils.NewHash(), nil)
			assert.Equal(t, test.want, fmt.Sprintf("%x", num.ToInt()))
		})
	}
}

func TestHead_GreaterThan(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name    string
		left    *Head
		right   *Head
		greater bool
	}{
		{"nil nil", nil, nil, false},
		{"present nil", testHead(1), nil, true},
		{"nil present", nil, testHead(1), false},
		{"less", testHead(1), testHead(2), false},
		{"equal", testHead(2), testHead(2), false},
		{"greater", testHead(2), testHead(1), true},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			assert.Equal(t, test.greater, test.left.GreaterThan(test.right))
		})
	}
}

func TestHead_NextInt(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name string
		bn   *Head
		want *big.Int
	}{
		{"nil", nil, nil},
		{"one", testHead(1), big.NewInt(2)},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			assert.Equal(t, test.want, test.bn.NextInt())
		})
	}
}

func TestHead_ChainLength(t *testing.T) {
	head := Head{}
	head.Parent.Store(&Head{})
	head.Parent.Load().Parent.Store(&Head{})

	assert.Equal(t, uint32(3), head.ChainLength())

	var head2 *Head
	assert.Equal(t, uint32(0), head2.ChainLength())
}

func TestHead_AsSlice(t *testing.T) {
	h1 := &Head{
		Number: 1,
	}
	h2 := &Head{
		Number: 2,
	}
	h2.Parent.Store(h1)
	h3 := &Head{
		Number: 3,
	}
	h3.Parent.Store(h2)

	assert.Empty(t, (*Head)(nil).AsSlice(0))
	assert.Empty(t, (*Head)(nil).AsSlice(1))

	assert.Empty(t, h3.AsSlice(0))
	assert.Equal(t, []*Head{h3}, h3.AsSlice(1))
	assert.Equal(t, []*Head{h3, h2}, h3.AsSlice(2))
	assert.Equal(t, []*Head{h3, h2, h1}, h3.AsSlice(3))
	assert.Equal(t, []*Head{h3, h2, h1}, h3.AsSlice(4))
}

func TestModels_HexToFunctionSelector(t *testing.T) {
	t.Parallel()
	fid := HexToFunctionSelector("0xb3f98adc")
	assert.Equal(t, "0xb3f98adc", fid.String())
}

func TestModels_HexToFunctionSelectorOverflow(t *testing.T) {
	t.Parallel()
	fid := HexToFunctionSelector("0xb3f98adc123456")
	assert.Equal(t, "0xb3f98adc", fid.String())
}

func TestModels_FunctionSelectorUnmarshalJSON(t *testing.T) {
	t.Parallel()
	bytes := []byte(`"0xb3f98adc"`)
	var fid FunctionSelector
	err := json.Unmarshal(bytes, &fid)
	assert.NoError(t, err)
	assert.Equal(t, "0xb3f98adc", fid.String())
}

func TestModels_FunctionSelectorUnmarshalJSONLiteral(t *testing.T) {
	t.Parallel()
	literalSelectorBytes := []byte(`"setBytes(bytes)"`)
	var fid FunctionSelector
	err := json.Unmarshal(literalSelectorBytes, &fid)
	assert.NoError(t, err)
	assert.Equal(t, "0xda359dc8", fid.String())
}

func TestModels_FunctionSelectorUnmarshalJSONError(t *testing.T) {
	t.Parallel()
	bytes := []byte(`"0xb3f98adc123456"`)
	var fid FunctionSelector
	err := json.Unmarshal(bytes, &fid)
	assert.Error(t, err)
}

func TestSafeByteSlice_Success(t *testing.T) {
	tests := []struct {
		ary      UntrustedBytes
		start    int
		end      int
		expected []byte
	}{
		{[]byte{1, 2, 3}, 0, 0, []byte{}},
		{[]byte{1, 2, 3}, 0, 1, []byte{1}},
		{[]byte{1, 2, 3}, 1, 3, []byte{2, 3}},
	}

	for i, test := range tests {
		t.Run(strconv.Itoa(i), func(t *testing.T) {
			actual, err := test.ary.SafeByteSlice(test.start, test.end)
			assert.NoError(t, err)
			assert.Equal(t, test.expected, actual)
		})
	}
}

func TestSafeByteSlice_Error(t *testing.T) {
	tests := []struct {
		ary   UntrustedBytes
		start int
		end   int
	}{
		{[]byte{1, 2, 3}, 2, -1},
		{[]byte{1, 2, 3}, 0, 4},
		{[]byte{1, 2, 3}, 3, 4},
		{[]byte{1, 2, 3}, 3, 2},
		{[]byte{1, 2, 3}, -1, 2},
	}

	for i, test := range tests {
		t.Run(strconv.Itoa(i), func(t *testing.T) {
			actual, err := test.ary.SafeByteSlice(test.start, test.end)
			assert.EqualError(t, err, "out of bounds slice access")
			var expected []byte
			assert.Equal(t, expected, actual)
		})
	}
}

func TestHead_EarliestInChain(t *testing.T) {
	h3 := Head{
		Number: 3,
	}
	h2 := &Head{Number: 2}
	h3.Parent.Store(h2)
	h1 := &Head{Number: 1}
	h2.Parent.Store(h1)

	assert.Equal(t, int64(1), h3.EarliestInChain().BlockNumber())
}

func TestHead_HeadAtHeight(t *testing.T) {
	h1 := &Head{
		Number: 1,
	}
	h2 := &Head{
		Hash:   common.BigToHash(big.NewInt(10)),
		Number: 2,
	}
	h2.Parent.Store(h1)
	h3 := Head{
		Number: 3,
	}
	h3.Parent.Store(h2)

	headAtHeight, err := h3.HeadAtHeight(2)
	require.NoError(t, err)
	assert.Equal(t, h2, headAtHeight)
	_, err = h3.HeadAtHeight(0)
	assert.Error(t, err, "expected to get an error if head is not in the chain")
}

func TestHead_IsInChain(t *testing.T) {
	hash1 := utils.NewHash()
	hash2 := utils.NewHash()
	hash3 := utils.NewHash()
	h1 := &Head{
		Number: 1,
		Hash:   hash1,
	}
	h2 := &Head{
		Hash:       hash2,
		ParentHash: hash1,
		Number:     2,
	}
	h2.Parent.Store(h1)
	h3 := Head{
		Hash:   hash3,
		Number: 3,
	}
	h3.Parent.Store(h2)

	assert.True(t, h3.IsInChain(hash1))
	assert.True(t, h3.IsInChain(hash2))
	assert.True(t, h3.IsInChain(hash3))
	assert.False(t, h3.IsInChain(utils.NewHash()))
	assert.False(t, h3.IsInChain(common.Hash{}))
}

func TestTxReceipt_ReceiptIndicatesRunLogFulfillment(t *testing.T) {
	tests := []struct {
		name string
		path string
		want bool
	}{
		{"basic", "../testdata/jsonrpc/getTransactionReceipt.json", false},
		{"runlog request", "../testdata/jsonrpc/runlogReceipt.json", false},
		{"runlog response", "../testdata/jsonrpc/responseReceipt.json", true},
	}

	for _, test := range tests {
		test := test
		t.Run(test.name, func(t *testing.T) {
			b, err := os.ReadFile(test.path)
			require.NoError(t, err)
			data := gjson.ParseBytes(b)
			var receipt gethtypes.Receipt
			require.NoError(t, json.Unmarshal([]byte(data.Get("result").String()), &receipt))
			require.Equal(t, test.want, ReceiptIndicatesRunLogFulfillment(receipt))
		})
	}
}

func TestHead_UnmarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		json     string
		expected *Head
	}{
		{"geth",
			`{"difficulty":"0xf3a00","extraData":"0xd883010503846765746887676f312e372e318664617277696e","gasLimit":"0xffc001","gasUsed":"0x0","hash":"0x41800b5c3f1717687d85fc9018faac0a6e90b39deaa0b99e7fe4fe796ddeb26a","logsBloom":"0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000","miner":"******************************************","mixHash":"0x0f98b15f1a4901a7e9204f3c500a7bd527b3fb2c3340e12176a44b83e414a69e","nonce":"0x0ece08ea8c49dfd9","number":"0x100","parentHash":"0x41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d","receiptsRoot":"0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421","sha3Uncles":"0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347","size":"0x218","stateRoot":"0xc7b01007a10da045eacb90385887dd0c38fcb5db7393006bdde24b93873c334b","timestamp":"0x58318da2","totalDifficulty":"0x1f3a00","transactions":[],"transactionsRoot":"0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421","uncles":[]}`,
			&Head{
				Hash:             common.HexToHash("0x41800b5c3f1717687d85fc9018faac0a6e90b39deaa0b99e7fe4fe796ddeb26a"),
				Number:           0x100,
				ParentHash:       common.HexToHash("0x41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d"),
				Timestamp:        time.Unix(0x58318da2, 0).UTC(),
				ReceiptsRoot:     common.HexToHash("0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"),
				TransactionsRoot: common.HexToHash("0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"),
				StateRoot:        common.HexToHash("0xc7b01007a10da045eacb90385887dd0c38fcb5db7393006bdde24b93873c334b"),
			},
		},
		{"parity",
			`{"author":"******************************************","difficulty":"0xf3a00","extraData":"0xd883010503846765746887676f312e372e318664617277696e","gasLimit":"0xffc001","gasUsed":"0x0","hash":"0x41800b5c3f1717687d85fc9018faac0a6e90b39deaa0b99e7fe4fe796ddeb26a","logsBloom":"0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000","miner":"******************************************","mixHash":"0x0f98b15f1a4901a7e9204f3c500a7bd527b3fb2c3340e12176a44b83e414a69e","nonce":"0x0ece08ea8c49dfd9","number":"0x100","parentHash":"0x41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d","receiptsRoot":"0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421","sealFields":["0xa00f98b15f1a4901a7e9204f3c500a7bd527b3fb2c3340e12176a44b83e414a69e","0x880ece08ea8c49dfd9"],"sha3Uncles":"0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347","size":"0x218","stateRoot":"0xc7b01007a10da045eacb90385887dd0c38fcb5db7393006bdde24b93873c334b","timestamp":"0x58318da2","totalDifficulty":"0x1f3a00","transactions":[],"transactionsRoot":"0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421","uncles":[]}`,
			&Head{
				Hash:             common.HexToHash("0x41800b5c3f1717687d85fc9018faac0a6e90b39deaa0b99e7fe4fe796ddeb26a"),
				Number:           0x100,
				ParentHash:       common.HexToHash("0x41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d"),
				Timestamp:        time.Unix(0x58318da2, 0).UTC(),
				ReceiptsRoot:     common.HexToHash("0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"),
				TransactionsRoot: common.HexToHash("0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"),
				StateRoot:        common.HexToHash("0xc7b01007a10da045eacb90385887dd0c38fcb5db7393006bdde24b93873c334b"),
			},
		},
		{"arbitrum",
			`{"number":"0x15156","hash":"0x752dab43f7a2482db39227d46cd307623b26167841e2207e93e7566ab7ab7871","parentHash":"0x923ad1e27c1d43cb2d2fb09e26d2502ca4b4914a2e0599161d279c6c06117d34","mixHash":"0x0000000000000000000000000000000000000000000000000000000000000000","nonce":"0x0000000000000000","sha3Uncles":"0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347","logsBloom":"0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000","transactionsRoot":"0x71448077f5ce420a8e24db62d4d58e8d8e6ad2c7e76318868e089d41f7e0faf3","stateRoot":"0x0000000000000000000000000000000000000000000000000000000000000000","receiptsRoot":"0x2c292672b8fc9d223647a2569e19721f0757c96a1421753a93e141f8e56cf504","miner":"0x0000000000000000000000000000000000000000","difficulty":"0x0","totalDifficulty":"0x0","extraData":"0x","size":"0x0","gasLimit":"0x11278208","gasUsed":"0x3d1fe9","timestamp":"0x60d0952d","transactions":["0xa1ea93556b93ed3b45cb24f21c8deb584e6a9049c35209242651bf3533c23b98","0xfc6593c45ba92351d17173aa1381e84734d252ab0169887783039212c4a41024","0x85ee9d04fd0ebb5f62191eeb53cb45d9c0945d43eba444c3548de2ac8421682f","0x50d120936473e5b75f6e04829ad4eeca7a1df7d3c5026ebb5d34af936a39b29c"],"uncles":[],"l1BlockNumber":"0x8652f9"}`,
			&Head{
				Hash:             common.HexToHash("0x752dab43f7a2482db39227d46cd307623b26167841e2207e93e7566ab7ab7871"),
				Number:           0x15156,
				ParentHash:       common.HexToHash("0x923ad1e27c1d43cb2d2fb09e26d2502ca4b4914a2e0599161d279c6c06117d34"),
				Timestamp:        time.Unix(0x60d0952d, 0).UTC(),
				L1BlockNumber:    sql.NullInt64{Int64: 0x8652f9, Valid: true},
				ReceiptsRoot:     common.HexToHash("0x2c292672b8fc9d223647a2569e19721f0757c96a1421753a93e141f8e56cf504"),
				TransactionsRoot: common.HexToHash("0x71448077f5ce420a8e24db62d4d58e8d8e6ad2c7e76318868e089d41f7e0faf3"),
				StateRoot:        common.HexToHash("0x0000000000000000000000000000000000000000000000000000000000000000"),
			},
		},
		{"arbitrum_empty_l1BlockNumber",
			`{"number":"0x15156","hash":"0x752dab43f7a2482db39227d46cd307623b26167841e2207e93e7566ab7ab7871","parentHash":"0x923ad1e27c1d43cb2d2fb09e26d2502ca4b4914a2e0599161d279c6c06117d34","mixHash":"0x0000000000000000000000000000000000000000000000000000000000000000","nonce":"0x0000000000000000","sha3Uncles":"0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347","logsBloom":"0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000","transactionsRoot":"0x71448077f5ce420a8e24db62d4d58e8d8e6ad2c7e76318868e089d41f7e0faf3","stateRoot":"0x0000000000000000000000000000000000000000000000000000000000000000","receiptsRoot":"0x2c292672b8fc9d223647a2569e19721f0757c96a1421753a93e141f8e56cf504","miner":"0x0000000000000000000000000000000000000000","difficulty":"0x0","totalDifficulty":"0x0","extraData":"0x","size":"0x0","gasLimit":"0x11278208","gasUsed":"0x3d1fe9","timestamp":"0x60d0952d","transactions":["0xa1ea93556b93ed3b45cb24f21c8deb584e6a9049c35209242651bf3533c23b98","0xfc6593c45ba92351d17173aa1381e84734d252ab0169887783039212c4a41024","0x85ee9d04fd0ebb5f62191eeb53cb45d9c0945d43eba444c3548de2ac8421682f","0x50d120936473e5b75f6e04829ad4eeca7a1df7d3c5026ebb5d34af936a39b29c"],"uncles":[]}`,
			&Head{
				Hash:             common.HexToHash("0x752dab43f7a2482db39227d46cd307623b26167841e2207e93e7566ab7ab7871"),
				Number:           0x15156,
				ParentHash:       common.HexToHash("0x923ad1e27c1d43cb2d2fb09e26d2502ca4b4914a2e0599161d279c6c06117d34"),
				Timestamp:        time.Unix(0x60d0952d, 0).UTC(),
				L1BlockNumber:    sql.NullInt64{Int64: 0, Valid: false},
				ReceiptsRoot:     common.HexToHash("0x2c292672b8fc9d223647a2569e19721f0757c96a1421753a93e141f8e56cf504"),
				TransactionsRoot: common.HexToHash("0x71448077f5ce420a8e24db62d4d58e8d8e6ad2c7e76318868e089d41f7e0faf3"),
				StateRoot:        common.HexToHash("0x0000000000000000000000000000000000000000000000000000000000000000"),
			},
		},
		{"not found",
			`null`,
			&Head{},
		},
	}

	for _, test := range tests {
		test := test
		t.Run(test.name, func(t *testing.T) {
			var head Head
			err := head.UnmarshalJSON([]byte(test.json))
			require.NoError(t, err)
			assert.Equal(t, test.expected.Hash, head.Hash)
			assert.Equal(t, test.expected.Number, head.Number)
			assert.Equal(t, test.expected.ParentHash, head.ParentHash)
			assert.Equal(t, test.expected.Timestamp.UTC().Unix(), head.Timestamp.UTC().Unix())
			assert.Equal(t, test.expected.L1BlockNumber, head.L1BlockNumber)
			assert.Equal(t, test.expected.ReceiptsRoot, head.ReceiptsRoot)
			assert.Equal(t, test.expected.TransactionsRoot, head.TransactionsRoot)
			assert.Equal(t, test.expected.StateRoot, head.StateRoot)
		})
	}
}

func TestHead_MarshalJSON(t *testing.T) {
	tests := []struct {
		name     string
		head     *Head
		expected string
	}{
		{"happy",
			&Head{
				Hash:             common.HexToHash("0x41800b5c3f1717687d85fc9018faac0a6e90b39deaa0b99e7fe4fe796ddeb26a"),
				Number:           0x100,
				ParentHash:       common.HexToHash("0x41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d"),
				Timestamp:        time.Unix(0x58318da2, 0).UTC(),
				ReceiptsRoot:     common.HexToHash("0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"),
				TransactionsRoot: common.HexToHash("0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421"),
				StateRoot:        common.HexToHash("0xc7b01007a10da045eacb90385887dd0c38fcb5db7393006bdde24b93873c334b"),
			},
			`{"hash":"0x41800b5c3f1717687d85fc9018faac0a6e90b39deaa0b99e7fe4fe796ddeb26a","number":"0x100","parentHash":"0x41941023680923e0fe4d74a34bdac8141f2540e3ae90623718e47d66d1ca4a2d","timestamp":"0x58318da2","receiptsRoot":"0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421","transactionsRoot":"0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421","stateRoot":"0xc7b01007a10da045eacb90385887dd0c38fcb5db7393006bdde24b93873c334b"}`,
		},
		{"empty",
			&Head{},
			`{"number":"0x0"}`,
		},
	}

	for _, test := range tests {
		test := test
		t.Run(test.name, func(t *testing.T) {
			bs, err := test.head.MarshalJSON()
			require.NoError(t, err)
			require.Equal(t, test.expected, string(bs))
		})
	}
}

const gethSampleBlock = `
{
    "baseFeePerGas": "0x93d0d7cd1",
    "difficulty": "0x2cea10d39a7363",
    "extraData": "0x706f6f6c696e2e636f6d22ed69bddc3cbc3b40",
    "gasLimit": "0x1c95111",
    "gasUsed": "0x1c93dad",
    "hash": "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0",
    "logsBloom": "0x6fb5cf075bea78f7df6daa6f9bf477bb6fefb775ddcdae7da49dedbbbff7d9ff6ffddbeffeb72df6fe1bffb1d60f4ff3d277f67d7b17b9d8f2bda5c7ba7ff95d7ddbd5fb37fa4ffffdcbaebf35f7fbf71fef76e4dffd19e8be4f3fc5ebd7bb9ef75debf822bffeaf553d15fde5fc2dd136bfcef9dfff0dfbceb897feef7e65c7f2f1afa32fef697eb64d356f4bf8f63f5fb5f5fdb3fd763db7fdafeaeff57fb1ffce7bd737ffed777bdfffd3df5ccdff58fdb66f76d0f3bbfff5fff671edbe777ddbfb373fa5b6e7bfafeffff68ff7ddbf756fdadf9e85dd4fb7bfbbd6de7ffffff377afda5eb5bfff3f79b7fd37ffe73e7dbbf7fbbd5d6fb05b3bf7ee77f2ef",
    "miner": "******************************************",
    "mixHash": "0x595101379a658f7ff50495da8d9c81dacd9335662f2fd69916db5875cf99f114",
    "nonce": "0xadd59e902f02fa41",
    "number": "0xe5a952",
    "parentHash": "0x653ea251c180d93296ef79378e64d7dc9a74f565a54df477faeb64d3330977dd",
    "receiptsRoot": "0xa47d1aa7bd72e74d3d3770ffa64bf934de880f72a63fd80311397065a54d69b5",
    "sha3Uncles": "0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347",
    "size": "0x39455",
    "stateRoot": "0xf8d62881bd11dc8b5054cbcd9b2650d4bf63699ee9013bd0ddd2c9dca4b3e942",
    "timestamp": "0x62bdc207",
    "totalDifficulty": "0xb35d9b2c89eef362797",
    "transactions": [
      {
        "blockHash": "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0",
        "blockNumber": "0xe5a952",
        "from": "******************************************",
        "gas": "0xdbba0",
        "gasPrice": "0x978a846d2",
        "maxFeePerGas": "0xd0892241d",
        "maxPriorityFeePerGas": "0x3b9aca01",
        "hash": "0x754f49f0a2ca7680806d261dd36ee95ac88a81da59fef0b5d8d691478f075d46",
        "input": "0x1cff79cd000000000000000000000000343933efdf64d2d6eeaf2dcd5fbd701541d64f67000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000e44a7d794a000000000000000000000000000000000000000000000005bc94810a20626a9a0000000000000000000000000000000000000000000000000e52b79acdb06152000000000000000000000000000000000000798f836298dfb377b3deeb7ade400000000000000000000000000000000000000000000000000de0b6b3a76400000000000000000000000000000000000000000000000000000000000062bdc2400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000798e37fb7bc47a14e3b89fa086d600000000000000000000000000000000000000000000000000000000",
        "nonce": "0xbf65",
        "to": "0x4cb18386e5d1f34dc6eea834bf3534a970a3f8e7",
        "transactionIndex": "0x0",
        "value": "0xa907",
        "type": "0x2",
        "accessList": [],
        "chainId": "0x1",
        "v": "0x0",
        "r": "0xcbdf4705610d7b20326dcd153491f37f133c34026f3e0abf72f9db03ac98de0e",
        "s": "0xa2b2d625d34315e8d6d0543e0f9393d2a14dddaf3678d7f0ed432df9cb8e5c3"
      },
      {
        "blockHash": "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0",
        "blockNumber": "0xe5a952",
        "from": "0xdc54b1e90a6da20b6a63ba5ffcace3e2883f641f",
        "gas": "0xda8e",
        "gasPrice": "0x99675abd1",
        "maxFeePerGas": "0xf0fa066f3",
        "maxPriorityFeePerGas": "0x59682f00",
        "hash": "0xec7c12169f1f1e1fc5b0d0723dda88b175bd8d99213a9c99b6019c927931f315",
        "input": "0x095ea7b300000000000000000000000068b3465833fb72a70ecdf485e0e4c7bd8665fc45ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff",
        "nonce": "0x1c",
        "to": "0x106552c11272420aad5d7e94f8acab9095a6c952",
        "transactionIndex": "0x191",
        "value": "0x0",
        "type": "0x2",
        "accessList": [],
        "chainId": "0x1",
        "v": "0x1",
        "r": "0x80039a5548aa5937d0988fc45b495296bde1449d790d2b74439149e17fc11b7a",
        "s": "0x126beedbe5a2876c462f2c7e29152694c03df13e9d68273f21a2716859aec2e0"
      },
      {
        "blockHash": "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0",
        "blockNumber": "0xe5a952",
        "from": "0x699e6d0ea5cdbaf3eeed5dc68541fbb826184586",
        "gas": "0x5208",
        "gasPrice": "0x99675abd1",
        "maxFeePerGas": "0xebffe7460",
        "maxPriorityFeePerGas": "0x59682f00",
        "hash": "0x94947a2a3882da035babf81e6579f177d1fb2086e4806324359de9ae3e2b220d",
        "input": "0x",
        "nonce": "0x153",
        "to": "0x6cb24a9e81d9da0b85d2fd22e8092ea3786b85a4",
        "transactionIndex": "0x192",
        "value": "0x7ad64bd6230c63a",
        "type": "0x2",
        "accessList": [],
        "chainId": "0x1",
        "v": "0x0",
        "r": "0xa6a0960d3ee106436591783cc2f03da462aadc731985212231b003675b975cbe",
        "s": "0x28e7801e2c24fb2ad841a087f8f6d9b02478a2f0e880453340c4c0fe182c5ef0"
      },
      {
        "blockHash": "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0",
        "blockNumber": "0xe5a952",
        "from": "0x0807c5c8fa8a8229870cfecb6e2e71dcb6a78261",
        "gas": "0x183ac",
        "gasPrice": "0x99675abd1",
        "maxFeePerGas": "0xe06f90913",
        "maxPriorityFeePerGas": "0x59682f00",
        "hash": "0x44e6a280c2caf190855435603319f75f9260d9ed706537d67aabeac0e48e9cdf",
        "input": "0xa9059cbb00000000000000000000000085b82324f03f389b7d0d56c25ec8d1ba83fad7b800000000000000000000000000000000000000000000000000000000240284ef",
        "nonce": "0x2a",
        "to": "0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48",
        "transactionIndex": "0x193",
        "value": "0x0",
        "type": "0x2",
        "accessList": [],
        "chainId": "0x1",
        "v": "0x1",
        "r": "0xe3224640b70f6bb899fd1e9695531e272f344fa2102465063d8427a2147331",
        "s": "0x59a3640055651ab2db9108e55cf3dfcbe78cadc8973fe0ce95667214e0cede99"
      },
      {
        "blockHash": "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0",
        "blockNumber": "0xe5a952",
        "from": "0xcbe6fe602ca089489675e617ba2ce6faa52c49df",
        "gas": "0x7f8e",
        "gasPrice": "0x99675abd1",
        "maxFeePerGas": "0xe06f90913",
        "maxPriorityFeePerGas": "0x59682f00",
        "hash": "0x4309e69bf880efba8fb2fc10217f1b032c1711b3172e62901dc75dd646fb0412",
        "input": "0x1998aeef",
        "nonce": "0x61",
        "to": "0xb75f09b4340aeb85cd5f2dd87d31751edc11ed39",
        "transactionIndex": "0x194",
        "value": "0xc249fdd32778000",
        "type": "0x2",
        "accessList": [],
        "chainId": "0x1",
        "v": "0x1",
        "r": "0xb7b480f5ee42552a9da24a6d1d1f9707e52489e3ef21c39e55e38be9e7bf9d04",
        "s": "0x77c6bd1ccda3b5b6940f20e847b99bb7f3389d068d3aa1f54b364b0c14196af8"
      },
      {
        "blockHash": "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0",
        "blockNumber": "0xe5a952",
        "from": "0xcba1a597261fc24a3569a161c6d8157999b3a342",
        "gas": "0x5208",
        "gasPrice": "0x99675abd1",
        "maxFeePerGas": "0x10a414b8d2",
        "maxPriorityFeePerGas": "0x59682f00",
        "hash": "0x804b80531b93cdb3f34d360b68cc1fec052269742033fcab6bc6240fbda782bd",
        "input": "0x",
        "nonce": "0x160",
        "to": "0xc941abf0ac772d5ca490dea8d7bd0ae123ce1409",
        "transactionIndex": "0x195",
        "value": "0x3782dace9d900000",
        "type": "0x2",
        "accessList": [],
        "chainId": "0x1",
        "v": "0x1",
        "r": "0xdef9f95429ccee8c280e8aa01076a0d69bb99a326391ac8cbc7da4ff8ea72edb",
        "s": "0x6e56cee027e245d90e02093e1cd2126f25e9025339d09a47d65cd6692cced0ec"
      },
      {
        "blockHash": "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0",
        "blockNumber": "0xe5a952",
        "from": "0x1e082cc52edd1558870f69f46b58d9e09c8b9954",
        "gas": "0x5208",
        "gasPrice": "0x99675abd1",
        "maxFeePerGas": "0xe06f90913",
        "maxPriorityFeePerGas": "0x59682f00",
        "hash": "0x8bee254970c86f934d25c2b4256e3ae914c45298b353b03e663e55b58cfad334",
        "input": "0x",
        "nonce": "0x8f",
        "to": "0x4733ec9cd2d5ae5bfb41fb4833e7a15e54c4a7a7",
        "transactionIndex": "0x196",
        "value": "0xb1a2bc2ec50000",
        "type": "0x2",
        "accessList": [],
        "chainId": "0x1",
        "v": "0x1",
        "r": "0xbe6e36292dc34a4e14f720e769a27edc0434bcad94d4df4be8ba1b60fcd849c6",
        "s": "0x493f73cbdf9ad54c9626c778e8dce08f2ae08895d449efbdece719d8a9115654"
      }
    ],
    "transactionsRoot": "0xa2ee120ac64a18e32a2f8a35ff6424992caf95995faba97e5184ecbdff0af289",
    "uncles": []
}
`

const paritySampleBlock = `
{
    "author": "0x03801efb0efe2a25ede5dd3a003ae880c0292e4d",
    "baseFeePerGas": "0x7",
    "difficulty": "0xfffffffffffffffffffffffffffffffe",
    "extraData": "0xdb830303038c4f70656e457468657265756d86312e34372e30826c69",
    "gasLimit": "0x1c9c380",
    "gasUsed": "0x27dfb2",
    "hash": "0x0ec62c2a397e114d84ce932387d841787d7ec5757ceba3708386da87934b7c82",
    "logsBloom": "0x00000000000000000000000000000000000000000000000000800000000100000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000020000000000000000000800000000000000000000000000000000400000000000000000000000000000000000000000010000000000000000000000000004004000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000080000000000000000020000000000000000000000000000000000000000000000000000000000000000000",
    "miner": "0x03801efb0efe2a25ede5dd3a003ae880c0292e4d",
    "number": "0x1ef81ff",
    "parentHash": "0x3aa1c729fb45888bc1ce777d00bad9637c0b5f7cb48b145ebacc16098e0132d4",
    "receiptsRoot": "0xa9fd0285ccd79ad2bd8c6de7cd6b687a1d942b810c613acd43729d2e6cc98240",
    "sealFields": [
      "0x8418af6ffb",
      "0xb841206930e3357eca1bb5181a07fa4bac1e39008add26679102af0984a69d8c2d3e0d53f8636a1259a395320ac7937a280c20e736ec4c0e7f72f0656409f068de2d00"
    ],
    "sha3Uncles": "0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347",
    "signature": "206930e3357eca1bb5181a07fa4bac1e39008add26679102af0984a69d8c2d3e0d53f8636a1259a395320ac7937a280c20e736ec4c0e7f72f0656409f068de2d00",
    "size": "0x2f65",
    "stateRoot": "0xc0c8637c447cd1f2ce3fdf55cd1f9293bac276f5ed37207d9cd6734dd858a54a",
    "step": "414150651",
    "timestamp": "0x62bdbfec",
    "totalDifficulty": "0x1ed3ef000000000000000000000000481b7ff2f",
    "transactions": [
      {
        "accessList": [],
        "blockHash": "0x0ec62c2a397e114d84ce932387d841787d7ec5757ceba3708386da87934b7c82",
        "blockNumber": "0x1ef81ff",
        "chainId": "0x2a",
        "condition": null,
        "creates": "0x4f6db99e4652915f1aa7d064e0821f9b35b5e4c5",
        "from": "0xefc474ebbe74e0b96f51cf84a00394f7dce563b0",
        "gas": "0x272cef",
        "gasPrice": "0x21ffed910",
        "hash": "0x6c5faccfc9d7a24710ac68ebf5a106d0b8cc3e6a0318098d4867ae0342c3cf4a",
        "input": "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",
        "maxFeePerGas": "0x21ffed911",
        "maxPriorityFeePerGas": "0x21ffed909",
        "nonce": "0xa6",
        "publicKey": "0x315947dd868c7c5c77a1052ca94381db3451c67cde16b0bb3822460a5630f73807bed17c6d53eff84e862c8eeb7623d1264328122e45e7d7c4d5ae3f341521a8",
        "r": "0x8aa81dd5b9caa6d950753d7cf2c0448f4c077efbae50ae1fe31028b34510192a",
        "raw": "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",
        "s": "0x25dce8987a6850159cdd2e39af17474f299a4f82ca3436424799168456ec2479",
        "to": null,
        "transactionIndex": "0x0",
        "type": "0x2",
        "v": "0x1",
        "value": "0x0"
      },
      {
        "blockHash": "0x0ec62c2a397e114d84ce932387d841787d7ec5757ceba3708386da87934b7c82",
        "blockNumber": "0x1ef81ff",
        "chainId": null,
        "condition": null,
        "creates": null,
        "from": "0xe6d63ed2c574b150a205d1d9cc7aaff1b7e4b59d",
        "gas": "0x2dc6c0",
        "gasPrice": "0x4f7915f5",
        "hash": "0xbe6122d6aaf84fb85f4df136d4662c6dc344248e987255c0daa1193b3f17d5a9",
        "input": "0xfdacd5760000000000000000000000000000000000000000000000000000000000000001",
        "nonce": "0x7",
        "publicKey": "0xb2a40fd8ec8703916fde9a0d3bb3c2391d7a2a1a6c1cd6492a7842d9daed24d5064847aaa242e635440f6dd06107044e1bd9387da6c32da3eaee56b928c6bdbf",
        "r": "0x4b3f442f3a014468b40d2fadea1b152b36582420d28fb69695658be40ffbdffa",
        "raw": "0xf88807844f7915f5832dc6c0949c97d0e47d81e0ffd0e41450427973e30ff1657b80a4fdacd57600000000000000000000000000000000000000000000000000000000000000011ba04b3f442f3a014468b40d2fadea1b152b36582420d28fb69695658be40ffbdffaa065f626f3a91ca662e56c42ea4376ee8f3db65a4ad613b9bdd2776c292869fee7",
        "s": "0x65f626f3a91ca662e56c42ea4376ee8f3db65a4ad613b9bdd2776c292869fee7",
        "standardV": "0x0",
        "to": "0x9c97d0e47d81e0ffd0e41450427973e30ff1657b",
        "transactionIndex": "0x1",
        "v": "0x1b",
        "value": "0x0"
      }
    ],
    "transactionsRoot": "0x83b29816e5acaf6110a616ec1e937fa9d78dc643cd580ce06eb54195b8fc1a70",
    "uncles": []
}

`

const eip4844Block = ` {
	"baseFeePerGas": "0x16740b3cb5",
	"blobGasUsed": "0xc0000",
	"difficulty": "0x0",
	"excessBlobGas": "0x4b80000",
	"extraData": "0xd883010d0c846765746888676f312e32302e31856c696e7578",
	"gasLimit": "0x1c9c380",
	"gasUsed": "0x18db0e0",
	"hash": "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590",
	"logsBloom": "0xc820da8f9c4ac1db241964223d4015549765668065d0c0418691262e44eda11970c271ead2b03ecffbb2ad52348c00cb0e11c083c3e26323855296236025e1d4198c892bb5cf0135cacc4f1bc138c88d8d44790497c6c201901a894b48966b2068f144bb16614271e6c05a854421290500081cea818eaae7950a8217a0d8d43a8b80018d800a43bd542d77230e5030861051152088a0d514d65177c2fa4198642eefd29e4dae806bfd4250ec63147fee38e368d00c01da1d67c1a5a24ed87331289bba074e1c2434147502a90df300a311b67056524e78a5b792930272b4607fb676060ea623d64ac1174a22b382a0648f5a35994b669131e3ea074ea128de45",
	"miner": "0x6a7aa9b882d50bb7bc5da1a244719c99f12f06a3",
	"mixHash": "0x27337177ae9744acb30c9d13e187c582367d75f702cc3ef2e77f9c6b10394ebf",
	"nonce": "0x0000000000000000",
	"number": "0x50e1d6",
	"parentBeaconBlockRoot": "0xcdfe5d7bfd13221a86881bee926ec7e7e6c1eed2e460bd7772caa64bed1582ec",
	"parentHash": "0x077c1d68b52f8203cb90a71759a09b11c2a6577f97ea1fd4a8686a387fbedac8",
	"receiptsRoot": "0xff81f4fddbcfcc550c4358136953b56a66a95c24089501e2085d8c4588ffdb1f",
	"sha3Uncles": "0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347",
	"size": "0x2b41f",
	"stateRoot": "0xc9541af90707127dde506b208de22cfda50ad2ef6e8b4f67212e6402d7099708",
	"timestamp": "0x65cf57dc",
	"totalDifficulty": "0x3c656d23029ab0",
	"transactions": [
		{
			"blockHash": "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590",
			"blockNumber": "0x50e1d6",
			"from": "0x3c352ea32dfbb757ccdf4b457e52daf6ecc21917",
			"gas": "0x14820",
			"gasPrice": "0x5d426cae68",
			"hash": "0x8857a9064210e138a996c82a9d58b001d198b2362e8ad2ede7056a9c942011aa",
			"input": "0x",
			"nonce": "0xa07aa",
			"to": "0xd3234d7985fdaefc68cbfa04e91ecca334b6545c",
			"transactionIndex": "0x0",
			"value": "0x6f05b59d3b20000",
			"type": "0x0",
			"chainId": "0xaa36a7",
			"v": "0x1546d72",
			"r": "0x2acc4f73342578b09d5152f60305f7cfe4f5d377da504e99ed3795b65b9bd5c",
			"s": "0x33470e89e7508816c8c71d2135fc16a49b34ffbd86d8f6506658819afb578944"
		},
		{
			"blockHash": "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590",
			"blockNumber": "0x50e1d6",
			"from": "0x87c9b02a10ec2cb4dcb3b2e573e26169cf3cd9bf",
			"gas": "0x14820",
			"gasPrice": "0x5d426cae68",
			"hash": "0x83170edd8c637791fc8bdf07ad687ab11456409546fea23d8db90933d18eb566",
			"input": "0x",
			"nonce": "0xa2e22",
			"to": "0x2853502c56fb2160fcdc044d4477408b9670058e",
			"transactionIndex": "0x1",
			"value": "0x6f05b59d3b20000",
			"type": "0x0",
			"chainId": "0xaa36a7",
			"v": "0x1546d72",
			"r": "0xf5f12880149214ef1109c83bd9e80a6851a581aba2cf10c1c3d0e8ad473db9f5",
			"s": "0x150c309b585f0b76fdb6ee4e26048fae88464e6ef2ff41a9341e84ddbbf4f7a7"
		},
		{
			"blockHash": "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590",
			"blockNumber": "0x50e1d6",
			"from": "0x0bf8d3a5bc2f3ce4ce93d0ef13c2d519d2efe7ab",
			"gas": "0x32aa5",
			"gasPrice": "0x17e3776320",
			"hash": "0xe9a8dc19ca3ace8b06fdb120fbb1333f75a14e06229536adbdae1ca018f39d2f",
			"input": "0x79df76820000000000000000000000000bf8d3a5bc2f3ce4ce93d0ef13c2d519d2efe7ab000000000000000000000000000000000000000000000000000000000002c3380000000000000000000000000000000000000000000000000000018db1eefe820000000000000000000000000000000000000000000000000000000191ea03c00000000000000000000000000000000000000000000000000000000000000015000000000000000000000000000000000000000000000000000000000000001cbeaa1a18797feaaae677a2c47f551623362b25a4cbed88a14cf06318974302be1e3a13405cc803063bb4549a79efa547f2024d470b53d21631f42e3ce993bdd7",
			"nonce": "0x3",
			"to": "0x321ce961084fcf3a56de4be2f2006707a0421aa4",
			"transactionIndex": "0x4a",
			"value": "0x0",
			"type": "0x0",
			"chainId": "0xaa36a7",
			"v": "0x1546d72",
			"r": "0x53fa23875340ac5bd7af3e5b4791981cad94f9a86da431bdafdaf0d4fccbfab9",
			"s": "0x1f3826a1d0358425f2bddacd9d87efeee07065492b7ccc499357d69437856859"
		},
		{
			"blockHash": "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590",
			"blockNumber": "0x50e1d6",
			"from": "0x1803c760451dc8da8935c7b7e47e1c60910e6986",
			"gas": "0x5208",
			"gasPrice": "0x17d9abf8b5",
			"maxFeePerGas": "0x8bb2c97000",
			"maxPriorityFeePerGas": "0x165a0bc00",
			"maxFeePerBlobGas": "0xdf8475800",
			"hash": "0x48a6a2294bd57cd0defc54968bf00179ef3c2d0c647d386e1840ee347d2dd0b8",
			"input": "0x",
			"nonce": "0x2861",
			"to": "0x4f56ffc63c28b72f79b02e91f11a4707bac4043c",
			"transactionIndex": "0x4b",
			"value": "0x0",
			"type": "0x3",
			"accessList": [],
			"chainId": "0xaa36a7",
			"blobVersionedHashes": [
				"0x01b3722197a3bb82f25a26ab702b63e7c5ce7296ec18f7a53d5e2c28081084a3",
				"0x0125c472df6afa04cbab756630f5bcf560fc929ee12c7dd4738cfd67fa7926d8",
				"0x016316f61a259aa607096440fc3eeb90356e079be01975d2fb18347bd50df33c"
			],
			"v": "0x0",
			"r": "0xf9cfd7e18a156807a42fc05d2255a9741168363a7184b4fa40a9dc9f83dcc3d4",
			"s": "0x9b12570519ef421bd31461bed09b3599db43354d3c88ff4bed17ddd964fadf6",
			"yParity": "0x0"
		},
		{
			"blockHash": "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590",
			"blockNumber": "0x50e1d6",
			"from": "0x56272f6932e6ae0116d270a2e919d20d3052f206",
			"gas": "0x5208",
			"gasPrice": "0x17d9abf8b5",
			"maxFeePerGas": "0x8bb2c97000",
			"maxPriorityFeePerGas": "0x165a0bc00",
			"maxFeePerBlobGas": "0xdf8475800",
			"hash": "0xa97e14a2e87d322fcb97edc4b25cd976d18963cfad19bfd4b9c8066a6a2d97cf",
			"input": "0x",
			"nonce": "0x2836",
			"to": "0x1803c760451dc8da8935c7b7e47e1c60910e6986",
			"transactionIndex": "0x4c",
			"value": "0x0",
			"type": "0x3",
			"accessList": [],
			"chainId": "0xaa36a7",
			"blobVersionedHashes": [
				"0x01adde6e37a3ba9e2a40052556b51960c2a4cc69c257992ae954c6d4f8d479c2",
				"0x0142116f5d1b4b60672ac5b69edb7431ffddb700227d9b4dc9adafe599b38943",
				"0x010ba493f0c9891f7109bd29e4914c4e79c7a35530981e746d02df66e5d57056"
			],
			"v": "0x1",
			"r": "0x82045878948921a0330ea7eb23b74f103f9f353704eaeab358e777bea238bfd",
			"s": "0x378128f0fa8059df16cdc95f215f1e669dc81cee7c39b804e6371e486aa1e753",
			"yParity": "0x1"
		},
		{
			"blockHash": "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590",
			"blockNumber": "0x50e1d6",
			"from": "0x779fa3507ad952f447c32f7d7e1f86ceddf2cc9d",
			"gas": "0x1118b",
			"gasPrice": "0x2e90edd000",
			"maxFeePerGas": "0x2e90edd000",
			"maxPriorityFeePerGas": "0x2e90edd000",
			"hash": "0x6e94385ca341d5763ebb21601a86cddbc77acb879735ce1d013b22dc03fb07a2",
			"input": "0x095ea7b3000000000000000000000000d3ec28ad6d777f5aa92377294b9b6522c719307900000000000000000000000000000000000000000000003635c9adc5dea00000",
			"nonce": "0x57",
			"to": "0xf7f928b2bf5cc7e0bd0f258165249fa3c2ef85ac",
			"transactionIndex": "0x10",
			"value": "0x0",
			"type": "0x2",
			"accessList": [],
			"chainId": "0xaa36a7",
			"v": "0x0",
			"r": "0x4b4f0ccda6366f4897cb8eb3dee4a72bba76758159ff7030a134022c0a11def2",
			"s": "0x27621a604938c68b98c52b57e2b27cb104e3838fd2864d18b6429d42ac2b2f0a",
			"yParity": "0x0"
		}
	],
	"transactionsRoot": "0xb9773c32f06f914e3e6da66a114e3ddf84e38dff62e26621b0ffecee38d5fe6e",
	"uncles": [],
	"withdrawals": [
		{
			"index": "0x233d1c2",
			"validatorIndex": "0x384",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xec0"
		},
		{
			"index": "0x233d1c3",
			"validatorIndex": "0x392",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xec0"
		},
		{
			"index": "0x233d1c4",
			"validatorIndex": "0x393",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xec0"
		},
		{
			"index": "0x233d1c5",
			"validatorIndex": "0x395",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xec0"
		},
		{
			"index": "0x233d1c6",
			"validatorIndex": "0x396",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xec0"
		},
		{
			"index": "0x233d1c7",
			"validatorIndex": "0x398",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xec0"
		},
		{
			"index": "0x233d1c8",
			"validatorIndex": "0x39a",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xec0"
		},
		{
			"index": "0x233d1c9",
			"validatorIndex": "0x39c",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xec0"
		},
		{
			"index": "0x233d1ca",
			"validatorIndex": "0x39d",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xec0"
		},
		{
			"index": "0x233d1cb",
			"validatorIndex": "0x39e",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xb10"
		},
		{
			"index": "0x233d1cc",
			"validatorIndex": "0x39f",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xb10"
		},
		{
			"index": "0x233d1cd",
			"validatorIndex": "0x3a3",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xb10"
		},
		{
			"index": "0x233d1ce",
			"validatorIndex": "0x3a4",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xb10"
		},
		{
			"index": "0x233d1cf",
			"validatorIndex": "0x3a5",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xb10"
		},
		{
			"index": "0x233d1d0",
			"validatorIndex": "0x3ad",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xb10"
		},
		{
			"index": "0x233d1d1",
			"validatorIndex": "0x3ae",
			"address": "0xe276bc378a527a8792b353cdca5b5e53263dfb9e",
			"amount": "0xb10"
		}
	],
	"withdrawalsRoot": "0x26a577aa6ab5e73684b15665f8510c7da8f29a66ed17bf73c1c11f4c3af9c955"
}`

func TestBlock_UnmarshalJSON(t *testing.T) {
	t.Run("unmarshals parity block", func(t *testing.T) {
		b := new(Block)
		err := b.UnmarshalJSON([]byte(paritySampleBlock))
		assert.NoError(t, err)

		assert.Equal(t, int64(32473599), b.Number)
		assert.Equal(t, "0x0ec62c2a397e114d84ce932387d841787d7ec5757ceba3708386da87934b7c82", b.Hash.Hex())
		assert.Equal(t, "0x3aa1c729fb45888bc1ce777d00bad9637c0b5f7cb48b145ebacc16098e0132d4", b.ParentHash.Hex())
		assert.Equal(t, assets.NewWeiI(7), b.BaseFeePerGas)
		assert.Equal(t, int64(1656602604), b.Timestamp.Unix())
		assert.Len(t, b.Transactions, 2)
	})
	t.Run("unmarshals geth block", func(t *testing.T) {
		b := new(Block)
		err := b.UnmarshalJSON([]byte(gethSampleBlock))
		require.NoError(t, err)

		assert.Equal(t, int64(15051090), b.Number)
		assert.Equal(t, "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0", b.Hash.Hex())
		assert.Equal(t, "0x653ea251c180d93296ef79378e64d7dc9a74f565a54df477faeb64d3330977dd", b.ParentHash.Hex())
		assert.Equal(t, assets.NewWeiI(39678999761), b.BaseFeePerGas)
		assert.Equal(t, int64(1656603143), b.Timestamp.Unix())
		assert.Len(t, b.Transactions, 7)
	})
	t.Run("handles empty result", func(t *testing.T) {
		b := new(Block)
		err := b.UnmarshalJSON([]byte("null"))
		require.Error(t, err)
		assert.Equal(t, pkgerrors.Cause(err), ErrMissingBlock)
		assert.True(t, pkgerrors.Is(err, ErrMissingBlock))
	})
	t.Run("unmarshals EIP-4844 block", func(t *testing.T) {
		b := new(Block)
		err := b.UnmarshalJSON([]byte(eip4844Block))
		require.NoError(t, err)
		assert.Equal(t, int64(5300694), b.Number)
		assert.Equal(t, "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590", b.Hash.Hex())
		assert.Equal(t, "0x077c1d68b52f8203cb90a71759a09b11c2a6577f97ea1fd4a8686a387fbedac8", b.ParentHash.Hex())
		assert.Equal(t, assets.NewWeiI(96436174005), b.BaseFeePerGas)
		assert.Equal(t, int64(1708087260), b.Timestamp.Unix())
		assert.Len(t, b.Transactions, 6)
	})
	t.Run("unmarshals EIP-4844 block", func(t *testing.T) {
		b := new(Block)
		err := b.UnmarshalJSON([]byte(eip4844Block))
		require.NoError(t, err)
		assert.Equal(t, int64(5300694), b.Number)
		assert.Equal(t, "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590", b.Hash.Hex())
		assert.Equal(t, "0x077c1d68b52f8203cb90a71759a09b11c2a6577f97ea1fd4a8686a387fbedac8", b.ParentHash.Hex())
		assert.Equal(t, assets.NewWeiI(96436174005), b.BaseFeePerGas)
		assert.Equal(t, int64(1708087260), b.Timestamp.Unix())
		assert.Len(t, b.Transactions, 6)
	})
}

func TestTransaction_UnmarshalJSON(t *testing.T) {
	t.Parallel()
	type args struct {
		data []byte
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *Transaction
	}{
		{
			name: "sample geth txn",
			args: args{
				[]byte(
					`{
	"blockHash": "0x45eb0a650b6b0b9fd1ee676b870e43fa7614f1034f7404070327a332faed05c0",
	"blockNumber": "0xe5a952",
	"from": "******************************************",
	"gas": "0xdbba0",
	"gasPrice": "0x978a846d2",
	"maxFeePerGas": "0xd0892241d",
	"maxPriorityFeePerGas": "0x3b9aca01",
	"hash": "0x754f49f0a2ca7680806d261dd36ee95ac88a81da59fef0b5d8d691478f075d46",
	"input": "0x1cff79cd000000000000000000000000343933efdf64d2d6eeaf2dcd5fbd701541d64f67000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000e44a7d794a000000000000000000000000000000000000000000000005bc94810a20626a9a0000000000000000000000000000000000000000000000000e52b79acdb06152000000000000000000000000000000000000798f836298dfb377b3deeb7ade400000000000000000000000000000000000000000000000000de0b6b3a76400000000000000000000000000000000000000000000000000000000000062bdc2400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000798e37fb7bc47a14e3b89fa086d600000000000000000000000000000000000000000000000000000000",
	"nonce": "0xbf65",
	"to": "0x4cb18386e5d1f34dc6eea834bf3534a970a3f8e7",
	"transactionIndex": "0x0",
	"value": "0xa907",
	"type": "0x2",
	"accessList": [],
	"chainId": "0x1",
	"v": "0x0",
	"r": "0xcbdf4705610d7b20326dcd153491f37f133c34026f3e0abf72f9db03ac98de0e",
	"s": "0xa2b2d625d34315e8d6d0543e0f9393d2a14dddaf3678d7f0ed432df9cb8e5c3"
}`,
				),
			},
			want: &Transaction{
				GasPrice:             assets.NewWei(mustHexToBig(t, "978a846d2")),
				GasLimit:             mustHextoUint32(t, "0xdbba0"),
				MaxFeePerGas:         assets.NewWei(mustHexToBig(t, "d0892241d")),
				MaxPriorityFeePerGas: assets.NewWei(mustHexToBig(t, "3b9aca01")),
				Type:                 0x2,
				Hash:                 common.HexToHash("0x754f49f0a2ca7680806d261dd36ee95ac88a81da59fef0b5d8d691478f075d46"),
			},
		},
		{
			name: "sample EIP4844 txn",
			args: args{
				[]byte(
					`
					{
					"blockHash": "0x3edd900025edab70dde26a52377c3d0a9474c3f540bd0131d58f508711272590",
					"blockNumber": "0x50e1d6",
					"from": "0x56272f6932e6ae0116d270a2e919d20d3052f206",
					"gas": "0x5208",
					"gasPrice": "0x17d9abf8b5",
					"maxFeePerGas": "0x8bb2c97000",
					"maxPriorityFeePerGas": "0x165a0bc00",
					"maxFeePerBlobGas": "0xdf8475800",
					"hash": "0xa97e14a2e87d322fcb97edc4b25cd976d18963cfad19bfd4b9c8066a6a2d97cf",
					"input": "0x",
					"nonce": "0x2836",
					"to": "0x1803c760451dc8da8935c7b7e47e1c60910e6986",
					"transactionIndex": "0x4c",
					"value": "0x0",
					"type": "0x3",
					"accessList": [],
					"chainId": "0xaa36a7",
					"blobVersionedHashes": [
						"0x01adde6e37a3ba9e2a40052556b51960c2a4cc69c257992ae954c6d4f8d479c2",
						"0x0142116f5d1b4b60672ac5b69edb7431ffddb700227d9b4dc9adafe599b38943",
						"0x010ba493f0c9891f7109bd29e4914c4e79c7a35530981e746d02df66e5d57056"
					],
					"v": "0x1",
					"r": "0x82045878948921a0330ea7eb23b74f103f9f353704eaeab358e777bea238bfd",
					"s": "0x378128f0fa8059df16cdc95f215f1e669dc81cee7c39b804e6371e486aa1e753",
					"yParity": "0x1"
					}`,
				),
			},
			want: &Transaction{
				GasPrice:             assets.NewWei(mustHexToBig(t, "17d9abf8b5")),
				GasLimit:             mustHextoUint32(t, "0x5208"),
				MaxFeePerGas:         assets.NewWei(mustHexToBig(t, "8bb2c97000")),
				MaxPriorityFeePerGas: assets.NewWei(mustHexToBig(t, "165a0bc00")),
				Type:                 0x3,
				Hash:                 common.HexToHash("0xa97e14a2e87d322fcb97edc4b25cd976d18963cfad19bfd4b9c8066a6a2d97cf"),
			},
		},
		{
			name: "sample parity txn",
			args: args{[]byte(
				`   {
	"blockHash": "0x0ec62c2a397e114d84ce932387d841787d7ec5757ceba3708386da87934b7c82",
	"blockNumber": "0x1ef81ff",
	"chainId": null,
	"condition": null,
	"creates": null,
	"from": "0xe6d63ed2c574b150a205d1d9cc7aaff1b7e4b59d",
	"gas": "0x2dc6c0",
	"gasPrice": "0x4f7915f5",
	"hash": "0xbe6122d6aaf84fb85f4df136d4662c6dc344248e987255c0daa1193b3f17d5a9",
	"input": "0xfdacd5760000000000000000000000000000000000000000000000000000000000000001",
	"nonce": "0x7",
	"publicKey": "0xb2a40fd8ec8703916fde9a0d3bb3c2391d7a2a1a6c1cd6492a7842d9daed24d5064847aaa242e635440f6dd06107044e1bd9387da6c32da3eaee56b928c6bdbf",
	"r": "0x4b3f442f3a014468b40d2fadea1b152b36582420d28fb69695658be40ffbdffa",
	"raw": "0xf88807844f7915f5832dc6c0949c97d0e47d81e0ffd0e41450427973e30ff1657b80a4fdacd57600000000000000000000000000000000000000000000000000000000000000011ba04b3f442f3a014468b40d2fadea1b152b36582420d28fb69695658be40ffbdffaa065f626f3a91ca662e56c42ea4376ee8f3db65a4ad613b9bdd2776c292869fee7",
	"s": "0x65f626f3a91ca662e56c42ea4376ee8f3db65a4ad613b9bdd2776c292869fee7",
	"standardV": "0x0",
	"to": "0x9c97d0e47d81e0ffd0e41450427973e30ff1657b",
	"transactionIndex": "0x1",
	"v": "0x1b",
	"value": "0x0"
  }`,
			)},
			want: &Transaction{
				GasPrice:             assets.NewWei(mustHexToBig(t, "4f7915f5")),
				GasLimit:             mustHextoUint32(t, "0x2dc6c0"),
				MaxFeePerGas:         nil,
				MaxPriorityFeePerGas: nil,
				Type:                 0,
				Hash:                 common.HexToHash("0xbe6122d6aaf84fb85f4df136d4662c6dc344248e987255c0daa1193b3f17d5a9"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := &Transaction{}
			err := got.UnmarshalJSON(tt.args.data)
			require.NoError(t, err)
			require.Equal(t, tt.want, got)
		})
	}
}

func TestTransaction_JSONRoundtrip(t *testing.T) {
	t.Parallel()
	want := &Transaction{
		GasPrice:             assets.NewWei(mustHexToBig(t, "978a846d2")),
		GasLimit:             mustHextoUint32(t, "0xdbba0"),
		MaxFeePerGas:         assets.NewWei(mustHexToBig(t, "d0892241d")),
		MaxPriorityFeePerGas: assets.NewWei(mustHexToBig(t, "3b9aca01")),
		Type:                 TxType(2),
		Hash:                 common.HexToHash("0x754f49f0a2ca7680806d261dd36ee95ac88a81da59fef0b5d8d691478f075d46"),
	}

	d, err := json.Marshal(want)
	require.NoError(t, err)
	got := new(Transaction)
	err = json.Unmarshal(d, got)
	require.NoError(t, err)
	assert.Equal(t, want, got)
}

func TestBlock_JSONRoundtrip(t *testing.T) {
	t.Parallel()

	d, err := json.Marshal(smallBlock)
	require.NoError(t, err)
	got := new(Block)
	err = json.Unmarshal(d, got)
	require.NoError(t, err)
	assert.Equal(t, smallBlock.Hash, got.Hash)
	assert.Equal(t, smallBlock.BaseFeePerGas, got.BaseFeePerGas)
	assert.Equal(t, smallBlock.Number, got.Number)
	assert.Equal(t, smallBlock.ParentHash, got.ParentHash)
	assert.Equal(t, smallBlock.Timestamp, got.Timestamp)

	assertTxnsEqual(t, smallBlock.Transactions, got.Transactions)
}

func assertTxnsEqual(t *testing.T, txns1, txns2 []Transaction) {
	require.Equal(t, len(txns1), len(txns2))
	for i := range txns1 {
		assert.Equal(t, txns1[i].GasLimit, txns2[i].GasLimit)
		assert.True(t, txns1[i].GasPrice.Equal(txns2[i].GasPrice))
		assert.Equal(t, txns1[i].Hash, txns2[i].Hash)
		assert.True(t, txns1[i].MaxFeePerGas.Equal(txns2[i].MaxFeePerGas))
		assert.True(t, txns1[i].MaxPriorityFeePerGas.Equal(txns2[i].MaxPriorityFeePerGas))
		assert.Equal(t, txns1[i].Type, txns2[i].Type)
	}
}
func TestTxType_JSONRoundtrip(t *testing.T) {
	t.Run("non zero", func(t *testing.T) {
		t.Parallel()
		want := TxType(2)
		d, err := json.Marshal(&want)
		require.NoError(t, err)
		got := new(TxType)
		err = json.Unmarshal(d, got)
		require.NoError(t, err)
		assert.Equal(t, want, *got)
	})

	t.Run("zero", func(t *testing.T) {
		t.Parallel()
		want := TxType(0)
		d, err := json.Marshal(&want)
		require.NoError(t, err)

		got := new(TxType)
		err = json.Unmarshal(d, got)
		require.NoError(t, err)
		assert.Equal(t, want, *got)
	})
}

func mustHextoUint32(t *testing.T, hx string) uint32 {
	temp := new(hexutil.Uint64)
	err := temp.UnmarshalText([]byte(hx))
	require.NoError(t, err)
	return uint32(*temp)
}

func mustHexToBig(t *testing.T, hx string) *big.Int {
	n, err := hex.ParseBig(hx)
	require.NoError(t, err)
	return n
}

func testHead(t int) *Head {
	h := NewHead(big.NewInt(int64(t)), utils.NewHash(), utils.NewHash(), ubig.NewI(0))
	return &h
}

func TestHash_UnmarshalJSON(t *testing.T) {
	t.Parallel()

	zeroHash := common.Hash{}

	t.Run("normal hash", func(t *testing.T) {
		t.Parallel()

		expectedHash := utils.NewHash()
		jsonData := []byte(fmt.Sprintf(`"%s"`, expectedHash.Hex()))

		var hash Hash
		err := json.Unmarshal(jsonData, &hash)
		require.NoError(t, err)

		assert.Equal(t, expectedHash.Bytes(), hash.Bytes())
	})

	t.Run("empty string should return zero hash", func(t *testing.T) {
		t.Parallel()

		jsonData := []byte(`""`)
		var hash Hash
		err := json.Unmarshal(jsonData, &hash)
		require.NoError(t, err)
		assert.Equal(t, zeroHash.Bytes(), hash.Bytes())
	})

	t.Run("0x00", func(t *testing.T) {
		t.Parallel()

		jsonData := []byte(`"0x00"`)
		var hash Hash
		err := json.Unmarshal(jsonData, &hash)
		require.NoError(t, err)

		assert.Equal(t, zeroHash.Bytes(), hash.Bytes())
	})

	t.Run("0x", func(t *testing.T) {
		t.Parallel()

		jsonData := []byte(`"0x"`)
		var hash Hash
		err := json.Unmarshal(jsonData, &hash)
		require.NoError(t, err)

		assert.Equal(t, zeroHash.Bytes(), hash.Bytes())
	})

	t.Run("odd length hex string should fail", func(t *testing.T) {
		t.Parallel()

		jsonData := []byte(`"0x1"`)
		var hash Hash
		err := json.Unmarshal(jsonData, &hash)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "hex string")
	})

	t.Run("invalid format", func(t *testing.T) {
		t.Parallel()

		jsonData := []byte(`"not-a-hash"`)
		var hash Hash
		err := json.Unmarshal(jsonData, &hash)

		require.Error(t, err)
		assert.Contains(t, err.Error(), "hex string")
	})
}
