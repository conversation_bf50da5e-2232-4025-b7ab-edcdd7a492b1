with-expecter: true
filename: "{{.InterfaceName | snakecase}}.go"
dir: mocks/{{ replaceAll .InterfaceDirRelative "internal" "internal_" }}
resolve-type-alias: false
packages:
    github.com/smartcontractkit/chainlink-ccip/commit/merkleroot:
        interfaces:
            Observer:
    github.com/smartcontractkit/chainlink-ccip/internal/reader:
        interfaces:
            HomeChain:
    github.com/smartcontractkit/chainlink-ccip/internal/plugincommon:
        interfaces:
            ChainSupport:
            PluginProcessor:
    github.com/smartcontractkit/chainlink-ccip/commit/merkleroot/rmn:
        interfaces:
            Controller:
            Stream:
    github.com/smartcontractkit/chainlink-ccip/pkg/reader:
        interfaces:
            CCIPReader:
            PriceReader:
            RMNHome:
    github.com/smartcontractkit/chainlink-ccip/execute/internal/cache:
        interfaces:
            TimeProvider:
    github.com/smartcontractkit/chainlink-ccip/pkg/ocrtypecodec/v1:
        interfaces:
            ExecCodec:
    github.com/smartcontractkit/chainlink-ccip/pkg/contractreader:
        interfaces:
            Extended:
            ContractReaderFacade:
    github.com/smartcontractkit/chainlink-ccip/pkg/types/ccipocr3:
        interfaces:
            ExecutePluginCodec:
            EstimateProvider:
            AddressCodec:
            CommitPluginCodec:
    github.com/smartcontractkit/chainlink-common/pkg/types:
            interfaces:
                ContractWriter:
                    config:
                        dir: mocks/chainlink_common/
    github.com/smartcontractkit/libocr/networking:
        interfaces:
            PeerGroupFactory:
                config:
                    dir: mocks/libocr_networking/
            PeerGroup:
                config:
                    dir: mocks/libocr_networking/
