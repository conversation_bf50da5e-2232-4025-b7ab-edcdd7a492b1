// Code generated by go-enum DO NOT EDIT.
// Version:
// Revision:
// Build Date:
// Built By:

package parse

import (
	"fmt"
	"strings"
)

const (
	// LogTypeMixedGoTestJSON is a LogType of type MixedGoTestJSON.
	LogTypeMixedGoTestJSON LogType = "MixedGoTestJSON"
	// LogTypeJSON is a LogType of type JSON.
	LogTypeJSON LogType = "JSON"
	// LogTypeMixed is a LogType of type Mixed.
	LogTypeMixed LogType = "Mixed"
	// LogTypeCI is a LogType of type CI.
	LogTypeCI LogType = "CI"
)

var ErrInvalidLogType = fmt.Errorf("not a valid LogType, try [%s]", strings.Join(_LogTypeNames, ", "))

var _LogTypeNames = []string{
	string(LogTypeMixedGoTestJSON),
	string(LogTypeJSON),
	string(LogTypeMixed),
	string(LogTypeCI),
}

// LogTypeNames returns a list of possible string values of LogType.
func LogTypeNames() []string {
	tmp := make([]string, len(_LogTypeNames))
	copy(tmp, _LogTypeNames)
	return tmp
}

// String implements the Stringer interface.
func (x LogType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x LogType) IsValid() bool {
	_, err := ParseLogType(string(x))
	return err == nil
}

var _LogTypeValue = map[string]LogType{
	"MixedGoTestJSON": LogTypeMixedGoTestJSON,
	"mixedgotestjson": LogTypeMixedGoTestJSON,
	"JSON":            LogTypeJSON,
	"json":            LogTypeJSON,
	"Mixed":           LogTypeMixed,
	"mixed":           LogTypeMixed,
	"CI":              LogTypeCI,
	"ci":              LogTypeCI,
}

// ParseLogType attempts to convert a string to a LogType.
func ParseLogType(name string) (LogType, error) {
	if x, ok := _LogTypeValue[name]; ok {
		return x, nil
	}
	// Case insensitive parse, do a separate lookup to prevent unnecessary cost of lowercasing a string if we don't need to.
	if x, ok := _LogTypeValue[strings.ToLower(name)]; ok {
		return x, nil
	}
	return LogType(""), fmt.Errorf("%s is %w", name, ErrInvalidLogType)
}
