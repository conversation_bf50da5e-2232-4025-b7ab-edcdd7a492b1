# All code generation should be run prior to pull request. Running it again should not produce a diff.
name: "Codegen Verifier"

on:
  pull_request:
  push:
    branches:
      - 'main'

jobs:
  codegen-verifier:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: .
    steps:
      - uses: actions/checkout@9bb56186c3b09b4f86b1c65136769dd318469633 # v4.1.2
      - name: Determine Go version
        id: go_version
        run: echo "GO_VERSION=$(cat go.mod |grep "^go"|cut -d' ' -f 2)" >> $GITHUB_ENV
      - name: Setup Go ${{ env.GO_VERSION }}
        uses: actions/setup-go@0c52d547c9bc32b1aa3301fd7a9cb496313a4491 # v5.0.0
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Display Go version
        run: go version
      - name: Install protoc
        run: make install-protoc
      - name: Re-Generate files
        run: |
          make generate
      - name: Tidy
        run: go mod tidy
      - name: ensure no changes
        run: |
          set -e
          git_status=$(git status --porcelain=v1)
          if [ ! -z "$git_status" ]; then
            git status
            git diff
            echo "Error: modified files detected, run 'make generate' / 'go mod tidy'."
            exit 1
          fi
