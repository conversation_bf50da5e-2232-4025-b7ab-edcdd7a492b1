name: "Run CCIP OCR3 Integration Test"

on:
  pull_request:
  push:
    branches:
      - 'main'

jobs:
  integration-test-matrix:
    env:
      # We explicitly have this env var not be "CL_DATABASE_URL" to avoid having it be used by core related tests
      # when they should not be using it, while still allowing us to DRY up the setup
      DB_URL: postgresql://postgres:postgres@localhost:5432/chainlink_test?sslmode=disable
    
    strategy:
      fail-fast: false
      matrix:
        type:
          - cmd: cd integration-tests/smoke/ccip && go test ccip_fees_test.go -timeout 12m -test.parallel=2 -count=1 -json
            name: "Fees Test"
          - cmd: cd integration-tests/smoke/ccip && go test -run "TestTokenTransfer_EVM2EVM" ccip_token_transfer_test.go -timeout 12m -test.parallel=1 -count=1 -json
            name: "Token Transfer Test"
          - cmd: cd integration-tests/smoke/ccip && go test ccip_usdc_test.go -timeout 12m -test.parallel=1 -count=1 -json
            name: "USDC Test"
          - cmd: cd integration-tests/smoke/ccip && go test ccip_ooo_execution_test.go -timeout 12m -test.parallel=1 -count=1 -json
            name: "OOO Execution Test"
          - cmd: cd integration-tests/smoke/ccip && go test -run "Test_CCIPMessaging_EVM2EVM" ccip_messaging_test.go -timeout 12m -test.parallel=2 -count=1 -json
            name: "Messaging Test"
          - cmd: cd integration-tests/smoke/ccip && go test -run "Test_CCIPBatching_MaxBatchSizeEVM" ccip_batching_test.go -timeout 12m -test.parallel=1 -count=1 -json
            name: "Batching Test Test_CCIPBatching_MaxBatchSizeEVM"
          - cmd: cd integration-tests/smoke/ccip && go test -run "^Test_CCIPBatching_MultiSource$" ccip_batching_test.go -timeout 12m -test.parallel=1 -count=1 -json
            name: "Batching Test Test_CCIPBatching_MultiSource"
          - cmd: cd integration-tests/smoke/ccip && go test -run "Test_CCIPBatching_MultiSource_MultiReports" ccip_batching_test.go -timeout 12m -test.parallel=1 -count=1 -json
            name: "Batching Test Test_CCIPBatching_MultiSource_MultiReports"
          - cmd: cd integration-tests/smoke/ccip && go test -run "^Test_CCIPBatching_SingleSource$" ccip_batching_test.go -timeout 12m -test.parallel=1 -count=1 -json
            name: "Batching Test Test_CCIPBatching_SingleSource"
          - cmd: cd integration-tests/smoke/ccip && go test -run "Test_CCIPBatching_SingleSource_MultipleReports" ccip_batching_test.go -timeout 12m -test.parallel=1 -count=1 -json
            name: "Batching Test Test_CCIPBatching_SingleSource_MultipleReports"
          - cmd: cd integration-tests/smoke/ccip && go test ccip_gas_price_updates_test.go -timeout 12m -test.parallel=2 -count=1 -json
            name: "Gas Price Updates Test"
          # TODO: this can only run in docker for now, switch to in-memory and uncomment
          # - cmd: cd integration-tests/smoke/ccip && go test ccip_token_price_updates_test.go -timeout 12m -test.parallel=2 -count=1 -json
          #   name: "Token Price Updates Test"
          - cmd: cd integration-tests/smoke/ccip && go test ccip_reader_test.go -timeout 5m -test.parallel=1 -count=1 -json
            name: "CCIPReader Test"
    
    name: Integration Tests (${{ matrix.type.name }})
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the chainlink-ccip repo
        uses: actions/checkout@9bb56186c3b09b4f86b1c65136769dd318469633 # v4.1.2
      - name: Determine Go version
        id: go_version
        run: echo "GO_VERSION=$(cat go.mod |grep "^go"|cut -d' ' -f 2)" >> $GITHUB_ENV
      - name: Setup Go ${{ env.GO_VERSION }}
        uses: actions/setup-go@0c52d547c9bc32b1aa3301fd7a9cb496313a4491 # v5.0.0
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Display Go version
        run: go version
      - name: Fetch latest pull request data
        id: fetch_pr_data
        uses: actions/github-script@v6
        # only run this step if the event is a pull request
        if: github.event_name == 'pull_request'
        with:
          script: |
            const pr = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number
            });
            return pr.data.body;
      - name: Get the chainlink commit sha from PR description, if applicable
        id: get_chainlink_sha
        run: |
          default="develop"
          if [ "${{ github.event_name }}" == "pull_request" ]; then
          comment=$(cat <<'GREAT_PR_DESCRIPTION_HERE'
          ${{ steps.fetch_pr_data.outputs.result }}
          GREAT_PR_DESCRIPTION_HERE
          )
            echo "$comment"
            core_ref=$(echo "$comment" | grep -oE 'core ref:\s*[a-f0-9]{40}' | awk '{print $NF}' || true)
            if [ -n "$core_ref" ]; then
              echo "Overriding chainlink repository commit hash with: $core_ref"
              echo "::set-output name=ref::$core_ref"
            else
              echo "Using default chainlink repository branch: $default"
              echo "::set-output name=ref::$default"
            fi
          else
            echo "::set-output name=ref::$default"
          fi
      - name: Clone Chainlink repo
        uses: actions/checkout@9bb56186c3b09b4f86b1c65136769dd318469633 # v4.1.2
        with:
          repository: smartcontractkit/chainlink
          ref: ${{ steps.get_chainlink_sha.outputs.ref }}
          path: chainlink
      - name: Get the correct chainlink-ccip commit SHA via GitHub API
        id: get_sha
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            COMMIT_SHA=${{ github.event.pull_request.head.sha }}
          else
            COMMIT_SHA=$(curl -s -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
              "https://api.github.com/repos/${{ github.repository }}/commits/${{ github.ref }}" | jq -r .sha)
          fi
          echo "::set-output name=sha::$COMMIT_SHA"
      - name: Update chainlink-ccip dependency in chainlink
        run: |
          cd $GITHUB_WORKSPACE/chainlink
          go get github.com/smartcontractkit/chainlink-ccip@${{ steps.get_sha.outputs.sha }}
          make gomodtidy
      - name: Setup Postgres
        uses: ./.github/actions/setup-postgres
      - name: Download Go vendor packages
        run: |
          cd $GITHUB_WORKSPACE/chainlink
          go mod download
          cd $GITHUB_WORKSPACE/chainlink/integration-tests
          go mod download
      - name: Build binary
        run: |
          cd $GITHUB_WORKSPACE/chainlink
          go build -o ccip.test .
      - name: Setup DB
        run: |
          cd $GITHUB_WORKSPACE/chainlink
          ./ccip.test local db preparetest
        env:
          CL_DATABASE_URL: ${{ env.DB_URL }}
      - name: Run ${{ matrix.type.name }}
        run: cd $GITHUB_WORKSPACE/chainlink/ && ${{ matrix.type.cmd }}
        env:
          CL_DATABASE_URL: ${{ env.DB_URL }}

  integration-test-ccip-ocr3:
    if: always()
    runs-on: ubuntu-latest
    needs: [integration-test-matrix]
    steps:
      - name: Fail the job if ccip tests in PR not successful
        if: always() && needs.integration-test-matrix.result == 'failure'
        run: exit 1
