name: Solana

on:
  push:
    branches:
      - main
  pull_request:

concurrency:
  group: solana-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    working-directory: ./chains/solana

jobs:
  get_anchor_version:
    name: Get Anchor Version
    runs-on: ubuntu-latest
    outputs:
      anchor_version: ${{ steps.anchorversion.outputs.anchor }}
    steps:
      - name: Checkout the repo
        uses: actions/checkout@44c2b7a8a4ea60a981eaca3cf939b5f4305c123b # v4.1.5
      - name: Get Anchor Version
        id: anchorversion
        run: |
          anchor=$(make anchor_version)
          echo "anchor=${anchor}" >>$GITHUB_OUTPUT

  build_solana:
    name: cache build artifacts
    runs-on: ubuntu-latest-8cores-32GB
    needs: [get_anchor_version]
    steps:
    - uses: actions/checkout@44c2b7a8a4ea60a981eaca3cf939b5f4305c123b # v4.1.5
    - name: cache docker build image
      id: cache-image
      uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
      with:
        lookup-only: true
        path: chains/solana/contracts/docker-build.tar
        key: ${{ runner.os }}-solana-build-${{ needs.get_anchor_version.outputs.anchor_version }}-${{ hashFiles('**/Cargo.lock') }}
    - name: Cache cargo target dir
      id: cache-target
      uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
      with:
        lookup-only: true
        path: chains/solana/contracts/target
        key: ${{ runner.os }}-solana-contract-artifacts-${{ hashFiles('**/Cargo.lock') }}
    - name: build & save image
      if: steps.cache-image.outputs.cache-hit != 'true'
      run: |
        cd contracts
        docker buildx build . -t ccip-solana:build --build-arg ANCHOR_CLI=${{ needs.get_anchor_version.outputs.anchor_version }}
        docker save -o docker-build.tar ccip-solana
    - name: build & save contract compilation artifacts
      if: steps.cache-target.outputs.cache-hit != 'true'
      run: |
        docker run -v "$(pwd)/":/solana ccip-solana:build bash -c "\
          set -eoux pipefail &&\
          RUSTUP_HOME=\"/root/.rustup\" &&\
          FORCE_COLOR=1 &&\
          cd /solana/contracts &&\
          anchor build &&\
          chmod -R 755 ./target"

  rust:
    name: rust tests
    runs-on: ubuntu-latest
    needs: [get_anchor_version, build_solana]
    steps:
    - uses: actions/checkout@44c2b7a8a4ea60a981eaca3cf939b5f4305c123b # v4.1.5
    - name: Cache cargo target dir
      uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
      with:
        fail-on-cache-miss: true
        path: chains/solana/contracts/target
        key: ${{ runner.os }}-solana-contract-artifacts-${{ hashFiles('**/Cargo.lock') }}
    - name: cache docker build image
      id: cache-image
      uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
      with:
        fail-on-cache-miss: true
        path: chains/solana/contracts/docker-build.tar
        key: ${{ runner.os }}-solana-build-${{ needs.get_anchor_version.outputs.anchor_version }}-${{ hashFiles('**/Cargo.lock') }}
    - name: load cached image
      run: |
        docker load --input contracts/docker-build.tar
    - name: run tests
      run: |
          docker run -v "$(pwd)/":/solana ccip-solana:build bash -c "\
          set -eoux pipefail &&\
          RUSTUP_HOME=\"/root/.rustup\" &&\
          FORCE_COLOR=1 &&\
          cd /solana/contracts &&\
          anchor build &&\
          cargo check &&\
          cargo clippy -- -D warnings &&\
          cargo test --workspace"

  go:
    name: go tests
    runs-on: ubuntu-latest-8cores-32GB
    needs: [get_anchor_version, build_solana]
    steps:
      - uses: actions/checkout@44c2b7a8a4ea60a981eaca3cf939b5f4305c123b # v4.1.5
      - name: Cache cargo target dir
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          fail-on-cache-miss: true
          path: chains/solana/contracts/target
          key: ${{ runner.os }}-solana-contract-artifacts-${{ hashFiles('**/Cargo.lock') }}
      - name: cache docker build image
        id: cache-image
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          fail-on-cache-miss: true
          path: chains/solana/contracts/docker-build.tar
          key: ${{ runner.os }}-solana-build-${{ needs.get_anchor_version.outputs.anchor_version }}-${{ hashFiles('**/Cargo.lock') }}
      - name: load cached image
        run: |
          docker load --input contracts/docker-build.tar
      - name: Setup go
        uses: actions/setup-go@cdcb36043654635271a94b9a6d1392de5bb323a7 # v5.0.1
        with:
          go-version-file: "./chains/solana/go.mod"
          check-latest: true
          cache-dependency-path: "./chains/solana/go.sum"
      - name: Install gotestloghelper
        run: go install github.com/smartcontractkit/chainlink-testing-framework/tools/gotestloghelper@latest
      - name: Install Solana CLI
        run: |
          sh -c "$(curl -sSfL https://release.anza.xyz/stable/install)" # always use latest stable release from solana
          echo "PATH=$HOME/.local/share/solana/install/active_release/bin:$PATH" >> $GITHUB_ENV
      - name: build + test
        run: |
          set -eoux pipefail
          # compile artifacts
          docker run -v "$(pwd)/":/solana ccip-solana:build bash -c "\
          set -eoux pipefail &&\
          RUSTUP_HOME=\"/root/.rustup\" &&\
          FORCE_COLOR=1 &&\
          cd /solana/contracts &&\
          anchor build"
          make go-tests

  lint:
    name: lint + check artifacts
    runs-on: ubuntu-latest
    needs: [get_anchor_version, build_solana]
    steps:
      - uses: actions/checkout@44c2b7a8a4ea60a981eaca3cf939b5f4305c123b # v4.1.5
      - name: Cache cargo target dir
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          fail-on-cache-miss: true
          path: chains/solana/contracts/target
          key: ${{ runner.os }}-solana-contract-artifacts-${{ hashFiles('**/Cargo.lock') }}
      - name: cache docker build image
        id: cache-image
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          fail-on-cache-miss: true
          path: chains/solana/contracts/docker-build.tar
          key: ${{ runner.os }}-solana-build-${{ needs.get_anchor_version.outputs.anchor_version }}-${{ hashFiles('**/Cargo.lock') }}
      - name: load cached image
        run: |
          docker load --input contracts/docker-build.tar
      - name: Setup go
        uses: actions/setup-go@cdcb36043654635271a94b9a6d1392de5bb323a7 # v5.0.1
        with:
          go-version-file: "./chains/solana/go.mod"
          check-latest: true
          cache-dependency-path: "./chains/solana/go.sum"
      - name: check artifacts
        run: |
          set -eoux pipefail
          # compile artifacts
          docker run -v "$(pwd)/":/solana ccip-solana:build bash -c "\
          set -eoux pipefail &&\
          RUSTUP_HOME=\"/root/.rustup\" &&\
          FORCE_COLOR=1 &&\
          cd /solana/contracts &&\
          rm -rf target/idl target/types &&\
          anchor build"

          go install github.com/gagliardetto/anchor-go@v0.2.3
          ./scripts/anchor-go-gen.sh
          make format
          git diff --exit-code
      - name: Install linter
        run: |
          curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.64.5
      - name: Run linter
        run: make lint-go
      - name: Print lint report artifact
        if: failure()
        shell: bash
        run: cat ./golangci-lint-report.xml
