run:
  timeout: 60s
linters:
  verbose: true
  enable:
    - copyloopvar
    - depguard
    - dupl
    - errcheck
    - errorlint
    - exhaustive
    - goconst
    - gocyclo
    - gofmt
    - goimports
    - gosec
    - gosimple
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - revive
    - rowserrcheck
    - sqlclosecheck
    - staticcheck
    - typecheck
    - unconvert
    - unparam
    - unused
linters-settings:
  gosec:
    # TODO - remove this and fix linter complaint
    excludes:
      - G115
  exhaustive:
    default-signifies-exhaustive: true
  goimports:
    local-prefixes: "github.com/smartcontractkit/chainlink-ccip"
  errorlint:
    errorf: true # Disallow formatting of errors without %w
  revive:
    confidence: 0.8
    rules:
      - name: blank-imports
      - name: context-as-argument
      - name: context-keys-type
      - name: dot-imports
      - name: error-return
      - name: error-strings
      - name: error-naming
      - name: if-return
      - name: increment-decrement
      - name: var-naming
      - name: var-declaration
      - name: package-comments
      - name: range
      - name: receiver-naming
      - name: time-naming
      - name: unexported-return
      - name: indent-error-flow
      - name: errorf
      - name: empty-block
      - name: superfluous-else
      # - name: unused-parameter
      - name: unreachable-code
      - name: redefines-builtin-id
      - name: waitgroup-by-value
      - name: unconditional-recursion
      - name: struct-tag
      - name: string-format
      - name: string-of-int
      - name: range-val-address
      - name: range-val-in-closure
      - name: modifies-value-receiver
      - name: modifies-parameter
      - name: identical-branches
      - name: get-return
      #- name: flag-parameter
      - name: early-return
      - name: defer
      - name: constant-logical-expr
      - name: confusing-naming
      - name: confusing-results
      - name: bool-literal-in-expr
      - name: atomic
  depguard:
    rules:
      main:
        list-mode: lax
        deny:
          - pkg: github.com/test-go/testify/assert
            desc: Use github.com/stretchr/testify/assert instead
          - pkg: github.com/test-go/testify/mock
            desc: Use github.com/stretchr/testify/mock instead
          - pkg: github.com/test-go/testify/require
            desc: Use github.com/stretchr/testify/require instead
  lll:
    # Max line length, lines longer will be reported.
    # '\t' is counted as 1 character by default, and can be changed with the tab-width option.
    # Default: 120.
    line-length: 120
  gocyclo:
    min-complexity: 13
issues:
  max-issues-per-linter: 0
  max-same-issues: 0
  exclude-rules:
    - path: test
      text: "^G404:"
      linters:
        - gosec
