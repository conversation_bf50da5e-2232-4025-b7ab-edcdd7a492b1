package ccip

import (
	ag_binary "github.com/gagliardetto/binary"
)

/**
 * This Errors file should be automatically generated by Anchor-Go but they don't have full support for errors.
 * So, this file is created manually but adhering to the conventions of anchor-go, which is why many definitions
 * have a nolint:all comment given that anchor conventions use underscores in names which isn't idiomatic in Go.
 */

////////////
// Router //
////////////

//nolint:all
type CcipRouterError ag_binary.BorshEnum

//nolint:all
const (
	Unauthorized_CcipRouterError CcipRouterError = iota
	InvalidInputsMint_CcipRouterError
	InvalidVersion_CcipRouterError
	FeeTokenMismatch_CcipRouterError
	RedundantOwnerProposal_CcipRouterError
	ReachedMaxSequenceNumber_CcipRouterError
	InvalidInputsTokenIndices_CcipRouterError
	InvalidInputsPoolAccounts_CcipRouterError
	InvalidInputsTokenAccounts_CcipRouterError
	InvalidInputsConfigAccounts_CcipRouterError
	InvalidInputsTokenAdminRegistryAccounts_CcipRouterError
	InvalidInputsLookupTableAccounts_CcipRouterError
	InvalidInputsLookupTableAccountWritable_CcipRouterError
	InvalidInputsTokenAmount_CcipRouterError
	InvalidInputsTransferAllAmount_CcipRouterError
	InvalidInputsAtaAddress_CcipRouterError
	InvalidInputsAtaWritable_CcipRouterError
	InvalidInputsChainSelector_CcipRouterError
	InsufficientLamports_CcipRouterError
	InsufficientFunds_CcipRouterError
	SourceTokenDataTooLarge_CcipRouterError
	InvalidTokenAdminRegistryInputsZeroAddress_CcipRouterError
	InvalidTokenAdminRegistryProposedAdmin_CcipRouterError
	SenderNotAllowed_CcipRouterError
	InvalidCodeVersion_CcipRouterError
	InvalidCcipVersionRollback_CcipRouterError
)

func (value CcipRouterError) String() string {
	switch value {
	case Unauthorized_CcipRouterError:
		return "Unauthorized"
	case InvalidInputsMint_CcipRouterError:
		return "InvalidInputsMint"
	case InvalidVersion_CcipRouterError:
		return "InvalidVersion"
	case FeeTokenMismatch_CcipRouterError:
		return "FeeTokenMismatch"
	case RedundantOwnerProposal_CcipRouterError:
		return "RedundantOwnerProposal"
	case ReachedMaxSequenceNumber_CcipRouterError:
		return "ReachedMaxSequenceNumber"
	case InvalidInputsTokenIndices_CcipRouterError:
		return "InvalidInputsTokenIndices"
	case InvalidInputsPoolAccounts_CcipRouterError:
		return "InvalidInputsPoolAccounts"
	case InvalidInputsTokenAccounts_CcipRouterError:
		return "InvalidInputsTokenAccounts"
	case InvalidInputsConfigAccounts_CcipRouterError:
		return "InvalidInputsConfigAccounts"
	case InvalidInputsTokenAdminRegistryAccounts_CcipRouterError:
		return "InvalidInputsTokenAdminRegistryAccounts"
	case InvalidInputsLookupTableAccounts_CcipRouterError:
		return "InvalidInputsLookupTableAccounts"
	case InvalidInputsLookupTableAccountWritable_CcipRouterError:
		return "InvalidInputsLookupTableAccountWritable"
	case InvalidInputsTokenAmount_CcipRouterError:
		return "InvalidInputsTokenAmount"
	case InvalidInputsTransferAllAmount_CcipRouterError:
		return "InvalidInputsTransferAllAmount"
	case InvalidInputsAtaAddress_CcipRouterError:
		return "InvalidInputsAtaAddress"
	case InvalidInputsAtaWritable_CcipRouterError:
		return "InvalidInputsAtaWritable"
	case InvalidInputsChainSelector_CcipRouterError:
		return "InvalidInputsChainSelector"
	case InsufficientLamports_CcipRouterError:
		return "InsufficientLamports"
	case InsufficientFunds_CcipRouterError:
		return "InsufficientFunds"
	case SourceTokenDataTooLarge_CcipRouterError:
		return "SourceTokenDataTooLarge"
	case InvalidTokenAdminRegistryInputsZeroAddress_CcipRouterError:
		return "InvalidTokenAdminRegistryInputsZeroAddress"
	case InvalidTokenAdminRegistryProposedAdmin_CcipRouterError:
		return "InvalidTokenAdminRegistryProposedAdmin"
	case SenderNotAllowed_CcipRouterError:
		return "SenderNotAllowed"
	case InvalidCodeVersion_CcipRouterError:
		return "InvalidCodeVersion"
	case InvalidCcipVersionRollback_CcipRouterError:
		return "InvalidCcipVersionRollback"
	default:
		return ""
	}
}

////////////////
// Fee Quoter //
////////////////

//nolint:all
type FeeQuoterError ag_binary.BorshEnum

//nolint:all
const (
	Unauthorized_FeeQuoterError FeeQuoterError = iota
	InvalidInputs_FeeQuoterError
	ZeroGasLimit_FeeQuoterError
	DefaultGasLimitExceedsMaximum_FeeQuoterError
	InvalidVersion_FeeQuoterError
	RedundantOwnerProposal_FeeQuoterError
	InvalidInputsMissingWritable_FeeQuoterError
	InvalidInputsChainSelector_FeeQuoterError
	InvalidInputsMint_FeeQuoterError
	InvalidInputsMintOwner_FeeQuoterError
	InvalidInputsTokenConfigAccount_FeeQuoterError
	InvalidInputsMissingExtraArgs_FeeQuoterError
	InvalidInputsMissingDataAfterExtraArgs_FeeQuoterError
	InvalidInputsDestChainStateAccount_FeeQuoterError
	InvalidInputsPerChainPerTokenConfig_FeeQuoterError
	InvalidInputsBillingTokenConfig_FeeQuoterError
	InvalidInputsAccountCount_FeeQuoterError
	InvalidInputsNoUpdates_FeeQuoterError
	InvalidInputsTokenAccounts_FeeQuoterError
	DestinationChainDisabled_FeeQuoterError
	FeeTokenDisabled_FeeQuoterError
	MessageTooLarge_FeeQuoterError
	UnsupportedNumberOfTokens_FeeQuoterError
	InvalidEVMAddress_FeeQuoterError
	InvalidEncoding_FeeQuoterError
	InvalidTokenPrice_FeeQuoterError
	StaleGasPrice_FeeQuoterError
	InvalidInputsMissingTokenConfig_FeeQuoterError
	MessageFeeTooHigh_FeeQuoterError
	MessageGasLimitTooHigh_FeeQuoterError
	ExtraArgOutOfOrderExecutionMustBeTrue_FeeQuoterError
	InvalidExtraArgsTag_FeeQuoterError
	InvalidChainFamilySelector_FeeQuoterError
	InvalidTokenReceiver_FeeQuoterError
	InvalidSVMAddress_FeeQuoterError
	UnauthorizedPriceUpdater_FeeQuoterError
	InvalidCodeVersion_FeeQuoterError
)

func (value FeeQuoterError) String() string {
	switch value {
	case Unauthorized_FeeQuoterError:
		return "Unauthorized"
	case InvalidInputs_FeeQuoterError:
		return "InvalidInputs"
	case ZeroGasLimit_FeeQuoterError:
		return "ZeroGasLimit"
	case DefaultGasLimitExceedsMaximum_FeeQuoterError:
		return "DefaultGasLimitExceedsMaximum"
	case InvalidVersion_FeeQuoterError:
		return "InvalidVersion"
	case RedundantOwnerProposal_FeeQuoterError:
		return "RedundantOwnerProposal"
	case InvalidInputsMissingWritable_FeeQuoterError:
		return "InvalidInputsMissingWritable"
	case InvalidInputsChainSelector_FeeQuoterError:
		return "InvalidInputsChainSelector"
	case InvalidInputsMint_FeeQuoterError:
		return "InvalidInputsMint"
	case InvalidInputsMintOwner_FeeQuoterError:
		return "InvalidInputsMintOwner"
	case InvalidInputsTokenConfigAccount_FeeQuoterError:
		return "InvalidInputsTokenConfigAccount"
	case InvalidInputsMissingExtraArgs_FeeQuoterError:
		return "InvalidInputsMissingExtraArgs"
	case InvalidInputsMissingDataAfterExtraArgs_FeeQuoterError:
		return "InvalidInputsMissingDataAfterExtraArgs"
	case InvalidInputsDestChainStateAccount_FeeQuoterError:
		return "InvalidInputsDestChainStateAccount"
	case InvalidInputsPerChainPerTokenConfig_FeeQuoterError:
		return "InvalidInputsPerChainPerTokenConfig"
	case InvalidInputsBillingTokenConfig_FeeQuoterError:
		return "InvalidInputsBillingTokenConfig"
	case InvalidInputsAccountCount_FeeQuoterError:
		return "InvalidInputsAccountCount"
	case InvalidInputsNoUpdates_FeeQuoterError:
		return "InvalidInputsNoUpdates"
	case InvalidInputsTokenAccounts_FeeQuoterError:
		return "InvalidInputsTokenAccounts"
	case DestinationChainDisabled_FeeQuoterError:
		return "DestinationChainDisabled"
	case FeeTokenDisabled_FeeQuoterError:
		return "FeeTokenDisabled"
	case MessageTooLarge_FeeQuoterError:
		return "MessageTooLarge"
	case UnsupportedNumberOfTokens_FeeQuoterError:
		return "UnsupportedNumberOfTokens"
	case InvalidEVMAddress_FeeQuoterError:
		return "InvalidEVMAddress"
	case InvalidEncoding_FeeQuoterError:
		return "InvalidEncoding"
	case InvalidTokenPrice_FeeQuoterError:
		return "InvalidTokenPrice"
	case StaleGasPrice_FeeQuoterError:
		return "StaleGasPrice"
	case InvalidInputsMissingTokenConfig_FeeQuoterError:
		return "InvalidInputsMissingTokenConfig"
	case MessageFeeTooHigh_FeeQuoterError:
		return "MessageFeeTooHigh"
	case MessageGasLimitTooHigh_FeeQuoterError:
		return "MessageGasLimitTooHigh"
	case ExtraArgOutOfOrderExecutionMustBeTrue_FeeQuoterError:
		return "ExtraArgOutOfOrderExecutionMustBeTrue"
	case InvalidExtraArgsTag_FeeQuoterError:
		return "InvalidExtraArgsTag"
	case InvalidChainFamilySelector_FeeQuoterError:
		return "InvalidChainFamilySelector"
	case InvalidTokenReceiver_FeeQuoterError:
		return "InvalidTokenReceiver"
	case InvalidSVMAddress_FeeQuoterError:
		return "InvalidSVMAddress"
	case UnauthorizedPriceUpdater_FeeQuoterError:
		return "UnauthorizedPriceUpdater"
	case InvalidCodeVersion_FeeQuoterError:
		return "InvalidCodeVersion"
	default:
		return ""
	}
}

/////////////
// Offramp //
/////////////

//nolint:all
type CcipOfframpError ag_binary.BorshEnum

//nolint:all
const (
	InvalidSequenceInterval_CcipOfframpError CcipOfframpError = iota
	RootNotCommitted_CcipOfframpError
	ExistingMerkleRoot_CcipOfframpError
	Unauthorized_CcipOfframpError
	InvalidNonce_CcipOfframpError
	InvalidInputsMissingWritable_CcipOfframpError
	OnrampNotConfigured_CcipOfframpError
	FailedToDeserializeReport_CcipOfframpError
	InvalidPluginType_CcipOfframpError
	InvalidVersion_CcipOfframpError
	MissingExpectedPriceUpdates_CcipOfframpError
	MissingExpectedMerkleRoot_CcipOfframpError
	UnexpectedMerkleRoot_CcipOfframpError
	RedundantOwnerProposal_CcipOfframpError
	UnsupportedSourceChainSelector_CcipOfframpError
	UnsupportedDestinationChainSelector_CcipOfframpError
	InvalidProof_CcipOfframpError
	InvalidMessage_CcipOfframpError
	ReachedMaxSequenceNumber_CcipOfframpError
	ManualExecutionNotAllowed_CcipOfframpError
	InvalidInputsNumberOfAccounts_CcipOfframpError
	InvalidInputsGlobalStateAccount_CcipOfframpError
	InvalidInputsTokenIndices_CcipOfframpError
	InvalidInputsPoolAccounts_CcipOfframpError
	InvalidInputsTokenAccounts_CcipOfframpError
	InvalidInputsSysvarAccount_CcipOfframpError
	InvalidInputsFeeQuoterAccount_CcipOfframpError
	InvalidInputsAllowedOfframpAccount_CcipOfframpError
	InvalidInputsConfigAccounts_CcipOfframpError
	InvalidInputsTokenAdminRegistryAccounts_CcipOfframpError
	InvalidInputsLookupTableAccounts_CcipOfframpError
	InvalidInputsLookupTableAccountWritable_CcipOfframpError
	OfframpReleaseMintBalanceMismatch_CcipOfframpError
	OfframpInvalidDataLength_CcipOfframpError
	StaleCommitReport_CcipOfframpError
	InvalidWritabilityBitmap_CcipOfframpError
	InvalidCodeVersion_CcipOfframpError
	Ocr3InvalidConfigFMustBePositive_CcipOfframpError
	Ocr3InvalidConfigTooManyTransmitters_CcipOfframpError
	Ocr3InvalidConfigTooManySigners_CcipOfframpError
	Ocr3InvalidConfigFIsTooHigh_CcipOfframpError
	Ocr3InvalidConfigRepeatedOracle_CcipOfframpError
	Ocr3WrongMessageLength_CcipOfframpError
	Ocr3ConfigDigestMismatch_CcipOfframpError
	Ocr3WrongNumberOfSignatures_CcipOfframpError
	Ocr3UnauthorizedTransmitter_CcipOfframpError
	Ocr3UnauthorizedSigner_CcipOfframpError
	Ocr3NonUniqueSignatures_CcipOfframpError
	Ocr3OracleCannotBeZeroAddress_CcipOfframpError
	Ocr3StaticConfigCannotBeChanged_CcipOfframpError
	Ocr3InvalidPluginType_CcipOfframpError
	Ocr3InvalidSignature_CcipOfframpError
	Ocr3SignaturesOutOfRegistration_CcipOfframpError
	InvalidOnrampAddress_CcipOfframpError
	InvalidInputsExternalExecutionSignerAccount_CcipOfframpError
	CommitReportHasPendingMessages_CcipOfframpError
)

func (value CcipOfframpError) String() string {
	switch value {
	case InvalidSequenceInterval_CcipOfframpError:
		return "InvalidSequenceInterval"
	case RootNotCommitted_CcipOfframpError:
		return "RootNotCommitted"
	case ExistingMerkleRoot_CcipOfframpError:
		return "ExistingMerkleRoot"
	case Unauthorized_CcipOfframpError:
		return "Unauthorized"
	case InvalidNonce_CcipOfframpError:
		return "InvalidNonce"
	case InvalidInputsMissingWritable_CcipOfframpError:
		return "InvalidInputsMissingWritable"
	case OnrampNotConfigured_CcipOfframpError:
		return "OnrampNotConfigured"
	case FailedToDeserializeReport_CcipOfframpError:
		return "FailedToDeserializeReport"
	case InvalidPluginType_CcipOfframpError:
		return "InvalidPluginType"
	case InvalidVersion_CcipOfframpError:
		return "InvalidVersion"
	case MissingExpectedPriceUpdates_CcipOfframpError:
		return "MissingExpectedPriceUpdates"
	case MissingExpectedMerkleRoot_CcipOfframpError:
		return "MissingExpectedMerkleRoot"
	case UnexpectedMerkleRoot_CcipOfframpError:
		return "UnexpectedMerkleRoot"
	case RedundantOwnerProposal_CcipOfframpError:
		return "RedundantOwnerProposal"
	case UnsupportedSourceChainSelector_CcipOfframpError:
		return "UnsupportedSourceChainSelector"
	case UnsupportedDestinationChainSelector_CcipOfframpError:
		return "UnsupportedDestinationChainSelector"
	case InvalidProof_CcipOfframpError:
		return "InvalidProof"
	case InvalidMessage_CcipOfframpError:
		return "InvalidMessage"
	case ReachedMaxSequenceNumber_CcipOfframpError:
		return "ReachedMaxSequenceNumber"
	case ManualExecutionNotAllowed_CcipOfframpError:
		return "ManualExecutionNotAllowed"
	case InvalidInputsNumberOfAccounts_CcipOfframpError:
		return "InvalidInputsNumberOfAccounts"
	case InvalidInputsGlobalStateAccount_CcipOfframpError:
		return "InvalidInputsGlobalStateAccount"
	case InvalidInputsTokenIndices_CcipOfframpError:
		return "InvalidInputsTokenIndices"
	case InvalidInputsPoolAccounts_CcipOfframpError:
		return "InvalidInputsPoolAccounts"
	case InvalidInputsTokenAccounts_CcipOfframpError:
		return "InvalidInputsTokenAccounts"
	case InvalidInputsSysvarAccount_CcipOfframpError:
		return "InvalidInputsSysvarAccount"
	case InvalidInputsFeeQuoterAccount_CcipOfframpError:
		return "InvalidInputsFeeQuoterAccount"
	case InvalidInputsAllowedOfframpAccount_CcipOfframpError:
		return "InvalidInputsAllowedOfframpAccount"
	case InvalidInputsConfigAccounts_CcipOfframpError:
		return "InvalidInputsConfigAccounts"
	case InvalidInputsTokenAdminRegistryAccounts_CcipOfframpError:
		return "InvalidInputsTokenAdminRegistryAccounts"
	case InvalidInputsLookupTableAccounts_CcipOfframpError:
		return "InvalidInputsLookupTableAccounts"
	case InvalidInputsLookupTableAccountWritable_CcipOfframpError:
		return "InvalidInputsLookupTableAccountWritable"
	case OfframpReleaseMintBalanceMismatch_CcipOfframpError:
		return "OfframpReleaseMintBalanceMismatch"
	case OfframpInvalidDataLength_CcipOfframpError:
		return "OfframpInvalidDataLength"
	case StaleCommitReport_CcipOfframpError:
		return "StaleCommitReport"
	case InvalidWritabilityBitmap_CcipOfframpError:
		return "InvalidWritabilityBitmap"
	case InvalidCodeVersion_CcipOfframpError:
		return "InvalidCodeVersion"
	case Ocr3InvalidConfigFMustBePositive_CcipOfframpError:
		return "Ocr3InvalidConfigFMustBePositive"
	case Ocr3InvalidConfigTooManyTransmitters_CcipOfframpError:
		return "Ocr3InvalidConfigTooManyTransmitters"
	case Ocr3InvalidConfigTooManySigners_CcipOfframpError:
		return "Ocr3InvalidConfigTooManySigners"
	case Ocr3InvalidConfigFIsTooHigh_CcipOfframpError:
		return "Ocr3InvalidConfigFIsTooHigh"
	case Ocr3InvalidConfigRepeatedOracle_CcipOfframpError:
		return "Ocr3InvalidConfigRepeatedOracle"
	case Ocr3WrongMessageLength_CcipOfframpError:
		return "Ocr3WrongMessageLength"
	case Ocr3ConfigDigestMismatch_CcipOfframpError:
		return "Ocr3ConfigDigestMismatch"
	case Ocr3WrongNumberOfSignatures_CcipOfframpError:
		return "Ocr3WrongNumberOfSignatures"
	case Ocr3UnauthorizedTransmitter_CcipOfframpError:
		return "Ocr3UnauthorizedTransmitter"
	case Ocr3UnauthorizedSigner_CcipOfframpError:
		return "Ocr3UnauthorizedSigner"
	case Ocr3NonUniqueSignatures_CcipOfframpError:
		return "Ocr3NonUniqueSignatures"
	case Ocr3OracleCannotBeZeroAddress_CcipOfframpError:
		return "Ocr3OracleCannotBeZeroAddress"
	case Ocr3StaticConfigCannotBeChanged_CcipOfframpError:
		return "Ocr3StaticConfigCannotBeChanged"
	case Ocr3InvalidPluginType_CcipOfframpError:
		return "Ocr3InvalidPluginType"
	case Ocr3InvalidSignature_CcipOfframpError:
		return "Ocr3InvalidSignature"
	case Ocr3SignaturesOutOfRegistration_CcipOfframpError:
		return "Ocr3Ocr3SignaturesOutOfRegistration"
	case InvalidOnrampAddress_CcipOfframpError:
		return "InvalidOnrampAddress"
	case InvalidInputsExternalExecutionSignerAccount_CcipOfframpError:
		return "InvalidInputsExternalExecutionSignerAccount"
	case CommitReportHasPendingMessages_CcipOfframpError:
		return "CommitReportHasPendingMessages"
	default:
		return ""
	}
}

///////////////
// RMNRemote //
///////////////

//nolint:all
type RmnRemoteError ag_binary.BorshEnum

//nolint:all
const (
	Unauthorized_RmnRemoteError RmnRemoteError = iota
	SubjectIsAlreadyCursed_RmnRemoteError
	SubjectWasNotCursed_RmnRemoteError
	RedundantOwnerProposal_RmnRemoteError
	InvalidVersion_RmnRemoteError
	SubjectCursed_RmnRemoteError
	GloballyCursed_RmnRemoteError
	InvalidCodeVersion_RmnRemoteError
)

func (value RmnRemoteError) String() string {
	switch value {
	case Unauthorized_RmnRemoteError:
		return "Unauthorized"
	case SubjectIsAlreadyCursed_RmnRemoteError:
		return "SubjectIsAlreadyCursed"
	case SubjectWasNotCursed_RmnRemoteError:
		return "SubjectWasNotCursed"
	case RedundantOwnerProposal_RmnRemoteError:
		return "RedundantOwnerProposal"
	case InvalidVersion_RmnRemoteError:
		return "InvalidVersion"
	case SubjectCursed_RmnRemoteError:
		return "SubjectCursed"
	case GloballyCursed_RmnRemoteError:
		return "GloballyCursed"
	case InvalidCodeVersion_RmnRemoteError:
		return "InvalidCodeVersion"
	default:
		return ""
	}
}
