[package]
name = "ping_pong_demo"
version = "0.1.0-dev"
description = "Created with Anchor"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "ping_pong_demo"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []

[dependencies]
solana-program = "1.17.25" # pin solana to 1.17
anchor-lang = { version = "0.29.0", features = [] }
anchor-spl = "0.29.0"
example_ccip_receiver = { path = "../example-ccip-receiver", features = ["no-entrypoint"]}
ccip_router = { path = "../ccip-router", features = ["cpi"] }

[build-dependencies]
build_commit = { path = "../../crates/build-commit" }
