////////////////////////////////////////////////////////////////////////////////
// Public modules, to be exposed via routers to lib.rs as program entrypoints //
////////////////////////////////////////////////////////////////////////////////
pub(super) mod admin; // to be invoked by admin only
pub(super) mod prices; // to be invoked by price updaters (e.g. offramp) only
pub(super) mod public; // to be invoked by users directly & onramp

//////////////////////////////////////////////////////////////////////////////////
// Private modules, just to be used within the instructions versioned submodule //
//////////////////////////////////////////////////////////////////////////////////
mod messages;
mod price_math;
mod safe_deserialize;
