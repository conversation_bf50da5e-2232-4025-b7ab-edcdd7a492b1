[package]
name = "lockrelease-token-pool"
version = "0.1.0-dev"
description = "Created with Anchor"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "lockrelease_token_pool"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []

[dependencies]
anchor-lang = { version = "0.29.0", features = ["init-if-needed"] }
anchor-spl = "0.29.0"
solana-program = "1.17.25" # pin solana to 1.17
spl-math = { version = "0.2.0", features = [ "no-entrypoint" ] }
base-token-pool = { version = "0.1.0-dev", path = "../base-token-pool/", features = ["no-entrypoint"] }
rmn_remote = {path = "../rmn-remote", features = ["cpi"]}
ccip_common = {path = "../ccip-common"}

[build-dependencies]
build_commit = { path = "../../crates/build-commit" }
