[package]
name = "ccip_router"
version = "0.1.0-dev"
description = "Created with Anchor"
edition = "2021"
build = "build.rs"

[lib]
crate-type = ["cdylib", "lib"]
name = "ccip_router"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []

[dependencies]
solana-program = "1.17.25" # pin solana to 1.17
anchor-lang = { version = "0.29.0", features = ["init-if-needed"] }
anchor-spl = "0.29.0"
bytemuck = "1.7"
ethnum = "1.5"
fee_quoter = {path = "../fee-quoter", features = ["cpi"]}
rmn_remote = {path = "../rmn-remote", features = ["cpi"]}
ccip_common = {path = "../ccip-common"}

[dev-dependencies]
hex = "0.4.3"

[build-dependencies]
build_commit = { path = "../../crates/build-commit" }
