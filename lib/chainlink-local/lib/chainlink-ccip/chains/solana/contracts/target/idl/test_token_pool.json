{"version": "0.1.0-dev", "name": "test_token_pool", "instructions": [{"name": "initialize", "accounts": [{"name": "state", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}, {"name": "programData", "isMut": false, "isSigner": false}], "args": [{"name": "poolType", "type": {"defined": "PoolType"}}, {"name": "router", "type": "public<PERSON>ey"}, {"name": "rmnRemote", "type": "public<PERSON>ey"}]}, {"name": "transferOwnership", "accounts": [{"name": "state", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}], "args": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}]}, {"name": "acceptOwnership", "accounts": [{"name": "state", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}], "args": []}, {"name": "setRouter", "accounts": [{"name": "state", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": false, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}], "args": [{"name": "newRouter", "type": "public<PERSON>ey"}]}, {"name": "initChainRemoteConfig", "accounts": [{"name": "state", "isMut": false, "isSigner": false}, {"name": "chainConfig", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "remoteChainSelector", "type": "u64"}, {"name": "mint", "type": "public<PERSON>ey"}, {"name": "cfg", "type": {"defined": "RemoteConfig"}}]}, {"name": "editChainRemoteConfig", "accounts": [{"name": "state", "isMut": false, "isSigner": false}, {"name": "chainConfig", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "remoteChainSelector", "type": "u64"}, {"name": "mint", "type": "public<PERSON>ey"}, {"name": "cfg", "type": {"defined": "RemoteConfig"}}]}, {"name": "appendRemotePoolAddresses", "accounts": [{"name": "state", "isMut": false, "isSigner": false}, {"name": "chainConfig", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "remoteChainSelector", "type": "u64"}, {"name": "mint", "type": "public<PERSON>ey"}, {"name": "addresses", "type": {"vec": {"defined": "Re<PERSON><PERSON><PERSON><PERSON>"}}}]}, {"name": "setChainRateLimit", "accounts": [{"name": "state", "isMut": false, "isSigner": false}, {"name": "chainConfig", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "remoteChainSelector", "type": "u64"}, {"name": "mint", "type": "public<PERSON>ey"}, {"name": "inbound", "type": {"defined": "RateLimitConfig"}}, {"name": "outbound", "type": {"defined": "RateLimitConfig"}}]}, {"name": "deleteChainConfig", "accounts": [{"name": "state", "isMut": false, "isSigner": false}, {"name": "chainConfig", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "remoteChainSelector", "type": "u64"}, {"name": "mint", "type": "public<PERSON>ey"}]}, {"name": "releaseOrMintTokens", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "offrampProgram", "isMut": false, "isSigner": false, "docs": ["CHECK offramp program: exists only to derive the allowed offramp PDA", "and the authority PDA."]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false, "docs": ["CHECK PDA of the router program verifying the signer is an allowed offramp.", "If PDA does not exist, the router doesn't allow this offramp"]}, {"name": "state", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "mint", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "poolTokenAccount", "isMut": true, "isSigner": false}, {"name": "chainConfig", "isMut": true, "isSigner": false}, {"name": "rmnRemote", "isMut": false, "isSigner": false}, {"name": "rmnRemoteCurses", "isMut": false, "isSigner": false}, {"name": "rmnRemoteConfig", "isMut": false, "isSigner": false}, {"name": "receiver<PERSON><PERSON><PERSON><PERSON>unt", "isMut": true, "isSigner": false}], "args": [{"name": "releaseOrMint", "type": {"defined": "ReleaseOrMintInV1"}}], "returns": {"defined": "ReleaseOrMintOutV1"}}, {"name": "lockOrBurnTokens", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "state", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "mint", "isMut": true, "isSigner": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false}, {"name": "poolTokenAccount", "isMut": true, "isSigner": false}, {"name": "rmnRemote", "isMut": false, "isSigner": false}, {"name": "rmnRemoteCurses", "isMut": false, "isSigner": false}, {"name": "rmnRemoteConfig", "isMut": false, "isSigner": false}, {"name": "chainConfig", "isMut": true, "isSigner": false}], "args": [{"name": "lockOrBurn", "type": {"defined": "LockOrBurnInV1"}}], "returns": {"defined": "LockOrBurnOutV1"}}], "accounts": [{"name": "State", "type": {"kind": "struct", "fields": [{"name": "poolType", "type": {"defined": "PoolType"}}, {"name": "config", "type": {"defined": "BaseConfig"}}]}}, {"name": "ChainConfig", "type": {"kind": "struct", "fields": [{"name": "base", "type": {"defined": "BaseChain"}}]}}], "types": [{"name": "PoolType", "type": {"kind": "enum", "variants": [{"name": "LockAndRelease"}, {"name": "BurnAndMint"}, {"name": "Wrapped"}]}}]}