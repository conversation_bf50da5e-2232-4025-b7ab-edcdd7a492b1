{"version": "0.1.0-dev", "name": "test_ccip_receiver", "docs": ["This program an example of a CCIP Receiver Program.", "Used to test CCIP Router execute."], "instructions": [{"name": "initialize", "docs": ["The initialization is responsibility of the External User, CCIP is not handling initialization of Accounts"], "accounts": [{"name": "counter", "isMut": true, "isSigner": false}, {"name": "externalExecutionConfig", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "router", "type": "public<PERSON>ey"}]}, {"name": "setRejectAll", "accounts": [{"name": "counter", "isMut": true, "isSigner": false}, {"name": "authority", "isMut": false, "isSigner": true}], "args": [{"name": "rejectAll", "type": "bool"}]}, {"name": "ccipReceive", "docs": ["This function is called by the CCIP Router to execute the CCIP message.", "The method name needs to be ccip_receive with Anchor encoding,", "if not using Anchor the discriminator needs to be [0x0b, 0xf4, 0x09, 0xf9, 0x2c, 0x53, 0x2f, 0xf5]", "You can send as many accounts as you need, specifying if mutable or not.", "But none of them could be an init, realloc or close.", "In this case, it increments the counter value by 1 and logs the parsed message."], "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "offrampProgram", "isMut": false, "isSigner": false, "docs": ["CHECK offramp program: exists only to derive the allowed offramp PDA", "and the authority PDA. Must be second."]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isMut": false, "isSigner": false, "docs": ["CHECK PDA of the router program verifying the signer is an allowed offramp.", "If PDA does not exist, the router doesn't allow this offramp"]}, {"name": "externalExecutionConfig", "isMut": true, "isSigner": false}, {"name": "counter", "isMut": true, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "message", "type": {"defined": "Any2SVMMessage"}}]}, {"name": "ccipTokenReleaseMint", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "poolTokenAccount", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "input", "type": {"defined": "ReleaseOrMintInV1"}}]}, {"name": "ccipTokenLockBurn", "accounts": [{"name": "authority", "isMut": false, "isSigner": true}, {"name": "poolTokenAccount", "isMut": true, "isSigner": false}, {"name": "mint", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}], "args": [{"name": "input", "type": {"defined": "LockOrBurnInV1"}}]}], "accounts": [{"name": "Counter", "type": {"kind": "struct", "fields": [{"name": "value", "type": "u64"}, {"name": "rejectAll", "type": "bool"}, {"name": "state", "type": {"defined": "BaseState"}}]}}, {"name": "ExternalExecutionConfig", "type": {"kind": "struct", "fields": []}}], "types": [{"name": "LockOrBurnInV1", "type": {"kind": "struct", "fields": [{"name": "receiver", "type": "bytes"}, {"name": "remoteChainSelector", "type": "u64"}, {"name": "originalSender", "type": "public<PERSON>ey"}, {"name": "amount", "type": "u64"}, {"name": "localToken", "type": "public<PERSON>ey"}]}}, {"name": "ReleaseOrMintInV1", "type": {"kind": "struct", "fields": [{"name": "originalSender", "type": "bytes"}, {"name": "remoteChainSelector", "type": "u64"}, {"name": "receiver", "type": "public<PERSON>ey"}, {"name": "amount", "type": {"array": ["u8", 32]}}, {"name": "localToken", "type": "public<PERSON>ey"}, {"name": "sourcePoolAddress", "docs": ["@dev WARNING: sourcePoolAddress should be checked prior to any processing of funds. Make sure it matches the", "expected pool address for the given remoteChainSelector."], "type": "bytes"}, {"name": "sourcePoolData", "type": "bytes"}, {"name": "offchainTokenData", "docs": ["@dev WARNING: offchainTokenData is untrusted data."], "type": "bytes"}]}}], "errors": [{"code": 6000, "name": "RejectAll", "msg": "Rejecting all messages"}]}