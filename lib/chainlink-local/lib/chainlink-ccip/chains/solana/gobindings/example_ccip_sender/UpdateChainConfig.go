// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package example_ccip_sender

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// UpdateChainConfig is the `updateChainConfig` instruction.
type UpdateChainConfig struct {
	ChainSelector  *uint64
	Recipient      *[]byte
	ExtraArgsBytes *[]byte

	// [0] = [WRITE] state
	//
	// [1] = [WRITE] chainConfig
	//
	// [2] = [WRITE, SIGNER] authority
	//
	// [3] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewUpdateChainConfigInstructionBuilder creates a new `UpdateChainConfig` instruction builder.
func NewUpdateChainConfigInstructionBuilder() *UpdateChainConfig {
	nd := &UpdateChainConfig{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 4),
	}
	return nd
}

// SetChainSelector sets the "chainSelector" parameter.
func (inst *UpdateChainConfig) SetChainSelector(chainSelector uint64) *UpdateChainConfig {
	inst.ChainSelector = &chainSelector
	return inst
}

// SetRecipient sets the "recipient" parameter.
func (inst *UpdateChainConfig) SetRecipient(recipient []byte) *UpdateChainConfig {
	inst.Recipient = &recipient
	return inst
}

// SetExtraArgsBytes sets the "extraArgsBytes" parameter.
func (inst *UpdateChainConfig) SetExtraArgsBytes(extraArgsBytes []byte) *UpdateChainConfig {
	inst.ExtraArgsBytes = &extraArgsBytes
	return inst
}

// SetStateAccount sets the "state" account.
func (inst *UpdateChainConfig) SetStateAccount(state ag_solanago.PublicKey) *UpdateChainConfig {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(state).WRITE()
	return inst
}

// GetStateAccount gets the "state" account.
func (inst *UpdateChainConfig) GetStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetChainConfigAccount sets the "chainConfig" account.
func (inst *UpdateChainConfig) SetChainConfigAccount(chainConfig ag_solanago.PublicKey) *UpdateChainConfig {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(chainConfig).WRITE()
	return inst
}

// GetChainConfigAccount gets the "chainConfig" account.
func (inst *UpdateChainConfig) GetChainConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *UpdateChainConfig) SetAuthorityAccount(authority ag_solanago.PublicKey) *UpdateChainConfig {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *UpdateChainConfig) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *UpdateChainConfig) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *UpdateChainConfig {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *UpdateChainConfig) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

func (inst UpdateChainConfig) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_UpdateChainConfig,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst UpdateChainConfig) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *UpdateChainConfig) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.ChainSelector == nil {
			return errors.New("ChainSelector parameter is not set")
		}
		if inst.Recipient == nil {
			return errors.New("Recipient parameter is not set")
		}
		if inst.ExtraArgsBytes == nil {
			return errors.New("ExtraArgsBytes parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.State is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.ChainConfig is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *UpdateChainConfig) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("UpdateChainConfig")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=3]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param(" ChainSelector", *inst.ChainSelector))
						paramsBranch.Child(ag_format.Param("     Recipient", *inst.Recipient))
						paramsBranch.Child(ag_format.Param("ExtraArgsBytes", *inst.ExtraArgsBytes))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=4]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("        state", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("  chainConfig", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("    authority", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("systemProgram", inst.AccountMetaSlice[3]))
					})
				})
		})
}

func (obj UpdateChainConfig) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `ChainSelector` param:
	err = encoder.Encode(obj.ChainSelector)
	if err != nil {
		return err
	}
	// Serialize `Recipient` param:
	err = encoder.Encode(obj.Recipient)
	if err != nil {
		return err
	}
	// Serialize `ExtraArgsBytes` param:
	err = encoder.Encode(obj.ExtraArgsBytes)
	if err != nil {
		return err
	}
	return nil
}
func (obj *UpdateChainConfig) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `ChainSelector`:
	err = decoder.Decode(&obj.ChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `Recipient`:
	err = decoder.Decode(&obj.Recipient)
	if err != nil {
		return err
	}
	// Deserialize `ExtraArgsBytes`:
	err = decoder.Decode(&obj.ExtraArgsBytes)
	if err != nil {
		return err
	}
	return nil
}

// NewUpdateChainConfigInstruction declares a new UpdateChainConfig instruction with the provided parameters and accounts.
func NewUpdateChainConfigInstruction(
	// Parameters:
	chainSelector uint64,
	recipient []byte,
	extraArgsBytes []byte,
	// Accounts:
	state ag_solanago.PublicKey,
	chainConfig ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *UpdateChainConfig {
	return NewUpdateChainConfigInstructionBuilder().
		SetChainSelector(chainSelector).
		SetRecipient(recipient).
		SetExtraArgsBytes(extraArgsBytes).
		SetStateAccount(state).
		SetChainConfigAccount(chainConfig).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
