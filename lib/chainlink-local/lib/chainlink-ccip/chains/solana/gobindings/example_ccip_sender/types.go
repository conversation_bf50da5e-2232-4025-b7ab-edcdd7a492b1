// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package example_ccip_sender

import ag_binary "github.com/gagliardetto/binary"

type SenderError ag_binary.BorshEnum

const (
	TransferTokenDuplicated_SenderError SenderError = iota
)

func (value SenderError) String() string {
	switch value {
	case TransferTokenDuplicated_SenderError:
		return "TransferTokenDuplicated"
	default:
		return ""
	}
}
