// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package fee_quoter

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Add a price updater address to the list of allowed price updaters.
// On price updates, the fee quoter will check the that caller is allowed.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for this operation.
// * `price_updater` - The price updater address.
type AddPriceUpdater struct {
	PriceUpdater *ag_solanago.PublicKey

	// [0] = [WRITE] allowedPriceUpdater
	//
	// [1] = [] config
	//
	// [2] = [WRITE, SIGNER] authority
	//
	// [3] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewAddPriceUpdaterInstructionBuilder creates a new `AddPriceUpdater` instruction builder.
func NewAddPriceUpdaterInstructionBuilder() *AddPriceUpdater {
	nd := &AddPriceUpdater{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 4),
	}
	return nd
}

// SetPriceUpdater sets the "priceUpdater" parameter.
func (inst *AddPriceUpdater) SetPriceUpdater(priceUpdater ag_solanago.PublicKey) *AddPriceUpdater {
	inst.PriceUpdater = &priceUpdater
	return inst
}

// SetAllowedPriceUpdaterAccount sets the "allowedPriceUpdater" account.
func (inst *AddPriceUpdater) SetAllowedPriceUpdaterAccount(allowedPriceUpdater ag_solanago.PublicKey) *AddPriceUpdater {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(allowedPriceUpdater).WRITE()
	return inst
}

// GetAllowedPriceUpdaterAccount gets the "allowedPriceUpdater" account.
func (inst *AddPriceUpdater) GetAllowedPriceUpdaterAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigAccount sets the "config" account.
func (inst *AddPriceUpdater) SetConfigAccount(config ag_solanago.PublicKey) *AddPriceUpdater {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *AddPriceUpdater) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *AddPriceUpdater) SetAuthorityAccount(authority ag_solanago.PublicKey) *AddPriceUpdater {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *AddPriceUpdater) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *AddPriceUpdater) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *AddPriceUpdater {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *AddPriceUpdater) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

func (inst AddPriceUpdater) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_AddPriceUpdater,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst AddPriceUpdater) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *AddPriceUpdater) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.PriceUpdater == nil {
			return errors.New("PriceUpdater parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.AllowedPriceUpdater is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *AddPriceUpdater) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("AddPriceUpdater")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("PriceUpdater", *inst.PriceUpdater))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=4]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("allowedPriceUpdater", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("             config", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("          authority", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("      systemProgram", inst.AccountMetaSlice[3]))
					})
				})
		})
}

func (obj AddPriceUpdater) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `PriceUpdater` param:
	err = encoder.Encode(obj.PriceUpdater)
	if err != nil {
		return err
	}
	return nil
}
func (obj *AddPriceUpdater) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `PriceUpdater`:
	err = decoder.Decode(&obj.PriceUpdater)
	if err != nil {
		return err
	}
	return nil
}

// NewAddPriceUpdaterInstruction declares a new AddPriceUpdater instruction with the provided parameters and accounts.
func NewAddPriceUpdaterInstruction(
	// Parameters:
	priceUpdater ag_solanago.PublicKey,
	// Accounts:
	allowedPriceUpdater ag_solanago.PublicKey,
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *AddPriceUpdater {
	return NewAddPriceUpdaterInstructionBuilder().
		SetPriceUpdater(priceUpdater).
		SetAllowedPriceUpdaterAccount(allowedPriceUpdater).
		SetConfigAccount(config).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
