// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package fee_quoter

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Disables the destination chain selector.
//
// The Admin is the only one able to disable the chain selector as destination. This method is thought of as an emergency kill-switch.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for disabling the chain selector.
// * `chain_selector` - The destination chain selector to be disabled.
type DisableDestChain struct {
	ChainSelector *uint64

	// [0] = [] config
	//
	// [1] = [WRITE] destChain
	//
	// [2] = [WRITE, SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewDisableDestChainInstructionBuilder creates a new `DisableDestChain` instruction builder.
func NewDisableDestChainInstructionBuilder() *DisableDestChain {
	nd := &DisableDestChain{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetChainSelector sets the "chainSelector" parameter.
func (inst *DisableDestChain) SetChainSelector(chainSelector uint64) *DisableDestChain {
	inst.ChainSelector = &chainSelector
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *DisableDestChain) SetConfigAccount(config ag_solanago.PublicKey) *DisableDestChain {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *DisableDestChain) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetDestChainAccount sets the "destChain" account.
func (inst *DisableDestChain) SetDestChainAccount(destChain ag_solanago.PublicKey) *DisableDestChain {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(destChain).WRITE()
	return inst
}

// GetDestChainAccount gets the "destChain" account.
func (inst *DisableDestChain) GetDestChainAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *DisableDestChain) SetAuthorityAccount(authority ag_solanago.PublicKey) *DisableDestChain {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *DisableDestChain) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst DisableDestChain) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_DisableDestChain,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst DisableDestChain) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *DisableDestChain) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.ChainSelector == nil {
			return errors.New("ChainSelector parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.DestChain is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *DisableDestChain) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("DisableDestChain")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("ChainSelector", *inst.ChainSelector))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("   config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("destChain", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("authority", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj DisableDestChain) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `ChainSelector` param:
	err = encoder.Encode(obj.ChainSelector)
	if err != nil {
		return err
	}
	return nil
}
func (obj *DisableDestChain) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `ChainSelector`:
	err = decoder.Decode(&obj.ChainSelector)
	if err != nil {
		return err
	}
	return nil
}

// NewDisableDestChainInstruction declares a new DisableDestChain instruction with the provided parameters and accounts.
func NewDisableDestChainInstruction(
	// Parameters:
	chainSelector uint64,
	// Accounts:
	config ag_solanago.PublicKey,
	destChain ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *DisableDestChain {
	return NewDisableDestChainInstructionBuilder().
		SetChainSelector(chainSelector).
		SetConfigAccount(config).
		SetDestChainAccount(destChain).
		SetAuthorityAccount(authority)
}
