// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package fee_quoter

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Updates the billing token configuration.
// Only CCIP Admin can update a billing token configuration.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for updating the billing token configuration.
// * `config` - The new billing token configuration.
type UpdateBillingTokenConfig struct {
	Config *BillingTokenConfig

	// [0] = [] config
	//
	// [1] = [WRITE] billingTokenConfig
	//
	// [2] = [SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewUpdateBillingTokenConfigInstructionBuilder creates a new `UpdateBillingTokenConfig` instruction builder.
func NewUpdateBillingTokenConfigInstructionBuilder() *UpdateBillingTokenConfig {
	nd := &UpdateBillingTokenConfig{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetConfig sets the "config" parameter.
func (inst *UpdateBillingTokenConfig) SetConfig(config BillingTokenConfig) *UpdateBillingTokenConfig {
	inst.Config = &config
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *UpdateBillingTokenConfig) SetConfigAccount(config ag_solanago.PublicKey) *UpdateBillingTokenConfig {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *UpdateBillingTokenConfig) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetBillingTokenConfigAccount sets the "billingTokenConfig" account.
func (inst *UpdateBillingTokenConfig) SetBillingTokenConfigAccount(billingTokenConfig ag_solanago.PublicKey) *UpdateBillingTokenConfig {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(billingTokenConfig).WRITE()
	return inst
}

// GetBillingTokenConfigAccount gets the "billingTokenConfig" account.
func (inst *UpdateBillingTokenConfig) GetBillingTokenConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *UpdateBillingTokenConfig) SetAuthorityAccount(authority ag_solanago.PublicKey) *UpdateBillingTokenConfig {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *UpdateBillingTokenConfig) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst UpdateBillingTokenConfig) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_UpdateBillingTokenConfig,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst UpdateBillingTokenConfig) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *UpdateBillingTokenConfig) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.Config == nil {
			return errors.New("Config parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.BillingTokenConfig is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *UpdateBillingTokenConfig) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("UpdateBillingTokenConfig")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("Config", *inst.Config))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("            config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("billingTokenConfig", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("         authority", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj UpdateBillingTokenConfig) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Config` param:
	err = encoder.Encode(obj.Config)
	if err != nil {
		return err
	}
	return nil
}
func (obj *UpdateBillingTokenConfig) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Config`:
	err = decoder.Decode(&obj.Config)
	if err != nil {
		return err
	}
	return nil
}

// NewUpdateBillingTokenConfigInstruction declares a new UpdateBillingTokenConfig instruction with the provided parameters and accounts.
func NewUpdateBillingTokenConfigInstruction(
	// Parameters:
	config BillingTokenConfig,
	// Accounts:
	configAccount ag_solanago.PublicKey,
	billingTokenConfig ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *UpdateBillingTokenConfig {
	return NewUpdateBillingTokenConfigInstructionBuilder().
		SetConfig(config).
		SetConfigAccount(configAccount).
		SetBillingTokenConfigAccount(billingTokenConfig).
		SetAuthorityAccount(authority)
}
