// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package fee_quoter

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Sets the link_token_mint and updates the link_token_local_decimals.
//
// Only the admin may set this.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for updating the configuration.
type SetLinkTokenMint struct {

	// [0] = [WRITE] config
	//
	// [1] = [] linkTokenMint
	//
	// [2] = [SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewSetLinkTokenMintInstructionBuilder creates a new `SetLinkTokenMint` instruction builder.
func NewSetLinkTokenMintInstructionBuilder() *SetLinkTokenMint {
	nd := &SetLinkTokenMint{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetConfigAccount sets the "config" account.
func (inst *SetLinkTokenMint) SetConfigAccount(config ag_solanago.PublicKey) *SetLinkTokenMint {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config).WRITE()
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *SetLinkTokenMint) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetLinkTokenMintAccount sets the "linkTokenMint" account.
func (inst *SetLinkTokenMint) SetLinkTokenMintAccount(linkTokenMint ag_solanago.PublicKey) *SetLinkTokenMint {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(linkTokenMint)
	return inst
}

// GetLinkTokenMintAccount gets the "linkTokenMint" account.
func (inst *SetLinkTokenMint) GetLinkTokenMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *SetLinkTokenMint) SetAuthorityAccount(authority ag_solanago.PublicKey) *SetLinkTokenMint {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *SetLinkTokenMint) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst SetLinkTokenMint) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_SetLinkTokenMint,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst SetLinkTokenMint) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *SetLinkTokenMint) Validate() error {
	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.LinkTokenMint is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *SetLinkTokenMint) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("SetLinkTokenMint")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=0]").ParentFunc(func(paramsBranch ag_treeout.Branches) {})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("       config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("linkTokenMint", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("    authority", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj SetLinkTokenMint) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	return nil
}
func (obj *SetLinkTokenMint) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	return nil
}

// NewSetLinkTokenMintInstruction declares a new SetLinkTokenMint instruction with the provided parameters and accounts.
func NewSetLinkTokenMintInstruction(
	// Accounts:
	config ag_solanago.PublicKey,
	linkTokenMint ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *SetLinkTokenMint {
	return NewSetLinkTokenMintInstructionBuilder().
		SetConfigAccount(config).
		SetLinkTokenMintAccount(linkTokenMint).
		SetAuthorityAccount(authority)
}
