// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package burnmint_token_pool

import (
	"fmt"
	ag_binary "github.com/gagliardetto/binary"
)

type State struct {
	Version uint8
	Config  BaseConfig
}

var StateDiscriminator = [8]byte{216, 146, 107, 94, 104, 75, 182, 177}

func (obj State) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(StateDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `Config` param:
	err = encoder.Encode(obj.Config)
	if err != nil {
		return err
	}
	return nil
}

func (obj *State) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(StateDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[216 146 107 94 104 75 182 177]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `Config`:
	err = decoder.Decode(&obj.Config)
	if err != nil {
		return err
	}
	return nil
}

type ChainConfig struct {
	Base BaseChain
}

var ChainConfigDiscriminator = [8]byte{13, 177, 233, 141, 212, 29, 148, 56}

func (obj ChainConfig) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(ChainConfigDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Base` param:
	err = encoder.Encode(obj.Base)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ChainConfig) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(ChainConfigDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[13 177 233 141 212 29 148 56]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Base`:
	err = decoder.Decode(&obj.Base)
	if err != nil {
		return err
	}
	return nil
}
