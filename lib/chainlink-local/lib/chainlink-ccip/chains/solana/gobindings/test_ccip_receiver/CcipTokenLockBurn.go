// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package test_ccip_receiver

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// CcipTokenLockBurn is the `ccipTokenLockBurn` instruction.
type CcipTokenLockBurn struct {
	Input *LockOrBurnInV1

	// [0] = [SIGNER] authority
	//
	// [1] = [WRITE] poolTokenAccount
	//
	// [2] = [] mint
	//
	// [3] = [] tokenProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewCcipTokenLockBurnInstructionBuilder creates a new `CcipTokenLockBurn` instruction builder.
func NewCcipTokenLockBurnInstructionBuilder() *CcipTokenLockBurn {
	nd := &CcipTokenLockBurn{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 4),
	}
	return nd
}

// SetInput sets the "input" parameter.
func (inst *CcipTokenLockBurn) SetInput(input LockOrBurnInV1) *CcipTokenLockBurn {
	inst.Input = &input
	return inst
}

// SetAuthorityAccount sets the "authority" account.
func (inst *CcipTokenLockBurn) SetAuthorityAccount(authority ag_solanago.PublicKey) *CcipTokenLockBurn {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *CcipTokenLockBurn) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetPoolTokenAccountAccount sets the "poolTokenAccount" account.
func (inst *CcipTokenLockBurn) SetPoolTokenAccountAccount(poolTokenAccount ag_solanago.PublicKey) *CcipTokenLockBurn {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(poolTokenAccount).WRITE()
	return inst
}

// GetPoolTokenAccountAccount gets the "poolTokenAccount" account.
func (inst *CcipTokenLockBurn) GetPoolTokenAccountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetMintAccount sets the "mint" account.
func (inst *CcipTokenLockBurn) SetMintAccount(mint ag_solanago.PublicKey) *CcipTokenLockBurn {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(mint)
	return inst
}

// GetMintAccount gets the "mint" account.
func (inst *CcipTokenLockBurn) GetMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetTokenProgramAccount sets the "tokenProgram" account.
func (inst *CcipTokenLockBurn) SetTokenProgramAccount(tokenProgram ag_solanago.PublicKey) *CcipTokenLockBurn {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(tokenProgram)
	return inst
}

// GetTokenProgramAccount gets the "tokenProgram" account.
func (inst *CcipTokenLockBurn) GetTokenProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

func (inst CcipTokenLockBurn) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_CcipTokenLockBurn,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst CcipTokenLockBurn) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *CcipTokenLockBurn) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.Input == nil {
			return errors.New("Input parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.PoolTokenAccount is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Mint is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.TokenProgram is not set")
		}
	}
	return nil
}

func (inst *CcipTokenLockBurn) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("CcipTokenLockBurn")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("Input", *inst.Input))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=4]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("   authority", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("   poolToken", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("        mint", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("tokenProgram", inst.AccountMetaSlice[3]))
					})
				})
		})
}

func (obj CcipTokenLockBurn) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Input` param:
	err = encoder.Encode(obj.Input)
	if err != nil {
		return err
	}
	return nil
}
func (obj *CcipTokenLockBurn) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Input`:
	err = decoder.Decode(&obj.Input)
	if err != nil {
		return err
	}
	return nil
}

// NewCcipTokenLockBurnInstruction declares a new CcipTokenLockBurn instruction with the provided parameters and accounts.
func NewCcipTokenLockBurnInstruction(
	// Parameters:
	input LockOrBurnInV1,
	// Accounts:
	authority ag_solanago.PublicKey,
	poolTokenAccount ag_solanago.PublicKey,
	mint ag_solanago.PublicKey,
	tokenProgram ag_solanago.PublicKey) *CcipTokenLockBurn {
	return NewCcipTokenLockBurnInstructionBuilder().
		SetInput(input).
		SetAuthorityAccount(authority).
		SetPoolTokenAccountAccount(poolTokenAccount).
		SetMintAccount(mint).
		SetTokenProgramAccount(tokenProgram)
}
