// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package rmn_remote

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Verifies that the subject is not cursed AND that this chain is not globally cursed.
// In case either of those assumptions fail, the instruction reverts.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required to inspect curses.
// * `subject` - The subject to verify. Note that this instruction will revert if the chain
// is globally cursed too, even if the provided subject is not explicitly cursed.
type VerifyNotCursed struct {
	Subject *CurseSubject

	// [0] = [] curses
	//
	// [1] = [] config
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewVerifyNotCursedInstructionBuilder creates a new `VerifyNotCursed` instruction builder.
func NewVerifyNotCursedInstructionBuilder() *VerifyNotCursed {
	nd := &VerifyNotCursed{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 2),
	}
	return nd
}

// SetSubject sets the "subject" parameter.
func (inst *VerifyNotCursed) SetSubject(subject CurseSubject) *VerifyNotCursed {
	inst.Subject = &subject
	return inst
}

// SetCursesAccount sets the "curses" account.
func (inst *VerifyNotCursed) SetCursesAccount(curses ag_solanago.PublicKey) *VerifyNotCursed {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(curses)
	return inst
}

// GetCursesAccount gets the "curses" account.
func (inst *VerifyNotCursed) GetCursesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigAccount sets the "config" account.
func (inst *VerifyNotCursed) SetConfigAccount(config ag_solanago.PublicKey) *VerifyNotCursed {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *VerifyNotCursed) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

func (inst VerifyNotCursed) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_VerifyNotCursed,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst VerifyNotCursed) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *VerifyNotCursed) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.Subject == nil {
			return errors.New("Subject parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Curses is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Config is not set")
		}
	}
	return nil
}

func (inst *VerifyNotCursed) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("VerifyNotCursed")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("Subject", *inst.Subject))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=2]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("curses", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("config", inst.AccountMetaSlice[1]))
					})
				})
		})
}

func (obj VerifyNotCursed) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Subject` param:
	err = encoder.Encode(obj.Subject)
	if err != nil {
		return err
	}
	return nil
}
func (obj *VerifyNotCursed) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Subject`:
	err = decoder.Decode(&obj.Subject)
	if err != nil {
		return err
	}
	return nil
}

// NewVerifyNotCursedInstruction declares a new VerifyNotCursed instruction with the provided parameters and accounts.
func NewVerifyNotCursedInstruction(
	// Parameters:
	subject CurseSubject,
	// Accounts:
	curses ag_solanago.PublicKey,
	config ag_solanago.PublicKey) *VerifyNotCursed {
	return NewVerifyNotCursedInstructionBuilder().
		SetSubject(subject).
		SetCursesAccount(curses).
		SetConfigAccount(config)
}
