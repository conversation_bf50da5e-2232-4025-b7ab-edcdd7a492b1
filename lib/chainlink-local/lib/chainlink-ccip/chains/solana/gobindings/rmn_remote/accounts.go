// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package rmn_remote

import (
	"fmt"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
)

type Config struct {
	Version            uint8
	Owner              ag_solanago.PublicKey
	ProposedOwner      ag_solanago.PublicKey
	DefaultCodeVersion CodeVersion
}

var ConfigDiscriminator = [8]byte{155, 12, 170, 224, 30, 250, 204, 130}

func (obj Config) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(ConfigDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `Owner` param:
	err = encoder.Encode(obj.Owner)
	if err != nil {
		return err
	}
	// Serialize `ProposedOwner` param:
	err = encoder.Encode(obj.ProposedOwner)
	if err != nil {
		return err
	}
	// Serialize `DefaultCodeVersion` param:
	err = encoder.Encode(obj.DefaultCodeVersion)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(ConfigDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[155 12 170 224 30 250 204 130]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `Owner`:
	err = decoder.Decode(&obj.Owner)
	if err != nil {
		return err
	}
	// Deserialize `ProposedOwner`:
	err = decoder.Decode(&obj.ProposedOwner)
	if err != nil {
		return err
	}
	// Deserialize `DefaultCodeVersion`:
	err = decoder.Decode(&obj.DefaultCodeVersion)
	if err != nil {
		return err
	}
	return nil
}

type Curses struct {
	Version        uint8
	CursedSubjects []CurseSubject
}

var CursesDiscriminator = [8]byte{129, 28, 49, 58, 74, 237, 146, 202}

func (obj Curses) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(CursesDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `CursedSubjects` param:
	err = encoder.Encode(obj.CursedSubjects)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Curses) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(CursesDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[129 28 49 58 74 237 146 202]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `CursedSubjects`:
	err = decoder.Decode(&obj.CursedSubjects)
	if err != nil {
		return err
	}
	return nil
}
