// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_common

import (
	"fmt"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
)

type TokenAdminRegistry struct {
	Version              uint8
	Administrator        ag_solanago.PublicKey
	PendingAdministrator ag_solanago.PublicKey
	LookupTable          ag_solanago.PublicKey
	WritableIndexes      [2]ag_binary.Uint128
	Mint                 ag_solanago.PublicKey
}

var TokenAdminRegistryDiscriminator = [8]byte{70, 92, 207, 200, 76, 17, 57, 114}

func (obj TokenAdminRegistry) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(TokenAdminRegistryDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `Administrator` param:
	err = encoder.Encode(obj.Administrator)
	if err != nil {
		return err
	}
	// Serialize `PendingAdministrator` param:
	err = encoder.Encode(obj.PendingAdministrator)
	if err != nil {
		return err
	}
	// Serialize `LookupTable` param:
	err = encoder.Encode(obj.LookupTable)
	if err != nil {
		return err
	}
	// Serialize `WritableIndexes` param:
	err = encoder.Encode(obj.WritableIndexes)
	if err != nil {
		return err
	}
	// Serialize `Mint` param:
	err = encoder.Encode(obj.Mint)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TokenAdminRegistry) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(TokenAdminRegistryDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[70 92 207 200 76 17 57 114]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `Administrator`:
	err = decoder.Decode(&obj.Administrator)
	if err != nil {
		return err
	}
	// Deserialize `PendingAdministrator`:
	err = decoder.Decode(&obj.PendingAdministrator)
	if err != nil {
		return err
	}
	// Deserialize `LookupTable`:
	err = decoder.Decode(&obj.LookupTable)
	if err != nil {
		return err
	}
	// Deserialize `WritableIndexes`:
	err = decoder.Decode(&obj.WritableIndexes)
	if err != nil {
		return err
	}
	// Deserialize `Mint`:
	err = decoder.Decode(&obj.Mint)
	if err != nil {
		return err
	}
	return nil
}
