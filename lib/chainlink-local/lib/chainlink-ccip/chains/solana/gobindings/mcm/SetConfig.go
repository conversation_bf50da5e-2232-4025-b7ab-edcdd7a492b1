// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package mcm

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Set up the configuration for the multisig instance.
//
// Validates and establishes the signer hierarchy, group structure, and quorum requirements.
// If `clear_root` is true, it also invalidates the current Merkle root.
//
// # Parameters
//
// - `ctx`: The context containing the multisig configuration account.
// - `multisig_id`: The unique identifier for this multisig instance.
// - `signer_groups`: Vector assigning each signer to a specific group (must match signers length).
// - `group_quorums`: Array defining the required signatures for each group. A group with quorum=0 is disabled.
// - `group_parents`: Array defining the hierarchical relationship between groups, forming a tree structure.
// - `clear_root`: If true, invalidates the current root to prevent further operations from being executed.
//
// # Example
//
// A group structure like this:
//
// ```text
// ┌──────┐
// ┌─►│2-of-3│◄───────┐
// │  └──────┘        │
// │        ▲         │
// │        │         │
// ┌──┴───┐ ┌──┴───┐ ┌───┴────┐
// ┌──►│1-of-2│ │2-of-2│ │signer A│
// │   └──────┘ └──────┘ └────────┘
// │       ▲      ▲  ▲
// │       │      │  │
// ┌───────┴┐ ┌────┴───┐ ┌┴───────┐
// │signer B│ │signer C│ │signer D│
// └────────┘ └────────┘ └────────┘
// ```
//
// Would be configured with:
// - group_quorums = [2, 1, 2, ...] (root: 2-of-3, group1: 1-of-2, group2: 2-of-2)
// - group_parents = [0, 0, 0, ...] (all groups under root)
type SetConfig struct {
	MultisigId   *[32]uint8
	SignerGroups *[]byte
	GroupQuorums *[32]uint8
	GroupParents *[32]uint8
	ClearRoot    *bool

	// [0] = [WRITE] multisigConfig
	//
	// [1] = [WRITE] configSigners
	//
	// [2] = [WRITE] rootMetadata
	//
	// [3] = [WRITE] expiringRootAndOpCount
	//
	// [4] = [WRITE, SIGNER] authority
	//
	// [5] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewSetConfigInstructionBuilder creates a new `SetConfig` instruction builder.
func NewSetConfigInstructionBuilder() *SetConfig {
	nd := &SetConfig{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 6),
	}
	return nd
}

// SetMultisigId sets the "multisigId" parameter.
func (inst *SetConfig) SetMultisigId(multisigId [32]uint8) *SetConfig {
	inst.MultisigId = &multisigId
	return inst
}

// SetSignerGroups sets the "signerGroups" parameter.
func (inst *SetConfig) SetSignerGroups(signerGroups []byte) *SetConfig {
	inst.SignerGroups = &signerGroups
	return inst
}

// SetGroupQuorums sets the "groupQuorums" parameter.
func (inst *SetConfig) SetGroupQuorums(groupQuorums [32]uint8) *SetConfig {
	inst.GroupQuorums = &groupQuorums
	return inst
}

// SetGroupParents sets the "groupParents" parameter.
func (inst *SetConfig) SetGroupParents(groupParents [32]uint8) *SetConfig {
	inst.GroupParents = &groupParents
	return inst
}

// SetClearRoot sets the "clearRoot" parameter.
func (inst *SetConfig) SetClearRoot(clearRoot bool) *SetConfig {
	inst.ClearRoot = &clearRoot
	return inst
}

// SetMultisigConfigAccount sets the "multisigConfig" account.
func (inst *SetConfig) SetMultisigConfigAccount(multisigConfig ag_solanago.PublicKey) *SetConfig {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(multisigConfig).WRITE()
	return inst
}

// GetMultisigConfigAccount gets the "multisigConfig" account.
func (inst *SetConfig) GetMultisigConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigSignersAccount sets the "configSigners" account.
func (inst *SetConfig) SetConfigSignersAccount(configSigners ag_solanago.PublicKey) *SetConfig {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(configSigners).WRITE()
	return inst
}

// GetConfigSignersAccount gets the "configSigners" account.
func (inst *SetConfig) GetConfigSignersAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetRootMetadataAccount sets the "rootMetadata" account.
func (inst *SetConfig) SetRootMetadataAccount(rootMetadata ag_solanago.PublicKey) *SetConfig {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(rootMetadata).WRITE()
	return inst
}

// GetRootMetadataAccount gets the "rootMetadata" account.
func (inst *SetConfig) GetRootMetadataAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetExpiringRootAndOpCountAccount sets the "expiringRootAndOpCount" account.
func (inst *SetConfig) SetExpiringRootAndOpCountAccount(expiringRootAndOpCount ag_solanago.PublicKey) *SetConfig {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(expiringRootAndOpCount).WRITE()
	return inst
}

// GetExpiringRootAndOpCountAccount gets the "expiringRootAndOpCount" account.
func (inst *SetConfig) GetExpiringRootAndOpCountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *SetConfig) SetAuthorityAccount(authority ag_solanago.PublicKey) *SetConfig {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *SetConfig) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *SetConfig) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *SetConfig {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *SetConfig) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

func (inst SetConfig) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_SetConfig,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst SetConfig) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *SetConfig) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.MultisigId == nil {
			return errors.New("MultisigId parameter is not set")
		}
		if inst.SignerGroups == nil {
			return errors.New("SignerGroups parameter is not set")
		}
		if inst.GroupQuorums == nil {
			return errors.New("GroupQuorums parameter is not set")
		}
		if inst.GroupParents == nil {
			return errors.New("GroupParents parameter is not set")
		}
		if inst.ClearRoot == nil {
			return errors.New("ClearRoot parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.MultisigConfig is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.ConfigSigners is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.RootMetadata is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.ExpiringRootAndOpCount is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *SetConfig) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("SetConfig")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=5]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("  MultisigId", *inst.MultisigId))
						paramsBranch.Child(ag_format.Param("SignerGroups", *inst.SignerGroups))
						paramsBranch.Child(ag_format.Param("GroupQuorums", *inst.GroupQuorums))
						paramsBranch.Child(ag_format.Param("GroupParents", *inst.GroupParents))
						paramsBranch.Child(ag_format.Param("   ClearRoot", *inst.ClearRoot))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=6]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("        multisigConfig", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("         configSigners", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("          rootMetadata", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("expiringRootAndOpCount", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("             authority", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("         systemProgram", inst.AccountMetaSlice[5]))
					})
				})
		})
}

func (obj SetConfig) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `MultisigId` param:
	err = encoder.Encode(obj.MultisigId)
	if err != nil {
		return err
	}
	// Serialize `SignerGroups` param:
	err = encoder.Encode(obj.SignerGroups)
	if err != nil {
		return err
	}
	// Serialize `GroupQuorums` param:
	err = encoder.Encode(obj.GroupQuorums)
	if err != nil {
		return err
	}
	// Serialize `GroupParents` param:
	err = encoder.Encode(obj.GroupParents)
	if err != nil {
		return err
	}
	// Serialize `ClearRoot` param:
	err = encoder.Encode(obj.ClearRoot)
	if err != nil {
		return err
	}
	return nil
}
func (obj *SetConfig) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `MultisigId`:
	err = decoder.Decode(&obj.MultisigId)
	if err != nil {
		return err
	}
	// Deserialize `SignerGroups`:
	err = decoder.Decode(&obj.SignerGroups)
	if err != nil {
		return err
	}
	// Deserialize `GroupQuorums`:
	err = decoder.Decode(&obj.GroupQuorums)
	if err != nil {
		return err
	}
	// Deserialize `GroupParents`:
	err = decoder.Decode(&obj.GroupParents)
	if err != nil {
		return err
	}
	// Deserialize `ClearRoot`:
	err = decoder.Decode(&obj.ClearRoot)
	if err != nil {
		return err
	}
	return nil
}

// NewSetConfigInstruction declares a new SetConfig instruction with the provided parameters and accounts.
func NewSetConfigInstruction(
	// Parameters:
	multisigId [32]uint8,
	signerGroups []byte,
	groupQuorums [32]uint8,
	groupParents [32]uint8,
	clearRoot bool,
	// Accounts:
	multisigConfig ag_solanago.PublicKey,
	configSigners ag_solanago.PublicKey,
	rootMetadata ag_solanago.PublicKey,
	expiringRootAndOpCount ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *SetConfig {
	return NewSetConfigInstructionBuilder().
		SetMultisigId(multisigId).
		SetSignerGroups(signerGroups).
		SetGroupQuorums(groupQuorums).
		SetGroupParents(groupParents).
		SetClearRoot(clearRoot).
		SetMultisigConfigAccount(multisigConfig).
		SetConfigSignersAccount(configSigners).
		SetRootMetadataAccount(rootMetadata).
		SetExpiringRootAndOpCountAccount(expiringRootAndOpCount).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
