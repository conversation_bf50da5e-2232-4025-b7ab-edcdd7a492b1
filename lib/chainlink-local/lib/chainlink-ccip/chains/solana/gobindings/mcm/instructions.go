// A multi-signature contract system that supports signing many transactions targeting
// multiple chains with a single set of signatures. This program manages multiple multisig
// configurations through Program Derived Accounts (PDAs), each identified by a unique
// multisig_id.
//
// Key Features:
// - Multiple Configurations: A single deployed program instance can manage
// multiple independent multisig configurations
// - Hierarchical Signature Groups: Supports complex approval structures with
// nested groups and customizable quorum requirements
// - Merkle Tree Operations: Batches signed operations in Merkle trees for
// efficient verification and execution
//
// Usage Flow:
// 1. Initialize multisig configuration with a unique ID
// 2. Set up signer hierarchy and group structure
// 3. Set a Merkle root with authenticated metadata and signatures
// 4. Execute operations by providing Merkle proofs
// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package mcm

import (
	"bytes"
	"fmt"
	ag_spew "github.com/davecgh/go-spew/spew"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_text "github.com/gagliardetto/solana-go/text"
	ag_treeout "github.com/gagliardetto/treeout"
)

var ProgramID ag_solanago.PublicKey

func SetProgramID(pubkey ag_solanago.PublicKey) {
	ProgramID = pubkey
	ag_solanago.RegisterInstructionDecoder(ProgramID, registryDecodeInstruction)
}

const ProgramName = "Mcm"

func init() {
	if !ProgramID.IsZero() {
		ag_solanago.RegisterInstructionDecoder(ProgramID, registryDecodeInstruction)
	}
}

var (
	// Initialize a new multisig configuration.
	//
	// Creates the foundation for a new multisig instance by initializing the core configuration
	// PDAs and registering the multisig_id and chain_id. This is the first step in setting up
	// a new multisig configuration.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the accounts required for initialization:
	// - `multisig_config`: PDA that will store the core configuration
	// - `root_metadata`: PDA that will store the current root's metadata
	// - `expiring_root_and_op_count`: PDA that tracks the current root and operation count
	// - `authority`: The deployer who becomes the initial owner
	// - `program_data`: Used to validate that the caller is the program's upgrade authority
	// - `chain_id`: Network identifier for the chain this configuration is targeting
	// - `multisig_id`: A unique, 32-byte identifier (left-padded) for this multisig instance
	//
	// # Access Control
	//
	// This instruction can only be called by the program's upgrade authority (typically the deployer).
	//
	// # Note
	//
	// After initialization, the owner can transfer ownership through the two-step
	// transfer_ownership/accept_ownership process.
	Instruction_Initialize = ag_binary.TypeID([8]byte{175, 175, 109, 31, 13, 152, 155, 237})

	// Propose a new owner for the multisig instance config.
	//
	// Only the current owner (admin) can propose a new owner.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the configuration account.
	// - `_multisig_id`: The multisig identifier.
	// - `proposed_owner`: The public key of the proposed new owner.
	Instruction_TransferOwnership = ag_binary.TypeID([8]byte{65, 177, 215, 73, 53, 45, 99, 47})

	// Accept ownership of the multisig config.
	//
	// The proposed new owner must call this function to assume ownership.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the configuration account.
	// - `_multisig_id`: The multisig identifier.
	Instruction_AcceptOwnership = ag_binary.TypeID([8]byte{172, 23, 43, 13, 238, 213, 85, 150})

	// Set up the configuration for the multisig instance.
	//
	// Validates and establishes the signer hierarchy, group structure, and quorum requirements.
	// If `clear_root` is true, it also invalidates the current Merkle root.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the multisig configuration account.
	// - `multisig_id`: The unique identifier for this multisig instance.
	// - `signer_groups`: Vector assigning each signer to a specific group (must match signers length).
	// - `group_quorums`: Array defining the required signatures for each group. A group with quorum=0 is disabled.
	// - `group_parents`: Array defining the hierarchical relationship between groups, forming a tree structure.
	// - `clear_root`: If true, invalidates the current root to prevent further operations from being executed.
	//
	// # Example
	//
	// A group structure like this:
	//
	// ```text
	// ┌──────┐
	// ┌─►│2-of-3│◄───────┐
	// │  └──────┘        │
	// │        ▲         │
	// │        │         │
	// ┌──┴───┐ ┌──┴───┐ ┌───┴────┐
	// ┌──►│1-of-2│ │2-of-2│ │signer A│
	// │   └──────┘ └──────┘ └────────┘
	// │       ▲      ▲  ▲
	// │       │      │  │
	// ┌───────┴┐ ┌────┴───┐ ┌┴───────┐
	// │signer B│ │signer C│ │signer D│
	// └────────┘ └────────┘ └────────┘
	// ```
	//
	// Would be configured with:
	// - group_quorums = [2, 1, 2, ...] (root: 2-of-3, group1: 1-of-2, group2: 2-of-2)
	// - group_parents = [0, 0, 0, ...] (all groups under root)
	Instruction_SetConfig = ag_binary.TypeID([8]byte{108, 158, 154, 175, 212, 98, 52, 66})

	// Set a new Merkle root that defines approved operations.
	//
	// This function updates the active Merkle root after verifying ECDSA signatures and validating
	// the provided metadata against a Merkle proof.
	//
	// # Parameters
	//
	// - `ctx`: The context containing required accounts.
	// - `multisig_id`: The multisig instance identifier.
	// - `root`: The new Merkle root to set.
	// - `valid_until`: timestamp until which the root remains valid.
	// - `metadata`: Structured input containing chain_id, multisig, and operation counters.
	// - `metadata_proof`: Merkle proof validating the metadata.
	Instruction_SetRoot = ag_binary.TypeID([8]byte{183, 49, 10, 206, 168, 183, 131, 67})

	// Executes an operation after verifying it's authorized in the current Merkle root.
	//
	// This function:
	// 1. Performs extensive validation checks on the operation
	// - Ensures the operation is within the allowed count range
	// - Verifies chain ID matches the configured chain
	// - Checks the root has not expired
	// - Validates the operation's nonce against current state
	// 2. Verifies the operation's inclusion in the Merkle tree
	// 3. Executes the cross-program invocation with the multisig signer PDA
	//
	// # Parameters
	//
	// - `ctx`: Context containing operation accounts and signer information
	// - `multisig_id`: Identifier for the multisig instance
	// - `chain_id`: Network identifier that must match configuration
	// - `nonce`: Operation counter that must match current state
	// - `data`: Instruction data to be executed
	// - `proof`: Merkle proof for operation verification
	//
	// # Security Considerations
	//
	// This instruction implements secure privilege delegation through PDA signing.
	// The multisig's signer PDA becomes the authoritative signer for the operation,
	// allowing controlled execution of privileged actions while maintaining the
	// security guarantees of the Merkle root validation.
	Instruction_Execute = ag_binary.TypeID([8]byte{130, 221, 242, 154, 13, 193, 189, 29})

	// Initialize the storage for signer addresses.
	//
	// Creates a temporary account to hold signer addresses during the multisig configuration process.
	//
	// # Parameters
	//
	// - `ctx`: The context containing required accounts.
	// - `multisig_id`: The multisig instance identifier.
	// - `total_signers`: The total number of signers to be added.
	Instruction_InitSigners = ag_binary.TypeID([8]byte{102, 182, 129, 16, 138, 142, 223, 196})

	// Append a batch of signer addresses to the temporary storage.
	//
	// Allows adding multiple signer addresses in batches to overcome transaction size limits.
	//
	// # Parameters
	//
	// - `ctx`: The context containing required accounts.
	// - `multisig_id`: The multisig instance identifier.
	// - `signers_batch`: A batch of Ethereum addresses (20 bytes each) to be added as signers.
	Instruction_AppendSigners = ag_binary.TypeID([8]byte{238, 209, 251, 39, 41, 241, 146, 25})

	// Clear the temporary signer storage.
	//
	// Closes the account storing signer addresses, allowing it to be reinitialized if needed.
	//
	// # Parameters
	//
	// - `ctx`: The context containing required accounts.
	// - `multisig_id`: The multisig instance identifier.
	Instruction_ClearSigners = ag_binary.TypeID([8]byte{90, 140, 170, 146, 128, 75, 100, 175})

	// Finalize the signer configuration.
	//
	// Marks the signer list as complete and ready for incorporation into the multisig configuration.
	//
	// # Parameters
	//
	// - `ctx`: The context containing required accounts.
	// - `multisig_id`: The multisig instance identifier.
	Instruction_FinalizeSigners = ag_binary.TypeID([8]byte{49, 254, 154, 226, 137, 199, 120, 63})

	// Initialize storage for ECDSA signatures.
	//
	// Creates a temporary account to hold signatures that will validate a new Merkle root.
	//
	// # Parameters
	//
	// - `ctx`: The context containing required accounts.
	// - `multisig_id`: The multisig instance identifier.
	// - `root`: The new Merkle root these signatures will approve.
	// - `valid_until`: Timestamp until which the root will remain valid.
	// - `total_signatures`: The total number of signatures to be added.
	Instruction_InitSignatures = ag_binary.TypeID([8]byte{190, 120, 207, 36, 26, 58, 196, 13})

	// Append a batch of ECDSA signatures to the temporary storage.
	//
	// Allows adding multiple signatures in batches to overcome transaction size limits.
	//
	// # Parameters
	//
	// - `ctx`: The context containing required accounts.
	// - `multisig_id`: The multisig instance identifier.
	// - `root`: The Merkle root being approved.
	// - `valid_until`: Timestamp until which the root will remain valid.
	// - `signatures_batch`: A batch of ECDSA signatures to be verified.
	Instruction_AppendSignatures = ag_binary.TypeID([8]byte{195, 112, 164, 69, 37, 137, 198, 54})

	// Clear the temporary signature storage.
	//
	// Closes the account storing signatures, allowing it to be reinitialized if needed.
	//
	// # Parameters
	//
	// - `ctx`: The context containing required accounts.
	// - `multisig_id`: The multisig instance identifier.
	// - `root`: The Merkle root associated with the signatures.
	// - `valid_until`: Timestamp until which the root would remain valid.
	Instruction_ClearSignatures = ag_binary.TypeID([8]byte{80, 0, 39, 255, 46, 165, 193, 109})

	// Finalize the signature configuration.
	//
	// Marks the signature list as finalized and ready for verification when setting a new root.
	//
	// # Parameters
	//
	// - `ctx`: The context containing required accounts.
	// - `multisig_id`: The multisig instance identifier.
	// - `root`: The Merkle root associated with the signatures.
	// - `valid_until`: Timestamp until which the root will remain valid.
	Instruction_FinalizeSignatures = ag_binary.TypeID([8]byte{77, 138, 152, 199, 37, 141, 189, 159})
)

// InstructionIDToName returns the name of the instruction given its ID.
func InstructionIDToName(id ag_binary.TypeID) string {
	switch id {
	case Instruction_Initialize:
		return "Initialize"
	case Instruction_TransferOwnership:
		return "TransferOwnership"
	case Instruction_AcceptOwnership:
		return "AcceptOwnership"
	case Instruction_SetConfig:
		return "SetConfig"
	case Instruction_SetRoot:
		return "SetRoot"
	case Instruction_Execute:
		return "Execute"
	case Instruction_InitSigners:
		return "InitSigners"
	case Instruction_AppendSigners:
		return "AppendSigners"
	case Instruction_ClearSigners:
		return "ClearSigners"
	case Instruction_FinalizeSigners:
		return "FinalizeSigners"
	case Instruction_InitSignatures:
		return "InitSignatures"
	case Instruction_AppendSignatures:
		return "AppendSignatures"
	case Instruction_ClearSignatures:
		return "ClearSignatures"
	case Instruction_FinalizeSignatures:
		return "FinalizeSignatures"
	default:
		return ""
	}
}

type Instruction struct {
	ag_binary.BaseVariant
}

func (inst *Instruction) EncodeToTree(parent ag_treeout.Branches) {
	if enToTree, ok := inst.Impl.(ag_text.EncodableToTree); ok {
		enToTree.EncodeToTree(parent)
	} else {
		parent.Child(ag_spew.Sdump(inst))
	}
}

var InstructionImplDef = ag_binary.NewVariantDefinition(
	ag_binary.AnchorTypeIDEncoding,
	[]ag_binary.VariantType{
		{
			"initialize", (*Initialize)(nil),
		},
		{
			"transfer_ownership", (*TransferOwnership)(nil),
		},
		{
			"accept_ownership", (*AcceptOwnership)(nil),
		},
		{
			"set_config", (*SetConfig)(nil),
		},
		{
			"set_root", (*SetRoot)(nil),
		},
		{
			"execute", (*Execute)(nil),
		},
		{
			"init_signers", (*InitSigners)(nil),
		},
		{
			"append_signers", (*AppendSigners)(nil),
		},
		{
			"clear_signers", (*ClearSigners)(nil),
		},
		{
			"finalize_signers", (*FinalizeSigners)(nil),
		},
		{
			"init_signatures", (*InitSignatures)(nil),
		},
		{
			"append_signatures", (*AppendSignatures)(nil),
		},
		{
			"clear_signatures", (*ClearSignatures)(nil),
		},
		{
			"finalize_signatures", (*FinalizeSignatures)(nil),
		},
	},
)

func (inst *Instruction) ProgramID() ag_solanago.PublicKey {
	return ProgramID
}

func (inst *Instruction) Accounts() (out []*ag_solanago.AccountMeta) {
	return inst.Impl.(ag_solanago.AccountsGettable).GetAccounts()
}

func (inst *Instruction) Data() ([]byte, error) {
	buf := new(bytes.Buffer)
	if err := ag_binary.NewBorshEncoder(buf).Encode(inst); err != nil {
		return nil, fmt.Errorf("unable to encode instruction: %w", err)
	}
	return buf.Bytes(), nil
}

func (inst *Instruction) TextEncode(encoder *ag_text.Encoder, option *ag_text.Option) error {
	return encoder.Encode(inst.Impl, option)
}

func (inst *Instruction) UnmarshalWithDecoder(decoder *ag_binary.Decoder) error {
	return inst.BaseVariant.UnmarshalBinaryVariant(decoder, InstructionImplDef)
}

func (inst *Instruction) MarshalWithEncoder(encoder *ag_binary.Encoder) error {
	err := encoder.WriteBytes(inst.TypeID.Bytes(), false)
	if err != nil {
		return fmt.Errorf("unable to write variant type: %w", err)
	}
	return encoder.Encode(inst.Impl)
}

func registryDecodeInstruction(accounts []*ag_solanago.AccountMeta, data []byte) (interface{}, error) {
	inst, err := DecodeInstruction(accounts, data)
	if err != nil {
		return nil, err
	}
	return inst, nil
}

func DecodeInstruction(accounts []*ag_solanago.AccountMeta, data []byte) (*Instruction, error) {
	inst := new(Instruction)
	if err := ag_binary.NewBorshDecoder(data).Decode(inst); err != nil {
		return nil, fmt.Errorf("unable to decode instruction: %w", err)
	}
	if v, ok := inst.Impl.(ag_solanago.AccountsSettable); ok {
		err := v.SetAccounts(accounts)
		if err != nil {
			return nil, fmt.Errorf("unable to set accounts for instruction: %w", err)
		}
	}
	return inst, nil
}
