// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package mcm

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Initialize storage for ECDSA signatures.
//
// Creates a temporary account to hold signatures that will validate a new Merkle root.
//
// # Parameters
//
// - `ctx`: The context containing required accounts.
// - `multisig_id`: The multisig instance identifier.
// - `root`: The new Merkle root these signatures will approve.
// - `valid_until`: Timestamp until which the root will remain valid.
// - `total_signatures`: The total number of signatures to be added.
type InitSignatures struct {
	MultisigId      *[32]uint8
	Root            *[32]uint8
	ValidUntil      *uint32
	TotalSignatures *uint8

	// [0] = [WRITE] signatures
	//
	// [1] = [WRITE, SIGNER] authority
	//
	// [2] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewInitSignaturesInstructionBuilder creates a new `InitSignatures` instruction builder.
func NewInitSignaturesInstructionBuilder() *InitSignatures {
	nd := &InitSignatures{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetMultisigId sets the "multisigId" parameter.
func (inst *InitSignatures) SetMultisigId(multisigId [32]uint8) *InitSignatures {
	inst.MultisigId = &multisigId
	return inst
}

// SetRoot sets the "root" parameter.
func (inst *InitSignatures) SetRoot(root [32]uint8) *InitSignatures {
	inst.Root = &root
	return inst
}

// SetValidUntil sets the "validUntil" parameter.
func (inst *InitSignatures) SetValidUntil(validUntil uint32) *InitSignatures {
	inst.ValidUntil = &validUntil
	return inst
}

// SetTotalSignatures sets the "totalSignatures" parameter.
func (inst *InitSignatures) SetTotalSignatures(totalSignatures uint8) *InitSignatures {
	inst.TotalSignatures = &totalSignatures
	return inst
}

// SetSignaturesAccount sets the "signatures" account.
func (inst *InitSignatures) SetSignaturesAccount(signatures ag_solanago.PublicKey) *InitSignatures {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(signatures).WRITE()
	return inst
}

// GetSignaturesAccount gets the "signatures" account.
func (inst *InitSignatures) GetSignaturesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *InitSignatures) SetAuthorityAccount(authority ag_solanago.PublicKey) *InitSignatures {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *InitSignatures) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *InitSignatures) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *InitSignatures {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *InitSignatures) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst InitSignatures) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_InitSignatures,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst InitSignatures) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *InitSignatures) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.MultisigId == nil {
			return errors.New("MultisigId parameter is not set")
		}
		if inst.Root == nil {
			return errors.New("Root parameter is not set")
		}
		if inst.ValidUntil == nil {
			return errors.New("ValidUntil parameter is not set")
		}
		if inst.TotalSignatures == nil {
			return errors.New("TotalSignatures parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Signatures is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *InitSignatures) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("InitSignatures")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=4]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("     MultisigId", *inst.MultisigId))
						paramsBranch.Child(ag_format.Param("           Root", *inst.Root))
						paramsBranch.Child(ag_format.Param("     ValidUntil", *inst.ValidUntil))
						paramsBranch.Child(ag_format.Param("TotalSignatures", *inst.TotalSignatures))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("   signatures", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("    authority", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("systemProgram", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj InitSignatures) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `MultisigId` param:
	err = encoder.Encode(obj.MultisigId)
	if err != nil {
		return err
	}
	// Serialize `Root` param:
	err = encoder.Encode(obj.Root)
	if err != nil {
		return err
	}
	// Serialize `ValidUntil` param:
	err = encoder.Encode(obj.ValidUntil)
	if err != nil {
		return err
	}
	// Serialize `TotalSignatures` param:
	err = encoder.Encode(obj.TotalSignatures)
	if err != nil {
		return err
	}
	return nil
}
func (obj *InitSignatures) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `MultisigId`:
	err = decoder.Decode(&obj.MultisigId)
	if err != nil {
		return err
	}
	// Deserialize `Root`:
	err = decoder.Decode(&obj.Root)
	if err != nil {
		return err
	}
	// Deserialize `ValidUntil`:
	err = decoder.Decode(&obj.ValidUntil)
	if err != nil {
		return err
	}
	// Deserialize `TotalSignatures`:
	err = decoder.Decode(&obj.TotalSignatures)
	if err != nil {
		return err
	}
	return nil
}

// NewInitSignaturesInstruction declares a new InitSignatures instruction with the provided parameters and accounts.
func NewInitSignaturesInstruction(
	// Parameters:
	multisigId [32]uint8,
	root [32]uint8,
	validUntil uint32,
	totalSignatures uint8,
	// Accounts:
	signatures ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *InitSignatures {
	return NewInitSignaturesInstructionBuilder().
		SetMultisigId(multisigId).
		SetRoot(root).
		SetValidUntil(validUntil).
		SetTotalSignatures(totalSignatures).
		SetSignaturesAccount(signatures).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
