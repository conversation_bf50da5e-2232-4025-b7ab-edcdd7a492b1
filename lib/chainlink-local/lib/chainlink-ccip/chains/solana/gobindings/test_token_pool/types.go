// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package test_token_pool

import ag_binary "github.com/gagliardetto/binary"

type PoolType ag_binary.BorshEnum

const (
	LockAndRelease_PoolType PoolType = iota
	BurnAndMint_PoolType
	Wrapped_PoolType
)

func (value PoolType) String() string {
	switch value {
	case LockAndRelease_PoolType:
		return "LockAndRelease"
	case BurnAndMint_PoolType:
		return "BurnAndMint"
	case Wrapped_PoolType:
		return "Wrapped"
	default:
		return ""
	}
}
