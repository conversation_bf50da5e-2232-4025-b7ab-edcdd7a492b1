// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package timelock

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Initialize the timelock configuration.
//
// # Parameters
//
// - `ctx`: The context containing the accounts required for initialization.
// - `timelock_id`: A unique, padded identifier for this timelock instance.
// - `min_delay`: The minimum delay (in seconds) required for scheduled operations.
type Initialize struct {
	TimelockId *[32]uint8
	MinDelay   *uint64

	// [0] = [WRITE] config
	//
	// [1] = [WRITE, SIGNER] authority
	//
	// [2] = [] systemProgram
	//
	// [3] = [] program
	//
	// [4] = [] programData
	//
	// [5] = [] accessControllerProgram
	//
	// [6] = [] proposerRoleAccessController
	//
	// [7] = [] executorRoleAccessController
	//
	// [8] = [] cancellerRoleAccessController
	//
	// [9] = [] bypasserRoleAccessController
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewInitializeInstructionBuilder creates a new `Initialize` instruction builder.
func NewInitializeInstructionBuilder() *Initialize {
	nd := &Initialize{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 10),
	}
	return nd
}

// SetTimelockId sets the "timelockId" parameter.
func (inst *Initialize) SetTimelockId(timelockId [32]uint8) *Initialize {
	inst.TimelockId = &timelockId
	return inst
}

// SetMinDelay sets the "minDelay" parameter.
func (inst *Initialize) SetMinDelay(minDelay uint64) *Initialize {
	inst.MinDelay = &minDelay
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *Initialize) SetConfigAccount(config ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config).WRITE()
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *Initialize) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *Initialize) SetAuthorityAccount(authority ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *Initialize) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *Initialize) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *Initialize) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetProgramAccount sets the "program" account.
func (inst *Initialize) SetProgramAccount(program ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(program)
	return inst
}

// GetProgramAccount gets the "program" account.
func (inst *Initialize) GetProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetProgramDataAccount sets the "programData" account.
func (inst *Initialize) SetProgramDataAccount(programData ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(programData)
	return inst
}

// GetProgramDataAccount gets the "programData" account.
func (inst *Initialize) GetProgramDataAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetAccessControllerProgramAccount sets the "accessControllerProgram" account.
func (inst *Initialize) SetAccessControllerProgramAccount(accessControllerProgram ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(accessControllerProgram)
	return inst
}

// GetAccessControllerProgramAccount gets the "accessControllerProgram" account.
func (inst *Initialize) GetAccessControllerProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetProposerRoleAccessControllerAccount sets the "proposerRoleAccessController" account.
func (inst *Initialize) SetProposerRoleAccessControllerAccount(proposerRoleAccessController ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(proposerRoleAccessController)
	return inst
}

// GetProposerRoleAccessControllerAccount gets the "proposerRoleAccessController" account.
func (inst *Initialize) GetProposerRoleAccessControllerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

// SetExecutorRoleAccessControllerAccount sets the "executorRoleAccessController" account.
func (inst *Initialize) SetExecutorRoleAccessControllerAccount(executorRoleAccessController ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[7] = ag_solanago.Meta(executorRoleAccessController)
	return inst
}

// GetExecutorRoleAccessControllerAccount gets the "executorRoleAccessController" account.
func (inst *Initialize) GetExecutorRoleAccessControllerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[7]
}

// SetCancellerRoleAccessControllerAccount sets the "cancellerRoleAccessController" account.
func (inst *Initialize) SetCancellerRoleAccessControllerAccount(cancellerRoleAccessController ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[8] = ag_solanago.Meta(cancellerRoleAccessController)
	return inst
}

// GetCancellerRoleAccessControllerAccount gets the "cancellerRoleAccessController" account.
func (inst *Initialize) GetCancellerRoleAccessControllerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[8]
}

// SetBypasserRoleAccessControllerAccount sets the "bypasserRoleAccessController" account.
func (inst *Initialize) SetBypasserRoleAccessControllerAccount(bypasserRoleAccessController ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[9] = ag_solanago.Meta(bypasserRoleAccessController)
	return inst
}

// GetBypasserRoleAccessControllerAccount gets the "bypasserRoleAccessController" account.
func (inst *Initialize) GetBypasserRoleAccessControllerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[9]
}

func (inst Initialize) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_Initialize,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst Initialize) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *Initialize) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.TimelockId == nil {
			return errors.New("TimelockId parameter is not set")
		}
		if inst.MinDelay == nil {
			return errors.New("MinDelay parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.Program is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.ProgramData is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.AccessControllerProgram is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.ProposerRoleAccessController is not set")
		}
		if inst.AccountMetaSlice[7] == nil {
			return errors.New("accounts.ExecutorRoleAccessController is not set")
		}
		if inst.AccountMetaSlice[8] == nil {
			return errors.New("accounts.CancellerRoleAccessController is not set")
		}
		if inst.AccountMetaSlice[9] == nil {
			return errors.New("accounts.BypasserRoleAccessController is not set")
		}
	}
	return nil
}

func (inst *Initialize) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("Initialize")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=2]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("TimelockId", *inst.TimelockId))
						paramsBranch.Child(ag_format.Param("  MinDelay", *inst.MinDelay))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=10]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("                       config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("                    authority", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("                systemProgram", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("                      program", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("                  programData", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("      accessControllerProgram", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta(" proposerRoleAccessController", inst.AccountMetaSlice[6]))
						accountsBranch.Child(ag_format.Meta(" executorRoleAccessController", inst.AccountMetaSlice[7]))
						accountsBranch.Child(ag_format.Meta("cancellerRoleAccessController", inst.AccountMetaSlice[8]))
						accountsBranch.Child(ag_format.Meta(" bypasserRoleAccessController", inst.AccountMetaSlice[9]))
					})
				})
		})
}

func (obj Initialize) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `TimelockId` param:
	err = encoder.Encode(obj.TimelockId)
	if err != nil {
		return err
	}
	// Serialize `MinDelay` param:
	err = encoder.Encode(obj.MinDelay)
	if err != nil {
		return err
	}
	return nil
}
func (obj *Initialize) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `TimelockId`:
	err = decoder.Decode(&obj.TimelockId)
	if err != nil {
		return err
	}
	// Deserialize `MinDelay`:
	err = decoder.Decode(&obj.MinDelay)
	if err != nil {
		return err
	}
	return nil
}

// NewInitializeInstruction declares a new Initialize instruction with the provided parameters and accounts.
func NewInitializeInstruction(
	// Parameters:
	timelockId [32]uint8,
	minDelay uint64,
	// Accounts:
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey,
	program ag_solanago.PublicKey,
	programData ag_solanago.PublicKey,
	accessControllerProgram ag_solanago.PublicKey,
	proposerRoleAccessController ag_solanago.PublicKey,
	executorRoleAccessController ag_solanago.PublicKey,
	cancellerRoleAccessController ag_solanago.PublicKey,
	bypasserRoleAccessController ag_solanago.PublicKey) *Initialize {
	return NewInitializeInstructionBuilder().
		SetTimelockId(timelockId).
		SetMinDelay(minDelay).
		SetConfigAccount(config).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram).
		SetProgramAccount(program).
		SetProgramDataAccount(programData).
		SetAccessControllerProgramAccount(accessControllerProgram).
		SetProposerRoleAccessControllerAccount(proposerRoleAccessController).
		SetExecutorRoleAccessControllerAccount(executorRoleAccessController).
		SetCancellerRoleAccessControllerAccount(cancellerRoleAccessController).
		SetBypasserRoleAccessControllerAccount(bypasserRoleAccessController)
}
