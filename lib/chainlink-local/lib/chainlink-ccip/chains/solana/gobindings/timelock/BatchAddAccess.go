// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package timelock

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Add a new access role in batch. Only the admin is allowed to perform this operation.
//
// # Parameters
//
// - `ctx`: The context containing the accounts required for batch adding access.
// - `timelock_id`: A unique, padded identifier for this timelock instance.
// - `role`: The role to be added.
type BatchAddAccess struct {
	TimelockId *[32]uint8
	Role       *Role

	// [0] = [] config
	//
	// [1] = [] accessControllerProgram
	//
	// [2] = [WRITE] roleAccessController
	//
	// [3] = [WRITE, SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewBatchAddAccessInstructionBuilder creates a new `BatchAddAccess` instruction builder.
func NewBatchAddAccessInstructionBuilder() *BatchAddAccess {
	nd := &BatchAddAccess{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 4),
	}
	return nd
}

// SetTimelockId sets the "timelockId" parameter.
func (inst *BatchAddAccess) SetTimelockId(timelockId [32]uint8) *BatchAddAccess {
	inst.TimelockId = &timelockId
	return inst
}

// SetRole sets the "role" parameter.
func (inst *BatchAddAccess) SetRole(role Role) *BatchAddAccess {
	inst.Role = &role
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *BatchAddAccess) SetConfigAccount(config ag_solanago.PublicKey) *BatchAddAccess {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *BatchAddAccess) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAccessControllerProgramAccount sets the "accessControllerProgram" account.
func (inst *BatchAddAccess) SetAccessControllerProgramAccount(accessControllerProgram ag_solanago.PublicKey) *BatchAddAccess {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(accessControllerProgram)
	return inst
}

// GetAccessControllerProgramAccount gets the "accessControllerProgram" account.
func (inst *BatchAddAccess) GetAccessControllerProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetRoleAccessControllerAccount sets the "roleAccessController" account.
func (inst *BatchAddAccess) SetRoleAccessControllerAccount(roleAccessController ag_solanago.PublicKey) *BatchAddAccess {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(roleAccessController).WRITE()
	return inst
}

// GetRoleAccessControllerAccount gets the "roleAccessController" account.
func (inst *BatchAddAccess) GetRoleAccessControllerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *BatchAddAccess) SetAuthorityAccount(authority ag_solanago.PublicKey) *BatchAddAccess {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *BatchAddAccess) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

func (inst BatchAddAccess) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_BatchAddAccess,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst BatchAddAccess) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *BatchAddAccess) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.TimelockId == nil {
			return errors.New("TimelockId parameter is not set")
		}
		if inst.Role == nil {
			return errors.New("Role parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.AccessControllerProgram is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.RoleAccessController is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *BatchAddAccess) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("BatchAddAccess")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=2]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("TimelockId", *inst.TimelockId))
						paramsBranch.Child(ag_format.Param("      Role", *inst.Role))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=4]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("                 config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("accessControllerProgram", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("   roleAccessController", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("              authority", inst.AccountMetaSlice[3]))
					})
				})
		})
}

func (obj BatchAddAccess) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `TimelockId` param:
	err = encoder.Encode(obj.TimelockId)
	if err != nil {
		return err
	}
	// Serialize `Role` param:
	err = encoder.Encode(obj.Role)
	if err != nil {
		return err
	}
	return nil
}
func (obj *BatchAddAccess) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `TimelockId`:
	err = decoder.Decode(&obj.TimelockId)
	if err != nil {
		return err
	}
	// Deserialize `Role`:
	err = decoder.Decode(&obj.Role)
	if err != nil {
		return err
	}
	return nil
}

// NewBatchAddAccessInstruction declares a new BatchAddAccess instruction with the provided parameters and accounts.
func NewBatchAddAccessInstruction(
	// Parameters:
	timelockId [32]uint8,
	role Role,
	// Accounts:
	config ag_solanago.PublicKey,
	accessControllerProgram ag_solanago.PublicKey,
	roleAccessController ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *BatchAddAccess {
	return NewBatchAddAccessInstructionBuilder().
		SetTimelockId(timelockId).
		SetRole(role).
		SetConfigAccount(config).
		SetAccessControllerProgramAccount(accessControllerProgram).
		SetRoleAccessControllerAccount(roleAccessController).
		SetAuthorityAccount(authority)
}
