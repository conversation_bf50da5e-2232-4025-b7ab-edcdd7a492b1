// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package timelock

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Append additional data to an instruction of a bypasser operation.
//
// # Parameters
//
// - `ctx`: The context containing the bypasser operation account.
// - `_timelock_id`: The timelock identifier.
// - `_id`: The operation identifier.
// - `ix_index`: The index of the instruction.
// - `ix_data_chunk`: The data to append.
type AppendBypasserInstructionData struct {
	TimelockId  *[32]uint8
	Id          *[32]uint8
	IxIndex     *uint32
	IxDataChunk *[]byte

	// [0] = [WRITE] operation
	//
	// [1] = [] config
	//
	// [2] = [] roleAccessController
	//
	// [3] = [WRITE, SIGNER] authority
	//
	// [4] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewAppendBypasserInstructionDataInstructionBuilder creates a new `AppendBypasserInstructionData` instruction builder.
func NewAppendBypasserInstructionDataInstructionBuilder() *AppendBypasserInstructionData {
	nd := &AppendBypasserInstructionData{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 5),
	}
	return nd
}

// SetTimelockId sets the "timelockId" parameter.
func (inst *AppendBypasserInstructionData) SetTimelockId(timelockId [32]uint8) *AppendBypasserInstructionData {
	inst.TimelockId = &timelockId
	return inst
}

// SetId sets the "id" parameter.
func (inst *AppendBypasserInstructionData) SetId(id [32]uint8) *AppendBypasserInstructionData {
	inst.Id = &id
	return inst
}

// SetIxIndex sets the "ixIndex" parameter.
func (inst *AppendBypasserInstructionData) SetIxIndex(ixIndex uint32) *AppendBypasserInstructionData {
	inst.IxIndex = &ixIndex
	return inst
}

// SetIxDataChunk sets the "ixDataChunk" parameter.
func (inst *AppendBypasserInstructionData) SetIxDataChunk(ixDataChunk []byte) *AppendBypasserInstructionData {
	inst.IxDataChunk = &ixDataChunk
	return inst
}

// SetOperationAccount sets the "operation" account.
func (inst *AppendBypasserInstructionData) SetOperationAccount(operation ag_solanago.PublicKey) *AppendBypasserInstructionData {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(operation).WRITE()
	return inst
}

// GetOperationAccount gets the "operation" account.
func (inst *AppendBypasserInstructionData) GetOperationAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigAccount sets the "config" account.
func (inst *AppendBypasserInstructionData) SetConfigAccount(config ag_solanago.PublicKey) *AppendBypasserInstructionData {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *AppendBypasserInstructionData) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetRoleAccessControllerAccount sets the "roleAccessController" account.
func (inst *AppendBypasserInstructionData) SetRoleAccessControllerAccount(roleAccessController ag_solanago.PublicKey) *AppendBypasserInstructionData {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(roleAccessController)
	return inst
}

// GetRoleAccessControllerAccount gets the "roleAccessController" account.
func (inst *AppendBypasserInstructionData) GetRoleAccessControllerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *AppendBypasserInstructionData) SetAuthorityAccount(authority ag_solanago.PublicKey) *AppendBypasserInstructionData {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *AppendBypasserInstructionData) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *AppendBypasserInstructionData) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *AppendBypasserInstructionData {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *AppendBypasserInstructionData) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

func (inst AppendBypasserInstructionData) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_AppendBypasserInstructionData,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst AppendBypasserInstructionData) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *AppendBypasserInstructionData) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.TimelockId == nil {
			return errors.New("TimelockId parameter is not set")
		}
		if inst.Id == nil {
			return errors.New("Id parameter is not set")
		}
		if inst.IxIndex == nil {
			return errors.New("IxIndex parameter is not set")
		}
		if inst.IxDataChunk == nil {
			return errors.New("IxDataChunk parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Operation is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.RoleAccessController is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *AppendBypasserInstructionData) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("AppendBypasserInstructionData")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=4]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param(" TimelockId", *inst.TimelockId))
						paramsBranch.Child(ag_format.Param("         Id", *inst.Id))
						paramsBranch.Child(ag_format.Param("    IxIndex", *inst.IxIndex))
						paramsBranch.Child(ag_format.Param("IxDataChunk", *inst.IxDataChunk))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=5]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("           operation", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("              config", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("roleAccessController", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("           authority", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("       systemProgram", inst.AccountMetaSlice[4]))
					})
				})
		})
}

func (obj AppendBypasserInstructionData) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `TimelockId` param:
	err = encoder.Encode(obj.TimelockId)
	if err != nil {
		return err
	}
	// Serialize `Id` param:
	err = encoder.Encode(obj.Id)
	if err != nil {
		return err
	}
	// Serialize `IxIndex` param:
	err = encoder.Encode(obj.IxIndex)
	if err != nil {
		return err
	}
	// Serialize `IxDataChunk` param:
	err = encoder.Encode(obj.IxDataChunk)
	if err != nil {
		return err
	}
	return nil
}
func (obj *AppendBypasserInstructionData) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `TimelockId`:
	err = decoder.Decode(&obj.TimelockId)
	if err != nil {
		return err
	}
	// Deserialize `Id`:
	err = decoder.Decode(&obj.Id)
	if err != nil {
		return err
	}
	// Deserialize `IxIndex`:
	err = decoder.Decode(&obj.IxIndex)
	if err != nil {
		return err
	}
	// Deserialize `IxDataChunk`:
	err = decoder.Decode(&obj.IxDataChunk)
	if err != nil {
		return err
	}
	return nil
}

// NewAppendBypasserInstructionDataInstruction declares a new AppendBypasserInstructionData instruction with the provided parameters and accounts.
func NewAppendBypasserInstructionDataInstruction(
	// Parameters:
	timelockId [32]uint8,
	id [32]uint8,
	ixIndex uint32,
	ixDataChunk []byte,
	// Accounts:
	operation ag_solanago.PublicKey,
	config ag_solanago.PublicKey,
	roleAccessController ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *AppendBypasserInstructionData {
	return NewAppendBypasserInstructionDataInstructionBuilder().
		SetTimelockId(timelockId).
		SetId(id).
		SetIxIndex(ixIndex).
		SetIxDataChunk(ixDataChunk).
		SetOperationAccount(operation).
		SetConfigAccount(config).
		SetRoleAccessControllerAccount(roleAccessController).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
