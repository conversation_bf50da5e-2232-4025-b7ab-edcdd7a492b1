// The `timelock` program provides a mechanism to schedule, execute, and (if needed) cancel
// operations in a delayed fashion. It supports both standard operations (which enforce delays and dependencies)
// and bypass operations (for emergency cases).
//
// Operation management for timelock system, handling both standard (timelock-enforced)
// and bypass (emergency) operations.
//
// Standard Operation Flow:
// - initialize -> append(init_ix, append_ix_data) -> finalize -> schedule -> execute_batch
// - Enforces timelock delays and predecessor dependencies
//
// Bypass Operation Flow:
// - initialize -> append(init_ix, append_ix_data) -> finalize -> bypass_execute_batch
// - No required delay or additional checks, closes operation account after execution
//
// Implementation uses separate code paths and PDAs for each operation type
// to maintain clear security boundaries and audit trails, despite similar logic.
// All operations enforce state transitions, size limits, and role-based access.
// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package timelock

import (
	"bytes"
	"fmt"
	ag_spew "github.com/davecgh/go-spew/spew"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_text "github.com/gagliardetto/solana-go/text"
	ag_treeout "github.com/gagliardetto/treeout"
)

var ProgramID ag_solanago.PublicKey

func SetProgramID(pubkey ag_solanago.PublicKey) {
	ProgramID = pubkey
	ag_solanago.RegisterInstructionDecoder(ProgramID, registryDecodeInstruction)
}

const ProgramName = "Timelock"

func init() {
	if !ProgramID.IsZero() {
		ag_solanago.RegisterInstructionDecoder(ProgramID, registryDecodeInstruction)
	}
}

var (
	// Initialize the timelock configuration.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the accounts required for initialization.
	// - `timelock_id`: A unique, padded identifier for this timelock instance.
	// - `min_delay`: The minimum delay (in seconds) required for scheduled operations.
	Instruction_Initialize = ag_binary.TypeID([8]byte{175, 175, 109, 31, 13, 152, 155, 237})

	// Add a new access role in batch. Only the admin is allowed to perform this operation.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the accounts required for batch adding access.
	// - `timelock_id`: A unique, padded identifier for this timelock instance.
	// - `role`: The role to be added.
	Instruction_BatchAddAccess = ag_binary.TypeID([8]byte{73, 141, 223, 79, 66, 154, 226, 67})

	// Initialize a new standard timelock operation.
	//
	// This sets up a new operation with the given ID, predecessor, salt, and expected number of instructions.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the operation account.
	// - `_timelock_id`: A padded identifier for the timelock (unused here but required for PDA derivation).
	// - `id`: The unique identifier for the operation.
	// - `predecessor`: The identifier of the predecessor operation.
	// - `salt`: A salt value to help create unique PDAs.
	// - `instruction_count`: The total number of instructions that will be added to this operation.
	Instruction_InitializeOperation = ag_binary.TypeID([8]byte{15, 96, 217, 171, 124, 4, 113, 243})

	// Append a new instruction to an existing standard operation.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the operation account.
	// - `_timelock_id`: The timelock identifier (for PDA derivation).
	// - `_id`: The operation identifier.
	// - `program_id`: The target program for the instruction.
	// - `accounts`: The list of accounts required for the instruction.
	Instruction_InitializeInstruction = ag_binary.TypeID([8]byte{195, 230, 213, 135, 144, 148, 142, 85})

	// Append additional instruction data to an instruction of an existing standard operation.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the operation account.
	// - `_timelock_id`: The timelock identifier (for PDA derivation).
	// - `_id`: The operation identifier.
	// - `ix_index`: The index of the instruction to which the data will be appended.
	// - `ix_data_chunk`: A chunk of data to be appended.
	Instruction_AppendInstructionData = ag_binary.TypeID([8]byte{76, 77, 102, 131, 136, 12, 45, 5})

	// Finalize a standard operation.
	//
	// Finalizing an operation marks it as ready for scheduling.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the operation account.
	// - `_timelock_id`: The timelock identifier (for PDA derivation).
	// - `_id`: The operation identifier.
	Instruction_FinalizeOperation = ag_binary.TypeID([8]byte{63, 208, 32, 98, 85, 182, 236, 140})

	// Clear an operation that has been finalized.
	//
	// This effectively closes the operation account so that it can be reinitialized later.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the operation account.
	// - `_timelock_id`: The timelock identifier.
	// - `_id`: The operation identifier.
	Instruction_ClearOperation = ag_binary.TypeID([8]byte{111, 217, 62, 240, 224, 75, 60, 58})

	// Schedule a finalized operation to be executed after a delay.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the accounts for scheduling.
	// - `timelock_id`: The timelock identifier.
	// - `id`: The operation identifier.
	// - `delay`: The delay (in seconds) before the operation can be executed.
	Instruction_ScheduleBatch = ag_binary.TypeID([8]byte{242, 140, 87, 106, 71, 226, 86, 32})

	// Cancel a scheduled operation.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the accounts for cancellation.
	// - `timelock_id`: The timelock identifier.
	// - `id`: The operation identifier (precalculated).
	Instruction_Cancel = ag_binary.TypeID([8]byte{232, 219, 223, 41, 219, 236, 220, 190})

	// Executes a scheduled batch of operations after validating readiness and predecessor dependencies.
	//
	// This function:
	// 1. Verifies the operation is ready for execution (delay period has passed)
	// 2. Validates that any predecessor operation has been completed
	// 3. Executes each instruction in the operation using the timelock signer PDA
	// 4. Emits events for each executed instruction
	//
	// # Parameters
	//
	// - `ctx`: Context containing operation accounts and signer information
	// - `timelock_id`: Identifier for the timelock instance
	// - `_id`: Operation ID (used for PDA derivation)
	//
	// # Security Considerations
	//
	// This instruction uses PDA signing to create a trusted execution environment.
	// The timelock's signer PDA will replace any account marked as a signer in the
	// original instructions, providing the necessary privileges while maintaining
	// security through program derivation.
	Instruction_ExecuteBatch = ag_binary.TypeID([8]byte{112, 159, 211, 51, 238, 70, 212, 60})

	// Initialize a bypasser operation.
	//
	// Bypasser operations have no predecessor and can be executed without delay.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the bypasser operation account.
	// - `_timelock_id`: The timelock identifier.
	// - `id`: The operation identifier.
	// - `salt`: A salt value for PDA derivation.
	// - `instruction_count`: The number of instructions to be added.
	Instruction_InitializeBypasserOperation = ag_binary.TypeID([8]byte{58, 27, 48, 204, 19, 197, 63, 26})

	// Initialize an instruction for a bypasser operation.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the bypasser operation account.
	// - `_timelock_id`: The timelock identifier.
	// - `_id`: The operation identifier.
	// - `program_id`: The target program for the instruction.
	// - `accounts`: The list of accounts required for the instruction.
	Instruction_InitializeBypasserInstruction = ag_binary.TypeID([8]byte{50, 17, 205, 172, 175, 140, 195, 39})

	// Append additional data to an instruction of a bypasser operation.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the bypasser operation account.
	// - `_timelock_id`: The timelock identifier.
	// - `_id`: The operation identifier.
	// - `ix_index`: The index of the instruction.
	// - `ix_data_chunk`: The data to append.
	Instruction_AppendBypasserInstructionData = ag_binary.TypeID([8]byte{184, 232, 151, 222, 111, 117, 215, 197})

	// Finalize a bypasser operation.
	//
	// Marks the bypasser operation as finalized, ready for execution.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the bypasser operation account.
	// - `_timelock_id`: The timelock identifier.
	// - `_id`: The operation identifier.
	Instruction_FinalizeBypasserOperation = ag_binary.TypeID([8]byte{45, 55, 198, 51, 124, 24, 169, 250})

	// Clear a finalized bypasser operation.
	//
	// Closes the bypasser operation account.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the bypasser operation account.
	// - `_timelock_id`: The timelock identifier.
	// - `_id`: The operation identifier.
	Instruction_ClearBypasserOperation = ag_binary.TypeID([8]byte{200, 21, 249, 130, 56, 13, 128, 32})

	// Execute operations immediately using the bypasser flow, bypassing time delays
	// and predecessor checks.
	//
	// This function provides an emergency execution mechanism that:
	// 1. Skips the timelock waiting period required for standard operations
	// 2. Does not enforce predecessor dependencies
	// 3. Closes the operation account after execution
	//
	// # Emergency Use Only
	//
	// The bypasser flow is intended strictly for emergency situations where
	// waiting for the standard timelock delay would cause harm. Access to this
	// function is tightly controlled through the Bypasser role.
	//
	// # Parameters
	//
	// - `ctx`: Context containing operation accounts and signer information
	// - `timelock_id`: Identifier for the timelock instance
	// - `_id`: Operation ID (used for PDA derivation)
	Instruction_BypasserExecuteBatch = ag_binary.TypeID([8]byte{90, 62, 66, 6, 227, 174, 30, 194})

	// Update the minimum delay required for scheduled operations.
	//
	// Only the admin can update the delay.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the configuration account.
	// - `_timelock_id`: The timelock identifier.
	// - `delay`: The new minimum delay value.
	Instruction_UpdateDelay = ag_binary.TypeID([8]byte{164, 186, 80, 62, 85, 88, 182, 147})

	// Block a function selector from being called.
	//
	// Only the admin can block function selectors.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the configuration account.
	// - `_timelock_id`: The timelock identifier.
	// - `selector`: The 8-byte function selector(Anchor discriminator) to block.
	Instruction_BlockFunctionSelector = ag_binary.TypeID([8]byte{119, 89, 101, 41, 72, 143, 218, 185})

	// Unblock a previously blocked function selector.
	//
	// Only the admin can unblock function selectors.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the configuration account.
	// - `_timelock_id`: The timelock identifier.
	// - `selector`: The function selector to unblock.
	Instruction_UnblockFunctionSelector = ag_binary.TypeID([8]byte{53, 84, 245, 196, 149, 52, 30, 57})

	// Propose a new owner for the timelock instance config.
	//
	// Only the current owner (admin) can propose a new owner.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the configuration account.
	// - `_timelock_id`: The timelock identifier.
	// - `proposed_owner`: The public key of the proposed new owner.
	Instruction_TransferOwnership = ag_binary.TypeID([8]byte{65, 177, 215, 73, 53, 45, 99, 47})

	// Accept ownership of the timelock config.
	//
	// The proposed new owner must call this function to assume ownership.
	//
	// # Parameters
	//
	// - `ctx`: The context containing the configuration account.
	// - `_timelock_id`: The timelock identifier.
	Instruction_AcceptOwnership = ag_binary.TypeID([8]byte{172, 23, 43, 13, 238, 213, 85, 150})
)

// InstructionIDToName returns the name of the instruction given its ID.
func InstructionIDToName(id ag_binary.TypeID) string {
	switch id {
	case Instruction_Initialize:
		return "Initialize"
	case Instruction_BatchAddAccess:
		return "BatchAddAccess"
	case Instruction_InitializeOperation:
		return "InitializeOperation"
	case Instruction_InitializeInstruction:
		return "InitializeInstruction"
	case Instruction_AppendInstructionData:
		return "AppendInstructionData"
	case Instruction_FinalizeOperation:
		return "FinalizeOperation"
	case Instruction_ClearOperation:
		return "ClearOperation"
	case Instruction_ScheduleBatch:
		return "ScheduleBatch"
	case Instruction_Cancel:
		return "Cancel"
	case Instruction_ExecuteBatch:
		return "ExecuteBatch"
	case Instruction_InitializeBypasserOperation:
		return "InitializeBypasserOperation"
	case Instruction_InitializeBypasserInstruction:
		return "InitializeBypasserInstruction"
	case Instruction_AppendBypasserInstructionData:
		return "AppendBypasserInstructionData"
	case Instruction_FinalizeBypasserOperation:
		return "FinalizeBypasserOperation"
	case Instruction_ClearBypasserOperation:
		return "ClearBypasserOperation"
	case Instruction_BypasserExecuteBatch:
		return "BypasserExecuteBatch"
	case Instruction_UpdateDelay:
		return "UpdateDelay"
	case Instruction_BlockFunctionSelector:
		return "BlockFunctionSelector"
	case Instruction_UnblockFunctionSelector:
		return "UnblockFunctionSelector"
	case Instruction_TransferOwnership:
		return "TransferOwnership"
	case Instruction_AcceptOwnership:
		return "AcceptOwnership"
	default:
		return ""
	}
}

type Instruction struct {
	ag_binary.BaseVariant
}

func (inst *Instruction) EncodeToTree(parent ag_treeout.Branches) {
	if enToTree, ok := inst.Impl.(ag_text.EncodableToTree); ok {
		enToTree.EncodeToTree(parent)
	} else {
		parent.Child(ag_spew.Sdump(inst))
	}
}

var InstructionImplDef = ag_binary.NewVariantDefinition(
	ag_binary.AnchorTypeIDEncoding,
	[]ag_binary.VariantType{
		{
			"initialize", (*Initialize)(nil),
		},
		{
			"batch_add_access", (*BatchAddAccess)(nil),
		},
		{
			"initialize_operation", (*InitializeOperation)(nil),
		},
		{
			"initialize_instruction", (*InitializeInstruction)(nil),
		},
		{
			"append_instruction_data", (*AppendInstructionData)(nil),
		},
		{
			"finalize_operation", (*FinalizeOperation)(nil),
		},
		{
			"clear_operation", (*ClearOperation)(nil),
		},
		{
			"schedule_batch", (*ScheduleBatch)(nil),
		},
		{
			"cancel", (*Cancel)(nil),
		},
		{
			"execute_batch", (*ExecuteBatch)(nil),
		},
		{
			"initialize_bypasser_operation", (*InitializeBypasserOperation)(nil),
		},
		{
			"initialize_bypasser_instruction", (*InitializeBypasserInstruction)(nil),
		},
		{
			"append_bypasser_instruction_data", (*AppendBypasserInstructionData)(nil),
		},
		{
			"finalize_bypasser_operation", (*FinalizeBypasserOperation)(nil),
		},
		{
			"clear_bypasser_operation", (*ClearBypasserOperation)(nil),
		},
		{
			"bypasser_execute_batch", (*BypasserExecuteBatch)(nil),
		},
		{
			"update_delay", (*UpdateDelay)(nil),
		},
		{
			"block_function_selector", (*BlockFunctionSelector)(nil),
		},
		{
			"unblock_function_selector", (*UnblockFunctionSelector)(nil),
		},
		{
			"transfer_ownership", (*TransferOwnership)(nil),
		},
		{
			"accept_ownership", (*AcceptOwnership)(nil),
		},
	},
)

func (inst *Instruction) ProgramID() ag_solanago.PublicKey {
	return ProgramID
}

func (inst *Instruction) Accounts() (out []*ag_solanago.AccountMeta) {
	return inst.Impl.(ag_solanago.AccountsGettable).GetAccounts()
}

func (inst *Instruction) Data() ([]byte, error) {
	buf := new(bytes.Buffer)
	if err := ag_binary.NewBorshEncoder(buf).Encode(inst); err != nil {
		return nil, fmt.Errorf("unable to encode instruction: %w", err)
	}
	return buf.Bytes(), nil
}

func (inst *Instruction) TextEncode(encoder *ag_text.Encoder, option *ag_text.Option) error {
	return encoder.Encode(inst.Impl, option)
}

func (inst *Instruction) UnmarshalWithDecoder(decoder *ag_binary.Decoder) error {
	return inst.BaseVariant.UnmarshalBinaryVariant(decoder, InstructionImplDef)
}

func (inst *Instruction) MarshalWithEncoder(encoder *ag_binary.Encoder) error {
	err := encoder.WriteBytes(inst.TypeID.Bytes(), false)
	if err != nil {
		return fmt.Errorf("unable to write variant type: %w", err)
	}
	return encoder.Encode(inst.Impl)
}

func registryDecodeInstruction(accounts []*ag_solanago.AccountMeta, data []byte) (interface{}, error) {
	inst, err := DecodeInstruction(accounts, data)
	if err != nil {
		return nil, err
	}
	return inst, nil
}

func DecodeInstruction(accounts []*ag_solanago.AccountMeta, data []byte) (*Instruction, error) {
	inst := new(Instruction)
	if err := ag_binary.NewBorshDecoder(data).Decode(inst); err != nil {
		return nil, fmt.Errorf("unable to decode instruction: %w", err)
	}
	if v, ok := inst.Impl.(ag_solanago.AccountsSettable); ok {
		err := v.SetAccounts(accounts)
		if err != nil {
			return nil, fmt.Errorf("unable to set accounts for instruction: %w", err)
		}
	}
	return inst, nil
}
