// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package timelock

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Initialize a bypasser operation.
//
// Bypasser operations have no predecessor and can be executed without delay.
//
// # Parameters
//
// - `ctx`: The context containing the bypasser operation account.
// - `_timelock_id`: The timelock identifier.
// - `id`: The operation identifier.
// - `salt`: A salt value for PDA derivation.
// - `instruction_count`: The number of instructions to be added.
type InitializeBypasserOperation struct {
	TimelockId       *[32]uint8
	Id               *[32]uint8
	Salt             *[32]uint8
	InstructionCount *uint32

	// [0] = [WRITE] operation
	//
	// [1] = [] config
	//
	// [2] = [] roleAccessController
	//
	// [3] = [WRITE, SIGNER] authority
	//
	// [4] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewInitializeBypasserOperationInstructionBuilder creates a new `InitializeBypasserOperation` instruction builder.
func NewInitializeBypasserOperationInstructionBuilder() *InitializeBypasserOperation {
	nd := &InitializeBypasserOperation{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 5),
	}
	return nd
}

// SetTimelockId sets the "timelockId" parameter.
func (inst *InitializeBypasserOperation) SetTimelockId(timelockId [32]uint8) *InitializeBypasserOperation {
	inst.TimelockId = &timelockId
	return inst
}

// SetId sets the "id" parameter.
func (inst *InitializeBypasserOperation) SetId(id [32]uint8) *InitializeBypasserOperation {
	inst.Id = &id
	return inst
}

// SetSalt sets the "salt" parameter.
func (inst *InitializeBypasserOperation) SetSalt(salt [32]uint8) *InitializeBypasserOperation {
	inst.Salt = &salt
	return inst
}

// SetInstructionCount sets the "instructionCount" parameter.
func (inst *InitializeBypasserOperation) SetInstructionCount(instructionCount uint32) *InitializeBypasserOperation {
	inst.InstructionCount = &instructionCount
	return inst
}

// SetOperationAccount sets the "operation" account.
func (inst *InitializeBypasserOperation) SetOperationAccount(operation ag_solanago.PublicKey) *InitializeBypasserOperation {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(operation).WRITE()
	return inst
}

// GetOperationAccount gets the "operation" account.
func (inst *InitializeBypasserOperation) GetOperationAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigAccount sets the "config" account.
func (inst *InitializeBypasserOperation) SetConfigAccount(config ag_solanago.PublicKey) *InitializeBypasserOperation {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *InitializeBypasserOperation) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetRoleAccessControllerAccount sets the "roleAccessController" account.
func (inst *InitializeBypasserOperation) SetRoleAccessControllerAccount(roleAccessController ag_solanago.PublicKey) *InitializeBypasserOperation {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(roleAccessController)
	return inst
}

// GetRoleAccessControllerAccount gets the "roleAccessController" account.
func (inst *InitializeBypasserOperation) GetRoleAccessControllerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *InitializeBypasserOperation) SetAuthorityAccount(authority ag_solanago.PublicKey) *InitializeBypasserOperation {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *InitializeBypasserOperation) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *InitializeBypasserOperation) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *InitializeBypasserOperation {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *InitializeBypasserOperation) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

func (inst InitializeBypasserOperation) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_InitializeBypasserOperation,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst InitializeBypasserOperation) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *InitializeBypasserOperation) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.TimelockId == nil {
			return errors.New("TimelockId parameter is not set")
		}
		if inst.Id == nil {
			return errors.New("Id parameter is not set")
		}
		if inst.Salt == nil {
			return errors.New("Salt parameter is not set")
		}
		if inst.InstructionCount == nil {
			return errors.New("InstructionCount parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Operation is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.RoleAccessController is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *InitializeBypasserOperation) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("InitializeBypasserOperation")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=4]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("      TimelockId", *inst.TimelockId))
						paramsBranch.Child(ag_format.Param("              Id", *inst.Id))
						paramsBranch.Child(ag_format.Param("            Salt", *inst.Salt))
						paramsBranch.Child(ag_format.Param("InstructionCount", *inst.InstructionCount))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=5]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("           operation", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("              config", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("roleAccessController", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("           authority", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("       systemProgram", inst.AccountMetaSlice[4]))
					})
				})
		})
}

func (obj InitializeBypasserOperation) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `TimelockId` param:
	err = encoder.Encode(obj.TimelockId)
	if err != nil {
		return err
	}
	// Serialize `Id` param:
	err = encoder.Encode(obj.Id)
	if err != nil {
		return err
	}
	// Serialize `Salt` param:
	err = encoder.Encode(obj.Salt)
	if err != nil {
		return err
	}
	// Serialize `InstructionCount` param:
	err = encoder.Encode(obj.InstructionCount)
	if err != nil {
		return err
	}
	return nil
}
func (obj *InitializeBypasserOperation) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `TimelockId`:
	err = decoder.Decode(&obj.TimelockId)
	if err != nil {
		return err
	}
	// Deserialize `Id`:
	err = decoder.Decode(&obj.Id)
	if err != nil {
		return err
	}
	// Deserialize `Salt`:
	err = decoder.Decode(&obj.Salt)
	if err != nil {
		return err
	}
	// Deserialize `InstructionCount`:
	err = decoder.Decode(&obj.InstructionCount)
	if err != nil {
		return err
	}
	return nil
}

// NewInitializeBypasserOperationInstruction declares a new InitializeBypasserOperation instruction with the provided parameters and accounts.
func NewInitializeBypasserOperationInstruction(
	// Parameters:
	timelockId [32]uint8,
	id [32]uint8,
	salt [32]uint8,
	instructionCount uint32,
	// Accounts:
	operation ag_solanago.PublicKey,
	config ag_solanago.PublicKey,
	roleAccessController ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *InitializeBypasserOperation {
	return NewInitializeBypasserOperationInstructionBuilder().
		SetTimelockId(timelockId).
		SetId(id).
		SetSalt(salt).
		SetInstructionCount(instructionCount).
		SetOperationAccount(operation).
		SetConfigAccount(config).
		SetRoleAccessControllerAccount(roleAccessController).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
