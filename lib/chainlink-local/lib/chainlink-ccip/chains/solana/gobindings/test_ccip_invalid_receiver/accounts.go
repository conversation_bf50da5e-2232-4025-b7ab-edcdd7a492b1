// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package test_ccip_invalid_receiver

import (
	"fmt"
	ag_binary "github.com/gagliardetto/binary"
)

type AllowedOfframp struct{}

var AllowedOfframpDiscriminator = [8]byte{247, 97, 179, 16, 207, 36, 236, 132}

func (obj <PERSON>fframp) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(AllowedOfframpDiscriminator[:], false)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AllowedOfframp) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(AllowedOfframpDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[247 97 179 16 207 36 236 132]",
				fmt.Sprint(discriminator[:]))
		}
	}
	return nil
}

type Counter struct {
	Value uint8
}

var CounterDiscriminator = [8]byte{255, 176, 4, 245, 188, 253, 124, 25}

func (obj Counter) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(CounterDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Value` param:
	err = encoder.Encode(obj.Value)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Counter) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(CounterDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[255 176 4 245 188 253 124 25]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Value`:
	err = decoder.Decode(&obj.Value)
	if err != nil {
		return err
	}
	return nil
}
