// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package test_ccip_invalid_receiver

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// PoolProxyReleaseOrMint is the `poolProxyReleaseOrMint` instruction.
type PoolProxyReleaseOrMint struct {
	ReleaseOrMint *ReleaseOrMintInV1

	// [0] = [] testPool
	// ··········· CHECK
	//
	// [1] = [] cpiSigner
	// ··········· CHECK
	//
	// [2] = [] offrampProgram
	//
	// [3] = [] allowedOfframp
	// ··········· CHECK
	//
	// [4] = [WRITE] state
	// ··········· CHECK
	//
	// [5] = [] tokenProgram
	// ··········· CHECK
	//
	// [6] = [WRITE] mint
	//
	// [7] = [] poolSigner
	// ··········· CHECK
	//
	// [8] = [WRITE] poolTokenAccount
	//
	// [9] = [WRITE] chainConfig
	// ··········· CHECK
	//
	// [10] = [] rmnRemote
	// ··········· CHECK
	//
	// [11] = [] rmnRemoteCurses
	// ··········· CHECK
	//
	// [12] = [] rmnRemoteConfig
	// ··········· CHECK
	//
	// [13] = [WRITE] receiverTokenAccount
	// ··········· CHECK
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewPoolProxyReleaseOrMintInstructionBuilder creates a new `PoolProxyReleaseOrMint` instruction builder.
func NewPoolProxyReleaseOrMintInstructionBuilder() *PoolProxyReleaseOrMint {
	nd := &PoolProxyReleaseOrMint{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 14),
	}
	return nd
}

// SetReleaseOrMint sets the "releaseOrMint" parameter.
func (inst *PoolProxyReleaseOrMint) SetReleaseOrMint(releaseOrMint ReleaseOrMintInV1) *PoolProxyReleaseOrMint {
	inst.ReleaseOrMint = &releaseOrMint
	return inst
}

// SetTestPoolAccount sets the "testPool" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetTestPoolAccount(testPool ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(testPool)
	return inst
}

// GetTestPoolAccount gets the "testPool" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetTestPoolAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetCpiSignerAccount sets the "cpiSigner" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetCpiSignerAccount(cpiSigner ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(cpiSigner)
	return inst
}

// GetCpiSignerAccount gets the "cpiSigner" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetCpiSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetOfframpProgramAccount sets the "offrampProgram" account.
func (inst *PoolProxyReleaseOrMint) SetOfframpProgramAccount(offrampProgram ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(offrampProgram)
	return inst
}

// GetOfframpProgramAccount gets the "offrampProgram" account.
func (inst *PoolProxyReleaseOrMint) GetOfframpProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetAllowedOfframpAccount sets the "allowedOfframp" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetAllowedOfframpAccount(allowedOfframp ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(allowedOfframp)
	return inst
}

// GetAllowedOfframpAccount gets the "allowedOfframp" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetAllowedOfframpAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetStateAccount sets the "state" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetStateAccount(state ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(state).WRITE()
	return inst
}

// GetStateAccount gets the "state" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetTokenProgramAccount sets the "tokenProgram" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetTokenProgramAccount(tokenProgram ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(tokenProgram)
	return inst
}

// GetTokenProgramAccount gets the "tokenProgram" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetTokenProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetMintAccount sets the "mint" account.
func (inst *PoolProxyReleaseOrMint) SetMintAccount(mint ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(mint).WRITE()
	return inst
}

// GetMintAccount gets the "mint" account.
func (inst *PoolProxyReleaseOrMint) GetMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

// SetPoolSignerAccount sets the "poolSigner" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetPoolSignerAccount(poolSigner ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[7] = ag_solanago.Meta(poolSigner)
	return inst
}

// GetPoolSignerAccount gets the "poolSigner" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetPoolSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[7]
}

// SetPoolTokenAccountAccount sets the "poolTokenAccount" account.
func (inst *PoolProxyReleaseOrMint) SetPoolTokenAccountAccount(poolTokenAccount ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[8] = ag_solanago.Meta(poolTokenAccount).WRITE()
	return inst
}

// GetPoolTokenAccountAccount gets the "poolTokenAccount" account.
func (inst *PoolProxyReleaseOrMint) GetPoolTokenAccountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[8]
}

// SetChainConfigAccount sets the "chainConfig" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetChainConfigAccount(chainConfig ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[9] = ag_solanago.Meta(chainConfig).WRITE()
	return inst
}

// GetChainConfigAccount gets the "chainConfig" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetChainConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[9]
}

// SetRmnRemoteAccount sets the "rmnRemote" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetRmnRemoteAccount(rmnRemote ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[10] = ag_solanago.Meta(rmnRemote)
	return inst
}

// GetRmnRemoteAccount gets the "rmnRemote" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetRmnRemoteAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[10]
}

// SetRmnRemoteCursesAccount sets the "rmnRemoteCurses" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetRmnRemoteCursesAccount(rmnRemoteCurses ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[11] = ag_solanago.Meta(rmnRemoteCurses)
	return inst
}

// GetRmnRemoteCursesAccount gets the "rmnRemoteCurses" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetRmnRemoteCursesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[11]
}

// SetRmnRemoteConfigAccount sets the "rmnRemoteConfig" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetRmnRemoteConfigAccount(rmnRemoteConfig ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[12] = ag_solanago.Meta(rmnRemoteConfig)
	return inst
}

// GetRmnRemoteConfigAccount gets the "rmnRemoteConfig" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetRmnRemoteConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[12]
}

// SetReceiverTokenAccountAccount sets the "receiverTokenAccount" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) SetReceiverTokenAccountAccount(receiverTokenAccount ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	inst.AccountMetaSlice[13] = ag_solanago.Meta(receiverTokenAccount).WRITE()
	return inst
}

// GetReceiverTokenAccountAccount gets the "receiverTokenAccount" account.
// CHECK
func (inst *PoolProxyReleaseOrMint) GetReceiverTokenAccountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[13]
}

func (inst PoolProxyReleaseOrMint) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_PoolProxyReleaseOrMint,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst PoolProxyReleaseOrMint) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *PoolProxyReleaseOrMint) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.ReleaseOrMint == nil {
			return errors.New("ReleaseOrMint parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.TestPool is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.CpiSigner is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.OfframpProgram is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.AllowedOfframp is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.State is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.TokenProgram is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.Mint is not set")
		}
		if inst.AccountMetaSlice[7] == nil {
			return errors.New("accounts.PoolSigner is not set")
		}
		if inst.AccountMetaSlice[8] == nil {
			return errors.New("accounts.PoolTokenAccount is not set")
		}
		if inst.AccountMetaSlice[9] == nil {
			return errors.New("accounts.ChainConfig is not set")
		}
		if inst.AccountMetaSlice[10] == nil {
			return errors.New("accounts.RmnRemote is not set")
		}
		if inst.AccountMetaSlice[11] == nil {
			return errors.New("accounts.RmnRemoteCurses is not set")
		}
		if inst.AccountMetaSlice[12] == nil {
			return errors.New("accounts.RmnRemoteConfig is not set")
		}
		if inst.AccountMetaSlice[13] == nil {
			return errors.New("accounts.ReceiverTokenAccount is not set")
		}
	}
	return nil
}

func (inst *PoolProxyReleaseOrMint) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("PoolProxyReleaseOrMint")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("ReleaseOrMint", *inst.ReleaseOrMint))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=14]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("       testPool", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("      cpiSigner", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta(" offrampProgram", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta(" allowedOfframp", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("          state", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("   tokenProgram", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("           mint", inst.AccountMetaSlice[6]))
						accountsBranch.Child(ag_format.Meta("     poolSigner", inst.AccountMetaSlice[7]))
						accountsBranch.Child(ag_format.Meta("      poolToken", inst.AccountMetaSlice[8]))
						accountsBranch.Child(ag_format.Meta("    chainConfig", inst.AccountMetaSlice[9]))
						accountsBranch.Child(ag_format.Meta("      rmnRemote", inst.AccountMetaSlice[10]))
						accountsBranch.Child(ag_format.Meta("rmnRemoteCurses", inst.AccountMetaSlice[11]))
						accountsBranch.Child(ag_format.Meta("rmnRemoteConfig", inst.AccountMetaSlice[12]))
						accountsBranch.Child(ag_format.Meta("  receiverToken", inst.AccountMetaSlice[13]))
					})
				})
		})
}

func (obj PoolProxyReleaseOrMint) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `ReleaseOrMint` param:
	err = encoder.Encode(obj.ReleaseOrMint)
	if err != nil {
		return err
	}
	return nil
}
func (obj *PoolProxyReleaseOrMint) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `ReleaseOrMint`:
	err = decoder.Decode(&obj.ReleaseOrMint)
	if err != nil {
		return err
	}
	return nil
}

// NewPoolProxyReleaseOrMintInstruction declares a new PoolProxyReleaseOrMint instruction with the provided parameters and accounts.
func NewPoolProxyReleaseOrMintInstruction(
	// Parameters:
	releaseOrMint ReleaseOrMintInV1,
	// Accounts:
	testPool ag_solanago.PublicKey,
	cpiSigner ag_solanago.PublicKey,
	offrampProgram ag_solanago.PublicKey,
	allowedOfframp ag_solanago.PublicKey,
	state ag_solanago.PublicKey,
	tokenProgram ag_solanago.PublicKey,
	mint ag_solanago.PublicKey,
	poolSigner ag_solanago.PublicKey,
	poolTokenAccount ag_solanago.PublicKey,
	chainConfig ag_solanago.PublicKey,
	rmnRemote ag_solanago.PublicKey,
	rmnRemoteCurses ag_solanago.PublicKey,
	rmnRemoteConfig ag_solanago.PublicKey,
	receiverTokenAccount ag_solanago.PublicKey) *PoolProxyReleaseOrMint {
	return NewPoolProxyReleaseOrMintInstructionBuilder().
		SetReleaseOrMint(releaseOrMint).
		SetTestPoolAccount(testPool).
		SetCpiSignerAccount(cpiSigner).
		SetOfframpProgramAccount(offrampProgram).
		SetAllowedOfframpAccount(allowedOfframp).
		SetStateAccount(state).
		SetTokenProgramAccount(tokenProgram).
		SetMintAccount(mint).
		SetPoolSignerAccount(poolSigner).
		SetPoolTokenAccountAccount(poolTokenAccount).
		SetChainConfigAccount(chainConfig).
		SetRmnRemoteAccount(rmnRemote).
		SetRmnRemoteCursesAccount(rmnRemoteCurses).
		SetRmnRemoteConfigAccount(rmnRemoteConfig).
		SetReceiverTokenAccountAccount(receiverTokenAccount)
}
