// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package external_program_cpi_stub

import (
	"fmt"
	ag_binary "github.com/gagliardetto/binary"
)

type Value struct {
	Value uint8
}

var ValueDiscriminator = [8]byte{135, 158, 244, 117, 72, 203, 24, 194}

func (obj Value) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(ValueDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Value` param:
	err = encoder.Encode(obj.Value)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Value) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(ValueDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[135 158 244 117 72 203 24 194]",
				fmt.<PERSON>(discriminator[:]))
		}
	}
	// Deserialize `Value`:
	err = decoder.Decode(&obj.Value)
	if err != nil {
		return err
	}
	return nil
}
