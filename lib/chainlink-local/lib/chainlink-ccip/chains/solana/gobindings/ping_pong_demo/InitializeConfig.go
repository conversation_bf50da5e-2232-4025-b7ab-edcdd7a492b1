// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ping_pong_demo

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// InitializeConfig is the `initializeConfig` instruction.
type InitializeConfig struct {
	Router                   *ag_solanago.PublicKey
	CounterpartChainSelector *uint64
	CounterpartAddress       *[]byte
	IsPaused                 *bool
	ExtraArgs                *[]byte

	// [0] = [WRITE] config
	//
	// [1] = [] feeTokenMint
	//
	// [2] = [WRITE, SIGNER] authority
	//
	// [3] = [] systemProgram
	//
	// [4] = [] program
	//
	// [5] = [] programData
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewInitializeConfigInstructionBuilder creates a new `InitializeConfig` instruction builder.
func NewInitializeConfigInstructionBuilder() *InitializeConfig {
	nd := &InitializeConfig{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 6),
	}
	return nd
}

// SetRouter sets the "router" parameter.
func (inst *InitializeConfig) SetRouter(router ag_solanago.PublicKey) *InitializeConfig {
	inst.Router = &router
	return inst
}

// SetCounterpartChainSelector sets the "counterpartChainSelector" parameter.
func (inst *InitializeConfig) SetCounterpartChainSelector(counterpartChainSelector uint64) *InitializeConfig {
	inst.CounterpartChainSelector = &counterpartChainSelector
	return inst
}

// SetCounterpartAddress sets the "counterpartAddress" parameter.
func (inst *InitializeConfig) SetCounterpartAddress(counterpartAddress []byte) *InitializeConfig {
	inst.CounterpartAddress = &counterpartAddress
	return inst
}

// SetIsPaused sets the "isPaused" parameter.
func (inst *InitializeConfig) SetIsPaused(isPaused bool) *InitializeConfig {
	inst.IsPaused = &isPaused
	return inst
}

// SetExtraArgs sets the "extraArgs" parameter.
func (inst *InitializeConfig) SetExtraArgs(extraArgs []byte) *InitializeConfig {
	inst.ExtraArgs = &extraArgs
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *InitializeConfig) SetConfigAccount(config ag_solanago.PublicKey) *InitializeConfig {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config).WRITE()
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *InitializeConfig) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetFeeTokenMintAccount sets the "feeTokenMint" account.
func (inst *InitializeConfig) SetFeeTokenMintAccount(feeTokenMint ag_solanago.PublicKey) *InitializeConfig {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(feeTokenMint)
	return inst
}

// GetFeeTokenMintAccount gets the "feeTokenMint" account.
func (inst *InitializeConfig) GetFeeTokenMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *InitializeConfig) SetAuthorityAccount(authority ag_solanago.PublicKey) *InitializeConfig {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *InitializeConfig) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *InitializeConfig) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *InitializeConfig {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *InitializeConfig) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetProgramAccount sets the "program" account.
func (inst *InitializeConfig) SetProgramAccount(program ag_solanago.PublicKey) *InitializeConfig {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(program)
	return inst
}

// GetProgramAccount gets the "program" account.
func (inst *InitializeConfig) GetProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetProgramDataAccount sets the "programData" account.
func (inst *InitializeConfig) SetProgramDataAccount(programData ag_solanago.PublicKey) *InitializeConfig {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(programData)
	return inst
}

// GetProgramDataAccount gets the "programData" account.
func (inst *InitializeConfig) GetProgramDataAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

func (inst InitializeConfig) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_InitializeConfig,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst InitializeConfig) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *InitializeConfig) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.Router == nil {
			return errors.New("Router parameter is not set")
		}
		if inst.CounterpartChainSelector == nil {
			return errors.New("CounterpartChainSelector parameter is not set")
		}
		if inst.CounterpartAddress == nil {
			return errors.New("CounterpartAddress parameter is not set")
		}
		if inst.IsPaused == nil {
			return errors.New("IsPaused parameter is not set")
		}
		if inst.ExtraArgs == nil {
			return errors.New("ExtraArgs parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.FeeTokenMint is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.Program is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.ProgramData is not set")
		}
	}
	return nil
}

func (inst *InitializeConfig) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("InitializeConfig")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=5]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("                  Router", *inst.Router))
						paramsBranch.Child(ag_format.Param("CounterpartChainSelector", *inst.CounterpartChainSelector))
						paramsBranch.Child(ag_format.Param("      CounterpartAddress", *inst.CounterpartAddress))
						paramsBranch.Child(ag_format.Param("                IsPaused", *inst.IsPaused))
						paramsBranch.Child(ag_format.Param("               ExtraArgs", *inst.ExtraArgs))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=6]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("       config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta(" feeTokenMint", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("    authority", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("systemProgram", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("      program", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("  programData", inst.AccountMetaSlice[5]))
					})
				})
		})
}

func (obj InitializeConfig) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Router` param:
	err = encoder.Encode(obj.Router)
	if err != nil {
		return err
	}
	// Serialize `CounterpartChainSelector` param:
	err = encoder.Encode(obj.CounterpartChainSelector)
	if err != nil {
		return err
	}
	// Serialize `CounterpartAddress` param:
	err = encoder.Encode(obj.CounterpartAddress)
	if err != nil {
		return err
	}
	// Serialize `IsPaused` param:
	err = encoder.Encode(obj.IsPaused)
	if err != nil {
		return err
	}
	// Serialize `ExtraArgs` param:
	err = encoder.Encode(obj.ExtraArgs)
	if err != nil {
		return err
	}
	return nil
}
func (obj *InitializeConfig) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Router`:
	err = decoder.Decode(&obj.Router)
	if err != nil {
		return err
	}
	// Deserialize `CounterpartChainSelector`:
	err = decoder.Decode(&obj.CounterpartChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `CounterpartAddress`:
	err = decoder.Decode(&obj.CounterpartAddress)
	if err != nil {
		return err
	}
	// Deserialize `IsPaused`:
	err = decoder.Decode(&obj.IsPaused)
	if err != nil {
		return err
	}
	// Deserialize `ExtraArgs`:
	err = decoder.Decode(&obj.ExtraArgs)
	if err != nil {
		return err
	}
	return nil
}

// NewInitializeConfigInstruction declares a new InitializeConfig instruction with the provided parameters and accounts.
func NewInitializeConfigInstruction(
	// Parameters:
	router ag_solanago.PublicKey,
	counterpartChainSelector uint64,
	counterpartAddress []byte,
	isPaused bool,
	extraArgs []byte,
	// Accounts:
	config ag_solanago.PublicKey,
	feeTokenMint ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey,
	program ag_solanago.PublicKey,
	programData ag_solanago.PublicKey) *InitializeConfig {
	return NewInitializeConfigInstructionBuilder().
		SetRouter(router).
		SetCounterpartChainSelector(counterpartChainSelector).
		SetCounterpartAddress(counterpartAddress).
		SetIsPaused(isPaused).
		SetExtraArgs(extraArgs).
		SetConfigAccount(config).
		SetFeeTokenMintAccount(feeTokenMint).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram).
		SetProgramAccount(program).
		SetProgramDataAccount(programData)
}
