// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ping_pong_demo

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Initialize is the `initialize` instruction.
type Initialize struct {

	// [0] = [] config
	//
	// [1] = [WRITE] nameVersion
	//
	// [2] = [] routerFeeBillingSigner
	// ··········· CHECK
	//
	// [3] = [] feeTokenProgram
	//
	// [4] = [] feeTokenMint
	//
	// [5] = [WRITE] feeTokenAta
	//
	// [6] = [] ccipSendSigner
	// ··········· CHECK
	//
	// [7] = [WRITE, SIGNER] authority
	//
	// [8] = [] associatedTokenProgram
	//
	// [9] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewInitializeInstructionBuilder creates a new `Initialize` instruction builder.
func NewInitializeInstructionBuilder() *Initialize {
	nd := &Initialize{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 10),
	}
	return nd
}

// SetConfigAccount sets the "config" account.
func (inst *Initialize) SetConfigAccount(config ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *Initialize) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetNameVersionAccount sets the "nameVersion" account.
func (inst *Initialize) SetNameVersionAccount(nameVersion ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(nameVersion).WRITE()
	return inst
}

// GetNameVersionAccount gets the "nameVersion" account.
func (inst *Initialize) GetNameVersionAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetRouterFeeBillingSignerAccount sets the "routerFeeBillingSigner" account.
// CHECK
func (inst *Initialize) SetRouterFeeBillingSignerAccount(routerFeeBillingSigner ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(routerFeeBillingSigner)
	return inst
}

// GetRouterFeeBillingSignerAccount gets the "routerFeeBillingSigner" account.
// CHECK
func (inst *Initialize) GetRouterFeeBillingSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetFeeTokenProgramAccount sets the "feeTokenProgram" account.
func (inst *Initialize) SetFeeTokenProgramAccount(feeTokenProgram ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(feeTokenProgram)
	return inst
}

// GetFeeTokenProgramAccount gets the "feeTokenProgram" account.
func (inst *Initialize) GetFeeTokenProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetFeeTokenMintAccount sets the "feeTokenMint" account.
func (inst *Initialize) SetFeeTokenMintAccount(feeTokenMint ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(feeTokenMint)
	return inst
}

// GetFeeTokenMintAccount gets the "feeTokenMint" account.
func (inst *Initialize) GetFeeTokenMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetFeeTokenAtaAccount sets the "feeTokenAta" account.
func (inst *Initialize) SetFeeTokenAtaAccount(feeTokenAta ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(feeTokenAta).WRITE()
	return inst
}

// GetFeeTokenAtaAccount gets the "feeTokenAta" account.
func (inst *Initialize) GetFeeTokenAtaAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetCcipSendSignerAccount sets the "ccipSendSigner" account.
// CHECK
func (inst *Initialize) SetCcipSendSignerAccount(ccipSendSigner ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(ccipSendSigner)
	return inst
}

// GetCcipSendSignerAccount gets the "ccipSendSigner" account.
// CHECK
func (inst *Initialize) GetCcipSendSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *Initialize) SetAuthorityAccount(authority ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[7] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *Initialize) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[7]
}

// SetAssociatedTokenProgramAccount sets the "associatedTokenProgram" account.
func (inst *Initialize) SetAssociatedTokenProgramAccount(associatedTokenProgram ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[8] = ag_solanago.Meta(associatedTokenProgram)
	return inst
}

// GetAssociatedTokenProgramAccount gets the "associatedTokenProgram" account.
func (inst *Initialize) GetAssociatedTokenProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[8]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *Initialize) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *Initialize {
	inst.AccountMetaSlice[9] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *Initialize) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[9]
}

func (inst Initialize) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_Initialize,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst Initialize) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *Initialize) Validate() error {
	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.NameVersion is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.RouterFeeBillingSigner is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.FeeTokenProgram is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.FeeTokenMint is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.FeeTokenAta is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.CcipSendSigner is not set")
		}
		if inst.AccountMetaSlice[7] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[8] == nil {
			return errors.New("accounts.AssociatedTokenProgram is not set")
		}
		if inst.AccountMetaSlice[9] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *Initialize) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("Initialize")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=0]").ParentFunc(func(paramsBranch ag_treeout.Branches) {})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=10]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("                config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("           nameVersion", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("routerFeeBillingSigner", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("       feeTokenProgram", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("          feeTokenMint", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("           feeTokenAta", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("        ccipSendSigner", inst.AccountMetaSlice[6]))
						accountsBranch.Child(ag_format.Meta("             authority", inst.AccountMetaSlice[7]))
						accountsBranch.Child(ag_format.Meta("associatedTokenProgram", inst.AccountMetaSlice[8]))
						accountsBranch.Child(ag_format.Meta("         systemProgram", inst.AccountMetaSlice[9]))
					})
				})
		})
}

func (obj Initialize) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	return nil
}
func (obj *Initialize) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	return nil
}

// NewInitializeInstruction declares a new Initialize instruction with the provided parameters and accounts.
func NewInitializeInstruction(
	// Accounts:
	config ag_solanago.PublicKey,
	nameVersion ag_solanago.PublicKey,
	routerFeeBillingSigner ag_solanago.PublicKey,
	feeTokenProgram ag_solanago.PublicKey,
	feeTokenMint ag_solanago.PublicKey,
	feeTokenAta ag_solanago.PublicKey,
	ccipSendSigner ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	associatedTokenProgram ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *Initialize {
	return NewInitializeInstructionBuilder().
		SetConfigAccount(config).
		SetNameVersionAccount(nameVersion).
		SetRouterFeeBillingSignerAccount(routerFeeBillingSigner).
		SetFeeTokenProgramAccount(feeTokenProgram).
		SetFeeTokenMintAccount(feeTokenMint).
		SetFeeTokenAtaAccount(feeTokenAta).
		SetCcipSendSignerAccount(ccipSendSigner).
		SetAuthorityAccount(authority).
		SetAssociatedTokenProgramAccount(associatedTokenProgram).
		SetSystemProgramAccount(systemProgram)
}
