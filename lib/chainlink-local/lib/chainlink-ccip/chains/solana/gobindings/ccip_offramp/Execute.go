// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_offramp

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Executes a message on the destination chain.
//
// The method name needs to be execute with Anchor encoding.
//
// This function is called by the OffChain when executing one Report to the SVM Router.
// In this Flow only one message is sent, the Execution Report. This is different as EVM does,
// this is because there is no try/catch mechanism to allow batch execution.
// This message validates that the Merkle Tree Proof of the given message is correct and is stored in the Commit Report Account.
// The message must be untouched to be executed.
// This message emits the event ExecutionStateChanged with the new state of the message.
// Finally, executes the CPI instruction to the receiver program in the ccip_receive message.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for the execute.
// * `raw_execution_report` - the serialized execution report containing only one message and proofs
// * `report_context_byte_words` - report_context after execution_report to match context for manually execute (proper decoding order)
// *  consists of:
// * report_context_byte_words[0]: ConfigDigest
// * report_context_byte_words[1]: 24 byte padding, 8 byte sequence number
type Execute struct {
	RawExecutionReport     *[]byte
	ReportContextByteWords *[2][32]uint8
	TokenIndexes           *[]byte

	// [0] = [] config
	//
	// [1] = [] referenceAddresses
	//
	// [2] = [] sourceChain
	//
	// [3] = [WRITE] commitReport
	//
	// [4] = [] offramp
	//
	// [5] = [] allowedOfframp
	// ··········· CHECK PDA of the router program verifying the signer is an allowed offramp.
	// ··········· If PDA does not exist, the router doesn't allow this offramp. This is just used
	// ··········· so that token pools and receivers can then check that the caller is an actual offramp that
	// ··········· has been registered in the router as such for that source chain.
	//
	// [6] = [WRITE, SIGNER] authority
	//
	// [7] = [] systemProgram
	//
	// [8] = [] sysvarInstructions
	//
	// [9] = [] rmnRemote
	//
	// [10] = [] rmnRemoteCurses
	//
	// [11] = [] rmnRemoteConfig
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewExecuteInstructionBuilder creates a new `Execute` instruction builder.
func NewExecuteInstructionBuilder() *Execute {
	nd := &Execute{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 12),
	}
	return nd
}

// SetRawExecutionReport sets the "rawExecutionReport" parameter.
func (inst *Execute) SetRawExecutionReport(rawExecutionReport []byte) *Execute {
	inst.RawExecutionReport = &rawExecutionReport
	return inst
}

// SetReportContextByteWords sets the "reportContextByteWords" parameter.
func (inst *Execute) SetReportContextByteWords(reportContextByteWords [2][32]uint8) *Execute {
	inst.ReportContextByteWords = &reportContextByteWords
	return inst
}

// SetTokenIndexes sets the "tokenIndexes" parameter.
func (inst *Execute) SetTokenIndexes(tokenIndexes []byte) *Execute {
	inst.TokenIndexes = &tokenIndexes
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *Execute) SetConfigAccount(config ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *Execute) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetReferenceAddressesAccount sets the "referenceAddresses" account.
func (inst *Execute) SetReferenceAddressesAccount(referenceAddresses ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(referenceAddresses)
	return inst
}

// GetReferenceAddressesAccount gets the "referenceAddresses" account.
func (inst *Execute) GetReferenceAddressesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetSourceChainAccount sets the "sourceChain" account.
func (inst *Execute) SetSourceChainAccount(sourceChain ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(sourceChain)
	return inst
}

// GetSourceChainAccount gets the "sourceChain" account.
func (inst *Execute) GetSourceChainAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetCommitReportAccount sets the "commitReport" account.
func (inst *Execute) SetCommitReportAccount(commitReport ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(commitReport).WRITE()
	return inst
}

// GetCommitReportAccount gets the "commitReport" account.
func (inst *Execute) GetCommitReportAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetOfframpAccount sets the "offramp" account.
func (inst *Execute) SetOfframpAccount(offramp ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(offramp)
	return inst
}

// GetOfframpAccount gets the "offramp" account.
func (inst *Execute) GetOfframpAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetAllowedOfframpAccount sets the "allowedOfframp" account.
// CHECK PDA of the router program verifying the signer is an allowed offramp.
// If PDA does not exist, the router doesn't allow this offramp. This is just used
// so that token pools and receivers can then check that the caller is an actual offramp that
// has been registered in the router as such for that source chain.
func (inst *Execute) SetAllowedOfframpAccount(allowedOfframp ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(allowedOfframp)
	return inst
}

// GetAllowedOfframpAccount gets the "allowedOfframp" account.
// CHECK PDA of the router program verifying the signer is an allowed offramp.
// If PDA does not exist, the router doesn't allow this offramp. This is just used
// so that token pools and receivers can then check that the caller is an actual offramp that
// has been registered in the router as such for that source chain.
func (inst *Execute) GetAllowedOfframpAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *Execute) SetAuthorityAccount(authority ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *Execute) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *Execute) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[7] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *Execute) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[7]
}

// SetSysvarInstructionsAccount sets the "sysvarInstructions" account.
func (inst *Execute) SetSysvarInstructionsAccount(sysvarInstructions ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[8] = ag_solanago.Meta(sysvarInstructions)
	return inst
}

// GetSysvarInstructionsAccount gets the "sysvarInstructions" account.
func (inst *Execute) GetSysvarInstructionsAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[8]
}

// SetRmnRemoteAccount sets the "rmnRemote" account.
func (inst *Execute) SetRmnRemoteAccount(rmnRemote ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[9] = ag_solanago.Meta(rmnRemote)
	return inst
}

// GetRmnRemoteAccount gets the "rmnRemote" account.
func (inst *Execute) GetRmnRemoteAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[9]
}

// SetRmnRemoteCursesAccount sets the "rmnRemoteCurses" account.
func (inst *Execute) SetRmnRemoteCursesAccount(rmnRemoteCurses ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[10] = ag_solanago.Meta(rmnRemoteCurses)
	return inst
}

// GetRmnRemoteCursesAccount gets the "rmnRemoteCurses" account.
func (inst *Execute) GetRmnRemoteCursesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[10]
}

// SetRmnRemoteConfigAccount sets the "rmnRemoteConfig" account.
func (inst *Execute) SetRmnRemoteConfigAccount(rmnRemoteConfig ag_solanago.PublicKey) *Execute {
	inst.AccountMetaSlice[11] = ag_solanago.Meta(rmnRemoteConfig)
	return inst
}

// GetRmnRemoteConfigAccount gets the "rmnRemoteConfig" account.
func (inst *Execute) GetRmnRemoteConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[11]
}

func (inst Execute) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_Execute,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst Execute) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *Execute) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.RawExecutionReport == nil {
			return errors.New("RawExecutionReport parameter is not set")
		}
		if inst.ReportContextByteWords == nil {
			return errors.New("ReportContextByteWords parameter is not set")
		}
		if inst.TokenIndexes == nil {
			return errors.New("TokenIndexes parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.ReferenceAddresses is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.SourceChain is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.CommitReport is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.Offramp is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.AllowedOfframp is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[7] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
		if inst.AccountMetaSlice[8] == nil {
			return errors.New("accounts.SysvarInstructions is not set")
		}
		if inst.AccountMetaSlice[9] == nil {
			return errors.New("accounts.RmnRemote is not set")
		}
		if inst.AccountMetaSlice[10] == nil {
			return errors.New("accounts.RmnRemoteCurses is not set")
		}
		if inst.AccountMetaSlice[11] == nil {
			return errors.New("accounts.RmnRemoteConfig is not set")
		}
	}
	return nil
}

func (inst *Execute) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("Execute")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=3]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("    RawExecutionReport", *inst.RawExecutionReport))
						paramsBranch.Child(ag_format.Param("ReportContextByteWords", *inst.ReportContextByteWords))
						paramsBranch.Child(ag_format.Param("          TokenIndexes", *inst.TokenIndexes))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=12]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("            config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("referenceAddresses", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("       sourceChain", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("      commitReport", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("           offramp", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("    allowedOfframp", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("         authority", inst.AccountMetaSlice[6]))
						accountsBranch.Child(ag_format.Meta("     systemProgram", inst.AccountMetaSlice[7]))
						accountsBranch.Child(ag_format.Meta("sysvarInstructions", inst.AccountMetaSlice[8]))
						accountsBranch.Child(ag_format.Meta("         rmnRemote", inst.AccountMetaSlice[9]))
						accountsBranch.Child(ag_format.Meta("   rmnRemoteCurses", inst.AccountMetaSlice[10]))
						accountsBranch.Child(ag_format.Meta("   rmnRemoteConfig", inst.AccountMetaSlice[11]))
					})
				})
		})
}

func (obj Execute) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `RawExecutionReport` param:
	err = encoder.Encode(obj.RawExecutionReport)
	if err != nil {
		return err
	}
	// Serialize `ReportContextByteWords` param:
	err = encoder.Encode(obj.ReportContextByteWords)
	if err != nil {
		return err
	}
	// Serialize `TokenIndexes` param:
	err = encoder.Encode(obj.TokenIndexes)
	if err != nil {
		return err
	}
	return nil
}
func (obj *Execute) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `RawExecutionReport`:
	err = decoder.Decode(&obj.RawExecutionReport)
	if err != nil {
		return err
	}
	// Deserialize `ReportContextByteWords`:
	err = decoder.Decode(&obj.ReportContextByteWords)
	if err != nil {
		return err
	}
	// Deserialize `TokenIndexes`:
	err = decoder.Decode(&obj.TokenIndexes)
	if err != nil {
		return err
	}
	return nil
}

// NewExecuteInstruction declares a new Execute instruction with the provided parameters and accounts.
func NewExecuteInstruction(
	// Parameters:
	rawExecutionReport []byte,
	reportContextByteWords [2][32]uint8,
	tokenIndexes []byte,
	// Accounts:
	config ag_solanago.PublicKey,
	referenceAddresses ag_solanago.PublicKey,
	sourceChain ag_solanago.PublicKey,
	commitReport ag_solanago.PublicKey,
	offramp ag_solanago.PublicKey,
	allowedOfframp ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey,
	sysvarInstructions ag_solanago.PublicKey,
	rmnRemote ag_solanago.PublicKey,
	rmnRemoteCurses ag_solanago.PublicKey,
	rmnRemoteConfig ag_solanago.PublicKey) *Execute {
	return NewExecuteInstructionBuilder().
		SetRawExecutionReport(rawExecutionReport).
		SetReportContextByteWords(reportContextByteWords).
		SetTokenIndexes(tokenIndexes).
		SetConfigAccount(config).
		SetReferenceAddressesAccount(referenceAddresses).
		SetSourceChainAccount(sourceChain).
		SetCommitReportAccount(commitReport).
		SetOfframpAccount(offramp).
		SetAllowedOfframpAccount(allowedOfframp).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram).
		SetSysvarInstructionsAccount(sysvarInstructions).
		SetRmnRemoteAccount(rmnRemote).
		SetRmnRemoteCursesAccount(rmnRemoteCurses).
		SetRmnRemoteConfigAccount(rmnRemoteConfig)
}
