// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_offramp

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Updates reference addresses in the offramp contract, such as
// the CCIP router, Fe<PERSON> Quoter, and the Offramp Lookup Table.
// Only the Admin may update these addresses.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for updating the reference addresses.
// * `router` - The router address to be set.
// * `fee_quoter` - The fee_quoter address to be set.
// * `offramp_lookup_table` - The offramp_lookup_table address to be set.
// * `rmn_remote` - The rmn_remote address to be set.
type UpdateReferenceAddresses struct {
	Router             *ag_solanago.PublicKey
	FeeQuoter          *ag_solanago.PublicKey
	OfframpLookupTable *ag_solanago.PublicKey
	RmnRemote          *ag_solanago.PublicKey

	// [0] = [] config
	//
	// [1] = [WRITE] referenceAddresses
	//
	// [2] = [SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewUpdateReferenceAddressesInstructionBuilder creates a new `UpdateReferenceAddresses` instruction builder.
func NewUpdateReferenceAddressesInstructionBuilder() *UpdateReferenceAddresses {
	nd := &UpdateReferenceAddresses{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetRouter sets the "router" parameter.
func (inst *UpdateReferenceAddresses) SetRouter(router ag_solanago.PublicKey) *UpdateReferenceAddresses {
	inst.Router = &router
	return inst
}

// SetFeeQuoter sets the "feeQuoter" parameter.
func (inst *UpdateReferenceAddresses) SetFeeQuoter(feeQuoter ag_solanago.PublicKey) *UpdateReferenceAddresses {
	inst.FeeQuoter = &feeQuoter
	return inst
}

// SetOfframpLookupTable sets the "offrampLookupTable" parameter.
func (inst *UpdateReferenceAddresses) SetOfframpLookupTable(offrampLookupTable ag_solanago.PublicKey) *UpdateReferenceAddresses {
	inst.OfframpLookupTable = &offrampLookupTable
	return inst
}

// SetRmnRemote sets the "rmnRemote" parameter.
func (inst *UpdateReferenceAddresses) SetRmnRemote(rmnRemote ag_solanago.PublicKey) *UpdateReferenceAddresses {
	inst.RmnRemote = &rmnRemote
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *UpdateReferenceAddresses) SetConfigAccount(config ag_solanago.PublicKey) *UpdateReferenceAddresses {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *UpdateReferenceAddresses) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetReferenceAddressesAccount sets the "referenceAddresses" account.
func (inst *UpdateReferenceAddresses) SetReferenceAddressesAccount(referenceAddresses ag_solanago.PublicKey) *UpdateReferenceAddresses {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(referenceAddresses).WRITE()
	return inst
}

// GetReferenceAddressesAccount gets the "referenceAddresses" account.
func (inst *UpdateReferenceAddresses) GetReferenceAddressesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *UpdateReferenceAddresses) SetAuthorityAccount(authority ag_solanago.PublicKey) *UpdateReferenceAddresses {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *UpdateReferenceAddresses) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst UpdateReferenceAddresses) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_UpdateReferenceAddresses,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst UpdateReferenceAddresses) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *UpdateReferenceAddresses) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.Router == nil {
			return errors.New("Router parameter is not set")
		}
		if inst.FeeQuoter == nil {
			return errors.New("FeeQuoter parameter is not set")
		}
		if inst.OfframpLookupTable == nil {
			return errors.New("OfframpLookupTable parameter is not set")
		}
		if inst.RmnRemote == nil {
			return errors.New("RmnRemote parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.ReferenceAddresses is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *UpdateReferenceAddresses) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("UpdateReferenceAddresses")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=4]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("            Router", *inst.Router))
						paramsBranch.Child(ag_format.Param("         FeeQuoter", *inst.FeeQuoter))
						paramsBranch.Child(ag_format.Param("OfframpLookupTable", *inst.OfframpLookupTable))
						paramsBranch.Child(ag_format.Param("         RmnRemote", *inst.RmnRemote))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("            config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("referenceAddresses", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("         authority", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj UpdateReferenceAddresses) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Router` param:
	err = encoder.Encode(obj.Router)
	if err != nil {
		return err
	}
	// Serialize `FeeQuoter` param:
	err = encoder.Encode(obj.FeeQuoter)
	if err != nil {
		return err
	}
	// Serialize `OfframpLookupTable` param:
	err = encoder.Encode(obj.OfframpLookupTable)
	if err != nil {
		return err
	}
	// Serialize `RmnRemote` param:
	err = encoder.Encode(obj.RmnRemote)
	if err != nil {
		return err
	}
	return nil
}
func (obj *UpdateReferenceAddresses) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Router`:
	err = decoder.Decode(&obj.Router)
	if err != nil {
		return err
	}
	// Deserialize `FeeQuoter`:
	err = decoder.Decode(&obj.FeeQuoter)
	if err != nil {
		return err
	}
	// Deserialize `OfframpLookupTable`:
	err = decoder.Decode(&obj.OfframpLookupTable)
	if err != nil {
		return err
	}
	// Deserialize `RmnRemote`:
	err = decoder.Decode(&obj.RmnRemote)
	if err != nil {
		return err
	}
	return nil
}

// NewUpdateReferenceAddressesInstruction declares a new UpdateReferenceAddresses instruction with the provided parameters and accounts.
func NewUpdateReferenceAddressesInstruction(
	// Parameters:
	router ag_solanago.PublicKey,
	feeQuoter ag_solanago.PublicKey,
	offrampLookupTable ag_solanago.PublicKey,
	rmnRemote ag_solanago.PublicKey,
	// Accounts:
	config ag_solanago.PublicKey,
	referenceAddresses ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *UpdateReferenceAddresses {
	return NewUpdateReferenceAddressesInstructionBuilder().
		SetRouter(router).
		SetFeeQuoter(feeQuoter).
		SetOfframpLookupTable(offrampLookupTable).
		SetRmnRemote(rmnRemote).
		SetConfigAccount(config).
		SetReferenceAddressesAccount(referenceAddresses).
		SetAuthorityAccount(authority)
}
