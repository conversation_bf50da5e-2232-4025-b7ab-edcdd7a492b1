// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_offramp

import (
	"fmt"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
)

type Config struct {
	Version                    uint8
	DefaultCodeVersion         uint8
	Padding0                   [6]uint8
	SvmChainSelector           uint64
	EnableManualExecutionAfter int64
	Padding1                   [8]uint8
	Owner                      ag_solanago.PublicKey
	ProposedOwner              ag_solanago.PublicKey
	Padding2                   [8]uint8
	Ocr3                       [2]Ocr3Config
}

var ConfigDiscriminator = [8]byte{155, 12, 170, 224, 30, 250, 204, 130}

func (obj Config) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(ConfigDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `DefaultCodeVersion` param:
	err = encoder.Encode(obj.DefaultCodeVersion)
	if err != nil {
		return err
	}
	// Serialize `Padding0` param:
	err = encoder.Encode(obj.Padding0)
	if err != nil {
		return err
	}
	// Serialize `SvmChainSelector` param:
	err = encoder.Encode(obj.SvmChainSelector)
	if err != nil {
		return err
	}
	// Serialize `EnableManualExecutionAfter` param:
	err = encoder.Encode(obj.EnableManualExecutionAfter)
	if err != nil {
		return err
	}
	// Serialize `Padding1` param:
	err = encoder.Encode(obj.Padding1)
	if err != nil {
		return err
	}
	// Serialize `Owner` param:
	err = encoder.Encode(obj.Owner)
	if err != nil {
		return err
	}
	// Serialize `ProposedOwner` param:
	err = encoder.Encode(obj.ProposedOwner)
	if err != nil {
		return err
	}
	// Serialize `Padding2` param:
	err = encoder.Encode(obj.Padding2)
	if err != nil {
		return err
	}
	// Serialize `Ocr3` param:
	err = encoder.Encode(obj.Ocr3)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(ConfigDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[155 12 170 224 30 250 204 130]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `DefaultCodeVersion`:
	err = decoder.Decode(&obj.DefaultCodeVersion)
	if err != nil {
		return err
	}
	// Deserialize `Padding0`:
	err = decoder.Decode(&obj.Padding0)
	if err != nil {
		return err
	}
	// Deserialize `SvmChainSelector`:
	err = decoder.Decode(&obj.SvmChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `EnableManualExecutionAfter`:
	err = decoder.Decode(&obj.EnableManualExecutionAfter)
	if err != nil {
		return err
	}
	// Deserialize `Padding1`:
	err = decoder.Decode(&obj.Padding1)
	if err != nil {
		return err
	}
	// Deserialize `Owner`:
	err = decoder.Decode(&obj.Owner)
	if err != nil {
		return err
	}
	// Deserialize `ProposedOwner`:
	err = decoder.Decode(&obj.ProposedOwner)
	if err != nil {
		return err
	}
	// Deserialize `Padding2`:
	err = decoder.Decode(&obj.Padding2)
	if err != nil {
		return err
	}
	// Deserialize `Ocr3`:
	err = decoder.Decode(&obj.Ocr3)
	if err != nil {
		return err
	}
	return nil
}

type ReferenceAddresses struct {
	Version            uint8
	Router             ag_solanago.PublicKey
	FeeQuoter          ag_solanago.PublicKey
	OfframpLookupTable ag_solanago.PublicKey
	RmnRemote          ag_solanago.PublicKey
}

var ReferenceAddressesDiscriminator = [8]byte{99, 5, 216, 212, 250, 75, 74, 12}

func (obj ReferenceAddresses) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(ReferenceAddressesDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `Router` param:
	err = encoder.Encode(obj.Router)
	if err != nil {
		return err
	}
	// Serialize `FeeQuoter` param:
	err = encoder.Encode(obj.FeeQuoter)
	if err != nil {
		return err
	}
	// Serialize `OfframpLookupTable` param:
	err = encoder.Encode(obj.OfframpLookupTable)
	if err != nil {
		return err
	}
	// Serialize `RmnRemote` param:
	err = encoder.Encode(obj.RmnRemote)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ReferenceAddresses) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(ReferenceAddressesDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[99 5 216 212 250 75 74 12]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `Router`:
	err = decoder.Decode(&obj.Router)
	if err != nil {
		return err
	}
	// Deserialize `FeeQuoter`:
	err = decoder.Decode(&obj.FeeQuoter)
	if err != nil {
		return err
	}
	// Deserialize `OfframpLookupTable`:
	err = decoder.Decode(&obj.OfframpLookupTable)
	if err != nil {
		return err
	}
	// Deserialize `RmnRemote`:
	err = decoder.Decode(&obj.RmnRemote)
	if err != nil {
		return err
	}
	return nil
}

type GlobalState struct {
	LatestPriceSequenceNumber uint64
}

var GlobalStateDiscriminator = [8]byte{163, 46, 74, 168, 216, 123, 133, 98}

func (obj GlobalState) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(GlobalStateDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `LatestPriceSequenceNumber` param:
	err = encoder.Encode(obj.LatestPriceSequenceNumber)
	if err != nil {
		return err
	}
	return nil
}

func (obj *GlobalState) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(GlobalStateDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[163 46 74 168 216 123 133 98]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `LatestPriceSequenceNumber`:
	err = decoder.Decode(&obj.LatestPriceSequenceNumber)
	if err != nil {
		return err
	}
	return nil
}

type SourceChain struct {
	Version       uint8
	ChainSelector uint64
	State         SourceChainState
	Config        SourceChainConfig
}

var SourceChainDiscriminator = [8]byte{242, 235, 220, 98, 252, 121, 191, 216}

func (obj SourceChain) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(SourceChainDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `ChainSelector` param:
	err = encoder.Encode(obj.ChainSelector)
	if err != nil {
		return err
	}
	// Serialize `State` param:
	err = encoder.Encode(obj.State)
	if err != nil {
		return err
	}
	// Serialize `Config` param:
	err = encoder.Encode(obj.Config)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SourceChain) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(SourceChainDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[242 235 220 98 252 121 191 216]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `ChainSelector`:
	err = decoder.Decode(&obj.ChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `State`:
	err = decoder.Decode(&obj.State)
	if err != nil {
		return err
	}
	// Deserialize `Config`:
	err = decoder.Decode(&obj.Config)
	if err != nil {
		return err
	}
	return nil
}

type CommitReport struct {
	Version         uint8
	ChainSelector   uint64
	MerkleRoot      [32]uint8
	Timestamp       int64
	MinMsgNr        uint64
	MaxMsgNr        uint64
	ExecutionStates ag_binary.Uint128
}

var CommitReportDiscriminator = [8]byte{46, 231, 247, 231, 174, 68, 34, 26}

func (obj CommitReport) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Write account discriminator:
	err = encoder.WriteBytes(CommitReportDiscriminator[:], false)
	if err != nil {
		return err
	}
	// Serialize `Version` param:
	err = encoder.Encode(obj.Version)
	if err != nil {
		return err
	}
	// Serialize `ChainSelector` param:
	err = encoder.Encode(obj.ChainSelector)
	if err != nil {
		return err
	}
	// Serialize `MerkleRoot` param:
	err = encoder.Encode(obj.MerkleRoot)
	if err != nil {
		return err
	}
	// Serialize `Timestamp` param:
	err = encoder.Encode(obj.Timestamp)
	if err != nil {
		return err
	}
	// Serialize `MinMsgNr` param:
	err = encoder.Encode(obj.MinMsgNr)
	if err != nil {
		return err
	}
	// Serialize `MaxMsgNr` param:
	err = encoder.Encode(obj.MaxMsgNr)
	if err != nil {
		return err
	}
	// Serialize `ExecutionStates` param:
	err = encoder.Encode(obj.ExecutionStates)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CommitReport) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Read and check account discriminator:
	{
		discriminator, err := decoder.ReadTypeID()
		if err != nil {
			return err
		}
		if !discriminator.Equal(CommitReportDiscriminator[:]) {
			return fmt.Errorf(
				"wrong discriminator: wanted %s, got %s",
				"[46 231 247 231 174 68 34 26]",
				fmt.Sprint(discriminator[:]))
		}
	}
	// Deserialize `Version`:
	err = decoder.Decode(&obj.Version)
	if err != nil {
		return err
	}
	// Deserialize `ChainSelector`:
	err = decoder.Decode(&obj.ChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `MerkleRoot`:
	err = decoder.Decode(&obj.MerkleRoot)
	if err != nil {
		return err
	}
	// Deserialize `Timestamp`:
	err = decoder.Decode(&obj.Timestamp)
	if err != nil {
		return err
	}
	// Deserialize `MinMsgNr`:
	err = decoder.Decode(&obj.MinMsgNr)
	if err != nil {
		return err
	}
	// Deserialize `MaxMsgNr`:
	err = decoder.Decode(&obj.MaxMsgNr)
	if err != nil {
		return err
	}
	// Deserialize `ExecutionStates`:
	err = decoder.Decode(&obj.ExecutionStates)
	if err != nil {
		return err
	}
	return nil
}
