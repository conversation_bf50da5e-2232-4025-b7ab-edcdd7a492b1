// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package lockrelease_token_pool

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// SetRebalancer is the `setRebalancer` instruction.
type SetRebalancer struct {
	Rebalancer *ag_solanago.PublicKey

	// [0] = [WRITE] state
	//
	// [1] = [] mint
	//
	// [2] = [SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewSetRebalancerInstructionBuilder creates a new `SetRebalancer` instruction builder.
func NewSetRebalancerInstructionBuilder() *SetRebalancer {
	nd := &SetRebalancer{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetRebalancer sets the "rebalancer" parameter.
func (inst *SetRebalancer) SetRebalancer(rebalancer ag_solanago.PublicKey) *SetRebalancer {
	inst.Rebalancer = &rebalancer
	return inst
}

// SetStateAccount sets the "state" account.
func (inst *SetRebalancer) SetStateAccount(state ag_solanago.PublicKey) *SetRebalancer {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(state).WRITE()
	return inst
}

// GetStateAccount gets the "state" account.
func (inst *SetRebalancer) GetStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetMintAccount sets the "mint" account.
func (inst *SetRebalancer) SetMintAccount(mint ag_solanago.PublicKey) *SetRebalancer {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(mint)
	return inst
}

// GetMintAccount gets the "mint" account.
func (inst *SetRebalancer) GetMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *SetRebalancer) SetAuthorityAccount(authority ag_solanago.PublicKey) *SetRebalancer {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *SetRebalancer) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst SetRebalancer) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_SetRebalancer,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst SetRebalancer) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *SetRebalancer) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.Rebalancer == nil {
			return errors.New("Rebalancer parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.State is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Mint is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *SetRebalancer) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("SetRebalancer")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("Rebalancer", *inst.Rebalancer))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("    state", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("     mint", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("authority", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj SetRebalancer) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `Rebalancer` param:
	err = encoder.Encode(obj.Rebalancer)
	if err != nil {
		return err
	}
	return nil
}
func (obj *SetRebalancer) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `Rebalancer`:
	err = decoder.Decode(&obj.Rebalancer)
	if err != nil {
		return err
	}
	return nil
}

// NewSetRebalancerInstruction declares a new SetRebalancer instruction with the provided parameters and accounts.
func NewSetRebalancerInstruction(
	// Parameters:
	rebalancer ag_solanago.PublicKey,
	// Accounts:
	state ag_solanago.PublicKey,
	mint ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *SetRebalancer {
	return NewSetRebalancerInstructionBuilder().
		SetRebalancer(rebalancer).
		SetStateAccount(state).
		SetMintAccount(mint).
		SetAuthorityAccount(authority)
}
