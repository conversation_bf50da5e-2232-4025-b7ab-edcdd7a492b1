// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// On Ramp Flow //
// Sends a message to the destination chain.
//
// Request a message to be sent to the destination chain.
// The method name needs to be ccip_send with Anchor encoding.
// This function is called by the CCIP Sender Contract (or final user) to send a message to the CCIP Router.
// The message will be sent to the receiver on the destination chain selector.
// This message emits the event CCIPMessageSent with all the necessary data to be retrieved by the OffChain Code
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for sending the message.
// * `dest_chain_selector` - The chain selector for the destination chain.
// * `message` - The message to be sent. The size limit of data is 256 bytes.
// * `token_indexes` - Indices into the remaining accounts vector where the subslice for a token begins.
type CcipSend struct {
	DestChainSelector *uint64
	Message           *SVM2AnyMessage
	TokenIndexes      *[]byte

	// [0] = [] config
	//
	// [1] = [WRITE] destChainState
	//
	// [2] = [WRITE] nonce
	//
	// [3] = [WRITE, SIGNER] authority
	//
	// [4] = [] systemProgram
	//
	// [5] = [] feeTokenProgram
	//
	// [6] = [] feeTokenMint
	//
	// [7] = [] feeTokenUserAssociatedAccount
	// ··········· If paying with native SOL, this must be the zero address.
	//
	// [8] = [WRITE] feeTokenReceiver
	//
	// [9] = [] feeBillingSigner
	//
	// [10] = [] feeQuoter
	//
	// [11] = [] feeQuoterConfig
	//
	// [12] = [] feeQuoterDestChain
	//
	// [13] = [] feeQuoterBillingTokenConfig
	//
	// [14] = [] feeQuoterLinkTokenConfig
	//
	// [15] = [] rmnRemote
	//
	// [16] = [] rmnRemoteCurses
	//
	// [17] = [] rmnRemoteConfig
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewCcipSendInstructionBuilder creates a new `CcipSend` instruction builder.
func NewCcipSendInstructionBuilder() *CcipSend {
	nd := &CcipSend{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 18),
	}
	return nd
}

// SetDestChainSelector sets the "destChainSelector" parameter.
func (inst *CcipSend) SetDestChainSelector(destChainSelector uint64) *CcipSend {
	inst.DestChainSelector = &destChainSelector
	return inst
}

// SetMessage sets the "message" parameter.
func (inst *CcipSend) SetMessage(message SVM2AnyMessage) *CcipSend {
	inst.Message = &message
	return inst
}

// SetTokenIndexes sets the "tokenIndexes" parameter.
func (inst *CcipSend) SetTokenIndexes(tokenIndexes []byte) *CcipSend {
	inst.TokenIndexes = &tokenIndexes
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *CcipSend) SetConfigAccount(config ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *CcipSend) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetDestChainStateAccount sets the "destChainState" account.
func (inst *CcipSend) SetDestChainStateAccount(destChainState ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(destChainState).WRITE()
	return inst
}

// GetDestChainStateAccount gets the "destChainState" account.
func (inst *CcipSend) GetDestChainStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetNonceAccount sets the "nonce" account.
func (inst *CcipSend) SetNonceAccount(nonce ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(nonce).WRITE()
	return inst
}

// GetNonceAccount gets the "nonce" account.
func (inst *CcipSend) GetNonceAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *CcipSend) SetAuthorityAccount(authority ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *CcipSend) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *CcipSend) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *CcipSend) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetFeeTokenProgramAccount sets the "feeTokenProgram" account.
func (inst *CcipSend) SetFeeTokenProgramAccount(feeTokenProgram ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(feeTokenProgram)
	return inst
}

// GetFeeTokenProgramAccount gets the "feeTokenProgram" account.
func (inst *CcipSend) GetFeeTokenProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetFeeTokenMintAccount sets the "feeTokenMint" account.
func (inst *CcipSend) SetFeeTokenMintAccount(feeTokenMint ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(feeTokenMint)
	return inst
}

// GetFeeTokenMintAccount gets the "feeTokenMint" account.
func (inst *CcipSend) GetFeeTokenMintAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

// SetFeeTokenUserAssociatedAccountAccount sets the "feeTokenUserAssociatedAccount" account.
// If paying with native SOL, this must be the zero address.
func (inst *CcipSend) SetFeeTokenUserAssociatedAccountAccount(feeTokenUserAssociatedAccount ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[7] = ag_solanago.Meta(feeTokenUserAssociatedAccount)
	return inst
}

// GetFeeTokenUserAssociatedAccountAccount gets the "feeTokenUserAssociatedAccount" account.
// If paying with native SOL, this must be the zero address.
func (inst *CcipSend) GetFeeTokenUserAssociatedAccountAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[7]
}

// SetFeeTokenReceiverAccount sets the "feeTokenReceiver" account.
func (inst *CcipSend) SetFeeTokenReceiverAccount(feeTokenReceiver ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[8] = ag_solanago.Meta(feeTokenReceiver).WRITE()
	return inst
}

// GetFeeTokenReceiverAccount gets the "feeTokenReceiver" account.
func (inst *CcipSend) GetFeeTokenReceiverAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[8]
}

// SetFeeBillingSignerAccount sets the "feeBillingSigner" account.
func (inst *CcipSend) SetFeeBillingSignerAccount(feeBillingSigner ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[9] = ag_solanago.Meta(feeBillingSigner)
	return inst
}

// GetFeeBillingSignerAccount gets the "feeBillingSigner" account.
func (inst *CcipSend) GetFeeBillingSignerAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[9]
}

// SetFeeQuoterAccount sets the "feeQuoter" account.
func (inst *CcipSend) SetFeeQuoterAccount(feeQuoter ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[10] = ag_solanago.Meta(feeQuoter)
	return inst
}

// GetFeeQuoterAccount gets the "feeQuoter" account.
func (inst *CcipSend) GetFeeQuoterAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[10]
}

// SetFeeQuoterConfigAccount sets the "feeQuoterConfig" account.
func (inst *CcipSend) SetFeeQuoterConfigAccount(feeQuoterConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[11] = ag_solanago.Meta(feeQuoterConfig)
	return inst
}

// GetFeeQuoterConfigAccount gets the "feeQuoterConfig" account.
func (inst *CcipSend) GetFeeQuoterConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[11]
}

// SetFeeQuoterDestChainAccount sets the "feeQuoterDestChain" account.
func (inst *CcipSend) SetFeeQuoterDestChainAccount(feeQuoterDestChain ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[12] = ag_solanago.Meta(feeQuoterDestChain)
	return inst
}

// GetFeeQuoterDestChainAccount gets the "feeQuoterDestChain" account.
func (inst *CcipSend) GetFeeQuoterDestChainAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[12]
}

// SetFeeQuoterBillingTokenConfigAccount sets the "feeQuoterBillingTokenConfig" account.
func (inst *CcipSend) SetFeeQuoterBillingTokenConfigAccount(feeQuoterBillingTokenConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[13] = ag_solanago.Meta(feeQuoterBillingTokenConfig)
	return inst
}

// GetFeeQuoterBillingTokenConfigAccount gets the "feeQuoterBillingTokenConfig" account.
func (inst *CcipSend) GetFeeQuoterBillingTokenConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[13]
}

// SetFeeQuoterLinkTokenConfigAccount sets the "feeQuoterLinkTokenConfig" account.
func (inst *CcipSend) SetFeeQuoterLinkTokenConfigAccount(feeQuoterLinkTokenConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[14] = ag_solanago.Meta(feeQuoterLinkTokenConfig)
	return inst
}

// GetFeeQuoterLinkTokenConfigAccount gets the "feeQuoterLinkTokenConfig" account.
func (inst *CcipSend) GetFeeQuoterLinkTokenConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[14]
}

// SetRmnRemoteAccount sets the "rmnRemote" account.
func (inst *CcipSend) SetRmnRemoteAccount(rmnRemote ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[15] = ag_solanago.Meta(rmnRemote)
	return inst
}

// GetRmnRemoteAccount gets the "rmnRemote" account.
func (inst *CcipSend) GetRmnRemoteAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[15]
}

// SetRmnRemoteCursesAccount sets the "rmnRemoteCurses" account.
func (inst *CcipSend) SetRmnRemoteCursesAccount(rmnRemoteCurses ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[16] = ag_solanago.Meta(rmnRemoteCurses)
	return inst
}

// GetRmnRemoteCursesAccount gets the "rmnRemoteCurses" account.
func (inst *CcipSend) GetRmnRemoteCursesAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[16]
}

// SetRmnRemoteConfigAccount sets the "rmnRemoteConfig" account.
func (inst *CcipSend) SetRmnRemoteConfigAccount(rmnRemoteConfig ag_solanago.PublicKey) *CcipSend {
	inst.AccountMetaSlice[17] = ag_solanago.Meta(rmnRemoteConfig)
	return inst
}

// GetRmnRemoteConfigAccount gets the "rmnRemoteConfig" account.
func (inst *CcipSend) GetRmnRemoteConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[17]
}

func (inst CcipSend) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_CcipSend,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst CcipSend) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *CcipSend) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.DestChainSelector == nil {
			return errors.New("DestChainSelector parameter is not set")
		}
		if inst.Message == nil {
			return errors.New("Message parameter is not set")
		}
		if inst.TokenIndexes == nil {
			return errors.New("TokenIndexes parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.DestChainState is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Nonce is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.FeeTokenProgram is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.FeeTokenMint is not set")
		}
		if inst.AccountMetaSlice[7] == nil {
			return errors.New("accounts.FeeTokenUserAssociatedAccount is not set")
		}
		if inst.AccountMetaSlice[8] == nil {
			return errors.New("accounts.FeeTokenReceiver is not set")
		}
		if inst.AccountMetaSlice[9] == nil {
			return errors.New("accounts.FeeBillingSigner is not set")
		}
		if inst.AccountMetaSlice[10] == nil {
			return errors.New("accounts.FeeQuoter is not set")
		}
		if inst.AccountMetaSlice[11] == nil {
			return errors.New("accounts.FeeQuoterConfig is not set")
		}
		if inst.AccountMetaSlice[12] == nil {
			return errors.New("accounts.FeeQuoterDestChain is not set")
		}
		if inst.AccountMetaSlice[13] == nil {
			return errors.New("accounts.FeeQuoterBillingTokenConfig is not set")
		}
		if inst.AccountMetaSlice[14] == nil {
			return errors.New("accounts.FeeQuoterLinkTokenConfig is not set")
		}
		if inst.AccountMetaSlice[15] == nil {
			return errors.New("accounts.RmnRemote is not set")
		}
		if inst.AccountMetaSlice[16] == nil {
			return errors.New("accounts.RmnRemoteCurses is not set")
		}
		if inst.AccountMetaSlice[17] == nil {
			return errors.New("accounts.RmnRemoteConfig is not set")
		}
	}
	return nil
}

func (inst *CcipSend) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("CcipSend")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=3]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("DestChainSelector", *inst.DestChainSelector))
						paramsBranch.Child(ag_format.Param("          Message", *inst.Message))
						paramsBranch.Child(ag_format.Param("     TokenIndexes", *inst.TokenIndexes))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=18]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("                     config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("             destChainState", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("                      nonce", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("                  authority", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("              systemProgram", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("            feeTokenProgram", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("               feeTokenMint", inst.AccountMetaSlice[6]))
						accountsBranch.Child(ag_format.Meta("     feeTokenUserAssociated", inst.AccountMetaSlice[7]))
						accountsBranch.Child(ag_format.Meta("           feeTokenReceiver", inst.AccountMetaSlice[8]))
						accountsBranch.Child(ag_format.Meta("           feeBillingSigner", inst.AccountMetaSlice[9]))
						accountsBranch.Child(ag_format.Meta("                  feeQuoter", inst.AccountMetaSlice[10]))
						accountsBranch.Child(ag_format.Meta("            feeQuoterConfig", inst.AccountMetaSlice[11]))
						accountsBranch.Child(ag_format.Meta("         feeQuoterDestChain", inst.AccountMetaSlice[12]))
						accountsBranch.Child(ag_format.Meta("feeQuoterBillingTokenConfig", inst.AccountMetaSlice[13]))
						accountsBranch.Child(ag_format.Meta("   feeQuoterLinkTokenConfig", inst.AccountMetaSlice[14]))
						accountsBranch.Child(ag_format.Meta("                  rmnRemote", inst.AccountMetaSlice[15]))
						accountsBranch.Child(ag_format.Meta("            rmnRemoteCurses", inst.AccountMetaSlice[16]))
						accountsBranch.Child(ag_format.Meta("            rmnRemoteConfig", inst.AccountMetaSlice[17]))
					})
				})
		})
}

func (obj CcipSend) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `DestChainSelector` param:
	err = encoder.Encode(obj.DestChainSelector)
	if err != nil {
		return err
	}
	// Serialize `Message` param:
	err = encoder.Encode(obj.Message)
	if err != nil {
		return err
	}
	// Serialize `TokenIndexes` param:
	err = encoder.Encode(obj.TokenIndexes)
	if err != nil {
		return err
	}
	return nil
}
func (obj *CcipSend) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `DestChainSelector`:
	err = decoder.Decode(&obj.DestChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `Message`:
	err = decoder.Decode(&obj.Message)
	if err != nil {
		return err
	}
	// Deserialize `TokenIndexes`:
	err = decoder.Decode(&obj.TokenIndexes)
	if err != nil {
		return err
	}
	return nil
}

// NewCcipSendInstruction declares a new CcipSend instruction with the provided parameters and accounts.
func NewCcipSendInstruction(
	// Parameters:
	destChainSelector uint64,
	message SVM2AnyMessage,
	tokenIndexes []byte,
	// Accounts:
	config ag_solanago.PublicKey,
	destChainState ag_solanago.PublicKey,
	nonce ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey,
	feeTokenProgram ag_solanago.PublicKey,
	feeTokenMint ag_solanago.PublicKey,
	feeTokenUserAssociatedAccount ag_solanago.PublicKey,
	feeTokenReceiver ag_solanago.PublicKey,
	feeBillingSigner ag_solanago.PublicKey,
	feeQuoter ag_solanago.PublicKey,
	feeQuoterConfig ag_solanago.PublicKey,
	feeQuoterDestChain ag_solanago.PublicKey,
	feeQuoterBillingTokenConfig ag_solanago.PublicKey,
	feeQuoterLinkTokenConfig ag_solanago.PublicKey,
	rmnRemote ag_solanago.PublicKey,
	rmnRemoteCurses ag_solanago.PublicKey,
	rmnRemoteConfig ag_solanago.PublicKey) *CcipSend {
	return NewCcipSendInstructionBuilder().
		SetDestChainSelector(destChainSelector).
		SetMessage(message).
		SetTokenIndexes(tokenIndexes).
		SetConfigAccount(config).
		SetDestChainStateAccount(destChainState).
		SetNonceAccount(nonce).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram).
		SetFeeTokenProgramAccount(feeTokenProgram).
		SetFeeTokenMintAccount(feeTokenMint).
		SetFeeTokenUserAssociatedAccountAccount(feeTokenUserAssociatedAccount).
		SetFeeTokenReceiverAccount(feeTokenReceiver).
		SetFeeBillingSignerAccount(feeBillingSigner).
		SetFeeQuoterAccount(feeQuoter).
		SetFeeQuoterConfigAccount(feeQuoterConfig).
		SetFeeQuoterDestChainAccount(feeQuoterDestChain).
		SetFeeQuoterBillingTokenConfigAccount(feeQuoterBillingTokenConfig).
		SetFeeQuoterLinkTokenConfigAccount(feeQuoterLinkTokenConfig).
		SetRmnRemoteAccount(rmnRemote).
		SetRmnRemoteCursesAccount(rmnRemoteCurses).
		SetRmnRemoteConfigAccount(rmnRemoteConfig)
}
