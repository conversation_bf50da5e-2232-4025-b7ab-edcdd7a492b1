// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Adds a new chain selector to the router.
//
// The Admin needs to add any new chain supported (this means both OnRamp and OffRamp).
// When adding a new chain, the Admin needs to specify if it's enabled or not.
// They may enable only source, or only destination, or neither, or both.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for adding the chain selector.
// * `new_chain_selector` - The new chain selector to be added.
// * `source_chain_config` - The configuration for the chain as source.
// * `dest_chain_config` - The configuration for the chain as destination.
type AddChainSelector struct {
	NewChainSelector *uint64
	DestChainConfig  *DestChainConfig

	// [0] = [WRITE] destChainState
	//
	// [1] = [] config
	//
	// [2] = [WRITE, SIGNER] authority
	//
	// [3] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewAddChainSelectorInstructionBuilder creates a new `AddChainSelector` instruction builder.
func NewAddChainSelectorInstructionBuilder() *AddChainSelector {
	nd := &AddChainSelector{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 4),
	}
	return nd
}

// SetNewChainSelector sets the "newChainSelector" parameter.
func (inst *AddChainSelector) SetNewChainSelector(newChainSelector uint64) *AddChainSelector {
	inst.NewChainSelector = &newChainSelector
	return inst
}

// SetDestChainConfig sets the "destChainConfig" parameter.
func (inst *AddChainSelector) SetDestChainConfig(destChainConfig DestChainConfig) *AddChainSelector {
	inst.DestChainConfig = &destChainConfig
	return inst
}

// SetDestChainStateAccount sets the "destChainState" account.
func (inst *AddChainSelector) SetDestChainStateAccount(destChainState ag_solanago.PublicKey) *AddChainSelector {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(destChainState).WRITE()
	return inst
}

// GetDestChainStateAccount gets the "destChainState" account.
func (inst *AddChainSelector) GetDestChainStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigAccount sets the "config" account.
func (inst *AddChainSelector) SetConfigAccount(config ag_solanago.PublicKey) *AddChainSelector {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *AddChainSelector) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *AddChainSelector) SetAuthorityAccount(authority ag_solanago.PublicKey) *AddChainSelector {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *AddChainSelector) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *AddChainSelector) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *AddChainSelector {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *AddChainSelector) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

func (inst AddChainSelector) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_AddChainSelector,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst AddChainSelector) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *AddChainSelector) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.NewChainSelector == nil {
			return errors.New("NewChainSelector parameter is not set")
		}
		if inst.DestChainConfig == nil {
			return errors.New("DestChainConfig parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.DestChainState is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *AddChainSelector) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("AddChainSelector")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=2]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("NewChainSelector", *inst.NewChainSelector))
						paramsBranch.Child(ag_format.Param(" DestChainConfig", *inst.DestChainConfig))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=4]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("destChainState", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("        config", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("     authority", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta(" systemProgram", inst.AccountMetaSlice[3]))
					})
				})
		})
}

func (obj AddChainSelector) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `NewChainSelector` param:
	err = encoder.Encode(obj.NewChainSelector)
	if err != nil {
		return err
	}
	// Serialize `DestChainConfig` param:
	err = encoder.Encode(obj.DestChainConfig)
	if err != nil {
		return err
	}
	return nil
}
func (obj *AddChainSelector) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `NewChainSelector`:
	err = decoder.Decode(&obj.NewChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `DestChainConfig`:
	err = decoder.Decode(&obj.DestChainConfig)
	if err != nil {
		return err
	}
	return nil
}

// NewAddChainSelectorInstruction declares a new AddChainSelector instruction with the provided parameters and accounts.
func NewAddChainSelectorInstruction(
	// Parameters:
	newChainSelector uint64,
	destChainConfig DestChainConfig,
	// Accounts:
	destChainState ag_solanago.PublicKey,
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *AddChainSelector {
	return NewAddChainSelectorInstructionBuilder().
		SetNewChainSelector(newChainSelector).
		SetDestChainConfig(destChainConfig).
		SetDestChainStateAccount(destChainState).
		SetConfigAccount(config).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
