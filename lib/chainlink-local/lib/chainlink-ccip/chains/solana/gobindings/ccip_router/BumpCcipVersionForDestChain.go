// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Bumps the CCIP version for a destination chain.
// This effectively just resets the sequence number of the destination chain state.
// If there had been a previous rollback, on re-upgrade the sequence number will resume from where it was
// prior to the rollback.
//
// # Arguments
// * `ctx` - The context containing the accounts required for the bump.
// * `dest_chain_selector` - The destination chain selector to bump version for.
type BumpCcipVersionForDestChain struct {
	DestChainSelector *uint64

	// [0] = [WRITE] destChainState
	//
	// [1] = [] config
	//
	// [2] = [WRITE, SIGNER] authority
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewBumpCcipVersionForDestChainInstructionBuilder creates a new `BumpCcipVersionForDestChain` instruction builder.
func NewBumpCcipVersionForDestChainInstructionBuilder() *BumpCcipVersionForDestChain {
	nd := &BumpCcipVersionForDestChain{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetDestChainSelector sets the "destChainSelector" parameter.
func (inst *BumpCcipVersionForDestChain) SetDestChainSelector(destChainSelector uint64) *BumpCcipVersionForDestChain {
	inst.DestChainSelector = &destChainSelector
	return inst
}

// SetDestChainStateAccount sets the "destChainState" account.
func (inst *BumpCcipVersionForDestChain) SetDestChainStateAccount(destChainState ag_solanago.PublicKey) *BumpCcipVersionForDestChain {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(destChainState).WRITE()
	return inst
}

// GetDestChainStateAccount gets the "destChainState" account.
func (inst *BumpCcipVersionForDestChain) GetDestChainStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetConfigAccount sets the "config" account.
func (inst *BumpCcipVersionForDestChain) SetConfigAccount(config ag_solanago.PublicKey) *BumpCcipVersionForDestChain {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *BumpCcipVersionForDestChain) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *BumpCcipVersionForDestChain) SetAuthorityAccount(authority ag_solanago.PublicKey) *BumpCcipVersionForDestChain {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(authority).WRITE().SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *BumpCcipVersionForDestChain) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst BumpCcipVersionForDestChain) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_BumpCcipVersionForDestChain,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst BumpCcipVersionForDestChain) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *BumpCcipVersionForDestChain) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.DestChainSelector == nil {
			return errors.New("DestChainSelector parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.DestChainState is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.Authority is not set")
		}
	}
	return nil
}

func (inst *BumpCcipVersionForDestChain) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("BumpCcipVersionForDestChain")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("DestChainSelector", *inst.DestChainSelector))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("destChainState", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("        config", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("     authority", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj BumpCcipVersionForDestChain) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `DestChainSelector` param:
	err = encoder.Encode(obj.DestChainSelector)
	if err != nil {
		return err
	}
	return nil
}
func (obj *BumpCcipVersionForDestChain) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `DestChainSelector`:
	err = decoder.Decode(&obj.DestChainSelector)
	if err != nil {
		return err
	}
	return nil
}

// NewBumpCcipVersionForDestChainInstruction declares a new BumpCcipVersionForDestChain instruction with the provided parameters and accounts.
func NewBumpCcipVersionForDestChainInstruction(
	// Parameters:
	destChainSelector uint64,
	// Accounts:
	destChainState ag_solanago.PublicKey,
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey) *BumpCcipVersionForDestChain {
	return NewBumpCcipVersionForDestChainInstructionBuilder().
		SetDestChainSelector(destChainSelector).
		SetDestChainStateAccount(destChainState).
		SetConfigAccount(config).
		SetAuthorityAccount(authority)
}
