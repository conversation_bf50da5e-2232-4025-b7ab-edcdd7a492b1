// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Updates the fee aggregator in the router configuration.
// The Admin is the only one able to update the fee aggregator.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for updating the configuration.
// * `fee_aggregator` - The new fee aggregator address (ATAs will be derived for it for each token).
type UpdateFeeAggregator struct {
	FeeAggregator *ag_solanago.PublicKey

	// [0] = [WRITE] config
	//
	// [1] = [SIGNER] authority
	//
	// [2] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewUpdateFeeAggregatorInstructionBuilder creates a new `UpdateFeeAggregator` instruction builder.
func NewUpdateFeeAggregatorInstructionBuilder() *UpdateFeeAggregator {
	nd := &UpdateFeeAggregator{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetFeeAggregator sets the "feeAggregator" parameter.
func (inst *UpdateFeeAggregator) SetFeeAggregator(feeAggregator ag_solanago.PublicKey) *UpdateFeeAggregator {
	inst.FeeAggregator = &feeAggregator
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *UpdateFeeAggregator) SetConfigAccount(config ag_solanago.PublicKey) *UpdateFeeAggregator {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config).WRITE()
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *UpdateFeeAggregator) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *UpdateFeeAggregator) SetAuthorityAccount(authority ag_solanago.PublicKey) *UpdateFeeAggregator {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *UpdateFeeAggregator) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *UpdateFeeAggregator) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *UpdateFeeAggregator {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *UpdateFeeAggregator) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst UpdateFeeAggregator) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_UpdateFeeAggregator,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst UpdateFeeAggregator) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *UpdateFeeAggregator) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.FeeAggregator == nil {
			return errors.New("FeeAggregator parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *UpdateFeeAggregator) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("UpdateFeeAggregator")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("FeeAggregator", *inst.FeeAggregator))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("       config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("    authority", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("systemProgram", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj UpdateFeeAggregator) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `FeeAggregator` param:
	err = encoder.Encode(obj.FeeAggregator)
	if err != nil {
		return err
	}
	return nil
}
func (obj *UpdateFeeAggregator) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `FeeAggregator`:
	err = decoder.Decode(&obj.FeeAggregator)
	if err != nil {
		return err
	}
	return nil
}

// NewUpdateFeeAggregatorInstruction declares a new UpdateFeeAggregator instruction with the provided parameters and accounts.
func NewUpdateFeeAggregatorInstruction(
	// Parameters:
	feeAggregator ag_solanago.PublicKey,
	// Accounts:
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *UpdateFeeAggregator {
	return NewUpdateFeeAggregatorInstructionBuilder().
		SetFeeAggregator(feeAggregator).
		SetConfigAccount(config).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
