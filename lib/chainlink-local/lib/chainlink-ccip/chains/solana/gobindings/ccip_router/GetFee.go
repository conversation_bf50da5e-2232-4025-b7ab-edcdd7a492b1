// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Queries the onramp for the fee required to send a message.
//
// This call is permissionless. Note it does not verify whether there's a curse active
// in order to avoid the RMN CPI overhead.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for obtaining the message fee.
// * `dest_chain_selector` - The chain selector for the destination chain.
// * `message` - The message to be sent. The size limit of data is 256 bytes.
type GetFee struct {
	DestChainSelector *uint64
	Message           *SVM2AnyMessage

	// [0] = [] config
	//
	// [1] = [] destChainState
	//
	// [2] = [] feeQuoter
	//
	// [3] = [] feeQuoterConfig
	//
	// [4] = [] feeQuoterDestChain
	//
	// [5] = [] feeQuoterBillingTokenConfig
	//
	// [6] = [] feeQuoterLinkTokenConfig
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewGetFeeInstructionBuilder creates a new `GetFee` instruction builder.
func NewGetFeeInstructionBuilder() *GetFee {
	nd := &GetFee{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 7),
	}
	return nd
}

// SetDestChainSelector sets the "destChainSelector" parameter.
func (inst *GetFee) SetDestChainSelector(destChainSelector uint64) *GetFee {
	inst.DestChainSelector = &destChainSelector
	return inst
}

// SetMessage sets the "message" parameter.
func (inst *GetFee) SetMessage(message SVM2AnyMessage) *GetFee {
	inst.Message = &message
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *GetFee) SetConfigAccount(config ag_solanago.PublicKey) *GetFee {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config)
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *GetFee) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetDestChainStateAccount sets the "destChainState" account.
func (inst *GetFee) SetDestChainStateAccount(destChainState ag_solanago.PublicKey) *GetFee {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(destChainState)
	return inst
}

// GetDestChainStateAccount gets the "destChainState" account.
func (inst *GetFee) GetDestChainStateAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetFeeQuoterAccount sets the "feeQuoter" account.
func (inst *GetFee) SetFeeQuoterAccount(feeQuoter ag_solanago.PublicKey) *GetFee {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(feeQuoter)
	return inst
}

// GetFeeQuoterAccount gets the "feeQuoter" account.
func (inst *GetFee) GetFeeQuoterAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

// SetFeeQuoterConfigAccount sets the "feeQuoterConfig" account.
func (inst *GetFee) SetFeeQuoterConfigAccount(feeQuoterConfig ag_solanago.PublicKey) *GetFee {
	inst.AccountMetaSlice[3] = ag_solanago.Meta(feeQuoterConfig)
	return inst
}

// GetFeeQuoterConfigAccount gets the "feeQuoterConfig" account.
func (inst *GetFee) GetFeeQuoterConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[3]
}

// SetFeeQuoterDestChainAccount sets the "feeQuoterDestChain" account.
func (inst *GetFee) SetFeeQuoterDestChainAccount(feeQuoterDestChain ag_solanago.PublicKey) *GetFee {
	inst.AccountMetaSlice[4] = ag_solanago.Meta(feeQuoterDestChain)
	return inst
}

// GetFeeQuoterDestChainAccount gets the "feeQuoterDestChain" account.
func (inst *GetFee) GetFeeQuoterDestChainAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[4]
}

// SetFeeQuoterBillingTokenConfigAccount sets the "feeQuoterBillingTokenConfig" account.
func (inst *GetFee) SetFeeQuoterBillingTokenConfigAccount(feeQuoterBillingTokenConfig ag_solanago.PublicKey) *GetFee {
	inst.AccountMetaSlice[5] = ag_solanago.Meta(feeQuoterBillingTokenConfig)
	return inst
}

// GetFeeQuoterBillingTokenConfigAccount gets the "feeQuoterBillingTokenConfig" account.
func (inst *GetFee) GetFeeQuoterBillingTokenConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[5]
}

// SetFeeQuoterLinkTokenConfigAccount sets the "feeQuoterLinkTokenConfig" account.
func (inst *GetFee) SetFeeQuoterLinkTokenConfigAccount(feeQuoterLinkTokenConfig ag_solanago.PublicKey) *GetFee {
	inst.AccountMetaSlice[6] = ag_solanago.Meta(feeQuoterLinkTokenConfig)
	return inst
}

// GetFeeQuoterLinkTokenConfigAccount gets the "feeQuoterLinkTokenConfig" account.
func (inst *GetFee) GetFeeQuoterLinkTokenConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[6]
}

func (inst GetFee) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_GetFee,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst GetFee) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *GetFee) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.DestChainSelector == nil {
			return errors.New("DestChainSelector parameter is not set")
		}
		if inst.Message == nil {
			return errors.New("Message parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.DestChainState is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.FeeQuoter is not set")
		}
		if inst.AccountMetaSlice[3] == nil {
			return errors.New("accounts.FeeQuoterConfig is not set")
		}
		if inst.AccountMetaSlice[4] == nil {
			return errors.New("accounts.FeeQuoterDestChain is not set")
		}
		if inst.AccountMetaSlice[5] == nil {
			return errors.New("accounts.FeeQuoterBillingTokenConfig is not set")
		}
		if inst.AccountMetaSlice[6] == nil {
			return errors.New("accounts.FeeQuoterLinkTokenConfig is not set")
		}
	}
	return nil
}

func (inst *GetFee) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("GetFee")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=2]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("DestChainSelector", *inst.DestChainSelector))
						paramsBranch.Child(ag_format.Param("          Message", *inst.Message))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=7]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("                     config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("             destChainState", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("                  feeQuoter", inst.AccountMetaSlice[2]))
						accountsBranch.Child(ag_format.Meta("            feeQuoterConfig", inst.AccountMetaSlice[3]))
						accountsBranch.Child(ag_format.Meta("         feeQuoterDestChain", inst.AccountMetaSlice[4]))
						accountsBranch.Child(ag_format.Meta("feeQuoterBillingTokenConfig", inst.AccountMetaSlice[5]))
						accountsBranch.Child(ag_format.Meta("   feeQuoterLinkTokenConfig", inst.AccountMetaSlice[6]))
					})
				})
		})
}

func (obj GetFee) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `DestChainSelector` param:
	err = encoder.Encode(obj.DestChainSelector)
	if err != nil {
		return err
	}
	// Serialize `Message` param:
	err = encoder.Encode(obj.Message)
	if err != nil {
		return err
	}
	return nil
}
func (obj *GetFee) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `DestChainSelector`:
	err = decoder.Decode(&obj.DestChainSelector)
	if err != nil {
		return err
	}
	// Deserialize `Message`:
	err = decoder.Decode(&obj.Message)
	if err != nil {
		return err
	}
	return nil
}

// NewGetFeeInstruction declares a new GetFee instruction with the provided parameters and accounts.
func NewGetFeeInstruction(
	// Parameters:
	destChainSelector uint64,
	message SVM2AnyMessage,
	// Accounts:
	config ag_solanago.PublicKey,
	destChainState ag_solanago.PublicKey,
	feeQuoter ag_solanago.PublicKey,
	feeQuoterConfig ag_solanago.PublicKey,
	feeQuoterDestChain ag_solanago.PublicKey,
	feeQuoterBillingTokenConfig ag_solanago.PublicKey,
	feeQuoterLinkTokenConfig ag_solanago.PublicKey) *GetFee {
	return NewGetFeeInstructionBuilder().
		SetDestChainSelector(destChainSelector).
		SetMessage(message).
		SetConfigAccount(config).
		SetDestChainStateAccount(destChainState).
		SetFeeQuoterAccount(feeQuoter).
		SetFeeQuoterConfigAccount(feeQuoterConfig).
		SetFeeQuoterDestChainAccount(feeQuoterDestChain).
		SetFeeQuoterBillingTokenConfigAccount(feeQuoterBillingTokenConfig).
		SetFeeQuoterLinkTokenConfigAccount(feeQuoterLinkTokenConfig)
}
