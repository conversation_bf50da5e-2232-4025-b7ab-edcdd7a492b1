// Code generated by https://github.com/gagliardetto/anchor-go. DO NOT EDIT.

package ccip_router

import (
	"errors"
	ag_binary "github.com/gagliardetto/binary"
	ag_solanago "github.com/gagliardetto/solana-go"
	ag_format "github.com/gagliardetto/solana-go/text/format"
	ag_treeout "github.com/gagliardetto/treeout"
)

// Updates the RMN remote program in the router configuration.
// The Admin is the only one able to update the RMN remote program.
//
// # Arguments
//
// * `ctx` - The context containing the accounts required for updating the configuration.
// * `rmn_remote,` - The new RMN remote address.
type UpdateRmnRemote struct {
	RmnRemote *ag_solanago.PublicKey

	// [0] = [WRITE] config
	//
	// [1] = [SIGNER] authority
	//
	// [2] = [] systemProgram
	ag_solanago.AccountMetaSlice `bin:"-" borsh_skip:"true"`
}

// NewUpdateRmnRemoteInstructionBuilder creates a new `UpdateRmnRemote` instruction builder.
func NewUpdateRmnRemoteInstructionBuilder() *UpdateRmnRemote {
	nd := &UpdateRmnRemote{
		AccountMetaSlice: make(ag_solanago.AccountMetaSlice, 3),
	}
	return nd
}

// SetRmnRemote sets the "rmnRemote" parameter.
func (inst *UpdateRmnRemote) SetRmnRemote(rmnRemote ag_solanago.PublicKey) *UpdateRmnRemote {
	inst.RmnRemote = &rmnRemote
	return inst
}

// SetConfigAccount sets the "config" account.
func (inst *UpdateRmnRemote) SetConfigAccount(config ag_solanago.PublicKey) *UpdateRmnRemote {
	inst.AccountMetaSlice[0] = ag_solanago.Meta(config).WRITE()
	return inst
}

// GetConfigAccount gets the "config" account.
func (inst *UpdateRmnRemote) GetConfigAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[0]
}

// SetAuthorityAccount sets the "authority" account.
func (inst *UpdateRmnRemote) SetAuthorityAccount(authority ag_solanago.PublicKey) *UpdateRmnRemote {
	inst.AccountMetaSlice[1] = ag_solanago.Meta(authority).SIGNER()
	return inst
}

// GetAuthorityAccount gets the "authority" account.
func (inst *UpdateRmnRemote) GetAuthorityAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[1]
}

// SetSystemProgramAccount sets the "systemProgram" account.
func (inst *UpdateRmnRemote) SetSystemProgramAccount(systemProgram ag_solanago.PublicKey) *UpdateRmnRemote {
	inst.AccountMetaSlice[2] = ag_solanago.Meta(systemProgram)
	return inst
}

// GetSystemProgramAccount gets the "systemProgram" account.
func (inst *UpdateRmnRemote) GetSystemProgramAccount() *ag_solanago.AccountMeta {
	return inst.AccountMetaSlice[2]
}

func (inst UpdateRmnRemote) Build() *Instruction {
	return &Instruction{BaseVariant: ag_binary.BaseVariant{
		Impl:   inst,
		TypeID: Instruction_UpdateRmnRemote,
	}}
}

// ValidateAndBuild validates the instruction parameters and accounts;
// if there is a validation error, it returns the error.
// Otherwise, it builds and returns the instruction.
func (inst UpdateRmnRemote) ValidateAndBuild() (*Instruction, error) {
	if err := inst.Validate(); err != nil {
		return nil, err
	}
	return inst.Build(), nil
}

func (inst *UpdateRmnRemote) Validate() error {
	// Check whether all (required) parameters are set:
	{
		if inst.RmnRemote == nil {
			return errors.New("RmnRemote parameter is not set")
		}
	}

	// Check whether all (required) accounts are set:
	{
		if inst.AccountMetaSlice[0] == nil {
			return errors.New("accounts.Config is not set")
		}
		if inst.AccountMetaSlice[1] == nil {
			return errors.New("accounts.Authority is not set")
		}
		if inst.AccountMetaSlice[2] == nil {
			return errors.New("accounts.SystemProgram is not set")
		}
	}
	return nil
}

func (inst *UpdateRmnRemote) EncodeToTree(parent ag_treeout.Branches) {
	parent.Child(ag_format.Program(ProgramName, ProgramID)).
		//
		ParentFunc(func(programBranch ag_treeout.Branches) {
			programBranch.Child(ag_format.Instruction("UpdateRmnRemote")).
				//
				ParentFunc(func(instructionBranch ag_treeout.Branches) {

					// Parameters of the instruction:
					instructionBranch.Child("Params[len=1]").ParentFunc(func(paramsBranch ag_treeout.Branches) {
						paramsBranch.Child(ag_format.Param("RmnRemote", *inst.RmnRemote))
					})

					// Accounts of the instruction:
					instructionBranch.Child("Accounts[len=3]").ParentFunc(func(accountsBranch ag_treeout.Branches) {
						accountsBranch.Child(ag_format.Meta("       config", inst.AccountMetaSlice[0]))
						accountsBranch.Child(ag_format.Meta("    authority", inst.AccountMetaSlice[1]))
						accountsBranch.Child(ag_format.Meta("systemProgram", inst.AccountMetaSlice[2]))
					})
				})
		})
}

func (obj UpdateRmnRemote) MarshalWithEncoder(encoder *ag_binary.Encoder) (err error) {
	// Serialize `RmnRemote` param:
	err = encoder.Encode(obj.RmnRemote)
	if err != nil {
		return err
	}
	return nil
}
func (obj *UpdateRmnRemote) UnmarshalWithDecoder(decoder *ag_binary.Decoder) (err error) {
	// Deserialize `RmnRemote`:
	err = decoder.Decode(&obj.RmnRemote)
	if err != nil {
		return err
	}
	return nil
}

// NewUpdateRmnRemoteInstruction declares a new UpdateRmnRemote instruction with the provided parameters and accounts.
func NewUpdateRmnRemoteInstruction(
	// Parameters:
	rmnRemote ag_solanago.PublicKey,
	// Accounts:
	config ag_solanago.PublicKey,
	authority ag_solanago.PublicKey,
	systemProgram ag_solanago.PublicKey) *UpdateRmnRemote {
	return NewUpdateRmnRemoteInstructionBuilder().
		SetRmnRemote(rmnRemote).
		SetConfigAccount(config).
		SetAuthorityAccount(authority).
		SetSystemProgramAccount(systemProgram)
}
