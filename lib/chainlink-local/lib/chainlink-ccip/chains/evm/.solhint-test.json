{"extends": "solhint:recommended", "plugins": ["chainlink-solidity"], "rules": {"compiler-version": ["off", "^0.8.0"], "const-name-snakecase": "off", "constructor-syntax": "error", "var-name-mixedcase": "off", "func-named-parameters": "off", "immutable-vars-naming": "off", "no-inline-assembly": "off", "contract-name-capwords": "off", "one-contract-per-file": "off", "avoid-low-level-calls": "off", "reentrancy": "off", "func-name-mixedcase": "off", "no-unused-import": "error", "gas-struct-packing": "warn", "interface-starts-with-i": "warn", "import-path-check": "off", "func-visibility": ["error", {"ignoreConstructors": true}], "not-rely-on-time": "off", "no-empty-blocks": "off", "quotes": ["error", "double"], "reason-string": ["warn", {"maxLength": 64}], "chainlink-solidity/prefix-internal-functions-with-underscore": "warn", "chainlink-solidity/prefix-private-functions-with-underscore": "warn", "chainlink-solidity/prefix-storage-variables-with-s-underscore": "warn", "chainlink-solidity/prefix-immutable-variables-with-i": "warn", "chainlink-solidity/all-caps-constant-storage-variables": "warn", "chainlink-solidity/no-hardhat-imports": "warn", "chainlink-solidity/inherited-constructor-args-not-in-contract-definition": "warn", "chainlink-solidity/explicit-returns": "warn"}}