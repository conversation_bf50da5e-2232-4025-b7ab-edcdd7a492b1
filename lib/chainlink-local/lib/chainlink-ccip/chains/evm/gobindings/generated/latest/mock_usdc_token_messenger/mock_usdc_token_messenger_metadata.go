// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package mock_usdc_token_messenger

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/pools/USDC/IMessageTransmitter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/pools/USDC/ITokenMessenger.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/test/mocks/MockE2EUSDCTokenMessenger.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/test/mocks/interfaces/IMessageTransmitterWithRelay.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/pools/USDC/IMessageTransmitter.sol\":{\"content\":\"/*\\n * Copyright (c) 2022, Circle Internet Financial Limited.\\n *\\n * Licensed under the Apache License, Version 2.0 (the \\\"License\\\");\\n * you may not use this file except in compliance with the License.\\n * You may obtain a copy of the License at\\n *\\n * http://www.apache.org/licenses/LICENSE-2.0\\n *\\n * Unless required by applicable law or agreed to in writing, software\\n * distributed under the License is distributed on an \\\"AS IS\\\" BASIS,\\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n * See the License for the specific language governing permissions and\\n * limitations under the License.\\n */\\npragma solidity ^0.8.0;\\n\\ninterface IMessageTransmitter {\\n  /// @notice Unlocks USDC tokens on the destination chain\\n  /// @param message The original message on the source chain\\n  ///     * Message format:\\n  ///     * Field                 Bytes      Type       Index\\n  ///     * version               4          uint32     0\\n  ///     * sourceDomain          4          uint32     4\\n  ///     * destinationDomain     4          uint32     8\\n  ///     * nonce                 8          uint64     12\\n  ///     * sender                32         bytes32    20\\n  ///     * recipient             32         bytes32    52\\n  ///     * destinationCaller     32         bytes32    84\\n  ///     * messageBody           dynamic    bytes      116\\n  /// param attestation A valid attestation is the concatenated 65-byte signature(s) of\\n  /// exactly `thresholdSignature` signatures, in increasing order of attester address.\\n  /// ***If the attester addresses recovered from signatures are not in increasing order,\\n  /// signature verification will fail.***\\n  /// If incorrect number of signatures or duplicate signatures are supplied,\\n  /// signature verification will fail.\\n  function receiveMessage(bytes calldata message, bytes calldata attestation) external returns (bool success);\\n\\n  /// Returns domain of chain on which the contract is deployed.\\n  /// @dev immutable\\n  function localDomain() external view returns (uint32);\\n\\n  /// Returns message format version.\\n  /// @dev immutable\\n  function version() external view returns (uint32);\\n}\\n\"},\"contracts/pools/USDC/ITokenMessenger.sol\":{\"content\":\"/*\\n * Copyright (c) 2022, Circle Internet Financial Limited.\\n *\\n * Licensed under the Apache License, Version 2.0 (the \\\"License\\\");\\n * you may not use this file except in compliance with the License.\\n * You may obtain a copy of the License at\\n *\\n * http://www.apache.org/licenses/LICENSE-2.0\\n *\\n * Unless required by applicable law or agreed to in writing, software\\n * distributed under the License is distributed on an \\\"AS IS\\\" BASIS,\\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n * See the License for the specific language governing permissions and\\n * limitations under the License.\\n */\\npragma solidity ^0.8.0;\\n\\ninterface ITokenMessenger {\\n  /// @notice Emitted when a DepositForBurn message is sent\\n  /// @param nonce Unique nonce reserved by message\\n  /// @param burnToken Address of token burnt on source domain\\n  /// @param amount Deposit amount\\n  /// @param depositor Address where deposit is transferred from\\n  /// @param mintRecipient Address receiving minted tokens on destination domain as bytes32\\n  /// @param destinationDomain Destination domain\\n  /// @param destinationTokenMessenger Address of TokenMessenger on destination domain as bytes32\\n  /// @param destinationCaller Authorized caller as bytes32 of receiveMessage() on destination domain,\\n  /// if not equal to bytes32(0). If equal to bytes32(0), any address can call receiveMessage().\\n  event DepositForBurn(\\n    uint64 indexed nonce,\\n    address indexed burnToken,\\n    uint256 amount,\\n    address indexed depositor,\\n    bytes32 mintRecipient,\\n    uint32 destinationDomain,\\n    bytes32 destinationTokenMessenger,\\n    bytes32 destinationCaller\\n  );\\n\\n  /// @notice Burns the tokens on the source side to produce a nonce through\\n  /// Circles Cross Chain Transfer Protocol.\\n  /// @param amount Amount of tokens to deposit and burn.\\n  /// @param destinationDomain Destination domain identifier.\\n  /// @param mintRecipient Address of mint recipient on destination domain.\\n  /// @param burnToken Address of contract to burn deposited tokens, on local domain.\\n  /// @param destinationCaller Caller on the destination domain, as bytes32.\\n  /// @return nonce The unique nonce used in unlocking the funds on the destination chain.\\n  /// @dev emits DepositForBurn\\n  function depositForBurnWithCaller(\\n    uint256 amount,\\n    uint32 destinationDomain,\\n    bytes32 mintRecipient,\\n    address burnToken,\\n    bytes32 destinationCaller\\n  ) external returns (uint64 nonce);\\n\\n  /// Returns the version of the message body format.\\n  /// @dev immutable\\n  function messageBodyVersion() external view returns (uint32);\\n\\n  /// Returns local Message Transmitter responsible for sending and receiving messages\\n  /// to/from remote domainsmessage transmitter for this token messenger.\\n  /// @dev immutable\\n  function localMessageTransmitter() external view returns (address);\\n}\\n\"},\"contracts/test/mocks/MockE2EUSDCTokenMessenger.sol\":{\"content\":\"/*\\n * Copyright (c) 2022, Circle Internet Financial Limited.\\n *\\n * Licensed under the Apache License, Version 2.0 (the \\\"License\\\");\\n * you may not use this file except in compliance with the License.\\n * You may obtain a copy of the License at\\n *\\n * http://www.apache.org/licenses/LICENSE-2.0\\n *\\n * Unless required by applicable law or agreed to in writing, software\\n * distributed under the License is distributed on an \\\"AS IS\\\" BASIS,\\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n * See the License for the specific language governing permissions and\\n * limitations under the License.\\n */\\npragma solidity ^0.8.24;\\n\\nimport {ITokenMessenger} from \\\"../../pools/USDC/ITokenMessenger.sol\\\";\\nimport {IMessageTransmitterWithRelay} from \\\"./interfaces/IMessageTransmitterWithRelay.sol\\\";\\nimport {IBurnMintERC20} from \\\"@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\\\";\\n\\n// This contract mocks both the ITokenMessenger and IMessageTransmitter\\n// contracts involved with the Cross Chain Token Protocol.\\n// solhint-disable\\ncontract MockE2EUSDCTokenMessenger is ITokenMessenger {\\n  uint32 private immutable i_messageBodyVersion;\\n  address private immutable i_transmitter;\\n\\n  bytes32 public constant DESTINATION_TOKEN_MESSENGER = keccak256(\\\"i_destinationTokenMessenger\\\");\\n\\n  uint64 public s_nonce;\\n\\n  // Local Message Transmitter responsible for sending and receiving messages to/from remote domains\\n  IMessageTransmitterWithRelay public immutable localMessageTransmitterWithRelay;\\n\\n  constructor(uint32 version, address transmitter) {\\n    i_messageBodyVersion = version;\\n    s_nonce = 1;\\n    i_transmitter = transmitter;\\n    localMessageTransmitterWithRelay = IMessageTransmitterWithRelay(transmitter);\\n  }\\n\\n  // The mock function is based on the same function in https://github.com/circlefin/evm-cctp-contracts/blob/master/src/TokenMessenger.sol\\n  function depositForBurnWithCaller(\\n    uint256 amount,\\n    uint32 destinationDomain,\\n    bytes32 mintRecipient,\\n    address burnToken,\\n    bytes32 destinationCaller\\n  ) external returns (uint64) {\\n    IBurnMintERC20(burnToken).transferFrom(msg.sender, address(this), amount);\\n    IBurnMintERC20(burnToken).burn(amount);\\n    // Format message body\\n    bytes memory _burnMessage = _formatMessage(\\n      i_messageBodyVersion,\\n      bytes32(uint256(uint160(burnToken))),\\n      mintRecipient,\\n      amount,\\n      bytes32(uint256(uint160(msg.sender)))\\n    );\\n    s_nonce =\\n      _sendDepositForBurnMessage(destinationDomain, DESTINATION_TOKEN_MESSENGER, destinationCaller, _burnMessage);\\n    emit DepositForBurn(\\n      s_nonce,\\n      burnToken,\\n      amount,\\n      msg.sender,\\n      mintRecipient,\\n      destinationDomain,\\n      DESTINATION_TOKEN_MESSENGER,\\n      destinationCaller\\n    );\\n    return s_nonce;\\n  }\\n\\n  function messageBodyVersion() external view returns (uint32) {\\n    return i_messageBodyVersion;\\n  }\\n\\n  function localMessageTransmitter() external view returns (address) {\\n    return i_transmitter;\\n  }\\n\\n  /**\\n   * @notice Sends a BurnMessage through the local message transmitter\\n   * @dev calls local message transmitter's sendMessage() function if `_destinationCaller` == bytes32(0),\\n   * or else calls sendMessageWithCaller().\\n   * @param _destinationDomain destination domain\\n   * @param _destinationTokenMessenger address of registered TokenMessenger contract on destination domain, as bytes32\\n   * @param _destinationCaller caller on the destination domain, as bytes32. If `_destinationCaller` == bytes32(0),\\n   * any address can call receiveMessage() on destination domain.\\n   * @param _burnMessage formatted BurnMessage bytes (message body)\\n   * @return nonce unique nonce reserved by message\\n   */\\n  function _sendDepositForBurnMessage(\\n    uint32 _destinationDomain,\\n    bytes32 _destinationTokenMessenger,\\n    bytes32 _destinationCaller,\\n    bytes memory _burnMessage\\n  ) internal returns (uint64 nonce) {\\n    if (_destinationCaller == bytes32(0)) {\\n      return localMessageTransmitterWithRelay.sendMessage(_destinationDomain, _destinationTokenMessenger, _burnMessage);\\n    } else {\\n      return localMessageTransmitterWithRelay.sendMessageWithCaller(\\n        _destinationDomain, _destinationTokenMessenger, _destinationCaller, _burnMessage\\n      );\\n    }\\n  }\\n\\n  /**\\n   * @notice Formats Burn message\\n   * @param _version The message body version\\n   * @param _burnToken The burn token address on source domain as bytes32\\n   * @param _mintRecipient The mint recipient address as bytes32\\n   * @param _amount The burn amount\\n   * @param _messageSender The message sender\\n   * @return Burn formatted message.\\n   */\\n  function _formatMessage(\\n    uint32 _version,\\n    bytes32 _burnToken,\\n    bytes32 _mintRecipient,\\n    uint256 _amount,\\n    bytes32 _messageSender\\n  ) internal pure returns (bytes memory) {\\n    return abi.encodePacked(_version, _burnToken, _mintRecipient, _amount, _messageSender);\\n  }\\n}\\n\"},\"contracts/test/mocks/interfaces/IMessageTransmitterWithRelay.sol\":{\"content\":\"/*\\n * Copyright (c) 2022, Circle Internet Financial Limited.\\n *\\n * Licensed under the Apache License, Version 2.0 (the \\\"License\\\");\\n * you may not use this file except in compliance with the License.\\n * You may obtain a copy of the License at\\n *\\n * http://www.apache.org/licenses/LICENSE-2.0\\n *\\n * Unless required by applicable law or agreed to in writing, software\\n * distributed under the License is distributed on an \\\"AS IS\\\" BASIS,\\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\n * See the License for the specific language governing permissions and\\n * limitations under the License.\\n */\\npragma solidity ^0.8.0;\\n\\nimport {IMessageTransmitter} from \\\"../../../pools/USDC/IMessageTransmitter.sol\\\";\\n\\n// This follows https://github.com/circlefin/evm-cctp-contracts/blob/master/src/interfaces/IMessageTransmitter.sol\\ninterface IMessageTransmitterWithRelay is IMessageTransmitter {\\n  /**\\n   * @notice Sends an outgoing message from the source domain.\\n   * @dev Increment nonce, format the message, and emit `MessageSent` event with message information.\\n   * @param destinationDomain Domain of destination chain\\n   * @param recipient Address of message recipient on destination domain as bytes32\\n   * @param messageBody Raw bytes content of message\\n   * @return nonce reserved by message\\n   */\\n  function sendMessage(\\n    uint32 destinationDomain,\\n    bytes32 recipient,\\n    bytes calldata messageBody\\n  ) external returns (uint64);\\n\\n  /**\\n   * @notice Sends an outgoing message from the source domain, with a specified caller on the\\n   * destination domain.\\n   * @dev Increment nonce, format the message, and emit `MessageSent` event with message information.\\n   * WARNING: if the `destinationCaller` does not represent a valid address as bytes32, then it will not be possible\\n   * to broadcast the message on the destination domain. This is an advanced feature, and the standard\\n   * sendMessage() should be preferred for use cases where a specific destination caller is not required.\\n   * @param destinationDomain Domain of destination chain\\n   * @param recipient Address of message recipient on destination domain as bytes32\\n   * @param destinationCaller caller on the destination domain, as bytes32\\n   * @param messageBody Raw bytes content of message\\n   * @return nonce reserved by message\\n   */\\n  function sendMessageWithCaller(\\n    uint32 destinationDomain,\\n    bytes32 recipient,\\n    bytes32 destinationCaller,\\n    bytes calldata messageBody\\n  ) external returns (uint64);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IERC20} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\n\\ninterface IBurnMintERC20 is IERC20 {\\n  /// @notice Mints new tokens for a given address.\\n  /// @param account The address to mint the new tokens to.\\n  /// @param amount The number of tokens to be minted.\\n  /// @dev this function increases the total supply.\\n  function mint(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from the sender.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burnFrom(address account, uint256 amount) external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.6.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20 {\\n  /**\\n   * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n   * another (`to`).\\n   *\\n   * Note that `value` may be zero.\\n   */\\n  event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n  /**\\n   * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n   * a call to {approve}. `value` is the new allowance.\\n   */\\n  event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n  /**\\n   * @dev Returns the amount of tokens in existence.\\n   */\\n  function totalSupply() external view returns (uint256);\\n\\n  /**\\n   * @dev Returns the amount of tokens owned by `account`.\\n   */\\n  function balanceOf(address account) external view returns (uint256);\\n\\n  /**\\n   * @dev Moves `amount` tokens from the caller's account to `to`.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transfer(address to, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Returns the remaining number of tokens that `spender` will be\\n   * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n   * zero by default.\\n   *\\n   * This value changes when {approve} or {transferFrom} are called.\\n   */\\n  function allowance(address owner, address spender) external view returns (uint256);\\n\\n  /**\\n   * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n   * that someone may use both the old and the new allowance by unfortunate\\n   * transaction ordering. One possible solution to mitigate this race\\n   * condition is to first reduce the spender's allowance to 0 and set the\\n   * desired value afterwards:\\n   * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n   *\\n   * Emits an {Approval} event.\\n   */\\n  function approve(address spender, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Moves `amount` tokens from `from` to `to` using the\\n   * allowance mechanism. `amount` is then deducted from the caller's\\n   * allowance.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transferFrom(address from, address to, uint256 amount) external returns (bool);\\n}\\n\"}}}"
