// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package ccip_home

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/capability/CCIPHome.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Internal.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/MerkleMultiProof.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/keystone/interfaces/ICapabilityConfiguration.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/keystone/interfaces/INodeInfoProvider.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/interfaces/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/capability/CCIPHome.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {ICapabilityConfiguration} from \\\"@chainlink/contracts/src/v0.8/keystone/interfaces/ICapabilityConfiguration.sol\\\";\\nimport {INodeInfoProvider} from \\\"@chainlink/contracts/src/v0.8/keystone/interfaces/INodeInfoProvider.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {Ownable2StepMsgSender} from \\\"@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\\\";\\n\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/interfaces/IERC165.sol\\\";\\nimport {EnumerableSet} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @notice CCIPHome stores the configuration for the CCIP capability.\\n/// We have two classes of configuration: chain configuration and DON (in the CapabilitiesRegistry sense) configuration.\\n/// Each chain will have a single configuration which includes information like the router address.\\n/// @dev This contract is a state machine with the following states:\\n/// - Init: The initial state of the contract, no config has been set, or all configs have been revoked.\\n///   [0, 0]\\n///\\n/// - Candidate: A new config has been set, but it has not been promoted yet, or all active configs have been revoked.\\n///   [0, 1]\\n///\\n/// - Active: A non-zero config has been promoted and is active, there is no candidate configured.\\n///   [1, 0]\\n///\\n/// - ActiveAndCandidate: A non-zero config has been promoted and is active, and a new config has been set as candidate.\\n///   [1, 1]\\n///\\n/// The following state transitions are allowed:\\n/// - Init -\\u003e Candidate: setCandidate()\\n/// - Candidate -\\u003e Active: promoteCandidateAndRevokeActive()\\n/// - Candidate -\\u003e Candidate: setCandidate()\\n/// - Candidate -\\u003e Init: revokeCandidate()\\n/// - Active -\\u003e ActiveAndCandidate: setCandidate()\\n/// - Active -\\u003e Init: promoteCandidateAndRevokeActive()\\n/// - ActiveAndCandidate -\\u003e Active: promoteCandidateAndRevokeActive()\\n/// - ActiveAndCandidate -\\u003e Active: revokeCandidate()\\n/// - ActiveAndCandidate -\\u003e ActiveAndCandidate: setCandidate()\\n///\\n/// This means the following calls are not allowed at the following states:\\n/// - Init: promoteCandidateAndRevokeActive(), as there is no config to promote.\\n/// - Init: revokeCandidate(), as there is no config to revoke\\n/// - Active: revokeCandidate(), as there is no candidate to revoke\\n/// Note that we explicitly do allow promoteCandidateAndRevokeActive() to be called when there is an active config but\\n/// no candidate config. This is the only way to remove the active config. The alternative would be to set some unusable\\n/// config as candidate and promote that, but fully clearing it is cleaner.\\n///\\n///       ┌─────────────┐   setCandidate     ┌─────────────┐\\n///       │             ├───────────────────►│             │ setCandidate\\n///       │    Init     │   revokeCandidate  │  Candidate  │◄───────────┐\\n///       │    [0,0]    │◄───────────────────┤    [0,1]    │────────────┘\\n///       │             │  ┌─────────────────┤             │\\n///       └─────────────┘  │  promote-       └─────────────┘\\n///                  ▲     │  Candidate\\n///        promote-  │     │\\n///        Candidate │     │\\n///                  │     │\\n///       ┌──────────┴──┐  │  promote-       ┌─────────────┐\\n///       │             │◄─┘  Candidate OR   │  Active \\u0026   │ setCandidate\\n///       │    Active   │    revokeCandidate │  Candidate  │◄───────────┐\\n///       │    [1,0]    │◄───────────────────┤    [1,1]    │────────────┘\\n///       │             ├───────────────────►│             │\\n///       └─────────────┘    setCandidate    └─────────────┘\\n///\\ncontract CCIPHome is Ownable2StepMsgSender, ITypeAndVersion, ICapabilityConfiguration, IERC165 {\\n  using EnumerableSet for EnumerableSet.UintSet;\\n\\n  event ChainConfigRemoved(uint64 chainSelector);\\n  event ChainConfigSet(uint64 chainSelector, ChainConfig chainConfig);\\n  event ConfigSet(bytes32 indexed configDigest, uint32 version, OCR3Config config);\\n  event ActiveConfigRevoked(bytes32 indexed configDigest);\\n  event CandidateConfigRevoked(bytes32 indexed configDigest);\\n  event ConfigPromoted(bytes32 indexed configDigest);\\n\\n  error ChainSelectorNotFound(uint64 chainSelector);\\n  error FChainMustBePositive();\\n  error ChainSelectorNotSet();\\n  error InvalidPluginType();\\n  error OfframpAddressCannotBeZero();\\n  error FChainTooHigh(uint256 fChain, uint256 FRoleDON);\\n  error TooManySigners();\\n  error FTooHigh();\\n  error RMNHomeAddressCannotBeZero();\\n  error InvalidNode(OCR3Node node);\\n  error NotEnoughTransmitters(uint256 got, uint256 minimum);\\n  error OnlyCapabilitiesRegistryCanCall();\\n  error ZeroAddressNotAllowed();\\n  error ConfigDigestMismatch(bytes32 expectedConfigDigest, bytes32 gotConfigDigest);\\n  error CanOnlySelfCall();\\n  error RevokingZeroDigestNotAllowed();\\n  error NoOpStateTransitionNotAllowed();\\n  error InvalidSelector(bytes4 selector);\\n  error DONIdMismatch(uint32 callDonId, uint32 capabilityRegistryDonId);\\n\\n  /// @notice Represents an oracle node in OCR3 configs part of the role DON.\\n  /// Every configured node should be a signer, but does not have to be a transmitter.\\n  struct OCR3Node {\\n    bytes32 p2pId; // Peer2Peer connection ID of the oracle.\\n    bytes signerKey; // On-chain signer public key.\\n    bytes transmitterKey; // On-chain transmitter public key. Can be set to empty bytes to represent that the node is a signer but not a transmitter.\\n  }\\n\\n  /// @notice OCR3 configuration.\\n  /// Note that FRoleDON \\u003e= fChain, since FRoleDON represents the role DON, and fChain represents sub-committees.\\n  /// FRoleDON values are typically identical across multiple OCR3 configs since the chains pertain to one role DON,\\n  /// but FRoleDON values can change across OCR3 configs to indicate role DON splits.\\n  struct OCR3Config {\\n    Internal.OCRPluginType pluginType; // ─╮ The plugin that the configuration is for.\\n    uint64 chainSelector; //               │ The (remote) chain that the configuration is for.\\n    uint8 FRoleDON; //                     │ The \\\"big F\\\" parameter for the role DON.\\n    uint64 offchainConfigVersion; // ──────╯ The version of the exec offchain configuration.\\n    bytes offrampAddress; // The remote chain offRamp address.\\n    bytes rmnHomeAddress; // The home chain RMN home address.\\n    OCR3Node[] nodes; // Keys \\u0026 IDs of nodes part of the role DON.\\n    bytes offchainConfig; // The offchain configuration for the OCR3 plugin. Protobuf encoded.\\n  }\\n\\n  struct VersionedConfig {\\n    uint32 version;\\n    bytes32 configDigest;\\n    OCR3Config config;\\n  }\\n\\n  /// @notice Chain configuration.\\n  /// Changes to chain configuration are detected out-of-band in plugins and decoded offchain.\\n  struct ChainConfig {\\n    bytes32[] readers; // The P2P IDs of the readers for the chain. These IDs must be registered in the capabilities registry.\\n    uint8 fChain; // The fault tolerance parameter of the chain.\\n    bytes config; // The chain configuration. This is kept intentionally opaque so as to add fields in the future if needed.\\n  }\\n\\n  /// @notice Chain configuration information struct used in applyChainConfigUpdates and getAllChainConfigs.\\n  struct ChainConfigArgs {\\n    uint64 chainSelector;\\n    ChainConfig chainConfig;\\n  }\\n\\n  string public constant override typeAndVersion = \\\"CCIPHome 1.6.0\\\";\\n\\n  /// @dev A prefix added to all config digests that is unique to the implementation.\\n  uint256 private constant PREFIX = 0x000a \\u003c\\u003c (256 - 16); // 0x000a00..00\\n  bytes32 internal constant EMPTY_ENCODED_ADDRESS_HASH = keccak256(abi.encode(address(0)));\\n  /// @dev 256 is the hard limit due to the bit encoding of their indexes into a uint256.\\n  uint256 internal constant MAX_NUM_ORACLES = 256;\\n\\n  /// @notice Used for encoding the config digest prefix.\\n  uint256 private constant PREFIX_MASK = type(uint256).max \\u003c\\u003c (256 - 16); // 0xFFFF00..00\\n  /// @notice The max number of configs that can be active at the same time.\\n  uint256 private constant MAX_CONCURRENT_CONFIGS = 2;\\n  /// @notice Helper to identify the zero config digest with less casting.\\n  bytes32 private constant ZERO_DIGEST = bytes32(uint256(0));\\n\\n  /// @dev The canonical capabilities registry address.\\n  address internal immutable i_capabilitiesRegistry;\\n\\n  /// @dev chain configuration for each chain that CCIP is deployed on.\\n  mapping(uint64 chainSelector =\\u003e ChainConfig chainConfig) private s_chainConfigurations;\\n\\n  /// @dev All chains that are configured.\\n  EnumerableSet.UintSet private s_remoteChainSelectors;\\n\\n  /// @notice This array holds the configs.\\n  /// @dev A DonID covers a single chain, and the plugin type is used to differentiate between the commit and execution.\\n  mapping(uint32 donId =\\u003e mapping(Internal.OCRPluginType pluginType =\\u003e VersionedConfig[MAX_CONCURRENT_CONFIGS])) private\\n    s_configs;\\n\\n  /// @notice The total number of configs ever set, used for generating the version of the configs.\\n  /// @dev Used to ensure unique digests across all configurations.\\n  uint32 private s_currentVersion = 0;\\n  /// @notice The index of the active config on a per-don and per-plugin basis.\\n  mapping(uint32 donId =\\u003e mapping(Internal.OCRPluginType pluginType =\\u003e uint32)) private s_activeConfigIndexes;\\n\\n  /// @notice Constructor for the CCIPHome contract takes in the address of the capabilities registry. This address\\n  /// is the only allowed caller to mutate the configuration through beforeCapabilityConfigSet.\\n  constructor(\\n    address capabilitiesRegistry\\n  ) {\\n    if (capabilitiesRegistry == address(0)) {\\n      revert ZeroAddressNotAllowed();\\n    }\\n    i_capabilitiesRegistry = capabilitiesRegistry;\\n  }\\n\\n  // ================================================================\\n  // │                    Capability Registry                       │\\n  // ================================================================\\n\\n  /// @notice Returns the capabilities registry address.\\n  /// @return The capabilities registry address.\\n  function getCapabilityRegistry() external view returns (address) {\\n    return i_capabilitiesRegistry;\\n  }\\n\\n  /// @inheritdoc IERC165\\n  /// @dev Required for the capabilities registry to recognize this contract.\\n  function supportsInterface(\\n    bytes4 interfaceId\\n  ) external pure override returns (bool) {\\n    return interfaceId == type(ICapabilityConfiguration).interfaceId || interfaceId == type(IERC165).interfaceId;\\n  }\\n\\n  /// @notice Called by the registry prior to the config being set for a particular DON.\\n  /// @dev precondition Requires destination chain config to be set.\\n  function beforeCapabilityConfigSet(\\n    bytes32[] calldata, // nodes.\\n    bytes calldata update,\\n    // Config count is unused because we don't want to invalidate a config on blue/green promotions so we keep track of\\n    // the actual newly submitted configs instead of the number of config mutations.\\n    uint64, // config count.\\n    uint32 donId\\n  ) external override {\\n    if (msg.sender != i_capabilitiesRegistry) {\\n      revert OnlyCapabilitiesRegistryCanCall();\\n    }\\n\\n    bytes4 selector = bytes4(update[:4]);\\n    // We only allow self-calls to the following approved methods.\\n    if (\\n      selector != this.setCandidate.selector \\u0026\\u0026 selector != this.revokeCandidate.selector\\n        \\u0026\\u0026 selector != this.promoteCandidateAndRevokeActive.selector\\n    ) {\\n      revert InvalidSelector(selector);\\n    }\\n\\n    // We validate that the call contains the correct DON ID. The DON ID is always the first function argument.\\n    uint256 callDonId = abi.decode(update[4:36], (uint256));\\n    if (callDonId != donId) {\\n      revert DONIdMismatch(uint32(callDonId), donId);\\n    }\\n\\n    // solhint-disable-next-line avoid-low-level-calls\\n    (bool success, bytes memory retData) = address(this).call(update);\\n    // if not successful, revert with the original revert.\\n    if (!success) {\\n      assembly {\\n        revert(add(retData, 0x20), returndatasize())\\n      }\\n    }\\n  }\\n\\n  /// @inheritdoc ICapabilityConfiguration\\n  /// @dev This function is not used in the CCIPHome contract but the interface requires it to be implemented.\\n  function getCapabilityConfiguration(\\n    uint32\\n  ) external pure override returns (bytes memory configuration) {\\n    return bytes(\\\"\\\");\\n  }\\n\\n  // ================================================================\\n  // │                          Getters                             │\\n  // ================================================================\\n\\n  /// @notice Returns the current active and candidate config digests.\\n  /// @dev Can be bytes32(0) if no config has been set yet or it has been revoked.\\n  /// @param donId The key of the plugin to get the config digests for.\\n  /// @return activeConfigDigest The digest of the active config.\\n  /// @return candidateConfigDigest The digest of the candidate config.\\n  function getConfigDigests(\\n    uint32 donId,\\n    Internal.OCRPluginType pluginType\\n  ) public view returns (bytes32 activeConfigDigest, bytes32 candidateConfigDigest) {\\n    return (\\n      s_configs[donId][pluginType][_getActiveIndex(donId, pluginType)].configDigest,\\n      s_configs[donId][pluginType][_getCandidateIndex(donId, pluginType)].configDigest\\n    );\\n  }\\n\\n  /// @notice Returns the active config digest for for a given key.\\n  /// @param donId The key of the plugin to get the config digests for.\\n  function getActiveDigest(uint32 donId, Internal.OCRPluginType pluginType) public view returns (bytes32) {\\n    return s_configs[donId][pluginType][_getActiveIndex(donId, pluginType)].configDigest;\\n  }\\n\\n  /// @notice Returns the candidate config digest for for a given key.\\n  /// @param donId The key of the plugin to get the config digests for.\\n  function getCandidateDigest(uint32 donId, Internal.OCRPluginType pluginType) public view returns (bytes32) {\\n    return s_configs[donId][pluginType][_getCandidateIndex(donId, pluginType)].configDigest;\\n  }\\n\\n  /// @notice The offchain code can use this to fetch an old config which might still be in use by some remotes. Use\\n  /// in case one of the configs is too large to be returnable by one of the other getters.\\n  /// @param donId The unique key for the DON that the configuration applies to.\\n  /// @param configDigest The digest of the config to fetch.\\n  /// @return versionedConfig The config and its version.\\n  /// @return ok True if the config was found, false otherwise.\\n  function getConfig(\\n    uint32 donId,\\n    Internal.OCRPluginType pluginType,\\n    bytes32 configDigest\\n  ) external view returns (VersionedConfig memory versionedConfig, bool ok) {\\n    for (uint256 i = 0; i \\u003c MAX_CONCURRENT_CONFIGS; ++i) {\\n      // We never want to return true for a zero digest, even if the caller is asking for it, as this can expose old\\n      // config state that is invalid.\\n      if (s_configs[donId][pluginType][i].configDigest == configDigest \\u0026\\u0026 configDigest != ZERO_DIGEST) {\\n        return (s_configs[donId][pluginType][i], true);\\n      }\\n    }\\n    // versionConfig is uninitialized so it contains default values.\\n    return (versionedConfig, false);\\n  }\\n\\n  /// @notice Returns the active and candidate configuration for a given plugin key.\\n  /// @param donId The unique key for the DON that the configuration applies to.\\n  /// @return activeConfig The active configuration.\\n  /// @return candidateConfig The candidate configuration.\\n  function getAllConfigs(\\n    uint32 donId,\\n    Internal.OCRPluginType pluginType\\n  ) external view returns (VersionedConfig memory activeConfig, VersionedConfig memory candidateConfig) {\\n    VersionedConfig memory storedActiveConfig = s_configs[donId][pluginType][_getActiveIndex(donId, pluginType)];\\n    if (storedActiveConfig.configDigest != ZERO_DIGEST) {\\n      activeConfig = storedActiveConfig;\\n    }\\n\\n    VersionedConfig memory storedCandidateConfig = s_configs[donId][pluginType][_getCandidateIndex(donId, pluginType)];\\n    if (storedCandidateConfig.configDigest != ZERO_DIGEST) {\\n      candidateConfig = storedCandidateConfig;\\n    }\\n\\n    return (activeConfig, candidateConfig);\\n  }\\n\\n  // ================================================================\\n  // │                     State transitions                        │\\n  // ================================================================\\n\\n  /// @notice Sets a new config as the candidate config. Does not influence the active config.\\n  /// @param donId The key of the plugin to set the config for.\\n  /// @return newConfigDigest The digest of the new config.\\n  function setCandidate(\\n    uint32 donId,\\n    Internal.OCRPluginType pluginType,\\n    OCR3Config calldata config,\\n    bytes32 digestToOverwrite\\n  ) external returns (bytes32 newConfigDigest) {\\n    _onlySelfCall();\\n    _validateConfig(config);\\n\\n    bytes32 existingDigest = getCandidateDigest(donId, pluginType);\\n\\n    if (existingDigest != digestToOverwrite) {\\n      revert ConfigDigestMismatch(existingDigest, digestToOverwrite);\\n    }\\n\\n    // are we going to overwrite a config? If so, emit an event.\\n    if (existingDigest != ZERO_DIGEST) {\\n      emit CandidateConfigRevoked(digestToOverwrite);\\n    }\\n\\n    uint32 newVersion = ++s_currentVersion;\\n    newConfigDigest = _calculateConfigDigest(donId, pluginType, abi.encode(config), newVersion);\\n\\n    VersionedConfig storage existingConfig = s_configs[donId][pluginType][_getCandidateIndex(donId, pluginType)];\\n    existingConfig.configDigest = newConfigDigest;\\n    existingConfig.version = newVersion;\\n    existingConfig.config = config;\\n\\n    emit ConfigSet(newConfigDigest, newVersion, config);\\n\\n    return newConfigDigest;\\n  }\\n\\n  /// @notice Revokes a specific config by digest.\\n  /// @param donId The key of the plugin to revoke the config for.\\n  /// @param configDigest The digest of the config to revoke. This is done to prevent accidental revokes.\\n  function revokeCandidate(uint32 donId, Internal.OCRPluginType pluginType, bytes32 configDigest) external {\\n    _onlySelfCall();\\n\\n    if (configDigest == ZERO_DIGEST) {\\n      revert RevokingZeroDigestNotAllowed();\\n    }\\n\\n    uint256 candidateConfigIndex = _getCandidateIndex(donId, pluginType);\\n    if (s_configs[donId][pluginType][candidateConfigIndex].configDigest != configDigest) {\\n      revert ConfigDigestMismatch(s_configs[donId][pluginType][candidateConfigIndex].configDigest, configDigest);\\n    }\\n\\n    emit CandidateConfigRevoked(configDigest);\\n    // Delete only the digest, as that's what's used to determine if a config is active. This means the actual config\\n    // stays in storage which should significantly reduce the gas cost of overwriting that storage space in the future.\\n    delete s_configs[donId][pluginType][candidateConfigIndex].configDigest;\\n  }\\n\\n  /// @notice Promotes the candidate config to the active config and revokes the active config.\\n  /// @param donId The key of the plugin to promote the config for.\\n  /// @param digestToPromote The digest of the config to promote.\\n  function promoteCandidateAndRevokeActive(\\n    uint32 donId,\\n    Internal.OCRPluginType pluginType,\\n    bytes32 digestToPromote,\\n    bytes32 digestToRevoke\\n  ) external {\\n    _onlySelfCall();\\n\\n    if (digestToPromote == ZERO_DIGEST \\u0026\\u0026 digestToRevoke == ZERO_DIGEST) {\\n      revert NoOpStateTransitionNotAllowed();\\n    }\\n\\n    uint256 candidateConfigIndex = _getCandidateIndex(donId, pluginType);\\n    if (s_configs[donId][pluginType][candidateConfigIndex].configDigest != digestToPromote) {\\n      revert ConfigDigestMismatch(s_configs[donId][pluginType][candidateConfigIndex].configDigest, digestToPromote);\\n    }\\n\\n    VersionedConfig storage activeConfig = s_configs[donId][pluginType][_getActiveIndex(donId, pluginType)];\\n    if (activeConfig.configDigest != digestToRevoke) {\\n      revert ConfigDigestMismatch(activeConfig.configDigest, digestToRevoke);\\n    }\\n\\n    delete activeConfig.configDigest;\\n\\n    s_activeConfigIndexes[donId][pluginType] ^= 1;\\n    if (digestToRevoke != ZERO_DIGEST) {\\n      emit ActiveConfigRevoked(digestToRevoke);\\n    }\\n\\n    emit ConfigPromoted(digestToPromote);\\n  }\\n\\n  /// @notice Calculates the config digest for a given plugin key, static config, and version.\\n  /// @param donId The key of the plugin to calculate the digest for.\\n  /// @param staticConfig The static part of the config.\\n  /// @param version The version of the config.\\n  /// @return The calculated config digest.\\n  function _calculateConfigDigest(\\n    uint32 donId,\\n    Internal.OCRPluginType pluginType,\\n    bytes memory staticConfig,\\n    uint32 version\\n  ) internal view returns (bytes32) {\\n    return bytes32(\\n      PREFIX\\n        | (\\n          uint256(\\n            keccak256(\\n              bytes.concat(\\n                abi.encode(bytes32(\\\"EVM\\\"), block.chainid, address(this), donId, pluginType, version), staticConfig\\n              )\\n            )\\n          ) \\u0026 ~PREFIX_MASK\\n        )\\n    );\\n  }\\n\\n  function _getActiveIndex(uint32 donId, Internal.OCRPluginType pluginType) private view returns (uint32) {\\n    return s_activeConfigIndexes[donId][pluginType];\\n  }\\n\\n  function _getCandidateIndex(uint32 donId, Internal.OCRPluginType pluginType) private view returns (uint32) {\\n    return s_activeConfigIndexes[donId][pluginType] ^ 1;\\n  }\\n\\n  // ================================================================\\n  // │                         Validation                           │\\n  // ================================================================\\n\\n  function _validateConfig(\\n    OCR3Config memory cfg\\n  ) internal view {\\n    if (cfg.chainSelector == 0) revert ChainSelectorNotSet();\\n    if (cfg.pluginType != Internal.OCRPluginType.Commit \\u0026\\u0026 cfg.pluginType != Internal.OCRPluginType.Execution) {\\n      revert InvalidPluginType();\\n    }\\n    if (cfg.offrampAddress.length == 0 || keccak256(cfg.offrampAddress) == EMPTY_ENCODED_ADDRESS_HASH) {\\n      revert OfframpAddressCannotBeZero();\\n    }\\n    if (cfg.rmnHomeAddress.length == 0 || keccak256(cfg.rmnHomeAddress) == EMPTY_ENCODED_ADDRESS_HASH) {\\n      revert RMNHomeAddressCannotBeZero();\\n    }\\n    if (!s_remoteChainSelectors.contains(cfg.chainSelector)) revert ChainSelectorNotFound(cfg.chainSelector);\\n\\n    // fChain cannot exceed FRoleDON, since it is a subcommittee in the larger DON.\\n    uint256 FRoleDON = cfg.FRoleDON;\\n    uint256 fChain = s_chainConfigurations[cfg.chainSelector].fChain;\\n    // fChain \\u003e 0 is enforced in applyChainConfigUpdates, and the presence of a chain config is checked above\\n    // FRoleDON != 0 because FRoleDON \\u003e= fChain is enforced here.\\n    if (fChain \\u003e FRoleDON) {\\n      revert FChainTooHigh(fChain, FRoleDON);\\n    }\\n\\n    // len(nodes) \\u003e= 3 * FRoleDON + 1.\\n    // len(nodes) == numberOfSigners.\\n    uint256 numberOfNodes = cfg.nodes.length;\\n    if (numberOfNodes \\u003e MAX_NUM_ORACLES) revert TooManySigners();\\n    if (numberOfNodes \\u003c= 3 * FRoleDON) revert FTooHigh();\\n\\n    uint256 nonZeroTransmitters = 0;\\n    bytes32[] memory p2pIds = new bytes32[](numberOfNodes);\\n    for (uint256 i = 0; i \\u003c numberOfNodes; ++i) {\\n      OCR3Node memory node = cfg.nodes[i];\\n\\n      // 3 * fChain + 1 \\u003c= nonZeroTransmitters \\u003c= 3 * FRoleDON + 1.\\n      // Transmitters can be set to 0 since there can be more signers than transmitters.\\n      if (node.transmitterKey.length != 0) {\\n        nonZeroTransmitters++;\\n      }\\n\\n      // Signer key and p2pIds must always be present.\\n      if (node.signerKey.length == 0 || node.p2pId == bytes32(0)) {\\n        revert InvalidNode(node);\\n      }\\n\\n      p2pIds[i] = node.p2pId;\\n    }\\n\\n    // We check for chain config presence above, so fChain here must be non-zero. fChain \\u003c= FRoleDON due to the checks\\n    // above. There can be less transmitters than signers - so they can be set to zero (which indicates that a node is\\n    // a signer, but not a transmitter).\\n    uint256 minTransmittersLength = 3 * fChain + 1;\\n    if (nonZeroTransmitters \\u003c minTransmittersLength) {\\n      revert NotEnoughTransmitters(nonZeroTransmitters, minTransmittersLength);\\n    }\\n\\n    // Check that the readers are in the capabilities registry.\\n    _ensureInRegistry(p2pIds);\\n  }\\n\\n  function _onlySelfCall() internal view {\\n    if (msg.sender != address(this)) {\\n      revert CanOnlySelfCall();\\n    }\\n  }\\n\\n  // ================================================================\\n  // │                    Chain Configuration                       │\\n  // ================================================================\\n\\n  /// @notice Returns the total number of chains configured.\\n  /// @return The total number of chains configured.\\n  function getNumChainConfigurations() external view returns (uint256) {\\n    return s_remoteChainSelectors.length();\\n  }\\n\\n  /// @notice Returns the chain configuration for a given chain selector.\\n  /// @param chainSelector The chain selector.\\n  /// @return chainConfig The chain configuration.\\n  function getChainConfig(\\n    uint64 chainSelector\\n  ) external view returns (ChainConfig memory) {\\n    return s_chainConfigurations[chainSelector];\\n  }\\n\\n  /// @notice Returns all the chain configurations.\\n  /// @param pageIndex The page index.\\n  /// @param pageSize The page size.\\n  /// @return paginatedChainConfigs chain configurations.\\n  function getAllChainConfigs(uint256 pageIndex, uint256 pageSize) external view returns (ChainConfigArgs[] memory) {\\n    uint256 numberOfChains = s_remoteChainSelectors.length();\\n    uint256 startIndex = pageIndex * pageSize;\\n\\n    if (pageSize == 0 || startIndex \\u003e= numberOfChains) {\\n      return new ChainConfigArgs[](0); // Return an empty array if pageSize is 0 or pageIndex is out of bounds.\\n    }\\n\\n    uint256 endIndex = startIndex + pageSize;\\n    if (endIndex \\u003e numberOfChains) {\\n      endIndex = numberOfChains;\\n    }\\n\\n    ChainConfigArgs[] memory paginatedChainConfigs = new ChainConfigArgs[](endIndex - startIndex);\\n\\n    for (uint256 i = startIndex; i \\u003c endIndex; ++i) {\\n      uint64 chainSelector = uint64(s_remoteChainSelectors.at(i));\\n      paginatedChainConfigs[i - startIndex] =\\n        ChainConfigArgs({chainSelector: chainSelector, chainConfig: s_chainConfigurations[chainSelector]});\\n    }\\n\\n    return paginatedChainConfigs;\\n  }\\n\\n  /// @notice Sets and/or removes chain configurations.\\n  /// @dev Does not validate that fChain \\u003c= FRoleDON and relies on OCR3Configs to be changed in case fChain becomes\\n  /// larger than the FRoleDON value.\\n  /// @param chainSelectorRemoves The chain configurations to remove.\\n  /// @param chainConfigAdds The chain configurations to add.\\n  function applyChainConfigUpdates(\\n    uint64[] calldata chainSelectorRemoves,\\n    ChainConfigArgs[] calldata chainConfigAdds\\n  ) external onlyOwner {\\n    // Process removals first.\\n    for (uint256 i = 0; i \\u003c chainSelectorRemoves.length; ++i) {\\n      // check if the chain selector is in s_remoteChainSelectors first.\\n      if (!s_remoteChainSelectors.contains(chainSelectorRemoves[i])) {\\n        revert ChainSelectorNotFound(chainSelectorRemoves[i]);\\n      }\\n\\n      delete s_chainConfigurations[chainSelectorRemoves[i]];\\n      s_remoteChainSelectors.remove(chainSelectorRemoves[i]);\\n\\n      emit ChainConfigRemoved(chainSelectorRemoves[i]);\\n    }\\n\\n    // Process additions next.\\n    for (uint256 i = 0; i \\u003c chainConfigAdds.length; ++i) {\\n      ChainConfig memory chainConfig = chainConfigAdds[i].chainConfig;\\n      uint64 chainSelector = chainConfigAdds[i].chainSelector;\\n\\n      // Verify that the provided readers are present in the capabilities registry.\\n      _ensureInRegistry(chainConfig.readers);\\n\\n      // Verify that fChain is positive.\\n      if (chainConfig.fChain == 0) {\\n        revert FChainMustBePositive();\\n      }\\n\\n      s_chainConfigurations[chainSelector] = chainConfig;\\n      s_remoteChainSelectors.add(chainSelector);\\n\\n      emit ChainConfigSet(chainSelector, chainConfig);\\n    }\\n  }\\n\\n  /// @notice Helper function to ensure that a node is in the capabilities registry.\\n  /// @param p2pIds The P2P IDs of the node to check.\\n  function _ensureInRegistry(\\n    bytes32[] memory p2pIds\\n  ) internal view {\\n    if (p2pIds.length != 0) {\\n      INodeInfoProvider(i_capabilitiesRegistry).getNodesByP2PIds(p2pIds);\\n    }\\n  }\\n}\\n\"},\"contracts/libraries/Internal.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {MerkleMultiProof} from \\\"../libraries/MerkleMultiProof.sol\\\";\\n\\n/// @notice Library for CCIP internal definitions common to multiple contracts.\\n/// @dev The following is a non-exhaustive list of \\\"known issues\\\" for CCIP:\\n/// - We could implement yield claiming for Blast. This is not worth the custom code path on non-blast chains.\\n/// - uint32 is used for timestamps, which will overflow in 2106. This is not a concern for the current use case, as we\\n/// expect to have migrated to a new version by then.\\nlibrary Internal {\\n  error InvalidEVMAddress(bytes encodedAddress);\\n  error Invalid32ByteAddress(bytes encodedAddress);\\n\\n  /// @dev We limit return data to a selector plus 4 words. This is to avoid malicious contracts from returning\\n  /// large amounts of data and causing repeated out-of-gas scenarios.\\n  uint16 internal constant MAX_RET_BYTES = 4 + 4 * 32;\\n  /// @dev The expected number of bytes returned by the balanceOf function.\\n  uint256 internal constant MAX_BALANCE_OF_RET_BYTES = 32;\\n\\n  /// @dev The address used to send calls for gas estimation.\\n  /// You only need to use this address if the minimum gas limit specified by the user is not actually enough to execute the\\n  /// given message and you're attempting to estimate the actual necessary gas limit\\n  address public constant GAS_ESTIMATION_SENDER = address(0xC11C11C11C11C11C11C11C11C11C11C11C11C1);\\n\\n  /// @notice A collection of token price and gas price updates.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct PriceUpdates {\\n    TokenPriceUpdate[] tokenPriceUpdates;\\n    GasPriceUpdate[] gasPriceUpdates;\\n  }\\n\\n  /// @notice Token price in USD.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct TokenPriceUpdate {\\n    address sourceToken; // Source token.\\n    uint224 usdPerToken; // 1e18 USD per 1e18 of the smallest token denomination.\\n  }\\n\\n  /// @notice Gas price for a given chain in USD, its value may contain tightly packed fields.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct GasPriceUpdate {\\n    uint64 destChainSelector; // Destination chain selector.\\n    uint224 usdPerUnitGas; // 1e18 USD per smallest unit (e.g. wei) of destination chain gas.\\n  }\\n\\n  /// @notice A timestamped uint224 value that can contain several tightly packed fields.\\n  struct TimestampedPackedUint224 {\\n    uint224 value; // ────╮ Value in uint224, packed.\\n    uint32 timestamp; // ─╯ Timestamp of the most recent price update.\\n  }\\n\\n  /// @dev Gas price is stored in 112-bit unsigned int. uint224 can pack 2 prices.\\n  /// When packing L1 and L2 gas prices, L1 gas price is left-shifted to the higher-order bits.\\n  /// Using uint8 type, which cannot be higher than other bit shift operands, to avoid shift operand type warning.\\n  uint8 public constant GAS_PRICE_BITS = 112;\\n\\n  struct SourceTokenData {\\n    // The source pool address, abi encoded. This value is trusted as it was obtained through the onRamp. It can be\\n    // relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint32 destGasAmount; // The amount of gas available for the releaseOrMint and balanceOf calls on the offRamp\\n  }\\n\\n  /// @notice Report that is submitted by the execution DON at the execution phase, including chain selector data.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct ExecutionReport {\\n    uint64 sourceChainSelector; // Source chain selector for which the report is submitted.\\n    Any2EVMRampMessage[] messages;\\n    // Contains a bytes array for each message, each inner bytes array contains bytes per transferred token.\\n    bytes[][] offchainTokenData;\\n    bytes32[] proofs;\\n    uint256 proofFlagBits;\\n  }\\n\\n  /// @dev Any2EVMRampMessage struct has 10 fields, including 3 variable unnested arrays, sender, data and tokenAmounts.\\n  /// Each variable array takes 1 more slot to store its length.\\n  /// When abi encoded, excluding array contents, Any2EVMMessage takes up a fixed number of 13 slots, 32 bytes each.\\n  /// Assume 1 slot for sender\\n  /// For structs that contain arrays, 1 more slot is added to the front, reaching a total of 14.\\n  /// The fixed bytes does not cover struct data (this is represented by MESSAGE_FIXED_BYTES_PER_TOKEN)\\n  uint256 public constant MESSAGE_FIXED_BYTES = 32 * 15;\\n\\n  /// @dev Any2EVMTokensTransfer struct bytes length\\n  /// 0x20\\n  /// sourcePoolAddress_offset\\n  /// destTokenAddress\\n  /// destGasAmount\\n  /// extraData_offset\\n  /// amount\\n  /// sourcePoolAddress_length\\n  /// sourcePoolAddress_content // assume 1 slot\\n  /// extraData_length // contents billed separately\\n  uint256 public constant MESSAGE_FIXED_BYTES_PER_TOKEN = 32 * (4 + (3 + 2));\\n\\n  bytes32 internal constant ANY_2_EVM_MESSAGE_HASH = keccak256(\\\"Any2EVMMessageHashV1\\\");\\n  bytes32 internal constant EVM_2_ANY_MESSAGE_HASH = keccak256(\\\"EVM2AnyMessageHashV1\\\");\\n\\n  /// @dev Used to hash messages for multi-lane family-agnostic OffRamps.\\n  /// OnRamp hash(EVM2AnyMessage) != Any2EVMRampMessage.messageId.\\n  /// OnRamp hash(EVM2AnyMessage) != OffRamp hash(Any2EVMRampMessage).\\n  /// @param original OffRamp message to hash.\\n  /// @param metadataHash Hash preimage to ensure global uniqueness.\\n  /// @return hashedMessage hashed message as a keccak256.\\n  function _hash(Any2EVMRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.header.messageId,\\n            original.receiver,\\n            original.header.sequenceNumber,\\n            original.gasLimit,\\n            original.header.nonce\\n          )\\n        ),\\n        keccak256(original.sender),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts))\\n      )\\n    );\\n  }\\n\\n  function _hash(EVM2AnyRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.sender,\\n            original.header.sequenceNumber,\\n            original.header.nonce,\\n            original.feeToken,\\n            original.feeTokenAmount\\n          )\\n        ),\\n        keccak256(original.receiver),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts)),\\n        keccak256(original.extraArgs)\\n      )\\n    );\\n  }\\n\\n  /// @dev We disallow the first 1024 addresses to avoid calling into a range known for hosting precompiles. Calling\\n  /// into precompiles probably won't cause any issues, but to be safe we can disallow this range. It is extremely\\n  /// unlikely that anyone would ever be able to generate an address in this range. There is no official range of\\n  /// precompiles, but EIP-7587 proposes to reserve the range 0x100 to 0x1ff. Our range is more conservative, even\\n  /// though it might not be exhaustive for all chains, which is OK. We also disallow the zero address, which is a\\n  /// common practice.\\n  uint256 public constant EVM_PRECOMPILE_SPACE = 1024;\\n\\n  // According to the Aptos docs, the first 0xa addresses are reserved for precompiles.\\n  // https://github.com/aptos-labs/aptos-core/blob/main/aptos-move/framework/aptos-framework/doc/account.md#function-create_framework_reserved_account-1\\n  uint256 public constant APTOS_PRECOMPILE_SPACE = 0x0b;\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// EVM address space. If it isn't it will revert with an InvalidEVMAddress error, which we can catch and handle\\n  /// more gracefully than a revert from abi.decode.\\n  function _validateEVMAddress(\\n    bytes memory encodedAddress\\n  ) internal pure {\\n    if (encodedAddress.length != 32) revert InvalidEVMAddress(encodedAddress);\\n    uint256 encodedAddressUint = abi.decode(encodedAddress, (uint256));\\n    if (encodedAddressUint \\u003e type(uint160).max || encodedAddressUint \\u003c EVM_PRECOMPILE_SPACE) {\\n      revert InvalidEVMAddress(encodedAddress);\\n    }\\n  }\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// bounds of [minValue, uint256.max]. If it isn't it will revert with an Invalid32ByteAddress error.\\n  function _validate32ByteAddress(bytes memory encodedAddress, uint256 minValue) internal pure {\\n    if (encodedAddress.length != 32) revert Invalid32ByteAddress(encodedAddress);\\n    if (minValue \\u003e 0) {\\n      if (abi.decode(encodedAddress, (uint256)) \\u003c minValue) {\\n        revert Invalid32ByteAddress(encodedAddress);\\n      }\\n    }\\n  }\\n\\n  /// @notice Enum listing the possible message execution states within the offRamp contract.\\n  /// UNTOUCHED never executed.\\n  /// IN_PROGRESS currently being executed, used a replay protection.\\n  /// SUCCESS successfully executed. End state.\\n  /// FAILURE unsuccessfully executed, manual execution is now enabled.\\n  /// @dev RMN depends on this enum, if changing, please notify the RMN maintainers.\\n  enum MessageExecutionState {\\n    UNTOUCHED,\\n    IN_PROGRESS,\\n    SUCCESS,\\n    FAILURE\\n  }\\n\\n  /// @notice CCIP OCR plugin type, used to separate execution \\u0026 commit transmissions and configs.\\n  enum OCRPluginType {\\n    Commit,\\n    Execution\\n  }\\n\\n  /// @notice Family-agnostic header for OnRamp \\u0026 OffRamp messages.\\n  /// The messageId is not expected to match hash(message), since it may originate from another ramp family.\\n  struct RampMessageHeader {\\n    bytes32 messageId; // Unique identifier for the message, generated with the source chain's encoding scheme (i.e. not necessarily abi.encoded).\\n    uint64 sourceChainSelector; // ─╮ the chain selector of the source chain, note: not chainId.\\n    uint64 destChainSelector; //    │ the chain selector of the destination chain, note: not chainId.\\n    uint64 sequenceNumber; //       │ sequence number, not unique across lanes.\\n    uint64 nonce; // ───────────────╯ nonce for this lane for this sender, not unique across senders/lanes.\\n  }\\n\\n  struct EVM2AnyTokenTransfer {\\n    // The source pool EVM address. This value is trusted as it was obtained through the onRamp. It can be relied\\n    // upon by the destination pool to validate the source pool.\\n    address sourcePoolAddress;\\n    // The EVM address of the destination token.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n    // Destination chain data used to execute the token transfer on the destination chain. For an EVM destination, it\\n    // consists of the amount of gas available for the releaseOrMint and transfer calls made by the offRamp.\\n    bytes destExecData;\\n  }\\n\\n  struct Any2EVMTokenTransfer {\\n    // The source pool EVM address encoded to bytes. This value is trusted as it is obtained through the onRamp. It can\\n    // be relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    address destTokenAddress; // ─╮ Address of destination token\\n    uint32 destGasAmount; // ─────╯ The amount of gas available for the releaseOrMint and transfer calls on the offRamp.\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  /// @notice Family-agnostic message routed to an OffRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage), hash(Any2EVMRampMessage) != messageId due to encoding\\n  /// and parameter differences.\\n  struct Any2EVMRampMessage {\\n    RampMessageHeader header; // Message header.\\n    bytes sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    address receiver; // receiver address on the destination chain.\\n    uint256 gasLimit; // user supplied maximum gas amount available for dest chain execution.\\n    Any2EVMTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  /// @notice Family-agnostic message emitted from the OnRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage) due to encoding \\u0026 parameter differences.\\n  /// messageId = hash(EVM2AnyRampMessage) using the source EVM chain's encoding format.\\n  struct EVM2AnyRampMessage {\\n    RampMessageHeader header; // Message header.\\n    address sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    bytes receiver; // receiver address on the destination chain.\\n    bytes extraArgs; // destination-chain specific extra args, such as the gasLimit for EVM chains.\\n    address feeToken; // fee token.\\n    uint256 feeTokenAmount; // fee token amount.\\n    uint256 feeValueJuels; // fee amount in Juels.\\n    EVM2AnyTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector EVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_EVM = 0x2812d52c;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SVM = 0x1e10bdc4;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector APTOS\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_APTOS = 0xac77ffec;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SUI\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SUI = 0xc4e05953;\\n\\n  /// @dev Holds a merkle root and interval for a source chain so that an array of these can be passed in the CommitReport.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  /// @dev inefficient struct packing intentionally chosen to maintain order of specificity. Not a storage struct so impact is minimal.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct MerkleRoot {\\n    uint64 sourceChainSelector; // Remote source chain selector that the Merkle Root is scoped to\\n    bytes onRampAddress; //        Generic onRamp address, to support arbitrary sources; for EVM, use abi.encode\\n    uint64 minSeqNr; // ─────────╮ Minimum sequence number, inclusive\\n    uint64 maxSeqNr; // ─────────╯ Maximum sequence number, inclusive\\n    bytes32 merkleRoot; //         Merkle root covering the interval \\u0026 source chain messages\\n  }\\n}\\n\"},\"contracts/libraries/MerkleMultiProof.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nlibrary MerkleMultiProof {\\n  /// @notice Leaf domain separator, should be used as the first 32 bytes of a leaf's preimage.\\n  bytes32 internal constant LEAF_DOMAIN_SEPARATOR = 0x0000000000000000000000000000000000000000000000000000000000000000;\\n  /// @notice Internal domain separator, should be used as the first 32 bytes of an internal node's preimage.\\n  bytes32 internal constant INTERNAL_DOMAIN_SEPARATOR =\\n    0x0000000000000000000000000000000000000000000000000000000000000001;\\n\\n  uint256 internal constant MAX_NUM_HASHES = 256;\\n\\n  error InvalidProof();\\n  error LeavesCannotBeEmpty();\\n\\n  /// @notice Computes the root based on provided pre-hashed leaf nodes in leaves, internal nodes  in proofs, and using\\n  /// proofFlagBits' i-th bit to determine if an element of proofs or one of the previously computed leafs or internal\\n  /// nodes will be used for the i-th hash.\\n  /// @param leaves Should be pre-hashed and the first 32 bytes of a leaf's preimage should match LEAF_DOMAIN_SEPARATOR.\\n  /// @param proofs Hashes to be used instead of a leaf hash when the proofFlagBits indicates a proof should be used.\\n  /// @param proofFlagBits A single uint256 of which each bit indicates whether a leaf or a proof needs to be used in\\n  /// a hash operation.\\n  /// @dev the maximum number of hash operations it set to 256. Any input that would require more than 256 hashes to get\\n  /// to a root will revert.\\n  /// @dev For given input `leaves` = [a,b,c] `proofs` = [D] and `proofFlagBits` = 5\\n  ///     totalHashes = 3 + 1 - 1 = 3\\n  ///  ** round 1 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 0) \\u0026 1 = true\\n  ///    hashes[0] = hashPair(a, b)\\n  ///    (leafPos, hashPos, proofPos) = (2, 0, 0);\\n  ///\\n  ///  ** round 2 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 1) \\u0026 1 = false\\n  ///    hashes[1] = hashPair(D, c)\\n  ///    (leafPos, hashPos, proofPos) = (3, 0, 1);\\n  ///\\n  ///  ** round 3 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 2) \\u0026 1 = true\\n  ///    hashes[2] = hashPair(hashes[0], hashes[1])\\n  ///    (leafPos, hashPos, proofPos) = (3, 2, 1);\\n  ///\\n  ///    i = 3 and no longer \\u003c totalHashes. The algorithm is done\\n  ///    return hashes[totalHashes - 1] = hashes[2]; the last hash we computed.\\n  // We mark this function as internal to force it to be inlined in contracts that use it, but semantically it is public.\\n  function _merkleRoot(\\n    bytes32[] memory leaves,\\n    bytes32[] memory proofs,\\n    uint256 proofFlagBits\\n  ) internal pure returns (bytes32) {\\n    unchecked {\\n      uint256 leavesLen = leaves.length;\\n      uint256 proofsLen = proofs.length;\\n      if (leavesLen == 0) revert LeavesCannotBeEmpty();\\n      if (!(leavesLen \\u003c= MAX_NUM_HASHES + 1 \\u0026\\u0026 proofsLen \\u003c= MAX_NUM_HASHES + 1)) revert InvalidProof();\\n      uint256 totalHashes = leavesLen + proofsLen - 1;\\n      if (!(totalHashes \\u003c= MAX_NUM_HASHES)) revert InvalidProof();\\n      if (totalHashes == 0) {\\n        return leaves[0];\\n      }\\n      bytes32[] memory hashes = new bytes32[](totalHashes);\\n      (uint256 leafPos, uint256 hashPos, uint256 proofPos) = (0, 0, 0);\\n\\n      for (uint256 i = 0; i \\u003c totalHashes; ++i) {\\n        // Checks if the bit flag signals the use of a supplied proof or a leaf/previous hash.\\n        bytes32 a;\\n        if (proofFlagBits \\u0026 (1 \\u003c\\u003c i) == (1 \\u003c\\u003c i)) {\\n          // Use a leaf or a previously computed hash.\\n          if (leafPos \\u003c leavesLen) {\\n            a = leaves[leafPos++];\\n          } else {\\n            a = hashes[hashPos++];\\n          }\\n        } else {\\n          // Use a supplied proof.\\n          a = proofs[proofPos++];\\n        }\\n\\n        // The second part of the hashed pair is never a proof as hashing two proofs would result in a\\n        // hash that can already be computed offchain.\\n        bytes32 b;\\n        if (leafPos \\u003c leavesLen) {\\n          b = leaves[leafPos++];\\n        } else {\\n          b = hashes[hashPos++];\\n        }\\n\\n        if (!(hashPos \\u003c= i)) revert InvalidProof();\\n\\n        hashes[i] = _hashPair(a, b);\\n      }\\n      if (!(hashPos == totalHashes - 1 \\u0026\\u0026 leafPos == leavesLen \\u0026\\u0026 proofPos == proofsLen)) revert InvalidProof();\\n      // Return the last hash.\\n      return hashes[totalHashes - 1];\\n    }\\n  }\\n\\n  /// @notice Hashes two bytes32 objects in their given order, prepended by the INTERNAL_DOMAIN_SEPARATOR.\\n  function _hashInternalNode(bytes32 left, bytes32 right) private pure returns (bytes32 hash) {\\n    return keccak256(abi.encode(INTERNAL_DOMAIN_SEPARATOR, left, right));\\n  }\\n\\n  /// @notice Hashes two bytes32 objects. The order is taken into account, using the lower value first.\\n  function _hashPair(bytes32 a, bytes32 b) private pure returns (bytes32) {\\n    return a \\u003c b ? _hashInternalNode(a, b) : _hashInternalNode(b, a);\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/keystone/interfaces/ICapabilityConfiguration.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice Interface for capability configuration contract. It MUST be\\n/// implemented for a contract to be used as a capability configuration.\\n/// The contract MAY store configuration that is shared across multiple\\n/// DON instances and capability versions.\\n/// @dev This interface does not guarantee the configuration contract's\\n/// correctness. It is the responsibility of the contract owner to ensure\\n/// that the configuration contract emits the CapabilityConfigurationSet\\n/// event when the configuration is set.\\ninterface ICapabilityConfiguration {\\n  /// @notice Emitted when a capability configuration is set.\\n  event CapabilityConfigurationSet();\\n\\n  /// @notice Returns the capability configuration for a particular DON instance.\\n  /// @dev donId is required to get DON-specific configuration. It avoids a\\n  /// situation where configuration size grows too large.\\n  /// @param donId The DON instance ID. These are stored in the CapabilitiesRegistry.\\n  /// @return configuration DON's configuration for the capability.\\n  function getCapabilityConfiguration(uint32 donId) external view returns (bytes memory configuration);\\n\\n  /// @notice Called by the registry prior to the config being set for a particular DON.\\n  /// @param nodes The nodes that the configuration is being set for.\\n  /// @param donCapabilityConfig The configuration being set on the capability registry.\\n  /// @param donCapabilityConfigCount The number of times the DON has been configured, tracked on the capability registry.\\n  /// @param donId The DON ID on the capability registry.\\n  function beforeCapabilityConfigSet(\\n    bytes32[] calldata nodes,\\n    bytes calldata donCapabilityConfig,\\n    uint64 donCapabilityConfigCount,\\n    uint32 donId\\n  ) external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/keystone/interfaces/INodeInfoProvider.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\n/// @title INodeInfoProvider\\n/// @notice Interface for retrieving node information.\\ninterface INodeInfoProvider {\\n  /// @notice This error is thrown when a node with the provided P2P ID is\\n  /// not found.\\n  /// @param nodeP2PId The node P2P ID used for the lookup.\\n  error NodeDoesNotExist(bytes32 nodeP2PId);\\n\\n  struct NodeInfo {\\n    /// @notice The id of the node operator that manages this node\\n    uint32 nodeOperatorId;\\n    /// @notice The number of times the node's configuration has been updated\\n    uint32 configCount;\\n    /// @notice The ID of the Workflow DON that the node belongs to. A node can\\n    /// only belong to one DON that accepts Workflows.\\n    uint32 workflowDONId;\\n    /// @notice The signer address for application-layer message verification.\\n    bytes32 signer;\\n    /// @notice This is an Ed25519 public key that is used to identify a node.\\n    /// This key is guaranteed to be unique in the CapabilitiesRegistry. It is\\n    /// used to identify a node in the the P2P network.\\n    bytes32 p2pId;\\n    /// @notice Public key used to encrypt secrets for this node\\n    bytes32 encryptionPublicKey;\\n    /// @notice The list of hashed capability IDs supported by the node\\n    bytes32[] hashedCapabilityIds;\\n    /// @notice The list of capabilities DON Ids supported by the node. A node\\n    /// can belong to multiple capabilities DONs. This list does not include a\\n    /// Workflow DON id if the node belongs to one.\\n    uint256[] capabilitiesDONIds;\\n  }\\n\\n  /// @notice Retrieves node information by its P2P ID.\\n  /// @param p2pId The P2P ID of the node to query for.\\n  /// @return nodeInfo The node data.\\n  function getNode(bytes32 p2pId) external view returns (NodeInfo memory nodeInfo);\\n\\n  /// @notice Retrieves all node information.\\n  /// @return NodeInfo[] Array of all nodes in the registry.\\n  function getNodes() external view returns (NodeInfo[] memory);\\n\\n  /// @notice Retrieves nodes by their P2P IDs.\\n  /// @param p2pIds Array of P2P IDs to query for.\\n  /// @return NodeInfo[] Array of node data corresponding to the provided P2P IDs.\\n  function getNodesByP2PIds(bytes32[] calldata p2pIds) external view returns (NodeInfo[] memory);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/interfaces/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (interfaces/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\nimport {IERC165} from \\\"../utils/introspection/IERC165.sol\\\";\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```solidity\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n    // To implement this library for multiple types with as little code\\n    // repetition as possible, we write it in terms of a generic Set type with\\n    // bytes32 values.\\n    // The Set implementation uses private functions, and user-facing\\n    // implementations (such as AddressSet) are just wrappers around the\\n    // underlying Set.\\n    // This means that we can only create new EnumerableSets for types that fit\\n    // in bytes32.\\n\\n    struct Set {\\n        // Storage of set values\\n        bytes32[] _values;\\n        // Position is the index of the value in the `values` array plus 1.\\n        // Position 0 is used to mean a value is not in the set.\\n        mapping(bytes32 value =\\u003e uint256) _positions;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function _add(Set storage set, bytes32 value) private returns (bool) {\\n        if (!_contains(set, value)) {\\n            set._values.push(value);\\n            // The value is stored at length-1, but we add 1 to all indexes\\n            // and use 0 as a sentinel value\\n            set._positions[value] = set._values.length;\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function _remove(Set storage set, bytes32 value) private returns (bool) {\\n        // We cache the value's position to prevent multiple reads from the same storage slot\\n        uint256 position = set._positions[value];\\n\\n        if (position != 0) {\\n            // Equivalent to contains(set, value)\\n            // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n            // the array, and then remove the last element (sometimes called as 'swap and pop').\\n            // This modifies the order of the array, as noted in {at}.\\n\\n            uint256 valueIndex = position - 1;\\n            uint256 lastIndex = set._values.length - 1;\\n\\n            if (valueIndex != lastIndex) {\\n                bytes32 lastValue = set._values[lastIndex];\\n\\n                // Move the lastValue to the index where the value to delete is\\n                set._values[valueIndex] = lastValue;\\n                // Update the tracked position of the lastValue (that was just moved)\\n                set._positions[lastValue] = position;\\n            }\\n\\n            // Delete the slot where the moved value was stored\\n            set._values.pop();\\n\\n            // Delete the tracked position for the deleted slot\\n            delete set._positions[value];\\n\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n        return set._positions[value] != 0;\\n    }\\n\\n    /**\\n     * @dev Returns the number of values on the set. O(1).\\n     */\\n    function _length(Set storage set) private view returns (uint256) {\\n        return set._values.length;\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n        return set._values[index];\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function _values(Set storage set) private view returns (bytes32[] memory) {\\n        return set._values;\\n    }\\n\\n    // Bytes32Set\\n\\n    struct Bytes32Set {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _add(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _remove(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n        return _contains(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(Bytes32Set storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n        return _at(set._inner, index);\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        bytes32[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // AddressSet\\n\\n    struct AddressSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(AddressSet storage set, address value) internal returns (bool) {\\n        return _add(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(AddressSet storage set, address value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(AddressSet storage set, address value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(AddressSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n        return address(uint160(uint256(_at(set._inner, index))));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(AddressSet storage set) internal view returns (address[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        address[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // UintSet\\n\\n    struct UintSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _add(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(UintSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n        return uint256(_at(set._inner, index));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(UintSet storage set) internal view returns (uint256[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        uint256[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n}\\n\"}}}"
