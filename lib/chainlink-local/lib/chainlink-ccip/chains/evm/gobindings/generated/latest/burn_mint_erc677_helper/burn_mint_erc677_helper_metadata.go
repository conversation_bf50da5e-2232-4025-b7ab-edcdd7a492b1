// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package burn_mint_erc677_helper

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/interfaces/IGetCCIPAdmin.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/test/helpers/BurnMintERC677Helper.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/ConfirmedOwner.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/ConfirmedOwnerWithProposal.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/OwnerIsCreator.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IERC677Receiver.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC677/BurnMintERC677.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC677/ERC677.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC677/IERC677.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/ERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Context.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/=node_modules/@chainlink/contracts/src/v0.8/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IGetCCIPAdmin.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IGetCCIPAdmin {\\n  /// @notice Returns the admin of the token.\\n  /// @dev This method is named to never conflict with existing methods.\\n  function getCCIPAdmin() external view returns (address);\\n}\\n\"},\"contracts/test/helpers/BurnMintERC677Helper.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IGetCCIPAdmin} from \\\"../../interfaces/IGetCCIPAdmin.sol\\\";\\nimport {BurnMintERC677} from \\\"@chainlink/shared/token/ERC677/BurnMintERC677.sol\\\";\\n\\ncontract BurnMintERC677Helper is BurnMintERC677, IGetCCIPAdmin {\\n  constructor(string memory name, string memory symbol) BurnMintERC677(name, symbol, 18, 0) {}\\n\\n  // Gives one full token to any given address.\\n  function drip(\\n    address to\\n  ) external {\\n    _mint(to, 1e18);\\n  }\\n\\n  function getCCIPAdmin() external view override returns (address) {\\n    return owner();\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/ConfirmedOwner.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {ConfirmedOwnerWithProposal} from \\\"./ConfirmedOwnerWithProposal.sol\\\";\\n\\n/// @title The ConfirmedOwner contract\\n/// @notice A contract with helpers for basic contract ownership.\\ncontract ConfirmedOwner is ConfirmedOwnerWithProposal {\\n  constructor(address newOwner) ConfirmedOwnerWithProposal(newOwner, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/ConfirmedOwnerWithProposal.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @title The ConfirmedOwner contract\\n/// @notice A contract with helpers for basic contract ownership.\\ncontract ConfirmedOwnerWithProposal is IOwnable {\\n  address private s_owner;\\n  address private s_pendingOwner;\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    // solhint-disable-next-line gas-custom-errors\\n    require(newOwner != address(0), \\\"Cannot set owner to zero\\\");\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    // solhint-disable-next-line gas-custom-errors\\n    require(msg.sender == s_pendingOwner, \\\"Must be proposed owner\\\");\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  function _transferOwnership(address to) private {\\n    // solhint-disable-next-line gas-custom-errors\\n    require(to != msg.sender, \\\"Cannot transfer to self\\\");\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    // solhint-disable-next-line gas-custom-errors\\n    require(msg.sender == s_owner, \\\"Only callable by owner\\\");\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/OwnerIsCreator.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {ConfirmedOwner} from \\\"./ConfirmedOwner.sol\\\";\\n\\n/// @title The OwnerIsCreator contract\\n/// @notice A contract with helpers for basic contract ownership.\\ncontract OwnerIsCreator is ConfirmedOwner {\\n  constructor() ConfirmedOwner(msg.sender) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IERC677Receiver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.6;\\n\\ninterface IERC677Receiver {\\n  function onTokenTransfer(address sender, uint256 amount, bytes calldata data) external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC20/IBurnMintERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IERC20} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\n\\ninterface IBurnMintERC20 is IERC20 {\\n  /// @notice Mints new tokens for a given address.\\n  /// @param account The address to mint the new tokens to.\\n  /// @param amount The number of tokens to be minted.\\n  /// @dev this function increases the total supply.\\n  function mint(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from the sender.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burn(address account, uint256 amount) external;\\n\\n  /// @notice Burns tokens from a given address..\\n  /// @param account The address to burn tokens from.\\n  /// @param amount The number of tokens to be burned.\\n  /// @dev this function decreases the total supply.\\n  function burnFrom(address account, uint256 amount) external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC677/BurnMintERC677.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IBurnMintERC20} from \\\"../ERC20/IBurnMintERC20.sol\\\";\\nimport {IERC677} from \\\"./IERC677.sol\\\";\\n\\nimport {ERC677} from \\\"./ERC677.sol\\\";\\nimport {OwnerIsCreator} from \\\"../../access/OwnerIsCreator.sol\\\";\\n\\nimport {ERC20Burnable} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/ERC20Burnable.sol\\\";\\nimport {EnumerableSet} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\\\";\\nimport {IERC165} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\\\";\\nimport {IERC20} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\\\";\\n\\n/// @notice A basic ERC677 compatible token contract with burn and minting roles.\\n/// @dev The total supply can be limited during deployment.\\ncontract BurnMintERC677 is IBurnMintERC20, ERC677, IERC165, ERC20Burnable, OwnerIsCreator {\\n  using EnumerableSet for EnumerableSet.AddressSet;\\n\\n  error SenderNotMinter(address sender);\\n  error SenderNotBurner(address sender);\\n  error MaxSupplyExceeded(uint256 supplyAfterMint);\\n\\n  event MintAccessGranted(address indexed minter);\\n  event BurnAccessGranted(address indexed burner);\\n  event MintAccessRevoked(address indexed minter);\\n  event BurnAccessRevoked(address indexed burner);\\n\\n  // @dev the allowed minter addresses\\n  EnumerableSet.AddressSet internal s_minters;\\n  // @dev the allowed burner addresses\\n  EnumerableSet.AddressSet internal s_burners;\\n\\n  /// @dev The number of decimals for the token\\n  uint8 internal immutable i_decimals;\\n\\n  /// @dev The maximum supply of the token, 0 if unlimited\\n  uint256 internal immutable i_maxSupply;\\n\\n  constructor(string memory name, string memory symbol, uint8 decimals_, uint256 maxSupply_) ERC677(name, symbol) {\\n    i_decimals = decimals_;\\n    i_maxSupply = maxSupply_;\\n  }\\n\\n  function supportsInterface(bytes4 interfaceId) public pure virtual override returns (bool) {\\n    return\\n      interfaceId == type(IERC20).interfaceId ||\\n      interfaceId == type(IERC677).interfaceId ||\\n      interfaceId == type(IBurnMintERC20).interfaceId ||\\n      interfaceId == type(IERC165).interfaceId;\\n  }\\n\\n  // ================================================================\\n  // |                            ERC20                             |\\n  // ================================================================\\n\\n  /// @dev Returns the number of decimals used in its user representation.\\n  function decimals() public view virtual override returns (uint8) {\\n    return i_decimals;\\n  }\\n\\n  /// @dev Returns the max supply of the token, 0 if unlimited.\\n  function maxSupply() public view virtual returns (uint256) {\\n    return i_maxSupply;\\n  }\\n\\n  /// @dev Uses OZ ERC20 _transfer to disallow sending to address(0).\\n  /// @dev Disallows sending to address(this)\\n  function _transfer(address from, address to, uint256 amount) internal virtual override validAddress(to) {\\n    super._transfer(from, to, amount);\\n  }\\n\\n  /// @dev Uses OZ ERC20 _approve to disallow approving for address(0).\\n  /// @dev Disallows approving for address(this)\\n  function _approve(address owner, address spender, uint256 amount) internal virtual override validAddress(spender) {\\n    super._approve(owner, spender, amount);\\n  }\\n\\n  /// @dev Exists to be backwards compatible with the older naming convention.\\n  function decreaseApproval(address spender, uint256 subtractedValue) external returns (bool success) {\\n    return decreaseAllowance(spender, subtractedValue);\\n  }\\n\\n  /// @dev Exists to be backwards compatible with the older naming convention.\\n  function increaseApproval(address spender, uint256 addedValue) external {\\n    increaseAllowance(spender, addedValue);\\n  }\\n\\n  /// @notice Check if recipient is valid (not this contract address).\\n  /// @param recipient the account we transfer/approve to.\\n  /// @dev Reverts with an empty revert to be compatible with the existing link token when\\n  /// the recipient is this contract address.\\n  modifier validAddress(address recipient) virtual {\\n    // solhint-disable-next-line reason-string, gas-custom-errors\\n    if (recipient == address(this)) revert();\\n    _;\\n  }\\n\\n  // ================================================================\\n  // |                      Burning \\u0026 minting                       |\\n  // ================================================================\\n\\n  /// @inheritdoc ERC20Burnable\\n  /// @dev Uses OZ ERC20 _burn to disallow burning from address(0).\\n  /// @dev Decreases the total supply.\\n  function burn(uint256 amount) public override(IBurnMintERC20, ERC20Burnable) onlyBurner {\\n    super.burn(amount);\\n  }\\n\\n  /// @inheritdoc IBurnMintERC20\\n  /// @dev Alias for BurnFrom for compatibility with the older naming convention.\\n  /// @dev Uses burnFrom for all validation \\u0026 logic.\\n  function burn(address account, uint256 amount) public virtual override {\\n    burnFrom(account, amount);\\n  }\\n\\n  /// @inheritdoc ERC20Burnable\\n  /// @dev Uses OZ ERC20 _burn to disallow burning from address(0).\\n  /// @dev Decreases the total supply.\\n  function burnFrom(address account, uint256 amount) public override(IBurnMintERC20, ERC20Burnable) onlyBurner {\\n    super.burnFrom(account, amount);\\n  }\\n\\n  /// @inheritdoc IBurnMintERC20\\n  /// @dev Uses OZ ERC20 _mint to disallow minting to address(0).\\n  /// @dev Disallows minting to address(this)\\n  /// @dev Increases the total supply.\\n  function mint(address account, uint256 amount) external override onlyMinter validAddress(account) {\\n    if (i_maxSupply != 0 \\u0026\\u0026 totalSupply() + amount \\u003e i_maxSupply) revert MaxSupplyExceeded(totalSupply() + amount);\\n\\n    _mint(account, amount);\\n  }\\n\\n  // ================================================================\\n  // |                            Roles                             |\\n  // ================================================================\\n\\n  /// @notice grants both mint and burn roles to `burnAndMinter`.\\n  /// @dev calls public functions so this function does not require\\n  /// access controls. This is handled in the inner functions.\\n  function grantMintAndBurnRoles(address burnAndMinter) external {\\n    grantMintRole(burnAndMinter);\\n    grantBurnRole(burnAndMinter);\\n  }\\n\\n  /// @notice Grants mint role to the given address.\\n  /// @dev only the owner can call this function.\\n  function grantMintRole(address minter) public onlyOwner {\\n    if (s_minters.add(minter)) {\\n      emit MintAccessGranted(minter);\\n    }\\n  }\\n\\n  /// @notice Grants burn role to the given address.\\n  /// @dev only the owner can call this function.\\n  function grantBurnRole(address burner) public onlyOwner {\\n    if (s_burners.add(burner)) {\\n      emit BurnAccessGranted(burner);\\n    }\\n  }\\n\\n  /// @notice Revokes mint role for the given address.\\n  /// @dev only the owner can call this function.\\n  function revokeMintRole(address minter) public onlyOwner {\\n    if (s_minters.remove(minter)) {\\n      emit MintAccessRevoked(minter);\\n    }\\n  }\\n\\n  /// @notice Revokes burn role from the given address.\\n  /// @dev only the owner can call this function\\n  function revokeBurnRole(address burner) public onlyOwner {\\n    if (s_burners.remove(burner)) {\\n      emit BurnAccessRevoked(burner);\\n    }\\n  }\\n\\n  /// @notice Returns all permissioned minters\\n  function getMinters() public view returns (address[] memory) {\\n    return s_minters.values();\\n  }\\n\\n  /// @notice Returns all permissioned burners\\n  function getBurners() public view returns (address[] memory) {\\n    return s_burners.values();\\n  }\\n\\n  // ================================================================\\n  // |                            Access                            |\\n  // ================================================================\\n\\n  /// @notice Checks whether a given address is a minter for this token.\\n  /// @return true if the address is allowed to mint.\\n  function isMinter(address minter) public view returns (bool) {\\n    return s_minters.contains(minter);\\n  }\\n\\n  /// @notice Checks whether a given address is a burner for this token.\\n  /// @return true if the address is allowed to burn.\\n  function isBurner(address burner) public view returns (bool) {\\n    return s_burners.contains(burner);\\n  }\\n\\n  /// @notice Checks whether the msg.sender is a permissioned minter for this token\\n  /// @dev Reverts with a SenderNotMinter if the check fails\\n  modifier onlyMinter() {\\n    if (!isMinter(msg.sender)) revert SenderNotMinter(msg.sender);\\n    _;\\n  }\\n\\n  /// @notice Checks whether the msg.sender is a permissioned burner for this token\\n  /// @dev Reverts with a SenderNotBurner if the check fails\\n  modifier onlyBurner() {\\n    if (!isBurner(msg.sender)) revert SenderNotBurner(msg.sender);\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC677/ERC677.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\npragma solidity ^0.8.0;\\n\\nimport {IERC677} from \\\"./IERC677.sol\\\";\\nimport {IERC677Receiver} from \\\"../../interfaces/IERC677Receiver.sol\\\";\\n\\nimport {ERC20} from \\\"../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/ERC20.sol\\\";\\n\\ncontract ERC677 is IERC677, ERC20 {\\n  constructor(string memory name, string memory symbol) ERC20(name, symbol) {}\\n\\n  /// @inheritdoc IERC677\\n  function transferAndCall(address to, uint256 amount, bytes memory data) public returns (bool success) {\\n    super.transfer(to, amount);\\n    emit Transfer(msg.sender, to, amount, data);\\n    if (to.code.length \\u003e 0) {\\n      IERC677Receiver(to).onTokenTransfer(msg.sender, amount, data);\\n    }\\n    return true;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/token/ERC677/IERC677.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IERC677 {\\n  event Transfer(address indexed from, address indexed to, uint256 value, bytes data);\\n\\n  /// @notice Transfer tokens from `msg.sender` to another address and then call `onTransferReceived` on receiver\\n  /// @param to The address which you want to transfer to\\n  /// @param amount The amount of tokens to be transferred\\n  /// @param data bytes Additional data with no specified format, sent in call to `to`\\n  /// @return true unless throwing\\n  function transferAndCall(address to, uint256 amount, bytes memory data) external returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/ERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (token/ERC20/ERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC20.sol\\\";\\nimport \\\"./extensions/IERC20Metadata.sol\\\";\\nimport \\\"../../utils/Context.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC20} interface.\\n *\\n * This implementation is agnostic to the way tokens are created. This means\\n * that a supply mechanism has to be added in a derived contract using {_mint}.\\n * For a generic mechanism see {ERC20PresetMinterPauser}.\\n *\\n * TIP: For a detailed writeup see our guide\\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\\n * to implement supply mechanisms].\\n *\\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\\n * instead returning `false` on failure. This behavior is nonetheless\\n * conventional and does not conflict with the expectations of ERC20\\n * applications.\\n *\\n * Additionally, an {Approval} event is emitted on calls to {transferFrom}.\\n * This allows applications to reconstruct the allowance for all accounts just\\n * by listening to said events. Other implementations of the EIP may not emit\\n * these events, as it isn't required by the specification.\\n *\\n * Finally, the non-standard {decreaseAllowance} and {increaseAllowance}\\n * functions have been added to mitigate the well-known issues around setting\\n * allowances. See {IERC20-approve}.\\n */\\ncontract ERC20 is Context, IERC20, IERC20Metadata {\\n  mapping(address =\\u003e uint256) private _balances;\\n\\n  mapping(address =\\u003e mapping(address =\\u003e uint256)) private _allowances;\\n\\n  uint256 private _totalSupply;\\n\\n  string private _name;\\n  string private _symbol;\\n\\n  /**\\n   * @dev Sets the values for {name} and {symbol}.\\n   *\\n   * The default value of {decimals} is 18. To select a different value for\\n   * {decimals} you should overload it.\\n   *\\n   * All two of these values are immutable: they can only be set once during\\n   * construction.\\n   */\\n  constructor(string memory name_, string memory symbol_) {\\n    _name = name_;\\n    _symbol = symbol_;\\n  }\\n\\n  /**\\n   * @dev Returns the name of the token.\\n   */\\n  function name() public view virtual override returns (string memory) {\\n    return _name;\\n  }\\n\\n  /**\\n   * @dev Returns the symbol of the token, usually a shorter version of the\\n   * name.\\n   */\\n  function symbol() public view virtual override returns (string memory) {\\n    return _symbol;\\n  }\\n\\n  /**\\n   * @dev Returns the number of decimals used to get its user representation.\\n   * For example, if `decimals` equals `2`, a balance of `505` tokens should\\n   * be displayed to a user as `5.05` (`505 / 10 ** 2`).\\n   *\\n   * Tokens usually opt for a value of 18, imitating the relationship between\\n   * Ether and Wei. This is the value {ERC20} uses, unless this function is\\n   * overridden;\\n   *\\n   * NOTE: This information is only used for _display_ purposes: it in\\n   * no way affects any of the arithmetic of the contract, including\\n   * {IERC20-balanceOf} and {IERC20-transfer}.\\n   */\\n  function decimals() public view virtual override returns (uint8) {\\n    return 18;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-totalSupply}.\\n   */\\n  function totalSupply() public view virtual override returns (uint256) {\\n    return _totalSupply;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-balanceOf}.\\n   */\\n  function balanceOf(address account) public view virtual override returns (uint256) {\\n    return _balances[account];\\n  }\\n\\n  /**\\n   * @dev See {IERC20-transfer}.\\n   *\\n   * Requirements:\\n   *\\n   * - `to` cannot be the zero address.\\n   * - the caller must have a balance of at least `amount`.\\n   */\\n  function transfer(address to, uint256 amount) public virtual override returns (bool) {\\n    address owner = _msgSender();\\n    _transfer(owner, to, amount);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-allowance}.\\n   */\\n  function allowance(address owner, address spender) public view virtual override returns (uint256) {\\n    return _allowances[owner][spender];\\n  }\\n\\n  /**\\n   * @dev See {IERC20-approve}.\\n   *\\n   * NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on\\n   * `transferFrom`. This is semantically equivalent to an infinite approval.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   */\\n  function approve(address spender, uint256 amount) public virtual override returns (bool) {\\n    address owner = _msgSender();\\n    _approve(owner, spender, amount);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev See {IERC20-transferFrom}.\\n   *\\n   * Emits an {Approval} event indicating the updated allowance. This is not\\n   * required by the EIP. See the note at the beginning of {ERC20}.\\n   *\\n   * NOTE: Does not update the allowance if the current allowance\\n   * is the maximum `uint256`.\\n   *\\n   * Requirements:\\n   *\\n   * - `from` and `to` cannot be the zero address.\\n   * - `from` must have a balance of at least `amount`.\\n   * - the caller must have allowance for ``from``'s tokens of at least\\n   * `amount`.\\n   */\\n  function transferFrom(address from, address to, uint256 amount) public virtual override returns (bool) {\\n    address spender = _msgSender();\\n    _spendAllowance(from, spender, amount);\\n    _transfer(from, to, amount);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev Atomically increases the allowance granted to `spender` by the caller.\\n   *\\n   * This is an alternative to {approve} that can be used as a mitigation for\\n   * problems described in {IERC20-approve}.\\n   *\\n   * Emits an {Approval} event indicating the updated allowance.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   */\\n  function increaseAllowance(address spender, uint256 addedValue) public virtual returns (bool) {\\n    address owner = _msgSender();\\n    _approve(owner, spender, allowance(owner, spender) + addedValue);\\n    return true;\\n  }\\n\\n  /**\\n   * @dev Atomically decreases the allowance granted to `spender` by the caller.\\n   *\\n   * This is an alternative to {approve} that can be used as a mitigation for\\n   * problems described in {IERC20-approve}.\\n   *\\n   * Emits an {Approval} event indicating the updated allowance.\\n   *\\n   * Requirements:\\n   *\\n   * - `spender` cannot be the zero address.\\n   * - `spender` must have allowance for the caller of at least\\n   * `subtractedValue`.\\n   */\\n  function decreaseAllowance(address spender, uint256 subtractedValue) public virtual returns (bool) {\\n    address owner = _msgSender();\\n    uint256 currentAllowance = allowance(owner, spender);\\n    require(currentAllowance \\u003e= subtractedValue, \\\"ERC20: decreased allowance below zero\\\");\\n    unchecked {\\n      _approve(owner, spender, currentAllowance - subtractedValue);\\n    }\\n\\n    return true;\\n  }\\n\\n  /**\\n   * @dev Moves `amount` of tokens from `from` to `to`.\\n   *\\n   * This internal function is equivalent to {transfer}, and can be used to\\n   * e.g. implement automatic token fees, slashing mechanisms, etc.\\n   *\\n   * Emits a {Transfer} event.\\n   *\\n   * Requirements:\\n   *\\n   * - `from` cannot be the zero address.\\n   * - `to` cannot be the zero address.\\n   * - `from` must have a balance of at least `amount`.\\n   */\\n  function _transfer(address from, address to, uint256 amount) internal virtual {\\n    require(from != address(0), \\\"ERC20: transfer from the zero address\\\");\\n    require(to != address(0), \\\"ERC20: transfer to the zero address\\\");\\n\\n    _beforeTokenTransfer(from, to, amount);\\n\\n    uint256 fromBalance = _balances[from];\\n    require(fromBalance \\u003e= amount, \\\"ERC20: transfer amount exceeds balance\\\");\\n    unchecked {\\n      _balances[from] = fromBalance - amount;\\n      // Overflow not possible: the sum of all balances is capped by totalSupply, and the sum is preserved by\\n      // decrementing then incrementing.\\n      _balances[to] += amount;\\n    }\\n\\n    emit Transfer(from, to, amount);\\n\\n    _afterTokenTransfer(from, to, amount);\\n  }\\n\\n  /** @dev Creates `amount` tokens and assigns them to `account`, increasing\\n   * the total supply.\\n   *\\n   * Emits a {Transfer} event with `from` set to the zero address.\\n   *\\n   * Requirements:\\n   *\\n   * - `account` cannot be the zero address.\\n   */\\n  function _mint(address account, uint256 amount) internal virtual {\\n    require(account != address(0), \\\"ERC20: mint to the zero address\\\");\\n\\n    _beforeTokenTransfer(address(0), account, amount);\\n\\n    _totalSupply += amount;\\n    unchecked {\\n      // Overflow not possible: balance + amount is at most totalSupply + amount, which is checked above.\\n      _balances[account] += amount;\\n    }\\n    emit Transfer(address(0), account, amount);\\n\\n    _afterTokenTransfer(address(0), account, amount);\\n  }\\n\\n  /**\\n   * @dev Destroys `amount` tokens from `account`, reducing the\\n   * total supply.\\n   *\\n   * Emits a {Transfer} event with `to` set to the zero address.\\n   *\\n   * Requirements:\\n   *\\n   * - `account` cannot be the zero address.\\n   * - `account` must have at least `amount` tokens.\\n   */\\n  function _burn(address account, uint256 amount) internal virtual {\\n    require(account != address(0), \\\"ERC20: burn from the zero address\\\");\\n\\n    _beforeTokenTransfer(account, address(0), amount);\\n\\n    uint256 accountBalance = _balances[account];\\n    require(accountBalance \\u003e= amount, \\\"ERC20: burn amount exceeds balance\\\");\\n    unchecked {\\n      _balances[account] = accountBalance - amount;\\n      // Overflow not possible: amount \\u003c= accountBalance \\u003c= totalSupply.\\n      _totalSupply -= amount;\\n    }\\n\\n    emit Transfer(account, address(0), amount);\\n\\n    _afterTokenTransfer(account, address(0), amount);\\n  }\\n\\n  /**\\n   * @dev Sets `amount` as the allowance of `spender` over the `owner` s tokens.\\n   *\\n   * This internal function is equivalent to `approve`, and can be used to\\n   * e.g. set automatic allowances for certain subsystems, etc.\\n   *\\n   * Emits an {Approval} event.\\n   *\\n   * Requirements:\\n   *\\n   * - `owner` cannot be the zero address.\\n   * - `spender` cannot be the zero address.\\n   */\\n  function _approve(address owner, address spender, uint256 amount) internal virtual {\\n    require(owner != address(0), \\\"ERC20: approve from the zero address\\\");\\n    require(spender != address(0), \\\"ERC20: approve to the zero address\\\");\\n\\n    _allowances[owner][spender] = amount;\\n    emit Approval(owner, spender, amount);\\n  }\\n\\n  /**\\n   * @dev Updates `owner` s allowance for `spender` based on spent `amount`.\\n   *\\n   * Does not update the allowance amount in case of infinite allowance.\\n   * Revert if not enough allowance is available.\\n   *\\n   * Might emit an {Approval} event.\\n   */\\n  function _spendAllowance(address owner, address spender, uint256 amount) internal virtual {\\n    uint256 currentAllowance = allowance(owner, spender);\\n    if (currentAllowance != type(uint256).max) {\\n      require(currentAllowance \\u003e= amount, \\\"ERC20: insufficient allowance\\\");\\n      unchecked {\\n        _approve(owner, spender, currentAllowance - amount);\\n      }\\n    }\\n  }\\n\\n  /**\\n   * @dev Hook that is called before any transfer of tokens. This includes\\n   * minting and burning.\\n   *\\n   * Calling conditions:\\n   *\\n   * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n   * will be transferred to `to`.\\n   * - when `from` is zero, `amount` tokens will be minted for `to`.\\n   * - when `to` is zero, `amount` of ``from``'s tokens will be burned.\\n   * - `from` and `to` are never both zero.\\n   *\\n   * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n   */\\n  function _beforeTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n\\n  /**\\n   * @dev Hook that is called after any transfer of tokens. This includes\\n   * minting and burning.\\n   *\\n   * Calling conditions:\\n   *\\n   * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n   * has been transferred to `to`.\\n   * - when `from` is zero, `amount` tokens have been minted for `to`.\\n   * - when `to` is zero, `amount` of ``from``'s tokens have been burned.\\n   * - `from` and `to` are never both zero.\\n   *\\n   * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n   */\\n  function _afterTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.6.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20 {\\n  /**\\n   * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n   * another (`to`).\\n   *\\n   * Note that `value` may be zero.\\n   */\\n  event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n  /**\\n   * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n   * a call to {approve}. `value` is the new allowance.\\n   */\\n  event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n  /**\\n   * @dev Returns the amount of tokens in existence.\\n   */\\n  function totalSupply() external view returns (uint256);\\n\\n  /**\\n   * @dev Returns the amount of tokens owned by `account`.\\n   */\\n  function balanceOf(address account) external view returns (uint256);\\n\\n  /**\\n   * @dev Moves `amount` tokens from the caller's account to `to`.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transfer(address to, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Returns the remaining number of tokens that `spender` will be\\n   * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n   * zero by default.\\n   *\\n   * This value changes when {approve} or {transferFrom} are called.\\n   */\\n  function allowance(address owner, address spender) external view returns (uint256);\\n\\n  /**\\n   * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n   * that someone may use both the old and the new allowance by unfortunate\\n   * transaction ordering. One possible solution to mitigate this race\\n   * condition is to first reduce the spender's allowance to 0 and set the\\n   * desired value afterwards:\\n   * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n   *\\n   * Emits an {Approval} event.\\n   */\\n  function approve(address spender, uint256 amount) external returns (bool);\\n\\n  /**\\n   * @dev Moves `amount` tokens from `from` to `to` using the\\n   * allowance mechanism. `amount` is then deducted from the caller's\\n   * allowance.\\n   *\\n   * Returns a boolean value indicating whether the operation succeeded.\\n   *\\n   * Emits a {Transfer} event.\\n   */\\n  function transferFrom(address from, address to, uint256 amount) external returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/ERC20Burnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.5.0) (token/ERC20/extensions/ERC20Burnable.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../ERC20.sol\\\";\\nimport \\\"../../../utils/Context.sol\\\";\\n\\n/**\\n * @dev Extension of {ERC20} that allows token holders to destroy both their own\\n * tokens and those that they have an allowance for, in a way that can be\\n * recognized off-chain (via event analysis).\\n */\\nabstract contract ERC20Burnable is Context, ERC20 {\\n  /**\\n   * @dev Destroys `amount` tokens from the caller.\\n   *\\n   * See {ERC20-_burn}.\\n   */\\n  function burn(uint256 amount) public virtual {\\n    _burn(_msgSender(), amount);\\n  }\\n\\n  /**\\n   * @dev Destroys `amount` tokens from `account`, deducting from the caller's\\n   * allowance.\\n   *\\n   * See {ERC20-_burn} and {ERC20-allowance}.\\n   *\\n   * Requirements:\\n   *\\n   * - the caller must have allowance for ``accounts``'s tokens of at least\\n   * `amount`.\\n   */\\n  function burnFrom(address account, uint256 amount) public virtual {\\n    _spendAllowance(account, _msgSender(), amount);\\n    _burn(account, amount);\\n  }\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/extensions/IERC20Metadata.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC20/extensions/IERC20Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC20.sol\\\";\\n\\n/**\\n * @dev Interface for the optional metadata functions from the ERC20 standard.\\n *\\n * _Available since v4.1._\\n */\\ninterface IERC20Metadata is IERC20 {\\n  /**\\n   * @dev Returns the name of the token.\\n   */\\n  function name() external view returns (string memory);\\n\\n  /**\\n   * @dev Returns the symbol of the token.\\n   */\\n  function symbol() external view returns (string memory);\\n\\n  /**\\n   * @dev Returns the decimals places of the token.\\n   */\\n  function decimals() external view returns (uint8);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/Context.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Context.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract Context {\\n  function _msgSender() internal view virtual returns (address) {\\n    return msg.sender;\\n  }\\n\\n  function _msgData() internal view virtual returns (bytes calldata) {\\n    return msg.data;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n  // To implement this library for multiple types with as little code\\n  // repetition as possible, we write it in terms of a generic Set type with\\n  // bytes32 values.\\n  // The Set implementation uses private functions, and user-facing\\n  // implementations (such as AddressSet) are just wrappers around the\\n  // underlying Set.\\n  // This means that we can only create new EnumerableSets for types that fit\\n  // in bytes32.\\n\\n  struct Set {\\n    // Storage of set values\\n    bytes32[] _values;\\n    // Position of the value in the `values` array, plus 1 because index 0\\n    // means a value is not in the set.\\n    mapping(bytes32 =\\u003e uint256) _indexes;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function _add(Set storage set, bytes32 value) private returns (bool) {\\n    if (!_contains(set, value)) {\\n      set._values.push(value);\\n      // The value is stored at length-1, but we add 1 to all indexes\\n      // and use 0 as a sentinel value\\n      set._indexes[value] = set._values.length;\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function _remove(Set storage set, bytes32 value) private returns (bool) {\\n    // We read and store the value's index to prevent multiple reads from the same storage slot\\n    uint256 valueIndex = set._indexes[value];\\n\\n    if (valueIndex != 0) {\\n      // Equivalent to contains(set, value)\\n      // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n      // the array, and then remove the last element (sometimes called as 'swap and pop').\\n      // This modifies the order of the array, as noted in {at}.\\n\\n      uint256 toDeleteIndex = valueIndex - 1;\\n      uint256 lastIndex = set._values.length - 1;\\n\\n      if (lastIndex != toDeleteIndex) {\\n        bytes32 lastValue = set._values[lastIndex];\\n\\n        // Move the last value to the index where the value to delete is\\n        set._values[toDeleteIndex] = lastValue;\\n        // Update the index for the moved value\\n        set._indexes[lastValue] = valueIndex; // Replace lastValue's index to valueIndex\\n      }\\n\\n      // Delete the slot where the moved value was stored\\n      set._values.pop();\\n\\n      // Delete the index for the deleted slot\\n      delete set._indexes[value];\\n\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n    return set._indexes[value] != 0;\\n  }\\n\\n  /**\\n   * @dev Returns the number of values on the set. O(1).\\n   */\\n  function _length(Set storage set) private view returns (uint256) {\\n    return set._values.length;\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n    return set._values[index];\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function _values(Set storage set) private view returns (bytes32[] memory) {\\n    return set._values;\\n  }\\n\\n  // Bytes32Set\\n\\n  struct Bytes32Set {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _add(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _remove(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n    return _contains(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(Bytes32Set storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n    return _at(set._inner, index);\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    bytes32[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // AddressSet\\n\\n  struct AddressSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(AddressSet storage set, address value) internal returns (bool) {\\n    return _add(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(AddressSet storage set, address value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(AddressSet storage set, address value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(AddressSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n    return address(uint160(uint256(_at(set._inner, index))));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(AddressSet storage set) internal view returns (address[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    address[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // UintSet\\n\\n  struct UintSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _add(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(UintSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n    return uint256(_at(set._inner, index));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(UintSet storage set) internal view returns (uint256[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    uint256[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n}\\n\"}}}"
