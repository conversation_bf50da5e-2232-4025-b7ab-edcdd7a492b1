// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package offramp_with_message_transformer

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":800},\"outputSelection\":{\"contracts/interfaces/IAny2EVMMessageReceiver.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IFeeQuoter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IMessageInterceptor.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IMessageTransformer.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/INonceManager.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IPool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IPriceRegistry.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IRMNRemote.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IRouter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/ITokenAdminRegistry.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Client.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/ERC165CheckerReverting.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Internal.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/MerkleMultiProof.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Pool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/ocr/MultiOCR3Base.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/offRamp/OffRamp.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/offRamp/OffRampWithMessageTransformer.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/call/CallWithExactGas.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/token/ERC20/IERC20.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IAny2EVMMessageReceiver.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\n\\n/// @notice Application contracts that intend to receive messages from  the router should implement this interface.\\ninterface IAny2EVMMessageReceiver {\\n  /// @notice Called by the Router to deliver a message. If this reverts, any token transfers also revert.\\n  /// The message will move to a FAILED state and become available for manual execution.\\n  /// @param message CCIP Message.\\n  /// @dev Note ensure you check the msg.sender is the OffRampRouter.\\n  function ccipReceive(\\n    Client.Any2EVMMessage calldata message\\n  ) external;\\n}\\n\"},\"contracts/interfaces/IFeeQuoter.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {IPriceRegistry} from \\\"./IPriceRegistry.sol\\\";\\n\\ninterface IFeeQuoter is IPriceRegistry {\\n  /// @notice Validates the ccip message \\u0026 returns the fee.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param message The message to get quote for.\\n  /// @return feeTokenAmount The amount of fee token needed for the fee, in smallest denomination of the fee token.\\n  function getValidatedFee(\\n    uint64 destChainSelector,\\n    Client.EVM2AnyMessage calldata message\\n  ) external view returns (uint256 feeTokenAmount);\\n\\n  /// @notice Converts the extraArgs to the latest version and returns the converted message fee in juels.\\n  /// @notice Validates pool return data.\\n  /// @param destChainSelector destination chain selector to process, must be a configured valid chain.\\n  /// @param feeToken token address used to pay for message fees, must be a configured valid fee token.\\n  /// @param feeTokenAmount Fee token amount.\\n  /// @param extraArgs Message extra args that were passed in by the client.\\n  /// @param messageReceiver Message receiver address in bytes from EVM2AnyMessage.receiver\\n  /// @return msgFeeJuels message fee in juels.\\n  /// @return isOutOfOrderExecution true if the message should be executed out of order.\\n  /// @return convertedExtraArgs extra args converted to the latest family-specific args version.\\n  /// @return tokenReceiver token receiver address in bytes on destination chain\\n  function processMessageArgs(\\n    uint64 destChainSelector,\\n    address feeToken,\\n    uint256 feeTokenAmount,\\n    bytes calldata extraArgs,\\n    bytes calldata messageReceiver\\n  )\\n    external\\n    view\\n    returns (\\n      uint256 msgFeeJuels,\\n      bool isOutOfOrderExecution,\\n      bytes memory convertedExtraArgs,\\n      bytes memory tokenReceiver\\n    );\\n\\n  /// @notice Validates pool return data.\\n  /// @param destChainSelector Destination chain selector to which the token amounts are sent to.\\n  /// @param onRampTokenTransfers Token amounts with populated pool return data.\\n  /// @param sourceTokenAmounts Token amounts originally sent in a Client.EVM2AnyMessage message.\\n  /// @return destExecDataPerToken Destination chain execution data.\\n  function processPoolReturnData(\\n    uint64 destChainSelector,\\n    Internal.EVM2AnyTokenTransfer[] calldata onRampTokenTransfers,\\n    Client.EVMTokenAmount[] calldata sourceTokenAmounts\\n  ) external view returns (bytes[] memory destExecDataPerToken);\\n}\\n\"},\"contracts/interfaces/IMessageInterceptor.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\n\\n/// @notice Interface for plug-in message hook contracts that intercept OffRamp \\u0026 OnRamp messages and perform\\n/// validations / state changes on top of the messages. The interceptor functions are expected to revert\\n/// on validation failures.\\ninterface IMessageInterceptor {\\n  /// @notice Common error that can be thrown on validation failures and used by consumers.\\n  /// @param errorReason abi encoded revert reason.\\n  error MessageValidationError(bytes errorReason);\\n\\n  /// @notice Intercepts \\u0026 validates the given OffRamp message. Reverts on validation failure.\\n  /// @param message to validate.\\n  function onInboundMessage(\\n    Client.Any2EVMMessage memory message\\n  ) external;\\n\\n  /// @notice Intercepts \\u0026 validates the given OnRamp message. Reverts on validation failure.\\n  /// @param destChainSelector remote destination chain selector where the message is being sent to.\\n  /// @param message to validate.\\n  function onOutboundMessage(uint64 destChainSelector, Client.EVM2AnyMessage memory message) external;\\n}\\n\"},\"contracts/interfaces/IMessageTransformer.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\n\\n/// @notice Interface for message hook contracts that transform OffRamp \\u0026 OnRamp messages.\\n/// The transformer functions are expected to revert on transform failures.\\ninterface IMessageTransformer {\\n  /// @notice Common error that can be thrown on transform failures and used by consumers\\n  /// @param errorReason abi encoded revert reason\\n  error MessageTransformError(bytes errorReason);\\n\\n  /// @notice Transforms the given OnRamp message. Reverts on transform failure\\n  /// Can be used for modifying message prior to execution\\n  /// e.g. adding extra metadata to the data field in CCIP message\\n  /// by receiver contract\\n  /// @param message to transform\\n  /// @return transformed message\\n  function transformOutboundMessage(\\n    Internal.EVM2AnyRampMessage memory message\\n  ) external returns (Internal.EVM2AnyRampMessage memory);\\n\\n  /// @notice Transforms the given OffRamp message. Reverts on transform failure\\n  /// One possible use case is to add extra metadata to the data field in CCIP message\\n  /// before consumption by the receiver contract\\n  /// @param message to transform\\n  /// @return transformed message\\n  function transformInboundMessage(\\n    Internal.Any2EVMRampMessage memory message\\n  ) external returns (Internal.Any2EVMRampMessage memory);\\n}\\n\"},\"contracts/interfaces/INonceManager.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice Contract interface that allows managing sender nonces.\\ninterface INonceManager {\\n  /// @notice Increments the outbound nonce for a given sender on a given destination chain.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param sender The sender address.\\n  /// @return incrementedOutboundNonce The new outbound nonce.\\n  function getIncrementedOutboundNonce(uint64 destChainSelector, address sender) external returns (uint64);\\n\\n  /// @notice Increments the inbound nonce for a given sender on a given source chain.\\n  /// @notice The increment is only applied if the resulting nonce matches the expectedNonce.\\n  /// @param sourceChainSelector The destination chain selector.\\n  /// @param expectedNonce The expected inbound nonce.\\n  /// @param sender The encoded sender address.\\n  /// @return incremented True if the nonce was incremented, false otherwise.\\n  function incrementInboundNonce(\\n    uint64 sourceChainSelector,\\n    uint64 expectedNonce,\\n    bytes calldata sender\\n  ) external returns (bool);\\n}\\n\"},\"contracts/interfaces/IPool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\n\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\\\";\\n\\n/// @notice Shared public interface for multiple V1 pool types.\\n/// Each pool type handles a different child token model e.g. lock/unlock, mint/burn.\\ninterface IPoolV1 is IERC165 {\\n  /// @notice Lock tokens into the pool or burn the tokens.\\n  /// @param lockOrBurnIn Encoded data fields for the processing of tokens on the source chain.\\n  /// @return lockOrBurnOut Encoded data fields for the processing of tokens on the destination chain.\\n  function lockOrBurn(\\n    Pool.LockOrBurnInV1 calldata lockOrBurnIn\\n  ) external returns (Pool.LockOrBurnOutV1 memory lockOrBurnOut);\\n\\n  /// @notice Releases or mints tokens to the receiver address.\\n  /// @param releaseOrMintIn All data required to release or mint tokens.\\n  /// @return releaseOrMintOut The amount of tokens released or minted on the local chain, denominated\\n  /// in the local token's decimals.\\n  /// @dev The offramp asserts that the balanceOf of the receiver has been incremented by exactly the number\\n  /// of tokens that is returned in ReleaseOrMintOutV1.destinationAmount. If the amounts do not match, the tx reverts.\\n  function releaseOrMint(\\n    Pool.ReleaseOrMintInV1 calldata releaseOrMintIn\\n  ) external returns (Pool.ReleaseOrMintOutV1 memory);\\n\\n  /// @notice Checks whether a remote chain is supported in the token pool.\\n  /// @param remoteChainSelector The selector of the remote chain.\\n  /// @return true if the given chain is a permissioned remote chain.\\n  function isSupportedChain(\\n    uint64 remoteChainSelector\\n  ) external view returns (bool);\\n\\n  /// @notice Returns if the token pool supports the given token.\\n  /// @param token The address of the token.\\n  /// @return true if the token is supported by the pool.\\n  function isSupportedToken(\\n    address token\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/IPriceRegistry.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\n\\ninterface IPriceRegistry {\\n  /// @notice Update the price for given tokens and gas prices for given chains.\\n  /// @param priceUpdates The price updates to apply.\\n  function updatePrices(\\n    Internal.PriceUpdates memory priceUpdates\\n  ) external;\\n\\n  /// @notice Get the `tokenPrice` for a given token.\\n  /// @param token The token to get the price for.\\n  /// @return tokenPrice The tokenPrice for the given token.\\n  function getTokenPrice(\\n    address token\\n  ) external view returns (Internal.TimestampedPackedUint224 memory);\\n\\n  /// @notice Get the `tokenPrice` for a given token, checks if the price is valid.\\n  /// @param token The token to get the price for.\\n  /// @return tokenPrice The tokenPrice for the given token if it exists and is valid.\\n  function getValidatedTokenPrice(\\n    address token\\n  ) external view returns (uint224);\\n\\n  /// @notice Get the `tokenPrice` for an array of tokens.\\n  /// @param tokens The tokens to get prices for.\\n  /// @return tokenPrices The tokenPrices for the given tokens.\\n  function getTokenPrices(\\n    address[] calldata tokens\\n  ) external view returns (Internal.TimestampedPackedUint224[] memory);\\n\\n  /// @notice Get an encoded `gasPrice` for a given destination chain ID.\\n  /// The 224-bit result encodes necessary gas price components.\\n  /// On L1 chains like Ethereum or Avax, the only component is the gas price.\\n  /// On Optimistic Rollups, there are two components - the L2 gas price, and L1 base fee for data availability.\\n  /// On future chains, there could be more or differing price components.\\n  /// PriceRegistry does not contain chain-specific logic to parse destination chain price components.\\n  /// @param destChainSelector The destination chain to get the price for.\\n  /// @return gasPrice The encoded gasPrice for the given destination chain ID.\\n  function getDestinationChainGasPrice(\\n    uint64 destChainSelector\\n  ) external view returns (Internal.TimestampedPackedUint224 memory);\\n\\n  /// @notice Gets the fee token price and the gas price, both denominated in dollars.\\n  /// @param token The source token to get the price for.\\n  /// @param destChainSelector The destination chain to get the gas price for.\\n  /// @return tokenPrice The price of the feeToken in 1e18 dollars per base unit.\\n  /// @return gasPrice The price of gas in 1e18 dollars per base unit.\\n  function getTokenAndGasPrices(\\n    address token,\\n    uint64 destChainSelector\\n  ) external view returns (uint224 tokenPrice, uint224 gasPrice);\\n\\n  /// @notice Convert a given token amount to target token amount.\\n  /// @param fromToken The given token address.\\n  /// @param fromTokenAmount The given token amount.\\n  /// @param toToken The target token address.\\n  /// @return toTokenAmount The target token amount.\\n  function convertTokenAmount(\\n    address fromToken,\\n    uint256 fromTokenAmount,\\n    address toToken\\n  ) external view returns (uint256 toTokenAmount);\\n\\n  /// @notice Get the list of fee tokens.\\n  /// @return feeTokens The tokens set as fee tokens.\\n  function getFeeTokens() external view returns (address[] memory);\\n}\\n\"},\"contracts/interfaces/IRMNRemote.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\n\\n/// @notice This interface contains the only RMN-related functions that might be used on-chain by other CCIP contracts.\\ninterface IRMNRemote {\\n  /// @notice signature components from RMN nodes.\\n  struct Signature {\\n    bytes32 r;\\n    bytes32 s;\\n  }\\n\\n  /// @notice Verifies signatures of RMN nodes, on dest lane updates as provided in the CommitReport.\\n  /// @param offRampAddress is not inferred by msg.sender, in case the call is made through RMNProxy.\\n  /// @param merkleRoots must be well formed, and is a representation of the CommitReport received from the oracles.\\n  /// @param signatures rmnNodes ECDSA sigs, only r \\u0026 s, must be sorted in ascending order by signer address.\\n  /// @dev Will revert if verification fails.\\n  function verify(\\n    address offRampAddress,\\n    Internal.MerkleRoot[] memory merkleRoots,\\n    Signature[] memory signatures\\n  ) external view;\\n\\n  /// @notice gets the current set of cursed subjects.\\n  /// @return subjects the list of cursed subjects.\\n  function getCursedSubjects() external view returns (bytes16[] memory subjects);\\n\\n  /// @notice If there is an active global or legacy curse, this function returns true.\\n  /// @return bool true if there is an active global curse.\\n  function isCursed() external view returns (bool);\\n\\n  /// @notice If there is an active global curse, or an active curse for `subject`, this function returns true.\\n  /// @param subject To check whether a particular chain is cursed, set to bytes16(uint128(chainSelector)).\\n  /// @return bool true if the provided subject is cured *or* if there is an active global curse.\\n  function isCursed(\\n    bytes16 subject\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/IRouter.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\n\\ninterface IRouter {\\n  error OnlyOffRamp();\\n\\n  /// @notice Route the message to its intended receiver contract.\\n  /// @param message Client.Any2EVMMessage struct.\\n  /// @param gasForCallExactCheck of params for exec.\\n  /// @param gasLimit set of params for exec.\\n  /// @param receiver set of params for exec.\\n  /// @dev if the receiver is a contracts that signals support for CCIP execution through EIP-165.\\n  /// the contract is called. If not, only tokens are transferred.\\n  /// @return success A boolean value indicating whether the ccip message was received without errors.\\n  /// @return retBytes A bytes array containing return data form CCIP receiver.\\n  /// @return gasUsed the gas used by the external customer call. Does not include any overhead.\\n  function routeMessage(\\n    Client.Any2EVMMessage calldata message,\\n    uint16 gasForCallExactCheck,\\n    uint256 gasLimit,\\n    address receiver\\n  ) external returns (bool success, bytes memory retBytes, uint256 gasUsed);\\n\\n  /// @notice Returns the configured onramp for a specific destination chain.\\n  /// @param destChainSelector The destination chain Id to get the onRamp for.\\n  /// @return onRampAddress The address of the onRamp.\\n  function getOnRamp(\\n    uint64 destChainSelector\\n  ) external view returns (address onRampAddress);\\n\\n  /// @notice Return true if the given offRamp is a configured offRamp for the given source chain.\\n  /// @param sourceChainSelector The source chain selector to check.\\n  /// @param offRamp The address of the offRamp to check.\\n  function isOffRamp(uint64 sourceChainSelector, address offRamp) external view returns (bool isOffRamp);\\n}\\n\"},\"contracts/interfaces/ITokenAdminRegistry.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.0;\\n\\ninterface ITokenAdminRegistry {\\n  /// @notice Returns the pool for the given token.\\n  function getPool(\\n    address token\\n  ) external view returns (address);\\n\\n  /// @notice Proposes an administrator for the given token as pending administrator.\\n  /// @param localToken The token to register the administrator for.\\n  /// @param administrator The administrator to register.\\n  function proposeAdministrator(address localToken, address administrator) external;\\n\\n  /// @notice Accepts the administrator role for a token.\\n  /// @param localToken The token to accept the administrator role for.\\n  /// @dev This function can only be called by the pending administrator.\\n  function acceptAdminRole(\\n    address localToken\\n  ) external;\\n\\n  /// @notice Sets the pool for a token. Setting the pool to address(0) effectively delists the token\\n  /// from CCIP. Setting the pool to any other address enables the token on CCIP.\\n  /// @param localToken The token to set the pool for.\\n  /// @param pool The pool to set for the token.\\n  function setPool(address localToken, address pool) external;\\n\\n  /// @notice Transfers the administrator role for a token to a new address with a 2-step process.\\n  /// @param localToken The token to transfer the administrator role for.\\n  /// @param newAdmin The address to transfer the administrator role to. Can be address(0) to cancel\\n  /// a pending transfer.\\n  /// @dev The new admin must call `acceptAdminRole` to accept the role.\\n  function transferAdminRole(address localToken, address newAdmin) external;\\n}\\n\"},\"contracts/libraries/Client.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n// End consumer library.\\nlibrary Client {\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct EVMTokenAmount {\\n    address token; // token address on the local chain.\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  struct Any2EVMMessage {\\n    bytes32 messageId; // MessageId corresponding to ccipSend on source.\\n    uint64 sourceChainSelector; // Source chain selector.\\n    bytes sender; // abi.decode(sender) if coming from an EVM chain.\\n    bytes data; // payload sent in original message.\\n    EVMTokenAmount[] destTokenAmounts; // Tokens and their amounts in their destination chain representation.\\n  }\\n\\n  // If extraArgs is empty bytes, the default is 200k gas limit.\\n  struct EVM2AnyMessage {\\n    bytes receiver; // abi.encode(receiver address) for dest EVM chains.\\n    bytes data; // Data payload.\\n    EVMTokenAmount[] tokenAmounts; // Token transfers.\\n    address feeToken; // Address of feeToken. address(0) means you will send msg.value.\\n    bytes extraArgs; // Populate this with _argsToBytes(EVMExtraArgsV2).\\n  }\\n\\n  // Tag to indicate only a gas limit. Only usable for EVM as destination chain.\\n  bytes4 public constant EVM_EXTRA_ARGS_V1_TAG = 0x97a657c9;\\n\\n  struct EVMExtraArgsV1 {\\n    uint256 gasLimit;\\n  }\\n\\n  function _argsToBytes(\\n    EVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(EVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n\\n  // Tag to indicate a gas limit (or dest chain equivalent processing units) and Out Of Order Execution. This tag is\\n  // available for multiple chain families. If there is no chain family specific tag, this is the default available\\n  // for a chain.\\n  // Note: not available for Solana VM based chains.\\n  bytes4 public constant GENERIC_EXTRA_ARGS_V2_TAG = 0x181dcf10;\\n\\n  /// @param gasLimit: gas limit for the callback on the destination chain.\\n  /// @param allowOutOfOrderExecution: if true, it indicates that the message can be executed in any order relative to\\n  /// other messages from the same sender. This value's default varies by chain. On some chains, a particular value is\\n  /// enforced, meaning if the expected value is not set, the message request will revert.\\n  /// @dev Fully compatible with the previously existing EVMExtraArgsV2.\\n  struct GenericExtraArgsV2 {\\n    uint256 gasLimit;\\n    bool allowOutOfOrderExecution;\\n  }\\n\\n  // Extra args tag for chains that use the Solana VM.\\n  bytes4 public constant SVM_EXTRA_ARGS_V1_TAG = 0x1f3b3aba;\\n\\n  struct SVMExtraArgsV1 {\\n    uint32 computeUnits;\\n    uint64 accountIsWritableBitmap;\\n    bool allowOutOfOrderExecution;\\n    bytes32 tokenReceiver;\\n    // Additional accounts needed for execution of CCIP receiver. Must be empty if message.receiver is zero.\\n    // Token transfer related accounts are specified in the token pool lookup table on SVM.\\n    bytes32[] accounts;\\n  }\\n\\n  /// @dev The maximum number of accounts that can be passed in SVMExtraArgs.\\n  uint256 public constant SVM_EXTRA_ARGS_MAX_ACCOUNTS = 64;\\n\\n  /// @dev The expected static payload size of a token transfer when Borsh encoded and submitted to SVM.\\n  /// TokenPool extra data and offchain data sizes are dynamic, and should be accounted for separately.\\n  uint256 public constant SVM_TOKEN_TRANSFER_DATA_OVERHEAD = (4 + 32) // source_pool\\n    + 32 // token_address\\n    + 4 // gas_amount\\n    + 4 // extra_data overhead\\n    + 32 // amount\\n    + 32 // size of the token lookup table account\\n    + 32 // token-related accounts in the lookup table, over-estimated to 32, typically between 11 - 13\\n    + 32 // token account belonging to the token receiver, e.g ATA, not included in the token lookup table\\n    + 32 // per-chain token pool config, not included in the token lookup table\\n    + 32 // per-chain token billing config, not always included in the token lookup table\\n    + 32; // OffRamp pool signer PDA, not included in the token lookup table\\n\\n  /// @dev Number of overhead accounts needed for message execution on SVM.\\n  /// @dev These are message.receiver, and the OffRamp Signer PDA specific to the receiver.\\n  uint256 public constant SVM_MESSAGING_ACCOUNTS_OVERHEAD = 2;\\n\\n  /// @dev The size of each SVM account address in bytes.\\n  uint256 public constant SVM_ACCOUNT_BYTE_SIZE = 32;\\n\\n  function _argsToBytes(\\n    GenericExtraArgsV2 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(GENERIC_EXTRA_ARGS_V2_TAG, extraArgs);\\n  }\\n\\n  function _svmArgsToBytes(\\n    SVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(SVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n}\\n\"},\"contracts/libraries/ERC165CheckerReverting.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\\\";\\n\\n/// @notice Library used to query support of an interface declared via {IERC165}.\\n/// @dev These functions return the actual result of the query: they do not `revert` if an interface is not supported.\\nlibrary ERC165CheckerReverting {\\n  error InsufficientGasForStaticCall();\\n\\n  // As per the EIP-165 spec, no interface should ever match 0xffffffff.\\n  bytes4 private constant INTERFACE_ID_INVALID = 0xffffffff;\\n\\n  /// @dev 30k gas is required to make the staticcall. Under the 63/64 rule this means that 30,477 gas must be available\\n  /// to ensure that at least 30k is forwarded. Checking for at least 31,000 ensures that after additional\\n  /// operations are performed there is still \\u003e= 30,477 gas remaining.\\n  /// 30,000 = ((30,477 * 63) / 64)\\n  uint256 private constant MINIMUM_GAS_REQUIREMENT = 31_000;\\n\\n  /// @notice Returns true if `account` supports a defined interface.\\n  /// @dev The function must support both the interfaceId and interfaces specified by ERC165 generally as per the standard.\\n  /// @param account the contract to be queried for support.\\n  /// @param interfaceId the interface being checked for support.\\n  /// @return true if the contract at account indicates support of the interface with, false otherwise.\\n  function _supportsInterfaceReverting(address account, bytes4 interfaceId) internal view returns (bool) {\\n    // As a gas optimization, short circuit return false if interfaceId is not supported, as it is most likely interfaceId\\n    // to be unsupported by the target.\\n    return _supportsERC165InterfaceUncheckedReverting(account, interfaceId)\\n      \\u0026\\u0026 !_supportsERC165InterfaceUncheckedReverting(account, INTERFACE_ID_INVALID)\\n      \\u0026\\u0026 _supportsERC165InterfaceUncheckedReverting(account, type(IERC165).interfaceId);\\n  }\\n\\n  /// @notice Query if a contract implements an interface, does not check ERC165 support\\n  /// @param account The address of the contract to query for support of an interface\\n  /// @param interfaceId The interface identifier, as specified in ERC-165\\n  /// @return true if the contract at account indicates support of the interface with\\n  /// identifier interfaceId, false otherwise\\n  /// @dev Assumes that account contains a contract that supports ERC165, otherwise\\n  /// the behavior of this method is undefined. This precondition can be checked.\\n  /// @dev Function will only revert if the minimum gas requirement is not met before the staticcall is performed.\\n  function _supportsERC165InterfaceUncheckedReverting(address account, bytes4 interfaceId) internal view returns (bool) {\\n    bytes memory encodedParams = abi.encodeWithSelector(IERC165.supportsInterface.selector, interfaceId);\\n\\n    bool success;\\n    uint256 returnSize;\\n    uint256 returnValue;\\n\\n    bytes4 notEnoughGasSelector = InsufficientGasForStaticCall.selector;\\n\\n    assembly {\\n      // The EVM does not return a specific error code if a revert is due to OOG. This check ensures that\\n      // the message will not throw an OOG error by requiring that the amount of gas for the following\\n      // staticcall exists before invoking it.\\n      if lt(gas(), MINIMUM_GAS_REQUIREMENT) {\\n        mstore(0x0, notEnoughGasSelector)\\n        revert(0x0, 0x4)\\n      }\\n\\n      success := staticcall(30000, account, add(encodedParams, 0x20), mload(encodedParams), 0x00, 0x20)\\n      returnSize := returndatasize()\\n      returnValue := mload(0x00)\\n    }\\n    return success \\u0026\\u0026 returnSize \\u003e= 0x20 \\u0026\\u0026 returnValue \\u003e 0;\\n  }\\n}\\n\"},\"contracts/libraries/Internal.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {MerkleMultiProof} from \\\"../libraries/MerkleMultiProof.sol\\\";\\n\\n/// @notice Library for CCIP internal definitions common to multiple contracts.\\n/// @dev The following is a non-exhaustive list of \\\"known issues\\\" for CCIP:\\n/// - We could implement yield claiming for Blast. This is not worth the custom code path on non-blast chains.\\n/// - uint32 is used for timestamps, which will overflow in 2106. This is not a concern for the current use case, as we\\n/// expect to have migrated to a new version by then.\\nlibrary Internal {\\n  error InvalidEVMAddress(bytes encodedAddress);\\n  error Invalid32ByteAddress(bytes encodedAddress);\\n\\n  /// @dev We limit return data to a selector plus 4 words. This is to avoid malicious contracts from returning\\n  /// large amounts of data and causing repeated out-of-gas scenarios.\\n  uint16 internal constant MAX_RET_BYTES = 4 + 4 * 32;\\n  /// @dev The expected number of bytes returned by the balanceOf function.\\n  uint256 internal constant MAX_BALANCE_OF_RET_BYTES = 32;\\n\\n  /// @dev The address used to send calls for gas estimation.\\n  /// You only need to use this address if the minimum gas limit specified by the user is not actually enough to execute the\\n  /// given message and you're attempting to estimate the actual necessary gas limit\\n  address public constant GAS_ESTIMATION_SENDER = address(0xC11C11C11C11C11C11C11C11C11C11C11C11C1);\\n\\n  /// @notice A collection of token price and gas price updates.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct PriceUpdates {\\n    TokenPriceUpdate[] tokenPriceUpdates;\\n    GasPriceUpdate[] gasPriceUpdates;\\n  }\\n\\n  /// @notice Token price in USD.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct TokenPriceUpdate {\\n    address sourceToken; // Source token.\\n    uint224 usdPerToken; // 1e18 USD per 1e18 of the smallest token denomination.\\n  }\\n\\n  /// @notice Gas price for a given chain in USD, its value may contain tightly packed fields.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct GasPriceUpdate {\\n    uint64 destChainSelector; // Destination chain selector.\\n    uint224 usdPerUnitGas; // 1e18 USD per smallest unit (e.g. wei) of destination chain gas.\\n  }\\n\\n  /// @notice A timestamped uint224 value that can contain several tightly packed fields.\\n  struct TimestampedPackedUint224 {\\n    uint224 value; // ────╮ Value in uint224, packed.\\n    uint32 timestamp; // ─╯ Timestamp of the most recent price update.\\n  }\\n\\n  /// @dev Gas price is stored in 112-bit unsigned int. uint224 can pack 2 prices.\\n  /// When packing L1 and L2 gas prices, L1 gas price is left-shifted to the higher-order bits.\\n  /// Using uint8 type, which cannot be higher than other bit shift operands, to avoid shift operand type warning.\\n  uint8 public constant GAS_PRICE_BITS = 112;\\n\\n  struct SourceTokenData {\\n    // The source pool address, abi encoded. This value is trusted as it was obtained through the onRamp. It can be\\n    // relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint32 destGasAmount; // The amount of gas available for the releaseOrMint and balanceOf calls on the offRamp\\n  }\\n\\n  /// @notice Report that is submitted by the execution DON at the execution phase, including chain selector data.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct ExecutionReport {\\n    uint64 sourceChainSelector; // Source chain selector for which the report is submitted.\\n    Any2EVMRampMessage[] messages;\\n    // Contains a bytes array for each message, each inner bytes array contains bytes per transferred token.\\n    bytes[][] offchainTokenData;\\n    bytes32[] proofs;\\n    uint256 proofFlagBits;\\n  }\\n\\n  /// @dev Any2EVMRampMessage struct has 10 fields, including 3 variable unnested arrays, sender, data and tokenAmounts.\\n  /// Each variable array takes 1 more slot to store its length.\\n  /// When abi encoded, excluding array contents, Any2EVMMessage takes up a fixed number of 13 slots, 32 bytes each.\\n  /// Assume 1 slot for sender\\n  /// For structs that contain arrays, 1 more slot is added to the front, reaching a total of 14.\\n  /// The fixed bytes does not cover struct data (this is represented by MESSAGE_FIXED_BYTES_PER_TOKEN)\\n  uint256 public constant MESSAGE_FIXED_BYTES = 32 * 15;\\n\\n  /// @dev Any2EVMTokensTransfer struct bytes length\\n  /// 0x20\\n  /// sourcePoolAddress_offset\\n  /// destTokenAddress\\n  /// destGasAmount\\n  /// extraData_offset\\n  /// amount\\n  /// sourcePoolAddress_length\\n  /// sourcePoolAddress_content // assume 1 slot\\n  /// extraData_length // contents billed separately\\n  uint256 public constant MESSAGE_FIXED_BYTES_PER_TOKEN = 32 * (4 + (3 + 2));\\n\\n  bytes32 internal constant ANY_2_EVM_MESSAGE_HASH = keccak256(\\\"Any2EVMMessageHashV1\\\");\\n  bytes32 internal constant EVM_2_ANY_MESSAGE_HASH = keccak256(\\\"EVM2AnyMessageHashV1\\\");\\n\\n  /// @dev Used to hash messages for multi-lane family-agnostic OffRamps.\\n  /// OnRamp hash(EVM2AnyMessage) != Any2EVMRampMessage.messageId.\\n  /// OnRamp hash(EVM2AnyMessage) != OffRamp hash(Any2EVMRampMessage).\\n  /// @param original OffRamp message to hash.\\n  /// @param metadataHash Hash preimage to ensure global uniqueness.\\n  /// @return hashedMessage hashed message as a keccak256.\\n  function _hash(Any2EVMRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.header.messageId,\\n            original.receiver,\\n            original.header.sequenceNumber,\\n            original.gasLimit,\\n            original.header.nonce\\n          )\\n        ),\\n        keccak256(original.sender),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts))\\n      )\\n    );\\n  }\\n\\n  function _hash(EVM2AnyRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.sender,\\n            original.header.sequenceNumber,\\n            original.header.nonce,\\n            original.feeToken,\\n            original.feeTokenAmount\\n          )\\n        ),\\n        keccak256(original.receiver),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts)),\\n        keccak256(original.extraArgs)\\n      )\\n    );\\n  }\\n\\n  /// @dev We disallow the first 1024 addresses to avoid calling into a range known for hosting precompiles. Calling\\n  /// into precompiles probably won't cause any issues, but to be safe we can disallow this range. It is extremely\\n  /// unlikely that anyone would ever be able to generate an address in this range. There is no official range of\\n  /// precompiles, but EIP-7587 proposes to reserve the range 0x100 to 0x1ff. Our range is more conservative, even\\n  /// though it might not be exhaustive for all chains, which is OK. We also disallow the zero address, which is a\\n  /// common practice.\\n  uint256 public constant EVM_PRECOMPILE_SPACE = 1024;\\n\\n  // According to the Aptos docs, the first 0xa addresses are reserved for precompiles.\\n  // https://github.com/aptos-labs/aptos-core/blob/main/aptos-move/framework/aptos-framework/doc/account.md#function-create_framework_reserved_account-1\\n  uint256 public constant APTOS_PRECOMPILE_SPACE = 0x0b;\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// EVM address space. If it isn't it will revert with an InvalidEVMAddress error, which we can catch and handle\\n  /// more gracefully than a revert from abi.decode.\\n  function _validateEVMAddress(\\n    bytes memory encodedAddress\\n  ) internal pure {\\n    if (encodedAddress.length != 32) revert InvalidEVMAddress(encodedAddress);\\n    uint256 encodedAddressUint = abi.decode(encodedAddress, (uint256));\\n    if (encodedAddressUint \\u003e type(uint160).max || encodedAddressUint \\u003c EVM_PRECOMPILE_SPACE) {\\n      revert InvalidEVMAddress(encodedAddress);\\n    }\\n  }\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// bounds of [minValue, uint256.max]. If it isn't it will revert with an Invalid32ByteAddress error.\\n  function _validate32ByteAddress(bytes memory encodedAddress, uint256 minValue) internal pure {\\n    if (encodedAddress.length != 32) revert Invalid32ByteAddress(encodedAddress);\\n    if (minValue \\u003e 0) {\\n      if (abi.decode(encodedAddress, (uint256)) \\u003c minValue) {\\n        revert Invalid32ByteAddress(encodedAddress);\\n      }\\n    }\\n  }\\n\\n  /// @notice Enum listing the possible message execution states within the offRamp contract.\\n  /// UNTOUCHED never executed.\\n  /// IN_PROGRESS currently being executed, used a replay protection.\\n  /// SUCCESS successfully executed. End state.\\n  /// FAILURE unsuccessfully executed, manual execution is now enabled.\\n  /// @dev RMN depends on this enum, if changing, please notify the RMN maintainers.\\n  enum MessageExecutionState {\\n    UNTOUCHED,\\n    IN_PROGRESS,\\n    SUCCESS,\\n    FAILURE\\n  }\\n\\n  /// @notice CCIP OCR plugin type, used to separate execution \\u0026 commit transmissions and configs.\\n  enum OCRPluginType {\\n    Commit,\\n    Execution\\n  }\\n\\n  /// @notice Family-agnostic header for OnRamp \\u0026 OffRamp messages.\\n  /// The messageId is not expected to match hash(message), since it may originate from another ramp family.\\n  struct RampMessageHeader {\\n    bytes32 messageId; // Unique identifier for the message, generated with the source chain's encoding scheme (i.e. not necessarily abi.encoded).\\n    uint64 sourceChainSelector; // ─╮ the chain selector of the source chain, note: not chainId.\\n    uint64 destChainSelector; //    │ the chain selector of the destination chain, note: not chainId.\\n    uint64 sequenceNumber; //       │ sequence number, not unique across lanes.\\n    uint64 nonce; // ───────────────╯ nonce for this lane for this sender, not unique across senders/lanes.\\n  }\\n\\n  struct EVM2AnyTokenTransfer {\\n    // The source pool EVM address. This value is trusted as it was obtained through the onRamp. It can be relied\\n    // upon by the destination pool to validate the source pool.\\n    address sourcePoolAddress;\\n    // The EVM address of the destination token.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n    // Destination chain data used to execute the token transfer on the destination chain. For an EVM destination, it\\n    // consists of the amount of gas available for the releaseOrMint and transfer calls made by the offRamp.\\n    bytes destExecData;\\n  }\\n\\n  struct Any2EVMTokenTransfer {\\n    // The source pool EVM address encoded to bytes. This value is trusted as it is obtained through the onRamp. It can\\n    // be relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    address destTokenAddress; // ─╮ Address of destination token\\n    uint32 destGasAmount; // ─────╯ The amount of gas available for the releaseOrMint and transfer calls on the offRamp.\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  /// @notice Family-agnostic message routed to an OffRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage), hash(Any2EVMRampMessage) != messageId due to encoding\\n  /// and parameter differences.\\n  struct Any2EVMRampMessage {\\n    RampMessageHeader header; // Message header.\\n    bytes sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    address receiver; // receiver address on the destination chain.\\n    uint256 gasLimit; // user supplied maximum gas amount available for dest chain execution.\\n    Any2EVMTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  /// @notice Family-agnostic message emitted from the OnRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage) due to encoding \\u0026 parameter differences.\\n  /// messageId = hash(EVM2AnyRampMessage) using the source EVM chain's encoding format.\\n  struct EVM2AnyRampMessage {\\n    RampMessageHeader header; // Message header.\\n    address sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    bytes receiver; // receiver address on the destination chain.\\n    bytes extraArgs; // destination-chain specific extra args, such as the gasLimit for EVM chains.\\n    address feeToken; // fee token.\\n    uint256 feeTokenAmount; // fee token amount.\\n    uint256 feeValueJuels; // fee amount in Juels.\\n    EVM2AnyTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector EVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_EVM = 0x2812d52c;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SVM = 0x1e10bdc4;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector APTOS\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_APTOS = 0xac77ffec;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SUI\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SUI = 0xc4e05953;\\n\\n  /// @dev Holds a merkle root and interval for a source chain so that an array of these can be passed in the CommitReport.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  /// @dev inefficient struct packing intentionally chosen to maintain order of specificity. Not a storage struct so impact is minimal.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct MerkleRoot {\\n    uint64 sourceChainSelector; // Remote source chain selector that the Merkle Root is scoped to\\n    bytes onRampAddress; //        Generic onRamp address, to support arbitrary sources; for EVM, use abi.encode\\n    uint64 minSeqNr; // ─────────╮ Minimum sequence number, inclusive\\n    uint64 maxSeqNr; // ─────────╯ Maximum sequence number, inclusive\\n    bytes32 merkleRoot; //         Merkle root covering the interval \\u0026 source chain messages\\n  }\\n}\\n\"},\"contracts/libraries/MerkleMultiProof.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nlibrary MerkleMultiProof {\\n  /// @notice Leaf domain separator, should be used as the first 32 bytes of a leaf's preimage.\\n  bytes32 internal constant LEAF_DOMAIN_SEPARATOR = 0x0000000000000000000000000000000000000000000000000000000000000000;\\n  /// @notice Internal domain separator, should be used as the first 32 bytes of an internal node's preimage.\\n  bytes32 internal constant INTERNAL_DOMAIN_SEPARATOR =\\n    0x0000000000000000000000000000000000000000000000000000000000000001;\\n\\n  uint256 internal constant MAX_NUM_HASHES = 256;\\n\\n  error InvalidProof();\\n  error LeavesCannotBeEmpty();\\n\\n  /// @notice Computes the root based on provided pre-hashed leaf nodes in leaves, internal nodes  in proofs, and using\\n  /// proofFlagBits' i-th bit to determine if an element of proofs or one of the previously computed leafs or internal\\n  /// nodes will be used for the i-th hash.\\n  /// @param leaves Should be pre-hashed and the first 32 bytes of a leaf's preimage should match LEAF_DOMAIN_SEPARATOR.\\n  /// @param proofs Hashes to be used instead of a leaf hash when the proofFlagBits indicates a proof should be used.\\n  /// @param proofFlagBits A single uint256 of which each bit indicates whether a leaf or a proof needs to be used in\\n  /// a hash operation.\\n  /// @dev the maximum number of hash operations it set to 256. Any input that would require more than 256 hashes to get\\n  /// to a root will revert.\\n  /// @dev For given input `leaves` = [a,b,c] `proofs` = [D] and `proofFlagBits` = 5\\n  ///     totalHashes = 3 + 1 - 1 = 3\\n  ///  ** round 1 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 0) \\u0026 1 = true\\n  ///    hashes[0] = hashPair(a, b)\\n  ///    (leafPos, hashPos, proofPos) = (2, 0, 0);\\n  ///\\n  ///  ** round 2 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 1) \\u0026 1 = false\\n  ///    hashes[1] = hashPair(D, c)\\n  ///    (leafPos, hashPos, proofPos) = (3, 0, 1);\\n  ///\\n  ///  ** round 3 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 2) \\u0026 1 = true\\n  ///    hashes[2] = hashPair(hashes[0], hashes[1])\\n  ///    (leafPos, hashPos, proofPos) = (3, 2, 1);\\n  ///\\n  ///    i = 3 and no longer \\u003c totalHashes. The algorithm is done\\n  ///    return hashes[totalHashes - 1] = hashes[2]; the last hash we computed.\\n  // We mark this function as internal to force it to be inlined in contracts that use it, but semantically it is public.\\n  function _merkleRoot(\\n    bytes32[] memory leaves,\\n    bytes32[] memory proofs,\\n    uint256 proofFlagBits\\n  ) internal pure returns (bytes32) {\\n    unchecked {\\n      uint256 leavesLen = leaves.length;\\n      uint256 proofsLen = proofs.length;\\n      if (leavesLen == 0) revert LeavesCannotBeEmpty();\\n      if (!(leavesLen \\u003c= MAX_NUM_HASHES + 1 \\u0026\\u0026 proofsLen \\u003c= MAX_NUM_HASHES + 1)) revert InvalidProof();\\n      uint256 totalHashes = leavesLen + proofsLen - 1;\\n      if (!(totalHashes \\u003c= MAX_NUM_HASHES)) revert InvalidProof();\\n      if (totalHashes == 0) {\\n        return leaves[0];\\n      }\\n      bytes32[] memory hashes = new bytes32[](totalHashes);\\n      (uint256 leafPos, uint256 hashPos, uint256 proofPos) = (0, 0, 0);\\n\\n      for (uint256 i = 0; i \\u003c totalHashes; ++i) {\\n        // Checks if the bit flag signals the use of a supplied proof or a leaf/previous hash.\\n        bytes32 a;\\n        if (proofFlagBits \\u0026 (1 \\u003c\\u003c i) == (1 \\u003c\\u003c i)) {\\n          // Use a leaf or a previously computed hash.\\n          if (leafPos \\u003c leavesLen) {\\n            a = leaves[leafPos++];\\n          } else {\\n            a = hashes[hashPos++];\\n          }\\n        } else {\\n          // Use a supplied proof.\\n          a = proofs[proofPos++];\\n        }\\n\\n        // The second part of the hashed pair is never a proof as hashing two proofs would result in a\\n        // hash that can already be computed offchain.\\n        bytes32 b;\\n        if (leafPos \\u003c leavesLen) {\\n          b = leaves[leafPos++];\\n        } else {\\n          b = hashes[hashPos++];\\n        }\\n\\n        if (!(hashPos \\u003c= i)) revert InvalidProof();\\n\\n        hashes[i] = _hashPair(a, b);\\n      }\\n      if (!(hashPos == totalHashes - 1 \\u0026\\u0026 leafPos == leavesLen \\u0026\\u0026 proofPos == proofsLen)) revert InvalidProof();\\n      // Return the last hash.\\n      return hashes[totalHashes - 1];\\n    }\\n  }\\n\\n  /// @notice Hashes two bytes32 objects in their given order, prepended by the INTERNAL_DOMAIN_SEPARATOR.\\n  function _hashInternalNode(bytes32 left, bytes32 right) private pure returns (bytes32 hash) {\\n    return keccak256(abi.encode(INTERNAL_DOMAIN_SEPARATOR, left, right));\\n  }\\n\\n  /// @notice Hashes two bytes32 objects. The order is taken into account, using the lower value first.\\n  function _hashPair(bytes32 a, bytes32 b) private pure returns (bytes32) {\\n    return a \\u003c b ? _hashInternalNode(a, b) : _hashInternalNode(b, a);\\n  }\\n}\\n\"},\"contracts/libraries/Pool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This library contains various token pool functions to aid constructing the return data.\\nlibrary Pool {\\n  // The tag used to signal support for the pool v1 standard.\\n  // bytes4(keccak256(\\\"CCIP_POOL_V1\\\"))\\n  bytes4 public constant CCIP_POOL_V1 = 0xaff2afbf;\\n\\n  // The number of bytes in the return data for a pool v1 releaseOrMint call.\\n  // This should match the size of the ReleaseOrMintOutV1 struct.\\n  uint16 public constant CCIP_POOL_V1_RET_BYTES = 32;\\n\\n  // The default max number of bytes in the return data for a pool v1 lockOrBurn call.\\n  // This data can be used to send information to the destination chain token pool. Can be overwritten\\n  // in the TokenTransferFeeConfig.destBytesOverhead if more data is required.\\n  uint32 public constant CCIP_LOCK_OR_BURN_V1_RET_BYTES = 32;\\n\\n  struct LockOrBurnInV1 {\\n    bytes receiver; //  The recipient of the tokens on the destination chain, abi encoded.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the destination chain.\\n    address originalSender; // ─────╯ The original sender of the tx on the source chain.\\n    uint256 amount; //  The amount of tokens to lock or burn, denominated in the source token's decimals.\\n    address localToken; //  The address on this chain of the token to lock or burn.\\n  }\\n\\n  struct LockOrBurnOutV1 {\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes destPoolData;\\n  }\\n\\n  struct ReleaseOrMintInV1 {\\n    bytes originalSender; //          The original sender of the tx on the source chain.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the source chain.\\n    address receiver; // ───────────╯ The recipient of the tokens on the destination chain.\\n    uint256 amount; //                The amount of tokens to release or mint, denominated in the source token's decimals.\\n    address localToken; //            The address on this chain of the token to release or mint.\\n    /// @dev WARNING: sourcePoolAddress should be checked prior to any processing of funds. Make sure it matches the\\n    /// expected pool address for the given remoteChainSelector.\\n    bytes sourcePoolAddress; //       The address of the source pool, abi encoded in the case of EVM chains.\\n    bytes sourcePoolData; //          The data received from the source pool to process the release or mint.\\n    /// @dev WARNING: offchainTokenData is untrusted data.\\n    bytes offchainTokenData; //       The offchain data to process the release or mint.\\n  }\\n\\n  struct ReleaseOrMintOutV1 {\\n    // The number of tokens released or minted on the destination chain, denominated in the local token's decimals.\\n    // This value is expected to be equal to the ReleaseOrMintInV1.amount in the case where the source and destination\\n    // chain have the same number of decimals.\\n    uint256 destinationAmount;\\n  }\\n}\\n\"},\"contracts/ocr/MultiOCR3Base.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {Ownable2StepMsgSender} from \\\"@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\n/// @notice Onchain verification of reports from the offchain reporting protocol with multiple OCR plugin support.\\nabstract contract MultiOCR3Base is ITypeAndVersion, Ownable2StepMsgSender {\\n  // Maximum number of oracles the offchain reporting protocol is designed for\\n  uint256 internal constant MAX_NUM_ORACLES = 256;\\n\\n  /// @notice Triggers a new run of the offchain reporting protocol.\\n  /// @param ocrPluginType OCR plugin type for which the config was set.\\n  /// @param configDigest configDigest of this configuration.\\n  /// @param signers ith element is address ith oracle uses to sign a report.\\n  /// @param transmitters ith element is address ith oracle uses to transmit a report via the transmit method.\\n  /// @param F maximum number of faulty/dishonest oracles the protocol can tolerate while still working correctly.\\n  event ConfigSet(uint8 ocrPluginType, bytes32 configDigest, address[] signers, address[] transmitters, uint8 F);\\n\\n  /// @notice Optionally emitted to indicate the latest configDigest and sequence number\\n  /// for which a report was successfully transmitted. Alternatively, the contract may\\n  /// use latestConfigDigestAndEpoch with scanLogs set to false.\\n  event Transmitted(uint8 indexed ocrPluginType, bytes32 configDigest, uint64 sequenceNumber);\\n\\n  enum InvalidConfigErrorType {\\n    F_MUST_BE_POSITIVE,\\n    TOO_MANY_TRANSMITTERS,\\n    TOO_MANY_SIGNERS,\\n    F_TOO_HIGH,\\n    REPEATED_ORACLE_ADDRESS,\\n    NO_TRANSMITTERS\\n  }\\n\\n  error InvalidConfig(InvalidConfigErrorType errorType);\\n  error WrongMessageLength(uint256 expected, uint256 actual);\\n  error ConfigDigestMismatch(bytes32 expected, bytes32 actual);\\n  error ForkedChain(uint256 expected, uint256 actual);\\n  error WrongNumberOfSignatures();\\n  error SignaturesOutOfRegistration();\\n  error UnauthorizedTransmitter();\\n  error UnauthorizedSigner();\\n  error NonUniqueSignatures();\\n  error OracleCannotBeZeroAddress();\\n  error StaticConfigCannotBeChanged(uint8 ocrPluginType);\\n  error InsufficientGasToCompleteTx(bytes4 err);\\n\\n  /// @dev Packing these fields used on the hot path in a ConfigInfo variable reduces the retrieval of all\\n  /// of them to a minimum number of SLOADs.\\n  struct ConfigInfo {\\n    bytes32 configDigest;\\n    uint8 F; // ─────────────────────────────╮ maximum number of faulty/dishonest oracles the system can tolerate.\\n    uint8 n; //                              │ number of configured signers.\\n    bool isSignatureVerificationEnabled; // ─╯ if true, requires signers and verifies signatures on transmission.\\n  }\\n\\n  /// @notice Used for s_oracles[a].role, where a is an address, to track the purpose of the address, or to indicate\\n  /// that the address is unset.\\n  enum Role {\\n    // No oracle role has been set for the address `a`\\n    Unset,\\n    // Signing address for the s_oracles[a].index'th oracle. I.e., report signatures from this oracle should ecrecover\\n    // back to address `a`.\\n    Signer,\\n    // Transmission address for the s_oracles[a].index'th oracle. I.e., if `a` report is received by\\n    // OCR2Aggregator.transmit in which msg.sender is  a, it is attributed to the s_oracles[a].index'th oracle.\\n    Transmitter\\n  }\\n\\n  struct Oracle {\\n    uint8 index; // ─╮ Index of oracle in s_signers/s_transmitters.\\n    Role role; // ───╯ Role of the address which mapped to this struct.\\n  }\\n\\n  /// @notice OCR configuration for a single OCR plugin within a DON.\\n  struct OCRConfig {\\n    ConfigInfo configInfo; //  latest OCR config.\\n    // NOTE: len(signers) can be different from len(transmitters). There is no index relationship between the two arrays.\\n    address[] signers; //      addresses oracles use to sign the reports.\\n    address[] transmitters; // addresses oracles use to transmit the reports.\\n  }\\n\\n  /// @notice Args to update an OCR Config.\\n  struct OCRConfigArgs {\\n    bytes32 configDigest; // The new config digest.\\n    uint8 ocrPluginType; // ─────────────────╮ OCR plugin type to update config for.\\n    uint8 F; //                              │ Maximum number of faulty/dishonest oracles.\\n    bool isSignatureVerificationEnabled; // ─╯ If true, requires signers and verifies signatures on transmission.\\n    address[] signers; // signing address of each oracle.\\n    address[] transmitters; // the address the oracle sends transactions from.\\n  }\\n\\n  /// @notice mapping of OCR plugin type -\\u003e DON config.\\n  mapping(uint8 ocrPluginType =\\u003e OCRConfig config) internal s_ocrConfigs;\\n\\n  /// @notice OCR plugin type =\\u003e signer OR transmitter address mapping.\\n  mapping(uint8 ocrPluginType =\\u003e mapping(address signerOrTransmiter =\\u003e Oracle oracle)) internal s_oracles;\\n\\n  // Constant-length components of the msg.data sent to transmit.\\n  // See the \\\"If we wanted to call sam\\\" example on for example reasoning.\\n  // https://solidity.readthedocs.io/en/v0.7.2/abi-spec.html\\n\\n  /// @notice Constant length component for transmit functions with no signatures.\\n  /// The signatures are expected to match transmitPlugin(reportContext, report).\\n  uint16 private constant TRANSMIT_MSGDATA_CONSTANT_LENGTH_COMPONENT_NO_SIGNATURES = 4 // function selector.\\n    + 2 * 32 // 2 words containing reportContext.\\n    + 32 // word containing start location of abiencoded report value.\\n    + 32; // word containing length of report.\\n\\n  /// @notice Extra constant length component for transmit functions with signatures (relative to no signatures).\\n  /// The signatures are expected to match transmitPlugin(reportContext, report, rs, ss, rawVs).\\n  uint16 private constant TRANSMIT_MSGDATA_EXTRA_CONSTANT_LENGTH_COMPONENT_FOR_SIGNATURES = 32 // word containing location start of abiencoded rs value.\\n    + 32 // word containing start location of abiencoded ss value.\\n    + 32 // rawVs value.\\n    + 32 // word containing length rs.\\n    + 32; // word containing length of ss.\\n\\n  uint256 internal immutable i_chainID;\\n\\n  constructor() {\\n    i_chainID = block.chainid;\\n  }\\n\\n  /// @notice Sets offchain reporting protocol configuration incl. participating oracles.\\n  /// NOTE: The OCR3 config must be sanity-checked against the home-chain registry configuration, to ensure home-chain\\n  /// and remote-chain parity!\\n  /// @param ocrConfigArgs OCR config update args.\\n  /// @dev precondition number of transmitters should match the expected F/fChain relationship.\\n  /// For transmitters, the function only validates that len(transmitters) \\u003e 0 \\u0026\\u0026 len(transmitters) \\u003c= MAX_NUM_ORACLES\\n  /// \\u0026\\u0026 len(transmitters) \\u003c= len(signers) [if sig verification is enabled].\\n  function setOCR3Configs(\\n    OCRConfigArgs[] memory ocrConfigArgs\\n  ) external onlyOwner {\\n    for (uint256 i; i \\u003c ocrConfigArgs.length; ++i) {\\n      _setOCR3Config(ocrConfigArgs[i]);\\n    }\\n  }\\n\\n  /// @notice Sets offchain reporting protocol configuration incl. participating oracles for a single OCR plugin type.\\n  /// @param ocrConfigArgs OCR config update args.\\n  function _setOCR3Config(\\n    OCRConfigArgs memory ocrConfigArgs\\n  ) internal {\\n    if (ocrConfigArgs.F == 0) revert InvalidConfig(InvalidConfigErrorType.F_MUST_BE_POSITIVE);\\n\\n    uint8 ocrPluginType = ocrConfigArgs.ocrPluginType;\\n    OCRConfig storage ocrConfig = s_ocrConfigs[ocrPluginType];\\n    ConfigInfo storage configInfo = ocrConfig.configInfo;\\n\\n    // If F is 0, then the config is not yet set.\\n    if (configInfo.F == 0) {\\n      configInfo.isSignatureVerificationEnabled = ocrConfigArgs.isSignatureVerificationEnabled;\\n    } else {\\n      if (configInfo.isSignatureVerificationEnabled != ocrConfigArgs.isSignatureVerificationEnabled) {\\n        revert StaticConfigCannotBeChanged(ocrPluginType);\\n      }\\n    }\\n\\n    address[] memory transmitters = ocrConfigArgs.transmitters;\\n    if (transmitters.length \\u003e MAX_NUM_ORACLES) revert InvalidConfig(InvalidConfigErrorType.TOO_MANY_TRANSMITTERS);\\n    if (transmitters.length == 0) revert InvalidConfig(InvalidConfigErrorType.NO_TRANSMITTERS);\\n\\n    _clearOracleRoles(ocrPluginType, ocrConfig.transmitters);\\n\\n    if (ocrConfigArgs.isSignatureVerificationEnabled) {\\n      _clearOracleRoles(ocrPluginType, ocrConfig.signers);\\n\\n      address[] memory signers = ocrConfigArgs.signers;\\n\\n      if (signers.length \\u003e MAX_NUM_ORACLES) revert InvalidConfig(InvalidConfigErrorType.TOO_MANY_SIGNERS);\\n      if (signers.length \\u003c= 3 * ocrConfigArgs.F) revert InvalidConfig(InvalidConfigErrorType.F_TOO_HIGH);\\n      // NOTE: Transmitters cannot exceed signers. Transmitters do not have to be \\u003e= 3F + 1 because they can\\n      // match \\u003e= 3fChain + 1, where fChain \\u003c= F. fChain is not represented in MultiOCR3Base - so we skip this check.\\n      if (signers.length \\u003c transmitters.length) revert InvalidConfig(InvalidConfigErrorType.TOO_MANY_TRANSMITTERS);\\n\\n      configInfo.n = uint8(signers.length);\\n      ocrConfig.signers = signers;\\n\\n      _assignOracleRoles(ocrPluginType, signers, Role.Signer);\\n    }\\n\\n    _assignOracleRoles(ocrPluginType, transmitters, Role.Transmitter);\\n\\n    ocrConfig.transmitters = transmitters;\\n    configInfo.F = ocrConfigArgs.F;\\n    configInfo.configDigest = ocrConfigArgs.configDigest;\\n\\n    emit ConfigSet(\\n      ocrPluginType, ocrConfigArgs.configDigest, ocrConfig.signers, ocrConfigArgs.transmitters, ocrConfigArgs.F\\n    );\\n    _afterOCR3ConfigSet(ocrPluginType);\\n  }\\n\\n  /// @notice Hook that is called after a plugin's OCR3 config changes.\\n  /// @param ocrPluginType Plugin type for which the config changed.\\n  function _afterOCR3ConfigSet(\\n    uint8 ocrPluginType\\n  ) internal virtual;\\n\\n  /// @notice Clears oracle roles for the provided oracle addresses.\\n  /// @param ocrPluginType OCR plugin type to clear roles for.\\n  /// @param oracleAddresses Oracle addresses to clear roles for.\\n  function _clearOracleRoles(uint8 ocrPluginType, address[] memory oracleAddresses) internal {\\n    for (uint256 i = 0; i \\u003c oracleAddresses.length; ++i) {\\n      delete s_oracles[ocrPluginType][oracleAddresses[i]];\\n    }\\n  }\\n\\n  /// @notice Assigns oracles roles for the provided oracle addresses with uniqueness verification.\\n  /// @param ocrPluginType OCR plugin type to assign roles for.\\n  /// @param oracleAddresses Oracle addresses to assign roles to.\\n  /// @param role Role to assign.\\n  function _assignOracleRoles(uint8 ocrPluginType, address[] memory oracleAddresses, Role role) internal {\\n    for (uint256 i = 0; i \\u003c oracleAddresses.length; ++i) {\\n      address oracle = oracleAddresses[i];\\n      if (s_oracles[ocrPluginType][oracle].role != Role.Unset) {\\n        revert InvalidConfig(InvalidConfigErrorType.REPEATED_ORACLE_ADDRESS);\\n      }\\n      if (oracle == address(0)) revert OracleCannotBeZeroAddress();\\n      s_oracles[ocrPluginType][oracle] = Oracle(uint8(i), role);\\n    }\\n  }\\n\\n  /// @notice _transmit is called to post a new report to the contract. The function should be called after the per-DON\\n  /// reporting logic is completed.\\n  /// @param ocrPluginType OCR plugin type to transmit report for\\n  /// @param report serialized report, which the signatures are signing.\\n  /// @param rs ith element is the R components of the ith signature on report. Must have at most MAX_NUM_ORACLES entries.\\n  /// @param ss ith element is the S components of the ith signature on report. Must have at most MAX_NUM_ORACLES entries.\\n  /// @param rawVs ith element is the the V component of the ith signature.\\n  function _transmit(\\n    uint8 ocrPluginType,\\n    // NOTE: If these parameters are changed, expectedMsgDataLength and/or TRANSMIT_MSGDATA_CONSTANT_LENGTH_COMPONENT\\n    // need to be changed accordingly.\\n    bytes32[2] calldata reportContext,\\n    bytes calldata report,\\n    bytes32[] memory rs,\\n    bytes32[] memory ss,\\n    bytes32 rawVs\\n  ) internal {\\n    // reportContext consists of:\\n    // reportContext[0]: ConfigDigest.\\n    // reportContext[1]: 24 byte padding, 8 byte sequence number.\\n    ConfigInfo memory configInfo = s_ocrConfigs[ocrPluginType].configInfo;\\n    bytes32 configDigest = reportContext[0];\\n\\n    // Scoping this reduces stack pressure and gas usage.\\n    {\\n      // one byte per entry in _report\\n      uint256 expectedDataLength = uint256(TRANSMIT_MSGDATA_CONSTANT_LENGTH_COMPONENT_NO_SIGNATURES) + report.length;\\n\\n      if (configInfo.isSignatureVerificationEnabled) {\\n        // 32 bytes per entry in _rs, _ss\\n        expectedDataLength +=\\n          TRANSMIT_MSGDATA_EXTRA_CONSTANT_LENGTH_COMPONENT_FOR_SIGNATURES + rs.length * 32 + ss.length * 32;\\n      }\\n\\n      if (msg.data.length != expectedDataLength) revert WrongMessageLength(expectedDataLength, msg.data.length);\\n    }\\n\\n    if (configInfo.configDigest != configDigest) {\\n      revert ConfigDigestMismatch(configInfo.configDigest, configDigest);\\n    }\\n    // If the cached chainID at time of deployment doesn't match the current chainID, we reject all signed reports.\\n    // This avoids a (rare) scenario where chain A forks into chain A and A', A' still has configDigest calculated\\n    // from chain A and so OCR reports will be valid on both forks.\\n    _whenChainNotForked();\\n\\n    // Scoping this reduces stack pressure and gas usage.\\n    {\\n      Oracle memory transmitter = s_oracles[ocrPluginType][msg.sender];\\n      // Check that sender is authorized to report.\\n      if (\\n        !(\\n          transmitter.role == Role.Transmitter\\n            \\u0026\\u0026 msg.sender == s_ocrConfigs[ocrPluginType].transmitters[transmitter.index]\\n        )\\n      ) {\\n        if (msg.sender != Internal.GAS_ESTIMATION_SENDER) {\\n          revert UnauthorizedTransmitter();\\n        }\\n      }\\n    }\\n\\n    if (configInfo.isSignatureVerificationEnabled) {\\n      // Scoping to reduce stack pressure.\\n      {\\n        if (rs.length != configInfo.F + 1) revert WrongNumberOfSignatures();\\n        if (rs.length != ss.length) revert SignaturesOutOfRegistration();\\n      }\\n\\n      bytes32 h = keccak256(abi.encodePacked(keccak256(report), reportContext));\\n      _verifySignatures(ocrPluginType, h, rs, ss, rawVs);\\n    }\\n\\n    emit Transmitted(ocrPluginType, configDigest, uint64(uint256(reportContext[1])));\\n  }\\n\\n  /// @notice Verifies the signatures of a hashed report value for one OCR plugin type.\\n  /// @param ocrPluginType OCR plugin type to transmit report for.\\n  /// @param hashedReport hashed encoded packing of report + reportContext.\\n  /// @param rs ith element is the R components of the ith signature on report. Must have at most MAX_NUM_ORACLES entries.\\n  /// @param ss ith element is the S components of the ith signature on report. Must have at most MAX_NUM_ORACLES entries.\\n  /// @param rawVs ith element is the the V component of the ith signature.\\n  function _verifySignatures(\\n    uint8 ocrPluginType,\\n    bytes32 hashedReport,\\n    bytes32[] memory rs,\\n    bytes32[] memory ss,\\n    bytes32 rawVs\\n  ) internal view {\\n    // Verify signatures attached to report. Using a uint256 means we can only verify up to 256 oracles.\\n    uint256 signed = 0;\\n\\n    uint256 numberOfSignatures = rs.length;\\n    for (uint256 i; i \\u003c numberOfSignatures; ++i) {\\n      // Safe from ECDSA malleability here since we check for duplicate signers.\\n      address signer = ecrecover(hashedReport, uint8(rawVs[i]) + 27, rs[i], ss[i]);\\n      // Since we disallow address(0) as a valid signer address, it can never have a signer role.\\n      Oracle memory oracle = s_oracles[ocrPluginType][signer];\\n      if (oracle.role != Role.Signer) revert UnauthorizedSigner();\\n      if (signed \\u0026 (0x1 \\u003c\\u003c oracle.index) != 0) revert NonUniqueSignatures();\\n      signed |= 0x1 \\u003c\\u003c oracle.index;\\n    }\\n  }\\n\\n  /// @notice Validates that the chain ID has not diverged after deployment. Reverts if the chain IDs do not match.\\n  function _whenChainNotForked() internal view {\\n    if (i_chainID != block.chainid) revert ForkedChain(i_chainID, block.chainid);\\n  }\\n\\n  /// @notice Information about current offchain reporting protocol configuration.\\n  /// @param ocrPluginType OCR plugin type to return config details for.\\n  /// @return ocrConfig OCR config for the plugin type.\\n  function latestConfigDetails(\\n    uint8 ocrPluginType\\n  ) external view returns (OCRConfig memory ocrConfig) {\\n    return s_ocrConfigs[ocrPluginType];\\n  }\\n}\\n\"},\"contracts/offRamp/OffRamp.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IAny2EVMMessageReceiver} from \\\"../interfaces/IAny2EVMMessageReceiver.sol\\\";\\nimport {IFeeQuoter} from \\\"../interfaces/IFeeQuoter.sol\\\";\\nimport {IMessageInterceptor} from \\\"../interfaces/IMessageInterceptor.sol\\\";\\nimport {INonceManager} from \\\"../interfaces/INonceManager.sol\\\";\\nimport {IPoolV1} from \\\"../interfaces/IPool.sol\\\";\\nimport {IRMNRemote} from \\\"../interfaces/IRMNRemote.sol\\\";\\nimport {IRouter} from \\\"../interfaces/IRouter.sol\\\";\\nimport {ITokenAdminRegistry} from \\\"../interfaces/ITokenAdminRegistry.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\nimport {ERC165CheckerReverting} from \\\"../libraries/ERC165CheckerReverting.sol\\\";\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {MerkleMultiProof} from \\\"../libraries/MerkleMultiProof.sol\\\";\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\nimport {MultiOCR3Base} from \\\"../ocr/MultiOCR3Base.sol\\\";\\nimport {CallWithExactGas} from \\\"@chainlink/contracts/src/v0.8/shared/call/CallWithExactGas.sol\\\";\\n\\nimport {IERC20} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/token/ERC20/IERC20.sol\\\";\\nimport {EnumerableSet} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @notice OffRamp enables OCR networks to execute multiple messages in an OffRamp in a single transaction.\\n/// @dev The OnRamp and OffRamp form a cross chain upgradeable unit. Any change to one of them results an\\n/// onchain upgrade of both contracts.\\n/// @dev MultiOCR3Base is used to store multiple OCR configs for the OffRamp. The execution plugin type has to be\\n/// configured without signature verification, and the commit plugin type with verification.\\ncontract OffRamp is ITypeAndVersion, MultiOCR3Base {\\n  using ERC165CheckerReverting for address;\\n  using EnumerableSet for EnumerableSet.UintSet;\\n\\n  error ZeroChainSelectorNotAllowed();\\n  error ExecutionError(bytes32 messageId, bytes err);\\n  error SourceChainNotEnabled(uint64 sourceChainSelector);\\n  error TokenDataMismatch(uint64 sourceChainSelector, uint64 sequenceNumber);\\n  error UnexpectedTokenData();\\n  error ManualExecutionNotYetEnabled(uint64 sourceChainSelector);\\n  error ManualExecutionGasLimitMismatch();\\n  error InvalidManualExecutionGasLimit(uint64 sourceChainSelector, bytes32 messageId, uint256 newLimit);\\n  error InvalidManualExecutionTokenGasOverride(\\n    bytes32 messageId, uint256 tokenIndex, uint256 oldLimit, uint256 tokenGasOverride\\n  );\\n  error ManualExecutionGasAmountCountMismatch(bytes32 messageId, uint64 sequenceNumber);\\n  error RootNotCommitted(uint64 sourceChainSelector);\\n  error RootAlreadyCommitted(uint64 sourceChainSelector, bytes32 merkleRoot);\\n  error InvalidRoot();\\n  error CanOnlySelfCall();\\n  error ReceiverError(bytes err);\\n  error TokenHandlingError(address target, bytes err);\\n  error ReleaseOrMintBalanceMismatch(uint256 amountReleased, uint256 balancePre, uint256 balancePost);\\n  error EmptyReport(uint64 sourceChainSelector);\\n  error EmptyBatch();\\n  error CursedByRMN(uint64 sourceChainSelector);\\n  error NotACompatiblePool(address notPool);\\n  error InvalidDataLength(uint256 expected, uint256 got);\\n  error InvalidNewState(uint64 sourceChainSelector, uint64 sequenceNumber, Internal.MessageExecutionState newState);\\n  error StaleCommitReport();\\n  error InvalidInterval(uint64 sourceChainSelector, uint64 min, uint64 max);\\n  error ZeroAddressNotAllowed();\\n  error InvalidMessageDestChainSelector(uint64 messageDestChainSelector);\\n  error SourceChainSelectorMismatch(uint64 reportSourceChainSelector, uint64 messageSourceChainSelector);\\n  error SignatureVerificationRequiredInCommitPlugin();\\n  error SignatureVerificationNotAllowedInExecutionPlugin();\\n  error CommitOnRampMismatch(bytes reportOnRamp, bytes configOnRamp);\\n  error InvalidOnRampUpdate(uint64 sourceChainSelector);\\n  error RootBlessingMismatch(uint64 sourceChainSelector, bytes32 merkleRoot, bool isBlessed);\\n\\n  /// @dev Atlas depends on various events, if changing, please notify Atlas.\\n  event StaticConfigSet(StaticConfig staticConfig);\\n  event DynamicConfigSet(DynamicConfig dynamicConfig);\\n  event ExecutionStateChanged(\\n    uint64 indexed sourceChainSelector,\\n    uint64 indexed sequenceNumber,\\n    bytes32 indexed messageId,\\n    bytes32 messageHash,\\n    Internal.MessageExecutionState state,\\n    bytes returnData,\\n    uint256 gasUsed\\n  );\\n  event SourceChainSelectorAdded(uint64 sourceChainSelector);\\n  event SourceChainConfigSet(uint64 indexed sourceChainSelector, SourceChainConfig sourceConfig);\\n  event SkippedAlreadyExecutedMessage(uint64 sourceChainSelector, uint64 sequenceNumber);\\n  event AlreadyAttempted(uint64 sourceChainSelector, uint64 sequenceNumber);\\n  event CommitReportAccepted(\\n    Internal.MerkleRoot[] blessedMerkleRoots,\\n    Internal.MerkleRoot[] unblessedMerkleRoots,\\n    Internal.PriceUpdates priceUpdates\\n  );\\n  event RootRemoved(bytes32 root);\\n  event SkippedReportExecution(uint64 sourceChainSelector);\\n\\n  /// @dev Struct that contains the static configuration. The individual components are stored as immutable variables.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct StaticConfig {\\n    uint64 chainSelector; // ───────╮ Destination chainSelector\\n    uint16 gasForCallExactCheck; // | Gas for call exact check\\n    IRMNRemote rmnRemote; // ───────╯ RMN Verification Contract\\n    address tokenAdminRegistry; // Token admin registry address\\n    address nonceManager; // Nonce manager address\\n  }\\n\\n  /// @dev Per-chain source config (defining a lane from a Source Chain -\\u003e Dest OffRamp).\\n  struct SourceChainConfig {\\n    IRouter router; // ─────────────────╮ Local router to use for messages coming from this source chain.\\n    bool isEnabled; //                  │ Flag whether the source chain is enabled or not.\\n    uint64 minSeqNr; //                 │ The min sequence number expected for future messages.\\n    bool isRMNVerificationDisabled; // ─╯ Flag whether the RMN verification is disabled or not.\\n    bytes onRamp; // OnRamp address on the source chain.\\n  }\\n\\n  /// @dev Same as SourceChainConfig but with source chain selector so that an array of these\\n  /// can be passed in the constructor and the applySourceChainConfigUpdates function.\\n  struct SourceChainConfigArgs {\\n    IRouter router; // ─────────────────╮  Local router to use for messages coming from this source chain.\\n    uint64 sourceChainSelector; //      │  Source chain selector of the config to update.\\n    bool isEnabled; //                  │  Flag whether the source chain is enabled or not.\\n    bool isRMNVerificationDisabled; // ─╯ Flag whether the RMN verification is disabled or not.\\n    bytes onRamp; // OnRamp address on the source chain.\\n  }\\n\\n  /// @dev Dynamic offRamp config.\\n  /// @dev Since DynamicConfig is part of DynamicConfigSet event, if changing it, we should update the ABI on Atlas.\\n  struct DynamicConfig {\\n    address feeQuoter; // ──────────────────────────────╮ FeeQuoter address on the local chain.\\n    uint32 permissionLessExecutionThresholdSeconds; // ─╯ Waiting time before manual execution is enabled.\\n    address messageInterceptor; // Optional, validates incoming messages (zero address = no interceptor).\\n  }\\n\\n  /// @dev Report that is committed by the observing DON at the committing phase.\\n  struct CommitReport {\\n    Internal.PriceUpdates priceUpdates; // List of gas and price updates to commit.\\n    Internal.MerkleRoot[] blessedMerkleRoots; // List of merkle roots from source chains for which RMN is enabled.\\n    Internal.MerkleRoot[] unblessedMerkleRoots; // List of merkle roots from source chains for which RMN is disabled.\\n    IRMNRemote.Signature[] rmnSignatures; // RMN signatures on the merkle roots.\\n  }\\n\\n  /// @dev Both receiverExecutionGasLimit and tokenGasOverrides are optional. To indicate no override, set the value\\n  /// to 0. The length of tokenGasOverrides must match the length of tokenAmounts, even if it only contains zeros.\\n  struct GasLimitOverride {\\n    uint256 receiverExecutionGasLimit; // Overrides EVM2EVMMessage.gasLimit.\\n    uint32[] tokenGasOverrides; // Overrides EVM2EVMMessage.sourceTokenData.destGasAmount, length must be same as tokenAmounts.\\n  }\\n\\n  // STATIC CONFIG\\n  string public constant override typeAndVersion = \\\"OffRamp 1.6.0\\\";\\n  /// @dev Hash of encoded address(0) used for empty address checks.\\n  bytes32 internal constant EMPTY_ENCODED_ADDRESS_HASH = keccak256(abi.encode(address(0)));\\n  /// @dev ChainSelector of this chain.\\n  uint64 internal immutable i_chainSelector;\\n  /// @dev The RMN verification contract.\\n  IRMNRemote internal immutable i_rmnRemote;\\n  /// @dev The address of the token admin registry.\\n  address internal immutable i_tokenAdminRegistry;\\n  /// @dev The address of the nonce manager.\\n  address internal immutable i_nonceManager;\\n  /// @dev The minimum amount of gas to perform the call with exact gas.\\n  /// We include this in the offRamp so that we can redeploy to adjust it should a hardfork change the gas costs of\\n  /// relevant opcodes in callWithExactGas.\\n  uint16 internal immutable i_gasForCallExactCheck;\\n\\n  // DYNAMIC CONFIG\\n  DynamicConfig internal s_dynamicConfig;\\n\\n  /// @notice Set of source chain selectors.\\n  EnumerableSet.UintSet internal s_sourceChainSelectors;\\n\\n  /// @notice SourceChainConfig per source chain selector.\\n  mapping(uint64 sourceChainSelector =\\u003e SourceChainConfig sourceChainConfig) private s_sourceChainConfigs;\\n\\n  // STATE\\n  /// @dev A mapping of sequence numbers (per source chain) to execution state using a bitmap with each execution\\n  /// state only taking up 2 bits of the uint256, packing 128 states into a single slot.\\n  /// Message state is tracked to ensure message can only be executed successfully once.\\n  mapping(uint64 sourceChainSelector =\\u003e mapping(uint64 seqNum =\\u003e uint256 executionStateBitmap)) internal\\n    s_executionStates;\\n\\n  /// @notice Commit timestamp of merkle roots per source chain.\\n  mapping(uint64 sourceChainSelector =\\u003e mapping(bytes32 merkleRoot =\\u003e uint256 timestamp)) internal s_roots;\\n  /// @dev The sequence number of the last price update.\\n  uint64 private s_latestPriceSequenceNumber;\\n\\n  constructor(\\n    StaticConfig memory staticConfig,\\n    DynamicConfig memory dynamicConfig,\\n    SourceChainConfigArgs[] memory sourceChainConfigs\\n  ) MultiOCR3Base() {\\n    if (\\n      address(staticConfig.rmnRemote) == address(0) || staticConfig.tokenAdminRegistry == address(0)\\n        || staticConfig.nonceManager == address(0)\\n    ) {\\n      revert ZeroAddressNotAllowed();\\n    }\\n\\n    if (staticConfig.chainSelector == 0) {\\n      revert ZeroChainSelectorNotAllowed();\\n    }\\n\\n    i_chainSelector = staticConfig.chainSelector;\\n    i_rmnRemote = staticConfig.rmnRemote;\\n    i_tokenAdminRegistry = staticConfig.tokenAdminRegistry;\\n    i_nonceManager = staticConfig.nonceManager;\\n    i_gasForCallExactCheck = staticConfig.gasForCallExactCheck;\\n    emit StaticConfigSet(staticConfig);\\n\\n    _setDynamicConfig(dynamicConfig);\\n    _applySourceChainConfigUpdates(sourceChainConfigs);\\n  }\\n\\n  // ================================================================\\n  // │                           Execution                          │\\n  // ================================================================\\n\\n  // The size of the execution state in bits.\\n  uint256 private constant MESSAGE_EXECUTION_STATE_BIT_WIDTH = 2;\\n  // The mask for the execution state bits.\\n  uint256 private constant MESSAGE_EXECUTION_STATE_MASK = (1 \\u003c\\u003c MESSAGE_EXECUTION_STATE_BIT_WIDTH) - 1;\\n\\n  /// @notice Returns the current execution state of a message based on its sequenceNumber.\\n  /// @param sourceChainSelector The source chain to get the execution state for.\\n  /// @param sequenceNumber The sequence number of the message to get the execution state for.\\n  /// @return executionState The current execution state of the message.\\n  /// @dev We use the literal number 128 because using a constant increased gas usage.\\n  function getExecutionState(\\n    uint64 sourceChainSelector,\\n    uint64 sequenceNumber\\n  ) public view returns (Internal.MessageExecutionState) {\\n    return Internal.MessageExecutionState(\\n      (\\n        _getSequenceNumberBitmap(sourceChainSelector, sequenceNumber)\\n          \\u003e\\u003e ((sequenceNumber % 128) * MESSAGE_EXECUTION_STATE_BIT_WIDTH)\\n      ) \\u0026 MESSAGE_EXECUTION_STATE_MASK\\n    );\\n  }\\n\\n  /// @notice Sets a new execution state for a given sequence number. It will overwrite any existing state.\\n  /// @param sourceChainSelector The source chain to set the execution state for.\\n  /// @param sequenceNumber The sequence number for which the state will be saved.\\n  /// @param newState The new value the state will be in after this function is called.\\n  /// @dev We use the literal number 128 because using a constant increased gas usage.\\n  function _setExecutionState(\\n    uint64 sourceChainSelector,\\n    uint64 sequenceNumber,\\n    Internal.MessageExecutionState newState\\n  ) internal {\\n    uint256 offset = (sequenceNumber % 128) * MESSAGE_EXECUTION_STATE_BIT_WIDTH;\\n    uint256 bitmap = _getSequenceNumberBitmap(sourceChainSelector, sequenceNumber);\\n    // To unset any potential existing state we zero the bits of the section the state occupies,\\n    // then we do an AND operation to blank out any existing state for the section.\\n    bitmap \\u0026= ~(MESSAGE_EXECUTION_STATE_MASK \\u003c\\u003c offset);\\n    // Set the new state.\\n    bitmap |= uint256(newState) \\u003c\\u003c offset;\\n\\n    s_executionStates[sourceChainSelector][sequenceNumber / 128] = bitmap;\\n  }\\n\\n  /// @param sourceChainSelector remote source chain selector to get sequence number bitmap for.\\n  /// @param sequenceNumber sequence number to get bitmap for.\\n  /// @return bitmap Bitmap of the given sequence number for the provided source chain selector. One bitmap represents\\n  /// 128 sequence numbers.\\n  function _getSequenceNumberBitmap(\\n    uint64 sourceChainSelector,\\n    uint64 sequenceNumber\\n  ) internal view returns (uint256 bitmap) {\\n    return s_executionStates[sourceChainSelector][sequenceNumber / 128];\\n  }\\n\\n  /// @notice Manually executes a set of reports.\\n  /// @param reports Internal.ExecutionReportSingleChain[] - list of reports to execute.\\n  /// @param gasLimitOverrides New gasLimit for each message per report. The outer array represents each report, the\\n  //  inner array represents each message in the report.\\n  //  i.e. gasLimitOverrides[report1][report1Message1] -\\u003e access message1 from report1\\n  /// @dev We permit gas limit overrides so that users may manually execute messages which failed due to insufficient\\n  /// gas provided. The reports do not have to contain all the messages (they can be omitted). Multiple reports can be\\n  /// passed in simultaneously.\\n  function manuallyExecute(\\n    Internal.ExecutionReport[] memory reports,\\n    GasLimitOverride[][] memory gasLimitOverrides\\n  ) external {\\n    // We do this here because the other _execute path is already covered by MultiOCR3Base.\\n    _whenChainNotForked();\\n\\n    uint256 numReports = reports.length;\\n    if (numReports != gasLimitOverrides.length) revert ManualExecutionGasLimitMismatch();\\n\\n    for (uint256 reportIndex = 0; reportIndex \\u003c numReports; ++reportIndex) {\\n      Internal.ExecutionReport memory report = reports[reportIndex];\\n\\n      uint256 numMsgs = report.messages.length;\\n      GasLimitOverride[] memory msgGasLimitOverrides = gasLimitOverrides[reportIndex];\\n\\n      // Gas override values need to be provided, even when no override is desired. We expect an array of the correct\\n      // size with all `0` values if no override is desired.\\n      if (numMsgs != msgGasLimitOverrides.length) revert ManualExecutionGasLimitMismatch();\\n\\n      for (uint256 msgIndex = 0; msgIndex \\u003c numMsgs; ++msgIndex) {\\n        uint256 newLimit = msgGasLimitOverrides[msgIndex].receiverExecutionGasLimit;\\n        Internal.Any2EVMRampMessage memory message = report.messages[msgIndex];\\n        if (newLimit != 0) {\\n          // Checks to ensure messages will not be executed with less gas than specified.\\n          if (newLimit \\u003c message.gasLimit) {\\n            revert InvalidManualExecutionGasLimit(report.sourceChainSelector, message.header.messageId, newLimit);\\n          }\\n        }\\n        if (message.tokenAmounts.length != msgGasLimitOverrides[msgIndex].tokenGasOverrides.length) {\\n          revert ManualExecutionGasAmountCountMismatch(message.header.messageId, message.header.sequenceNumber);\\n        }\\n\\n        // The gas limit can not be lowered as that could cause the message to fail. If manual execution is done\\n        // from an UNTOUCHED state and we would allow lower gas limit, anyone could grief by executing the message with\\n        // lower gas limit than the DON would have used. This results in the message being marked FAILURE and the DON\\n        // would not attempt it with the correct gas limit.\\n        for (uint256 tokenIndex = 0; tokenIndex \\u003c message.tokenAmounts.length; ++tokenIndex) {\\n          uint256 tokenGasOverride = msgGasLimitOverrides[msgIndex].tokenGasOverrides[tokenIndex];\\n          if (tokenGasOverride != 0) {\\n            uint256 destGasAmount = message.tokenAmounts[tokenIndex].destGasAmount;\\n            if (tokenGasOverride \\u003c destGasAmount) {\\n              revert InvalidManualExecutionTokenGasOverride(\\n                message.header.messageId, tokenIndex, destGasAmount, tokenGasOverride\\n              );\\n            }\\n          }\\n        }\\n      }\\n    }\\n\\n    _batchExecute(reports, gasLimitOverrides);\\n  }\\n\\n  /// @notice Transmit function for execution reports. The function takes no signatures, and expects the exec plugin\\n  /// type to be configured with no signatures.\\n  /// @param report serialized execution report.\\n  function execute(bytes32[2] calldata reportContext, bytes calldata report) external {\\n    _batchExecute(abi.decode(report, (Internal.ExecutionReport[])), new GasLimitOverride[][](0));\\n\\n    bytes32[] memory emptySigs = new bytes32[](0);\\n    _transmit(uint8(Internal.OCRPluginType.Execution), reportContext, report, emptySigs, emptySigs, bytes32(\\\"\\\"));\\n  }\\n\\n  /// @notice Batch executes a set of reports, each report matching one single source chain.\\n  /// @param reports Set of execution reports (one per chain) containing the messages and proofs.\\n  /// @param manualExecGasLimits An array of gas limits to use for manual execution The outer array represents each\\n  //  report, the inner array represents each message in the report.\\n  //  i.e. gasLimitOverrides[report1][report1Message1] -\\u003e access message1 from report1.\\n  /// @dev The manualExecGasLimits array should either be empty, or match the length of the reports array.\\n  /// @dev If called from manual execution, each inner array's length has to match the number of messages.\\n  function _batchExecute(\\n    Internal.ExecutionReport[] memory reports,\\n    GasLimitOverride[][] memory manualExecGasOverrides\\n  ) internal {\\n    if (reports.length == 0) revert EmptyBatch();\\n\\n    bool areManualGasLimitsEmpty = manualExecGasOverrides.length == 0;\\n    // Cache array for gas savings in the loop's condition.\\n    GasLimitOverride[] memory emptyGasLimits = new GasLimitOverride[](0);\\n\\n    for (uint256 i = 0; i \\u003c reports.length; ++i) {\\n      _executeSingleReport(reports[i], areManualGasLimitsEmpty ? emptyGasLimits : manualExecGasOverrides[i]);\\n    }\\n  }\\n\\n  /// @notice Executes a report, executing each message in order.\\n  /// @param report The execution report containing the messages and proofs.\\n  /// @param manualExecGasExecOverrides An array of gas limits to use for manual execution.\\n  /// @dev If called from the DON, this array is always empty.\\n  /// @dev If called from manual execution, this array is always same length as messages.\\n  /// @dev This function can fully revert in some cases, reverting potentially valid other reports with it. The reasons\\n  /// for these reverts are so severe that we prefer to revert the entire batch instead of silently failing.\\n  function _executeSingleReport(\\n    Internal.ExecutionReport memory report,\\n    GasLimitOverride[] memory manualExecGasExecOverrides\\n  ) internal {\\n    uint64 sourceChainSelector = report.sourceChainSelector;\\n    bool manualExecution = manualExecGasExecOverrides.length != 0;\\n    if (i_rmnRemote.isCursed(bytes16(uint128(sourceChainSelector)))) {\\n      if (manualExecution) {\\n        // For manual execution we don't want to silently fail so we revert.\\n        revert CursedByRMN(sourceChainSelector);\\n      }\\n      // For DON execution we do not revert as a single lane curse can revert the entire batch.\\n      emit SkippedReportExecution(sourceChainSelector);\\n      return;\\n    }\\n\\n    uint256 numMsgs = report.messages.length;\\n    if (numMsgs == 0) revert EmptyReport(report.sourceChainSelector);\\n    if (numMsgs != report.offchainTokenData.length) revert UnexpectedTokenData();\\n\\n    bytes32[] memory hashedLeaves = new bytes32[](numMsgs);\\n\\n    {\\n      // We do this hash here instead of in _verify to avoid two separate loops over the same data. Hashing all of the\\n      // message fields ensures that the message being executed is correct and not tampered with. Including the known\\n      // OnRamp ensures that the message originates from the correct on ramp version. We know the sourceChainSelector\\n      // and i_destChainSelector are correct because we revert below when they are not.\\n      bytes32 metaDataHash = keccak256(\\n        abi.encode(\\n          Internal.ANY_2_EVM_MESSAGE_HASH,\\n          sourceChainSelector,\\n          i_chainSelector,\\n          keccak256(_getEnabledSourceChainConfig(sourceChainSelector).onRamp)\\n        )\\n      );\\n\\n      for (uint256 i = 0; i \\u003c numMsgs; ++i) {\\n        Internal.Any2EVMRampMessage memory message = report.messages[i];\\n\\n        // Commits do not verify the destChainSelector in the message since only the root is committed, so we\\n        // have to check it explicitly. This check is also important as we have assumed the metaDataHash above uses\\n        // the i_chainSelector as the destChainSelector.\\n        if (message.header.destChainSelector != i_chainSelector) {\\n          revert InvalidMessageDestChainSelector(message.header.destChainSelector);\\n        }\\n        // If the message source chain selector does not match the report's source chain selector and the root has not\\n        // been committed for the report source chain selector this will be caught by the root verification.\\n        // This acts as an extra check to ensure the message source chain selector matches the report's source chain.\\n        if (message.header.sourceChainSelector != sourceChainSelector) {\\n          revert SourceChainSelectorMismatch(sourceChainSelector, message.header.sourceChainSelector);\\n        }\\n\\n        hashedLeaves[i] = Internal._hash(message, metaDataHash);\\n      }\\n    }\\n\\n    // SECURITY CRITICAL CHECK.\\n    uint256 timestampCommitted = _verify(sourceChainSelector, hashedLeaves, report.proofs, report.proofFlagBits);\\n    if (timestampCommitted == 0) revert RootNotCommitted(sourceChainSelector);\\n\\n    // Execute messages.\\n    for (uint256 i = 0; i \\u003c numMsgs; ++i) {\\n      uint256 gasStart = gasleft();\\n      Internal.Any2EVMRampMessage memory message = report.messages[i];\\n      message = _beforeExecuteSingleMessage(message);\\n\\n      Internal.MessageExecutionState originalState =\\n        getExecutionState(sourceChainSelector, message.header.sequenceNumber);\\n      // Two valid cases here, we either have never touched this message before, or we tried to execute and failed. This\\n      // check protects against reentry and re-execution because the other state is IN_PROGRESS which should not be\\n      // allowed to execute.\\n      if (\\n        !(\\n          originalState == Internal.MessageExecutionState.UNTOUCHED\\n            || originalState == Internal.MessageExecutionState.FAILURE\\n        )\\n      ) {\\n        // If the message has already been executed, we skip it. We want to not revert on race conditions between\\n        // executing parties. This will allow us to open up manual exec while also attempting with the DON, without\\n        // reverting an entire DON batch when a user manually executes while the tx is inflight.\\n        emit SkippedAlreadyExecutedMessage(sourceChainSelector, message.header.sequenceNumber);\\n        continue;\\n      }\\n      uint32[] memory tokenGasOverrides;\\n      if (manualExecution) {\\n        tokenGasOverrides = manualExecGasExecOverrides[i].tokenGasOverrides;\\n        bool isOldCommitReport =\\n          (block.timestamp - timestampCommitted) \\u003e s_dynamicConfig.permissionLessExecutionThresholdSeconds;\\n        // Manually execution is fine if we previously failed or if the commit report is just too old.\\n        // Acceptable state transitions: UNTOUCHED-\\u003eSUCCESS, UNTOUCHED-\\u003eFAILURE, FAILURE-\\u003eSUCCESS.\\n        if (!(isOldCommitReport || originalState == Internal.MessageExecutionState.FAILURE)) {\\n          revert ManualExecutionNotYetEnabled(sourceChainSelector);\\n        }\\n\\n        // Manual execution gas limit can override gas limit specified in the message. Value of 0 indicates no override.\\n        if (manualExecGasExecOverrides[i].receiverExecutionGasLimit != 0) {\\n          message.gasLimit = manualExecGasExecOverrides[i].receiverExecutionGasLimit;\\n        }\\n      } else {\\n        // DON can only execute a message once.\\n        // Acceptable state transitions: UNTOUCHED-\\u003eSUCCESS, UNTOUCHED-\\u003eFAILURE.\\n        if (originalState != Internal.MessageExecutionState.UNTOUCHED) {\\n          emit AlreadyAttempted(sourceChainSelector, message.header.sequenceNumber);\\n          continue;\\n        }\\n      }\\n\\n      // Nonce changes per state transition (these only apply for ordered messages):\\n      // UNTOUCHED -\\u003e FAILURE  nonce bump.\\n      // UNTOUCHED -\\u003e SUCCESS  nonce bump.\\n      // FAILURE   -\\u003e SUCCESS  no nonce bump.\\n      // UNTOUCHED messages MUST be executed in order always.\\n      // If nonce == 0 then out of order execution is allowed.\\n      if (message.header.nonce != 0) {\\n        if (originalState == Internal.MessageExecutionState.UNTOUCHED) {\\n          // If a nonce is not incremented, that means it was skipped, and we can ignore the message.\\n          if (\\n            !INonceManager(i_nonceManager).incrementInboundNonce(\\n              sourceChainSelector, message.header.nonce, message.sender\\n            )\\n          ) continue;\\n        }\\n      }\\n\\n      // We expect only valid messages will be committed but we check when executing as a defense in depth measure.\\n      bytes[] memory offchainTokenData = report.offchainTokenData[i];\\n      if (message.tokenAmounts.length != offchainTokenData.length) {\\n        revert TokenDataMismatch(sourceChainSelector, message.header.sequenceNumber);\\n      }\\n\\n      _setExecutionState(sourceChainSelector, message.header.sequenceNumber, Internal.MessageExecutionState.IN_PROGRESS);\\n      (Internal.MessageExecutionState newState, bytes memory returnData) =\\n        _trialExecute(message, offchainTokenData, tokenGasOverrides);\\n      _setExecutionState(sourceChainSelector, message.header.sequenceNumber, newState);\\n\\n      // Since it's hard to estimate whether manual execution will succeed, we revert the entire transaction if it\\n      // fails. This will show the user if their manual exec will fail before they submit it.\\n      if (manualExecution) {\\n        if (newState == Internal.MessageExecutionState.FAILURE) {\\n          if (originalState != Internal.MessageExecutionState.UNTOUCHED) {\\n            // If manual execution fails, we revert the entire transaction, unless the originalState is UNTOUCHED as we\\n            // would still be making progress by changing the state from UNTOUCHED to FAILURE.\\n            revert ExecutionError(message.header.messageId, returnData);\\n          }\\n        }\\n      }\\n\\n      // The only valid prior states are UNTOUCHED and FAILURE (checked above).\\n      // The only valid post states are FAILURE and SUCCESS (checked below).\\n      if (newState != Internal.MessageExecutionState.SUCCESS) {\\n        if (newState != Internal.MessageExecutionState.FAILURE) {\\n          revert InvalidNewState(sourceChainSelector, message.header.sequenceNumber, newState);\\n        }\\n      }\\n\\n      emit ExecutionStateChanged(\\n        sourceChainSelector,\\n        message.header.sequenceNumber,\\n        message.header.messageId,\\n        hashedLeaves[i],\\n        newState,\\n        returnData,\\n        // This emit covers not only the execution through the router, but also all of the overhead in executing the\\n        // message. This gives the most accurate representation of the gas used in the execution.\\n        gasStart - gasleft()\\n      );\\n    }\\n  }\\n\\n  /// @notice Try executing a message.\\n  /// @param message Internal.Any2EVMRampMessage memory message.\\n  /// @param offchainTokenData Data provided by the DON for token transfers.\\n  /// @return executionState The new state of the message, being either SUCCESS or FAILURE.\\n  /// @return errData Revert data in bytes if CCIP receiver reverted during execution.\\n  function _trialExecute(\\n    Internal.Any2EVMRampMessage memory message,\\n    bytes[] memory offchainTokenData,\\n    uint32[] memory tokenGasOverrides\\n  ) internal returns (Internal.MessageExecutionState executionState, bytes memory) {\\n    try this.executeSingleMessage(message, offchainTokenData, tokenGasOverrides) {}\\n    catch (bytes memory err) {\\n      if (msg.sender == Internal.GAS_ESTIMATION_SENDER) {\\n        if (\\n          CallWithExactGas.NOT_ENOUGH_GAS_FOR_CALL_SIG == bytes4(err)\\n            || CallWithExactGas.NO_GAS_FOR_CALL_EXACT_CHECK_SIG == bytes4(err)\\n            || ERC165CheckerReverting.InsufficientGasForStaticCall.selector == bytes4(err)\\n        ) {\\n          revert InsufficientGasToCompleteTx(bytes4(err));\\n        }\\n      }\\n      // return the message execution state as FAILURE and the revert data.\\n      // Max length of revert data is Router.MAX_RET_BYTES, max length of err is 4 + Router.MAX_RET_BYTES.\\n      return (Internal.MessageExecutionState.FAILURE, err);\\n    }\\n    // If message execution succeeded, no CCIP receiver return data is expected, return with empty bytes.\\n    return (Internal.MessageExecutionState.SUCCESS, \\\"\\\");\\n  }\\n\\n  /// @notice hook for applying custom logic to the input message before executeSingleMessage()\\n  /// @param message initial message\\n  /// @return transformedMessage modified message\\n  function _beforeExecuteSingleMessage(\\n    Internal.Any2EVMRampMessage memory message\\n  ) internal virtual returns (Internal.Any2EVMRampMessage memory transformedMessage) {\\n    return message;\\n  }\\n\\n  /// @notice Executes a single message.\\n  /// @param message The message that will be executed.\\n  /// @param offchainTokenData Token transfer data to be passed to TokenPool.\\n  /// @dev We make this external and callable by the contract itself, in order to try/catch\\n  /// its execution and enforce atomicity among successful message processing and token transfer.\\n  /// @dev We use ERC-165 to check for the ccipReceive interface to permit sending tokens to contracts, for example\\n  /// smart contract wallets, without an associated message.\\n  function executeSingleMessage(\\n    Internal.Any2EVMRampMessage memory message,\\n    bytes[] calldata offchainTokenData,\\n    uint32[] calldata tokenGasOverrides\\n  ) external {\\n    if (msg.sender != address(this)) revert CanOnlySelfCall();\\n\\n    Client.EVMTokenAmount[] memory destTokenAmounts = new Client.EVMTokenAmount[](0);\\n    if (message.tokenAmounts.length \\u003e 0) {\\n      destTokenAmounts = _releaseOrMintTokens(\\n        message.tokenAmounts,\\n        message.sender,\\n        message.receiver,\\n        message.header.sourceChainSelector,\\n        offchainTokenData,\\n        tokenGasOverrides\\n      );\\n    }\\n\\n    Client.Any2EVMMessage memory any2EvmMessage = Client.Any2EVMMessage({\\n      messageId: message.header.messageId,\\n      sourceChainSelector: message.header.sourceChainSelector,\\n      sender: message.sender,\\n      data: message.data,\\n      destTokenAmounts: destTokenAmounts\\n    });\\n\\n    // The main message interceptor is the aggregate rate limiter, but we also allow for a custom interceptor. This is\\n    // why we always have to call into the contract when it's enabled, even when there are no tokens in the message.\\n    address messageInterceptor = s_dynamicConfig.messageInterceptor;\\n    if (messageInterceptor != address(0)) {\\n      try IMessageInterceptor(messageInterceptor).onInboundMessage(any2EvmMessage) {}\\n      catch (bytes memory err) {\\n        revert IMessageInterceptor.MessageValidationError(err);\\n      }\\n    }\\n\\n    // There are three cases in which we skip calling the receiver:\\n    // 1. If the message data is empty AND the gas limit is 0.\\n    //          This indicates a message that only transfers tokens. It is valid to only send tokens to a contract\\n    //          that supports the IAny2EVMMessageReceiver interface, but without this first check we would call the\\n    //          receiver without any gas, which would revert the transaction.\\n    // 2. If the receiver is not a contract.\\n    // 3. If the receiver is a contract but it does not support the IAny2EVMMessageReceiver interface.\\n    //\\n    // The ordering of these checks is important, as the first check is the cheapest to execute.\\n    //\\n    // To prevent message delivery bypass issues, a modified version of the ERC165Checker is used\\n    // which checks for sufficient gas before making the external call.\\n    if (\\n      (message.data.length == 0 \\u0026\\u0026 message.gasLimit == 0) || message.receiver.code.length == 0\\n        || !message.receiver._supportsInterfaceReverting(type(IAny2EVMMessageReceiver).interfaceId)\\n    ) return;\\n\\n    (bool success, bytes memory returnData,) = s_sourceChainConfigs[message.header.sourceChainSelector]\\n      .router\\n      .routeMessage(any2EvmMessage, i_gasForCallExactCheck, message.gasLimit, message.receiver);\\n    // If CCIP receiver execution is not successful, revert the call including token transfers.\\n    if (!success) revert ReceiverError(returnData);\\n  }\\n\\n  // ================================================================\\n  // │                      Tokens and pools                        │\\n  // ================================================================\\n\\n  /// @notice Uses a pool to release or mint a token to a receiver address, with balance checks before and after the\\n  /// transfer. This is done to ensure the exact number of tokens the pool claims to release are actually transferred.\\n  /// @dev The local token address is validated through the TokenAdminRegistry. If, due to some misconfiguration, the\\n  /// token is unknown to the registry, the offRamp will revert. The tx, and the tokens, can be retrieved by registering\\n  /// the token on this chain, and re-trying the msg.\\n  /// @param sourceTokenAmount Amount and source data of the token to be released/minted.\\n  /// @param originalSender The message sender on the source chain.\\n  /// @param receiver The address that will receive the tokens.\\n  /// @param sourceChainSelector The remote source chain selector\\n  /// @param offchainTokenData Data fetched offchain by the DON.\\n  /// @return destTokenAmount local token address with amount.\\n  function _releaseOrMintSingleToken(\\n    Internal.Any2EVMTokenTransfer memory sourceTokenAmount,\\n    bytes memory originalSender,\\n    address receiver,\\n    uint64 sourceChainSelector,\\n    bytes memory offchainTokenData\\n  ) internal returns (Client.EVMTokenAmount memory destTokenAmount) {\\n    // We need to safely decode the token address from the sourceTokenData as it could be wrong, in which case it\\n    // doesn't have to be a valid EVM address.\\n    address localToken = sourceTokenAmount.destTokenAddress;\\n    // We check with the token admin registry if the token has a pool on this chain.\\n    address localPoolAddress = ITokenAdminRegistry(i_tokenAdminRegistry).getPool(localToken);\\n    // This will call the supportsInterface through the ERC165Checker, and not directly on the pool address.\\n    // This is done to prevent a pool from reverting the entire transaction if it doesn't support the interface.\\n    // The call gets a max or 30k gas per instance, of which there are three. This means offchain gas estimations should\\n    // account for 90k gas overhead due to the interface check.\\n    if (localPoolAddress == address(0) || !localPoolAddress._supportsInterfaceReverting(Pool.CCIP_POOL_V1)) {\\n      revert NotACompatiblePool(localPoolAddress);\\n    }\\n\\n    // We retrieve the local token balance of the receiver before the pool call.\\n    (uint256 balancePre, uint256 gasLeft) = _getBalanceOfReceiver(receiver, localToken, sourceTokenAmount.destGasAmount);\\n\\n    // We determined that the pool address is a valid EVM address, but that does not mean the code at this address is a\\n    // (compatible) pool contract. _callWithExactGasSafeReturnData will check if the location contains a contract. If it\\n    // doesn't it reverts with a known error. We call the pool with exact gas  to increase resistance against malicious\\n    // tokens or token pools. We protect against return data bombs by capping the return data size at MAX_RET_BYTES.\\n    (bool success, bytes memory returnData, uint256 gasUsedReleaseOrMint) = CallWithExactGas\\n      ._callWithExactGasSafeReturnData(\\n      abi.encodeCall(\\n        IPoolV1.releaseOrMint,\\n        Pool.ReleaseOrMintInV1({\\n          originalSender: originalSender,\\n          receiver: receiver,\\n          amount: sourceTokenAmount.amount,\\n          localToken: localToken,\\n          remoteChainSelector: sourceChainSelector,\\n          sourcePoolAddress: sourceTokenAmount.sourcePoolAddress,\\n          sourcePoolData: sourceTokenAmount.extraData,\\n          offchainTokenData: offchainTokenData\\n        })\\n      ),\\n      localPoolAddress,\\n      gasLeft,\\n      i_gasForCallExactCheck,\\n      Internal.MAX_RET_BYTES\\n    );\\n\\n    // Wrap and rethrow the error so we can catch it lower in the stack.\\n    if (!success) revert TokenHandlingError(localPoolAddress, returnData);\\n\\n    // If the call was successful, the returnData should be the amount released or minted denominated in the local\\n    // token's decimals.\\n    if (returnData.length != Pool.CCIP_POOL_V1_RET_BYTES) {\\n      revert InvalidDataLength(Pool.CCIP_POOL_V1_RET_BYTES, returnData.length);\\n    }\\n    uint256 localAmount = abi.decode(returnData, (uint256));\\n\\n    // We don't need to do balance checks if the pool is the receiver, as they would always fail in the case\\n    // of a lockRelease pool.\\n    if (receiver != localPoolAddress) {\\n      (uint256 balancePost,) = _getBalanceOfReceiver(receiver, localToken, gasLeft - gasUsedReleaseOrMint);\\n\\n      // First we check if the subtraction would result in an underflow to ensure we revert with a clear error.\\n      if (balancePost \\u003c balancePre || balancePost - balancePre != localAmount) {\\n        revert ReleaseOrMintBalanceMismatch(localAmount, balancePre, balancePost);\\n      }\\n    }\\n\\n    return Client.EVMTokenAmount({token: localToken, amount: localAmount});\\n  }\\n\\n  /// @notice Retrieves the balance of a receiver address for a given token.\\n  /// @param receiver The address to check the balance of.\\n  /// @param token The token address.\\n  /// @param gasLimit The gas limit to use for the call.\\n  /// @return balance The balance of the receiver.\\n  /// @return gasLeft The gas left after the call.\\n  function _getBalanceOfReceiver(\\n    address receiver,\\n    address token,\\n    uint256 gasLimit\\n  ) internal returns (uint256 balance, uint256 gasLeft) {\\n    (bool success, bytes memory returnData, uint256 gasUsed) = CallWithExactGas._callWithExactGasSafeReturnData(\\n      abi.encodeCall(IERC20.balanceOf, (receiver)), token, gasLimit, i_gasForCallExactCheck, Internal.MAX_RET_BYTES\\n    );\\n    if (!success) revert TokenHandlingError(token, returnData);\\n\\n    // If the call was successful, the returnData should contain only the balance.\\n    if (returnData.length != Internal.MAX_BALANCE_OF_RET_BYTES) {\\n      revert InvalidDataLength(Internal.MAX_BALANCE_OF_RET_BYTES, returnData.length);\\n    }\\n\\n    // Return the decoded balance, which cannot fail as we checked the length, and the gas that is left\\n    // after this call.\\n    return (abi.decode(returnData, (uint256)), gasLimit - gasUsed);\\n  }\\n\\n  /// @notice Uses pools to release or mint a number of different tokens to a receiver address.\\n  /// @param sourceTokenAmounts List of token amounts with source data of the tokens to be released/minted.\\n  /// @param originalSender The message sender on the source chain.\\n  /// @param receiver The address that will receive the tokens.\\n  /// @param sourceChainSelector The remote source chain selector.\\n  /// @param offchainTokenData Array of token data fetched offchain by the DON.\\n  /// @param tokenGasOverrides Array of override gas limits to use for token transfers. If empty, the normal gas limit\\n  /// as defined on the source chain is used.\\n  /// @return destTokenAmounts local token addresses with amounts.\\n  function _releaseOrMintTokens(\\n    Internal.Any2EVMTokenTransfer[] memory sourceTokenAmounts,\\n    bytes memory originalSender,\\n    address receiver,\\n    uint64 sourceChainSelector,\\n    bytes[] calldata offchainTokenData,\\n    uint32[] calldata tokenGasOverrides\\n  ) internal returns (Client.EVMTokenAmount[] memory destTokenAmounts) {\\n    destTokenAmounts = new Client.EVMTokenAmount[](sourceTokenAmounts.length);\\n    bool isTokenGasOverridesEmpty = tokenGasOverrides.length == 0;\\n    for (uint256 i = 0; i \\u003c sourceTokenAmounts.length; ++i) {\\n      if (!isTokenGasOverridesEmpty) {\\n        if (tokenGasOverrides[i] != 0) {\\n          sourceTokenAmounts[i].destGasAmount = tokenGasOverrides[i];\\n        }\\n      }\\n      destTokenAmounts[i] = _releaseOrMintSingleToken(\\n        sourceTokenAmounts[i], originalSender, receiver, sourceChainSelector, offchainTokenData[i]\\n      );\\n    }\\n\\n    return destTokenAmounts;\\n  }\\n\\n  // ================================================================\\n  // │                           Commit                             │\\n  // ================================================================\\n\\n  /// @notice Transmit function for commit reports. The function requires signatures,\\n  /// and expects the commit plugin type to be configured with signatures.\\n  /// @param report serialized commit report.\\n  /// @dev A commitReport can have two distinct parts (batched together to amortize the cost of checking sigs):\\n  /// 1. Price updates\\n  /// 2. A batch of merkle root and sequence number intervals (per-source)\\n  /// Both have their own, separate, staleness checks, with price updates using the epoch and round number of the latest\\n  /// price update. The merkle root checks for staleness are based on the seqNums.  They need to be separate because\\n  /// a price report for round t+2 might be included before a report containing a merkle root for round t+1. This merkle\\n  /// root report for round t+1 is still valid and should not be rejected. When a report with a stale root but valid\\n  /// price updates is submitted, we are OK to revert to preserve the invariant that we always revert on invalid\\n  /// sequence number ranges. If that happens, prices will be updated in later rounds.\\n  function commit(\\n    bytes32[2] calldata reportContext,\\n    bytes calldata report,\\n    bytes32[] calldata rs,\\n    bytes32[] calldata ss,\\n    bytes32 rawVs\\n  ) external {\\n    CommitReport memory commitReport = abi.decode(report, (CommitReport));\\n    DynamicConfig storage dynamicConfig = s_dynamicConfig;\\n\\n    // Verify RMN signatures\\n    if (commitReport.blessedMerkleRoots.length \\u003e 0) {\\n      i_rmnRemote.verify(address(this), commitReport.blessedMerkleRoots, commitReport.rmnSignatures);\\n    }\\n\\n    // Check if the report contains price updates.\\n    if (commitReport.priceUpdates.tokenPriceUpdates.length \\u003e 0 || commitReport.priceUpdates.gasPriceUpdates.length \\u003e 0)\\n    {\\n      uint64 ocrSequenceNumber = uint64(uint256(reportContext[1]));\\n\\n      // Check for price staleness based on the epoch and round.\\n      if (s_latestPriceSequenceNumber \\u003c ocrSequenceNumber) {\\n        // If prices are not stale, update the latest epoch and round.\\n        s_latestPriceSequenceNumber = ocrSequenceNumber;\\n        // And update the prices in the fee quoter.\\n        IFeeQuoter(dynamicConfig.feeQuoter).updatePrices(commitReport.priceUpdates);\\n      } else {\\n        // If prices are stale and the report doesn't contain a root, this report does not have any valid information\\n        // and we revert. If it does contain a merkle root, continue to the root checking section.\\n        if (commitReport.blessedMerkleRoots.length + commitReport.unblessedMerkleRoots.length == 0) {\\n          revert StaleCommitReport();\\n        }\\n      }\\n    }\\n\\n    for (uint256 i = 0; i \\u003c commitReport.blessedMerkleRoots.length; ++i) {\\n      _commitRoot(commitReport.blessedMerkleRoots[i], true);\\n    }\\n\\n    for (uint256 i = 0; i \\u003c commitReport.unblessedMerkleRoots.length; ++i) {\\n      _commitRoot(commitReport.unblessedMerkleRoots[i], false);\\n    }\\n\\n    emit CommitReportAccepted(\\n      commitReport.blessedMerkleRoots, commitReport.unblessedMerkleRoots, commitReport.priceUpdates\\n    );\\n\\n    _transmit(uint8(Internal.OCRPluginType.Commit), reportContext, report, rs, ss, rawVs);\\n  }\\n\\n  /// @notice Commits a single merkle root. The blessing status has to match the source chain config.\\n  /// @dev An unblessed root means that RMN verification is disabled for the source chain. It does not mean there is\\n  /// some future point where the root will be blessed.\\n  /// @param root The merkle root to commit.\\n  /// @param isBlessed The blessing status of the root.\\n  function _commitRoot(Internal.MerkleRoot memory root, bool isBlessed) internal {\\n    uint64 sourceChainSelector = root.sourceChainSelector;\\n\\n    if (i_rmnRemote.isCursed(bytes16(uint128(sourceChainSelector)))) {\\n      revert CursedByRMN(sourceChainSelector);\\n    }\\n\\n    SourceChainConfig storage sourceChainConfig = _getEnabledSourceChainConfig(sourceChainSelector);\\n\\n    // If the root is blessed but RMN blessing is disabled for the source chain, or if the root is not blessed but RMN\\n    // blessing is enabled, we revert.\\n    if (isBlessed == sourceChainConfig.isRMNVerificationDisabled) {\\n      revert RootBlessingMismatch(sourceChainSelector, root.merkleRoot, isBlessed);\\n    }\\n\\n    if (keccak256(root.onRampAddress) != keccak256(sourceChainConfig.onRamp)) {\\n      revert CommitOnRampMismatch(root.onRampAddress, sourceChainConfig.onRamp);\\n    }\\n\\n    if (sourceChainConfig.minSeqNr != root.minSeqNr || root.minSeqNr \\u003e root.maxSeqNr) {\\n      revert InvalidInterval(sourceChainSelector, root.minSeqNr, root.maxSeqNr);\\n    }\\n\\n    bytes32 merkleRoot = root.merkleRoot;\\n    if (merkleRoot == bytes32(0)) revert InvalidRoot();\\n    // If we reached this section, the report should contain a valid root.\\n    // We disallow duplicate roots as that would reset the timestamp and delay potential manual execution.\\n    if (s_roots[sourceChainSelector][merkleRoot] != 0) {\\n      revert RootAlreadyCommitted(sourceChainSelector, merkleRoot);\\n    }\\n\\n    sourceChainConfig.minSeqNr = root.maxSeqNr + 1;\\n    s_roots[sourceChainSelector][merkleRoot] = block.timestamp;\\n  }\\n\\n  /// @notice Returns the sequence number of the last price update.\\n  /// @return sequenceNumber The latest price update sequence number.\\n  function getLatestPriceSequenceNumber() external view returns (uint64) {\\n    return s_latestPriceSequenceNumber;\\n  }\\n\\n  /// @notice Returns the timestamp of a potentially previously committed merkle root.\\n  /// If the root was never committed 0 will be returned.\\n  /// @param sourceChainSelector The source chain selector.\\n  /// @param root The merkle root to check the commit status for.\\n  /// @return timestamp The timestamp of the committed root or zero in the case that it was never committed.\\n  function getMerkleRoot(uint64 sourceChainSelector, bytes32 root) external view returns (uint256) {\\n    return s_roots[sourceChainSelector][root];\\n  }\\n\\n  /// @notice Returns timestamp of when root was accepted or 0 if verification fails.\\n  /// @dev This method uses a merkle tree within a merkle tree, with the hashedLeaves,\\n  /// proofs and proofFlagBits being used to get the root of the inner tree.\\n  /// This root is then used as the singular leaf of the outer tree.\\n  /// @return timestamp The commit timestamp of the root.\\n  function _verify(\\n    uint64 sourceChainSelector,\\n    bytes32[] memory hashedLeaves,\\n    bytes32[] memory proofs,\\n    uint256 proofFlagBits\\n  ) internal view virtual returns (uint256 timestamp) {\\n    bytes32 root = MerkleMultiProof._merkleRoot(hashedLeaves, proofs, proofFlagBits);\\n    return s_roots[sourceChainSelector][root];\\n  }\\n\\n  /// @inheritdoc MultiOCR3Base\\n  function _afterOCR3ConfigSet(\\n    uint8 ocrPluginType\\n  ) internal override {\\n    bool isSignatureVerificationEnabled = s_ocrConfigs[ocrPluginType].configInfo.isSignatureVerificationEnabled;\\n\\n    if (ocrPluginType == uint8(Internal.OCRPluginType.Commit)) {\\n      // Signature verification must be enabled for commit plugin.\\n      if (!isSignatureVerificationEnabled) {\\n        revert SignatureVerificationRequiredInCommitPlugin();\\n      }\\n      // When the OCR config changes, we reset the sequence number  since it is scoped per config digest.\\n      // Note that s_minSeqNr/roots do not need to be reset as the roots persist across reconfigurations\\n      // and are de-duplicated separately.\\n      s_latestPriceSequenceNumber = 0;\\n    } else if (ocrPluginType == uint8(Internal.OCRPluginType.Execution)) {\\n      // Signature verification must be disabled for execution plugin.\\n      if (isSignatureVerificationEnabled) {\\n        revert SignatureVerificationNotAllowedInExecutionPlugin();\\n      }\\n    }\\n  }\\n\\n  // ================================================================\\n  // │                           Config                             │\\n  // ================================================================\\n\\n  /// @notice Returns the static config.\\n  /// @dev This function will always return the same struct as the contents is static and can never change.\\n  /// @return staticConfig The static config.\\n  function getStaticConfig() external view returns (StaticConfig memory) {\\n    return StaticConfig({\\n      chainSelector: i_chainSelector,\\n      gasForCallExactCheck: i_gasForCallExactCheck,\\n      rmnRemote: i_rmnRemote,\\n      tokenAdminRegistry: i_tokenAdminRegistry,\\n      nonceManager: i_nonceManager\\n    });\\n  }\\n\\n  /// @notice Returns the current dynamic config.\\n  /// @return dynamicConfig The current dynamic config.\\n  function getDynamicConfig() external view returns (DynamicConfig memory) {\\n    return s_dynamicConfig;\\n  }\\n\\n  /// @notice Returns the source chain config for the provided source chain selector.\\n  /// @param sourceChainSelector chain to retrieve configuration for.\\n  /// @return sourceChainConfig The config for the source chain.\\n  function getSourceChainConfig(\\n    uint64 sourceChainSelector\\n  ) external view returns (SourceChainConfig memory) {\\n    return s_sourceChainConfigs[sourceChainSelector];\\n  }\\n\\n  /// @notice Returns all source chain configs.\\n  /// @return sourceChainConfigs The source chain configs corresponding to all the supported chain selectors.\\n  function getAllSourceChainConfigs() external view returns (uint64[] memory, SourceChainConfig[] memory) {\\n    SourceChainConfig[] memory sourceChainConfigs = new SourceChainConfig[](s_sourceChainSelectors.length());\\n    uint64[] memory sourceChainSelectors = new uint64[](s_sourceChainSelectors.length());\\n    for (uint256 i = 0; i \\u003c s_sourceChainSelectors.length(); ++i) {\\n      sourceChainSelectors[i] = uint64(s_sourceChainSelectors.at(i));\\n      sourceChainConfigs[i] = s_sourceChainConfigs[sourceChainSelectors[i]];\\n    }\\n    return (sourceChainSelectors, sourceChainConfigs);\\n  }\\n\\n  /// @notice Updates source configs.\\n  /// @param sourceChainConfigUpdates Source chain configs.\\n  function applySourceChainConfigUpdates(\\n    SourceChainConfigArgs[] memory sourceChainConfigUpdates\\n  ) external onlyOwner {\\n    _applySourceChainConfigUpdates(sourceChainConfigUpdates);\\n  }\\n\\n  /// @notice Updates source configs.\\n  /// @param sourceChainConfigUpdates Source chain configs.\\n  function _applySourceChainConfigUpdates(\\n    SourceChainConfigArgs[] memory sourceChainConfigUpdates\\n  ) internal {\\n    for (uint256 i = 0; i \\u003c sourceChainConfigUpdates.length; ++i) {\\n      SourceChainConfigArgs memory sourceConfigUpdate = sourceChainConfigUpdates[i];\\n      uint64 sourceChainSelector = sourceConfigUpdate.sourceChainSelector;\\n\\n      if (sourceChainSelector == 0) {\\n        revert ZeroChainSelectorNotAllowed();\\n      }\\n\\n      if (address(sourceConfigUpdate.router) == address(0)) {\\n        revert ZeroAddressNotAllowed();\\n      }\\n\\n      SourceChainConfig storage currentConfig = s_sourceChainConfigs[sourceChainSelector];\\n      bytes memory newOnRamp = sourceConfigUpdate.onRamp;\\n\\n      if (currentConfig.onRamp.length == 0) {\\n        currentConfig.minSeqNr = 1;\\n        emit SourceChainSelectorAdded(sourceChainSelector);\\n      } else {\\n        if (currentConfig.minSeqNr != 1 \\u0026\\u0026 keccak256(currentConfig.onRamp) != keccak256(newOnRamp)) {\\n          // OnRamp updates should only happens due to a misconfiguration.\\n          // If an OnRamp is misconfigured, no reports should have been committed and no messages should have been\\n          // executed. This is enforced by the onRamp address check in the commit function.\\n          revert InvalidOnRampUpdate(sourceChainSelector);\\n        }\\n      }\\n\\n      // OnRamp can never be zero - if it is, then the source chain has been added for the first time.\\n      if (newOnRamp.length == 0 || keccak256(newOnRamp) == EMPTY_ENCODED_ADDRESS_HASH) {\\n        revert ZeroAddressNotAllowed();\\n      }\\n\\n      currentConfig.onRamp = newOnRamp;\\n      currentConfig.isEnabled = sourceConfigUpdate.isEnabled;\\n      currentConfig.router = sourceConfigUpdate.router;\\n      currentConfig.isRMNVerificationDisabled = sourceConfigUpdate.isRMNVerificationDisabled;\\n\\n      // We don't need to check the return value, as inserting the item twice has no effect.\\n      s_sourceChainSelectors.add(sourceChainSelector);\\n\\n      emit SourceChainConfigSet(sourceChainSelector, currentConfig);\\n    }\\n  }\\n\\n  /// @notice Sets the dynamic config.\\n  /// @param dynamicConfig The new dynamic config.\\n  function setDynamicConfig(\\n    DynamicConfig memory dynamicConfig\\n  ) external onlyOwner {\\n    _setDynamicConfig(dynamicConfig);\\n  }\\n\\n  /// @notice Sets the dynamic config.\\n  /// @param dynamicConfig The dynamic config.\\n  function _setDynamicConfig(\\n    DynamicConfig memory dynamicConfig\\n  ) internal {\\n    if (dynamicConfig.feeQuoter == address(0)) {\\n      revert ZeroAddressNotAllowed();\\n    }\\n\\n    s_dynamicConfig = dynamicConfig;\\n\\n    emit DynamicConfigSet(dynamicConfig);\\n  }\\n\\n  /// @notice Returns a source chain config with a check that the config is enabled.\\n  /// @param sourceChainSelector Source chain selector to check for cursing.\\n  /// @return sourceChainConfig The source chain config storage pointer.\\n  function _getEnabledSourceChainConfig(\\n    uint64 sourceChainSelector\\n  ) internal view returns (SourceChainConfig storage) {\\n    SourceChainConfig storage sourceChainConfig = s_sourceChainConfigs[sourceChainSelector];\\n    if (!sourceChainConfig.isEnabled) {\\n      revert SourceChainNotEnabled(sourceChainSelector);\\n    }\\n\\n    return sourceChainConfig;\\n  }\\n\\n  // ================================================================\\n  // │                            Access                            │\\n  // ================================================================\\n\\n  /// @notice Reverts as this contract should not be able to receive CCIP messages.\\n  function ccipReceive(\\n    Client.Any2EVMMessage calldata\\n  ) external pure {\\n    // solhint-disable-next-line\\n    revert();\\n  }\\n}\\n\"},\"contracts/offRamp/OffRampWithMessageTransformer.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IMessageTransformer} from \\\"../interfaces/IMessageTransformer.sol\\\";\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {OffRamp} from \\\"./OffRamp.sol\\\";\\n\\n/// @notice OffRamp that uses a message transformer to transform messages before execution\\ncontract OffRampWithMessageTransformer is OffRamp {\\n  address internal s_messageTransformer;\\n\\n  constructor(\\n    StaticConfig memory staticConfig,\\n    DynamicConfig memory dynamicConfig,\\n    SourceChainConfigArgs[] memory sourceChainConfigs,\\n    address messageTransformerAddr\\n  ) OffRamp(staticConfig, dynamicConfig, sourceChainConfigs) {\\n    if (messageTransformerAddr == address(0)) {\\n      revert ZeroAddressNotAllowed();\\n    }\\n    s_messageTransformer = messageTransformerAddr;\\n  }\\n\\n  /// @notice Get the address of the message transformer\\n  /// @return messageTransformerAddr The address of the message transformer\\n  function getMessageTransformer() external view returns (address) {\\n    return s_messageTransformer;\\n  }\\n\\n  /// @notice Set the address of the message transformer\\n  /// @param messageTransformerAddr The address of the message transformer\\n  function setMessageTransformer(\\n    address messageTransformerAddr\\n  ) external onlyOwner {\\n    if (messageTransformerAddr == address(0)) {\\n      revert ZeroAddressNotAllowed();\\n    }\\n    s_messageTransformer = messageTransformerAddr;\\n  }\\n\\n  /// @inheritdoc OffRamp\\n  function _beforeExecuteSingleMessage(\\n    Internal.Any2EVMRampMessage memory message\\n  ) internal override returns (Internal.Any2EVMRampMessage memory) {\\n    try IMessageTransformer(s_messageTransformer).transformInboundMessage(message) returns (\\n      Internal.Any2EVMRampMessage memory transformedMessage\\n    ) {\\n      return transformedMessage;\\n    } catch (bytes memory err) {\\n      revert IMessageTransformer.MessageTransformError(err);\\n    }\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/call/CallWithExactGas.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This library contains various callWithExactGas functions. All of them are\\n/// safe from gas bomb attacks.\\n/// @dev There is code duplication in this library. This is done to not leave the assembly\\n/// the blocks.\\nlibrary CallWithExactGas {\\n  error NoContract();\\n  error NoGasForCallExactCheck();\\n  error NotEnoughGasForCall();\\n\\n  bytes4 internal constant NO_CONTRACT_SIG = 0x0c3b563c;\\n  bytes4 internal constant NO_GAS_FOR_CALL_EXACT_CHECK_SIG = 0xafa32a2c;\\n  bytes4 internal constant NOT_ENOUGH_GAS_FOR_CALL_SIG = 0x37c3be29;\\n\\n  /// @notice calls target address with exactly gasAmount gas and payload as calldata.\\n  /// Accounts for gasForCallExactCheck gas that will be used by this function. Will revert\\n  /// if the target is not a contact. Will revert when there is not enough gas to call the\\n  /// target with gasAmount gas.\\n  /// @dev Ignores the return data, which makes it immune to gas bomb attacks.\\n  /// @return success whether the call succeeded\\n  function _callWithExactGas(\\n    bytes memory payload,\\n    address target,\\n    uint256 gasLimit,\\n    uint16 gasForCallExactCheck\\n  ) internal returns (bool success) {\\n    assembly {\\n      // solidity calls check that a contract actually exists at the destination, so we do the same\\n      // Note we do this check prior to measuring gas so gasForCallExactCheck (our \\\"cushion\\\")\\n      // doesn't need to account for it.\\n      if iszero(extcodesize(target)) {\\n        mstore(0x0, NO_CONTRACT_SIG)\\n        revert(0x0, 0x4)\\n      }\\n\\n      let g := gas()\\n      // Compute g -= gasForCallExactCheck and check for underflow\\n      // The gas actually passed to the callee is _min(gasAmount, 63//64*gas available).\\n      // We want to ensure that we revert if gasAmount \\u003e  63//64*gas available\\n      // as we do not want to provide them with less, however that check itself costs\\n      // gas. gasForCallExactCheck ensures we have at least enough gas to be able\\n      // to revert if gasAmount \\u003e  63//64*gas available.\\n      if lt(g, gasForCallExactCheck) {\\n        mstore(0x0, NO_GAS_FOR_CALL_EXACT_CHECK_SIG)\\n        revert(0x0, 0x4)\\n      }\\n      g := sub(g, gasForCallExactCheck)\\n      // if g - g//64 \\u003c= gasAmount, revert. We subtract g//64 because of EIP-150\\n      if iszero(gt(sub(g, div(g, 64)), gasLimit)) {\\n        mstore(0x0, NOT_ENOUGH_GAS_FOR_CALL_SIG)\\n        revert(0x0, 0x4)\\n      }\\n\\n      // call and return whether we succeeded. ignore return data\\n      // call(gas,addr,value,argsOffset,argsLength,retOffset,retLength)\\n      success := call(gasLimit, target, 0, add(payload, 0x20), mload(payload), 0x0, 0x0)\\n    }\\n    return success;\\n  }\\n\\n  /// @notice calls target address with exactly gasAmount gas and payload as calldata.\\n  /// Account for gasForCallExactCheck gas that will be used by this function. Will revert\\n  /// if the target is not a contact. Will revert when there is not enough gas to call the\\n  /// target with gasAmount gas.\\n  /// @dev Caps the return data length, which makes it immune to gas bomb attacks.\\n  /// @dev Return data cap logic borrowed from\\n  /// https://github.com/nomad-xyz/ExcessivelySafeCall/blob/main/src/ExcessivelySafeCall.sol.\\n  /// @return success whether the call succeeded\\n  /// @return retData the return data from the call, capped at maxReturnBytes bytes\\n  /// @return gasUsed the gas used by the external call. Does not include the overhead of this function.\\n  function _callWithExactGasSafeReturnData(\\n    bytes memory payload,\\n    address target,\\n    uint256 gasLimit,\\n    uint16 gasForCallExactCheck,\\n    uint16 maxReturnBytes\\n  ) internal returns (bool success, bytes memory retData, uint256 gasUsed) {\\n    // allocate retData memory ahead of time\\n    retData = new bytes(maxReturnBytes);\\n\\n    assembly {\\n      // solidity calls check that a contract actually exists at the destination, so we do the same\\n      // Note we do this check prior to measuring gas so gasForCallExactCheck (our \\\"cushion\\\")\\n      // doesn't need to account for it.\\n      if iszero(extcodesize(target)) {\\n        mstore(0x0, NO_CONTRACT_SIG)\\n        revert(0x0, 0x4)\\n      }\\n\\n      let g := gas()\\n      // Compute g -= gasForCallExactCheck and check for underflow\\n      // The gas actually passed to the callee is _min(gasAmount, 63//64*gas available).\\n      // We want to ensure that we revert if gasAmount \\u003e  63//64*gas available\\n      // as we do not want to provide them with less, however that check itself costs\\n      // gas. gasForCallExactCheck ensures we have at least enough gas to be able\\n      // to revert if gasAmount \\u003e  63//64*gas available.\\n      if lt(g, gasForCallExactCheck) {\\n        mstore(0x0, NO_GAS_FOR_CALL_EXACT_CHECK_SIG)\\n        revert(0x0, 0x4)\\n      }\\n      g := sub(g, gasForCallExactCheck)\\n      // if g - g//64 \\u003c= gasAmount, revert. We subtract g//64 because of EIP-150\\n      if iszero(gt(sub(g, div(g, 64)), gasLimit)) {\\n        mstore(0x0, NOT_ENOUGH_GAS_FOR_CALL_SIG)\\n        revert(0x0, 0x4)\\n      }\\n\\n      // We save the gas before the call so we can calculate how much gas the call used\\n      let gasBeforeCall := gas()\\n      // call and return whether we succeeded. ignore return data\\n      // call(gas,addr,value,argsOffset,argsLength,retOffset,retLength)\\n      success := call(gasLimit, target, 0, add(payload, 0x20), mload(payload), 0x0, 0x0)\\n      gasUsed := sub(gasBeforeCall, gas())\\n\\n      // limit our copy to maxReturnBytes bytes\\n      let toCopy := returndatasize()\\n      if gt(toCopy, maxReturnBytes) {\\n        toCopy := maxReturnBytes\\n      }\\n      // Store the length of the copied bytes\\n      mstore(retData, toCopy)\\n      // copy the bytes from retData[0:_toCopy]\\n      returndatacopy(add(retData, 0x20), 0x0, toCopy)\\n    }\\n    return (success, retData, gasUsed);\\n  }\\n\\n  /// @notice Calls target address with exactly gasAmount gas and payload as calldata\\n  /// or reverts if at least gasLimit gas is not available.\\n  /// @dev Does not check if target is a contract. If it is not a contract, the low-level\\n  /// call will still be made and it will succeed.\\n  /// @dev Ignores the return data, which makes it immune to gas bomb attacks.\\n  /// @return success whether the call succeeded\\n  /// @return sufficientGas Whether there was enough gas to make the call\\n  function _callWithExactGasEvenIfTargetIsNoContract(\\n    bytes memory payload,\\n    address target,\\n    uint256 gasLimit,\\n    uint16 gasForCallExactCheck\\n  ) internal returns (bool success, bool sufficientGas) {\\n    assembly {\\n      let g := gas()\\n      // Compute g -= CALL_WITH_EXACT_GAS_CUSHION and check for underflow. We\\n      // need the cushion since the logic following the above call to gas also\\n      // costs gas which we cannot account for exactly. So cushion is a\\n      // conservative upper bound for the cost of this logic.\\n      if iszero(lt(g, gasForCallExactCheck)) {\\n        g := sub(g, gasForCallExactCheck)\\n        // If g - g//64 \\u003c= gasAmount, we don't have enough gas. We subtract g//64 because of EIP-150.\\n        if gt(sub(g, div(g, 64)), gasLimit) {\\n          // Call and ignore success/return data. Note that we did not check\\n          // whether a contract actually exists at the target address.\\n          success := call(gasLimit, target, 0, add(payload, 0x20), mload(payload), 0x0, 0x0)\\n          sufficientGas := true\\n        }\\n      }\\n    }\\n    return (success, sufficientGas);\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/token/ERC20/IERC20.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20 {\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n    /**\\n     * @dev Returns the value of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the value of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address to, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\\n     * caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 value) external returns (bool);\\n\\n    /**\\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the\\n     * allowance mechanism. `value` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 value) external returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```solidity\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n    // To implement this library for multiple types with as little code\\n    // repetition as possible, we write it in terms of a generic Set type with\\n    // bytes32 values.\\n    // The Set implementation uses private functions, and user-facing\\n    // implementations (such as AddressSet) are just wrappers around the\\n    // underlying Set.\\n    // This means that we can only create new EnumerableSets for types that fit\\n    // in bytes32.\\n\\n    struct Set {\\n        // Storage of set values\\n        bytes32[] _values;\\n        // Position is the index of the value in the `values` array plus 1.\\n        // Position 0 is used to mean a value is not in the set.\\n        mapping(bytes32 value =\\u003e uint256) _positions;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function _add(Set storage set, bytes32 value) private returns (bool) {\\n        if (!_contains(set, value)) {\\n            set._values.push(value);\\n            // The value is stored at length-1, but we add 1 to all indexes\\n            // and use 0 as a sentinel value\\n            set._positions[value] = set._values.length;\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function _remove(Set storage set, bytes32 value) private returns (bool) {\\n        // We cache the value's position to prevent multiple reads from the same storage slot\\n        uint256 position = set._positions[value];\\n\\n        if (position != 0) {\\n            // Equivalent to contains(set, value)\\n            // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n            // the array, and then remove the last element (sometimes called as 'swap and pop').\\n            // This modifies the order of the array, as noted in {at}.\\n\\n            uint256 valueIndex = position - 1;\\n            uint256 lastIndex = set._values.length - 1;\\n\\n            if (valueIndex != lastIndex) {\\n                bytes32 lastValue = set._values[lastIndex];\\n\\n                // Move the lastValue to the index where the value to delete is\\n                set._values[valueIndex] = lastValue;\\n                // Update the tracked position of the lastValue (that was just moved)\\n                set._positions[lastValue] = position;\\n            }\\n\\n            // Delete the slot where the moved value was stored\\n            set._values.pop();\\n\\n            // Delete the tracked position for the deleted slot\\n            delete set._positions[value];\\n\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n        return set._positions[value] != 0;\\n    }\\n\\n    /**\\n     * @dev Returns the number of values on the set. O(1).\\n     */\\n    function _length(Set storage set) private view returns (uint256) {\\n        return set._values.length;\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n        return set._values[index];\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function _values(Set storage set) private view returns (bytes32[] memory) {\\n        return set._values;\\n    }\\n\\n    // Bytes32Set\\n\\n    struct Bytes32Set {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _add(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _remove(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n        return _contains(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(Bytes32Set storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n        return _at(set._inner, index);\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        bytes32[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // AddressSet\\n\\n    struct AddressSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(AddressSet storage set, address value) internal returns (bool) {\\n        return _add(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(AddressSet storage set, address value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(AddressSet storage set, address value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(AddressSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n        return address(uint160(uint256(_at(set._inner, index))));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(AddressSet storage set) internal view returns (address[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        address[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // UintSet\\n\\n    struct UintSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _add(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(UintSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n        return uint256(_at(set._inner, index));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(UintSet storage set) internal view returns (uint256[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        uint256[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n}\\n\"}}}"
