// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package commit_store

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
	"github.com/smartcontractkit/chainlink-ccip/chains/evm/gobindings/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

type CommitStoreCommitReport struct {
	PriceUpdates InternalPriceUpdates
	Interval     CommitStoreInterval
	MerkleRoot   [32]byte
}

type CommitStoreDynamicConfig struct {
	PriceRegistry common.Address
}

type CommitStoreInterval struct {
	Min uint64
	Max uint64
}

type CommitStoreStaticConfig struct {
	ChainSelector       uint64
	SourceChainSelector uint64
	OnRamp              common.Address
	RmnProxy            common.Address
}

type InternalGasPriceUpdate struct {
	DestChainSelector uint64
	UsdPerUnitGas     *big.Int
}

type InternalPriceUpdates struct {
	TokenPriceUpdates []InternalTokenPriceUpdate
	GasPriceUpdates   []InternalGasPriceUpdate
}

type InternalTokenPriceUpdate struct {
	SourceToken common.Address
	UsdPerToken *big.Int
}

var CommitStoreMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"sourceChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"onRamp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"rmnProxy\",\"type\":\"address\"}],\"internalType\":\"structCommitStore.StaticConfig\",\"name\":\"staticConfig\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"expected\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"actual\",\"type\":\"bytes32\"}],\"name\":\"ConfigDigestMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CursedByRMN\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"expected\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"actual\",\"type\":\"uint256\"}],\"name\":\"ForkedChain\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidCommitStoreConfig\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"enumOCR2Base.InvalidConfigErrorType\",\"name\":\"errorType\",\"type\":\"uint8\"}],\"name\":\"InvalidConfig\",\"type\":\"error\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"min\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"max\",\"type\":\"uint64\"}],\"internalType\":\"structCommitStore.Interval\",\"name\":\"interval\",\"type\":\"tuple\"}],\"name\":\"InvalidInterval\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidProof\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidRoot\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LeavesCannotBeEmpty\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NonUniqueSignatures\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OracleCannotBeZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PausedError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RootAlreadyCommitted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SignaturesOutOfRegistration\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StaleReport\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedSigner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedTransmitter\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"expected\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"actual\",\"type\":\"uint256\"}],\"name\":\"WrongMessageLength\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"WrongNumberOfSignatures\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"sourceChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"onRamp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"rmnProxy\",\"type\":\"address\"}],\"indexed\":false,\"internalType\":\"structCommitStore.StaticConfig\",\"name\":\"staticConfig\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"priceRegistry\",\"type\":\"address\"}],\"indexed\":false,\"internalType\":\"structCommitStore.DynamicConfig\",\"name\":\"dynamicConfig\",\"type\":\"tuple\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"previousConfigBlockNumber\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"configCount\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"signers\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"transmitters\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"f\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"onchainConfig\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"oldEpochAndRound\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"newEpochAndRound\",\"type\":\"uint40\"}],\"name\":\"LatestPriceEpochAndRoundSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"sourceToken\",\"type\":\"address\"},{\"internalType\":\"uint224\",\"name\":\"usdPerToken\",\"type\":\"uint224\"}],\"internalType\":\"structInternal.TokenPriceUpdate[]\",\"name\":\"tokenPriceUpdates\",\"type\":\"tuple[]\"},{\"components\":[{\"internalType\":\"uint64\",\"name\":\"destChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint224\",\"name\":\"usdPerUnitGas\",\"type\":\"uint224\"}],\"internalType\":\"structInternal.GasPriceUpdate[]\",\"name\":\"gasPriceUpdates\",\"type\":\"tuple[]\"}],\"internalType\":\"structInternal.PriceUpdates\",\"name\":\"priceUpdates\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint64\",\"name\":\"min\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"max\",\"type\":\"uint64\"}],\"internalType\":\"structCommitStore.Interval\",\"name\":\"interval\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"merkleRoot\",\"type\":\"bytes32\"}],\"indexed\":false,\"internalType\":\"structCommitStore.CommitReport\",\"name\":\"report\",\"type\":\"tuple\"}],\"name\":\"ReportAccepted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"}],\"name\":\"RootRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"oldSeqNum\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"newSeqNum\",\"type\":\"uint64\"}],\"name\":\"SequenceNumberSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"name\":\"Transmitted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDynamicConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"priceRegistry\",\"type\":\"address\"}],\"internalType\":\"structCommitStore.DynamicConfig\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getExpectedNextSequenceNumber\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getLatestPriceEpochAndRound\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"}],\"name\":\"getMerkleRoot\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getStaticConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"sourceChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"onRamp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"rmnProxy\",\"type\":\"address\"}],\"internalType\":\"structCommitStore.StaticConfig\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTransmitters\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"}],\"name\":\"isBlessed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isUnpausedAndNotCursed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestConfigDetails\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"configCount\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"blockNumber\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestConfigDigestAndEpoch\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"scanLogs\",\"type\":\"bool\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"rootToReset\",\"type\":\"bytes32[]\"}],\"name\":\"resetUnblessedRoots\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint40\",\"name\":\"latestPriceEpochAndRound\",\"type\":\"uint40\"}],\"name\":\"setLatestPriceEpochAndRound\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint64\",\"name\":\"minSeqNr\",\"type\":\"uint64\"}],\"name\":\"setMinSeqNr\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"signers\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"transmitters\",\"type\":\"address[]\"},{\"internalType\":\"uint8\",\"name\":\"f\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"onchainConfig\",\"type\":\"bytes\"},{\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"name\":\"setOCR2Config\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[3]\",\"name\":\"reportContext\",\"type\":\"bytes32[3]\"},{\"internalType\":\"bytes\",\"name\":\"report\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"rs\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"ss\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32\",\"name\":\"rawVs\",\"type\":\"bytes32\"}],\"name\":\"transmit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"typeAndVersion\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"hashedLeaves\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"proofs\",\"type\":\"bytes32[]\"},{\"internalType\":\"uint256\",\"name\":\"proofFlagBits\",\"type\":\"uint256\"}],\"name\":\"verify\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}]",
	Bin: "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",
}

var CommitStoreABI = CommitStoreMetaData.ABI

var CommitStoreBin = CommitStoreMetaData.Bin

func DeployCommitStore(auth *bind.TransactOpts, backend bind.ContractBackend, staticConfig CommitStoreStaticConfig) (common.Address, *types.Transaction, *CommitStore, error) {
	parsed, err := CommitStoreMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(CommitStoreBin), backend, staticConfig)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &CommitStore{address: address, abi: *parsed, CommitStoreCaller: CommitStoreCaller{contract: contract}, CommitStoreTransactor: CommitStoreTransactor{contract: contract}, CommitStoreFilterer: CommitStoreFilterer{contract: contract}}, nil
}

type CommitStore struct {
	address common.Address
	abi     abi.ABI
	CommitStoreCaller
	CommitStoreTransactor
	CommitStoreFilterer
}

type CommitStoreCaller struct {
	contract *bind.BoundContract
}

type CommitStoreTransactor struct {
	contract *bind.BoundContract
}

type CommitStoreFilterer struct {
	contract *bind.BoundContract
}

type CommitStoreSession struct {
	Contract     *CommitStore
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type CommitStoreCallerSession struct {
	Contract *CommitStoreCaller
	CallOpts bind.CallOpts
}

type CommitStoreTransactorSession struct {
	Contract     *CommitStoreTransactor
	TransactOpts bind.TransactOpts
}

type CommitStoreRaw struct {
	Contract *CommitStore
}

type CommitStoreCallerRaw struct {
	Contract *CommitStoreCaller
}

type CommitStoreTransactorRaw struct {
	Contract *CommitStoreTransactor
}

func NewCommitStore(address common.Address, backend bind.ContractBackend) (*CommitStore, error) {
	abi, err := abi.JSON(strings.NewReader(CommitStoreABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindCommitStore(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &CommitStore{address: address, abi: abi, CommitStoreCaller: CommitStoreCaller{contract: contract}, CommitStoreTransactor: CommitStoreTransactor{contract: contract}, CommitStoreFilterer: CommitStoreFilterer{contract: contract}}, nil
}

func NewCommitStoreCaller(address common.Address, caller bind.ContractCaller) (*CommitStoreCaller, error) {
	contract, err := bindCommitStore(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &CommitStoreCaller{contract: contract}, nil
}

func NewCommitStoreTransactor(address common.Address, transactor bind.ContractTransactor) (*CommitStoreTransactor, error) {
	contract, err := bindCommitStore(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &CommitStoreTransactor{contract: contract}, nil
}

func NewCommitStoreFilterer(address common.Address, filterer bind.ContractFilterer) (*CommitStoreFilterer, error) {
	contract, err := bindCommitStore(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &CommitStoreFilterer{contract: contract}, nil
}

func bindCommitStore(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := CommitStoreMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_CommitStore *CommitStoreRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CommitStore.Contract.CommitStoreCaller.contract.Call(opts, result, method, params...)
}

func (_CommitStore *CommitStoreRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStore.Contract.CommitStoreTransactor.contract.Transfer(opts)
}

func (_CommitStore *CommitStoreRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CommitStore.Contract.CommitStoreTransactor.contract.Transact(opts, method, params...)
}

func (_CommitStore *CommitStoreCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CommitStore.Contract.contract.Call(opts, result, method, params...)
}

func (_CommitStore *CommitStoreTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStore.Contract.contract.Transfer(opts)
}

func (_CommitStore *CommitStoreTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CommitStore.Contract.contract.Transact(opts, method, params...)
}

func (_CommitStore *CommitStoreCaller) GetDynamicConfig(opts *bind.CallOpts) (CommitStoreDynamicConfig, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "getDynamicConfig")

	if err != nil {
		return *new(CommitStoreDynamicConfig), err
	}

	out0 := *abi.ConvertType(out[0], new(CommitStoreDynamicConfig)).(*CommitStoreDynamicConfig)

	return out0, err

}

func (_CommitStore *CommitStoreSession) GetDynamicConfig() (CommitStoreDynamicConfig, error) {
	return _CommitStore.Contract.GetDynamicConfig(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) GetDynamicConfig() (CommitStoreDynamicConfig, error) {
	return _CommitStore.Contract.GetDynamicConfig(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) GetExpectedNextSequenceNumber(opts *bind.CallOpts) (uint64, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "getExpectedNextSequenceNumber")

	if err != nil {
		return *new(uint64), err
	}

	out0 := *abi.ConvertType(out[0], new(uint64)).(*uint64)

	return out0, err

}

func (_CommitStore *CommitStoreSession) GetExpectedNextSequenceNumber() (uint64, error) {
	return _CommitStore.Contract.GetExpectedNextSequenceNumber(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) GetExpectedNextSequenceNumber() (uint64, error) {
	return _CommitStore.Contract.GetExpectedNextSequenceNumber(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) GetLatestPriceEpochAndRound(opts *bind.CallOpts) (uint64, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "getLatestPriceEpochAndRound")

	if err != nil {
		return *new(uint64), err
	}

	out0 := *abi.ConvertType(out[0], new(uint64)).(*uint64)

	return out0, err

}

func (_CommitStore *CommitStoreSession) GetLatestPriceEpochAndRound() (uint64, error) {
	return _CommitStore.Contract.GetLatestPriceEpochAndRound(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) GetLatestPriceEpochAndRound() (uint64, error) {
	return _CommitStore.Contract.GetLatestPriceEpochAndRound(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) GetMerkleRoot(opts *bind.CallOpts, root [32]byte) (*big.Int, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "getMerkleRoot", root)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_CommitStore *CommitStoreSession) GetMerkleRoot(root [32]byte) (*big.Int, error) {
	return _CommitStore.Contract.GetMerkleRoot(&_CommitStore.CallOpts, root)
}

func (_CommitStore *CommitStoreCallerSession) GetMerkleRoot(root [32]byte) (*big.Int, error) {
	return _CommitStore.Contract.GetMerkleRoot(&_CommitStore.CallOpts, root)
}

func (_CommitStore *CommitStoreCaller) GetStaticConfig(opts *bind.CallOpts) (CommitStoreStaticConfig, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "getStaticConfig")

	if err != nil {
		return *new(CommitStoreStaticConfig), err
	}

	out0 := *abi.ConvertType(out[0], new(CommitStoreStaticConfig)).(*CommitStoreStaticConfig)

	return out0, err

}

func (_CommitStore *CommitStoreSession) GetStaticConfig() (CommitStoreStaticConfig, error) {
	return _CommitStore.Contract.GetStaticConfig(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) GetStaticConfig() (CommitStoreStaticConfig, error) {
	return _CommitStore.Contract.GetStaticConfig(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) GetTransmitters(opts *bind.CallOpts) ([]common.Address, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "getTransmitters")

	if err != nil {
		return *new([]common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new([]common.Address)).(*[]common.Address)

	return out0, err

}

func (_CommitStore *CommitStoreSession) GetTransmitters() ([]common.Address, error) {
	return _CommitStore.Contract.GetTransmitters(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) GetTransmitters() ([]common.Address, error) {
	return _CommitStore.Contract.GetTransmitters(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) IsBlessed(opts *bind.CallOpts, root [32]byte) (bool, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "isBlessed", root)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStore *CommitStoreSession) IsBlessed(root [32]byte) (bool, error) {
	return _CommitStore.Contract.IsBlessed(&_CommitStore.CallOpts, root)
}

func (_CommitStore *CommitStoreCallerSession) IsBlessed(root [32]byte) (bool, error) {
	return _CommitStore.Contract.IsBlessed(&_CommitStore.CallOpts, root)
}

func (_CommitStore *CommitStoreCaller) IsUnpausedAndNotCursed(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "isUnpausedAndNotCursed")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStore *CommitStoreSession) IsUnpausedAndNotCursed() (bool, error) {
	return _CommitStore.Contract.IsUnpausedAndNotCursed(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) IsUnpausedAndNotCursed() (bool, error) {
	return _CommitStore.Contract.IsUnpausedAndNotCursed(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) LatestConfigDetails(opts *bind.CallOpts) (LatestConfigDetails,

	error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "latestConfigDetails")

	outstruct := new(LatestConfigDetails)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ConfigCount = *abi.ConvertType(out[0], new(uint32)).(*uint32)
	outstruct.BlockNumber = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.ConfigDigest = *abi.ConvertType(out[2], new([32]byte)).(*[32]byte)

	return *outstruct, err

}

func (_CommitStore *CommitStoreSession) LatestConfigDetails() (LatestConfigDetails,

	error) {
	return _CommitStore.Contract.LatestConfigDetails(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) LatestConfigDetails() (LatestConfigDetails,

	error) {
	return _CommitStore.Contract.LatestConfigDetails(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) LatestConfigDigestAndEpoch(opts *bind.CallOpts) (LatestConfigDigestAndEpoch,

	error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "latestConfigDigestAndEpoch")

	outstruct := new(LatestConfigDigestAndEpoch)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ScanLogs = *abi.ConvertType(out[0], new(bool)).(*bool)
	outstruct.ConfigDigest = *abi.ConvertType(out[1], new([32]byte)).(*[32]byte)
	outstruct.Epoch = *abi.ConvertType(out[2], new(uint32)).(*uint32)

	return *outstruct, err

}

func (_CommitStore *CommitStoreSession) LatestConfigDigestAndEpoch() (LatestConfigDigestAndEpoch,

	error) {
	return _CommitStore.Contract.LatestConfigDigestAndEpoch(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) LatestConfigDigestAndEpoch() (LatestConfigDigestAndEpoch,

	error) {
	return _CommitStore.Contract.LatestConfigDigestAndEpoch(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_CommitStore *CommitStoreSession) Owner() (common.Address, error) {
	return _CommitStore.Contract.Owner(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) Owner() (common.Address, error) {
	return _CommitStore.Contract.Owner(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) Paused(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "paused")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStore *CommitStoreSession) Paused() (bool, error) {
	return _CommitStore.Contract.Paused(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) Paused() (bool, error) {
	return _CommitStore.Contract.Paused(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_CommitStore *CommitStoreSession) TypeAndVersion() (string, error) {
	return _CommitStore.Contract.TypeAndVersion(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCallerSession) TypeAndVersion() (string, error) {
	return _CommitStore.Contract.TypeAndVersion(&_CommitStore.CallOpts)
}

func (_CommitStore *CommitStoreCaller) Verify(opts *bind.CallOpts, hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _CommitStore.contract.Call(opts, &out, "verify", hashedLeaves, proofs, proofFlagBits)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_CommitStore *CommitStoreSession) Verify(hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	return _CommitStore.Contract.Verify(&_CommitStore.CallOpts, hashedLeaves, proofs, proofFlagBits)
}

func (_CommitStore *CommitStoreCallerSession) Verify(hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	return _CommitStore.Contract.Verify(&_CommitStore.CallOpts, hashedLeaves, proofs, proofFlagBits)
}

func (_CommitStore *CommitStoreTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStore.contract.Transact(opts, "acceptOwnership")
}

func (_CommitStore *CommitStoreSession) AcceptOwnership() (*types.Transaction, error) {
	return _CommitStore.Contract.AcceptOwnership(&_CommitStore.TransactOpts)
}

func (_CommitStore *CommitStoreTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _CommitStore.Contract.AcceptOwnership(&_CommitStore.TransactOpts)
}

func (_CommitStore *CommitStoreTransactor) Pause(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStore.contract.Transact(opts, "pause")
}

func (_CommitStore *CommitStoreSession) Pause() (*types.Transaction, error) {
	return _CommitStore.Contract.Pause(&_CommitStore.TransactOpts)
}

func (_CommitStore *CommitStoreTransactorSession) Pause() (*types.Transaction, error) {
	return _CommitStore.Contract.Pause(&_CommitStore.TransactOpts)
}

func (_CommitStore *CommitStoreTransactor) ResetUnblessedRoots(opts *bind.TransactOpts, rootToReset [][32]byte) (*types.Transaction, error) {
	return _CommitStore.contract.Transact(opts, "resetUnblessedRoots", rootToReset)
}

func (_CommitStore *CommitStoreSession) ResetUnblessedRoots(rootToReset [][32]byte) (*types.Transaction, error) {
	return _CommitStore.Contract.ResetUnblessedRoots(&_CommitStore.TransactOpts, rootToReset)
}

func (_CommitStore *CommitStoreTransactorSession) ResetUnblessedRoots(rootToReset [][32]byte) (*types.Transaction, error) {
	return _CommitStore.Contract.ResetUnblessedRoots(&_CommitStore.TransactOpts, rootToReset)
}

func (_CommitStore *CommitStoreTransactor) SetLatestPriceEpochAndRound(opts *bind.TransactOpts, latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStore.contract.Transact(opts, "setLatestPriceEpochAndRound", latestPriceEpochAndRound)
}

func (_CommitStore *CommitStoreSession) SetLatestPriceEpochAndRound(latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStore.Contract.SetLatestPriceEpochAndRound(&_CommitStore.TransactOpts, latestPriceEpochAndRound)
}

func (_CommitStore *CommitStoreTransactorSession) SetLatestPriceEpochAndRound(latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStore.Contract.SetLatestPriceEpochAndRound(&_CommitStore.TransactOpts, latestPriceEpochAndRound)
}

func (_CommitStore *CommitStoreTransactor) SetMinSeqNr(opts *bind.TransactOpts, minSeqNr uint64) (*types.Transaction, error) {
	return _CommitStore.contract.Transact(opts, "setMinSeqNr", minSeqNr)
}

func (_CommitStore *CommitStoreSession) SetMinSeqNr(minSeqNr uint64) (*types.Transaction, error) {
	return _CommitStore.Contract.SetMinSeqNr(&_CommitStore.TransactOpts, minSeqNr)
}

func (_CommitStore *CommitStoreTransactorSession) SetMinSeqNr(minSeqNr uint64) (*types.Transaction, error) {
	return _CommitStore.Contract.SetMinSeqNr(&_CommitStore.TransactOpts, minSeqNr)
}

func (_CommitStore *CommitStoreTransactor) SetOCR2Config(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	return _CommitStore.contract.Transact(opts, "setOCR2Config", signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
}

func (_CommitStore *CommitStoreSession) SetOCR2Config(signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	return _CommitStore.Contract.SetOCR2Config(&_CommitStore.TransactOpts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
}

func (_CommitStore *CommitStoreTransactorSession) SetOCR2Config(signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	return _CommitStore.Contract.SetOCR2Config(&_CommitStore.TransactOpts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
}

func (_CommitStore *CommitStoreTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _CommitStore.contract.Transact(opts, "transferOwnership", to)
}

func (_CommitStore *CommitStoreSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _CommitStore.Contract.TransferOwnership(&_CommitStore.TransactOpts, to)
}

func (_CommitStore *CommitStoreTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _CommitStore.Contract.TransferOwnership(&_CommitStore.TransactOpts, to)
}

func (_CommitStore *CommitStoreTransactor) Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _CommitStore.contract.Transact(opts, "transmit", reportContext, report, rs, ss, rawVs)
}

func (_CommitStore *CommitStoreSession) Transmit(reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _CommitStore.Contract.Transmit(&_CommitStore.TransactOpts, reportContext, report, rs, ss, rawVs)
}

func (_CommitStore *CommitStoreTransactorSession) Transmit(reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _CommitStore.Contract.Transmit(&_CommitStore.TransactOpts, reportContext, report, rs, ss, rawVs)
}

func (_CommitStore *CommitStoreTransactor) Unpause(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStore.contract.Transact(opts, "unpause")
}

func (_CommitStore *CommitStoreSession) Unpause() (*types.Transaction, error) {
	return _CommitStore.Contract.Unpause(&_CommitStore.TransactOpts)
}

func (_CommitStore *CommitStoreTransactorSession) Unpause() (*types.Transaction, error) {
	return _CommitStore.Contract.Unpause(&_CommitStore.TransactOpts)
}

type CommitStoreConfigSetIterator struct {
	Event *CommitStoreConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreConfigSetIterator) Error() error {
	return it.fail
}

func (it *CommitStoreConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreConfigSet struct {
	StaticConfig  CommitStoreStaticConfig
	DynamicConfig CommitStoreDynamicConfig
	Raw           types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterConfigSet(opts *bind.FilterOpts) (*CommitStoreConfigSetIterator, error) {

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return &CommitStoreConfigSetIterator{contract: _CommitStore.contract, event: "ConfigSet", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *CommitStoreConfigSet) (event.Subscription, error) {

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreConfigSet)
				if err := _CommitStore.contract.UnpackLog(event, "ConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseConfigSet(log types.Log) (*CommitStoreConfigSet, error) {
	event := new(CommitStoreConfigSet)
	if err := _CommitStore.contract.UnpackLog(event, "ConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreConfigSet0Iterator struct {
	Event *CommitStoreConfigSet0

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreConfigSet0Iterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreConfigSet0)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreConfigSet0)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreConfigSet0Iterator) Error() error {
	return it.fail
}

func (it *CommitStoreConfigSet0Iterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreConfigSet0 struct {
	PreviousConfigBlockNumber uint32
	ConfigDigest              [32]byte
	ConfigCount               uint64
	Signers                   []common.Address
	Transmitters              []common.Address
	F                         uint8
	OnchainConfig             []byte
	OffchainConfigVersion     uint64
	OffchainConfig            []byte
	Raw                       types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterConfigSet0(opts *bind.FilterOpts) (*CommitStoreConfigSet0Iterator, error) {

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "ConfigSet0")
	if err != nil {
		return nil, err
	}
	return &CommitStoreConfigSet0Iterator{contract: _CommitStore.contract, event: "ConfigSet0", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchConfigSet0(opts *bind.WatchOpts, sink chan<- *CommitStoreConfigSet0) (event.Subscription, error) {

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "ConfigSet0")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreConfigSet0)
				if err := _CommitStore.contract.UnpackLog(event, "ConfigSet0", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseConfigSet0(log types.Log) (*CommitStoreConfigSet0, error) {
	event := new(CommitStoreConfigSet0)
	if err := _CommitStore.contract.UnpackLog(event, "ConfigSet0", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreLatestPriceEpochAndRoundSetIterator struct {
	Event *CommitStoreLatestPriceEpochAndRoundSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreLatestPriceEpochAndRoundSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreLatestPriceEpochAndRoundSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreLatestPriceEpochAndRoundSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreLatestPriceEpochAndRoundSetIterator) Error() error {
	return it.fail
}

func (it *CommitStoreLatestPriceEpochAndRoundSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreLatestPriceEpochAndRoundSet struct {
	OldEpochAndRound *big.Int
	NewEpochAndRound *big.Int
	Raw              types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterLatestPriceEpochAndRoundSet(opts *bind.FilterOpts) (*CommitStoreLatestPriceEpochAndRoundSetIterator, error) {

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "LatestPriceEpochAndRoundSet")
	if err != nil {
		return nil, err
	}
	return &CommitStoreLatestPriceEpochAndRoundSetIterator{contract: _CommitStore.contract, event: "LatestPriceEpochAndRoundSet", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchLatestPriceEpochAndRoundSet(opts *bind.WatchOpts, sink chan<- *CommitStoreLatestPriceEpochAndRoundSet) (event.Subscription, error) {

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "LatestPriceEpochAndRoundSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreLatestPriceEpochAndRoundSet)
				if err := _CommitStore.contract.UnpackLog(event, "LatestPriceEpochAndRoundSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseLatestPriceEpochAndRoundSet(log types.Log) (*CommitStoreLatestPriceEpochAndRoundSet, error) {
	event := new(CommitStoreLatestPriceEpochAndRoundSet)
	if err := _CommitStore.contract.UnpackLog(event, "LatestPriceEpochAndRoundSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreOwnershipTransferRequestedIterator struct {
	Event *CommitStoreOwnershipTransferRequested

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreOwnershipTransferRequestedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &CommitStoreOwnershipTransferRequestedIterator{contract: _CommitStore.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *CommitStoreOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreOwnershipTransferRequested)
				if err := _CommitStore.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseOwnershipTransferRequested(log types.Log) (*CommitStoreOwnershipTransferRequested, error) {
	event := new(CommitStoreOwnershipTransferRequested)
	if err := _CommitStore.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreOwnershipTransferredIterator struct {
	Event *CommitStoreOwnershipTransferred

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreOwnershipTransferredIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreOwnershipTransferredIterator) Error() error {
	return it.fail
}

func (it *CommitStoreOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &CommitStoreOwnershipTransferredIterator{contract: _CommitStore.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CommitStoreOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreOwnershipTransferred)
				if err := _CommitStore.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseOwnershipTransferred(log types.Log) (*CommitStoreOwnershipTransferred, error) {
	event := new(CommitStoreOwnershipTransferred)
	if err := _CommitStore.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStorePausedIterator struct {
	Event *CommitStorePaused

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStorePausedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStorePaused)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStorePaused)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStorePausedIterator) Error() error {
	return it.fail
}

func (it *CommitStorePausedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStorePaused struct {
	Account common.Address
	Raw     types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterPaused(opts *bind.FilterOpts) (*CommitStorePausedIterator, error) {

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "Paused")
	if err != nil {
		return nil, err
	}
	return &CommitStorePausedIterator{contract: _CommitStore.contract, event: "Paused", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchPaused(opts *bind.WatchOpts, sink chan<- *CommitStorePaused) (event.Subscription, error) {

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "Paused")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStorePaused)
				if err := _CommitStore.contract.UnpackLog(event, "Paused", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParsePaused(log types.Log) (*CommitStorePaused, error) {
	event := new(CommitStorePaused)
	if err := _CommitStore.contract.UnpackLog(event, "Paused", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreReportAcceptedIterator struct {
	Event *CommitStoreReportAccepted

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreReportAcceptedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreReportAccepted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreReportAccepted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreReportAcceptedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreReportAcceptedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreReportAccepted struct {
	Report CommitStoreCommitReport
	Raw    types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterReportAccepted(opts *bind.FilterOpts) (*CommitStoreReportAcceptedIterator, error) {

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "ReportAccepted")
	if err != nil {
		return nil, err
	}
	return &CommitStoreReportAcceptedIterator{contract: _CommitStore.contract, event: "ReportAccepted", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchReportAccepted(opts *bind.WatchOpts, sink chan<- *CommitStoreReportAccepted) (event.Subscription, error) {

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "ReportAccepted")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreReportAccepted)
				if err := _CommitStore.contract.UnpackLog(event, "ReportAccepted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseReportAccepted(log types.Log) (*CommitStoreReportAccepted, error) {
	event := new(CommitStoreReportAccepted)
	if err := _CommitStore.contract.UnpackLog(event, "ReportAccepted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreRootRemovedIterator struct {
	Event *CommitStoreRootRemoved

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreRootRemovedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreRootRemoved)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreRootRemoved)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreRootRemovedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreRootRemovedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreRootRemoved struct {
	Root [32]byte
	Raw  types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterRootRemoved(opts *bind.FilterOpts) (*CommitStoreRootRemovedIterator, error) {

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "RootRemoved")
	if err != nil {
		return nil, err
	}
	return &CommitStoreRootRemovedIterator{contract: _CommitStore.contract, event: "RootRemoved", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchRootRemoved(opts *bind.WatchOpts, sink chan<- *CommitStoreRootRemoved) (event.Subscription, error) {

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "RootRemoved")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreRootRemoved)
				if err := _CommitStore.contract.UnpackLog(event, "RootRemoved", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseRootRemoved(log types.Log) (*CommitStoreRootRemoved, error) {
	event := new(CommitStoreRootRemoved)
	if err := _CommitStore.contract.UnpackLog(event, "RootRemoved", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreSequenceNumberSetIterator struct {
	Event *CommitStoreSequenceNumberSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreSequenceNumberSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreSequenceNumberSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreSequenceNumberSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreSequenceNumberSetIterator) Error() error {
	return it.fail
}

func (it *CommitStoreSequenceNumberSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreSequenceNumberSet struct {
	OldSeqNum uint64
	NewSeqNum uint64
	Raw       types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterSequenceNumberSet(opts *bind.FilterOpts) (*CommitStoreSequenceNumberSetIterator, error) {

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "SequenceNumberSet")
	if err != nil {
		return nil, err
	}
	return &CommitStoreSequenceNumberSetIterator{contract: _CommitStore.contract, event: "SequenceNumberSet", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchSequenceNumberSet(opts *bind.WatchOpts, sink chan<- *CommitStoreSequenceNumberSet) (event.Subscription, error) {

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "SequenceNumberSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreSequenceNumberSet)
				if err := _CommitStore.contract.UnpackLog(event, "SequenceNumberSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseSequenceNumberSet(log types.Log) (*CommitStoreSequenceNumberSet, error) {
	event := new(CommitStoreSequenceNumberSet)
	if err := _CommitStore.contract.UnpackLog(event, "SequenceNumberSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreTransmittedIterator struct {
	Event *CommitStoreTransmitted

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreTransmittedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreTransmitted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreTransmitted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreTransmittedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreTransmittedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreTransmitted struct {
	ConfigDigest [32]byte
	Epoch        uint32
	Raw          types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterTransmitted(opts *bind.FilterOpts) (*CommitStoreTransmittedIterator, error) {

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "Transmitted")
	if err != nil {
		return nil, err
	}
	return &CommitStoreTransmittedIterator{contract: _CommitStore.contract, event: "Transmitted", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchTransmitted(opts *bind.WatchOpts, sink chan<- *CommitStoreTransmitted) (event.Subscription, error) {

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "Transmitted")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreTransmitted)
				if err := _CommitStore.contract.UnpackLog(event, "Transmitted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseTransmitted(log types.Log) (*CommitStoreTransmitted, error) {
	event := new(CommitStoreTransmitted)
	if err := _CommitStore.contract.UnpackLog(event, "Transmitted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreUnpausedIterator struct {
	Event *CommitStoreUnpaused

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreUnpausedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreUnpaused)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreUnpaused)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreUnpausedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreUnpausedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreUnpaused struct {
	Account common.Address
	Raw     types.Log
}

func (_CommitStore *CommitStoreFilterer) FilterUnpaused(opts *bind.FilterOpts) (*CommitStoreUnpausedIterator, error) {

	logs, sub, err := _CommitStore.contract.FilterLogs(opts, "Unpaused")
	if err != nil {
		return nil, err
	}
	return &CommitStoreUnpausedIterator{contract: _CommitStore.contract, event: "Unpaused", logs: logs, sub: sub}, nil
}

func (_CommitStore *CommitStoreFilterer) WatchUnpaused(opts *bind.WatchOpts, sink chan<- *CommitStoreUnpaused) (event.Subscription, error) {

	logs, sub, err := _CommitStore.contract.WatchLogs(opts, "Unpaused")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreUnpaused)
				if err := _CommitStore.contract.UnpackLog(event, "Unpaused", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStore *CommitStoreFilterer) ParseUnpaused(log types.Log) (*CommitStoreUnpaused, error) {
	event := new(CommitStoreUnpaused)
	if err := _CommitStore.contract.UnpackLog(event, "Unpaused", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type LatestConfigDetails struct {
	ConfigCount  uint32
	BlockNumber  uint32
	ConfigDigest [32]byte
}
type LatestConfigDigestAndEpoch struct {
	ScanLogs     bool
	ConfigDigest [32]byte
	Epoch        uint32
}

func (_CommitStore *CommitStore) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _CommitStore.abi.Events["ConfigSet"].ID:
		return _CommitStore.ParseConfigSet(log)
	case _CommitStore.abi.Events["ConfigSet0"].ID:
		return _CommitStore.ParseConfigSet0(log)
	case _CommitStore.abi.Events["LatestPriceEpochAndRoundSet"].ID:
		return _CommitStore.ParseLatestPriceEpochAndRoundSet(log)
	case _CommitStore.abi.Events["OwnershipTransferRequested"].ID:
		return _CommitStore.ParseOwnershipTransferRequested(log)
	case _CommitStore.abi.Events["OwnershipTransferred"].ID:
		return _CommitStore.ParseOwnershipTransferred(log)
	case _CommitStore.abi.Events["Paused"].ID:
		return _CommitStore.ParsePaused(log)
	case _CommitStore.abi.Events["ReportAccepted"].ID:
		return _CommitStore.ParseReportAccepted(log)
	case _CommitStore.abi.Events["RootRemoved"].ID:
		return _CommitStore.ParseRootRemoved(log)
	case _CommitStore.abi.Events["SequenceNumberSet"].ID:
		return _CommitStore.ParseSequenceNumberSet(log)
	case _CommitStore.abi.Events["Transmitted"].ID:
		return _CommitStore.ParseTransmitted(log)
	case _CommitStore.abi.Events["Unpaused"].ID:
		return _CommitStore.ParseUnpaused(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (CommitStoreConfigSet) Topic() common.Hash {
	return common.HexToHash("0xc9d7123efd4203e60b0f0a4b1dbc4800fc97ce63679f71c3a27279b24a7ddec3")
}

func (CommitStoreConfigSet0) Topic() common.Hash {
	return common.HexToHash("0x1591690b8638f5fb2dbec82ac741805ac5da8b45dc5263f4875b0496fdce4e05")
}

func (CommitStoreLatestPriceEpochAndRoundSet) Topic() common.Hash {
	return common.HexToHash("0xf0d557bfce33e354b41885eb9264448726cfe51f486ffa69809d2bf565456444")
}

func (CommitStoreOwnershipTransferRequested) Topic() common.Hash {
	return common.HexToHash("0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278")
}

func (CommitStoreOwnershipTransferred) Topic() common.Hash {
	return common.HexToHash("0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0")
}

func (CommitStorePaused) Topic() common.Hash {
	return common.HexToHash("0x62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258")
}

func (CommitStoreReportAccepted) Topic() common.Hash {
	return common.HexToHash("0x291698c01aa71f912280535d88a00d2c59fb63530a3f5d0098560468acb9ebf5")
}

func (CommitStoreRootRemoved) Topic() common.Hash {
	return common.HexToHash("0x202f1139a3e334b6056064c0e9b19fd07e44a88d8f6e5ded571b24cf8c371f12")
}

func (CommitStoreSequenceNumberSet) Topic() common.Hash {
	return common.HexToHash("0xea59e8027e41fda1525220008cf2416797405065eb21b0ebd417bfc6d361b8de")
}

func (CommitStoreTransmitted) Topic() common.Hash {
	return common.HexToHash("0xb04e63db38c49950639fa09d29872f21f5d49d614f3a969d8adf3d4b52e41a62")
}

func (CommitStoreUnpaused) Topic() common.Hash {
	return common.HexToHash("0x5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa")
}

func (_CommitStore *CommitStore) Address() common.Address {
	return _CommitStore.address
}

type CommitStoreInterface interface {
	GetDynamicConfig(opts *bind.CallOpts) (CommitStoreDynamicConfig, error)

	GetExpectedNextSequenceNumber(opts *bind.CallOpts) (uint64, error)

	GetLatestPriceEpochAndRound(opts *bind.CallOpts) (uint64, error)

	GetMerkleRoot(opts *bind.CallOpts, root [32]byte) (*big.Int, error)

	GetStaticConfig(opts *bind.CallOpts) (CommitStoreStaticConfig, error)

	GetTransmitters(opts *bind.CallOpts) ([]common.Address, error)

	IsBlessed(opts *bind.CallOpts, root [32]byte) (bool, error)

	IsUnpausedAndNotCursed(opts *bind.CallOpts) (bool, error)

	LatestConfigDetails(opts *bind.CallOpts) (LatestConfigDetails,

		error)

	LatestConfigDigestAndEpoch(opts *bind.CallOpts) (LatestConfigDigestAndEpoch,

		error)

	Owner(opts *bind.CallOpts) (common.Address, error)

	Paused(opts *bind.CallOpts) (bool, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	Verify(opts *bind.CallOpts, hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error)

	AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error)

	Pause(opts *bind.TransactOpts) (*types.Transaction, error)

	ResetUnblessedRoots(opts *bind.TransactOpts, rootToReset [][32]byte) (*types.Transaction, error)

	SetLatestPriceEpochAndRound(opts *bind.TransactOpts, latestPriceEpochAndRound *big.Int) (*types.Transaction, error)

	SetMinSeqNr(opts *bind.TransactOpts, minSeqNr uint64) (*types.Transaction, error)

	SetOCR2Config(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error)

	TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error)

	Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error)

	Unpause(opts *bind.TransactOpts) (*types.Transaction, error)

	FilterConfigSet(opts *bind.FilterOpts) (*CommitStoreConfigSetIterator, error)

	WatchConfigSet(opts *bind.WatchOpts, sink chan<- *CommitStoreConfigSet) (event.Subscription, error)

	ParseConfigSet(log types.Log) (*CommitStoreConfigSet, error)

	FilterConfigSet0(opts *bind.FilterOpts) (*CommitStoreConfigSet0Iterator, error)

	WatchConfigSet0(opts *bind.WatchOpts, sink chan<- *CommitStoreConfigSet0) (event.Subscription, error)

	ParseConfigSet0(log types.Log) (*CommitStoreConfigSet0, error)

	FilterLatestPriceEpochAndRoundSet(opts *bind.FilterOpts) (*CommitStoreLatestPriceEpochAndRoundSetIterator, error)

	WatchLatestPriceEpochAndRoundSet(opts *bind.WatchOpts, sink chan<- *CommitStoreLatestPriceEpochAndRoundSet) (event.Subscription, error)

	ParseLatestPriceEpochAndRoundSet(log types.Log) (*CommitStoreLatestPriceEpochAndRoundSet, error)

	FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreOwnershipTransferRequestedIterator, error)

	WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *CommitStoreOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferRequested(log types.Log) (*CommitStoreOwnershipTransferRequested, error)

	FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreOwnershipTransferredIterator, error)

	WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CommitStoreOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferred(log types.Log) (*CommitStoreOwnershipTransferred, error)

	FilterPaused(opts *bind.FilterOpts) (*CommitStorePausedIterator, error)

	WatchPaused(opts *bind.WatchOpts, sink chan<- *CommitStorePaused) (event.Subscription, error)

	ParsePaused(log types.Log) (*CommitStorePaused, error)

	FilterReportAccepted(opts *bind.FilterOpts) (*CommitStoreReportAcceptedIterator, error)

	WatchReportAccepted(opts *bind.WatchOpts, sink chan<- *CommitStoreReportAccepted) (event.Subscription, error)

	ParseReportAccepted(log types.Log) (*CommitStoreReportAccepted, error)

	FilterRootRemoved(opts *bind.FilterOpts) (*CommitStoreRootRemovedIterator, error)

	WatchRootRemoved(opts *bind.WatchOpts, sink chan<- *CommitStoreRootRemoved) (event.Subscription, error)

	ParseRootRemoved(log types.Log) (*CommitStoreRootRemoved, error)

	FilterSequenceNumberSet(opts *bind.FilterOpts) (*CommitStoreSequenceNumberSetIterator, error)

	WatchSequenceNumberSet(opts *bind.WatchOpts, sink chan<- *CommitStoreSequenceNumberSet) (event.Subscription, error)

	ParseSequenceNumberSet(log types.Log) (*CommitStoreSequenceNumberSet, error)

	FilterTransmitted(opts *bind.FilterOpts) (*CommitStoreTransmittedIterator, error)

	WatchTransmitted(opts *bind.WatchOpts, sink chan<- *CommitStoreTransmitted) (event.Subscription, error)

	ParseTransmitted(log types.Log) (*CommitStoreTransmitted, error)

	FilterUnpaused(opts *bind.FilterOpts) (*CommitStoreUnpausedIterator, error)

	WatchUnpaused(opts *bind.WatchOpts, sink chan<- *CommitStoreUnpaused) (event.Subscription, error)

	ParseUnpaused(log types.Log) (*CommitStoreUnpaused, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
