// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package rmn_remote

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/interfaces/IRMN.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IRMNRemote.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Internal.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/MerkleMultiProof.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/rmn/RMNRemote.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/enumerable/EnumerableSetWithBytes16.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IRMN.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This interface contains the only RMN-related functions that might be used on-chain by other CCIP contracts.\\ninterface IRMN {\\n  /// @notice A Merkle root tagged with the address of the commit store contract it is destined for.\\n  struct TaggedRoot {\\n    address commitStore;\\n    bytes32 root;\\n  }\\n\\n  /// @notice Callers MUST NOT cache the return value as a blessed tagged root could become unblessed.\\n  function isBlessed(\\n    TaggedRoot calldata taggedRoot\\n  ) external view returns (bool);\\n\\n  /// @notice Iff there is an active global or legacy curse, this function returns true.\\n  function isCursed() external view returns (bool);\\n\\n  /// @notice Iff there is an active global curse, or an active curse for `subject`, this function returns true.\\n  /// @param subject To check whether a particular chain is cursed, set to bytes16(uint128(chainSelector)).\\n  function isCursed(\\n    bytes16 subject\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/IRMNRemote.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\n\\n/// @notice This interface contains the only RMN-related functions that might be used on-chain by other CCIP contracts.\\ninterface IRMNRemote {\\n  /// @notice signature components from RMN nodes.\\n  struct Signature {\\n    bytes32 r;\\n    bytes32 s;\\n  }\\n\\n  /// @notice Verifies signatures of RMN nodes, on dest lane updates as provided in the CommitReport.\\n  /// @param offRampAddress is not inferred by msg.sender, in case the call is made through RMNProxy.\\n  /// @param merkleRoots must be well formed, and is a representation of the CommitReport received from the oracles.\\n  /// @param signatures rmnNodes ECDSA sigs, only r \\u0026 s, must be sorted in ascending order by signer address.\\n  /// @dev Will revert if verification fails.\\n  function verify(\\n    address offRampAddress,\\n    Internal.MerkleRoot[] memory merkleRoots,\\n    Signature[] memory signatures\\n  ) external view;\\n\\n  /// @notice gets the current set of cursed subjects.\\n  /// @return subjects the list of cursed subjects.\\n  function getCursedSubjects() external view returns (bytes16[] memory subjects);\\n\\n  /// @notice If there is an active global or legacy curse, this function returns true.\\n  /// @return bool true if there is an active global curse.\\n  function isCursed() external view returns (bool);\\n\\n  /// @notice If there is an active global curse, or an active curse for `subject`, this function returns true.\\n  /// @param subject To check whether a particular chain is cursed, set to bytes16(uint128(chainSelector)).\\n  /// @return bool true if the provided subject is cured *or* if there is an active global curse.\\n  function isCursed(\\n    bytes16 subject\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/libraries/Internal.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {MerkleMultiProof} from \\\"../libraries/MerkleMultiProof.sol\\\";\\n\\n/// @notice Library for CCIP internal definitions common to multiple contracts.\\n/// @dev The following is a non-exhaustive list of \\\"known issues\\\" for CCIP:\\n/// - We could implement yield claiming for Blast. This is not worth the custom code path on non-blast chains.\\n/// - uint32 is used for timestamps, which will overflow in 2106. This is not a concern for the current use case, as we\\n/// expect to have migrated to a new version by then.\\nlibrary Internal {\\n  error InvalidEVMAddress(bytes encodedAddress);\\n  error Invalid32ByteAddress(bytes encodedAddress);\\n\\n  /// @dev We limit return data to a selector plus 4 words. This is to avoid malicious contracts from returning\\n  /// large amounts of data and causing repeated out-of-gas scenarios.\\n  uint16 internal constant MAX_RET_BYTES = 4 + 4 * 32;\\n  /// @dev The expected number of bytes returned by the balanceOf function.\\n  uint256 internal constant MAX_BALANCE_OF_RET_BYTES = 32;\\n\\n  /// @dev The address used to send calls for gas estimation.\\n  /// You only need to use this address if the minimum gas limit specified by the user is not actually enough to execute the\\n  /// given message and you're attempting to estimate the actual necessary gas limit\\n  address public constant GAS_ESTIMATION_SENDER = address(0xC11C11C11C11C11C11C11C11C11C11C11C11C1);\\n\\n  /// @notice A collection of token price and gas price updates.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct PriceUpdates {\\n    TokenPriceUpdate[] tokenPriceUpdates;\\n    GasPriceUpdate[] gasPriceUpdates;\\n  }\\n\\n  /// @notice Token price in USD.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct TokenPriceUpdate {\\n    address sourceToken; // Source token.\\n    uint224 usdPerToken; // 1e18 USD per 1e18 of the smallest token denomination.\\n  }\\n\\n  /// @notice Gas price for a given chain in USD, its value may contain tightly packed fields.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct GasPriceUpdate {\\n    uint64 destChainSelector; // Destination chain selector.\\n    uint224 usdPerUnitGas; // 1e18 USD per smallest unit (e.g. wei) of destination chain gas.\\n  }\\n\\n  /// @notice A timestamped uint224 value that can contain several tightly packed fields.\\n  struct TimestampedPackedUint224 {\\n    uint224 value; // ────╮ Value in uint224, packed.\\n    uint32 timestamp; // ─╯ Timestamp of the most recent price update.\\n  }\\n\\n  /// @dev Gas price is stored in 112-bit unsigned int. uint224 can pack 2 prices.\\n  /// When packing L1 and L2 gas prices, L1 gas price is left-shifted to the higher-order bits.\\n  /// Using uint8 type, which cannot be higher than other bit shift operands, to avoid shift operand type warning.\\n  uint8 public constant GAS_PRICE_BITS = 112;\\n\\n  struct SourceTokenData {\\n    // The source pool address, abi encoded. This value is trusted as it was obtained through the onRamp. It can be\\n    // relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint32 destGasAmount; // The amount of gas available for the releaseOrMint and balanceOf calls on the offRamp\\n  }\\n\\n  /// @notice Report that is submitted by the execution DON at the execution phase, including chain selector data.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct ExecutionReport {\\n    uint64 sourceChainSelector; // Source chain selector for which the report is submitted.\\n    Any2EVMRampMessage[] messages;\\n    // Contains a bytes array for each message, each inner bytes array contains bytes per transferred token.\\n    bytes[][] offchainTokenData;\\n    bytes32[] proofs;\\n    uint256 proofFlagBits;\\n  }\\n\\n  /// @dev Any2EVMRampMessage struct has 10 fields, including 3 variable unnested arrays, sender, data and tokenAmounts.\\n  /// Each variable array takes 1 more slot to store its length.\\n  /// When abi encoded, excluding array contents, Any2EVMMessage takes up a fixed number of 13 slots, 32 bytes each.\\n  /// Assume 1 slot for sender\\n  /// For structs that contain arrays, 1 more slot is added to the front, reaching a total of 14.\\n  /// The fixed bytes does not cover struct data (this is represented by MESSAGE_FIXED_BYTES_PER_TOKEN)\\n  uint256 public constant MESSAGE_FIXED_BYTES = 32 * 15;\\n\\n  /// @dev Any2EVMTokensTransfer struct bytes length\\n  /// 0x20\\n  /// sourcePoolAddress_offset\\n  /// destTokenAddress\\n  /// destGasAmount\\n  /// extraData_offset\\n  /// amount\\n  /// sourcePoolAddress_length\\n  /// sourcePoolAddress_content // assume 1 slot\\n  /// extraData_length // contents billed separately\\n  uint256 public constant MESSAGE_FIXED_BYTES_PER_TOKEN = 32 * (4 + (3 + 2));\\n\\n  bytes32 internal constant ANY_2_EVM_MESSAGE_HASH = keccak256(\\\"Any2EVMMessageHashV1\\\");\\n  bytes32 internal constant EVM_2_ANY_MESSAGE_HASH = keccak256(\\\"EVM2AnyMessageHashV1\\\");\\n\\n  /// @dev Used to hash messages for multi-lane family-agnostic OffRamps.\\n  /// OnRamp hash(EVM2AnyMessage) != Any2EVMRampMessage.messageId.\\n  /// OnRamp hash(EVM2AnyMessage) != OffRamp hash(Any2EVMRampMessage).\\n  /// @param original OffRamp message to hash.\\n  /// @param metadataHash Hash preimage to ensure global uniqueness.\\n  /// @return hashedMessage hashed message as a keccak256.\\n  function _hash(Any2EVMRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.header.messageId,\\n            original.receiver,\\n            original.header.sequenceNumber,\\n            original.gasLimit,\\n            original.header.nonce\\n          )\\n        ),\\n        keccak256(original.sender),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts))\\n      )\\n    );\\n  }\\n\\n  function _hash(EVM2AnyRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.sender,\\n            original.header.sequenceNumber,\\n            original.header.nonce,\\n            original.feeToken,\\n            original.feeTokenAmount\\n          )\\n        ),\\n        keccak256(original.receiver),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts)),\\n        keccak256(original.extraArgs)\\n      )\\n    );\\n  }\\n\\n  /// @dev We disallow the first 1024 addresses to avoid calling into a range known for hosting precompiles. Calling\\n  /// into precompiles probably won't cause any issues, but to be safe we can disallow this range. It is extremely\\n  /// unlikely that anyone would ever be able to generate an address in this range. There is no official range of\\n  /// precompiles, but EIP-7587 proposes to reserve the range 0x100 to 0x1ff. Our range is more conservative, even\\n  /// though it might not be exhaustive for all chains, which is OK. We also disallow the zero address, which is a\\n  /// common practice.\\n  uint256 public constant EVM_PRECOMPILE_SPACE = 1024;\\n\\n  // According to the Aptos docs, the first 0xa addresses are reserved for precompiles.\\n  // https://github.com/aptos-labs/aptos-core/blob/main/aptos-move/framework/aptos-framework/doc/account.md#function-create_framework_reserved_account-1\\n  uint256 public constant APTOS_PRECOMPILE_SPACE = 0x0b;\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// EVM address space. If it isn't it will revert with an InvalidEVMAddress error, which we can catch and handle\\n  /// more gracefully than a revert from abi.decode.\\n  function _validateEVMAddress(\\n    bytes memory encodedAddress\\n  ) internal pure {\\n    if (encodedAddress.length != 32) revert InvalidEVMAddress(encodedAddress);\\n    uint256 encodedAddressUint = abi.decode(encodedAddress, (uint256));\\n    if (encodedAddressUint \\u003e type(uint160).max || encodedAddressUint \\u003c EVM_PRECOMPILE_SPACE) {\\n      revert InvalidEVMAddress(encodedAddress);\\n    }\\n  }\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// bounds of [minValue, uint256.max]. If it isn't it will revert with an Invalid32ByteAddress error.\\n  function _validate32ByteAddress(bytes memory encodedAddress, uint256 minValue) internal pure {\\n    if (encodedAddress.length != 32) revert Invalid32ByteAddress(encodedAddress);\\n    if (minValue \\u003e 0) {\\n      if (abi.decode(encodedAddress, (uint256)) \\u003c minValue) {\\n        revert Invalid32ByteAddress(encodedAddress);\\n      }\\n    }\\n  }\\n\\n  /// @notice Enum listing the possible message execution states within the offRamp contract.\\n  /// UNTOUCHED never executed.\\n  /// IN_PROGRESS currently being executed, used a replay protection.\\n  /// SUCCESS successfully executed. End state.\\n  /// FAILURE unsuccessfully executed, manual execution is now enabled.\\n  /// @dev RMN depends on this enum, if changing, please notify the RMN maintainers.\\n  enum MessageExecutionState {\\n    UNTOUCHED,\\n    IN_PROGRESS,\\n    SUCCESS,\\n    FAILURE\\n  }\\n\\n  /// @notice CCIP OCR plugin type, used to separate execution \\u0026 commit transmissions and configs.\\n  enum OCRPluginType {\\n    Commit,\\n    Execution\\n  }\\n\\n  /// @notice Family-agnostic header for OnRamp \\u0026 OffRamp messages.\\n  /// The messageId is not expected to match hash(message), since it may originate from another ramp family.\\n  struct RampMessageHeader {\\n    bytes32 messageId; // Unique identifier for the message, generated with the source chain's encoding scheme (i.e. not necessarily abi.encoded).\\n    uint64 sourceChainSelector; // ─╮ the chain selector of the source chain, note: not chainId.\\n    uint64 destChainSelector; //    │ the chain selector of the destination chain, note: not chainId.\\n    uint64 sequenceNumber; //       │ sequence number, not unique across lanes.\\n    uint64 nonce; // ───────────────╯ nonce for this lane for this sender, not unique across senders/lanes.\\n  }\\n\\n  struct EVM2AnyTokenTransfer {\\n    // The source pool EVM address. This value is trusted as it was obtained through the onRamp. It can be relied\\n    // upon by the destination pool to validate the source pool.\\n    address sourcePoolAddress;\\n    // The EVM address of the destination token.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n    // Destination chain data used to execute the token transfer on the destination chain. For an EVM destination, it\\n    // consists of the amount of gas available for the releaseOrMint and transfer calls made by the offRamp.\\n    bytes destExecData;\\n  }\\n\\n  struct Any2EVMTokenTransfer {\\n    // The source pool EVM address encoded to bytes. This value is trusted as it is obtained through the onRamp. It can\\n    // be relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    address destTokenAddress; // ─╮ Address of destination token\\n    uint32 destGasAmount; // ─────╯ The amount of gas available for the releaseOrMint and transfer calls on the offRamp.\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  /// @notice Family-agnostic message routed to an OffRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage), hash(Any2EVMRampMessage) != messageId due to encoding\\n  /// and parameter differences.\\n  struct Any2EVMRampMessage {\\n    RampMessageHeader header; // Message header.\\n    bytes sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    address receiver; // receiver address on the destination chain.\\n    uint256 gasLimit; // user supplied maximum gas amount available for dest chain execution.\\n    Any2EVMTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  /// @notice Family-agnostic message emitted from the OnRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage) due to encoding \\u0026 parameter differences.\\n  /// messageId = hash(EVM2AnyRampMessage) using the source EVM chain's encoding format.\\n  struct EVM2AnyRampMessage {\\n    RampMessageHeader header; // Message header.\\n    address sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    bytes receiver; // receiver address on the destination chain.\\n    bytes extraArgs; // destination-chain specific extra args, such as the gasLimit for EVM chains.\\n    address feeToken; // fee token.\\n    uint256 feeTokenAmount; // fee token amount.\\n    uint256 feeValueJuels; // fee amount in Juels.\\n    EVM2AnyTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector EVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_EVM = 0x2812d52c;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SVM = 0x1e10bdc4;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector APTOS\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_APTOS = 0xac77ffec;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SUI\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SUI = 0xc4e05953;\\n\\n  /// @dev Holds a merkle root and interval for a source chain so that an array of these can be passed in the CommitReport.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  /// @dev inefficient struct packing intentionally chosen to maintain order of specificity. Not a storage struct so impact is minimal.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct MerkleRoot {\\n    uint64 sourceChainSelector; // Remote source chain selector that the Merkle Root is scoped to\\n    bytes onRampAddress; //        Generic onRamp address, to support arbitrary sources; for EVM, use abi.encode\\n    uint64 minSeqNr; // ─────────╮ Minimum sequence number, inclusive\\n    uint64 maxSeqNr; // ─────────╯ Maximum sequence number, inclusive\\n    bytes32 merkleRoot; //         Merkle root covering the interval \\u0026 source chain messages\\n  }\\n}\\n\"},\"contracts/libraries/MerkleMultiProof.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nlibrary MerkleMultiProof {\\n  /// @notice Leaf domain separator, should be used as the first 32 bytes of a leaf's preimage.\\n  bytes32 internal constant LEAF_DOMAIN_SEPARATOR = 0x0000000000000000000000000000000000000000000000000000000000000000;\\n  /// @notice Internal domain separator, should be used as the first 32 bytes of an internal node's preimage.\\n  bytes32 internal constant INTERNAL_DOMAIN_SEPARATOR =\\n    0x0000000000000000000000000000000000000000000000000000000000000001;\\n\\n  uint256 internal constant MAX_NUM_HASHES = 256;\\n\\n  error InvalidProof();\\n  error LeavesCannotBeEmpty();\\n\\n  /// @notice Computes the root based on provided pre-hashed leaf nodes in leaves, internal nodes  in proofs, and using\\n  /// proofFlagBits' i-th bit to determine if an element of proofs or one of the previously computed leafs or internal\\n  /// nodes will be used for the i-th hash.\\n  /// @param leaves Should be pre-hashed and the first 32 bytes of a leaf's preimage should match LEAF_DOMAIN_SEPARATOR.\\n  /// @param proofs Hashes to be used instead of a leaf hash when the proofFlagBits indicates a proof should be used.\\n  /// @param proofFlagBits A single uint256 of which each bit indicates whether a leaf or a proof needs to be used in\\n  /// a hash operation.\\n  /// @dev the maximum number of hash operations it set to 256. Any input that would require more than 256 hashes to get\\n  /// to a root will revert.\\n  /// @dev For given input `leaves` = [a,b,c] `proofs` = [D] and `proofFlagBits` = 5\\n  ///     totalHashes = 3 + 1 - 1 = 3\\n  ///  ** round 1 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 0) \\u0026 1 = true\\n  ///    hashes[0] = hashPair(a, b)\\n  ///    (leafPos, hashPos, proofPos) = (2, 0, 0);\\n  ///\\n  ///  ** round 2 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 1) \\u0026 1 = false\\n  ///    hashes[1] = hashPair(D, c)\\n  ///    (leafPos, hashPos, proofPos) = (3, 0, 1);\\n  ///\\n  ///  ** round 3 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 2) \\u0026 1 = true\\n  ///    hashes[2] = hashPair(hashes[0], hashes[1])\\n  ///    (leafPos, hashPos, proofPos) = (3, 2, 1);\\n  ///\\n  ///    i = 3 and no longer \\u003c totalHashes. The algorithm is done\\n  ///    return hashes[totalHashes - 1] = hashes[2]; the last hash we computed.\\n  // We mark this function as internal to force it to be inlined in contracts that use it, but semantically it is public.\\n  function _merkleRoot(\\n    bytes32[] memory leaves,\\n    bytes32[] memory proofs,\\n    uint256 proofFlagBits\\n  ) internal pure returns (bytes32) {\\n    unchecked {\\n      uint256 leavesLen = leaves.length;\\n      uint256 proofsLen = proofs.length;\\n      if (leavesLen == 0) revert LeavesCannotBeEmpty();\\n      if (!(leavesLen \\u003c= MAX_NUM_HASHES + 1 \\u0026\\u0026 proofsLen \\u003c= MAX_NUM_HASHES + 1)) revert InvalidProof();\\n      uint256 totalHashes = leavesLen + proofsLen - 1;\\n      if (!(totalHashes \\u003c= MAX_NUM_HASHES)) revert InvalidProof();\\n      if (totalHashes == 0) {\\n        return leaves[0];\\n      }\\n      bytes32[] memory hashes = new bytes32[](totalHashes);\\n      (uint256 leafPos, uint256 hashPos, uint256 proofPos) = (0, 0, 0);\\n\\n      for (uint256 i = 0; i \\u003c totalHashes; ++i) {\\n        // Checks if the bit flag signals the use of a supplied proof or a leaf/previous hash.\\n        bytes32 a;\\n        if (proofFlagBits \\u0026 (1 \\u003c\\u003c i) == (1 \\u003c\\u003c i)) {\\n          // Use a leaf or a previously computed hash.\\n          if (leafPos \\u003c leavesLen) {\\n            a = leaves[leafPos++];\\n          } else {\\n            a = hashes[hashPos++];\\n          }\\n        } else {\\n          // Use a supplied proof.\\n          a = proofs[proofPos++];\\n        }\\n\\n        // The second part of the hashed pair is never a proof as hashing two proofs would result in a\\n        // hash that can already be computed offchain.\\n        bytes32 b;\\n        if (leafPos \\u003c leavesLen) {\\n          b = leaves[leafPos++];\\n        } else {\\n          b = hashes[hashPos++];\\n        }\\n\\n        if (!(hashPos \\u003c= i)) revert InvalidProof();\\n\\n        hashes[i] = _hashPair(a, b);\\n      }\\n      if (!(hashPos == totalHashes - 1 \\u0026\\u0026 leafPos == leavesLen \\u0026\\u0026 proofPos == proofsLen)) revert InvalidProof();\\n      // Return the last hash.\\n      return hashes[totalHashes - 1];\\n    }\\n  }\\n\\n  /// @notice Hashes two bytes32 objects in their given order, prepended by the INTERNAL_DOMAIN_SEPARATOR.\\n  function _hashInternalNode(bytes32 left, bytes32 right) private pure returns (bytes32 hash) {\\n    return keccak256(abi.encode(INTERNAL_DOMAIN_SEPARATOR, left, right));\\n  }\\n\\n  /// @notice Hashes two bytes32 objects. The order is taken into account, using the lower value first.\\n  function _hashPair(bytes32 a, bytes32 b) private pure returns (bytes32) {\\n    return a \\u003c b ? _hashInternalNode(a, b) : _hashInternalNode(b, a);\\n  }\\n}\\n\"},\"contracts/rmn/RMNRemote.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IRMN} from \\\"../interfaces/IRMN.sol\\\";\\nimport {IRMNRemote} from \\\"../interfaces/IRMNRemote.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {Ownable2StepMsgSender} from \\\"@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\\\";\\nimport {EnumerableSet} from \\\"@chainlink/contracts/src/v0.8/shared/enumerable/EnumerableSetWithBytes16.sol\\\";\\n\\n/// @dev An active curse on this subject will cause isCursed() and isCursed(bytes16) to return true. Use this subject\\n/// for issues affecting all of CCIP chains, or pertaining to the chain that this contract is deployed on, instead of\\n/// using the local chain selector as a subject.\\nbytes16 constant GLOBAL_CURSE_SUBJECT = 0x01000000000000000000000000000001;\\n\\n/// @notice This contract supports verification of RMN reports for any Any2EVM OffRamp.\\n/// @dev This contract implements both the new IRMNRemote interface and the legacy IRMN interface. This is to allow for\\n/// a seamless migration from the legacy RMN contract to this one. The only function that has been dropped in the newer\\n/// interface is `isBlessed`. For the `isBlessed` function, this contract relays the call to the legacy RMN contract.\\ncontract RMNRemote is Ownable2StepMsgSender, ITypeAndVersion, IRMNRemote, IRMN {\\n  using EnumerableSet for EnumerableSet.Bytes16Set;\\n\\n  error AlreadyCursed(bytes16 subject);\\n  error ConfigNotSet();\\n  error DuplicateOnchainPublicKey();\\n  error InvalidSignature();\\n  error InvalidSignerOrder();\\n  error NotEnoughSigners();\\n  error NotCursed(bytes16 subject);\\n  error OutOfOrderSignatures();\\n  error ThresholdNotMet();\\n  error UnexpectedSigner();\\n  error ZeroValueNotAllowed();\\n  error IsBlessedNotAvailable();\\n\\n  event ConfigSet(uint32 indexed version, Config config);\\n  event Cursed(bytes16[] subjects);\\n  event Uncursed(bytes16[] subjects);\\n\\n  /// @dev the configuration of an RMN signer.\\n  struct Signer {\\n    address onchainPublicKey; // ─╮ For signing reports.\\n    uint64 nodeIndex; // ─────────╯ Maps to nodes in home chain config, should be strictly increasing.\\n  }\\n\\n  /// @dev the contract config.\\n  struct Config {\\n    bytes32 rmnHomeContractConfigDigest; // Digest of the RMNHome contract config.\\n    Signer[] signers; // List of signers.\\n    uint64 fSign; // Max number of faulty RMN nodes; f+1 signers are required to verify a report, must configure 2f+1 signers in total.\\n  }\\n\\n  /// @dev part of the payload that RMN nodes sign: keccak256(abi.encode(RMN_V1_6_ANY2EVM_REPORT, report)).\\n  /// @dev this struct is only ever abi-encoded and hashed; it is never stored.\\n  struct Report {\\n    uint256 destChainId; //                 To guard against chain selector misconfiguration.\\n    uint64 destChainSelector; //  ────────╮ The chain selector of the destination chain.\\n    address rmnRemoteContractAddress; // ─╯ The address of this contract.\\n    address offrampAddress; //              The address of the offramp on the same chain as this contract.\\n    bytes32 rmnHomeContractConfigDigest; // The digest of the RMNHome contract config.\\n    Internal.MerkleRoot[] merkleRoots; //   The dest lane updates.\\n  }\\n\\n  /// @dev this is included in the preimage of the digest that RMN nodes sign.\\n  bytes32 private constant RMN_V1_6_ANY2EVM_REPORT = keccak256(\\\"RMN_V1_6_ANY2EVM_REPORT\\\");\\n\\n  string public constant override typeAndVersion = \\\"RMNRemote 1.6.0\\\";\\n  uint64 internal immutable i_localChainSelector;\\n  IRMN internal immutable i_legacyRMN;\\n\\n  Config private s_config;\\n  uint32 private s_configCount;\\n\\n  /// @dev RMN nodes only generate sigs with v=27; making this constant allows us to save gas by not transmitting v.\\n  /// @dev Any valid ECDSA sig (r, s, v) can be \\\"flipped\\\" into (r, s*, v*) without knowing the private key (where v=27 or 28 for secp256k1)\\n  /// https://github.com/kadenzipfel/smart-contract-vulnerabilities/blob/master/vulnerabilities/signature-malleability.md.\\n  uint8 private constant ECDSA_RECOVERY_V = 27;\\n\\n  EnumerableSet.Bytes16Set private s_cursedSubjects;\\n  mapping(address signer =\\u003e bool exists) private s_signers; // for more gas efficient verify.\\n\\n  /// @param localChainSelector the chain selector of the chain this contract is deployed to.\\n  constructor(uint64 localChainSelector, IRMN legacyRMN) {\\n    if (localChainSelector == 0) revert ZeroValueNotAllowed();\\n    i_localChainSelector = localChainSelector;\\n\\n    i_legacyRMN = legacyRMN;\\n  }\\n\\n  // ================================================================\\n  // │                         Verification                         │\\n  // ================================================================\\n\\n  /// @inheritdoc IRMNRemote\\n  function verify(\\n    address offRampAddress,\\n    Internal.MerkleRoot[] calldata merkleRoots,\\n    Signature[] calldata signatures\\n  ) external view {\\n    if (s_configCount == 0) {\\n      revert ConfigNotSet();\\n    }\\n    if (signatures.length \\u003c s_config.fSign + 1) revert ThresholdNotMet();\\n\\n    bytes32 digest = keccak256(\\n      abi.encode(\\n        RMN_V1_6_ANY2EVM_REPORT,\\n        Report({\\n          destChainId: block.chainid,\\n          destChainSelector: i_localChainSelector,\\n          rmnRemoteContractAddress: address(this),\\n          offrampAddress: offRampAddress,\\n          rmnHomeContractConfigDigest: s_config.rmnHomeContractConfigDigest,\\n          merkleRoots: merkleRoots\\n        })\\n      )\\n    );\\n\\n    address prevAddress;\\n    address signerAddress;\\n    for (uint256 i = 0; i \\u003c signatures.length; ++i) {\\n      signerAddress = ecrecover(digest, ECDSA_RECOVERY_V, signatures[i].r, signatures[i].s);\\n      if (signerAddress == address(0)) revert InvalidSignature();\\n      if (prevAddress \\u003e= signerAddress) revert OutOfOrderSignatures();\\n      if (!s_signers[signerAddress]) revert UnexpectedSigner();\\n      prevAddress = signerAddress;\\n    }\\n  }\\n\\n  // ================================================================\\n  // │                            Config                            │\\n  // ================================================================\\n\\n  /// @notice Sets the configuration of the contract.\\n  /// @param newConfig the new configuration.\\n  /// @dev setting config is atomic; we delete all pre-existing config and set everything from scratch.\\n  function setConfig(\\n    Config calldata newConfig\\n  ) external onlyOwner {\\n    if (newConfig.rmnHomeContractConfigDigest == bytes32(0)) {\\n      revert ZeroValueNotAllowed();\\n    }\\n\\n    // signers are in ascending order of nodeIndex.\\n    for (uint256 i = 1; i \\u003c newConfig.signers.length; ++i) {\\n      if (!(newConfig.signers[i - 1].nodeIndex \\u003c newConfig.signers[i].nodeIndex)) {\\n        revert InvalidSignerOrder();\\n      }\\n    }\\n\\n    // min signers requirement is tenable.\\n    if (newConfig.signers.length \\u003c 2 * newConfig.fSign + 1) {\\n      revert NotEnoughSigners();\\n    }\\n\\n    // clear the old signers.\\n    for (uint256 i = s_config.signers.length; i \\u003e 0; --i) {\\n      delete s_signers[s_config.signers[i - 1].onchainPublicKey];\\n    }\\n\\n    // set the new signers.\\n    for (uint256 i = 0; i \\u003c newConfig.signers.length; ++i) {\\n      if (s_signers[newConfig.signers[i].onchainPublicKey]) {\\n        revert DuplicateOnchainPublicKey();\\n      }\\n      s_signers[newConfig.signers[i].onchainPublicKey] = true;\\n    }\\n\\n    s_config = newConfig;\\n    uint32 newConfigCount = ++s_configCount;\\n    emit ConfigSet(newConfigCount, newConfig);\\n  }\\n\\n  /// @notice Returns the current configuration of the contract and a version number.\\n  /// @return version the current configs version.\\n  /// @return config the current config.\\n  function getVersionedConfig() external view returns (uint32 version, Config memory config) {\\n    return (s_configCount, s_config);\\n  }\\n\\n  /// @notice Returns the chain selector configured at deployment time.\\n  /// @return localChainSelector the chain selector, not the chain ID.\\n  function getLocalChainSelector() external view returns (uint64 localChainSelector) {\\n    return i_localChainSelector;\\n  }\\n\\n  /// @notice Returns the 32 byte header used in computing the report digest.\\n  /// @return digestHeader the digest header.\\n  function getReportDigestHeader() external pure returns (bytes32 digestHeader) {\\n    return RMN_V1_6_ANY2EVM_REPORT;\\n  }\\n\\n  // ================================================================\\n  // │                           Cursing                            │\\n  // ================================================================\\n\\n  /// @notice Curse a single subject.\\n  /// @param subject the subject to curse.\\n  function curse(\\n    bytes16 subject\\n  ) external {\\n    bytes16[] memory subjects = new bytes16[](1);\\n    subjects[0] = subject;\\n    curse(subjects);\\n  }\\n\\n  /// @notice Curse an array of subjects.\\n  /// @param subjects the subjects to curse.\\n  /// @dev reverts if any of the subjects are already cursed or if there is a duplicate.\\n  function curse(\\n    bytes16[] memory subjects\\n  ) public onlyOwner {\\n    for (uint256 i = 0; i \\u003c subjects.length; ++i) {\\n      if (!s_cursedSubjects.add(subjects[i])) {\\n        revert AlreadyCursed(subjects[i]);\\n      }\\n    }\\n    emit Cursed(subjects);\\n  }\\n\\n  /// @notice Uncurse a single subject.\\n  /// @param subject the subject to uncurse.\\n  function uncurse(\\n    bytes16 subject\\n  ) external {\\n    bytes16[] memory subjects = new bytes16[](1);\\n    subjects[0] = subject;\\n    uncurse(subjects);\\n  }\\n\\n  /// @notice Uncurse an array of subjects.\\n  /// @param subjects the subjects to uncurse.\\n  /// @dev reverts if any of the subjects are not cursed or if there is a duplicate.\\n  function uncurse(\\n    bytes16[] memory subjects\\n  ) public onlyOwner {\\n    for (uint256 i = 0; i \\u003c subjects.length; ++i) {\\n      if (!s_cursedSubjects.remove(subjects[i])) {\\n        revert NotCursed(subjects[i]);\\n      }\\n    }\\n    emit Uncursed(subjects);\\n  }\\n\\n  /// @inheritdoc IRMNRemote\\n  function getCursedSubjects() external view returns (bytes16[] memory subjects) {\\n    return s_cursedSubjects.values();\\n  }\\n\\n  /// @inheritdoc IRMNRemote\\n  function isCursed() external view override(IRMN, IRMNRemote) returns (bool) {\\n    // There are zero curses under normal circumstances, which means it's cheaper to check for the absence of curses.\\n    // than to check the subject list for the global curse subject.\\n    if (s_cursedSubjects.length() == 0) {\\n      return false;\\n    }\\n    return s_cursedSubjects.contains(GLOBAL_CURSE_SUBJECT);\\n  }\\n\\n  /// @inheritdoc IRMNRemote\\n  function isCursed(\\n    bytes16 subject\\n  ) external view override(IRMN, IRMNRemote) returns (bool) {\\n    // There are zero curses under normal circumstances, which means it's cheaper to check for the absence of curses.\\n    // than to check the subject list twice, as we have to check for both the given and global curse subjects.\\n    if (s_cursedSubjects.length() == 0) {\\n      return false;\\n    }\\n    return s_cursedSubjects.contains(subject) || s_cursedSubjects.contains(GLOBAL_CURSE_SUBJECT);\\n  }\\n\\n  // ================================================================\\n  // │                     Legacy pass through                      │\\n  // ================================================================\\n\\n  /// @inheritdoc IRMN\\n  /// @dev This function is only expected to be used for messages from CCIP versions below 1.6.\\n  function isBlessed(\\n    TaggedRoot calldata taggedRoot\\n  ) external view returns (bool) {\\n    if (i_legacyRMN == IRMN(address(0))) {\\n      revert IsBlessedNotAvailable();\\n    }\\n\\n    return i_legacyRMN.isBlessed(taggedRoot);\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/enumerable/EnumerableSetWithBytes16.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\n/// @dev this is a fully copy of OZ's EnumerableSet library with the addition of a Bytes16Set\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```solidity\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n  // To implement this library for multiple types with as little code\\n  // repetition as possible, we write it in terms of a generic Set type with\\n  // bytes32 values.\\n  // The Set implementation uses private functions, and user-facing\\n  // implementations (such as AddressSet) are just wrappers around the\\n  // underlying Set.\\n  // This means that we can only create new EnumerableSets for types that fit\\n  // in bytes32.\\n\\n  struct Set {\\n    // Storage of set values\\n    bytes32[] _values;\\n    // Position is the index of the value in the `values` array plus 1.\\n    // Position 0 is used to mean a value is not in the set.\\n    mapping(bytes32 value =\\u003e uint256) _positions;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function _add(Set storage set, bytes32 value) private returns (bool) {\\n    if (!_contains(set, value)) {\\n      set._values.push(value);\\n      // The value is stored at length-1, but we add 1 to all indexes\\n      // and use 0 as a sentinel value\\n      set._positions[value] = set._values.length;\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function _remove(Set storage set, bytes32 value) private returns (bool) {\\n    // We cache the value's position to prevent multiple reads from the same storage slot\\n    uint256 position = set._positions[value];\\n\\n    if (position != 0) {\\n      // Equivalent to contains(set, value)\\n      // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n      // the array, and then remove the last element (sometimes called as 'swap and pop').\\n      // This modifies the order of the array, as noted in {at}.\\n\\n      uint256 valueIndex = position - 1;\\n      uint256 lastIndex = set._values.length - 1;\\n\\n      if (valueIndex != lastIndex) {\\n        bytes32 lastValue = set._values[lastIndex];\\n\\n        // Move the lastValue to the index where the value to delete is\\n        set._values[valueIndex] = lastValue;\\n        // Update the tracked position of the lastValue (that was just moved)\\n        set._positions[lastValue] = position;\\n      }\\n\\n      // Delete the slot where the moved value was stored\\n      set._values.pop();\\n\\n      // Delete the tracked position for the deleted slot\\n      delete set._positions[value];\\n\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n    return set._positions[value] != 0;\\n  }\\n\\n  /**\\n   * @dev Returns the number of values on the set. O(1).\\n   */\\n  function _length(Set storage set) private view returns (uint256) {\\n    return set._values.length;\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n    return set._values[index];\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function _values(Set storage set) private view returns (bytes32[] memory) {\\n    return set._values;\\n  }\\n\\n  // Bytes32Set\\n\\n  struct Bytes32Set {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _add(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _remove(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n    return _contains(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(Bytes32Set storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n    return _at(set._inner, index);\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    bytes32[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // AddressSet\\n\\n  struct AddressSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(AddressSet storage set, address value) internal returns (bool) {\\n    return _add(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(AddressSet storage set, address value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(AddressSet storage set, address value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(AddressSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n    return address(uint160(uint256(_at(set._inner, index))));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(AddressSet storage set) internal view returns (address[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    address[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // UintSet\\n\\n  struct UintSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _add(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(UintSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n    return uint256(_at(set._inner, index));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(UintSet storage set) internal view returns (uint256[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    uint256[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // Bytes16Set\\n\\n  struct Bytes16Set {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(Bytes16Set storage set, bytes16 value) internal returns (bool) {\\n    return _add(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(Bytes16Set storage set, bytes16 value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(Bytes16Set storage set, bytes16 value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(Bytes16Set storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(Bytes16Set storage set, uint256 index) internal view returns (bytes16) {\\n    return bytes16(_at(set._inner, index));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(Bytes16Set storage set) internal view returns (bytes16[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    bytes16[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"}}}"
