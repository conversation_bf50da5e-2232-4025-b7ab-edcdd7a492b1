// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package token_admin_registry

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/interfaces/IPool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/ITokenAdminRegistry.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Pool.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/tokenAdminRegistry/TokenAdminRegistry.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/interfaces/IPool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Pool} from \\\"../libraries/Pool.sol\\\";\\n\\nimport {IERC165} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\\\";\\n\\n/// @notice Shared public interface for multiple V1 pool types.\\n/// Each pool type handles a different child token model e.g. lock/unlock, mint/burn.\\ninterface IPoolV1 is IERC165 {\\n  /// @notice Lock tokens into the pool or burn the tokens.\\n  /// @param lockOrBurnIn Encoded data fields for the processing of tokens on the source chain.\\n  /// @return lockOrBurnOut Encoded data fields for the processing of tokens on the destination chain.\\n  function lockOrBurn(\\n    Pool.LockOrBurnInV1 calldata lockOrBurnIn\\n  ) external returns (Pool.LockOrBurnOutV1 memory lockOrBurnOut);\\n\\n  /// @notice Releases or mints tokens to the receiver address.\\n  /// @param releaseOrMintIn All data required to release or mint tokens.\\n  /// @return releaseOrMintOut The amount of tokens released or minted on the local chain, denominated\\n  /// in the local token's decimals.\\n  /// @dev The offramp asserts that the balanceOf of the receiver has been incremented by exactly the number\\n  /// of tokens that is returned in ReleaseOrMintOutV1.destinationAmount. If the amounts do not match, the tx reverts.\\n  function releaseOrMint(\\n    Pool.ReleaseOrMintInV1 calldata releaseOrMintIn\\n  ) external returns (Pool.ReleaseOrMintOutV1 memory);\\n\\n  /// @notice Checks whether a remote chain is supported in the token pool.\\n  /// @param remoteChainSelector The selector of the remote chain.\\n  /// @return true if the given chain is a permissioned remote chain.\\n  function isSupportedChain(\\n    uint64 remoteChainSelector\\n  ) external view returns (bool);\\n\\n  /// @notice Returns if the token pool supports the given token.\\n  /// @param token The address of the token.\\n  /// @return true if the token is supported by the pool.\\n  function isSupportedToken(\\n    address token\\n  ) external view returns (bool);\\n}\\n\"},\"contracts/interfaces/ITokenAdminRegistry.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.0;\\n\\ninterface ITokenAdminRegistry {\\n  /// @notice Returns the pool for the given token.\\n  function getPool(\\n    address token\\n  ) external view returns (address);\\n\\n  /// @notice Proposes an administrator for the given token as pending administrator.\\n  /// @param localToken The token to register the administrator for.\\n  /// @param administrator The administrator to register.\\n  function proposeAdministrator(address localToken, address administrator) external;\\n\\n  /// @notice Accepts the administrator role for a token.\\n  /// @param localToken The token to accept the administrator role for.\\n  /// @dev This function can only be called by the pending administrator.\\n  function acceptAdminRole(\\n    address localToken\\n  ) external;\\n\\n  /// @notice Sets the pool for a token. Setting the pool to address(0) effectively delists the token\\n  /// from CCIP. Setting the pool to any other address enables the token on CCIP.\\n  /// @param localToken The token to set the pool for.\\n  /// @param pool The pool to set for the token.\\n  function setPool(address localToken, address pool) external;\\n\\n  /// @notice Transfers the administrator role for a token to a new address with a 2-step process.\\n  /// @param localToken The token to transfer the administrator role for.\\n  /// @param newAdmin The address to transfer the administrator role to. Can be address(0) to cancel\\n  /// a pending transfer.\\n  /// @dev The new admin must call `acceptAdminRole` to accept the role.\\n  function transferAdminRole(address localToken, address newAdmin) external;\\n}\\n\"},\"contracts/libraries/Pool.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n/// @notice This library contains various token pool functions to aid constructing the return data.\\nlibrary Pool {\\n  // The tag used to signal support for the pool v1 standard.\\n  // bytes4(keccak256(\\\"CCIP_POOL_V1\\\"))\\n  bytes4 public constant CCIP_POOL_V1 = 0xaff2afbf;\\n\\n  // The number of bytes in the return data for a pool v1 releaseOrMint call.\\n  // This should match the size of the ReleaseOrMintOutV1 struct.\\n  uint16 public constant CCIP_POOL_V1_RET_BYTES = 32;\\n\\n  // The default max number of bytes in the return data for a pool v1 lockOrBurn call.\\n  // This data can be used to send information to the destination chain token pool. Can be overwritten\\n  // in the TokenTransferFeeConfig.destBytesOverhead if more data is required.\\n  uint32 public constant CCIP_LOCK_OR_BURN_V1_RET_BYTES = 32;\\n\\n  struct LockOrBurnInV1 {\\n    bytes receiver; //  The recipient of the tokens on the destination chain, abi encoded.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the destination chain.\\n    address originalSender; // ─────╯ The original sender of the tx on the source chain.\\n    uint256 amount; //  The amount of tokens to lock or burn, denominated in the source token's decimals.\\n    address localToken; //  The address on this chain of the token to lock or burn.\\n  }\\n\\n  struct LockOrBurnOutV1 {\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes destPoolData;\\n  }\\n\\n  struct ReleaseOrMintInV1 {\\n    bytes originalSender; //          The original sender of the tx on the source chain.\\n    uint64 remoteChainSelector; // ─╮ The chain ID of the source chain.\\n    address receiver; // ───────────╯ The recipient of the tokens on the destination chain.\\n    uint256 amount; //                The amount of tokens to release or mint, denominated in the source token's decimals.\\n    address localToken; //            The address on this chain of the token to release or mint.\\n    /// @dev WARNING: sourcePoolAddress should be checked prior to any processing of funds. Make sure it matches the\\n    /// expected pool address for the given remoteChainSelector.\\n    bytes sourcePoolAddress; //       The address of the source pool, abi encoded in the case of EVM chains.\\n    bytes sourcePoolData; //          The data received from the source pool to process the release or mint.\\n    /// @dev WARNING: offchainTokenData is untrusted data.\\n    bytes offchainTokenData; //       The offchain data to process the release or mint.\\n  }\\n\\n  struct ReleaseOrMintOutV1 {\\n    // The number of tokens released or minted on the destination chain, denominated in the local token's decimals.\\n    // This value is expected to be equal to the ReleaseOrMintInV1.amount in the case where the source and destination\\n    // chain have the same number of decimals.\\n    uint256 destinationAmount;\\n  }\\n}\\n\"},\"contracts/tokenAdminRegistry/TokenAdminRegistry.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IPoolV1} from \\\"../interfaces/IPool.sol\\\";\\nimport {ITokenAdminRegistry} from \\\"../interfaces/ITokenAdminRegistry.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {Ownable2StepMsgSender} from \\\"@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\\\";\\n\\nimport {EnumerableSet} from\\n  \\\"@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @notice This contract stores the token pool configuration for all CCIP enabled tokens. It works\\n/// on a self-serve basis, where tokens can be registered without intervention from the CCIP owner.\\n/// @dev This contract is not considered upgradable, as it is a customer facing contract that will store\\n/// significant amounts of data.\\ncontract TokenAdminRegistry is ITokenAdminRegistry, ITypeAndVersion, Ownable2StepMsgSender {\\n  using EnumerableSet for EnumerableSet.AddressSet;\\n\\n  error OnlyRegistryModuleOrOwner(address sender);\\n  error OnlyAdministrator(address sender, address token);\\n  error OnlyPendingAdministrator(address sender, address token);\\n  error AlreadyRegistered(address token);\\n  error ZeroAddress();\\n  error InvalidTokenPoolToken(address token);\\n\\n  event PoolSet(address indexed token, address indexed previousPool, address indexed newPool);\\n  event AdministratorTransferRequested(address indexed token, address indexed currentAdmin, address indexed newAdmin);\\n  event AdministratorTransferred(address indexed token, address indexed newAdmin);\\n  event RegistryModuleAdded(address module);\\n  event RegistryModuleRemoved(address indexed module);\\n\\n  // The struct is packed in a way that optimizes the attributes that are accessed together.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct TokenConfig {\\n    address administrator; // the current administrator of the token\\n    address pendingAdministrator; // the address that is pending to become the new administrator\\n    address tokenPool; // the token pool for this token. Can be address(0) if not deployed or not configured.\\n  }\\n\\n  string public constant override typeAndVersion = \\\"TokenAdminRegistry 1.5.0\\\";\\n\\n  // Mapping of token address to token configuration\\n  mapping(address token =\\u003e TokenConfig) internal s_tokenConfig;\\n\\n  // All tokens that have been configured\\n  EnumerableSet.AddressSet internal s_tokens;\\n\\n  // Registry modules are allowed to register administrators for tokens\\n  EnumerableSet.AddressSet internal s_registryModules;\\n\\n  /// @notice Returns all pools for the given tokens.\\n  /// @dev Will return address(0) for tokens that do not have a pool.\\n  function getPools(\\n    address[] calldata tokens\\n  ) external view returns (address[] memory) {\\n    address[] memory pools = new address[](tokens.length);\\n    for (uint256 i = 0; i \\u003c tokens.length; ++i) {\\n      pools[i] = s_tokenConfig[tokens[i]].tokenPool;\\n    }\\n    return pools;\\n  }\\n\\n  /// @inheritdoc ITokenAdminRegistry\\n  function getPool(\\n    address token\\n  ) external view returns (address) {\\n    return s_tokenConfig[token].tokenPool;\\n  }\\n\\n  /// @notice Returns the configuration for a token.\\n  /// @param token The token to get the configuration for.\\n  /// @return config The configuration for the token.\\n  function getTokenConfig(\\n    address token\\n  ) external view returns (TokenConfig memory) {\\n    return s_tokenConfig[token];\\n  }\\n\\n  /// @notice Returns a list of tokens that are configured in the token admin registry.\\n  /// @param startIndex Starting index in list, can be 0 if you want to start from the beginning.\\n  /// @param maxCount Maximum number of tokens to retrieve. Since the list can be large,\\n  /// it is recommended to use a paging mechanism to retrieve all tokens. If querying for very\\n  /// large lists, RPCs can time out. If you want all tokens, use type(uint64).max.\\n  /// @return tokens List of configured tokens.\\n  /// @dev The function is paginated to avoid RPC timeouts.\\n  /// @dev The ordering is guaranteed to remain the same as it is not possible to remove tokens\\n  /// from s_tokens.\\n  function getAllConfiguredTokens(uint64 startIndex, uint64 maxCount) external view returns (address[] memory tokens) {\\n    uint256 numberOfTokens = s_tokens.length();\\n    if (startIndex \\u003e= numberOfTokens) {\\n      return tokens;\\n    }\\n    uint256 count = maxCount;\\n    if (count + startIndex \\u003e numberOfTokens) {\\n      count = numberOfTokens - startIndex;\\n    }\\n    tokens = new address[](count);\\n    for (uint256 i = 0; i \\u003c count; ++i) {\\n      tokens[i] = s_tokens.at(startIndex + i);\\n    }\\n\\n    return tokens;\\n  }\\n\\n  // ================================================================\\n  // │                  Administrator functions                     │\\n  // ================================================================\\n\\n  /// @notice Sets the pool for a token. Setting the pool to address(0) effectively delists the token\\n  /// from CCIP. Setting the pool to any other address enables the token on CCIP.\\n  /// @param localToken The token to set the pool for.\\n  /// @param pool The pool to set for the token.\\n  function setPool(address localToken, address pool) external onlyTokenAdmin(localToken) {\\n    // The pool has to support the token, but we want to allow removing the pool, so we only check\\n    // if the pool supports the token if it is not address(0).\\n    if (pool != address(0) \\u0026\\u0026 !IPoolV1(pool).isSupportedToken(localToken)) {\\n      revert InvalidTokenPoolToken(localToken);\\n    }\\n\\n    TokenConfig storage config = s_tokenConfig[localToken];\\n\\n    address previousPool = config.tokenPool;\\n    config.tokenPool = pool;\\n\\n    if (previousPool != pool) {\\n      emit PoolSet(localToken, previousPool, pool);\\n    }\\n  }\\n\\n  /// @notice Transfers the administrator role for a token to a new address with a 2-step process.\\n  /// @param localToken The token to transfer the administrator role for.\\n  /// @param newAdmin The address to transfer the administrator role to. Can be address(0) to cancel\\n  /// a pending transfer.\\n  /// @dev The new admin must call `acceptAdminRole` to accept the role.\\n  function transferAdminRole(address localToken, address newAdmin) external onlyTokenAdmin(localToken) {\\n    TokenConfig storage config = s_tokenConfig[localToken];\\n    config.pendingAdministrator = newAdmin;\\n\\n    emit AdministratorTransferRequested(localToken, msg.sender, newAdmin);\\n  }\\n\\n  /// @notice Accepts the administrator role for a token.\\n  /// @param localToken The token to accept the administrator role for.\\n  /// @dev This function can only be called by the pending administrator.\\n  function acceptAdminRole(\\n    address localToken\\n  ) external {\\n    TokenConfig storage config = s_tokenConfig[localToken];\\n    if (config.pendingAdministrator != msg.sender) {\\n      revert OnlyPendingAdministrator(msg.sender, localToken);\\n    }\\n\\n    config.administrator = msg.sender;\\n    config.pendingAdministrator = address(0);\\n\\n    emit AdministratorTransferred(localToken, msg.sender);\\n  }\\n\\n  // ================================================================\\n  // │                    Administrator config                      │\\n  // ================================================================\\n\\n  /// @notice Public getter to check for permissions of an administrator\\n  function isAdministrator(address localToken, address administrator) external view returns (bool) {\\n    return s_tokenConfig[localToken].administrator == administrator;\\n  }\\n\\n  /// @inheritdoc ITokenAdminRegistry\\n  /// @dev Can only be called by a registry module.\\n  function proposeAdministrator(address localToken, address administrator) external {\\n    if (!isRegistryModule(msg.sender) \\u0026\\u0026 msg.sender != owner()) {\\n      revert OnlyRegistryModuleOrOwner(msg.sender);\\n    }\\n    if (administrator == address(0)) {\\n      revert ZeroAddress();\\n    }\\n    TokenConfig storage config = s_tokenConfig[localToken];\\n\\n    if (config.administrator != address(0)) {\\n      revert AlreadyRegistered(localToken);\\n    }\\n\\n    config.pendingAdministrator = administrator;\\n\\n    // We don't care if it's already in the set, as it's a no-op.\\n    s_tokens.add(localToken);\\n\\n    emit AdministratorTransferRequested(localToken, address(0), administrator);\\n  }\\n\\n  // ================================================================\\n  // │                      Registry Modules                        │\\n  // ================================================================\\n\\n  /// @notice Checks if an address is a registry module.\\n  /// @param module The address to check.\\n  /// @return True if the address is a registry module, false otherwise.\\n  function isRegistryModule(\\n    address module\\n  ) public view returns (bool) {\\n    return s_registryModules.contains(module);\\n  }\\n\\n  /// @notice Adds a new registry module to the list of allowed modules.\\n  /// @param module The module to add.\\n  function addRegistryModule(\\n    address module\\n  ) external onlyOwner {\\n    if (s_registryModules.add(module)) {\\n      emit RegistryModuleAdded(module);\\n    }\\n  }\\n\\n  /// @notice Removes a registry module from the list of allowed modules.\\n  /// @param module The module to remove.\\n  function removeRegistryModule(\\n    address module\\n  ) external onlyOwner {\\n    if (s_registryModules.remove(module)) {\\n      emit RegistryModuleRemoved(module);\\n    }\\n  }\\n\\n  // ================================================================\\n  // │                           Access                             │\\n  // ================================================================\\n\\n  /// @notice Checks if an address is the administrator of the given token.\\n  modifier onlyTokenAdmin(\\n    address token\\n  ) {\\n    if (s_tokenConfig[token].administrator != msg.sender) {\\n      revert OnlyAdministrator(msg.sender, token);\\n    }\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/introspection/IERC165.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/introspection/IERC165.sol)\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Interface of the ERC165 standard, as defined in the\\n * https://eips.ethereum.org/EIPS/eip-165[EIP].\\n *\\n * Implementers can declare support of contract interfaces, which can then be\\n * queried by others ({ERC165Checker}).\\n *\\n * For an implementation, see {ERC165}.\\n */\\ninterface IERC165 {\\n    /**\\n     * @dev Returns true if this contract implements the interface defined by\\n     * `interfaceId`. See the corresponding\\n     * https://eips.ethereum.org/EIPS/eip-165#how-interfaces-are-identified[EIP section]\\n     * to learn more about how these ids are created.\\n     *\\n     * This function call must use less than 30 000 gas.\\n     */\\n    function supportsInterface(bytes4 interfaceId) external view returns (bool);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v5.0.2/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v5.0.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.20;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```solidity\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n    // To implement this library for multiple types with as little code\\n    // repetition as possible, we write it in terms of a generic Set type with\\n    // bytes32 values.\\n    // The Set implementation uses private functions, and user-facing\\n    // implementations (such as AddressSet) are just wrappers around the\\n    // underlying Set.\\n    // This means that we can only create new EnumerableSets for types that fit\\n    // in bytes32.\\n\\n    struct Set {\\n        // Storage of set values\\n        bytes32[] _values;\\n        // Position is the index of the value in the `values` array plus 1.\\n        // Position 0 is used to mean a value is not in the set.\\n        mapping(bytes32 value =\\u003e uint256) _positions;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function _add(Set storage set, bytes32 value) private returns (bool) {\\n        if (!_contains(set, value)) {\\n            set._values.push(value);\\n            // The value is stored at length-1, but we add 1 to all indexes\\n            // and use 0 as a sentinel value\\n            set._positions[value] = set._values.length;\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function _remove(Set storage set, bytes32 value) private returns (bool) {\\n        // We cache the value's position to prevent multiple reads from the same storage slot\\n        uint256 position = set._positions[value];\\n\\n        if (position != 0) {\\n            // Equivalent to contains(set, value)\\n            // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n            // the array, and then remove the last element (sometimes called as 'swap and pop').\\n            // This modifies the order of the array, as noted in {at}.\\n\\n            uint256 valueIndex = position - 1;\\n            uint256 lastIndex = set._values.length - 1;\\n\\n            if (valueIndex != lastIndex) {\\n                bytes32 lastValue = set._values[lastIndex];\\n\\n                // Move the lastValue to the index where the value to delete is\\n                set._values[valueIndex] = lastValue;\\n                // Update the tracked position of the lastValue (that was just moved)\\n                set._positions[lastValue] = position;\\n            }\\n\\n            // Delete the slot where the moved value was stored\\n            set._values.pop();\\n\\n            // Delete the tracked position for the deleted slot\\n            delete set._positions[value];\\n\\n            return true;\\n        } else {\\n            return false;\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n        return set._positions[value] != 0;\\n    }\\n\\n    /**\\n     * @dev Returns the number of values on the set. O(1).\\n     */\\n    function _length(Set storage set) private view returns (uint256) {\\n        return set._values.length;\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n        return set._values[index];\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function _values(Set storage set) private view returns (bytes32[] memory) {\\n        return set._values;\\n    }\\n\\n    // Bytes32Set\\n\\n    struct Bytes32Set {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _add(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n        return _remove(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n        return _contains(set._inner, value);\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(Bytes32Set storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n        return _at(set._inner, index);\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        bytes32[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // AddressSet\\n\\n    struct AddressSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(AddressSet storage set, address value) internal returns (bool) {\\n        return _add(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(AddressSet storage set, address value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(AddressSet storage set, address value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(AddressSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n        return address(uint160(uint256(_at(set._inner, index))));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(AddressSet storage set) internal view returns (address[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        address[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n\\n    // UintSet\\n\\n    struct UintSet {\\n        Set _inner;\\n    }\\n\\n    /**\\n     * @dev Add a value to a set. O(1).\\n     *\\n     * Returns true if the value was added to the set, that is if it was not\\n     * already present.\\n     */\\n    function add(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _add(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the value was removed from the set, that is if it was\\n     * present.\\n     */\\n    function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n        return _remove(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns true if the value is in the set. O(1).\\n     */\\n    function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n        return _contains(set._inner, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Returns the number of values in the set. O(1).\\n     */\\n    function length(UintSet storage set) internal view returns (uint256) {\\n        return _length(set._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the value stored at position `index` in the set. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n        return uint256(_at(set._inner, index));\\n    }\\n\\n    /**\\n     * @dev Return the entire set in an array\\n     *\\n     * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n     * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n     * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n     * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n     */\\n    function values(UintSet storage set) internal view returns (uint256[] memory) {\\n        bytes32[] memory store = _values(set._inner);\\n        uint256[] memory result;\\n\\n        /// @solidity memory-safe-assembly\\n        assembly {\\n            result := store\\n        }\\n\\n        return result;\\n    }\\n}\\n\"}}}"
