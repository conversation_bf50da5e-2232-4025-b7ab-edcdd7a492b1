// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package rmn_home

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/rmn/RMNHome.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/rmn/RMNHome.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {Ownable2StepMsgSender} from \\\"@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\\\";\\n\\n/// @notice Stores the home configuration for RMN, that is referenced by CCIP oracles, RMN nodes, and the RMNRemote\\n/// contracts.\\n/// @dev This contract is a state machine with the following states:\\n/// - Init: The initial state of the contract, no config has been set, or all configs have been revoked.\\n///   [0, 0]\\n///\\n/// - Candidate: A new config has been set, but it has not been promoted yet, or all active configs have been revoked.\\n///   [0, 1]\\n///\\n/// - Active: A non-zero config has been promoted and is active, there is no candidate configured.\\n///   [1, 0]\\n///\\n/// - ActiveAndCandidate: A non-zero config has been promoted and is active, and a new config has been set as candidate.\\n///   [1, 1]\\n///\\n/// The following state transitions are allowed:\\n/// - Init -\\u003e Candidate: setCandidate()\\n/// - Candidate -\\u003e Active: promoteCandidateAndRevokeActive()\\n/// - Candidate -\\u003e Candidate: setCandidate()\\n/// - Candidate -\\u003e Init: revokeCandidate()\\n/// - Active -\\u003e ActiveAndCandidate: setCandidate()\\n/// - Active -\\u003e Init: promoteCandidateAndRevokeActive()\\n/// - ActiveAndCandidate -\\u003e Active: promoteCandidateAndRevokeActive()\\n/// - ActiveAndCandidate -\\u003e Active: revokeCandidate()\\n/// - ActiveAndCandidate -\\u003e ActiveAndCandidate: setCandidate()\\n///\\n/// This means the following calls are not allowed at the following states:\\n/// - Init: promoteCandidateAndRevokeActive(), as there is no config to promote.\\n/// - Init: revokeCandidate(), as there is no config to revoke\\n/// - Active: revokeCandidate(), as there is no candidate to revoke\\n/// Note that we explicitly do allow promoteCandidateAndRevokeActive() to be called when there is an active config but\\n/// no candidate config. This is the only way to remove the active config. The alternative would be to set some unusable\\n/// config as candidate and promote that, but fully clearing it is cleaner.\\n///\\n///       ┌─────────────┐   setCandidate     ┌─────────────┐\\n///       │             ├───────────────────►│             │ setCandidate\\n///       │    Init     │   revokeCandidate  │  Candidate  │◄───────────┐\\n///       │    [0,0]    │◄───────────────────┤    [0,1]    │────────────┘\\n///       │             │  ┌─────────────────┤             │\\n///       └─────────────┘  │  promote-       └─────────────┘\\n///                  ▲     │  Candidate\\n///        promote-  │     │\\n///        Candidate │     │\\n///                  │     │\\n///       ┌──────────┴──┐  │  promote-       ┌─────────────┐\\n///       │             │◄─┘  Candidate OR   │  Active \\u0026   │ setCandidate\\n///       │    Active   │    revokeCandidate │  Candidate  │◄───────────┐\\n///       │    [1,0]    │◄───────────────────┤    [1,1]    │────────────┘\\n///       │             ├───────────────────►│             │\\n///       └─────────────┘    setCandidate    └─────────────┘\\n///\\ncontract RMNHome is Ownable2StepMsgSender, ITypeAndVersion {\\n  event ConfigSet(bytes32 indexed configDigest, uint32 version, StaticConfig staticConfig, DynamicConfig dynamicConfig);\\n  event ActiveConfigRevoked(bytes32 indexed configDigest);\\n  event CandidateConfigRevoked(bytes32 indexed configDigest);\\n  event DynamicConfigSet(bytes32 indexed configDigest, DynamicConfig dynamicConfig);\\n  event ConfigPromoted(bytes32 indexed configDigest);\\n\\n  error OutOfBoundsNodesLength();\\n  error DuplicatePeerId();\\n  error DuplicateOffchainPublicKey();\\n  error DuplicateSourceChain();\\n  error OutOfBoundsObserverNodeIndex();\\n  error NotEnoughObservers();\\n  error ConfigDigestMismatch(bytes32 expectedConfigDigest, bytes32 gotConfigDigest);\\n  error DigestNotFound(bytes32 configDigest);\\n  error RevokingZeroDigestNotAllowed();\\n  error NoOpStateTransitionNotAllowed();\\n\\n  struct Node {\\n    bytes32 peerId; //            Used for p2p communication.\\n    bytes32 offchainPublicKey; // Observations are signed with this public key, and are only verified offchain.\\n  }\\n\\n  struct SourceChain {\\n    uint64 chainSelector; // ─╮ The Source chain selector.\\n    uint64 fObserve; // ──────╯ Maximum number of faulty observers; f+1 observers required to agree on an observation for this source chain.\\n    uint256 observerNodesBitmap; // ObserverNodesBitmap \\u0026 (1\\u003c\\u003ci) == (1\\u003c\\u003ci) iff StaticConfig.nodes[i] is an observer for this source chain.\\n  }\\n\\n  struct StaticConfig {\\n    // No sorting requirement for nodes, but ensure that SourceChain.observerNodesBitmap in the home chain config \\u0026\\n    // Signer.nodeIndex in the remote chain configs are appropriately updated when changing this field.\\n    Node[] nodes;\\n    bytes offchainConfig; // Offchain configuration for RMN nodes.\\n  }\\n\\n  struct DynamicConfig {\\n    // No sorting requirement for source chains, it is most gas efficient to append new source chains to the right.\\n    SourceChain[] sourceChains;\\n    bytes offchainConfig; // Offchain configuration for RMN nodes.\\n  }\\n\\n  /// @notice The main struct stored in the contract, containing the static and dynamic parts of the config as well as\\n  /// the version and the digest of the config.\\n  struct VersionedConfig {\\n    uint32 version;\\n    bytes32 configDigest;\\n    StaticConfig staticConfig;\\n    DynamicConfig dynamicConfig;\\n  }\\n\\n  string public constant override typeAndVersion = \\\"RMNHome 1.6.0\\\";\\n\\n  /// @notice Used for encoding the config digest prefix, unique per Home contract implementation.\\n  uint256 private constant PREFIX = 0x000b \\u003c\\u003c (256 - 16); // 0x000b00..00.\\n  /// @notice Used for encoding the config digest prefix\\n  uint256 private constant PREFIX_MASK = type(uint256).max \\u003c\\u003c (256 - 16); // 0xFFFF00..00.\\n  /// @notice The max number of configs that can be active at the same time.\\n  uint256 private constant MAX_CONCURRENT_CONFIGS = 2;\\n  /// @notice Helper to identify the zero config digest with less casting.\\n  bytes32 private constant ZERO_DIGEST = bytes32(uint256(0));\\n  /// @notice To ensure that observerNodesBitmap can be bit-encoded into a uint256.\\n  uint256 private constant MAX_NODES = 256;\\n\\n  /// @notice This array holds the configs.\\n  /// @dev Value i in this array is valid iff s_configs[i].configDigest != 0.\\n  VersionedConfig[MAX_CONCURRENT_CONFIGS] private s_configs;\\n\\n  /// @notice The latest version set, incremented by one for each new config.\\n  uint32 private s_currentVersion = 0;\\n  /// @notice The index of the active config. Used to determine which config is active. Adding the configs to a list\\n  /// with two items and using this index to determine which one is active is a gas efficient way to handle this. Having\\n  /// a set place for the active config would mean we have to copy the candidate config to the active config when it is\\n  /// promoted, which would be more expensive. This index allows us to flip the configs around using `XOR 1`, which\\n  /// flips 0 to 1 and 1 to 0.\\n  uint32 private s_activeConfigIndex = 0;\\n\\n  // ================================================================\\n  // │                          Getters                             │\\n  // ================================================================\\n\\n  /// @notice Returns the current active and candidate config digests.\\n  /// @dev Can be bytes32(0) if no config has been set yet or it has been revoked.\\n  /// @return activeConfigDigest The digest of the active config.\\n  /// @return candidateConfigDigest The digest of the candidate config.\\n  function getConfigDigests() external view returns (bytes32 activeConfigDigest, bytes32 candidateConfigDigest) {\\n    return (s_configs[_getActiveIndex()].configDigest, s_configs[_getCandidateIndex()].configDigest);\\n  }\\n\\n  /// @notice Returns the active config digest.\\n  function getActiveDigest() external view returns (bytes32) {\\n    return s_configs[_getActiveIndex()].configDigest;\\n  }\\n\\n  /// @notice Returns the candidate config digest.\\n  function getCandidateDigest() public view returns (bytes32) {\\n    return s_configs[_getCandidateIndex()].configDigest;\\n  }\\n\\n  /// @notice The offchain code can use this to fetch an old config which might still be in use by some remotes. Use\\n  /// in case one of the configs is too large to be returnable by one of the other getters.\\n  /// @param configDigest The digest of the config to fetch.\\n  /// @return versionedConfig The config and its version.\\n  /// @return ok True if the config was found, false otherwise.\\n  function getConfig(\\n    bytes32 configDigest\\n  ) external view returns (VersionedConfig memory versionedConfig, bool ok) {\\n    for (uint256 i = 0; i \\u003c MAX_CONCURRENT_CONFIGS; ++i) {\\n      // We never want to return true for a zero digest, even if the caller is asking for it, as this can expose old\\n      // config state that is invalid.\\n      if (s_configs[i].configDigest == configDigest \\u0026\\u0026 configDigest != ZERO_DIGEST) {\\n        return (s_configs[i], true);\\n      }\\n    }\\n    return (versionedConfig, false);\\n  }\\n\\n  function getAllConfigs()\\n    external\\n    view\\n    returns (VersionedConfig memory activeConfig, VersionedConfig memory candidateConfig)\\n  {\\n    VersionedConfig memory storedActiveConfig = s_configs[_getActiveIndex()];\\n    if (storedActiveConfig.configDigest != ZERO_DIGEST) {\\n      activeConfig = storedActiveConfig;\\n    }\\n\\n    VersionedConfig memory storedCandidateConfig = s_configs[_getCandidateIndex()];\\n    if (storedCandidateConfig.configDigest != ZERO_DIGEST) {\\n      candidateConfig = storedCandidateConfig;\\n    }\\n\\n    return (activeConfig, candidateConfig);\\n  }\\n\\n  // ================================================================\\n  // │                     State transitions                        │\\n  // ================================================================\\n\\n  /// @notice Sets a new config as the candidate config. Does not influence the active config.\\n  /// @param staticConfig The static part of the config.\\n  /// @param dynamicConfig The dynamic part of the config.\\n  /// @param digestToOverwrite The digest of the config to overwrite, or ZERO_DIGEST if no config is to be overwritten.\\n  /// This is done to prevent accidental overwrites.\\n  /// @return newConfigDigest The digest of the new config.\\n  function setCandidate(\\n    StaticConfig calldata staticConfig,\\n    DynamicConfig calldata dynamicConfig,\\n    bytes32 digestToOverwrite\\n  ) external onlyOwner returns (bytes32 newConfigDigest) {\\n    _validateStaticAndDynamicConfig(staticConfig, dynamicConfig);\\n\\n    bytes32 existingDigest = getCandidateDigest();\\n\\n    if (existingDigest != digestToOverwrite) {\\n      revert ConfigDigestMismatch(existingDigest, digestToOverwrite);\\n    }\\n\\n    // are we going to overwrite a config? If so, emit an event.\\n    if (existingDigest != ZERO_DIGEST) {\\n      emit CandidateConfigRevoked(digestToOverwrite);\\n    }\\n\\n    uint32 newVersion = ++s_currentVersion;\\n    newConfigDigest = _calculateConfigDigest(abi.encode(staticConfig), newVersion);\\n\\n    VersionedConfig storage existingConfig = s_configs[_getCandidateIndex()];\\n    existingConfig.configDigest = newConfigDigest;\\n    existingConfig.version = newVersion;\\n    existingConfig.staticConfig = staticConfig;\\n    existingConfig.dynamicConfig = dynamicConfig;\\n\\n    emit ConfigSet(newConfigDigest, newVersion, staticConfig, dynamicConfig);\\n\\n    return newConfigDigest;\\n  }\\n\\n  /// @notice Revokes a specific config by digest. This is used when the candidate config turns out to be incorrect to\\n  /// remove it without it ever having to be promoted. It's also possible to revoke the candidate config by setting a\\n  /// newer candidate config using `setCandidate`.\\n  /// @param configDigest The digest of the config to revoke. This is done to prevent accidental revokes.\\n  function revokeCandidate(\\n    bytes32 configDigest\\n  ) external onlyOwner {\\n    if (configDigest == ZERO_DIGEST) {\\n      revert RevokingZeroDigestNotAllowed();\\n    }\\n\\n    uint256 candidateConfigIndex = _getCandidateIndex();\\n    if (s_configs[candidateConfigIndex].configDigest != configDigest) {\\n      revert ConfigDigestMismatch(s_configs[candidateConfigIndex].configDigest, configDigest);\\n    }\\n\\n    emit CandidateConfigRevoked(configDigest);\\n    // Delete only the digest, as that's what's used to determine if a config is active. This means the actual\\n    // config stays in storage which should significantly reduce the gas cost of overwriting that storage space in\\n    // the future.\\n    delete s_configs[candidateConfigIndex].configDigest;\\n  }\\n\\n  /// @notice Promotes the candidate config to the active config and revokes the active config.\\n  /// @param digestToPromote The digest of the config to promote.\\n  /// @param digestToRevoke The digest of the config to revoke.\\n  /// @dev No config is changed in storage, the only storage changes that happen are\\n  /// - The activeConfigIndex is flipped.\\n  /// - The digest of the old active config is deleted.\\n  function promoteCandidateAndRevokeActive(bytes32 digestToPromote, bytes32 digestToRevoke) external onlyOwner {\\n    if (digestToPromote == ZERO_DIGEST \\u0026\\u0026 digestToRevoke == ZERO_DIGEST) {\\n      revert NoOpStateTransitionNotAllowed();\\n    }\\n\\n    uint256 candidateConfigIndex = _getCandidateIndex();\\n    if (s_configs[candidateConfigIndex].configDigest != digestToPromote) {\\n      revert ConfigDigestMismatch(s_configs[candidateConfigIndex].configDigest, digestToPromote);\\n    }\\n\\n    VersionedConfig storage activeConfig = s_configs[_getActiveIndex()];\\n    if (activeConfig.configDigest != digestToRevoke) {\\n      revert ConfigDigestMismatch(activeConfig.configDigest, digestToRevoke);\\n    }\\n\\n    delete activeConfig.configDigest;\\n\\n    s_activeConfigIndex ^= 1;\\n    if (digestToRevoke != ZERO_DIGEST) {\\n      emit ActiveConfigRevoked(digestToRevoke);\\n    }\\n\\n    emit ConfigPromoted(digestToPromote);\\n  }\\n\\n  /// @notice Sets the dynamic config for a specific config.\\n  /// @param newDynamicConfig The new dynamic config.\\n  /// @param currentDigest The digest of the config to update.\\n  /// @dev This does not update the config digest as only the static config is part of the digest.\\n  function setDynamicConfig(DynamicConfig calldata newDynamicConfig, bytes32 currentDigest) external onlyOwner {\\n    for (uint256 i = 0; i \\u003c MAX_CONCURRENT_CONFIGS; ++i) {\\n      if (s_configs[i].configDigest == currentDigest \\u0026\\u0026 currentDigest != ZERO_DIGEST) {\\n        _validateDynamicConfig(newDynamicConfig, s_configs[i].staticConfig.nodes.length);\\n        // Since the static config doesn't change we don't have to update the digest or version.\\n        s_configs[i].dynamicConfig = newDynamicConfig;\\n\\n        emit DynamicConfigSet(currentDigest, newDynamicConfig);\\n        return;\\n      }\\n    }\\n\\n    revert DigestNotFound(currentDigest);\\n  }\\n\\n  /// @notice Calculates the config digest for a given plugin key, static config, and version.\\n  /// @param staticConfig The static part of the config.\\n  /// @param version The version of the config.\\n  /// @return The calculated config digest.\\n  function _calculateConfigDigest(bytes memory staticConfig, uint32 version) internal view returns (bytes32) {\\n    return bytes32(\\n      PREFIX\\n        | (\\n          uint256(\\n            keccak256(bytes.concat(abi.encode(bytes32(\\\"EVM\\\"), block.chainid, address(this), version), staticConfig))\\n          ) \\u0026 ~PREFIX_MASK\\n        )\\n    );\\n  }\\n\\n  function _getActiveIndex() private view returns (uint32) {\\n    return s_activeConfigIndex;\\n  }\\n\\n  function _getCandidateIndex() private view returns (uint32) {\\n    return s_activeConfigIndex ^ 1;\\n  }\\n\\n  // ================================================================\\n  // │                         Validation                           │\\n  // ================================================================\\n\\n  /// @notice Validates the static and dynamic config. Reverts when the config is invalid.\\n  /// @param staticConfig The static part of the config.\\n  /// @param dynamicConfig The dynamic part of the config.\\n  function _validateStaticAndDynamicConfig(\\n    StaticConfig memory staticConfig,\\n    DynamicConfig memory dynamicConfig\\n  ) internal pure {\\n    // Ensure that observerNodesBitmap can be bit-encoded into a uint256.\\n    if (staticConfig.nodes.length \\u003e MAX_NODES) {\\n      revert OutOfBoundsNodesLength();\\n    }\\n\\n    // Ensure no peerId or offchainPublicKey is duplicated.\\n    for (uint256 i = 0; i \\u003c staticConfig.nodes.length; ++i) {\\n      for (uint256 j = i + 1; j \\u003c staticConfig.nodes.length; ++j) {\\n        if (staticConfig.nodes[i].peerId == staticConfig.nodes[j].peerId) {\\n          revert DuplicatePeerId();\\n        }\\n        if (staticConfig.nodes[i].offchainPublicKey == staticConfig.nodes[j].offchainPublicKey) {\\n          revert DuplicateOffchainPublicKey();\\n        }\\n      }\\n    }\\n\\n    _validateDynamicConfig(dynamicConfig, staticConfig.nodes.length);\\n  }\\n\\n  /// @notice Validates the dynamic config. Reverts when the config is invalid.\\n  /// @param dynamicConfig The dynamic part of the config.\\n  /// @param numberOfNodes The number of nodes in the static config.\\n  function _validateDynamicConfig(DynamicConfig memory dynamicConfig, uint256 numberOfNodes) internal pure {\\n    uint256 numberOfSourceChains = dynamicConfig.sourceChains.length;\\n    for (uint256 i = 0; i \\u003c numberOfSourceChains; ++i) {\\n      SourceChain memory currentSourceChain = dynamicConfig.sourceChains[i];\\n      // Ensure the source chain is unique.\\n      for (uint256 j = i + 1; j \\u003c numberOfSourceChains; ++j) {\\n        if (currentSourceChain.chainSelector == dynamicConfig.sourceChains[j].chainSelector) {\\n          revert DuplicateSourceChain();\\n        }\\n      }\\n\\n      // all observer node indices are valid.\\n      uint256 bitmap = currentSourceChain.observerNodesBitmap;\\n      // Check if there are any bits set for indexes outside of the expected range.\\n      if (bitmap \\u0026 (type(uint256).max \\u003e\\u003e (256 - numberOfNodes)) != bitmap) {\\n        revert OutOfBoundsObserverNodeIndex();\\n      }\\n\\n      uint256 observersCount = 0;\\n      for (; bitmap != 0; ++observersCount) {\\n        bitmap \\u0026= bitmap - 1;\\n      }\\n\\n      // min observers are tenable.\\n      if (observersCount \\u003c 2 * currentSourceChain.fObserve + 1) {\\n        revert NotEnoughObservers();\\n      }\\n    }\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"}}}"
