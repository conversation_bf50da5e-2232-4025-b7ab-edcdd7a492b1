// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.


package multi_aggregate_rate_limiter

var SolidityStandardInput = "{\"version\":\"0.8.26\",\"language\":\"Solidity\",\"settings\":{\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"appendCBOR\":true,\"bytecodeHash\":\"none\",\"useLiteralContent\":false},\"optimizer\":{\"enabled\":true,\"runs\":80000},\"outputSelection\":{\"contracts/MultiAggregateRateLimiter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IFeeQuoter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IMessageInterceptor.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/interfaces/IPriceRegistry.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Client.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/Internal.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/MerkleMultiProof.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/RateLimiter.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"contracts/libraries/USDPriceWith18Decimals.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/AuthorizedCallers.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/enumerable/EnumerableMapAddresses.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/enumerable/EnumerableMapBytes32.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableMap.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\":{\"\":[\"ast\"],\"*\":[\"abi\",\"evm.bytecode.object\",\"evm.bytecode.sourceMap\",\"evm.bytecode.linkReferences\",\"evm.deployedBytecode.object\",\"evm.deployedBytecode.sourceMap\",\"evm.deployedBytecode.linkReferences\",\"evm.deployedBytecode.immutableReferences\",\"evm.methodIdentifiers\",\"metadata\"]}},\"remappings\":[\"forge-std/=node_modules/@chainlink/contracts/src/v0.8/vendor/forge-std/src/\",\"@chainlink/contracts/=node_modules/@chainlink/contracts/\"],\"viaIR\":true},\"sources\":{\"contracts/MultiAggregateRateLimiter.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.24;\\n\\nimport {IFeeQuoter} from \\\"./interfaces/IFeeQuoter.sol\\\";\\nimport {IMessageInterceptor} from \\\"./interfaces/IMessageInterceptor.sol\\\";\\nimport {ITypeAndVersion} from \\\"@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\\\";\\n\\nimport {Client} from \\\"./libraries/Client.sol\\\";\\nimport {RateLimiter} from \\\"./libraries/RateLimiter.sol\\\";\\nimport {USDPriceWith18Decimals} from \\\"./libraries/USDPriceWith18Decimals.sol\\\";\\nimport {AuthorizedCallers} from \\\"@chainlink/contracts/src/v0.8/shared/access/AuthorizedCallers.sol\\\";\\nimport {EnumerableMapAddresses} from \\\"@chainlink/contracts/src/v0.8/shared/enumerable/EnumerableMapAddresses.sol\\\";\\n\\n/// @notice The aggregate rate limiter is a wrapper of the token bucket rate limiter which permits rate limiting based\\n/// on the aggregate value of a group of token transfers , using a fee quoter to convert to a numeraire asset e.g. USD.\\n/// The contract is a standalone multi-lane message validator contract, which can be called by authorized ramp contracts\\n/// to apply rate limit changes to lanes, and revert when the rate limits get breached.\\ncontract MultiAggregateRateLimiter is IMessageInterceptor, AuthorizedCallers, ITypeAndVersion {\\n  using RateLimiter for RateLimiter.TokenBucket;\\n  using USDPriceWith18Decimals for uint224;\\n  using EnumerableMapAddresses for EnumerableMapAddresses.AddressToBytesMap;\\n\\n  error PriceNotFoundForToken(address token);\\n  error ZeroChainSelectorNotAllowed();\\n\\n  event RateLimiterConfigUpdated(uint64 indexed remoteChainSelector, bool isOutboundLane, RateLimiter.Config config);\\n  event FeeQuoterSet(address newFeeQuoter);\\n  event TokenAggregateRateLimitAdded(uint64 remoteChainSelector, bytes remoteToken, address localToken);\\n  event TokenAggregateRateLimitRemoved(uint64 remoteChainSelector, address localToken);\\n\\n  /// @notice LocalRateLimitToken struct containing the local token address with the remote chain selector.\\n  /// The struct is used for removals and updates, since the local -\\u003e remote token mappings are scoped per-chain.\\n  struct LocalRateLimitToken {\\n    uint64 remoteChainSelector; // ─╮ Remote chain selector for which to update the rate limit token mapping.\\n    address localToken; // ─────────╯ Token on the chain on which the multi-ARL is deployed.\\n  }\\n\\n  /// @notice RateLimitTokenArgs struct containing both the local and remote token addresses.\\n  struct RateLimitTokenArgs {\\n    LocalRateLimitToken localTokenArgs; // Local token update args scoped to one remote chain.\\n    bytes remoteToken; // Token on the remote chain, for OnRamp - dest, or OffRamp - source.\\n  }\\n\\n  /// @notice Update args for a single rate limiter config update.\\n  struct RateLimiterConfigArgs {\\n    uint64 remoteChainSelector; // ─╮ Remote chain selector to set config for.\\n    bool isOutboundLane; // ────────╯ If set to true, represents the outbound message lane (OnRamp), and the inbound message lane otherwise (OffRamp)\\n    RateLimiter.Config rateLimiterConfig; // Rate limiter config to set\\n  }\\n\\n  /// @notice Struct to store rate limit token buckets for both lane directions.\\n  struct RateLimiterBuckets {\\n    RateLimiter.TokenBucket inboundLaneBucket; // Bucket for the inbound lane (remote -\\u003e local).\\n    RateLimiter.TokenBucket outboundLaneBucket; // Bucket for the outbound lane (local -\\u003e remote).\\n  }\\n\\n  string public constant override typeAndVersion = \\\"MultiAggregateRateLimiter 1.6.0\\\";\\n\\n  bytes32 private constant EMPTY_ENCODED_ADDRESS_HASH = keccak256(abi.encode(address(0)));\\n\\n  /// @dev Tokens that should be included in Aggregate Rate Limiting (from local chain (this chain) -\\u003e remote),\\n  /// grouped per-remote chain.\\n  mapping(uint64 remoteChainSelector =\\u003e EnumerableMapAddresses.AddressToBytesMap tokensLocalToRemote) private\\n    s_rateLimitedTokensLocalToRemote;\\n\\n  /// @notice The address of the FeeQuoter used to query token values for rate limiting.\\n  address internal s_feeQuoter;\\n\\n  /// @notice Rate limiter token bucket states per chain, with separate buckets for inbound and outbound lanes.\\n  mapping(uint64 remoteChainSelector =\\u003e RateLimiterBuckets buckets) private s_rateLimitersByChainSelector;\\n\\n  /// @param feeQuoter the fee quoter to set.\\n  /// @param authorizedCallers the authorized callers to set.\\n  constructor(address feeQuoter, address[] memory authorizedCallers) AuthorizedCallers(authorizedCallers) {\\n    _setFeeQuoter(feeQuoter);\\n  }\\n\\n  /// @inheritdoc IMessageInterceptor\\n  function onInboundMessage(\\n    Client.Any2EVMMessage memory message\\n  ) external onlyAuthorizedCallers {\\n    _applyRateLimit(message.sourceChainSelector, message.destTokenAmounts, false);\\n  }\\n\\n  /// @inheritdoc IMessageInterceptor\\n  function onOutboundMessage(\\n    uint64 destChainSelector,\\n    Client.EVM2AnyMessage calldata message\\n  ) external onlyAuthorizedCallers {\\n    _applyRateLimit(destChainSelector, message.tokenAmounts, true);\\n  }\\n\\n  /// @notice Applies the rate limit to the token bucket if enabled.\\n  /// @param remoteChainSelector The remote chain selector.\\n  /// @param tokenAmounts The tokens and amounts to rate limit.\\n  /// @param isOutgoingLane if set to true, fetches the bucket for the outgoing message lane (OnRamp).\\n  function _applyRateLimit(\\n    uint64 remoteChainSelector,\\n    Client.EVMTokenAmount[] memory tokenAmounts,\\n    bool isOutgoingLane\\n  ) private {\\n    RateLimiter.TokenBucket storage tokenBucket = _getTokenBucket(remoteChainSelector, isOutgoingLane);\\n\\n    // Skip rate limiting if it is disabled.\\n    if (tokenBucket.isEnabled) {\\n      uint256 value;\\n      for (uint256 i = 0; i \\u003c tokenAmounts.length; ++i) {\\n        if (s_rateLimitedTokensLocalToRemote[remoteChainSelector].contains(tokenAmounts[i].token)) {\\n          value += _getTokenValue(tokenAmounts[i]);\\n        }\\n      }\\n      // Rate limit on aggregated token value.\\n      if (value \\u003e 0) tokenBucket._consume(value, address(0));\\n    }\\n  }\\n\\n  /// @param remoteChainSelector chain selector to retrieve token bucket for.\\n  /// @param isOutboundLane if set to true, fetches the bucket for the outbound message lane (OnRamp).\\n  /// Otherwise fetches for the inbound message lane (OffRamp).\\n  /// @return bucket Storage pointer to the token bucket representing a specific lane.\\n  function _getTokenBucket(\\n    uint64 remoteChainSelector,\\n    bool isOutboundLane\\n  ) internal view returns (RateLimiter.TokenBucket storage) {\\n    RateLimiterBuckets storage rateLimiterBuckets = s_rateLimitersByChainSelector[remoteChainSelector];\\n    if (isOutboundLane) {\\n      return rateLimiterBuckets.outboundLaneBucket;\\n    } else {\\n      return rateLimiterBuckets.inboundLaneBucket;\\n    }\\n  }\\n\\n  /// @notice Retrieves the token value for a token using the FeeQuoter.\\n  /// @param tokenAmount The token and amount to get the value for.\\n  /// @return tokenValue USD value in 18 decimals.\\n  function _getTokenValue(\\n    Client.EVMTokenAmount memory tokenAmount\\n  ) internal view returns (uint256) {\\n    // not fetching validated price, as price staleness is not important for value-based rate limiting we only\\n    // need to verify the price is not 0.\\n    uint224 pricePerToken = IFeeQuoter(s_feeQuoter).getTokenPrice(tokenAmount.token).value;\\n    if (pricePerToken == 0) revert PriceNotFoundForToken(tokenAmount.token);\\n    return pricePerToken._calcUSDValueFromTokenAmount(tokenAmount.amount);\\n  }\\n\\n  /// @notice Gets the token bucket with its values for the block it was requested at.\\n  /// @param remoteChainSelector chain selector to retrieve state for.\\n  /// @param isOutboundLane if set to true, fetches the rate limit state for the outbound message lane (OnRamp).\\n  /// Otherwise fetches for the inbound message lane (OffRamp).\\n  /// The outbound and inbound message rate limit state is completely separated.\\n  /// @return tokenBucket The token bucket.\\n  function currentRateLimiterState(\\n    uint64 remoteChainSelector,\\n    bool isOutboundLane\\n  ) external view returns (RateLimiter.TokenBucket memory) {\\n    return _getTokenBucket(remoteChainSelector, isOutboundLane)._currentTokenBucketState();\\n  }\\n\\n  /// @notice Applies the provided rate limiter config updates.\\n  /// @param rateLimiterUpdates Rate limiter updates.\\n  /// @dev Only callable by the owner.\\n  function applyRateLimiterConfigUpdates(\\n    RateLimiterConfigArgs[] memory rateLimiterUpdates\\n  ) external onlyOwner {\\n    for (uint256 i = 0; i \\u003c rateLimiterUpdates.length; ++i) {\\n      RateLimiterConfigArgs memory updateArgs = rateLimiterUpdates[i];\\n      RateLimiter.Config memory configUpdate = updateArgs.rateLimiterConfig;\\n      uint64 remoteChainSelector = updateArgs.remoteChainSelector;\\n\\n      if (remoteChainSelector == 0) {\\n        revert ZeroChainSelectorNotAllowed();\\n      }\\n\\n      bool isOutboundLane = updateArgs.isOutboundLane;\\n\\n      RateLimiter.TokenBucket storage tokenBucket = _getTokenBucket(remoteChainSelector, isOutboundLane);\\n\\n      if (tokenBucket.lastUpdated == 0) {\\n        // Token bucket needs to be newly added.\\n        RateLimiter._validateTokenBucketConfig(configUpdate, false);\\n        RateLimiter.TokenBucket memory newTokenBucket = RateLimiter.TokenBucket({\\n          rate: configUpdate.rate,\\n          capacity: configUpdate.capacity,\\n          tokens: configUpdate.capacity,\\n          lastUpdated: uint32(block.timestamp),\\n          isEnabled: configUpdate.isEnabled\\n        });\\n\\n        if (isOutboundLane) {\\n          s_rateLimitersByChainSelector[remoteChainSelector].outboundLaneBucket = newTokenBucket;\\n        } else {\\n          s_rateLimitersByChainSelector[remoteChainSelector].inboundLaneBucket = newTokenBucket;\\n        }\\n      } else {\\n        tokenBucket._setTokenBucketConfig(configUpdate);\\n      }\\n      emit RateLimiterConfigUpdated(remoteChainSelector, isOutboundLane, configUpdate);\\n    }\\n  }\\n\\n  /// @notice Gets all tokens which are included in Aggregate Rate Limiting.\\n  /// @dev the order of IDs in the list is **not guaranteed**, therefore, if ordering matters when making successive\\n  /// calls, one should keep the block height constant to ensure a consistent result.\\n  /// @param remoteChainSelector chain selector to get rate limit tokens for.\\n  /// @return localTokens The local chain representation of the tokens that are rate limited.\\n  /// @return remoteTokens The remote representation of the tokens that are rate limited.\\n  function getAllRateLimitTokens(\\n    uint64 remoteChainSelector\\n  ) external view returns (address[] memory localTokens, bytes[] memory remoteTokens) {\\n    uint256 tokenCount = s_rateLimitedTokensLocalToRemote[remoteChainSelector].length();\\n\\n    localTokens = new address[](tokenCount);\\n    remoteTokens = new bytes[](tokenCount);\\n\\n    for (uint256 i = 0; i \\u003c tokenCount; ++i) {\\n      (address localToken, bytes memory remoteToken) = s_rateLimitedTokensLocalToRemote[remoteChainSelector].at(i);\\n      localTokens[i] = localToken;\\n      remoteTokens[i] = remoteToken;\\n    }\\n    return (localTokens, remoteTokens);\\n  }\\n\\n  /// @notice Adds or removes tokens from being used in Aggregate Rate Limiting.\\n  /// @param removes - A list of one or more tokens to be removed.\\n  /// @param adds - A list of one or more tokens to be added.\\n  function updateRateLimitTokens(\\n    LocalRateLimitToken[] memory removes,\\n    RateLimitTokenArgs[] memory adds\\n  ) external onlyOwner {\\n    for (uint256 i = 0; i \\u003c removes.length; ++i) {\\n      address localToken = removes[i].localToken;\\n      uint64 remoteChainSelector = removes[i].remoteChainSelector;\\n\\n      if (s_rateLimitedTokensLocalToRemote[remoteChainSelector].remove(localToken)) {\\n        emit TokenAggregateRateLimitRemoved(remoteChainSelector, localToken);\\n      }\\n    }\\n\\n    for (uint256 i = 0; i \\u003c adds.length; ++i) {\\n      LocalRateLimitToken memory localTokenArgs = adds[i].localTokenArgs;\\n      bytes memory remoteToken = adds[i].remoteToken;\\n      address localToken = localTokenArgs.localToken;\\n\\n      if (localToken == address(0) || remoteToken.length == 0 || keccak256(remoteToken) == EMPTY_ENCODED_ADDRESS_HASH) {\\n        revert ZeroAddressNotAllowed();\\n      }\\n\\n      uint64 remoteChainSelector = localTokenArgs.remoteChainSelector;\\n\\n      if (s_rateLimitedTokensLocalToRemote[remoteChainSelector].set(localToken, remoteToken)) {\\n        emit TokenAggregateRateLimitAdded(remoteChainSelector, remoteToken, localToken);\\n      }\\n    }\\n  }\\n\\n  /// @return feeQuoter The configured FeeQuoter address.\\n  function getFeeQuoter() external view returns (address feeQuoter) {\\n    return s_feeQuoter;\\n  }\\n\\n  /// @notice Sets the FeeQuoter address.\\n  /// @param newFeeQuoter the address of the new FeeQuoter.\\n  /// @dev precondition The address must be a non-zero address.\\n  function setFeeQuoter(\\n    address newFeeQuoter\\n  ) external onlyOwner {\\n    _setFeeQuoter(newFeeQuoter);\\n  }\\n\\n  /// @notice Sets the FeeQuoter address.\\n  /// @param newFeeQuoter the address of the new FeeQuoter.\\n  /// @dev precondition The address must be a non-zero address.\\n  function _setFeeQuoter(\\n    address newFeeQuoter\\n  ) internal {\\n    if (newFeeQuoter == address(0)) {\\n      revert ZeroAddressNotAllowed();\\n    }\\n\\n    s_feeQuoter = newFeeQuoter;\\n    emit FeeQuoterSet(newFeeQuoter);\\n  }\\n}\\n\"},\"contracts/interfaces/IFeeQuoter.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\nimport {IPriceRegistry} from \\\"./IPriceRegistry.sol\\\";\\n\\ninterface IFeeQuoter is IPriceRegistry {\\n  /// @notice Validates the ccip message \\u0026 returns the fee.\\n  /// @param destChainSelector The destination chain selector.\\n  /// @param message The message to get quote for.\\n  /// @return feeTokenAmount The amount of fee token needed for the fee, in smallest denomination of the fee token.\\n  function getValidatedFee(\\n    uint64 destChainSelector,\\n    Client.EVM2AnyMessage calldata message\\n  ) external view returns (uint256 feeTokenAmount);\\n\\n  /// @notice Converts the extraArgs to the latest version and returns the converted message fee in juels.\\n  /// @notice Validates pool return data.\\n  /// @param destChainSelector destination chain selector to process, must be a configured valid chain.\\n  /// @param feeToken token address used to pay for message fees, must be a configured valid fee token.\\n  /// @param feeTokenAmount Fee token amount.\\n  /// @param extraArgs Message extra args that were passed in by the client.\\n  /// @param messageReceiver Message receiver address in bytes from EVM2AnyMessage.receiver\\n  /// @return msgFeeJuels message fee in juels.\\n  /// @return isOutOfOrderExecution true if the message should be executed out of order.\\n  /// @return convertedExtraArgs extra args converted to the latest family-specific args version.\\n  /// @return tokenReceiver token receiver address in bytes on destination chain\\n  function processMessageArgs(\\n    uint64 destChainSelector,\\n    address feeToken,\\n    uint256 feeTokenAmount,\\n    bytes calldata extraArgs,\\n    bytes calldata messageReceiver\\n  )\\n    external\\n    view\\n    returns (\\n      uint256 msgFeeJuels,\\n      bool isOutOfOrderExecution,\\n      bytes memory convertedExtraArgs,\\n      bytes memory tokenReceiver\\n    );\\n\\n  /// @notice Validates pool return data.\\n  /// @param destChainSelector Destination chain selector to which the token amounts are sent to.\\n  /// @param onRampTokenTransfers Token amounts with populated pool return data.\\n  /// @param sourceTokenAmounts Token amounts originally sent in a Client.EVM2AnyMessage message.\\n  /// @return destExecDataPerToken Destination chain execution data.\\n  function processPoolReturnData(\\n    uint64 destChainSelector,\\n    Internal.EVM2AnyTokenTransfer[] calldata onRampTokenTransfers,\\n    Client.EVMTokenAmount[] calldata sourceTokenAmounts\\n  ) external view returns (bytes[] memory destExecDataPerToken);\\n}\\n\"},\"contracts/interfaces/IMessageInterceptor.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\nimport {Client} from \\\"../libraries/Client.sol\\\";\\n\\n/// @notice Interface for plug-in message hook contracts that intercept OffRamp \\u0026 OnRamp messages and perform\\n/// validations / state changes on top of the messages. The interceptor functions are expected to revert\\n/// on validation failures.\\ninterface IMessageInterceptor {\\n  /// @notice Common error that can be thrown on validation failures and used by consumers.\\n  /// @param errorReason abi encoded revert reason.\\n  error MessageValidationError(bytes errorReason);\\n\\n  /// @notice Intercepts \\u0026 validates the given OffRamp message. Reverts on validation failure.\\n  /// @param message to validate.\\n  function onInboundMessage(\\n    Client.Any2EVMMessage memory message\\n  ) external;\\n\\n  /// @notice Intercepts \\u0026 validates the given OnRamp message. Reverts on validation failure.\\n  /// @param destChainSelector remote destination chain selector where the message is being sent to.\\n  /// @param message to validate.\\n  function onOutboundMessage(uint64 destChainSelector, Client.EVM2AnyMessage memory message) external;\\n}\\n\"},\"contracts/interfaces/IPriceRegistry.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Internal} from \\\"../libraries/Internal.sol\\\";\\n\\ninterface IPriceRegistry {\\n  /// @notice Update the price for given tokens and gas prices for given chains.\\n  /// @param priceUpdates The price updates to apply.\\n  function updatePrices(\\n    Internal.PriceUpdates memory priceUpdates\\n  ) external;\\n\\n  /// @notice Get the `tokenPrice` for a given token.\\n  /// @param token The token to get the price for.\\n  /// @return tokenPrice The tokenPrice for the given token.\\n  function getTokenPrice(\\n    address token\\n  ) external view returns (Internal.TimestampedPackedUint224 memory);\\n\\n  /// @notice Get the `tokenPrice` for a given token, checks if the price is valid.\\n  /// @param token The token to get the price for.\\n  /// @return tokenPrice The tokenPrice for the given token if it exists and is valid.\\n  function getValidatedTokenPrice(\\n    address token\\n  ) external view returns (uint224);\\n\\n  /// @notice Get the `tokenPrice` for an array of tokens.\\n  /// @param tokens The tokens to get prices for.\\n  /// @return tokenPrices The tokenPrices for the given tokens.\\n  function getTokenPrices(\\n    address[] calldata tokens\\n  ) external view returns (Internal.TimestampedPackedUint224[] memory);\\n\\n  /// @notice Get an encoded `gasPrice` for a given destination chain ID.\\n  /// The 224-bit result encodes necessary gas price components.\\n  /// On L1 chains like Ethereum or Avax, the only component is the gas price.\\n  /// On Optimistic Rollups, there are two components - the L2 gas price, and L1 base fee for data availability.\\n  /// On future chains, there could be more or differing price components.\\n  /// PriceRegistry does not contain chain-specific logic to parse destination chain price components.\\n  /// @param destChainSelector The destination chain to get the price for.\\n  /// @return gasPrice The encoded gasPrice for the given destination chain ID.\\n  function getDestinationChainGasPrice(\\n    uint64 destChainSelector\\n  ) external view returns (Internal.TimestampedPackedUint224 memory);\\n\\n  /// @notice Gets the fee token price and the gas price, both denominated in dollars.\\n  /// @param token The source token to get the price for.\\n  /// @param destChainSelector The destination chain to get the gas price for.\\n  /// @return tokenPrice The price of the feeToken in 1e18 dollars per base unit.\\n  /// @return gasPrice The price of gas in 1e18 dollars per base unit.\\n  function getTokenAndGasPrices(\\n    address token,\\n    uint64 destChainSelector\\n  ) external view returns (uint224 tokenPrice, uint224 gasPrice);\\n\\n  /// @notice Convert a given token amount to target token amount.\\n  /// @param fromToken The given token address.\\n  /// @param fromTokenAmount The given token amount.\\n  /// @param toToken The target token address.\\n  /// @return toTokenAmount The target token amount.\\n  function convertTokenAmount(\\n    address fromToken,\\n    uint256 fromTokenAmount,\\n    address toToken\\n  ) external view returns (uint256 toTokenAmount);\\n\\n  /// @notice Get the list of fee tokens.\\n  /// @return feeTokens The tokens set as fee tokens.\\n  function getFeeTokens() external view returns (address[] memory);\\n}\\n\"},\"contracts/libraries/Client.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\n// End consumer library.\\nlibrary Client {\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct EVMTokenAmount {\\n    address token; // token address on the local chain.\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  struct Any2EVMMessage {\\n    bytes32 messageId; // MessageId corresponding to ccipSend on source.\\n    uint64 sourceChainSelector; // Source chain selector.\\n    bytes sender; // abi.decode(sender) if coming from an EVM chain.\\n    bytes data; // payload sent in original message.\\n    EVMTokenAmount[] destTokenAmounts; // Tokens and their amounts in their destination chain representation.\\n  }\\n\\n  // If extraArgs is empty bytes, the default is 200k gas limit.\\n  struct EVM2AnyMessage {\\n    bytes receiver; // abi.encode(receiver address) for dest EVM chains.\\n    bytes data; // Data payload.\\n    EVMTokenAmount[] tokenAmounts; // Token transfers.\\n    address feeToken; // Address of feeToken. address(0) means you will send msg.value.\\n    bytes extraArgs; // Populate this with _argsToBytes(EVMExtraArgsV2).\\n  }\\n\\n  // Tag to indicate only a gas limit. Only usable for EVM as destination chain.\\n  bytes4 public constant EVM_EXTRA_ARGS_V1_TAG = 0x97a657c9;\\n\\n  struct EVMExtraArgsV1 {\\n    uint256 gasLimit;\\n  }\\n\\n  function _argsToBytes(\\n    EVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(EVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n\\n  // Tag to indicate a gas limit (or dest chain equivalent processing units) and Out Of Order Execution. This tag is\\n  // available for multiple chain families. If there is no chain family specific tag, this is the default available\\n  // for a chain.\\n  // Note: not available for Solana VM based chains.\\n  bytes4 public constant GENERIC_EXTRA_ARGS_V2_TAG = 0x181dcf10;\\n\\n  /// @param gasLimit: gas limit for the callback on the destination chain.\\n  /// @param allowOutOfOrderExecution: if true, it indicates that the message can be executed in any order relative to\\n  /// other messages from the same sender. This value's default varies by chain. On some chains, a particular value is\\n  /// enforced, meaning if the expected value is not set, the message request will revert.\\n  /// @dev Fully compatible with the previously existing EVMExtraArgsV2.\\n  struct GenericExtraArgsV2 {\\n    uint256 gasLimit;\\n    bool allowOutOfOrderExecution;\\n  }\\n\\n  // Extra args tag for chains that use the Solana VM.\\n  bytes4 public constant SVM_EXTRA_ARGS_V1_TAG = 0x1f3b3aba;\\n\\n  struct SVMExtraArgsV1 {\\n    uint32 computeUnits;\\n    uint64 accountIsWritableBitmap;\\n    bool allowOutOfOrderExecution;\\n    bytes32 tokenReceiver;\\n    // Additional accounts needed for execution of CCIP receiver. Must be empty if message.receiver is zero.\\n    // Token transfer related accounts are specified in the token pool lookup table on SVM.\\n    bytes32[] accounts;\\n  }\\n\\n  /// @dev The maximum number of accounts that can be passed in SVMExtraArgs.\\n  uint256 public constant SVM_EXTRA_ARGS_MAX_ACCOUNTS = 64;\\n\\n  /// @dev The expected static payload size of a token transfer when Borsh encoded and submitted to SVM.\\n  /// TokenPool extra data and offchain data sizes are dynamic, and should be accounted for separately.\\n  uint256 public constant SVM_TOKEN_TRANSFER_DATA_OVERHEAD = (4 + 32) // source_pool\\n    + 32 // token_address\\n    + 4 // gas_amount\\n    + 4 // extra_data overhead\\n    + 32 // amount\\n    + 32 // size of the token lookup table account\\n    + 32 // token-related accounts in the lookup table, over-estimated to 32, typically between 11 - 13\\n    + 32 // token account belonging to the token receiver, e.g ATA, not included in the token lookup table\\n    + 32 // per-chain token pool config, not included in the token lookup table\\n    + 32 // per-chain token billing config, not always included in the token lookup table\\n    + 32; // OffRamp pool signer PDA, not included in the token lookup table\\n\\n  /// @dev Number of overhead accounts needed for message execution on SVM.\\n  /// @dev These are message.receiver, and the OffRamp Signer PDA specific to the receiver.\\n  uint256 public constant SVM_MESSAGING_ACCOUNTS_OVERHEAD = 2;\\n\\n  /// @dev The size of each SVM account address in bytes.\\n  uint256 public constant SVM_ACCOUNT_BYTE_SIZE = 32;\\n\\n  function _argsToBytes(\\n    GenericExtraArgsV2 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(GENERIC_EXTRA_ARGS_V2_TAG, extraArgs);\\n  }\\n\\n  function _svmArgsToBytes(\\n    SVMExtraArgsV1 memory extraArgs\\n  ) internal pure returns (bytes memory bts) {\\n    return abi.encodeWithSelector(SVM_EXTRA_ARGS_V1_TAG, extraArgs);\\n  }\\n}\\n\"},\"contracts/libraries/Internal.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {MerkleMultiProof} from \\\"../libraries/MerkleMultiProof.sol\\\";\\n\\n/// @notice Library for CCIP internal definitions common to multiple contracts.\\n/// @dev The following is a non-exhaustive list of \\\"known issues\\\" for CCIP:\\n/// - We could implement yield claiming for Blast. This is not worth the custom code path on non-blast chains.\\n/// - uint32 is used for timestamps, which will overflow in 2106. This is not a concern for the current use case, as we\\n/// expect to have migrated to a new version by then.\\nlibrary Internal {\\n  error InvalidEVMAddress(bytes encodedAddress);\\n  error Invalid32ByteAddress(bytes encodedAddress);\\n\\n  /// @dev We limit return data to a selector plus 4 words. This is to avoid malicious contracts from returning\\n  /// large amounts of data and causing repeated out-of-gas scenarios.\\n  uint16 internal constant MAX_RET_BYTES = 4 + 4 * 32;\\n  /// @dev The expected number of bytes returned by the balanceOf function.\\n  uint256 internal constant MAX_BALANCE_OF_RET_BYTES = 32;\\n\\n  /// @dev The address used to send calls for gas estimation.\\n  /// You only need to use this address if the minimum gas limit specified by the user is not actually enough to execute the\\n  /// given message and you're attempting to estimate the actual necessary gas limit\\n  address public constant GAS_ESTIMATION_SENDER = address(0xC11C11C11C11C11C11C11C11C11C11C11C11C1);\\n\\n  /// @notice A collection of token price and gas price updates.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct PriceUpdates {\\n    TokenPriceUpdate[] tokenPriceUpdates;\\n    GasPriceUpdate[] gasPriceUpdates;\\n  }\\n\\n  /// @notice Token price in USD.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct TokenPriceUpdate {\\n    address sourceToken; // Source token.\\n    uint224 usdPerToken; // 1e18 USD per 1e18 of the smallest token denomination.\\n  }\\n\\n  /// @notice Gas price for a given chain in USD, its value may contain tightly packed fields.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct GasPriceUpdate {\\n    uint64 destChainSelector; // Destination chain selector.\\n    uint224 usdPerUnitGas; // 1e18 USD per smallest unit (e.g. wei) of destination chain gas.\\n  }\\n\\n  /// @notice A timestamped uint224 value that can contain several tightly packed fields.\\n  struct TimestampedPackedUint224 {\\n    uint224 value; // ────╮ Value in uint224, packed.\\n    uint32 timestamp; // ─╯ Timestamp of the most recent price update.\\n  }\\n\\n  /// @dev Gas price is stored in 112-bit unsigned int. uint224 can pack 2 prices.\\n  /// When packing L1 and L2 gas prices, L1 gas price is left-shifted to the higher-order bits.\\n  /// Using uint8 type, which cannot be higher than other bit shift operands, to avoid shift operand type warning.\\n  uint8 public constant GAS_PRICE_BITS = 112;\\n\\n  struct SourceTokenData {\\n    // The source pool address, abi encoded. This value is trusted as it was obtained through the onRamp. It can be\\n    // relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    // The address of the destination token, abi encoded in the case of EVM chains.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint32 destGasAmount; // The amount of gas available for the releaseOrMint and balanceOf calls on the offRamp\\n  }\\n\\n  /// @notice Report that is submitted by the execution DON at the execution phase, including chain selector data.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  struct ExecutionReport {\\n    uint64 sourceChainSelector; // Source chain selector for which the report is submitted.\\n    Any2EVMRampMessage[] messages;\\n    // Contains a bytes array for each message, each inner bytes array contains bytes per transferred token.\\n    bytes[][] offchainTokenData;\\n    bytes32[] proofs;\\n    uint256 proofFlagBits;\\n  }\\n\\n  /// @dev Any2EVMRampMessage struct has 10 fields, including 3 variable unnested arrays, sender, data and tokenAmounts.\\n  /// Each variable array takes 1 more slot to store its length.\\n  /// When abi encoded, excluding array contents, Any2EVMMessage takes up a fixed number of 13 slots, 32 bytes each.\\n  /// Assume 1 slot for sender\\n  /// For structs that contain arrays, 1 more slot is added to the front, reaching a total of 14.\\n  /// The fixed bytes does not cover struct data (this is represented by MESSAGE_FIXED_BYTES_PER_TOKEN)\\n  uint256 public constant MESSAGE_FIXED_BYTES = 32 * 15;\\n\\n  /// @dev Any2EVMTokensTransfer struct bytes length\\n  /// 0x20\\n  /// sourcePoolAddress_offset\\n  /// destTokenAddress\\n  /// destGasAmount\\n  /// extraData_offset\\n  /// amount\\n  /// sourcePoolAddress_length\\n  /// sourcePoolAddress_content // assume 1 slot\\n  /// extraData_length // contents billed separately\\n  uint256 public constant MESSAGE_FIXED_BYTES_PER_TOKEN = 32 * (4 + (3 + 2));\\n\\n  bytes32 internal constant ANY_2_EVM_MESSAGE_HASH = keccak256(\\\"Any2EVMMessageHashV1\\\");\\n  bytes32 internal constant EVM_2_ANY_MESSAGE_HASH = keccak256(\\\"EVM2AnyMessageHashV1\\\");\\n\\n  /// @dev Used to hash messages for multi-lane family-agnostic OffRamps.\\n  /// OnRamp hash(EVM2AnyMessage) != Any2EVMRampMessage.messageId.\\n  /// OnRamp hash(EVM2AnyMessage) != OffRamp hash(Any2EVMRampMessage).\\n  /// @param original OffRamp message to hash.\\n  /// @param metadataHash Hash preimage to ensure global uniqueness.\\n  /// @return hashedMessage hashed message as a keccak256.\\n  function _hash(Any2EVMRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.header.messageId,\\n            original.receiver,\\n            original.header.sequenceNumber,\\n            original.gasLimit,\\n            original.header.nonce\\n          )\\n        ),\\n        keccak256(original.sender),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts))\\n      )\\n    );\\n  }\\n\\n  function _hash(EVM2AnyRampMessage memory original, bytes32 metadataHash) internal pure returns (bytes32) {\\n    // Fixed-size message fields are included in nested hash to reduce stack pressure.\\n    // This hashing scheme is also used by RMN. If changing it, please notify the RMN maintainers.\\n    return keccak256(\\n      abi.encode(\\n        MerkleMultiProof.LEAF_DOMAIN_SEPARATOR,\\n        metadataHash,\\n        keccak256(\\n          abi.encode(\\n            original.sender,\\n            original.header.sequenceNumber,\\n            original.header.nonce,\\n            original.feeToken,\\n            original.feeTokenAmount\\n          )\\n        ),\\n        keccak256(original.receiver),\\n        keccak256(original.data),\\n        keccak256(abi.encode(original.tokenAmounts)),\\n        keccak256(original.extraArgs)\\n      )\\n    );\\n  }\\n\\n  /// @dev We disallow the first 1024 addresses to avoid calling into a range known for hosting precompiles. Calling\\n  /// into precompiles probably won't cause any issues, but to be safe we can disallow this range. It is extremely\\n  /// unlikely that anyone would ever be able to generate an address in this range. There is no official range of\\n  /// precompiles, but EIP-7587 proposes to reserve the range 0x100 to 0x1ff. Our range is more conservative, even\\n  /// though it might not be exhaustive for all chains, which is OK. We also disallow the zero address, which is a\\n  /// common practice.\\n  uint256 public constant EVM_PRECOMPILE_SPACE = 1024;\\n\\n  // According to the Aptos docs, the first 0xa addresses are reserved for precompiles.\\n  // https://github.com/aptos-labs/aptos-core/blob/main/aptos-move/framework/aptos-framework/doc/account.md#function-create_framework_reserved_account-1\\n  uint256 public constant APTOS_PRECOMPILE_SPACE = 0x0b;\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// EVM address space. If it isn't it will revert with an InvalidEVMAddress error, which we can catch and handle\\n  /// more gracefully than a revert from abi.decode.\\n  function _validateEVMAddress(\\n    bytes memory encodedAddress\\n  ) internal pure {\\n    if (encodedAddress.length != 32) revert InvalidEVMAddress(encodedAddress);\\n    uint256 encodedAddressUint = abi.decode(encodedAddress, (uint256));\\n    if (encodedAddressUint \\u003e type(uint160).max || encodedAddressUint \\u003c EVM_PRECOMPILE_SPACE) {\\n      revert InvalidEVMAddress(encodedAddress);\\n    }\\n  }\\n\\n  /// @notice This methods provides validation for parsing abi encoded addresses by ensuring the address is within the\\n  /// bounds of [minValue, uint256.max]. If it isn't it will revert with an Invalid32ByteAddress error.\\n  function _validate32ByteAddress(bytes memory encodedAddress, uint256 minValue) internal pure {\\n    if (encodedAddress.length != 32) revert Invalid32ByteAddress(encodedAddress);\\n    if (minValue \\u003e 0) {\\n      if (abi.decode(encodedAddress, (uint256)) \\u003c minValue) {\\n        revert Invalid32ByteAddress(encodedAddress);\\n      }\\n    }\\n  }\\n\\n  /// @notice Enum listing the possible message execution states within the offRamp contract.\\n  /// UNTOUCHED never executed.\\n  /// IN_PROGRESS currently being executed, used a replay protection.\\n  /// SUCCESS successfully executed. End state.\\n  /// FAILURE unsuccessfully executed, manual execution is now enabled.\\n  /// @dev RMN depends on this enum, if changing, please notify the RMN maintainers.\\n  enum MessageExecutionState {\\n    UNTOUCHED,\\n    IN_PROGRESS,\\n    SUCCESS,\\n    FAILURE\\n  }\\n\\n  /// @notice CCIP OCR plugin type, used to separate execution \\u0026 commit transmissions and configs.\\n  enum OCRPluginType {\\n    Commit,\\n    Execution\\n  }\\n\\n  /// @notice Family-agnostic header for OnRamp \\u0026 OffRamp messages.\\n  /// The messageId is not expected to match hash(message), since it may originate from another ramp family.\\n  struct RampMessageHeader {\\n    bytes32 messageId; // Unique identifier for the message, generated with the source chain's encoding scheme (i.e. not necessarily abi.encoded).\\n    uint64 sourceChainSelector; // ─╮ the chain selector of the source chain, note: not chainId.\\n    uint64 destChainSelector; //    │ the chain selector of the destination chain, note: not chainId.\\n    uint64 sequenceNumber; //       │ sequence number, not unique across lanes.\\n    uint64 nonce; // ───────────────╯ nonce for this lane for this sender, not unique across senders/lanes.\\n  }\\n\\n  struct EVM2AnyTokenTransfer {\\n    // The source pool EVM address. This value is trusted as it was obtained through the onRamp. It can be relied\\n    // upon by the destination pool to validate the source pool.\\n    address sourcePoolAddress;\\n    // The EVM address of the destination token.\\n    // This value is UNTRUSTED as any pool owner can return whatever value they want.\\n    bytes destTokenAddress;\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n    // Destination chain data used to execute the token transfer on the destination chain. For an EVM destination, it\\n    // consists of the amount of gas available for the releaseOrMint and transfer calls made by the offRamp.\\n    bytes destExecData;\\n  }\\n\\n  struct Any2EVMTokenTransfer {\\n    // The source pool EVM address encoded to bytes. This value is trusted as it is obtained through the onRamp. It can\\n    // be relied upon by the destination pool to validate the source pool.\\n    bytes sourcePoolAddress;\\n    address destTokenAddress; // ─╮ Address of destination token\\n    uint32 destGasAmount; // ─────╯ The amount of gas available for the releaseOrMint and transfer calls on the offRamp.\\n    // Optional pool data to be transferred to the destination chain. Be default this is capped at\\n    // CCIP_LOCK_OR_BURN_V1_RET_BYTES bytes. If more data is required, the TokenTransferFeeConfig.destBytesOverhead\\n    // has to be set for the specific token.\\n    bytes extraData;\\n    uint256 amount; // Amount of tokens.\\n  }\\n\\n  /// @notice Family-agnostic message routed to an OffRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage), hash(Any2EVMRampMessage) != messageId due to encoding\\n  /// and parameter differences.\\n  struct Any2EVMRampMessage {\\n    RampMessageHeader header; // Message header.\\n    bytes sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    address receiver; // receiver address on the destination chain.\\n    uint256 gasLimit; // user supplied maximum gas amount available for dest chain execution.\\n    Any2EVMTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  /// @notice Family-agnostic message emitted from the OnRamp.\\n  /// Note: hash(Any2EVMRampMessage) != hash(EVM2AnyRampMessage) due to encoding \\u0026 parameter differences.\\n  /// messageId = hash(EVM2AnyRampMessage) using the source EVM chain's encoding format.\\n  struct EVM2AnyRampMessage {\\n    RampMessageHeader header; // Message header.\\n    address sender; // sender address on the source chain.\\n    bytes data; // arbitrary data payload supplied by the message sender.\\n    bytes receiver; // receiver address on the destination chain.\\n    bytes extraArgs; // destination-chain specific extra args, such as the gasLimit for EVM chains.\\n    address feeToken; // fee token.\\n    uint256 feeTokenAmount; // fee token amount.\\n    uint256 feeValueJuels; // fee amount in Juels.\\n    EVM2AnyTokenTransfer[] tokenAmounts; // array of tokens and amounts to transfer.\\n  }\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector EVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_EVM = 0x2812d52c;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SVM\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SVM = 0x1e10bdc4;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector APTOS\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_APTOS = 0xac77ffec;\\n\\n  // bytes4(keccak256(\\\"CCIP ChainFamilySelector SUI\\\"));\\n  bytes4 public constant CHAIN_FAMILY_SELECTOR_SUI = 0xc4e05953;\\n\\n  /// @dev Holds a merkle root and interval for a source chain so that an array of these can be passed in the CommitReport.\\n  /// @dev RMN depends on this struct, if changing, please notify the RMN maintainers.\\n  /// @dev inefficient struct packing intentionally chosen to maintain order of specificity. Not a storage struct so impact is minimal.\\n  // solhint-disable-next-line gas-struct-packing\\n  struct MerkleRoot {\\n    uint64 sourceChainSelector; // Remote source chain selector that the Merkle Root is scoped to\\n    bytes onRampAddress; //        Generic onRamp address, to support arbitrary sources; for EVM, use abi.encode\\n    uint64 minSeqNr; // ─────────╮ Minimum sequence number, inclusive\\n    uint64 maxSeqNr; // ─────────╯ Maximum sequence number, inclusive\\n    bytes32 merkleRoot; //         Merkle root covering the interval \\u0026 source chain messages\\n  }\\n}\\n\"},\"contracts/libraries/MerkleMultiProof.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nlibrary MerkleMultiProof {\\n  /// @notice Leaf domain separator, should be used as the first 32 bytes of a leaf's preimage.\\n  bytes32 internal constant LEAF_DOMAIN_SEPARATOR = 0x0000000000000000000000000000000000000000000000000000000000000000;\\n  /// @notice Internal domain separator, should be used as the first 32 bytes of an internal node's preimage.\\n  bytes32 internal constant INTERNAL_DOMAIN_SEPARATOR =\\n    0x0000000000000000000000000000000000000000000000000000000000000001;\\n\\n  uint256 internal constant MAX_NUM_HASHES = 256;\\n\\n  error InvalidProof();\\n  error LeavesCannotBeEmpty();\\n\\n  /// @notice Computes the root based on provided pre-hashed leaf nodes in leaves, internal nodes  in proofs, and using\\n  /// proofFlagBits' i-th bit to determine if an element of proofs or one of the previously computed leafs or internal\\n  /// nodes will be used for the i-th hash.\\n  /// @param leaves Should be pre-hashed and the first 32 bytes of a leaf's preimage should match LEAF_DOMAIN_SEPARATOR.\\n  /// @param proofs Hashes to be used instead of a leaf hash when the proofFlagBits indicates a proof should be used.\\n  /// @param proofFlagBits A single uint256 of which each bit indicates whether a leaf or a proof needs to be used in\\n  /// a hash operation.\\n  /// @dev the maximum number of hash operations it set to 256. Any input that would require more than 256 hashes to get\\n  /// to a root will revert.\\n  /// @dev For given input `leaves` = [a,b,c] `proofs` = [D] and `proofFlagBits` = 5\\n  ///     totalHashes = 3 + 1 - 1 = 3\\n  ///  ** round 1 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 0) \\u0026 1 = true\\n  ///    hashes[0] = hashPair(a, b)\\n  ///    (leafPos, hashPos, proofPos) = (2, 0, 0);\\n  ///\\n  ///  ** round 2 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 1) \\u0026 1 = false\\n  ///    hashes[1] = hashPair(D, c)\\n  ///    (leafPos, hashPos, proofPos) = (3, 0, 1);\\n  ///\\n  ///  ** round 3 **\\n  ///    proofFlagBits = (5 \\u003e\\u003e 2) \\u0026 1 = true\\n  ///    hashes[2] = hashPair(hashes[0], hashes[1])\\n  ///    (leafPos, hashPos, proofPos) = (3, 2, 1);\\n  ///\\n  ///    i = 3 and no longer \\u003c totalHashes. The algorithm is done\\n  ///    return hashes[totalHashes - 1] = hashes[2]; the last hash we computed.\\n  // We mark this function as internal to force it to be inlined in contracts that use it, but semantically it is public.\\n  function _merkleRoot(\\n    bytes32[] memory leaves,\\n    bytes32[] memory proofs,\\n    uint256 proofFlagBits\\n  ) internal pure returns (bytes32) {\\n    unchecked {\\n      uint256 leavesLen = leaves.length;\\n      uint256 proofsLen = proofs.length;\\n      if (leavesLen == 0) revert LeavesCannotBeEmpty();\\n      if (!(leavesLen \\u003c= MAX_NUM_HASHES + 1 \\u0026\\u0026 proofsLen \\u003c= MAX_NUM_HASHES + 1)) revert InvalidProof();\\n      uint256 totalHashes = leavesLen + proofsLen - 1;\\n      if (!(totalHashes \\u003c= MAX_NUM_HASHES)) revert InvalidProof();\\n      if (totalHashes == 0) {\\n        return leaves[0];\\n      }\\n      bytes32[] memory hashes = new bytes32[](totalHashes);\\n      (uint256 leafPos, uint256 hashPos, uint256 proofPos) = (0, 0, 0);\\n\\n      for (uint256 i = 0; i \\u003c totalHashes; ++i) {\\n        // Checks if the bit flag signals the use of a supplied proof or a leaf/previous hash.\\n        bytes32 a;\\n        if (proofFlagBits \\u0026 (1 \\u003c\\u003c i) == (1 \\u003c\\u003c i)) {\\n          // Use a leaf or a previously computed hash.\\n          if (leafPos \\u003c leavesLen) {\\n            a = leaves[leafPos++];\\n          } else {\\n            a = hashes[hashPos++];\\n          }\\n        } else {\\n          // Use a supplied proof.\\n          a = proofs[proofPos++];\\n        }\\n\\n        // The second part of the hashed pair is never a proof as hashing two proofs would result in a\\n        // hash that can already be computed offchain.\\n        bytes32 b;\\n        if (leafPos \\u003c leavesLen) {\\n          b = leaves[leafPos++];\\n        } else {\\n          b = hashes[hashPos++];\\n        }\\n\\n        if (!(hashPos \\u003c= i)) revert InvalidProof();\\n\\n        hashes[i] = _hashPair(a, b);\\n      }\\n      if (!(hashPos == totalHashes - 1 \\u0026\\u0026 leafPos == leavesLen \\u0026\\u0026 proofPos == proofsLen)) revert InvalidProof();\\n      // Return the last hash.\\n      return hashes[totalHashes - 1];\\n    }\\n  }\\n\\n  /// @notice Hashes two bytes32 objects in their given order, prepended by the INTERNAL_DOMAIN_SEPARATOR.\\n  function _hashInternalNode(bytes32 left, bytes32 right) private pure returns (bytes32 hash) {\\n    return keccak256(abi.encode(INTERNAL_DOMAIN_SEPARATOR, left, right));\\n  }\\n\\n  /// @notice Hashes two bytes32 objects. The order is taken into account, using the lower value first.\\n  function _hashPair(bytes32 a, bytes32 b) private pure returns (bytes32) {\\n    return a \\u003c b ? _hashInternalNode(a, b) : _hashInternalNode(b, a);\\n  }\\n}\\n\"},\"contracts/libraries/RateLimiter.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\n/// @notice Implements Token Bucket rate limiting.\\n/// @dev uint128 is safe for rate limiter state.\\n/// - For USD value rate limiting, it can adequately store USD value in 18 decimals.\\n/// - For ERC20 token amount rate limiting, all tokens that will be listed will have at most a supply of uint128.max\\n/// tokens, and it will therefore not overflow the bucket. In exceptional scenarios where tokens consumed may be larger\\n/// than uint128, e.g. compromised issuer, an enabled RateLimiter will check and revert.\\nlibrary RateLimiter {\\n  error BucketOverfilled();\\n  error OnlyCallableByAdminOrOwner();\\n  error TokenMaxCapacityExceeded(uint256 capacity, uint256 requested, address tokenAddress);\\n  error TokenRateLimitReached(uint256 minWaitInSeconds, uint256 available, address tokenAddress);\\n  error AggregateValueMaxCapacityExceeded(uint256 capacity, uint256 requested);\\n  error AggregateValueRateLimitReached(uint256 minWaitInSeconds, uint256 available);\\n  error InvalidRateLimitRate(Config rateLimiterConfig);\\n  error DisabledNonZeroRateLimit(Config config);\\n  error RateLimitMustBeDisabled();\\n\\n  event TokensConsumed(uint256 tokens);\\n  event ConfigChanged(Config config);\\n\\n  struct TokenBucket {\\n    uint128 tokens; // ──────╮ Current number of tokens that are in the bucket.\\n    uint32 lastUpdated; //   │ Timestamp in seconds of the last token refill, good for 100+ years.\\n    bool isEnabled; // ──────╯ Indication whether the rate limiting is enabled or not.\\n    uint128 capacity; // ────╮ Maximum number of tokens that can be in the bucket.\\n    uint128 rate; // ────────╯ Number of tokens per second that the bucket is refilled.\\n  }\\n\\n  struct Config {\\n    bool isEnabled; // Indication whether the rate limiting should be enabled.\\n    uint128 capacity; // ────╮ Specifies the capacity of the rate limiter.\\n    uint128 rate; //  ───────╯ Specifies the rate of the rate limiter.\\n  }\\n\\n  /// @notice _consume removes the given tokens from the pool, lowering the rate tokens allowed to be\\n  /// consumed for subsequent calls.\\n  /// @param requestTokens The total tokens to be consumed from the bucket.\\n  /// @param tokenAddress The token to consume capacity for, use 0x0 to indicate aggregate value capacity.\\n  /// @dev Reverts when requestTokens exceeds bucket capacity or available tokens in the bucket.\\n  /// @dev emits removal of requestTokens if requestTokens is \\u003e 0.\\n  function _consume(TokenBucket storage s_bucket, uint256 requestTokens, address tokenAddress) internal {\\n    // If there is no value to remove or rate limiting is turned off, skip this step to reduce gas usage.\\n    if (!s_bucket.isEnabled || requestTokens == 0) {\\n      return;\\n    }\\n\\n    uint256 tokens = s_bucket.tokens;\\n    uint256 capacity = s_bucket.capacity;\\n    uint256 timeDiff = block.timestamp - s_bucket.lastUpdated;\\n\\n    if (timeDiff != 0) {\\n      if (tokens \\u003e capacity) revert BucketOverfilled();\\n\\n      // Refill tokens when arriving at a new block time.\\n      tokens = _calculateRefill(capacity, tokens, timeDiff, s_bucket.rate);\\n\\n      s_bucket.lastUpdated = uint32(block.timestamp);\\n    }\\n\\n    if (capacity \\u003c requestTokens) {\\n      // Token address 0 indicates consuming aggregate value rate limit capacity.\\n      if (tokenAddress == address(0)) revert AggregateValueMaxCapacityExceeded(capacity, requestTokens);\\n      revert TokenMaxCapacityExceeded(capacity, requestTokens, tokenAddress);\\n    }\\n    if (tokens \\u003c requestTokens) {\\n      uint256 rate = s_bucket.rate;\\n      // Wait required until the bucket is refilled enough to accept this value, round up to next higher second.\\n      // Consume is not guaranteed to succeed after wait time passes if there is competing traffic.\\n      // This acts as a lower bound of wait time.\\n      uint256 minWaitInSeconds = ((requestTokens - tokens) + (rate - 1)) / rate;\\n\\n      if (tokenAddress == address(0)) revert AggregateValueRateLimitReached(minWaitInSeconds, tokens);\\n      revert TokenRateLimitReached(minWaitInSeconds, tokens, tokenAddress);\\n    }\\n    tokens -= requestTokens;\\n\\n    // Downcast is safe here, as tokens is not larger than capacity.\\n    s_bucket.tokens = uint128(tokens);\\n    emit TokensConsumed(requestTokens);\\n  }\\n\\n  /// @notice Gets the token bucket with its values for the block it was requested at.\\n  /// @return The token bucket.\\n  function _currentTokenBucketState(\\n    TokenBucket memory bucket\\n  ) internal view returns (TokenBucket memory) {\\n    // We update the bucket to reflect the status at the exact time of the call. This means we might need to refill a\\n    // part of the bucket based on the time that has passed since the last update.\\n    bucket.tokens =\\n      uint128(_calculateRefill(bucket.capacity, bucket.tokens, block.timestamp - bucket.lastUpdated, bucket.rate));\\n    bucket.lastUpdated = uint32(block.timestamp);\\n    return bucket;\\n  }\\n\\n  /// @notice Sets the rate limited config.\\n  /// @param s_bucket The token bucket.\\n  /// @param config The new config.\\n  function _setTokenBucketConfig(TokenBucket storage s_bucket, Config memory config) internal {\\n    // First update the bucket to make sure the proper rate is used for all the time up until the config change.\\n    uint256 timeDiff = block.timestamp - s_bucket.lastUpdated;\\n    if (timeDiff != 0) {\\n      s_bucket.tokens = uint128(_calculateRefill(s_bucket.capacity, s_bucket.tokens, timeDiff, s_bucket.rate));\\n\\n      s_bucket.lastUpdated = uint32(block.timestamp);\\n    }\\n\\n    s_bucket.tokens = uint128(_min(config.capacity, s_bucket.tokens));\\n    s_bucket.isEnabled = config.isEnabled;\\n    s_bucket.capacity = config.capacity;\\n    s_bucket.rate = config.rate;\\n\\n    emit ConfigChanged(config);\\n  }\\n\\n  /// @notice Validates the token bucket config.\\n  function _validateTokenBucketConfig(Config memory config, bool mustBeDisabled) internal pure {\\n    if (config.isEnabled) {\\n      if (config.rate \\u003e= config.capacity || config.rate == 0) {\\n        revert InvalidRateLimitRate(config);\\n      }\\n      if (mustBeDisabled) {\\n        revert RateLimitMustBeDisabled();\\n      }\\n    } else {\\n      if (config.rate != 0 || config.capacity != 0) {\\n        revert DisabledNonZeroRateLimit(config);\\n      }\\n    }\\n  }\\n\\n  /// @notice Calculate refilled tokens.\\n  /// @param capacity bucket capacity.\\n  /// @param tokens current bucket tokens.\\n  /// @param timeDiff block time difference since last refill.\\n  /// @param rate bucket refill rate.\\n  /// @return the value of tokens after refill.\\n  function _calculateRefill(\\n    uint256 capacity,\\n    uint256 tokens,\\n    uint256 timeDiff,\\n    uint256 rate\\n  ) private pure returns (uint256) {\\n    return _min(capacity, tokens + timeDiff * rate);\\n  }\\n\\n  /// @notice Return the smallest of two integers.\\n  /// @param a first int.\\n  /// @param b second int.\\n  /// @return smallest.\\n  function _min(uint256 a, uint256 b) internal pure returns (uint256) {\\n    return a \\u003c b ? a : b;\\n  }\\n}\\n\"},\"contracts/libraries/USDPriceWith18Decimals.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.0;\\n\\nlibrary USDPriceWith18Decimals {\\n  /// @notice Takes a price in USD, with 18 decimals per 1e18 token amount, and amount of the smallest token\\n  /// denomination, calculates the value in USD with 18 decimals.\\n  /// @param tokenPrice The USD price of the token.\\n  /// @param tokenAmount Amount of the smallest token denomination.\\n  /// @return USD value with 18 decimals.\\n  /// @dev this function assumes that no more than 1e59 US dollar worth of token is passed in. If more is sent, this\\n  /// function will overflow and revert. Since there isn't even close to 1e59 dollars, this is ok for all legit tokens.\\n  function _calcUSDValueFromTokenAmount(uint224 tokenPrice, uint256 tokenAmount) internal pure returns (uint256) {\\n    /// LINK Example:\\n    /// tokenPrice:         8e18 -\\u003e $8/LINK, as 1e18 token amount is 1 LINK, worth 8 USD, or 8e18 with 18 decimals\\n    /// tokenAmount:        2e18 -\\u003e 2 LINK\\n    /// result:             8e18 * 2e18 / 1e18 -\\u003e 16e18 with 18 decimals = $16\\n\\n    /// USDC Example:\\n    /// tokenPrice:         1e30 -\\u003e $1/USDC, as 1e18 token amount is 1e12 USDC, worth 1e12 USD, or 1e30 with 18 decimals\\n    /// tokenAmount:        5e6  -\\u003e 5 USDC\\n    /// result:             1e30 * 5e6 / 1e18 -\\u003e 5e18 with 18 decimals = $5\\n    return (tokenPrice * tokenAmount) / 1e18;\\n  }\\n\\n  /// @notice Takes a price in USD, with 18 decimals per 1e18 token amount, and USD value with 18 decimals, calculates\\n  /// amount of the smallest token denomination.\\n  /// @param tokenPrice The USD price of the token.\\n  /// @param usdValue USD value with 18 decimals.\\n  /// @return Amount of the smallest token denomination.\\n  function _calcTokenAmountFromUSDValue(uint224 tokenPrice, uint256 usdValue) internal pure returns (uint256) {\\n    /// LINK Example:\\n    /// tokenPrice:          8e18 -\\u003e $8/LINK, as 1e18 token amount is 1 LINK, worth 8 USD, or 8e18 with 18 decimals\\n    /// usdValue:           16e18 -\\u003e $16\\n    /// result:             16e18 * 1e18 / 8e18 -\\u003e 2e18 = 2 LINK\\n\\n    /// USDC Example:\\n    /// tokenPrice:         1e30 -\\u003e $1/USDC, as 1e18 token amount is 1e12 USDC, worth 1e12 USD, or 1e30 with 18 decimals\\n    /// usdValue:           5e18 -\\u003e $5\\n    /// result:             5e18 * 1e18 / 1e30 -\\u003e 5e6 = 5 USDC\\n    return (usdValue * 1e18) / tokenPrice;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/AuthorizedCallers.sol\":{\"content\":\"// SPDX-License-Identifier: BUSL-1.1\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2StepMsgSender} from \\\"./Ownable2StepMsgSender.sol\\\";\\nimport {EnumerableSet} from \\\"../../vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/// @title The AuthorizedCallers contract\\n/// @notice A contract that manages multiple authorized callers. Enables restricting access to certain functions to a set of addresses.\\ncontract AuthorizedCallers is Ownable2StepMsgSender {\\n  using EnumerableSet for EnumerableSet.AddressSet;\\n\\n  event AuthorizedCallerAdded(address caller);\\n  event AuthorizedCallerRemoved(address caller);\\n\\n  error UnauthorizedCaller(address caller);\\n  error ZeroAddressNotAllowed();\\n\\n  /// @notice Update args for changing the authorized callers\\n  struct AuthorizedCallerArgs {\\n    address[] addedCallers;\\n    address[] removedCallers;\\n  }\\n\\n  /// @dev Set of authorized callers\\n  EnumerableSet.AddressSet internal s_authorizedCallers;\\n\\n  /// @param authorizedCallers the authorized callers to set\\n  constructor(address[] memory authorizedCallers) {\\n    _applyAuthorizedCallerUpdates(\\n      AuthorizedCallerArgs({addedCallers: authorizedCallers, removedCallers: new address[](0)})\\n    );\\n  }\\n\\n  /// @return authorizedCallers Returns all authorized callers\\n  function getAllAuthorizedCallers() external view returns (address[] memory) {\\n    return s_authorizedCallers.values();\\n  }\\n\\n  /// @notice Updates the list of authorized callers\\n  /// @param authorizedCallerArgs Callers to add and remove. Removals are performed first.\\n  function applyAuthorizedCallerUpdates(AuthorizedCallerArgs memory authorizedCallerArgs) external onlyOwner {\\n    _applyAuthorizedCallerUpdates(authorizedCallerArgs);\\n  }\\n\\n  /// @notice Updates the list of authorized callers\\n  /// @param authorizedCallerArgs Callers to add and remove. Removals are performed first.\\n  function _applyAuthorizedCallerUpdates(AuthorizedCallerArgs memory authorizedCallerArgs) internal {\\n    address[] memory removedCallers = authorizedCallerArgs.removedCallers;\\n    for (uint256 i = 0; i \\u003c removedCallers.length; ++i) {\\n      address caller = removedCallers[i];\\n\\n      if (s_authorizedCallers.remove(caller)) {\\n        emit AuthorizedCallerRemoved(caller);\\n      }\\n    }\\n\\n    address[] memory addedCallers = authorizedCallerArgs.addedCallers;\\n    for (uint256 i = 0; i \\u003c addedCallers.length; ++i) {\\n      address caller = addedCallers[i];\\n\\n      if (caller == address(0)) {\\n        revert ZeroAddressNotAllowed();\\n      }\\n\\n      s_authorizedCallers.add(caller);\\n      emit AuthorizedCallerAdded(caller);\\n    }\\n  }\\n\\n  /// @notice Checks the sender and reverts if it is anyone other than a listed authorized caller.\\n  function _validateCaller() internal view {\\n    if (!s_authorizedCallers.contains(msg.sender)) {\\n      revert UnauthorizedCaller(msg.sender);\\n    }\\n  }\\n\\n  /// @notice Checks the sender and reverts if it is anyone other than a listed authorized caller.\\n  modifier onlyAuthorizedCallers() {\\n    _validateCaller();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2Step.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {IOwnable} from \\\"../interfaces/IOwnable.sol\\\";\\n\\n/// @notice A minimal contract that implements 2-step ownership transfer and nothing more. It's made to be minimal\\n/// to reduce the impact of the bytecode size on any contract that inherits from it.\\ncontract Ownable2Step is IOwnable {\\n  /// @notice The pending owner is the address to which ownership may be transferred.\\n  address private s_pendingOwner;\\n  /// @notice The owner is the current owner of the contract.\\n  /// @dev The owner is the second storage variable so any implementing contract could pack other state with it\\n  /// instead of the much less used s_pendingOwner.\\n  address private s_owner;\\n\\n  error OwnerCannotBeZero();\\n  error MustBeProposedOwner();\\n  error CannotTransferToSelf();\\n  error OnlyCallableByOwner();\\n\\n  event OwnershipTransferRequested(address indexed from, address indexed to);\\n  event OwnershipTransferred(address indexed from, address indexed to);\\n\\n  constructor(address newOwner, address pendingOwner) {\\n    if (newOwner == address(0)) {\\n      revert OwnerCannotBeZero();\\n    }\\n\\n    s_owner = newOwner;\\n    if (pendingOwner != address(0)) {\\n      _transferOwnership(pendingOwner);\\n    }\\n  }\\n\\n  /// @notice Get the current owner\\n  function owner() public view override returns (address) {\\n    return s_owner;\\n  }\\n\\n  /// @notice Allows an owner to begin transferring ownership to a new address. The new owner needs to call\\n  /// `acceptOwnership` to accept the transfer before any permissions are changed.\\n  /// @param to The address to which ownership will be transferred.\\n  function transferOwnership(address to) public override onlyOwner {\\n    _transferOwnership(to);\\n  }\\n\\n  /// @notice validate, transfer ownership, and emit relevant events\\n  /// @param to The address to which ownership will be transferred.\\n  function _transferOwnership(address to) private {\\n    if (to == msg.sender) {\\n      revert CannotTransferToSelf();\\n    }\\n\\n    s_pendingOwner = to;\\n\\n    emit OwnershipTransferRequested(s_owner, to);\\n  }\\n\\n  /// @notice Allows an ownership transfer to be completed by the recipient.\\n  function acceptOwnership() external override {\\n    if (msg.sender != s_pendingOwner) {\\n      revert MustBeProposedOwner();\\n    }\\n\\n    address oldOwner = s_owner;\\n    s_owner = msg.sender;\\n    s_pendingOwner = address(0);\\n\\n    emit OwnershipTransferred(oldOwner, msg.sender);\\n  }\\n\\n  /// @notice validate access\\n  function _validateOwnership() internal view {\\n    if (msg.sender != s_owner) {\\n      revert OnlyCallableByOwner();\\n    }\\n  }\\n\\n  /// @notice Reverts if called by anyone other than the contract owner.\\n  modifier onlyOwner() {\\n    _validateOwnership();\\n    _;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/access/Ownable2StepMsgSender.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.4;\\n\\nimport {Ownable2Step} from \\\"./Ownable2Step.sol\\\";\\n\\n/// @notice Sets the msg.sender to be the owner of the contract and does not set a pending owner.\\ncontract Ownable2StepMsgSender is Ownable2Step {\\n  constructor() Ownable2Step(msg.sender, address(0)) {}\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/enumerable/EnumerableMapAddresses.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n/* solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore */\\npragma solidity ^0.8.0;\\n\\nimport {EnumerableMap} from \\\"../../vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableMap.sol\\\";\\nimport {EnumerableMapBytes32} from \\\"./EnumerableMapBytes32.sol\\\";\\n\\n// TODO: the lib can be replaced with OZ v5.1 post-upgrade, which has AddressToAddressMap and AddressToBytes32Map\\nlibrary EnumerableMapAddresses {\\n  using EnumerableMap for EnumerableMap.UintToAddressMap;\\n  using EnumerableMap for EnumerableMap.Bytes32ToBytes32Map;\\n  using EnumerableMapBytes32 for EnumerableMapBytes32.Bytes32ToBytesMap;\\n\\n  struct AddressToAddressMap {\\n    EnumerableMap.UintToAddressMap _inner;\\n  }\\n\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function set(AddressToAddressMap storage map, address key, address value) internal returns (bool) {\\n    return map._inner.set(uint256(uint160(key)), value);\\n  }\\n\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function remove(AddressToAddressMap storage map, address key) internal returns (bool) {\\n    return map._inner.remove(uint256(uint160(key)));\\n  }\\n\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function contains(AddressToAddressMap storage map, address key) internal view returns (bool) {\\n    return map._inner.contains(uint256(uint160(key)));\\n  }\\n\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function length(AddressToAddressMap storage map) internal view returns (uint256) {\\n    return map._inner.length();\\n  }\\n\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function at(AddressToAddressMap storage map, uint256 index) internal view returns (address, address) {\\n    (uint256 key, address value) = map._inner.at(index);\\n    return (address(uint160(key)), value);\\n  }\\n\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function tryGet(AddressToAddressMap storage map, address key) internal view returns (bool, address) {\\n    return map._inner.tryGet(uint256(uint160(key)));\\n  }\\n\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function get(AddressToAddressMap storage map, address key) internal view returns (address) {\\n    return map._inner.get(uint256(uint160(key)));\\n  }\\n\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function get(\\n    AddressToAddressMap storage map,\\n    address key,\\n    string memory errorMessage\\n  ) internal view returns (address) {\\n    return map._inner.get(uint256(uint160(key)), errorMessage);\\n  }\\n\\n  struct AddressToBytes32Map {\\n    EnumerableMap.Bytes32ToBytes32Map _inner;\\n  }\\n\\n  /**\\n   * @dev Adds a key-value pair to a map, or updates the value for an existing\\n   * key. O(1).\\n   *\\n   * Returns true if the key was added to the map, that is if it was not\\n   * already present.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function set(AddressToBytes32Map storage map, address key, bytes32 value) internal returns (bool) {\\n    return map._inner.set(bytes32(uint256(uint160(key))), value);\\n  }\\n\\n  /**\\n   * @dev Removes a value from a map. O(1).\\n   *\\n   * Returns true if the key was removed from the map, that is if it was present.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function remove(AddressToBytes32Map storage map, address key) internal returns (bool) {\\n    return map._inner.remove(bytes32(uint256(uint160(key))));\\n  }\\n\\n  /**\\n   * @dev Returns true if the key is in the map. O(1).\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function contains(AddressToBytes32Map storage map, address key) internal view returns (bool) {\\n    return map._inner.contains(bytes32(uint256(uint160(key))));\\n  }\\n\\n  /**\\n   * @dev Returns the number of elements in the map. O(1).\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function length(AddressToBytes32Map storage map) internal view returns (uint256) {\\n    return map._inner.length();\\n  }\\n\\n  /**\\n   * @dev Returns the element stored at position `index` in the map. O(1).\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function at(AddressToBytes32Map storage map, uint256 index) internal view returns (address, bytes32) {\\n    (bytes32 key, bytes32 value) = map._inner.at(index);\\n    return (address(uint160(uint256(key))), value);\\n  }\\n\\n  /**\\n   * @dev Tries to returns the value associated with `key`. O(1).\\n   * Does not revert if `key` is not in the map.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function tryGet(AddressToBytes32Map storage map, address key) internal view returns (bool, bytes32) {\\n    (bool success, bytes32 value) = map._inner.tryGet(bytes32(uint256(uint160(key))));\\n    return (success, value);\\n  }\\n\\n  /**\\n   * @dev Returns the value associated with `key`. O(1).\\n   *\\n   * Requirements:\\n   *\\n   * - `key` must be in the map.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function get(AddressToBytes32Map storage map, address key) internal view returns (bytes32) {\\n    return map._inner.get(bytes32(uint256(uint160(key))));\\n  }\\n\\n  struct AddressToBytesMap {\\n    EnumerableMapBytes32.Bytes32ToBytesMap _inner;\\n  }\\n\\n  /**\\n   * @dev Sets the value for `key` in the map. Returns true if the key was added to the map, that is if it was not already present.\\n   * @param map The map where the value will be set\\n   * @param key The key to set the value for\\n   * @param value The value to set for the key\\n   * @return bool indicating whether the key was added to the map\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function set(AddressToBytesMap storage map, address key, bytes memory value) internal returns (bool) {\\n    return map._inner.set(bytes32(uint256(uint160(key))), value);\\n  }\\n\\n  /**\\n   * @dev Removes the value for `key` in the map. Returns true if the key was removed from the map, that is if it was present.\\n   * @param map The map where the value will be removed\\n   * @param key The key to remove the value for\\n   * @return bool indicating whether the key was removed from the map\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function remove(AddressToBytesMap storage map, address key) internal returns (bool) {\\n    return map._inner.remove(bytes32(uint256(uint160(key))));\\n  }\\n\\n  /**\\n   * @dev Checks if the map contains the `key`. Returns true if the key is in the map.\\n   * @param map The map to check for the presence of the key\\n   * @param key The key to check for presence in the map\\n   * @return bool indicating whether the key is in the map\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function contains(AddressToBytesMap storage map, address key) internal view returns (bool) {\\n    return map._inner.contains(bytes32(uint256(uint160(key))));\\n  }\\n\\n  /**\\n   * @dev Returns the number of elements in the map.\\n   * @param map The map to check the length of\\n   * @return uint256 indicating the number of elements in the map\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function length(AddressToBytesMap storage map) internal view returns (uint256) {\\n    return map._inner.length();\\n  }\\n\\n  /**\\n   * @dev Returns the element stored at position `index` in the map. Note that there are no guarantees on the ordering of values inside the array, and it may change when more values are added or removed.\\n   * @param map The map to retrieve the element from\\n   * @param index The index to retrieve the element at\\n   * @return address The key of the element at the specified index\\n   * @return bytes The value of the element at the specified index\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function at(AddressToBytesMap storage map, uint256 index) internal view returns (address, bytes memory) {\\n    (bytes32 key, bytes memory value) = map._inner.at(index);\\n    return (address(uint160(uint256(key))), value);\\n  }\\n\\n  /**\\n   * @dev Tries to return the value associated with `key`. Does not revert if `key` is not in the map.\\n   * @param map The map to retrieve the value from\\n   * @param key The key to retrieve the value for\\n   * @return bool indicating whether the key was in the map\\n   * @return bytes The value associated with the key\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function tryGet(AddressToBytesMap storage map, address key) internal view returns (bool, bytes memory) {\\n    return map._inner.tryGet(bytes32(uint256(uint160(key))));\\n  }\\n\\n  /**\\n   * @dev Returns the value associated with `key`.\\n   * @param map The map to retrieve the value from\\n   * @param key The key to retrieve the value for\\n   * @return bytes The value associated with the key\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function get(AddressToBytesMap storage map, address key) internal view returns (bytes memory) {\\n    return map._inner.get(bytes32(uint256(uint160(key))));\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/enumerable/EnumerableMapBytes32.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n/* solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore */\\npragma solidity ^0.8.0;\\n\\nimport {EnumerableSet} from \\\"../../vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\\\";\\n\\n/**\\n * @dev Library for managing an enumerable variant of Solidity's\\n * https://solidity.readthedocs.io/en/latest/types.html#mapping-types[`mapping`]\\n * type.\\n *\\n * Maps have the following properties:\\n *\\n * - Entries are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Entries are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableMapBytes32 for EnumerableMapBytes32.Bytes32ToBytesMap;\\n *\\n *     // Declare a set state variable\\n *     EnumerableMapBytes32.Bytes32ToBytesMap private myMap;\\n * }\\n * ```\\n *\\n * The following map types are supported:\\n *\\n * - `bytes32 -\\u003e bytes` (`Bytes32ToBytes`)\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean up an EnumerableMapBytes32, you should remove all elements one by one.\\n * ====\\n */\\nlibrary EnumerableMapBytes32 {\\n  using EnumerableSet for EnumerableSet.Bytes32Set;\\n\\n  error NonexistentKeyError();\\n\\n  struct Bytes32ToBytesMap {\\n    EnumerableSet.Bytes32Set _keys;\\n    mapping(bytes32 =\\u003e bytes) _values;\\n  }\\n\\n  /**\\n   * @dev Adds a key-value pair to a map, or updates the value for an existing\\n   * key. O(1).\\n   *\\n   * Returns true if the key was added to the map, that is if it was not\\n   * already present.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function set(Bytes32ToBytesMap storage map, bytes32 key, bytes memory value) internal returns (bool) {\\n    map._values[key] = value;\\n    return map._keys.add(key);\\n  }\\n\\n  /**\\n   * @dev Removes a key-value pair from a map. O(1).\\n   *\\n   * Returns true if the key was removed from the map, that is if it was present.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function remove(Bytes32ToBytesMap storage map, bytes32 key) internal returns (bool) {\\n    delete map._values[key];\\n    return map._keys.remove(key);\\n  }\\n\\n  /**\\n   * @dev Returns true if the key is in the map. O(1).\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function contains(Bytes32ToBytesMap storage map, bytes32 key) internal view returns (bool) {\\n    return map._keys.contains(key);\\n  }\\n\\n  /**\\n   * @dev Returns the number of key-value pairs in the map. O(1).\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function length(Bytes32ToBytesMap storage map) internal view returns (uint256) {\\n    return map._keys.length();\\n  }\\n\\n  /**\\n   * @dev Returns the key-value pair stored at position `index` in the map. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of entries inside the\\n   * array, and it may change when more entries are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function at(Bytes32ToBytesMap storage map, uint256 index) internal view returns (bytes32, bytes memory) {\\n    bytes32 key = map._keys.at(index);\\n    return (key, map._values[key]);\\n  }\\n\\n  /**\\n   * @dev Tries to returns the value associated with `key`. O(1).\\n   * Does not revert if `key` is not in the map.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function tryGet(Bytes32ToBytesMap storage map, bytes32 key) internal view returns (bool, bytes memory) {\\n    bytes memory value = map._values[key];\\n    if (value.length == 0) {\\n      return (contains(map, key), bytes(\\\"\\\"));\\n    } else {\\n      return (true, value);\\n    }\\n  }\\n\\n  /**\\n   * @dev Returns the value associated with `key`. O(1).\\n   *\\n   * Requirements:\\n   *\\n   * - `key` must be in the map.\\n   */\\n  // solhint-disable-next-line chainlink-solidity/prefix-internal-functions-with-underscore\\n  function get(Bytes32ToBytesMap storage map, bytes32 key) internal view returns (bytes memory) {\\n    bytes memory value = map._values[key];\\n    if (value.length == 0 \\u0026\\u0026 !contains(map, key)) {\\n      revert NonexistentKeyError();\\n    }\\n    return value;\\n  }\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/IOwnable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface IOwnable {\\n  function owner() external returns (address);\\n\\n  function transferOwnership(address recipient) external;\\n\\n  function acceptOwnership() external;\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/shared/interfaces/ITypeAndVersion.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity ^0.8.0;\\n\\ninterface ITypeAndVersion {\\n  function typeAndVersion() external pure returns (string memory);\\n}\\n\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableMap.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/structs/EnumerableMap.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableMap.js.\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./EnumerableSet.sol\\\";\\n\\n/**\\n * @dev Library for managing an enumerable variant of Solidity's\\n * https://solidity.readthedocs.io/en/latest/types.html#mapping-types[`mapping`]\\n * type.\\n *\\n * Maps have the following properties:\\n *\\n * - Entries are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Entries are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableMap for EnumerableMap.UintToAddressMap;\\n *\\n *     // Declare a set state variable\\n *     EnumerableMap.UintToAddressMap private myMap;\\n * }\\n * ```\\n *\\n * The following map types are supported:\\n *\\n * - `uint256 -\\u003e address` (`UintToAddressMap`) since v3.0.0\\n * - `address -\\u003e uint256` (`AddressToUintMap`) since v4.6.0\\n * - `bytes32 -\\u003e bytes32` (`Bytes32ToBytes32Map`) since v4.6.0\\n * - `uint256 -\\u003e uint256` (`UintToUintMap`) since v4.7.0\\n * - `bytes32 -\\u003e uint256` (`Bytes32ToUintMap`) since v4.7.0\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableMap, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableMap.\\n * ====\\n */\\nlibrary EnumerableMap {\\n    using EnumerableSet for EnumerableSet.Bytes32Set;\\n\\n    // To implement this library for multiple types with as little code\\n    // repetition as possible, we write it in terms of a generic Map type with\\n    // bytes32 keys and values.\\n    // The Map implementation uses private functions, and user-facing\\n    // implementations (such as Uint256ToAddressMap) are just wrappers around\\n    // the underlying Map.\\n    // This means that we can only create new EnumerableMaps for types that fit\\n    // in bytes32.\\n\\n    struct Bytes32ToBytes32Map {\\n        // Storage of keys\\n        EnumerableSet.Bytes32Set _keys;\\n        mapping(bytes32 =\\u003e bytes32) _values;\\n    }\\n\\n    /**\\n     * @dev Adds a key-value pair to a map, or updates the value for an existing\\n     * key. O(1).\\n     *\\n     * Returns true if the key was added to the map, that is if it was not\\n     * already present.\\n     */\\n    function set(\\n        Bytes32ToBytes32Map storage map,\\n        bytes32 key,\\n        bytes32 value\\n    ) internal returns (bool) {\\n        map._values[key] = value;\\n        return map._keys.add(key);\\n    }\\n\\n    /**\\n     * @dev Removes a key-value pair from a map. O(1).\\n     *\\n     * Returns true if the key was removed from the map, that is if it was present.\\n     */\\n    function remove(Bytes32ToBytes32Map storage map, bytes32 key) internal returns (bool) {\\n        delete map._values[key];\\n        return map._keys.remove(key);\\n    }\\n\\n    /**\\n     * @dev Returns true if the key is in the map. O(1).\\n     */\\n    function contains(Bytes32ToBytes32Map storage map, bytes32 key) internal view returns (bool) {\\n        return map._keys.contains(key);\\n    }\\n\\n    /**\\n     * @dev Returns the number of key-value pairs in the map. O(1).\\n     */\\n    function length(Bytes32ToBytes32Map storage map) internal view returns (uint256) {\\n        return map._keys.length();\\n    }\\n\\n    /**\\n     * @dev Returns the key-value pair stored at position `index` in the map. O(1).\\n     *\\n     * Note that there are no guarantees on the ordering of entries inside the\\n     * array, and it may change when more entries are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(Bytes32ToBytes32Map storage map, uint256 index) internal view returns (bytes32, bytes32) {\\n        bytes32 key = map._keys.at(index);\\n        return (key, map._values[key]);\\n    }\\n\\n    /**\\n     * @dev Tries to returns the value associated with `key`. O(1).\\n     * Does not revert if `key` is not in the map.\\n     */\\n    function tryGet(Bytes32ToBytes32Map storage map, bytes32 key) internal view returns (bool, bytes32) {\\n        bytes32 value = map._values[key];\\n        if (value == bytes32(0)) {\\n            return (contains(map, key), bytes32(0));\\n        } else {\\n            return (true, value);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the value associated with `key`. O(1).\\n     *\\n     * Requirements:\\n     *\\n     * - `key` must be in the map.\\n     */\\n    function get(Bytes32ToBytes32Map storage map, bytes32 key) internal view returns (bytes32) {\\n        bytes32 value = map._values[key];\\n        require(value != 0 || contains(map, key), \\\"EnumerableMap: nonexistent key\\\");\\n        return value;\\n    }\\n\\n    /**\\n     * @dev Same as {get}, with a custom error message when `key` is not in the map.\\n     *\\n     * CAUTION: This function is deprecated because it requires allocating memory for the error\\n     * message unnecessarily. For custom revert reasons use {tryGet}.\\n     */\\n    function get(\\n        Bytes32ToBytes32Map storage map,\\n        bytes32 key,\\n        string memory errorMessage\\n    ) internal view returns (bytes32) {\\n        bytes32 value = map._values[key];\\n        require(value != 0 || contains(map, key), errorMessage);\\n        return value;\\n    }\\n\\n    // UintToUintMap\\n\\n    struct UintToUintMap {\\n        Bytes32ToBytes32Map _inner;\\n    }\\n\\n    /**\\n     * @dev Adds a key-value pair to a map, or updates the value for an existing\\n     * key. O(1).\\n     *\\n     * Returns true if the key was added to the map, that is if it was not\\n     * already present.\\n     */\\n    function set(\\n        UintToUintMap storage map,\\n        uint256 key,\\n        uint256 value\\n    ) internal returns (bool) {\\n        return set(map._inner, bytes32(key), bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the key was removed from the map, that is if it was present.\\n     */\\n    function remove(UintToUintMap storage map, uint256 key) internal returns (bool) {\\n        return remove(map._inner, bytes32(key));\\n    }\\n\\n    /**\\n     * @dev Returns true if the key is in the map. O(1).\\n     */\\n    function contains(UintToUintMap storage map, uint256 key) internal view returns (bool) {\\n        return contains(map._inner, bytes32(key));\\n    }\\n\\n    /**\\n     * @dev Returns the number of elements in the map. O(1).\\n     */\\n    function length(UintToUintMap storage map) internal view returns (uint256) {\\n        return length(map._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the element stored at position `index` in the set. O(1).\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(UintToUintMap storage map, uint256 index) internal view returns (uint256, uint256) {\\n        (bytes32 key, bytes32 value) = at(map._inner, index);\\n        return (uint256(key), uint256(value));\\n    }\\n\\n    /**\\n     * @dev Tries to returns the value associated with `key`. O(1).\\n     * Does not revert if `key` is not in the map.\\n     */\\n    function tryGet(UintToUintMap storage map, uint256 key) internal view returns (bool, uint256) {\\n        (bool success, bytes32 value) = tryGet(map._inner, bytes32(key));\\n        return (success, uint256(value));\\n    }\\n\\n    /**\\n     * @dev Returns the value associated with `key`. O(1).\\n     *\\n     * Requirements:\\n     *\\n     * - `key` must be in the map.\\n     */\\n    function get(UintToUintMap storage map, uint256 key) internal view returns (uint256) {\\n        return uint256(get(map._inner, bytes32(key)));\\n    }\\n\\n    /**\\n     * @dev Same as {get}, with a custom error message when `key` is not in the map.\\n     *\\n     * CAUTION: This function is deprecated because it requires allocating memory for the error\\n     * message unnecessarily. For custom revert reasons use {tryGet}.\\n     */\\n    function get(\\n        UintToUintMap storage map,\\n        uint256 key,\\n        string memory errorMessage\\n    ) internal view returns (uint256) {\\n        return uint256(get(map._inner, bytes32(key), errorMessage));\\n    }\\n\\n    // UintToAddressMap\\n\\n    struct UintToAddressMap {\\n        Bytes32ToBytes32Map _inner;\\n    }\\n\\n    /**\\n     * @dev Adds a key-value pair to a map, or updates the value for an existing\\n     * key. O(1).\\n     *\\n     * Returns true if the key was added to the map, that is if it was not\\n     * already present.\\n     */\\n    function set(\\n        UintToAddressMap storage map,\\n        uint256 key,\\n        address value\\n    ) internal returns (bool) {\\n        return set(map._inner, bytes32(key), bytes32(uint256(uint160(value))));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the key was removed from the map, that is if it was present.\\n     */\\n    function remove(UintToAddressMap storage map, uint256 key) internal returns (bool) {\\n        return remove(map._inner, bytes32(key));\\n    }\\n\\n    /**\\n     * @dev Returns true if the key is in the map. O(1).\\n     */\\n    function contains(UintToAddressMap storage map, uint256 key) internal view returns (bool) {\\n        return contains(map._inner, bytes32(key));\\n    }\\n\\n    /**\\n     * @dev Returns the number of elements in the map. O(1).\\n     */\\n    function length(UintToAddressMap storage map) internal view returns (uint256) {\\n        return length(map._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the element stored at position `index` in the set. O(1).\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(UintToAddressMap storage map, uint256 index) internal view returns (uint256, address) {\\n        (bytes32 key, bytes32 value) = at(map._inner, index);\\n        return (uint256(key), address(uint160(uint256(value))));\\n    }\\n\\n    /**\\n     * @dev Tries to returns the value associated with `key`. O(1).\\n     * Does not revert if `key` is not in the map.\\n     */\\n    function tryGet(UintToAddressMap storage map, uint256 key) internal view returns (bool, address) {\\n        (bool success, bytes32 value) = tryGet(map._inner, bytes32(key));\\n        return (success, address(uint160(uint256(value))));\\n    }\\n\\n    /**\\n     * @dev Returns the value associated with `key`. O(1).\\n     *\\n     * Requirements:\\n     *\\n     * - `key` must be in the map.\\n     */\\n    function get(UintToAddressMap storage map, uint256 key) internal view returns (address) {\\n        return address(uint160(uint256(get(map._inner, bytes32(key)))));\\n    }\\n\\n    /**\\n     * @dev Same as {get}, with a custom error message when `key` is not in the map.\\n     *\\n     * CAUTION: This function is deprecated because it requires allocating memory for the error\\n     * message unnecessarily. For custom revert reasons use {tryGet}.\\n     */\\n    function get(\\n        UintToAddressMap storage map,\\n        uint256 key,\\n        string memory errorMessage\\n    ) internal view returns (address) {\\n        return address(uint160(uint256(get(map._inner, bytes32(key), errorMessage))));\\n    }\\n\\n    // AddressToUintMap\\n\\n    struct AddressToUintMap {\\n        Bytes32ToBytes32Map _inner;\\n    }\\n\\n    /**\\n     * @dev Adds a key-value pair to a map, or updates the value for an existing\\n     * key. O(1).\\n     *\\n     * Returns true if the key was added to the map, that is if it was not\\n     * already present.\\n     */\\n    function set(\\n        AddressToUintMap storage map,\\n        address key,\\n        uint256 value\\n    ) internal returns (bool) {\\n        return set(map._inner, bytes32(uint256(uint160(key))), bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the key was removed from the map, that is if it was present.\\n     */\\n    function remove(AddressToUintMap storage map, address key) internal returns (bool) {\\n        return remove(map._inner, bytes32(uint256(uint160(key))));\\n    }\\n\\n    /**\\n     * @dev Returns true if the key is in the map. O(1).\\n     */\\n    function contains(AddressToUintMap storage map, address key) internal view returns (bool) {\\n        return contains(map._inner, bytes32(uint256(uint160(key))));\\n    }\\n\\n    /**\\n     * @dev Returns the number of elements in the map. O(1).\\n     */\\n    function length(AddressToUintMap storage map) internal view returns (uint256) {\\n        return length(map._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the element stored at position `index` in the set. O(1).\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(AddressToUintMap storage map, uint256 index) internal view returns (address, uint256) {\\n        (bytes32 key, bytes32 value) = at(map._inner, index);\\n        return (address(uint160(uint256(key))), uint256(value));\\n    }\\n\\n    /**\\n     * @dev Tries to returns the value associated with `key`. O(1).\\n     * Does not revert if `key` is not in the map.\\n     */\\n    function tryGet(AddressToUintMap storage map, address key) internal view returns (bool, uint256) {\\n        (bool success, bytes32 value) = tryGet(map._inner, bytes32(uint256(uint160(key))));\\n        return (success, uint256(value));\\n    }\\n\\n    /**\\n     * @dev Returns the value associated with `key`. O(1).\\n     *\\n     * Requirements:\\n     *\\n     * - `key` must be in the map.\\n     */\\n    function get(AddressToUintMap storage map, address key) internal view returns (uint256) {\\n        return uint256(get(map._inner, bytes32(uint256(uint160(key)))));\\n    }\\n\\n    /**\\n     * @dev Same as {get}, with a custom error message when `key` is not in the map.\\n     *\\n     * CAUTION: This function is deprecated because it requires allocating memory for the error\\n     * message unnecessarily. For custom revert reasons use {tryGet}.\\n     */\\n    function get(\\n        AddressToUintMap storage map,\\n        address key,\\n        string memory errorMessage\\n    ) internal view returns (uint256) {\\n        return uint256(get(map._inner, bytes32(uint256(uint160(key))), errorMessage));\\n    }\\n\\n    // Bytes32ToUintMap\\n\\n    struct Bytes32ToUintMap {\\n        Bytes32ToBytes32Map _inner;\\n    }\\n\\n    /**\\n     * @dev Adds a key-value pair to a map, or updates the value for an existing\\n     * key. O(1).\\n     *\\n     * Returns true if the key was added to the map, that is if it was not\\n     * already present.\\n     */\\n    function set(\\n        Bytes32ToUintMap storage map,\\n        bytes32 key,\\n        uint256 value\\n    ) internal returns (bool) {\\n        return set(map._inner, key, bytes32(value));\\n    }\\n\\n    /**\\n     * @dev Removes a value from a set. O(1).\\n     *\\n     * Returns true if the key was removed from the map, that is if it was present.\\n     */\\n    function remove(Bytes32ToUintMap storage map, bytes32 key) internal returns (bool) {\\n        return remove(map._inner, key);\\n    }\\n\\n    /**\\n     * @dev Returns true if the key is in the map. O(1).\\n     */\\n    function contains(Bytes32ToUintMap storage map, bytes32 key) internal view returns (bool) {\\n        return contains(map._inner, key);\\n    }\\n\\n    /**\\n     * @dev Returns the number of elements in the map. O(1).\\n     */\\n    function length(Bytes32ToUintMap storage map) internal view returns (uint256) {\\n        return length(map._inner);\\n    }\\n\\n    /**\\n     * @dev Returns the element stored at position `index` in the set. O(1).\\n     * Note that there are no guarantees on the ordering of values inside the\\n     * array, and it may change when more values are added or removed.\\n     *\\n     * Requirements:\\n     *\\n     * - `index` must be strictly less than {length}.\\n     */\\n    function at(Bytes32ToUintMap storage map, uint256 index) internal view returns (bytes32, uint256) {\\n        (bytes32 key, bytes32 value) = at(map._inner, index);\\n        return (key, uint256(value));\\n    }\\n\\n    /**\\n     * @dev Tries to returns the value associated with `key`. O(1).\\n     * Does not revert if `key` is not in the map.\\n     */\\n    function tryGet(Bytes32ToUintMap storage map, bytes32 key) internal view returns (bool, uint256) {\\n        (bool success, bytes32 value) = tryGet(map._inner, key);\\n        return (success, uint256(value));\\n    }\\n\\n    /**\\n     * @dev Returns the value associated with `key`. O(1).\\n     *\\n     * Requirements:\\n     *\\n     * - `key` must be in the map.\\n     */\\n    function get(Bytes32ToUintMap storage map, bytes32 key) internal view returns (uint256) {\\n        return uint256(get(map._inner, key));\\n    }\\n\\n    /**\\n     * @dev Same as {get}, with a custom error message when `key` is not in the map.\\n     *\\n     * CAUTION: This function is deprecated because it requires allocating memory for the error\\n     * message unnecessarily. For custom revert reasons use {tryGet}.\\n     */\\n    function get(\\n        Bytes32ToUintMap storage map,\\n        bytes32 key,\\n        string memory errorMessage\\n    ) internal view returns (uint256) {\\n        return uint256(get(map._inner, key, errorMessage));\\n    }\\n}\"},\"node_modules/@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/utils/structs/EnumerableSet.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.8.0) (utils/structs/EnumerableSet.sol)\\n// This file was procedurally generated from scripts/generate/templates/EnumerableSet.js.\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Library for managing\\n * https://en.wikipedia.org/wiki/Set_(abstract_data_type)[sets] of primitive\\n * types.\\n *\\n * Sets have the following properties:\\n *\\n * - Elements are added, removed, and checked for existence in constant time\\n * (O(1)).\\n * - Elements are enumerated in O(n). No guarantees are made on the ordering.\\n *\\n * ```\\n * contract Example {\\n *     // Add the library methods\\n *     using EnumerableSet for EnumerableSet.AddressSet;\\n *\\n *     // Declare a set state variable\\n *     EnumerableSet.AddressSet private mySet;\\n * }\\n * ```\\n *\\n * As of v3.3.0, sets of type `bytes32` (`Bytes32Set`), `address` (`AddressSet`)\\n * and `uint256` (`UintSet`) are supported.\\n *\\n * [WARNING]\\n * ====\\n * Trying to delete such a structure from storage will likely result in data corruption, rendering the structure\\n * unusable.\\n * See https://github.com/ethereum/solidity/pull/11843[ethereum/solidity#11843] for more info.\\n *\\n * In order to clean an EnumerableSet, you can either remove all elements one by one or create a fresh instance using an\\n * array of EnumerableSet.\\n * ====\\n */\\nlibrary EnumerableSet {\\n  // To implement this library for multiple types with as little code\\n  // repetition as possible, we write it in terms of a generic Set type with\\n  // bytes32 values.\\n  // The Set implementation uses private functions, and user-facing\\n  // implementations (such as AddressSet) are just wrappers around the\\n  // underlying Set.\\n  // This means that we can only create new EnumerableSets for types that fit\\n  // in bytes32.\\n\\n  struct Set {\\n    // Storage of set values\\n    bytes32[] _values;\\n    // Position of the value in the `values` array, plus 1 because index 0\\n    // means a value is not in the set.\\n    mapping(bytes32 =\\u003e uint256) _indexes;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function _add(Set storage set, bytes32 value) private returns (bool) {\\n    if (!_contains(set, value)) {\\n      set._values.push(value);\\n      // The value is stored at length-1, but we add 1 to all indexes\\n      // and use 0 as a sentinel value\\n      set._indexes[value] = set._values.length;\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function _remove(Set storage set, bytes32 value) private returns (bool) {\\n    // We read and store the value's index to prevent multiple reads from the same storage slot\\n    uint256 valueIndex = set._indexes[value];\\n\\n    if (valueIndex != 0) {\\n      // Equivalent to contains(set, value)\\n      // To delete an element from the _values array in O(1), we swap the element to delete with the last one in\\n      // the array, and then remove the last element (sometimes called as 'swap and pop').\\n      // This modifies the order of the array, as noted in {at}.\\n\\n      uint256 toDeleteIndex = valueIndex - 1;\\n      uint256 lastIndex = set._values.length - 1;\\n\\n      if (lastIndex != toDeleteIndex) {\\n        bytes32 lastValue = set._values[lastIndex];\\n\\n        // Move the last value to the index where the value to delete is\\n        set._values[toDeleteIndex] = lastValue;\\n        // Update the index for the moved value\\n        set._indexes[lastValue] = valueIndex; // Replace lastValue's index to valueIndex\\n      }\\n\\n      // Delete the slot where the moved value was stored\\n      set._values.pop();\\n\\n      // Delete the index for the deleted slot\\n      delete set._indexes[value];\\n\\n      return true;\\n    } else {\\n      return false;\\n    }\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function _contains(Set storage set, bytes32 value) private view returns (bool) {\\n    return set._indexes[value] != 0;\\n  }\\n\\n  /**\\n   * @dev Returns the number of values on the set. O(1).\\n   */\\n  function _length(Set storage set) private view returns (uint256) {\\n    return set._values.length;\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function _at(Set storage set, uint256 index) private view returns (bytes32) {\\n    return set._values[index];\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function _values(Set storage set) private view returns (bytes32[] memory) {\\n    return set._values;\\n  }\\n\\n  // Bytes32Set\\n\\n  struct Bytes32Set {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _add(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(Bytes32Set storage set, bytes32 value) internal returns (bool) {\\n    return _remove(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(Bytes32Set storage set, bytes32 value) internal view returns (bool) {\\n    return _contains(set._inner, value);\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(Bytes32Set storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(Bytes32Set storage set, uint256 index) internal view returns (bytes32) {\\n    return _at(set._inner, index);\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(Bytes32Set storage set) internal view returns (bytes32[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    bytes32[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // AddressSet\\n\\n  struct AddressSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(AddressSet storage set, address value) internal returns (bool) {\\n    return _add(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(AddressSet storage set, address value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(AddressSet storage set, address value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(uint256(uint160(value))));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(AddressSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(AddressSet storage set, uint256 index) internal view returns (address) {\\n    return address(uint160(uint256(_at(set._inner, index))));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(AddressSet storage set) internal view returns (address[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    address[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n\\n  // UintSet\\n\\n  struct UintSet {\\n    Set _inner;\\n  }\\n\\n  /**\\n   * @dev Add a value to a set. O(1).\\n   *\\n   * Returns true if the value was added to the set, that is if it was not\\n   * already present.\\n   */\\n  function add(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _add(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Removes a value from a set. O(1).\\n   *\\n   * Returns true if the value was removed from the set, that is if it was\\n   * present.\\n   */\\n  function remove(UintSet storage set, uint256 value) internal returns (bool) {\\n    return _remove(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns true if the value is in the set. O(1).\\n   */\\n  function contains(UintSet storage set, uint256 value) internal view returns (bool) {\\n    return _contains(set._inner, bytes32(value));\\n  }\\n\\n  /**\\n   * @dev Returns the number of values in the set. O(1).\\n   */\\n  function length(UintSet storage set) internal view returns (uint256) {\\n    return _length(set._inner);\\n  }\\n\\n  /**\\n   * @dev Returns the value stored at position `index` in the set. O(1).\\n   *\\n   * Note that there are no guarantees on the ordering of values inside the\\n   * array, and it may change when more values are added or removed.\\n   *\\n   * Requirements:\\n   *\\n   * - `index` must be strictly less than {length}.\\n   */\\n  function at(UintSet storage set, uint256 index) internal view returns (uint256) {\\n    return uint256(_at(set._inner, index));\\n  }\\n\\n  /**\\n   * @dev Return the entire set in an array\\n   *\\n   * WARNING: This operation will copy the entire storage to memory, which can be quite expensive. This is designed\\n   * to mostly be used by view accessors that are queried without any gas fees. Developers should keep in mind that\\n   * this function has an unbounded cost, and using it as part of a state-changing function may render the function\\n   * uncallable if the set grows to a point where copying to memory consumes too much gas to fit in a block.\\n   */\\n  function values(UintSet storage set) internal view returns (uint256[] memory) {\\n    bytes32[] memory store = _values(set._inner);\\n    uint256[] memory result;\\n\\n    /// @solidity memory-safe-assembly\\n    assembly {\\n      result := store\\n    }\\n\\n    return result;\\n  }\\n}\\n\"}}}"
