// Code generated by mockery v2.52.3. DO NOT EDIT.

package v1

import (
	exectypes "github.com/smartcontractkit/chainlink-ccip/execute/exectypes"
	mock "github.com/stretchr/testify/mock"
)

// MockExecCodec is an autogenerated mock type for the ExecCodec type
type MockExecCodec struct {
	mock.Mock
}

type MockExecCodec_Expecter struct {
	mock *mock.Mock
}

func (_m *MockExecCodec) EXPECT() *MockExecCodec_Expecter {
	return &MockExecCodec_Expecter{mock: &_m.Mock}
}

// DecodeObservation provides a mock function with given fields: data
func (_m *MockExecCodec) DecodeObservation(data []byte) (exectypes.Observation, error) {
	ret := _m.Called(data)

	if len(ret) == 0 {
		panic("no return value specified for DecodeObservation")
	}

	var r0 exectypes.Observation
	var r1 error
	if rf, ok := ret.Get(0).(func([]byte) (exectypes.Observation, error)); ok {
		return rf(data)
	}
	if rf, ok := ret.Get(0).(func([]byte) exectypes.Observation); ok {
		r0 = rf(data)
	} else {
		r0 = ret.Get(0).(exectypes.Observation)
	}

	if rf, ok := ret.Get(1).(func([]byte) error); ok {
		r1 = rf(data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExecCodec_DecodeObservation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecodeObservation'
type MockExecCodec_DecodeObservation_Call struct {
	*mock.Call
}

// DecodeObservation is a helper method to define mock.On call
//   - data []byte
func (_e *MockExecCodec_Expecter) DecodeObservation(data interface{}) *MockExecCodec_DecodeObservation_Call {
	return &MockExecCodec_DecodeObservation_Call{Call: _e.mock.On("DecodeObservation", data)}
}

func (_c *MockExecCodec_DecodeObservation_Call) Run(run func(data []byte)) *MockExecCodec_DecodeObservation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]byte))
	})
	return _c
}

func (_c *MockExecCodec_DecodeObservation_Call) Return(_a0 exectypes.Observation, _a1 error) *MockExecCodec_DecodeObservation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExecCodec_DecodeObservation_Call) RunAndReturn(run func([]byte) (exectypes.Observation, error)) *MockExecCodec_DecodeObservation_Call {
	_c.Call.Return(run)
	return _c
}

// DecodeOutcome provides a mock function with given fields: data
func (_m *MockExecCodec) DecodeOutcome(data []byte) (exectypes.Outcome, error) {
	ret := _m.Called(data)

	if len(ret) == 0 {
		panic("no return value specified for DecodeOutcome")
	}

	var r0 exectypes.Outcome
	var r1 error
	if rf, ok := ret.Get(0).(func([]byte) (exectypes.Outcome, error)); ok {
		return rf(data)
	}
	if rf, ok := ret.Get(0).(func([]byte) exectypes.Outcome); ok {
		r0 = rf(data)
	} else {
		r0 = ret.Get(0).(exectypes.Outcome)
	}

	if rf, ok := ret.Get(1).(func([]byte) error); ok {
		r1 = rf(data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExecCodec_DecodeOutcome_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecodeOutcome'
type MockExecCodec_DecodeOutcome_Call struct {
	*mock.Call
}

// DecodeOutcome is a helper method to define mock.On call
//   - data []byte
func (_e *MockExecCodec_Expecter) DecodeOutcome(data interface{}) *MockExecCodec_DecodeOutcome_Call {
	return &MockExecCodec_DecodeOutcome_Call{Call: _e.mock.On("DecodeOutcome", data)}
}

func (_c *MockExecCodec_DecodeOutcome_Call) Run(run func(data []byte)) *MockExecCodec_DecodeOutcome_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]byte))
	})
	return _c
}

func (_c *MockExecCodec_DecodeOutcome_Call) Return(_a0 exectypes.Outcome, _a1 error) *MockExecCodec_DecodeOutcome_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExecCodec_DecodeOutcome_Call) RunAndReturn(run func([]byte) (exectypes.Outcome, error)) *MockExecCodec_DecodeOutcome_Call {
	_c.Call.Return(run)
	return _c
}

// EncodeObservation provides a mock function with given fields: observation
func (_m *MockExecCodec) EncodeObservation(observation exectypes.Observation) ([]byte, error) {
	ret := _m.Called(observation)

	if len(ret) == 0 {
		panic("no return value specified for EncodeObservation")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(exectypes.Observation) ([]byte, error)); ok {
		return rf(observation)
	}
	if rf, ok := ret.Get(0).(func(exectypes.Observation) []byte); ok {
		r0 = rf(observation)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(exectypes.Observation) error); ok {
		r1 = rf(observation)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExecCodec_EncodeObservation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EncodeObservation'
type MockExecCodec_EncodeObservation_Call struct {
	*mock.Call
}

// EncodeObservation is a helper method to define mock.On call
//   - observation exectypes.Observation
func (_e *MockExecCodec_Expecter) EncodeObservation(observation interface{}) *MockExecCodec_EncodeObservation_Call {
	return &MockExecCodec_EncodeObservation_Call{Call: _e.mock.On("EncodeObservation", observation)}
}

func (_c *MockExecCodec_EncodeObservation_Call) Run(run func(observation exectypes.Observation)) *MockExecCodec_EncodeObservation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(exectypes.Observation))
	})
	return _c
}

func (_c *MockExecCodec_EncodeObservation_Call) Return(_a0 []byte, _a1 error) *MockExecCodec_EncodeObservation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExecCodec_EncodeObservation_Call) RunAndReturn(run func(exectypes.Observation) ([]byte, error)) *MockExecCodec_EncodeObservation_Call {
	_c.Call.Return(run)
	return _c
}

// EncodeOutcome provides a mock function with given fields: outcome
func (_m *MockExecCodec) EncodeOutcome(outcome exectypes.Outcome) ([]byte, error) {
	ret := _m.Called(outcome)

	if len(ret) == 0 {
		panic("no return value specified for EncodeOutcome")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(exectypes.Outcome) ([]byte, error)); ok {
		return rf(outcome)
	}
	if rf, ok := ret.Get(0).(func(exectypes.Outcome) []byte); ok {
		r0 = rf(outcome)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(exectypes.Outcome) error); ok {
		r1 = rf(outcome)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockExecCodec_EncodeOutcome_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EncodeOutcome'
type MockExecCodec_EncodeOutcome_Call struct {
	*mock.Call
}

// EncodeOutcome is a helper method to define mock.On call
//   - outcome exectypes.Outcome
func (_e *MockExecCodec_Expecter) EncodeOutcome(outcome interface{}) *MockExecCodec_EncodeOutcome_Call {
	return &MockExecCodec_EncodeOutcome_Call{Call: _e.mock.On("EncodeOutcome", outcome)}
}

func (_c *MockExecCodec_EncodeOutcome_Call) Run(run func(outcome exectypes.Outcome)) *MockExecCodec_EncodeOutcome_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(exectypes.Outcome))
	})
	return _c
}

func (_c *MockExecCodec_EncodeOutcome_Call) Return(_a0 []byte, _a1 error) *MockExecCodec_EncodeOutcome_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockExecCodec_EncodeOutcome_Call) RunAndReturn(run func(exectypes.Outcome) ([]byte, error)) *MockExecCodec_EncodeOutcome_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockExecCodec creates a new instance of MockExecCodec. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockExecCodec(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockExecCodec {
	mock := &MockExecCodec{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
