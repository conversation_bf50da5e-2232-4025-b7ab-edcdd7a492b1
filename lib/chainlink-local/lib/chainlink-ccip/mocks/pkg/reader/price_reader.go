// Code generated by mockery v2.52.3. DO NOT EDIT.

package reader

import (
	context "context"

	ccipocr3 "github.com/smartcontractkit/chainlink-ccip/pkg/types/ccipocr3"

	mock "github.com/stretchr/testify/mock"
)

// MockPriceReader is an autogenerated mock type for the PriceReader type
type MockPriceReader struct {
	mock.Mock
}

type MockPriceReader_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPriceReader) EXPECT() *MockPriceReader_Expecter {
	return &MockPriceReader_Expecter{mock: &_m.Mock}
}

// GetFeeQuoterTokenUpdates provides a mock function with given fields: ctx, tokens, chain
func (_m *MockPriceReader) GetFeeQuoterTokenUpdates(ctx context.Context, tokens []ccipocr3.UnknownEncodedAddress, chain ccipocr3.ChainSelector) (map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedBig, error) {
	ret := _m.Called(ctx, tokens, chain)

	if len(ret) == 0 {
		panic("no return value specified for GetFeeQuoterTokenUpdates")
	}

	var r0 map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedBig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.UnknownEncodedAddress, ccipocr3.ChainSelector) (map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedBig, error)); ok {
		return rf(ctx, tokens, chain)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.UnknownEncodedAddress, ccipocr3.ChainSelector) map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedBig); ok {
		r0 = rf(ctx, tokens, chain)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedBig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []ccipocr3.UnknownEncodedAddress, ccipocr3.ChainSelector) error); ok {
		r1 = rf(ctx, tokens, chain)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPriceReader_GetFeeQuoterTokenUpdates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeeQuoterTokenUpdates'
type MockPriceReader_GetFeeQuoterTokenUpdates_Call struct {
	*mock.Call
}

// GetFeeQuoterTokenUpdates is a helper method to define mock.On call
//   - ctx context.Context
//   - tokens []ccipocr3.UnknownEncodedAddress
//   - chain ccipocr3.ChainSelector
func (_e *MockPriceReader_Expecter) GetFeeQuoterTokenUpdates(ctx interface{}, tokens interface{}, chain interface{}) *MockPriceReader_GetFeeQuoterTokenUpdates_Call {
	return &MockPriceReader_GetFeeQuoterTokenUpdates_Call{Call: _e.mock.On("GetFeeQuoterTokenUpdates", ctx, tokens, chain)}
}

func (_c *MockPriceReader_GetFeeQuoterTokenUpdates_Call) Run(run func(ctx context.Context, tokens []ccipocr3.UnknownEncodedAddress, chain ccipocr3.ChainSelector)) *MockPriceReader_GetFeeQuoterTokenUpdates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.UnknownEncodedAddress), args[2].(ccipocr3.ChainSelector))
	})
	return _c
}

func (_c *MockPriceReader_GetFeeQuoterTokenUpdates_Call) Return(_a0 map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedBig, _a1 error) *MockPriceReader_GetFeeQuoterTokenUpdates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPriceReader_GetFeeQuoterTokenUpdates_Call) RunAndReturn(run func(context.Context, []ccipocr3.UnknownEncodedAddress, ccipocr3.ChainSelector) (map[ccipocr3.UnknownEncodedAddress]ccipocr3.TimestampedBig, error)) *MockPriceReader_GetFeeQuoterTokenUpdates_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeedPricesUSD provides a mock function with given fields: ctx, tokens
func (_m *MockPriceReader) GetFeedPricesUSD(ctx context.Context, tokens []ccipocr3.UnknownEncodedAddress) (ccipocr3.TokenPriceMap, error) {
	ret := _m.Called(ctx, tokens)

	if len(ret) == 0 {
		panic("no return value specified for GetFeedPricesUSD")
	}

	var r0 ccipocr3.TokenPriceMap
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.UnknownEncodedAddress) (ccipocr3.TokenPriceMap, error)); ok {
		return rf(ctx, tokens)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []ccipocr3.UnknownEncodedAddress) ccipocr3.TokenPriceMap); ok {
		r0 = rf(ctx, tokens)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ccipocr3.TokenPriceMap)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []ccipocr3.UnknownEncodedAddress) error); ok {
		r1 = rf(ctx, tokens)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPriceReader_GetFeedPricesUSD_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeedPricesUSD'
type MockPriceReader_GetFeedPricesUSD_Call struct {
	*mock.Call
}

// GetFeedPricesUSD is a helper method to define mock.On call
//   - ctx context.Context
//   - tokens []ccipocr3.UnknownEncodedAddress
func (_e *MockPriceReader_Expecter) GetFeedPricesUSD(ctx interface{}, tokens interface{}) *MockPriceReader_GetFeedPricesUSD_Call {
	return &MockPriceReader_GetFeedPricesUSD_Call{Call: _e.mock.On("GetFeedPricesUSD", ctx, tokens)}
}

func (_c *MockPriceReader_GetFeedPricesUSD_Call) Run(run func(ctx context.Context, tokens []ccipocr3.UnknownEncodedAddress)) *MockPriceReader_GetFeedPricesUSD_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]ccipocr3.UnknownEncodedAddress))
	})
	return _c
}

func (_c *MockPriceReader_GetFeedPricesUSD_Call) Return(_a0 ccipocr3.TokenPriceMap, _a1 error) *MockPriceReader_GetFeedPricesUSD_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPriceReader_GetFeedPricesUSD_Call) RunAndReturn(run func(context.Context, []ccipocr3.UnknownEncodedAddress) (ccipocr3.TokenPriceMap, error)) *MockPriceReader_GetFeedPricesUSD_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPriceReader creates a new instance of MockPriceReader. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPriceReader(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPriceReader {
	mock := &MockPriceReader{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
