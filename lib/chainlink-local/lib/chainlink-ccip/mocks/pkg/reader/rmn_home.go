// Code generated by mockery v2.52.3. DO NOT EDIT.

package reader

import (
	context "context"

	ccipocr3 "github.com/smartcontractkit/chainlink-ccip/pkg/types/ccipocr3"

	mock "github.com/stretchr/testify/mock"

	types "github.com/smartcontractkit/chainlink-ccip/commit/merkleroot/rmn/types"
)

// MockRMNHome is an autogenerated mock type for the RMNHome type
type MockRMNHome struct {
	mock.Mock
}

type MockRMNHome_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRMNHome) EXPECT() *MockRMNHome_Expecter {
	return &MockRMNHome_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with no fields
func (_m *MockRMNHome) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRMNHome_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type MockRMNHome_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *MockRMNHome_Expecter) Close() *MockRMNHome_Close_Call {
	return &MockRMNHome_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *MockRMNHome_Close_Call) Run(run func()) *MockRMNHome_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRMNHome_Close_Call) Return(_a0 error) *MockRMNHome_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRMNHome_Close_Call) RunAndReturn(run func() error) *MockRMNHome_Close_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllConfigDigests provides a mock function with no fields
func (_m *MockRMNHome) GetAllConfigDigests() (ccipocr3.Bytes32, ccipocr3.Bytes32) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllConfigDigests")
	}

	var r0 ccipocr3.Bytes32
	var r1 ccipocr3.Bytes32
	if rf, ok := ret.Get(0).(func() (ccipocr3.Bytes32, ccipocr3.Bytes32)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() ccipocr3.Bytes32); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ccipocr3.Bytes32)
		}
	}

	if rf, ok := ret.Get(1).(func() ccipocr3.Bytes32); ok {
		r1 = rf()
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(ccipocr3.Bytes32)
		}
	}

	return r0, r1
}

// MockRMNHome_GetAllConfigDigests_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllConfigDigests'
type MockRMNHome_GetAllConfigDigests_Call struct {
	*mock.Call
}

// GetAllConfigDigests is a helper method to define mock.On call
func (_e *MockRMNHome_Expecter) GetAllConfigDigests() *MockRMNHome_GetAllConfigDigests_Call {
	return &MockRMNHome_GetAllConfigDigests_Call{Call: _e.mock.On("GetAllConfigDigests")}
}

func (_c *MockRMNHome_GetAllConfigDigests_Call) Run(run func()) *MockRMNHome_GetAllConfigDigests_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRMNHome_GetAllConfigDigests_Call) Return(activeConfigDigest ccipocr3.Bytes32, candidateConfigDigest ccipocr3.Bytes32) *MockRMNHome_GetAllConfigDigests_Call {
	_c.Call.Return(activeConfigDigest, candidateConfigDigest)
	return _c
}

func (_c *MockRMNHome_GetAllConfigDigests_Call) RunAndReturn(run func() (ccipocr3.Bytes32, ccipocr3.Bytes32)) *MockRMNHome_GetAllConfigDigests_Call {
	_c.Call.Return(run)
	return _c
}

// GetFObserve provides a mock function with given fields: configDigest
func (_m *MockRMNHome) GetFObserve(configDigest ccipocr3.Bytes32) (map[ccipocr3.ChainSelector]int, error) {
	ret := _m.Called(configDigest)

	if len(ret) == 0 {
		panic("no return value specified for GetFObserve")
	}

	var r0 map[ccipocr3.ChainSelector]int
	var r1 error
	if rf, ok := ret.Get(0).(func(ccipocr3.Bytes32) (map[ccipocr3.ChainSelector]int, error)); ok {
		return rf(configDigest)
	}
	if rf, ok := ret.Get(0).(func(ccipocr3.Bytes32) map[ccipocr3.ChainSelector]int); ok {
		r0 = rf(configDigest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]int)
		}
	}

	if rf, ok := ret.Get(1).(func(ccipocr3.Bytes32) error); ok {
		r1 = rf(configDigest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRMNHome_GetFObserve_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFObserve'
type MockRMNHome_GetFObserve_Call struct {
	*mock.Call
}

// GetFObserve is a helper method to define mock.On call
//   - configDigest ccipocr3.Bytes32
func (_e *MockRMNHome_Expecter) GetFObserve(configDigest interface{}) *MockRMNHome_GetFObserve_Call {
	return &MockRMNHome_GetFObserve_Call{Call: _e.mock.On("GetFObserve", configDigest)}
}

func (_c *MockRMNHome_GetFObserve_Call) Run(run func(configDigest ccipocr3.Bytes32)) *MockRMNHome_GetFObserve_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ccipocr3.Bytes32))
	})
	return _c
}

func (_c *MockRMNHome_GetFObserve_Call) Return(_a0 map[ccipocr3.ChainSelector]int, _a1 error) *MockRMNHome_GetFObserve_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRMNHome_GetFObserve_Call) RunAndReturn(run func(ccipocr3.Bytes32) (map[ccipocr3.ChainSelector]int, error)) *MockRMNHome_GetFObserve_Call {
	_c.Call.Return(run)
	return _c
}

// GetOffChainConfig provides a mock function with given fields: configDigest
func (_m *MockRMNHome) GetOffChainConfig(configDigest ccipocr3.Bytes32) (ccipocr3.Bytes, error) {
	ret := _m.Called(configDigest)

	if len(ret) == 0 {
		panic("no return value specified for GetOffChainConfig")
	}

	var r0 ccipocr3.Bytes
	var r1 error
	if rf, ok := ret.Get(0).(func(ccipocr3.Bytes32) (ccipocr3.Bytes, error)); ok {
		return rf(configDigest)
	}
	if rf, ok := ret.Get(0).(func(ccipocr3.Bytes32) ccipocr3.Bytes); ok {
		r0 = rf(configDigest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ccipocr3.Bytes)
		}
	}

	if rf, ok := ret.Get(1).(func(ccipocr3.Bytes32) error); ok {
		r1 = rf(configDigest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRMNHome_GetOffChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOffChainConfig'
type MockRMNHome_GetOffChainConfig_Call struct {
	*mock.Call
}

// GetOffChainConfig is a helper method to define mock.On call
//   - configDigest ccipocr3.Bytes32
func (_e *MockRMNHome_Expecter) GetOffChainConfig(configDigest interface{}) *MockRMNHome_GetOffChainConfig_Call {
	return &MockRMNHome_GetOffChainConfig_Call{Call: _e.mock.On("GetOffChainConfig", configDigest)}
}

func (_c *MockRMNHome_GetOffChainConfig_Call) Run(run func(configDigest ccipocr3.Bytes32)) *MockRMNHome_GetOffChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ccipocr3.Bytes32))
	})
	return _c
}

func (_c *MockRMNHome_GetOffChainConfig_Call) Return(_a0 ccipocr3.Bytes, _a1 error) *MockRMNHome_GetOffChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRMNHome_GetOffChainConfig_Call) RunAndReturn(run func(ccipocr3.Bytes32) (ccipocr3.Bytes, error)) *MockRMNHome_GetOffChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetRMNEnabledSourceChains provides a mock function with given fields: configDigest
func (_m *MockRMNHome) GetRMNEnabledSourceChains(configDigest ccipocr3.Bytes32) (map[ccipocr3.ChainSelector]bool, error) {
	ret := _m.Called(configDigest)

	if len(ret) == 0 {
		panic("no return value specified for GetRMNEnabledSourceChains")
	}

	var r0 map[ccipocr3.ChainSelector]bool
	var r1 error
	if rf, ok := ret.Get(0).(func(ccipocr3.Bytes32) (map[ccipocr3.ChainSelector]bool, error)); ok {
		return rf(configDigest)
	}
	if rf, ok := ret.Get(0).(func(ccipocr3.Bytes32) map[ccipocr3.ChainSelector]bool); ok {
		r0 = rf(configDigest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[ccipocr3.ChainSelector]bool)
		}
	}

	if rf, ok := ret.Get(1).(func(ccipocr3.Bytes32) error); ok {
		r1 = rf(configDigest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRMNHome_GetRMNEnabledSourceChains_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRMNEnabledSourceChains'
type MockRMNHome_GetRMNEnabledSourceChains_Call struct {
	*mock.Call
}

// GetRMNEnabledSourceChains is a helper method to define mock.On call
//   - configDigest ccipocr3.Bytes32
func (_e *MockRMNHome_Expecter) GetRMNEnabledSourceChains(configDigest interface{}) *MockRMNHome_GetRMNEnabledSourceChains_Call {
	return &MockRMNHome_GetRMNEnabledSourceChains_Call{Call: _e.mock.On("GetRMNEnabledSourceChains", configDigest)}
}

func (_c *MockRMNHome_GetRMNEnabledSourceChains_Call) Run(run func(configDigest ccipocr3.Bytes32)) *MockRMNHome_GetRMNEnabledSourceChains_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ccipocr3.Bytes32))
	})
	return _c
}

func (_c *MockRMNHome_GetRMNEnabledSourceChains_Call) Return(_a0 map[ccipocr3.ChainSelector]bool, _a1 error) *MockRMNHome_GetRMNEnabledSourceChains_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRMNHome_GetRMNEnabledSourceChains_Call) RunAndReturn(run func(ccipocr3.Bytes32) (map[ccipocr3.ChainSelector]bool, error)) *MockRMNHome_GetRMNEnabledSourceChains_Call {
	_c.Call.Return(run)
	return _c
}

// GetRMNNodesInfo provides a mock function with given fields: configDigest
func (_m *MockRMNHome) GetRMNNodesInfo(configDigest ccipocr3.Bytes32) ([]types.HomeNodeInfo, error) {
	ret := _m.Called(configDigest)

	if len(ret) == 0 {
		panic("no return value specified for GetRMNNodesInfo")
	}

	var r0 []types.HomeNodeInfo
	var r1 error
	if rf, ok := ret.Get(0).(func(ccipocr3.Bytes32) ([]types.HomeNodeInfo, error)); ok {
		return rf(configDigest)
	}
	if rf, ok := ret.Get(0).(func(ccipocr3.Bytes32) []types.HomeNodeInfo); ok {
		r0 = rf(configDigest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.HomeNodeInfo)
		}
	}

	if rf, ok := ret.Get(1).(func(ccipocr3.Bytes32) error); ok {
		r1 = rf(configDigest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockRMNHome_GetRMNNodesInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRMNNodesInfo'
type MockRMNHome_GetRMNNodesInfo_Call struct {
	*mock.Call
}

// GetRMNNodesInfo is a helper method to define mock.On call
//   - configDigest ccipocr3.Bytes32
func (_e *MockRMNHome_Expecter) GetRMNNodesInfo(configDigest interface{}) *MockRMNHome_GetRMNNodesInfo_Call {
	return &MockRMNHome_GetRMNNodesInfo_Call{Call: _e.mock.On("GetRMNNodesInfo", configDigest)}
}

func (_c *MockRMNHome_GetRMNNodesInfo_Call) Run(run func(configDigest ccipocr3.Bytes32)) *MockRMNHome_GetRMNNodesInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ccipocr3.Bytes32))
	})
	return _c
}

func (_c *MockRMNHome_GetRMNNodesInfo_Call) Return(_a0 []types.HomeNodeInfo, _a1 error) *MockRMNHome_GetRMNNodesInfo_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockRMNHome_GetRMNNodesInfo_Call) RunAndReturn(run func(ccipocr3.Bytes32) ([]types.HomeNodeInfo, error)) *MockRMNHome_GetRMNNodesInfo_Call {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with no fields
func (_m *MockRMNHome) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// MockRMNHome_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type MockRMNHome_HealthReport_Call struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *MockRMNHome_Expecter) HealthReport() *MockRMNHome_HealthReport_Call {
	return &MockRMNHome_HealthReport_Call{Call: _e.mock.On("HealthReport")}
}

func (_c *MockRMNHome_HealthReport_Call) Run(run func()) *MockRMNHome_HealthReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRMNHome_HealthReport_Call) Return(_a0 map[string]error) *MockRMNHome_HealthReport_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRMNHome_HealthReport_Call) RunAndReturn(run func() map[string]error) *MockRMNHome_HealthReport_Call {
	_c.Call.Return(run)
	return _c
}

// IsRMNHomeConfigDigestSet provides a mock function with given fields: configDigest
func (_m *MockRMNHome) IsRMNHomeConfigDigestSet(configDigest ccipocr3.Bytes32) bool {
	ret := _m.Called(configDigest)

	if len(ret) == 0 {
		panic("no return value specified for IsRMNHomeConfigDigestSet")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(ccipocr3.Bytes32) bool); ok {
		r0 = rf(configDigest)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// MockRMNHome_IsRMNHomeConfigDigestSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsRMNHomeConfigDigestSet'
type MockRMNHome_IsRMNHomeConfigDigestSet_Call struct {
	*mock.Call
}

// IsRMNHomeConfigDigestSet is a helper method to define mock.On call
//   - configDigest ccipocr3.Bytes32
func (_e *MockRMNHome_Expecter) IsRMNHomeConfigDigestSet(configDigest interface{}) *MockRMNHome_IsRMNHomeConfigDigestSet_Call {
	return &MockRMNHome_IsRMNHomeConfigDigestSet_Call{Call: _e.mock.On("IsRMNHomeConfigDigestSet", configDigest)}
}

func (_c *MockRMNHome_IsRMNHomeConfigDigestSet_Call) Run(run func(configDigest ccipocr3.Bytes32)) *MockRMNHome_IsRMNHomeConfigDigestSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ccipocr3.Bytes32))
	})
	return _c
}

func (_c *MockRMNHome_IsRMNHomeConfigDigestSet_Call) Return(_a0 bool) *MockRMNHome_IsRMNHomeConfigDigestSet_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRMNHome_IsRMNHomeConfigDigestSet_Call) RunAndReturn(run func(ccipocr3.Bytes32) bool) *MockRMNHome_IsRMNHomeConfigDigestSet_Call {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with no fields
func (_m *MockRMNHome) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// MockRMNHome_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type MockRMNHome_Name_Call struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *MockRMNHome_Expecter) Name() *MockRMNHome_Name_Call {
	return &MockRMNHome_Name_Call{Call: _e.mock.On("Name")}
}

func (_c *MockRMNHome_Name_Call) Run(run func()) *MockRMNHome_Name_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRMNHome_Name_Call) Return(_a0 string) *MockRMNHome_Name_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRMNHome_Name_Call) RunAndReturn(run func() string) *MockRMNHome_Name_Call {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with no fields
func (_m *MockRMNHome) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRMNHome_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type MockRMNHome_Ready_Call struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *MockRMNHome_Expecter) Ready() *MockRMNHome_Ready_Call {
	return &MockRMNHome_Ready_Call{Call: _e.mock.On("Ready")}
}

func (_c *MockRMNHome_Ready_Call) Run(run func()) *MockRMNHome_Ready_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockRMNHome_Ready_Call) Return(_a0 error) *MockRMNHome_Ready_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRMNHome_Ready_Call) RunAndReturn(run func() error) *MockRMNHome_Ready_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *MockRMNHome) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockRMNHome_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type MockRMNHome_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *MockRMNHome_Expecter) Start(_a0 interface{}) *MockRMNHome_Start_Call {
	return &MockRMNHome_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *MockRMNHome_Start_Call) Run(run func(_a0 context.Context)) *MockRMNHome_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockRMNHome_Start_Call) Return(_a0 error) *MockRMNHome_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockRMNHome_Start_Call) RunAndReturn(run func(context.Context) error) *MockRMNHome_Start_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockRMNHome creates a new instance of MockRMNHome. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRMNHome(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRMNHome {
	mock := &MockRMNHome{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
