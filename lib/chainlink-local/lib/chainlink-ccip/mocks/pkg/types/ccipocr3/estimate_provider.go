// Code generated by mockery v2.52.3. DO NOT EDIT.

package ccipocr3

import (
	ccipocr3 "github.com/smartcontractkit/chainlink-ccip/pkg/types/ccipocr3"
	mock "github.com/stretchr/testify/mock"
)

// MockEstimateProvider is an autogenerated mock type for the EstimateProvider type
type MockEstimateProvider struct {
	mock.Mock
}

type MockEstimateProvider_Expecter struct {
	mock *mock.Mock
}

func (_m *MockEstimateProvider) EXPECT() *MockEstimateProvider_Expecter {
	return &MockEstimateProvider_Expecter{mock: &_m.Mock}
}

// CalculateMerkleTreeGas provides a mock function with given fields: numRequests
func (_m *MockEstimateProvider) CalculateMerkleTreeGas(numRequests int) uint64 {
	ret := _m.Called(numRequests)

	if len(ret) == 0 {
		panic("no return value specified for CalculateMerkleTreeGas")
	}

	var r0 uint64
	if rf, ok := ret.Get(0).(func(int) uint64); ok {
		r0 = rf(numRequests)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	return r0
}

// MockEstimateProvider_CalculateMerkleTreeGas_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CalculateMerkleTreeGas'
type MockEstimateProvider_CalculateMerkleTreeGas_Call struct {
	*mock.Call
}

// CalculateMerkleTreeGas is a helper method to define mock.On call
//   - numRequests int
func (_e *MockEstimateProvider_Expecter) CalculateMerkleTreeGas(numRequests interface{}) *MockEstimateProvider_CalculateMerkleTreeGas_Call {
	return &MockEstimateProvider_CalculateMerkleTreeGas_Call{Call: _e.mock.On("CalculateMerkleTreeGas", numRequests)}
}

func (_c *MockEstimateProvider_CalculateMerkleTreeGas_Call) Run(run func(numRequests int)) *MockEstimateProvider_CalculateMerkleTreeGas_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *MockEstimateProvider_CalculateMerkleTreeGas_Call) Return(_a0 uint64) *MockEstimateProvider_CalculateMerkleTreeGas_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockEstimateProvider_CalculateMerkleTreeGas_Call) RunAndReturn(run func(int) uint64) *MockEstimateProvider_CalculateMerkleTreeGas_Call {
	_c.Call.Return(run)
	return _c
}

// CalculateMessageMaxGas provides a mock function with given fields: msg
func (_m *MockEstimateProvider) CalculateMessageMaxGas(msg ccipocr3.Message) uint64 {
	ret := _m.Called(msg)

	if len(ret) == 0 {
		panic("no return value specified for CalculateMessageMaxGas")
	}

	var r0 uint64
	if rf, ok := ret.Get(0).(func(ccipocr3.Message) uint64); ok {
		r0 = rf(msg)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	return r0
}

// MockEstimateProvider_CalculateMessageMaxGas_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CalculateMessageMaxGas'
type MockEstimateProvider_CalculateMessageMaxGas_Call struct {
	*mock.Call
}

// CalculateMessageMaxGas is a helper method to define mock.On call
//   - msg ccipocr3.Message
func (_e *MockEstimateProvider_Expecter) CalculateMessageMaxGas(msg interface{}) *MockEstimateProvider_CalculateMessageMaxGas_Call {
	return &MockEstimateProvider_CalculateMessageMaxGas_Call{Call: _e.mock.On("CalculateMessageMaxGas", msg)}
}

func (_c *MockEstimateProvider_CalculateMessageMaxGas_Call) Run(run func(msg ccipocr3.Message)) *MockEstimateProvider_CalculateMessageMaxGas_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ccipocr3.Message))
	})
	return _c
}

func (_c *MockEstimateProvider_CalculateMessageMaxGas_Call) Return(_a0 uint64) *MockEstimateProvider_CalculateMessageMaxGas_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockEstimateProvider_CalculateMessageMaxGas_Call) RunAndReturn(run func(ccipocr3.Message) uint64) *MockEstimateProvider_CalculateMessageMaxGas_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockEstimateProvider creates a new instance of MockEstimateProvider. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockEstimateProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockEstimateProvider {
	mock := &MockEstimateProvider{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
