// Code generated by mockery v2.52.3. DO NOT EDIT.

package rmn

import (
	context "context"

	ccipocr3 "github.com/smartcontractkit/chainlink-ccip/pkg/types/ccipocr3"

	mock "github.com/stretchr/testify/mock"

	rmn "github.com/smartcontractkit/chainlink-ccip/commit/merkleroot/rmn"

	rmntypes "github.com/smartcontractkit/chainlink-ccip/commit/merkleroot/rmn/types"

	serialization "github.com/smartcontractkit/chainlink-protos/rmn/v1.6/go/serialization"

	types "github.com/smartcontractkit/libocr/ragep2p/types"
)

// MockController is an autogenerated mock type for the Controller type
type MockController struct {
	mock.Mock
}

type MockController_Expecter struct {
	mock *mock.Mock
}

func (_m *MockController) EXPECT() *MockController_Expecter {
	return &MockController_Expecter{mock: &_m.<PERSON>}
}

// Close provides a mock function with no fields
func (_m *MockController) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockController_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type MockController_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *MockController_Expecter) Close() *MockController_Close_Call {
	return &MockController_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *MockController_Close_Call) Run(run func()) *MockController_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockController_Close_Call) Return(_a0 error) *MockController_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockController_Close_Call) RunAndReturn(run func() error) *MockController_Close_Call {
	_c.Call.Return(run)
	return _c
}

// ComputeReportSignatures provides a mock function with given fields: ctx, destChain, requestedUpdates, rmnRemoteCfg
func (_m *MockController) ComputeReportSignatures(ctx context.Context, destChain *serialization.LaneDest, requestedUpdates []*serialization.FixedDestLaneUpdateRequest, rmnRemoteCfg ccipocr3.RemoteConfig) (*rmn.ReportSignatures, error) {
	ret := _m.Called(ctx, destChain, requestedUpdates, rmnRemoteCfg)

	if len(ret) == 0 {
		panic("no return value specified for ComputeReportSignatures")
	}

	var r0 *rmn.ReportSignatures
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *serialization.LaneDest, []*serialization.FixedDestLaneUpdateRequest, ccipocr3.RemoteConfig) (*rmn.ReportSignatures, error)); ok {
		return rf(ctx, destChain, requestedUpdates, rmnRemoteCfg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *serialization.LaneDest, []*serialization.FixedDestLaneUpdateRequest, ccipocr3.RemoteConfig) *rmn.ReportSignatures); ok {
		r0 = rf(ctx, destChain, requestedUpdates, rmnRemoteCfg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*rmn.ReportSignatures)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *serialization.LaneDest, []*serialization.FixedDestLaneUpdateRequest, ccipocr3.RemoteConfig) error); ok {
		r1 = rf(ctx, destChain, requestedUpdates, rmnRemoteCfg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockController_ComputeReportSignatures_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ComputeReportSignatures'
type MockController_ComputeReportSignatures_Call struct {
	*mock.Call
}

// ComputeReportSignatures is a helper method to define mock.On call
//   - ctx context.Context
//   - destChain *serialization.LaneDest
//   - requestedUpdates []*serialization.FixedDestLaneUpdateRequest
//   - rmnRemoteCfg ccipocr3.RemoteConfig
func (_e *MockController_Expecter) ComputeReportSignatures(ctx interface{}, destChain interface{}, requestedUpdates interface{}, rmnRemoteCfg interface{}) *MockController_ComputeReportSignatures_Call {
	return &MockController_ComputeReportSignatures_Call{Call: _e.mock.On("ComputeReportSignatures", ctx, destChain, requestedUpdates, rmnRemoteCfg)}
}

func (_c *MockController_ComputeReportSignatures_Call) Run(run func(ctx context.Context, destChain *serialization.LaneDest, requestedUpdates []*serialization.FixedDestLaneUpdateRequest, rmnRemoteCfg ccipocr3.RemoteConfig)) *MockController_ComputeReportSignatures_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*serialization.LaneDest), args[2].([]*serialization.FixedDestLaneUpdateRequest), args[3].(ccipocr3.RemoteConfig))
	})
	return _c
}

func (_c *MockController_ComputeReportSignatures_Call) Return(_a0 *rmn.ReportSignatures, _a1 error) *MockController_ComputeReportSignatures_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockController_ComputeReportSignatures_Call) RunAndReturn(run func(context.Context, *serialization.LaneDest, []*serialization.FixedDestLaneUpdateRequest, ccipocr3.RemoteConfig) (*rmn.ReportSignatures, error)) *MockController_ComputeReportSignatures_Call {
	_c.Call.Return(run)
	return _c
}

// InitConnection provides a mock function with given fields: ctx, commitConfigDigest, rmnHomeConfigDigest, oraclePeerIDs, rmnNodes
func (_m *MockController) InitConnection(ctx context.Context, commitConfigDigest ccipocr3.Bytes32, rmnHomeConfigDigest ccipocr3.Bytes32, oraclePeerIDs []types.PeerID, rmnNodes []rmntypes.HomeNodeInfo) error {
	ret := _m.Called(ctx, commitConfigDigest, rmnHomeConfigDigest, oraclePeerIDs, rmnNodes)

	if len(ret) == 0 {
		panic("no return value specified for InitConnection")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, ccipocr3.Bytes32, ccipocr3.Bytes32, []types.PeerID, []rmntypes.HomeNodeInfo) error); ok {
		r0 = rf(ctx, commitConfigDigest, rmnHomeConfigDigest, oraclePeerIDs, rmnNodes)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockController_InitConnection_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InitConnection'
type MockController_InitConnection_Call struct {
	*mock.Call
}

// InitConnection is a helper method to define mock.On call
//   - ctx context.Context
//   - commitConfigDigest ccipocr3.Bytes32
//   - rmnHomeConfigDigest ccipocr3.Bytes32
//   - oraclePeerIDs []types.PeerID
//   - rmnNodes []rmntypes.HomeNodeInfo
func (_e *MockController_Expecter) InitConnection(ctx interface{}, commitConfigDigest interface{}, rmnHomeConfigDigest interface{}, oraclePeerIDs interface{}, rmnNodes interface{}) *MockController_InitConnection_Call {
	return &MockController_InitConnection_Call{Call: _e.mock.On("InitConnection", ctx, commitConfigDigest, rmnHomeConfigDigest, oraclePeerIDs, rmnNodes)}
}

func (_c *MockController_InitConnection_Call) Run(run func(ctx context.Context, commitConfigDigest ccipocr3.Bytes32, rmnHomeConfigDigest ccipocr3.Bytes32, oraclePeerIDs []types.PeerID, rmnNodes []rmntypes.HomeNodeInfo)) *MockController_InitConnection_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ccipocr3.Bytes32), args[2].(ccipocr3.Bytes32), args[3].([]types.PeerID), args[4].([]rmntypes.HomeNodeInfo))
	})
	return _c
}

func (_c *MockController_InitConnection_Call) Return(_a0 error) *MockController_InitConnection_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockController_InitConnection_Call) RunAndReturn(run func(context.Context, ccipocr3.Bytes32, ccipocr3.Bytes32, []types.PeerID, []rmntypes.HomeNodeInfo) error) *MockController_InitConnection_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockController creates a new instance of MockController. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockController(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockController {
	mock := &MockController{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
