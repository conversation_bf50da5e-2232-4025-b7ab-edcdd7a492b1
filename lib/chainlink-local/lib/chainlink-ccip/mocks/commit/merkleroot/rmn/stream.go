// Code generated by mockery v2.52.3. DO NOT EDIT.

package rmn

import mock "github.com/stretchr/testify/mock"

// MockStream is an autogenerated mock type for the Stream type
type MockStream struct {
	mock.Mock
}

type MockStream_Expecter struct {
	mock *mock.Mock
}

func (_m *MockStream) EXPECT() *MockStream_Expecter {
	return &MockStream_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with no fields
func (_m *MockStream) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockStream_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type MockStream_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *MockStream_Expecter) Close() *MockStream_Close_Call {
	return &MockStream_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *MockStream_Close_Call) Run(run func()) *MockStream_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockStream_Close_Call) Return(_a0 error) *MockStream_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockStream_Close_Call) RunAndReturn(run func() error) *MockStream_Close_Call {
	_c.Call.Return(run)
	return _c
}

// ReceiveMessages provides a mock function with no fields
func (_m *MockStream) ReceiveMessages() <-chan []byte {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ReceiveMessages")
	}

	var r0 <-chan []byte
	if rf, ok := ret.Get(0).(func() <-chan []byte); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan []byte)
		}
	}

	return r0
}

// MockStream_ReceiveMessages_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReceiveMessages'
type MockStream_ReceiveMessages_Call struct {
	*mock.Call
}

// ReceiveMessages is a helper method to define mock.On call
func (_e *MockStream_Expecter) ReceiveMessages() *MockStream_ReceiveMessages_Call {
	return &MockStream_ReceiveMessages_Call{Call: _e.mock.On("ReceiveMessages")}
}

func (_c *MockStream_ReceiveMessages_Call) Run(run func()) *MockStream_ReceiveMessages_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockStream_ReceiveMessages_Call) Return(_a0 <-chan []byte) *MockStream_ReceiveMessages_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockStream_ReceiveMessages_Call) RunAndReturn(run func() <-chan []byte) *MockStream_ReceiveMessages_Call {
	_c.Call.Return(run)
	return _c
}

// SendMessage provides a mock function with given fields: data
func (_m *MockStream) SendMessage(data []byte) {
	_m.Called(data)
}

// MockStream_SendMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMessage'
type MockStream_SendMessage_Call struct {
	*mock.Call
}

// SendMessage is a helper method to define mock.On call
//   - data []byte
func (_e *MockStream_Expecter) SendMessage(data interface{}) *MockStream_SendMessage_Call {
	return &MockStream_SendMessage_Call{Call: _e.mock.On("SendMessage", data)}
}

func (_c *MockStream_SendMessage_Call) Run(run func(data []byte)) *MockStream_SendMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]byte))
	})
	return _c
}

func (_c *MockStream_SendMessage_Call) Return() *MockStream_SendMessage_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockStream_SendMessage_Call) RunAndReturn(run func([]byte)) *MockStream_SendMessage_Call {
	_c.Run(run)
	return _c
}

// NewMockStream creates a new instance of MockStream. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockStream(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockStream {
	mock := &MockStream{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
