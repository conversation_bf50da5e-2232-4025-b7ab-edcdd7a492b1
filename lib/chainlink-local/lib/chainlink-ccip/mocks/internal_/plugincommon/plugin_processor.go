// Code generated by mockery v2.52.3. DO NOT EDIT.

package plugincommon

import (
	context "context"

	plugincommon "github.com/smartcontractkit/chainlink-ccip/internal/plugincommon"
	mock "github.com/stretchr/testify/mock"
)

// MockPluginProcessor is an autogenerated mock type for the PluginProcessor type
type MockPluginProcessor[Query any, Observation any, Outcome any] struct {
	mock.Mock
}

type MockPluginProcessor_Expecter[Query any, Observation any, Outcome any] struct {
	mock *mock.Mock
}

func (_m *MockPluginProcessor[Query, Observation, Outcome]) EXPECT() *MockPluginProcessor_Expecter[Query, Observation, Outcome] {
	return &MockPluginProcessor_Expecter[Query, Observation, Outcome]{mock: &_m.Mock}
}

// Close provides a mock function with no fields
func (_m *MockPluginProcessor[Query, Observation, Outcome]) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPluginProcessor_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type MockPluginProcessor_Close_Call[Query any, Observation any, Outcome any] struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *MockPluginProcessor_Expecter[Query, Observation, Outcome]) Close() *MockPluginProcessor_Close_Call[Query, Observation, Outcome] {
	return &MockPluginProcessor_Close_Call[Query, Observation, Outcome]{Call: _e.mock.On("Close")}
}

func (_c *MockPluginProcessor_Close_Call[Query, Observation, Outcome]) Run(run func()) *MockPluginProcessor_Close_Call[Query, Observation, Outcome] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockPluginProcessor_Close_Call[Query, Observation, Outcome]) Return(_a0 error) *MockPluginProcessor_Close_Call[Query, Observation, Outcome] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPluginProcessor_Close_Call[Query, Observation, Outcome]) RunAndReturn(run func() error) *MockPluginProcessor_Close_Call[Query, Observation, Outcome] {
	_c.Call.Return(run)
	return _c
}

// Observation provides a mock function with given fields: ctx, prev, query
func (_m *MockPluginProcessor[Query, Observation, Outcome]) Observation(ctx context.Context, prev Outcome, query Query) (Observation, error) {
	ret := _m.Called(ctx, prev, query)

	if len(ret) == 0 {
		panic("no return value specified for Observation")
	}

	var r0 Observation
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, Outcome, Query) (Observation, error)); ok {
		return rf(ctx, prev, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, Outcome, Query) Observation); ok {
		r0 = rf(ctx, prev, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Observation)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, Outcome, Query) error); ok {
		r1 = rf(ctx, prev, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPluginProcessor_Observation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Observation'
type MockPluginProcessor_Observation_Call[Query any, Observation any, Outcome any] struct {
	*mock.Call
}

// Observation is a helper method to define mock.On call
//   - ctx context.Context
//   - prev Outcome
//   - query Query
func (_e *MockPluginProcessor_Expecter[Query, Observation, Outcome]) Observation(ctx interface{}, prev interface{}, query interface{}) *MockPluginProcessor_Observation_Call[Query, Observation, Outcome] {
	return &MockPluginProcessor_Observation_Call[Query, Observation, Outcome]{Call: _e.mock.On("Observation", ctx, prev, query)}
}

func (_c *MockPluginProcessor_Observation_Call[Query, Observation, Outcome]) Run(run func(ctx context.Context, prev Outcome, query Query)) *MockPluginProcessor_Observation_Call[Query, Observation, Outcome] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(Outcome), args[2].(Query))
	})
	return _c
}

func (_c *MockPluginProcessor_Observation_Call[Query, Observation, Outcome]) Return(_a0 Observation, _a1 error) *MockPluginProcessor_Observation_Call[Query, Observation, Outcome] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPluginProcessor_Observation_Call[Query, Observation, Outcome]) RunAndReturn(run func(context.Context, Outcome, Query) (Observation, error)) *MockPluginProcessor_Observation_Call[Query, Observation, Outcome] {
	_c.Call.Return(run)
	return _c
}

// Outcome provides a mock function with given fields: ctx, prev, query, aos
func (_m *MockPluginProcessor[Query, Observation, Outcome]) Outcome(ctx context.Context, prev Outcome, query Query, aos []plugincommon.AttributedObservation[Observation]) (Outcome, error) {
	ret := _m.Called(ctx, prev, query, aos)

	if len(ret) == 0 {
		panic("no return value specified for Outcome")
	}

	var r0 Outcome
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, Outcome, Query, []plugincommon.AttributedObservation[Observation]) (Outcome, error)); ok {
		return rf(ctx, prev, query, aos)
	}
	if rf, ok := ret.Get(0).(func(context.Context, Outcome, Query, []plugincommon.AttributedObservation[Observation]) Outcome); ok {
		r0 = rf(ctx, prev, query, aos)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Outcome)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, Outcome, Query, []plugincommon.AttributedObservation[Observation]) error); ok {
		r1 = rf(ctx, prev, query, aos)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPluginProcessor_Outcome_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Outcome'
type MockPluginProcessor_Outcome_Call[Query any, Observation any, Outcome any] struct {
	*mock.Call
}

// Outcome is a helper method to define mock.On call
//   - ctx context.Context
//   - prev Outcome
//   - query Query
//   - aos []plugincommon.AttributedObservation[Observation]
func (_e *MockPluginProcessor_Expecter[Query, Observation, Outcome]) Outcome(ctx interface{}, prev interface{}, query interface{}, aos interface{}) *MockPluginProcessor_Outcome_Call[Query, Observation, Outcome] {
	return &MockPluginProcessor_Outcome_Call[Query, Observation, Outcome]{Call: _e.mock.On("Outcome", ctx, prev, query, aos)}
}

func (_c *MockPluginProcessor_Outcome_Call[Query, Observation, Outcome]) Run(run func(ctx context.Context, prev Outcome, query Query, aos []plugincommon.AttributedObservation[Observation])) *MockPluginProcessor_Outcome_Call[Query, Observation, Outcome] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(Outcome), args[2].(Query), args[3].([]plugincommon.AttributedObservation[Observation]))
	})
	return _c
}

func (_c *MockPluginProcessor_Outcome_Call[Query, Observation, Outcome]) Return(_a0 Outcome, _a1 error) *MockPluginProcessor_Outcome_Call[Query, Observation, Outcome] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPluginProcessor_Outcome_Call[Query, Observation, Outcome]) RunAndReturn(run func(context.Context, Outcome, Query, []plugincommon.AttributedObservation[Observation]) (Outcome, error)) *MockPluginProcessor_Outcome_Call[Query, Observation, Outcome] {
	_c.Call.Return(run)
	return _c
}

// Query provides a mock function with given fields: ctx, prev
func (_m *MockPluginProcessor[Query, Observation, Outcome]) Query(ctx context.Context, prev Outcome) (Query, error) {
	ret := _m.Called(ctx, prev)

	if len(ret) == 0 {
		panic("no return value specified for Query")
	}

	var r0 Query
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, Outcome) (Query, error)); ok {
		return rf(ctx, prev)
	}
	if rf, ok := ret.Get(0).(func(context.Context, Outcome) Query); ok {
		r0 = rf(ctx, prev)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Query)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, Outcome) error); ok {
		r1 = rf(ctx, prev)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPluginProcessor_Query_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Query'
type MockPluginProcessor_Query_Call[Query any, Observation any, Outcome any] struct {
	*mock.Call
}

// Query is a helper method to define mock.On call
//   - ctx context.Context
//   - prev Outcome
func (_e *MockPluginProcessor_Expecter[Query, Observation, Outcome]) Query(ctx interface{}, prev interface{}) *MockPluginProcessor_Query_Call[Query, Observation, Outcome] {
	return &MockPluginProcessor_Query_Call[Query, Observation, Outcome]{Call: _e.mock.On("Query", ctx, prev)}
}

func (_c *MockPluginProcessor_Query_Call[Query, Observation, Outcome]) Run(run func(ctx context.Context, prev Outcome)) *MockPluginProcessor_Query_Call[Query, Observation, Outcome] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(Outcome))
	})
	return _c
}

func (_c *MockPluginProcessor_Query_Call[Query, Observation, Outcome]) Return(_a0 Query, _a1 error) *MockPluginProcessor_Query_Call[Query, Observation, Outcome] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPluginProcessor_Query_Call[Query, Observation, Outcome]) RunAndReturn(run func(context.Context, Outcome) (Query, error)) *MockPluginProcessor_Query_Call[Query, Observation, Outcome] {
	_c.Call.Return(run)
	return _c
}

// ValidateObservation provides a mock function with given fields: prev, query, ao
func (_m *MockPluginProcessor[Query, Observation, Outcome]) ValidateObservation(prev Outcome, query Query, ao plugincommon.AttributedObservation[Observation]) error {
	ret := _m.Called(prev, query, ao)

	if len(ret) == 0 {
		panic("no return value specified for ValidateObservation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(Outcome, Query, plugincommon.AttributedObservation[Observation]) error); ok {
		r0 = rf(prev, query, ao)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPluginProcessor_ValidateObservation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidateObservation'
type MockPluginProcessor_ValidateObservation_Call[Query any, Observation any, Outcome any] struct {
	*mock.Call
}

// ValidateObservation is a helper method to define mock.On call
//   - prev Outcome
//   - query Query
//   - ao plugincommon.AttributedObservation[Observation]
func (_e *MockPluginProcessor_Expecter[Query, Observation, Outcome]) ValidateObservation(prev interface{}, query interface{}, ao interface{}) *MockPluginProcessor_ValidateObservation_Call[Query, Observation, Outcome] {
	return &MockPluginProcessor_ValidateObservation_Call[Query, Observation, Outcome]{Call: _e.mock.On("ValidateObservation", prev, query, ao)}
}

func (_c *MockPluginProcessor_ValidateObservation_Call[Query, Observation, Outcome]) Run(run func(prev Outcome, query Query, ao plugincommon.AttributedObservation[Observation])) *MockPluginProcessor_ValidateObservation_Call[Query, Observation, Outcome] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(Outcome), args[1].(Query), args[2].(plugincommon.AttributedObservation[Observation]))
	})
	return _c
}

func (_c *MockPluginProcessor_ValidateObservation_Call[Query, Observation, Outcome]) Return(_a0 error) *MockPluginProcessor_ValidateObservation_Call[Query, Observation, Outcome] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPluginProcessor_ValidateObservation_Call[Query, Observation, Outcome]) RunAndReturn(run func(Outcome, Query, plugincommon.AttributedObservation[Observation]) error) *MockPluginProcessor_ValidateObservation_Call[Query, Observation, Outcome] {
	_c.Call.Return(run)
	return _c
}

// NewMockPluginProcessor creates a new instance of MockPluginProcessor. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPluginProcessor[Query any, Observation any, Outcome any](t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPluginProcessor[Query, Observation, Outcome] {
	mock := &MockPluginProcessor[Query, Observation, Outcome]{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
