// Code generated by mockery v2.52.3. DO NOT EDIT.

package networking

import (
	networking "github.com/smartcontractkit/libocr/networking"
	mock "github.com/stretchr/testify/mock"
)

// MockPeerGroup is an autogenerated mock type for the PeerGroup type
type MockPeerGroup struct {
	mock.Mock
}

type MockPeerGroup_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPeerGroup) EXPECT() *MockPeerGroup_Expecter {
	return &MockPeerGroup_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with no fields
func (_m *MockPeerGroup) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockPeerGroup_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Mock<PERSON>eerGroup_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *MockPeerGroup_Expecter) Close() *MockPeerGroup_Close_Call {
	return &MockPeerGroup_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *MockPeerGroup_Close_Call) Run(run func()) *MockPeerGroup_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockPeerGroup_Close_Call) Return(_a0 error) *MockPeerGroup_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockPeerGroup_Close_Call) RunAndReturn(run func() error) *MockPeerGroup_Close_Call {
	_c.Call.Return(run)
	return _c
}

// NewStream provides a mock function with given fields: remotePeerID, newStreamArgs
func (_m *MockPeerGroup) NewStream(remotePeerID string, newStreamArgs networking.NewStreamArgs) (networking.Stream, error) {
	ret := _m.Called(remotePeerID, newStreamArgs)

	if len(ret) == 0 {
		panic("no return value specified for NewStream")
	}

	var r0 networking.Stream
	var r1 error
	if rf, ok := ret.Get(0).(func(string, networking.NewStreamArgs) (networking.Stream, error)); ok {
		return rf(remotePeerID, newStreamArgs)
	}
	if rf, ok := ret.Get(0).(func(string, networking.NewStreamArgs) networking.Stream); ok {
		r0 = rf(remotePeerID, newStreamArgs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(networking.Stream)
		}
	}

	if rf, ok := ret.Get(1).(func(string, networking.NewStreamArgs) error); ok {
		r1 = rf(remotePeerID, newStreamArgs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockPeerGroup_NewStream_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewStream'
type MockPeerGroup_NewStream_Call struct {
	*mock.Call
}

// NewStream is a helper method to define mock.On call
//   - remotePeerID string
//   - newStreamArgs networking.NewStreamArgs
func (_e *MockPeerGroup_Expecter) NewStream(remotePeerID interface{}, newStreamArgs interface{}) *MockPeerGroup_NewStream_Call {
	return &MockPeerGroup_NewStream_Call{Call: _e.mock.On("NewStream", remotePeerID, newStreamArgs)}
}

func (_c *MockPeerGroup_NewStream_Call) Run(run func(remotePeerID string, newStreamArgs networking.NewStreamArgs)) *MockPeerGroup_NewStream_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(networking.NewStreamArgs))
	})
	return _c
}

func (_c *MockPeerGroup_NewStream_Call) Return(_a0 networking.Stream, _a1 error) *MockPeerGroup_NewStream_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockPeerGroup_NewStream_Call) RunAndReturn(run func(string, networking.NewStreamArgs) (networking.Stream, error)) *MockPeerGroup_NewStream_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPeerGroup creates a new instance of MockPeerGroup. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPeerGroup(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPeerGroup {
	mock := &MockPeerGroup{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
