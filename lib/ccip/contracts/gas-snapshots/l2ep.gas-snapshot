ArbitrumCrossDomainForwarder_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 37548)
ArbitrumCrossDomainForwarder_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 12954)
ArbitrumCrossDomainForwarder_Constructor:test_InitialState() (gas: 22133)
ArbitrumCrossDomainForwarder_Forward:test_Forward() (gas: 47818)
ArbitrumCrossDomainForwarder_Forward:test_ForwardRevert() (gas: 22154)
ArbitrumCrossDomainForwarder_Forward:test_NotCallableByUnknownAddress() (gas: 16040)
ArbitrumCrossDomainForwarder_TransferL1Ownership:test_CallableByL1Owner() (gas: 41439)
ArbitrumCrossDomainForwarder_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 19274)
ArbitrumCrossDomainForwarder_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 18622)
ArbitrumCrossDomainForwarder_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 13232)
ArbitrumCrossDomainGovernor_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 37548)
ArbitrumCrossDomainGovernor_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 12954)
ArbitrumCrossDomainGovernor_Constructor:test_InitialState() (gas: 22156)
ArbitrumCrossDomainGovernor_Forward:test_CallableByL2Owner() (gas: 49953)
ArbitrumCrossDomainGovernor_Forward:test_Forward() (gas: 47847)
ArbitrumCrossDomainGovernor_Forward:test_ForwardRevert() (gas: 24298)
ArbitrumCrossDomainGovernor_Forward:test_NotCallableByUnknownAddress() (gas: 18216)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_BubbleUpRevert() (gas: 19363)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_CallableByCrossDomainMessengerAddressOrL1Owner() (gas: 60787)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_CallableByL2Owner() (gas: 62915)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_NotCallableByUnknownAddress() (gas: 18228)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_RevertsBatchWhenOneCallFails() (gas: 64276)
ArbitrumCrossDomainGovernor_TransferL1Ownership:test_CallableByL1Owner() (gas: 41439)
ArbitrumCrossDomainGovernor_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 19274)
ArbitrumCrossDomainGovernor_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 18622)
ArbitrumCrossDomainGovernor_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 13232)
ArbitrumSequencerUptimeFeed_AggregatorV3Interface:test_AggregatorV3Interface() (gas: 104748)
ArbitrumSequencerUptimeFeed_AggregatorV3Interface:test_Return0WhenRoundDoesNotExistYet() (gas: 19939)
ArbitrumSequencerUptimeFeed_Constants:test_InitialState() (gas: 8514)
ArbitrumSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceAllowReadsIfConsumingContractIsWhitelisted() (gas: 592246)
ArbitrumSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceDisallowReadsIfConsumingContractIsNotWhitelisted() (gas: 562331)
ArbitrumSequencerUptimeFeed_UpdateStatus:test_IgnoreOutOfOrderUpdates() (gas: 99559)
ArbitrumSequencerUptimeFeed_UpdateStatus:test_RevertIfNotL2CrossDomainMessengerAddr() (gas: 15436)
ArbitrumSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndNoTimeChange() (gas: 114515)
ArbitrumSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndTimeChange() (gas: 114598)
ArbitrumValidator_Validate:test_PostSequencerOffline() (gas: 69027)
OptimismCrossDomainForwarder_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 47108)
OptimismCrossDomainForwarder_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 22132)
OptimismCrossDomainForwarder_Constructor:test_InitialState() (gas: 21969)
OptimismCrossDomainForwarder_Forward:test_Forward() (gas: 58213)
OptimismCrossDomainForwarder_Forward:test_ForwardRevert() (gas: 32510)
OptimismCrossDomainForwarder_Forward:test_NotCallableByUnknownAddress() (gas: 13852)
OptimismCrossDomainForwarder_TransferL1Ownership:test_CallableByL1Owner() (gas: 48900)
OptimismCrossDomainForwarder_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 28718)
OptimismCrossDomainForwarder_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 16434)
OptimismCrossDomainForwarder_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 11044)
OptimismCrossDomainGovernor_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 47108)
OptimismCrossDomainGovernor_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 22132)
OptimismCrossDomainGovernor_Constructor:test_InitialState() (gas: 21992)
OptimismCrossDomainGovernor_Forward:test_CallableByL2Owner() (gas: 47797)
OptimismCrossDomainGovernor_Forward:test_Forward() (gas: 58262)
OptimismCrossDomainGovernor_Forward:test_ForwardRevert() (gas: 32569)
OptimismCrossDomainGovernor_Forward:test_NotCallableByUnknownAddress() (gas: 16031)
OptimismCrossDomainGovernor_ForwardDelegate:test_BubbleUpRevert() (gas: 29147)
OptimismCrossDomainGovernor_ForwardDelegate:test_CallableByCrossDomainMessengerAddressOrL1Owner() (gas: 72836)
OptimismCrossDomainGovernor_ForwardDelegate:test_CallableByL2Owner() (gas: 72841)
OptimismCrossDomainGovernor_ForwardDelegate:test_NotCallableByUnknownAddress() (gas: 16043)
OptimismCrossDomainGovernor_ForwardDelegate:test_RevertsBatchWhenOneCallFails() (gas: 76045)
OptimismCrossDomainGovernor_TransferL1Ownership:test_CallableByL1Owner() (gas: 48900)
OptimismCrossDomainGovernor_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 28718)
OptimismCrossDomainGovernor_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 16434)
OptimismCrossDomainGovernor_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 11044)
OptimismSequencerUptimeFeed_AggregatorV3Interface:test_AggregatorV3Interface() (gas: 72304)
OptimismSequencerUptimeFeed_AggregatorV3Interface:test_RevertGetAnswerWhenRoundDoesNotExistYet() (gas: 17639)
OptimismSequencerUptimeFeed_AggregatorV3Interface:test_RevertGetRoundDataWhenRoundDoesNotExistYet() (gas: 17875)
OptimismSequencerUptimeFeed_AggregatorV3Interface:test_RevertGetTimestampWhenRoundDoesNotExistYet() (gas: 17628)
OptimismSequencerUptimeFeed_Constructor:test_InitialState() (gas: 22024)
OptimismSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceAllowReadsIfConsumingContractIsWhitelisted() (gas: 589475)
OptimismSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceDisallowReadsIfConsumingContractIsNotWhitelisted() (gas: 562336)
OptimismSequencerUptimeFeed_UpdateStatus:test_IgnoreOutOfOrderUpdates() (gas: 67816)
OptimismSequencerUptimeFeed_UpdateStatus:test_RevertIfNotL2CrossDomainMessengerAddr() (gas: 13070)
OptimismSequencerUptimeFeed_UpdateStatus:test_RevertIfNotL2CrossDomainMessengerAddrAndNotL1SenderAddr() (gas: 23529)
OptimismSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenNoChange() (gas: 77211)
OptimismSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndNoTimeChange() (gas: 96101)
OptimismSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndTimeChange() (gas: 96184)
OptimismValidator_SetGasLimit:test_CorrectlyUpdatesTheGasLimit() (gas: 18658)
OptimismValidator_Validate:test_PostSequencerOffline() (gas: 74741)
OptimismValidator_Validate:test_PostSequencerStatusWhenThereIsNotStatusChange() (gas: 74820)
OptimismValidator_Validate:test_RevertsIfCalledByAnAccountWithNoAccess() (gas: 15556)
ScrollCrossDomainForwarder_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 47200)
ScrollCrossDomainForwarder_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 22182)
ScrollCrossDomainForwarder_Constructor:test_InitialState() (gas: 21645)
ScrollCrossDomainForwarder_Forward:test_Forward() (gas: 58277)
ScrollCrossDomainForwarder_Forward:test_ForwardRevert() (gas: 32566)
ScrollCrossDomainForwarder_Forward:test_NotCallableByUnknownAddress() (gas: 13852)
ScrollCrossDomainForwarder_TransferL1Ownership:test_CallableByL1Owner() (gas: 48963)
ScrollCrossDomainForwarder_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 28782)
ScrollCrossDomainForwarder_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 16434)
ScrollCrossDomainForwarder_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 11044)
ScrollCrossDomainGovernor_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 47200)
ScrollCrossDomainGovernor_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 22182)
ScrollCrossDomainGovernor_Constructor:test_InitialState() (gas: 21668)
ScrollCrossDomainGovernor_Forward:test_CallableByL2Owner() (gas: 47792)
ScrollCrossDomainGovernor_Forward:test_Forward() (gas: 58321)
ScrollCrossDomainGovernor_Forward:test_ForwardRevert() (gas: 32622)
ScrollCrossDomainGovernor_Forward:test_NotCallableByUnknownAddress() (gas: 16028)
ScrollCrossDomainGovernor_ForwardDelegate:test_BubbleUpRevert() (gas: 29205)
ScrollCrossDomainGovernor_ForwardDelegate:test_CallableByCrossDomainMessengerAddressOrL1Owner() (gas: 72900)
ScrollCrossDomainGovernor_ForwardDelegate:test_CallableByL2Owner() (gas: 72905)
ScrollCrossDomainGovernor_ForwardDelegate:test_NotCallableByUnknownAddress() (gas: 16040)
ScrollCrossDomainGovernor_ForwardDelegate:test_RevertsBatchWhenOneCallFails() (gas: 76110)
ScrollCrossDomainGovernor_TransferL1Ownership:test_CallableByL1Owner() (gas: 48963)
ScrollCrossDomainGovernor_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 28782)
ScrollCrossDomainGovernor_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 16434)
ScrollCrossDomainGovernor_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 11044)
ScrollSequencerUptimeFeed_AggregatorV3Interface:test_AggregatorV3Interface() (gas: 72327)
ScrollSequencerUptimeFeed_AggregatorV3Interface:test_RevertGetAnswerWhenRoundDoesNotExistYet() (gas: 17639)
ScrollSequencerUptimeFeed_AggregatorV3Interface:test_RevertGetRoundDataWhenRoundDoesNotExistYet() (gas: 17875)
ScrollSequencerUptimeFeed_AggregatorV3Interface:test_RevertGetTimestampWhenRoundDoesNotExistYet() (gas: 17628)
ScrollSequencerUptimeFeed_Constructor:test_InitialState() (gas: 174375)
ScrollSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceAllowReadsIfConsumingContractIsWhitelisted() (gas: 589475)
ScrollSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceDisallowReadsIfConsumingContractIsNotWhitelisted() (gas: 562336)
ScrollSequencerUptimeFeed_UpdateStatus:test_IgnoreOutOfOrderUpdates() (gas: 67862)
ScrollSequencerUptimeFeed_UpdateStatus:test_RevertIfNotL2CrossDomainMessengerAddr() (gas: 13070)
ScrollSequencerUptimeFeed_UpdateStatus:test_RevertIfNotL2CrossDomainMessengerAddrAndNotL1SenderAddr() (gas: 23529)
ScrollSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenNoChange() (gas: 77257)
ScrollSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndNoTimeChange() (gas: 96147)
ScrollSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndTimeChange() (gas: 96230)
ScrollValidator_SetGasLimit:test_CorrectlyUpdatesTheGasLimit() (gas: 18792)
ScrollValidator_Validate:test_PostSequencerOffline() (gas: 78276)
ScrollValidator_Validate:test_PostSequencerStatusWhenThereIsNotStatusChange() (gas: 78361)
ScrollValidator_Validate:test_RevertsIfCalledByAnAccountWithNoAccess() (gas: 15556)
ZKSyncSequencerUptimeFeed_AggregatorV3Interface:test_AggregatorV3Interface() (gas: 67072)
ZKSyncSequencerUptimeFeed_AggregatorV3Interface:test_RevertGetAnswerWhenRoundDoesNotExistYet() (gas: 17639)
ZKSyncSequencerUptimeFeed_AggregatorV3Interface:test_RevertGetRoundDataWhenRoundDoesNotExistYet() (gas: 17875)
ZKSyncSequencerUptimeFeed_AggregatorV3Interface:test_RevertGetTimestampWhenRoundDoesNotExistYet() (gas: 17628)
ZKSyncSequencerUptimeFeed_Constructor:test_InitialState() (gas: 22028)
ZKSyncSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceAllowReadsIfConsumingContractIsWhitelisted() (gas: 589495)
ZKSyncSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceDisallowReadsIfConsumingContractIsNotWhitelisted() (gas: 562342)
ZKSyncSequencerUptimeFeed_UpdateStatus:test_IgnoreOutOfOrderUpdates() (gas: 61871)
ZKSyncSequencerUptimeFeed_UpdateStatus:test_RevertIfNotL2CrossDomainMessengerAddr() (gas: 13026)
ZKSyncSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenNoChange() (gas: 71272)
ZKSyncSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndNoTimeChange() (gas: 90164)
ZKSyncSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndTimeChange() (gas: 90247)
ZKSyncValidator_Constructor:test_ConstructingRevertedWithInvalidChainId() (gas: 104046)
ZKSyncValidator_Constructor:test_ConstructingRevertedWithZeroL1BridgeAddress() (gas: 81761)
ZKSyncValidator_Constructor:test_ConstructingRevertedWithZeroL2UpdateFeedAddress() (gas: 81818)
ZKSyncValidator_GetChainId:test_CorrectlyGetsTheChainId() (gas: 8346)
ZKSyncValidator_GetSetL2GasPerPubdataByteLimit:test_CorrectlyGetsAndUpdatesTheGasPerPubdataByteLimit() (gas: 18902)
ZKSyncValidator_Validate:test_PostSequencerOffline() (gas: 52205)
ZKSyncValidator_Validate:test_PostSequencerStatusWhenThereIsNotStatusChange() (gas: 52307)
ZKSyncValidator_Validate:test_RevertsIfCalledByAnAccountWithNoAccess() (gas: 15629)