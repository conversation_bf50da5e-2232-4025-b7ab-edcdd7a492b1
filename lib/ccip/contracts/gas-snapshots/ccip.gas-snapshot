ARMProxyStandaloneTest:test_ARMCallEmptyContractRevert() (gas: 19675)
ARMProxyStandaloneTest:test_Constructor() (gas: 310043)
ARMProxyStandaloneTest:test_SetARM() (gas: 16587)
ARMProxyStandaloneTest:test_SetARMzero() (gas: 11297)
ARMProxyTest:test_ARMCallRevertReasonForwarded() (gas: 47898)
ARMProxyTest:test_ARMIsBlessed_Success() (gas: 36363)
ARMProxyTest:test_ARMIsCursed_Success() (gas: 49851)
AggregateTokenLimiter_constructor:test_Constructor_Success() (gas: 27118)
AggregateTokenLimiter_getTokenBucket:test_GetTokenBucket_Success() (gas: 19871)
AggregateTokenLimiter_getTokenBucket:test_Refill_Success() (gas: 41586)
AggregateTokenLimiter_getTokenBucket:test_TimeUnderflow_Revert() (gas: 15452)
AggregateTokenLimiter_getTokenLimitAdmin:test_GetTokenLimitAdmin_Success() (gas: 10537)
AggregateTokenLimiter_getTokenValue:test_GetTokenValue_Success() (gas: 17531)
AggregateTokenLimiter_getTokenValue:test_NoTokenPrice_Reverts() (gas: 21414)
AggregateTokenLimiter_rateLimitValue:test_AggregateValueMaxCapacityExceeded_Revert() (gas: 16586)
AggregateTokenLimiter_rateLimitValue:test_RateLimitValueSuccess_gas() (gas: 18357)
AggregateTokenLimiter_setAdmin:test_OnlyOwnerOrAdmin_Revert() (gas: 13078)
AggregateTokenLimiter_setAdmin:test_Owner_Success() (gas: 19016)
AggregateTokenLimiter_setRateLimiterConfig:test_OnlyOnlyCallableByAdminOrOwner_Revert() (gas: 17546)
AggregateTokenLimiter_setRateLimiterConfig:test_Owner_Success() (gas: 30393)
AggregateTokenLimiter_setRateLimiterConfig:test_TokenLimitAdmin_Success() (gas: 32407)
BurnFromMintTokenPool_lockOrBurn:test_ChainNotAllowed_Revert() (gas: 27346)
BurnFromMintTokenPool_lockOrBurn:test_PoolBurnRevertNotHealthy_Revert() (gas: 54856)
BurnFromMintTokenPool_lockOrBurn:test_PoolBurn_Success() (gas: 244410)
BurnFromMintTokenPool_lockOrBurn:test_setup_Success() (gas: 24167)
BurnMintTokenPool_lockOrBurn:test_ChainNotAllowed_Revert() (gas: 27486)
BurnMintTokenPool_lockOrBurn:test_PoolBurnRevertNotHealthy_Revert() (gas: 54856)
BurnMintTokenPool_lockOrBurn:test_PoolBurn_Success() (gas: 242315)
BurnMintTokenPool_lockOrBurn:test_Setup_Success() (gas: 17830)
BurnMintTokenPool_releaseOrMint:test_ChainNotAllowed_Revert() (gas: 27309)
BurnMintTokenPool_releaseOrMint:test_PoolMintNotHealthy_Revert() (gas: 54618)
BurnMintTokenPool_releaseOrMint:test_PoolMint_Success() (gas: 109405)
BurnMintWithLockReleaseFlagTokenPool_lockOrBurn:test_LockOrBurn_CorrectReturnData_Success() (gas: 242495)
BurnWithFromMintTokenPool_lockOrBurn:test_ChainNotAllowed_Revert() (gas: 27346)
BurnWithFromMintTokenPool_lockOrBurn:test_PoolBurnRevertNotHealthy_Revert() (gas: 54856)
BurnWithFromMintTokenPool_lockOrBurn:test_PoolBurn_Success() (gas: 244454)
BurnWithFromMintTokenPool_lockOrBurn:test_Setup_Success() (gas: 24180)
CCIPClientExample_sanity:test_ImmutableExamples_Success() (gas: 2060218)
CCIPHome__validateConfig:test__validateConfigLessTransmittersThanSigners_Success() (gas: 334693)
CCIPHome__validateConfig:test__validateConfigSmallerFChain_Success() (gas: 466117)
CCIPHome__validateConfig:test__validateConfig_ABIEncodedAddress_OfframpAddressCannotBeZero_Reverts() (gas: 289739)
CCIPHome__validateConfig:test__validateConfig_ABIEncodedAddress_RMNHomeAddressCannotBeZero_Reverts() (gas: 290034)
CCIPHome__validateConfig:test__validateConfig_ChainSelectorNotFound_Reverts() (gas: 292771)
CCIPHome__validateConfig:test__validateConfig_ChainSelectorNotSet_Reverts() (gas: 289373)
CCIPHome__validateConfig:test__validateConfig_FChainTooHigh_Reverts() (gas: 337311)
CCIPHome__validateConfig:test__validateConfig_FMustBePositive_Reverts() (gas: 291145)
CCIPHome__validateConfig:test__validateConfig_FTooHigh_Reverts() (gas: 290604)
CCIPHome__validateConfig:test__validateConfig_NodeNotInRegistry_Reverts() (gas: 344238)
CCIPHome__validateConfig:test__validateConfig_NotEnoughTransmittersEmptyAddresses_Reverts() (gas: 309179)
CCIPHome__validateConfig:test__validateConfig_NotEnoughTransmitters_Reverts() (gas: 1212133)
CCIPHome__validateConfig:test__validateConfig_OfframpAddressCannotBeZero_Reverts() (gas: 289400)
CCIPHome__validateConfig:test__validateConfig_RMNHomeAddressCannotBeZero_Reverts() (gas: 289661)
CCIPHome__validateConfig:test__validateConfig_Success() (gas: 300616)
CCIPHome__validateConfig:test__validateConfig_TooManySigners_Reverts() (gas: 773237)
CCIPHome__validateConfig:test__validateConfig_ZeroP2PId_Reverts() (gas: 293988)
CCIPHome__validateConfig:test__validateConfig_ZeroSignerKey_Reverts() (gas: 294035)
CCIPHome_applyChainConfigUpdates:test__applyChainConfigUpdates_FChainNotPositive_Reverts() (gas: 185242)
CCIPHome_applyChainConfigUpdates:test_applyChainConfigUpdates_addChainConfigs_Success() (gas: 347249)
CCIPHome_applyChainConfigUpdates:test_applyChainConfigUpdates_nodeNotInRegistry_Reverts() (gas: 20631)
CCIPHome_applyChainConfigUpdates:test_applyChainConfigUpdates_removeChainConfigs_Success() (gas: 270824)
CCIPHome_applyChainConfigUpdates:test_applyChainConfigUpdates_selectorNotFound_Reverts() (gas: 14952)
CCIPHome_applyChainConfigUpdates:test_getPaginatedCCIPHomes_Success() (gas: 370980)
CCIPHome_beforeCapabilityConfigSet:test_beforeCapabilityConfigSet_DONIdMismatch_reverts() (gas: 27137)
CCIPHome_beforeCapabilityConfigSet:test_beforeCapabilityConfigSet_InnerCallReverts_reverts() (gas: 11783)
CCIPHome_beforeCapabilityConfigSet:test_beforeCapabilityConfigSet_InvalidSelector_reverts() (gas: 11038)
CCIPHome_beforeCapabilityConfigSet:test_beforeCapabilityConfigSet_OnlyCapabilitiesRegistryCanCall_reverts() (gas: 26150)
CCIPHome_beforeCapabilityConfigSet:test_beforeCapabilityConfigSet_success() (gas: 1436726)
CCIPHome_constructor:test_constructor_CapabilitiesRegistryAddressZero_reverts() (gas: 63878)
CCIPHome_constructor:test_constructor_success() (gas: 3521034)
CCIPHome_constructor:test_getCapabilityConfiguration_success() (gas: 9173)
CCIPHome_constructor:test_supportsInterface_success() (gas: 9865)
CCIPHome_getAllConfigs:test_getAllConfigs_success() (gas: 2765282)
CCIPHome_getConfigDigests:test_getConfigDigests_success() (gas: 2539724)
CCIPHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive_CanOnlySelfCall_reverts() (gas: 9110)
CCIPHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive_ConfigDigestMismatch_reverts() (gas: 23052)
CCIPHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive_NoOpStateTransitionNotAllowed_reverts() (gas: 8818)
CCIPHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive_multiplePlugins_success() (gas: 5096112)
CCIPHome_revokeCandidate:test_revokeCandidate_CanOnlySelfCall_reverts() (gas: 9068)
CCIPHome_revokeCandidate:test_revokeCandidate_ConfigDigestMismatch_reverts() (gas: 19128)
CCIPHome_revokeCandidate:test_revokeCandidate_RevokingZeroDigestNotAllowed_reverts() (gas: 8773)
CCIPHome_revokeCandidate:test_revokeCandidate_success() (gas: 30676)
CCIPHome_setCandidate:test_setCandidate_CanOnlySelfCall_reverts() (gas: 19051)
CCIPHome_setCandidate:test_setCandidate_ConfigDigestMismatch_reverts() (gas: 1388198)
CCIPHome_setCandidate:test_setCandidate_success() (gas: 1357740)
CommitStore_constructor:test_Constructor_Success() (gas: 2855567)
CommitStore_isUnpausedAndRMNHealthy:test_RMN_Success() (gas: 73954)
CommitStore_report:test_InvalidIntervalMinLargerThanMax_Revert() (gas: 28739)
CommitStore_report:test_InvalidInterval_Revert() (gas: 28679)
CommitStore_report:test_InvalidRootRevert() (gas: 27912)
CommitStore_report:test_OnlyGasPriceUpdates_Success() (gas: 53448)
CommitStore_report:test_OnlyPriceUpdateStaleReport_Revert() (gas: 59286)
CommitStore_report:test_OnlyTokenPriceUpdates_Success() (gas: 53446)
CommitStore_report:test_Paused_Revert() (gas: 21319)
CommitStore_report:test_ReportAndPriceUpdate_Success() (gas: 84485)
CommitStore_report:test_ReportOnlyRootSuccess_gas() (gas: 56342)
CommitStore_report:test_RootAlreadyCommitted_Revert() (gas: 64077)
CommitStore_report:test_StaleReportWithRoot_Success() (gas: 117309)
CommitStore_report:test_Unhealthy_Revert() (gas: 44823)
CommitStore_report:test_ValidPriceUpdateThenStaleReportWithRoot_Success() (gas: 98929)
CommitStore_report:test_ZeroEpochAndRound_Revert() (gas: 27707)
CommitStore_resetUnblessedRoots:test_OnlyOwner_Revert() (gas: 11376)
CommitStore_resetUnblessedRoots:test_ResetUnblessedRoots_Success() (gas: 144186)
CommitStore_setDynamicConfig:test_InvalidCommitStoreConfig_Revert() (gas: 37314)
CommitStore_setDynamicConfig:test_OnlyOwner_Revert() (gas: 37483)
CommitStore_setDynamicConfig:test_PriceEpochCleared_Success() (gas: 129329)
CommitStore_setLatestPriceEpochAndRound:test_OnlyOwner_Revert() (gas: 11099)
CommitStore_setLatestPriceEpochAndRound:test_SetLatestPriceEpochAndRound_Success() (gas: 20690)
CommitStore_setMinSeqNr:test_OnlyOwner_Revert() (gas: 11098)
CommitStore_verify:test_Blessed_Success() (gas: 96581)
CommitStore_verify:test_NotBlessed_Success() (gas: 61473)
CommitStore_verify:test_Paused_Revert() (gas: 18568)
CommitStore_verify:test_TooManyLeaves_Revert() (gas: 36848)
DefensiveExampleTest:test_HappyPath_Success() (gas: 200267)
DefensiveExampleTest:test_Recovery() (gas: 424613)
E2E:test_E2E_3MessagesSuccess_gas() (gas: 1138593)
EVM2EVMOffRamp__releaseOrMintToken:test__releaseOrMintToken_NotACompatiblePool_Revert() (gas: 38322)
EVM2EVMOffRamp__releaseOrMintToken:test__releaseOrMintToken_Success() (gas: 101428)
EVM2EVMOffRamp__releaseOrMintToken:test__releaseOrMintToken_TokenHandlingError_transfer_Revert() (gas: 80487)
EVM2EVMOffRamp__releaseOrMintToken:test_releaseOrMintToken_InvalidDataLength_Revert() (gas: 37365)
EVM2EVMOffRamp__releaseOrMintToken:test_releaseOrMintToken_ReleaseOrMintBalanceMismatch_Revert() (gas: 91915)
EVM2EVMOffRamp__releaseOrMintToken:test_releaseOrMintToken_TokenHandlingError_BalanceOf_Revert() (gas: 37841)
EVM2EVMOffRamp__releaseOrMintToken:test_releaseOrMintToken_skip_ReleaseOrMintBalanceMismatch_if_pool_Revert() (gas: 84113)
EVM2EVMOffRamp__releaseOrMintTokens:test_OverValueWithARLOff_Success() (gas: 369720)
EVM2EVMOffRamp__releaseOrMintTokens:test_PriceNotFoundForToken_Reverts() (gas: 137592)
EVM2EVMOffRamp__releaseOrMintTokens:test_RateLimitErrors_Reverts() (gas: 766486)
EVM2EVMOffRamp__releaseOrMintTokens:test_TokenHandlingError_Reverts() (gas: 171941)
EVM2EVMOffRamp__releaseOrMintTokens:test__releaseOrMintTokens_NotACompatiblePool_Reverts() (gas: 29717)
EVM2EVMOffRamp__releaseOrMintTokens:test_releaseOrMintTokens_InvalidDataLengthReturnData_Revert() (gas: 67360)
EVM2EVMOffRamp__releaseOrMintTokens:test_releaseOrMintTokens_InvalidEVMAddress_Revert() (gas: 43677)
EVM2EVMOffRamp__releaseOrMintTokens:test_releaseOrMintTokens_Success() (gas: 202313)
EVM2EVMOffRamp__releaseOrMintTokens:test_releaseOrMintTokens_destDenominatedDecimals_Success() (gas: 213452)
EVM2EVMOffRamp__report:test_Report_Success() (gas: 127774)
EVM2EVMOffRamp__trialExecute:test_RateLimitError_Success() (gas: 230875)
EVM2EVMOffRamp__trialExecute:test_TokenHandlingErrorIsCaught_Success() (gas: 239552)
EVM2EVMOffRamp__trialExecute:test_TokenPoolIsNotAContract_Success() (gas: 323274)
EVM2EVMOffRamp__trialExecute:test_trialExecute_Success() (gas: 304201)
EVM2EVMOffRamp_ccipReceive:test_Reverts() (gas: 17048)
EVM2EVMOffRamp_constructor:test_CommitStoreAlreadyInUse_Revert() (gas: 153120)
EVM2EVMOffRamp_constructor:test_Constructor_Success() (gas: 5212732)
EVM2EVMOffRamp_constructor:test_ZeroOnRampAddress_Revert() (gas: 143845)
EVM2EVMOffRamp_execute:test_EmptyReport_Revert() (gas: 21507)
EVM2EVMOffRamp_execute:test_InvalidMessageId_Revert() (gas: 36936)
EVM2EVMOffRamp_execute:test_InvalidSourceChain_Revert() (gas: 52324)
EVM2EVMOffRamp_execute:test_InvalidSourcePoolAddress_Success() (gas: 463753)
EVM2EVMOffRamp_execute:test_ManualExecutionNotYetEnabled_Revert() (gas: 48346)
EVM2EVMOffRamp_execute:test_MessageTooLarge_Revert() (gas: 153019)
EVM2EVMOffRamp_execute:test_Paused_Revert() (gas: 103946)
EVM2EVMOffRamp_execute:test_ReceiverError_Success() (gas: 165358)
EVM2EVMOffRamp_execute:test_RetryFailedMessageWithoutManualExecution_Success() (gas: 180107)
EVM2EVMOffRamp_execute:test_RootNotCommitted_Revert() (gas: 43157)
EVM2EVMOffRamp_execute:test_SingleMessageNoTokensUnordered_Success() (gas: 160119)
EVM2EVMOffRamp_execute:test_SingleMessageNoTokens_Success() (gas: 175497)
EVM2EVMOffRamp_execute:test_SingleMessageToNonCCIPReceiver_Success() (gas: 237901)
EVM2EVMOffRamp_execute:test_SingleMessagesNoTokensSuccess_gas() (gas: 115048)
EVM2EVMOffRamp_execute:test_SkippedIncorrectNonceStillExecutes_Success() (gas: 400597)
EVM2EVMOffRamp_execute:test_SkippedIncorrectNonce_Success() (gas: 54774)
EVM2EVMOffRamp_execute:test_StrictUntouchedToSuccess_Success() (gas: 132556)
EVM2EVMOffRamp_execute:test_TokenDataMismatch_Revert() (gas: 52786)
EVM2EVMOffRamp_execute:test_TwoMessagesWithTokensAndGE_Success() (gas: 556453)
EVM2EVMOffRamp_execute:test_TwoMessagesWithTokensSuccess_gas() (gas: 486701)
EVM2EVMOffRamp_execute:test_UnexpectedTokenData_Revert() (gas: 35887)
EVM2EVMOffRamp_execute:test_Unhealthy_Revert() (gas: 538315)
EVM2EVMOffRamp_execute:test_UnsupportedNumberOfTokens_Revert() (gas: 65298)
EVM2EVMOffRamp_execute:test__execute_SkippedAlreadyExecutedMessageUnordered_Success() (gas: 124107)
EVM2EVMOffRamp_execute:test__execute_SkippedAlreadyExecutedMessage_Success() (gas: 144365)
EVM2EVMOffRamp_execute:test_execute_RouterYULCall_Success() (gas: 394187)
EVM2EVMOffRamp_executeSingleMessage:test_MessageSender_Revert() (gas: 18685)
EVM2EVMOffRamp_executeSingleMessage:test_NonContractWithTokens_Success() (gas: 269248)
EVM2EVMOffRamp_executeSingleMessage:test_NonContract_Success() (gas: 18815)
EVM2EVMOffRamp_executeSingleMessage:test_TokenHandlingError_Revert() (gas: 216651)
EVM2EVMOffRamp_executeSingleMessage:test_ZeroGasDONExecution_Revert() (gas: 48391)
EVM2EVMOffRamp_executeSingleMessage:test_executeSingleMessage_NoTokens_Success() (gas: 47823)
EVM2EVMOffRamp_executeSingleMessage:test_executeSingleMessage_WithTokens_Success() (gas: 305545)
EVM2EVMOffRamp_executeSingleMessage:test_executeSingleMessage_ZeroGasZeroData_Success() (gas: 70839)
EVM2EVMOffRamp_execute_upgrade:test_V2NonceNewSenderStartsAtZero_Success() (gas: 232136)
EVM2EVMOffRamp_execute_upgrade:test_V2NonceStartsAtV1Nonce_Success() (gas: 281170)
EVM2EVMOffRamp_execute_upgrade:test_V2OffRampNonceSkipsIfMsgInFlight_Success() (gas: 262488)
EVM2EVMOffRamp_execute_upgrade:test_V2SenderNoncesReadsPreviousRamp_Success() (gas: 230645)
EVM2EVMOffRamp_execute_upgrade:test_V2_Success() (gas: 132092)
EVM2EVMOffRamp_getAllRateLimitTokens:test_GetAllRateLimitTokens_Success() (gas: 38626)
EVM2EVMOffRamp_getExecutionState:test_FillExecutionState_Success() (gas: 3397486)
EVM2EVMOffRamp_getExecutionState:test_GetExecutionState_Success() (gas: 84833)
EVM2EVMOffRamp_manuallyExecute:test_ManualExecFailedTx_Revert() (gas: 188280)
EVM2EVMOffRamp_manuallyExecute:test_ManualExecForkedChain_Revert() (gas: 27574)
EVM2EVMOffRamp_manuallyExecute:test_ManualExecGasLimitMismatch_Revert() (gas: 46457)
EVM2EVMOffRamp_manuallyExecute:test_ManualExecInvalidGasLimit_Revert() (gas: 27948)
EVM2EVMOffRamp_manuallyExecute:test_ManualExecWithMultipleMessagesAndSourceTokens_Success() (gas: 523312)
EVM2EVMOffRamp_manuallyExecute:test_ManualExecWithSourceTokens_Success() (gas: 338454)
EVM2EVMOffRamp_manuallyExecute:test_ManualExec_Success() (gas: 189760)
EVM2EVMOffRamp_manuallyExecute:test_ReentrancyManualExecuteFails_Success() (gas: 2192118)
EVM2EVMOffRamp_manuallyExecute:test_manuallyExecute_DestinationGasAmountCountMismatch_Revert() (gas: 356045)
EVM2EVMOffRamp_manuallyExecute:test_manuallyExecute_DoesNotRevertIfUntouched_Success() (gas: 145457)
EVM2EVMOffRamp_manuallyExecute:test_manuallyExecute_InvalidTokenGasOverride_Revert() (gas: 359274)
EVM2EVMOffRamp_manuallyExecute:test_manuallyExecute_LowGasLimitManualExec_Success() (gas: 450711)
EVM2EVMOffRamp_manuallyExecute:test_manuallyExecute_WithGasOverride_Success() (gas: 192223)
EVM2EVMOffRamp_manuallyExecute:test_manuallyExecute_WithInvalidReceiverExecutionGasOverride_Revert() (gas: 155387)
EVM2EVMOffRamp_manuallyExecute:test_manuallyExecute_WithInvalidSourceTokenDataCount_Revert() (gas: 60494)
EVM2EVMOffRamp_metadataHash:test_MetadataHash_Success() (gas: 8895)
EVM2EVMOffRamp_setDynamicConfig:test_NonOwner_Revert() (gas: 40357)
EVM2EVMOffRamp_setDynamicConfig:test_RouterZeroAddress_Revert() (gas: 38419)
EVM2EVMOffRamp_setDynamicConfig:test_SetDynamicConfig_Success() (gas: 142469)
EVM2EVMOffRamp_updateRateLimitTokens:test_updateRateLimitTokens_AddsAndRemoves_Success() (gas: 162818)
EVM2EVMOffRamp_updateRateLimitTokens:test_updateRateLimitTokens_NonOwner_Revert() (gas: 16936)
EVM2EVMOffRamp_updateRateLimitTokens:test_updateRateLimitTokens_Success() (gas: 197985)
EVM2EVMOnRamp_constructor:test_Constructor_Success() (gas: 5056698)
EVM2EVMOnRamp_forwardFromRouter:test_CannotSendZeroTokens_Revert() (gas: 36063)
EVM2EVMOnRamp_forwardFromRouter:test_EnforceOutOfOrder_Revert() (gas: 99022)
EVM2EVMOnRamp_forwardFromRouter:test_ForwardFromRouterExtraArgsV2AllowOutOfOrderTrue_Success() (gas: 114937)
EVM2EVMOnRamp_forwardFromRouter:test_ForwardFromRouterExtraArgsV2_Success() (gas: 114979)
EVM2EVMOnRamp_forwardFromRouter:test_ForwardFromRouterSuccessCustomExtraArgs() (gas: 131003)
EVM2EVMOnRamp_forwardFromRouter:test_ForwardFromRouterSuccessLegacyExtraArgs() (gas: 139443)
EVM2EVMOnRamp_forwardFromRouter:test_ForwardFromRouter_Success() (gas: 130619)
EVM2EVMOnRamp_forwardFromRouter:test_InvalidAddressEncodePacked_Revert() (gas: 38647)
EVM2EVMOnRamp_forwardFromRouter:test_InvalidAddress_Revert() (gas: 38830)
EVM2EVMOnRamp_forwardFromRouter:test_InvalidChainSelector_Revert() (gas: 25726)
EVM2EVMOnRamp_forwardFromRouter:test_InvalidExtraArgsTag_Revert() (gas: 25545)
EVM2EVMOnRamp_forwardFromRouter:test_MaxCapacityExceeded_Revert() (gas: 84273)
EVM2EVMOnRamp_forwardFromRouter:test_MaxFeeBalanceReached_Revert() (gas: 36847)
EVM2EVMOnRamp_forwardFromRouter:test_MessageGasLimitTooHigh_Revert() (gas: 29327)
EVM2EVMOnRamp_forwardFromRouter:test_MessageTooLarge_Revert() (gas: 107850)
EVM2EVMOnRamp_forwardFromRouter:test_OriginalSender_Revert() (gas: 22823)
EVM2EVMOnRamp_forwardFromRouter:test_OverValueWithARLOff_Success() (gas: 227322)
EVM2EVMOnRamp_forwardFromRouter:test_Paused_Revert() (gas: 53432)
EVM2EVMOnRamp_forwardFromRouter:test_Permissions_Revert() (gas: 25757)
EVM2EVMOnRamp_forwardFromRouter:test_PriceNotFoundForToken_Revert() (gas: 57722)
EVM2EVMOnRamp_forwardFromRouter:test_ShouldIncrementNonceOnlyOnOrdered_Success() (gas: 182247)
EVM2EVMOnRamp_forwardFromRouter:test_ShouldIncrementSeqNumAndNonce_Success() (gas: 180718)
EVM2EVMOnRamp_forwardFromRouter:test_ShouldStoreNonLinkFees() (gas: 133270)
EVM2EVMOnRamp_forwardFromRouter:test_SourceTokenDataTooLarge_Revert() (gas: 3943537)
EVM2EVMOnRamp_forwardFromRouter:test_TooManyTokens_Revert() (gas: 30472)
EVM2EVMOnRamp_forwardFromRouter:test_Unhealthy_Revert() (gas: 43480)
EVM2EVMOnRamp_forwardFromRouter:test_UnsupportedToken_Revert() (gas: 110111)
EVM2EVMOnRamp_forwardFromRouter:test_ZeroAddressReceiver_Revert() (gas: 316020)
EVM2EVMOnRamp_forwardFromRouter:test_forwardFromRouter_ShouldStoreLinkFees_Success() (gas: 113067)
EVM2EVMOnRamp_forwardFromRouter:test_forwardFromRouter_UnsupportedToken_Revert() (gas: 72824)
EVM2EVMOnRamp_forwardFromRouter:test_forwardFromRouter_correctSourceTokenData_Success() (gas: 719887)
EVM2EVMOnRamp_forwardFromRouter_upgrade:test_V2NonceNewSenderStartsAtZero_Success() (gas: 148808)
EVM2EVMOnRamp_forwardFromRouter_upgrade:test_V2NonceStartsAtV1Nonce_Success() (gas: 192679)
EVM2EVMOnRamp_forwardFromRouter_upgrade:test_V2SenderNoncesReadsPreviousRamp_Success() (gas: 123243)
EVM2EVMOnRamp_forwardFromRouter_upgrade:test_V2_Success() (gas: 96028)
EVM2EVMOnRamp_getDataAvailabilityCost:test_EmptyMessageCalculatesDataAvailabilityCost_Success() (gas: 20598)
EVM2EVMOnRamp_getDataAvailabilityCost:test_SimpleMessageCalculatesDataAvailabilityCost_Success() (gas: 20966)
EVM2EVMOnRamp_getFee:test_EmptyMessage_Success() (gas: 79854)
EVM2EVMOnRamp_getFee:test_GetFeeOfZeroForTokenMessage_Success() (gas: 85353)
EVM2EVMOnRamp_getFee:test_HighGasMessage_Success() (gas: 235702)
EVM2EVMOnRamp_getFee:test_MessageGasLimitTooHigh_Revert() (gas: 16943)
EVM2EVMOnRamp_getFee:test_MessageTooLarge_Revert() (gas: 95505)
EVM2EVMOnRamp_getFee:test_MessageWithDataAndTokenTransfer_Success() (gas: 158970)
EVM2EVMOnRamp_getFee:test_NotAFeeToken_Revert() (gas: 24323)
EVM2EVMOnRamp_getFee:test_SingleTokenMessage_Success() (gas: 119700)
EVM2EVMOnRamp_getFee:test_TooManyTokens_Revert() (gas: 20142)
EVM2EVMOnRamp_getFee:test_ZeroDataAvailabilityMultiplier_Success() (gas: 67550)
EVM2EVMOnRamp_getSupportedTokens:test_GetSupportedTokens_Revert() (gas: 10532)
EVM2EVMOnRamp_getTokenPool:test_GetTokenPool_Success() (gas: 35297)
EVM2EVMOnRamp_getTokenTransferCost:test_CustomTokenBpsFee_Success() (gas: 43218)
EVM2EVMOnRamp_getTokenTransferCost:test_FeeTokenBpsFee_Success() (gas: 33280)
EVM2EVMOnRamp_getTokenTransferCost:test_LargeTokenTransferChargesMaxFeeAndGas_Success() (gas: 28551)
EVM2EVMOnRamp_getTokenTransferCost:test_MixedTokenTransferFee_Success() (gas: 122690)
EVM2EVMOnRamp_getTokenTransferCost:test_NoTokenTransferChargesZeroFee_Success() (gas: 15403)
EVM2EVMOnRamp_getTokenTransferCost:test_SmallTokenTransferChargesMinFeeAndGas_Success() (gas: 28359)
EVM2EVMOnRamp_getTokenTransferCost:test_UnsupportedToken_Revert() (gas: 21353)
EVM2EVMOnRamp_getTokenTransferCost:test_ZeroAmountTokenTransferChargesMinFeeAndGas_Success() (gas: 28382)
EVM2EVMOnRamp_getTokenTransferCost:test_ZeroFeeConfigChargesMinFee_Success() (gas: 38899)
EVM2EVMOnRamp_getTokenTransferCost:test__getTokenTransferCost_selfServeUsesDefaults_Success() (gas: 29674)
EVM2EVMOnRamp_linkAvailableForPayment:test_InsufficientLinkBalance_Success() (gas: 32773)
EVM2EVMOnRamp_linkAvailableForPayment:test_LinkAvailableForPayment_Success() (gas: 135254)
EVM2EVMOnRamp_payNops:test_AdminPayNops_Success() (gas: 143667)
EVM2EVMOnRamp_payNops:test_InsufficientBalance_Revert() (gas: 29213)
EVM2EVMOnRamp_payNops:test_NoFeesToPay_Revert() (gas: 127659)
EVM2EVMOnRamp_payNops:test_NoNopsToPay_Revert() (gas: 133532)
EVM2EVMOnRamp_payNops:test_NopPayNops_Success() (gas: 146954)
EVM2EVMOnRamp_payNops:test_OwnerPayNops_Success() (gas: 141529)
EVM2EVMOnRamp_payNops:test_PayNopsSuccessAfterSetNops() (gas: 298706)
EVM2EVMOnRamp_payNops:test_WrongPermissions_Revert() (gas: 15378)
EVM2EVMOnRamp_setDynamicConfig:test_SetConfigInvalidConfig_Revert() (gas: 42524)
EVM2EVMOnRamp_setDynamicConfig:test_SetConfigOnlyOwner_Revert() (gas: 21426)
EVM2EVMOnRamp_setDynamicConfig:test_SetDynamicConfig_Success() (gas: 54301)
EVM2EVMOnRamp_setFeeTokenConfig:test_OnlyCallableByOwnerOrAdmin_Revert() (gas: 13530)
EVM2EVMOnRamp_setFeeTokenConfig:test_SetFeeTokenConfigByAdmin_Success() (gas: 16497)
EVM2EVMOnRamp_setFeeTokenConfig:test_SetFeeTokenConfig_Success() (gas: 14036)
EVM2EVMOnRamp_setNops:test_AdminCanSetNops_Success() (gas: 61872)
EVM2EVMOnRamp_setNops:test_IncludesPayment_Success() (gas: 470886)
EVM2EVMOnRamp_setNops:test_LinkTokenCannotBeNop_Revert() (gas: 57370)
EVM2EVMOnRamp_setNops:test_NonOwnerOrAdmin_Revert() (gas: 14779)
EVM2EVMOnRamp_setNops:test_NotEnoughFundsForPayout_Revert() (gas: 85222)
EVM2EVMOnRamp_setNops:test_SetNopsRemovesOldNopsCompletely_Success() (gas: 60868)
EVM2EVMOnRamp_setNops:test_SetNops_Success() (gas: 174097)
EVM2EVMOnRamp_setNops:test_TooManyNops_Revert() (gas: 193503)
EVM2EVMOnRamp_setNops:test_ZeroAddressCannotBeNop_Revert() (gas: 53711)
EVM2EVMOnRamp_setTokenTransferFeeConfig:test__setTokenTransferFeeConfig_InvalidDestBytesOverhead_Revert() (gas: 14616)
EVM2EVMOnRamp_setTokenTransferFeeConfig:test__setTokenTransferFeeConfig_OnlyCallableByOwnerOrAdmin_Revert() (gas: 14427)
EVM2EVMOnRamp_setTokenTransferFeeConfig:test__setTokenTransferFeeConfig_Success() (gas: 85487)
EVM2EVMOnRamp_setTokenTransferFeeConfig:test__setTokenTransferFeeConfig_byAdmin_Success() (gas: 17468)
EVM2EVMOnRamp_withdrawNonLinkFees:test_LinkBalanceNotSettled_Revert() (gas: 83639)
EVM2EVMOnRamp_withdrawNonLinkFees:test_NonOwnerOrAdmin_Revert() (gas: 15353)
EVM2EVMOnRamp_withdrawNonLinkFees:test_SettlingBalance_Success() (gas: 272970)
EVM2EVMOnRamp_withdrawNonLinkFees:test_WithdrawNonLinkFees_Success() (gas: 53627)
EVM2EVMOnRamp_withdrawNonLinkFees:test_WithdrawToZeroAddress_Revert() (gas: 12875)
EtherSenderReceiverTest_ccipReceive:test_ccipReceive_fallbackToWethTransfer() (gas: 96907)
EtherSenderReceiverTest_ccipReceive:test_ccipReceive_happyPath() (gas: 49775)
EtherSenderReceiverTest_ccipReceive:test_ccipReceive_wrongToken() (gas: 17435)
EtherSenderReceiverTest_ccipReceive:test_ccipReceive_wrongTokenAmount() (gas: 15728)
EtherSenderReceiverTest_ccipSend:test_ccipSend_reverts_insufficientFee_feeToken() (gas: 99909)
EtherSenderReceiverTest_ccipSend:test_ccipSend_reverts_insufficientFee_native() (gas: 76138)
EtherSenderReceiverTest_ccipSend:test_ccipSend_reverts_insufficientFee_weth() (gas: 99931)
EtherSenderReceiverTest_ccipSend:test_ccipSend_success_feeToken() (gas: 145010)
EtherSenderReceiverTest_ccipSend:test_ccipSend_success_native() (gas: 80373)
EtherSenderReceiverTest_ccipSend:test_ccipSend_success_nativeExcess() (gas: 80560)
EtherSenderReceiverTest_ccipSend:test_ccipSend_success_weth() (gas: 96064)
EtherSenderReceiverTest_constructor:test_constructor() (gas: 17553)
EtherSenderReceiverTest_getFee:test_getFee() (gas: 27346)
EtherSenderReceiverTest_validateFeeToken:test_validateFeeToken_reverts_feeToken_tokenAmountNotEqualToMsgValue() (gas: 20375)
EtherSenderReceiverTest_validateFeeToken:test_validateFeeToken_valid_feeToken() (gas: 16724)
EtherSenderReceiverTest_validateFeeToken:test_validateFeeToken_valid_native() (gas: 16657)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_dataOverwrittenToMsgSender() (gas: 25457)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_emptyDataOverwrittenToMsgSender() (gas: 25307)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_invalidTokenAmounts() (gas: 17925)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_tokenOverwrittenToWeth() (gas: 25329)
EtherSenderReceiverTest_validatedMessage:test_validatedMessage_validMessage_extraArgs() (gas: 26370)
FactoryBurnMintERC20_approve:test_Approve_Success() (gas: 55766)
FactoryBurnMintERC20_approve:test_InvalidAddress_Reverts() (gas: 10709)
FactoryBurnMintERC20_burn:test_BasicBurn_Success() (gas: 172367)
FactoryBurnMintERC20_burn:test_BurnFromZeroAddress_Reverts() (gas: 47272)
FactoryBurnMintERC20_burn:test_ExceedsBalance_Reverts() (gas: 21939)
FactoryBurnMintERC20_burn:test_SenderNotBurner_Reverts() (gas: 13493)
FactoryBurnMintERC20_burnFrom:test_BurnFrom_Success() (gas: 58231)
FactoryBurnMintERC20_burnFrom:test_ExceedsBalance_Reverts() (gas: 36138)
FactoryBurnMintERC20_burnFrom:test_InsufficientAllowance_Reverts() (gas: 22031)
FactoryBurnMintERC20_burnFrom:test_SenderNotBurner_Reverts() (gas: 13460)
FactoryBurnMintERC20_burnFromAlias:test_BurnFrom_Success() (gas: 58205)
FactoryBurnMintERC20_burnFromAlias:test_ExceedsBalance_Reverts() (gas: 36102)
FactoryBurnMintERC20_burnFromAlias:test_InsufficientAllowance_Reverts() (gas: 21986)
FactoryBurnMintERC20_burnFromAlias:test_SenderNotBurner_Reverts() (gas: 13415)
FactoryBurnMintERC20_constructor:test_Constructor_Success() (gas: 1475646)
FactoryBurnMintERC20_decreaseApproval:test_DecreaseApproval_Success() (gas: 31340)
FactoryBurnMintERC20_getCCIPAdmin:test_getCCIPAdmin_Success() (gas: 12684)
FactoryBurnMintERC20_getCCIPAdmin:test_setCCIPAdmin_Success() (gas: 23757)
FactoryBurnMintERC20_grantMintAndBurnRoles:test_GrantMintAndBurnRoles_Success() (gas: 121084)
FactoryBurnMintERC20_grantRole:test_GrantBurnAccess_Success() (gas: 53341)
FactoryBurnMintERC20_grantRole:test_GrantMany_Success() (gas: 961332)
FactoryBurnMintERC20_grantRole:test_GrantMintAccess_Success() (gas: 94068)
FactoryBurnMintERC20_increaseApproval:test_IncreaseApproval_Success() (gas: 44345)
FactoryBurnMintERC20_mint:test_BasicMint_Success() (gas: 149777)
FactoryBurnMintERC20_mint:test_MaxSupplyExceeded_Reverts() (gas: 50681)
FactoryBurnMintERC20_mint:test_SenderNotMinter_Reverts() (gas: 11372)
FactoryBurnMintERC20_supportsInterface:test_SupportsInterface_Success() (gas: 11439)
FactoryBurnMintERC20_transfer:test_InvalidAddress_Reverts() (gas: 10707)
FactoryBurnMintERC20_transfer:test_Transfer_Success() (gas: 42449)
FeeQuoter_applyDestChainConfigUpdates:test_InvalidChainFamilySelector_Revert() (gas: 16878)
FeeQuoter_applyDestChainConfigUpdates:test_InvalidDestChainConfigDestChainSelectorEqZero_Revert() (gas: 16780)
FeeQuoter_applyDestChainConfigUpdates:test_applyDestChainConfigUpdatesDefaultTxGasLimitEqZero_Revert() (gas: 16822)
FeeQuoter_applyDestChainConfigUpdates:test_applyDestChainConfigUpdatesDefaultTxGasLimitGtMaxPerMessageGasLimit_Revert() (gas: 41225)
FeeQuoter_applyDestChainConfigUpdates:test_applyDestChainConfigUpdatesZeroIntput_Success() (gas: 12506)
FeeQuoter_applyDestChainConfigUpdates:test_applyDestChainConfigUpdates_Success() (gas: 140338)
FeeQuoter_applyFeeTokensUpdates:test_ApplyFeeTokensUpdates_Success() (gas: 80418)
FeeQuoter_applyFeeTokensUpdates:test_OnlyCallableByOwner_Revert() (gas: 12709)
FeeQuoter_applyPremiumMultiplierWeiPerEthUpdates:test_OnlyCallableByOwnerOrAdmin_Revert() (gas: 11547)
FeeQuoter_applyPremiumMultiplierWeiPerEthUpdates:test_applyPremiumMultiplierWeiPerEthUpdatesMultipleTokens_Success() (gas: 54684)
FeeQuoter_applyPremiumMultiplierWeiPerEthUpdates:test_applyPremiumMultiplierWeiPerEthUpdatesSingleToken_Success() (gas: 45130)
FeeQuoter_applyPremiumMultiplierWeiPerEthUpdates:test_applyPremiumMultiplierWeiPerEthUpdatesZeroInput() (gas: 12332)
FeeQuoter_applyTokenTransferFeeConfigUpdates:test_ApplyTokenTransferFeeConfig_Success() (gas: 87853)
FeeQuoter_applyTokenTransferFeeConfigUpdates:test_ApplyTokenTransferFeeZeroInput() (gas: 13255)
FeeQuoter_applyTokenTransferFeeConfigUpdates:test_InvalidDestBytesOverhead_Revert() (gas: 17300)
FeeQuoter_applyTokenTransferFeeConfigUpdates:test_OnlyCallableByOwnerOrAdmin_Revert() (gas: 12352)
FeeQuoter_constructor:test_InvalidLinkTokenEqZeroAddress_Revert() (gas: 106579)
FeeQuoter_constructor:test_InvalidMaxFeeJuelsPerMsg_Revert() (gas: 110929)
FeeQuoter_constructor:test_InvalidStalenessThreshold_Revert() (gas: 110982)
FeeQuoter_constructor:test_Setup_Success() (gas: 5013710)
FeeQuoter_convertTokenAmount:test_ConvertTokenAmount_Success() (gas: 68383)
FeeQuoter_convertTokenAmount:test_LinkTokenNotSupported_Revert() (gas: 29076)
FeeQuoter_getDataAvailabilityCost:test_EmptyMessageCalculatesDataAvailabilityCost_Success() (gas: 96067)
FeeQuoter_getDataAvailabilityCost:test_SimpleMessageCalculatesDataAvailabilityCostUnsupportedDestChainSelector_Success() (gas: 14708)
FeeQuoter_getDataAvailabilityCost:test_SimpleMessageCalculatesDataAvailabilityCost_Success() (gas: 20786)
FeeQuoter_getTokenAndGasPrices:test_GetFeeTokenAndGasPrices_Success() (gas: 72793)
FeeQuoter_getTokenAndGasPrices:test_StaleGasPrice_Revert() (gas: 26331)
FeeQuoter_getTokenAndGasPrices:test_StalenessCheckDisabled_Success() (gas: 111781)
FeeQuoter_getTokenAndGasPrices:test_UnsupportedChain_Revert() (gas: 16103)
FeeQuoter_getTokenAndGasPrices:test_ZeroGasPrice_Success() (gas: 109015)
FeeQuoter_getTokenPrice:test_GetTokenPriceFromFeed_Success() (gas: 66273)
FeeQuoter_getTokenPrices:test_GetTokenPrices_Success() (gas: 78322)
FeeQuoter_getTokenTransferCost:test_CustomTokenBpsFee_Success() (gas: 39239)
FeeQuoter_getTokenTransferCost:test_FeeTokenBpsFee_Success() (gas: 34876)
FeeQuoter_getTokenTransferCost:test_LargeTokenTransferChargesMaxFeeAndGas_Success() (gas: 27950)
FeeQuoter_getTokenTransferCost:test_MixedTokenTransferFee_Success() (gas: 97567)
FeeQuoter_getTokenTransferCost:test_NoTokenTransferChargesZeroFee_Success() (gas: 20441)
FeeQuoter_getTokenTransferCost:test_SmallTokenTransferChargesMinFeeAndGas_Success() (gas: 27825)
FeeQuoter_getTokenTransferCost:test_ZeroAmountTokenTransferChargesMinFeeAndGas_Success() (gas: 27781)
FeeQuoter_getTokenTransferCost:test_ZeroFeeConfigChargesMinFee_Success() (gas: 40372)
FeeQuoter_getTokenTransferCost:test_getTokenTransferCost_selfServeUsesDefaults_Success() (gas: 29499)
FeeQuoter_getValidatedFee:test_DestinationChainNotEnabled_Revert() (gas: 18414)
FeeQuoter_getValidatedFee:test_EmptyMessage_Success() (gas: 82992)
FeeQuoter_getValidatedFee:test_EnforceOutOfOrder_Revert() (gas: 53530)
FeeQuoter_getValidatedFee:test_HighGasMessage_Success() (gas: 239454)
FeeQuoter_getValidatedFee:test_InvalidEVMAddress_Revert() (gas: 22617)
FeeQuoter_getValidatedFee:test_MessageGasLimitTooHigh_Revert() (gas: 29903)
FeeQuoter_getValidatedFee:test_MessageTooLarge_Revert() (gas: 100366)
FeeQuoter_getValidatedFee:test_MessageWithDataAndTokenTransfer_Success() (gas: 142856)
FeeQuoter_getValidatedFee:test_NotAFeeToken_Revert() (gas: 21223)
FeeQuoter_getValidatedFee:test_SingleTokenMessage_Success() (gas: 114090)
FeeQuoter_getValidatedFee:test_TooManyTokens_Revert() (gas: 22765)
FeeQuoter_getValidatedFee:test_ZeroDataAvailabilityMultiplier_Success() (gas: 63829)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedErc20Above18Decimals_Success() (gas: 1913624)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedErc20Below18Decimals_Success() (gas: 1913582)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedFeedAt0Decimals_Success() (gas: 1893701)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedFeedAt18Decimals_Success() (gas: 1913356)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedFlippedDecimals_Success() (gas: 1913560)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedMaxInt224Value_Success() (gas: 1913372)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeedOverStalenessPeriod_Success() (gas: 64610)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPriceFromFeed_Success() (gas: 64490)
FeeQuoter_getValidatedTokenPrice:test_GetValidatedTokenPrice_Success() (gas: 58894)
FeeQuoter_getValidatedTokenPrice:test_OverflowFeedPrice_Revert() (gas: 1913069)
FeeQuoter_getValidatedTokenPrice:test_StaleFeeToken_Success() (gas: 61764)
FeeQuoter_getValidatedTokenPrice:test_TokenNotSupportedFeed_Revert() (gas: 116495)
FeeQuoter_getValidatedTokenPrice:test_TokenNotSupported_Revert() (gas: 14037)
FeeQuoter_getValidatedTokenPrice:test_UnderflowFeedPrice_Revert() (gas: 1911746)
FeeQuoter_onReport:test_OnReport_StaleUpdate_Revert() (gas: 43675)
FeeQuoter_onReport:test_onReport_InvalidForwarder_Reverts() (gas: 23514)
FeeQuoter_onReport:test_onReport_Success() (gas: 80116)
FeeQuoter_onReport:test_onReport_UnsupportedToken_Reverts() (gas: 26882)
FeeQuoter_parseEVMExtraArgsFromBytes:test_EVMExtraArgsDefault_Success() (gas: 17404)
FeeQuoter_parseEVMExtraArgsFromBytes:test_EVMExtraArgsEnforceOutOfOrder_Revert() (gas: 21545)
FeeQuoter_parseEVMExtraArgsFromBytes:test_EVMExtraArgsGasLimitTooHigh_Revert() (gas: 18636)
FeeQuoter_parseEVMExtraArgsFromBytes:test_EVMExtraArgsInvalidExtraArgsTag_Revert() (gas: 18154)
FeeQuoter_parseEVMExtraArgsFromBytes:test_EVMExtraArgsV1_Success() (gas: 18513)
FeeQuoter_parseEVMExtraArgsFromBytes:test_EVMExtraArgsV2_Success() (gas: 18636)
FeeQuoter_processMessageArgs:test_processMessageArgs_InvalidEVMAddressDestToken_Revert() (gas: 44860)
FeeQuoter_processMessageArgs:test_processMessageArgs_InvalidExtraArgs_Revert() (gas: 19914)
FeeQuoter_processMessageArgs:test_processMessageArgs_MalformedEVMExtraArgs_Revert() (gas: 20333)
FeeQuoter_processMessageArgs:test_processMessageArgs_MessageFeeTooHigh_Revert() (gas: 17904)
FeeQuoter_processMessageArgs:test_processMessageArgs_SourceTokenDataTooLarge_Revert() (gas: 122889)
FeeQuoter_processMessageArgs:test_processMessageArgs_TokenAmountArraysMismatching_Revert() (gas: 42122)
FeeQuoter_processMessageArgs:test_processMessageArgs_WitEVMExtraArgsV2_Success() (gas: 28578)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithConvertedTokenAmount_Success() (gas: 29949)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithCorrectPoolReturnData_Success() (gas: 76465)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithEVMExtraArgsV1_Success() (gas: 28176)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithEmptyEVMExtraArgs_Success() (gas: 26047)
FeeQuoter_processMessageArgs:test_processMessageArgs_WithLinkTokenAmount_Success() (gas: 19523)
FeeQuoter_updatePrices:test_OnlyCallableByUpdater_Revert() (gas: 12176)
FeeQuoter_updatePrices:test_OnlyGasPrice_Success() (gas: 23730)
FeeQuoter_updatePrices:test_OnlyTokenPrice_Success() (gas: 28505)
FeeQuoter_updatePrices:test_UpdatableByAuthorizedCaller_Success() (gas: 74596)
FeeQuoter_updatePrices:test_UpdateMultiplePrices_Success() (gas: 145320)
FeeQuoter_updateTokenPriceFeeds:test_FeedNotUpdated() (gas: 50875)
FeeQuoter_updateTokenPriceFeeds:test_FeedUnset_Success() (gas: 63847)
FeeQuoter_updateTokenPriceFeeds:test_FeedUpdatedByNonOwner_Revert() (gas: 20142)
FeeQuoter_updateTokenPriceFeeds:test_MultipleFeedUpdate_Success() (gas: 89470)
FeeQuoter_updateTokenPriceFeeds:test_SingleFeedUpdate_Success() (gas: 51121)
FeeQuoter_updateTokenPriceFeeds:test_ZeroFeeds_Success() (gas: 12437)
FeeQuoter_validateDestFamilyAddress:test_InvalidEVMAddressEncodePacked_Revert() (gas: 10655)
FeeQuoter_validateDestFamilyAddress:test_InvalidEVMAddressPrecompiles_Revert() (gas: 4001603)
FeeQuoter_validateDestFamilyAddress:test_InvalidEVMAddress_Revert() (gas: 10839)
FeeQuoter_validateDestFamilyAddress:test_ValidEVMAddress_Success() (gas: 6731)
FeeQuoter_validateDestFamilyAddress:test_ValidNonEVMAddress_Success() (gas: 6511)
HybridLockReleaseUSDCTokenPool_TransferLiquidity:test_cannotTransferLiquidityDuringPendingMigration_Revert() (gas: 176771)
HybridLockReleaseUSDCTokenPool_TransferLiquidity:test_transferLiquidity_Success() (gas: 166828)
HybridLockReleaseUSDCTokenPool_lockOrBurn:test_PrimaryMechanism_Success() (gas: 135764)
HybridLockReleaseUSDCTokenPool_lockOrBurn:test_WhileMigrationPause_Revert() (gas: 109696)
HybridLockReleaseUSDCTokenPool_lockOrBurn:test_onLockReleaseMechanism_Success() (gas: 146859)
HybridLockReleaseUSDCTokenPool_lockOrBurn:test_onLockReleaseMechanism_thenSwitchToPrimary_Success() (gas: 208941)
HybridLockReleaseUSDCTokenPool_releaseOrMint:test_OnLockReleaseMechanism_Success() (gas: 213084)
HybridLockReleaseUSDCTokenPool_releaseOrMint:test_WhileMigrationPause_Revert() (gas: 109606)
HybridLockReleaseUSDCTokenPool_releaseOrMint:test_incomingMessageWithPrimaryMechanism() (gas: 265786)
LockReleaseTokenPool_canAcceptLiquidity:test_CanAcceptLiquidity_Success() (gas: 3129432)
LockReleaseTokenPool_lockOrBurn:test_LockOrBurnWithAllowList_Revert() (gas: 29734)
LockReleaseTokenPool_lockOrBurn:test_LockOrBurnWithAllowList_Success() (gas: 80502)
LockReleaseTokenPool_lockOrBurn:test_PoolBurnRevertNotHealthy_Revert() (gas: 59205)
LockReleaseTokenPool_provideLiquidity:test_LiquidityNotAccepted_Revert() (gas: 3125846)
LockReleaseTokenPool_provideLiquidity:test_Unauthorized_Revert() (gas: 11489)
LockReleaseTokenPool_releaseOrMint:test_ChainNotAllowed_Revert() (gas: 74080)
LockReleaseTokenPool_releaseOrMint:test_PoolMintNotHealthy_Revert() (gas: 54717)
LockReleaseTokenPool_releaseOrMint:test_ReleaseOrMint_Success() (gas: 223184)
LockReleaseTokenPool_setRebalancer:test_SetRebalancer_Revert() (gas: 10893)
LockReleaseTokenPool_setRebalancer:test_SetRebalancer_Success() (gas: 18072)
LockReleaseTokenPool_supportsInterface:test_SupportsInterface_Success() (gas: 10196)
LockReleaseTokenPool_transferLiquidity:test_transferLiquidity_Success() (gas: 83270)
LockReleaseTokenPool_transferLiquidity:test_transferLiquidity_transferTooMuch_Revert() (gas: 56019)
LockReleaseTokenPool_withdrawalLiquidity:test_InsufficientLiquidity_Revert() (gas: 60122)
LockReleaseTokenPool_withdrawalLiquidity:test_Unauthorized_Revert() (gas: 11397)
MerkleMultiProofTest:test_CVE_2023_34459() (gas: 5478)
MerkleMultiProofTest:test_EmptyLeaf_Revert() (gas: 3585)
MerkleMultiProofTest:test_MerkleRoot256() (gas: 394891)
MerkleMultiProofTest:test_MerkleRootSingleLeaf_Success() (gas: 3661)
MerkleMultiProofTest:test_SpecSync_gas() (gas: 34129)
MockRouterTest:test_ccipSendWithEVMExtraArgsV1_Success() (gas: 110073)
MockRouterTest:test_ccipSendWithEVMExtraArgsV2_Success() (gas: 132614)
MockRouterTest:test_ccipSendWithInsufficientNativeTokens_Revert() (gas: 34037)
MockRouterTest:test_ccipSendWithInvalidEVMExtraArgs_Revert() (gas: 106684)
MockRouterTest:test_ccipSendWithInvalidMsgValue_Revert() (gas: 60886)
MockRouterTest:test_ccipSendWithLinkFeeTokenAndValidMsgValue_Success() (gas: 126696)
MockRouterTest:test_ccipSendWithLinkFeeTokenButInsufficientAllowance_Revert() (gas: 63500)
MockRouterTest:test_ccipSendWithSufficientNativeFeeTokens_Success() (gas: 44048)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_MultipleConfigsBothLanes_Success() (gas: 133528)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_MultipleConfigs_Success() (gas: 315630)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_OnlyCallableByOwner_Revert() (gas: 17864)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_SingleConfigOutbound_Success() (gas: 76453)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_SingleConfig_Success() (gas: 76369)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_UpdateExistingConfigWithNoDifference_Success() (gas: 38736)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_UpdateExistingConfig_Success() (gas: 53869)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_ZeroChainSelector_Revert() (gas: 17109)
MultiAggregateRateLimiter_applyRateLimiterConfigUpdates:test_ZeroConfigs_Success() (gas: 12436)
MultiAggregateRateLimiter_constructor:test_ConstructorNoAuthorizedCallers_Success() (gas: 1958738)
MultiAggregateRateLimiter_constructor:test_Constructor_Success() (gas: 2075046)
MultiAggregateRateLimiter_getTokenBucket:test_GetTokenBucket_Success() (gas: 30794)
MultiAggregateRateLimiter_getTokenBucket:test_Refill_Success() (gas: 48099)
MultiAggregateRateLimiter_getTokenBucket:test_TimeUnderflow_Revert() (gas: 15929)
MultiAggregateRateLimiter_getTokenValue:test_GetTokenValue_Success() (gas: 17525)
MultiAggregateRateLimiter_getTokenValue:test_NoTokenPrice_Reverts() (gas: 21408)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageFromUnauthorizedCaller_Revert() (gas: 14617)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithDifferentTokensOnDifferentChains_Success() (gas: 210107)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithDisabledRateLimitToken_Success() (gas: 58416)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithNoTokens_Success() (gas: 17743)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithRateLimitDisabled_Success() (gas: 45162)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithRateLimitExceeded_Revert() (gas: 46370)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithRateLimitReset_Success() (gas: 76561)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithTokensOnDifferentChains_Success() (gas: 308233)
MultiAggregateRateLimiter_onInboundMessage:test_ValidateMessageWithTokens_Success() (gas: 50558)
MultiAggregateRateLimiter_onOutboundMessage:test_RateLimitValueDifferentLanes_Success() (gas: 51181)
MultiAggregateRateLimiter_onOutboundMessage:test_ValidateMessageWithNoTokens_Success() (gas: 19302)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageFromUnauthorizedCaller_Revert() (gas: 15913)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithDifferentTokensOnDifferentChains_Success() (gas: 209885)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithDisabledRateLimitToken_Success() (gas: 60182)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithRateLimitDisabled_Success() (gas: 46935)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithRateLimitExceeded_Revert() (gas: 48179)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithRateLimitReset_Success() (gas: 77728)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithTokensOnDifferentChains_Success() (gas: 308237)
MultiAggregateRateLimiter_onOutboundMessage:test_onOutboundMessage_ValidateMessageWithTokens_Success() (gas: 52346)
MultiAggregateRateLimiter_setFeeQuoter:test_OnlyOwner_Revert() (gas: 11337)
MultiAggregateRateLimiter_setFeeQuoter:test_Owner_Success() (gas: 19090)
MultiAggregateRateLimiter_setFeeQuoter:test_ZeroAddress_Revert() (gas: 10609)
MultiAggregateRateLimiter_updateRateLimitTokens:test_NonOwner_Revert() (gas: 18878)
MultiAggregateRateLimiter_updateRateLimitTokens:test_UpdateRateLimitTokensMultipleChains_Success() (gas: 280256)
MultiAggregateRateLimiter_updateRateLimitTokens:test_UpdateRateLimitTokensSingleChain_Success() (gas: 254729)
MultiAggregateRateLimiter_updateRateLimitTokens:test_UpdateRateLimitTokens_AddsAndRemoves_Success() (gas: 204595)
MultiAggregateRateLimiter_updateRateLimitTokens:test_UpdateRateLimitTokens_RemoveNonExistentToken_Success() (gas: 28826)
MultiAggregateRateLimiter_updateRateLimitTokens:test_ZeroDestToken_Revert() (gas: 18324)
MultiAggregateRateLimiter_updateRateLimitTokens:test_ZeroSourceToken_Revert() (gas: 18253)
MultiOCR3Base_setOCR3Configs:test_FMustBePositive_Revert() (gas: 59397)
MultiOCR3Base_setOCR3Configs:test_FTooHigh_Revert() (gas: 44190)
MultiOCR3Base_setOCR3Configs:test_MoreTransmittersThanSigners_Revert() (gas: 104822)
MultiOCR3Base_setOCR3Configs:test_NoTransmitters_Revert() (gas: 18886)
MultiOCR3Base_setOCR3Configs:test_RepeatSignerAddress_Revert() (gas: 283736)
MultiOCR3Base_setOCR3Configs:test_RepeatTransmitterAddress_Revert() (gas: 422361)
MultiOCR3Base_setOCR3Configs:test_SetConfigIgnoreSigners_Success() (gas: 511918)
MultiOCR3Base_setOCR3Configs:test_SetConfigWithSignersMismatchingTransmitters_Success() (gas: 680323)
MultiOCR3Base_setOCR3Configs:test_SetConfigWithSigners_Success() (gas: 828900)
MultiOCR3Base_setOCR3Configs:test_SetConfigWithoutSigners_Success() (gas: 457292)
MultiOCR3Base_setOCR3Configs:test_SetConfigsZeroInput_Success() (gas: 12481)
MultiOCR3Base_setOCR3Configs:test_SetMultipleConfigs_Success() (gas: 2141722)
MultiOCR3Base_setOCR3Configs:test_SignerCannotBeZeroAddress_Revert() (gas: 141835)
MultiOCR3Base_setOCR3Configs:test_StaticConfigChange_Revert() (gas: 807623)
MultiOCR3Base_setOCR3Configs:test_TooManySigners_Revert() (gas: 158867)
MultiOCR3Base_setOCR3Configs:test_TooManyTransmitters_Revert() (gas: 112335)
MultiOCR3Base_setOCR3Configs:test_TransmitterCannotBeZeroAddress_Revert() (gas: 254201)
MultiOCR3Base_setOCR3Configs:test_UpdateConfigSigners_Success() (gas: 861206)
MultiOCR3Base_setOCR3Configs:test_UpdateConfigTransmittersWithoutSigners_Success() (gas: 475852)
MultiOCR3Base_transmit:test_ConfigDigestMismatch_Revert() (gas: 42956)
MultiOCR3Base_transmit:test_ForkedChain_Revert() (gas: 48639)
MultiOCR3Base_transmit:test_InsufficientSignatures_Revert() (gas: 77138)
MultiOCR3Base_transmit:test_NonUniqueSignature_Revert() (gas: 65912)
MultiOCR3Base_transmit:test_SignatureOutOfRegistration_Revert() (gas: 33495)
MultiOCR3Base_transmit:test_TooManySignatures_Revert() (gas: 79795)
MultiOCR3Base_transmit:test_TransmitSigners_gas_Success() (gas: 33664)
MultiOCR3Base_transmit:test_TransmitWithExtraCalldataArgs_Revert() (gas: 47200)
MultiOCR3Base_transmit:test_TransmitWithLessCalldataArgs_Revert() (gas: 25768)
MultiOCR3Base_transmit:test_TransmitWithoutSignatureVerification_gas_Success() (gas: 18745)
MultiOCR3Base_transmit:test_UnAuthorizedTransmitter_Revert() (gas: 24234)
MultiOCR3Base_transmit:test_UnauthorizedSigner_Revert() (gas: 61275)
MultiOCR3Base_transmit:test_UnconfiguredPlugin_Revert() (gas: 39933)
MultiOCR3Base_transmit:test_ZeroSignatures_Revert() (gas: 33049)
MultiOnRampTokenPoolReentrancy:test_OnRampTokenPoolReentrancy_Success() (gas: 233865)
MultiRampsE2E:test_E2E_3MessagesMMultiOffRampSuccess_gas() (gas: 1512594)
NonceManager_NonceIncrementation:test_getIncrementedOutboundNonce_Success() (gas: 37934)
NonceManager_NonceIncrementation:test_incrementInboundNonce_Skip() (gas: 23706)
NonceManager_NonceIncrementation:test_incrementInboundNonce_Success() (gas: 38778)
NonceManager_NonceIncrementation:test_incrementNoncesInboundAndOutbound_Success() (gas: 71901)
NonceManager_OffRampUpgrade:test_NoPrevOffRampForChain_Success() (gas: 262171)
NonceManager_OffRampUpgrade:test_UpgradedNonceNewSenderStartsAtZero_Success() (gas: 265848)
NonceManager_OffRampUpgrade:test_UpgradedNonceStartsAtV1Nonce_Success() (gas: 329848)
NonceManager_OffRampUpgrade:test_UpgradedOffRampNonceSkipsIfMsgInFlight_Success() (gas: 300818)
NonceManager_OffRampUpgrade:test_UpgradedSenderNoncesReadsPreviousRampTransitive_Success() (gas: 249120)
NonceManager_OffRampUpgrade:test_UpgradedSenderNoncesReadsPreviousRamp_Success() (gas: 237027)
NonceManager_OffRampUpgrade:test_Upgraded_Success() (gas: 153760)
NonceManager_OnRampUpgrade:test_UpgradeNonceNewSenderStartsAtZero_Success() (gas: 169096)
NonceManager_OnRampUpgrade:test_UpgradeNonceStartsAtV1Nonce_Success() (gas: 221311)
NonceManager_OnRampUpgrade:test_UpgradeSenderNoncesReadsPreviousRamp_Success() (gas: 126745)
NonceManager_OnRampUpgrade:test_Upgrade_Success() (gas: 107815)
NonceManager_applyPreviousRampsUpdates:test_MultipleRampsUpdates() (gas: 123102)
NonceManager_applyPreviousRampsUpdates:test_PreviousRampAlreadySetOffRamp_Revert() (gas: 43079)
NonceManager_applyPreviousRampsUpdates:test_PreviousRampAlreadySetOnRampAndOffRamp_Revert() (gas: 64408)
NonceManager_applyPreviousRampsUpdates:test_PreviousRampAlreadySetOnRamp_Revert() (gas: 42943)
NonceManager_applyPreviousRampsUpdates:test_SingleRampUpdate() (gas: 66666)
NonceManager_applyPreviousRampsUpdates:test_ZeroInput() (gas: 12070)
NonceManager_typeAndVersion:test_typeAndVersion() (gas: 9705)
OCR2BaseNoChecks_setOCR2Config:test_FMustBePositive_Revert() (gas: 12210)
OCR2BaseNoChecks_setOCR2Config:test_RepeatAddress_Revert() (gas: 42431)
OCR2BaseNoChecks_setOCR2Config:test_SetConfigSuccess_gas() (gas: 84597)
OCR2BaseNoChecks_setOCR2Config:test_TooManyTransmitter_Revert() (gas: 38177)
OCR2BaseNoChecks_setOCR2Config:test_TransmitterCannotBeZeroAddress_Revert() (gas: 24308)
OCR2BaseNoChecks_transmit:test_ConfigDigestMismatch_Revert() (gas: 17499)
OCR2BaseNoChecks_transmit:test_ForkedChain_Revert() (gas: 26798)
OCR2BaseNoChecks_transmit:test_TransmitSuccess_gas() (gas: 27499)
OCR2BaseNoChecks_transmit:test_UnAuthorizedTransmitter_Revert() (gas: 21335)
OCR2Base_setOCR2Config:test_FMustBePositive_Revert() (gas: 12216)
OCR2Base_setOCR2Config:test_FTooHigh_Revert() (gas: 12372)
OCR2Base_setOCR2Config:test_OracleOutOfRegister_Revert() (gas: 14919)
OCR2Base_setOCR2Config:test_RepeatAddress_Revert() (gas: 45469)
OCR2Base_setOCR2Config:test_SetConfigSuccess_gas() (gas: 155220)
OCR2Base_setOCR2Config:test_SingerCannotBeZeroAddress_Revert() (gas: 24425)
OCR2Base_setOCR2Config:test_TooManySigners_Revert() (gas: 20535)
OCR2Base_setOCR2Config:test_TransmitterCannotBeZeroAddress_Revert() (gas: 47316)
OCR2Base_transmit:test_ConfigDigestMismatch_Revert() (gas: 19668)
OCR2Base_transmit:test_ForkedChain_Revert() (gas: 37749)
OCR2Base_transmit:test_NonUniqueSignature_Revert() (gas: 55360)
OCR2Base_transmit:test_SignatureOutOfRegistration_Revert() (gas: 20989)
OCR2Base_transmit:test_Transmit2SignersSuccess_gas() (gas: 51689)
OCR2Base_transmit:test_UnAuthorizedTransmitter_Revert() (gas: 23511)
OCR2Base_transmit:test_UnauthorizedSigner_Revert() (gas: 39707)
OCR2Base_transmit:test_WrongNumberOfSignatures_Revert() (gas: 20584)
OffRamp_afterOC3ConfigSet:test_afterOCR3ConfigSet_SignatureVerificationDisabled_Revert() (gas: 5913989)
OffRamp_applySourceChainConfigUpdates:test_AddMultipleChains_Success() (gas: 626106)
OffRamp_applySourceChainConfigUpdates:test_AddNewChain_Success() (gas: 166490)
OffRamp_applySourceChainConfigUpdates:test_ApplyZeroUpdates_Success() (gas: 16763)
OffRamp_applySourceChainConfigUpdates:test_InvalidOnRampUpdate_Revert() (gas: 272148)
OffRamp_applySourceChainConfigUpdates:test_ReplaceExistingChainOnRamp_Success() (gas: 168572)
OffRamp_applySourceChainConfigUpdates:test_ReplaceExistingChain_Success() (gas: 181027)
OffRamp_applySourceChainConfigUpdates:test_RouterAddress_Revert() (gas: 13463)
OffRamp_applySourceChainConfigUpdates:test_ZeroOnRampAddress_Revert() (gas: 72746)
OffRamp_applySourceChainConfigUpdates:test_ZeroSourceChainSelector_Revert() (gas: 15519)
OffRamp_batchExecute:test_MultipleReportsDifferentChainsSkipCursedChain_Success() (gas: 178112)
OffRamp_batchExecute:test_MultipleReportsDifferentChains_Success() (gas: 335759)
OffRamp_batchExecute:test_MultipleReportsSameChain_Success() (gas: 279025)
OffRamp_batchExecute:test_MultipleReportsSkipDuplicate_Success() (gas: 169394)
OffRamp_batchExecute:test_OutOfBoundsGasLimitsAccess_Revert() (gas: 189154)
OffRamp_batchExecute:test_SingleReport_Success() (gas: 157181)
OffRamp_batchExecute:test_Unhealthy_Success() (gas: 546430)
OffRamp_batchExecute:test_ZeroReports_Revert() (gas: 10622)
OffRamp_ccipReceive:test_Reverts() (gas: 15407)
OffRamp_commit:test_CommitOnRampMismatch_Revert() (gas: 92905)
OffRamp_commit:test_FailedRMNVerification_Reverts() (gas: 61599)
OffRamp_commit:test_InvalidIntervalMinLargerThanMax_Revert() (gas: 68173)
OffRamp_commit:test_InvalidInterval_Revert() (gas: 64291)
OffRamp_commit:test_InvalidRootRevert() (gas: 63356)
OffRamp_commit:test_NoConfigWithOtherConfigPresent_Revert() (gas: 6674716)
OffRamp_commit:test_NoConfig_Revert() (gas: 6258388)
OffRamp_commit:test_OnlyGasPriceUpdates_Success() (gas: 113042)
OffRamp_commit:test_OnlyPriceUpdateStaleReport_Revert() (gas: 121403)
OffRamp_commit:test_OnlyTokenPriceUpdates_Success() (gas: 113063)
OffRamp_commit:test_PriceSequenceNumberCleared_Success() (gas: 355198)
OffRamp_commit:test_ReportAndPriceUpdate_Success() (gas: 164400)
OffRamp_commit:test_ReportOnlyRootSuccess_gas() (gas: 139413)
OffRamp_commit:test_RootAlreadyCommitted_Revert() (gas: 146555)
OffRamp_commit:test_SourceChainNotEnabled_Revert() (gas: 59858)
OffRamp_commit:test_StaleReportWithRoot_Success() (gas: 232042)
OffRamp_commit:test_UnauthorizedTransmitter_Revert() (gas: 125409)
OffRamp_commit:test_Unhealthy_Revert() (gas: 58633)
OffRamp_commit:test_ValidPriceUpdateThenStaleReportWithRoot_Success() (gas: 206713)
OffRamp_commit:test_ZeroEpochAndRound_Revert() (gas: 51722)
OffRamp_constructor:test_Constructor_Success() (gas: 6219782)
OffRamp_constructor:test_SourceChainSelector_Revert() (gas: 135943)
OffRamp_constructor:test_ZeroChainSelector_Revert() (gas: 103375)
OffRamp_constructor:test_ZeroNonceManager_Revert() (gas: 101269)
OffRamp_constructor:test_ZeroOnRampAddress_Revert() (gas: 161468)
OffRamp_constructor:test_ZeroRMNRemote_Revert() (gas: 101189)
OffRamp_constructor:test_ZeroTokenAdminRegistry_Revert() (gas: 101227)
OffRamp_execute:test_IncorrectArrayType_Revert() (gas: 17639)
OffRamp_execute:test_LargeBatch_Success() (gas: 3427645)
OffRamp_execute:test_MultipleReportsWithPartialValidationFailures_Success() (gas: 373111)
OffRamp_execute:test_MultipleReports_Success() (gas: 301100)
OffRamp_execute:test_NoConfigWithOtherConfigPresent_Revert() (gas: 7083648)
OffRamp_execute:test_NoConfig_Revert() (gas: 6308123)
OffRamp_execute:test_NonArray_Revert() (gas: 27599)
OffRamp_execute:test_SingleReport_Success() (gas: 176391)
OffRamp_execute:test_UnauthorizedTransmitter_Revert() (gas: 148409)
OffRamp_execute:test_WrongConfigWithSigners_Revert() (gas: 7086397)
OffRamp_execute:test_ZeroReports_Revert() (gas: 17361)
OffRamp_executeSingleMessage:test_MessageSender_Revert() (gas: 18533)
OffRamp_executeSingleMessage:test_NonContractWithTokens_Success() (gas: 238070)
OffRamp_executeSingleMessage:test_NonContract_Success() (gas: 20781)
OffRamp_executeSingleMessage:test_TokenHandlingError_Revert() (gas: 198585)
OffRamp_executeSingleMessage:test_ZeroGasDONExecution_Revert() (gas: 49306)
OffRamp_executeSingleMessage:test_executeSingleMessage_NoTokens_Success() (gas: 48750)
OffRamp_executeSingleMessage:test_executeSingleMessage_WithFailingValidationNoRouterCall_Revert() (gas: 218028)
OffRamp_executeSingleMessage:test_executeSingleMessage_WithFailingValidation_Revert() (gas: 85296)
OffRamp_executeSingleMessage:test_executeSingleMessage_WithTokens_Success() (gas: 268187)
OffRamp_executeSingleMessage:test_executeSingleMessage_WithVInterception_Success() (gas: 91724)
OffRamp_executeSingleReport:test_DisabledSourceChain_Revert() (gas: 28319)
OffRamp_executeSingleReport:test_EmptyReport_Revert() (gas: 22074)
OffRamp_executeSingleReport:test_InvalidSourcePoolAddress_Success() (gas: 472256)
OffRamp_executeSingleReport:test_ManualExecutionNotYetEnabled_Revert() (gas: 48431)
OffRamp_executeSingleReport:test_MismatchingDestChainSelector_Revert() (gas: 34018)
OffRamp_executeSingleReport:test_NonExistingSourceChain_Revert() (gas: 28495)
OffRamp_executeSingleReport:test_ReceiverError_Success() (gas: 188133)
OffRamp_executeSingleReport:test_RetryFailedMessageWithoutManualExecution_Revert() (gas: 198626)
OffRamp_executeSingleReport:test_RootNotCommitted_Revert() (gas: 40800)
OffRamp_executeSingleReport:test_RouterYULCall_Revert() (gas: 413282)
OffRamp_executeSingleReport:test_SingleMessageNoTokensOtherChain_Success() (gas: 249874)
OffRamp_executeSingleReport:test_SingleMessageNoTokensUnordered_Success() (gas: 193688)
OffRamp_executeSingleReport:test_SingleMessageNoTokens_Success() (gas: 213722)
OffRamp_executeSingleReport:test_SingleMessageToNonCCIPReceiver_Success() (gas: 249587)
OffRamp_executeSingleReport:test_SingleMessagesNoTokensSuccess_gas() (gas: 142200)
OffRamp_executeSingleReport:test_SkippedIncorrectNonceStillExecutes_Success() (gas: 403400)
OffRamp_executeSingleReport:test_SkippedIncorrectNonce_Success() (gas: 58352)
OffRamp_executeSingleReport:test_TokenDataMismatch_Revert() (gas: 73930)
OffRamp_executeSingleReport:test_TwoMessagesWithTokensAndGE_Success() (gas: 575505)
OffRamp_executeSingleReport:test_TwoMessagesWithTokensSuccess_gas() (gas: 524219)
OffRamp_executeSingleReport:test_UnexpectedTokenData_Revert() (gas: 33823)
OffRamp_executeSingleReport:test_UnhealthySingleChainCurse_Revert() (gas: 541960)
OffRamp_executeSingleReport:test_Unhealthy_Success() (gas: 541974)
OffRamp_executeSingleReport:test_WithCurseOnAnotherSourceChain_Success() (gas: 452599)
OffRamp_executeSingleReport:test__execute_SkippedAlreadyExecutedMessageUnordered_Success() (gas: 136018)
OffRamp_executeSingleReport:test__execute_SkippedAlreadyExecutedMessage_Success() (gas: 165723)
OffRamp_getExecutionState:test_FillExecutionState_Success() (gas: 3885554)
OffRamp_getExecutionState:test_GetDifferentChainExecutionState_Success() (gas: 120606)
OffRamp_getExecutionState:test_GetExecutionState_Success() (gas: 89288)
OffRamp_manuallyExecute:test_ManualExecGasLimitMismatchSingleReport_Revert() (gas: 81352)
OffRamp_manuallyExecute:test_manuallyExecute_DestinationGasAmountCountMismatch_Revert() (gas: 74113)
OffRamp_manuallyExecute:test_manuallyExecute_DoesNotRevertIfUntouched_Success() (gas: 173204)
OffRamp_manuallyExecute:test_manuallyExecute_FailedTx_Revert() (gas: 214198)
OffRamp_manuallyExecute:test_manuallyExecute_ForkedChain_Revert() (gas: 27122)
OffRamp_manuallyExecute:test_manuallyExecute_GasLimitMismatchMultipleReports_Revert() (gas: 165422)
OffRamp_manuallyExecute:test_manuallyExecute_InvalidReceiverExecutionGasLimit_Revert() (gas: 27659)
OffRamp_manuallyExecute:test_manuallyExecute_InvalidTokenGasOverride_Revert() (gas: 55236)
OffRamp_manuallyExecute:test_manuallyExecute_LowGasLimit_Success() (gas: 498623)
OffRamp_manuallyExecute:test_manuallyExecute_MultipleReportsWithSingleCursedLane_Revert() (gas: 316276)
OffRamp_manuallyExecute:test_manuallyExecute_ReentrancyFails_Success() (gas: 2242292)
OffRamp_manuallyExecute:test_manuallyExecute_SourceChainSelectorMismatch_Revert() (gas: 165599)
OffRamp_manuallyExecute:test_manuallyExecute_Success() (gas: 227243)
OffRamp_manuallyExecute:test_manuallyExecute_WithGasOverride_Success() (gas: 227783)
OffRamp_manuallyExecute:test_manuallyExecute_WithMultiReportGasOverride_Success() (gas: 781795)
OffRamp_manuallyExecute:test_manuallyExecute_WithPartialMessages_Success() (gas: 347514)
OffRamp_releaseOrMintSingleToken:test__releaseOrMintSingleToken_NotACompatiblePool_Revert() (gas: 37656)
OffRamp_releaseOrMintSingleToken:test__releaseOrMintSingleToken_Success() (gas: 101388)
OffRamp_releaseOrMintSingleToken:test__releaseOrMintSingleToken_TokenHandlingError_transfer_Revert() (gas: 79803)
OffRamp_releaseOrMintSingleToken:test_releaseOrMintToken_InvalidDataLength_Revert() (gas: 36752)
OffRamp_releaseOrMintSingleToken:test_releaseOrMintToken_ReleaseOrMintBalanceMismatch_Revert() (gas: 91284)
OffRamp_releaseOrMintSingleToken:test_releaseOrMintToken_TokenHandlingError_BalanceOf_Revert() (gas: 37241)
OffRamp_releaseOrMintSingleToken:test_releaseOrMintToken_skip_ReleaseOrMintBalanceMismatch_if_pool_Revert() (gas: 83440)
OffRamp_releaseOrMintTokens:test_TokenHandlingError_Reverts() (gas: 155850)
OffRamp_releaseOrMintTokens:test__releaseOrMintTokens_PoolIsNotAPool_Reverts() (gas: 23903)
OffRamp_releaseOrMintTokens:test_releaseOrMintTokens_InvalidDataLengthReturnData_Revert() (gas: 62773)
OffRamp_releaseOrMintTokens:test_releaseOrMintTokens_PoolDoesNotSupportDest_Reverts() (gas: 78303)
OffRamp_releaseOrMintTokens:test_releaseOrMintTokens_Success() (gas: 168497)
OffRamp_releaseOrMintTokens:test_releaseOrMintTokens_WithGasOverride_Success() (gas: 170409)
OffRamp_releaseOrMintTokens:test_releaseOrMintTokens_destDenominatedDecimals_Success() (gas: 181670)
OffRamp_setDynamicConfig:test_FeeQuoterZeroAddress_Revert() (gas: 11291)
OffRamp_setDynamicConfig:test_NonOwner_Revert() (gas: 13906)
OffRamp_setDynamicConfig:test_SetDynamicConfigWithInterceptor_Success() (gas: 46378)
OffRamp_setDynamicConfig:test_SetDynamicConfig_Success() (gas: 24420)
OffRamp_trialExecute:test_RateLimitError_Success() (gas: 212846)
OffRamp_trialExecute:test_TokenHandlingErrorIsCaught_Success() (gas: 221512)
OffRamp_trialExecute:test_TokenPoolIsNotAContract_Success() (gas: 289387)
OffRamp_trialExecute:test_trialExecute_Success() (gas: 271931)
OnRampTokenPoolReentrancy:test_OnRampTokenPoolReentrancy_Success() (gas: 398904)
OnRamp_applyAllowListUpdates:test_applyAllowListUpdates_InvalidAllowListRequestDisabledAllowListWithAdds() (gas: 18030)
OnRamp_applyAllowListUpdates:test_applyAllowListUpdates_Revert() (gas: 67893)
OnRamp_applyAllowListUpdates:test_applyAllowListUpdates_Success() (gas: 325457)
OnRamp_applyDestChainConfigUpdates:test_ApplyDestChainConfigUpdates_Success() (gas: 65878)
OnRamp_applyDestChainConfigUpdates:test_ApplyDestChainConfigUpdates_WithInvalidChainSelector_Revert() (gas: 13631)
OnRamp_constructor:test_Constructor_EnableAllowList_ForwardFromRouter_Reverts() (gas: 2673564)
OnRamp_constructor:test_Constructor_InvalidConfigChainSelectorEqZero_Revert() (gas: 95249)
OnRamp_constructor:test_Constructor_InvalidConfigNonceManagerEqAddressZero_Revert() (gas: 93191)
OnRamp_constructor:test_Constructor_InvalidConfigRMNProxyEqAddressZero_Revert() (gas: 98224)
OnRamp_constructor:test_Constructor_InvalidConfigTokenAdminRegistryEqAddressZero_Revert() (gas: 93247)
OnRamp_constructor:test_Constructor_Success() (gas: 2753286)
OnRamp_forwardFromRouter:test_ForwardFromRouterExtraArgsV2AllowOutOfOrderTrue_Success() (gas: 115379)
OnRamp_forwardFromRouter:test_ForwardFromRouterExtraArgsV2_Success() (gas: 146180)
OnRamp_forwardFromRouter:test_ForwardFromRouterSuccessCustomExtraArgs() (gas: 145777)
OnRamp_forwardFromRouter:test_ForwardFromRouterSuccessEmptyExtraArgs() (gas: 143938)
OnRamp_forwardFromRouter:test_ForwardFromRouterSuccessLegacyExtraArgs() (gas: 145974)
OnRamp_forwardFromRouter:test_ForwardFromRouter_Success() (gas: 145372)
OnRamp_forwardFromRouter:test_ForwardFromRouter_Success_ConfigurableSourceRouter() (gas: 140713)
OnRamp_forwardFromRouter:test_InvalidExtraArgsTag_Revert() (gas: 38554)
OnRamp_forwardFromRouter:test_MessageInterceptionError_Revert() (gas: 143063)
OnRamp_forwardFromRouter:test_MesssageFeeTooHigh_Revert() (gas: 36596)
OnRamp_forwardFromRouter:test_MultiCannotSendZeroTokens_Revert() (gas: 36527)
OnRamp_forwardFromRouter:test_OriginalSender_Revert() (gas: 18291)
OnRamp_forwardFromRouter:test_Paused_Revert() (gas: 38431)
OnRamp_forwardFromRouter:test_Permissions_Revert() (gas: 23640)
OnRamp_forwardFromRouter:test_ShouldIncrementNonceOnlyOnOrdered_Success() (gas: 186528)
OnRamp_forwardFromRouter:test_ShouldIncrementSeqNumAndNonce_Success() (gas: 212912)
OnRamp_forwardFromRouter:test_ShouldStoreLinkFees() (gas: 147028)
OnRamp_forwardFromRouter:test_ShouldStoreNonLinkFees() (gas: 161133)
OnRamp_forwardFromRouter:test_SourceTokenDataTooLarge_Revert() (gas: 3984961)
OnRamp_forwardFromRouter:test_UnAllowedOriginalSender_Revert() (gas: 24010)
OnRamp_forwardFromRouter:test_UnsupportedToken_Revert() (gas: 75866)
OnRamp_forwardFromRouter:test_forwardFromRouter_UnsupportedToken_Revert() (gas: 38599)
OnRamp_forwardFromRouter:test_forwardFromRouter_WithInterception_Success() (gas: 281479)
OnRamp_getFee:test_EmptyMessage_Success() (gas: 98597)
OnRamp_getFee:test_EnforceOutOfOrder_Revert() (gas: 65467)
OnRamp_getFee:test_GetFeeOfZeroForTokenMessage_Success() (gas: 87012)
OnRamp_getFee:test_NotAFeeTokenButPricedToken_Revert() (gas: 35114)
OnRamp_getFee:test_SingleTokenMessage_Success() (gas: 113725)
OnRamp_getFee:test_Unhealthy_Revert() (gas: 17039)
OnRamp_getSupportedTokens:test_GetSupportedTokens_Revert() (gas: 10474)
OnRamp_getTokenPool:test_GetTokenPool_Success() (gas: 35348)
OnRamp_setDynamicConfig:test_setDynamicConfig_InvalidConfigFeeAggregatorEqAddressZero_Revert() (gas: 11536)
OnRamp_setDynamicConfig:test_setDynamicConfig_InvalidConfigFeeQuoterEqAddressZero_Revert() (gas: 13195)
OnRamp_setDynamicConfig:test_setDynamicConfig_InvalidConfigInvalidConfig_Revert() (gas: 11522)
OnRamp_setDynamicConfig:test_setDynamicConfig_InvalidConfigOnlyOwner_Revert() (gas: 16850)
OnRamp_setDynamicConfig:test_setDynamicConfig_InvalidConfigReentrancyGuardEnteredEqTrue_Revert() (gas: 13265)
OnRamp_setDynamicConfig:test_setDynamicConfig_Success() (gas: 56347)
OnRamp_withdrawFeeTokens:test_WithdrawFeeTokens_Success() (gas: 97341)
PingPong_ccipReceive:test_CcipReceive_Success() (gas: 155846)
PingPong_plumbing:test_OutOfOrderExecution_Success() (gas: 20310)
PingPong_plumbing:test_Pausing_Success() (gas: 17810)
PingPong_startPingPong:test_StartPingPong_With_OOO_Success() (gas: 167068)
PingPong_startPingPong:test_StartPingPong_With_Sequenced_Ordered_Success() (gas: 186486)
RMNHome__validateStaticAndDynamicConfig:test_validateStaticAndDynamicConfig_DuplicateOffchainPublicKey_reverts() (gas: 18822)
RMNHome__validateStaticAndDynamicConfig:test_validateStaticAndDynamicConfig_DuplicatePeerId_reverts() (gas: 18682)
RMNHome__validateStaticAndDynamicConfig:test_validateStaticAndDynamicConfig_DuplicateSourceChain_reverts() (gas: 20371)
RMNHome__validateStaticAndDynamicConfig:test_validateStaticAndDynamicConfig_MinObserversTooHigh_reverts() (gas: 20810)
RMNHome__validateStaticAndDynamicConfig:test_validateStaticAndDynamicConfig_OutOfBoundsNodesLength_reverts() (gas: 137268)
RMNHome__validateStaticAndDynamicConfig:test_validateStaticAndDynamicConfig_OutOfBoundsObserverNodeIndex_reverts() (gas: 20472)
RMNHome_getConfigDigests:test_getConfigDigests_success() (gas: 1077745)
RMNHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive_ConfigDigestMismatch_reverts() (gas: 23857)
RMNHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive_NoOpStateTransitionNotAllowed_reverts() (gas: 10575)
RMNHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive_OnlyOwner_reverts() (gas: 10936)
RMNHome_promoteCandidateAndRevokeActive:test_promoteCandidateAndRevokeActive_success() (gas: 1083071)
RMNHome_revokeCandidate:test_revokeCandidate_ConfigDigestMismatch_reverts() (gas: 19063)
RMNHome_revokeCandidate:test_revokeCandidate_OnlyOwner_reverts() (gas: 10963)
RMNHome_revokeCandidate:test_revokeCandidate_RevokingZeroDigestNotAllowed_reverts() (gas: 10606)
RMNHome_revokeCandidate:test_revokeCandidate_success() (gas: 28147)
RMNHome_setCandidate:test_setCandidate_ConfigDigestMismatch_reverts() (gas: 594679)
RMNHome_setCandidate:test_setCandidate_OnlyOwner_reverts() (gas: 15177)
RMNHome_setCandidate:test_setCandidate_success() (gas: 588379)
RMNHome_setDynamicConfig:test_setDynamicConfig_DigestNotFound_reverts() (gas: 30159)
RMNHome_setDynamicConfig:test_setDynamicConfig_MinObserversTooHigh_reverts() (gas: 18848)
RMNHome_setDynamicConfig:test_setDynamicConfig_OnlyOwner_reverts() (gas: 14115)
RMNHome_setDynamicConfig:test_setDynamicConfig_success() (gas: 103911)
RMNRemote_constructor:test_constructor_success() (gas: 8334)
RMNRemote_constructor:test_constructor_zeroChainSelector_reverts() (gas: 59165)
RMNRemote_curse:test_curse_AlreadyCursed_duplicateSubject_reverts() (gas: 154457)
RMNRemote_curse:test_curse_calledByNonOwner_reverts() (gas: 18780)
RMNRemote_curse:test_curse_success() (gas: 149365)
RMNRemote_global_and_legacy_curses:test_global_and_legacy_curses_success() (gas: 133464)
RMNRemote_setConfig:test_setConfig_addSigner_removeSigner_success() (gas: 976479)
RMNRemote_setConfig:test_setConfig_duplicateOnChainPublicKey_reverts() (gas: 323272)
RMNRemote_setConfig:test_setConfig_invalidSignerOrder_reverts() (gas: 80138)
RMNRemote_setConfig:test_setConfig_minSignersIs0_success() (gas: 700548)
RMNRemote_setConfig:test_setConfig_minSignersTooHigh_reverts() (gas: 54024)
RMNRemote_uncurse:test_uncurse_NotCursed_duplicatedUncurseSubject_reverts() (gas: 51912)
RMNRemote_uncurse:test_uncurse_calledByNonOwner_reverts() (gas: 18748)
RMNRemote_uncurse:test_uncurse_success() (gas: 40151)
RMNRemote_verify_withConfigNotSet:test_verify_reverts() (gas: 13650)
RMNRemote_verify_withConfigSet:test_verify_InvalidSignature_reverts() (gas: 78519)
RMNRemote_verify_withConfigSet:test_verify_OutOfOrderSignatures_duplicateSignature_reverts() (gas: 76336)
RMNRemote_verify_withConfigSet:test_verify_OutOfOrderSignatures_not_sorted_reverts() (gas: 83399)
RMNRemote_verify_withConfigSet:test_verify_ThresholdNotMet_reverts() (gas: 153002)
RMNRemote_verify_withConfigSet:test_verify_UnexpectedSigner_reverts() (gas: 387667)
RMNRemote_verify_withConfigSet:test_verify_minSignersIsZero_success() (gas: 184524)
RMNRemote_verify_withConfigSet:test_verify_success() (gas: 68207)
RMN_constructor:test_Constructor_Success() (gas: 48994)
RMN_getRecordedCurseRelatedOps:test_OpsPostDeployment() (gas: 19732)
RMN_lazyVoteToCurseUpdate_Benchmark:test_VoteToCurseLazilyRetain3VotersUponConfigChange_gas() (gas: 152296)
RMN_ownerUnbless:test_Unbless_Success() (gas: 74936)
RMN_ownerUnvoteToCurse:test_CanBlessAndCurseAfterGlobalCurseIsLifted() (gas: 471829)
RMN_ownerUnvoteToCurse:test_IsIdempotent() (gas: 398492)
RMN_ownerUnvoteToCurse:test_NonOwner_Revert() (gas: 18723)
RMN_ownerUnvoteToCurse:test_OwnerUnvoteToCurseSuccess_gas() (gas: 358084)
RMN_ownerUnvoteToCurse:test_UnknownVoter_Revert() (gas: 33190)
RMN_ownerUnvoteToCurse_Benchmark:test_OwnerUnvoteToCurse_1Voter_LiftsCurse_gas() (gas: 262408)
RMN_permaBlessing:test_PermaBlessing() (gas: 202777)
RMN_setConfig:test_BlessVoterIsZeroAddress_Revert() (gas: 15500)
RMN_setConfig:test_EitherThresholdIsZero_Revert() (gas: 21107)
RMN_setConfig:test_NonOwner_Revert() (gas: 14725)
RMN_setConfig:test_RepeatedAddress_Revert() (gas: 18219)
RMN_setConfig:test_SetConfigSuccess_gas() (gas: 104154)
RMN_setConfig:test_TotalWeightsSmallerThanEachThreshold_Revert() (gas: 30185)
RMN_setConfig:test_VoteToBlessByEjectedVoter_Revert() (gas: 130461)
RMN_setConfig:test_VotersLengthIsZero_Revert() (gas: 12149)
RMN_setConfig:test_WeightIsZeroAddress_Revert() (gas: 15740)
RMN_setConfig_Benchmark_1:test_SetConfig_7Voters_gas() (gas: 659600)
RMN_setConfig_Benchmark_2:test_ResetConfig_7Voters_gas() (gas: 212652)
RMN_unvoteToCurse:test_InvalidCursesHash() (gas: 26430)
RMN_unvoteToCurse:test_OwnerSkips() (gas: 33831)
RMN_unvoteToCurse:test_OwnerSucceeds() (gas: 64005)
RMN_unvoteToCurse:test_UnauthorizedVoter() (gas: 47715)
RMN_unvoteToCurse:test_ValidCursesHash() (gas: 61145)
RMN_unvoteToCurse:test_VotersCantLiftCurseButOwnerCan() (gas: 629190)
RMN_voteToBless:test_Curse_Revert() (gas: 473408)
RMN_voteToBless:test_IsAlreadyBlessed_Revert() (gas: 115435)
RMN_voteToBless:test_RootSuccess() (gas: 558661)
RMN_voteToBless:test_SenderAlreadyVoted_Revert() (gas: 97234)
RMN_voteToBless:test_UnauthorizedVoter_Revert() (gas: 17126)
RMN_voteToBless_Benchmark:test_1RootSuccess_gas() (gas: 44718)
RMN_voteToBless_Benchmark:test_3RootSuccess_gas() (gas: 98694)
RMN_voteToBless_Benchmark:test_5RootSuccess_gas() (gas: 152608)
RMN_voteToBless_Blessed_Benchmark:test_1RootSuccessBecameBlessed_gas() (gas: 29682)
RMN_voteToBless_Blessed_Benchmark:test_1RootSuccess_gas() (gas: 27628)
RMN_voteToBless_Blessed_Benchmark:test_3RootSuccess_gas() (gas: 81626)
RMN_voteToBless_Blessed_Benchmark:test_5RootSuccess_gas() (gas: 135518)
RMN_voteToCurse:test_CurseOnlyWhenThresholdReached_Success() (gas: 1651170)
RMN_voteToCurse:test_EmptySubjects_Revert() (gas: 14061)
RMN_voteToCurse:test_EvenIfAlreadyCursed_Success() (gas: 535124)
RMN_voteToCurse:test_OwnerCanCurseAndUncurse() (gas: 400060)
RMN_voteToCurse:test_RepeatedSubject_Revert() (gas: 144405)
RMN_voteToCurse:test_ReusedCurseId_Revert() (gas: 146972)
RMN_voteToCurse:test_UnauthorizedVoter_Revert() (gas: 12666)
RMN_voteToCurse:test_VoteToCurse_NoCurse_Success() (gas: 187556)
RMN_voteToCurse:test_VoteToCurse_YesCurse_Success() (gas: 473079)
RMN_voteToCurse_2:test_VotesAreDroppedIfSubjectIsNotCursedDuringConfigChange() (gas: 371083)
RMN_voteToCurse_2:test_VotesAreRetainedIfSubjectIsCursedDuringConfigChange() (gas: 1154362)
RMN_voteToCurse_Benchmark_1:test_VoteToCurse_NewSubject_NewVoter_NoCurse_gas() (gas: 141118)
RMN_voteToCurse_Benchmark_1:test_VoteToCurse_NewSubject_NewVoter_YesCurse_gas() (gas: 165258)
RMN_voteToCurse_Benchmark_2:test_VoteToCurse_OldSubject_NewVoter_NoCurse_gas() (gas: 121437)
RMN_voteToCurse_Benchmark_2:test_VoteToCurse_OldSubject_OldVoter_NoCurse_gas() (gas: 98373)
RMN_voteToCurse_Benchmark_3:test_VoteToCurse_OldSubject_NewVoter_YesCurse_gas() (gas: 145784)
RateLimiter_constructor:test_Constructor_Success() (gas: 19734)
RateLimiter_consume:test_AggregateValueMaxCapacityExceeded_Revert() (gas: 16042)
RateLimiter_consume:test_AggregateValueRateLimitReached_Revert() (gas: 22390)
RateLimiter_consume:test_ConsumeAggregateValue_Success() (gas: 31518)
RateLimiter_consume:test_ConsumeTokens_Success() (gas: 20381)
RateLimiter_consume:test_ConsumeUnlimited_Success() (gas: 40687)
RateLimiter_consume:test_ConsumingMoreThanUint128_Revert() (gas: 15822)
RateLimiter_consume:test_RateLimitReachedOverConsecutiveBlocks_Revert() (gas: 25798)
RateLimiter_consume:test_Refill_Success() (gas: 37444)
RateLimiter_consume:test_TokenMaxCapacityExceeded_Revert() (gas: 18388)
RateLimiter_consume:test_TokenRateLimitReached_Revert() (gas: 24886)
RateLimiter_currentTokenBucketState:test_CurrentTokenBucketState_Success() (gas: 38944)
RateLimiter_currentTokenBucketState:test_Refill_Success() (gas: 46849)
RateLimiter_setTokenBucketConfig:test_SetRateLimiterConfig_Success() (gas: 38506)
RegistryModuleOwnerCustom_constructor:test_constructor_Revert() (gas: 36107)
RegistryModuleOwnerCustom_registerAccessControlDefaultAdmin:test_registerAccessControlDefaultAdmin_Revert() (gas: 20206)
RegistryModuleOwnerCustom_registerAccessControlDefaultAdmin:test_registerAccessControlDefaultAdmin_Success() (gas: 130628)
RegistryModuleOwnerCustom_registerAdminViaGetCCIPAdmin:test_registerAdminViaGetCCIPAdmin_Revert() (gas: 19773)
RegistryModuleOwnerCustom_registerAdminViaGetCCIPAdmin:test_registerAdminViaGetCCIPAdmin_Success() (gas: 130108)
RegistryModuleOwnerCustom_registerAdminViaOwner:test_registerAdminViaOwner_Revert() (gas: 19593)
RegistryModuleOwnerCustom_registerAdminViaOwner:test_registerAdminViaOwner_Success() (gas: 129927)
Router_applyRampUpdates:test_OffRampMismatch_Revert() (gas: 89366)
Router_applyRampUpdates:test_OffRampUpdatesWithRouting() (gas: ********)
Router_applyRampUpdates:test_OnRampDisable() (gas: 56007)
Router_applyRampUpdates:test_OnlyOwner_Revert() (gas: 12356)
Router_ccipSend:test_CCIPSendLinkFeeNoTokenSuccess_gas() (gas: 115091)
Router_ccipSend:test_CCIPSendLinkFeeOneTokenSuccess_gas() (gas: 203469)
Router_ccipSend:test_CCIPSendNativeFeeNoTokenSuccess_gas() (gas: 127448)
Router_ccipSend:test_CCIPSendNativeFeeOneTokenSuccess_gas() (gas: 215829)
Router_ccipSend:test_FeeTokenAmountTooLow_Revert() (gas: 69017)
Router_ccipSend:test_InvalidMsgValue() (gas: 32155)
Router_ccipSend:test_NativeFeeTokenInsufficientValue() (gas: 71657)
Router_ccipSend:test_NativeFeeTokenOverpay_Success() (gas: 175345)
Router_ccipSend:test_NativeFeeTokenZeroValue() (gas: 58759)
Router_ccipSend:test_NativeFeeToken_Success() (gas: 173861)
Router_ccipSend:test_NonLinkFeeToken_Success() (gas: 243736)
Router_ccipSend:test_UnsupportedDestinationChain_Revert() (gas: 24854)
Router_ccipSend:test_WhenNotHealthy_Revert() (gas: 44811)
Router_ccipSend:test_WrappedNativeFeeToken_Success() (gas: 176149)
Router_ccipSend:test_ZeroFeeAndGasPrice_Success() (gas: 246695)
Router_constructor:test_Constructor_Success() (gas: 13128)
Router_getArmProxy:test_getArmProxy() (gas: 10573)
Router_getFee:test_GetFeeSupportedChain_Success() (gas: 49153)
Router_getFee:test_UnsupportedDestinationChain_Revert() (gas: 17192)
Router_getSupportedTokens:test_GetSupportedTokens_Revert() (gas: 10532)
Router_recoverTokens:test_RecoverTokensInvalidRecipient_Revert() (gas: 11334)
Router_recoverTokens:test_RecoverTokensNoFunds_Revert() (gas: 20267)
Router_recoverTokens:test_RecoverTokensNonOwner_Revert() (gas: 11171)
Router_recoverTokens:test_RecoverTokensValueReceiver_Revert() (gas: 358049)
Router_recoverTokens:test_RecoverTokens_Success() (gas: 52508)
Router_routeMessage:test_AutoExec_Success() (gas: 42816)
Router_routeMessage:test_ExecutionEvent_Success() (gas: 158520)
Router_routeMessage:test_ManualExec_Success() (gas: 35546)
Router_routeMessage:test_OnlyOffRamp_Revert() (gas: 25224)
Router_routeMessage:test_WhenNotHealthy_Revert() (gas: 44799)
Router_setWrappedNative:test_OnlyOwner_Revert() (gas: 10998)
SelfFundedPingPong_ccipReceive:test_FundingIfNotANop_Revert() (gas: 55660)
SelfFundedPingPong_ccipReceive:test_Funding_Success() (gas: 428230)
SelfFundedPingPong_setCountIncrBeforeFunding:test_setCountIncrBeforeFunding() (gas: 20181)
TokenAdminRegistry_acceptAdminRole:test_acceptAdminRole_OnlyPendingAdministrator_Revert() (gas: 51163)
TokenAdminRegistry_acceptAdminRole:test_acceptAdminRole_Success() (gas: 44004)
TokenAdminRegistry_addRegistryModule:test_addRegistryModule_OnlyOwner_Revert() (gas: 12653)
TokenAdminRegistry_addRegistryModule:test_addRegistryModule_Success() (gas: 67056)
TokenAdminRegistry_getAllConfiguredTokens:test_getAllConfiguredTokens_outOfBounds_Success() (gas: 11362)
TokenAdminRegistry_getPool:test_getPool_Success() (gas: 17602)
TokenAdminRegistry_getPools:test_getPools_Success() (gas: 39962)
TokenAdminRegistry_isAdministrator:test_isAdministrator_Success() (gas: 106006)
TokenAdminRegistry_proposeAdministrator:test_proposeAdministrator_AlreadyRegistered_Revert() (gas: 104346)
TokenAdminRegistry_proposeAdministrator:test_proposeAdministrator_OnlyRegistryModule_Revert() (gas: 15610)
TokenAdminRegistry_proposeAdministrator:test_proposeAdministrator_ZeroAddress_Revert() (gas: 15155)
TokenAdminRegistry_proposeAdministrator:test_proposeAdministrator_module_Success() (gas: 112962)
TokenAdminRegistry_proposeAdministrator:test_proposeAdministrator_owner_Success() (gas: 107965)
TokenAdminRegistry_proposeAdministrator:test_proposeAdministrator_reRegisterWhileUnclaimed_Success() (gas: 116067)
TokenAdminRegistry_removeRegistryModule:test_removeRegistryModule_OnlyOwner_Revert() (gas: 12609)
TokenAdminRegistry_removeRegistryModule:test_removeRegistryModule_Success() (gas: 54524)
TokenAdminRegistry_setPool:test_setPool_InvalidTokenPoolToken_Revert() (gas: 19316)
TokenAdminRegistry_setPool:test_setPool_OnlyAdministrator_Revert() (gas: 18137)
TokenAdminRegistry_setPool:test_setPool_Success() (gas: 36135)
TokenAdminRegistry_setPool:test_setPool_ZeroAddressRemovesPool_Success() (gas: 30842)
TokenAdminRegistry_transferAdminRole:test_transferAdminRole_OnlyAdministrator_Revert() (gas: 18103)
TokenAdminRegistry_transferAdminRole:test_transferAdminRole_Success() (gas: 49438)
TokenPoolFactory_constructor:test_constructor_Revert() (gas: 1121620)
TokenPoolFactory_createTokenPool:test_createTokenPoolLockRelease_ExistingToken_predict_Success() (gas: 12535175)
TokenPoolFactory_createTokenPool:test_createTokenPool_BurnFromMintTokenPool_Success() (gas: 6414943)
TokenPoolFactory_createTokenPool:test_createTokenPool_ExistingRemoteToken_AndPredictPool_Success() (gas: 13284620)
TokenPoolFactory_createTokenPool:test_createTokenPool_RemoteTokenHasDifferentDecimals_Success() (gas: 13291989)
TokenPoolFactory_createTokenPool:test_createTokenPool_WithNoExistingRemoteContracts_predict_Success() (gas: 13622819)
TokenPoolFactory_createTokenPool:test_createTokenPool_WithNoExistingTokenOnRemoteChain_Success() (gas: 6201644)
TokenPoolFactory_createTokenPool:test_createTokenPool_WithRemoteTokenAndRemotePool_Success() (gas: 6411396)
TokenPoolWithAllowList_applyAllowListUpdates:test_AllowListNotEnabled_Revert() (gas: 2732579)
TokenPoolWithAllowList_applyAllowListUpdates:test_OnlyOwner_Revert() (gas: 12059)
TokenPoolWithAllowList_applyAllowListUpdates:test_SetAllowListSkipsZero_Success() (gas: 23512)
TokenPoolWithAllowList_applyAllowListUpdates:test_SetAllowList_Success() (gas: 177934)
TokenPoolWithAllowList_getAllowList:test_GetAllowList_Success() (gas: 23764)
TokenPoolWithAllowList_getAllowListEnabled:test_GetAllowListEnabled_Success() (gas: 8353)
TokenPoolWithAllowList_setRouter:test_SetRouter_Success() (gas: 24867)
TokenPoolWithAllowList_setRouter:test_ZeroAddressNotAllowed_Revert() (gas: 10696)
TokenPool_addRemotePool:test_NonExistentChain_Revert() (gas: 14222)
TokenPool_addRemotePool:test_PoolAlreadyAdded_Revert() (gas: 117183)
TokenPool_addRemotePool:test_ZeroLengthAddressNotAllowed_Revert() (gas: 14037)
TokenPool_addRemotePool:test_addRemotePool_MultipleActive() (gas: 472725)
TokenPool_addRemotePool:test_addRemotePool_Success() (gas: 157106)
TokenPool_applyChainUpdates:test_applyChainUpdates_InvalidRateLimitRate_Revert() (gas: 455399)
TokenPool_applyChainUpdates:test_applyChainUpdates_NonExistentChain_Revert() (gas: 15010)
TokenPool_applyChainUpdates:test_applyChainUpdates_OnlyCallableByOwner_Revert() (gas: 11819)
TokenPool_applyChainUpdates:test_applyChainUpdates_Success() (gas: 592007)
TokenPool_applyChainUpdates:test_applyChainUpdates_UpdatesRemotePoolHashes() (gas: 1077674)
TokenPool_applyChainUpdates:test_applyChainUpdates_ZeroAddressNotAllowed_Revert() (gas: 226422)
TokenPool_calculateLocalAmount:test_calculateLocalAmount() (gas: 94754)
TokenPool_calculateLocalAmount:test_calculateLocalAmount_RevertWhen_HighAmountOverflows() (gas: 2731145)
TokenPool_calculateLocalAmount:test_calculateLocalAmount_RevertWhen_HighLocalDecimalsOverflows() (gas: 2730754)
TokenPool_calculateLocalAmount:test_calculateLocalAmount_RevertWhen_HighRemoteDecimalsOverflows() (gas: 9558)
TokenPool_calculateLocalAmount:test_calculateLocalAmount_RevertWhen_LowRemoteDecimalsOverflows() (gas: 2730777)
TokenPool_constructor:test_constructor() (gas: 21909)
TokenPool_constructor:test_constructor_DecimalCallFails() (gas: 2728920)
TokenPool_constructor:test_constructor_RevertWhen_InvalidDecimalArgs() (gas: 77725)
TokenPool_constructor:test_constructor_RevertWhen_ZeroAddressNotAllowed() (gas: 71520)
TokenPool_getRemotePool:test_getRemotePools() (gas: 330178)
TokenPool_onlyOffRamp:test_CallerIsNotARampOnRouter_Revert() (gas: 21458)
TokenPool_onlyOffRamp:test_ChainNotAllowed_Revert() (gas: 240293)
TokenPool_onlyOffRamp:test_onlyOffRamp_Success() (gas: 94196)
TokenPool_onlyOnRamp:test_CallerIsNotARampOnRouter_Revert() (gas: 21155)
TokenPool_onlyOnRamp:test_ChainNotAllowed_Revert() (gas: 204261)
TokenPool_onlyOnRamp:test_onlyOnRamp_Success() (gas: 49198)
TokenPool_parseRemoteDecimals:test_parseRemoteDecimals() (gas: 13954)
TokenPool_parseRemoteDecimals:test_parseRemoteDecimals_NoDecimalsDefaultsToLocalDecimals() (gas: 9703)
TokenPool_parseRemoteDecimals:test_parseRemoteDecimals_RevertWhen_InvalidRemoteChainDecimals_DigitTooLarge() (gas: 10656)
TokenPool_parseRemoteDecimals:test_parseRemoteDecimals_RevertWhen_InvalidRemoteChainDecimals_WrongType() (gas: 11489)
TokenPool_removeRemotePool:test_InvalidRemotePoolForChain_Revert() (gas: 17488)
TokenPool_removeRemotePool:test_NonExistentChain_Revert() (gas: 14244)
TokenPool_removeRemotePool:test_removeRemotePool_Success() (gas: 187979)
TokenPool_setChainRateLimiterConfig:test_NonExistentChain_Revert() (gas: 17236)
TokenPool_setChainRateLimiterConfig:test_OnlyOwnerOrRateLimitAdmin_Revert() (gas: 15240)
TokenPool_setRateLimitAdmin:test_SetRateLimitAdmin_Revert() (gas: 10936)
TokenPool_setRateLimitAdmin:test_SetRateLimitAdmin_Success() (gas: 37540)
TokenProxy_ccipSend:test_CcipSendGasShouldBeZero_Revert() (gas: 17226)
TokenProxy_ccipSend:test_CcipSendInsufficientAllowance_Revert() (gas: 139114)
TokenProxy_ccipSend:test_CcipSendInvalidToken_Revert() (gas: 16000)
TokenProxy_ccipSend:test_CcipSendNative_Success() (gas: 245063)
TokenProxy_ccipSend:test_CcipSendNoDataAllowed_Revert() (gas: 16384)
TokenProxy_ccipSend:test_CcipSend_Success() (gas: 263889)
TokenProxy_constructor:test_Constructor() (gas: 13836)
TokenProxy_getFee:test_GetFeeGasShouldBeZero_Revert() (gas: 16899)
TokenProxy_getFee:test_GetFeeInvalidToken_Revert() (gas: 12706)
TokenProxy_getFee:test_GetFeeNoDataAllowed_Revert() (gas: 15885)
TokenProxy_getFee:test_GetFee_Success() (gas: 90200)
USDCBridgeMigrator_BurnLockedUSDC:test_PrimaryMechanism_Success() (gas: 135852)
USDCBridgeMigrator_BurnLockedUSDC:test_WhileMigrationPause_Revert() (gas: 109729)
USDCBridgeMigrator_BurnLockedUSDC:test_invalidPermissions_Revert() (gas: 39465)
USDCBridgeMigrator_BurnLockedUSDC:test_lockOrBurn_then_BurnInCCTPMigration_Success() (gas: 309798)
USDCBridgeMigrator_BurnLockedUSDC:test_onLockReleaseMechanism_Success() (gas: 146961)
USDCBridgeMigrator_BurnLockedUSDC:test_onLockReleaseMechanism_thenSwitchToPrimary_Success() (gas: 209091)
USDCBridgeMigrator_cancelMigrationProposal:test_cancelExistingCCTPMigrationProposal_Success() (gas: 56049)
USDCBridgeMigrator_cancelMigrationProposal:test_cannotCancelANonExistentMigrationProposal_Revert() (gas: 12636)
USDCBridgeMigrator_excludeTokensFromBurn:test_excludeTokensWhenNoMigrationProposalPending_Revert() (gas: 13513)
USDCBridgeMigrator_proposeMigration:test_ChainNotUsingLockRelease_Revert() (gas: 15699)
USDCBridgeMigrator_provideLiquidity:test_PrimaryMechanism_Success() (gas: 135852)
USDCBridgeMigrator_provideLiquidity:test_WhileMigrationPause_Revert() (gas: 109729)
USDCBridgeMigrator_provideLiquidity:test_cannotModifyLiquidityWithoutPermissions_Revert() (gas: 13362)
USDCBridgeMigrator_provideLiquidity:test_cannotProvideLiquidityWhenMigrationProposalPending_Revert() (gas: 67359)
USDCBridgeMigrator_provideLiquidity:test_cannotProvideLiquidity_AfterMigration_Revert() (gas: 313639)
USDCBridgeMigrator_provideLiquidity:test_invalidPermissions_Revert() (gas: 39444)
USDCBridgeMigrator_provideLiquidity:test_lockOrBurn_then_BurnInCCTPMigration_Success() (gas: 309816)
USDCBridgeMigrator_provideLiquidity:test_onLockReleaseMechanism_Success() (gas: 146983)
USDCBridgeMigrator_provideLiquidity:test_onLockReleaseMechanism_thenSwitchToPrimary_Success() (gas: 209108)
USDCBridgeMigrator_releaseOrMint:test_OnLockReleaseMechanism_Success() (gas: 213107)
USDCBridgeMigrator_releaseOrMint:test_WhileMigrationPause_Revert() (gas: 109606)
USDCBridgeMigrator_releaseOrMint:test_incomingMessageWithPrimaryMechanism() (gas: 265839)
USDCBridgeMigrator_releaseOrMint:test_unstickManualTxAfterMigration_destChain_Success() (gas: 150414)
USDCBridgeMigrator_releaseOrMint:test_unstickManualTxAfterMigration_homeChain_Success() (gas: 511552)
USDCBridgeMigrator_updateChainSelectorMechanism:test_PrimaryMechanism_Success() (gas: 135852)
USDCBridgeMigrator_updateChainSelectorMechanism:test_WhileMigrationPause_Revert() (gas: 109729)
USDCBridgeMigrator_updateChainSelectorMechanism:test_cannotRevertChainMechanism_afterMigration_Revert() (gas: 313218)
USDCBridgeMigrator_updateChainSelectorMechanism:test_invalidPermissions_Revert() (gas: 39465)
USDCBridgeMigrator_updateChainSelectorMechanism:test_lockOrBurn_then_BurnInCCTPMigration_Success() (gas: 309798)
USDCBridgeMigrator_updateChainSelectorMechanism:test_onLockReleaseMechanism_Success() (gas: 146961)
USDCBridgeMigrator_updateChainSelectorMechanism:test_onLockReleaseMechanism_thenSwitchToPrimary_Success() (gas: 209109)
USDCTokenPool__validateMessage:test_ValidateInvalidMessage_Revert() (gas: 25789)
USDCTokenPool_lockOrBurn:test_CallerIsNotARampOnRouter_Revert() (gas: 35274)
USDCTokenPool_lockOrBurn:test_LockOrBurnWithAllowList_Revert() (gas: 29875)
USDCTokenPool_lockOrBurn:test_LockOrBurn_Success() (gas: 133446)
USDCTokenPool_lockOrBurn:test_UnknownDomain_Revert() (gas: 433218)
USDCTokenPool_releaseOrMint:test_ReleaseOrMintRealTx_Success() (gas: 265601)
USDCTokenPool_releaseOrMint:test_TokenMaxCapacityExceeded_Revert() (gas: 47169)
USDCTokenPool_releaseOrMint:test_UnlockingUSDCFailed_Revert() (gas: 95194)
USDCTokenPool_setDomains:test_InvalidDomain_Revert() (gas: 66393)
USDCTokenPool_setDomains:test_OnlyOwner_Revert() (gas: 11270)
USDCTokenPool_supportsInterface:test_SupportsInterface_Success() (gas: 10041)