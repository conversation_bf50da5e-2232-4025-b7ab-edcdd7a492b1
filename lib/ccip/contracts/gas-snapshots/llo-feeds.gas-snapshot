ByteUtilTest:test_readAddress() (gas: 3388)
ByteUtilTest:test_readAddressMultiWord() (gas: 3386)
ByteUtilTest:test_readAddressWithEmptyArray() (gas: 3274)
ByteUtilTest:test_readAddressWithNotEnoughBytes() (gas: 3314)
ByteUtilTest:test_readUint192Max() (gas: 3326)
ByteUtilTest:test_readUint192Min() (gas: 3349)
ByteUtilTest:test_readUint192MultiWord() (gas: 3327)
ByteUtilTest:test_readUint192WithEmptyArray() (gas: 3274)
ByteUtilTest:test_readUint192WithNotEnoughBytes() (gas: 3314)
ByteUtilTest:test_readUint256Max() (gas: 3343)
ByteUtilTest:test_readUint256Min() (gas: 3387)
ByteUtilTest:test_readUint256MultiWord() (gas: 3341)
ByteUtilTest:test_readUint256WithEmptyArray() (gas: 3296)
ByteUtilTest:test_readUint256WithNotEnoughBytes() (gas: 3293)
ByteUtilTest:test_readUint32Max() (gas: 3348)
ByteUtilTest:test_readUint32Min() (gas: 3328)
ByteUtilTest:test_readUint32MultiWord() (gas: 3393)
ByteUtilTest:test_readUint32WithEmptyArray() (gas: 3253)
ByteUtilTest:test_readUint32WithNotEnoughBytes() (gas: 3272)
ByteUtilTest:test_readZeroAddress() (gas: 3365)
ChannelConfigStoreTest:testSetChannelDefinitions() (gas: 46927)
ChannelConfigStoreTest:testSupportsInterface() (gas: 8367)
ChannelConfigStoreTest:testTypeAndVersion() (gas: 9621)
DestinationFeeManagerProcessFeeTest:test_DiscountIsAppliedForNative() (gas: 52717)
DestinationFeeManagerProcessFeeTest:test_DiscountIsReturnedForNative() (gas: 52645)
DestinationFeeManagerProcessFeeTest:test_DiscountIsReturnedForNativeWithSurcharge() (gas: 78879)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountCantBeSetToMoreThanMaximum() (gas: 19544)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountIsOverridenByIndividualDiscountLink() (gas: 79509)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountIsOverridenByIndividualDiscountNative() (gas: 82523)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountIsUpdatedAfterBeingSetToZeroLink() (gas: 48409)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountIsUpdatedAfterBeingSetToZeroNative() (gas: 51589)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountWithLink() (gas: 51902)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountWithNative() (gas: 54892)
DestinationFeeManagerProcessFeeTest:test_GlobalDiscountWithNativeAndLink() (gas: 83739)
DestinationFeeManagerProcessFeeTest:test_V1PayloadVerifies() (gas: 29280)
DestinationFeeManagerProcessFeeTest:test_V1PayloadVerifiesAndReturnsChange() (gas: 61209)
DestinationFeeManagerProcessFeeTest:test_V2PayloadVerifies() (gas: 123469)
DestinationFeeManagerProcessFeeTest:test_V2PayloadWithoutQuoteFails() (gas: 29691)
DestinationFeeManagerProcessFeeTest:test_V2PayloadWithoutZeroFee() (gas: 77080)
DestinationFeeManagerProcessFeeTest:test_WithdrawERC20() (gas: 72819)
DestinationFeeManagerProcessFeeTest:test_WithdrawNonAdminAddr() (gas: 56357)
DestinationFeeManagerProcessFeeTest:test_WithdrawUnwrappedNative() (gas: 26434)
DestinationFeeManagerProcessFeeTest:test_addVerifier() (gas: 128899)
DestinationFeeManagerProcessFeeTest:test_addVerifierExistingAddress() (gas: 34192)
DestinationFeeManagerProcessFeeTest:test_baseFeeIsAppliedForLink() (gas: 19497)
DestinationFeeManagerProcessFeeTest:test_baseFeeIsAppliedForNative() (gas: 22502)
DestinationFeeManagerProcessFeeTest:test_correctDiscountIsAppliedWhenBothTokensAreDiscounted() (gas: 91133)
DestinationFeeManagerProcessFeeTest:test_discountAIsNotAppliedWhenSetForOtherUsers() (gas: 58864)
DestinationFeeManagerProcessFeeTest:test_discountFeeRoundsDownWhenUneven() (gas: 52919)
DestinationFeeManagerProcessFeeTest:test_discountIsAppliedForLink() (gas: 49730)
DestinationFeeManagerProcessFeeTest:test_discountIsAppliedWith100PercentSurcharge() (gas: 78908)
DestinationFeeManagerProcessFeeTest:test_discountIsNoLongerAppliedAfterRemoving() (gas: 48362)
DestinationFeeManagerProcessFeeTest:test_discountIsNotAppliedForInvalidTokenAddress() (gas: 17516)
DestinationFeeManagerProcessFeeTest:test_discountIsNotAppliedToOtherFeeds() (gas: 56912)
DestinationFeeManagerProcessFeeTest:test_discountIsReturnedForLink() (gas: 49657)
DestinationFeeManagerProcessFeeTest:test_emptyQuoteRevertsWithError() (gas: 12253)
DestinationFeeManagerProcessFeeTest:test_eventIsEmittedAfterSurchargeIsSet() (gas: 41379)
DestinationFeeManagerProcessFeeTest:test_eventIsEmittedIfNotEnoughLink() (gas: 182671)
DestinationFeeManagerProcessFeeTest:test_eventIsEmittedUponWithdraw() (gas: 69080)
DestinationFeeManagerProcessFeeTest:test_feeIsUpdatedAfterDiscountIsRemoved() (gas: 51626)
DestinationFeeManagerProcessFeeTest:test_feeIsUpdatedAfterNewDiscountIsApplied() (gas: 67777)
DestinationFeeManagerProcessFeeTest:test_feeIsUpdatedAfterNewSurchargeIsApplied() (gas: 66960)
DestinationFeeManagerProcessFeeTest:test_feeIsZeroWith100PercentDiscount() (gas: 52073)
DestinationFeeManagerProcessFeeTest:test_getBaseRewardWithLinkQuote() (gas: 19495)
DestinationFeeManagerProcessFeeTest:test_getLinkFeeIsRoundedUp() (gas: 49857)
DestinationFeeManagerProcessFeeTest:test_getLinkRewardIsSameAsFee() (gas: 55762)
DestinationFeeManagerProcessFeeTest:test_getLinkRewardWithNativeQuoteAndSurchargeWithLinkDiscount() (gas: 85050)
DestinationFeeManagerProcessFeeTest:test_getRewardWithLinkDiscount() (gas: 49726)
DestinationFeeManagerProcessFeeTest:test_getRewardWithLinkQuoteAndLinkDiscount() (gas: 49685)
DestinationFeeManagerProcessFeeTest:test_getRewardWithNativeQuote() (gas: 22456)
DestinationFeeManagerProcessFeeTest:test_getRewardWithNativeQuoteAndSurcharge() (gas: 53190)
DestinationFeeManagerProcessFeeTest:test_linkAvailableForPaymentReturnsLinkBalance() (gas: 53194)
DestinationFeeManagerProcessFeeTest:test_nativeSurcharge0Percent() (gas: 33198)
DestinationFeeManagerProcessFeeTest:test_nativeSurcharge100Percent() (gas: 53170)
DestinationFeeManagerProcessFeeTest:test_nativeSurchargeCannotExceed100Percent() (gas: 17152)
DestinationFeeManagerProcessFeeTest:test_nativeSurchargeEventIsEmittedOnUpdate() (gas: 41357)
DestinationFeeManagerProcessFeeTest:test_noFeeIsAppliedWhenReportHasZeroFee() (gas: 51918)
DestinationFeeManagerProcessFeeTest:test_noFeeIsAppliedWhenReportHasZeroFeeAndDiscountAndSurchargeIsSet() (gas: 78108)
DestinationFeeManagerProcessFeeTest:test_nonAdminProxyUserCannotProcessFee() (gas: 24141)
DestinationFeeManagerProcessFeeTest:test_nonAdminUserCanNotSetDiscount() (gas: 19784)
DestinationFeeManagerProcessFeeTest:test_onlyCallableByOwnerReverts() (gas: 15475)
DestinationFeeManagerProcessFeeTest:test_onlyOwnerCanSetGlobalDiscount() (gas: 19929)
DestinationFeeManagerProcessFeeTest:test_payLinkDeficit() (gas: 199884)
DestinationFeeManagerProcessFeeTest:test_payLinkDeficitOnlyCallableByAdmin() (gas: 17348)
DestinationFeeManagerProcessFeeTest:test_payLinkDeficitPaysAllFeesProcessed() (gas: 221262)
DestinationFeeManagerProcessFeeTest:test_payLinkDeficitTwice() (gas: 204204)
DestinationFeeManagerProcessFeeTest:test_poolIdsCannotBeZeroAddress() (gas: 117907)
DestinationFeeManagerProcessFeeTest:test_processFeeAsProxy() (gas: 123807)
DestinationFeeManagerProcessFeeTest:test_processFeeDefaultReportsStillVerifiesWithEmptyQuote() (gas: 29767)
DestinationFeeManagerProcessFeeTest:test_processFeeEmitsEventIfNotEnoughLink() (gas: 167721)
DestinationFeeManagerProcessFeeTest:test_processFeeIfSubscriberIsSelf() (gas: 32607)
DestinationFeeManagerProcessFeeTest:test_processFeeNative() (gas: 180514)
DestinationFeeManagerProcessFeeTest:test_processFeeUsesCorrectDigest() (gas: 125076)
DestinationFeeManagerProcessFeeTest:test_processFeeWithDefaultReportPayloadAndQuoteStillVerifies() (gas: 31844)
DestinationFeeManagerProcessFeeTest:test_processFeeWithDiscountEmitsEvent() (gas: 245978)
DestinationFeeManagerProcessFeeTest:test_processFeeWithInvalidReportVersionFailsToDecode() (gas: 30814)
DestinationFeeManagerProcessFeeTest:test_processFeeWithNoDiscountDoesNotEmitEvent() (gas: 173419)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNative() (gas: 188379)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeLinkAddress() (gas: 138180)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeLinkAddressExcessiveFee() (gas: 163791)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeShortFunds() (gas: 97147)
DestinationFeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeWithExcessiveFee() (gas: 195364)
DestinationFeeManagerProcessFeeTest:test_processFeeWithWithCorruptQuotePayload() (gas: 77390)
DestinationFeeManagerProcessFeeTest:test_processFeeWithWithEmptyQuotePayload() (gas: 30028)
DestinationFeeManagerProcessFeeTest:test_processFeeWithWithZeroQuotePayload() (gas: 30078)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroLinkNonZeroNativeWithLinkQuote() (gas: 37626)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroLinkNonZeroNativeWithNativeQuote() (gas: 160391)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkReturnsChange() (gas: 58387)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkWithLinkQuote() (gas: 123718)
DestinationFeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkWithNativeQuote() (gas: 40330)
DestinationFeeManagerProcessFeeTest:test_processMultipleLinkReports() (gas: 233880)
DestinationFeeManagerProcessFeeTest:test_processMultipleUnwrappedNativeReports() (gas: 267669)
DestinationFeeManagerProcessFeeTest:test_processMultipleV1Reports() (gas: 81177)
DestinationFeeManagerProcessFeeTest:test_processMultipleWrappedNativeReports() (gas: 250518)
DestinationFeeManagerProcessFeeTest:test_processPoolIdsPassedMismatched() (gas: 98815)
DestinationFeeManagerProcessFeeTest:test_processV1V2V3Reports() (gas: 218585)
DestinationFeeManagerProcessFeeTest:test_processV1V2V3ReportsWithUnwrapped() (gas: 260249)
DestinationFeeManagerProcessFeeTest:test_removeVerifierNonExistentAddress() (gas: 12822)
DestinationFeeManagerProcessFeeTest:test_removeVerifierZeroAaddress() (gas: 10700)
DestinationFeeManagerProcessFeeTest:test_reportWithNoExpiryOrFeeReturnsZero() (gas: 13682)
DestinationFeeManagerProcessFeeTest:test_revertOnSettingAnAddressZeroVerifier() (gas: 10636)
DestinationFeeManagerProcessFeeTest:test_rewardsAreCorrectlySentToEachAssociatedPoolWhenVerifyingInBulk() (gas: 266627)
DestinationFeeManagerProcessFeeTest:test_setDiscountOver100Percent() (gas: 19540)
DestinationFeeManagerProcessFeeTest:test_setRewardManagerZeroAddress() (gas: 10626)
DestinationFeeManagerProcessFeeTest:test_subscriberDiscountEventIsEmittedOnUpdate() (gas: 46285)
DestinationFeeManagerProcessFeeTest:test_surchargeFeeRoundsUpWhenUneven() (gas: 53501)
DestinationFeeManagerProcessFeeTest:test_surchargeIsApplied() (gas: 53426)
DestinationFeeManagerProcessFeeTest:test_surchargeIsAppliedForNativeFeeWithDiscount() (gas: 79315)
DestinationFeeManagerProcessFeeTest:test_surchargeIsNoLongerAppliedAfterRemoving() (gas: 49149)
DestinationFeeManagerProcessFeeTest:test_surchargeIsNotAppliedForLinkFee() (gas: 52223)
DestinationFeeManagerProcessFeeTest:test_surchargeIsNotAppliedWith100PercentDiscount() (gas: 78311)
DestinationFeeManagerProcessFeeTest:test_testRevertIfReportHasExpired() (gas: 14987)
DestinationRewardManagerClaimTest:test_claimAllRecipients() (gas: 277223)
DestinationRewardManagerClaimTest:test_claimMultipleRecipients() (gas: 154387)
DestinationRewardManagerClaimTest:test_claimRewardsWithDuplicatePoolIdsDoesNotPayoutTwice() (gas: 330244)
DestinationRewardManagerClaimTest:test_claimSingleRecipient() (gas: 89047)
DestinationRewardManagerClaimTest:test_claimUnevenAmountRoundsDown() (gas: 315447)
DestinationRewardManagerClaimTest:test_claimUnregisteredPoolId() (gas: 35168)
DestinationRewardManagerClaimTest:test_claimUnregisteredRecipient() (gas: 41205)
DestinationRewardManagerClaimTest:test_eventIsEmittedUponClaim() (gas: 86092)
DestinationRewardManagerClaimTest:test_eventIsNotEmittedUponUnsuccessfulClaim() (gas: 25054)
DestinationRewardManagerClaimTest:test_recipientsClaimMultipleDeposits() (gas: 386925)
DestinationRewardManagerClaimTest:test_singleRecipientClaimMultipleDeposits() (gas: 137797)
DestinationRewardManagerNoRecipientSet:test_claimAllRecipientsAfterRecipientsSet() (gas: 494460)
DestinationRewardManagerPayRecipientsTest:test_addFundsToPoolAsNonOwnerOrFeeManager() (gas: 11503)
DestinationRewardManagerPayRecipientsTest:test_addFundsToPoolAsOwner() (gas: 53947)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipients() (gas: 253082)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipientsFromNonAdminUser() (gas: 20472)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipientsFromRecipientInPool() (gas: 248964)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalInvalidRecipient() (gas: 264532)
DestinationRewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalUnregisteredRecipient() (gas: 268017)
DestinationRewardManagerPayRecipientsTest:test_payRecipientWithInvalidPool() (gas: 31133)
DestinationRewardManagerPayRecipientsTest:test_payRecipientsEmptyRecipientList() (gas: 27554)
DestinationRewardManagerPayRecipientsTest:test_payRecipientsWithInvalidPoolId() (gas: 33639)
DestinationRewardManagerPayRecipientsTest:test_paySingleRecipient() (gas: 86938)
DestinationRewardManagerPayRecipientsTest:test_paySubsetOfRecipientsInPool() (gas: 200719)
DestinationRewardManagerRecipientClaimDifferentWeightsTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 280885)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsMultiplePools() (gas: 512553)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsSinglePool() (gas: 283681)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimEmptyPoolWhenSecondPoolContainsFunds() (gas: 293533)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsMultiplePools() (gas: 263107)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsSinglePool() (gas: 154553)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleRecipientMultiplePools() (gas: 132669)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleUniqueRecipient() (gas: 106068)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimUnevenAmountRoundsDown() (gas: 579848)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_claimUnregisteredRecipient() (gas: 64672)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorAndTotalPoolsEqual() (gas: 13074)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorCannotBeGreaterThanTotalPools() (gas: 12703)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorSingleResult() (gas: 22471)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPools() (gas: 32248)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPoolsWhereAlreadyClaimed() (gas: 148645)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInNoPools() (gas: 21728)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInSinglePool() (gas: 27765)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_recipientsClaimMultipleDeposits() (gas: 391495)
DestinationRewardManagerRecipientClaimMultiplePoolsTest:test_singleRecipientClaimMultipleDeposits() (gas: 137882)
DestinationRewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 199566)
DestinationRewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmountWithSmallDeposit() (gas: 219439)
DestinationRewardManagerSetRecipientsTest:test_eventIsEmittedUponSetRecipients() (gas: 193892)
DestinationRewardManagerSetRecipientsTest:test_setRecipientContainsDuplicateRecipients() (gas: 128245)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientFromManagerAddress() (gas: 213998)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientFromNonOwnerOrFeeManagerAddress() (gas: 21496)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientTwice() (gas: 195650)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientWeights() (gas: 182793)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroAddress() (gas: 92387)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroWeight() (gas: 193497)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipients() (gas: 187752)
DestinationRewardManagerSetRecipientsTest:test_setRewardRecipientsIsEmpty() (gas: 89276)
DestinationRewardManagerSetRecipientsTest:test_setSingleRewardRecipient() (gas: 112534)
DestinationRewardManagerSetupTest:test_addFeeManagerExistingAddress() (gas: 35281)
DestinationRewardManagerSetupTest:test_addFeeManagerZeroAddress() (gas: 10580)
DestinationRewardManagerSetupTest:test_addRemoveFeeManager() (gas: 48248)
DestinationRewardManagerSetupTest:test_eventEmittedUponFeeManagerUpdate() (gas: 41581)
DestinationRewardManagerSetupTest:test_eventEmittedUponFeePaid() (gas: 261361)
DestinationRewardManagerSetupTest:test_rejectsZeroLinkAddressOnConstruction() (gas: 59481)
DestinationRewardManagerSetupTest:test_removeFeeManagerNonExistentAddress() (gas: 12778)
DestinationRewardManagerSetupTest:test_setFeeManagerZeroAddress() (gas: 17084)
DestinationRewardManagerUpdateRewardRecipientsMultiplePoolsTest:test_updatePrimaryRecipientWeights() (gas: 376742)
DestinationRewardManagerUpdateRewardRecipientsTest:test_eventIsEmittedUponUpdateRecipients() (gas: 280443)
DestinationRewardManagerUpdateRewardRecipientsTest:test_onlyAdminCanUpdateRecipients() (gas: 19705)
DestinationRewardManagerUpdateRewardRecipientsTest:test_partialUpdateRecipientWeights() (gas: 221040)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateAllRecipientsWithSameAddressAndWeight() (gas: 274265)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsToSubset() (gas: 254188)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithExcessiveWeight() (gas: 259175)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithSameAddressAndWeight() (gas: 149872)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithUnderWeightSet() (gas: 259249)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientWeights() (gas: 372223)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientWithNewZeroAddress() (gas: 270736)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsContainsDuplicateRecipients() (gas: 288531)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentLargerSet() (gas: 407832)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentPartialSet() (gas: 317985)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSet() (gas: 377740)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSetWithInvalidWeights() (gas: 312078)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForLargerSet() (gas: 399655)
DestinationRewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForSmallerSet() (gas: 289469)
DestinationVerifierBulkVerifyBillingReport:test_verifyWithBulkLink() (gas: 642599)
DestinationVerifierBulkVerifyBillingReport:test_verifyWithBulkNative() (gas: 643674)
DestinationVerifierBulkVerifyBillingReport:test_verifyWithBulkNativeUnwrapped() (gas: 665238)
DestinationVerifierBulkVerifyBillingReport:test_verifyWithBulkNativeUnwrappedReturnsChange() (gas: 665193)
DestinationVerifierConstructorTest:test_falseIfIsNotCorrectInterface() (gas: 8481)
DestinationVerifierConstructorTest:test_revertsIfInitializedWithEmptyVerifierProxy() (gas: 60885)
DestinationVerifierConstructorTest:test_trueIfIsCorrectInterface() (gas: 9383)
DestinationVerifierConstructorTest:test_typeAndVersion() (gas: 2624729)
DestinationVerifierProxyInitializeVerifierTest:test_correctlySetsTheOwner() (gas: 862226)
DestinationVerifierProxyInitializeVerifierTest:test_correctlySetsVersion() (gas: 9841)
DestinationVerifierProxyInitializeVerifierTest:test_setVerifierCalledByNoOwner() (gas: 17483)
DestinationVerifierProxyInitializeVerifierTest:test_setVerifierOk() (gas: 27727)
DestinationVerifierProxyInitializeVerifierTest:test_setVerifierWhichDoesntHonourInterface() (gas: 16535)
DestinationVerifierSetAccessControllerTest:test_emitsTheCorrectEvent() (gas: 35391)
DestinationVerifierSetAccessControllerTest:test_revertsIfCalledByNonOwner() (gas: 15089)
DestinationVerifierSetAccessControllerTest:test_successfullySetsNewAccessController() (gas: 34885)
DestinationVerifierSetAccessControllerTest:test_successfullySetsNewAccessControllerIsEmpty() (gas: 15007)
DestinationVerifierSetConfigTest:test_NoDonConfigAlreadyExists() (gas: 2877761)
DestinationVerifierSetConfigTest:test_addressesAndWeightsDoNotProduceSideEffectsInDonConfigIds() (gas: 1323254)
DestinationVerifierSetConfigTest:test_donConfigIdIsSameForSignersInDifferentOrder() (gas: 1290451)
DestinationVerifierSetConfigTest:test_removeLatestConfig() (gas: 786161)
DestinationVerifierSetConfigTest:test_removeLatestConfigWhenNoConfigShouldFail() (gas: 12870)
DestinationVerifierSetConfigTest:test_revertsIfCalledByNonOwner() (gas: 174936)
DestinationVerifierSetConfigTest:test_revertsIfDuplicateSigners() (gas: 171299)
DestinationVerifierSetConfigTest:test_revertsIfFaultToleranceIsZero() (gas: 168506)
DestinationVerifierSetConfigTest:test_revertsIfNotEnoughSigners() (gas: 11571)
DestinationVerifierSetConfigTest:test_revertsIfSetWithTooManySigners() (gas: 17943)
DestinationVerifierSetConfigTest:test_revertsIfSignerContainsZeroAddress() (gas: 324006)
DestinationVerifierSetConfigTest:test_setConfigActiveUnknownDonConfigId() (gas: 13124)
DestinationVerifierSetConfigTest:test_setConfigWithActivationTime() (gas: 1088159)
DestinationVerifierSetConfigTest:test_setConfigWithActivationTimeEarlierThanLatestConfigShouldFail() (gas: 1963073)
DestinationVerifierSetConfigTest:test_setConfigWithActivationTimeNoFutureTimeShouldFail() (gas: 259470)
DestinationVerifierSetConfigTest:test_setConfigWithActivationTimeTheSameAsLatestConfigShouldFail() (gas: 1283783)
FeeManagerProcessFeeTest:test_DiscountIsAppliedForNative() (gas: 52645)
FeeManagerProcessFeeTest:test_DiscountIsReturnedForNative() (gas: 52595)
FeeManagerProcessFeeTest:test_DiscountIsReturnedForNativeWithSurcharge() (gas: 78808)
FeeManagerProcessFeeTest:test_V1PayloadVerifies() (gas: 26974)
FeeManagerProcessFeeTest:test_V1PayloadVerifiesAndReturnsChange() (gas: 58904)
FeeManagerProcessFeeTest:test_V2PayloadVerifies() (gas: 116750)
FeeManagerProcessFeeTest:test_V2PayloadWithoutQuoteFails() (gas: 27389)
FeeManagerProcessFeeTest:test_V2PayloadWithoutZeroFee() (gas: 70364)
FeeManagerProcessFeeTest:test_WithdrawERC20() (gas: 72682)
FeeManagerProcessFeeTest:test_WithdrawNonAdminAddr() (gas: 56286)
FeeManagerProcessFeeTest:test_WithdrawUnwrappedNative() (gas: 26387)
FeeManagerProcessFeeTest:test_baseFeeIsAppliedForLink() (gas: 17190)
FeeManagerProcessFeeTest:test_baseFeeIsAppliedForNative() (gas: 20128)
FeeManagerProcessFeeTest:test_correctDiscountIsAppliedWhenBothTokensAreDiscounted() (gas: 91011)
FeeManagerProcessFeeTest:test_discountAIsNotAppliedWhenSetForOtherUsers() (gas: 56534)
FeeManagerProcessFeeTest:test_discountFeeRoundsDownWhenUneven() (gas: 52847)
FeeManagerProcessFeeTest:test_discountIsAppliedForLink() (gas: 49636)
FeeManagerProcessFeeTest:test_discountIsAppliedWith100PercentSurcharge() (gas: 78903)
FeeManagerProcessFeeTest:test_discountIsNoLongerAppliedAfterRemoving() (gas: 46511)
FeeManagerProcessFeeTest:test_discountIsNotAppliedForInvalidTokenAddress() (gas: 17560)
FeeManagerProcessFeeTest:test_discountIsNotAppliedToOtherFeeds() (gas: 54604)
FeeManagerProcessFeeTest:test_discountIsReturnedForLink() (gas: 49608)
FeeManagerProcessFeeTest:test_emptyQuoteRevertsWithError() (gas: 12163)
FeeManagerProcessFeeTest:test_eventIsEmittedAfterSurchargeIsSet() (gas: 41356)
FeeManagerProcessFeeTest:test_eventIsEmittedIfNotEnoughLink() (gas: 173756)
FeeManagerProcessFeeTest:test_eventIsEmittedUponWithdraw() (gas: 69009)
FeeManagerProcessFeeTest:test_feeIsUpdatedAfterDiscountIsRemoved() (gas: 49757)
FeeManagerProcessFeeTest:test_feeIsUpdatedAfterNewDiscountIsApplied() (gas: 67699)
FeeManagerProcessFeeTest:test_feeIsUpdatedAfterNewSurchargeIsApplied() (gas: 64368)
FeeManagerProcessFeeTest:test_feeIsZeroWith100PercentDiscount() (gas: 52045)
FeeManagerProcessFeeTest:test_getBaseRewardWithLinkQuote() (gas: 17207)
FeeManagerProcessFeeTest:test_getLinkFeeIsRoundedUp() (gas: 49829)
FeeManagerProcessFeeTest:test_getLinkRewardIsSameAsFee() (gas: 55641)
FeeManagerProcessFeeTest:test_getLinkRewardWithNativeQuoteAndSurchargeWithLinkDiscount() (gas: 82765)
FeeManagerProcessFeeTest:test_getRewardWithLinkDiscount() (gas: 49654)
FeeManagerProcessFeeTest:test_getRewardWithLinkQuoteAndLinkDiscount() (gas: 49657)
FeeManagerProcessFeeTest:test_getRewardWithNativeQuote() (gas: 20148)
FeeManagerProcessFeeTest:test_getRewardWithNativeQuoteAndSurcharge() (gas: 50838)
FeeManagerProcessFeeTest:test_linkAvailableForPaymentReturnsLinkBalance() (gas: 53192)
FeeManagerProcessFeeTest:test_nativeSurcharge0Percent() (gas: 30848)
FeeManagerProcessFeeTest:test_nativeSurcharge100Percent() (gas: 50863)
FeeManagerProcessFeeTest:test_nativeSurchargeCannotExceed100Percent() (gas: 17175)
FeeManagerProcessFeeTest:test_nativeSurchargeEventIsEmittedOnUpdate() (gas: 41402)
FeeManagerProcessFeeTest:test_noFeeIsAppliedWhenReportHasZeroFee() (gas: 51868)
FeeManagerProcessFeeTest:test_noFeeIsAppliedWhenReportHasZeroFeeAndDiscountAndSurchargeIsSet() (gas: 78104)
FeeManagerProcessFeeTest:test_nonAdminProxyUserCannotProcessFee() (gas: 21895)
FeeManagerProcessFeeTest:test_nonAdminUserCanNotSetDiscount() (gas: 19849)
FeeManagerProcessFeeTest:test_payLinkDeficit() (gas: 194429)
FeeManagerProcessFeeTest:test_payLinkDeficitOnlyCallableByAdmin() (gas: 17413)
FeeManagerProcessFeeTest:test_payLinkDeficitPaysAllFeesProcessed() (gas: 214755)
FeeManagerProcessFeeTest:test_payLinkDeficitTwice() (gas: 198803)
FeeManagerProcessFeeTest:test_processFeeAsProxy() (gas: 117088)
FeeManagerProcessFeeTest:test_processFeeDefaultReportsStillVerifiesWithEmptyQuote() (gas: 27462)
FeeManagerProcessFeeTest:test_processFeeEmitsEventIfNotEnoughLink() (gas: 163205)
FeeManagerProcessFeeTest:test_processFeeIfSubscriberIsSelf() (gas: 30327)
FeeManagerProcessFeeTest:test_processFeeNative() (gas: 173826)
FeeManagerProcessFeeTest:test_processFeeUsesCorrectDigest() (gas: 118379)
FeeManagerProcessFeeTest:test_processFeeWithDefaultReportPayloadAndQuoteStillVerifies() (gas: 29536)
FeeManagerProcessFeeTest:test_processFeeWithDiscountEmitsEvent() (gas: 241353)
FeeManagerProcessFeeTest:test_processFeeWithInvalidReportVersionFailsToDecode() (gas: 28511)
FeeManagerProcessFeeTest:test_processFeeWithNoDiscountDoesNotEmitEvent() (gas: 166753)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNative() (gas: 181691)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeLinkAddress() (gas: 131466)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeLinkAddressExcessiveFee() (gas: 157072)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeShortFunds() (gas: 92635)
FeeManagerProcessFeeTest:test_processFeeWithUnwrappedNativeWithExcessiveFee() (gas: 188654)
FeeManagerProcessFeeTest:test_processFeeWithWithCorruptQuotePayload() (gas: 70675)
FeeManagerProcessFeeTest:test_processFeeWithWithEmptyQuotePayload() (gas: 27727)
FeeManagerProcessFeeTest:test_processFeeWithWithZeroQuotePayload() (gas: 27777)
FeeManagerProcessFeeTest:test_processFeeWithZeroLinkNonZeroNativeWithLinkQuote() (gas: 32967)
FeeManagerProcessFeeTest:test_processFeeWithZeroLinkNonZeroNativeWithNativeQuote() (gas: 153725)
FeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkReturnsChange() (gas: 53795)
FeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkWithLinkQuote() (gas: 116999)
FeeManagerProcessFeeTest:test_processFeeWithZeroNativeNonZeroLinkWithNativeQuote() (gas: 35738)
FeeManagerProcessFeeTest:test_processMultipleLinkReports() (gas: 223133)
FeeManagerProcessFeeTest:test_processMultipleUnwrappedNativeReports() (gas: 256996)
FeeManagerProcessFeeTest:test_processMultipleV1Reports() (gas: 74793)
FeeManagerProcessFeeTest:test_processMultipleWrappedNativeReports() (gas: 239801)
FeeManagerProcessFeeTest:test_processV1V2V3Reports() (gas: 207915)
FeeManagerProcessFeeTest:test_processV1V2V3ReportsWithUnwrapped() (gas: 249580)
FeeManagerProcessFeeTest:test_reportWithNoExpiryOrFeeReturnsZero() (gas: 13613)
FeeManagerProcessFeeTest:test_setDiscountOver100Percent() (gas: 19562)
FeeManagerProcessFeeTest:test_subscriberDiscountEventIsEmittedOnUpdate() (gas: 46261)
FeeManagerProcessFeeTest:test_surchargeFeeRoundsUpWhenUneven() (gas: 51215)
FeeManagerProcessFeeTest:test_surchargeIsApplied() (gas: 51096)
FeeManagerProcessFeeTest:test_surchargeIsAppliedForNativeFeeWithDiscount() (gas: 79265)
FeeManagerProcessFeeTest:test_surchargeIsNoLongerAppliedAfterRemoving() (gas: 47076)
FeeManagerProcessFeeTest:test_surchargeIsNotAppliedForLinkFee() (gas: 49938)
FeeManagerProcessFeeTest:test_surchargeIsNotAppliedWith100PercentDiscount() (gas: 78261)
FeeManagerProcessFeeTest:test_testRevertIfReportHasExpired() (gas: 14919)
MultiVerifierBillingTests:test_multipleFeeManagersAndVerifiers() (gas: 4598487)
RewardManagerClaimTest:test_claimAllRecipients() (gas: 277131)
RewardManagerClaimTest:test_claimMultipleRecipients() (gas: 154341)
RewardManagerClaimTest:test_claimRewardsWithDuplicatePoolIdsDoesNotPayoutTwice() (gas: 330086)
RewardManagerClaimTest:test_claimSingleRecipient() (gas: 89024)
RewardManagerClaimTest:test_claimUnevenAmountRoundsDown() (gas: 315289)
RewardManagerClaimTest:test_claimUnregisteredPoolId() (gas: 35145)
RewardManagerClaimTest:test_claimUnregisteredRecipient() (gas: 41182)
RewardManagerClaimTest:test_eventIsEmittedUponClaim() (gas: 86069)
RewardManagerClaimTest:test_eventIsNotEmittedUponUnsuccessfulClaim() (gas: 25031)
RewardManagerClaimTest:test_recipientsClaimMultipleDeposits() (gas: 386675)
RewardManagerClaimTest:test_singleRecipientClaimMultipleDeposits() (gas: 137685)
RewardManagerNoRecipientSet:test_claimAllRecipientsAfterRecipientsSet() (gas: 492113)
RewardManagerPayRecipientsTest:test_addFundsToPoolAsNonOwnerOrFeeManager() (gas: 11437)
RewardManagerPayRecipientsTest:test_addFundsToPoolAsOwner() (gas: 53894)
RewardManagerPayRecipientsTest:test_payAllRecipients() (gas: 250840)
RewardManagerPayRecipientsTest:test_payAllRecipientsFromNonAdminUser() (gas: 20475)
RewardManagerPayRecipientsTest:test_payAllRecipientsFromRecipientInPool() (gas: 251086)
RewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalInvalidRecipient() (gas: 262290)
RewardManagerPayRecipientsTest:test_payAllRecipientsWithAdditionalUnregisteredRecipient() (gas: 265775)
RewardManagerPayRecipientsTest:test_payRecipientWithInvalidPool() (gas: 28891)
RewardManagerPayRecipientsTest:test_payRecipientsEmptyRecipientList() (gas: 25312)
RewardManagerPayRecipientsTest:test_payRecipientsWithInvalidPoolId() (gas: 31397)
RewardManagerPayRecipientsTest:test_paySingleRecipient() (gas: 84696)
RewardManagerPayRecipientsTest:test_paySubsetOfRecipientsInPool() (gas: 198477)
RewardManagerRecipientClaimDifferentWeightsTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 280793)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsMultiplePools() (gas: 512369)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimAllRecipientsSinglePool() (gas: 283589)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimEmptyPoolWhenSecondPoolContainsFunds() (gas: 293418)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsMultiplePools() (gas: 263015)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimMultipleRecipientsSinglePool() (gas: 154507)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleRecipientMultiplePools() (gas: 132623)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimSingleUniqueRecipient() (gas: 106022)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimUnevenAmountRoundsDown() (gas: 579532)
RewardManagerRecipientClaimMultiplePoolsTest:test_claimUnregisteredRecipient() (gas: 64626)
RewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorAndTotalPoolsEqual() (gas: 13051)
RewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorCannotBeGreaterThanTotalPools() (gas: 12680)
RewardManagerRecipientClaimMultiplePoolsTest:test_getAvailableRewardsCursorSingleResult() (gas: 22448)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPools() (gas: 32225)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInBothPoolsWhereAlreadyClaimed() (gas: 148553)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInNoPools() (gas: 21705)
RewardManagerRecipientClaimMultiplePoolsTest:test_getRewardsAvailableToRecipientInSinglePool() (gas: 27742)
RewardManagerRecipientClaimMultiplePoolsTest:test_recipientsClaimMultipleDeposits() (gas: 391245)
RewardManagerRecipientClaimMultiplePoolsTest:test_singleRecipientClaimMultipleDeposits() (gas: 137770)
RewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmount() (gas: 199454)
RewardManagerRecipientClaimUnevenWeightTest:test_allRecipientsClaimingReceiveExpectedAmountWithSmallDeposit() (gas: 219327)
RewardManagerSetRecipientsTest:test_eventIsEmittedUponSetRecipients() (gas: 191729)
RewardManagerSetRecipientsTest:test_setRecipientContainsDuplicateRecipients() (gas: 126082)
RewardManagerSetRecipientsTest:test_setRewardRecipientFromManagerAddress() (gas: 193880)
RewardManagerSetRecipientsTest:test_setRewardRecipientFromNonOwnerOrFeeManagerAddress() (gas: 21452)
RewardManagerSetRecipientsTest:test_setRewardRecipientTwice() (gas: 193324)
RewardManagerSetRecipientsTest:test_setRewardRecipientWeights() (gas: 180630)
RewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroAddress() (gas: 90224)
RewardManagerSetRecipientsTest:test_setRewardRecipientWithZeroWeight() (gas: 191334)
RewardManagerSetRecipientsTest:test_setRewardRecipients() (gas: 185589)
RewardManagerSetRecipientsTest:test_setRewardRecipientsIsEmpty() (gas: 87113)
RewardManagerSetRecipientsTest:test_setSingleRewardRecipient() (gas: 110371)
RewardManagerSetupTest:test_eventEmittedUponFeeManagerUpdate() (gas: 21388)
RewardManagerSetupTest:test_eventEmittedUponFeePaid() (gas: 259132)
RewardManagerSetupTest:test_rejectsZeroLinkAddressOnConstruction() (gas: 59411)
RewardManagerSetupTest:test_setFeeManagerZeroAddress() (gas: 17038)
RewardManagerUpdateRewardRecipientsMultiplePoolsTest:test_updatePrimaryRecipientWeights() (gas: 376628)
RewardManagerUpdateRewardRecipientsTest:test_eventIsEmittedUponUpdateRecipients() (gas: 280487)
RewardManagerUpdateRewardRecipientsTest:test_onlyAdminCanUpdateRecipients() (gas: 19749)
RewardManagerUpdateRewardRecipientsTest:test_partialUpdateRecipientWeights() (gas: 220972)
RewardManagerUpdateRewardRecipientsTest:test_updateAllRecipientsWithSameAddressAndWeight() (gas: 274309)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsToSubset() (gas: 254232)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithExcessiveWeight() (gas: 259219)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithSameAddressAndWeight() (gas: 149916)
RewardManagerUpdateRewardRecipientsTest:test_updatePartialRecipientsWithUnderWeightSet() (gas: 259293)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientWeights() (gas: 372109)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientWithNewZeroAddress() (gas: 270780)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsContainsDuplicateRecipients() (gas: 288575)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentLargerSet() (gas: 407876)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentPartialSet() (gas: 318029)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSet() (gas: 377784)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsToDifferentSetWithInvalidWeights() (gas: 312122)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForLargerSet() (gas: 399699)
RewardManagerUpdateRewardRecipientsTest:test_updateRecipientsUpdateAndRemoveExistingForSmallerSet() (gas: 289513)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_correctlyRemovesAMiddleDigest() (gas: 27017)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_correctlyRemovesTheFirstDigest() (gas: 26984)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_correctlyUnsetsDigestsInSequence() (gas: 45102)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_revertsIfCalledByNonOwner() (gas: 15016)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_revertsIfRemovingAnEmptyDigest() (gas: 10907)
VerificationdeactivateConfigWhenThereAreMultipleDigestsTest:test_revertsIfRemovingAnNonExistentDigest() (gas: 13381)
VerifierActivateConfigTest:test_revertsIfDigestIsEmpty() (gas: 10984)
VerifierActivateConfigTest:test_revertsIfDigestNotSet() (gas: 13394)
VerifierActivateConfigTest:test_revertsIfNotOwner() (gas: 17182)
VerifierActivateConfigWithDeactivatedConfigTest:test_allowsVerification() (gas: 97175)
VerifierActivateFeedTest:test_revertsIfNoFeedExistsActivate() (gas: 13179)
VerifierActivateFeedTest:test_revertsIfNoFeedExistsDeactivate() (gas: 13157)
VerifierActivateFeedTest:test_revertsIfNotOwnerActivateFeed() (gas: 17109)
VerifierActivateFeedTest:test_revertsIfNotOwnerDeactivateFeed() (gas: 17164)
VerifierBillingTests:test_rewardsAreDistributedAccordingToWeights() (gas: 1736216)
VerifierBillingTests:test_rewardsAreDistributedAccordingToWeightsMultipleWeigths() (gas: 4468029)
VerifierBillingTests:test_rewardsAreDistributedAccordingToWeightsUsingHistoricalConfigs() (gas: 2106504)
VerifierBillingTests:test_verifyWithLinkV3Report() (gas: 1593617)
VerifierBillingTests:test_verifyWithNativeERC20() (gas: 1467526)
VerifierBillingTests:test_verifyWithNativeUnwrapped() (gas: 1378718)
VerifierBillingTests:test_verifyWithNativeUnwrappedReturnsChange() (gas: 1385764)
VerifierBulkVerifyBillingReport:test_verifyMultiVersions() (gas: 476595)
VerifierBulkVerifyBillingReport:test_verifyMultiVersionsReturnsVerifiedReports() (gas: 474853)
VerifierBulkVerifyBillingReport:test_verifyWithBulkLink() (gas: 557541)
VerifierBulkVerifyBillingReport:test_verifyWithBulkNative() (gas: 560806)
VerifierBulkVerifyBillingReport:test_verifyWithBulkNativeUnwrapped() (gas: 568629)
VerifierBulkVerifyBillingReport:test_verifyWithBulkNativeUnwrappedReturnsChange() (gas: 575635)
VerifierConstructorTest:test_revertsIfInitializedWithEmptyVerifierProxy() (gas: 59960)
VerifierConstructorTest:test_setsTheCorrectProperties() (gas: 1813269)
VerifierDeactivateFeedWithVerifyTest:test_currentReportAllowsVerification() (gas: 192073)
VerifierDeactivateFeedWithVerifyTest:test_currentReportFailsVerification() (gas: 113388)
VerifierDeactivateFeedWithVerifyTest:test_previousReportAllowsVerification() (gas: 99624)
VerifierDeactivateFeedWithVerifyTest:test_previousReportFailsVerification() (gas: 69943)
VerifierInterfacesTest:test_DestinationContractInterfaces() (gas: 628127)
VerifierProxyAccessControlledVerificationTest:test_proxiesToTheVerifierIfHasAccess() (gas: 208529)
VerifierProxyAccessControlledVerificationTest:test_revertsIfNoAccess() (gas: 112345)
VerifierProxyConstructorTest:test_correctlySetsTheCorrectAccessControllerInterface() (gas: 1485359)
VerifierProxyConstructorTest:test_correctlySetsTheOwner() (gas: 1465483)
VerifierProxyConstructorTest:test_correctlySetsVersion() (gas: 9701)
VerifierProxyInitializeVerifierTest:test_revertsIfDigestAlreadySet() (gas: 54133)
VerifierProxyInitializeVerifierTest:test_revertsIfNotCorrectVerifier() (gas: 13613)
VerifierProxyInitializeVerifierTest:test_revertsIfNotOwner() (gas: 17168)
VerifierProxyInitializeVerifierTest:test_revertsIfVerifierAlreadyInitialized() (gas: 42047)
VerifierProxyInitializeVerifierTest:test_revertsIfZeroAddress() (gas: 10956)
VerifierProxyInitializeVerifierTest:test_setFeeManagerWhichDoesntHonourIERC165Interface() (gas: 13823)
VerifierProxyInitializeVerifierTest:test_setFeeManagerWhichDoesntHonourInterface() (gas: 16290)
VerifierProxyInitializeVerifierTest:test_setFeeManagerZeroAddress() (gas: 10933)
VerifierProxyInitializeVerifierTest:test_updatesVerifierIfVerifier() (gas: 54086)
VerifierProxySetAccessControllerTest:test_emitsTheCorrectEvent() (gas: 35348)
VerifierProxySetAccessControllerTest:test_revertsIfCalledByNonOwner() (gas: 15069)
VerifierProxySetAccessControllerTest:test_successfullySetsNewAccessController() (gas: 34921)
VerifierProxySetAccessControllerTest:test_successfullySetsNewAccessControllerIsEmpty() (gas: 15020)
VerifierProxyUnsetVerifierTest:test_revertsIfDigestDoesNotExist() (gas: 13149)
VerifierProxyUnsetVerifierTest:test_revertsIfNotAdmin() (gas: 14973)
VerifierProxyUnsetVerifierWithPreviouslySetVerifierTest:test_correctlyUnsetsVerifier() (gas: 15555)
VerifierProxyUnsetVerifierWithPreviouslySetVerifierTest:test_emitsAnEventAfterUnsettingVerifier() (gas: 17961)
VerifierProxyVerifyTest:test_proxiesToTheCorrectVerifier() (gas: 204342)
VerifierProxyVerifyTest:test_revertsIfNoVerifierConfigured() (gas: 117264)
VerifierSetAccessControllerTest:test_revertsIfCalledByNonOwner() (gas: 17196)
VerifierSetAccessControllerTest:test_setFeeManagerWhichDoesntHonourInterface() (gas: 16272)
VerifierSetAccessControllerTest:test_successfullySetsNewFeeManager() (gas: 42226)
VerifierSetConfigFromSourceMultipleDigestsTest:test_correctlySetsConfigWhenDigestsAreRemoved() (gas: 542302)
VerifierSetConfigFromSourceMultipleDigestsTest:test_correctlyUpdatesDigestsOnMultipleVerifiersInTheProxy() (gas: 967768)
VerifierSetConfigFromSourceMultipleDigestsTest:test_correctlyUpdatesTheDigestInTheProxy() (gas: 523251)
VerifierSetConfigFromSourceTest:test_revertsIfCalledByNonOwner() (gas: 183217)
VerifierSetConfigTest:test_correctlyUpdatesTheConfig() (gas: 1062438)
VerifierSetConfigTest:test_revertsIfCalledByNonOwner() (gas: 182986)
VerifierSetConfigTest:test_revertsIfDuplicateSigners() (gas: 251561)
VerifierSetConfigTest:test_revertsIfFaultToleranceIsZero() (gas: 176543)
VerifierSetConfigTest:test_revertsIfNotEnoughSigners() (gas: 15828)
VerifierSetConfigTest:test_revertsIfSetWithTooManySigners() (gas: 22213)
VerifierSetConfigTest:test_revertsIfSignerContainsZeroAddress() (gas: 228034)
VerifierSetConfigWhenThereAreMultipleDigestsTest:test_correctlySetsConfigWhenDigestsAreRemoved() (gas: 542051)
VerifierSetConfigWhenThereAreMultipleDigestsTest:test_correctlyUpdatesDigestsOnMultipleVerifiersInTheProxy() (gas: 967257)
VerifierSetConfigWhenThereAreMultipleDigestsTest:test_correctlyUpdatesTheDigestInTheProxy() (gas: 522991)
VerifierSupportsInterfaceTest:test_falseIfIsNotCorrectInterface() (gas: 8421)
VerifierSupportsInterfaceTest:test_trueIfIsCorrectInterface() (gas: 8464)
VerifierTestBillingReport:test_verifyWithLink() (gas: 275293)
VerifierTestBillingReport:test_verifyWithNative() (gas: 316326)
VerifierTestBillingReport:test_verifyWithNativeUnwrapped() (gas: 318574)
VerifierTestBillingReport:test_verifyWithNativeUnwrappedReturnsChange() (gas: 325642)
VerifierVerifyBulkTest:test_revertsVerifyBulkIfNoAccess() (gas: 112867)
VerifierVerifyBulkTest:test_verifyBulkSingleCaseWithSingleConfig() (gas: 745006)
VerifierVerifyBulkTest:test_verifyBulkWithSingleConfigOneVerifyFails() (gas: 698163)
VerifierVerifyMultipleConfigDigestTest:test_canVerifyNewerReportsWithNewerConfigs() (gas: 133961)
VerifierVerifyMultipleConfigDigestTest:test_canVerifyOlderReportsWithOlderConfigs() (gas: 189865)
VerifierVerifyMultipleConfigDigestTest:test_revertsIfAReportIsVerifiedWithAnExistingButIncorrectDigest() (gas: 88216)
VerifierVerifyMultipleConfigDigestTest:test_revertsIfVerifyingWithAnUnsetDigest() (gas: 128073)
VerifierVerifySingleConfigDigestTest:test_emitsAnEventIfReportVerified() (gas: 186956)
VerifierVerifySingleConfigDigestTest:test_returnsThePriceAndBlockNumIfReportVerified() (gas: 189847)
VerifierVerifySingleConfigDigestTest:test_revertsIfConfigDigestNotSet() (gas: 116141)
VerifierVerifySingleConfigDigestTest:test_revertsIfDuplicateSignersHaveSigned() (gas: 182326)
VerifierVerifySingleConfigDigestTest:test_revertsIfMismatchedSignatureLength() (gas: 53108)
VerifierVerifySingleConfigDigestTest:test_revertsIfReportHasUnconfiguredFeedID() (gas: 103987)
VerifierVerifySingleConfigDigestTest:test_revertsIfVerifiedByNonProxy() (gas: 100992)
VerifierVerifySingleConfigDigestTest:test_revertsIfVerifiedWithIncorrectAddresses() (gas: 184077)
VerifierVerifySingleConfigDigestTest:test_revertsIfWrongNumberOfSigners() (gas: 110042)
VerifierVerifySingleConfigDigestTest:test_setsTheCorrectEpoch() (gas: 194592)
VerifierVerifyTest:test_canVerifyNewerReportsWithNewerConfigs() (gas: 862947)
VerifierVerifyTest:test_canVerifyOlderV3ReportsWithOlderConfigs() (gas: 815907)
VerifierVerifyTest:test_failToVerifyReportIfDupSigners() (gas: 450675)
VerifierVerifyTest:test_failToVerifyReportIfNoSigners() (gas: 426452)
VerifierVerifyTest:test_failToVerifyReportIfNotEnoughSigners() (gas: 434774)
VerifierVerifyTest:test_failToVerifyReportIfSignerNotInConfig() (gas: 456826)
VerifierVerifyTest:test_revertsVerifyIfNoAccess() (gas: 109465)
VerifierVerifyTest:test_rollingOutConfiguration() (gas: 1497140)
VerifierVerifyTest:test_scenarioRollingNewChainWithHistoricConfigs() (gas: 976048)
VerifierVerifyTest:test_verifyFailsWhenReportIsOlderThanConfig() (gas: 2303291)
VerifierVerifyTest:test_verifyReport() (gas: 1434772)
VerifierVerifyTest:test_verifyTooglingActiveFlagsDonConfigs() (gas: 1918758)
Verifier_accessControlledVerify:testVerifyWithAccessControl_gas() (gas: 212077)
Verifier_bulkVerifyWithFee:testBulkVerifyProxyWithLinkFeeSuccess_gas() (gas: 519389)
Verifier_bulkVerifyWithFee:testBulkVerifyProxyWithNativeFeeSuccess_gas() (gas: 542808)
Verifier_setConfig:testSetConfigSuccess_gas() (gas: 922616)
Verifier_verify:testVerifyProxySuccess_gas() (gas: 198742)
Verifier_verify:testVerifySuccess_gas() (gas: 186736)
Verifier_verifyWithFee:testVerifyProxyWithLinkFeeSuccess_gas() (gas: 238899)
Verifier_verifyWithFee:testVerifyProxyWithNativeFeeSuccess_gas() (gas: 257399)