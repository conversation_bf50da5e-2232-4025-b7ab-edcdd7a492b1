LiquidityManager__report:test_EmptyReportReverts() (gas: 11181)
LiquidityManager_addLiquidity:test_addLiquiditySuccess() (gas: 279154)
LiquidityManager_rebalanceLiquidity:test_InsufficientLiquidityReverts() (gas: 206745)
LiquidityManager_rebalanceLiquidity:test_InvalidRemoteChainReverts() (gas: 192319)
LiquidityManager_rebalanceLiquidity:test_rebalanceBetweenPoolsSuccess() (gas: 9141768)
LiquidityManager_rebalanceLiquidity:test_rebalanceBetweenPoolsSuccess_AlreadyFinalized() (gas: 9306597)
LiquidityManager_rebalanceLiquidity:test_rebalanceBetweenPools_MultiStageFinalization() (gas: 9301803)
LiquidityManager_rebalanceLiquidity:test_rebalanceBetweenPools_NativeRewrap() (gas: 9231693)
LiquidityManager_rebalanceLiquidity:test_rebalanceLiquiditySuccess() (gas: 382880)
LiquidityManager_receive:test_receive_success() (gas: 21182)
LiquidityManager_removeLiquidity:test_InsufficientLiquidityReverts() (gas: 184869)
LiquidityManager_removeLiquidity:test_OnlyFinanceRoleReverts() (gas: 10872)
LiquidityManager_removeLiquidity:test_removeLiquiditySuccess() (gas: 236324)
LiquidityManager_setCrossChainRebalancer:test_OnlyOwnerReverts() (gas: 17005)
LiquidityManager_setCrossChainRebalancer:test_ZeroAddressReverts() (gas: 21624)
LiquidityManager_setCrossChainRebalancer:test_ZeroChainSelectorReverts() (gas: 13099)
LiquidityManager_setCrossChainRebalancer:test_setCrossChainRebalancerSuccess() (gas: 162186)
LiquidityManager_setFinanceRole:test_OnlyOwnerReverts() (gas: 10987)
LiquidityManager_setFinanceRole:test_setFinanceRoleSuccess() (gas: 21836)
LiquidityManager_setLocalLiquidityContainer:test_OnlyOwnerReverts() (gas: 11052)
LiquidityManager_setLocalLiquidityContainer:test_ReverstWhen_CalledWithTheZeroAddress() (gas: 10643)
LiquidityManager_setLocalLiquidityContainer:test_setLocalLiquidityContainerSuccess() (gas: 3847225)
LiquidityManager_setMinimumLiquidity:test_OnlyOwnerReverts() (gas: 10925)
LiquidityManager_setMinimumLiquidity:test_setMinimumLiquiditySuccess() (gas: 36389)
LiquidityManager_withdrawERC20:test_withdrawERC20Reverts() (gas: 180359)
LiquidityManager_withdrawERC20:test_withdrawERC20Success() (gas: 205858)
LiquidityManager_withdrawNative:test_OnlyFinanceRoleReverts() (gas: 13046)
LiquidityManager_withdrawNative:test_withdrawNative_success() (gas: 51398)
OCR3Base_setOCR3Config:testFMustBePositiveReverts() (gas: 12245)
OCR3Base_setOCR3Config:testFTooHighReverts() (gas: 12429)
OCR3Base_setOCR3Config:testOracleOutOfRegisterReverts() (gas: 14847)
OCR3Base_setOCR3Config:testRepeatAddressReverts() (gas: 44932)
OCR3Base_setOCR3Config:testSetConfigSuccess() (gas: 154642)
OCR3Base_setOCR3Config:testSignerCannotBeZeroAddressReverts() (gas: 23712)
OCR3Base_setOCR3Config:testTooManySignersReverts() (gas: 19832)
OCR3Base_setOCR3Config:testTransmitterCannotBeZeroAddressReverts() (gas: 46539)
OCR3Base_transmit:testConfigDigestMismatchReverts() (gas: 24827)
OCR3Base_transmit:testForkedChainReverts() (gas: 42846)
OCR3Base_transmit:testNonIncreasingSequenceNumberReverts() (gas: 30522)
OCR3Base_transmit:testNonUniqueSignatureReverts() (gas: 60370)
OCR3Base_transmit:testSignatureOutOfRegistrationReverts() (gas: 26128)
OCR3Base_transmit:testTransmit2SignersSuccess_gas() (gas: 56771)
OCR3Base_transmit:testUnAuthorizedTransmitterReverts() (gas: 28618)
OCR3Base_transmit:testUnauthorizedSignerReverts() (gas: 44759)
OCR3Base_transmit:testWrongNumberOfSignaturesReverts() (gas: 25678)
OptimismL1BridgeAdapter_finalizeWithdrawERC20:testFinalizeWithdrawERC20Reverts() (gas: 12932)
OptimismL1BridgeAdapter_finalizeWithdrawERC20:testfinalizeWithdrawERC20FinalizeSuccess() (gas: 16972)
OptimismL1BridgeAdapter_finalizeWithdrawERC20:testfinalizeWithdrawERC20proveWithdrawalSuccess() (gas: 20758)