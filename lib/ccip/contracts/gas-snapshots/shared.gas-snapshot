AuthorizedCallers_applyAuthorizedCallerUpdates:test_AddAndRemove_Success() (gas: 124925)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_OnlyAdd_Success() (gas: 132902)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_OnlyCallableByOwner_Revert() (gas: 12335)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_OnlyRemove_Success() (gas: 44930)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_RemoveThenAdd_Success() (gas: 57028)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_SkipRemove_Success() (gas: 32013)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_ZeroAddressNotAllowed_Revert() (gas: 64661)
AuthorizedCallers_constructor:test_ZeroAddressNotAllowed_Revert() (gas: 64661)
AuthorizedCallers_constructor:test_constructor_Success() (gas: 693193)
BurnMintERC20_approve:test_approve() (gas: 57609)
BurnMintERC20_approve:test_approve_RevertWhen_InvalidAddress() (gas: 11289)
BurnMintERC20_burn:test_BasicBurn() (gas: 153528)
BurnMintERC20_burn:test_burn_RevertWhen_BurnFromZeroAddress() (gas: 43800)
BurnMintERC20_burn:test_burn_RevertWhen_ExceedsBalance() (gas: 21868)
BurnMintERC20_burn:test_burn_RevertWhen_SenderNotBurner() (gas: 67663)
BurnMintERC20_burnFrom:test_BurnFrom() (gas: 57929)
BurnMintERC20_burnFrom:test_burnFrom_RevertWhen_ExceedsBalance() (gas: 35838)
BurnMintERC20_burnFrom:test_burnFrom_RevertWhen_InsufficientAllowance() (gas: 21834)
BurnMintERC20_burnFrom:test_burnFrom_RevertWhen_SenderNotBurner() (gas: 67662)
BurnMintERC20_burnFromAlias:test_burn() (gas: 57973)
BurnMintERC20_burnFromAlias:test_burn_RevertWhen_ExceedsBalance() (gas: 35880)
BurnMintERC20_burnFromAlias:test_burn_RevertWhen_InsufficientAllowance() (gas: 21897)
BurnMintERC20_burnFromAlias:test_burn_RevertWhen_SenderNotBurner() (gas: 67650)
BurnMintERC20_constructor:test_Constructor() (gas: 1680059)
BurnMintERC20_getCCIPAdmin:test_getCCIPAdmin() (gas: 10544)
BurnMintERC20_getCCIPAdmin:test_setCCIPAdmin() (gas: 21562)
BurnMintERC20_grantMintAndBurnRoles:test_GrantMintAndBurnRoles() (gas: 79089)
BurnMintERC20_mint:test_mint() (gas: 101815)
BurnMintERC20_mint:test_mint_RevertWhen_InvalidRecipient() (gas: 66372)
BurnMintERC20_mint:test_mint_RevertWhen_MaxSupplyExceeded() (gas: 50365)
BurnMintERC20_mint:test_mint_RevertWhen_SenderNotMinter() (gas: 65827)
BurnMintERC20_supportsInterface:test_SupportsInterface() (gas: 11202)
BurnMintERC20_transfer:test_transfer() (gas: 42318)
BurnMintERC20_transfer:test_transfer_RevertWhen_InvalidAddress() (gas: 11287)
BurnMintERC677_approve:testApproveSuccess() (gas: 55477)
BurnMintERC677_approve:testInvalidAddressReverts() (gas: 10653)
BurnMintERC677_burn:testBasicBurnSuccess() (gas: 172022)
BurnMintERC677_burn:testBurnFromZeroAddressReverts() (gas: 47166)
BurnMintERC677_burn:testExceedsBalanceReverts() (gas: 21816)
BurnMintERC677_burn:testSenderNotBurnerReverts() (gas: 13346)
BurnMintERC677_burnFrom:testBurnFromSuccess() (gas: 57892)
BurnMintERC677_burnFrom:testExceedsBalanceReverts() (gas: 35838)
BurnMintERC677_burnFrom:testInsufficientAllowanceReverts() (gas: 21824)
BurnMintERC677_burnFrom:testSenderNotBurnerReverts() (gas: 13346)
BurnMintERC677_burnFromAlias:testBurnFromSuccess() (gas: 57919)
BurnMintERC677_burnFromAlias:testExceedsBalanceReverts() (gas: 35854)
BurnMintERC677_burnFromAlias:testInsufficientAllowanceReverts() (gas: 21844)
BurnMintERC677_burnFromAlias:testSenderNotBurnerReverts() (gas: 13366)
BurnMintERC677_constructor:testConstructorSuccess() (gas: 1651040)
BurnMintERC677_decreaseApproval:testDecreaseApprovalSuccess() (gas: 31049)
BurnMintERC677_grantMintAndBurnRoles:testGrantMintAndBurnRolesSuccess() (gas: 121279)
BurnMintERC677_grantRole:testGrantBurnAccessSuccess() (gas: 53420)
BurnMintERC677_grantRole:testGrantManySuccess() (gas: 937593)
BurnMintERC677_grantRole:testGrantMintAccessSuccess() (gas: 94297)
BurnMintERC677_increaseApproval:testIncreaseApprovalSuccess() (gas: 44052)
BurnMintERC677_mint:testBasicMintSuccess() (gas: 149664)
BurnMintERC677_mint:testMaxSupplyExceededReverts() (gas: 50345)
BurnMintERC677_mint:testSenderNotMinterReverts() (gas: 11182)
BurnMintERC677_supportsInterface:testConstructorSuccess() (gas: 12455)
BurnMintERC677_transfer:testInvalidAddressReverts() (gas: 10629)
BurnMintERC677_transfer:testTransferSuccess() (gas: 42279)
CallWithExactGas__callWithExactGas:test_CallWithExactGasReceiverErrorSuccess() (gas: 65883)
CallWithExactGas__callWithExactGas:test_CallWithExactGasSafeReturnDataExactGas() (gas: 18293)
CallWithExactGas__callWithExactGas:test_NoContractReverts() (gas: 11544)
CallWithExactGas__callWithExactGas:test_NoGasForCallExactCheckReverts() (gas: 15760)
CallWithExactGas__callWithExactGas:test_NotEnoughGasForCallReverts() (gas: 16210)
CallWithExactGas__callWithExactGas:test_callWithExactGasSuccess(bytes,bytes4) (runs: 256, μ: 15741, ~: 15694)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_CallWithExactGasEvenIfTargetIsNoContractExactGasSuccess() (gas: 20076)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_CallWithExactGasEvenIfTargetIsNoContractReceiverErrorSuccess() (gas: 66394)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_CallWithExactGasEvenIfTargetIsNoContractSuccess(bytes,bytes4) (runs: 256, μ: 16250, ~: 16203)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_NoContractSuccess() (gas: 12942)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_NoGasForCallExactCheckReturnFalseSuccess() (gas: 12984)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_NotEnoughGasForCallReturnsFalseSuccess() (gas: 13293)
CallWithExactGas__callWithExactGasSafeReturnData:test_CallWithExactGasSafeReturnDataExactGas() (gas: 20287)
CallWithExactGas__callWithExactGasSafeReturnData:test_NoContractReverts() (gas: 13893)
CallWithExactGas__callWithExactGasSafeReturnData:test_NoGasForCallExactCheckReverts() (gas: 16108)
CallWithExactGas__callWithExactGasSafeReturnData:test_NotEnoughGasForCallReverts() (gas: 16535)
CallWithExactGas__callWithExactGasSafeReturnData:test_callWithExactGasSafeReturnData_ThrowOOGError_Revert() (gas: 36666)
EnumerableMapAddresses_at:testAtSuccess() (gas: 95055)
EnumerableMapAddresses_at:testBytes32AtSuccess() (gas: 94850)
EnumerableMapAddresses_at:testBytesAtSuccess() (gas: 96529)
EnumerableMapAddresses_contains:testBytes32ContainsSuccess() (gas: 93503)
EnumerableMapAddresses_contains:testBytesContainsSuccess() (gas: 93996)
EnumerableMapAddresses_contains:testContainsSuccess() (gas: 93678)
EnumerableMapAddresses_get:testBytes32GetSuccess() (gas: 94260)
EnumerableMapAddresses_get:testBytesGetSuccess() (gas: 95853)
EnumerableMapAddresses_get:testGetSuccess() (gas: 94431)
EnumerableMapAddresses_get_errorMessage:testBytesGetErrorMessageSuccess() (gas: 95852)
EnumerableMapAddresses_get_errorMessage:testGetErrorMessageSuccess() (gas: 94467)
EnumerableMapAddresses_length:testBytes32LengthSuccess() (gas: 72418)
EnumerableMapAddresses_length:testBytesLengthSuccess() (gas: 72982)
EnumerableMapAddresses_length:testLengthSuccess() (gas: 72609)
EnumerableMapAddresses_remove:testBytes32RemoveSuccess() (gas: 73432)
EnumerableMapAddresses_remove:testBytesRemoveSuccess() (gas: 74216)
EnumerableMapAddresses_remove:testRemoveSuccess() (gas: 73651)
EnumerableMapAddresses_set:testBytes32SetSuccess() (gas: 94475)
EnumerableMapAddresses_set:testBytesSetSuccess() (gas: 95405)
EnumerableMapAddresses_set:testSetSuccess() (gas: 94660)
EnumerableMapAddresses_tryGet:testBytes32TryGetSuccess() (gas: 94602)
EnumerableMapAddresses_tryGet:testBytesTryGetSuccess() (gas: 96250)
EnumerableMapAddresses_tryGet:testTryGetSuccess() (gas: 94869)
OpStackBurnMintERC677_constructor:testConstructorSuccess() (gas: 1721311)
OpStackBurnMintERC677_interfaceCompatibility:testBurnCompatibility() (gas: 291155)
OpStackBurnMintERC677_interfaceCompatibility:testMintCompatibility() (gas: 137917)
OpStackBurnMintERC677_interfaceCompatibility:testStaticFunctionsCompatibility() (gas: 13773)
OpStackBurnMintERC677_supportsInterface:testConstructorSuccess() (gas: 12728)
Ownable2Step_acceptOwnership:test_acceptOwnership_MustBeProposedOwner_reverts() (gas: 10353)
Ownable2Step_acceptOwnership:test_acceptOwnership_success() (gas: 31070)
Ownable2Step_constructor:test_constructor_OwnerCannotBeZero_reverts() (gas: 35924)
Ownable2Step_constructor:test_constructor_success() (gas: 10424)
Ownable2Step_onlyOwner:test_onlyOwner_OnlyCallableByOwner_reverts() (gas: 10746)
Ownable2Step_onlyOwner:test_onlyOwner_success() (gas: 7503)
Ownable2Step_transferOwnership:test_transferOwnership_CannotTransferToSelf_reverts() (gas: 10494)
Ownable2Step_transferOwnership:test_transferOwnership_success() (gas: 30124)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_EmptySubset_Reverts() (gas: 5191)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_EmptySuperset_Reverts() (gas: 4522)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_HasDuplicates_Reverts() (gas: 7738)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_NotASubset_Reverts() (gas: 11635)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SingleElementSubset() (gas: 3908)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SingleElementSubsetAndSuperset_Equal() (gas: 1459)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SingleElementSubsetAndSuperset_NotEqual_Reverts() (gas: 6149)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SubsetEqualsSuperset_NoRevert() (gas: 7830)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SubsetLargerThanSuperset_Reverts() (gas: 15363)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SupersetHasDuplicates_Reverts() (gas: 8767)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_UnsortedSubset_Reverts() (gas: 7106)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_UnsortedSuperset_Reverts() (gas: 8947)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_ValidSubset_Success() (gas: 5653)