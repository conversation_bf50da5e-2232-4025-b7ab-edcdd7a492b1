WorkflowRegistryManager_activateVersion:test_WhenTheVersionNumberIsNotActive() (gas: 419)
WorkflowRegistryManageraddVersion:test_WhenAutoActivateIsFalse() (gas: 164)
WorkflowRegistryManageraddVersion:test_WhenAutoActivateIsTrue() (gas: 120)
WorkflowRegistryManagergetActiveVersion:test_WhenAnActiveVersionExists() (gas: 120)
WorkflowRegistryManagergetActiveVersion:test_WhenNoActiveVersionIsAvailable() (gas: 139)
WorkflowRegistryManagergetAllVersions:test_WhenLimitExceedsMaximumPaginationLimit() (gas: 161)
WorkflowRegistryManagergetAllVersions:test_WhenRequestingWithInvalidStartIndex() (gas: 142)
WorkflowRegistryManagergetAllVersions:test_WhenRequestingWithValidStartIndexAndLimitWithinBounds() (gas: 120)
WorkflowRegistryManagergetLatestVersion:test_WhenNoVersionsHaveBeenRegistered() (gas: 120)
WorkflowRegistryManagergetLatestVersion:test_WhenVersionsHaveBeenRegistered() (gas: 139)
WorkflowRegistryManagergetVersion:test_WhenVersionNumberIsNotRegistered() (gas: 120)
WorkflowRegistryManagergetVersion:test_WhenVersionNumberIsRegistered() (gas: 139)
WorkflowRegistryManagergetVersionNumber:test_WhenAVersionIsRegisteredForTheContractAddressAndChainIDCombination() (gas: 161)
WorkflowRegistryManagergetVersionNumber:test_WhenNoVersionIsRegisteredForTheContractAddressAndChainIDCombination() (gas: 120)
WorkflowRegistryManagergetVersionNumber:test_WhenTheContractAddressIsInvalid() (gas: 142)
WorkflowRegistry_activateWorkflow:test_WhenTheCallerIsAnAuthorizedAddress() (gas: 491327)
WorkflowRegistry_deleteWorkflow:test_WhenTheCallerIsAnAuthorizedAddress_AndTheDonIDIsAllowed() (gas: 400597)
WorkflowRegistry_deleteWorkflow:test_WhenTheCallerIsAnAuthorizedAddress_AndTheDonIDIsNotAllowed() (gas: 418579)
WorkflowRegistry_getAllAllowedDONs:test_WhenTheSetOfAllowedDONsIsEmpty() (gas: 25780)
WorkflowRegistry_getAllAllowedDONs:test_WhenThereAreMultipleAllowedDONs() (gas: 75437)
WorkflowRegistry_getAllAllowedDONs:test_WhenThereIsASingleAllowedDON() (gas: 16566)
WorkflowRegistry_getWorkflowMetadata:test_WhenTheWorkflowDoesNotExist() (gas: 17521)
WorkflowRegistry_getWorkflowMetadata:test_WhenTheWorkflowExistsWithTheOwnerAndName() (gas: 490176)
WorkflowRegistry_getWorkflowMetadataListByDON:test_WhenLimitExceedsTotalWorkflows() (gas: 127660)
WorkflowRegistry_getWorkflowMetadataListByDON:test_WhenLimitIsEqualToTotalWorkflows() (gas: 128013)
WorkflowRegistry_getWorkflowMetadataListByDON:test_WhenLimitIsLessThanTotalWorkflows() (gas: 90119)
WorkflowRegistry_getWorkflowMetadataListByDON:test_WhenStartIs0() (gas: 127572)
WorkflowRegistry_getWorkflowMetadataListByDON:test_WhenStartIsGreaterThan0() (gas: 90076)
WorkflowRegistry_getWorkflowMetadataListByDON:test_WhenStartIsGreaterThanOrEqualToTotalWorkflows() (gas: 13454)
WorkflowRegistry_getWorkflowMetadataListByDON:test_WhenTheDONHasNoWorkflows() (gas: 13410)
WorkflowRegistry_getWorkflowMetadataListByOwner:test_WhenLimitExceedsTotalWorkflows() (gas: 128221)
WorkflowRegistry_getWorkflowMetadataListByOwner:test_WhenLimitIsEqualToTotalWorkflows() (gas: 128320)
WorkflowRegistry_getWorkflowMetadataListByOwner:test_WhenLimitIsLessThanTotalWorkflows() (gas: 90404)
WorkflowRegistry_getWorkflowMetadataListByOwner:test_WhenStartIs0_AndLimitIs0() (gas: 128118)
WorkflowRegistry_getWorkflowMetadataListByOwner:test_WhenStartIsGreaterThan0() (gas: 90361)
WorkflowRegistry_getWorkflowMetadataListByOwner:test_WhenStartIsGreaterThanOrEqualToTotalWorkflows() (gas: 13764)
WorkflowRegistry_getWorkflowMetadataListByOwner:test_WhenTheOwnerHasNoWorkflows() (gas: 13984)
WorkflowRegistry_pauseWorkflow:test_WhenTheDonIDIsAllowed_AndTheCallerIsAnAuthorizedAddress() (gas: 491299)
WorkflowRegistry_pauseWorkflow:test_WhenTheDonIDIsAllowed_AndTheCallerIsAnUnauthorizedAddress() (gas: 499097)
WorkflowRegistry_pauseWorkflow:test_WhenTheDonIDIsNotAllowed_AndTheCallerIsAnAuthorizedAddress() (gas: 498850)
WorkflowRegistry_pauseWorkflow:test_WhenTheDonIDIsNotAllowed_AndTheCallerIsAnUnauthorizedAddress() (gas: 503264)
WorkflowRegistry_registerWorkflow:test_WhenTheWorkflowInputsAreAllValid() (gas: 549829)
WorkflowRegistry_requestForceUpdateSecrets:test_WhenTheCallerIsAnAuthorizedAddress_AndTheWorkflowIsInAnAllowedDON() (gas: 891242)
WorkflowRegistry_requestForceUpdateSecrets:test_WhenTheCallerIsAnAuthorizedAddress_AndTheWorkflowIsNotInAnAllowedDON() (gas: 488397)
WorkflowRegistry_requestForceUpdateSecrets:test_WhenTheCallerIsNotAnAuthorizedAddress() (gas: 486751)
WorkflowRegistry_updateAllowedDONs:test_WhenTheBoolInputIsFalse() (gas: 29811)
WorkflowRegistry_updateAllowedDONs:test_WhenTheBoolInputIsTrue() (gas: 170386)
WorkflowRegistry_updateAuthorizedAddresses:test_WhenTheBoolInputIsFalse() (gas: 30350)
WorkflowRegistry_updateAuthorizedAddresses:test_WhenTheBoolInputIsTrue() (gas: 175605)
WorkflowRegistry_updateWorkflow:test_WhenTheWorkflowInputsAreAllValid() (gas: 501589)
WorkflowRegistrygetAllAuthorizedAddresses:test_WhenTheSetOfAuthorizedAddressesIsEmpty() (gas: 26152)
WorkflowRegistrygetAllAuthorizedAddresses:test_WhenThereAreMultipleAuthorizedAddresses() (gas: 78270)
WorkflowRegistrygetAllAuthorizedAddresses:test_WhenThereIsASingleAuthorizedAddress() (gas: 16814)