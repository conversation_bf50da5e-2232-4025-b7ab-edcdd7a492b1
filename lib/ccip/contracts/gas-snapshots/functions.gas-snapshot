ChainSpecificUtil__getL1FeeUpperLimit_Arbitrum:test__getL1FeeUpperLimit_SuccessWhenArbitrumGoerli() (gas: 16076840)
ChainSpecificUtil__getL1FeeUpperLimit_Arbitrum:test__getL1FeeUpperLimit_SuccessWhenArbitrumMainnet() (gas: 16076819)
ChainSpecificUtil__getL1FeeUpperLimit_Arbitrum:test__getL1FeeUpperLimit_SuccessWhenArbitrumSepolia() (gas: 16076888)
ChainSpecificUtil__getL1FeeUpperLimit_Base:test__getL1FeeUpperLimit_SuccessWhenBaseGoerli() (gas: 16076608)
ChainSpecificUtil__getL1FeeUpperLimit_Base:test__getL1FeeUpperLimit_SuccessWhenBaseMainnet() (gas: 16076564)
ChainSpecificUtil__getL1FeeUpperLimit_Base:test__getL1FeeUpperLimit_SuccessWhenBaseSepolia() (gas: 16076635)
ChainSpecificUtil__getL1FeeUpperLimit_Optimism:test__getL1FeeUpperLimit_SuccessWhenOptimismGoerli() (gas: 16076521)
ChainSpecificUtil__getL1FeeUpperLimit_Optimism:test__getL1FeeUpperLimit_SuccessWhenOptimismMainnet() (gas: 16076543)
ChainSpecificUtil__getL1FeeUpperLimit_Optimism:test__getL1FeeUpperLimit_SuccessWhenOptimismSepolia() (gas: 16076586)
FunctionsBilling_Constructor:test_Constructor_Success() (gas: 17982)
FunctionsBilling_DeleteCommitment:test_DeleteCommitment_RevertIfNotRouter() (gas: 13283)
FunctionsBilling_DeleteCommitment:test_DeleteCommitment_Success() (gas: 15853)
FunctionsBilling_EstimateCost:test_EstimateCost_RevertsIfGasPriceAboveCeiling() (gas: 32450)
FunctionsBilling_EstimateCost:test_EstimateCost_Success() (gas: 90977)
FunctionsBilling_EstimateCost:test_EstimateCost_SuccessLowGasPrice() (gas: 91102)
FunctionsBilling_GetAdminFeeJuels:test_GetAdminFeeJuels_Success() (gas: 18671)
FunctionsBilling_GetConfig:test_GetConfig_Success() (gas: 30750)
FunctionsBilling_GetDONFeeJuels:test_GetDONFeeJuels_Success() (gas: 41151)
FunctionsBilling_GetOperationFee:test_GetOperationFeeJuels_Success() (gas: 40548)
FunctionsBilling_GetWeiPerUnitLink:test_GetWeiPerUnitLink_Success() (gas: 29751)
FunctionsBilling_OracleWithdraw:test_OracleWithdraw_RevertIfInsufficientBalance() (gas: 70136)
FunctionsBilling_OracleWithdraw:test_OracleWithdraw_RevertWithNoBalance() (gas: 108953)
FunctionsBilling_OracleWithdraw:test_OracleWithdraw_SuccessCoordinatorOwner() (gas: 129908)
FunctionsBilling_OracleWithdraw:test_OracleWithdraw_SuccessTransmitterWithBalanceNoAmountGiven() (gas: 171930)
FunctionsBilling_OracleWithdraw:test_OracleWithdraw_SuccessTransmitterWithBalanceValidAmountGiven() (gas: 145165)
FunctionsBilling_OracleWithdrawAll:test_OracleWithdrawAll_RevertIfNotOwner() (gas: 13320)
FunctionsBilling_OracleWithdrawAll:test_OracleWithdrawAll_SuccessPaysTransmittersWithBalance() (gas: 222357)
FunctionsBilling_UpdateConfig:test_UpdateConfig_RevertIfNotOwner() (gas: 21632)
FunctionsBilling_UpdateConfig:test_UpdateConfig_Success() (gas: 54099)
FunctionsBilling__DisperseFeePool:test__DisperseFeePool_RevertIfNotSet() (gas: 8810)
FunctionsBilling__FulfillAndBill:test__FulfillAndBill_RevertIfInvalidCommitment() (gas: 13353)
FunctionsBilling__FulfillAndBill:test__FulfillAndBill_Success() (gas: 185976)
FunctionsBilling__StartBilling:test__FulfillAndBill_HasUniqueGlobalRequestId() (gas: 524582)
FunctionsClient_Constructor:test_Constructor_Success() (gas: 10407)
FunctionsClient_HandleOracleFulfillment:test_HandleOracleFulfillment_RevertIfNotRouter() (gas: 14595)
FunctionsClient_HandleOracleFulfillment:test_HandleOracleFulfillment_Success() (gas: 22983)
FunctionsClient__SendRequest:test__SendRequest_RevertIfInvalidCallbackGasLimit() (gas: 55069)
FunctionsCoordinator_Constructor:test_Constructor_Success() (gas: 15085)
FunctionsCoordinator_GetDONPublicKey:test_GetDONPublicKey_RevertIfEmpty() (gas: 15356)
FunctionsCoordinator_GetDONPublicKey:test_GetDONPublicKey_Success() (gas: 91691)
FunctionsCoordinator_GetThresholdPublicKey:test_GetThresholdPublicKey_RevertIfEmpty() (gas: 15334)
FunctionsCoordinator_GetThresholdPublicKey:test_GetThresholdPublicKey_Success() (gas: 515951)
FunctionsCoordinator_SetDONPublicKey:test_SetDONPublicKey_RevertNotOwner() (gas: 20409)
FunctionsCoordinator_SetDONPublicKey:test_SetDONPublicKey_Success() (gas: 88970)
FunctionsCoordinator_SetThresholdPublicKey:test_SetThresholdPublicKey_RevertNotOwner() (gas: 13915)
FunctionsCoordinator_SetThresholdPublicKey:test_SetThresholdPublicKey_Success() (gas: 513165)
FunctionsCoordinator_StartRequest:test_StartRequest_RevertIfNotRouter() (gas: 22802)
FunctionsCoordinator_StartRequest:test_StartRequest_Success() (gas: 152650)
FunctionsCoordinator__IsTransmitter:test__IsTransmitter_SuccessFound() (gas: 15106)
FunctionsCoordinator__IsTransmitter:test__IsTransmitter_SuccessNotFound() (gas: 22916)
FunctionsRequest_DEFAULT_BUFFER_SIZE:test_DEFAULT_BUFFER_SIZE() (gas: 3089)
FunctionsRequest_EncodeCBOR:test_EncodeCBOR_Success() (gas: 3066)
FunctionsRequest_REQUEST_DATA_VERSION:test_REQUEST_DATA_VERSION() (gas: 3068)
FunctionsRouter_Constructor:test_Constructor_Success() (gas: 15107)
FunctionsRouter_Fulfill:test_Fulfill_RequestNotProcessedCostExceedsCommitment() (gas: 172475)
FunctionsRouter_Fulfill:test_Fulfill_RequestNotProcessedInsufficientGas() (gas: 163010)
FunctionsRouter_Fulfill:test_Fulfill_RequestNotProcessedInvalidCommitment() (gas: 38777)
FunctionsRouter_Fulfill:test_Fulfill_RequestNotProcessedInvalidRequestId() (gas: 35900)
FunctionsRouter_Fulfill:test_Fulfill_RequestNotProcessedSubscriptionBalanceInvariant() (gas: 180976)
FunctionsRouter_Fulfill:test_Fulfill_RevertIfNotCommittedCoordinator() (gas: 28152)
FunctionsRouter_Fulfill:test_Fulfill_RevertIfPaused() (gas: 156535)
FunctionsRouter_Fulfill:test_Fulfill_SuccessClientNoLongerExists() (gas: 335070)
FunctionsRouter_Fulfill:test_Fulfill_SuccessFulfilled() (gas: 348695)
FunctionsRouter_Fulfill:test_Fulfill_SuccessUserCallbackReverts() (gas: 2627478)
FunctionsRouter_Fulfill:test_Fulfill_SuccessUserCallbackRunsOutOfGas() (gas: 658423)
FunctionsRouter_GetAdminFee:test_GetAdminFee_Success() (gas: 18323)
FunctionsRouter_GetAllowListId:test_GetAllowListId_Success() (gas: 13241)
FunctionsRouter_GetConfig:test_GetConfig_Success() (gas: 40193)
FunctionsRouter_GetContractById:test_GetContractById_RevertIfRouteDoesNotExist() (gas: 13862)
FunctionsRouter_GetContractById:test_GetContractById_SuccessIfRouteExists() (gas: 17704)
FunctionsRouter_GetProposedContractById:test_GetProposedContractById_RevertIfRouteDoesNotExist() (gas: 16417)
FunctionsRouter_GetProposedContractById:test_GetProposedContractById_SuccessIfRouteExists() (gas: 24289)
FunctionsRouter_GetProposedContractSet:test_GetProposedContractSet_Success() (gas: 27289)
FunctionsRouter_IsValidCallbackGasLimit:test_IsValidCallbackGasLimit_RevertGasLimitTooBig() (gas: 28087)
FunctionsRouter_IsValidCallbackGasLimit:test_IsValidCallbackGasLimit_RevertInvalidConfig() (gas: 41095)
FunctionsRouter_IsValidCallbackGasLimit:test_IsValidCallbackGasLimit_Success() (gas: 24632)
FunctionsRouter_Pause:test_Pause_RevertIfNotOwner() (gas: 13361)
FunctionsRouter_Pause:test_Pause_Success() (gas: 20647)
FunctionsRouter_ProposeContractsUpdate:test_ProposeContractsUpdate_RevertIfEmptyAddress() (gas: 14769)
FunctionsRouter_ProposeContractsUpdate:test_ProposeContractsUpdate_RevertIfExceedsMaxProposal() (gas: 21716)
FunctionsRouter_ProposeContractsUpdate:test_ProposeContractsUpdate_RevertIfLengthMismatch() (gas: 14670)
FunctionsRouter_ProposeContractsUpdate:test_ProposeContractsUpdate_RevertIfNotNewContract() (gas: 19048)
FunctionsRouter_ProposeContractsUpdate:test_ProposeContractsUpdate_RevertIfNotOwner() (gas: 23392)
FunctionsRouter_ProposeContractsUpdate:test_ProposeContractsUpdate_Success() (gas: 118479)
FunctionsRouter_SendRequest:test_SendRequest_RevertIfConsumerNotAllowed() (gas: 59400)
FunctionsRouter_SendRequest:test_SendRequest_RevertIfDuplicateRequestId() (gas: 217996)
FunctionsRouter_SendRequest:test_SendRequest_RevertIfEmptyData() (gas: 29426)
FunctionsRouter_SendRequest:test_SendRequest_RevertIfIncorrectDonId() (gas: 57882)
FunctionsRouter_SendRequest:test_SendRequest_RevertIfInsufficientSubscriptionBalance() (gas: 208451)
FunctionsRouter_SendRequest:test_SendRequest_RevertIfInvalidCallbackGasLimit() (gas: 50953)
FunctionsRouter_SendRequest:test_SendRequest_RevertIfInvalidDonId() (gas: 25082)
FunctionsRouter_SendRequest:test_SendRequest_RevertIfNoSubscription() (gas: 29177)
FunctionsRouter_SendRequest:test_SendRequest_RevertIfPaused() (gas: 34291)
FunctionsRouter_SendRequest:test_SendRequest_Success() (gas: 226324)
FunctionsRouter_SendRequestToProposed:test_SendRequestToProposed_RevertIfConsumerNotAllowed() (gas: 65918)
FunctionsRouter_SendRequestToProposed:test_SendRequestToProposed_RevertIfEmptyData() (gas: 36012)
FunctionsRouter_SendRequestToProposed:test_SendRequestToProposed_RevertIfIncorrectDonId() (gas: 29896)
FunctionsRouter_SendRequestToProposed:test_SendRequestToProposed_RevertIfInvalidCallbackGasLimit() (gas: 57539)
FunctionsRouter_SendRequestToProposed:test_SendRequestToProposed_RevertIfInvalidDonId() (gas: 27503)
FunctionsRouter_SendRequestToProposed:test_SendRequestToProposed_RevertIfNoSubscription() (gas: 35672)
FunctionsRouter_SendRequestToProposed:test_SendRequestToProposed_RevertIfPaused() (gas: 40810)
FunctionsRouter_SendRequestToProposed:test_SendRequestToProposed_Success() (gas: 232783)
FunctionsRouter_SendRequestToProposed:test_SendRequest_RevertIfInsufficientSubscriptionBalance() (gas: 214899)
FunctionsRouter_SetAllowListId:test_SetAllowListId_Success() (gas: 33531)
FunctionsRouter_SetAllowListId:test_UpdateConfig_RevertIfNotOwner() (gas: 13381)
FunctionsRouter_Unpause:test_Unpause_RevertIfNotOwner() (gas: 13337)
FunctionsRouter_Unpause:test_Unpause_Success() (gas: 77748)
FunctionsRouter_UpdateConfig:test_UpdateConfig_RevertIfNotOwner() (gas: 24415)
FunctionsRouter_UpdateConfig:test_UpdateConfig_Success() (gas: 63331)
FunctionsRouter_UpdateContracts:test_UpdateContracts_RevertIfNotOwner() (gas: 13314)
FunctionsRouter_UpdateContracts:test_UpdateContracts_Success() (gas: 39269)
FunctionsSubscriptions_AcceptSubscriptionOwnerTransfer:test_AcceptSubscriptionOwnerTransfer_RevertIfNotAllowedSender() (gas: 60413)
FunctionsSubscriptions_AcceptSubscriptionOwnerTransfer:test_AcceptSubscriptionOwnerTransfer_RevertIfPaused() (gas: 61040)
FunctionsSubscriptions_AcceptSubscriptionOwnerTransfer:test_AcceptSubscriptionOwnerTransfer_RevertIfSenderBecomesBlocked() (gas: 139706)
FunctionsSubscriptions_AcceptSubscriptionOwnerTransfer:test_AcceptSubscriptionOwnerTransfer_RevertIfSenderIsNotNewOwner() (gas: 62780)
FunctionsSubscriptions_AcceptSubscriptionOwnerTransfer:test_AcceptSubscriptionOwnerTransfer_Success() (gas: 239919)
FunctionsSubscriptions_AddConsumer:test_AddConsumer_RevertIfMaximumConsumers() (gas: 138033)
FunctionsSubscriptions_AddConsumer:test_AddConsumer_RevertIfMaximumConsumersAfterConfigUpdate() (gas: 164977)
FunctionsSubscriptions_AddConsumer:test_AddConsumer_RevertIfNoSubscription() (gas: 12933)
FunctionsSubscriptions_AddConsumer:test_AddConsumer_RevertIfNotAllowedSender() (gas: 102428)
FunctionsSubscriptions_AddConsumer:test_AddConsumer_RevertIfNotSubscriptionOwner() (gas: 87272)
FunctionsSubscriptions_AddConsumer:test_AddConsumer_RevertIfPaused() (gas: 18100)
FunctionsSubscriptions_AddConsumer:test_AddConsumer_Success() (gas: 96221)
FunctionsSubscriptions_CancelSubscription:test_CancelSubscription_RevertIfNoSubscription() (gas: 15053)
FunctionsSubscriptions_CancelSubscription:test_CancelSubscription_RevertIfNotAllowedSender() (gas: 102507)
FunctionsSubscriptions_CancelSubscription:test_CancelSubscription_RevertIfNotSubscriptionOwner() (gas: 89341)
FunctionsSubscriptions_CancelSubscription:test_CancelSubscription_RevertIfPaused() (gas: 20157)
FunctionsSubscriptions_CancelSubscription:test_CancelSubscription_RevertIfPendingRequests() (gas: 218308)
FunctionsSubscriptions_CancelSubscription:test_CancelSubscription_SuccessForfeitAllBalanceAsDeposit() (gas: 115656)
FunctionsSubscriptions_CancelSubscription:test_CancelSubscription_SuccessForfeitSomeBalanceAsDeposit() (gas: 126964)
FunctionsSubscriptions_CancelSubscription_ReceiveDeposit:test_CancelSubscription_SuccessRecieveDeposit() (gas: 75369)
FunctionsSubscriptions_Constructor:test_Constructor_Success() (gas: 10488)
FunctionsSubscriptions_CreateSubscriptionWithConsumer:test_CreateSubscriptionWithConsumer_RevertIfNotAllowedSender() (gas: 28688)
FunctionsSubscriptions_CreateSubscriptionWithConsumer:test_CreateSubscriptionWithConsumer_RevertIfPaused() (gas: 18038)
FunctionsSubscriptions_CreateSubscriptionWithConsumer:test_CreateSubscriptionWithConsumer_Success() (gas: 353899)
FunctionsSubscriptions_GetConsumer:test_GetConsumer_Success() (gas: 17279)
FunctionsSubscriptions_GetFlags:test_GetFlags_SuccessInvalidSubscription() (gas: 13504)
FunctionsSubscriptions_GetFlags:test_GetFlags_SuccessValidSubscription() (gas: 41221)
FunctionsSubscriptions_GetSubscription:test_GetSubscription_Success() (gas: 32968)
FunctionsSubscriptions_GetSubscriptionCount:test_GetSubscriptionCount_Success() (gas: 13305)
FunctionsSubscriptions_GetSubscriptionsInRange:test_GetSubscriptionsInRange_RevertIfEndIsAfterLastSubscription() (gas: 16570)
FunctionsSubscriptions_GetSubscriptionsInRange:test_GetSubscriptionsInRange_RevertIfStartIsAfterEnd() (gas: 13465)
FunctionsSubscriptions_GetSubscriptionsInRange:test_GetSubscriptionsInRange_Success() (gas: 65968)
FunctionsSubscriptions_GetTotalBalance:test_GetTotalBalance_Success() (gas: 15370)
FunctionsSubscriptions_OnTokenTransfer:test_OnTokenTransfer_RevertIfCallerIsNoCalldata() (gas: 39908)
FunctionsSubscriptions_OnTokenTransfer:test_OnTokenTransfer_RevertIfCallerIsNoSubscription() (gas: 42382)
FunctionsSubscriptions_OnTokenTransfer:test_OnTokenTransfer_RevertIfCallerIsNotLink() (gas: 13419)
FunctionsSubscriptions_OnTokenTransfer:test_OnTokenTransfer_RevertIfPaused() (gas: 47325)
FunctionsSubscriptions_OnTokenTransfer:test_OnTokenTransfer_Success() (gas: 84314)
FunctionsSubscriptions_OracleWithdraw:test_OracleWithdraw_RevertIfAmountMoreThanBalance() (gas: 20744)
FunctionsSubscriptions_OracleWithdraw:test_OracleWithdraw_RevertIfBalanceInvariant() (gas: 189)
FunctionsSubscriptions_OracleWithdraw:test_OracleWithdraw_RevertIfNoAmount() (gas: 15686)
FunctionsSubscriptions_OracleWithdraw:test_OracleWithdraw_RevertIfPaused() (gas: 20859)
FunctionsSubscriptions_OracleWithdraw:test_OracleWithdraw_SuccessPaysRecipient() (gas: 60119)
FunctionsSubscriptions_OracleWithdraw:test_OracleWithdraw_SuccessSetsBalanceToZero() (gas: 57716)
FunctionsSubscriptions_OwnerCancelSubscription:test_OwnerCancelSubscription_RevertIfNoSubscription() (gas: 12796)
FunctionsSubscriptions_OwnerCancelSubscription:test_OwnerCancelSubscription_RevertIfNotOwner() (gas: 15594)
FunctionsSubscriptions_OwnerCancelSubscription:test_OwnerCancelSubscription_Success() (gas: 55177)
FunctionsSubscriptions_OwnerCancelSubscription:test_OwnerCancelSubscription_SuccessDeletesSubscription() (gas: 49607)
FunctionsSubscriptions_OwnerCancelSubscription:test_OwnerCancelSubscription_SuccessSubOwnerRefunded() (gas: 53166)
FunctionsSubscriptions_OwnerCancelSubscription:test_OwnerCancelSubscription_SuccessWhenRequestInFlight() (gas: 186511)
FunctionsSubscriptions_OwnerWithdraw:test_OwnerWithdraw_RevertIfAmountMoreThanBalance() (gas: 17923)
FunctionsSubscriptions_OwnerWithdraw:test_OwnerWithdraw_RevertIfBalanceInvariant() (gas: 210)
FunctionsSubscriptions_OwnerWithdraw:test_OwnerWithdraw_RevertIfNotOwner() (gas: 15600)
FunctionsSubscriptions_OwnerWithdraw:test_OwnerWithdraw_SuccessIfNoAmount() (gas: 33839)
FunctionsSubscriptions_OwnerWithdraw:test_OwnerWithdraw_SuccessIfRecipientAddressZero() (gas: 31627)
FunctionsSubscriptions_OwnerWithdraw:test_OwnerWithdraw_SuccessPaysRecipient() (gas: 33935)
FunctionsSubscriptions_OwnerWithdraw:test_OwnerWithdraw_SuccessSetsBalanceToZero() (gas: 31584)
FunctionsSubscriptions_PendingRequestExists:test_PendingRequestExists_SuccessFalseIfNoPendingRequests() (gas: 17796)
FunctionsSubscriptions_PendingRequestExists:test_PendingRequestExists_SuccessTrueIfPendingRequests() (gas: 203311)
FunctionsSubscriptions_ProposeSubscriptionOwnerTransfer:test_ProposeSubscriptionOwnerTransfer_RevertIfEmptyNewOwner() (gas: 27642)
FunctionsSubscriptions_ProposeSubscriptionOwnerTransfer:test_ProposeSubscriptionOwnerTransfer_RevertIfInvalidNewOwner() (gas: 57793)
FunctionsSubscriptions_ProposeSubscriptionOwnerTransfer:test_ProposeSubscriptionOwnerTransfer_RevertIfNoSubscription() (gas: 15013)
FunctionsSubscriptions_ProposeSubscriptionOwnerTransfer:test_ProposeSubscriptionOwnerTransfer_RevertIfNotAllowedSender() (gas: 119820)
FunctionsSubscriptions_ProposeSubscriptionOwnerTransfer:test_ProposeSubscriptionOwnerTransfer_RevertIfNotSubscriptionOwner() (gas: 17947)
FunctionsSubscriptions_ProposeSubscriptionOwnerTransfer:test_ProposeSubscriptionOwnerTransfer_RevertIfPaused() (gas: 20181)
FunctionsSubscriptions_ProposeSubscriptionOwnerTransfer:test_ProposeSubscriptionOwnerTransfer_Success() (gas: 68574)
FunctionsSubscriptions_ProposeSubscriptionOwnerTransfer:test_ProposeSubscriptionOwnerTransfer_SuccessChangeProposedOwner() (gas: 83582)
FunctionsSubscriptions_RecoverFunds:test_OwnerCancelSubscription_RevertIfNotOwner() (gas: 15577)
FunctionsSubscriptions_RecoverFunds:test_RecoverFunds_Success() (gas: 41358)
FunctionsSubscriptions_RemoveConsumer:test_RemoveConsumer_RevertIfInvalidConsumer() (gas: 30310)
FunctionsSubscriptions_RemoveConsumer:test_RemoveConsumer_RevertIfNoSubscription() (gas: 15031)
FunctionsSubscriptions_RemoveConsumer:test_RemoveConsumer_RevertIfNotAllowedSender() (gas: 102444)
FunctionsSubscriptions_RemoveConsumer:test_RemoveConsumer_RevertIfNotSubscriptionOwner() (gas: 87277)
FunctionsSubscriptions_RemoveConsumer:test_RemoveConsumer_RevertIfPaused() (gas: 18103)
FunctionsSubscriptions_RemoveConsumer:test_RemoveConsumer_RevertIfPendingRequests() (gas: 215863)
FunctionsSubscriptions_RemoveConsumer:test_RemoveConsumer_Success() (gas: 42066)
FunctionsSubscriptions_SetFlags:test_SetFlags_RevertIfNoSubscription() (gas: 12888)
FunctionsSubscriptions_SetFlags:test_SetFlags_RevertIfNotOwner() (gas: 15684)
FunctionsSubscriptions_SetFlags:test_SetFlags_Success() (gas: 38434)
FunctionsSubscriptions_TimeoutRequests:test_TimeoutRequests_RevertIfPaused() (gas: 25955)
FunctionsSubscriptions_TimeoutRequests:test_TimeoutRequests_RevertIfTimeoutNotExceeded() (gas: 25261)
FunctionsSubscriptions_TimeoutRequests:test_TimeoutRequests_RevertInvalidRequest() (gas: 28220)
FunctionsSubscriptions_TimeoutRequests:test_TimeoutRequests_Success() (gas: 58394)
FunctionsSubscriptions_createSubscription:test_CreateSubscription_RevertIfNotAllowedSender() (gas: 26462)
FunctionsSubscriptions_createSubscription:test_CreateSubscription_RevertIfPaused() (gas: 15759)
FunctionsSubscriptions_createSubscription:test_CreateSubscription_Success() (gas: 153701)
FunctionsTermsOfServiceAllowList_AcceptTermsOfService:testAcceptTermsOfService_InvalidSigner_vuln() (gas: 94943)
FunctionsTermsOfServiceAllowList_AcceptTermsOfService:test_AcceptTermsOfService_RevertIfAcceptorIsNotSender() (gas: 25925)
FunctionsTermsOfServiceAllowList_AcceptTermsOfService:test_AcceptTermsOfService_RevertIfBlockedSender() (gas: 89013)
FunctionsTermsOfServiceAllowList_AcceptTermsOfService:test_AcceptTermsOfService_RevertIfInvalidSigner() (gas: 23620)
FunctionsTermsOfServiceAllowList_AcceptTermsOfService:test_AcceptTermsOfService_RevertIfRecipientContractIsNotSender() (gas: 1866619)
FunctionsTermsOfServiceAllowList_AcceptTermsOfService:test_AcceptTermsOfService_RevertIfRecipientIsNotSender() (gas: 28526)
FunctionsTermsOfServiceAllowList_AcceptTermsOfService:test_AcceptTermsOfService_SuccessIfAcceptingForContract() (gas: 1946966)
FunctionsTermsOfServiceAllowList_AcceptTermsOfService:test_AcceptTermsOfService_SuccessIfAcceptingForSelf() (gas: 104555)
FunctionsTermsOfServiceAllowList_BlockSender:test_BlockSender_RevertIfNotOwner() (gas: 15535)
FunctionsTermsOfServiceAllowList_BlockSender:test_BlockSender_Success() (gas: 97587)
FunctionsTermsOfServiceAllowList_Constructor:test_Constructor_Success() (gas: 15345)
FunctionsTermsOfServiceAllowList_GetAllAllowedSenders:test_GetAllAllowedSenders_Success() (gas: 19243)
FunctionsTermsOfServiceAllowList_GetAllowedSendersCount:test_GetAllowedSendersCount_Success() (gas: 13332)
FunctionsTermsOfServiceAllowList_GetAllowedSendersInRange:test_GetAllowedSendersInRange_RevertIfAllowedSendersIsEmpty() (gas: 13326576)
FunctionsTermsOfServiceAllowList_GetAllowedSendersInRange:test_GetAllowedSendersInRange_RevertIfEndIsAfterLastAllowedSender() (gas: 16532)
FunctionsTermsOfServiceAllowList_GetAllowedSendersInRange:test_GetAllowedSendersInRange_RevertIfStartIsAfterEnd() (gas: 13307)
FunctionsTermsOfServiceAllowList_GetAllowedSendersInRange:test_GetAllowedSendersInRange_Success() (gas: 20335)
FunctionsTermsOfServiceAllowList_GetBlockedSendersCount:test_GetBlockedSendersCount_Success() (gas: 13268)
FunctionsTermsOfServiceAllowList_GetBlockedSendersInRange:test_GetBlockedSendersInRange_RevertIfAllowedSendersIsEmpty() (gas: 13326564)
FunctionsTermsOfServiceAllowList_GetBlockedSendersInRange:test_GetBlockedSendersInRange_RevertIfEndIsAfterLastAllowedSender() (gas: 16549)
FunctionsTermsOfServiceAllowList_GetBlockedSendersInRange:test_GetBlockedSendersInRange_RevertIfStartIsAfterEnd() (gas: 13345)
FunctionsTermsOfServiceAllowList_GetBlockedSendersInRange:test_GetBlockedSendersInRange_Success() (gas: 18580)
FunctionsTermsOfServiceAllowList_GetConfig:test_GetConfig_Success() (gas: 16411)
FunctionsTermsOfServiceAllowList_GetMessage:test_GetMessage_Success() (gas: 11918)
FunctionsTermsOfServiceAllowList_HasAccess:test_HasAccess_FalseWhenEnabled() (gas: 16235)
FunctionsTermsOfServiceAllowList_HasAccess:test_HasAccess_TrueWhenDisabled() (gas: 23877)
FunctionsTermsOfServiceAllowList_IsBlockedSender:test_IsBlockedSender_SuccessFalse() (gas: 15776)
FunctionsTermsOfServiceAllowList_IsBlockedSender:test_IsBlockedSender_SuccessTrue() (gas: 86974)
FunctionsTermsOfServiceAllowList_MigratePreviouslyAllowedSenders:test_MigratePreviouslyAllowedSenders_RevertIfNotOwner() (gas: 13987)
FunctionsTermsOfServiceAllowList_MigratePreviouslyAllowedSenders:test_MigratePreviouslyAllowedSenders_Success() (gas: 210636)
FunctionsTermsOfServiceAllowList_UnblockSender:test_UnblockSender_RevertIfNotOwner() (gas: 13548)
FunctionsTermsOfServiceAllowList_UnblockSender:test_UnblockSender_Success() (gas: 96240)
FunctionsTermsOfServiceAllowList_UpdateConfig:test_UpdateConfig_RevertIfNotOwner() (gas: 13819)
FunctionsTermsOfServiceAllowList_UpdateConfig:test_UpdateConfig_Success() (gas: 22824)
Gas_AcceptTermsOfService:test_AcceptTermsOfService_Gas() (gas: 81752)
Gas_AddConsumer:test_AddConsumer_Gas() (gas: 76167)
Gas_CreateSubscription:test_CreateSubscription_Gas() (gas: 73419)
Gas_FulfillRequest_DuplicateRequestID:test_FulfillRequest_DuplicateRequestID_MaximumGas() (gas: 17589)
Gas_FulfillRequest_DuplicateRequestID:test_FulfillRequest_DuplicateRequestID_MinimumGas() (gas: 17051)
Gas_FulfillRequest_Success:test_FulfillRequest_Success_MaximumGas() (gas: 498189)
Gas_FulfillRequest_Success:test_FulfillRequest_Success_MinimumGas() (gas: 199381)
Gas_FundSubscription:test_FundSubscription_Gas() (gas: 35545)
Gas_SendRequest:test_SendRequest_MaximumGas() (gas: 981365)
Gas_SendRequest:test_SendRequest_MinimumGas() (gas: 178588)