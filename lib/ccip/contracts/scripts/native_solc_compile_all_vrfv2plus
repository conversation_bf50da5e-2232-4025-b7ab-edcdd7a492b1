\#!/usr/bin/env bash

set -e

echo " ┌──────────────────────────────────────────────┐"
echo " │          Compiling VRF contracts...          │"
echo " └──────────────────────────────────────────────┘"

SOLC_VERSION="0.8.19"
OPTIMIZE_RUNS=1000000

SCRIPTPATH="$( cd "$(dirname "$0")" >/dev/null 2>&1 ; pwd -P )"
ROOT="$( cd "$(dirname "$0")" >/dev/null 2>&1 ; cd ../../ && pwd -P )"
python3 -m pip install --require-hashes -r "$SCRIPTPATH"/requirements.txt

solc-select install $SOLC_VERSION
solc-select use $SOLC_VERSION
export SOLC_VERSION=$SOLC_VERSION

compileContract () {
  local contract
  contract=$(basename "$1" ".sol")

  solc @openzeppelin/="$ROOT"/contracts/node_modules/@openzeppelin/ \
      --overwrite --optimize --optimize-runs $OPTIMIZE_RUNS --metadata-hash none \
      -o "$ROOT"/contracts/solc/v$SOLC_VERSION/"$contract" \
      --abi --bin --allow-paths "$ROOT"/contracts/src/v0.8,"$ROOT"/contracts/node_modules\
      "$ROOT"/contracts/src/v0.8/"$1"
}

compileContractAltOpts () {
  local contract
  contract=$(basename "$1" ".sol")

  solc @openzeppelin/="$ROOT"/contracts/node_modules/@openzeppelin/ \
      --overwrite --optimize --optimize-runs "$2" --metadata-hash none \
      -o "$ROOT"/contracts/solc/v$SOLC_VERSION/"$contract" \
      --abi --bin --allow-paths "$ROOT"/contracts/src/v0.8,"$ROOT"/contracts/node_modules\
      "$ROOT"/contracts/src/v0.8/"$1"
}

# VRF V2Plus
compileContract vrf/dev/interfaces/IVRFCoordinatorV2PlusInternal.sol
compileContract vrf/dev/testhelpers/VRFV2PlusConsumerExample.sol
compileContractAltOpts vrf/dev/VRFCoordinatorV2_5.sol 500
compileContractAltOpts vrf/dev/VRFCoordinatorV2_5_Arbitrum.sol 500
compileContractAltOpts vrf/dev/VRFCoordinatorV2_5_Optimism.sol 500
compileContract vrf/dev/BatchVRFCoordinatorV2Plus.sol
compileContract vrf/dev/VRFV2PlusWrapper.sol
compileContract vrf/dev/VRFV2PlusWrapper_Arbitrum.sol
compileContract vrf/dev/VRFV2PlusWrapper_Optimism.sol
compileContract vrf/dev/testhelpers/VRFConsumerV2PlusUpgradeableExample.sol
compileContract vrf/dev/testhelpers/VRFMaliciousConsumerV2Plus.sol
compileContract vrf/dev/testhelpers/VRFV2PlusExternalSubOwnerExample.sol
compileContract vrf/dev/testhelpers/VRFV2PlusSingleConsumerExample.sol
compileContract vrf/dev/testhelpers/VRFV2PlusWrapperConsumerExample.sol
compileContract vrf/dev/testhelpers/VRFV2PlusRevertingExample.sol
compileContract vrf/dev/testhelpers/VRFConsumerV2PlusUpgradeableExample.sol
compileContract vrf/dev/testhelpers/VRFV2PlusMaliciousMigrator.sol
compileContract vrf/dev/libraries/VRFV2PlusClient.sol
compileContract vrf/dev/testhelpers/VRFCoordinatorV2Plus_V2Example.sol
compileContract vrf/dev/TrustedBlockhashStore.sol
compileContract vrf/dev/testhelpers/VRFV2PlusLoadTestWithMetrics.sol
compileContractAltOpts vrf/dev/testhelpers/VRFCoordinatorV2PlusUpgradedVersion.sol 5
compileContract vrf/dev/testhelpers/VRFV2PlusWrapperLoadTestConsumer.sol
compileContract vrf/testhelpers/VRFMockETHLINKAggregator.sol
compileContract vrf/dev/testhelpers/VRFV2PlusLoadTestWithMetrics.sol
compileContract vrf/BatchBlockhashStore.sol
compileContract vrf/dev/BlockhashStore.sol
