import hre from 'hardhat'
//import big from 'ethers'
// pass in the correct constrcutor arguments for the contract &
// run with `npx hardhat run zksync-verify.ts --config ./hardhat.ccip.zksync.config.ts` & remember to change the appropriate `default network` in the config file or pass as argument
async function main() {
  await hre.run('verify:verify', {
    address: '******************************************',
    constructorArguments: [
      {
        voters: [
          {
            blessVoteAddr: '******************************************',
            curseVoteAddr: '******************************************',
            blessWeight: 1,
            curseWeight: 1,
          },
        ],
        blessWeightThreshold: 1,
        curseWeightThreshold: 1,
      },
    ],
  })
}

main().catch((error) => {
  console.error(error)
  process.exitCode = 1
})
