// SPDX-License-Identifier: MIT
/**
 * @notice This is a deprecated interface. Please use AutomationRegistryInterface1_2 directly.
 */
pragma solidity ^0.8.0;
// solhint-disable-next-line no-unused-import
import {Config, State} from "./AutomationRegistryInterface1_2.sol";
// solhint-disable-next-line no-unused-import
import {AutomationRegistryBaseInterface as KeeperRegistryBaseInterface} from "./AutomationRegistryInterface1_2.sol";
// solhint-disable-next-line no-unused-import
import {AutomationRegistryInterface as KeeperRegistryInterface} from "./AutomationRegistryInterface1_2.sol";
// solhint-disable-next-line no-unused-import
import {AutomationRegistryExecutableInterface as KeeperRegistryExecutableInterface} from "./AutomationRegistryInterface1_2.sol";
