// SPDX-License-Identifier: MIT

pragma solidity 0.8.6;

import {VRFSubscriptionBalanceMonitor} from "../dev/VRFSubscriptionBalanceMonitor.sol";

contract VRFSubscriptionBalanceMonitorExposed is VRFSubscriptionBalanceMonitor {
  constructor(
    address linkT<PERSON><PERSON><PERSON><PERSON>,
    address coordinator<PERSON><PERSON><PERSON>,
    address keeper<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    uint256 minWaitPeriodSeconds
  ) VRFSubscriptionBalanceMonitor(linkTokenAddress, coordinatorAddress, keeperRegistryAddress, minWaitPeriodSeconds) {}

  function setLastTopUpXXXTestOnly(uint64 target, uint56 lastTopUpTimestamp) external {
    s_targets[target].lastTopUpTimestamp = lastTopUpTimestamp;
  }
}
