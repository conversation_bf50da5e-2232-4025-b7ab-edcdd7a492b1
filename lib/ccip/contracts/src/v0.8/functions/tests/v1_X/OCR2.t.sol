// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

// ================================================================
// |                           OCR2Base                           |
// ================================================================

/// @notice #constructor
contract OCR2Base_Constructor {}

/// @notice #checkConfigValid
contract OCR2Base_CheckConfigValid {}

/// @notice #latestConfigDigestAndEpoch
contract OCR2Base_LatestConfigDigestAndEpoch {}

/// @notice #setConfig
contract OCR2Base_SetConfig {}

/// @notice #configDigestFromConfigData
contract OCR2Base_ConfigDigestFromConfigData {}

/// @notice #latestConfigDetails
contract OCR2Base_LatestConfigDetails {}

/// @notice #transmitters
contract OCR2Base_Transmitters {}

/// @notice #_report
contract OCR2Base__Report {
  // TODO: make contract internal function helper
}

/// @notice #requireExpectedMsgDataLength
contract OCR2Base_RequireExpectedMsgDataLength {}

/// @notice #transmit
contract OCR2Base_Transmit {}
