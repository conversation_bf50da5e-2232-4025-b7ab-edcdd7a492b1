// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.24;

import {IWrappedNative} from "../../../ccip/interfaces/IWrappedNative.sol";

import {ArbitrumL1BridgeAdapter, IOutbox} from "../../bridge-adapters/ArbitrumL1BridgeAdapter.sol";
import "forge-std/Test.sol";

import {IL1GatewayRouter} from "@arbitrum/token-bridge-contracts/contracts/tokenbridge/ethereum/gateway/IL1GatewayRouter.sol";
import {IGatewayRouter} from "@arbitrum/token-bridge-contracts/contracts/tokenbridge/libraries/gateway/IGatewayRouter.sol";
import {IERC20} from "../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol";

//contract ArbitrumL1BridgeAdapterSetup is Test {
//  uint256 internal mainnetFork;
//  uint256 internal arbitrumFork;
//
//  string internal constant MAINNET_RPC_URL = "<url>";
//
//  address internal constant L1_GATEWAY_ROUTER = ******************************************;
//  address internal constant L1_ERC20_GATEWAY = ******************************************;
//  address internal constant L1_INBOX = ******************************************;
//  // inbox ******************************************?
//  address internal constant L1_OUTBOX = ******************************************;
//
//  IERC20 internal constant L1_LINK = IERC20(******************************************);
//  IWrappedNative internal constant L1_WRAPPED_NATIVE = IWrappedNative(******************************************);
//
//  address internal constant L2_GATEWAY_ROUTER = ******************************************;
//  address internal constant L2_ETH_WITHDRAWAL_PRECOMPILE = ******************************************;
//
//  IERC20 internal constant L2_LINK = IERC20(******************************************);
//  IWrappedNative internal constant L2_WRAPPED_NATIVE = IWrappedNative(******************************************);
//
//  ArbitrumL1BridgeAdapter internal s_l1BridgeAdapter;
//
//  uint256 internal constant TOKEN_BALANCE = 10e18;
//  address internal constant OWNER = address(0xdead);
//
//  function setUp() public {
//    vm.startPrank(OWNER);
//
//    mainnetFork = vm.createFork(MAINNET_RPC_URL);
//    vm.selectFork(mainnetFork);
//
//    s_l1BridgeAdapter = new ArbitrumL1BridgeAdapter(
//      IL1GatewayRouter(L1_GATEWAY_ROUTER),
//      IOutbox(L1_OUTBOX),
//      L1_ERC20_GATEWAY
//    );
//
//    deal(address(L1_LINK), OWNER, TOKEN_BALANCE);
//    deal(address(L1_WRAPPED_NATIVE), OWNER, TOKEN_BALANCE);
//
//    vm.label(OWNER, "Owner");
//    vm.label(L1_GATEWAY_ROUTER, "L1GatewayRouter");
//    vm.label(L1_ERC20_GATEWAY, "L1 ERC20 Gateway");
//  }
//}
//
//contract ArbitrumL1BridgeAdapter_sendERC20 is ArbitrumL1BridgeAdapterSetup {
//  event TransferRouted(address indexed token, address indexed _userFrom, address indexed _userTo, address gateway);
//
//  function test_sendERC20Success() public {
//    L1_LINK.approve(address(s_l1BridgeAdapter), TOKEN_BALANCE);
//
//    vm.expectEmit();
//    emit TransferRouted(address(L1_LINK), address(s_l1BridgeAdapter), OWNER, L1_ERC20_GATEWAY);
//
//    uint256 expectedCost = s_l1BridgeAdapter.MAX_GAS() *
//      s_l1BridgeAdapter.GAS_PRICE_BID() +
//      s_l1BridgeAdapter.MAX_SUBMISSION_COST();
//
//    s_l1BridgeAdapter.sendERC20{value: expectedCost}(address(L1_LINK), OWNER, OWNER, TOKEN_BALANCE);
//  }
//
//  function test_BridgeFeeTooLowReverts() public {
//    L1_LINK.approve(address(s_l1BridgeAdapter), TOKEN_BALANCE);
//    uint256 expectedCost = s_l1BridgeAdapter.MAX_GAS() *
//      s_l1BridgeAdapter.GAS_PRICE_BID() +
//      s_l1BridgeAdapter.MAX_SUBMISSION_COST();
//
//    vm.expectRevert(
//      abi.encodeWithSelector(ArbitrumL1BridgeAdapter.InsufficientEthValue.selector, expectedCost, expectedCost - 1)
//    );
//
//    s_l1BridgeAdapter.sendERC20{value: expectedCost - 1}(address(L1_LINK), OWNER, OWNER, TOKEN_BALANCE);
//  }
//
//  function test_noApprovalReverts() public {
//    uint256 expectedCost = s_l1BridgeAdapter.MAX_GAS() *
//      s_l1BridgeAdapter.GAS_PRICE_BID() +
//      s_l1BridgeAdapter.MAX_SUBMISSION_COST();
//
//    vm.expectRevert("SafeERC20: low-level call failed");
//
//    s_l1BridgeAdapter.sendERC20{value: expectedCost}(address(L1_LINK), OWNER, OWNER, TOKEN_BALANCE);
//  }
//}
