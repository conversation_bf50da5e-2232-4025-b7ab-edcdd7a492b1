// SPDX-License-Identifier: BUSL-1.1
pragma solidity 0.8.24;

import {IWrappedNative} from "../../../ccip/interfaces/IWrappedNative.sol";

import {ArbitrumL2BridgeAdapter, IL2GatewayRouter} from "../../bridge-adapters/ArbitrumL2BridgeAdapter.sol";
import "forge-std/Test.sol";

import {IERC20} from "../../../vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol";

//contract ArbitrumL2BridgeAdapterSetup is Test {
//  uint256 internal arbitrumFork;
//
//  string internal constant ARBITRUM_RPC_URL = "<url>";
//
//  address internal constant L2_GATEWAY_ROUTER = ******************************************;
//  address internal constant L2_ETH_WITHDRAWAL_PRECOMPILE = ******************************************;
//
//  IERC20 internal constant L1_LINK = IERC20(******************************************);
//  IERC20 internal constant L2_LINK = IERC20(******************************************);
//  IWrappedNative internal constant L2_WRAPPED_NATIVE = IWrappedNative(******************************************);
//
//  uint256 internal constant TOKEN_BALANCE = 10e18;
//  address internal constant OWNER = address(0xdead);
//
//  ArbitrumL2BridgeAdapter internal s_l2BridgeAdapter;
//
//  function setUp() public {
//    vm.startPrank(OWNER);
//
//    arbitrumFork = vm.createFork(ARBITRUM_RPC_URL);
//
//    vm.selectFork(arbitrumFork);
//    s_l2BridgeAdapter = new ArbitrumL2BridgeAdapter(IL2GatewayRouter(L2_GATEWAY_ROUTER));
//    deal(address(L2_LINK), OWNER, TOKEN_BALANCE);
//    deal(address(L2_WRAPPED_NATIVE), OWNER, TOKEN_BALANCE);
//
//    vm.label(OWNER, "Owner");
//    vm.label(L2_GATEWAY_ROUTER, "L2GatewayRouterProxy");
//    vm.label(******************************************, "L2GatewayRouter");
//    vm.label(L2_ETH_WITHDRAWAL_PRECOMPILE, "Precompile: ArbSys");
//  }
//}
//
//contract ArbitrumL2BridgeAdapter_sendERC20 is ArbitrumL2BridgeAdapterSetup {
//  function test_sendERC20Success() public {
//    L2_LINK.approve(address(s_l2BridgeAdapter), TOKEN_BALANCE);
//
//    s_l2BridgeAdapter.sendERC20(address(L1_LINK), address(L2_LINK), OWNER, TOKEN_BALANCE);
//  }
//}
