{"name": "@chainlink/contracts-ccip", "version": "1.5.1", "description": "Chainlink CCIP smart contracts", "author": "Chainlink devs", "license": "BUSL-1.1", "private": false, "scripts": {"test": "hardhat test --parallel", "lint": "eslint --ext js,ts .", "prettier:check": "prettier '**/*' --check --ignore-unknown", "prettier:write": "prettier '**/*' --write --ignore-unknown", "size": "hardhat size-contracts", "clean": "hardhat clean", "compile:native": "./scripts/native_solc_compile_all", "compile": "hardhat compile --no-typechain", "zksync:compile": "npx hardhat compile --config ./hardhat.ccip.zksync.config.ts --no-typechain", "zksync:verify:sepolia": "npx hardhat run scripts/zksyncverify/zksync-verify.ts --network zkSyncSepolia --config ./hardhat.ccip.zksync.config.ts", "zksync:verify": "npx hardhat run scripts/zksyncverify/zksync-verify.ts --network zkSync --config ./hardhat.ccip.zksync.config.ts", "coverage": "hardhat coverage", "prepare": "chmod +x .husky/prepare.sh && ./.husky/prepare.sh", "prepublishOnly": "pnpm compile && ./scripts/prepublish_generate_abi_folder", "publish-beta": "pnpm publish --tag beta", "publish-prod": "npm dist-tag add @chainlink/contracts-ccip@1.5.1 latest", "solhint:ccip": "solhint --max-warnings 0 \"./src/v0.8/ccip/**/*.sol\"", "solhint": "solhint --max-warnings 0 \"./src/v0.8/**/*.sol\""}, "files": ["src/v0.8/ccip/**/*.sol", "src/v0.8/shared/access/*.sol", "src/v0.8/shared/call/CallWithExactGas.sol", "src/v0.8/shared/enumerable/EnumerableMapBytes32.sol", "src/v0.8/shared/enumerable/EnumerableMapAddresses.sol", "src/v0.8/shared/interfaces/IOwnable.sol", "src/v0.8/shared/interfaces/ITypeAndVersion.sol", "src/v0.8/shared/interfaces/IERC677Receiver.sol", "src/v0.8/shared/interfaces/AggregatorV3Interface.sol", "src/v0.8/shared/token/ERC20/IBurnMintERC20.sol", "src/v0.8/shared/token/ERC677/IERC677.sol", "src/v0.8/shared/token/ERC677/IERC677Receiver.sol", "src/v0.8/shared/token/ERC677/ERC677.sol", "src/v0.8/shared/token/ERC677/BurnMintERC677.sol", "src/v0.8/shared/util/SortedSetValidationUtil.sol", "src/v0.8/liquiditymanager/interfaces/ILiquidityContainer.sol", "src/v0.8/vendor/openzeppelin-solidity", "src/v0.8/vendor/Context.sol", "src/v0.8/vendor/Pausable.sol", "abi/v0.8/", "src/v0.8/ccip/LICENSE.md", "src/v0.8/ccip/v1.5-CCIP-License-grants.md", "!src/v0.8/ccip/test/**/*", "src/v0.8/ccip/test/mocks/**/*", "!src/v0.8/ccip/test/mocks/test/*", "!src/v0.8/ccip/onRamp/OnRamp.sol", "!src/v0.8/ccip/offRamp/OffRamp.sol", "!src/v0.8/ccip/ocr/MultiOCR3Base.sol", "!src/v0.8/ccip/NonceManager.sol", "!src/v0.8/ccip/MultiAggregateRateLimiter.sol", "!src/v0.8/ccip/capability", "!src/v0.8/ccip/FeeQuoter.sol", "!src/v0.8/ccip/interfaces/encodingutils/ICCIPEncodingUtils.sol", "!src/v0.8/ccip/rmn"], "pnpm": {"_comment": "See https://github.com/ethers-io/ethers.js/discussions/2849#discussioncomment-2696454", "_comment2_logger": "See https://github.com/ethers-io/ethers.js/issues/379 we pin this version since that's what was used in the old yarn lockfile", "overrides": {"@ethersproject/logger": "5.0.6"}}, "engines": {"node": ">=18", "pnpm": ">=9"}, "devDependencies": {"@ethereum-waffle/mock-contract": "^3.4.4", "@ethersproject/abi": "~5.7.0", "@ethersproject/bignumber": "~5.7.0", "@ethersproject/contracts": "~5.7.0", "@ethersproject/providers": "~5.7.2", "@matterlabs/hardhat-zksync-solc": "^1.2.1", "@nomicfoundation/hardhat-chai-matchers": "^1.0.6", "@nomicfoundation/hardhat-ethers": "^3.0.6", "@nomicfoundation/hardhat-network-helpers": "^1.0.11", "@nomicfoundation/hardhat-verify": "^2.0.9", "@typechain/ethers-v5": "^7.2.0", "@typechain/hardhat": "^7.0.0", "@types/cbor": "~5.0.1", "@types/chai": "^4.3.17", "@types/debug": "^4.1.12", "@types/deep-equal-in-any-order": "^1.0.3", "@types/mocha": "^10.0.7", "@types/node": "^20.14.15", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "abi-to-sol": "^0.6.6", "cbor": "^5.2.0", "chai": "^4.5.0", "debug": "^4.3.6", "deep-equal-in-any-order": "^2.0.6", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "ethers": "~5.7.2", "hardhat": "~2.20.1", "hardhat-abi-exporter": "^2.10.1", "hardhat-ignore-warnings": "^0.2.6", "husky": "^9.0.11", "lint-staged": "^15.2.2", "moment": "^2.30.1", "prettier": "^3.3.3", "prettier-plugin-solidity": "^1.3.1", "solhint": "^5.0.1", "solhint-plugin-chainlink-solidity": "git+https://github.com/smartcontractkit/chainlink-solhint-rules.git#v1.2.1", "solhint-plugin-prettier": "^0.1.0", "ts-node": "^10.9.2", "typechain": "^8.2.1", "typescript": "^5.5.4"}, "dependencies": {"@arbitrum/nitro-contracts": "1.1.1", "@arbitrum/token-bridge-contracts": "1.1.2", "@changesets/changelog-github": "^0.5.0", "@changesets/cli": "~2.27.7", "@eth-optimism/contracts": "0.6.0", "@matterlabs/hardhat-zksync-verify": "^1.6.0", "@openzeppelin/contracts": "4.9.3", "@openzeppelin/contracts-upgradeable": "4.9.3", "@scroll-tech/contracts": "0.1.0", "@zksync/contracts": "git+https://github.com/matter-labs/era-contracts.git#446d391d34bdb48255d5f8fef8a8248925fc98b9", "semver": "^7.6.3"}, "lint-staged": {"*.sol": ["forge fmt"]}}