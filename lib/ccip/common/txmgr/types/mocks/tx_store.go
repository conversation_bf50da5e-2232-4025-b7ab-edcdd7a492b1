// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"
	big "math/big"

	feetypes "github.com/smartcontractkit/chainlink/v2/common/fee/types"
	mock "github.com/stretchr/testify/mock"

	null "gopkg.in/guregu/null.v4"

	time "time"

	txmgrtypes "github.com/smartcontractkit/chainlink/v2/common/txmgr/types"

	types "github.com/smartcontractkit/chainlink/v2/common/types"

	uuid "github.com/google/uuid"
)

// TxStore is an autogenerated mock type for the TxStore type
type TxStore[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	mock.Mock
}

type TxStore_Expecter[ADDR types.<PERSON><PERSON><PERSON>, CHAIN_ID types.ID, TX_HASH types.<PERSON>hab<PERSON>, BLOCK_HASH types.<PERSON>hab<PERSON>, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	mock *mock.Mock
}

func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) EXPECT() *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{mock: &_m.Mock}
}

// Abandon provides a mock function with given fields: ctx, id, addr
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Abandon(ctx context.Context, id CHAIN_ID, addr ADDR) error {
	ret := _m.Called(ctx, id, addr)

	if len(ret) == 0 {
		panic("no return value specified for Abandon")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID, ADDR) error); ok {
		r0 = rf(ctx, id, addr)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_Abandon_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Abandon'
type TxStore_Abandon_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// Abandon is a helper method to define mock.On call
//   - ctx context.Context
//   - id CHAIN_ID
//   - addr ADDR
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Abandon(ctx interface{}, id interface{}, addr interface{}) *TxStore_Abandon_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_Abandon_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("Abandon", ctx, id, addr)}
}

func (_c *TxStore_Abandon_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, id CHAIN_ID, addr ADDR)) *TxStore_Abandon_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CHAIN_ID), args[2].(ADDR))
	})
	return _c
}

func (_c *TxStore_Abandon_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_Abandon_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_Abandon_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, CHAIN_ID, ADDR) error) *TxStore_Abandon_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// CheckTxQueueCapacity provides a mock function with given fields: ctx, fromAddress, maxQueuedTransactions, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CheckTxQueueCapacity(ctx context.Context, fromAddress ADDR, maxQueuedTransactions uint64, chainID CHAIN_ID) error {
	ret := _m.Called(ctx, fromAddress, maxQueuedTransactions, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CheckTxQueueCapacity")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, uint64, CHAIN_ID) error); ok {
		r0 = rf(ctx, fromAddress, maxQueuedTransactions, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_CheckTxQueueCapacity_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckTxQueueCapacity'
type TxStore_CheckTxQueueCapacity_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// CheckTxQueueCapacity is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress ADDR
//   - maxQueuedTransactions uint64
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CheckTxQueueCapacity(ctx interface{}, fromAddress interface{}, maxQueuedTransactions interface{}, chainID interface{}) *TxStore_CheckTxQueueCapacity_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_CheckTxQueueCapacity_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("CheckTxQueueCapacity", ctx, fromAddress, maxQueuedTransactions, chainID)}
}

func (_c *TxStore_CheckTxQueueCapacity_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, fromAddress ADDR, maxQueuedTransactions uint64, chainID CHAIN_ID)) *TxStore_CheckTxQueueCapacity_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(uint64), args[3].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_CheckTxQueueCapacity_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(err error) *TxStore_CheckTxQueueCapacity_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(err)
	return _c
}

func (_c *TxStore_CheckTxQueueCapacity_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, uint64, CHAIN_ID) error) *TxStore_CheckTxQueueCapacity_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Close() {
	_m.Called()
}

// TxStore_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type TxStore_Close_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Close() *TxStore_Close_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_Close_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("Close")}
}

func (_c *TxStore_Close_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func()) *TxStore_Close_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TxStore_Close_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return() *TxStore_Close_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return()
	return _c
}

func (_c *TxStore_Close_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func()) *TxStore_Close_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// CountTransactionsByState provides a mock function with given fields: ctx, state, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CountTransactionsByState(ctx context.Context, state txmgrtypes.TxState, chainID CHAIN_ID) (uint32, error) {
	ret := _m.Called(ctx, state, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CountTransactionsByState")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxState, CHAIN_ID) (uint32, error)); ok {
		return rf(ctx, state, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxState, CHAIN_ID) uint32); ok {
		r0 = rf(ctx, state, chainID)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, txmgrtypes.TxState, CHAIN_ID) error); ok {
		r1 = rf(ctx, state, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_CountTransactionsByState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountTransactionsByState'
type TxStore_CountTransactionsByState_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// CountTransactionsByState is a helper method to define mock.On call
//   - ctx context.Context
//   - state txmgrtypes.TxState
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CountTransactionsByState(ctx interface{}, state interface{}, chainID interface{}) *TxStore_CountTransactionsByState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_CountTransactionsByState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("CountTransactionsByState", ctx, state, chainID)}
}

func (_c *TxStore_CountTransactionsByState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, state txmgrtypes.TxState, chainID CHAIN_ID)) *TxStore_CountTransactionsByState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(txmgrtypes.TxState), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_CountTransactionsByState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(count uint32, err error) *TxStore_CountTransactionsByState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(count, err)
	return _c
}

func (_c *TxStore_CountTransactionsByState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, txmgrtypes.TxState, CHAIN_ID) (uint32, error)) *TxStore_CountTransactionsByState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// CountUnconfirmedTransactions provides a mock function with given fields: ctx, fromAddress, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CountUnconfirmedTransactions(ctx context.Context, fromAddress ADDR, chainID CHAIN_ID) (uint32, error) {
	ret := _m.Called(ctx, fromAddress, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CountUnconfirmedTransactions")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) (uint32, error)); ok {
		return rf(ctx, fromAddress, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) uint32); ok {
		r0 = rf(ctx, fromAddress, chainID)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, CHAIN_ID) error); ok {
		r1 = rf(ctx, fromAddress, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_CountUnconfirmedTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountUnconfirmedTransactions'
type TxStore_CountUnconfirmedTransactions_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// CountUnconfirmedTransactions is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress ADDR
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CountUnconfirmedTransactions(ctx interface{}, fromAddress interface{}, chainID interface{}) *TxStore_CountUnconfirmedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_CountUnconfirmedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("CountUnconfirmedTransactions", ctx, fromAddress, chainID)}
}

func (_c *TxStore_CountUnconfirmedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, fromAddress ADDR, chainID CHAIN_ID)) *TxStore_CountUnconfirmedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_CountUnconfirmedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(count uint32, err error) *TxStore_CountUnconfirmedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(count, err)
	return _c
}

func (_c *TxStore_CountUnconfirmedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, CHAIN_ID) (uint32, error)) *TxStore_CountUnconfirmedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// CountUnstartedTransactions provides a mock function with given fields: ctx, fromAddress, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CountUnstartedTransactions(ctx context.Context, fromAddress ADDR, chainID CHAIN_ID) (uint32, error) {
	ret := _m.Called(ctx, fromAddress, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CountUnstartedTransactions")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) (uint32, error)); ok {
		return rf(ctx, fromAddress, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) uint32); ok {
		r0 = rf(ctx, fromAddress, chainID)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, CHAIN_ID) error); ok {
		r1 = rf(ctx, fromAddress, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_CountUnstartedTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountUnstartedTransactions'
type TxStore_CountUnstartedTransactions_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// CountUnstartedTransactions is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress ADDR
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CountUnstartedTransactions(ctx interface{}, fromAddress interface{}, chainID interface{}) *TxStore_CountUnstartedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_CountUnstartedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("CountUnstartedTransactions", ctx, fromAddress, chainID)}
}

func (_c *TxStore_CountUnstartedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, fromAddress ADDR, chainID CHAIN_ID)) *TxStore_CountUnstartedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_CountUnstartedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(count uint32, err error) *TxStore_CountUnstartedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(count, err)
	return _c
}

func (_c *TxStore_CountUnstartedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, CHAIN_ID) (uint32, error)) *TxStore_CountUnstartedTransactions_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// CreateTransaction provides a mock function with given fields: ctx, txRequest, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CreateTransaction(ctx context.Context, txRequest txmgrtypes.TxRequest[ADDR, TX_HASH], chainID CHAIN_ID) (txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, txRequest, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CreateTransaction")
	}

	var r0 txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxRequest[ADDR, TX_HASH], CHAIN_ID) (txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, txRequest, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxRequest[ADDR, TX_HASH], CHAIN_ID) txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, txRequest, chainID)
	} else {
		r0 = ret.Get(0).(txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
	}

	if rf, ok := ret.Get(1).(func(context.Context, txmgrtypes.TxRequest[ADDR, TX_HASH], CHAIN_ID) error); ok {
		r1 = rf(ctx, txRequest, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_CreateTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTransaction'
type TxStore_CreateTransaction_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// CreateTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - txRequest txmgrtypes.TxRequest[ADDR,TX_HASH]
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) CreateTransaction(ctx interface{}, txRequest interface{}, chainID interface{}) *TxStore_CreateTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_CreateTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("CreateTransaction", ctx, txRequest, chainID)}
}

func (_c *TxStore_CreateTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, txRequest txmgrtypes.TxRequest[ADDR, TX_HASH], chainID CHAIN_ID)) *TxStore_CreateTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(txmgrtypes.TxRequest[ADDR, TX_HASH]), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_CreateTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(tx txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_CreateTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *TxStore_CreateTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, txmgrtypes.TxRequest[ADDR, TX_HASH], CHAIN_ID) (txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_CreateTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// DeleteInProgressAttempt provides a mock function with given fields: ctx, attempt
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) DeleteInProgressAttempt(ctx context.Context, attempt txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error {
	ret := _m.Called(ctx, attempt)

	if len(ret) == 0 {
		panic("no return value specified for DeleteInProgressAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error); ok {
		r0 = rf(ctx, attempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_DeleteInProgressAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteInProgressAttempt'
type TxStore_DeleteInProgressAttempt_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// DeleteInProgressAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - attempt txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) DeleteInProgressAttempt(ctx interface{}, attempt interface{}) *TxStore_DeleteInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_DeleteInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("DeleteInProgressAttempt", ctx, attempt)}
}

func (_c *TxStore_DeleteInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, attempt txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])) *TxStore_DeleteInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]))
	})
	return _c
}

func (_c *TxStore_DeleteInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_DeleteInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_DeleteInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error) *TxStore_DeleteInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindEarliestUnconfirmedBroadcastTime provides a mock function with given fields: ctx, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindEarliestUnconfirmedBroadcastTime(ctx context.Context, chainID CHAIN_ID) (null.Time, error) {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindEarliestUnconfirmedBroadcastTime")
	}

	var r0 null.Time
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID) (null.Time, error)); ok {
		return rf(ctx, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID) null.Time); ok {
		r0 = rf(ctx, chainID)
	} else {
		r0 = ret.Get(0).(null.Time)
	}

	if rf, ok := ret.Get(1).(func(context.Context, CHAIN_ID) error); ok {
		r1 = rf(ctx, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindEarliestUnconfirmedBroadcastTime_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindEarliestUnconfirmedBroadcastTime'
type TxStore_FindEarliestUnconfirmedBroadcastTime_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindEarliestUnconfirmedBroadcastTime is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindEarliestUnconfirmedBroadcastTime(ctx interface{}, chainID interface{}) *TxStore_FindEarliestUnconfirmedBroadcastTime_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindEarliestUnconfirmedBroadcastTime_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindEarliestUnconfirmedBroadcastTime", ctx, chainID)}
}

func (_c *TxStore_FindEarliestUnconfirmedBroadcastTime_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, chainID CHAIN_ID)) *TxStore_FindEarliestUnconfirmedBroadcastTime_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindEarliestUnconfirmedBroadcastTime_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 null.Time, _a1 error) *TxStore_FindEarliestUnconfirmedBroadcastTime_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TxStore_FindEarliestUnconfirmedBroadcastTime_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, CHAIN_ID) (null.Time, error)) *TxStore_FindEarliestUnconfirmedBroadcastTime_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindEarliestUnconfirmedTxAttemptBlock provides a mock function with given fields: ctx, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindEarliestUnconfirmedTxAttemptBlock(ctx context.Context, chainID CHAIN_ID) (null.Int, error) {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindEarliestUnconfirmedTxAttemptBlock")
	}

	var r0 null.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID) (null.Int, error)); ok {
		return rf(ctx, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID) null.Int); ok {
		r0 = rf(ctx, chainID)
	} else {
		r0 = ret.Get(0).(null.Int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, CHAIN_ID) error); ok {
		r1 = rf(ctx, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindEarliestUnconfirmedTxAttemptBlock'
type TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindEarliestUnconfirmedTxAttemptBlock is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindEarliestUnconfirmedTxAttemptBlock(ctx interface{}, chainID interface{}) *TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindEarliestUnconfirmedTxAttemptBlock", ctx, chainID)}
}

func (_c *TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, chainID CHAIN_ID)) *TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 null.Int, _a1 error) *TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, CHAIN_ID) (null.Int, error)) *TxStore_FindEarliestUnconfirmedTxAttemptBlock_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindLatestSequence provides a mock function with given fields: ctx, fromAddress, chainId
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindLatestSequence(ctx context.Context, fromAddress ADDR, chainId CHAIN_ID) (SEQ, error) {
	ret := _m.Called(ctx, fromAddress, chainId)

	if len(ret) == 0 {
		panic("no return value specified for FindLatestSequence")
	}

	var r0 SEQ
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) (SEQ, error)); ok {
		return rf(ctx, fromAddress, chainId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) SEQ); ok {
		r0 = rf(ctx, fromAddress, chainId)
	} else {
		r0 = ret.Get(0).(SEQ)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, CHAIN_ID) error); ok {
		r1 = rf(ctx, fromAddress, chainId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindLatestSequence_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindLatestSequence'
type TxStore_FindLatestSequence_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindLatestSequence is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress ADDR
//   - chainId CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindLatestSequence(ctx interface{}, fromAddress interface{}, chainId interface{}) *TxStore_FindLatestSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindLatestSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindLatestSequence", ctx, fromAddress, chainId)}
}

func (_c *TxStore_FindLatestSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, fromAddress ADDR, chainId CHAIN_ID)) *TxStore_FindLatestSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindLatestSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 SEQ, _a1 error) *TxStore_FindLatestSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TxStore_FindLatestSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, CHAIN_ID) (SEQ, error)) *TxStore_FindLatestSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindNextUnstartedTransactionFromAddress provides a mock function with given fields: ctx, fromAddress, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindNextUnstartedTransactionFromAddress(ctx context.Context, fromAddress ADDR, chainID CHAIN_ID) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, fromAddress, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindNextUnstartedTransactionFromAddress")
	}

	var r0 *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, fromAddress, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, fromAddress, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, CHAIN_ID) error); ok {
		r1 = rf(ctx, fromAddress, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindNextUnstartedTransactionFromAddress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindNextUnstartedTransactionFromAddress'
type TxStore_FindNextUnstartedTransactionFromAddress_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindNextUnstartedTransactionFromAddress is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress ADDR
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindNextUnstartedTransactionFromAddress(ctx interface{}, fromAddress interface{}, chainID interface{}) *TxStore_FindNextUnstartedTransactionFromAddress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindNextUnstartedTransactionFromAddress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindNextUnstartedTransactionFromAddress", ctx, fromAddress, chainID)}
}

func (_c *TxStore_FindNextUnstartedTransactionFromAddress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, fromAddress ADDR, chainID CHAIN_ID)) *TxStore_FindNextUnstartedTransactionFromAddress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindNextUnstartedTransactionFromAddress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], _a1 error) *TxStore_FindNextUnstartedTransactionFromAddress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TxStore_FindNextUnstartedTransactionFromAddress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, CHAIN_ID) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindNextUnstartedTransactionFromAddress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTransactionsConfirmedInBlockRange provides a mock function with given fields: ctx, highBlockNumber, lowBlockNumber, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTransactionsConfirmedInBlockRange(ctx context.Context, highBlockNumber int64, lowBlockNumber int64, chainID CHAIN_ID) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, highBlockNumber, lowBlockNumber, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTransactionsConfirmedInBlockRange")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, CHAIN_ID) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, highBlockNumber, lowBlockNumber, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, CHAIN_ID) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, highBlockNumber, lowBlockNumber, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64, CHAIN_ID) error); ok {
		r1 = rf(ctx, highBlockNumber, lowBlockNumber, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTransactionsConfirmedInBlockRange_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTransactionsConfirmedInBlockRange'
type TxStore_FindTransactionsConfirmedInBlockRange_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTransactionsConfirmedInBlockRange is a helper method to define mock.On call
//   - ctx context.Context
//   - highBlockNumber int64
//   - lowBlockNumber int64
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTransactionsConfirmedInBlockRange(ctx interface{}, highBlockNumber interface{}, lowBlockNumber interface{}, chainID interface{}) *TxStore_FindTransactionsConfirmedInBlockRange_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTransactionsConfirmedInBlockRange_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTransactionsConfirmedInBlockRange", ctx, highBlockNumber, lowBlockNumber, chainID)}
}

func (_c *TxStore_FindTransactionsConfirmedInBlockRange_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, highBlockNumber int64, lowBlockNumber int64, chainID CHAIN_ID)) *TxStore_FindTransactionsConfirmedInBlockRange_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindTransactionsConfirmedInBlockRange_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(etxs []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTransactionsConfirmedInBlockRange_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(etxs, err)
	return _c
}

func (_c *TxStore_FindTransactionsConfirmedInBlockRange_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, int64, int64, CHAIN_ID) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTransactionsConfirmedInBlockRange_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxAttemptsConfirmedMissingReceipt provides a mock function with given fields: ctx, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxAttemptsConfirmedMissingReceipt(ctx context.Context, chainID CHAIN_ID) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxAttemptsConfirmedMissingReceipt")
	}

	var r0 []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID) []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, CHAIN_ID) error); ok {
		r1 = rf(ctx, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxAttemptsConfirmedMissingReceipt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxAttemptsConfirmedMissingReceipt'
type TxStore_FindTxAttemptsConfirmedMissingReceipt_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxAttemptsConfirmedMissingReceipt is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxAttemptsConfirmedMissingReceipt(ctx interface{}, chainID interface{}) *TxStore_FindTxAttemptsConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxAttemptsConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxAttemptsConfirmedMissingReceipt", ctx, chainID)}
}

func (_c *TxStore_FindTxAttemptsConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, chainID CHAIN_ID)) *TxStore_FindTxAttemptsConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindTxAttemptsConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(attempts []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxAttemptsConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(attempts, err)
	return _c
}

func (_c *TxStore_FindTxAttemptsConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, CHAIN_ID) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxAttemptsConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxAttemptsRequiringReceiptFetch provides a mock function with given fields: ctx, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxAttemptsRequiringReceiptFetch(ctx context.Context, chainID CHAIN_ID) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxAttemptsRequiringReceiptFetch")
	}

	var r0 []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID) []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, CHAIN_ID) error); ok {
		r1 = rf(ctx, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxAttemptsRequiringReceiptFetch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxAttemptsRequiringReceiptFetch'
type TxStore_FindTxAttemptsRequiringReceiptFetch_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxAttemptsRequiringReceiptFetch is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxAttemptsRequiringReceiptFetch(ctx interface{}, chainID interface{}) *TxStore_FindTxAttemptsRequiringReceiptFetch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxAttemptsRequiringReceiptFetch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxAttemptsRequiringReceiptFetch", ctx, chainID)}
}

func (_c *TxStore_FindTxAttemptsRequiringReceiptFetch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, chainID CHAIN_ID)) *TxStore_FindTxAttemptsRequiringReceiptFetch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindTxAttemptsRequiringReceiptFetch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(attempts []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxAttemptsRequiringReceiptFetch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(attempts, err)
	return _c
}

func (_c *TxStore_FindTxAttemptsRequiringReceiptFetch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, CHAIN_ID) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxAttemptsRequiringReceiptFetch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxAttemptsRequiringResend provides a mock function with given fields: ctx, olderThan, maxInFlightTransactions, chainID, address
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxAttemptsRequiringResend(ctx context.Context, olderThan time.Time, maxInFlightTransactions uint32, chainID CHAIN_ID, address ADDR) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, olderThan, maxInFlightTransactions, chainID, address)

	if len(ret) == 0 {
		panic("no return value specified for FindTxAttemptsRequiringResend")
	}

	var r0 []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, uint32, CHAIN_ID, ADDR) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, olderThan, maxInFlightTransactions, chainID, address)
	}
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, uint32, CHAIN_ID, ADDR) []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, olderThan, maxInFlightTransactions, chainID, address)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, time.Time, uint32, CHAIN_ID, ADDR) error); ok {
		r1 = rf(ctx, olderThan, maxInFlightTransactions, chainID, address)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxAttemptsRequiringResend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxAttemptsRequiringResend'
type TxStore_FindTxAttemptsRequiringResend_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxAttemptsRequiringResend is a helper method to define mock.On call
//   - ctx context.Context
//   - olderThan time.Time
//   - maxInFlightTransactions uint32
//   - chainID CHAIN_ID
//   - address ADDR
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxAttemptsRequiringResend(ctx interface{}, olderThan interface{}, maxInFlightTransactions interface{}, chainID interface{}, address interface{}) *TxStore_FindTxAttemptsRequiringResend_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxAttemptsRequiringResend_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxAttemptsRequiringResend", ctx, olderThan, maxInFlightTransactions, chainID, address)}
}

func (_c *TxStore_FindTxAttemptsRequiringResend_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, olderThan time.Time, maxInFlightTransactions uint32, chainID CHAIN_ID, address ADDR)) *TxStore_FindTxAttemptsRequiringResend_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Time), args[2].(uint32), args[3].(CHAIN_ID), args[4].(ADDR))
	})
	return _c
}

func (_c *TxStore_FindTxAttemptsRequiringResend_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(attempts []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxAttemptsRequiringResend_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(attempts, err)
	return _c
}

func (_c *TxStore_FindTxAttemptsRequiringResend_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, time.Time, uint32, CHAIN_ID, ADDR) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxAttemptsRequiringResend_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxWithIdempotencyKey provides a mock function with given fields: ctx, idempotencyKey, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxWithIdempotencyKey(ctx context.Context, idempotencyKey string, chainID CHAIN_ID) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, idempotencyKey, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxWithIdempotencyKey")
	}

	var r0 *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, CHAIN_ID) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, idempotencyKey, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, CHAIN_ID) *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, idempotencyKey, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, CHAIN_ID) error); ok {
		r1 = rf(ctx, idempotencyKey, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxWithIdempotencyKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxWithIdempotencyKey'
type TxStore_FindTxWithIdempotencyKey_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxWithIdempotencyKey is a helper method to define mock.On call
//   - ctx context.Context
//   - idempotencyKey string
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxWithIdempotencyKey(ctx interface{}, idempotencyKey interface{}, chainID interface{}) *TxStore_FindTxWithIdempotencyKey_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxWithIdempotencyKey_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxWithIdempotencyKey", ctx, idempotencyKey, chainID)}
}

func (_c *TxStore_FindTxWithIdempotencyKey_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, idempotencyKey string, chainID CHAIN_ID)) *TxStore_FindTxWithIdempotencyKey_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindTxWithIdempotencyKey_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(tx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxWithIdempotencyKey_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *TxStore_FindTxWithIdempotencyKey_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, string, CHAIN_ID) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxWithIdempotencyKey_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxWithSequence provides a mock function with given fields: ctx, fromAddress, seq
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxWithSequence(ctx context.Context, fromAddress ADDR, seq SEQ) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, fromAddress, seq)

	if len(ret) == 0 {
		panic("no return value specified for FindTxWithSequence")
	}

	var r0 *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, SEQ) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, fromAddress, seq)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, SEQ) *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, fromAddress, seq)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, SEQ) error); ok {
		r1 = rf(ctx, fromAddress, seq)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxWithSequence_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxWithSequence'
type TxStore_FindTxWithSequence_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxWithSequence is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress ADDR
//   - seq SEQ
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxWithSequence(ctx interface{}, fromAddress interface{}, seq interface{}) *TxStore_FindTxWithSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxWithSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxWithSequence", ctx, fromAddress, seq)}
}

func (_c *TxStore_FindTxWithSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, fromAddress ADDR, seq SEQ)) *TxStore_FindTxWithSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(SEQ))
	})
	return _c
}

func (_c *TxStore_FindTxWithSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxWithSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(etx, err)
	return _c
}

func (_c *TxStore_FindTxWithSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, SEQ) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxWithSequence_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxesByMetaFieldAndStates provides a mock function with given fields: ctx, metaField, metaValue, states, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesByMetaFieldAndStates(ctx context.Context, metaField string, metaValue string, states []txmgrtypes.TxState, chainID *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, metaField, metaValue, states, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesByMetaFieldAndStates")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, metaField, metaValue, states, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, []txmgrtypes.TxState, *big.Int) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, metaField, metaValue, states, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, []txmgrtypes.TxState, *big.Int) error); ok {
		r1 = rf(ctx, metaField, metaValue, states, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxesByMetaFieldAndStates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesByMetaFieldAndStates'
type TxStore_FindTxesByMetaFieldAndStates_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxesByMetaFieldAndStates is a helper method to define mock.On call
//   - ctx context.Context
//   - metaField string
//   - metaValue string
//   - states []txmgrtypes.TxState
//   - chainID *big.Int
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesByMetaFieldAndStates(ctx interface{}, metaField interface{}, metaValue interface{}, states interface{}, chainID interface{}) *TxStore_FindTxesByMetaFieldAndStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxesByMetaFieldAndStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxesByMetaFieldAndStates", ctx, metaField, metaValue, states, chainID)}
}

func (_c *TxStore_FindTxesByMetaFieldAndStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, metaField string, metaValue string, states []txmgrtypes.TxState, chainID *big.Int)) *TxStore_FindTxesByMetaFieldAndStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].([]txmgrtypes.TxState), args[4].(*big.Int))
	})
	return _c
}

func (_c *TxStore_FindTxesByMetaFieldAndStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(tx []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxesByMetaFieldAndStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *TxStore_FindTxesByMetaFieldAndStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, string, string, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxesByMetaFieldAndStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxesPendingCallback provides a mock function with given fields: ctx, blockNum, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesPendingCallback(ctx context.Context, blockNum int64, chainID CHAIN_ID) ([]txmgrtypes.ReceiptPlus[R], error) {
	ret := _m.Called(ctx, blockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesPendingCallback")
	}

	var r0 []txmgrtypes.ReceiptPlus[R]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, CHAIN_ID) ([]txmgrtypes.ReceiptPlus[R], error)); ok {
		return rf(ctx, blockNum, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, CHAIN_ID) []txmgrtypes.ReceiptPlus[R]); ok {
		r0 = rf(ctx, blockNum, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]txmgrtypes.ReceiptPlus[R])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, CHAIN_ID) error); ok {
		r1 = rf(ctx, blockNum, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxesPendingCallback_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesPendingCallback'
type TxStore_FindTxesPendingCallback_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxesPendingCallback is a helper method to define mock.On call
//   - ctx context.Context
//   - blockNum int64
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesPendingCallback(ctx interface{}, blockNum interface{}, chainID interface{}) *TxStore_FindTxesPendingCallback_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxesPendingCallback_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxesPendingCallback", ctx, blockNum, chainID)}
}

func (_c *TxStore_FindTxesPendingCallback_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, blockNum int64, chainID CHAIN_ID)) *TxStore_FindTxesPendingCallback_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindTxesPendingCallback_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(receiptsPlus []txmgrtypes.ReceiptPlus[R], err error) *TxStore_FindTxesPendingCallback_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(receiptsPlus, err)
	return _c
}

func (_c *TxStore_FindTxesPendingCallback_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, int64, CHAIN_ID) ([]txmgrtypes.ReceiptPlus[R], error)) *TxStore_FindTxesPendingCallback_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxesWithAttemptsAndReceiptsByIdsAndState provides a mock function with given fields: ctx, ids, states, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesWithAttemptsAndReceiptsByIdsAndState(ctx context.Context, ids []int64, states []txmgrtypes.TxState, chainID *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, ids, states, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesWithAttemptsAndReceiptsByIdsAndState")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, ids, states, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64, []txmgrtypes.TxState, *big.Int) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, ids, states, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64, []txmgrtypes.TxState, *big.Int) error); ok {
		r1 = rf(ctx, ids, states, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesWithAttemptsAndReceiptsByIdsAndState'
type TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxesWithAttemptsAndReceiptsByIdsAndState is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
//   - states []txmgrtypes.TxState
//   - chainID *big.Int
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesWithAttemptsAndReceiptsByIdsAndState(ctx interface{}, ids interface{}, states interface{}, chainID interface{}) *TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxesWithAttemptsAndReceiptsByIdsAndState", ctx, ids, states, chainID)}
}

func (_c *TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, ids []int64, states []txmgrtypes.TxState, chainID *big.Int)) *TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64), args[2].([]txmgrtypes.TxState), args[3].(*big.Int))
	})
	return _c
}

func (_c *TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(tx []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, []int64, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxesWithMetaFieldByReceiptBlockNum provides a mock function with given fields: ctx, metaField, blockNum, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesWithMetaFieldByReceiptBlockNum(ctx context.Context, metaField string, blockNum int64, chainID *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, metaField, blockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesWithMetaFieldByReceiptBlockNum")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, metaField, blockNum, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, *big.Int) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, metaField, blockNum, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int64, *big.Int) error); ok {
		r1 = rf(ctx, metaField, blockNum, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesWithMetaFieldByReceiptBlockNum'
type TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxesWithMetaFieldByReceiptBlockNum is a helper method to define mock.On call
//   - ctx context.Context
//   - metaField string
//   - blockNum int64
//   - chainID *big.Int
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesWithMetaFieldByReceiptBlockNum(ctx interface{}, metaField interface{}, blockNum interface{}, chainID interface{}) *TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxesWithMetaFieldByReceiptBlockNum", ctx, metaField, blockNum, chainID)}
}

func (_c *TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, metaField string, blockNum int64, chainID *big.Int)) *TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(*big.Int))
	})
	return _c
}

func (_c *TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(tx []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, string, int64, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxesWithMetaFieldByStates provides a mock function with given fields: ctx, metaField, states, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesWithMetaFieldByStates(ctx context.Context, metaField string, states []txmgrtypes.TxState, chainID *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, metaField, states, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesWithMetaFieldByStates")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, metaField, states, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, []txmgrtypes.TxState, *big.Int) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, metaField, states, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, []txmgrtypes.TxState, *big.Int) error); ok {
		r1 = rf(ctx, metaField, states, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxesWithMetaFieldByStates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesWithMetaFieldByStates'
type TxStore_FindTxesWithMetaFieldByStates_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxesWithMetaFieldByStates is a helper method to define mock.On call
//   - ctx context.Context
//   - metaField string
//   - states []txmgrtypes.TxState
//   - chainID *big.Int
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxesWithMetaFieldByStates(ctx interface{}, metaField interface{}, states interface{}, chainID interface{}) *TxStore_FindTxesWithMetaFieldByStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxesWithMetaFieldByStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxesWithMetaFieldByStates", ctx, metaField, states, chainID)}
}

func (_c *TxStore_FindTxesWithMetaFieldByStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, metaField string, states []txmgrtypes.TxState, chainID *big.Int)) *TxStore_FindTxesWithMetaFieldByStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]txmgrtypes.TxState), args[3].(*big.Int))
	})
	return _c
}

func (_c *TxStore_FindTxesWithMetaFieldByStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(tx []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxesWithMetaFieldByStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *TxStore_FindTxesWithMetaFieldByStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, string, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxesWithMetaFieldByStates_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxsRequiringGasBump provides a mock function with given fields: ctx, address, blockNum, gasBumpThreshold, depth, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxsRequiringGasBump(ctx context.Context, address ADDR, blockNum int64, gasBumpThreshold int64, depth int64, chainID CHAIN_ID) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, address, blockNum, gasBumpThreshold, depth, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxsRequiringGasBump")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, int64, int64, int64, CHAIN_ID) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, address, blockNum, gasBumpThreshold, depth, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, int64, int64, int64, CHAIN_ID) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, address, blockNum, gasBumpThreshold, depth, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, int64, int64, int64, CHAIN_ID) error); ok {
		r1 = rf(ctx, address, blockNum, gasBumpThreshold, depth, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxsRequiringGasBump_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxsRequiringGasBump'
type TxStore_FindTxsRequiringGasBump_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxsRequiringGasBump is a helper method to define mock.On call
//   - ctx context.Context
//   - address ADDR
//   - blockNum int64
//   - gasBumpThreshold int64
//   - depth int64
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxsRequiringGasBump(ctx interface{}, address interface{}, blockNum interface{}, gasBumpThreshold interface{}, depth interface{}, chainID interface{}) *TxStore_FindTxsRequiringGasBump_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxsRequiringGasBump_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxsRequiringGasBump", ctx, address, blockNum, gasBumpThreshold, depth, chainID)}
}

func (_c *TxStore_FindTxsRequiringGasBump_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, address ADDR, blockNum int64, gasBumpThreshold int64, depth int64, chainID CHAIN_ID)) *TxStore_FindTxsRequiringGasBump_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(int64), args[3].(int64), args[4].(int64), args[5].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindTxsRequiringGasBump_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(etxs []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxsRequiringGasBump_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(etxs, err)
	return _c
}

func (_c *TxStore_FindTxsRequiringGasBump_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, int64, int64, int64, CHAIN_ID) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxsRequiringGasBump_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxsRequiringResubmissionDueToInsufficientFunds provides a mock function with given fields: ctx, address, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxsRequiringResubmissionDueToInsufficientFunds(ctx context.Context, address ADDR, chainID CHAIN_ID) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, address, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxsRequiringResubmissionDueToInsufficientFunds")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, address, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, address, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, CHAIN_ID) error); ok {
		r1 = rf(ctx, address, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxsRequiringResubmissionDueToInsufficientFunds'
type TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxsRequiringResubmissionDueToInsufficientFunds is a helper method to define mock.On call
//   - ctx context.Context
//   - address ADDR
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) FindTxsRequiringResubmissionDueToInsufficientFunds(ctx interface{}, address interface{}, chainID interface{}) *TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("FindTxsRequiringResubmissionDueToInsufficientFunds", ctx, address, chainID)}
}

func (_c *TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, address ADDR, chainID CHAIN_ID)) *TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(etxs []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(etxs, err)
	return _c
}

func (_c *TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, CHAIN_ID) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// GetAbandonedTransactionsByBatch provides a mock function with given fields: ctx, chainID, enabledAddrs, offset, limit
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) GetAbandonedTransactionsByBatch(ctx context.Context, chainID CHAIN_ID, enabledAddrs []ADDR, offset uint, limit uint) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, chainID, enabledAddrs, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetAbandonedTransactionsByBatch")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID, []ADDR, uint, uint) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, chainID, enabledAddrs, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID, []ADDR, uint, uint) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, chainID, enabledAddrs, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, CHAIN_ID, []ADDR, uint, uint) error); ok {
		r1 = rf(ctx, chainID, enabledAddrs, offset, limit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_GetAbandonedTransactionsByBatch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAbandonedTransactionsByBatch'
type TxStore_GetAbandonedTransactionsByBatch_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// GetAbandonedTransactionsByBatch is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID CHAIN_ID
//   - enabledAddrs []ADDR
//   - offset uint
//   - limit uint
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) GetAbandonedTransactionsByBatch(ctx interface{}, chainID interface{}, enabledAddrs interface{}, offset interface{}, limit interface{}) *TxStore_GetAbandonedTransactionsByBatch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_GetAbandonedTransactionsByBatch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("GetAbandonedTransactionsByBatch", ctx, chainID, enabledAddrs, offset, limit)}
}

func (_c *TxStore_GetAbandonedTransactionsByBatch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, chainID CHAIN_ID, enabledAddrs []ADDR, offset uint, limit uint)) *TxStore_GetAbandonedTransactionsByBatch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CHAIN_ID), args[2].([]ADDR), args[3].(uint), args[4].(uint))
	})
	return _c
}

func (_c *TxStore_GetAbandonedTransactionsByBatch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(txs []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_GetAbandonedTransactionsByBatch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(txs, err)
	return _c
}

func (_c *TxStore_GetAbandonedTransactionsByBatch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, CHAIN_ID, []ADDR, uint, uint) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_GetAbandonedTransactionsByBatch_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// GetInProgressTxAttempts provides a mock function with given fields: ctx, address, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) GetInProgressTxAttempts(ctx context.Context, address ADDR, chainID CHAIN_ID) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, address, chainID)

	if len(ret) == 0 {
		panic("no return value specified for GetInProgressTxAttempts")
	}

	var r0 []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, address, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, address, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, CHAIN_ID) error); ok {
		r1 = rf(ctx, address, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_GetInProgressTxAttempts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetInProgressTxAttempts'
type TxStore_GetInProgressTxAttempts_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// GetInProgressTxAttempts is a helper method to define mock.On call
//   - ctx context.Context
//   - address ADDR
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) GetInProgressTxAttempts(ctx interface{}, address interface{}, chainID interface{}) *TxStore_GetInProgressTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_GetInProgressTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("GetInProgressTxAttempts", ctx, address, chainID)}
}

func (_c *TxStore_GetInProgressTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, address ADDR, chainID CHAIN_ID)) *TxStore_GetInProgressTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_GetInProgressTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(attempts []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_GetInProgressTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(attempts, err)
	return _c
}

func (_c *TxStore_GetInProgressTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, CHAIN_ID) ([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_GetInProgressTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// GetTxByID provides a mock function with given fields: ctx, id
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) GetTxByID(ctx context.Context, id int64) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetTxByID")
	}

	var r0 *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_GetTxByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTxByID'
type TxStore_GetTxByID_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// GetTxByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) GetTxByID(ctx interface{}, id interface{}) *TxStore_GetTxByID_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_GetTxByID_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("GetTxByID", ctx, id)}
}

func (_c *TxStore_GetTxByID_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, id int64)) *TxStore_GetTxByID_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *TxStore_GetTxByID_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(tx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_GetTxByID_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *TxStore_GetTxByID_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, int64) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_GetTxByID_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// GetTxInProgress provides a mock function with given fields: ctx, fromAddress
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) GetTxInProgress(ctx context.Context, fromAddress ADDR) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, fromAddress)

	if len(ret) == 0 {
		panic("no return value specified for GetTxInProgress")
	}

	var r0 *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, fromAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR) *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, fromAddress)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR) error); ok {
		r1 = rf(ctx, fromAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_GetTxInProgress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTxInProgress'
type TxStore_GetTxInProgress_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// GetTxInProgress is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress ADDR
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) GetTxInProgress(ctx interface{}, fromAddress interface{}) *TxStore_GetTxInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_GetTxInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("GetTxInProgress", ctx, fromAddress)}
}

func (_c *TxStore_GetTxInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, fromAddress ADDR)) *TxStore_GetTxInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR))
	})
	return _c
}

func (_c *TxStore_GetTxInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxStore_GetTxInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(etx, err)
	return _c
}

func (_c *TxStore_GetTxInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR) (*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxStore_GetTxInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// HasInProgressTransaction provides a mock function with given fields: ctx, account, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) HasInProgressTransaction(ctx context.Context, account ADDR, chainID CHAIN_ID) (bool, error) {
	ret := _m.Called(ctx, account, chainID)

	if len(ret) == 0 {
		panic("no return value specified for HasInProgressTransaction")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) (bool, error)); ok {
		return rf(ctx, account, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, CHAIN_ID) bool); ok {
		r0 = rf(ctx, account, chainID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, CHAIN_ID) error); ok {
		r1 = rf(ctx, account, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_HasInProgressTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HasInProgressTransaction'
type TxStore_HasInProgressTransaction_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// HasInProgressTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - account ADDR
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) HasInProgressTransaction(ctx interface{}, account interface{}, chainID interface{}) *TxStore_HasInProgressTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_HasInProgressTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("HasInProgressTransaction", ctx, account, chainID)}
}

func (_c *TxStore_HasInProgressTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, account ADDR, chainID CHAIN_ID)) *TxStore_HasInProgressTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_HasInProgressTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(exists bool, err error) *TxStore_HasInProgressTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(exists, err)
	return _c
}

func (_c *TxStore_HasInProgressTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, CHAIN_ID) (bool, error)) *TxStore_HasInProgressTransaction_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// LoadTxAttempts provides a mock function with given fields: ctx, etx
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) LoadTxAttempts(ctx context.Context, etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error {
	ret := _m.Called(ctx, etx)

	if len(ret) == 0 {
		panic("no return value specified for LoadTxAttempts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error); ok {
		r0 = rf(ctx, etx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_LoadTxAttempts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LoadTxAttempts'
type TxStore_LoadTxAttempts_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// LoadTxAttempts is a helper method to define mock.On call
//   - ctx context.Context
//   - etx *txmgrtypes.Tx[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) LoadTxAttempts(ctx interface{}, etx interface{}) *TxStore_LoadTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_LoadTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("LoadTxAttempts", ctx, etx)}
}

func (_c *TxStore_LoadTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])) *TxStore_LoadTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]))
	})
	return _c
}

func (_c *TxStore_LoadTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_LoadTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_LoadTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error) *TxStore_LoadTxAttempts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// MarkAllConfirmedMissingReceipt provides a mock function with given fields: ctx, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) MarkAllConfirmedMissingReceipt(ctx context.Context, chainID CHAIN_ID) error {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for MarkAllConfirmedMissingReceipt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID) error); ok {
		r0 = rf(ctx, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_MarkAllConfirmedMissingReceipt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkAllConfirmedMissingReceipt'
type TxStore_MarkAllConfirmedMissingReceipt_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// MarkAllConfirmedMissingReceipt is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) MarkAllConfirmedMissingReceipt(ctx interface{}, chainID interface{}) *TxStore_MarkAllConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_MarkAllConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("MarkAllConfirmedMissingReceipt", ctx, chainID)}
}

func (_c *TxStore_MarkAllConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, chainID CHAIN_ID)) *TxStore_MarkAllConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_MarkAllConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(err error) *TxStore_MarkAllConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(err)
	return _c
}

func (_c *TxStore_MarkAllConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, CHAIN_ID) error) *TxStore_MarkAllConfirmedMissingReceipt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// MarkOldTxesMissingReceiptAsErrored provides a mock function with given fields: ctx, blockNum, latestFinalizedBlockNum, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) MarkOldTxesMissingReceiptAsErrored(ctx context.Context, blockNum int64, latestFinalizedBlockNum int64, chainID CHAIN_ID) error {
	ret := _m.Called(ctx, blockNum, latestFinalizedBlockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for MarkOldTxesMissingReceiptAsErrored")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, CHAIN_ID) error); ok {
		r0 = rf(ctx, blockNum, latestFinalizedBlockNum, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_MarkOldTxesMissingReceiptAsErrored_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkOldTxesMissingReceiptAsErrored'
type TxStore_MarkOldTxesMissingReceiptAsErrored_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// MarkOldTxesMissingReceiptAsErrored is a helper method to define mock.On call
//   - ctx context.Context
//   - blockNum int64
//   - latestFinalizedBlockNum int64
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) MarkOldTxesMissingReceiptAsErrored(ctx interface{}, blockNum interface{}, latestFinalizedBlockNum interface{}, chainID interface{}) *TxStore_MarkOldTxesMissingReceiptAsErrored_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_MarkOldTxesMissingReceiptAsErrored_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("MarkOldTxesMissingReceiptAsErrored", ctx, blockNum, latestFinalizedBlockNum, chainID)}
}

func (_c *TxStore_MarkOldTxesMissingReceiptAsErrored_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, blockNum int64, latestFinalizedBlockNum int64, chainID CHAIN_ID)) *TxStore_MarkOldTxesMissingReceiptAsErrored_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_MarkOldTxesMissingReceiptAsErrored_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_MarkOldTxesMissingReceiptAsErrored_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_MarkOldTxesMissingReceiptAsErrored_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, int64, int64, CHAIN_ID) error) *TxStore_MarkOldTxesMissingReceiptAsErrored_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// PreloadTxes provides a mock function with given fields: ctx, attempts
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) PreloadTxes(ctx context.Context, attempts []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error {
	ret := _m.Called(ctx, attempts)

	if len(ret) == 0 {
		panic("no return value specified for PreloadTxes")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error); ok {
		r0 = rf(ctx, attempts)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_PreloadTxes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreloadTxes'
type TxStore_PreloadTxes_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// PreloadTxes is a helper method to define mock.On call
//   - ctx context.Context
//   - attempts []txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) PreloadTxes(ctx interface{}, attempts interface{}) *TxStore_PreloadTxes_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_PreloadTxes_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("PreloadTxes", ctx, attempts)}
}

func (_c *TxStore_PreloadTxes_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, attempts []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])) *TxStore_PreloadTxes_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]))
	})
	return _c
}

func (_c *TxStore_PreloadTxes_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_PreloadTxes_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_PreloadTxes_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, []txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error) *TxStore_PreloadTxes_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// PruneUnstartedTxQueue provides a mock function with given fields: ctx, queueSize, subject
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) PruneUnstartedTxQueue(ctx context.Context, queueSize uint32, subject uuid.UUID) ([]int64, error) {
	ret := _m.Called(ctx, queueSize, subject)

	if len(ret) == 0 {
		panic("no return value specified for PruneUnstartedTxQueue")
	}

	var r0 []int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint32, uuid.UUID) ([]int64, error)); ok {
		return rf(ctx, queueSize, subject)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint32, uuid.UUID) []int64); ok {
		r0 = rf(ctx, queueSize, subject)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint32, uuid.UUID) error); ok {
		r1 = rf(ctx, queueSize, subject)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxStore_PruneUnstartedTxQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PruneUnstartedTxQueue'
type TxStore_PruneUnstartedTxQueue_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// PruneUnstartedTxQueue is a helper method to define mock.On call
//   - ctx context.Context
//   - queueSize uint32
//   - subject uuid.UUID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) PruneUnstartedTxQueue(ctx interface{}, queueSize interface{}, subject interface{}) *TxStore_PruneUnstartedTxQueue_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_PruneUnstartedTxQueue_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("PruneUnstartedTxQueue", ctx, queueSize, subject)}
}

func (_c *TxStore_PruneUnstartedTxQueue_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, queueSize uint32, subject uuid.UUID)) *TxStore_PruneUnstartedTxQueue_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint32), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *TxStore_PruneUnstartedTxQueue_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(ids []int64, err error) *TxStore_PruneUnstartedTxQueue_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(ids, err)
	return _c
}

func (_c *TxStore_PruneUnstartedTxQueue_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, uint32, uuid.UUID) ([]int64, error)) *TxStore_PruneUnstartedTxQueue_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// ReapTxHistory provides a mock function with given fields: ctx, timeThreshold, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) ReapTxHistory(ctx context.Context, timeThreshold time.Time, chainID CHAIN_ID) error {
	ret := _m.Called(ctx, timeThreshold, chainID)

	if len(ret) == 0 {
		panic("no return value specified for ReapTxHistory")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, CHAIN_ID) error); ok {
		r0 = rf(ctx, timeThreshold, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_ReapTxHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReapTxHistory'
type TxStore_ReapTxHistory_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// ReapTxHistory is a helper method to define mock.On call
//   - ctx context.Context
//   - timeThreshold time.Time
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) ReapTxHistory(ctx interface{}, timeThreshold interface{}, chainID interface{}) *TxStore_ReapTxHistory_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_ReapTxHistory_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("ReapTxHistory", ctx, timeThreshold, chainID)}
}

func (_c *TxStore_ReapTxHistory_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, timeThreshold time.Time, chainID CHAIN_ID)) *TxStore_ReapTxHistory_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Time), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_ReapTxHistory_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_ReapTxHistory_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_ReapTxHistory_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, time.Time, CHAIN_ID) error) *TxStore_ReapTxHistory_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// SaveConfirmedMissingReceiptAttempt provides a mock function with given fields: ctx, timeout, attempt, broadcastAt
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveConfirmedMissingReceiptAttempt(ctx context.Context, timeout time.Duration, attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], broadcastAt time.Time) error {
	ret := _m.Called(ctx, timeout, attempt, broadcastAt)

	if len(ret) == 0 {
		panic("no return value specified for SaveConfirmedMissingReceiptAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Duration, *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], time.Time) error); ok {
		r0 = rf(ctx, timeout, attempt, broadcastAt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_SaveConfirmedMissingReceiptAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveConfirmedMissingReceiptAttempt'
type TxStore_SaveConfirmedMissingReceiptAttempt_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// SaveConfirmedMissingReceiptAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - timeout time.Duration
//   - attempt *txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
//   - broadcastAt time.Time
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveConfirmedMissingReceiptAttempt(ctx interface{}, timeout interface{}, attempt interface{}, broadcastAt interface{}) *TxStore_SaveConfirmedMissingReceiptAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_SaveConfirmedMissingReceiptAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("SaveConfirmedMissingReceiptAttempt", ctx, timeout, attempt, broadcastAt)}
}

func (_c *TxStore_SaveConfirmedMissingReceiptAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, timeout time.Duration, attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], broadcastAt time.Time)) *TxStore_SaveConfirmedMissingReceiptAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Duration), args[2].(*txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]), args[3].(time.Time))
	})
	return _c
}

func (_c *TxStore_SaveConfirmedMissingReceiptAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_SaveConfirmedMissingReceiptAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_SaveConfirmedMissingReceiptAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, time.Duration, *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], time.Time) error) *TxStore_SaveConfirmedMissingReceiptAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// SaveFetchedReceipts provides a mock function with given fields: ctx, r, state, errorMsg, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveFetchedReceipts(ctx context.Context, r []R, state txmgrtypes.TxState, errorMsg *string, chainID CHAIN_ID) error {
	ret := _m.Called(ctx, r, state, errorMsg, chainID)

	if len(ret) == 0 {
		panic("no return value specified for SaveFetchedReceipts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []R, txmgrtypes.TxState, *string, CHAIN_ID) error); ok {
		r0 = rf(ctx, r, state, errorMsg, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_SaveFetchedReceipts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveFetchedReceipts'
type TxStore_SaveFetchedReceipts_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// SaveFetchedReceipts is a helper method to define mock.On call
//   - ctx context.Context
//   - r []R
//   - state txmgrtypes.TxState
//   - errorMsg *string
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveFetchedReceipts(ctx interface{}, r interface{}, state interface{}, errorMsg interface{}, chainID interface{}) *TxStore_SaveFetchedReceipts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_SaveFetchedReceipts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("SaveFetchedReceipts", ctx, r, state, errorMsg, chainID)}
}

func (_c *TxStore_SaveFetchedReceipts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, r []R, state txmgrtypes.TxState, errorMsg *string, chainID CHAIN_ID)) *TxStore_SaveFetchedReceipts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]R), args[2].(txmgrtypes.TxState), args[3].(*string), args[4].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_SaveFetchedReceipts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_SaveFetchedReceipts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_SaveFetchedReceipts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, []R, txmgrtypes.TxState, *string, CHAIN_ID) error) *TxStore_SaveFetchedReceipts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// SaveInProgressAttempt provides a mock function with given fields: ctx, attempt
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveInProgressAttempt(ctx context.Context, attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error {
	ret := _m.Called(ctx, attempt)

	if len(ret) == 0 {
		panic("no return value specified for SaveInProgressAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error); ok {
		r0 = rf(ctx, attempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_SaveInProgressAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveInProgressAttempt'
type TxStore_SaveInProgressAttempt_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// SaveInProgressAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - attempt *txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveInProgressAttempt(ctx interface{}, attempt interface{}) *TxStore_SaveInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_SaveInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("SaveInProgressAttempt", ctx, attempt)}
}

func (_c *TxStore_SaveInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])) *TxStore_SaveInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]))
	})
	return _c
}

func (_c *TxStore_SaveInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_SaveInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_SaveInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error) *TxStore_SaveInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// SaveInsufficientFundsAttempt provides a mock function with given fields: ctx, timeout, attempt, broadcastAt
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveInsufficientFundsAttempt(ctx context.Context, timeout time.Duration, attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], broadcastAt time.Time) error {
	ret := _m.Called(ctx, timeout, attempt, broadcastAt)

	if len(ret) == 0 {
		panic("no return value specified for SaveInsufficientFundsAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Duration, *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], time.Time) error); ok {
		r0 = rf(ctx, timeout, attempt, broadcastAt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_SaveInsufficientFundsAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveInsufficientFundsAttempt'
type TxStore_SaveInsufficientFundsAttempt_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// SaveInsufficientFundsAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - timeout time.Duration
//   - attempt *txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
//   - broadcastAt time.Time
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveInsufficientFundsAttempt(ctx interface{}, timeout interface{}, attempt interface{}, broadcastAt interface{}) *TxStore_SaveInsufficientFundsAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_SaveInsufficientFundsAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("SaveInsufficientFundsAttempt", ctx, timeout, attempt, broadcastAt)}
}

func (_c *TxStore_SaveInsufficientFundsAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, timeout time.Duration, attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], broadcastAt time.Time)) *TxStore_SaveInsufficientFundsAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Duration), args[2].(*txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]), args[3].(time.Time))
	})
	return _c
}

func (_c *TxStore_SaveInsufficientFundsAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_SaveInsufficientFundsAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_SaveInsufficientFundsAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, time.Duration, *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], time.Time) error) *TxStore_SaveInsufficientFundsAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// SaveReplacementInProgressAttempt provides a mock function with given fields: ctx, oldAttempt, replacementAttempt
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveReplacementInProgressAttempt(ctx context.Context, oldAttempt txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], replacementAttempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error {
	ret := _m.Called(ctx, oldAttempt, replacementAttempt)

	if len(ret) == 0 {
		panic("no return value specified for SaveReplacementInProgressAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error); ok {
		r0 = rf(ctx, oldAttempt, replacementAttempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_SaveReplacementInProgressAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveReplacementInProgressAttempt'
type TxStore_SaveReplacementInProgressAttempt_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// SaveReplacementInProgressAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - oldAttempt txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
//   - replacementAttempt *txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveReplacementInProgressAttempt(ctx interface{}, oldAttempt interface{}, replacementAttempt interface{}) *TxStore_SaveReplacementInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_SaveReplacementInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("SaveReplacementInProgressAttempt", ctx, oldAttempt, replacementAttempt)}
}

func (_c *TxStore_SaveReplacementInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, oldAttempt txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], replacementAttempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])) *TxStore_SaveReplacementInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]), args[2].(*txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]))
	})
	return _c
}

func (_c *TxStore_SaveReplacementInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_SaveReplacementInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_SaveReplacementInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error) *TxStore_SaveReplacementInProgressAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// SaveSentAttempt provides a mock function with given fields: ctx, timeout, attempt, broadcastAt
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveSentAttempt(ctx context.Context, timeout time.Duration, attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], broadcastAt time.Time) error {
	ret := _m.Called(ctx, timeout, attempt, broadcastAt)

	if len(ret) == 0 {
		panic("no return value specified for SaveSentAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Duration, *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], time.Time) error); ok {
		r0 = rf(ctx, timeout, attempt, broadcastAt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_SaveSentAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveSentAttempt'
type TxStore_SaveSentAttempt_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// SaveSentAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - timeout time.Duration
//   - attempt *txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
//   - broadcastAt time.Time
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SaveSentAttempt(ctx interface{}, timeout interface{}, attempt interface{}, broadcastAt interface{}) *TxStore_SaveSentAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_SaveSentAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("SaveSentAttempt", ctx, timeout, attempt, broadcastAt)}
}

func (_c *TxStore_SaveSentAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, timeout time.Duration, attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], broadcastAt time.Time)) *TxStore_SaveSentAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Duration), args[2].(*txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]), args[3].(time.Time))
	})
	return _c
}

func (_c *TxStore_SaveSentAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_SaveSentAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_SaveSentAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, time.Duration, *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], time.Time) error) *TxStore_SaveSentAttempt_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// SetBroadcastBeforeBlockNum provides a mock function with given fields: ctx, blockNum, chainID
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SetBroadcastBeforeBlockNum(ctx context.Context, blockNum int64, chainID CHAIN_ID) error {
	ret := _m.Called(ctx, blockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for SetBroadcastBeforeBlockNum")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, CHAIN_ID) error); ok {
		r0 = rf(ctx, blockNum, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_SetBroadcastBeforeBlockNum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetBroadcastBeforeBlockNum'
type TxStore_SetBroadcastBeforeBlockNum_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// SetBroadcastBeforeBlockNum is a helper method to define mock.On call
//   - ctx context.Context
//   - blockNum int64
//   - chainID CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) SetBroadcastBeforeBlockNum(ctx interface{}, blockNum interface{}, chainID interface{}) *TxStore_SetBroadcastBeforeBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_SetBroadcastBeforeBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("SetBroadcastBeforeBlockNum", ctx, blockNum, chainID)}
}

func (_c *TxStore_SetBroadcastBeforeBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, blockNum int64, chainID CHAIN_ID)) *TxStore_SetBroadcastBeforeBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_SetBroadcastBeforeBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_SetBroadcastBeforeBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_SetBroadcastBeforeBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, int64, CHAIN_ID) error) *TxStore_SetBroadcastBeforeBlockNum_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// UpdateBroadcastAts provides a mock function with given fields: ctx, now, etxIDs
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateBroadcastAts(ctx context.Context, now time.Time, etxIDs []int64) error {
	ret := _m.Called(ctx, now, etxIDs)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBroadcastAts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, []int64) error); ok {
		r0 = rf(ctx, now, etxIDs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_UpdateBroadcastAts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateBroadcastAts'
type TxStore_UpdateBroadcastAts_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// UpdateBroadcastAts is a helper method to define mock.On call
//   - ctx context.Context
//   - now time.Time
//   - etxIDs []int64
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateBroadcastAts(ctx interface{}, now interface{}, etxIDs interface{}) *TxStore_UpdateBroadcastAts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_UpdateBroadcastAts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("UpdateBroadcastAts", ctx, now, etxIDs)}
}

func (_c *TxStore_UpdateBroadcastAts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, now time.Time, etxIDs []int64)) *TxStore_UpdateBroadcastAts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Time), args[2].([]int64))
	})
	return _c
}

func (_c *TxStore_UpdateBroadcastAts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_UpdateBroadcastAts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_UpdateBroadcastAts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, time.Time, []int64) error) *TxStore_UpdateBroadcastAts_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// UpdateTxAttemptInProgressToBroadcast provides a mock function with given fields: ctx, etx, attempt, NewAttemptState
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxAttemptInProgressToBroadcast(ctx context.Context, etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], attempt txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], NewAttemptState txmgrtypes.TxAttemptState) error {
	ret := _m.Called(ctx, etx, attempt, NewAttemptState)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxAttemptInProgressToBroadcast")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], txmgrtypes.TxAttemptState) error); ok {
		r0 = rf(ctx, etx, attempt, NewAttemptState)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_UpdateTxAttemptInProgressToBroadcast_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxAttemptInProgressToBroadcast'
type TxStore_UpdateTxAttemptInProgressToBroadcast_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// UpdateTxAttemptInProgressToBroadcast is a helper method to define mock.On call
//   - ctx context.Context
//   - etx *txmgrtypes.Tx[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
//   - attempt txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
//   - NewAttemptState txmgrtypes.TxAttemptState
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxAttemptInProgressToBroadcast(ctx interface{}, etx interface{}, attempt interface{}, NewAttemptState interface{}) *TxStore_UpdateTxAttemptInProgressToBroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_UpdateTxAttemptInProgressToBroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("UpdateTxAttemptInProgressToBroadcast", ctx, etx, attempt, NewAttemptState)}
}

func (_c *TxStore_UpdateTxAttemptInProgressToBroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], attempt txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], NewAttemptState txmgrtypes.TxAttemptState)) *TxStore_UpdateTxAttemptInProgressToBroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]), args[2].(txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]), args[3].(txmgrtypes.TxAttemptState))
	})
	return _c
}

func (_c *TxStore_UpdateTxAttemptInProgressToBroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_UpdateTxAttemptInProgressToBroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_UpdateTxAttemptInProgressToBroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], txmgrtypes.TxAttemptState) error) *TxStore_UpdateTxAttemptInProgressToBroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// UpdateTxCallbackCompleted provides a mock function with given fields: ctx, pipelineTaskRunRid, chainId
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxCallbackCompleted(ctx context.Context, pipelineTaskRunRid uuid.UUID, chainId CHAIN_ID) error {
	ret := _m.Called(ctx, pipelineTaskRunRid, chainId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxCallbackCompleted")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, CHAIN_ID) error); ok {
		r0 = rf(ctx, pipelineTaskRunRid, chainId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_UpdateTxCallbackCompleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxCallbackCompleted'
type TxStore_UpdateTxCallbackCompleted_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// UpdateTxCallbackCompleted is a helper method to define mock.On call
//   - ctx context.Context
//   - pipelineTaskRunRid uuid.UUID
//   - chainId CHAIN_ID
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxCallbackCompleted(ctx interface{}, pipelineTaskRunRid interface{}, chainId interface{}) *TxStore_UpdateTxCallbackCompleted_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_UpdateTxCallbackCompleted_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("UpdateTxCallbackCompleted", ctx, pipelineTaskRunRid, chainId)}
}

func (_c *TxStore_UpdateTxCallbackCompleted_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, pipelineTaskRunRid uuid.UUID, chainId CHAIN_ID)) *TxStore_UpdateTxCallbackCompleted_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(CHAIN_ID))
	})
	return _c
}

func (_c *TxStore_UpdateTxCallbackCompleted_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_UpdateTxCallbackCompleted_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_UpdateTxCallbackCompleted_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, uuid.UUID, CHAIN_ID) error) *TxStore_UpdateTxCallbackCompleted_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// UpdateTxFatalError provides a mock function with given fields: ctx, etx
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxFatalError(ctx context.Context, etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error {
	ret := _m.Called(ctx, etx)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxFatalError")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error); ok {
		r0 = rf(ctx, etx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_UpdateTxFatalError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxFatalError'
type TxStore_UpdateTxFatalError_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// UpdateTxFatalError is a helper method to define mock.On call
//   - ctx context.Context
//   - etx *txmgrtypes.Tx[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxFatalError(ctx interface{}, etx interface{}) *TxStore_UpdateTxFatalError_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_UpdateTxFatalError_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("UpdateTxFatalError", ctx, etx)}
}

func (_c *TxStore_UpdateTxFatalError_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])) *TxStore_UpdateTxFatalError_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]))
	})
	return _c
}

func (_c *TxStore_UpdateTxFatalError_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_UpdateTxFatalError_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_UpdateTxFatalError_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error) *TxStore_UpdateTxFatalError_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// UpdateTxForRebroadcast provides a mock function with given fields: ctx, etx, etxAttempt
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxForRebroadcast(ctx context.Context, etx txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], etxAttempt txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error {
	ret := _m.Called(ctx, etx, etxAttempt)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxForRebroadcast")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error); ok {
		r0 = rf(ctx, etx, etxAttempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_UpdateTxForRebroadcast_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxForRebroadcast'
type TxStore_UpdateTxForRebroadcast_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// UpdateTxForRebroadcast is a helper method to define mock.On call
//   - ctx context.Context
//   - etx txmgrtypes.Tx[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
//   - etxAttempt txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxForRebroadcast(ctx interface{}, etx interface{}, etxAttempt interface{}) *TxStore_UpdateTxForRebroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_UpdateTxForRebroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("UpdateTxForRebroadcast", ctx, etx, etxAttempt)}
}

func (_c *TxStore_UpdateTxForRebroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, etx txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], etxAttempt txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])) *TxStore_UpdateTxForRebroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]), args[2].(txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]))
	})
	return _c
}

func (_c *TxStore_UpdateTxForRebroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_UpdateTxForRebroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_UpdateTxForRebroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error) *TxStore_UpdateTxForRebroadcast_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// UpdateTxUnstartedToInProgress provides a mock function with given fields: ctx, etx, attempt
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxUnstartedToInProgress(ctx context.Context, etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error {
	ret := _m.Called(ctx, etx, attempt)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxUnstartedToInProgress")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error); ok {
		r0 = rf(ctx, etx, attempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_UpdateTxUnstartedToInProgress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxUnstartedToInProgress'
type TxStore_UpdateTxUnstartedToInProgress_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// UpdateTxUnstartedToInProgress is a helper method to define mock.On call
//   - ctx context.Context
//   - etx *txmgrtypes.Tx[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
//   - attempt *txmgrtypes.TxAttempt[CHAIN_ID,ADDR,TX_HASH,BLOCK_HASH,SEQ,FEE]
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxUnstartedToInProgress(ctx interface{}, etx interface{}, attempt interface{}) *TxStore_UpdateTxUnstartedToInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_UpdateTxUnstartedToInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("UpdateTxUnstartedToInProgress", ctx, etx, attempt)}
}

func (_c *TxStore_UpdateTxUnstartedToInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, etx *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], attempt *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])) *TxStore_UpdateTxUnstartedToInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]), args[2].(*txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]))
	})
	return _c
}

func (_c *TxStore_UpdateTxUnstartedToInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_UpdateTxUnstartedToInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_UpdateTxUnstartedToInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, *txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], *txmgrtypes.TxAttempt[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) error) *TxStore_UpdateTxUnstartedToInProgress_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// UpdateTxsUnconfirmed provides a mock function with given fields: ctx, ids
func (_m *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxsUnconfirmed(ctx context.Context, ids []int64) error {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxsUnconfirmed")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) error); ok {
		r0 = rf(ctx, ids)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxStore_UpdateTxsUnconfirmed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxsUnconfirmed'
type TxStore_UpdateTxsUnconfirmed_Call[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// UpdateTxsUnconfirmed is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *TxStore_Expecter[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) UpdateTxsUnconfirmed(ctx interface{}, ids interface{}) *TxStore_UpdateTxsUnconfirmed_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	return &TxStore_UpdateTxsUnconfirmed_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{Call: _e.mock.On("UpdateTxsUnconfirmed", ctx, ids)}
}

func (_c *TxStore_UpdateTxsUnconfirmed_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Run(run func(ctx context.Context, ids []int64)) *TxStore_UpdateTxsUnconfirmed_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *TxStore_UpdateTxsUnconfirmed_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) Return(_a0 error) *TxStore_UpdateTxsUnconfirmed_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxStore_UpdateTxsUnconfirmed_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]) RunAndReturn(run func(context.Context, []int64) error) *TxStore_UpdateTxsUnconfirmed_Call[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// NewTxStore creates a new instance of TxStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTxStore[ADDR types.Hashable, CHAIN_ID types.ID, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, R txmgrtypes.ChainReceipt[TX_HASH, BLOCK_HASH], SEQ types.Sequence, FEE feetypes.Fee](t interface {
	mock.TestingT
	Cleanup(func())
}) *TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE] {
	mock := &TxStore[ADDR, CHAIN_ID, TX_HASH, BLOCK_HASH, R, SEQ, FEE]{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
