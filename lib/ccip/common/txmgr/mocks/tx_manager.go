// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"
	big "math/big"

	feetypes "github.com/smartcontractkit/chainlink/v2/common/fee/types"
	mock "github.com/stretchr/testify/mock"

	null "gopkg.in/guregu/null.v4"

	pkgtypes "github.com/smartcontractkit/chainlink-common/pkg/types"

	txmgr "github.com/smartcontractkit/chainlink/v2/common/txmgr"

	txmgrtypes "github.com/smartcontractkit/chainlink/v2/common/txmgr/types"

	types "github.com/smartcontractkit/chainlink/v2/common/types"
)

// TxManager is an autogenerated mock type for the TxManager type
type TxManager[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	mock.Mock
}

type TxManager_Expecter[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	mock *mock.Mock
}

func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) EXPECT() *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{mock: &_m.Mock}
}

// Close provides a mock function with given fields:
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxManager_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type TxManager_Close_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Close() *TxManager_Close_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_Close_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("Close")}
}

func (_c *TxManager_Close_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func()) *TxManager_Close_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TxManager_Close_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(_a0 error) *TxManager_Close_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxManager_Close_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func() error) *TxManager_Close_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// CountTransactionsByState provides a mock function with given fields: ctx, state
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) CountTransactionsByState(ctx context.Context, state txmgrtypes.TxState) (uint32, error) {
	ret := _m.Called(ctx, state)

	if len(ret) == 0 {
		panic("no return value specified for CountTransactionsByState")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxState) (uint32, error)); ok {
		return rf(ctx, state)
	}
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxState) uint32); ok {
		r0 = rf(ctx, state)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, txmgrtypes.TxState) error); ok {
		r1 = rf(ctx, state)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_CountTransactionsByState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountTransactionsByState'
type TxManager_CountTransactionsByState_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// CountTransactionsByState is a helper method to define mock.On call
//   - ctx context.Context
//   - state txmgrtypes.TxState
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) CountTransactionsByState(ctx interface{}, state interface{}) *TxManager_CountTransactionsByState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_CountTransactionsByState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("CountTransactionsByState", ctx, state)}
}

func (_c *TxManager_CountTransactionsByState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, state txmgrtypes.TxState)) *TxManager_CountTransactionsByState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(txmgrtypes.TxState))
	})
	return _c
}

func (_c *TxManager_CountTransactionsByState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(count uint32, err error) *TxManager_CountTransactionsByState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(count, err)
	return _c
}

func (_c *TxManager_CountTransactionsByState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, txmgrtypes.TxState) (uint32, error)) *TxManager_CountTransactionsByState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// CreateTransaction provides a mock function with given fields: ctx, txRequest
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) CreateTransaction(ctx context.Context, txRequest txmgrtypes.TxRequest[ADDR, TX_HASH]) (txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, txRequest)

	if len(ret) == 0 {
		panic("no return value specified for CreateTransaction")
	}

	var r0 txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxRequest[ADDR, TX_HASH]) (txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, txRequest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, txmgrtypes.TxRequest[ADDR, TX_HASH]) txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, txRequest)
	} else {
		r0 = ret.Get(0).(txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
	}

	if rf, ok := ret.Get(1).(func(context.Context, txmgrtypes.TxRequest[ADDR, TX_HASH]) error); ok {
		r1 = rf(ctx, txRequest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_CreateTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTransaction'
type TxManager_CreateTransaction_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// CreateTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - txRequest txmgrtypes.TxRequest[ADDR,TX_HASH]
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) CreateTransaction(ctx interface{}, txRequest interface{}) *TxManager_CreateTransaction_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_CreateTransaction_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("CreateTransaction", ctx, txRequest)}
}

func (_c *TxManager_CreateTransaction_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, txRequest txmgrtypes.TxRequest[ADDR, TX_HASH])) *TxManager_CreateTransaction_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(txmgrtypes.TxRequest[ADDR, TX_HASH]))
	})
	return _c
}

func (_c *TxManager_CreateTransaction_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(etx txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxManager_CreateTransaction_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(etx, err)
	return _c
}

func (_c *TxManager_CreateTransaction_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, txmgrtypes.TxRequest[ADDR, TX_HASH]) (txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxManager_CreateTransaction_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindEarliestUnconfirmedBroadcastTime provides a mock function with given fields: ctx
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindEarliestUnconfirmedBroadcastTime(ctx context.Context) (null.Time, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for FindEarliestUnconfirmedBroadcastTime")
	}

	var r0 null.Time
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (null.Time, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) null.Time); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(null.Time)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_FindEarliestUnconfirmedBroadcastTime_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindEarliestUnconfirmedBroadcastTime'
type TxManager_FindEarliestUnconfirmedBroadcastTime_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindEarliestUnconfirmedBroadcastTime is a helper method to define mock.On call
//   - ctx context.Context
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindEarliestUnconfirmedBroadcastTime(ctx interface{}) *TxManager_FindEarliestUnconfirmedBroadcastTime_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_FindEarliestUnconfirmedBroadcastTime_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("FindEarliestUnconfirmedBroadcastTime", ctx)}
}

func (_c *TxManager_FindEarliestUnconfirmedBroadcastTime_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context)) *TxManager_FindEarliestUnconfirmedBroadcastTime_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *TxManager_FindEarliestUnconfirmedBroadcastTime_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(_a0 null.Time, _a1 error) *TxManager_FindEarliestUnconfirmedBroadcastTime_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TxManager_FindEarliestUnconfirmedBroadcastTime_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context) (null.Time, error)) *TxManager_FindEarliestUnconfirmedBroadcastTime_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindEarliestUnconfirmedTxAttemptBlock provides a mock function with given fields: ctx
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindEarliestUnconfirmedTxAttemptBlock(ctx context.Context) (null.Int, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for FindEarliestUnconfirmedTxAttemptBlock")
	}

	var r0 null.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (null.Int, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) null.Int); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(null.Int)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindEarliestUnconfirmedTxAttemptBlock'
type TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindEarliestUnconfirmedTxAttemptBlock is a helper method to define mock.On call
//   - ctx context.Context
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindEarliestUnconfirmedTxAttemptBlock(ctx interface{}) *TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("FindEarliestUnconfirmedTxAttemptBlock", ctx)}
}

func (_c *TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context)) *TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(_a0 null.Int, _a1 error) *TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context) (null.Int, error)) *TxManager_FindEarliestUnconfirmedTxAttemptBlock_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxesByMetaFieldAndStates provides a mock function with given fields: ctx, metaField, metaValue, states, chainID
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindTxesByMetaFieldAndStates(ctx context.Context, metaField string, metaValue string, states []txmgrtypes.TxState, chainID *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, metaField, metaValue, states, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesByMetaFieldAndStates")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, metaField, metaValue, states, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, []txmgrtypes.TxState, *big.Int) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, metaField, metaValue, states, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, []txmgrtypes.TxState, *big.Int) error); ok {
		r1 = rf(ctx, metaField, metaValue, states, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_FindTxesByMetaFieldAndStates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesByMetaFieldAndStates'
type TxManager_FindTxesByMetaFieldAndStates_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxesByMetaFieldAndStates is a helper method to define mock.On call
//   - ctx context.Context
//   - metaField string
//   - metaValue string
//   - states []txmgrtypes.TxState
//   - chainID *big.Int
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindTxesByMetaFieldAndStates(ctx interface{}, metaField interface{}, metaValue interface{}, states interface{}, chainID interface{}) *TxManager_FindTxesByMetaFieldAndStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_FindTxesByMetaFieldAndStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("FindTxesByMetaFieldAndStates", ctx, metaField, metaValue, states, chainID)}
}

func (_c *TxManager_FindTxesByMetaFieldAndStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, metaField string, metaValue string, states []txmgrtypes.TxState, chainID *big.Int)) *TxManager_FindTxesByMetaFieldAndStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].([]txmgrtypes.TxState), args[4].(*big.Int))
	})
	return _c
}

func (_c *TxManager_FindTxesByMetaFieldAndStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(txes []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxManager_FindTxesByMetaFieldAndStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(txes, err)
	return _c
}

func (_c *TxManager_FindTxesByMetaFieldAndStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, string, string, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxManager_FindTxesByMetaFieldAndStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxesWithAttemptsAndReceiptsByIdsAndState provides a mock function with given fields: ctx, ids, states, chainID
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindTxesWithAttemptsAndReceiptsByIdsAndState(ctx context.Context, ids []int64, states []txmgrtypes.TxState, chainID *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, ids, states, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesWithAttemptsAndReceiptsByIdsAndState")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, ids, states, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64, []txmgrtypes.TxState, *big.Int) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, ids, states, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64, []txmgrtypes.TxState, *big.Int) error); ok {
		r1 = rf(ctx, ids, states, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesWithAttemptsAndReceiptsByIdsAndState'
type TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxesWithAttemptsAndReceiptsByIdsAndState is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
//   - states []txmgrtypes.TxState
//   - chainID *big.Int
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindTxesWithAttemptsAndReceiptsByIdsAndState(ctx interface{}, ids interface{}, states interface{}, chainID interface{}) *TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("FindTxesWithAttemptsAndReceiptsByIdsAndState", ctx, ids, states, chainID)}
}

func (_c *TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, ids []int64, states []txmgrtypes.TxState, chainID *big.Int)) *TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64), args[2].([]txmgrtypes.TxState), args[3].(*big.Int))
	})
	return _c
}

func (_c *TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(txes []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(txes, err)
	return _c
}

func (_c *TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, []int64, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxManager_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxesWithMetaFieldByReceiptBlockNum provides a mock function with given fields: ctx, metaField, blockNum, chainID
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindTxesWithMetaFieldByReceiptBlockNum(ctx context.Context, metaField string, blockNum int64, chainID *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, metaField, blockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesWithMetaFieldByReceiptBlockNum")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, metaField, blockNum, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, *big.Int) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, metaField, blockNum, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int64, *big.Int) error); ok {
		r1 = rf(ctx, metaField, blockNum, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesWithMetaFieldByReceiptBlockNum'
type TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxesWithMetaFieldByReceiptBlockNum is a helper method to define mock.On call
//   - ctx context.Context
//   - metaField string
//   - blockNum int64
//   - chainID *big.Int
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindTxesWithMetaFieldByReceiptBlockNum(ctx interface{}, metaField interface{}, blockNum interface{}, chainID interface{}) *TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("FindTxesWithMetaFieldByReceiptBlockNum", ctx, metaField, blockNum, chainID)}
}

func (_c *TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, metaField string, blockNum int64, chainID *big.Int)) *TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(*big.Int))
	})
	return _c
}

func (_c *TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(txes []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(txes, err)
	return _c
}

func (_c *TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, string, int64, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxManager_FindTxesWithMetaFieldByReceiptBlockNum_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// FindTxesWithMetaFieldByStates provides a mock function with given fields: ctx, metaField, states, chainID
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindTxesWithMetaFieldByStates(ctx context.Context, metaField string, states []txmgrtypes.TxState, chainID *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, metaField, states, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesWithMetaFieldByStates")
	}

	var r0 []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, metaField, states, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, []txmgrtypes.TxState, *big.Int) []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, metaField, states, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, []txmgrtypes.TxState, *big.Int) error); ok {
		r1 = rf(ctx, metaField, states, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_FindTxesWithMetaFieldByStates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesWithMetaFieldByStates'
type TxManager_FindTxesWithMetaFieldByStates_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// FindTxesWithMetaFieldByStates is a helper method to define mock.On call
//   - ctx context.Context
//   - metaField string
//   - states []txmgrtypes.TxState
//   - chainID *big.Int
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) FindTxesWithMetaFieldByStates(ctx interface{}, metaField interface{}, states interface{}, chainID interface{}) *TxManager_FindTxesWithMetaFieldByStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_FindTxesWithMetaFieldByStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("FindTxesWithMetaFieldByStates", ctx, metaField, states, chainID)}
}

func (_c *TxManager_FindTxesWithMetaFieldByStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, metaField string, states []txmgrtypes.TxState, chainID *big.Int)) *TxManager_FindTxesWithMetaFieldByStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]txmgrtypes.TxState), args[3].(*big.Int))
	})
	return _c
}

func (_c *TxManager_FindTxesWithMetaFieldByStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(txes []*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxManager_FindTxesWithMetaFieldByStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(txes, err)
	return _c
}

func (_c *TxManager_FindTxesWithMetaFieldByStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, string, []txmgrtypes.TxState, *big.Int) ([]*txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxManager_FindTxesWithMetaFieldByStates_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// GetForwarderForEOA provides a mock function with given fields: ctx, eoa
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) GetForwarderForEOA(ctx context.Context, eoa ADDR) (ADDR, error) {
	ret := _m.Called(ctx, eoa)

	if len(ret) == 0 {
		panic("no return value specified for GetForwarderForEOA")
	}

	var r0 ADDR
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR) (ADDR, error)); ok {
		return rf(ctx, eoa)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR) ADDR); ok {
		r0 = rf(ctx, eoa)
	} else {
		r0 = ret.Get(0).(ADDR)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR) error); ok {
		r1 = rf(ctx, eoa)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_GetForwarderForEOA_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForwarderForEOA'
type TxManager_GetForwarderForEOA_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// GetForwarderForEOA is a helper method to define mock.On call
//   - ctx context.Context
//   - eoa ADDR
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) GetForwarderForEOA(ctx interface{}, eoa interface{}) *TxManager_GetForwarderForEOA_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_GetForwarderForEOA_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("GetForwarderForEOA", ctx, eoa)}
}

func (_c *TxManager_GetForwarderForEOA_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, eoa ADDR)) *TxManager_GetForwarderForEOA_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR))
	})
	return _c
}

func (_c *TxManager_GetForwarderForEOA_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(forwarder ADDR, err error) *TxManager_GetForwarderForEOA_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(forwarder, err)
	return _c
}

func (_c *TxManager_GetForwarderForEOA_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR) (ADDR, error)) *TxManager_GetForwarderForEOA_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// GetForwarderForEOAOCR2Feeds provides a mock function with given fields: ctx, eoa, ocr2AggregatorID
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) GetForwarderForEOAOCR2Feeds(ctx context.Context, eoa ADDR, ocr2AggregatorID ADDR) (ADDR, error) {
	ret := _m.Called(ctx, eoa, ocr2AggregatorID)

	if len(ret) == 0 {
		panic("no return value specified for GetForwarderForEOAOCR2Feeds")
	}

	var r0 ADDR
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, ADDR) (ADDR, error)); ok {
		return rf(ctx, eoa, ocr2AggregatorID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ADDR, ADDR) ADDR); ok {
		r0 = rf(ctx, eoa, ocr2AggregatorID)
	} else {
		r0 = ret.Get(0).(ADDR)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ADDR, ADDR) error); ok {
		r1 = rf(ctx, eoa, ocr2AggregatorID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_GetForwarderForEOAOCR2Feeds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetForwarderForEOAOCR2Feeds'
type TxManager_GetForwarderForEOAOCR2Feeds_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// GetForwarderForEOAOCR2Feeds is a helper method to define mock.On call
//   - ctx context.Context
//   - eoa ADDR
//   - ocr2AggregatorID ADDR
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) GetForwarderForEOAOCR2Feeds(ctx interface{}, eoa interface{}, ocr2AggregatorID interface{}) *TxManager_GetForwarderForEOAOCR2Feeds_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_GetForwarderForEOAOCR2Feeds_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("GetForwarderForEOAOCR2Feeds", ctx, eoa, ocr2AggregatorID)}
}

func (_c *TxManager_GetForwarderForEOAOCR2Feeds_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, eoa ADDR, ocr2AggregatorID ADDR)) *TxManager_GetForwarderForEOAOCR2Feeds_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ADDR), args[2].(ADDR))
	})
	return _c
}

func (_c *TxManager_GetForwarderForEOAOCR2Feeds_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(forwarder ADDR, err error) *TxManager_GetForwarderForEOAOCR2Feeds_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(forwarder, err)
	return _c
}

func (_c *TxManager_GetForwarderForEOAOCR2Feeds_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, ADDR, ADDR) (ADDR, error)) *TxManager_GetForwarderForEOAOCR2Feeds_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// GetTransactionStatus provides a mock function with given fields: ctx, transactionID
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) GetTransactionStatus(ctx context.Context, transactionID string) (pkgtypes.TransactionStatus, error) {
	ret := _m.Called(ctx, transactionID)

	if len(ret) == 0 {
		panic("no return value specified for GetTransactionStatus")
	}

	var r0 pkgtypes.TransactionStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (pkgtypes.TransactionStatus, error)); ok {
		return rf(ctx, transactionID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) pkgtypes.TransactionStatus); ok {
		r0 = rf(ctx, transactionID)
	} else {
		r0 = ret.Get(0).(pkgtypes.TransactionStatus)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, transactionID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_GetTransactionStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTransactionStatus'
type TxManager_GetTransactionStatus_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// GetTransactionStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - transactionID string
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) GetTransactionStatus(ctx interface{}, transactionID interface{}) *TxManager_GetTransactionStatus_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_GetTransactionStatus_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("GetTransactionStatus", ctx, transactionID)}
}

func (_c *TxManager_GetTransactionStatus_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, transactionID string)) *TxManager_GetTransactionStatus_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *TxManager_GetTransactionStatus_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(state pkgtypes.TransactionStatus, err error) *TxManager_GetTransactionStatus_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(state, err)
	return _c
}

func (_c *TxManager_GetTransactionStatus_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, string) (pkgtypes.TransactionStatus, error)) *TxManager_GetTransactionStatus_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with given fields:
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// TxManager_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type TxManager_HealthReport_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) HealthReport() *TxManager_HealthReport_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_HealthReport_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("HealthReport")}
}

func (_c *TxManager_HealthReport_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func()) *TxManager_HealthReport_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TxManager_HealthReport_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(_a0 map[string]error) *TxManager_HealthReport_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxManager_HealthReport_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func() map[string]error) *TxManager_HealthReport_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with given fields:
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// TxManager_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type TxManager_Name_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Name() *TxManager_Name_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_Name_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("Name")}
}

func (_c *TxManager_Name_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func()) *TxManager_Name_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TxManager_Name_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(_a0 string) *TxManager_Name_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxManager_Name_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func() string) *TxManager_Name_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// OnNewLongestChain provides a mock function with given fields: ctx, head
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) OnNewLongestChain(ctx context.Context, head HEAD) {
	_m.Called(ctx, head)
}

// TxManager_OnNewLongestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnNewLongestChain'
type TxManager_OnNewLongestChain_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// OnNewLongestChain is a helper method to define mock.On call
//   - ctx context.Context
//   - head HEAD
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) OnNewLongestChain(ctx interface{}, head interface{}) *TxManager_OnNewLongestChain_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_OnNewLongestChain_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("OnNewLongestChain", ctx, head)}
}

func (_c *TxManager_OnNewLongestChain_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, head HEAD)) *TxManager_OnNewLongestChain_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(HEAD))
	})
	return _c
}

func (_c *TxManager_OnNewLongestChain_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return() *TxManager_OnNewLongestChain_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return()
	return _c
}

func (_c *TxManager_OnNewLongestChain_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, HEAD)) *TxManager_OnNewLongestChain_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with given fields:
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxManager_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type TxManager_Ready_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Ready() *TxManager_Ready_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_Ready_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("Ready")}
}

func (_c *TxManager_Ready_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func()) *TxManager_Ready_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TxManager_Ready_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(_a0 error) *TxManager_Ready_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxManager_Ready_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func() error) *TxManager_Ready_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// RegisterResumeCallback provides a mock function with given fields: fn
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RegisterResumeCallback(fn txmgr.ResumeCallback) {
	_m.Called(fn)
}

// TxManager_RegisterResumeCallback_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterResumeCallback'
type TxManager_RegisterResumeCallback_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// RegisterResumeCallback is a helper method to define mock.On call
//   - fn txmgr.ResumeCallback
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RegisterResumeCallback(fn interface{}) *TxManager_RegisterResumeCallback_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_RegisterResumeCallback_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("RegisterResumeCallback", fn)}
}

func (_c *TxManager_RegisterResumeCallback_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(fn txmgr.ResumeCallback)) *TxManager_RegisterResumeCallback_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(txmgr.ResumeCallback))
	})
	return _c
}

func (_c *TxManager_RegisterResumeCallback_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return() *TxManager_RegisterResumeCallback_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return()
	return _c
}

func (_c *TxManager_RegisterResumeCallback_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(txmgr.ResumeCallback)) *TxManager_RegisterResumeCallback_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// Reset provides a mock function with given fields: addr, abandon
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Reset(addr ADDR, abandon bool) error {
	ret := _m.Called(addr, abandon)

	if len(ret) == 0 {
		panic("no return value specified for Reset")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(ADDR, bool) error); ok {
		r0 = rf(addr, abandon)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxManager_Reset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Reset'
type TxManager_Reset_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// Reset is a helper method to define mock.On call
//   - addr ADDR
//   - abandon bool
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Reset(addr interface{}, abandon interface{}) *TxManager_Reset_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_Reset_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("Reset", addr, abandon)}
}

func (_c *TxManager_Reset_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(addr ADDR, abandon bool)) *TxManager_Reset_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ADDR), args[1].(bool))
	})
	return _c
}

func (_c *TxManager_Reset_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(_a0 error) *TxManager_Reset_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxManager_Reset_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(ADDR, bool) error) *TxManager_Reset_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// SendNativeToken provides a mock function with given fields: ctx, chainID, from, to, value, gasLimit
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) SendNativeToken(ctx context.Context, chainID CHAIN_ID, from ADDR, to ADDR, value big.Int, gasLimit uint64) (txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error) {
	ret := _m.Called(ctx, chainID, from, to, value, gasLimit)

	if len(ret) == 0 {
		panic("no return value specified for SendNativeToken")
	}

	var r0 txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID, ADDR, ADDR, big.Int, uint64) (txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)); ok {
		return rf(ctx, chainID, from, to, value, gasLimit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, CHAIN_ID, ADDR, ADDR, big.Int, uint64) txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]); ok {
		r0 = rf(ctx, chainID, from, to, value, gasLimit)
	} else {
		r0 = ret.Get(0).(txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE])
	}

	if rf, ok := ret.Get(1).(func(context.Context, CHAIN_ID, ADDR, ADDR, big.Int, uint64) error); ok {
		r1 = rf(ctx, chainID, from, to, value, gasLimit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// TxManager_SendNativeToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendNativeToken'
type TxManager_SendNativeToken_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// SendNativeToken is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID CHAIN_ID
//   - from ADDR
//   - to ADDR
//   - value big.Int
//   - gasLimit uint64
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) SendNativeToken(ctx interface{}, chainID interface{}, from interface{}, to interface{}, value interface{}, gasLimit interface{}) *TxManager_SendNativeToken_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_SendNativeToken_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("SendNativeToken", ctx, chainID, from, to, value, gasLimit)}
}

func (_c *TxManager_SendNativeToken_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(ctx context.Context, chainID CHAIN_ID, from ADDR, to ADDR, value big.Int, gasLimit uint64)) *TxManager_SendNativeToken_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(CHAIN_ID), args[2].(ADDR), args[3].(ADDR), args[4].(big.Int), args[5].(uint64))
	})
	return _c
}

func (_c *TxManager_SendNativeToken_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(etx txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], err error) *TxManager_SendNativeToken_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(etx, err)
	return _c
}

func (_c *TxManager_SendNativeToken_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context, CHAIN_ID, ADDR, ADDR, big.Int, uint64) (txmgrtypes.Tx[CHAIN_ID, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE], error)) *TxManager_SendNativeToken_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TxManager_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type TxManager_Start_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Start(_a0 interface{}) *TxManager_Start_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_Start_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("Start", _a0)}
}

func (_c *TxManager_Start_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(_a0 context.Context)) *TxManager_Start_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *TxManager_Start_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return(_a0 error) *TxManager_Start_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TxManager_Start_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(context.Context) error) *TxManager_Start_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// Trigger provides a mock function with given fields: addr
func (_m *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Trigger(addr ADDR) {
	_m.Called(addr)
}

// TxManager_Trigger_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Trigger'
type TxManager_Trigger_Call[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee] struct {
	*mock.Call
}

// Trigger is a helper method to define mock.On call
//   - addr ADDR
func (_e *TxManager_Expecter[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Trigger(addr interface{}) *TxManager_Trigger_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	return &TxManager_Trigger_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{Call: _e.mock.On("Trigger", addr)}
}

func (_c *TxManager_Trigger_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Run(run func(addr ADDR)) *TxManager_Trigger_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(ADDR))
	})
	return _c
}

func (_c *TxManager_Trigger_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) Return() *TxManager_Trigger_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return()
	return _c
}

func (_c *TxManager_Trigger_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]) RunAndReturn(run func(ADDR)) *TxManager_Trigger_Call[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	_c.Call.Return(run)
	return _c
}

// NewTxManager creates a new instance of TxManager. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTxManager[CHAIN_ID types.ID, HEAD types.Head[BLOCK_HASH], ADDR types.Hashable, TX_HASH types.Hashable, BLOCK_HASH types.Hashable, SEQ types.Sequence, FEE feetypes.Fee](t interface {
	mock.TestingT
	Cleanup(func())
}) *TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE] {
	mock := &TxManager[CHAIN_ID, HEAD, ADDR, TX_HASH, BLOCK_HASH, SEQ, FEE]{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
