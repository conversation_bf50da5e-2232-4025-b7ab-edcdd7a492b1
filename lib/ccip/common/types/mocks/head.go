// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	big "math/big"
	time "time"

	mock "github.com/stretchr/testify/mock"

	types "github.com/smartcontractkit/chainlink/v2/common/types"
)

// Head is an autogenerated mock type for the Head type
type Head[BLOCK_HASH types.Hashable] struct {
	mock.Mock
}

type Head_Expecter[BLOCK_HASH types.Hashable] struct {
	mock *mock.Mock
}

func (_m *Head[BLOCK_HASH]) EXPECT() *Head_Expecter[BLOCK_HASH] {
	return &Head_Expecter[BLOCK_HASH]{mock: &_m.Mock}
}

// BlockDifficulty provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) BlockDifficulty() *big.Int {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for BlockDifficulty")
	}

	var r0 *big.Int
	if rf, ok := ret.Get(0).(func() *big.Int); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	return r0
}

// Head_BlockDifficulty_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BlockDifficulty'
type Head_BlockDifficulty_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// BlockDifficulty is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) BlockDifficulty() *Head_BlockDifficulty_Call[BLOCK_HASH] {
	return &Head_BlockDifficulty_Call[BLOCK_HASH]{Call: _e.mock.On("BlockDifficulty")}
}

func (_c *Head_BlockDifficulty_Call[BLOCK_HASH]) Run(run func()) *Head_BlockDifficulty_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_BlockDifficulty_Call[BLOCK_HASH]) Return(_a0 *big.Int) *Head_BlockDifficulty_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_BlockDifficulty_Call[BLOCK_HASH]) RunAndReturn(run func() *big.Int) *Head_BlockDifficulty_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// BlockHash provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) BlockHash() BLOCK_HASH {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for BlockHash")
	}

	var r0 BLOCK_HASH
	if rf, ok := ret.Get(0).(func() BLOCK_HASH); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(BLOCK_HASH)
	}

	return r0
}

// Head_BlockHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BlockHash'
type Head_BlockHash_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// BlockHash is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) BlockHash() *Head_BlockHash_Call[BLOCK_HASH] {
	return &Head_BlockHash_Call[BLOCK_HASH]{Call: _e.mock.On("BlockHash")}
}

func (_c *Head_BlockHash_Call[BLOCK_HASH]) Run(run func()) *Head_BlockHash_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_BlockHash_Call[BLOCK_HASH]) Return(_a0 BLOCK_HASH) *Head_BlockHash_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_BlockHash_Call[BLOCK_HASH]) RunAndReturn(run func() BLOCK_HASH) *Head_BlockHash_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// BlockNumber provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) BlockNumber() int64 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for BlockNumber")
	}

	var r0 int64
	if rf, ok := ret.Get(0).(func() int64); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int64)
	}

	return r0
}

// Head_BlockNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BlockNumber'
type Head_BlockNumber_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// BlockNumber is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) BlockNumber() *Head_BlockNumber_Call[BLOCK_HASH] {
	return &Head_BlockNumber_Call[BLOCK_HASH]{Call: _e.mock.On("BlockNumber")}
}

func (_c *Head_BlockNumber_Call[BLOCK_HASH]) Run(run func()) *Head_BlockNumber_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_BlockNumber_Call[BLOCK_HASH]) Return(_a0 int64) *Head_BlockNumber_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_BlockNumber_Call[BLOCK_HASH]) RunAndReturn(run func() int64) *Head_BlockNumber_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// ChainLength provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) ChainLength() uint32 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ChainLength")
	}

	var r0 uint32
	if rf, ok := ret.Get(0).(func() uint32); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(uint32)
	}

	return r0
}

// Head_ChainLength_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChainLength'
type Head_ChainLength_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// ChainLength is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) ChainLength() *Head_ChainLength_Call[BLOCK_HASH] {
	return &Head_ChainLength_Call[BLOCK_HASH]{Call: _e.mock.On("ChainLength")}
}

func (_c *Head_ChainLength_Call[BLOCK_HASH]) Run(run func()) *Head_ChainLength_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_ChainLength_Call[BLOCK_HASH]) Return(_a0 uint32) *Head_ChainLength_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_ChainLength_Call[BLOCK_HASH]) RunAndReturn(run func() uint32) *Head_ChainLength_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// EarliestHeadInChain provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) EarliestHeadInChain() types.Head[BLOCK_HASH] {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for EarliestHeadInChain")
	}

	var r0 types.Head[BLOCK_HASH]
	if rf, ok := ret.Get(0).(func() types.Head[BLOCK_HASH]); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.Head[BLOCK_HASH])
		}
	}

	return r0
}

// Head_EarliestHeadInChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EarliestHeadInChain'
type Head_EarliestHeadInChain_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// EarliestHeadInChain is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) EarliestHeadInChain() *Head_EarliestHeadInChain_Call[BLOCK_HASH] {
	return &Head_EarliestHeadInChain_Call[BLOCK_HASH]{Call: _e.mock.On("EarliestHeadInChain")}
}

func (_c *Head_EarliestHeadInChain_Call[BLOCK_HASH]) Run(run func()) *Head_EarliestHeadInChain_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_EarliestHeadInChain_Call[BLOCK_HASH]) Return(_a0 types.Head[BLOCK_HASH]) *Head_EarliestHeadInChain_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_EarliestHeadInChain_Call[BLOCK_HASH]) RunAndReturn(run func() types.Head[BLOCK_HASH]) *Head_EarliestHeadInChain_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// GetParent provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) GetParent() types.Head[BLOCK_HASH] {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetParent")
	}

	var r0 types.Head[BLOCK_HASH]
	if rf, ok := ret.Get(0).(func() types.Head[BLOCK_HASH]); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.Head[BLOCK_HASH])
		}
	}

	return r0
}

// Head_GetParent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetParent'
type Head_GetParent_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// GetParent is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) GetParent() *Head_GetParent_Call[BLOCK_HASH] {
	return &Head_GetParent_Call[BLOCK_HASH]{Call: _e.mock.On("GetParent")}
}

func (_c *Head_GetParent_Call[BLOCK_HASH]) Run(run func()) *Head_GetParent_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_GetParent_Call[BLOCK_HASH]) Return(_a0 types.Head[BLOCK_HASH]) *Head_GetParent_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_GetParent_Call[BLOCK_HASH]) RunAndReturn(run func() types.Head[BLOCK_HASH]) *Head_GetParent_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// GetParentHash provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) GetParentHash() BLOCK_HASH {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetParentHash")
	}

	var r0 BLOCK_HASH
	if rf, ok := ret.Get(0).(func() BLOCK_HASH); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(BLOCK_HASH)
	}

	return r0
}

// Head_GetParentHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetParentHash'
type Head_GetParentHash_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// GetParentHash is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) GetParentHash() *Head_GetParentHash_Call[BLOCK_HASH] {
	return &Head_GetParentHash_Call[BLOCK_HASH]{Call: _e.mock.On("GetParentHash")}
}

func (_c *Head_GetParentHash_Call[BLOCK_HASH]) Run(run func()) *Head_GetParentHash_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_GetParentHash_Call[BLOCK_HASH]) Return(_a0 BLOCK_HASH) *Head_GetParentHash_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_GetParentHash_Call[BLOCK_HASH]) RunAndReturn(run func() BLOCK_HASH) *Head_GetParentHash_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// GetTimestamp provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) GetTimestamp() time.Time {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetTimestamp")
	}

	var r0 time.Time
	if rf, ok := ret.Get(0).(func() time.Time); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(time.Time)
	}

	return r0
}

// Head_GetTimestamp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTimestamp'
type Head_GetTimestamp_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// GetTimestamp is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) GetTimestamp() *Head_GetTimestamp_Call[BLOCK_HASH] {
	return &Head_GetTimestamp_Call[BLOCK_HASH]{Call: _e.mock.On("GetTimestamp")}
}

func (_c *Head_GetTimestamp_Call[BLOCK_HASH]) Run(run func()) *Head_GetTimestamp_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_GetTimestamp_Call[BLOCK_HASH]) Return(_a0 time.Time) *Head_GetTimestamp_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_GetTimestamp_Call[BLOCK_HASH]) RunAndReturn(run func() time.Time) *Head_GetTimestamp_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// HashAtHeight provides a mock function with given fields: blockNum
func (_m *Head[BLOCK_HASH]) HashAtHeight(blockNum int64) BLOCK_HASH {
	ret := _m.Called(blockNum)

	if len(ret) == 0 {
		panic("no return value specified for HashAtHeight")
	}

	var r0 BLOCK_HASH
	if rf, ok := ret.Get(0).(func(int64) BLOCK_HASH); ok {
		r0 = rf(blockNum)
	} else {
		r0 = ret.Get(0).(BLOCK_HASH)
	}

	return r0
}

// Head_HashAtHeight_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HashAtHeight'
type Head_HashAtHeight_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// HashAtHeight is a helper method to define mock.On call
//   - blockNum int64
func (_e *Head_Expecter[BLOCK_HASH]) HashAtHeight(blockNum interface{}) *Head_HashAtHeight_Call[BLOCK_HASH] {
	return &Head_HashAtHeight_Call[BLOCK_HASH]{Call: _e.mock.On("HashAtHeight", blockNum)}
}

func (_c *Head_HashAtHeight_Call[BLOCK_HASH]) Run(run func(blockNum int64)) *Head_HashAtHeight_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int64))
	})
	return _c
}

func (_c *Head_HashAtHeight_Call[BLOCK_HASH]) Return(_a0 BLOCK_HASH) *Head_HashAtHeight_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_HashAtHeight_Call[BLOCK_HASH]) RunAndReturn(run func(int64) BLOCK_HASH) *Head_HashAtHeight_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// HeadAtHeight provides a mock function with given fields: blockNum
func (_m *Head[BLOCK_HASH]) HeadAtHeight(blockNum int64) (types.Head[BLOCK_HASH], error) {
	ret := _m.Called(blockNum)

	if len(ret) == 0 {
		panic("no return value specified for HeadAtHeight")
	}

	var r0 types.Head[BLOCK_HASH]
	var r1 error
	if rf, ok := ret.Get(0).(func(int64) (types.Head[BLOCK_HASH], error)); ok {
		return rf(blockNum)
	}
	if rf, ok := ret.Get(0).(func(int64) types.Head[BLOCK_HASH]); ok {
		r0 = rf(blockNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.Head[BLOCK_HASH])
		}
	}

	if rf, ok := ret.Get(1).(func(int64) error); ok {
		r1 = rf(blockNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Head_HeadAtHeight_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HeadAtHeight'
type Head_HeadAtHeight_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// HeadAtHeight is a helper method to define mock.On call
//   - blockNum int64
func (_e *Head_Expecter[BLOCK_HASH]) HeadAtHeight(blockNum interface{}) *Head_HeadAtHeight_Call[BLOCK_HASH] {
	return &Head_HeadAtHeight_Call[BLOCK_HASH]{Call: _e.mock.On("HeadAtHeight", blockNum)}
}

func (_c *Head_HeadAtHeight_Call[BLOCK_HASH]) Run(run func(blockNum int64)) *Head_HeadAtHeight_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int64))
	})
	return _c
}

func (_c *Head_HeadAtHeight_Call[BLOCK_HASH]) Return(_a0 types.Head[BLOCK_HASH], _a1 error) *Head_HeadAtHeight_Call[BLOCK_HASH] {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Head_HeadAtHeight_Call[BLOCK_HASH]) RunAndReturn(run func(int64) (types.Head[BLOCK_HASH], error)) *Head_HeadAtHeight_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// IsValid provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) IsValid() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsValid")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// Head_IsValid_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsValid'
type Head_IsValid_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// IsValid is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) IsValid() *Head_IsValid_Call[BLOCK_HASH] {
	return &Head_IsValid_Call[BLOCK_HASH]{Call: _e.mock.On("IsValid")}
}

func (_c *Head_IsValid_Call[BLOCK_HASH]) Run(run func()) *Head_IsValid_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_IsValid_Call[BLOCK_HASH]) Return(_a0 bool) *Head_IsValid_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_IsValid_Call[BLOCK_HASH]) RunAndReturn(run func() bool) *Head_IsValid_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// LatestFinalizedHead provides a mock function with given fields:
func (_m *Head[BLOCK_HASH]) LatestFinalizedHead() types.Head[BLOCK_HASH] {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for LatestFinalizedHead")
	}

	var r0 types.Head[BLOCK_HASH]
	if rf, ok := ret.Get(0).(func() types.Head[BLOCK_HASH]); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.Head[BLOCK_HASH])
		}
	}

	return r0
}

// Head_LatestFinalizedHead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestFinalizedHead'
type Head_LatestFinalizedHead_Call[BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// LatestFinalizedHead is a helper method to define mock.On call
func (_e *Head_Expecter[BLOCK_HASH]) LatestFinalizedHead() *Head_LatestFinalizedHead_Call[BLOCK_HASH] {
	return &Head_LatestFinalizedHead_Call[BLOCK_HASH]{Call: _e.mock.On("LatestFinalizedHead")}
}

func (_c *Head_LatestFinalizedHead_Call[BLOCK_HASH]) Run(run func()) *Head_LatestFinalizedHead_Call[BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Head_LatestFinalizedHead_Call[BLOCK_HASH]) Return(_a0 types.Head[BLOCK_HASH]) *Head_LatestFinalizedHead_Call[BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Head_LatestFinalizedHead_Call[BLOCK_HASH]) RunAndReturn(run func() types.Head[BLOCK_HASH]) *Head_LatestFinalizedHead_Call[BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// NewHead creates a new instance of Head. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewHead[BLOCK_HASH types.Hashable](t interface {
	mock.TestingT
	Cleanup(func())
}) *Head[BLOCK_HASH] {
	mock := &Head[BLOCK_HASH]{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
