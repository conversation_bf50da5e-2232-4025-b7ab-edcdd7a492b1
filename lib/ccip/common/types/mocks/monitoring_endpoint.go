// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// MonitoringEndpoint is an autogenerated mock type for the MonitoringEndpoint type
type MonitoringEndpoint struct {
	mock.Mock
}

type MonitoringEndpoint_Expecter struct {
	mock *mock.Mock
}

func (_m *MonitoringEndpoint) EXPECT() *MonitoringEndpoint_Expecter {
	return &MonitoringEndpoint_Expecter{mock: &_m.Mock}
}

// SendLog provides a mock function with given fields: log
func (_m *MonitoringEndpoint) SendLog(log []byte) {
	_m.Called(log)
}

// MonitoringEndpoint_SendLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendLog'
type MonitoringEndpoint_SendLog_Call struct {
	*mock.Call
}

// SendLog is a helper method to define mock.On call
//   - log []byte
func (_e *MonitoringEndpoint_Expecter) SendLog(log interface{}) *MonitoringEndpoint_SendLog_Call {
	return &MonitoringEndpoint_SendLog_Call{Call: _e.mock.On("SendLog", log)}
}

func (_c *MonitoringEndpoint_SendLog_Call) Run(run func(log []byte)) *MonitoringEndpoint_SendLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]byte))
	})
	return _c
}

func (_c *MonitoringEndpoint_SendLog_Call) Return() *MonitoringEndpoint_SendLog_Call {
	_c.Call.Return()
	return _c
}

func (_c *MonitoringEndpoint_SendLog_Call) RunAndReturn(run func([]byte)) *MonitoringEndpoint_SendLog_Call {
	_c.Call.Return(run)
	return _c
}

// NewMonitoringEndpoint creates a new instance of MonitoringEndpoint. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMonitoringEndpoint(t interface {
	mock.TestingT
	Cleanup(func())
}) *MonitoringEndpoint {
	mock := &MonitoringEndpoint{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
