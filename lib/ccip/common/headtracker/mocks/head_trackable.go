// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	types "github.com/smartcontractkit/chainlink/v2/common/types"
)

// HeadTrackable is an autogenerated mock type for the HeadTrackable type
type HeadTrackable[H types.Head[BLOCK_HASH], BLOCK_HASH types.Hashable] struct {
	mock.Mock
}

type HeadTrackable_Expecter[H types.Head[BLOCK_HASH], BLOCK_HASH types.Hashable] struct {
	mock *mock.Mock
}

func (_m *HeadTrackable[H, BLOCK_HASH]) EXPECT() *HeadTrackable_Expecter[H, BLOCK_HASH] {
	return &HeadTrackable_Expecter[H, BLOCK_HASH]{mock: &_m.Mock}
}

// OnNewLongestChain provides a mock function with given fields: ctx, head
func (_m *HeadTrackable[H, BLOCK_HASH]) OnNewLongestChain(ctx context.Context, head H) {
	_m.Called(ctx, head)
}

// HeadTrackable_OnNewLongestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnNewLongestChain'
type HeadTrackable_OnNewLongestChain_Call[H types.Head[BLOCK_HASH], BLOCK_HASH types.Hashable] struct {
	*mock.Call
}

// OnNewLongestChain is a helper method to define mock.On call
//   - ctx context.Context
//   - head H
func (_e *HeadTrackable_Expecter[H, BLOCK_HASH]) OnNewLongestChain(ctx interface{}, head interface{}) *HeadTrackable_OnNewLongestChain_Call[H, BLOCK_HASH] {
	return &HeadTrackable_OnNewLongestChain_Call[H, BLOCK_HASH]{Call: _e.mock.On("OnNewLongestChain", ctx, head)}
}

func (_c *HeadTrackable_OnNewLongestChain_Call[H, BLOCK_HASH]) Run(run func(ctx context.Context, head H)) *HeadTrackable_OnNewLongestChain_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(H))
	})
	return _c
}

func (_c *HeadTrackable_OnNewLongestChain_Call[H, BLOCK_HASH]) Return() *HeadTrackable_OnNewLongestChain_Call[H, BLOCK_HASH] {
	_c.Call.Return()
	return _c
}

func (_c *HeadTrackable_OnNewLongestChain_Call[H, BLOCK_HASH]) RunAndReturn(run func(context.Context, H)) *HeadTrackable_OnNewLongestChain_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// NewHeadTrackable creates a new instance of HeadTrackable. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewHeadTrackable[H types.Head[BLOCK_HASH], BLOCK_HASH types.Hashable](t interface {
	mock.TestingT
	Cleanup(func())
}) *HeadTrackable[H, BLOCK_HASH] {
	mock := &HeadTrackable[H, BLOCK_HASH]{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
