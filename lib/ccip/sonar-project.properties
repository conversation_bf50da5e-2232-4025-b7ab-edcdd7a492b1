sonar.projectKey=smartcontractkit_chainlink-ccip
sonar.sources=.
sonar.sourceEncoding=UTF-8
sonar.python.version=3.8

# Full exclusions from the static analysis
sonar.exclusions=\
**/node_modules/**/*,\
**/mocks/**/*,\
**/testdata/**/*,\
**/contracts/typechain/**/*,\
**/contracts/artifacts/**/*,\
**/contracts/cache/**/*,\
**/contracts/scripts/**/*,\
**/generated/**/*,\
**/fixtures/**/*,\
**/testutils/**/*,\
**/gen/**/*,\
**/testfiles/**/*,\
**/testconfig/**/*,\
**/core/web/assets/**/*,\
**/core/scripts/**/*,\
**/docs/**/*,\
**/tools/**/*,\
**/fuzz/**/*,\
**/*.pb.go,\
**/*report.xml,\
**/*.config.ts,\
**/*.txt,\
**/*.abi,\
**/*.bin,\
**/*_codecgen.go,\
**/*_gen.go,\
**/tsconfig.json,\
**/debug.go

# Coverage exclusions
sonar.coverage.exclusions=\
**/*.test.ts,\
**/*_test.go,\
**/contracts/test/**/*,\
**/contracts/**/tests/**/*,\
**/core/**/cltest/**/*,\
**/integration-tests/**/*,\
**/plugins/**/*,\
**/main.go,\
**/0195_add_not_null_to_evm_chain_id_in_job_specs.go

# Duplication exclusions: mercury excluded because current MercuryProvider and Factory APIs are inherently duplicated due to embedded versioning
# Ethereum contracts were added temporarily, since until we fully migrate we will have parallel helpers that use Seth and since it's temporary
# it doesn't make sense to fight with some cyclic imports and similar issues and it's easier to have copy of some functions in both places
sonar.cpd.exclusions=\
**/contracts/**/*.sol,\
**/config.go,\
**/core/services/ocr2/plugins/ocr2keeper/evm/**/*,\
**/core/services/ocr2/plugins/mercury/plugin.go,\
**/integration-tests/load/**/*,\
**/integration-tests/contracts/ethereum_keeper_contracts.go,\
integration-tests/contracts/ethereum_contracts_seth.go,\
integration-tests/contracts/ethereum_contracts_seth.go,\
integration-tests/actions/seth/actions.go,\
dashboard-lib/**

# Tests' root folder, inclusions (tests to check and count) and exclusions
sonar.tests=.
sonar.test.inclusions=\
**/*_test.go,\
**/*.test.ts
sonar.test.exclusions=\
**/integration-tests/**/*