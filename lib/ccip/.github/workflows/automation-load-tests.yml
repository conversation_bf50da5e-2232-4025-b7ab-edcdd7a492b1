name: Automation Load Test
on:
  workflow_dispatch:
    inputs:
      test_config_override_path:
        description: Path to a test config file used to override the default test config
        required: false
        type: string    
      test_secrets_override_key:
        description: 'Key to run tests with custom test secrets'
        required: false
        type: string
      slackMemberID:
        description: Notifies test results (Not your @)
        required: true
        default: U02Q14G80TY
        type: string         

jobs:
  run-e2e-tests-workflow:
    name: Run E2E Tests
    uses: smartcontractkit/.github/.github/workflows/run-e2e-tests.yml@aad83f232743646faa35f5ac03ee3829148d37ce
    with:
      test_path: .github/e2e-tests.yml
      test_ids: 'load/automationv2_1/automationv2_1_test.go:TestLogTrigger'
      test_config_override_path: ${{ inputs.test_config_override_path }}
      SLACK_USER: ${{ inputs.slackMemberID }}
      SLACK_CHANNEL: C03KJ5S7KEK
    secrets:
      QA_AWS_REGION: ${{ secrets.QA_AWS_REGION }}
      QA_AWS_ROLE_TO_ASSUME: ${{ secrets.QA_AWS_ROLE_TO_ASSUME }}
      QA_AWS_ACCOUNT_NUMBER: ${{ secrets.QA_AWS_ACCOUNT_NUMBER }}
      QA_PYROSCOPE_INSTANCE: ${{ secrets.QA_PYROSCOPE_INSTANCE }}
      QA_PYROSCOPE_KEY: ${{ secrets.QA_PYROSCOPE_KEY }}
      QA_KUBECONFIG: ${{ secrets.QA_KUBECONFIG }}
      GRAFANA_INTERNAL_TENANT_ID: ${{ secrets.GRAFANA_INTERNAL_TENANT_ID }}
      GRAFANA_INTERNAL_BASIC_AUTH: ${{ secrets.GRAFANA_INTERNAL_BASIC_AUTH }}
      GRAFANA_INTERNAL_HOST: ${{ secrets.GRAFANA_INTERNAL_HOST }}
      GRAFANA_INTERNAL_URL_SHORTENER_TOKEN: ${{ secrets.GRAFANA_INTERNAL_URL_SHORTENER_TOKEN }}
      LOKI_TENANT_ID: ${{ secrets.LOKI_TENANT_ID }}
      LOKI_URL: ${{ secrets.LOKI_URL }}
      LOKI_BASIC_AUTH: ${{ secrets.LOKI_BASIC_AUTH }}
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}  
      AWS_REGION: ${{ secrets.QA_AWS_REGION }}
      AWS_OIDC_IAM_ROLE_VALIDATION_PROD_ARN: ${{ secrets.AWS_OIDC_IAM_ROLE_VALIDATION_PROD_ARN }}
      AWS_API_GW_HOST_GRAFANA: ${{ secrets.AWS_API_GW_HOST_GRAFANA }}        
      TEST_SECRETS_OVERRIDE_BASE64: ${{ secrets[inputs.test_secrets_override_key] }}
      SLACK_BOT_TOKEN: ${{ secrets.QA_SLACK_API_KEY }}
      SLACK_API_KEY: ${{ secrets.QA_SLACK_API_KEY }}
