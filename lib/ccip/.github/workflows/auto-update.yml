name: Auto Update
on:
  push:
    branches:
      - develop
jobs:
  autoupdate:
    name: Auto Update
    runs-on: ubuntu-latest
    steps:
      - uses: docker://chinthakagodawita/autoupdate-action:v1
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
          PR_FILTER: "labelled"
          PR_LABELS: "auto-update"
          MERGE_MSG: "Branch was auto-updated."
          MERGE_CONFLICT_ACTION: "ignore"
