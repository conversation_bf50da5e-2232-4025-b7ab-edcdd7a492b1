name: Run Selected E2E Tests

on:
  workflow_dispatch:
    inputs:
      chainlink_version:
        description: 'Enter Chainlink version to use for the tests. Example: "v2.10.0" or sha'
        required: false
        type: string
      test_ids:
        description: 'Run all tests "*" by default. Or, enter test IDs to run separated by commas. Example: "run_all_in_ocr_tests_go,run_TestOCRv2Request_in_ocr2_test_go". Check all test IDs in .github/e2e-tests.yml'
        default: "*"
        required: true
        type: string
      test_secrets_override_key:
        description: 'Enter the secret key to override test secrets'
        required: false
        type: string 
      test_config_override_path:
        description: 'Path to a test config file used to override the default test config'
        required: false
        type: string
      with_existing_remote_runner_version:
        description: 'Use the existing remote runner version for k8s tests. Example: "d3bf5044af33e08be788a2df31c4a745cf69d787"'
        required: false
        type: string         
      workflow_run_name:
        description: 'Enter the name of the workflow run'
        default: 'Run E2E Tests'
        required: false
        type: string
        
run-name: ${{ inputs.workflow_run_name }}

jobs:
  call-run-e2e-tests-workflow:
    name: Run E2E Tests
    uses: smartcontractkit/.github/.github/workflows/run-e2e-tests.yml@aad83f232743646faa35f5ac03ee3829148d37ce
    with:
      chainlink_version: ${{ github.event.inputs.chainlink_version }}
      test_path: .github/e2e-tests.yml
      test_ids: ${{ github.event.inputs.test_ids }}
      test_config_override_path: ${{ github.event.inputs.test_config_override_path }}
      with_existing_remote_runner_version: ${{ github.event.inputs.with_existing_remote_runner_version }}
    secrets:
      QA_AWS_REGION: ${{ secrets.QA_AWS_REGION }}
      QA_AWS_ROLE_TO_ASSUME: ${{ secrets.QA_AWS_ROLE_TO_ASSUME }}
      QA_AWS_ACCOUNT_NUMBER: ${{ secrets.QA_AWS_ACCOUNT_NUMBER }}
      QA_PYROSCOPE_INSTANCE: ${{ secrets.QA_PYROSCOPE_INSTANCE }}
      QA_PYROSCOPE_KEY: ${{ secrets.QA_PYROSCOPE_KEY }}
      QA_KUBECONFIG: ${{ secrets.QA_KUBECONFIG }}
      GRAFANA_INTERNAL_TENANT_ID: ${{ secrets.GRAFANA_INTERNAL_TENANT_ID }}
      GRAFANA_INTERNAL_BASIC_AUTH: ${{ secrets.GRAFANA_INTERNAL_BASIC_AUTH }}
      GRAFANA_INTERNAL_HOST: ${{ secrets.GRAFANA_INTERNAL_HOST }}
      GRAFANA_INTERNAL_URL_SHORTENER_TOKEN: ${{ secrets.GRAFANA_INTERNAL_URL_SHORTENER_TOKEN }}
      LOKI_TENANT_ID: ${{ secrets.LOKI_TENANT_ID }}
      LOKI_URL: ${{ secrets.LOKI_URL }}
      LOKI_BASIC_AUTH: ${{ secrets.LOKI_BASIC_AUTH }}
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}  
      AWS_REGION: ${{ secrets.QA_AWS_REGION }}
      AWS_OIDC_IAM_ROLE_VALIDATION_PROD_ARN: ${{ secrets.AWS_OIDC_IAM_ROLE_VALIDATION_PROD_ARN }}
      AWS_API_GW_HOST_GRAFANA: ${{ secrets.AWS_API_GW_HOST_GRAFANA }}
      TEST_SECRETS_OVERRIDE_BASE64: ${{ secrets[inputs.test_secrets_override_key] }}
      SLACK_BOT_TOKEN: ${{ secrets.QA_SLACK_API_KEY }}
