name: On Demand VRFV2 Plus Performance Test
on:
  workflow_dispatch:
    inputs:
      performanceTestType:
        description: Performance Test Type of test to run
        type: string
        required: true
      test_list_regex:
        description: "Regex for tests to run"
        required: false
        default: "(TestVRFV2PlusPerformance)"
      test_config_override_path:
        description: Path to a test config file used to override the default test config
        required: false
        type: string               
      test_secrets_override_key:
        description: 'Key to run tests with custom test secrets'
        required: false
        type: string 
      chainlink_version:
        description: Chainlink image version to use
        default: develop
        required: false
        type: string
      notify_user_id_on_failure:
        description: 'Enter Slack user ID to notify on test failure'
        required: false
        type: string        

jobs:
  set-tests-to-run:
    name: Set tests to run
    runs-on: ubuntu-latest
    outputs:
      test_list: ${{ steps.set-tests.outputs.test_list }}
    steps:
      - name: Generate Test List JSON
        id: set-tests
        run: |
          TEST_CMD='cd integration-tests/load && go test -v -count=1 -timeout 24h -run "${{ inputs.test_list_regex }}" ./vrfv2plus'
          TEST_CONFIG_OVERRIDE_PATH=${{ inputs.test_config_override_path }}
          TEST_TYPE=${{ inputs.performanceTestType }}

          TEST_LIST=$(jq -n -c \
            --arg test_cmd "$TEST_CMD" \
            --arg test_config_override_path "$TEST_CONFIG_OVERRIDE_PATH" \
            --arg TEST_TYPE "$TEST_TYPE" \
            '{
              "tests": [
                {
                  "id": "TestVRFv2Plus_Performance",
                  "path": "integration-tests/load/vrfv2plus/vrfv2plus_test.go",
                  "runs_on": "ubuntu-latest",
                  "test_env_type": "docker",
                  "test_cmd": $test_cmd,
                  "test_config_override_path": $test_config_override_path,
                  "test_env_vars": {
                    "TEST_TYPE": $TEST_TYPE   
                  }
                }
              ]
            }')

          echo "test_list=$TEST_LIST" >> $GITHUB_OUTPUT

  run-e2e-tests-workflow:
    name: Run E2E Tests
    needs: set-tests-to-run
    uses: smartcontractkit/.github/.github/workflows/run-e2e-tests.yml@aad83f232743646faa35f5ac03ee3829148d37ce
    with:
      custom_test_list_json: ${{ needs.set-tests-to-run.outputs.test_list }}
      chainlink_version: ${{ inputs.chainlink_version }}
      slack_notification_after_tests: always
      slack_notification_after_tests_name: "VRF V2 Plus Performance Tests with test config: ${{ inputs.test_config_override_path || 'default' }}"
      slack_notification_after_tests_notify_user_id_on_failure: ${{ inputs.notify_user_id_on_failure }}
    secrets:
      QA_AWS_REGION: ${{ secrets.QA_AWS_REGION }}
      QA_AWS_ROLE_TO_ASSUME: ${{ secrets.QA_AWS_ROLE_TO_ASSUME }}
      QA_AWS_ACCOUNT_NUMBER: ${{ secrets.QA_AWS_ACCOUNT_NUMBER }}
      QA_PYROSCOPE_INSTANCE: ${{ secrets.QA_PYROSCOPE_INSTANCE }}
      QA_PYROSCOPE_KEY: ${{ secrets.QA_PYROSCOPE_KEY }}
      QA_KUBECONFIG: ${{ secrets.QA_KUBECONFIG }}
      GRAFANA_INTERNAL_TENANT_ID: ${{ secrets.GRAFANA_INTERNAL_TENANT_ID }}
      GRAFANA_INTERNAL_BASIC_AUTH: ${{ secrets.GRAFANA_INTERNAL_BASIC_AUTH }}
      GRAFANA_INTERNAL_HOST: ${{ secrets.GRAFANA_INTERNAL_HOST }}
      GRAFANA_INTERNAL_URL_SHORTENER_TOKEN: ${{ secrets.GRAFANA_INTERNAL_URL_SHORTENER_TOKEN }}
      LOKI_TENANT_ID: ${{ secrets.LOKI_TENANT_ID }}
      LOKI_URL: ${{ secrets.LOKI_URL }}
      LOKI_BASIC_AUTH: ${{ secrets.LOKI_BASIC_AUTH }}
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}  
      AWS_REGION: ${{ secrets.QA_AWS_REGION }}
      AWS_OIDC_IAM_ROLE_VALIDATION_PROD_ARN: ${{ secrets.AWS_OIDC_IAM_ROLE_VALIDATION_PROD_ARN }}
      AWS_API_GW_HOST_GRAFANA: ${{ secrets.AWS_API_GW_HOST_GRAFANA }}        
      TEST_SECRETS_OVERRIDE_BASE64: ${{ secrets[inputs.test_secrets_override_key] }}
      SLACK_BOT_TOKEN: ${{ secrets.QA_SLACK_API_KEY }}
      SLACK_NOTIFICATION_AFTER_TESTS_CHANNEL_ID: ${{ secrets.QA_VRF_SLACK_CHANNEL }}
      SLACK_API_KEY: ${{ secrets.QA_SLACK_API_KEY }}
      SLACK_CHANNEL: ${{ secrets.QA_VRF_SLACK_CHANNEL }}      
