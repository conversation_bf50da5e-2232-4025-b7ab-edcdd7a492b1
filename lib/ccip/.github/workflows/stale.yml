# Workflow to manage automatically closing stale pull requests and issues.
# See configuration for configuration.
name: Manage stale Issues and PRs

on:
  schedule:
    - cron: "0 0 * * *" # Will be triggered every day at midnight UTC

jobs:
  stale:

    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write

    steps:
    - uses: actions/stale@28ca1036281a5e5922ead5184a1bbf96e5fc984e # v9.0.0
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        days-before-issue-stale: -1 # disables marking issues as stale automatically. Issues can still be marked as stale manually, in which the closure policy applies.
        stale-pr-message: 'This PR is stale because it has been open 45 days with no activity. Remove stale label or comment or this will be closed in 10 days.'
        close-pr-message: 'This PR was closed because it has been stalled for 10 days with no activity.'
        days-before-pr-stale: 45
        days-before-pr-close: 10
