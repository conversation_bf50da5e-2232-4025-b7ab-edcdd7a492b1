# This file specifies the GitHub runner for each E2E test and is utilized by all E2E CI workflows.
#
# Each entry in this file includes the following:
# - The GitHub runner (runs_on field) that will execute tests.
# - The tests that will be run by the runner.
# - The triggers (e.g., Run PR E2E Tests, Nightly E2E Tests) that should trigger these tests.
#
runner-test-matrix:

  # START: OCR tests

  # Example of 1 runner for all tests in integration-tests/smoke/ocr_test.go
  - id: smoke/ocr_test.go:* 
    path: integration-tests/smoke/ocr_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/ocr_test.go -timeout 30m -count=1 -test.parallel=2 -json
    pyroscope_env: ci-smoke-ocr-evm-simulated

  - id: soak/ocr_test.go:TestOCRv1Soak
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestOCRv1Soak$ -test.parallel=1 -timeout 900h -count=1 -json
    test_cmd_opts: 2>&1 | tee /tmp/gotest.log | gotestloghelper -ci -singlepackage -hidepassingtests=false
    test_secrets_required: true    
    test_env_vars:
      TEST_SUITE: soak

  - id: soak/ocr_test.go:TestOCRv2Soak
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestOCRv2Soak$ -test.parallel=1 -timeout 900h -count=1 -json
    test_cmd_opts: 2>&1 | tee /tmp/gotest.log | gotestloghelper -ci -singlepackage -hidepassingtests=false
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak

  - id: soak/ocr_test.go:TestOCRv2Soak_WemixTestnet
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestOCRv2Soak$ -test.parallel=1 -timeout 900h -count=1 -json
    test_config_override_path: integration-tests/testconfig/ocr2/overrides/wemix_testnet.toml
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak

  - id: soak/ocr_test.go:TestForwarderOCRv1Soak
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestForwarderOCRv1Soak$ -test.parallel=1 -timeout 900h -count=1 -json
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak

  - id: soak/ocr_test.go:TestForwarderOCRv2Soak
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestForwarderOCRv2Soak$ -test.parallel=1 -timeout 900h -count=1 -json
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak
      
  - id: soak/ocr_test.go:TestOCRSoak_GethReorgBelowFinality_FinalityTagDisabled
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run TestOCRSoak_GethReorgBelowFinality_FinalityTagDisabled -test.parallel=1 -timeout 900h -count=1 -json
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak      

  - id: soak/ocr_test.go:TestOCRSoak_GethReorgBelowFinality_FinalityTagEnabled
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestOCRSoak_GethReorgBelowFinality_FinalityTagEnabled$ -test.parallel=1 -timeout 900h -count=1 -json
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak      

  - id: soak/ocr_test.go:TestOCRSoak_GasSpike
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestOCRSoak_GasSpike$ -test.parallel=1 -timeout 900h -count=1 -json
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak      

  - id: soak/ocr_test.go:TestOCRSoak_ChangeBlockGasLimit
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestOCRSoak_ChangeBlockGasLimit$ -test.parallel=1 -timeout 900h -count=1 -json
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak   

  - id: soak/ocr_test.go:TestOCRSoak_RPCDownForAllCLNodes
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestOCRSoak_RPCDownForAllCLNodes$ -test.parallel=1 -timeout 900h -count=1 -json
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak  

  - id: soak/ocr_test.go:TestOCRSoak_RPCDownForHalfCLNodes
    path: integration-tests/soak/ocr_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ && go test soak/ocr_test.go -v -test.run ^TestOCRSoak_RPCDownForHalfCLNodes$ -test.parallel=1 -timeout 900h -count=1 -json
    test_secrets_required: true
    test_env_vars:
      TEST_SUITE: soak                             

  - id: smoke/forwarder_ocr_test.go:*
    path: integration-tests/smoke/forwarder_ocr_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/forwarder_ocr_test.go -timeout 30m -count=1 -test.parallel=2 -json
    pyroscope_env: ci-smoke-forwarder-ocr-evm-simulated

  - id: smoke/forwarders_ocr2_test.go:*
    path: integration-tests/smoke/forwarders_ocr2_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/forwarders_ocr2_test.go -timeout 30m -count=1 -test.parallel=2 -json
    pyroscope_env: ci-smoke-forwarder-ocr-evm-simulated

  - id: smoke/ocr2_test.go:*
    path: integration-tests/smoke/ocr2_test.go
    test_env_type: docker
    runs_on: ubuntu22.04-16cores-64GB
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/ocr2_test.go -timeout 30m -count=1 -test.parallel=6 -json
    pyroscope_env: ci-smoke-ocr2-evm-simulated
    test_env_vars:
      E2E_TEST_CHAINLINK_VERSION: '{{ env.DEFAULT_CHAINLINK_PLUGINS_VERSION }}' # This is the chainlink version that has the plugins
      
  - id: smoke/ocr2_test.go:*-plugins
    path: integration-tests/smoke/ocr2_test.go
    test_env_type: docker
    runs_on: ubuntu22.04-16cores-64GB
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/ocr2_test.go -timeout 30m -count=1 -test.parallel=6 -json
    pyroscope_env: ci-smoke-ocr2-plugins-evm-simulated
    test_env_vars:
      E2E_TEST_CHAINLINK_VERSION: '{{ env.DEFAULT_CHAINLINK_PLUGINS_VERSION }}' # This is the chainlink version that has the plugins
      ENABLE_OTEL_TRACES: true

  - id: chaos/ocr_chaos_test.go
    path: integration-tests/chaos/ocr_chaos_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    triggers:
      - Automation On Demand Tests
      - E2E Chaos Tests
    test_cmd: cd integration-tests/chaos && DETACH_RUNNER=false go test -test.run "^TestOCRChaos$" -v -test.parallel=10 -timeout 60m -count=1 -json
    test_env_vars:
      TEST_SUITE: chaos

  # END: OCR tests

  # START: Automation tests
  
  - id: smoke/automation_test.go:^TestAutomationBasic/registry_2_0|TestAutomationBasic/registry_2_1_conditional|TestAutomationBasic/registry_2_1_logtrigger$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run "^TestAutomationBasic/registry_2_0|TestAutomationBasic/registry_2_1_conditional|TestAutomationBasic/registry_2_1_logtrigger$" -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated

  - id: smoke/automation_test.go:^TestAutomationBasic/registry_2_1_with_mercury_v02|TestAutomationBasic/registry_2_1_with_mercury_v03|TestAutomationBasic/registry_2_1_with_logtrigger_and_mercury_v02$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run "^TestAutomationBasic/registry_2_1_with_mercury_v02|TestAutomationBasic/registry_2_1_with_mercury_v03|TestAutomationBasic/registry_2_1_with_logtrigger_and_mercury_v02$" -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated

  - id: smoke/automation_test.go:^TestAutomationBasic/registry_2_2_conditional|TestAutomationBasic/registry_2_2_logtrigger|TestAutomationBasic/registry_2_2_with_mercury_v02$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run "^TestAutomationBasic/registry_2_2_conditional|TestAutomationBasic/registry_2_2_logtrigger|TestAutomationBasic/registry_2_2_with_mercury_v02$" -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated

  - id: smoke/automation_test.go:^TestAutomationBasic/registry_2_2_with_mercury_v03|TestAutomationBasic/registry_2_2_with_logtrigger_and_mercury_v02$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run "^TestAutomationBasic/registry_2_2_with_mercury_v03|TestAutomationBasic/registry_2_2_with_logtrigger_and_mercury_v02$" -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated

  - id: smoke/automation_test.go:^TestAutomationBasic/registry_2_3_conditional_native|TestAutomationBasic/registry_2_3_conditional_link$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run "^TestAutomationBasic/registry_2_3_conditional_native|TestAutomationBasic/registry_2_3_conditional_link$" -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated

  - id: smoke/automation_test.go:^TestAutomationBasic/registry_2_3_logtrigger_native|TestAutomationBasic/registry_2_3_logtrigger_link$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run "^TestAutomationBasic/registry_2_3_logtrigger_native|TestAutomationBasic/registry_2_3_logtrigger_link$" -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated

  - id: smoke/automation_test.go:^TestAutomationBasic/registry_2_3_with_mercury_v03_link|TestAutomationBasic/registry_2_3_with_logtrigger_and_mercury_v02_link$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run "^TestAutomationBasic/registry_2_3_with_mercury_v03_link|TestAutomationBasic/registry_2_3_with_logtrigger_and_mercury_v02_link$" -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated                

  - id: smoke/automation_test.go:^TestSetUpkeepTriggerConfig$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestSetUpkeepTriggerConfig$ -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/automation_test.go:^TestAutomationAddFunds$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationAddFunds$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/automation_test.go:^TestAutomationPauseUnPause$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationPauseUnPause$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/automation_test.go:^TestAutomationRegisterUpkeep$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationRegisterUpkeep$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/automation_test.go:^TestAutomationPauseRegistry$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationPauseRegistry$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/automation_test.go:^TestAutomationKeeperNodesDown$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationKeeperNodesDown$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/automation_test.go:^TestAutomationPerformSimulation$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationPerformSimulation$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/automation_test.go:^TestAutomationCheckPerformGasLimit$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationCheckPerformGasLimit$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/automation_test.go:^TestUpdateCheckData$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestUpdateCheckData$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/automation_test.go:^TestSetOffchainConfigWithMaxGasPrice$
    path: integration-tests/smoke/automation_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestSetOffchainConfigWithMaxGasPrice$ -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-automation-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperBasicSmoke$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperBasicSmoke$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperBlockCountPerTurn$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperBlockCountPerTurn$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperSimulation$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperSimulation$ -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperCheckPerformGasLimit$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperCheckPerformGasLimit$ -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperRegisterUpkeep$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperRegisterUpkeep$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperAddFunds$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperAddFunds$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperRemove$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperRemove$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated     
    
  - id: smoke/keeper_test.go:^TestKeeperPauseRegistry$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperPauseRegistry$ -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperMigrateRegistry$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperMigrateRegistry$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperNodeDown$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperNodeDown$ -test.parallel=3 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated  

  - id: smoke/keeper_test.go:^TestKeeperPauseUnPauseUpkeep$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperPauseUnPauseUpkeep$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated         

  - id: smoke/keeper_test.go:^TestKeeperUpdateCheckData$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperUpdateCheckData$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated         

  - id: smoke/keeper_test.go:^TestKeeperJobReplacement$
    path: integration-tests/smoke/keeper_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestKeeperJobReplacement$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-keeper-evm-simulated                           

  - id: load/automationv2_1/automationv2_1_test.go:TestLogTrigger
    path: integration-tests/load/automationv2_1/automationv2_1_test.go
    runs_on: ubuntu-latest
    test_env_type: k8s-remote-runner
    test_cmd: cd integration-tests/load/automationv2_1 && go test -test.run TestLogTrigger -test.parallel=1 -timeout 60m -count=1 -json
    remote_runner_memory: 4Gi
    test_secrets_required: true
    test_env_vars:
      TEST_LOG_LEVEL: info
      TEST_SUITE: automationv2_1
    pyroscope_env: automation-load-test

  - id: smoke/automation_upgrade_test.go:^TestAutomationNodeUpgrade/registry_2_0
    path: integration-tests/smoke/automation_upgrade_test.go
    test_env_type: docker
    runs_on: ubuntu22.04-8cores-32GB
    triggers:
      - Automation Nightly Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationNodeUpgrade/registry_2_0 -test.parallel=1 -timeout 60m -count=1 -json
    test_env_vars:
      E2E_TEST_CHAINLINK_IMAGE: public.ecr.aws/chainlink/chainlink
      E2E_TEST_CHAINLINK_VERSION: latest
      E2E_TEST_CHAINLINK_UPGRADE_IMAGE: '{{ env.QA_CHAINLINK_IMAGE }}'
      E2E_TEST_CHAINLINK_UPGRADE_VERSION: '{{ env.DEFAULT_CHAINLINK_UPGRADE_VERSION }}'
    pyroscope_env: ci-smoke-automation-upgrade-tests

  - id: smoke/automation_upgrade_test.go:^TestAutomationNodeUpgrade/registry_2_1
    path: integration-tests/smoke/automation_upgrade_test.go
    test_env_type: docker
    runs_on: ubuntu22.04-8cores-32GB
    triggers:
      - Automation Nightly Tests    
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationNodeUpgrade/registry_2_1 -test.parallel=5 -timeout 60m -count=1 -json
    test_env_vars:
      E2E_TEST_CHAINLINK_IMAGE: public.ecr.aws/chainlink/chainlink
      E2E_TEST_CHAINLINK_VERSION: latest
      E2E_TEST_CHAINLINK_UPGRADE_IMAGE: '{{ env.QA_CHAINLINK_IMAGE }}'
      E2E_TEST_CHAINLINK_UPGRADE_VERSION: '{{ env.DEFAULT_CHAINLINK_UPGRADE_VERSION }}'
    pyroscope_env: ci-smoke-automation-upgrade-tests

  - id: smoke/automation_upgrade_test.go:^TestAutomationNodeUpgrade/registry_2_2
    path: integration-tests/smoke/automation_upgrade_test.go
    test_env_type: docker
    runs_on: ubuntu22.04-8cores-32GB
    triggers:
      - Automation Nightly Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestAutomationNodeUpgrade/registry_2_2 -test.parallel=5 -timeout 60m -count=1 -json
    test_env_vars:
      E2E_TEST_CHAINLINK_IMAGE: public.ecr.aws/chainlink/chainlink
      E2E_TEST_CHAINLINK_VERSION: latest
      E2E_TEST_CHAINLINK_UPGRADE_IMAGE: '{{ env.QA_CHAINLINK_IMAGE }}'
      E2E_TEST_CHAINLINK_UPGRADE_VERSION: '{{ env.DEFAULT_CHAINLINK_UPGRADE_VERSION }}'
    pyroscope_env: ci-smoke-automation-upgrade-tests

  - id: reorg/automation_reorg_test.go^TestAutomationReorg/registry_2_0
    path: integration-tests/reorg/automation_reorg_test.go
    runs_on: ubuntu-latest
    test_env_type: docker
    test_env_vars:
      TEST_SUITE: reorg
    triggers:
      - Automation On Demand Tests
    test_cmd: cd integration-tests/reorg && DETACH_RUNNER=false go test -v -test.run ^TestAutomationReorg/registry_2_0 -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-automation-on-demand-reorg

  - id: reorg/automation_reorg_test.go^TestAutomationReorg/registry_2_1
    path: integration-tests/reorg/automation_reorg_test.go
    runs_on: ubuntu-latest
    test_env_type: docker
    test_env_vars:
      TEST_SUITE: reorg
    triggers:
      - Automation On Demand Tests
    test_cmd: cd integration-tests/reorg && DETACH_RUNNER=false go test -v -test.run ^TestAutomationReorg/registry_2_1 -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-automation-on-demand-reorg

  - id: reorg/automation_reorg_test.go^TestAutomationReorg/registry_2_2
    path: integration-tests/reorg/automation_reorg_test.go
    runs_on: ubuntu-latest
    test_env_type: docker
    test_env_vars:
      TEST_SUITE: reorg
    triggers:
      - Automation On Demand Tests
    test_cmd: cd integration-tests/reorg && DETACH_RUNNER=false go test -v -test.run ^TestAutomationReorg/registry_2_2 -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-automation-on-demand-reorg

  - id: reorg/automation_reorg_test.go^TestAutomationReorg/registry_2_3
    path: integration-tests/reorg/automation_reorg_test.go
    runs_on: ubuntu-latest
    test_env_type: docker
    test_env_vars:
      TEST_SUITE: reorg
    triggers:
      - Automation On Demand Tests
    test_cmd: cd integration-tests/reorg && DETACH_RUNNER=false go test -v -test.run ^TestAutomationReorg/registry_2_3 -test.parallel=2 -timeout 30m -count=1 -json
    pyroscope_env: ci-automation-on-demand-reorg

  - id: chaos/automation_chaos_test.go
    path: integration-tests/chaos/automation_chaos_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    triggers:
      - Automation On Demand Tests
      - E2E Chaos Tests
    test_cmd: cd integration-tests/chaos && DETACH_RUNNER=false go test -v -test.run ^TestAutomationChaos$ -test.parallel=20 -timeout 60m -count=1 -json
    pyroscope_env: ci-automation-on-demand-chaos
    test_env_vars:
      TEST_SUITE: chaos

  - id: benchmark/automation_test.go:TestAutomationBenchmark
    path: integration-tests/benchmark/automation_test.go
    test_env_type: k8s-remote-runner
    remote_runner_memory: 4Gi
    runs_on: ubuntu-latest
    # triggers:
      # - Nightly E2E Tests
    test_cmd: cd integration-tests/benchmark && go test -v -test.run ^TestAutomationBenchmark$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-benchmark-automation-nightly
    test_env_vars:
      TEST_LOG_LEVEL: info
      TEST_SUITE: benchmark
      TEST_TYPE: benchmark

  - id: soak/automation_test.go:TestAutomationBenchmark
    path: integration-tests/benchmark/automation_test.go
    test_env_type: k8s-remote-runner
    remote_runner_memory: 4Gi
    runs_on: ubuntu-latest
    # triggers:
    # - Nightly E2E Tests
    test_cmd: cd integration-tests/benchmark && go test -v -test.run ^TestAutomationBenchmark$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-benchmark-automation-nightly
    test_env_vars:
      TEST_LOG_LEVEL: info
      TEST_SUITE: benchmark
      TEST_TYPE: soak

  # END: Automation tests

  # START: VRF tests

  - id: smoke/vrfv2_test.go:TestVRFv2Basic
    path: integration-tests/smoke/vrfv2_test.go
    runs_on: ubuntu22.04-8cores-32GB
    test_env_type: docker
    test_cmd: cd integration-tests/smoke && go test -v -test.run TestVRFv2Basic -test.parallel=1 -timeout 30m -count=1 -json
    test_secrets_required: true
    triggers:
      - On Demand VRFV2 Smoke Test (Ethereum clients)

  - id: load/vrfv2plus/vrfv2plus_test.go:^TestVRFV2PlusPerformance$Smoke
    path: integration-tests/load/vrfv2plus/vrfv2plus_test.go
    runs_on: ubuntu22.04-8cores-32GB
    test_env_type: docker
    test_cmd: cd integration-tests/load/vrfv2plus && go test -v -test.run ^TestVRFV2PlusPerformance$ -test.parallel=1 -timeout 24h -count=1 -json
    test_config_override_required: true
    test_secrets_required: true
    test_env_vars:
      TEST_TYPE: Smoke
    triggers:
      - On Demand VRFV2 Plus Performance Test          

  - id: load/vrfv2plus/vrfv2plus_test.go:^TestVRFV2PlusBHSPerformance$Smoke
    path: integration-tests/load/vrfv2plus/vrfv2plus_test.go
    runs_on: ubuntu22.04-8cores-32GB
    test_env_type: docker
    test_cmd: cd integration-tests/load/vrfv2plus && go test -v -test.run ^TestVRFV2PlusBHSPerformance$ -test.parallel=1 -timeout 24h -count=1 -json
    test_config_override_required: true
    test_secrets_required: true
    test_env_vars:
      TEST_TYPE: Smoke
    triggers:
      - On Demand VRFV2 Plus Performance Test  

  - id: load/vrfv2/vrfv2_test.go:^TestVRFV2Performance$Smoke
    path: integration-tests/load/vrfv2/vrfv2_test.go
    runs_on: ubuntu22.04-8cores-32GB
    test_env_type: docker
    test_cmd: cd integration-tests/load/vrfv2 && go test -v -test.run ^TestVRFV2Performance$ -test.parallel=1 -timeout 24h -count=1 -json
    test_config_override_required: true
    test_secrets_required: true
    test_env_vars:
      TEST_TYPE: Smoke      
    triggers:
      - On Demand VRFV2 Performance Test      

  - id: load/vrfv2/vrfv2_test.go:^TestVRFV2PlusBHSPerformance$Smoke
    path: integration-tests/load/vrfv2/vrfv2_test.go
    runs_on: ubuntu22.04-8cores-32GB
    test_env_type: docker
    test_cmd: cd integration-tests/load/vrfv2 && go test -v -test.run ^TestVRFV2PlusBHSPerformance$ -test.parallel=1 -timeout 24h -count=1 -json
    test_config_override_required: true
    test_secrets_required: true
    test_env_vars:
      TEST_TYPE: Smoke
    triggers:
      - On Demand VRFV2 Performance Test

  - id: smoke/vrf_test.go:*
    path: integration-tests/smoke/vrf_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/vrf_test.go -timeout 30m -count=1 -test.parallel=2 -json
    pyroscope_env: ci-smoke-vrf-evm-simulated

  - id: smoke/vrfv2_test.go:*
    path: integration-tests/smoke/vrfv2_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/vrfv2_test.go -timeout 30m -count=1 -test.parallel=6 -json
    pyroscope_env: ci-smoke-vrf2-evm-simulated

  - id: smoke/vrfv2plus_test.go:*
    path: integration-tests/smoke/vrfv2plus_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/vrfv2plus_test.go -timeout 30m -count=1 -test.parallel=9 -json
    pyroscope_env: ci-smoke-vrf2plus-evm-simulated

  # VRFv2Plus tests on any live testnet (test_config_override_path required)
  # Tests have to run in sequence because of a single private key used
  - id: TestVRFv2Plus_LiveTestnets
    path: integration-tests/smoke/vrfv2plus_test.go
    runs_on: ubuntu-latest
    test_env_type: docker
    test_cmd: cd integration-tests/smoke && go test -v -test.run "TestVRFv2Plus$/(Link_Billing|Native_Billing|Direct_Funding)|TestVRFV2PlusWithBHS" -test.parallel=1 -timeout 2h -count=1 -json

  # VRFv2Plus release tests on Sepolia testnet
  - id: TestVRFv2Plus_Release_Sepolia
    path: integration-tests/smoke/vrfv2plus_test.go
    runs_on: ubuntu-latest
    test_env_type: docker
    test_cmd: cd integration-tests/smoke && go test -v -test.run "TestVRFv2Plus$/(Link_Billing|Native_Billing|Direct_Funding)|TestVRFV2PlusWithBHS" -test.parallel=1 -timeout 2h -count=1 -json
    test_config_override_path: integration-tests/testconfig/vrfv2plus/overrides/new_env/sepolia_new_env_test_config.toml
    triggers:
      - VRF E2E Release Tests

  # END: VRF tests

  # START: LogPoller tests

  - id: smoke/log_poller_test.go:^TestLogPollerFewFiltersFixedDepth$
    path: integration-tests/smoke/log_poller_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestLogPollerFewFiltersFixedDepth$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-log_poller-evm-simulated

  - id: smoke/log_poller_test.go:^TestLogPollerFewFiltersFinalityTag$
    path: integration-tests/smoke/log_poller_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestLogPollerFewFiltersFinalityTag$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-log_poller-evm-simulated

  - id: smoke/log_poller_test.go:^TestLogPollerWithChaosFixedDepth$
    path: integration-tests/smoke/log_poller_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestLogPollerWithChaosFixedDepth$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-log_poller-evm-simulated

  - id: smoke/log_poller_test.go:^TestLogPollerWithChaosFinalityTag$
    path: integration-tests/smoke/log_poller_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestLogPollerWithChaosFinalityTag$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-log_poller-evm-simulated

  - id: smoke/log_poller_test.go:^TestLogPollerWithChaosPostgresFinalityTag$
    path: integration-tests/smoke/log_poller_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestLogPollerWithChaosPostgresFinalityTag$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-log_poller-evm-simulated

  - id: smoke/log_poller_test.go:^TestLogPollerWithChaosPostgresFixedDepth$
    path: integration-tests/smoke/log_poller_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestLogPollerWithChaosPostgresFixedDepth$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-log_poller-evm-simulated

  - id: smoke/log_poller_test.go:^TestLogPollerReplayFixedDepth$
    path: integration-tests/smoke/log_poller_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestLogPollerReplayFixedDepth$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-log_poller-evm-simulated

  - id: smoke/log_poller_test.go:^TestLogPollerReplayFinalityTag$
    path: integration-tests/smoke/log_poller_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/smoke && go test -test.run ^TestLogPollerReplayFinalityTag$ -test.parallel=1 -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-log_poller-evm-simulated

  # END: LogPoller tests

  # START: Other tests

  - id: smoke/runlog_test.go:*
    path: integration-tests/smoke/runlog_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/runlog_test.go -timeout 30m -test.parallel=2 -count=1 -json
    pyroscope_env: ci-smoke-runlog-evm-simulated

  - id: smoke/cron_test.go:*
    path: integration-tests/smoke/cron_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/cron_test.go -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-cron-evm-simulated

  - id: smoke/flux_test.go:*
    path: integration-tests/smoke/flux_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/flux_test.go -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-flux-evm-simulated    

  - id: smoke/reorg_above_finality_test.go:*
    path: integration-tests/smoke/reorg_above_finality_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/reorg_above_finality_test.go -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-reorg-above-finality-evm-simulated
    
  - id: migration/upgrade_version_test.go:*
    path: integration-tests/migration/upgrade_version_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/migration && go test upgrade_version_test.go -timeout 30m -count=1 -test.parallel=2 -json
    test_env_vars:
      E2E_TEST_CHAINLINK_IMAGE: public.ecr.aws/w0i8p0z9/chainlink-ccip
      E2E_TEST_CHAINLINK_VERSION: '{{ env.LATEST_CHAINLINK_RELEASE_VERSION }}'
      E2E_TEST_CHAINLINK_UPGRADE_IMAGE: '{{ env.QA_CHAINLINK_IMAGE }}'
      E2E_TEST_CHAINLINK_UPGRADE_VERSION: '{{ env.DEFAULT_CHAINLINK_VERSION }}'

  - id: smoke/job_distributor_test.go:*
    path: integration-tests/smoke/job_distributor_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E Core Tests
      - Merge Queue E2E Core Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ && go test smoke/job_distributor_test.go -timeout 30m -count=1 -json
    pyroscope_env: ci-smoke-jd-evm-simulated

  # END: Other tests

  # START: CCIP tests

  - id: ccip-smoke
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPForBidirectionalLane$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2

  - id: ccip-smoke-1.4-pools
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPForBidirectionalLane$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2
    test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/contract-version1.4.toml
     
  - id: ccip-smoke-usdc
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPForBidirectionalLane$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2
    test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/usdc_mock_deployment.toml

  - id: ccip-smoke-lbtc-32bytes-destination-pool-data
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPForBidirectionalLane$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2
    test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/lbtc_mock_deployment_with_32bytes_data.toml

  - id: ccip-smoke-lbtc-non32bytes-destination-pool-data
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPForBidirectionalLane$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2
    test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/lbtc_mock_deployment_with_non32bytes_data.toml

  - id: ccip-smoke-db-compatibility
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPForBidirectionalLane$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2
    test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/db-compatibility.toml

  - id: ccip-smoke-leader-lane
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    # Leader lane test is flakey in Core repo - Need to fix CCIP-3074 to enable it.
    triggers:
      # - PR E2E CCIP Tests
      # - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPForBidirectionalLane$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2
    test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/leader-lane.toml

  - id: ccip-tests/smoke/ccip_test.go:^TestSmokeCCIPTokenPoolRateLimits$
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPTokenPoolRateLimits$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2

  - id: ccip-tests/smoke/ccip_test.go:^TestSmokeCCIPMulticall$
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPMulticall$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2

  - id: ccip-tests/smoke/ccip_test.go:^TestSmokeCCIPManuallyExecuteAfterExecutionFailingDueToInsufficientGas$
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPManuallyExecuteAfterExecutionFailingDueToInsufficientGas$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2

  - id: ccip-tests/smoke/ccip_test.go:^TestSmokeCCIPOnRampLimits$
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPOnRampLimits$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2

  - id: ccip-tests/smoke/ccip_test.go:^TestSmokeCCIPOffRampCapacityLimit$
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPOffRampCapacityLimit$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2      

  - id: ccip-tests/smoke/ccip_test.go:^TestSmokeCCIPOffRampAggRateLimit$
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPOffRampAggRateLimit$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2

  - id: ccip-tests/smoke/ccip_test.go:^TestSmokeCCIPReorgBelowFinality$
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPReorgBelowFinality$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2
    test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/ccip-reorg.toml

  - id: ccip-tests/smoke/ccip_test.go:^TestSmokeCCIPReorgAboveFinalityAtDestination$
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPReorgAboveFinalityAtDestination$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2
    test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/ccip-reorg.toml

  - id: ccip-tests/smoke/ccip_test.go:^TestSmokeCCIPReorgAboveFinalityAtSource$
    path: integration-tests/ccip-tests/smoke/ccip_test.go
    test_env_type: docker
    runs_on: ubuntu-latest
    triggers:
      - PR E2E CCIP Tests
      - Merge Queue E2E CCIP Tests
      - Nightly E2E Tests
    test_cmd: cd integration-tests/ccip-tests/smoke && go test ccip_test.go -test.run ^TestSmokeCCIPReorgAboveFinalityAtSource$ -timeout 30m -count=1 -test.parallel=1 -json
    test_env_vars:
      E2E_TEST_SELECTED_NETWORK: SIMULATED_1,SIMULATED_2
    test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/ccip-reorg.toml

  - id: integration-tests/ccip-tests/load/ccip_test.go:TestLoadCCIPStableRPS
    path: integration-tests/ccip-tests/load/ccip_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    test_cmd: cd integration-tests/ccip-tests/load && DETACH_RUNNER=false go test -test.run ^TestLoadCCIPStableRPS$ -timeout 70m -count=1 -test.parallel=1 -json
    test_env_vars:
      TEST_SUITE: ccip-load
      E2E_TEST_GRAFANA_DASHBOARD_URL: "/d/6vjVx-1V8/ccip-long-running-tests"
    triggers:
      - E2E CCIP Load Tests  
    test_artifacts_on_failure: 
      - ./integration-tests/load/logs/payload_ccip.json

  # Enable when CCIP-2277 is resolved
  #
  # - id: integration-tests/ccip-tests/load/ccip_test.go:TestLoadCCIPStableRPSAfterARMCurseAndUncurse
  #   path: integration-tests/ccip-tests/load/ccip_test.go
  #   test_env_type: k8s-remote-runner
  #   runs_on: ubuntu-latest
  #   test_cmd: cd integration-tests/ccip-tests/load && DETACH_RUNNER=false go test -test.run $TestLoadCCIPStableRPSAfterARMCurseAndUncurse$ -timeout 70m -count=1 -test.parallel=1 -json
  #   test_config_override_path: integration-tests/ccip-tests/testconfig/tomls/load-with-arm-curse-uncurse.toml
  #   test_env_vars:
  #     E2E_TEST_GRAFANA_DASHBOARD_URL: "/d/6vjVx-1V8/ccip-long-running-tests"
  #   triggers:
  #     - E2E CCIP Load Tests  
  #   test_artifacts_on_failure: 
  #     - ./integration-tests/load/logs/payload_ccip.json

  - id: ccip-tests/chaos/ccip_test.go
    path: integration-tests/ccip-tests/chaos/ccip_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    triggers:
      - E2E CCIP Chaos Tests
    test_cmd: cd integration-tests/ccip-tests/chaos && DETACH_RUNNER=false go test ccip_test.go -v -test.parallel=11 -timeout 60m -count=1 -json
    test_env_vars:
      TEST_SUITE: chaos
      TEST_TRIGGERED_BY: ccip-cron-chaos-eth
      TEST_LOG_LEVEL: debug

  - id: ccip-tests/load/ccip_test.go:^TestLoadCCIPStableWithPodChaosDiffCommitAndExec
    path: integration-tests/ccip-tests/load/ccip_test.go
    test_env_type: k8s-remote-runner
    runs_on: ubuntu-latest
    triggers:
      # Disabled until CCIP-2555 is resolved
      # - E2E CCIP Chaos Tests
    test_cmd: cd integration-tests/ccip-tests/load && DETACH_RUNNER=false go test -run '^TestLoadCCIPStableWithPodChaosDiffCommitAndExec' -v -test.parallel=4 -timeout 120m -count=1 -json
    test_env_vars:
      TEST_SUITE: chaos
      TEST_TRIGGERED_BY: ccip-cron-chaos-eth
      TEST_LOG_LEVEL: debug
      E2E_TEST_GRAFANA_DASHBOARD_URL: /d/6vjVx-1V8/ccip-long-running-tests
            
  # END: CCIP tests