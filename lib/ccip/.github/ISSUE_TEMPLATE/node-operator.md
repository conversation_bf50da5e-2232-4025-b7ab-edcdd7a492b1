---
name: Node Operator Issue
about: Report an issue encountered while operating a Chainlink node.
title: '[NODE] <replace with issue title>'
labels: 'Node Operator'
assignees: ''
---

**Description**
[replace this line with a clear and concise description of the issue you are experiencing]

**Basic Information**
[replace this line with basic information about the issue you are experiencing, including but not limited to all relevant logs and any other relevant information, such as if you are using a Docker container to run the node, job specification, oracle contract address, transaction IDs, etc.]

- Network: [e.g. Ethereum Mainnet, Ropsten]
- Blockchain Client: [name and version of blockchain client e.g. Geth v1.9.6]
- Go Version: [e.g. v1.12]
- Operating System: [name and version of operating system running Chainlink node]
- Commit: [log INFO line when starting node]
- Hosting Provider: [e.g. AWS, GCP, self-hosted]
- Startup Command: [e.g. `docker run smartcontract/chainlink local n`]

**Environment Variables**
[replace this line with the output of the environment variables when running the node in debug mode]

**Steps to Reproduce**
[replace this line with detailed steps to reproduce the issue you are experiencing]

**Additional Information**
[replace this line with any additional information you would like to provide, such as screenshots illustrating the issue]
