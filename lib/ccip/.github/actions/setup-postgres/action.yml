name: Setup Postgresql
description: Setup postgres docker container via docker-compose, allowing usage of a custom command, see https://github.com/orgs/community/discussions/26688
inputs:
  base-path:
    description: Path to the base of the repo
    required: false
    default: .
runs:
  using: composite
  steps:
    - name: Start postgres service
      run: docker compose up -d
      shell: bash
      working-directory: ${{ inputs.base-path }}/.github/actions/setup-postgres
    - name: Wait for postgres service to be healthy
      run: ./wait-for-healthy-postgres.sh
      shell: bash
      working-directory: ${{ inputs.base-path }}/.github/actions/setup-postgres
