{"name": "delete-deployments", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"start": "ts-node -T .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "engines": {"node": ">=18", "pnpm": ">=9"}, "dependencies": {"@actions/core": "^1.10.1", "@octokit/action": "^6.1.0", "@octokit/plugin-retry": "^6.0.1", "@octokit/plugin-throttling": "^7.0.0", "ts-node": "^10.9.2"}, "devDependencies": {"@octokit/types": "^11.1.0", "@types/node": "^20.12.10", "typescript": "^5.4.5"}}