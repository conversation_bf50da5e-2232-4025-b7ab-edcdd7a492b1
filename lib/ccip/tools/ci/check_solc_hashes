#!/usr/bin/env bash

# This script checks that the locally installed solc binaries match the given hashes.
# It is intended to be run on CI to give some guarantee of the integrity of the installed solidity compilers.
# Only supports linux-amd64 binaries.

set -e

SOLC_8_6_LOCAL_PATH="$HOME/.solc-select/artifacts/solc-0.8.6/solc-0.8.6"

SOLC_8_6_LOCAL_SHA=`sha256sum -b $SOLC_8_6_LOCAL_PATH | cut -d " " -f1`
SOLC_8_6_EXPECTED_SHA="abd5c4f3f262bc3ed7951b968c63f98e83f66d9a5c3568ab306eac49250aec3e"

if [ "$SOLC_8_6_LOCAL_SHA" != "$SOLC_8_6_EXPECTED_SHA" ]; then
  printf "solc 0.8.6 did not match checksum.\nGot '$SOLC_8_6_LOCAL_SHA'\nExpected '$SOLC_8_6_EXPECTED_SHA'\n"
  exit 1
fi

