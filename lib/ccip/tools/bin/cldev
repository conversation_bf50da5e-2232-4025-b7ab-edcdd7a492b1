#!/usr/bin/env bash

set -e
LDFLAGS="`tools/bin/ldflags`"
export CL_DATABASE_URL="${CL_DATABASE_URL:=postgresql://localhost:5432/chainlink_dev?sslmode=disable}"
export CL_CONFIG=${CL_CONFIG:"WebServer.TLS.HTTPSPort = 0"}

case "$1" in
    node | core | n)
      key='0x9CA9d2D5E04012C9Ed24C0e513C9bfAa4A2dD77f'
      echo "** Running node"
      go run -ldflags "$LDFLAGS" . --  node start -d -p tools/secrets/password.txt -a tools/secrets/apicredentials
      ;;
    *)
      go run . -- "$@"
      ;;
esac
