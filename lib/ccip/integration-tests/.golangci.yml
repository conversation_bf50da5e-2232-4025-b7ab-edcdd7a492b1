run:
  timeout: 15m
linters:
  enable:
    - exhaustive
    - exportloopref
    - revive
    - goimports
    - gosec
    - misspell
    - rowserrcheck
    - errorlint
linters-settings:
  exhaustive:
    default-signifies-exhaustive: true
  goimports:
    local-prefixes: github.com/smartcontractkit/chainlink
  golint:
    min-confidence: 0.999
  gosec:
    excludes:
      - G101
  govet:
    enable:
      - shadow
  revive:
    confidence: 0.8
    rules:
      - name: blank-imports
      - name: context-as-argument
      - name: context-keys-type
      - name: dot-imports
      - name: error-return
      - name: error-strings
      - name: error-naming
      - name: if-return
      - name: increment-decrement
      # - name: var-naming // doesn't work with some generated names
      - name: var-declaration
      - name: package-comments
      - name: range
      - name: receiver-naming
      - name: time-naming
      - name: unexported-return
      - name: indent-error-flow
      - name: errorf
      - name: empty-block
      - name: superfluous-else
      #- name: unused-parameter
      - name: unreachable-code
      - name: redefines-builtin-id
      - name: waitgroup-by-value
      - name: unconditional-recursion
      - name: struct-tag
      - name: string-format
      - name: string-of-int
      - name: range-val-address
      - name: range-val-in-closure
      - name: modifies-value-receiver
      - name: modifies-parameter
      - name: identical-branches
      - name: get-return
      # - name: flag-parameter // probably one we should work on doing better at in the future
      # - name: early-return // probably one we should work on doing better at in the future
      - name: defer
      - name: constant-logical-expr
      - name: confusing-naming
      - name: confusing-results
      - name: bool-literal-in-expr
      - name: atomic
issues:
  exclude-rules:
    - path: deployment/memory/(.+)\.go
      linters:
        - revive
    - text: "^G404: Use of weak random number generator"
      linters:
        - gosec
    - linters:
        - govet
      text: "declaration of \"err\" shadows"
