// Code generated by github.com/Khan/genqlient, DO NOT EDIT.

package generated

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/Khan/genqlient/graphql"
	"github.com/smartcontractkit/chainlink/v2/core/web/gqlscalar"
)

// ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload includes the requested fields of the GraphQL interface ApproveJobProposalSpecPayload.
//
// ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload is implemented by the following types:
// ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess
// ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError
// ApproveJobProposalSpecApproveJobProposalSpecNotFoundError
type ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload interface {
	implementsGraphQLInterfaceApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess) implementsGraphQLInterfaceApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload() {
}
func (v *ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError) implementsGraphQLInterfaceApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload() {
}
func (v *ApproveJobProposalSpecApproveJobProposalSpecNotFoundError) implementsGraphQLInterfaceApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload() {
}

func __unmarshalApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload(b []byte, v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "ApproveJobProposalSpecSuccess":
		*v = new(ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess)
		return json.Unmarshal(b, *v)
	case "JobAlreadyExistsError":
		*v = new(ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError)
		return json.Unmarshal(b, *v)
	case "NotFoundError":
		*v = new(ApproveJobProposalSpecApproveJobProposalSpecNotFoundError)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing ApproveJobProposalSpecPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload: "%v"`, tn.TypeName)
	}
}

func __marshalApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload(v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess:
		typename = "ApproveJobProposalSpecSuccess"

		result := struct {
			TypeName string `json:"__typename"`
			*ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess
		}{typename, v}
		return json.Marshal(result)
	case *ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError:
		typename = "JobAlreadyExistsError"

		result := struct {
			TypeName string `json:"__typename"`
			*ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError
		}{typename, v}
		return json.Marshal(result)
	case *ApproveJobProposalSpecApproveJobProposalSpecNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*ApproveJobProposalSpecApproveJobProposalSpecNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload: "%T"`, v)
	}
}

// ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess includes the requested fields of the GraphQL type ApproveJobProposalSpecSuccess.
type ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess struct {
	Typename string                                                                                       `json:"__typename"`
	Spec     ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec `json:"spec"`
}

// GetTypename returns ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess.Typename, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess) GetTypename() string {
	return v.Typename
}

// GetSpec returns ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess.Spec, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccess) GetSpec() ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec {
	return v.Spec
}

// ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec includes the requested fields of the GraphQL type JobProposalSpec.
type ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec struct {
	Id              string     `json:"id"`
	Definition      string     `json:"definition"`
	Version         int        `json:"version"`
	Status          SpecStatus `json:"status"`
	StatusUpdatedAt string     `json:"statusUpdatedAt"`
	CreatedAt       string     `json:"createdAt"`
	UpdatedAt       string     `json:"updatedAt"`
}

// GetId returns ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec.Id, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec) GetId() string {
	return v.Id
}

// GetDefinition returns ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec.Definition, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec) GetDefinition() string {
	return v.Definition
}

// GetVersion returns ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec.Version, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec) GetVersion() int {
	return v.Version
}

// GetStatus returns ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec.Status, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec) GetStatus() SpecStatus {
	return v.Status
}

// GetStatusUpdatedAt returns ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec.StatusUpdatedAt, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec) GetStatusUpdatedAt() string {
	return v.StatusUpdatedAt
}

// GetCreatedAt returns ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec.CreatedAt, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec) GetCreatedAt() string {
	return v.CreatedAt
}

// GetUpdatedAt returns ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec.UpdatedAt, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecSuccessSpecJobProposalSpec) GetUpdatedAt() string {
	return v.UpdatedAt
}

// ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError includes the requested fields of the GraphQL type JobAlreadyExistsError.
type ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError.Typename, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError) GetTypename() string {
	return v.Typename
}

// GetMessage returns ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError.Message, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError) GetMessage() string {
	return v.Message
}

// GetCode returns ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError.Code, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecJobAlreadyExistsError) GetCode() ErrorCode {
	return v.Code
}

// ApproveJobProposalSpecApproveJobProposalSpecNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type ApproveJobProposalSpecApproveJobProposalSpecNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns ApproveJobProposalSpecApproveJobProposalSpecNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecNotFoundError) GetTypename() string {
	return v.Typename
}

// GetMessage returns ApproveJobProposalSpecApproveJobProposalSpecNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecNotFoundError) GetMessage() string {
	return v.Message
}

// GetCode returns ApproveJobProposalSpecApproveJobProposalSpecNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecApproveJobProposalSpecNotFoundError) GetCode() ErrorCode {
	return v.Code
}

// ApproveJobProposalSpecResponse is returned by ApproveJobProposalSpec on success.
type ApproveJobProposalSpecResponse struct {
	ApproveJobProposalSpec ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload `json:"-"`
}

// GetApproveJobProposalSpec returns ApproveJobProposalSpecResponse.ApproveJobProposalSpec, and is useful for accessing the field via an interface.
func (v *ApproveJobProposalSpecResponse) GetApproveJobProposalSpec() ApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload {
	return v.ApproveJobProposalSpec
}

func (v *ApproveJobProposalSpecResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*ApproveJobProposalSpecResponse
		ApproveJobProposalSpec json.RawMessage `json:"approveJobProposalSpec"`
		graphql.NoUnmarshalJSON
	}
	firstPass.ApproveJobProposalSpecResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.ApproveJobProposalSpec
		src := firstPass.ApproveJobProposalSpec
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal ApproveJobProposalSpecResponse.ApproveJobProposalSpec: %w", err)
			}
		}
	}
	return nil
}

type __premarshalApproveJobProposalSpecResponse struct {
	ApproveJobProposalSpec json.RawMessage `json:"approveJobProposalSpec"`
}

func (v *ApproveJobProposalSpecResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *ApproveJobProposalSpecResponse) __premarshalJSON() (*__premarshalApproveJobProposalSpecResponse, error) {
	var retval __premarshalApproveJobProposalSpecResponse

	{

		dst := &retval.ApproveJobProposalSpec
		src := v.ApproveJobProposalSpec
		var err error
		*dst, err = __marshalApproveJobProposalSpecApproveJobProposalSpecApproveJobProposalSpecPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal ApproveJobProposalSpecResponse.ApproveJobProposalSpec: %w", err)
		}
	}
	return &retval, nil
}

// BridgeParts includes the GraphQL fields of Bridge requested by the fragment BridgeParts.
type BridgeParts struct {
	Id                     string `json:"id"`
	Name                   string `json:"name"`
	Url                    string `json:"url"`
	Confirmations          int    `json:"confirmations"`
	OutgoingToken          string `json:"outgoingToken"`
	MinimumContractPayment string `json:"minimumContractPayment"`
	CreatedAt              string `json:"createdAt"`
}

// GetId returns BridgeParts.Id, and is useful for accessing the field via an interface.
func (v *BridgeParts) GetId() string { return v.Id }

// GetName returns BridgeParts.Name, and is useful for accessing the field via an interface.
func (v *BridgeParts) GetName() string { return v.Name }

// GetUrl returns BridgeParts.Url, and is useful for accessing the field via an interface.
func (v *BridgeParts) GetUrl() string { return v.Url }

// GetConfirmations returns BridgeParts.Confirmations, and is useful for accessing the field via an interface.
func (v *BridgeParts) GetConfirmations() int { return v.Confirmations }

// GetOutgoingToken returns BridgeParts.OutgoingToken, and is useful for accessing the field via an interface.
func (v *BridgeParts) GetOutgoingToken() string { return v.OutgoingToken }

// GetMinimumContractPayment returns BridgeParts.MinimumContractPayment, and is useful for accessing the field via an interface.
func (v *BridgeParts) GetMinimumContractPayment() string { return v.MinimumContractPayment }

// GetCreatedAt returns BridgeParts.CreatedAt, and is useful for accessing the field via an interface.
func (v *BridgeParts) GetCreatedAt() string { return v.CreatedAt }

// CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload includes the requested fields of the GraphQL interface CancelJobProposalSpecPayload.
//
// CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload is implemented by the following types:
// CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess
// CancelJobProposalSpecCancelJobProposalSpecNotFoundError
type CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload interface {
	implementsGraphQLInterfaceCancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess) implementsGraphQLInterfaceCancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload() {
}
func (v *CancelJobProposalSpecCancelJobProposalSpecNotFoundError) implementsGraphQLInterfaceCancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload() {
}

func __unmarshalCancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload(b []byte, v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "CancelJobProposalSpecSuccess":
		*v = new(CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess)
		return json.Unmarshal(b, *v)
	case "NotFoundError":
		*v = new(CancelJobProposalSpecCancelJobProposalSpecNotFoundError)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing CancelJobProposalSpecPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload: "%v"`, tn.TypeName)
	}
}

func __marshalCancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload(v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess:
		typename = "CancelJobProposalSpecSuccess"

		result := struct {
			TypeName string `json:"__typename"`
			*CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess
		}{typename, v}
		return json.Marshal(result)
	case *CancelJobProposalSpecCancelJobProposalSpecNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*CancelJobProposalSpecCancelJobProposalSpecNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload: "%T"`, v)
	}
}

// CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess includes the requested fields of the GraphQL type CancelJobProposalSpecSuccess.
type CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess struct {
	Typename string                                                                                    `json:"__typename"`
	Spec     CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec `json:"spec"`
}

// GetTypename returns CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess.Typename, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess) GetTypename() string {
	return v.Typename
}

// GetSpec returns CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess.Spec, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccess) GetSpec() CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec {
	return v.Spec
}

// CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec includes the requested fields of the GraphQL type JobProposalSpec.
type CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec struct {
	Id              string     `json:"id"`
	Definition      string     `json:"definition"`
	Version         int        `json:"version"`
	Status          SpecStatus `json:"status"`
	StatusUpdatedAt string     `json:"statusUpdatedAt"`
	CreatedAt       string     `json:"createdAt"`
	UpdatedAt       string     `json:"updatedAt"`
}

// GetId returns CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec.Id, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec) GetId() string {
	return v.Id
}

// GetDefinition returns CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec.Definition, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec) GetDefinition() string {
	return v.Definition
}

// GetVersion returns CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec.Version, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec) GetVersion() int {
	return v.Version
}

// GetStatus returns CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec.Status, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec) GetStatus() SpecStatus {
	return v.Status
}

// GetStatusUpdatedAt returns CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec.StatusUpdatedAt, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec) GetStatusUpdatedAt() string {
	return v.StatusUpdatedAt
}

// GetCreatedAt returns CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec.CreatedAt, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec) GetCreatedAt() string {
	return v.CreatedAt
}

// GetUpdatedAt returns CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec.UpdatedAt, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecSuccessSpecJobProposalSpec) GetUpdatedAt() string {
	return v.UpdatedAt
}

// CancelJobProposalSpecCancelJobProposalSpecNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type CancelJobProposalSpecCancelJobProposalSpecNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns CancelJobProposalSpecCancelJobProposalSpecNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecNotFoundError) GetTypename() string {
	return v.Typename
}

// GetMessage returns CancelJobProposalSpecCancelJobProposalSpecNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecNotFoundError) GetMessage() string {
	return v.Message
}

// GetCode returns CancelJobProposalSpecCancelJobProposalSpecNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecCancelJobProposalSpecNotFoundError) GetCode() ErrorCode { return v.Code }

// CancelJobProposalSpecResponse is returned by CancelJobProposalSpec on success.
type CancelJobProposalSpecResponse struct {
	CancelJobProposalSpec CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload `json:"-"`
}

// GetCancelJobProposalSpec returns CancelJobProposalSpecResponse.CancelJobProposalSpec, and is useful for accessing the field via an interface.
func (v *CancelJobProposalSpecResponse) GetCancelJobProposalSpec() CancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload {
	return v.CancelJobProposalSpec
}

func (v *CancelJobProposalSpecResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*CancelJobProposalSpecResponse
		CancelJobProposalSpec json.RawMessage `json:"cancelJobProposalSpec"`
		graphql.NoUnmarshalJSON
	}
	firstPass.CancelJobProposalSpecResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.CancelJobProposalSpec
		src := firstPass.CancelJobProposalSpec
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalCancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal CancelJobProposalSpecResponse.CancelJobProposalSpec: %w", err)
			}
		}
	}
	return nil
}

type __premarshalCancelJobProposalSpecResponse struct {
	CancelJobProposalSpec json.RawMessage `json:"cancelJobProposalSpec"`
}

func (v *CancelJobProposalSpecResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *CancelJobProposalSpecResponse) __premarshalJSON() (*__premarshalCancelJobProposalSpecResponse, error) {
	var retval __premarshalCancelJobProposalSpecResponse

	{

		dst := &retval.CancelJobProposalSpec
		src := v.CancelJobProposalSpec
		var err error
		*dst, err = __marshalCancelJobProposalSpecCancelJobProposalSpecCancelJobProposalSpecPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal CancelJobProposalSpecResponse.CancelJobProposalSpec: %w", err)
		}
	}
	return &retval, nil
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload includes the requested fields of the GraphQL interface CreateFeedsManagerChainConfigPayload.
//
// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload is implemented by the following types:
// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess
// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors
// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload interface {
	implementsGraphQLInterfaceCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess) implementsGraphQLInterfaceCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload() {
}
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors) implementsGraphQLInterfaceCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload() {
}
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError) implementsGraphQLInterfaceCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload() {
}

func __unmarshalCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload(b []byte, v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "CreateFeedsManagerChainConfigSuccess":
		*v = new(CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess)
		return json.Unmarshal(b, *v)
	case "InputErrors":
		*v = new(CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors)
		return json.Unmarshal(b, *v)
	case "NotFoundError":
		*v = new(CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing CreateFeedsManagerChainConfigPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload: "%v"`, tn.TypeName)
	}
}

func __marshalCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload(v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess:
		typename = "CreateFeedsManagerChainConfigSuccess"

		result := struct {
			TypeName string `json:"__typename"`
			*CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess
		}{typename, v}
		return json.Marshal(result)
	case *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors:
		typename = "InputErrors"

		result := struct {
			TypeName string `json:"__typename"`
			*CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors
		}{typename, v}
		return json.Marshal(result)
	case *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload: "%T"`, v)
	}
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess includes the requested fields of the GraphQL type CreateFeedsManagerChainConfigSuccess.
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess struct {
	Typename    string                                                                                                                           `json:"__typename"`
	ChainConfig CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig `json:"chainConfig"`
}

// GetTypename returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess.Typename, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess) GetTypename() string {
	return v.Typename
}

// GetChainConfig returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess.ChainConfig, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccess) GetChainConfig() CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig {
	return v.ChainConfig
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig includes the requested fields of the GraphQL type FeedsManagerChainConfig.
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig struct {
	Id                   string                                                                                                                                                     `json:"id"`
	ChainID              string                                                                                                                                                     `json:"chainID"`
	ChainType            string                                                                                                                                                     `json:"chainType"`
	AccountAddr          string                                                                                                                                                     `json:"accountAddr"`
	AdminAddr            string                                                                                                                                                     `json:"adminAddr"`
	FluxMonitorJobConfig CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigFluxMonitorJobConfig       `json:"fluxMonitorJobConfig"`
	Ocr1JobConfig        CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig `json:"ocr1JobConfig"`
	Ocr2JobConfig        CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig `json:"ocr2JobConfig"`
}

// GetId returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig.Id, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig) GetId() string {
	return v.Id
}

// GetChainID returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig.ChainID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig) GetChainID() string {
	return v.ChainID
}

// GetChainType returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig.ChainType, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig) GetChainType() string {
	return v.ChainType
}

// GetAccountAddr returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig.AccountAddr, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig) GetAccountAddr() string {
	return v.AccountAddr
}

// GetAdminAddr returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig.AdminAddr, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig) GetAdminAddr() string {
	return v.AdminAddr
}

// GetFluxMonitorJobConfig returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig.FluxMonitorJobConfig, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig) GetFluxMonitorJobConfig() CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigFluxMonitorJobConfig {
	return v.FluxMonitorJobConfig
}

// GetOcr1JobConfig returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig.Ocr1JobConfig, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig) GetOcr1JobConfig() CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig {
	return v.Ocr1JobConfig
}

// GetOcr2JobConfig returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig.Ocr2JobConfig, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfig) GetOcr2JobConfig() CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig {
	return v.Ocr2JobConfig
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigFluxMonitorJobConfig includes the requested fields of the GraphQL type FluxMonitorJobConfig.
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigFluxMonitorJobConfig struct {
	Enabled bool `json:"enabled"`
}

// GetEnabled returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigFluxMonitorJobConfig.Enabled, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigFluxMonitorJobConfig) GetEnabled() bool {
	return v.Enabled
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig includes the requested fields of the GraphQL type OCR1JobConfig.
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig struct {
	Enabled     bool   `json:"enabled"`
	IsBootstrap bool   `json:"isBootstrap"`
	Multiaddr   string `json:"multiaddr"`
	P2pPeerID   string `json:"p2pPeerID"`
	KeyBundleID string `json:"keyBundleID"`
}

// GetEnabled returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig.Enabled, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig) GetEnabled() bool {
	return v.Enabled
}

// GetIsBootstrap returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig.IsBootstrap, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig) GetIsBootstrap() bool {
	return v.IsBootstrap
}

// GetMultiaddr returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig.Multiaddr, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig) GetMultiaddr() string {
	return v.Multiaddr
}

// GetP2pPeerID returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig.P2pPeerID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig) GetP2pPeerID() string {
	return v.P2pPeerID
}

// GetKeyBundleID returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig.KeyBundleID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr1JobConfigOCR1JobConfig) GetKeyBundleID() string {
	return v.KeyBundleID
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig includes the requested fields of the GraphQL type OCR2JobConfig.
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig struct {
	Enabled          bool                                                                                                                                                              `json:"enabled"`
	IsBootstrap      bool                                                                                                                                                              `json:"isBootstrap"`
	Multiaddr        string                                                                                                                                                            `json:"multiaddr"`
	ForwarderAddress string                                                                                                                                                            `json:"forwarderAddress"`
	P2pPeerID        string                                                                                                                                                            `json:"p2pPeerID"`
	KeyBundleID      string                                                                                                                                                            `json:"keyBundleID"`
	Plugins          CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins `json:"plugins"`
}

// GetEnabled returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig.Enabled, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig) GetEnabled() bool {
	return v.Enabled
}

// GetIsBootstrap returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig.IsBootstrap, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig) GetIsBootstrap() bool {
	return v.IsBootstrap
}

// GetMultiaddr returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig.Multiaddr, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig) GetMultiaddr() string {
	return v.Multiaddr
}

// GetForwarderAddress returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig.ForwarderAddress, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig) GetForwarderAddress() string {
	return v.ForwarderAddress
}

// GetP2pPeerID returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig.P2pPeerID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig) GetP2pPeerID() string {
	return v.P2pPeerID
}

// GetKeyBundleID returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig.KeyBundleID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig) GetKeyBundleID() string {
	return v.KeyBundleID
}

// GetPlugins returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig.Plugins, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfig) GetPlugins() CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins {
	return v.Plugins
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins includes the requested fields of the GraphQL type Plugins.
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins struct {
	Commit     bool `json:"commit"`
	Execute    bool `json:"execute"`
	Median     bool `json:"median"`
	Mercury    bool `json:"mercury"`
	Rebalancer bool `json:"rebalancer"`
}

// GetCommit returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins.Commit, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins) GetCommit() bool {
	return v.Commit
}

// GetExecute returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins.Execute, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins) GetExecute() bool {
	return v.Execute
}

// GetMedian returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins.Median, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins) GetMedian() bool {
	return v.Median
}

// GetMercury returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins.Mercury, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins) GetMercury() bool {
	return v.Mercury
}

// GetRebalancer returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins.Rebalancer, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigSuccessChainConfigFeedsManagerChainConfigOcr2JobConfigOCR2JobConfigPlugins) GetRebalancer() bool {
	return v.Rebalancer
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors includes the requested fields of the GraphQL type InputErrors.
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors struct {
	Typename string                                                                                  `json:"__typename"`
	Errors   []CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrorsErrorsInputError `json:"errors"`
}

// GetTypename returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors.Typename, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors) GetTypename() string {
	return v.Typename
}

// GetErrors returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors.Errors, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrors) GetErrors() []CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrorsErrorsInputError {
	return v.Errors
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrorsErrorsInputError includes the requested fields of the GraphQL type InputError.
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrorsErrorsInputError struct {
	Message string `json:"message"`
	Path    string `json:"path"`
}

// GetMessage returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrorsErrorsInputError.Message, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrorsErrorsInputError) GetMessage() string {
	return v.Message
}

// GetPath returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrorsErrorsInputError.Path, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigInputErrorsErrorsInputError) GetPath() string {
	return v.Path
}

// CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError) GetTypename() string {
	return v.Typename
}

// GetMessage returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError) GetMessage() string {
	return v.Message
}

// GetCode returns CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigNotFoundError) GetCode() ErrorCode {
	return v.Code
}

type CreateFeedsManagerChainConfigInput struct {
	FeedsManagerID       string `json:"feedsManagerID"`
	ChainID              string `json:"chainID"`
	ChainType            string `json:"chainType"`
	AccountAddr          string `json:"accountAddr"`
	AccountAddrPubKey    string `json:"accountAddrPubKey"`
	AdminAddr            string `json:"adminAddr"`
	FluxMonitorEnabled   bool   `json:"fluxMonitorEnabled"`
	Ocr1Enabled          bool   `json:"ocr1Enabled"`
	Ocr1IsBootstrap      bool   `json:"ocr1IsBootstrap"`
	Ocr1Multiaddr        string `json:"ocr1Multiaddr"`
	Ocr1P2PPeerID        string `json:"ocr1P2PPeerID"`
	Ocr1KeyBundleID      string `json:"ocr1KeyBundleID"`
	Ocr2Enabled          bool   `json:"ocr2Enabled"`
	Ocr2IsBootstrap      bool   `json:"ocr2IsBootstrap"`
	Ocr2Multiaddr        string `json:"ocr2Multiaddr"`
	Ocr2ForwarderAddress string `json:"ocr2ForwarderAddress"`
	Ocr2P2PPeerID        string `json:"ocr2P2PPeerID"`
	Ocr2KeyBundleID      string `json:"ocr2KeyBundleID"`
	Ocr2Plugins          string `json:"ocr2Plugins"`
}

// GetFeedsManagerID returns CreateFeedsManagerChainConfigInput.FeedsManagerID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetFeedsManagerID() string { return v.FeedsManagerID }

// GetChainID returns CreateFeedsManagerChainConfigInput.ChainID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetChainID() string { return v.ChainID }

// GetChainType returns CreateFeedsManagerChainConfigInput.ChainType, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetChainType() string { return v.ChainType }

// GetAccountAddr returns CreateFeedsManagerChainConfigInput.AccountAddr, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetAccountAddr() string { return v.AccountAddr }

// GetAccountAddrPubKey returns CreateFeedsManagerChainConfigInput.AccountAddrPubKey, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetAccountAddrPubKey() string {
	return v.AccountAddrPubKey
}

// GetAdminAddr returns CreateFeedsManagerChainConfigInput.AdminAddr, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetAdminAddr() string { return v.AdminAddr }

// GetFluxMonitorEnabled returns CreateFeedsManagerChainConfigInput.FluxMonitorEnabled, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetFluxMonitorEnabled() bool {
	return v.FluxMonitorEnabled
}

// GetOcr1Enabled returns CreateFeedsManagerChainConfigInput.Ocr1Enabled, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr1Enabled() bool { return v.Ocr1Enabled }

// GetOcr1IsBootstrap returns CreateFeedsManagerChainConfigInput.Ocr1IsBootstrap, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr1IsBootstrap() bool { return v.Ocr1IsBootstrap }

// GetOcr1Multiaddr returns CreateFeedsManagerChainConfigInput.Ocr1Multiaddr, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr1Multiaddr() string { return v.Ocr1Multiaddr }

// GetOcr1P2PPeerID returns CreateFeedsManagerChainConfigInput.Ocr1P2PPeerID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr1P2PPeerID() string { return v.Ocr1P2PPeerID }

// GetOcr1KeyBundleID returns CreateFeedsManagerChainConfigInput.Ocr1KeyBundleID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr1KeyBundleID() string { return v.Ocr1KeyBundleID }

// GetOcr2Enabled returns CreateFeedsManagerChainConfigInput.Ocr2Enabled, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr2Enabled() bool { return v.Ocr2Enabled }

// GetOcr2IsBootstrap returns CreateFeedsManagerChainConfigInput.Ocr2IsBootstrap, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr2IsBootstrap() bool { return v.Ocr2IsBootstrap }

// GetOcr2Multiaddr returns CreateFeedsManagerChainConfigInput.Ocr2Multiaddr, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr2Multiaddr() string { return v.Ocr2Multiaddr }

// GetOcr2ForwarderAddress returns CreateFeedsManagerChainConfigInput.Ocr2ForwarderAddress, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr2ForwarderAddress() string {
	return v.Ocr2ForwarderAddress
}

// GetOcr2P2PPeerID returns CreateFeedsManagerChainConfigInput.Ocr2P2PPeerID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr2P2PPeerID() string { return v.Ocr2P2PPeerID }

// GetOcr2KeyBundleID returns CreateFeedsManagerChainConfigInput.Ocr2KeyBundleID, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr2KeyBundleID() string { return v.Ocr2KeyBundleID }

// GetOcr2Plugins returns CreateFeedsManagerChainConfigInput.Ocr2Plugins, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigInput) GetOcr2Plugins() string { return v.Ocr2Plugins }

// CreateFeedsManagerChainConfigResponse is returned by CreateFeedsManagerChainConfig on success.
type CreateFeedsManagerChainConfigResponse struct {
	CreateFeedsManagerChainConfig CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload `json:"-"`
}

// GetCreateFeedsManagerChainConfig returns CreateFeedsManagerChainConfigResponse.CreateFeedsManagerChainConfig, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerChainConfigResponse) GetCreateFeedsManagerChainConfig() CreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload {
	return v.CreateFeedsManagerChainConfig
}

func (v *CreateFeedsManagerChainConfigResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*CreateFeedsManagerChainConfigResponse
		CreateFeedsManagerChainConfig json.RawMessage `json:"createFeedsManagerChainConfig"`
		graphql.NoUnmarshalJSON
	}
	firstPass.CreateFeedsManagerChainConfigResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.CreateFeedsManagerChainConfig
		src := firstPass.CreateFeedsManagerChainConfig
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal CreateFeedsManagerChainConfigResponse.CreateFeedsManagerChainConfig: %w", err)
			}
		}
	}
	return nil
}

type __premarshalCreateFeedsManagerChainConfigResponse struct {
	CreateFeedsManagerChainConfig json.RawMessage `json:"createFeedsManagerChainConfig"`
}

func (v *CreateFeedsManagerChainConfigResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *CreateFeedsManagerChainConfigResponse) __premarshalJSON() (*__premarshalCreateFeedsManagerChainConfigResponse, error) {
	var retval __premarshalCreateFeedsManagerChainConfigResponse

	{

		dst := &retval.CreateFeedsManagerChainConfig
		src := v.CreateFeedsManagerChainConfig
		var err error
		*dst, err = __marshalCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigCreateFeedsManagerChainConfigPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal CreateFeedsManagerChainConfigResponse.CreateFeedsManagerChainConfig: %w", err)
		}
	}
	return &retval, nil
}

// CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload includes the requested fields of the GraphQL interface CreateFeedsManagerPayload.
//
// CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload is implemented by the following types:
// CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess
// CreateFeedsManagerCreateFeedsManagerDuplicateFeedsManagerError
// CreateFeedsManagerCreateFeedsManagerInputErrors
// CreateFeedsManagerCreateFeedsManagerNotFoundError
// CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError
type CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload interface {
	implementsGraphQLInterfaceCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess) implementsGraphQLInterfaceCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload() {
}
func (v *CreateFeedsManagerCreateFeedsManagerDuplicateFeedsManagerError) implementsGraphQLInterfaceCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload() {
}
func (v *CreateFeedsManagerCreateFeedsManagerInputErrors) implementsGraphQLInterfaceCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload() {
}
func (v *CreateFeedsManagerCreateFeedsManagerNotFoundError) implementsGraphQLInterfaceCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload() {
}
func (v *CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError) implementsGraphQLInterfaceCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload() {
}

func __unmarshalCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload(b []byte, v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "CreateFeedsManagerSuccess":
		*v = new(CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess)
		return json.Unmarshal(b, *v)
	case "DuplicateFeedsManagerError":
		*v = new(CreateFeedsManagerCreateFeedsManagerDuplicateFeedsManagerError)
		return json.Unmarshal(b, *v)
	case "InputErrors":
		*v = new(CreateFeedsManagerCreateFeedsManagerInputErrors)
		return json.Unmarshal(b, *v)
	case "NotFoundError":
		*v = new(CreateFeedsManagerCreateFeedsManagerNotFoundError)
		return json.Unmarshal(b, *v)
	case "SingleFeedsManagerError":
		*v = new(CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing CreateFeedsManagerPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload: "%v"`, tn.TypeName)
	}
}

func __marshalCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload(v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess:
		typename = "CreateFeedsManagerSuccess"

		result := struct {
			TypeName string `json:"__typename"`
			*CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess
		}{typename, v}
		return json.Marshal(result)
	case *CreateFeedsManagerCreateFeedsManagerDuplicateFeedsManagerError:
		typename = "DuplicateFeedsManagerError"

		result := struct {
			TypeName string `json:"__typename"`
			*CreateFeedsManagerCreateFeedsManagerDuplicateFeedsManagerError
		}{typename, v}
		return json.Marshal(result)
	case *CreateFeedsManagerCreateFeedsManagerInputErrors:
		typename = "InputErrors"

		result := struct {
			TypeName string `json:"__typename"`
			*CreateFeedsManagerCreateFeedsManagerInputErrors
		}{typename, v}
		return json.Marshal(result)
	case *CreateFeedsManagerCreateFeedsManagerNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*CreateFeedsManagerCreateFeedsManagerNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case *CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError:
		typename = "SingleFeedsManagerError"

		result := struct {
			TypeName string `json:"__typename"`
			*CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload: "%T"`, v)
	}
}

// CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess includes the requested fields of the GraphQL type CreateFeedsManagerSuccess.
type CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess struct {
	Typename     string                                                                    `json:"__typename"`
	FeedsManager CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager `json:"feedsManager"`
}

// GetTypename returns CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess.Typename, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess) GetTypename() string {
	return v.Typename
}

// GetFeedsManager returns CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess.FeedsManager, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccess) GetFeedsManager() CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager {
	return v.FeedsManager
}

// CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager includes the requested fields of the GraphQL type FeedsManager.
type CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager struct {
	FeedsManagerParts `json:"-"`
}

// GetId returns CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager.Id, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager) GetId() string {
	return v.FeedsManagerParts.Id
}

// GetName returns CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager.Name, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager) GetName() string {
	return v.FeedsManagerParts.Name
}

// GetUri returns CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager.Uri, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager) GetUri() string {
	return v.FeedsManagerParts.Uri
}

// GetPublicKey returns CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager.PublicKey, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager) GetPublicKey() string {
	return v.FeedsManagerParts.PublicKey
}

// GetIsConnectionActive returns CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager.IsConnectionActive, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager) GetIsConnectionActive() bool {
	return v.FeedsManagerParts.IsConnectionActive
}

// GetCreatedAt returns CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager.CreatedAt, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager) GetCreatedAt() string {
	return v.FeedsManagerParts.CreatedAt
}

func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager
		graphql.NoUnmarshalJSON
	}
	firstPass.CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.FeedsManagerParts)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager struct {
	Id string `json:"id"`

	Name string `json:"name"`

	Uri string `json:"uri"`

	PublicKey string `json:"publicKey"`

	IsConnectionActive bool `json:"isConnectionActive"`

	CreatedAt string `json:"createdAt"`
}

func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager) __premarshalJSON() (*__premarshalCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager, error) {
	var retval __premarshalCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerSuccessFeedsManager

	retval.Id = v.FeedsManagerParts.Id
	retval.Name = v.FeedsManagerParts.Name
	retval.Uri = v.FeedsManagerParts.Uri
	retval.PublicKey = v.FeedsManagerParts.PublicKey
	retval.IsConnectionActive = v.FeedsManagerParts.IsConnectionActive
	retval.CreatedAt = v.FeedsManagerParts.CreatedAt
	return &retval, nil
}

// CreateFeedsManagerCreateFeedsManagerDuplicateFeedsManagerError includes the requested fields of the GraphQL type DuplicateFeedsManagerError.
type CreateFeedsManagerCreateFeedsManagerDuplicateFeedsManagerError struct {
	Typename string `json:"__typename"`
}

// GetTypename returns CreateFeedsManagerCreateFeedsManagerDuplicateFeedsManagerError.Typename, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerDuplicateFeedsManagerError) GetTypename() string {
	return v.Typename
}

// CreateFeedsManagerCreateFeedsManagerInputErrors includes the requested fields of the GraphQL type InputErrors.
type CreateFeedsManagerCreateFeedsManagerInputErrors struct {
	Typename string                                                            `json:"__typename"`
	Errors   []CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError `json:"errors"`
}

// GetTypename returns CreateFeedsManagerCreateFeedsManagerInputErrors.Typename, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerInputErrors) GetTypename() string { return v.Typename }

// GetErrors returns CreateFeedsManagerCreateFeedsManagerInputErrors.Errors, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerInputErrors) GetErrors() []CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError {
	return v.Errors
}

// CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError includes the requested fields of the GraphQL type InputError.
type CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError struct {
	Message string    `json:"message"`
	Code    ErrorCode `json:"code"`
	Path    string    `json:"path"`
}

// GetMessage returns CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError.Message, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError) GetMessage() string {
	return v.Message
}

// GetCode returns CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError.Code, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError) GetCode() ErrorCode {
	return v.Code
}

// GetPath returns CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError.Path, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerInputErrorsErrorsInputError) GetPath() string {
	return v.Path
}

// CreateFeedsManagerCreateFeedsManagerNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type CreateFeedsManagerCreateFeedsManagerNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns CreateFeedsManagerCreateFeedsManagerNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerNotFoundError) GetTypename() string { return v.Typename }

// GetMessage returns CreateFeedsManagerCreateFeedsManagerNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerNotFoundError) GetMessage() string { return v.Message }

// GetCode returns CreateFeedsManagerCreateFeedsManagerNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerNotFoundError) GetCode() ErrorCode { return v.Code }

// CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError includes the requested fields of the GraphQL type SingleFeedsManagerError.
type CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError.Typename, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError) GetTypename() string {
	return v.Typename
}

// GetMessage returns CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError.Message, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError) GetMessage() string {
	return v.Message
}

// GetCode returns CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError.Code, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerCreateFeedsManagerSingleFeedsManagerError) GetCode() ErrorCode {
	return v.Code
}

type CreateFeedsManagerInput struct {
	Name      string `json:"name"`
	Uri       string `json:"uri"`
	PublicKey string `json:"publicKey"`
}

// GetName returns CreateFeedsManagerInput.Name, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerInput) GetName() string { return v.Name }

// GetUri returns CreateFeedsManagerInput.Uri, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerInput) GetUri() string { return v.Uri }

// GetPublicKey returns CreateFeedsManagerInput.PublicKey, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerInput) GetPublicKey() string { return v.PublicKey }

// CreateFeedsManagerResponse is returned by CreateFeedsManager on success.
type CreateFeedsManagerResponse struct {
	CreateFeedsManager CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload `json:"-"`
}

// GetCreateFeedsManager returns CreateFeedsManagerResponse.CreateFeedsManager, and is useful for accessing the field via an interface.
func (v *CreateFeedsManagerResponse) GetCreateFeedsManager() CreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload {
	return v.CreateFeedsManager
}

func (v *CreateFeedsManagerResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*CreateFeedsManagerResponse
		CreateFeedsManager json.RawMessage `json:"createFeedsManager"`
		graphql.NoUnmarshalJSON
	}
	firstPass.CreateFeedsManagerResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.CreateFeedsManager
		src := firstPass.CreateFeedsManager
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal CreateFeedsManagerResponse.CreateFeedsManager: %w", err)
			}
		}
	}
	return nil
}

type __premarshalCreateFeedsManagerResponse struct {
	CreateFeedsManager json.RawMessage `json:"createFeedsManager"`
}

func (v *CreateFeedsManagerResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *CreateFeedsManagerResponse) __premarshalJSON() (*__premarshalCreateFeedsManagerResponse, error) {
	var retval __premarshalCreateFeedsManagerResponse

	{

		dst := &retval.CreateFeedsManager
		src := v.CreateFeedsManager
		var err error
		*dst, err = __marshalCreateFeedsManagerCreateFeedsManagerCreateFeedsManagerPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal CreateFeedsManagerResponse.CreateFeedsManager: %w", err)
		}
	}
	return &retval, nil
}

type ErrorCode string

const (
	ErrorCodeNotFound      ErrorCode = "NOT_FOUND"
	ErrorCodeInvalidInput  ErrorCode = "INVALID_INPUT"
	ErrorCodeUnprocessable ErrorCode = "UNPROCESSABLE"
)

// FeedsManagerParts includes the GraphQL fields of FeedsManager requested by the fragment FeedsManagerParts.
type FeedsManagerParts struct {
	Id                 string `json:"id"`
	Name               string `json:"name"`
	Uri                string `json:"uri"`
	PublicKey          string `json:"publicKey"`
	IsConnectionActive bool   `json:"isConnectionActive"`
	CreatedAt          string `json:"createdAt"`
}

// GetId returns FeedsManagerParts.Id, and is useful for accessing the field via an interface.
func (v *FeedsManagerParts) GetId() string { return v.Id }

// GetName returns FeedsManagerParts.Name, and is useful for accessing the field via an interface.
func (v *FeedsManagerParts) GetName() string { return v.Name }

// GetUri returns FeedsManagerParts.Uri, and is useful for accessing the field via an interface.
func (v *FeedsManagerParts) GetUri() string { return v.Uri }

// GetPublicKey returns FeedsManagerParts.PublicKey, and is useful for accessing the field via an interface.
func (v *FeedsManagerParts) GetPublicKey() string { return v.PublicKey }

// GetIsConnectionActive returns FeedsManagerParts.IsConnectionActive, and is useful for accessing the field via an interface.
func (v *FeedsManagerParts) GetIsConnectionActive() bool { return v.IsConnectionActive }

// GetCreatedAt returns FeedsManagerParts.CreatedAt, and is useful for accessing the field via an interface.
func (v *FeedsManagerParts) GetCreatedAt() string { return v.CreatedAt }

// FetchAccountsEthKeysEthKeysPayload includes the requested fields of the GraphQL type EthKeysPayload.
type FetchAccountsEthKeysEthKeysPayload struct {
	Results []FetchAccountsEthKeysEthKeysPayloadResultsEthKey `json:"results"`
}

// GetResults returns FetchAccountsEthKeysEthKeysPayload.Results, and is useful for accessing the field via an interface.
func (v *FetchAccountsEthKeysEthKeysPayload) GetResults() []FetchAccountsEthKeysEthKeysPayloadResultsEthKey {
	return v.Results
}

// FetchAccountsEthKeysEthKeysPayloadResultsEthKey includes the requested fields of the GraphQL type EthKey.
type FetchAccountsEthKeysEthKeysPayloadResultsEthKey struct {
	Address     string                                               `json:"address"`
	IsDisabled  bool                                                 `json:"isDisabled"`
	Chain       FetchAccountsEthKeysEthKeysPayloadResultsEthKeyChain `json:"chain"`
	EthBalance  string                                               `json:"ethBalance"`
	LinkBalance string                                               `json:"linkBalance"`
}

// GetAddress returns FetchAccountsEthKeysEthKeysPayloadResultsEthKey.Address, and is useful for accessing the field via an interface.
func (v *FetchAccountsEthKeysEthKeysPayloadResultsEthKey) GetAddress() string { return v.Address }

// GetIsDisabled returns FetchAccountsEthKeysEthKeysPayloadResultsEthKey.IsDisabled, and is useful for accessing the field via an interface.
func (v *FetchAccountsEthKeysEthKeysPayloadResultsEthKey) GetIsDisabled() bool { return v.IsDisabled }

// GetChain returns FetchAccountsEthKeysEthKeysPayloadResultsEthKey.Chain, and is useful for accessing the field via an interface.
func (v *FetchAccountsEthKeysEthKeysPayloadResultsEthKey) GetChain() FetchAccountsEthKeysEthKeysPayloadResultsEthKeyChain {
	return v.Chain
}

// GetEthBalance returns FetchAccountsEthKeysEthKeysPayloadResultsEthKey.EthBalance, and is useful for accessing the field via an interface.
func (v *FetchAccountsEthKeysEthKeysPayloadResultsEthKey) GetEthBalance() string { return v.EthBalance }

// GetLinkBalance returns FetchAccountsEthKeysEthKeysPayloadResultsEthKey.LinkBalance, and is useful for accessing the field via an interface.
func (v *FetchAccountsEthKeysEthKeysPayloadResultsEthKey) GetLinkBalance() string {
	return v.LinkBalance
}

// FetchAccountsEthKeysEthKeysPayloadResultsEthKeyChain includes the requested fields of the GraphQL type Chain.
type FetchAccountsEthKeysEthKeysPayloadResultsEthKeyChain struct {
	Id      string `json:"id"`
	Enabled bool   `json:"enabled"`
}

// GetId returns FetchAccountsEthKeysEthKeysPayloadResultsEthKeyChain.Id, and is useful for accessing the field via an interface.
func (v *FetchAccountsEthKeysEthKeysPayloadResultsEthKeyChain) GetId() string { return v.Id }

// GetEnabled returns FetchAccountsEthKeysEthKeysPayloadResultsEthKeyChain.Enabled, and is useful for accessing the field via an interface.
func (v *FetchAccountsEthKeysEthKeysPayloadResultsEthKeyChain) GetEnabled() bool { return v.Enabled }

// FetchAccountsResponse is returned by FetchAccounts on success.
type FetchAccountsResponse struct {
	EthKeys FetchAccountsEthKeysEthKeysPayload `json:"ethKeys"`
}

// GetEthKeys returns FetchAccountsResponse.EthKeys, and is useful for accessing the field via an interface.
func (v *FetchAccountsResponse) GetEthKeys() FetchAccountsEthKeysEthKeysPayload { return v.EthKeys }

// FetchCSAKeysCsaKeysCSAKeysPayload includes the requested fields of the GraphQL type CSAKeysPayload.
type FetchCSAKeysCsaKeysCSAKeysPayload struct {
	Results []FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey `json:"results"`
}

// GetResults returns FetchCSAKeysCsaKeysCSAKeysPayload.Results, and is useful for accessing the field via an interface.
func (v *FetchCSAKeysCsaKeysCSAKeysPayload) GetResults() []FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey {
	return v.Results
}

// FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey includes the requested fields of the GraphQL type CSAKey.
type FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey struct {
	Id        string `json:"id"`
	PublicKey string `json:"publicKey"`
	Version   int    `json:"version"`
}

// GetId returns FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey.Id, and is useful for accessing the field via an interface.
func (v *FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey) GetId() string { return v.Id }

// GetPublicKey returns FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey.PublicKey, and is useful for accessing the field via an interface.
func (v *FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey) GetPublicKey() string { return v.PublicKey }

// GetVersion returns FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey.Version, and is useful for accessing the field via an interface.
func (v *FetchCSAKeysCsaKeysCSAKeysPayloadResultsCSAKey) GetVersion() int { return v.Version }

// FetchCSAKeysResponse is returned by FetchCSAKeys on success.
type FetchCSAKeysResponse struct {
	CsaKeys FetchCSAKeysCsaKeysCSAKeysPayload `json:"csaKeys"`
}

// GetCsaKeys returns FetchCSAKeysResponse.CsaKeys, and is useful for accessing the field via an interface.
func (v *FetchCSAKeysResponse) GetCsaKeys() FetchCSAKeysCsaKeysCSAKeysPayload { return v.CsaKeys }

// FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayload includes the requested fields of the GraphQL type OCR2KeyBundlesPayload.
type FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayload struct {
	Results []FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle `json:"results"`
}

// GetResults returns FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayload.Results, and is useful for accessing the field via an interface.
func (v *FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayload) GetResults() []FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle {
	return v.Results
}

// FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle includes the requested fields of the GraphQL type OCR2KeyBundle.
type FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle struct {
	Id                string        `json:"id"`
	ChainType         OCR2ChainType `json:"chainType"`
	ConfigPublicKey   string        `json:"configPublicKey"`
	OnChainPublicKey  string        `json:"onChainPublicKey"`
	OffChainPublicKey string        `json:"offChainPublicKey"`
}

// GetId returns FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle.Id, and is useful for accessing the field via an interface.
func (v *FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle) GetId() string {
	return v.Id
}

// GetChainType returns FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle.ChainType, and is useful for accessing the field via an interface.
func (v *FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle) GetChainType() OCR2ChainType {
	return v.ChainType
}

// GetConfigPublicKey returns FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle.ConfigPublicKey, and is useful for accessing the field via an interface.
func (v *FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle) GetConfigPublicKey() string {
	return v.ConfigPublicKey
}

// GetOnChainPublicKey returns FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle.OnChainPublicKey, and is useful for accessing the field via an interface.
func (v *FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle) GetOnChainPublicKey() string {
	return v.OnChainPublicKey
}

// GetOffChainPublicKey returns FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle.OffChainPublicKey, and is useful for accessing the field via an interface.
func (v *FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayloadResultsOCR2KeyBundle) GetOffChainPublicKey() string {
	return v.OffChainPublicKey
}

// FetchOCR2KeyBundlesResponse is returned by FetchOCR2KeyBundles on success.
type FetchOCR2KeyBundlesResponse struct {
	Ocr2KeyBundles FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayload `json:"ocr2KeyBundles"`
}

// GetOcr2KeyBundles returns FetchOCR2KeyBundlesResponse.Ocr2KeyBundles, and is useful for accessing the field via an interface.
func (v *FetchOCR2KeyBundlesResponse) GetOcr2KeyBundles() FetchOCR2KeyBundlesOcr2KeyBundlesOCR2KeyBundlesPayload {
	return v.Ocr2KeyBundles
}

// FetchP2PKeysP2pKeysP2PKeysPayload includes the requested fields of the GraphQL type P2PKeysPayload.
type FetchP2PKeysP2pKeysP2PKeysPayload struct {
	Results []FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey `json:"results"`
}

// GetResults returns FetchP2PKeysP2pKeysP2PKeysPayload.Results, and is useful for accessing the field via an interface.
func (v *FetchP2PKeysP2pKeysP2PKeysPayload) GetResults() []FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey {
	return v.Results
}

// FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey includes the requested fields of the GraphQL type P2PKey.
type FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey struct {
	Id        string `json:"id"`
	PeerID    string `json:"peerID"`
	PublicKey string `json:"publicKey"`
}

// GetId returns FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey.Id, and is useful for accessing the field via an interface.
func (v *FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey) GetId() string { return v.Id }

// GetPeerID returns FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey.PeerID, and is useful for accessing the field via an interface.
func (v *FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey) GetPeerID() string { return v.PeerID }

// GetPublicKey returns FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey.PublicKey, and is useful for accessing the field via an interface.
func (v *FetchP2PKeysP2pKeysP2PKeysPayloadResultsP2PKey) GetPublicKey() string { return v.PublicKey }

// FetchP2PKeysResponse is returned by FetchP2PKeys on success.
type FetchP2PKeysResponse struct {
	P2pKeys FetchP2PKeysP2pKeysP2PKeysPayload `json:"p2pKeys"`
}

// GetP2pKeys returns FetchP2PKeysResponse.P2pKeys, and is useful for accessing the field via an interface.
func (v *FetchP2PKeysResponse) GetP2pKeys() FetchP2PKeysP2pKeysP2PKeysPayload { return v.P2pKeys }

// GetBridgeBridge includes the requested fields of the GraphQL type Bridge.
type GetBridgeBridge struct {
	Typename    string `json:"__typename"`
	BridgeParts `json:"-"`
}

// GetTypename returns GetBridgeBridge.Typename, and is useful for accessing the field via an interface.
func (v *GetBridgeBridge) GetTypename() string { return v.Typename }

// GetId returns GetBridgeBridge.Id, and is useful for accessing the field via an interface.
func (v *GetBridgeBridge) GetId() string { return v.BridgeParts.Id }

// GetName returns GetBridgeBridge.Name, and is useful for accessing the field via an interface.
func (v *GetBridgeBridge) GetName() string { return v.BridgeParts.Name }

// GetUrl returns GetBridgeBridge.Url, and is useful for accessing the field via an interface.
func (v *GetBridgeBridge) GetUrl() string { return v.BridgeParts.Url }

// GetConfirmations returns GetBridgeBridge.Confirmations, and is useful for accessing the field via an interface.
func (v *GetBridgeBridge) GetConfirmations() int { return v.BridgeParts.Confirmations }

// GetOutgoingToken returns GetBridgeBridge.OutgoingToken, and is useful for accessing the field via an interface.
func (v *GetBridgeBridge) GetOutgoingToken() string { return v.BridgeParts.OutgoingToken }

// GetMinimumContractPayment returns GetBridgeBridge.MinimumContractPayment, and is useful for accessing the field via an interface.
func (v *GetBridgeBridge) GetMinimumContractPayment() string {
	return v.BridgeParts.MinimumContractPayment
}

// GetCreatedAt returns GetBridgeBridge.CreatedAt, and is useful for accessing the field via an interface.
func (v *GetBridgeBridge) GetCreatedAt() string { return v.BridgeParts.CreatedAt }

func (v *GetBridgeBridge) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetBridgeBridge
		graphql.NoUnmarshalJSON
	}
	firstPass.GetBridgeBridge = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.BridgeParts)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetBridgeBridge struct {
	Typename string `json:"__typename"`

	Id string `json:"id"`

	Name string `json:"name"`

	Url string `json:"url"`

	Confirmations int `json:"confirmations"`

	OutgoingToken string `json:"outgoingToken"`

	MinimumContractPayment string `json:"minimumContractPayment"`

	CreatedAt string `json:"createdAt"`
}

func (v *GetBridgeBridge) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetBridgeBridge) __premarshalJSON() (*__premarshalGetBridgeBridge, error) {
	var retval __premarshalGetBridgeBridge

	retval.Typename = v.Typename
	retval.Id = v.BridgeParts.Id
	retval.Name = v.BridgeParts.Name
	retval.Url = v.BridgeParts.Url
	retval.Confirmations = v.BridgeParts.Confirmations
	retval.OutgoingToken = v.BridgeParts.OutgoingToken
	retval.MinimumContractPayment = v.BridgeParts.MinimumContractPayment
	retval.CreatedAt = v.BridgeParts.CreatedAt
	return &retval, nil
}

// GetBridgeBridgeBridgePayload includes the requested fields of the GraphQL interface BridgePayload.
//
// GetBridgeBridgeBridgePayload is implemented by the following types:
// GetBridgeBridge
// GetBridgeBridgeNotFoundError
type GetBridgeBridgeBridgePayload interface {
	implementsGraphQLInterfaceGetBridgeBridgeBridgePayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *GetBridgeBridge) implementsGraphQLInterfaceGetBridgeBridgeBridgePayload()              {}
func (v *GetBridgeBridgeNotFoundError) implementsGraphQLInterfaceGetBridgeBridgeBridgePayload() {}

func __unmarshalGetBridgeBridgeBridgePayload(b []byte, v *GetBridgeBridgeBridgePayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "Bridge":
		*v = new(GetBridgeBridge)
		return json.Unmarshal(b, *v)
	case "NotFoundError":
		*v = new(GetBridgeBridgeNotFoundError)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing BridgePayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for GetBridgeBridgeBridgePayload: "%v"`, tn.TypeName)
	}
}

func __marshalGetBridgeBridgeBridgePayload(v *GetBridgeBridgeBridgePayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *GetBridgeBridge:
		typename = "Bridge"

		premarshaled, err := v.__premarshalJSON()
		if err != nil {
			return nil, err
		}
		result := struct {
			TypeName string `json:"__typename"`
			*__premarshalGetBridgeBridge
		}{typename, premarshaled}
		return json.Marshal(result)
	case *GetBridgeBridgeNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*GetBridgeBridgeNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for GetBridgeBridgeBridgePayload: "%T"`, v)
	}
}

// GetBridgeBridgeNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type GetBridgeBridgeNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns GetBridgeBridgeNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *GetBridgeBridgeNotFoundError) GetTypename() string { return v.Typename }

// GetMessage returns GetBridgeBridgeNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *GetBridgeBridgeNotFoundError) GetMessage() string { return v.Message }

// GetCode returns GetBridgeBridgeNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *GetBridgeBridgeNotFoundError) GetCode() ErrorCode { return v.Code }

// GetBridgeResponse is returned by GetBridge on success.
type GetBridgeResponse struct {
	Bridge GetBridgeBridgeBridgePayload `json:"-"`
}

// GetBridge returns GetBridgeResponse.Bridge, and is useful for accessing the field via an interface.
func (v *GetBridgeResponse) GetBridge() GetBridgeBridgeBridgePayload { return v.Bridge }

func (v *GetBridgeResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetBridgeResponse
		Bridge json.RawMessage `json:"bridge"`
		graphql.NoUnmarshalJSON
	}
	firstPass.GetBridgeResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Bridge
		src := firstPass.Bridge
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalGetBridgeBridgeBridgePayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal GetBridgeResponse.Bridge: %w", err)
			}
		}
	}
	return nil
}

type __premarshalGetBridgeResponse struct {
	Bridge json.RawMessage `json:"bridge"`
}

func (v *GetBridgeResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetBridgeResponse) __premarshalJSON() (*__premarshalGetBridgeResponse, error) {
	var retval __premarshalGetBridgeResponse

	{

		dst := &retval.Bridge
		src := v.Bridge
		var err error
		*dst, err = __marshalGetBridgeBridgeBridgePayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal GetBridgeResponse.Bridge: %w", err)
		}
	}
	return &retval, nil
}

// GetFeedsManagerFeedsManager includes the requested fields of the GraphQL type FeedsManager.
type GetFeedsManagerFeedsManager struct {
	Typename          string `json:"__typename"`
	FeedsManagerParts `json:"-"`
}

// GetTypename returns GetFeedsManagerFeedsManager.Typename, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManager) GetTypename() string { return v.Typename }

// GetId returns GetFeedsManagerFeedsManager.Id, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManager) GetId() string { return v.FeedsManagerParts.Id }

// GetName returns GetFeedsManagerFeedsManager.Name, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManager) GetName() string { return v.FeedsManagerParts.Name }

// GetUri returns GetFeedsManagerFeedsManager.Uri, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManager) GetUri() string { return v.FeedsManagerParts.Uri }

// GetPublicKey returns GetFeedsManagerFeedsManager.PublicKey, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManager) GetPublicKey() string { return v.FeedsManagerParts.PublicKey }

// GetIsConnectionActive returns GetFeedsManagerFeedsManager.IsConnectionActive, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManager) GetIsConnectionActive() bool {
	return v.FeedsManagerParts.IsConnectionActive
}

// GetCreatedAt returns GetFeedsManagerFeedsManager.CreatedAt, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManager) GetCreatedAt() string { return v.FeedsManagerParts.CreatedAt }

func (v *GetFeedsManagerFeedsManager) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetFeedsManagerFeedsManager
		graphql.NoUnmarshalJSON
	}
	firstPass.GetFeedsManagerFeedsManager = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.FeedsManagerParts)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetFeedsManagerFeedsManager struct {
	Typename string `json:"__typename"`

	Id string `json:"id"`

	Name string `json:"name"`

	Uri string `json:"uri"`

	PublicKey string `json:"publicKey"`

	IsConnectionActive bool `json:"isConnectionActive"`

	CreatedAt string `json:"createdAt"`
}

func (v *GetFeedsManagerFeedsManager) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetFeedsManagerFeedsManager) __premarshalJSON() (*__premarshalGetFeedsManagerFeedsManager, error) {
	var retval __premarshalGetFeedsManagerFeedsManager

	retval.Typename = v.Typename
	retval.Id = v.FeedsManagerParts.Id
	retval.Name = v.FeedsManagerParts.Name
	retval.Uri = v.FeedsManagerParts.Uri
	retval.PublicKey = v.FeedsManagerParts.PublicKey
	retval.IsConnectionActive = v.FeedsManagerParts.IsConnectionActive
	retval.CreatedAt = v.FeedsManagerParts.CreatedAt
	return &retval, nil
}

// GetFeedsManagerFeedsManagerFeedsManagerPayload includes the requested fields of the GraphQL interface FeedsManagerPayload.
//
// GetFeedsManagerFeedsManagerFeedsManagerPayload is implemented by the following types:
// GetFeedsManagerFeedsManager
// GetFeedsManagerFeedsManagerNotFoundError
type GetFeedsManagerFeedsManagerFeedsManagerPayload interface {
	implementsGraphQLInterfaceGetFeedsManagerFeedsManagerFeedsManagerPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *GetFeedsManagerFeedsManager) implementsGraphQLInterfaceGetFeedsManagerFeedsManagerFeedsManagerPayload() {
}
func (v *GetFeedsManagerFeedsManagerNotFoundError) implementsGraphQLInterfaceGetFeedsManagerFeedsManagerFeedsManagerPayload() {
}

func __unmarshalGetFeedsManagerFeedsManagerFeedsManagerPayload(b []byte, v *GetFeedsManagerFeedsManagerFeedsManagerPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "FeedsManager":
		*v = new(GetFeedsManagerFeedsManager)
		return json.Unmarshal(b, *v)
	case "NotFoundError":
		*v = new(GetFeedsManagerFeedsManagerNotFoundError)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing FeedsManagerPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for GetFeedsManagerFeedsManagerFeedsManagerPayload: "%v"`, tn.TypeName)
	}
}

func __marshalGetFeedsManagerFeedsManagerFeedsManagerPayload(v *GetFeedsManagerFeedsManagerFeedsManagerPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *GetFeedsManagerFeedsManager:
		typename = "FeedsManager"

		premarshaled, err := v.__premarshalJSON()
		if err != nil {
			return nil, err
		}
		result := struct {
			TypeName string `json:"__typename"`
			*__premarshalGetFeedsManagerFeedsManager
		}{typename, premarshaled}
		return json.Marshal(result)
	case *GetFeedsManagerFeedsManagerNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*GetFeedsManagerFeedsManagerNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for GetFeedsManagerFeedsManagerFeedsManagerPayload: "%T"`, v)
	}
}

// GetFeedsManagerFeedsManagerNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type GetFeedsManagerFeedsManagerNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns GetFeedsManagerFeedsManagerNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManagerNotFoundError) GetTypename() string { return v.Typename }

// GetMessage returns GetFeedsManagerFeedsManagerNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManagerNotFoundError) GetMessage() string { return v.Message }

// GetCode returns GetFeedsManagerFeedsManagerNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerFeedsManagerNotFoundError) GetCode() ErrorCode { return v.Code }

// GetFeedsManagerResponse is returned by GetFeedsManager on success.
type GetFeedsManagerResponse struct {
	FeedsManager GetFeedsManagerFeedsManagerFeedsManagerPayload `json:"-"`
}

// GetFeedsManager returns GetFeedsManagerResponse.FeedsManager, and is useful for accessing the field via an interface.
func (v *GetFeedsManagerResponse) GetFeedsManager() GetFeedsManagerFeedsManagerFeedsManagerPayload {
	return v.FeedsManager
}

func (v *GetFeedsManagerResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetFeedsManagerResponse
		FeedsManager json.RawMessage `json:"feedsManager"`
		graphql.NoUnmarshalJSON
	}
	firstPass.GetFeedsManagerResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.FeedsManager
		src := firstPass.FeedsManager
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalGetFeedsManagerFeedsManagerFeedsManagerPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal GetFeedsManagerResponse.FeedsManager: %w", err)
			}
		}
	}
	return nil
}

type __premarshalGetFeedsManagerResponse struct {
	FeedsManager json.RawMessage `json:"feedsManager"`
}

func (v *GetFeedsManagerResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetFeedsManagerResponse) __premarshalJSON() (*__premarshalGetFeedsManagerResponse, error) {
	var retval __premarshalGetFeedsManagerResponse

	{

		dst := &retval.FeedsManager
		src := v.FeedsManager
		var err error
		*dst, err = __marshalGetFeedsManagerFeedsManagerFeedsManagerPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal GetFeedsManagerResponse.FeedsManager: %w", err)
		}
	}
	return &retval, nil
}

// GetJobJob includes the requested fields of the GraphQL type Job.
type GetJobJob struct {
	Typename string `json:"__typename"`
	JobParts `json:"-"`
}

// GetTypename returns GetJobJob.Typename, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetTypename() string { return v.Typename }

// GetId returns GetJobJob.Id, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetId() string { return v.JobParts.Id }

// GetName returns GetJobJob.Name, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetName() string { return v.JobParts.Name }

// GetSchemaVersion returns GetJobJob.SchemaVersion, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetSchemaVersion() int { return v.JobParts.SchemaVersion }

// GetGasLimit returns GetJobJob.GasLimit, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetGasLimit() int { return v.JobParts.GasLimit }

// GetForwardingAllowed returns GetJobJob.ForwardingAllowed, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetForwardingAllowed() bool { return v.JobParts.ForwardingAllowed }

// GetMaxTaskDuration returns GetJobJob.MaxTaskDuration, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetMaxTaskDuration() string { return v.JobParts.MaxTaskDuration }

// GetExternalJobID returns GetJobJob.ExternalJobID, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetExternalJobID() string { return v.JobParts.ExternalJobID }

// GetType returns GetJobJob.Type, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetType() string { return v.JobParts.Type }

// GetSpec returns GetJobJob.Spec, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetSpec() JobPartsSpecJobSpec { return v.JobParts.Spec }

// GetObservationSource returns GetJobJob.ObservationSource, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetObservationSource() string { return v.JobParts.ObservationSource }

// GetErrors returns GetJobJob.Errors, and is useful for accessing the field via an interface.
func (v *GetJobJob) GetErrors() []JobPartsErrorsJobError { return v.JobParts.Errors }

func (v *GetJobJob) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetJobJob
		graphql.NoUnmarshalJSON
	}
	firstPass.GetJobJob = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.JobParts)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetJobJob struct {
	Typename string `json:"__typename"`

	Id string `json:"id"`

	Name string `json:"name"`

	SchemaVersion int `json:"schemaVersion"`

	GasLimit int `json:"gasLimit"`

	ForwardingAllowed bool `json:"forwardingAllowed"`

	MaxTaskDuration string `json:"maxTaskDuration"`

	ExternalJobID string `json:"externalJobID"`

	Type string `json:"type"`

	Spec json.RawMessage `json:"spec"`

	ObservationSource string `json:"observationSource"`

	Errors []JobPartsErrorsJobError `json:"errors"`
}

func (v *GetJobJob) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetJobJob) __premarshalJSON() (*__premarshalGetJobJob, error) {
	var retval __premarshalGetJobJob

	retval.Typename = v.Typename
	retval.Id = v.JobParts.Id
	retval.Name = v.JobParts.Name
	retval.SchemaVersion = v.JobParts.SchemaVersion
	retval.GasLimit = v.JobParts.GasLimit
	retval.ForwardingAllowed = v.JobParts.ForwardingAllowed
	retval.MaxTaskDuration = v.JobParts.MaxTaskDuration
	retval.ExternalJobID = v.JobParts.ExternalJobID
	retval.Type = v.JobParts.Type
	{

		dst := &retval.Spec
		src := v.JobParts.Spec
		var err error
		*dst, err = __marshalJobPartsSpecJobSpec(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal GetJobJob.JobParts.Spec: %w", err)
		}
	}
	retval.ObservationSource = v.JobParts.ObservationSource
	retval.Errors = v.JobParts.Errors
	return &retval, nil
}

// GetJobJobJobPayload includes the requested fields of the GraphQL interface JobPayload.
//
// GetJobJobJobPayload is implemented by the following types:
// GetJobJob
// GetJobJobNotFoundError
type GetJobJobJobPayload interface {
	implementsGraphQLInterfaceGetJobJobJobPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *GetJobJob) implementsGraphQLInterfaceGetJobJobJobPayload()              {}
func (v *GetJobJobNotFoundError) implementsGraphQLInterfaceGetJobJobJobPayload() {}

func __unmarshalGetJobJobJobPayload(b []byte, v *GetJobJobJobPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "Job":
		*v = new(GetJobJob)
		return json.Unmarshal(b, *v)
	case "NotFoundError":
		*v = new(GetJobJobNotFoundError)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing JobPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for GetJobJobJobPayload: "%v"`, tn.TypeName)
	}
}

func __marshalGetJobJobJobPayload(v *GetJobJobJobPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *GetJobJob:
		typename = "Job"

		premarshaled, err := v.__premarshalJSON()
		if err != nil {
			return nil, err
		}
		result := struct {
			TypeName string `json:"__typename"`
			*__premarshalGetJobJob
		}{typename, premarshaled}
		return json.Marshal(result)
	case *GetJobJobNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*GetJobJobNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for GetJobJobJobPayload: "%T"`, v)
	}
}

// GetJobJobNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type GetJobJobNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns GetJobJobNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *GetJobJobNotFoundError) GetTypename() string { return v.Typename }

// GetMessage returns GetJobJobNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *GetJobJobNotFoundError) GetMessage() string { return v.Message }

// GetCode returns GetJobJobNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *GetJobJobNotFoundError) GetCode() ErrorCode { return v.Code }

// GetJobProposalJobProposal includes the requested fields of the GraphQL type JobProposal.
type GetJobProposalJobProposal struct {
	Typename      string                                             `json:"__typename"`
	Id            string                                             `json:"id"`
	Name          string                                             `json:"name"`
	Status        JobProposalStatus                                  `json:"status"`
	RemoteUUID    string                                             `json:"remoteUUID"`
	ExternalJobID string                                             `json:"externalJobID"`
	JobID         string                                             `json:"jobID"`
	FeedsManager  GetJobProposalJobProposalFeedsManager              `json:"feedsManager"`
	MultiAddrs    []string                                           `json:"multiAddrs"`
	PendingUpdate bool                                               `json:"pendingUpdate"`
	Specs         []GetJobProposalJobProposalSpecsJobProposalSpec    `json:"specs"`
	LatestSpec    GetJobProposalJobProposalLatestSpecJobProposalSpec `json:"latestSpec"`
}

// GetTypename returns GetJobProposalJobProposal.Typename, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetTypename() string { return v.Typename }

// GetId returns GetJobProposalJobProposal.Id, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetId() string { return v.Id }

// GetName returns GetJobProposalJobProposal.Name, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetName() string { return v.Name }

// GetStatus returns GetJobProposalJobProposal.Status, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetStatus() JobProposalStatus { return v.Status }

// GetRemoteUUID returns GetJobProposalJobProposal.RemoteUUID, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetRemoteUUID() string { return v.RemoteUUID }

// GetExternalJobID returns GetJobProposalJobProposal.ExternalJobID, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetExternalJobID() string { return v.ExternalJobID }

// GetJobID returns GetJobProposalJobProposal.JobID, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetJobID() string { return v.JobID }

// GetFeedsManager returns GetJobProposalJobProposal.FeedsManager, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetFeedsManager() GetJobProposalJobProposalFeedsManager {
	return v.FeedsManager
}

// GetMultiAddrs returns GetJobProposalJobProposal.MultiAddrs, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetMultiAddrs() []string { return v.MultiAddrs }

// GetPendingUpdate returns GetJobProposalJobProposal.PendingUpdate, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetPendingUpdate() bool { return v.PendingUpdate }

// GetSpecs returns GetJobProposalJobProposal.Specs, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetSpecs() []GetJobProposalJobProposalSpecsJobProposalSpec {
	return v.Specs
}

// GetLatestSpec returns GetJobProposalJobProposal.LatestSpec, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposal) GetLatestSpec() GetJobProposalJobProposalLatestSpecJobProposalSpec {
	return v.LatestSpec
}

// GetJobProposalJobProposalFeedsManager includes the requested fields of the GraphQL type FeedsManager.
type GetJobProposalJobProposalFeedsManager struct {
	FeedsManagerParts `json:"-"`
}

// GetId returns GetJobProposalJobProposalFeedsManager.Id, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalFeedsManager) GetId() string { return v.FeedsManagerParts.Id }

// GetName returns GetJobProposalJobProposalFeedsManager.Name, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalFeedsManager) GetName() string { return v.FeedsManagerParts.Name }

// GetUri returns GetJobProposalJobProposalFeedsManager.Uri, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalFeedsManager) GetUri() string { return v.FeedsManagerParts.Uri }

// GetPublicKey returns GetJobProposalJobProposalFeedsManager.PublicKey, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalFeedsManager) GetPublicKey() string {
	return v.FeedsManagerParts.PublicKey
}

// GetIsConnectionActive returns GetJobProposalJobProposalFeedsManager.IsConnectionActive, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalFeedsManager) GetIsConnectionActive() bool {
	return v.FeedsManagerParts.IsConnectionActive
}

// GetCreatedAt returns GetJobProposalJobProposalFeedsManager.CreatedAt, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalFeedsManager) GetCreatedAt() string {
	return v.FeedsManagerParts.CreatedAt
}

func (v *GetJobProposalJobProposalFeedsManager) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetJobProposalJobProposalFeedsManager
		graphql.NoUnmarshalJSON
	}
	firstPass.GetJobProposalJobProposalFeedsManager = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.FeedsManagerParts)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalGetJobProposalJobProposalFeedsManager struct {
	Id string `json:"id"`

	Name string `json:"name"`

	Uri string `json:"uri"`

	PublicKey string `json:"publicKey"`

	IsConnectionActive bool `json:"isConnectionActive"`

	CreatedAt string `json:"createdAt"`
}

func (v *GetJobProposalJobProposalFeedsManager) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetJobProposalJobProposalFeedsManager) __premarshalJSON() (*__premarshalGetJobProposalJobProposalFeedsManager, error) {
	var retval __premarshalGetJobProposalJobProposalFeedsManager

	retval.Id = v.FeedsManagerParts.Id
	retval.Name = v.FeedsManagerParts.Name
	retval.Uri = v.FeedsManagerParts.Uri
	retval.PublicKey = v.FeedsManagerParts.PublicKey
	retval.IsConnectionActive = v.FeedsManagerParts.IsConnectionActive
	retval.CreatedAt = v.FeedsManagerParts.CreatedAt
	return &retval, nil
}

// GetJobProposalJobProposalJobProposalPayload includes the requested fields of the GraphQL interface JobProposalPayload.
//
// GetJobProposalJobProposalJobProposalPayload is implemented by the following types:
// GetJobProposalJobProposal
// GetJobProposalJobProposalNotFoundError
type GetJobProposalJobProposalJobProposalPayload interface {
	implementsGraphQLInterfaceGetJobProposalJobProposalJobProposalPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *GetJobProposalJobProposal) implementsGraphQLInterfaceGetJobProposalJobProposalJobProposalPayload() {
}
func (v *GetJobProposalJobProposalNotFoundError) implementsGraphQLInterfaceGetJobProposalJobProposalJobProposalPayload() {
}

func __unmarshalGetJobProposalJobProposalJobProposalPayload(b []byte, v *GetJobProposalJobProposalJobProposalPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "JobProposal":
		*v = new(GetJobProposalJobProposal)
		return json.Unmarshal(b, *v)
	case "NotFoundError":
		*v = new(GetJobProposalJobProposalNotFoundError)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing JobProposalPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for GetJobProposalJobProposalJobProposalPayload: "%v"`, tn.TypeName)
	}
}

func __marshalGetJobProposalJobProposalJobProposalPayload(v *GetJobProposalJobProposalJobProposalPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *GetJobProposalJobProposal:
		typename = "JobProposal"

		result := struct {
			TypeName string `json:"__typename"`
			*GetJobProposalJobProposal
		}{typename, v}
		return json.Marshal(result)
	case *GetJobProposalJobProposalNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*GetJobProposalJobProposalNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for GetJobProposalJobProposalJobProposalPayload: "%T"`, v)
	}
}

// GetJobProposalJobProposalLatestSpecJobProposalSpec includes the requested fields of the GraphQL type JobProposalSpec.
type GetJobProposalJobProposalLatestSpecJobProposalSpec struct {
	Id              string     `json:"id"`
	Definition      string     `json:"definition"`
	Version         int        `json:"version"`
	Status          SpecStatus `json:"status"`
	StatusUpdatedAt string     `json:"statusUpdatedAt"`
	CreatedAt       string     `json:"createdAt"`
	UpdatedAt       string     `json:"updatedAt"`
}

// GetId returns GetJobProposalJobProposalLatestSpecJobProposalSpec.Id, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalLatestSpecJobProposalSpec) GetId() string { return v.Id }

// GetDefinition returns GetJobProposalJobProposalLatestSpecJobProposalSpec.Definition, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalLatestSpecJobProposalSpec) GetDefinition() string {
	return v.Definition
}

// GetVersion returns GetJobProposalJobProposalLatestSpecJobProposalSpec.Version, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalLatestSpecJobProposalSpec) GetVersion() int { return v.Version }

// GetStatus returns GetJobProposalJobProposalLatestSpecJobProposalSpec.Status, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalLatestSpecJobProposalSpec) GetStatus() SpecStatus { return v.Status }

// GetStatusUpdatedAt returns GetJobProposalJobProposalLatestSpecJobProposalSpec.StatusUpdatedAt, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalLatestSpecJobProposalSpec) GetStatusUpdatedAt() string {
	return v.StatusUpdatedAt
}

// GetCreatedAt returns GetJobProposalJobProposalLatestSpecJobProposalSpec.CreatedAt, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalLatestSpecJobProposalSpec) GetCreatedAt() string {
	return v.CreatedAt
}

// GetUpdatedAt returns GetJobProposalJobProposalLatestSpecJobProposalSpec.UpdatedAt, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalLatestSpecJobProposalSpec) GetUpdatedAt() string {
	return v.UpdatedAt
}

// GetJobProposalJobProposalNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type GetJobProposalJobProposalNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns GetJobProposalJobProposalNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalNotFoundError) GetTypename() string { return v.Typename }

// GetMessage returns GetJobProposalJobProposalNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalNotFoundError) GetMessage() string { return v.Message }

// GetCode returns GetJobProposalJobProposalNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalNotFoundError) GetCode() ErrorCode { return v.Code }

// GetJobProposalJobProposalSpecsJobProposalSpec includes the requested fields of the GraphQL type JobProposalSpec.
type GetJobProposalJobProposalSpecsJobProposalSpec struct {
	Id              string     `json:"id"`
	Definition      string     `json:"definition"`
	Version         int        `json:"version"`
	Status          SpecStatus `json:"status"`
	StatusUpdatedAt string     `json:"statusUpdatedAt"`
	CreatedAt       string     `json:"createdAt"`
	UpdatedAt       string     `json:"updatedAt"`
}

// GetId returns GetJobProposalJobProposalSpecsJobProposalSpec.Id, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalSpecsJobProposalSpec) GetId() string { return v.Id }

// GetDefinition returns GetJobProposalJobProposalSpecsJobProposalSpec.Definition, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalSpecsJobProposalSpec) GetDefinition() string { return v.Definition }

// GetVersion returns GetJobProposalJobProposalSpecsJobProposalSpec.Version, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalSpecsJobProposalSpec) GetVersion() int { return v.Version }

// GetStatus returns GetJobProposalJobProposalSpecsJobProposalSpec.Status, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalSpecsJobProposalSpec) GetStatus() SpecStatus { return v.Status }

// GetStatusUpdatedAt returns GetJobProposalJobProposalSpecsJobProposalSpec.StatusUpdatedAt, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalSpecsJobProposalSpec) GetStatusUpdatedAt() string {
	return v.StatusUpdatedAt
}

// GetCreatedAt returns GetJobProposalJobProposalSpecsJobProposalSpec.CreatedAt, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalSpecsJobProposalSpec) GetCreatedAt() string { return v.CreatedAt }

// GetUpdatedAt returns GetJobProposalJobProposalSpecsJobProposalSpec.UpdatedAt, and is useful for accessing the field via an interface.
func (v *GetJobProposalJobProposalSpecsJobProposalSpec) GetUpdatedAt() string { return v.UpdatedAt }

// GetJobProposalResponse is returned by GetJobProposal on success.
type GetJobProposalResponse struct {
	JobProposal GetJobProposalJobProposalJobProposalPayload `json:"-"`
}

// GetJobProposal returns GetJobProposalResponse.JobProposal, and is useful for accessing the field via an interface.
func (v *GetJobProposalResponse) GetJobProposal() GetJobProposalJobProposalJobProposalPayload {
	return v.JobProposal
}

func (v *GetJobProposalResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetJobProposalResponse
		JobProposal json.RawMessage `json:"jobProposal"`
		graphql.NoUnmarshalJSON
	}
	firstPass.GetJobProposalResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.JobProposal
		src := firstPass.JobProposal
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalGetJobProposalJobProposalJobProposalPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal GetJobProposalResponse.JobProposal: %w", err)
			}
		}
	}
	return nil
}

type __premarshalGetJobProposalResponse struct {
	JobProposal json.RawMessage `json:"jobProposal"`
}

func (v *GetJobProposalResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetJobProposalResponse) __premarshalJSON() (*__premarshalGetJobProposalResponse, error) {
	var retval __premarshalGetJobProposalResponse

	{

		dst := &retval.JobProposal
		src := v.JobProposal
		var err error
		*dst, err = __marshalGetJobProposalJobProposalJobProposalPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal GetJobProposalResponse.JobProposal: %w", err)
		}
	}
	return &retval, nil
}

// GetJobResponse is returned by GetJob on success.
type GetJobResponse struct {
	Job GetJobJobJobPayload `json:"-"`
}

// GetJob returns GetJobResponse.Job, and is useful for accessing the field via an interface.
func (v *GetJobResponse) GetJob() GetJobJobJobPayload { return v.Job }

func (v *GetJobResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*GetJobResponse
		Job json.RawMessage `json:"job"`
		graphql.NoUnmarshalJSON
	}
	firstPass.GetJobResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Job
		src := firstPass.Job
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalGetJobJobJobPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal GetJobResponse.Job: %w", err)
			}
		}
	}
	return nil
}

type __premarshalGetJobResponse struct {
	Job json.RawMessage `json:"job"`
}

func (v *GetJobResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *GetJobResponse) __premarshalJSON() (*__premarshalGetJobResponse, error) {
	var retval __premarshalGetJobResponse

	{

		dst := &retval.Job
		src := v.Job
		var err error
		*dst, err = __marshalGetJobJobJobPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal GetJobResponse.Job: %w", err)
		}
	}
	return &retval, nil
}

// JobParts includes the GraphQL fields of Job requested by the fragment JobParts.
type JobParts struct {
	Id                string                   `json:"id"`
	Name              string                   `json:"name"`
	SchemaVersion     int                      `json:"schemaVersion"`
	GasLimit          int                      `json:"gasLimit"`
	ForwardingAllowed bool                     `json:"forwardingAllowed"`
	MaxTaskDuration   string                   `json:"maxTaskDuration"`
	ExternalJobID     string                   `json:"externalJobID"`
	Type              string                   `json:"type"`
	Spec              JobPartsSpecJobSpec      `json:"-"`
	ObservationSource string                   `json:"observationSource"`
	Errors            []JobPartsErrorsJobError `json:"errors"`
}

// GetId returns JobParts.Id, and is useful for accessing the field via an interface.
func (v *JobParts) GetId() string { return v.Id }

// GetName returns JobParts.Name, and is useful for accessing the field via an interface.
func (v *JobParts) GetName() string { return v.Name }

// GetSchemaVersion returns JobParts.SchemaVersion, and is useful for accessing the field via an interface.
func (v *JobParts) GetSchemaVersion() int { return v.SchemaVersion }

// GetGasLimit returns JobParts.GasLimit, and is useful for accessing the field via an interface.
func (v *JobParts) GetGasLimit() int { return v.GasLimit }

// GetForwardingAllowed returns JobParts.ForwardingAllowed, and is useful for accessing the field via an interface.
func (v *JobParts) GetForwardingAllowed() bool { return v.ForwardingAllowed }

// GetMaxTaskDuration returns JobParts.MaxTaskDuration, and is useful for accessing the field via an interface.
func (v *JobParts) GetMaxTaskDuration() string { return v.MaxTaskDuration }

// GetExternalJobID returns JobParts.ExternalJobID, and is useful for accessing the field via an interface.
func (v *JobParts) GetExternalJobID() string { return v.ExternalJobID }

// GetType returns JobParts.Type, and is useful for accessing the field via an interface.
func (v *JobParts) GetType() string { return v.Type }

// GetSpec returns JobParts.Spec, and is useful for accessing the field via an interface.
func (v *JobParts) GetSpec() JobPartsSpecJobSpec { return v.Spec }

// GetObservationSource returns JobParts.ObservationSource, and is useful for accessing the field via an interface.
func (v *JobParts) GetObservationSource() string { return v.ObservationSource }

// GetErrors returns JobParts.Errors, and is useful for accessing the field via an interface.
func (v *JobParts) GetErrors() []JobPartsErrorsJobError { return v.Errors }

func (v *JobParts) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*JobParts
		Spec json.RawMessage `json:"spec"`
		graphql.NoUnmarshalJSON
	}
	firstPass.JobParts = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.Spec
		src := firstPass.Spec
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalJobPartsSpecJobSpec(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal JobParts.Spec: %w", err)
			}
		}
	}
	return nil
}

type __premarshalJobParts struct {
	Id string `json:"id"`

	Name string `json:"name"`

	SchemaVersion int `json:"schemaVersion"`

	GasLimit int `json:"gasLimit"`

	ForwardingAllowed bool `json:"forwardingAllowed"`

	MaxTaskDuration string `json:"maxTaskDuration"`

	ExternalJobID string `json:"externalJobID"`

	Type string `json:"type"`

	Spec json.RawMessage `json:"spec"`

	ObservationSource string `json:"observationSource"`

	Errors []JobPartsErrorsJobError `json:"errors"`
}

func (v *JobParts) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *JobParts) __premarshalJSON() (*__premarshalJobParts, error) {
	var retval __premarshalJobParts

	retval.Id = v.Id
	retval.Name = v.Name
	retval.SchemaVersion = v.SchemaVersion
	retval.GasLimit = v.GasLimit
	retval.ForwardingAllowed = v.ForwardingAllowed
	retval.MaxTaskDuration = v.MaxTaskDuration
	retval.ExternalJobID = v.ExternalJobID
	retval.Type = v.Type
	{

		dst := &retval.Spec
		src := v.Spec
		var err error
		*dst, err = __marshalJobPartsSpecJobSpec(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal JobParts.Spec: %w", err)
		}
	}
	retval.ObservationSource = v.ObservationSource
	retval.Errors = v.Errors
	return &retval, nil
}

// JobPartsErrorsJobError includes the requested fields of the GraphQL type JobError.
type JobPartsErrorsJobError struct {
	Id          string `json:"id"`
	Description string `json:"description"`
	Occurrences int    `json:"occurrences"`
	CreatedAt   string `json:"createdAt"`
	UpdatedAt   string `json:"updatedAt"`
}

// GetId returns JobPartsErrorsJobError.Id, and is useful for accessing the field via an interface.
func (v *JobPartsErrorsJobError) GetId() string { return v.Id }

// GetDescription returns JobPartsErrorsJobError.Description, and is useful for accessing the field via an interface.
func (v *JobPartsErrorsJobError) GetDescription() string { return v.Description }

// GetOccurrences returns JobPartsErrorsJobError.Occurrences, and is useful for accessing the field via an interface.
func (v *JobPartsErrorsJobError) GetOccurrences() int { return v.Occurrences }

// GetCreatedAt returns JobPartsErrorsJobError.CreatedAt, and is useful for accessing the field via an interface.
func (v *JobPartsErrorsJobError) GetCreatedAt() string { return v.CreatedAt }

// GetUpdatedAt returns JobPartsErrorsJobError.UpdatedAt, and is useful for accessing the field via an interface.
func (v *JobPartsErrorsJobError) GetUpdatedAt() string { return v.UpdatedAt }

// JobPartsSpecBlockHeaderFeederSpec includes the requested fields of the GraphQL type BlockHeaderFeederSpec.
type JobPartsSpecBlockHeaderFeederSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecBlockHeaderFeederSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecBlockHeaderFeederSpec) GetTypename() string { return v.Typename }

// JobPartsSpecBlockhashStoreSpec includes the requested fields of the GraphQL type BlockhashStoreSpec.
type JobPartsSpecBlockhashStoreSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecBlockhashStoreSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecBlockhashStoreSpec) GetTypename() string { return v.Typename }

// JobPartsSpecBootstrapSpec includes the requested fields of the GraphQL type BootstrapSpec.
type JobPartsSpecBootstrapSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecBootstrapSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecBootstrapSpec) GetTypename() string { return v.Typename }

// JobPartsSpecCronSpec includes the requested fields of the GraphQL type CronSpec.
type JobPartsSpecCronSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecCronSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecCronSpec) GetTypename() string { return v.Typename }

// JobPartsSpecDirectRequestSpec includes the requested fields of the GraphQL type DirectRequestSpec.
type JobPartsSpecDirectRequestSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecDirectRequestSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecDirectRequestSpec) GetTypename() string { return v.Typename }

// JobPartsSpecFluxMonitorSpec includes the requested fields of the GraphQL type FluxMonitorSpec.
type JobPartsSpecFluxMonitorSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecFluxMonitorSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecFluxMonitorSpec) GetTypename() string { return v.Typename }

// JobPartsSpecGatewaySpec includes the requested fields of the GraphQL type GatewaySpec.
type JobPartsSpecGatewaySpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecGatewaySpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecGatewaySpec) GetTypename() string { return v.Typename }

// JobPartsSpecJobSpec includes the requested fields of the GraphQL interface JobSpec.
//
// JobPartsSpecJobSpec is implemented by the following types:
// JobPartsSpecBlockHeaderFeederSpec
// JobPartsSpecBlockhashStoreSpec
// JobPartsSpecBootstrapSpec
// JobPartsSpecCronSpec
// JobPartsSpecDirectRequestSpec
// JobPartsSpecFluxMonitorSpec
// JobPartsSpecGatewaySpec
// JobPartsSpecKeeperSpec
// JobPartsSpecOCR2Spec
// JobPartsSpecOCRSpec
// JobPartsSpecStandardCapabilitiesSpec
// JobPartsSpecVRFSpec
// JobPartsSpecWebhookSpec
// JobPartsSpecWorkflowSpec
type JobPartsSpecJobSpec interface {
	implementsGraphQLInterfaceJobPartsSpecJobSpec()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *JobPartsSpecBlockHeaderFeederSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()    {}
func (v *JobPartsSpecBlockhashStoreSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()       {}
func (v *JobPartsSpecBootstrapSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()            {}
func (v *JobPartsSpecCronSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()                 {}
func (v *JobPartsSpecDirectRequestSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()        {}
func (v *JobPartsSpecFluxMonitorSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()          {}
func (v *JobPartsSpecGatewaySpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()              {}
func (v *JobPartsSpecKeeperSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()               {}
func (v *JobPartsSpecOCR2Spec) implementsGraphQLInterfaceJobPartsSpecJobSpec()                 {}
func (v *JobPartsSpecOCRSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()                  {}
func (v *JobPartsSpecStandardCapabilitiesSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec() {}
func (v *JobPartsSpecVRFSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()                  {}
func (v *JobPartsSpecWebhookSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()              {}
func (v *JobPartsSpecWorkflowSpec) implementsGraphQLInterfaceJobPartsSpecJobSpec()             {}

func __unmarshalJobPartsSpecJobSpec(b []byte, v *JobPartsSpecJobSpec) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "BlockHeaderFeederSpec":
		*v = new(JobPartsSpecBlockHeaderFeederSpec)
		return json.Unmarshal(b, *v)
	case "BlockhashStoreSpec":
		*v = new(JobPartsSpecBlockhashStoreSpec)
		return json.Unmarshal(b, *v)
	case "BootstrapSpec":
		*v = new(JobPartsSpecBootstrapSpec)
		return json.Unmarshal(b, *v)
	case "CronSpec":
		*v = new(JobPartsSpecCronSpec)
		return json.Unmarshal(b, *v)
	case "DirectRequestSpec":
		*v = new(JobPartsSpecDirectRequestSpec)
		return json.Unmarshal(b, *v)
	case "FluxMonitorSpec":
		*v = new(JobPartsSpecFluxMonitorSpec)
		return json.Unmarshal(b, *v)
	case "GatewaySpec":
		*v = new(JobPartsSpecGatewaySpec)
		return json.Unmarshal(b, *v)
	case "KeeperSpec":
		*v = new(JobPartsSpecKeeperSpec)
		return json.Unmarshal(b, *v)
	case "OCR2Spec":
		*v = new(JobPartsSpecOCR2Spec)
		return json.Unmarshal(b, *v)
	case "OCRSpec":
		*v = new(JobPartsSpecOCRSpec)
		return json.Unmarshal(b, *v)
	case "StandardCapabilitiesSpec":
		*v = new(JobPartsSpecStandardCapabilitiesSpec)
		return json.Unmarshal(b, *v)
	case "VRFSpec":
		*v = new(JobPartsSpecVRFSpec)
		return json.Unmarshal(b, *v)
	case "WebhookSpec":
		*v = new(JobPartsSpecWebhookSpec)
		return json.Unmarshal(b, *v)
	case "WorkflowSpec":
		*v = new(JobPartsSpecWorkflowSpec)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing JobSpec.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for JobPartsSpecJobSpec: "%v"`, tn.TypeName)
	}
}

func __marshalJobPartsSpecJobSpec(v *JobPartsSpecJobSpec) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *JobPartsSpecBlockHeaderFeederSpec:
		typename = "BlockHeaderFeederSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecBlockHeaderFeederSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecBlockhashStoreSpec:
		typename = "BlockhashStoreSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecBlockhashStoreSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecBootstrapSpec:
		typename = "BootstrapSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecBootstrapSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecCronSpec:
		typename = "CronSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecCronSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecDirectRequestSpec:
		typename = "DirectRequestSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecDirectRequestSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecFluxMonitorSpec:
		typename = "FluxMonitorSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecFluxMonitorSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecGatewaySpec:
		typename = "GatewaySpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecGatewaySpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecKeeperSpec:
		typename = "KeeperSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecKeeperSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecOCR2Spec:
		typename = "OCR2Spec"

		premarshaled, err := v.__premarshalJSON()
		if err != nil {
			return nil, err
		}
		result := struct {
			TypeName string `json:"__typename"`
			*__premarshalJobPartsSpecOCR2Spec
		}{typename, premarshaled}
		return json.Marshal(result)
	case *JobPartsSpecOCRSpec:
		typename = "OCRSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecOCRSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecStandardCapabilitiesSpec:
		typename = "StandardCapabilitiesSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecStandardCapabilitiesSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecVRFSpec:
		typename = "VRFSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecVRFSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecWebhookSpec:
		typename = "WebhookSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecWebhookSpec
		}{typename, v}
		return json.Marshal(result)
	case *JobPartsSpecWorkflowSpec:
		typename = "WorkflowSpec"

		result := struct {
			TypeName string `json:"__typename"`
			*JobPartsSpecWorkflowSpec
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for JobPartsSpecJobSpec: "%T"`, v)
	}
}

// JobPartsSpecKeeperSpec includes the requested fields of the GraphQL type KeeperSpec.
type JobPartsSpecKeeperSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecKeeperSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecKeeperSpec) GetTypename() string { return v.Typename }

// JobPartsSpecOCR2Spec includes the requested fields of the GraphQL type OCR2Spec.
type JobPartsSpecOCR2Spec struct {
	Typename string `json:"__typename"`
	OCR2Spec `json:"-"`
}

// GetTypename returns JobPartsSpecOCR2Spec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetTypename() string { return v.Typename }

// GetBlockchainTimeout returns JobPartsSpecOCR2Spec.BlockchainTimeout, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetBlockchainTimeout() string { return v.OCR2Spec.BlockchainTimeout }

// GetContractID returns JobPartsSpecOCR2Spec.ContractID, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetContractID() string { return v.OCR2Spec.ContractID }

// GetContractConfigConfirmations returns JobPartsSpecOCR2Spec.ContractConfigConfirmations, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetContractConfigConfirmations() int {
	return v.OCR2Spec.ContractConfigConfirmations
}

// GetContractConfigTrackerPollInterval returns JobPartsSpecOCR2Spec.ContractConfigTrackerPollInterval, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetContractConfigTrackerPollInterval() string {
	return v.OCR2Spec.ContractConfigTrackerPollInterval
}

// GetCreatedAt returns JobPartsSpecOCR2Spec.CreatedAt, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetCreatedAt() string { return v.OCR2Spec.CreatedAt }

// GetOcrKeyBundleID returns JobPartsSpecOCR2Spec.OcrKeyBundleID, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetOcrKeyBundleID() string { return v.OCR2Spec.OcrKeyBundleID }

// GetMonitoringEndpoint returns JobPartsSpecOCR2Spec.MonitoringEndpoint, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetMonitoringEndpoint() string { return v.OCR2Spec.MonitoringEndpoint }

// GetP2pv2Bootstrappers returns JobPartsSpecOCR2Spec.P2pv2Bootstrappers, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetP2pv2Bootstrappers() []string { return v.OCR2Spec.P2pv2Bootstrappers }

// GetRelay returns JobPartsSpecOCR2Spec.Relay, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetRelay() string { return v.OCR2Spec.Relay }

// GetRelayConfig returns JobPartsSpecOCR2Spec.RelayConfig, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetRelayConfig() gqlscalar.Map { return v.OCR2Spec.RelayConfig }

// GetTransmitterID returns JobPartsSpecOCR2Spec.TransmitterID, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetTransmitterID() string { return v.OCR2Spec.TransmitterID }

// GetPluginType returns JobPartsSpecOCR2Spec.PluginType, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetPluginType() string { return v.OCR2Spec.PluginType }

// GetPluginConfig returns JobPartsSpecOCR2Spec.PluginConfig, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCR2Spec) GetPluginConfig() gqlscalar.Map { return v.OCR2Spec.PluginConfig }

func (v *JobPartsSpecOCR2Spec) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*JobPartsSpecOCR2Spec
		graphql.NoUnmarshalJSON
	}
	firstPass.JobPartsSpecOCR2Spec = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.OCR2Spec)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalJobPartsSpecOCR2Spec struct {
	Typename string `json:"__typename"`

	BlockchainTimeout string `json:"blockchainTimeout"`

	ContractID string `json:"contractID"`

	ContractConfigConfirmations int `json:"contractConfigConfirmations"`

	ContractConfigTrackerPollInterval string `json:"contractConfigTrackerPollInterval"`

	CreatedAt string `json:"createdAt"`

	OcrKeyBundleID string `json:"ocrKeyBundleID"`

	MonitoringEndpoint string `json:"monitoringEndpoint"`

	P2pv2Bootstrappers []string `json:"p2pv2Bootstrappers"`

	Relay string `json:"relay"`

	RelayConfig gqlscalar.Map `json:"relayConfig"`

	TransmitterID string `json:"transmitterID"`

	PluginType string `json:"pluginType"`

	PluginConfig gqlscalar.Map `json:"pluginConfig"`
}

func (v *JobPartsSpecOCR2Spec) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *JobPartsSpecOCR2Spec) __premarshalJSON() (*__premarshalJobPartsSpecOCR2Spec, error) {
	var retval __premarshalJobPartsSpecOCR2Spec

	retval.Typename = v.Typename
	retval.BlockchainTimeout = v.OCR2Spec.BlockchainTimeout
	retval.ContractID = v.OCR2Spec.ContractID
	retval.ContractConfigConfirmations = v.OCR2Spec.ContractConfigConfirmations
	retval.ContractConfigTrackerPollInterval = v.OCR2Spec.ContractConfigTrackerPollInterval
	retval.CreatedAt = v.OCR2Spec.CreatedAt
	retval.OcrKeyBundleID = v.OCR2Spec.OcrKeyBundleID
	retval.MonitoringEndpoint = v.OCR2Spec.MonitoringEndpoint
	retval.P2pv2Bootstrappers = v.OCR2Spec.P2pv2Bootstrappers
	retval.Relay = v.OCR2Spec.Relay
	retval.RelayConfig = v.OCR2Spec.RelayConfig
	retval.TransmitterID = v.OCR2Spec.TransmitterID
	retval.PluginType = v.OCR2Spec.PluginType
	retval.PluginConfig = v.OCR2Spec.PluginConfig
	return &retval, nil
}

// JobPartsSpecOCRSpec includes the requested fields of the GraphQL type OCRSpec.
type JobPartsSpecOCRSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecOCRSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecOCRSpec) GetTypename() string { return v.Typename }

// JobPartsSpecStandardCapabilitiesSpec includes the requested fields of the GraphQL type StandardCapabilitiesSpec.
type JobPartsSpecStandardCapabilitiesSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecStandardCapabilitiesSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecStandardCapabilitiesSpec) GetTypename() string { return v.Typename }

// JobPartsSpecVRFSpec includes the requested fields of the GraphQL type VRFSpec.
type JobPartsSpecVRFSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecVRFSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecVRFSpec) GetTypename() string { return v.Typename }

// JobPartsSpecWebhookSpec includes the requested fields of the GraphQL type WebhookSpec.
type JobPartsSpecWebhookSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecWebhookSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecWebhookSpec) GetTypename() string { return v.Typename }

// JobPartsSpecWorkflowSpec includes the requested fields of the GraphQL type WorkflowSpec.
type JobPartsSpecWorkflowSpec struct {
	Typename string `json:"__typename"`
}

// GetTypename returns JobPartsSpecWorkflowSpec.Typename, and is useful for accessing the field via an interface.
func (v *JobPartsSpecWorkflowSpec) GetTypename() string { return v.Typename }

type JobProposalStatus string

const (
	JobProposalStatusPending   JobProposalStatus = "PENDING"
	JobProposalStatusApproved  JobProposalStatus = "APPROVED"
	JobProposalStatusRejected  JobProposalStatus = "REJECTED"
	JobProposalStatusCancelled JobProposalStatus = "CANCELLED"
	JobProposalStatusDeleted   JobProposalStatus = "DELETED"
	JobProposalStatusRevoked   JobProposalStatus = "REVOKED"
)

// ListBridgesBridgesBridgesPayload includes the requested fields of the GraphQL type BridgesPayload.
type ListBridgesBridgesBridgesPayload struct {
	Results  []ListBridgesBridgesBridgesPayloadResultsBridge            `json:"results"`
	Metadata ListBridgesBridgesBridgesPayloadMetadataPaginationMetadata `json:"metadata"`
}

// GetResults returns ListBridgesBridgesBridgesPayload.Results, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayload) GetResults() []ListBridgesBridgesBridgesPayloadResultsBridge {
	return v.Results
}

// GetMetadata returns ListBridgesBridgesBridgesPayload.Metadata, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayload) GetMetadata() ListBridgesBridgesBridgesPayloadMetadataPaginationMetadata {
	return v.Metadata
}

// ListBridgesBridgesBridgesPayloadMetadataPaginationMetadata includes the requested fields of the GraphQL type PaginationMetadata.
type ListBridgesBridgesBridgesPayloadMetadataPaginationMetadata struct {
	Total int `json:"total"`
}

// GetTotal returns ListBridgesBridgesBridgesPayloadMetadataPaginationMetadata.Total, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayloadMetadataPaginationMetadata) GetTotal() int { return v.Total }

// ListBridgesBridgesBridgesPayloadResultsBridge includes the requested fields of the GraphQL type Bridge.
type ListBridgesBridgesBridgesPayloadResultsBridge struct {
	BridgeParts `json:"-"`
}

// GetId returns ListBridgesBridgesBridgesPayloadResultsBridge.Id, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayloadResultsBridge) GetId() string { return v.BridgeParts.Id }

// GetName returns ListBridgesBridgesBridgesPayloadResultsBridge.Name, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayloadResultsBridge) GetName() string { return v.BridgeParts.Name }

// GetUrl returns ListBridgesBridgesBridgesPayloadResultsBridge.Url, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayloadResultsBridge) GetUrl() string { return v.BridgeParts.Url }

// GetConfirmations returns ListBridgesBridgesBridgesPayloadResultsBridge.Confirmations, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayloadResultsBridge) GetConfirmations() int {
	return v.BridgeParts.Confirmations
}

// GetOutgoingToken returns ListBridgesBridgesBridgesPayloadResultsBridge.OutgoingToken, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayloadResultsBridge) GetOutgoingToken() string {
	return v.BridgeParts.OutgoingToken
}

// GetMinimumContractPayment returns ListBridgesBridgesBridgesPayloadResultsBridge.MinimumContractPayment, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayloadResultsBridge) GetMinimumContractPayment() string {
	return v.BridgeParts.MinimumContractPayment
}

// GetCreatedAt returns ListBridgesBridgesBridgesPayloadResultsBridge.CreatedAt, and is useful for accessing the field via an interface.
func (v *ListBridgesBridgesBridgesPayloadResultsBridge) GetCreatedAt() string {
	return v.BridgeParts.CreatedAt
}

func (v *ListBridgesBridgesBridgesPayloadResultsBridge) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*ListBridgesBridgesBridgesPayloadResultsBridge
		graphql.NoUnmarshalJSON
	}
	firstPass.ListBridgesBridgesBridgesPayloadResultsBridge = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.BridgeParts)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalListBridgesBridgesBridgesPayloadResultsBridge struct {
	Id string `json:"id"`

	Name string `json:"name"`

	Url string `json:"url"`

	Confirmations int `json:"confirmations"`

	OutgoingToken string `json:"outgoingToken"`

	MinimumContractPayment string `json:"minimumContractPayment"`

	CreatedAt string `json:"createdAt"`
}

func (v *ListBridgesBridgesBridgesPayloadResultsBridge) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *ListBridgesBridgesBridgesPayloadResultsBridge) __premarshalJSON() (*__premarshalListBridgesBridgesBridgesPayloadResultsBridge, error) {
	var retval __premarshalListBridgesBridgesBridgesPayloadResultsBridge

	retval.Id = v.BridgeParts.Id
	retval.Name = v.BridgeParts.Name
	retval.Url = v.BridgeParts.Url
	retval.Confirmations = v.BridgeParts.Confirmations
	retval.OutgoingToken = v.BridgeParts.OutgoingToken
	retval.MinimumContractPayment = v.BridgeParts.MinimumContractPayment
	retval.CreatedAt = v.BridgeParts.CreatedAt
	return &retval, nil
}

// ListBridgesResponse is returned by ListBridges on success.
type ListBridgesResponse struct {
	Bridges ListBridgesBridgesBridgesPayload `json:"bridges"`
}

// GetBridges returns ListBridgesResponse.Bridges, and is useful for accessing the field via an interface.
func (v *ListBridgesResponse) GetBridges() ListBridgesBridgesBridgesPayload { return v.Bridges }

// ListFeedsManagersFeedsManagersFeedsManagersPayload includes the requested fields of the GraphQL type FeedsManagersPayload.
type ListFeedsManagersFeedsManagersFeedsManagersPayload struct {
	Results []ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager `json:"results"`
}

// GetResults returns ListFeedsManagersFeedsManagersFeedsManagersPayload.Results, and is useful for accessing the field via an interface.
func (v *ListFeedsManagersFeedsManagersFeedsManagersPayload) GetResults() []ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager {
	return v.Results
}

// ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager includes the requested fields of the GraphQL type FeedsManager.
type ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager struct {
	FeedsManagerParts `json:"-"`
}

// GetId returns ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager.Id, and is useful for accessing the field via an interface.
func (v *ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager) GetId() string {
	return v.FeedsManagerParts.Id
}

// GetName returns ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager.Name, and is useful for accessing the field via an interface.
func (v *ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager) GetName() string {
	return v.FeedsManagerParts.Name
}

// GetUri returns ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager.Uri, and is useful for accessing the field via an interface.
func (v *ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager) GetUri() string {
	return v.FeedsManagerParts.Uri
}

// GetPublicKey returns ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager.PublicKey, and is useful for accessing the field via an interface.
func (v *ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager) GetPublicKey() string {
	return v.FeedsManagerParts.PublicKey
}

// GetIsConnectionActive returns ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager.IsConnectionActive, and is useful for accessing the field via an interface.
func (v *ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager) GetIsConnectionActive() bool {
	return v.FeedsManagerParts.IsConnectionActive
}

// GetCreatedAt returns ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager.CreatedAt, and is useful for accessing the field via an interface.
func (v *ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager) GetCreatedAt() string {
	return v.FeedsManagerParts.CreatedAt
}

func (v *ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager
		graphql.NoUnmarshalJSON
	}
	firstPass.ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.FeedsManagerParts)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager struct {
	Id string `json:"id"`

	Name string `json:"name"`

	Uri string `json:"uri"`

	PublicKey string `json:"publicKey"`

	IsConnectionActive bool `json:"isConnectionActive"`

	CreatedAt string `json:"createdAt"`
}

func (v *ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *ListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager) __premarshalJSON() (*__premarshalListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager, error) {
	var retval __premarshalListFeedsManagersFeedsManagersFeedsManagersPayloadResultsFeedsManager

	retval.Id = v.FeedsManagerParts.Id
	retval.Name = v.FeedsManagerParts.Name
	retval.Uri = v.FeedsManagerParts.Uri
	retval.PublicKey = v.FeedsManagerParts.PublicKey
	retval.IsConnectionActive = v.FeedsManagerParts.IsConnectionActive
	retval.CreatedAt = v.FeedsManagerParts.CreatedAt
	return &retval, nil
}

// ListFeedsManagersResponse is returned by ListFeedsManagers on success.
type ListFeedsManagersResponse struct {
	FeedsManagers ListFeedsManagersFeedsManagersFeedsManagersPayload `json:"feedsManagers"`
}

// GetFeedsManagers returns ListFeedsManagersResponse.FeedsManagers, and is useful for accessing the field via an interface.
func (v *ListFeedsManagersResponse) GetFeedsManagers() ListFeedsManagersFeedsManagersFeedsManagersPayload {
	return v.FeedsManagers
}

// ListJobsJobsJobsPayload includes the requested fields of the GraphQL type JobsPayload.
type ListJobsJobsJobsPayload struct {
	Results  []ListJobsJobsJobsPayloadResultsJob               `json:"results"`
	Metadata ListJobsJobsJobsPayloadMetadataPaginationMetadata `json:"metadata"`
}

// GetResults returns ListJobsJobsJobsPayload.Results, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayload) GetResults() []ListJobsJobsJobsPayloadResultsJob { return v.Results }

// GetMetadata returns ListJobsJobsJobsPayload.Metadata, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayload) GetMetadata() ListJobsJobsJobsPayloadMetadataPaginationMetadata {
	return v.Metadata
}

// ListJobsJobsJobsPayloadMetadataPaginationMetadata includes the requested fields of the GraphQL type PaginationMetadata.
type ListJobsJobsJobsPayloadMetadataPaginationMetadata struct {
	Total int `json:"total"`
}

// GetTotal returns ListJobsJobsJobsPayloadMetadataPaginationMetadata.Total, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadMetadataPaginationMetadata) GetTotal() int { return v.Total }

// ListJobsJobsJobsPayloadResultsJob includes the requested fields of the GraphQL type Job.
type ListJobsJobsJobsPayloadResultsJob struct {
	JobParts `json:"-"`
}

// GetId returns ListJobsJobsJobsPayloadResultsJob.Id, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetId() string { return v.JobParts.Id }

// GetName returns ListJobsJobsJobsPayloadResultsJob.Name, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetName() string { return v.JobParts.Name }

// GetSchemaVersion returns ListJobsJobsJobsPayloadResultsJob.SchemaVersion, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetSchemaVersion() int { return v.JobParts.SchemaVersion }

// GetGasLimit returns ListJobsJobsJobsPayloadResultsJob.GasLimit, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetGasLimit() int { return v.JobParts.GasLimit }

// GetForwardingAllowed returns ListJobsJobsJobsPayloadResultsJob.ForwardingAllowed, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetForwardingAllowed() bool {
	return v.JobParts.ForwardingAllowed
}

// GetMaxTaskDuration returns ListJobsJobsJobsPayloadResultsJob.MaxTaskDuration, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetMaxTaskDuration() string {
	return v.JobParts.MaxTaskDuration
}

// GetExternalJobID returns ListJobsJobsJobsPayloadResultsJob.ExternalJobID, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetExternalJobID() string {
	return v.JobParts.ExternalJobID
}

// GetType returns ListJobsJobsJobsPayloadResultsJob.Type, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetType() string { return v.JobParts.Type }

// GetSpec returns ListJobsJobsJobsPayloadResultsJob.Spec, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetSpec() JobPartsSpecJobSpec { return v.JobParts.Spec }

// GetObservationSource returns ListJobsJobsJobsPayloadResultsJob.ObservationSource, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetObservationSource() string {
	return v.JobParts.ObservationSource
}

// GetErrors returns ListJobsJobsJobsPayloadResultsJob.Errors, and is useful for accessing the field via an interface.
func (v *ListJobsJobsJobsPayloadResultsJob) GetErrors() []JobPartsErrorsJobError {
	return v.JobParts.Errors
}

func (v *ListJobsJobsJobsPayloadResultsJob) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*ListJobsJobsJobsPayloadResultsJob
		graphql.NoUnmarshalJSON
	}
	firstPass.ListJobsJobsJobsPayloadResultsJob = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.JobParts)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalListJobsJobsJobsPayloadResultsJob struct {
	Id string `json:"id"`

	Name string `json:"name"`

	SchemaVersion int `json:"schemaVersion"`

	GasLimit int `json:"gasLimit"`

	ForwardingAllowed bool `json:"forwardingAllowed"`

	MaxTaskDuration string `json:"maxTaskDuration"`

	ExternalJobID string `json:"externalJobID"`

	Type string `json:"type"`

	Spec json.RawMessage `json:"spec"`

	ObservationSource string `json:"observationSource"`

	Errors []JobPartsErrorsJobError `json:"errors"`
}

func (v *ListJobsJobsJobsPayloadResultsJob) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *ListJobsJobsJobsPayloadResultsJob) __premarshalJSON() (*__premarshalListJobsJobsJobsPayloadResultsJob, error) {
	var retval __premarshalListJobsJobsJobsPayloadResultsJob

	retval.Id = v.JobParts.Id
	retval.Name = v.JobParts.Name
	retval.SchemaVersion = v.JobParts.SchemaVersion
	retval.GasLimit = v.JobParts.GasLimit
	retval.ForwardingAllowed = v.JobParts.ForwardingAllowed
	retval.MaxTaskDuration = v.JobParts.MaxTaskDuration
	retval.ExternalJobID = v.JobParts.ExternalJobID
	retval.Type = v.JobParts.Type
	{

		dst := &retval.Spec
		src := v.JobParts.Spec
		var err error
		*dst, err = __marshalJobPartsSpecJobSpec(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal ListJobsJobsJobsPayloadResultsJob.JobParts.Spec: %w", err)
		}
	}
	retval.ObservationSource = v.JobParts.ObservationSource
	retval.Errors = v.JobParts.Errors
	return &retval, nil
}

// ListJobsResponse is returned by ListJobs on success.
type ListJobsResponse struct {
	Jobs ListJobsJobsJobsPayload `json:"jobs"`
}

// GetJobs returns ListJobsResponse.Jobs, and is useful for accessing the field via an interface.
func (v *ListJobsResponse) GetJobs() ListJobsJobsJobsPayload { return v.Jobs }

type OCR2ChainType string

const (
	OCR2ChainTypeEvm      OCR2ChainType = "EVM"
	OCR2ChainTypeCosmos   OCR2ChainType = "COSMOS"
	OCR2ChainTypeSolana   OCR2ChainType = "SOLANA"
	OCR2ChainTypeStarknet OCR2ChainType = "STARKNET"
	OCR2ChainTypeAptos    OCR2ChainType = "APTOS"
)

// ####################
// Jobs and Job Proposals
// ####################
type OCR2Spec struct {
	BlockchainTimeout                 string        `json:"blockchainTimeout"`
	ContractID                        string        `json:"contractID"`
	ContractConfigConfirmations       int           `json:"contractConfigConfirmations"`
	ContractConfigTrackerPollInterval string        `json:"contractConfigTrackerPollInterval"`
	CreatedAt                         string        `json:"createdAt"`
	OcrKeyBundleID                    string        `json:"ocrKeyBundleID"`
	MonitoringEndpoint                string        `json:"monitoringEndpoint"`
	P2pv2Bootstrappers                []string      `json:"p2pv2Bootstrappers"`
	Relay                             string        `json:"relay"`
	RelayConfig                       gqlscalar.Map `json:"relayConfig"`
	TransmitterID                     string        `json:"transmitterID"`
	PluginType                        string        `json:"pluginType"`
	PluginConfig                      gqlscalar.Map `json:"pluginConfig"`
}

// GetBlockchainTimeout returns OCR2Spec.BlockchainTimeout, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetBlockchainTimeout() string { return v.BlockchainTimeout }

// GetContractID returns OCR2Spec.ContractID, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetContractID() string { return v.ContractID }

// GetContractConfigConfirmations returns OCR2Spec.ContractConfigConfirmations, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetContractConfigConfirmations() int { return v.ContractConfigConfirmations }

// GetContractConfigTrackerPollInterval returns OCR2Spec.ContractConfigTrackerPollInterval, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetContractConfigTrackerPollInterval() string {
	return v.ContractConfigTrackerPollInterval
}

// GetCreatedAt returns OCR2Spec.CreatedAt, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetCreatedAt() string { return v.CreatedAt }

// GetOcrKeyBundleID returns OCR2Spec.OcrKeyBundleID, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetOcrKeyBundleID() string { return v.OcrKeyBundleID }

// GetMonitoringEndpoint returns OCR2Spec.MonitoringEndpoint, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetMonitoringEndpoint() string { return v.MonitoringEndpoint }

// GetP2pv2Bootstrappers returns OCR2Spec.P2pv2Bootstrappers, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetP2pv2Bootstrappers() []string { return v.P2pv2Bootstrappers }

// GetRelay returns OCR2Spec.Relay, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetRelay() string { return v.Relay }

// GetRelayConfig returns OCR2Spec.RelayConfig, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetRelayConfig() gqlscalar.Map { return v.RelayConfig }

// GetTransmitterID returns OCR2Spec.TransmitterID, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetTransmitterID() string { return v.TransmitterID }

// GetPluginType returns OCR2Spec.PluginType, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetPluginType() string { return v.PluginType }

// GetPluginConfig returns OCR2Spec.PluginConfig, and is useful for accessing the field via an interface.
func (v *OCR2Spec) GetPluginConfig() gqlscalar.Map { return v.PluginConfig }

// RejectJobProposalSpecRejectJobProposalSpecNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type RejectJobProposalSpecRejectJobProposalSpecNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns RejectJobProposalSpecRejectJobProposalSpecNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecNotFoundError) GetTypename() string {
	return v.Typename
}

// GetMessage returns RejectJobProposalSpecRejectJobProposalSpecNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecNotFoundError) GetMessage() string {
	return v.Message
}

// GetCode returns RejectJobProposalSpecRejectJobProposalSpecNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecNotFoundError) GetCode() ErrorCode { return v.Code }

// RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload includes the requested fields of the GraphQL interface RejectJobProposalSpecPayload.
//
// RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload is implemented by the following types:
// RejectJobProposalSpecRejectJobProposalSpecNotFoundError
// RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess
type RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload interface {
	implementsGraphQLInterfaceRejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *RejectJobProposalSpecRejectJobProposalSpecNotFoundError) implementsGraphQLInterfaceRejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload() {
}
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess) implementsGraphQLInterfaceRejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload() {
}

func __unmarshalRejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload(b []byte, v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "NotFoundError":
		*v = new(RejectJobProposalSpecRejectJobProposalSpecNotFoundError)
		return json.Unmarshal(b, *v)
	case "RejectJobProposalSpecSuccess":
		*v = new(RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing RejectJobProposalSpecPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload: "%v"`, tn.TypeName)
	}
}

func __marshalRejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload(v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *RejectJobProposalSpecRejectJobProposalSpecNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*RejectJobProposalSpecRejectJobProposalSpecNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess:
		typename = "RejectJobProposalSpecSuccess"

		result := struct {
			TypeName string `json:"__typename"`
			*RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload: "%T"`, v)
	}
}

// RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess includes the requested fields of the GraphQL type RejectJobProposalSpecSuccess.
type RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess struct {
	Typename string                                                                                    `json:"__typename"`
	Spec     RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec `json:"spec"`
}

// GetTypename returns RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess.Typename, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess) GetTypename() string {
	return v.Typename
}

// GetSpec returns RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess.Spec, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccess) GetSpec() RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec {
	return v.Spec
}

// RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec includes the requested fields of the GraphQL type JobProposalSpec.
type RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec struct {
	Id              string     `json:"id"`
	Definition      string     `json:"definition"`
	Version         int        `json:"version"`
	Status          SpecStatus `json:"status"`
	StatusUpdatedAt string     `json:"statusUpdatedAt"`
	CreatedAt       string     `json:"createdAt"`
	UpdatedAt       string     `json:"updatedAt"`
}

// GetId returns RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec.Id, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec) GetId() string {
	return v.Id
}

// GetDefinition returns RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec.Definition, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec) GetDefinition() string {
	return v.Definition
}

// GetVersion returns RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec.Version, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec) GetVersion() int {
	return v.Version
}

// GetStatus returns RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec.Status, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec) GetStatus() SpecStatus {
	return v.Status
}

// GetStatusUpdatedAt returns RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec.StatusUpdatedAt, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec) GetStatusUpdatedAt() string {
	return v.StatusUpdatedAt
}

// GetCreatedAt returns RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec.CreatedAt, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec) GetCreatedAt() string {
	return v.CreatedAt
}

// GetUpdatedAt returns RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec.UpdatedAt, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecSuccessSpecJobProposalSpec) GetUpdatedAt() string {
	return v.UpdatedAt
}

// RejectJobProposalSpecResponse is returned by RejectJobProposalSpec on success.
type RejectJobProposalSpecResponse struct {
	RejectJobProposalSpec RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload `json:"-"`
}

// GetRejectJobProposalSpec returns RejectJobProposalSpecResponse.RejectJobProposalSpec, and is useful for accessing the field via an interface.
func (v *RejectJobProposalSpecResponse) GetRejectJobProposalSpec() RejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload {
	return v.RejectJobProposalSpec
}

func (v *RejectJobProposalSpecResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*RejectJobProposalSpecResponse
		RejectJobProposalSpec json.RawMessage `json:"rejectJobProposalSpec"`
		graphql.NoUnmarshalJSON
	}
	firstPass.RejectJobProposalSpecResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.RejectJobProposalSpec
		src := firstPass.RejectJobProposalSpec
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalRejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal RejectJobProposalSpecResponse.RejectJobProposalSpec: %w", err)
			}
		}
	}
	return nil
}

type __premarshalRejectJobProposalSpecResponse struct {
	RejectJobProposalSpec json.RawMessage `json:"rejectJobProposalSpec"`
}

func (v *RejectJobProposalSpecResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *RejectJobProposalSpecResponse) __premarshalJSON() (*__premarshalRejectJobProposalSpecResponse, error) {
	var retval __premarshalRejectJobProposalSpecResponse

	{

		dst := &retval.RejectJobProposalSpec
		src := v.RejectJobProposalSpec
		var err error
		*dst, err = __marshalRejectJobProposalSpecRejectJobProposalSpecRejectJobProposalSpecPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal RejectJobProposalSpecResponse.RejectJobProposalSpec: %w", err)
		}
	}
	return &retval, nil
}

type SpecStatus string

const (
	SpecStatusUnknown   SpecStatus = "UNKNOWN"
	SpecStatusPending   SpecStatus = "PENDING"
	SpecStatusApproved  SpecStatus = "APPROVED"
	SpecStatusRejected  SpecStatus = "REJECTED"
	SpecStatusCancelled SpecStatus = "CANCELLED"
	SpecStatusRevoked   SpecStatus = "REVOKED"
)

type UpdateFeedsManagerInput struct {
	Name      string `json:"name"`
	Uri       string `json:"uri"`
	PublicKey string `json:"publicKey"`
}

// GetName returns UpdateFeedsManagerInput.Name, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerInput) GetName() string { return v.Name }

// GetUri returns UpdateFeedsManagerInput.Uri, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerInput) GetUri() string { return v.Uri }

// GetPublicKey returns UpdateFeedsManagerInput.PublicKey, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerInput) GetPublicKey() string { return v.PublicKey }

// UpdateFeedsManagerResponse is returned by UpdateFeedsManager on success.
type UpdateFeedsManagerResponse struct {
	UpdateFeedsManager UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload `json:"-"`
}

// GetUpdateFeedsManager returns UpdateFeedsManagerResponse.UpdateFeedsManager, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerResponse) GetUpdateFeedsManager() UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload {
	return v.UpdateFeedsManager
}

func (v *UpdateFeedsManagerResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*UpdateFeedsManagerResponse
		UpdateFeedsManager json.RawMessage `json:"updateFeedsManager"`
		graphql.NoUnmarshalJSON
	}
	firstPass.UpdateFeedsManagerResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.UpdateFeedsManager
		src := firstPass.UpdateFeedsManager
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal UpdateFeedsManagerResponse.UpdateFeedsManager: %w", err)
			}
		}
	}
	return nil
}

type __premarshalUpdateFeedsManagerResponse struct {
	UpdateFeedsManager json.RawMessage `json:"updateFeedsManager"`
}

func (v *UpdateFeedsManagerResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *UpdateFeedsManagerResponse) __premarshalJSON() (*__premarshalUpdateFeedsManagerResponse, error) {
	var retval __premarshalUpdateFeedsManagerResponse

	{

		dst := &retval.UpdateFeedsManager
		src := v.UpdateFeedsManager
		var err error
		*dst, err = __marshalUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal UpdateFeedsManagerResponse.UpdateFeedsManager: %w", err)
		}
	}
	return &retval, nil
}

// UpdateFeedsManagerUpdateFeedsManagerInputErrors includes the requested fields of the GraphQL type InputErrors.
type UpdateFeedsManagerUpdateFeedsManagerInputErrors struct {
	Typename string                                                            `json:"__typename"`
	Errors   []UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError `json:"errors"`
}

// GetTypename returns UpdateFeedsManagerUpdateFeedsManagerInputErrors.Typename, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerInputErrors) GetTypename() string { return v.Typename }

// GetErrors returns UpdateFeedsManagerUpdateFeedsManagerInputErrors.Errors, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerInputErrors) GetErrors() []UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError {
	return v.Errors
}

// UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError includes the requested fields of the GraphQL type InputError.
type UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError struct {
	Message string    `json:"message"`
	Code    ErrorCode `json:"code"`
	Path    string    `json:"path"`
}

// GetMessage returns UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError.Message, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError) GetMessage() string {
	return v.Message
}

// GetCode returns UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError.Code, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError) GetCode() ErrorCode {
	return v.Code
}

// GetPath returns UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError.Path, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerInputErrorsErrorsInputError) GetPath() string {
	return v.Path
}

// UpdateFeedsManagerUpdateFeedsManagerNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type UpdateFeedsManagerUpdateFeedsManagerNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns UpdateFeedsManagerUpdateFeedsManagerNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerNotFoundError) GetTypename() string { return v.Typename }

// GetMessage returns UpdateFeedsManagerUpdateFeedsManagerNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerNotFoundError) GetMessage() string { return v.Message }

// GetCode returns UpdateFeedsManagerUpdateFeedsManagerNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerNotFoundError) GetCode() ErrorCode { return v.Code }

// UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload includes the requested fields of the GraphQL interface UpdateFeedsManagerPayload.
//
// UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload is implemented by the following types:
// UpdateFeedsManagerUpdateFeedsManagerInputErrors
// UpdateFeedsManagerUpdateFeedsManagerNotFoundError
// UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess
type UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload interface {
	implementsGraphQLInterfaceUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *UpdateFeedsManagerUpdateFeedsManagerInputErrors) implementsGraphQLInterfaceUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload() {
}
func (v *UpdateFeedsManagerUpdateFeedsManagerNotFoundError) implementsGraphQLInterfaceUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload() {
}
func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess) implementsGraphQLInterfaceUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload() {
}

func __unmarshalUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload(b []byte, v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "InputErrors":
		*v = new(UpdateFeedsManagerUpdateFeedsManagerInputErrors)
		return json.Unmarshal(b, *v)
	case "NotFoundError":
		*v = new(UpdateFeedsManagerUpdateFeedsManagerNotFoundError)
		return json.Unmarshal(b, *v)
	case "UpdateFeedsManagerSuccess":
		*v = new(UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing UpdateFeedsManagerPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload: "%v"`, tn.TypeName)
	}
}

func __marshalUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload(v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *UpdateFeedsManagerUpdateFeedsManagerInputErrors:
		typename = "InputErrors"

		result := struct {
			TypeName string `json:"__typename"`
			*UpdateFeedsManagerUpdateFeedsManagerInputErrors
		}{typename, v}
		return json.Marshal(result)
	case *UpdateFeedsManagerUpdateFeedsManagerNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*UpdateFeedsManagerUpdateFeedsManagerNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess:
		typename = "UpdateFeedsManagerSuccess"

		result := struct {
			TypeName string `json:"__typename"`
			*UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerPayload: "%T"`, v)
	}
}

// UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess includes the requested fields of the GraphQL type UpdateFeedsManagerSuccess.
type UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess struct {
	Typename     string                                                                    `json:"__typename"`
	FeedsManager UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager `json:"feedsManager"`
}

// GetTypename returns UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess.Typename, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess) GetTypename() string {
	return v.Typename
}

// GetFeedsManager returns UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess.FeedsManager, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccess) GetFeedsManager() UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager {
	return v.FeedsManager
}

// UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager includes the requested fields of the GraphQL type FeedsManager.
type UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager struct {
	FeedsManagerParts `json:"-"`
}

// GetId returns UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager.Id, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager) GetId() string {
	return v.FeedsManagerParts.Id
}

// GetName returns UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager.Name, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager) GetName() string {
	return v.FeedsManagerParts.Name
}

// GetUri returns UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager.Uri, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager) GetUri() string {
	return v.FeedsManagerParts.Uri
}

// GetPublicKey returns UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager.PublicKey, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager) GetPublicKey() string {
	return v.FeedsManagerParts.PublicKey
}

// GetIsConnectionActive returns UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager.IsConnectionActive, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager) GetIsConnectionActive() bool {
	return v.FeedsManagerParts.IsConnectionActive
}

// GetCreatedAt returns UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager.CreatedAt, and is useful for accessing the field via an interface.
func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager) GetCreatedAt() string {
	return v.FeedsManagerParts.CreatedAt
}

func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager
		graphql.NoUnmarshalJSON
	}
	firstPass.UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	err = json.Unmarshal(
		b, &v.FeedsManagerParts)
	if err != nil {
		return err
	}
	return nil
}

type __premarshalUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager struct {
	Id string `json:"id"`

	Name string `json:"name"`

	Uri string `json:"uri"`

	PublicKey string `json:"publicKey"`

	IsConnectionActive bool `json:"isConnectionActive"`

	CreatedAt string `json:"createdAt"`
}

func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *UpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager) __premarshalJSON() (*__premarshalUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager, error) {
	var retval __premarshalUpdateFeedsManagerUpdateFeedsManagerUpdateFeedsManagerSuccessFeedsManager

	retval.Id = v.FeedsManagerParts.Id
	retval.Name = v.FeedsManagerParts.Name
	retval.Uri = v.FeedsManagerParts.Uri
	retval.PublicKey = v.FeedsManagerParts.PublicKey
	retval.IsConnectionActive = v.FeedsManagerParts.IsConnectionActive
	retval.CreatedAt = v.FeedsManagerParts.CreatedAt
	return &retval, nil
}

type UpdateJobProposalSpecDefinitionInput struct {
	Definition string `json:"definition"`
}

// GetDefinition returns UpdateJobProposalSpecDefinitionInput.Definition, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionInput) GetDefinition() string { return v.Definition }

// UpdateJobProposalSpecDefinitionResponse is returned by UpdateJobProposalSpecDefinition on success.
type UpdateJobProposalSpecDefinitionResponse struct {
	UpdateJobProposalSpecDefinition UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload `json:"-"`
}

// GetUpdateJobProposalSpecDefinition returns UpdateJobProposalSpecDefinitionResponse.UpdateJobProposalSpecDefinition, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionResponse) GetUpdateJobProposalSpecDefinition() UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload {
	return v.UpdateJobProposalSpecDefinition
}

func (v *UpdateJobProposalSpecDefinitionResponse) UnmarshalJSON(b []byte) error {

	if string(b) == "null" {
		return nil
	}

	var firstPass struct {
		*UpdateJobProposalSpecDefinitionResponse
		UpdateJobProposalSpecDefinition json.RawMessage `json:"updateJobProposalSpecDefinition"`
		graphql.NoUnmarshalJSON
	}
	firstPass.UpdateJobProposalSpecDefinitionResponse = v

	err := json.Unmarshal(b, &firstPass)
	if err != nil {
		return err
	}

	{
		dst := &v.UpdateJobProposalSpecDefinition
		src := firstPass.UpdateJobProposalSpecDefinition
		if len(src) != 0 && string(src) != "null" {
			err = __unmarshalUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload(
				src, dst)
			if err != nil {
				return fmt.Errorf(
					"unable to unmarshal UpdateJobProposalSpecDefinitionResponse.UpdateJobProposalSpecDefinition: %w", err)
			}
		}
	}
	return nil
}

type __premarshalUpdateJobProposalSpecDefinitionResponse struct {
	UpdateJobProposalSpecDefinition json.RawMessage `json:"updateJobProposalSpecDefinition"`
}

func (v *UpdateJobProposalSpecDefinitionResponse) MarshalJSON() ([]byte, error) {
	premarshaled, err := v.__premarshalJSON()
	if err != nil {
		return nil, err
	}
	return json.Marshal(premarshaled)
}

func (v *UpdateJobProposalSpecDefinitionResponse) __premarshalJSON() (*__premarshalUpdateJobProposalSpecDefinitionResponse, error) {
	var retval __premarshalUpdateJobProposalSpecDefinitionResponse

	{

		dst := &retval.UpdateJobProposalSpecDefinition
		src := v.UpdateJobProposalSpecDefinition
		var err error
		*dst, err = __marshalUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload(
			&src)
		if err != nil {
			return nil, fmt.Errorf(
				"unable to marshal UpdateJobProposalSpecDefinitionResponse.UpdateJobProposalSpecDefinition: %w", err)
		}
	}
	return &retval, nil
}

// UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError includes the requested fields of the GraphQL type NotFoundError.
type UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError struct {
	Typename string    `json:"__typename"`
	Message  string    `json:"message"`
	Code     ErrorCode `json:"code"`
}

// GetTypename returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError.Typename, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError) GetTypename() string {
	return v.Typename
}

// GetMessage returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError.Message, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError) GetMessage() string {
	return v.Message
}

// GetCode returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError.Code, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError) GetCode() ErrorCode {
	return v.Code
}

// UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload includes the requested fields of the GraphQL interface UpdateJobProposalSpecDefinitionPayload.
//
// UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload is implemented by the following types:
// UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError
// UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess
type UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload interface {
	implementsGraphQLInterfaceUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload()
	// GetTypename returns the receiver's concrete GraphQL type-name (see interface doc for possible values).
	GetTypename() string
}

func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError) implementsGraphQLInterfaceUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload() {
}
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess) implementsGraphQLInterfaceUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload() {
}

func __unmarshalUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload(b []byte, v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload) error {
	if string(b) == "null" {
		return nil
	}

	var tn struct {
		TypeName string `json:"__typename"`
	}
	err := json.Unmarshal(b, &tn)
	if err != nil {
		return err
	}

	switch tn.TypeName {
	case "NotFoundError":
		*v = new(UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError)
		return json.Unmarshal(b, *v)
	case "UpdateJobProposalSpecDefinitionSuccess":
		*v = new(UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess)
		return json.Unmarshal(b, *v)
	case "":
		return fmt.Errorf(
			"response was missing UpdateJobProposalSpecDefinitionPayload.__typename")
	default:
		return fmt.Errorf(
			`unexpected concrete type for UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload: "%v"`, tn.TypeName)
	}
}

func __marshalUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload(v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload) ([]byte, error) {

	var typename string
	switch v := (*v).(type) {
	case *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError:
		typename = "NotFoundError"

		result := struct {
			TypeName string `json:"__typename"`
			*UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionNotFoundError
		}{typename, v}
		return json.Marshal(result)
	case *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess:
		typename = "UpdateJobProposalSpecDefinitionSuccess"

		result := struct {
			TypeName string `json:"__typename"`
			*UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess
		}{typename, v}
		return json.Marshal(result)
	case nil:
		return []byte("null"), nil
	default:
		return nil, fmt.Errorf(
			`unexpected concrete type for UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionPayload: "%T"`, v)
	}
}

// UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess includes the requested fields of the GraphQL type UpdateJobProposalSpecDefinitionSuccess.
type UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess struct {
	Typename string                                                                                                                  `json:"__typename"`
	Spec     UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec `json:"spec"`
}

// GetTypename returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess.Typename, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess) GetTypename() string {
	return v.Typename
}

// GetSpec returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess.Spec, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccess) GetSpec() UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec {
	return v.Spec
}

// UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec includes the requested fields of the GraphQL type JobProposalSpec.
type UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec struct {
	Id              string     `json:"id"`
	Definition      string     `json:"definition"`
	Version         int        `json:"version"`
	Status          SpecStatus `json:"status"`
	StatusUpdatedAt string     `json:"statusUpdatedAt"`
	CreatedAt       string     `json:"createdAt"`
	UpdatedAt       string     `json:"updatedAt"`
}

// GetId returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec.Id, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec) GetId() string {
	return v.Id
}

// GetDefinition returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec.Definition, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec) GetDefinition() string {
	return v.Definition
}

// GetVersion returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec.Version, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec) GetVersion() int {
	return v.Version
}

// GetStatus returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec.Status, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec) GetStatus() SpecStatus {
	return v.Status
}

// GetStatusUpdatedAt returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec.StatusUpdatedAt, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec) GetStatusUpdatedAt() string {
	return v.StatusUpdatedAt
}

// GetCreatedAt returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec.CreatedAt, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec) GetCreatedAt() string {
	return v.CreatedAt
}

// GetUpdatedAt returns UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec.UpdatedAt, and is useful for accessing the field via an interface.
func (v *UpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionUpdateJobProposalSpecDefinitionSuccessSpecJobProposalSpec) GetUpdatedAt() string {
	return v.UpdatedAt
}

// __ApproveJobProposalSpecInput is used internally by genqlient
type __ApproveJobProposalSpecInput struct {
	Id    string `json:"id"`
	Force bool   `json:"force"`
}

// GetId returns __ApproveJobProposalSpecInput.Id, and is useful for accessing the field via an interface.
func (v *__ApproveJobProposalSpecInput) GetId() string { return v.Id }

// GetForce returns __ApproveJobProposalSpecInput.Force, and is useful for accessing the field via an interface.
func (v *__ApproveJobProposalSpecInput) GetForce() bool { return v.Force }

// __CancelJobProposalSpecInput is used internally by genqlient
type __CancelJobProposalSpecInput struct {
	Id string `json:"id"`
}

// GetId returns __CancelJobProposalSpecInput.Id, and is useful for accessing the field via an interface.
func (v *__CancelJobProposalSpecInput) GetId() string { return v.Id }

// __CreateFeedsManagerChainConfigInput is used internally by genqlient
type __CreateFeedsManagerChainConfigInput struct {
	Input CreateFeedsManagerChainConfigInput `json:"input"`
}

// GetInput returns __CreateFeedsManagerChainConfigInput.Input, and is useful for accessing the field via an interface.
func (v *__CreateFeedsManagerChainConfigInput) GetInput() CreateFeedsManagerChainConfigInput {
	return v.Input
}

// __CreateFeedsManagerInput is used internally by genqlient
type __CreateFeedsManagerInput struct {
	Input CreateFeedsManagerInput `json:"input"`
}

// GetInput returns __CreateFeedsManagerInput.Input, and is useful for accessing the field via an interface.
func (v *__CreateFeedsManagerInput) GetInput() CreateFeedsManagerInput { return v.Input }

// __GetBridgeInput is used internally by genqlient
type __GetBridgeInput struct {
	Id string `json:"id"`
}

// GetId returns __GetBridgeInput.Id, and is useful for accessing the field via an interface.
func (v *__GetBridgeInput) GetId() string { return v.Id }

// __GetFeedsManagerInput is used internally by genqlient
type __GetFeedsManagerInput struct {
	Id string `json:"id"`
}

// GetId returns __GetFeedsManagerInput.Id, and is useful for accessing the field via an interface.
func (v *__GetFeedsManagerInput) GetId() string { return v.Id }

// __GetJobInput is used internally by genqlient
type __GetJobInput struct {
	Id string `json:"id"`
}

// GetId returns __GetJobInput.Id, and is useful for accessing the field via an interface.
func (v *__GetJobInput) GetId() string { return v.Id }

// __GetJobProposalInput is used internally by genqlient
type __GetJobProposalInput struct {
	Id string `json:"id"`
}

// GetId returns __GetJobProposalInput.Id, and is useful for accessing the field via an interface.
func (v *__GetJobProposalInput) GetId() string { return v.Id }

// __ListBridgesInput is used internally by genqlient
type __ListBridgesInput struct {
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
}

// GetOffset returns __ListBridgesInput.Offset, and is useful for accessing the field via an interface.
func (v *__ListBridgesInput) GetOffset() int { return v.Offset }

// GetLimit returns __ListBridgesInput.Limit, and is useful for accessing the field via an interface.
func (v *__ListBridgesInput) GetLimit() int { return v.Limit }

// __ListJobsInput is used internally by genqlient
type __ListJobsInput struct {
	Offset int `json:"offset"`
	Limit  int `json:"limit"`
}

// GetOffset returns __ListJobsInput.Offset, and is useful for accessing the field via an interface.
func (v *__ListJobsInput) GetOffset() int { return v.Offset }

// GetLimit returns __ListJobsInput.Limit, and is useful for accessing the field via an interface.
func (v *__ListJobsInput) GetLimit() int { return v.Limit }

// __RejectJobProposalSpecInput is used internally by genqlient
type __RejectJobProposalSpecInput struct {
	Id string `json:"id"`
}

// GetId returns __RejectJobProposalSpecInput.Id, and is useful for accessing the field via an interface.
func (v *__RejectJobProposalSpecInput) GetId() string { return v.Id }

// __UpdateFeedsManagerInput is used internally by genqlient
type __UpdateFeedsManagerInput struct {
	Id    string                  `json:"id"`
	Input UpdateFeedsManagerInput `json:"input"`
}

// GetId returns __UpdateFeedsManagerInput.Id, and is useful for accessing the field via an interface.
func (v *__UpdateFeedsManagerInput) GetId() string { return v.Id }

// GetInput returns __UpdateFeedsManagerInput.Input, and is useful for accessing the field via an interface.
func (v *__UpdateFeedsManagerInput) GetInput() UpdateFeedsManagerInput { return v.Input }

// __UpdateJobProposalSpecDefinitionInput is used internally by genqlient
type __UpdateJobProposalSpecDefinitionInput struct {
	Id    string                               `json:"id"`
	Input UpdateJobProposalSpecDefinitionInput `json:"input"`
}

// GetId returns __UpdateJobProposalSpecDefinitionInput.Id, and is useful for accessing the field via an interface.
func (v *__UpdateJobProposalSpecDefinitionInput) GetId() string { return v.Id }

// GetInput returns __UpdateJobProposalSpecDefinitionInput.Input, and is useful for accessing the field via an interface.
func (v *__UpdateJobProposalSpecDefinitionInput) GetInput() UpdateJobProposalSpecDefinitionInput {
	return v.Input
}

// The query or mutation executed by ApproveJobProposalSpec.
const ApproveJobProposalSpec_Operation = `
mutation ApproveJobProposalSpec ($id: ID!, $force: Boolean) {
	approveJobProposalSpec(id: $id, force: $force) {
		__typename
		... on ApproveJobProposalSpecSuccess {
			spec {
				id
				definition
				version
				status
				statusUpdatedAt
				createdAt
				updatedAt
			}
		}
		... on JobAlreadyExistsError {
			message
			code
		}
		... on NotFoundError {
			message
			code
		}
	}
}
`

func ApproveJobProposalSpec(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
	force bool,
) (*ApproveJobProposalSpecResponse, error) {
	req_ := &graphql.Request{
		OpName: "ApproveJobProposalSpec",
		Query:  ApproveJobProposalSpec_Operation,
		Variables: &__ApproveJobProposalSpecInput{
			Id:    id,
			Force: force,
		},
	}
	var err_ error

	var data_ ApproveJobProposalSpecResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by CancelJobProposalSpec.
const CancelJobProposalSpec_Operation = `
mutation CancelJobProposalSpec ($id: ID!) {
	cancelJobProposalSpec(id: $id) {
		__typename
		... on CancelJobProposalSpecSuccess {
			spec {
				id
				definition
				version
				status
				statusUpdatedAt
				createdAt
				updatedAt
			}
		}
		... on NotFoundError {
			message
			code
		}
	}
}
`

func CancelJobProposalSpec(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
) (*CancelJobProposalSpecResponse, error) {
	req_ := &graphql.Request{
		OpName: "CancelJobProposalSpec",
		Query:  CancelJobProposalSpec_Operation,
		Variables: &__CancelJobProposalSpecInput{
			Id: id,
		},
	}
	var err_ error

	var data_ CancelJobProposalSpecResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by CreateFeedsManager.
const CreateFeedsManager_Operation = `
mutation CreateFeedsManager ($input: CreateFeedsManagerInput!) {
	createFeedsManager(input: $input) {
		__typename
		... on CreateFeedsManagerSuccess {
			feedsManager {
				... FeedsManagerParts
			}
		}
		... on SingleFeedsManagerError {
			message
			code
		}
		... on NotFoundError {
			message
			code
		}
		... on InputErrors {
			errors {
				message
				code
				path
			}
		}
	}
}
fragment FeedsManagerParts on FeedsManager {
	id
	name
	uri
	publicKey
	isConnectionActive
	createdAt
}
`

func CreateFeedsManager(
	ctx_ context.Context,
	client_ graphql.Client,
	input CreateFeedsManagerInput,
) (*CreateFeedsManagerResponse, error) {
	req_ := &graphql.Request{
		OpName: "CreateFeedsManager",
		Query:  CreateFeedsManager_Operation,
		Variables: &__CreateFeedsManagerInput{
			Input: input,
		},
	}
	var err_ error

	var data_ CreateFeedsManagerResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by CreateFeedsManagerChainConfig.
const CreateFeedsManagerChainConfig_Operation = `
mutation CreateFeedsManagerChainConfig ($input: CreateFeedsManagerChainConfigInput!) {
	createFeedsManagerChainConfig(input: $input) {
		__typename
		... on CreateFeedsManagerChainConfigSuccess {
			chainConfig {
				id
				chainID
				chainType
				accountAddr
				adminAddr
				fluxMonitorJobConfig {
					enabled
				}
				ocr1JobConfig {
					enabled
					isBootstrap
					multiaddr
					p2pPeerID
					keyBundleID
				}
				ocr2JobConfig {
					enabled
					isBootstrap
					multiaddr
					forwarderAddress
					p2pPeerID
					keyBundleID
					plugins {
						commit
						execute
						median
						mercury
						rebalancer
					}
				}
			}
		}
		... on NotFoundError {
			message
			code
		}
		... on InputErrors {
			errors {
				message
				path
			}
		}
	}
}
`

// createFeedsManagerChainConfig.graphql
func CreateFeedsManagerChainConfig(
	ctx_ context.Context,
	client_ graphql.Client,
	input CreateFeedsManagerChainConfigInput,
) (*CreateFeedsManagerChainConfigResponse, error) {
	req_ := &graphql.Request{
		OpName: "CreateFeedsManagerChainConfig",
		Query:  CreateFeedsManagerChainConfig_Operation,
		Variables: &__CreateFeedsManagerChainConfigInput{
			Input: input,
		},
	}
	var err_ error

	var data_ CreateFeedsManagerChainConfigResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by FetchAccounts.
const FetchAccounts_Operation = `
query FetchAccounts {
	ethKeys {
		results {
			address
			isDisabled
			chain {
				id
				enabled
			}
			ethBalance
			linkBalance
		}
	}
}
`

func FetchAccounts(
	ctx_ context.Context,
	client_ graphql.Client,
) (*FetchAccountsResponse, error) {
	req_ := &graphql.Request{
		OpName: "FetchAccounts",
		Query:  FetchAccounts_Operation,
	}
	var err_ error

	var data_ FetchAccountsResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by FetchCSAKeys.
const FetchCSAKeys_Operation = `
query FetchCSAKeys {
	csaKeys {
		results {
			id
			publicKey
			version
		}
	}
}
`

func FetchCSAKeys(
	ctx_ context.Context,
	client_ graphql.Client,
) (*FetchCSAKeysResponse, error) {
	req_ := &graphql.Request{
		OpName: "FetchCSAKeys",
		Query:  FetchCSAKeys_Operation,
	}
	var err_ error

	var data_ FetchCSAKeysResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by FetchOCR2KeyBundles.
const FetchOCR2KeyBundles_Operation = `
query FetchOCR2KeyBundles {
	ocr2KeyBundles {
		results {
			id
			chainType
			configPublicKey
			onChainPublicKey
			offChainPublicKey
		}
	}
}
`

func FetchOCR2KeyBundles(
	ctx_ context.Context,
	client_ graphql.Client,
) (*FetchOCR2KeyBundlesResponse, error) {
	req_ := &graphql.Request{
		OpName: "FetchOCR2KeyBundles",
		Query:  FetchOCR2KeyBundles_Operation,
	}
	var err_ error

	var data_ FetchOCR2KeyBundlesResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by FetchP2PKeys.
const FetchP2PKeys_Operation = `
query FetchP2PKeys {
	p2pKeys {
		results {
			id
			peerID
			publicKey
		}
	}
}
`

func FetchP2PKeys(
	ctx_ context.Context,
	client_ graphql.Client,
) (*FetchP2PKeysResponse, error) {
	req_ := &graphql.Request{
		OpName: "FetchP2PKeys",
		Query:  FetchP2PKeys_Operation,
	}
	var err_ error

	var data_ FetchP2PKeysResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by GetBridge.
const GetBridge_Operation = `
query GetBridge ($id: ID!) {
	bridge(id: $id) {
		__typename
		... BridgeParts
		... on NotFoundError {
			message
			code
		}
	}
}
fragment BridgeParts on Bridge {
	id
	name
	url
	confirmations
	outgoingToken
	minimumContractPayment
	createdAt
}
`

func GetBridge(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
) (*GetBridgeResponse, error) {
	req_ := &graphql.Request{
		OpName: "GetBridge",
		Query:  GetBridge_Operation,
		Variables: &__GetBridgeInput{
			Id: id,
		},
	}
	var err_ error

	var data_ GetBridgeResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by GetFeedsManager.
const GetFeedsManager_Operation = `
query GetFeedsManager ($id: ID!) {
	feedsManager(id: $id) {
		__typename
		... FeedsManagerParts
		... on NotFoundError {
			message
			code
		}
	}
}
fragment FeedsManagerParts on FeedsManager {
	id
	name
	uri
	publicKey
	isConnectionActive
	createdAt
}
`

func GetFeedsManager(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
) (*GetFeedsManagerResponse, error) {
	req_ := &graphql.Request{
		OpName: "GetFeedsManager",
		Query:  GetFeedsManager_Operation,
		Variables: &__GetFeedsManagerInput{
			Id: id,
		},
	}
	var err_ error

	var data_ GetFeedsManagerResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by GetJob.
const GetJob_Operation = `
query GetJob ($id: ID!) {
	job(id: $id) {
		__typename
		... JobParts
		... on NotFoundError {
			message
			code
		}
	}
}
fragment JobParts on Job {
	id
	name
	schemaVersion
	gasLimit
	forwardingAllowed
	maxTaskDuration
	externalJobID
	type
	spec {
		__typename
		... on OCR2Spec {
			... OCR2Spec
		}
	}
	observationSource
	errors {
		id
		description
		occurrences
		createdAt
		updatedAt
	}
}
fragment OCR2Spec on OCR2Spec {
	blockchainTimeout
	contractID
	contractConfigConfirmations
	contractConfigTrackerPollInterval
	createdAt
	ocrKeyBundleID
	monitoringEndpoint
	p2pv2Bootstrappers
	relay
	relayConfig
	transmitterID
	pluginType
	pluginConfig
}
`

func GetJob(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
) (*GetJobResponse, error) {
	req_ := &graphql.Request{
		OpName: "GetJob",
		Query:  GetJob_Operation,
		Variables: &__GetJobInput{
			Id: id,
		},
	}
	var err_ error

	var data_ GetJobResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by GetJobProposal.
const GetJobProposal_Operation = `
query GetJobProposal ($id: ID!) {
	jobProposal(id: $id) {
		__typename
		... on JobProposal {
			id
			name
			status
			remoteUUID
			externalJobID
			jobID
			feedsManager {
				... FeedsManagerParts
			}
			multiAddrs
			pendingUpdate
			specs {
				id
				definition
				version
				status
				statusUpdatedAt
				createdAt
				updatedAt
			}
			latestSpec {
				id
				definition
				version
				status
				statusUpdatedAt
				createdAt
				updatedAt
			}
		}
		... on NotFoundError {
			message
			code
		}
	}
}
fragment FeedsManagerParts on FeedsManager {
	id
	name
	uri
	publicKey
	isConnectionActive
	createdAt
}
`

func GetJobProposal(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
) (*GetJobProposalResponse, error) {
	req_ := &graphql.Request{
		OpName: "GetJobProposal",
		Query:  GetJobProposal_Operation,
		Variables: &__GetJobProposalInput{
			Id: id,
		},
	}
	var err_ error

	var data_ GetJobProposalResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by ListBridges.
const ListBridges_Operation = `
query ListBridges ($offset: Int, $limit: Int) {
	bridges(offset: $offset, limit: $limit) {
		results {
			... BridgeParts
		}
		metadata {
			total
		}
	}
}
fragment BridgeParts on Bridge {
	id
	name
	url
	confirmations
	outgoingToken
	minimumContractPayment
	createdAt
}
`

func ListBridges(
	ctx_ context.Context,
	client_ graphql.Client,
	offset int,
	limit int,
) (*ListBridgesResponse, error) {
	req_ := &graphql.Request{
		OpName: "ListBridges",
		Query:  ListBridges_Operation,
		Variables: &__ListBridgesInput{
			Offset: offset,
			Limit:  limit,
		},
	}
	var err_ error

	var data_ ListBridgesResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by ListFeedsManagers.
const ListFeedsManagers_Operation = `
query ListFeedsManagers {
	feedsManagers {
		results {
			... FeedsManagerParts
		}
	}
}
fragment FeedsManagerParts on FeedsManager {
	id
	name
	uri
	publicKey
	isConnectionActive
	createdAt
}
`

func ListFeedsManagers(
	ctx_ context.Context,
	client_ graphql.Client,
) (*ListFeedsManagersResponse, error) {
	req_ := &graphql.Request{
		OpName: "ListFeedsManagers",
		Query:  ListFeedsManagers_Operation,
	}
	var err_ error

	var data_ ListFeedsManagersResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by ListJobs.
const ListJobs_Operation = `
query ListJobs ($offset: Int, $limit: Int) {
	jobs(offset: $offset, limit: $limit) {
		results {
			... JobParts
		}
		metadata {
			total
		}
	}
}
fragment JobParts on Job {
	id
	name
	schemaVersion
	gasLimit
	forwardingAllowed
	maxTaskDuration
	externalJobID
	type
	spec {
		__typename
		... on OCR2Spec {
			... OCR2Spec
		}
	}
	observationSource
	errors {
		id
		description
		occurrences
		createdAt
		updatedAt
	}
}
fragment OCR2Spec on OCR2Spec {
	blockchainTimeout
	contractID
	contractConfigConfirmations
	contractConfigTrackerPollInterval
	createdAt
	ocrKeyBundleID
	monitoringEndpoint
	p2pv2Bootstrappers
	relay
	relayConfig
	transmitterID
	pluginType
	pluginConfig
}
`

func ListJobs(
	ctx_ context.Context,
	client_ graphql.Client,
	offset int,
	limit int,
) (*ListJobsResponse, error) {
	req_ := &graphql.Request{
		OpName: "ListJobs",
		Query:  ListJobs_Operation,
		Variables: &__ListJobsInput{
			Offset: offset,
			Limit:  limit,
		},
	}
	var err_ error

	var data_ ListJobsResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by RejectJobProposalSpec.
const RejectJobProposalSpec_Operation = `
mutation RejectJobProposalSpec ($id: ID!) {
	rejectJobProposalSpec(id: $id) {
		__typename
		... on RejectJobProposalSpecSuccess {
			spec {
				id
				definition
				version
				status
				statusUpdatedAt
				createdAt
				updatedAt
			}
		}
		... on NotFoundError {
			message
			code
		}
	}
}
`

func RejectJobProposalSpec(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
) (*RejectJobProposalSpecResponse, error) {
	req_ := &graphql.Request{
		OpName: "RejectJobProposalSpec",
		Query:  RejectJobProposalSpec_Operation,
		Variables: &__RejectJobProposalSpecInput{
			Id: id,
		},
	}
	var err_ error

	var data_ RejectJobProposalSpecResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by UpdateFeedsManager.
const UpdateFeedsManager_Operation = `
mutation UpdateFeedsManager ($id: ID!, $input: UpdateFeedsManagerInput!) {
	updateFeedsManager(id: $id, input: $input) {
		__typename
		... on UpdateFeedsManagerSuccess {
			feedsManager {
				... FeedsManagerParts
			}
		}
		... on NotFoundError {
			message
			code
		}
		... on InputErrors {
			errors {
				message
				code
				path
			}
		}
	}
}
fragment FeedsManagerParts on FeedsManager {
	id
	name
	uri
	publicKey
	isConnectionActive
	createdAt
}
`

func UpdateFeedsManager(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
	input UpdateFeedsManagerInput,
) (*UpdateFeedsManagerResponse, error) {
	req_ := &graphql.Request{
		OpName: "UpdateFeedsManager",
		Query:  UpdateFeedsManager_Operation,
		Variables: &__UpdateFeedsManagerInput{
			Id:    id,
			Input: input,
		},
	}
	var err_ error

	var data_ UpdateFeedsManagerResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}

// The query or mutation executed by UpdateJobProposalSpecDefinition.
const UpdateJobProposalSpecDefinition_Operation = `
mutation UpdateJobProposalSpecDefinition ($id: ID!, $input: UpdateJobProposalSpecDefinitionInput!) {
	updateJobProposalSpecDefinition(id: $id, input: $input) {
		__typename
		... on UpdateJobProposalSpecDefinitionSuccess {
			spec {
				id
				definition
				version
				status
				statusUpdatedAt
				createdAt
				updatedAt
			}
		}
		... on NotFoundError {
			message
			code
		}
	}
}
`

func UpdateJobProposalSpecDefinition(
	ctx_ context.Context,
	client_ graphql.Client,
	id string,
	input UpdateJobProposalSpecDefinitionInput,
) (*UpdateJobProposalSpecDefinitionResponse, error) {
	req_ := &graphql.Request{
		OpName: "UpdateJobProposalSpecDefinition",
		Query:  UpdateJobProposalSpecDefinition_Operation,
		Variables: &__UpdateJobProposalSpecDefinitionInput{
			Id:    id,
			Input: input,
		},
	}
	var err_ error

	var data_ UpdateJobProposalSpecDefinitionResponse
	resp_ := &graphql.Response{Data: &data_}

	err_ = client_.MakeRequest(
		ctx_,
		req_,
		resp_,
	)

	return &data_, err_
}
