# DO NOT UPDATE THIS FILE WITH ANY SECRET VALUES.
# This file serves as a template for the actual ~/.testsecrets file. Follow these steps to use it:
# 1. Create a copy of this template in your home directory under ~/.testsecrets
# 2. Update ~/.testsecrets with actual secret values required for your tests. The file will be automatically loaded by the test framework
# 3. Only include secrets necessary for the tests you are running. For example, if you are only running tests on Ethereum Mainnet, you do not need secrets for Base Mainnet. Comment other env vars.
# DO NOT COMMIT THE ACTUAL SECRETS FILE TO THE REPOSITORY.

# Chainlink image used for NewCLCluster
E2E_TEST_CHAINLINK_IMAGE="***.dkr.ecr.***.amazonaws.com/chainlink-ccip"

# Chainlink upgrade image for NewCLCluster. Used only for upgrade tests
E2E_TEST_CHAINLINK_UPGRADE_IMAGE="***.dkr.ecr.***.amazonaws.com/chainlink-ccip"

# Ethereum network secrets
E2E_TEST_ETHEREUM_MAINNET_WALLET_KEY="<private key for ethereum network>"
E2E_TEST_ETHEREUM_MAINNET_RPC_HTTP_URL="<http url 0 for ethereum network>"
E2E_TEST_ETHEREUM_MAINNET_RPC_HTTP_URL_1="<http url 1 for ethereum network>"
E2E_TEST_ETHEREUM_MAINNET_RPC_WS_URL="<ws ws 0 for ethereum network>"
E2E_TEST_ETHEREUM_MAINNET_RPC_WS_URL_1="<ws ws 1 for ethereum network>"

# Base network secrets
E2E_TEST_BASE_MAINNET_WALLET_KEY="<private key for base network>"
E2E_TEST_BASE_MAINNET_RPC_HTTP_URL="<http url 0 for base network>"
E2E_TEST_BASE_MAINNET_RPC_HTTP_URL_1="<http url 1 for base network>"
E2E_TEST_BASE_MAINNET_RPC_WS_URL="<ws ws 0 for base network>"
E2E_TEST_BASE_MAINNET_RPC_WS_URL_1="<ws ws 1 for base network>"

# Other networks secrets (pattern for envs)
# E2E_TEST_(.+)_WALLET_KEY_(\d+)="<i-th wallet key for (.+) network>"
# E2E_TEST_(.+)_RPC_HTTP_URL_(\d+)="<i-th http url for (.+) network>"
# E2E_TEST_(.+)_RPC_WS_URL_(\d+)="<i-th ws url for (.+) network>"

# Loki secrets
E2E_TEST_LOKI_TENANT_ID="<tenant id for loki>"
E2E_TEST_LOKI_ENDPOINT="<url for loki>"

# Grafana secrets
E2E_TEST_GRAFANA_BASE_URL="<url for grafana>"
E2E_TEST_GRAFANA_DASHBOARD_URL="/d/6vjVx-1V8/ccip-long-running-tests"