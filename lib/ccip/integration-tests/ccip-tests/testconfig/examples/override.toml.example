
[CCIP]
[CCIP.Deployments]
Data = """
{
  "lane_configs": {
    "geth_1337": {
      "is_mock_arm": true,
      "fee_token": "******************************************",
      "bridge_tokens": [
        "******************************************"
      ],
      "bridge_tokens_pools": [
        "******************************************"
      ],
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "geth_2337": {
          "on_ramp": "******************************************",
          "deployed_at": 71207
        }
      },
      "dest_contracts": {
        "geth_2337": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": ""
        }
      }
    },
    "geth_2337": {
      "is_mock_arm": true,
      "fee_token": "******************************************",
      "bridge_tokens": [
        "******************************************"
      ],
      "bridge_tokens_pools": [
        "******************************************"
      ],
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "geth_1337": {
          "on_ramp": "******************************************",
          "deployed_at": 71209
        }
      },
      "dest_contracts": {
        "geth_1337": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": ""
        }
      }
    }
  }
}
"""

[CCIP.Env]
EnvUser = 'crib-deployment'
Mockserver = 'http://mockserver:1080'

[CCIP.Env.Network]
selected_networks = ['geth_1337', 'geth_2337']

[CCIP.Env.Network.EVMNetworks.geth_1337]
evm_name = 'geth_1337'
evm_chain_id = 1337
evm_urls = ['wss://chain-alpha-rpc.nodeops.sand.cldev.sh/ws/']
evm_http_urls = ['https://chain-alpha-rpc.nodeops.sand.cldev.sh/']
evm_keys = ['8f2a55949038a9610f50fb23b5883af3b4ecb3c3bb792cbcefbd1542c692be63']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 500000
evm_transaction_timeout = '2m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 10000
evm_supports_eip1559 = false
evm_default_gas_limit = 6000000
evm_finality_depth = 1

[CCIP.Env.Network.EVMNetworks.geth_2337]
evm_name = 'geth_2337'
evm_chain_id = 2337
evm_urls = ['wss://chain-beta-rpc.nodeops.sand.cldev.sh/ws/']
evm_http_urls = ['https://chain-beta-rpc.nodeops.sand.cldev.sh/']
evm_keys = ['8f2a55949038a9610f50fb23b5883af3b4ecb3c3bb792cbcefbd1542c692be63']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 500000
evm_transaction_timeout = '2m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 10000
evm_supports_eip1559 = false
evm_default_gas_limit = 6000000
evm_finality_depth = 1

[CCIP.Env.ExistingCLCluster]
Name = 'crib-mono-repo-test'

[CCIP.Groups]
[CCIP.Groups.smoke]
LocalCluster = false
ExistingDeployment = true


[CCIP.Groups.smoke.MsgDetails]
MsgType = 'DataWithToken'
DestGasLimit = 100000
DataLength = 1000
NoOfTokens = 1
AmountPerToken = 1

