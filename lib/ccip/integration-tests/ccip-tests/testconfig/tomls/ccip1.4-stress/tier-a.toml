## Baseline performance test on simulated environment (with chaos)
## 40 chains / 400 lanes
## historyDepth 200 / finalityDepth 200
## block_time = 1s
## throughput 1msg / 5s
## 20% Token, 60% DataWithToken, 15% Regular size msgs, 5% Large msgs
##
## make test_load_ccip testimage=<aws account number>.dkr.ecr.<aws region>.amazonaws.com/chainlink-ccip-tests:ccip-develop \
##      testname=TestLoadCCIPStableRequestTriggeringWithNetworkChaos \
##      override_toml=./testconfig/tomls/ccip1.4-stress/tier-a.toml \
##      secret_toml=./testconfig/tomls/secrets.toml

[CCIP]
[CCIP.ContractVersions]
PriceRegistry = '1.2.0'
OffRamp = '1.2.0'
OnRamp = '1.2.0'
TokenPool = '1.4.0'
CommitStore = '1.2.0'

[CCIP.Env]
TTL = '8h'

[CCIP.Env.Network]
selected_networks= ['PRIVATE-CHAIN-1', 'SLOW-CHAIN-1', 'SLOW-CHAIN-2', 'SLOW-CHAIN-3']

[CCIP.Env.Network.EVMNetworks.PRIVATE-CHAIN-1]
evm_name = 'private-chain-1'
evm_chain_id = 2337
evm_urls = ['wss://ignore-this-url.com']
evm_http_urls = ['https://ignore-this-url.com']
evm_keys = ['59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 5000
evm_transaction_timeout = '3m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 1000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_finality_depth = 1

[CCIP.Env.Network.EVMNetworks.SLOW-CHAIN-1]
evm_name = 'slow-chain-1'
evm_chain_id = 90000001
evm_urls = ['wss://ignore-this-url.com']
evm_http_urls = ['https://ignore-this-url.com']
evm_keys = ['ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 5000
evm_transaction_timeout = '3m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 1000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_finality_depth = 1

[CCIP.Env.Network.EVMNetworks.SLOW-CHAIN-2]
evm_name = 'slow-chain-2'
evm_chain_id = 90000002
evm_urls = ['wss://ignore-this-url.com']
evm_http_urls = ['https://ignore-this-url.com']
evm_keys = ['ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 5000
evm_transaction_timeout = '3m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 1000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_finality_depth = 1

[CCIP.Env.Network.EVMNetworks.SLOW-CHAIN-3]
evm_name = 'slow-chain-3'
evm_chain_id = 1337
evm_urls = ['wss://ignore-this-url.com']
evm_http_urls = ['https://ignore-this-url.com']
evm_keys = ['ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 5000
evm_transaction_timeout = '3m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 1000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_finality_depth = 1

[CCIP.Env.Network.AnvilConfigs.PRIVATE-CHAIN-1]
block_time = 2

#
[CCIP.Env.Network.AnvilConfigs.SLOW-CHAIN-1]
block_time = 12

[CCIP.Env.Network.AnvilConfigs.SLOW-CHAIN-2]
block_time = 12

[CCIP.Env.Network.AnvilConfigs.SLOW-CHAIN-3]
block_time = 12

[CCIP.Env.NewCLCluster]
NoOfNodes = 17
NodeMemory = '10Gi'
NodeCPU = '6'
DBMemory = '16Gi'
DBCPU = '4'
DBStorageClass = 'gp3'
PromPgExporter = true
DBCapacity = '50Gi'
IsStateful = true
DBArgs = ['shared_buffers=4096MB', 'effective_cache_size=8192MB', 'work_mem=128MB']

[CCIP.Env.NewCLCluster.Common]
BaseConfigTOML = """
[Feature]
LogPoller = true
CCIP = true

[Log]
Level = 'debug'
JSONConsole = true

[Log.File]
MaxSize = '0b'

[WebServer]
AllowOrigins = '*'
HTTPPort = 6688
SecureCookies = false
HTTPWriteTimeout = '1m'

[WebServer.RateLimit]
Authenticated = 2000
Unauthenticated = 1000

[WebServer.TLS]
HTTPSPort = 0

[Database]
MaxIdleConns = 20
MaxOpenConns = 30
MigrateOnStartup = true

[OCR2]
Enabled = true
DefaultTransactionQueueDepth = 0

[OCR]
Enabled = false
DefaultTransactionQueueDepth = 0

[P2P]
[P2P.V2]
Enabled = true
ListenAddresses = ['0.0.0.0:6690']
AnnounceAddresses = ['0.0.0.0:6690']
DeltaDial = '500ms'
DeltaReconcile = '5s'
"""

#CommonChainConfigTOML = """
#[HeadTracker]
#HistoryDepth = 200
#
#[GasEstimator]
#PriceMax = '200 gwei'
#LimitDefault = 6000000
#FeeCapDefault = '200 gwei'
#"""

[CCIP.Groups]
[CCIP.Groups.load]
DenselyConnectedNetworkChainIds = ['90000001', '90000002', '1337']
KeepEnvAlive = true
NoOfCommitNodes = 16
PhaseTimeout = '40m'
NodeFunding = 1000.0
NoOfRoutersPerPair = 2
NoOfNetworks = 40
MaxNoOfLanes = 400

[CCIP.Groups.load.OffRampConfig]
BatchGasLimit = 11000000

[CCIP.Groups.load.TokenConfig]
TimeoutForPriceUpdate = '15m'
NoOfTokensPerChain = 60
NoOfTokensWithDynamicPrice = 15
DynamicPriceUpdateInterval ='5m'
CCIPOwnerTokens = true

[CCIP.Groups.load.LoadProfile]
TestDuration = '4h'
OptimizeSpace = true
NetworkChaosDelay = '100ms'
TimeUnit = '5s'
RequestPerUnitTime = [1]

[CCIP.Groups.load.LoadProfile.LoadFrequency.slow-chain-1]
TimeUnit = '10s'
RequestPerUnitTime = [1]

[CCIP.Groups.load.LoadProfile.LoadFrequency.slow-chain-2]
TimeUnit = '10s'
RequestPerUnitTime = [1]

[CCIP.Groups.load.LoadProfile.LoadFrequency.slow-chain-3]
TimeUnit = '10s'
RequestPerUnitTime = [1]

# to represent 20%, 60%, 15%, 5% of the total messages
[CCIP.Groups.load.LoadProfile.MsgProfile]
Frequencies = [4,12,3,1]

[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'Token'
DestGasLimit = 0
DataLength = 0
NoOfTokens = 5
AmountPerToken = 1

[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'DataWithToken'
DestGasLimit = 500000
DataLength = 5000
NoOfTokens = 5
AmountPerToken = 1

[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'Data'
DestGasLimit = 800000
DataLength = 10000

[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'Data'
DestGasLimit = 2500000
DataLength = 10000