[CCIP]
[CCIP.ContractVersions]
PriceRegistry = '1.2.0'
OffRamp = '1.2.0'
OnRamp = '1.2.0'
TokenPool = '1.4.0'
CommitStore = '1.2.0'

[CCIP.Env]
TTL = '15h'

[CCIP.Env.Network]
selected_networks= ['PRIVATE-CHAIN-1', 'PRIVATE-CHAIN-2']

[CCIP.Env.Network.EVMNetworks.PRIVATE-CHAIN-1]
evm_name = 'private-chain-1'
evm_chain_id = 2337
evm_urls = ['wss://ignore-this-url.com']
evm_http_urls = ['https://ignore-this-url.com']
evm_keys = ['59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 5000
evm_transaction_timeout = '3m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 1000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_finality_depth = 1

[CCIP.Env.Network.EVMNetworks.PRIVATE-CHAIN-2]
evm_name = 'private-chain-2'
evm_chain_id = 1337
evm_urls = ['wss://ignore-this-url.com']
evm_http_urls = ['https://ignore-this-url.com']
evm_keys = ['ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 5000
evm_transaction_timeout = '3m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 1000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_finality_depth = 1

[CCIP.Env.Network.AnvilConfigs.PRIVATE-CHAIN-1]
block_time = 1
#
[CCIP.Env.Network.AnvilConfigs.PRIVATE-CHAIN-2]
block_time = 1

[CCIP.Env.NewCLCluster]
NoOfNodes = 17
NodeMemory = '12Gi'
NodeCPU = '6'
DBMemory = '10Gi'
DBCPU = '2'
DBStorageClass = 'gp3'
PromPgExporter = true
DBCapacity = '50Gi'
IsStateful = true
DBArgs = ['shared_buffers=2048MB', 'effective_cache_size=4096MB', 'work_mem=64MB']

#[CCIP.Env.NewCLCluster.Common]
#CommonChainConfigTOML = """
#[HeadTracker]
#HistoryDepth = 3000
#
#[GasEstimator]
#PriceMax = '200 gwei'
#LimitDefault = 6000000
#FeeCapDefault = '200 gwei'
#"""

[CCIP.Groups]
[CCIP.Groups.load]
KeepEnvAlive = true
NoOfCommitNodes = 16
PhaseTimeout = '40m'
NodeFunding = 1000.0
NoOfRoutersPerPair = 2
NoOfNetworks = 40
MaxNoOfLanes = 200

[CCIP.Groups.load.OffRampConfig]
BatchGasLimit = 11000000

[CCIP.Groups.load.TokenConfig]
TimeoutForPriceUpdate = '15m'
NoOfTokensPerChain = 60
NoOfTokensWithDynamicPrice = 15
DynamicPriceUpdateInterval ='15s'
CCIPOwnerTokens = true

[CCIP.Groups.load.LoadProfile]
TestDuration = '4h'
TimeUnit = '5s'
RequestPerUnitTime = [1]
OptimizeSpace = true
NetworkChaosDelay = '100ms'

# to represent 20%, 60%, 15%, 5% of the total messages
[CCIP.Groups.load.LoadProfile.MsgProfile]
Frequencies = [4,12,3,1]

[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'Token'
DestGasLimit = 0
DataLength = 0
NoOfTokens = 5
AmountPerToken = 1

[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'DataWithToken'
DestGasLimit = 500000
DataLength = 5000
NoOfTokens = 5
AmountPerToken = 1

[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'Data'
DestGasLimit = 800000
DataLength = 10000

[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'Data'
DestGasLimit = 2500000
DataLength = 10000