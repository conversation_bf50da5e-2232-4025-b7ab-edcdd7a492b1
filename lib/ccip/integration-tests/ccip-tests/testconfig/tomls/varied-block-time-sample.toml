[CCIP]
[CCIP.Env]
[CCIP.Env.Network]
selected_networks= ['PRIVATE-CHAIN-1', 'PRIVATE-CHAIN-2']

[CCIP.Env.Network.EVMNetworks.PRIVATE-CHAIN-1]
evm_name = 'private-chain-1'
evm_chain_id = 2337
evm_urls = ['wss://ignore-this-url.com']
evm_http_urls = ['https://ignore-this-url.com']
evm_keys = ['59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 5000
evm_transaction_timeout = '3m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 1000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_finality_depth = 100 # with 50 blocks of finality, and 12s block time, we have 20 minutes of finality

[CCIP.Env.Network.EVMNetworks.PRIVATE-CHAIN-2]
evm_name = 'private-chain-2'
evm_chain_id = 1337
evm_urls = ['wss://ignore-this-url.com']
evm_http_urls = ['https://ignore-this-url.com']
evm_keys = ['ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80']
evm_simulated = true
client_implementation = 'Ethereum'
evm_chainlink_transaction_limit = 5000
evm_transaction_timeout = '3m'
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 1000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_finality_depth = 1 # with 1 block of finality, and 1s block time, we have instant finality


[CCIP.Env.Network.AnvilConfigs.PRIVATE-CHAIN-1]
block_time = 12

[CCIP.Env.Network.AnvilConfigs.PRIVATE-CHAIN-2]
block_time = 1

[CCIP.Groups]
[CCIP.Groups.smoke]
LocalCluster = false