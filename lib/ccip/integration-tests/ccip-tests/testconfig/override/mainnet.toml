[CCIP]
[CCIP.ContractVersions]
PriceRegistry = 'latest'
OffRamp = 'latest'
OnRamp = 'latest'
TokenPool = 'latest'
CommitStore = 'latest'

[CCIP.Deployments]
Data = """
{
  "lane_configs": {
    "Arbitrum Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "0x82aF49447D8a07e3bd95BD0d56f35241523fBab1",
      "arm": "0xC2C5E22a2d9715ed5C5BCC4D8eFf5966cf260744",
      "router": "0x141fa059441E0ca23ce184B6A78bafD2A517DdE8",
      "price_registry": "0x13015e4E6f839E1Aa1016DF521ea458ecA20438c",
      "wrapped_native": "0x82aF49447D8a07e3bd95BD0d56f35241523fBab1",
      "src_contracts": {
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Blast Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Metis Andromeda": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "WeMix Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "ZKSync Mainnet": {
          "on_ramp": "0xd67F6713Fa4448548c984a9a7DCFBD13B0fB78D6",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Avalanche Mainnet": {
          "off_ramp": "0x95095007d5Cc3E7517A1A03c9e228adA5D0bc376",
          "commit_store": "0x46679C9E93B7312A9191A9aD12A73b0c86A33623",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "0x16B9709F8A23B9EB922E8Dde7EaB1Ede7C79F663",
          "commit_store": "0x6c3fD63b9BdE38C414530727a5De858ca023cFc4",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Blast Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Metis Andromeda": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "0x27a971D482335d0f8d1917451390734f7372A4a3",
          "commit_store": "0x6642E640321e1Ad01eef2fC2ad5427D84A2Ee269",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "0xcabc2D71dC3172a154A5A34cD706B050e0ef9b6f",
          "commit_store": "0x78B15A57889200F246fc52790c4F3DfC37d82Aa2",
          "receiver_dapp": "******************************************"
        },
        "WeMix Mainnet": {
          "off_ramp": "0x893c14bA328A49336a188F972f997C0d7286B8E4",
          "commit_store": "0xc986D260b096E8708D82063309fB98734481A045",
          "receiver_dapp": "******************************************"
        },
        "ZKSync Mainnet": {
          "off_ramp": "0x052CF0c46375287255c71B179b10a7BFFD97502F",
          "commit_store": "0xE19E9765857A2371d849FDd26D62D2463fb7a0a9",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Avalanche Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "0x4f6Ec25f06A114ADD3154DC17fb637F750AdaA31",
      "router": "0xF4c7E640EdA248ef95972845a62bdC74237805dB",
      "price_registry": "0xfA4edD04eaAcDB07c8D73621bc1790eC50D8c489",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "WeMix Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "WeMix Mainnet": {
          "off_ramp": "0xFA5CF1bBFe0Ba5c01e60513EF8960945A99B78A4",
          "commit_store": "0x671c83B1Ebe798bfC625E99Be0FF7C48F6E4C491",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "BSC Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c",
      "arm": "0x56491A98199aD2e687Ea9D0cFB7b4AC57B4980Fc",
      "router": "0x34B03Cb9086d7D758AC55af71584F81A598759FE",
      "price_registry": "0xd64aAbD70A71d9f0A00B99F6EFc1626aA2dD43C7",
      "wrapped_native": "0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Blast Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "WeMix Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "0xc69a550470bEbC5c3Be98A4C3dD26C6AdD90C64b",
          "commit_store": "0x49FeF2978569E8061a7CA5cC676d46970613e9D0",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Blast Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "0x21159ebdA3E6A2437bCD6ef39853042ACC436D2D",
          "commit_store": "0x018Bb120265672C699969a9e2193755d4CF1ca16",
          "receiver_dapp": "******************************************"
        },
        "WeMix Mainnet": {
          "off_ramp": "0x512CA54a0F6447AC41c07Da3336DFcA042D88A7B",
          "commit_store": "0xae79C737801b04ECA277d50FDeaC4006C3725F62",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Base Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "0x91cB19E7c4Ba9B08CF544cDc9143042150B007C3",
      "router": "0x881e3A65B4d4a04dD529061dd0071cf975F58bCD",
      "price_registry": "0x6337a58D4BD7Ba691B66341779e8f87d4679923a",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Blast Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "0x61C3f6d72c80A3D1790b213c4cB58c3d4aaFccDF",
          "commit_store": "0x700C6715734111a6D1Cf414F46D85627b298B5dd",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Blast Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "0x74d574D11977fC8D40f8590C419504cbE178ADB7",
          "commit_store": "0x565f70396Ff82C23d25Dd3E57A9A66367dccdF3B",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Blast Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "0x96aFe20249C4f6f55d2fe0E792138f6a4dC566A4",
      "router": "0x12e0B8E349C6fb7E6E40713E8125C3cF1127ea8C",
      "price_registry": "0x4f66d9e65af0d3DC27897E29f571f933291bb07c",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Celo": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Ethereum Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Blast Mainnet": {
          "on_ramp": "0x6751cA96b769129dFE6eB8E349c310deCEDb4e36",
          "deployed_at": 0
        },
        "Celo": {
          "on_ramp": "0x741599d9a5a1bfC40A22f530fbCd85E2718e9F90",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "0xf50B9A46C394bD98491ce163d420222d8030F6F0",
          "deployed_at": 0
        },
        "Metis Andromeda": {
          "on_ramp": "0x75d536eED32f4c8Bb39F4B0c992163f5BA49B84e",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "0xeA6d4a24B262aB3e61a8A62f018A30beCD086f82",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "0x3455D8E039736944e66e19eAc77a42e8077B07bf",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "0x15a9D79d6b3485F70bF82bC49dDD1fcB37A7149c",
          "deployed_at": 0
        },
        "WeMix Mainnet": {
          "on_ramp": "0xdEFeADd30D5BFD403d86245b43e39a73d76423cC",
          "deployed_at": 0
        },
        "ZKSync Mainnet": {
          "on_ramp": "0x9B14AE850653dD0E30fBC93ab7f77D0d638a365B",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "0xdf615eF8D4C64d0ED8Fd7824BBEd2f6a10245aC9",
          "commit_store": "0xf7B343A17445F175f2Dd9f5CB29BAf0a8dE75ed3",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "0xd98E80C79a15E4dbaF4C40B6cCDF690fe619BFBb",
          "commit_store": "0xA9f9bF2b643348c0884f2eBA4F712E833DA9a2b8",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "0x66d84fedED0e51aeB47ceD1BB2fc0221Ae8D7C12",
          "commit_store": "0x9B9Ec8E26955c034828bBD78E22ab258d983dCdb",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "0x6B4B6359Dd5B47Cdb030E5921456D2a0625a9EbD",
          "commit_store": "0xDaC3A82Cc5e7C137bF28e6EF4F68f29D66205ffe",
          "receiver_dapp": "******************************************"
        },
        "Blast Mainnet": {
          "off_ramp": "0xF4468E56179e6EF59d6f5B133D9355AAD91Ea9ae",
          "commit_store": "0x52275dC17f9eD92230C8C4d57fD36d128701f694",
          "receiver_dapp": "******************************************"
        },
        "Celo": {
          "off_ramp": "0x794aE32b63b8a82a6e2Ec5017bbC6bfbddA5ce96",
          "commit_store": "0x95deB0c4bB9168202d50E874865f9A1842b82D64",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "0x70C705ff3eCAA04c8c61d581a59a168a1c49c2ec",
          "commit_store": "0x9D93D536Ced80871Bf3DA5Bb47bAedE62c794f8A",
          "receiver_dapp": "******************************************"
        },
        "Metis Andromeda": {
          "off_ramp": "0x330349112e13232131Da51f9f3b153d825f65e61",
          "commit_store": "0x0f89C7c0586536B618e0469402e1c8234bc52959",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "0xb57D52F7Cb7BBD19a117585bbaf712108E56dd8f",
          "commit_store": "0x01346721418045A6c07b71052e452eF8615e9084",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "0x562a2025E60AA19Aa03Ea41D70ea1FD3286d1D3B",
          "commit_store": "0x83F3DA5aa2C7534d694B0acde7624573c830250D",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "0x718672076D6d51E4c76142B37bC99E4945d704a3",
          "commit_store": "0x57b548C9c213EA2bcf60193E3D7fd2d2b53Fb9b3",
          "receiver_dapp": "******************************************"
        },
        "WeMix Mainnet": {
          "off_ramp": "0xc1EcCE580B2C96f4fd202fB7c2a259ECe19a1bF2",
          "commit_store": "0xA4755Cd68CA2092447c8c842659a2931f9110320",
          "receiver_dapp": "******************************************"
        },
        "ZKSync Mainnet": {
          "off_ramp": "0x6868FefbEFDc2B2FB75E6ED216dB1BeC02563D69",
          "commit_store": "0x0d26BaE784c8986502E072F4e73B6168e2052045",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Gnosis Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "0xe91D153E0b41518A2Ce8Dd3D7944Fa863463a97d",
      "arm": "0x2ab5ff904CFFdD37f19cC34597cF425916F2DAcA",
      "router": "0x4aAD6071085df840abD9Baf1697d5D5992bDadce",
      "price_registry": "0xec00a50EFb62F5f686E0FdEFDD6e10744dc53cAD",
      "wrapped_native": "0xe91D153E0b41518A2Ce8Dd3D7944Fa863463a97d",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Kroma Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "******************************************",
      "router": "0xE93E8B0d1b1CEB44350C8758ed1E2799CCee31aB",
      "price_registry": "0x8155B4710e7bbC90924E957104F94Afd4f95Eca2",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "WeMix Mainnet": {
          "on_ramp": "0xf3baf38136b55F656CC5fdd4417EB6C160877104",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "WeMix Mainnet": {
          "off_ramp": "0xcB154Bb4ed63FC30C416Dd16A2d1C64D8DE8DfD5",
          "commit_store": "0x6091D319080D06e1b656F83Ff001DAc124C55613",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Metis Andromeda": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Mode Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Optimism Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "WeMix Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "0xF8E38B4503418659F791F2135c4912F85BFB7988",
          "commit_store": "0x23CAc55aDDF28179A999858720E9Fe686372083A",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "WeMix Mainnet": {
          "off_ramp": "0x2f40dCCb74d8B2dd7af065232a06778f2D019375",
          "commit_store": "0xd1111a601BAb64a6428426095206A43710CaE932",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Polygon Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270",
      "arm": "0x569a295a09634Ac9414c3efe4E8931986d68F937",
      "router": "0x849c5ED5a80F5B408Dd4969b78c2C8fdf0565Bfe",
      "price_registry": "0x30D873664Ba766C983984C7AF9A921ccE36D34e1",
      "wrapped_native": "0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "WeMix Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "WeMix Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "WeMix Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "0xdBa0afF04bf63Ba6b75DEC94d1A934a367CAA782",
      "router": "0x7798b795Fde864f4Cd1b124a38Ba9619B7F8A442",
      "price_registry": "0x252863688762aD86868D3d3076233Eacd80c7055",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Kroma Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Kroma Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "ZKSync Mainnet": {
      "is_native_fee_token": true,
      "fee_token": "******************************************",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    }
  }
}
"""

[CCIP.Env]
TTL = '8h'

[CCIP.Env.Network]
selected_networks = [
    'ARBITRUM_MAINNET',
    'AVALANCHE_MAINNET',
    'BASE_MAINNET',
    'BLAST_MAINNET',
    'BSC_MAINNET',
    'CELO_MAINNET',
    'ETHEREUM_MAINNET',
    'GNOSIS_MAINNET',
    'KROMA_MAINNET',
    'METIS_ANDROMEDA',
    'MODE_MAINNET',
    'OPTIMISM_MAINNET',
    'POLYGON_MAINNET',
    'WEMIX_MAINNET',
    'ZKSYNC_MAINNET',
]

[CCIP.Groups.load]
NetworkPairs = [
    'ARBITRUM_MAINNET,BSC_MAINNET',
    'ARBITRUM_MAINNET,OPTIMISM_MAINNET',
    'AVALANCHE_MAINNET,ARBITRUM_MAINNET',
    'AVALANCHE_MAINNET,BASE_MAINNET',
    'AVALANCHE_MAINNET,BSC_MAINNET',
    'AVALANCHE_MAINNET,OPTIMISM_MAINNET',
    'AVALANCHE_MAINNET,POLYGON_MAINNET',
    'AVALANCHE_MAINNET,WEMIX_MAINNET',
    'BASE_MAINNET,ARBITRUM_MAINNET',
    'BASE_MAINNET,BSC_MAINNET',
    'BASE_MAINNET,OPTIMISM_MAINNET',
    'BASE_MAINNET,POLYGON_MAINNET',
    'BLAST_MAINNET,ARBITRUM_MAINNET',
    'BLAST_MAINNET,BASE_MAINNET',
    'BLAST_MAINNET,BSC_MAINNET',
    'BSC_MAINNET,OPTIMISM_MAINNET',
    'BSC_MAINNET,POLYGON_MAINNET',
    'BSC_MAINNET,WEMIX_MAINNET',
    'ETHEREUM_MAINNET,ARBITRUM_MAINNET',
    'ETHEREUM_MAINNET,AVALANCHE_MAINNET',
    'ETHEREUM_MAINNET,BASE_MAINNET',
    'ETHEREUM_MAINNET,BLAST_MAINNET',
    'ETHEREUM_MAINNET,BSC_MAINNET',
    'ETHEREUM_MAINNET,CELO_MAINNET',
    'ETHEREUM_MAINNET,GNOSIS_MAINNET',
    'ETHEREUM_MAINNET,METIS_ANDROMEDA',
    'ETHEREUM_MAINNET,MODE_MAINNET',
    'ETHEREUM_MAINNET,OPTIMISM_MAINNET',
    'ETHEREUM_MAINNET,POLYGON_MAINNET',
    'ETHEREUM_MAINNET,WEMIX_MAINNET',
    'ETHEREUM_MAINNET,ZKSYNC_MAINNET',
    'GNOSIS_MAINNET,ARBITRUM_MAINNET',
    'GNOSIS_MAINNET,AVALANCHE_MAINNET',
    'GNOSIS_MAINNET,BASE_MAINNET',
    'GNOSIS_MAINNET,BSC_MAINNET',
    'GNOSIS_MAINNET,OPTIMISM_MAINNET',
    'GNOSIS_MAINNET,POLYGON_MAINNET',
    'METIS_ANDROMEDA,ARBITRUM_MAINNET',
    'MODE_MAINNET,ARBITRUM_MAINNET',
    'MODE_MAINNET,BASE_MAINNET',
    'MODE_MAINNET,BSC_MAINNET',
    'MODE_MAINNET,OPTIMISM_MAINNET',
    'OPTIMISM_MAINNET,POLYGON_MAINNET',
    'OPTIMISM_MAINNET,WEMIX_MAINNET',
    'POLYGON_MAINNET,ARBITRUM_MAINNET',
    'POLYGON_MAINNET,WEMIX_MAINNET',
    'WEMIX_MAINNET,ARBITRUM_MAINNET',
    'WEMIX_MAINNET,KROMA_MAINNET',
    'ZKSYNC_MAINNET,ARBITRUM_MAINNET'
]

BiDirectionalLane = true
PhaseTimeout = '20m'
ExistingDeployment = true
SkipRequestIfAnotherRequestTriggeredWithin = '40m'

[CCIP.Groups.load.TokenConfig]
NoOfTokensPerChain = 1

[CCIP.Groups.load.LoadProfile]
RequestPerUnitTime = [1]
TimeUnit = '1h'
TestDuration = '5h'
TestRunName = 'Soak_test_mainnet'
FailOnFirstErrorInLoad = true


[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'Data'
DestGasLimit = 0
DataLength = 100
NoOfTokens = 1
AmountPerToken = 1

[CCIP.Groups.smoke]
# these are all the valid network pairs
NetworkPairs = [
    'ARBITRUM_MAINNET,BSC_MAINNET',
    'ARBITRUM_MAINNET,OPTIMISM_MAINNET',
    'AVALANCHE_MAINNET,ARBITRUM_MAINNET',
    'AVALANCHE_MAINNET,BASE_MAINNET',
    'AVALANCHE_MAINNET,BSC_MAINNET',
    'AVALANCHE_MAINNET,OPTIMISM_MAINNET',
    'AVALANCHE_MAINNET,POLYGON_MAINNET',
    'AVALANCHE_MAINNET,WEMIX_MAINNET',
    'BASE_MAINNET,ARBITRUM_MAINNET',
    'BASE_MAINNET,BSC_MAINNET',
    'BASE_MAINNET,OPTIMISM_MAINNET',
    'BASE_MAINNET,POLYGON_MAINNET',
    'BLAST_MAINNET,ARBITRUM_MAINNET',
    'BLAST_MAINNET,BASE_MAINNET',
    'BLAST_MAINNET,BSC_MAINNET',
    'BSC_MAINNET,OPTIMISM_MAINNET',
    'BSC_MAINNET,POLYGON_MAINNET',
    'BSC_MAINNET,WEMIX_MAINNET',
    'ETHEREUM_MAINNET,ARBITRUM_MAINNET',
    'ETHEREUM_MAINNET,AVALANCHE_MAINNET',
    'ETHEREUM_MAINNET,BASE_MAINNET',
    'ETHEREUM_MAINNET,BLAST_MAINNET',
    'ETHEREUM_MAINNET,BSC_MAINNET',
    'ETHEREUM_MAINNET,CELO_MAINNET',
    'ETHEREUM_MAINNET,GNOSIS_MAINNET',
    'ETHEREUM_MAINNET,METIS_ANDROMEDA',
    'ETHEREUM_MAINNET,MODE_MAINNET',
    'ETHEREUM_MAINNET,OPTIMISM_MAINNET',
    'ETHEREUM_MAINNET,POLYGON_MAINNET',
    'ETHEREUM_MAINNET,WEMIX_MAINNET',
    'ETHEREUM_MAINNET,ZKSYNC_MAINNET',
    'GNOSIS_MAINNET,ARBITRUM_MAINNET',
    'GNOSIS_MAINNET,AVALANCHE_MAINNET',
    'GNOSIS_MAINNET,BASE_MAINNET',
    'GNOSIS_MAINNET,BSC_MAINNET',
    'GNOSIS_MAINNET,OPTIMISM_MAINNET',
    'GNOSIS_MAINNET,POLYGON_MAINNET',
    'METIS_ANDROMEDA,ARBITRUM_MAINNET',
    'MODE_MAINNET,ARBITRUM_MAINNET',
    'MODE_MAINNET,BASE_MAINNET',
    'MODE_MAINNET,BSC_MAINNET',
    'MODE_MAINNET,OPTIMISM_MAINNET',
    'OPTIMISM_MAINNET,POLYGON_MAINNET',
    'OPTIMISM_MAINNET,WEMIX_MAINNET',
    'POLYGON_MAINNET,ARBITRUM_MAINNET',
    'POLYGON_MAINNET,WEMIX_MAINNET',
    'WEMIX_MAINNET,ARBITRUM_MAINNET',
    'WEMIX_MAINNET,KROMA_MAINNET',
    'ZKSYNC_MAINNET,ARBITRUM_MAINNET'
]

BiDirectionalLane = true
PhaseTimeout = '20m'
LocalCluster = false
ExistingDeployment = true
ReuseContracts = true
SkipRequestIfAnotherRequestTriggeredWithin = '24h'

[CCIP.Groups.smoke.TokenConfig]
NoOfTokensPerChain = 1
CCIPOwnerTokens = true

[CCIP.Groups.smoke.MsgDetails]
MsgType = 'Data'
DestGasLimit = 0
DataLength = 100
NoOfTokens = 1
AmountPerToken = 1