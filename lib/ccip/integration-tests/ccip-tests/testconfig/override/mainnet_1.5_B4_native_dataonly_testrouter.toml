[CCIP]
[CCIP.ContractVersions]
PriceRegistry = 'latest'
OffRamp = 'latest'
OnRamp = 'latest'
TokenPool = 'latest'
CommitStore = 'latest'

[CCIP.Deployments]
Data = """
{
  "lane_configs": {
    "Arbitrum Mainnet": {
      "is_mock_arm": true,
      "fee_token": "0xf97f4df75117a78c1A5a0DBb814Af92458539FB4",
      "arm": "0xC2C5E22a2d9715ed5C5BCC4D8eFf5966cf260744",
      "router": "0x33340200b7893fc478Eb2558FfC7B100E5B3869c",
      "price_registry": "0x3971cfEf12c4CC6eD14D65B39C9EC6C740C19A40",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Metis Andromeda": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Avalanche Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Metis Andromeda": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "0x27a971D482335d0f8d1917451390734f7372A4a3",
          "commit_store": "0x6642E640321e1Ad01eef2fC2ad5427D84A2Ee269",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "0xcabc2D71dC3172a154A5A34cD706B050e0ef9b6f",
          "commit_store": "0x78B15A57889200F246fc52790c4F3DfC37d82Aa2",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Avalanche Mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "arm": "0x4f6Ec25f06A114ADD3154DC17fb637F750AdaA31",
      "router": "0xC485fDa586037F8a312C2492419C9ce25cF7FDD8",
      "price_registry": "0x718b6f7454531F6CBdB9eC08F87C8663A7c4FAC2",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Base Mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "bridge_tokens": [
        "0x88Fb150BDc53A65fe94Dea0c9BA0a6dAf8C6e196"
      ],
      "bridge_tokens_pools": [
        "0x1568A4131760231712E59778DAB9EFE67911f4ff"
      ],
      "arm": "0x91cB19E7c4Ba9B08CF544cDc9143042150B007C3",
      "router": "0xcd06f191359cfA6DB55F7D38134C9f89a2D5Ba56",
      "price_registry": "0xC792246cF4f41100CA3c67cbF3888D5Cf8FED50B",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "0x74d574D11977fC8D40f8590C419504cbE178ADB7",
          "commit_store": "0x565f70396Ff82C23d25Dd3E57A9A66367dccdF3B",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "BSC Mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "arm": "0x56491A98199aD2e687Ea9D0cFB7b4AC57B4980Fc",
      "router": "0x641Fb431CD2dA50fF40e7E0272d2B1e58c1ff236",
      "price_registry": "0x2A92BCecd6e702702864E134821FD2DE73C3e180",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "0x21159ebdA3E6A2437bCD6ef39853042ACC436D2D",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Blast Mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "",
      "src_contracts": {
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Celo": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "bridge_tokens": [
        "******************************************"
      ],
      "bridge_tokens_pools": [
        "******************************************"
      ],
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Ethereum Mainnet": {
      "is_mock_arm": true,
      "fee_token": "******************************************",
      "bridge_tokens": [
        "******************************************"
      ],
      "bridge_tokens_pools": [
        "******************************************"
      ],
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "0x948306C220Ac325fa9392A6E601042A3CD0b480d",
          "deployed_at": 0
        },
        "Blast Mainnet": {
          "on_ramp": "0x4545F9a17DA50110632C14704a15d893BF9CBD27",
          "deployed_at": 0
        },
        "Celo": {
          "on_ramp": "0xEd5bE9508ae56531cc0EDe6A3bD588Eb9E2e3cfa",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "0xf50B9A46C394bD98491ce163d420222d8030F6F0",
          "deployed_at": 0
        },
        "linea-mainnet": {
          "on_ramp": "0x626189C882A80fF0D036d8D9f6447555e81F78E9",
          "deployed_at": 0
        },
        "Metis Andromeda": {
          "on_ramp": "0x75d536eED32f4c8Bb39F4B0c992163f5BA49B84e",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "0xeA6d4a24B262aB3e61a8A62f018A30beCD086f82",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "0x3455D8E039736944e66e19eAc77a42e8077B07bf",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "0x15a9D79d6b3485F70bF82bC49dDD1fcB37A7149c",
          "deployed_at": 0
        },
        "scroll-mainnet": {
          "on_ramp": "0x362A221C3cfd7F992DFE221687323F0BA9BA8187",
          "deployed_at": 0
        },
        "wemix-mainnet": {
          "on_ramp": "0xCbE7e5DA76dC99Ac317adF6d99137005FDA4E2C4",
          "deployed_at": 0
        },
        "zksync-mainnet": {
          "on_ramp": "0xD54C93A99CBCb8D865E13DA321B540171795A89f",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "0xdf615eF8D4C64d0ED8Fd7824BBEd2f6a10245aC9",
          "commit_store": "0xf7B343A17445F175f2Dd9f5CB29BAf0a8dE75ed3",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "0xd98E80C79a15E4dbaF4C40B6cCDF690fe619BFBb",
          "commit_store": "0xA9f9bF2b643348c0884f2eBA4F712E833DA9a2b8",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "0x6B4B6359Dd5B47Cdb030E5921456D2a0625a9EbD",
          "commit_store": "0xDaC3A82Cc5e7C137bF28e6EF4F68f29D66205ffe",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "0x66d84fedED0e51aeB47ceD1BB2fc0221Ae8D7C12",
          "commit_store": "0x9B9Ec8E26955c034828bBD78E22ab258d983dCdb",
          "receiver_dapp": "******************************************"
        },
        "Blast Mainnet": {
          "off_ramp": "0x4e0092bBC8EfAb6Eca295caB66986193b90a1Bb9",
          "commit_store": "0xd7cA96B58EE33FdB3aa1392c30eD02645b1F28e2",
          "receiver_dapp": "******************************************"
        },
        "Celo": {
          "off_ramp": "0x90902C0AEE857F3A42f2beBEa38724cE7b7a0cff",
          "commit_store": "0x25adA90B241143DD5Df04Fb06C1fF6E7f7624ad9",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "0x70C705ff3eCAA04c8c61d581a59a168a1c49c2ec",
          "commit_store": "0x9D93D536Ced80871Bf3DA5Bb47bAedE62c794f8A",
          "receiver_dapp": "******************************************"
        },
        "linea-mainnet": {
          "off_ramp": "0x418dcbCf229897d0CCf1B8B464Db06C23879FBB4",
          "commit_store": "0x9f592c28590595F3F78a8881E8Dbb9984ed705cD",
          "receiver_dapp": "******************************************"
        },
        "Metis Andromeda": {
          "off_ramp": "0x330349112e13232131Da51f9f3b153d825f65e61",
          "commit_store": "0x0f89C7c0586536B618e0469402e1c8234bc52959",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "0xb57D52F7Cb7BBD19a117585bbaf712108E56dd8f",
          "commit_store": "0x01346721418045A6c07b71052e452eF8615e9084",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "0x562a2025E60AA19Aa03Ea41D70ea1FD3286d1D3B",
          "commit_store": "0x83F3DA5aa2C7534d694B0acde7624573c830250D",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "0x718672076D6d51E4c76142B37bC99E4945d704a3",
          "commit_store": "0x57b548C9c213EA2bcf60193E3D7fd2d2b53Fb9b3",
          "receiver_dapp": "******************************************"
        },
        "scroll-mainnet": {
          "off_ramp": "0x26a10137A54F4Ea01D20758Ac5AdBf9326340Fc3",
          "commit_store": "0x57d6cD9CD44770C807b2763Dbe4CFDA0113dd114",
          "receiver_dapp": "******************************************"
        },
        "wemix-mainnet": {
          "off_ramp": "0xF92Fa796F5307b029c65CA26f322a6D86f211194",
          "commit_store": "0xbeC110FF43D52be2066B06525304A9924E16b73b",
          "receiver_dapp": "******************************************"
        },
        "zksync-mainnet": {
          "off_ramp": "0x7c887B97F9Bba9355EC10e2bA949AdB491Ef44Fd",
          "commit_store": "0xA42bf0c8794FA8853Ec0F1B24a489972e8CF4C30",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Gnosis Mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "0x0000000000000000000000000000000000000000",
      "arm": "0x2ab5ff904CFFdD37f19cC34597cF425916F2DAcA",
      "router": "0xe6A934D3754797bCe4375368F5f12b94DBc19Fcc",
      "price_registry": "0x03aF5C79b0D49C040413FdA1e7B2cAa54a0fa5F4",
      "wrapped_native": "0xe91D153E0b41518A2Ce8Dd3D7944Fa863463a97d",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "kroma-mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "bridge_tokens": [
        "******************************************"
      ],
      "bridge_tokens_pools": [
        "0xd9547B702673c61c84bb2dF6FDf6D2F5309fE536"
      ],
      "arm": "0xB59779d3364BC6d71168245f9ebb96469E5a5a98",
      "router": "0xE93E8B0d1b1CEB44350C8758ed1E2799CCee31aB",
      "price_registry": "0x8155B4710e7bbC90924E957104F94Afd4f95Eca2",
      "wrapped_native": "",
      "src_contracts": {
        "wemix-mainnet": {
          "on_ramp": "0x3C5Ab46fA1dB1dECD854224654313a69bf9fcAD3",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "wemix-mainnet": {
          "off_ramp": "0xF886d8DC64E544af4835cbf91e5678A54D95B80e",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "linea-mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "",
      "src_contracts": {
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "scroll-mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "scroll-mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Metis Andromeda": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Mode Mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Optimism Mainnet": {
      "is_mock_arm": true,
      "fee_token": "******************************************",
      "bridge_tokens": [
        "******************************************"
      ],
      "bridge_tokens_pools": [
        "******************************************"
      ],
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Mode Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Polygon Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Mode Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Polygon Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "Polygon Mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "bridge_tokens": [
        "0xb0897686c545045aFc77CF20eC7A532E3120E0F1"
      ],
      "bridge_tokens_pools": [
        "0x086892015567fb8764d02c6845C85C25C8FcA389"
      ],
      "arm": "0x569a295a09634Ac9414c3efe4E8931986d68F937",
      "router": "0x10ea937A855268E5336F78B262B4d82ad1Cb84BC",
      "price_registry": "0xd6D571B37B26Ee1b99FDFa097034Ea4B9E3b76BA",
      "wrapped_native": "0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270",
      "src_contracts": {
        "Arbitrum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Avalanche Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Base Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "BSC Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Gnosis Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "Optimism Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Arbitrum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Avalanche Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Base Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "BSC Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Gnosis Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "Optimism Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "scroll-mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "",
      "src_contracts": {
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "linea-mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "linea-mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "wemix-mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "bridge_tokens": [
        "******************************************"
      ],
      "bridge_tokens_pools": [
        "******************************************"
      ],
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "******************************************",
      "src_contracts": {
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        },
        "kroma-mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        },
        "kroma-mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    },
    "zksync-mainnet": {
      "is_native_fee_token": true,
      "is_mock_arm": true,
      "fee_token": "",
      "arm": "******************************************",
      "router": "******************************************",
      "price_registry": "******************************************",
      "wrapped_native": "",
      "src_contracts": {
        "Ethereum Mainnet": {
          "on_ramp": "******************************************",
          "deployed_at": 0
        }
      },
      "dest_contracts": {
        "Ethereum Mainnet": {
          "off_ramp": "******************************************",
          "commit_store": "******************************************",
          "receiver_dapp": "******************************************"
        }
      }
    }
  }
}
"""

[CCIP.Env]
TTL = '8h'

[CCIP.Env.Network]
selected_networks = [
    'ARBITRUM_MAINNET',
    'AVALANCHE_MAINNET',
    'BASE_MAINNET',
#    'BLAST_MAINNET',
    'BSC_MAINNET',
#    'CELO_MAINNET',
    'ETHEREUM_MAINNET',
    'GNOSIS_MAINNET',
#    'KROMA_MAINNET',
#    'METIS_ANDROMEDA',
    'MODE_MAINNET',
    'OPTIMISM_MAINNET',
    'POLYGON_MAINNET',
#    'WEMIX_MAINNET',
#    'ZKSYNC_MAINNET',
]


[CCIP.Groups.smoke]
# these are all the valid network pairs
NetworkPairs = [
#    'WEMIX_MAINNET,ARBITRUM_MAINNET',
#    'WEMIX_MAINNET,AVALANCHE_MAINNET',
#    'WEMIX_MAINNET,BSC_MAINNET',
#    'WEMIX_MAINNET,OPTIMISM_MAINNET',
#    'WEMIX_MAINNET,ETHEREUM_MAINNET',
#    'WEMIX_MAINNET,POLYGON_MAINNET',
#    'WEMIX_MAINNET,KROMA_MAINNET',
#    'ZKSYNC_MAINNET,ETHEREUM_MAINNET',
#    'CELO_MAINNET,ETHEREUM_MAINNET',

    'ARBITRUM_MAINNET,BSC_MAINNET',
    'ARBITRUM_MAINNET,OPTIMISM_MAINNET',
    'AVALANCHE_MAINNET,ARBITRUM_MAINNET',
    'AVALANCHE_MAINNET,BASE_MAINNET',
    'AVALANCHE_MAINNET,BSC_MAINNET',
    'AVALANCHE_MAINNET,OPTIMISM_MAINNET',
    'AVALANCHE_MAINNET,POLYGON_MAINNET',
#    'AVALANCHE_MAINNET,WEMIX_MAINNET',
    'BASE_MAINNET,ARBITRUM_MAINNET',
    'BASE_MAINNET,BSC_MAINNET',
    'BASE_MAINNET,OPTIMISM_MAINNET',
    'BASE_MAINNET,POLYGON_MAINNET',
#    'BLAST_MAINNET,ARBITRUM_MAINNET',
#    'BLAST_MAINNET,BASE_MAINNET',
#    'BLAST_MAINNET,BSC_MAINNET',
    'BSC_MAINNET,OPTIMISM_MAINNET',
    'BSC_MAINNET,POLYGON_MAINNET',
#    'BSC_MAINNET,WEMIX_MAINNET',
    'ETHEREUM_MAINNET,ARBITRUM_MAINNET',
    'ETHEREUM_MAINNET,AVALANCHE_MAINNET',
    'ETHEREUM_MAINNET,BASE_MAINNET',
#    'ETHEREUM_MAINNET,BLAST_MAINNET',
    'ETHEREUM_MAINNET,BSC_MAINNET',
#    'ETHEREUM_MAINNET,CELO_MAINNET',
    'ETHEREUM_MAINNET,GNOSIS_MAINNET',
#    'ETHEREUM_MAINNET,METIS_ANDROMEDA',
    'ETHEREUM_MAINNET,MODE_MAINNET',
    'ETHEREUM_MAINNET,OPTIMISM_MAINNET',
    'ETHEREUM_MAINNET,POLYGON_MAINNET',
#    'ETHEREUM_MAINNET,WEMIX_MAINNET',
#    'ETHEREUM_MAINNET,ZKSYNC_MAINNET',
    'GNOSIS_MAINNET,ARBITRUM_MAINNET',
    'GNOSIS_MAINNET,AVALANCHE_MAINNET',
    'GNOSIS_MAINNET,BASE_MAINNET',
    'GNOSIS_MAINNET,BSC_MAINNET',
    'GNOSIS_MAINNET,OPTIMISM_MAINNET',
    'GNOSIS_MAINNET,POLYGON_MAINNET',
#    'METIS_ANDROMEDA,ARBITRUM_MAINNET',
    'MODE_MAINNET,ARBITRUM_MAINNET',
    'MODE_MAINNET,BASE_MAINNET',
    'MODE_MAINNET,BSC_MAINNET',
    'MODE_MAINNET,OPTIMISM_MAINNET',
    'OPTIMISM_MAINNET,POLYGON_MAINNET',
#    'OPTIMISM_MAINNET,WEMIX_MAINNET',
    'POLYGON_MAINNET,ARBITRUM_MAINNET',
#    'POLYGON_MAINNET,WEMIX_MAINNET',
#    'WEMIX_MAINNET,ARBITRUM_MAINNET',
#    'WEMIX_MAINNET,KROMA_MAINNET',
#    'ZKSYNC_MAINNET,ARBITRUM_MAINNET'
]

BiDirectionalLane = true
PhaseTimeout = '20m'
LocalCluster = false
ExistingDeployment = true
ReuseContracts = true
SkipRequestIfAnotherRequestTriggeredWithin = '24h'

[CCIP.Groups.smoke.TokenConfig]
NoOfTokensPerChain = 1
CCIPOwnerTokens = true

[CCIP.Groups.smoke.MsgDetails]
MsgType = 'Data'
DestGasLimit = 0
DataLength = 100
NoOfTokens = 1
AmountPerToken = 1

[CCIP.Groups.load]
NetworkPairs = [
    'ARBITRUM_MAINNET,BSC_MAINNET',
    'ARBITRUM_MAINNET,OPTIMISM_MAINNET',
    'AVALANCHE_MAINNET,ARBITRUM_MAINNET',
    'AVALANCHE_MAINNET,BASE_MAINNET',
    'AVALANCHE_MAINNET,BSC_MAINNET',
    'AVALANCHE_MAINNET,OPTIMISM_MAINNET',
    'AVALANCHE_MAINNET,POLYGON_MAINNET',
    'BASE_MAINNET,ARBITRUM_MAINNET',
    'BASE_MAINNET,BSC_MAINNET',
    'BASE_MAINNET,OPTIMISM_MAINNET',
    'BASE_MAINNET,POLYGON_MAINNET',
    'BSC_MAINNET,OPTIMISM_MAINNET',
    'BSC_MAINNET,POLYGON_MAINNET',
    'ETHEREUM_MAINNET,ARBITRUM_MAINNET',
    'ETHEREUM_MAINNET,AVALANCHE_MAINNET',
    'ETHEREUM_MAINNET,BASE_MAINNET',
    'ETHEREUM_MAINNET,BSC_MAINNET',
    'ETHEREUM_MAINNET,GNOSIS_MAINNET',
    'ETHEREUM_MAINNET,MODE_MAINNET',
    'ETHEREUM_MAINNET,OPTIMISM_MAINNET',
    'ETHEREUM_MAINNET,POLYGON_MAINNET',
    'GNOSIS_MAINNET,ARBITRUM_MAINNET',
    'GNOSIS_MAINNET,AVALANCHE_MAINNET',
    'GNOSIS_MAINNET,BASE_MAINNET',
    'GNOSIS_MAINNET,BSC_MAINNET',
    'GNOSIS_MAINNET,OPTIMISM_MAINNET',
    'GNOSIS_MAINNET,POLYGON_MAINNET',
    'MODE_MAINNET,ARBITRUM_MAINNET',
    'MODE_MAINNET,BASE_MAINNET',
    'MODE_MAINNET,BSC_MAINNET',
    'MODE_MAINNET,OPTIMISM_MAINNET',
    'OPTIMISM_MAINNET,POLYGON_MAINNET',
    'POLYGON_MAINNET,ARBITRUM_MAINNET',
]

BiDirectionalLane = true
PhaseTimeout = '50m'
LocalCluster = false
ExistingDeployment = true
ReuseContracts = true

[CCIP.Groups.load.TokenConfig]
NoOfTokensPerChain = 1

[CCIP.Groups.load.LoadProfile]
RequestPerUnitTime = [1]
TimeUnit = '30m'
TestDuration = '2h'
TestRunName = 'SoakTest_mainnet_test_router'
FailOnFirstErrorInLoad = false

[[CCIP.Groups.load.LoadProfile.MsgProfile.MsgDetails]]
MsgType = 'Data'
DestGasLimit = 0
DataLength = 100
NoOfTokens = 1
AmountPerToken = 1