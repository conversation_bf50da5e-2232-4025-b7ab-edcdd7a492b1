{"lane_configs": {"Arbitrum Mainnet": {"is_native_fee_token": true, "fee_token": "0xf97f4df75117a78c1A5a0DBb814Af92458539FB4", "bridge_tokens": [], "bridge_tokens_pools": [], "arm": "0xe06b0e8c4bd455153e8794ad7Ea8Ff5A14B64E4b", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Avalanche Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Base Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "BSC Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Optimism Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Polygon Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "WeMix Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}}, "dest_contracts": {"Avalanche Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Base Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "BSC Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Optimism Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Polygon Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "WeMix Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "Avalanche Mainnet": {"is_native_fee_token": true, "fee_token": "******************************************", "bridge_tokens": [], "bridge_tokens_pools": [], "arm": "0xdFD6C0dc67666DE3bB36b31eec5c7B1542A82C1E", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Arbitrum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Base Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "BSC Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Optimism Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Polygon Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "WeMix Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}}, "dest_contracts": {"Arbitrum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Base Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "BSC Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Optimism Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Polygon Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "WeMix Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "Base Mainnet": {"is_native_fee_token": true, "fee_token": "******************************************", "bridge_tokens": [], "bridge_tokens_pools": [], "arm": "0x38660c8CC222c0192b635c2ac09687B4F25cCE5F", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Arbitrum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Avalanche Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "BSC Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Optimism Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Polygon Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}}, "dest_contracts": {"Arbitrum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Avalanche Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "BSC Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Optimism Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Polygon Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "BSC Mainnet": {"is_native_fee_token": true, "fee_token": "******************************************", "bridge_tokens": [], "bridge_tokens_pools": [], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Avalanche Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Arbitrum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Base Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Optimism Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Polygon Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "WeMix Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}}, "dest_contracts": {"Avalanche Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Arbitrum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Base Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Optimism Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Polygon Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "WeMix Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "Ethereum Mainnet": {"is_native_fee_token": true, "fee_token": "******************************************", "bridge_tokens": [], "bridge_tokens_pools": [], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Arbitrum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Avalanche Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Base Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "BSC Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Optimism Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Polygon Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "WeMix Mainnet": {"on_ramp": "0xCbE7e5DA76dC99Ac317adF6d99137005FDA4E2C4", "deployed_at": 11111111}}, "dest_contracts": {"Arbitrum Mainnet": {"off_ramp": "0xeFC4a18af59398FF23bfe7325F2401aD44286F4d", "commit_store": "0x9B2EEd6A1e16cB50Ed4c876D2dD69468B21b7749", "receiver_dapp": "******************************************"}, "Avalanche Mainnet": {"off_ramp": "0x569940e02D4425eac61A7601632eC00d69f75c17", "commit_store": "0x2aa101BF99CaeF7fc1355D4c493a1fe187A007cE", "receiver_dapp": "******************************************"}, "Base Mainnet": {"off_ramp": "0xdf85c8381954694E74abD07488f452b4c2Cddfb3", "commit_store": "0x8DC27D621c41a32140e22E2a4dAf1259639BAe04", "receiver_dapp": "******************************************"}, "BSC Mainnet": {"off_ramp": "0x7Afe7088aff57173565F4b034167643AA8b9171c", "commit_store": "0x87c55D48DF6EF7B08153Ab079e76bFEcbb793D75", "receiver_dapp": "******************************************"}, "Optimism Mainnet": {"off_ramp": "0xB095900fB91db00E6abD247A5A5AD1cee3F20BF7", "commit_store": "0x4af4B497c998007eF83ad130318eB2b925a79dc8", "receiver_dapp": "******************************************"}, "Polygon Mainnet": {"off_ramp": "0x0af338F0E314c7551bcE0EF516d46d855b0Ee395", "commit_store": "0xD37a60E8C36E802D2E1a6321832Ee85556Beeb76", "receiver_dapp": "******************************************"}, "WeMix Mainnet": {"off_ramp": "0x3a129e6C18b23d18BA9E6Aa14Dc2e79d1f91c6c5", "commit_store": "0x31f6ab382DDeb9A316Ab61C3945a5292a50a89AB", "receiver_dapp": "******************************************"}}}, "Kroma Mainnet": {"is_native_fee_token": true, "fee_token": "0xC1F6f7622ad37C3f46cDF6F8AA0344ADE80BF450", "bridge_tokens": [], "bridge_tokens_pools": [], "arm": "0xB59779d3364BC6d71168245f9ebb96469E5a5a98", "router": "0xE93E8B0d1b1CEB44350C8758ed1E2799CCee31aB", "price_registry": "0x8155B4710e7bbC90924E957104F94Afd4f95Eca2", "wrapped_native": "0x4200000000000000000000000000000000000001", "src_contracts": {"WeMix Mainnet": {"on_ramp": "0x3C5Ab46fA1dB1dECD854224654313a69bf9fcAD3", "deployed_at": 11111111}}, "dest_contracts": {"WeMix Mainnet": {"off_ramp": "0x2B555774B3D1dcbcd76efb7751F3c5FbCFABC5C4", "commit_store": "0x213124614aAf31eBCE7c612A12aac5f8aAD77DE4", "receiver_dapp": "******************************************"}}}, "Optimism Mainnet": {"is_native_fee_token": true, "fee_token": "0x350a791Bfc2C21F9Ed5d10980Dad2e2638ffa7f6", "bridge_tokens": [], "bridge_tokens_pools": [], "arm": "0x8C7C2C3362a42308BB5c368677Ad321D11693b81", "router": "0x3206695CaE29952f4b0c22a169725a865bc8Ce0f", "price_registry": "0xb52545aECE8C73A97E52a146757EC15b90Ed8488", "wrapped_native": "******************************************", "src_contracts": {"Arbitrum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Avalanche Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Base Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "BSC Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Polygon Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "WeMix Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}}, "dest_contracts": {"Arbitrum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Avalanche Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Base Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "BSC Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Polygon Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "WeMix Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "Polygon Mainnet": {"is_native_fee_token": true, "fee_token": "******************************************", "bridge_tokens": [], "bridge_tokens_pools": [], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Arbitrum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Avalanche Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Base Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "BSC Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Optimism Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "WeMix Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}}, "dest_contracts": {"Arbitrum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Avalanche Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Base Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "BSC Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Optimism Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "WeMix Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "WeMix Mainnet": {"is_native_fee_token": true, "fee_token": "******************************************", "bridge_tokens": [], "bridge_tokens_pools": [], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "0x7D72b22a74A216Af4a002a1095C8C707d6eC1C5f", "src_contracts": {"Arbitrum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Avalanche Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "BSC Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Optimism Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Polygon Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}, "Kroma Mainnet": {"on_ramp": "******************************************", "deployed_at": 11111111}}, "dest_contracts": {"Arbitrum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Avalanche Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "BSC Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Optimism Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Polygon Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Kroma Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}}}