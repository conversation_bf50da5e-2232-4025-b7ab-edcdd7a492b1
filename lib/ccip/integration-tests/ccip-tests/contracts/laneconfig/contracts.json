{"lane_configs": {"Arbitrum Mainnet": {"fee_token": "******************************************", "bridge_tokens": ["******************************************"], "bridge_tokens_pools": [""], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 126471491}}, "dest_contracts": {"Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "Avalanche Mainnet": {"fee_token": "******************************************", "bridge_tokens": ["******************************************"], "bridge_tokens_pools": ["******************************************"], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 32263102}, "Polygon Mainnet": {"on_ramp": "******************************************", "deployed_at": 32562460}}, "dest_contracts": {"Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Polygon Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "Base Mainnet": {"fee_token": "******************************************", "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 3316617}}, "dest_contracts": {"Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "BSC Mainnet": {"fee_token": "******************************************", "bridge_tokens": ["******************************************"], "bridge_tokens_pools": [""], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 31312405}}, "dest_contracts": {"Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "Ethereum Mainnet": {"fee_token": "******************************************", "bridge_tokens": ["******************************************"], "bridge_tokens_pools": ["******************************************"], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Arbitrum Mainnet": {"on_ramp": "******************************************", "deployed_at": 18029393}, "Avalanche Mainnet": {"on_ramp": "******************************************", "deployed_at": 17636709}, "BSC Mainnet": {"on_ramp": "******************************************", "deployed_at": 18029385}, "Base Mainnet": {"on_ramp": "******************************************", "deployed_at": 18029431}, "Optimism Mainnet": {"on_ramp": "******************************************", "deployed_at": 17636647}, "Polygon Mainnet": {"on_ramp": "0x0f27c8532457b66D6037141DEB0ed479Dad04B3c", "deployed_at": 17636734}}, "dest_contracts": {"Arbitrum Mainnet": {"off_ramp": "0x61135E701a2214C170c5F596D0067798FEfbaaE4", "commit_store": "0x3d3467e1036Ee25F6F4aa15e3Abf77443A23144C", "receiver_dapp": "******************************************"}, "Avalanche Mainnet": {"off_ramp": "0x1C207dabc46902dF9028b27D6d301c3849b2D12c", "commit_store": "0x40c558575093eC1099CC21B020d9b8D13c74417F", "receiver_dapp": "******************************************"}, "BSC Mainnet": {"off_ramp": "0xC7176620daf49A39a17FF9A6C2DE1eAA6033EE94", "commit_store": "0x7986C9892389854cAAbAC785ff18123B0070a5Fd", "receiver_dapp": "******************************************"}, "Base Mainnet": {"off_ramp": "0xfF51C00546AA3d9051a4B96Ae81346E14709CD24", "commit_store": "0x2D1708ff2a15adbE313eA8C6035aA24d0FBA1c77", "receiver_dapp": "******************************************"}, "Optimism Mainnet": {"off_ramp": "0x41627a90f2c6238f2BADAB72D5aB050B857fdAb5", "commit_store": "0x8bEFCa744c6f2b567b1863dcF055C593afdC11A0", "receiver_dapp": "******************************************"}, "Polygon Mainnet": {"off_ramp": "0xBDd822f3bC2EAB6818CfA3053107831D4E93fE72", "commit_store": "0x20718EfbC25Dba60FD51c2c81362b83f7C411A6D", "receiver_dapp": "******************************************"}}}, "Optimism Mainnet": {"fee_token": "******************************************", "bridge_tokens": ["******************************************"], "bridge_tokens_pools": ["******************************************"], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 106535110}}, "dest_contracts": {"Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}, "Polygon Mainnet": {"fee_token": "******************************************", "bridge_tokens": ["******************************************"], "bridge_tokens_pools": ["******************************************"], "arm": "******************************************", "router": "******************************************", "price_registry": "******************************************", "wrapped_native": "******************************************", "src_contracts": {"Avalanche Mainnet": {"on_ramp": "******************************************", "deployed_at": 45041759}, "Ethereum Mainnet": {"on_ramp": "******************************************", "deployed_at": 44762064}}, "dest_contracts": {"Avalanche Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}, "Ethereum Mainnet": {"off_ramp": "******************************************", "commit_store": "******************************************", "receiver_dapp": "******************************************"}}}}}