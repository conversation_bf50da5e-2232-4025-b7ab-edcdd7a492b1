package vrfv2plus

const (
	ErrDeployCoordinatorV2Plus                     = "error deploying VRF CoordinatorV2Plus"
	ErrDeployBatchCoordinatorV2Plus                = "error deploying Batch VRF CoordinatorV2Plus"
	ErrCreatingVRFv2PlusKey                        = "error creating VRFv2Plus key"
	ErrAdvancedConsumer                            = "error deploying VRFv2Plus Advanced Consumer"
	ErrCreatingVRFv2PlusJob                        = "error creating VRFv2Plus job"
	ErrDeployVRFV2_5Contracts                      = "error deploying VRFV2_5 contracts"
	ErrAddConsumerToSub                            = "error adding consumer to VRF Subscription"
	ErrFundSubWithNativeToken                      = "error funding subscription with native token"
	ErrSetLinkNativeLinkFeed                       = "error setting Link and ETH/LINK feed for VRF Coordinator contract"
	ErrCreateVRFV2PlusJobs                         = "error creating VRF V2 Plus Jobs"
	ErrRequestRandomnessDirectFundingLinkPayment   = "error requesting randomness with direct funding and link payment"
	ErrRequestRandomnessDirectFundingNativePayment = "error requesting randomness with direct funding and native payment"
	ErrLinkTotalBalance                            = "error waiting for RandomWordsFulfilled event"
	ErrNativeTokenBalance                          = "error waiting for RandomWordsFulfilled event"
	ErrDeployWrapper                               = "error deploying VRFV2PlusWrapper"
	ErrSetL1FeeCalculation                         = "error setting L1 fee calculation"
)
