#!/usr/bin/env bash

# build test binaries
# accepts a single space separated argument of the folders to build

set -ex

# get this scripts directory
SCRIPT_DIR=$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" &>/dev/null && pwd)

cd "$SCRIPT_DIR"/../ || { echo "Error: Failed to change directory to $SCRIPT_DIR/../"; exit 1; }

helm repo update

# parse out quotes if they exist in the string
temp="${1%\"}"
tosplit="${temp#\"}"

# find the suite name
OIFS=$IFS
IFS=' '
for x in $tosplit
do
    if [ "$x" = "load" ]; then
      echo "Changing directory and executing go test -c ./... for 'load' package"
      pushd "./load" && go test -c -tags embed -o ../ ./...
      popd
    elif [ "$x" = "ccip-load" ]; then
      echo "Changing directory and executing go test -c ./... for 'ccip-load' package"
      pushd "./ccip-tests/load" && go test -c -tags embed -o ../ ./...
      mv ../load.test ../ccip-load.test # rename the binary to match the suite name
      popd
    else
      go test -c -tags embed ./"${x}"
    fi
    echo "Built ${x}.test"
done
IFS=$OIFS
