[Logging]
# set to true to flush logs to selected target regardless of test result; otherwise logs are only flushed if test failed
test_log_collect = false

[Logging.Grafana]
base_url="https://grafana.ops.prod.cldev.sh"
base_url_github_ci="http://localhost:8080/primary"
dashboard_url="/d/ddf75041-1e39-42af-aa46-361fe4c36e9e/ci-e2e-tests-logs"

[Logging.LogStream]
# supported targets: file, loki, in-memory. if empty no logs will be persisted
log_targets = ["file"]
# context timeout for starting log producer and also time-frame for requesting logs
log_producer_timeout = "10s"
# number of retries before log producer gives up and stops listening to logs
log_producer_retry_limit = 10

[ChainlinkImage]
# postgres version to use
postgres_version = "15.6"
# chainlink image tag to use
version = "2.12.0"
# Set chainlink image using E2E_TEST_CHAINLINK_IMAGE env, as it is a test secret

[Common]
# chainlink node funding in native token
chainlink_node_funding = 0.5

[Network]
# slice of networks to use; at lesat one network must be selected; each selected network must either be already defined in the CTF as a known network, or be defined in
# TOML test files as a new network
selected_networks = ["simulated"]

[PrivateEthereumNetwork]
# ethereum version to use; eth1 or eth2 (post-merge)
ethereum_version = "eth1"
# execution layer to use; geth, besu, nethermind, erigon or reth
execution_layer = "geth"

[PrivateEthereumNetwork.EthereumChainConfig]
# duration of single slot, lower => faster block production, must be >= 3
seconds_per_slot = 3
# number of slots in epoch, lower => faster epoch finalisation, must be >= 2
slots_per_epoch = 2
# extra genesis delay, no need to modify, but it should be after all validators/beacon chain starts
genesis_delay = 15
# number of validators in the network
validator_count = 4
# chain id to use
chain_id = 1337
# slice of addresses that will be funded with native token in genesis
addresses_to_fund = ["******************************************"]

# map of hard fork epochs for each network; key is fork name, value is hard fork epoch
# keep in mind that this depends on the specific version of eth2 client you are using
# this configuration is fault-tolerant and incorrect forks will be ignored
[PrivateEthereumNetwork.EthereumChainConfig.HardForkEpochs]
Deneb = 500

# General config of the Chainklink node corresponding to core/services/chainlink/config.go (Config struct) that excludes
# all chain-specific configuration, which is built based on selected_networks and either Chainlink Node's defaults for
# each network, or ChainConfigTOMLByChainID (if an entry with matching chain id is defined) or CommonChainConfigTOML (if no
# entry with matching chain id is defined).
#
# Please remember that if either ChainConfigTOMLByChainID or CommonChainConfigTOML is defined, it will override any defaults
# that Chainlink Node might have for the given network. Part of the configuration that defines blockchain node URLs is always
# dynamically generated based on the EVMNetwork configuration.
#
# Last, but not least, currently all selected networks are treated as EVM networks. There's no way to provide Solana, Starknet,
# Cosmos or Aptos configuration yet.
#
# If BaseConfigTOML is empty, then default base config provided by the Chainlink Node is used.
# Also, if tracing is enabled unique id will be generated and shared between all Chainlink nodes in the same test.
[NodeConfig]
BaseConfigTOML = """
[Feature]
FeedsManager = true
LogPoller = true
UICSAKeys = true

[Log]
Level = 'debug'
JSONConsole = true

[Log.File]
MaxSize = '0b'

[WebServer]
AllowOrigins = '*'
HTTPPort = 6688
SecureCookies = false
HTTPWriteTimeout = '3m'
SessionTimeout = '999h0m0s'

[WebServer.RateLimit]
Authenticated = 2000
Unauthenticated = 1000

[WebServer.TLS]
HTTPSPort = 0

[Database]
MaxIdleConns = 20
MaxOpenConns = 40
MigrateOnStartup = true

[OCR]
Enabled = true
DefaultTransactionQueueDepth = 0

[P2P]
[P2P.V2]
Enabled = true
ListenAddresses = ['0.0.0.0:6690']
AnnounceAddresses = ['0.0.0.0:6690']
DeltaDial = '500ms'
DeltaReconcile = '5s'
"""

# Overrides default config TOML related to EVMNode configs for chainlink nodes; applicable to all EVM node configs in chainlink TOML.
# Do not use it, if you want the default values to be used. Passing blockchain nodes URLs here will have no effect.
CommonChainConfigTOML = """
"""

[NodeConfig.ChainConfigTOMLByChainID]
# Chain-specific EVMNode config TOML for chainlink nodes; applicable to all EVM node configs in chainlink TOML. It takes precedence
# over CommonChainConfigTOML and Chainlink Node's defaults. Passing blockchain nodes URLs here will have no effect.
1337 = """
AutoCreateKey = true
FinalityDepth = 1
MinContractPayment = 0
[GasEstimator]
PriceMax = '200 gwei'
LimitDefault = 6000000
FeeCapDefault = '200 gwei'
"""

[Seth]
# controls which transactions are decoded/traced. Possbile values are: none, all, reverted (default).
# if transaction level doesn't match, then calling Decode() does nothing. It's advised to keep it set
# to 'reverted' to limit noise.
tracing_level = "reverted"
# saves each decoding/tracing results to DOT files; what exactly is saved depends on what we
# were able te decode, we try to save maximum information possible. It can either be:
# just tx hash, decoded transaction or call trace. Which transactions traces are saved depends
# on 'tracing_level'.
trace_outputs = ["dot", "console"]

# number of addresses to be generated and runtime, if set to 0, no addresses will be generated
# each generated address will receive a proportion of native tokens from root private key's balance
# with the value equal to (root_balance / ephemeral_addresses_number) - transfer_fee * ephemeral_addresses_number
ephemeral_addresses_number = 10

# If enabled we will panic when getting transaction options if current key/address has a pending transaction
# That's because the one we are about to send would get queued, possibly for a very long time
pending_nonce_protection_enabled = true

# Amount to be left on root key/address, when we are using ephemeral addresses. It's the amount that will not
# be divided into ephemeral keys. Default value is good for simulated networks, but you should change it for
# something much more reasonable for live networks.
root_key_funds_buffer = 1000 # 1000 ethers

# when enabled when creating a new Seth client we will send 10k wei from root address to root address
# to make sure transaction can be submited and mined
check_rpc_health_on_start = false

# feature-flagged expriments: "slow_funds_return" sets funds return priority to 'slow' (core only!);
# "eip_1559_fee_equalizer" sets the tip/base fee to the higher value in case there's 3+ orders of magnitude difference between them
experiments_enabled = []

[Seth.nonce_manager]
# rate-limiting of key syncs, to prevent spamming the node with nonce calls
key_sync_rate_limit_per_sec = 10
# how long to wait for a key sync to complete before giving up
key_sync_timeout = "100s"
# how long to wait before retrying a key sync
key_sync_retry_delay = "1s"
# how many times to retry a key sync before giving up
key_sync_retries = 10

# this is a default config that will be used if you haven't specified any network specific settings
# you can always override that by providing network specific settings like in the examples below
[[Seth.networks]]
name = "Default"
transaction_timeout = "60s"
# enable EIP-1559 transactions, because Seth will disable them if they are not supported
eip_1559_dynamic_fees = true
# enable automated gas estimation, because Seth will auto-disable it if any of the required JSON RPC methods are missing
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 100
gas_price_estimation_tx_priority = "standard"

# fallback values
transfer_gas_fee = 21_000
gas_price = 150_000_000_000   #150 gwei
gas_fee_cap = 150_000_000_000 #150 gwei
gas_tip_cap = 50_000_000_000  #50 gwei

[[Seth.networks]]
name = "Anvil"
transaction_timeout = "30s"
transfer_gas_fee = 21_000
gas_limit = 8_000_000
gas_price = 1_000_000_000
eip_1559_dynamic_fees = true
gas_fee_cap = 15_000_000_000
gas_tip_cap = 5_000_000_000

[[Seth.networks]]
name = "Geth"
transaction_timeout = "30s"

# gas limits
transfer_gas_fee = 21_000
# gas limit should be explicitly set only if you are connecting to a node that's incapable of estimating gas limit itself (should only happen for very old versions)
gas_limit = 8_000_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 1_000_000_000

# EIP-1559 transactions
eip_1559_dynamic_fees = true
gas_fee_cap = 15_000_000_000
gas_tip_cap = 5_000_000_000

[[Seth.networks]]
name = "Avalanche Fuji"
transaction_timeout = "3m"
eip_1559_dynamic_fees = true

# automated gas estimation for live networks
# if set to true we will dynamically estimate gas for every transaction (based on suggested values, priority and congestion rate for last X blocks)
# gas_price_estimation_enabled = true
# number of blocks to use for congestion rate estimation (it will determine buffer added on top of suggested values)
# gas_price_estimation_blocks = 100
# transaction priority, which determines adjustment factor multiplier applied to suggested values (fast - 1.2x, standard - 1x, slow - 0.8x)
# gas_price_estimation_tx_priority = "standard"

# URLs
# if set they will overwrite URLs from EVMNetwork that Seth uses, can be either WS(S) or HTTP(S)
# urls_secret = ["ws://your-ws-url:8546"]

# gas_limits
# gas limit should be explicitly set only if you are connecting to a node that's incapable of estimating gas limit itself (should only happen for very old versions)
# gas_limit = 8_000_000
# transfer_gas_fee is gas limit that will be used, when funding CL nodes and returning funds from there and when funding and returning funds from ephemeral keys
# we use hardcoded value in order to estimate how much funds are available for sending or returning after tx costs have been paid
transfer_gas_fee = 21_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 30_000_000_000

# EIP-1559 transactions
gas_fee_cap = 30_000_000_000
gas_tip_cap = 1_800_000_000

[[Seth.networks]]
name = "Sepolia Testnet"
transaction_timeout = "3m"
eip_1559_dynamic_fees = true
transfer_gas_fee = 21_000
gas_price = 105_000_000_000
gas_fee_cap = 150_312_843_059
gas_tip_cap = 40_416_094
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "Polygon Amoy"
transaction_timeout = "3m"
eip_1559_dynamic_fees = true

# automated gas estimation for live networks
# if set to true we will dynamically estimate gas for every transaction (based on suggested values, priority and congestion rate for last X blocks)
gas_price_estimation_enabled = true
# number of blocks to use for congestion rate estimation (it will determine buffer added on top of suggested values)
gas_price_estimation_blocks = 100
# transaction priority, which determines adjustment factor multiplier applied to suggested values (fast - 1.2x, standard - 1x, slow - 0.8x)
gas_price_estimation_tx_priority = "standard"

# URLs
# if set they will overwrite URLs from EVMNetwork that Seth uses, can be either WS(S) or HTTP(S)
# urls_secret = ["ws://your-ws-url:8546"]

# gas_limits
# gas limit should be explicitly set only if you are connecting to a node that's incapable of estimating gas limit itself (should only happen for very old versions)
# gas_limit = 6_000_000
# transfer_gas_fee is gas limit that will be used, when funding CL nodes and returning funds from there and when funding and returning funds from ephemeral keys
# we use hardcoded value in order to be estimate how much funds are available for sending or returning after tx costs have been paid
transfer_gas_fee = 21_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 200_000_000_000

# EIP-1559 transactions
gas_fee_cap = 200_000_000_000
gas_tip_cap = 25_000_000_000

[[Seth.networks]]
name = "Optimism Sepolia"
transaction_timeout = "3m"

# if set to true we will estimate gas for every transaction
gas_price_estimation_enabled = true

# transfer_gas_fee is gas limit that will be used, when funding CL nodes and returning funds from there and when funding and returning funds from ephemeral keys
# we use hardcoded value in order to be estimate how much funds are available for sending or returning after tx costs have been paid
transfer_gas_fee = 21_000
# gas limit should be explicitly set only if you are connecting to a node that's incapable of estimating gas limit itself (should only happen for very old versions)
# gas_limit = 100_000_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 30_000_000_000
# EIP-1559 transactions
eip_1559_dynamic_fees = true
gas_fee_cap = 30_000_000_000
gas_tip_cap = 1_000_000_000

# how many last blocks to use, when estimating gas for a transaction
gas_price_estimation_blocks = 30
# priority of the transaction, can be "fast", "standard" or "slow" (the higher the priority, the higher adjustment factor will be used for gas estimation) [default: "standard"]
gas_price_estimation_tx_priority = "standard"


[[Seth.networks]]
name = "Base Sepolia"
transaction_timeout = "3m"

# if set to true we will estimate gas for every transaction
gas_price_estimation_enabled = true

# transfer_gas_fee is gas limit that will be used, when funding CL nodes and returning funds from there and when funding and returning funds from ephemeral keys
# we use hardcoded value in order to be estimate how much funds are available for sending or returning after tx costs have been paid
transfer_gas_fee = 21_000
# gas limit should be explicitly set only if you are connecting to a node that's incapable of estimating gas limit itself (should only happen for very old versions)
# gas_limit = 100_000_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 30_000_000_000
# EIP-1559 transactions
eip_1559_dynamic_fees = true
gas_fee_cap = 30_000_000_000
gas_tip_cap = 1_000_000_000

# how many last blocks to use, when estimating gas for a transaction
gas_price_estimation_blocks = 50
# priority of the transaction, can be "fast", "standard" or "slow" (the higher the priority, the higher adjustment factor will be used for gas estimation) [default: "standard"]
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "Arbitrum Sepolia"
transaction_timeout = "10m"
transfer_gas_fee = 50_000
# gas_limit = 15_000_000
# legacy transactions
gas_price = 200_000_000_000
# EIP-1559 transactions
eip_1559_dynamic_fees = true
gas_fee_cap = 200_000_000_000
gas_tip_cap = 10_000_000_000
# if set to true we will estimate gas for every transaction
gas_price_estimation_enabled = true
# how many last blocks to use, when estimating gas for a transaction
gas_price_estimation_blocks = 100
# priority of the transaction, can be "fast", "standard" or "slow" (the higher the priority, the higher adjustment factor will be used for gas estimation) [default: "standard"]

gas_price_estimation_tx_priority = "standard"


[[Seth.networks]]
name = "Nexon Mainnet"
transaction_timeout = "3m"
eip_1559_dynamic_fees = true
transfer_gas_fee = 21_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 30_000_000_000

# EIP-1559 transactions
gas_fee_cap = 30_000_000_000
gas_tip_cap = 1_800_000_000


[Network.EVMNetworks.NEXON_MAINNET]
evm_name = "NEXON_MAINNET"
#evm_urls = ["rpc ws endpoint"]
#evm_http_urls = ["rpc http endpoint"]
client_implementation = "Ethereum"
#evm_keys = ["private keys you want to use"]
evm_simulated = false
evm_chainlink_transaction_limit = 5000
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 10000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_chain_id = 60118

[[Seth.networks]]
name = "Nexon Stage"
transaction_timeout = "3m"
eip_1559_dynamic_fees = true
transfer_gas_fee = 21_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 30_000_000_000

# EIP-1559 transactions
gas_fee_cap = 30_000_000_000
gas_tip_cap = 1_800_000_000


[Network.EVMNetworks.NEXON_STAGE]
evm_name = "NEXON_STAGE"
#evm_urls = ["rpc ws endpoint"]
#evm_http_urls = ["rpc http endpoint"]
client_implementation = "Ethereum"
#evm_keys = ["private keys you want to use"]
evm_simulated = false
evm_chainlink_transaction_limit = 5000
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 10000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_chain_id = 847799


####

[[Seth.networks]]
name = "Nexon QA"
transaction_timeout = "3m"
eip_1559_dynamic_fees = true
transfer_gas_fee = 21_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 30_000_000_000

# EIP-1559 transactions
gas_fee_cap = 30_000_000_000
gas_tip_cap = 1_800_000_000


[Network.EVMNetworks.NEXON_QA]
evm_name = "NEXON_QA"
#evm_urls = ["rpc ws endpoint"]
#evm_http_urls = ["rpc http endpoint"]
client_implementation = "Ethereum"
#evm_keys = ["private keys you want to use"]
evm_simulated = false
evm_chainlink_transaction_limit = 5000
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 10000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_chain_id = 807424

#####

[[Seth.networks]]
name = "Nexon Test"
transaction_timeout = "3m"
eip_1559_dynamic_fees = true
transfer_gas_fee = 21_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 30_000_000_000

# EIP-1559 transactions
gas_fee_cap = 30_000_000_000
gas_tip_cap = 1_800_000_000


[Network.EVMNetworks.NEXON_TEST]
evm_name = "NEXON_TEST"
#evm_urls = ["rpc ws endpoint"]
#evm_http_urls = ["rpc http endpoint"]
client_implementation = "Ethereum"
#evm_keys = ["private keys you want to use"]
evm_simulated = false
evm_chainlink_transaction_limit = 5000
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 10000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_chain_id = 595581

#####
[[Seth.networks]]
name = "Nexon Dev"
transaction_timeout = "3m"
eip_1559_dynamic_fees = true
transfer_gas_fee = 21_000

# manual settings, used when gas_price_estimation_enabled is false or when it fails
# legacy transactions
gas_price = 30_000_000_000

# EIP-1559 transactions
gas_fee_cap = 30_000_000_000
gas_tip_cap = 1_800_000_000


[Network.EVMNetworks.NEXON_DEV]
evm_name = "NEXON_DEV"
#evm_urls = ["rpc ws endpoint"]
#evm_http_urls = ["rpc http endpoint"]
client_implementation = "Ethereum"
#evm_keys = ["private keys you want to use"]
evm_simulated = false
evm_chainlink_transaction_limit = 5000
evm_minimum_confirmations = 1
evm_gas_estimation_buffer = 10000
evm_supports_eip1559 = true
evm_default_gas_limit = 6000000
evm_chain_id = 5668

[[Seth.networks]]
name = "LINEA_SEPOLIA"
chain_id = "59141"
transaction_timeout = "10m"
transfer_gas_fee = 21_000
gas_price = 200_000_000_000_000
eip_1559_dynamic_fees = false
gas_fee_cap = 109_694_825_437
gas_tip_cap = 30_000_000_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "LINEA_MAINNET"
chain_id = "59144"
transaction_timeout = "10m"
transfer_gas_fee = 21_000
gas_price = 200_000_000_000_000
eip_1559_dynamic_fees = false
gas_fee_cap = 109_694_825_437
gas_tip_cap = 30_000_000_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "ZKSYNC_SEPOLIA"
chain_id = "300"
transaction_timeout = "3m"
transfer_gas_fee = 21_000
gas_price = 200_000_000_000
eip_1559_dynamic_fees = true
gas_fee_cap = 109_694_825_437
gas_tip_cap = 30_000_000_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "ZKSYNC_MAINNET"
chain_id = "324"
transaction_timeout = "3m"
transfer_gas_fee = 21_000
gas_price = 200_000_000_000
eip_1559_dynamic_fees = true
gas_fee_cap = 109_694_825_437
gas_tip_cap = 30_000_000_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "POLYGON_ZKEVM_CARDONA"
transaction_timeout = "3m"
transfer_gas_fee = 21_000
gas_price = 743_000_000
eip_1559_dynamic_fees = false
gas_fee_cap = 1_725_800_000
gas_tip_cap = 822_800_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "HEDERA_TESTNET"
transaction_timeout = "3m"
transfer_gas_fee = 800_000
gas_limit = 2_000_000
gas_price = 2_500_000_000_000
eip_1559_dynamic_fees = false
gas_fee_cap = 109_694_825_437
gas_tip_cap = 30_000_000_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "TREASURE_RUBY"
transaction_timeout = "10m"
transfer_gas_fee = 21_000
gas_price = 100_000_000
eip_1559_dynamic_fees = true
gas_fee_cap = 200_000_000
gas_tip_cap = 100_000_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "XLAYER_MAINNET"
transaction_timeout = "10m"
transfer_gas_fee = 21_000
gas_price = 13_400_000_000
eip_1559_dynamic_fees = false
gas_fee_cap = 109_694_825_437
gas_tip_cap = 30_000_000_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 500
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "XLAYER_SEPOLIA"
transaction_timeout = "10m"
transfer_gas_fee = 21_000
gas_price = 200_000_000_000
eip_1559_dynamic_fees = false
gas_fee_cap = 109_694_825_437
gas_tip_cap = 30_000_000_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 500
gas_price_estimation_tx_priority = "standard"

[[Seth.networks]]
name = "POLYGON_MAINNET"
transaction_timeout = "3m"
transfer_gas_fee = 21_000
gas_price = 31_000_000_000
eip_1559_dynamic_fees = true
gas_fee_cap = 61_000_000_000
gas_tip_cap = 30_000_000_000
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000

[[Seth.networks]]
name = "ARBITRUM_MAINNET"
transaction_timeout = "10m"
transfer_gas_fee = 21_000
gas_price = 10_000_000
eip_1559_dynamic_fees = true
gas_fee_cap = 10_000_000
gas_tip_cap = 0
gas_price_estimation_enabled = true
gas_price_estimation_blocks = 1000

####

[Network.EVMNetworks.SONEIUM_SEPOLIA]
evm_name = "SONEIUM_SEPOLIA"
evm_chain_id = 1946
client_implementation = "Optimism"
evm_simulated = false

####
