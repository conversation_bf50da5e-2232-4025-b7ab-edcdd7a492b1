# Example of full config with all fields
# General part
[ChainlinkImage]
version="2.7.0"

[Logging]
# if set to true will save logs even if test did not fail
test_log_collect=false

[Logging.LogStream]
# supported targets: file, loki, in-memory. if empty no logs will be persistet
log_targets=["file"]
# context timeout for starting log producer and also time-frame for requesting logs
log_producer_timeout="10s"
# number of retries before log producer gives up and stops listening to logs
log_producer_retry_limit=10

# if you want to use polygon_mumbial
[Network]
selected_networks=["polygon_mumbai"]

[PrivateEthereumNetwork]
# pos or pow
consensus_type="pos"
# only prysm supported currently
consensus_layer="prysm"
# geth, besu, nethermind or erigon
execution_layer="geth"
# if true after env started it will wait for at least 1 epoch to be finalised before continuing
wait_for_finalization=false

[PrivateEthereumNetwork.EthereumChainConfig]
# duration of single slot, lower => faster block production, must be >= 4
seconds_per_slot=12
# numer of slots in epoch, lower => faster epoch finalisation, must be >= 4
slots_per_epoch=6
# extra genesis gelay, no need to modify, but it should be after all validators/beacon chain starts
genesis_delay=15
# number of validators in the network
validator_count=8
chain_id=1337
# list of addresses to be prefunded in genesis
addresses_to_fund=["******************************************"]

[PrivateEthereumNetwork.EthereumChainConfig.HardForkEpochs]
# hardforks to be applied (fork_name = epoch)
Deneb=500

# Chainlink node TOML config
[NodeConfig]
BaseConfigTOML = """
[Feature]
FeedsManager = true
LogPoller = true
UICSAKeys = true

[Log]
Level = 'debug'
JSONConsole = true

[Log.File]
MaxSize = '0b'

[WebServer]
AllowOrigins = '*'
HTTPPort = 6688
SecureCookies = false
HTTPWriteTimeout = '3m'
SessionTimeout = '999h0m0s'

[WebServer.RateLimit]
Authenticated = 2000
Unauthenticated = 1000

[WebServer.TLS]
HTTPSPort = 0

[Database]
MaxIdleConns = 20
MaxOpenConns = 40
MigrateOnStartup = true

[OCR]
Enabled = true

[P2P]
[P2P.V2]
ListenAddresses = ['0.0.0.0:6690']
"""

# override config toml related to EVMNode configs for chainlink nodes; applicable to all EVM node configs in chainlink toml
CommonChainConfigTOML = """
AutoCreateKey = true
FinalityDepth = 1
MinContractPayment = 0

[GasEstimator]
PriceMax = '200 gwei'
LimitDefault = 6000000
FeeCapDefault = '200 gwei'
"""

# chainlink override config toml for EVMNode config specific to EVM chains with chain id as mentioned in the key
[NodeConfig.ChainConfigTOMLByChainID]
# applicable for arbitrum-goerli chain
421613 = """
[GasEstimator]
PriceMax = '400 gwei'
LimitDefault = 100000000
FeeCapDefault = '200 gwei'
BumpThreshold = 60
BumpPercent = 20
BumpMin = '100 gwei'
"""

# Common
[Common]
chainlink_node_funding = 0.5

# Product part
[VRFv2Plus]
[VRFv2Plus.General]
cancel_subs_after_test_run = true
max_gas_price_gwei = 1000
link_native_feed_response = 1000000000000000000
minimum_confirmations = 3
subscription_billing_type = "LINK_AND_NATIVE"
subscription_funding_amount_link = 5.0
number_of_words = 3
callback_gas_limit = 1000000
max_gas_limit_coordinator_config = 2500000
fallback_wei_per_unit_link = "60000000000000000"
staleness_seconds = 86400
gas_after_payment_calculation = 33825
fulfilment_flat_fee_link_ppm_tier_1 = 500
fulfilment_flat_fee_link_ppm_tier_2 = 500
fulfilment_flat_fee_link_ppm_tier_3 = 500
fulfilment_flat_fee_link_ppm_tier_4 = 500
fulfilment_flat_fee_link_ppm_tier_5 = 500
reqs_for_tier_2 = 0
reqs_for_tier_3 = 0
reqs_for_tier_4 = 0
reqs_for_tier_5 = 0
number_of_sub_to_create = 1
randomness_request_count_per_request = 1
randomness_request_count_per_request_deviation = 0
random_words_fulfilled_event_timeout = "2m"
wrapped_gas_overhead = 50000
coordinator_gas_overhead_native = 52000
coordinator_gas_overhead_link = 74000
coordinator_gas_overhead_per_word = 440
wrapper_premium_percentage = 25
wrapper_max_number_of_words = 10
wrapper_consumer_funding_amount_native_token = 1.0
wrapper_consumer_funding_amount_link = 10
subscription_funding_amount_native=1
fulfillment_flat_fee_native_ppm=500
fulfillment_flat_fee_link_discount_ppm=100
native_premium_percentage=1
link_premium_percentage=1

[VRFv2Plus.Performance]
test_duration = "2m"
rate_limit_unit_duration = "3s"
rps = 1
use_existing_env = false

[VRFv2Plus.NewEnv]
sub_funds_link = 1
sub_funds_native = 1
node_funds = 10
node_sending_key_funding = 1000

[VRFv2Plus.ExistingEnv]
coordinator_address = ""
consumer_address = ""
sub_id = 1
key_hash = ""
create_fund_subs_and_add_consumers = true
link_address = ""
sub_funds_link = 10
node_sending_key_funding_min = 1
node_sending_keys = [
    "",
    "",
    "",
    "",
    "",
    "",
]