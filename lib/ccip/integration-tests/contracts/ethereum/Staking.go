// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package ethereum

import (
	"errors"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
)

// StakingPoolConstructorParams is an auto generated low-level Go binding around an user-defined struct.
type StakingPoolConstructorParams struct {
	LINKAddress                    common.Address
	MonitoredFeed                  common.Address
	InitialMaxPoolSize             *big.Int
	InitialMaxCommunityStakeAmount *big.Int
	InitialMaxOperatorStakeAmount  *big.Int
	MinCommunityStakeAmount        *big.Int
	MinOperatorStakeAmount         *big.Int
	PriorityPeriodThreshold        *big.Int
	RegularPeriodThreshold         *big.Int
	MaxAlertingRewardAmount        *big.Int
	MinInitialOperatorCount        *big.Int
	MinRewardDuration              *big.Int
	SlashableDuration              *big.Int
	DelegationRateDenominator      *big.Int
}

// StakingMetaData contains all meta data concerning the Staking contract.
var StakingMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"components\":[{\"internalType\":\"contractLinkTokenInterface\",\"name\":\"LINKAddress\",\"type\":\"address\"},{\"internalType\":\"contractAggregatorV3Interface\",\"name\":\"monitoredFeed\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"initialMaxPoolSize\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"initialMaxCommunityStakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"initialMaxOperatorStakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minCommunityStakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minOperatorStakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"priorityPeriodThreshold\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"regularPeriodThreshold\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxAlertingRewardAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minInitialOperatorCount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minRewardDuration\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"slashableDuration\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"delegationRateDenominator\",\"type\":\"uint256\"}],\"internalType\":\"structStaking.PoolConstructorParams\",\"name\":\"params\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AccessForbidden\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundId\",\"type\":\"uint256\"}],\"name\":\"AlertAlreadyExists\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"AlertInvalid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CastError\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"remainingAmount\",\"type\":\"uint256\"}],\"name\":\"ExcessiveStakeAmount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"ExistingStakeFound\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"currentOperatorsCount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minInitialOperatorsCount\",\"type\":\"uint256\"}],\"name\":\"InadequateInitialOperatorsCount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"remainingPoolSize\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"requiredPoolSize\",\"type\":\"uint256\"}],\"name\":\"InsufficientRemainingPoolSpace\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requiredAmount\",\"type\":\"uint256\"}],\"name\":\"InsufficientStakeAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidDelegationRate\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidMaxAlertingRewardAmount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"maxStakeAmount\",\"type\":\"uint256\"}],\"name\":\"InvalidMaxStakeAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidMigrationTarget\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidMinCommunityStakeAmount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidMinOperatorStakeAmount\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"maxPoolSize\",\"type\":\"uint256\"}],\"name\":\"InvalidPoolSize\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"currentStatus\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"requiredStatus\",\"type\":\"bool\"}],\"name\":\"InvalidPoolStatus\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidRegularPeriodThreshold\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"MerkleRootNotSet\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"OperatorAlreadyExists\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"OperatorDoesNotExist\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"OperatorIsAssignedToFeed\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"}],\"name\":\"OperatorIsLocked\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RewardDurationTooShort\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SenderNotLinkToken\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"StakeNotFound\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"alerter\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"roundId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"rewardAmount\",\"type\":\"uint256\"}],\"name\":\"AlertRaised\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"newMerkleRoot\",\"type\":\"bytes32\"}],\"name\":\"MerkleRootChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"principal\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"baseReward\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"delegationReward\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"Migrated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"migrationTarget\",\"type\":\"address\"}],\"name\":\"MigrationTargetAccepted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"migrationTarget\",\"type\":\"address\"}],\"name\":\"MigrationTargetProposed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"newStake\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"totalStake\",\"type\":\"uint256\"}],\"name\":\"Staked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"principal\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"baseReward\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"delegationReward\",\"type\":\"uint256\"}],\"name\":\"Unstaked\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptMigrationTarget\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"}],\"name\":\"addOperators\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"addReward\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"alerter\",\"type\":\"address\"}],\"name\":\"canAlert\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"newRate\",\"type\":\"uint256\"}],\"name\":\"changeRewardRate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"conclude\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"emergencyPause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"emergencyUnpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAvailableReward\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"getBaseReward\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getChainlinkToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCommunityStakerLimits\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDelegatesCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDelegationRateDenominator\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"getDelegationReward\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getEarnedBaseRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getEarnedDelegationRewards\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getFeedOperators\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMaxPoolSize\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMerkleRoot\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMigrationTarget\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getMonitoredFeed\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getOperatorLimits\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRewardRate\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRewardTimestamps\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"getStake\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTotalCommunityStakedAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTotalDelegatedAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTotalRemovedAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTotalStakedAmount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"},{\"internalType\":\"bytes32[]\",\"name\":\"proof\",\"type\":\"bytes32[]\"}],\"name\":\"hasAccess\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isActive\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"staker\",\"type\":\"address\"}],\"name\":\"isOperator\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isPaused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"migrate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"onTokenTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"migrationTarget\",\"type\":\"address\"}],\"name\":\"proposeMigrationTarget\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"raiseAlert\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"}],\"name\":\"removeOperators\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"operators\",\"type\":\"address[]\"}],\"name\":\"setFeedOperators\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"newMerkleRoot\",\"type\":\"bytes32\"}],\"name\":\"setMerkleRoot\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"maxPoolSize\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxCommunityStakeAmount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxOperatorStakeAmount\",\"type\":\"uint256\"}],\"name\":\"setPoolConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"initialRewardRate\",\"type\":\"uint256\"}],\"name\":\"start\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"typeAndVersion\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unstake\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"withdrawRemovedStake\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"withdrawUnusedReward\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}]",
	Bin: "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",
}

// StakingABI is the input ABI used to generate the binding from.
// Deprecated: Use StakingMetaData.ABI instead.
var StakingABI = StakingMetaData.ABI

// StakingBin is the compiled bytecode used for deploying new contracts.
// Deprecated: Use StakingMetaData.Bin instead.
var StakingBin = StakingMetaData.Bin

// DeployStaking deploys a new Ethereum contract, binding an instance of Staking to it.
func DeployStaking(auth *bind.TransactOpts, backend bind.ContractBackend, params StakingPoolConstructorParams) (common.Address, *types.Transaction, *Staking, error) {
	parsed, err := StakingMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(StakingBin), backend, params)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &Staking{StakingCaller: StakingCaller{contract: contract}, StakingTransactor: StakingTransactor{contract: contract}, StakingFilterer: StakingFilterer{contract: contract}}, nil
}

// Staking is an auto generated Go binding around an Ethereum contract.
type Staking struct {
	StakingCaller     // Read-only binding to the contract
	StakingTransactor // Write-only binding to the contract
	StakingFilterer   // Log filterer for contract events
}

// StakingCaller is an auto generated read-only Go binding around an Ethereum contract.
type StakingCaller struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// StakingTransactor is an auto generated write-only Go binding around an Ethereum contract.
type StakingTransactor struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// StakingFilterer is an auto generated log filtering Go binding around an Ethereum contract events.
type StakingFilterer struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// StakingSession is an auto generated Go binding around an Ethereum contract,
// with pre-set call and transact options.
type StakingSession struct {
	Contract     *Staking          // Generic contract binding to set the session for
	CallOpts     bind.CallOpts     // Call options to use throughout this session
	TransactOpts bind.TransactOpts // Transaction auth options to use throughout this session
}

// StakingCallerSession is an auto generated read-only Go binding around an Ethereum contract,
// with pre-set call options.
type StakingCallerSession struct {
	Contract *StakingCaller // Generic contract caller binding to set the session for
	CallOpts bind.CallOpts  // Call options to use throughout this session
}

// StakingTransactorSession is an auto generated write-only Go binding around an Ethereum contract,
// with pre-set transact options.
type StakingTransactorSession struct {
	Contract     *StakingTransactor // Generic contract transactor binding to set the session for
	TransactOpts bind.TransactOpts  // Transaction auth options to use throughout this session
}

// StakingRaw is an auto generated low-level Go binding around an Ethereum contract.
type StakingRaw struct {
	Contract *Staking // Generic contract binding to access the raw methods on
}

// StakingCallerRaw is an auto generated low-level read-only Go binding around an Ethereum contract.
type StakingCallerRaw struct {
	Contract *StakingCaller // Generic read-only contract binding to access the raw methods on
}

// StakingTransactorRaw is an auto generated low-level write-only Go binding around an Ethereum contract.
type StakingTransactorRaw struct {
	Contract *StakingTransactor // Generic write-only contract binding to access the raw methods on
}

// NewStaking creates a new instance of Staking, bound to a specific deployed contract.
func NewStaking(address common.Address, backend bind.ContractBackend) (*Staking, error) {
	contract, err := bindStaking(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &Staking{StakingCaller: StakingCaller{contract: contract}, StakingTransactor: StakingTransactor{contract: contract}, StakingFilterer: StakingFilterer{contract: contract}}, nil
}

// NewStakingCaller creates a new read-only instance of Staking, bound to a specific deployed contract.
func NewStakingCaller(address common.Address, caller bind.ContractCaller) (*StakingCaller, error) {
	contract, err := bindStaking(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &StakingCaller{contract: contract}, nil
}

// NewStakingTransactor creates a new write-only instance of Staking, bound to a specific deployed contract.
func NewStakingTransactor(address common.Address, transactor bind.ContractTransactor) (*StakingTransactor, error) {
	contract, err := bindStaking(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &StakingTransactor{contract: contract}, nil
}

// NewStakingFilterer creates a new log filterer instance of Staking, bound to a specific deployed contract.
func NewStakingFilterer(address common.Address, filterer bind.ContractFilterer) (*StakingFilterer, error) {
	contract, err := bindStaking(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &StakingFilterer{contract: contract}, nil
}

// bindStaking binds a generic wrapper to an already deployed contract.
func bindStaking(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := abi.JSON(strings.NewReader(StakingABI))
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, parsed, caller, transactor, filterer), nil
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_Staking *StakingRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _Staking.Contract.StakingCaller.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_Staking *StakingRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.Contract.StakingTransactor.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_Staking *StakingRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _Staking.Contract.StakingTransactor.contract.Transact(opts, method, params...)
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_Staking *StakingCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _Staking.Contract.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_Staking *StakingTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.Contract.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_Staking *StakingTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _Staking.Contract.contract.Transact(opts, method, params...)
}

// CanAlert is a free data retrieval call binding the contract method 0xc1852f58.
//
// Solidity: function canAlert(address alerter) view returns(bool)
func (_Staking *StakingCaller) CanAlert(opts *bind.CallOpts, alerter common.Address) (bool, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "canAlert", alerter)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// CanAlert is a free data retrieval call binding the contract method 0xc1852f58.
//
// Solidity: function canAlert(address alerter) view returns(bool)
func (_Staking *StakingSession) CanAlert(alerter common.Address) (bool, error) {
	return _Staking.Contract.CanAlert(&_Staking.CallOpts, alerter)
}

// CanAlert is a free data retrieval call binding the contract method 0xc1852f58.
//
// Solidity: function canAlert(address alerter) view returns(bool)
func (_Staking *StakingCallerSession) CanAlert(alerter common.Address) (bool, error) {
	return _Staking.Contract.CanAlert(&_Staking.CallOpts, alerter)
}

// GetAvailableReward is a free data retrieval call binding the contract method 0xe0974ea5.
//
// Solidity: function getAvailableReward() view returns(uint256)
func (_Staking *StakingCaller) GetAvailableReward(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getAvailableReward")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetAvailableReward is a free data retrieval call binding the contract method 0xe0974ea5.
//
// Solidity: function getAvailableReward() view returns(uint256)
func (_Staking *StakingSession) GetAvailableReward() (*big.Int, error) {
	return _Staking.Contract.GetAvailableReward(&_Staking.CallOpts)
}

// GetAvailableReward is a free data retrieval call binding the contract method 0xe0974ea5.
//
// Solidity: function getAvailableReward() view returns(uint256)
func (_Staking *StakingCallerSession) GetAvailableReward() (*big.Int, error) {
	return _Staking.Contract.GetAvailableReward(&_Staking.CallOpts)
}

// GetBaseReward is a free data retrieval call binding the contract method 0x9a109bc2.
//
// Solidity: function getBaseReward(address staker) view returns(uint256)
func (_Staking *StakingCaller) GetBaseReward(opts *bind.CallOpts, staker common.Address) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getBaseReward", staker)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetBaseReward is a free data retrieval call binding the contract method 0x9a109bc2.
//
// Solidity: function getBaseReward(address staker) view returns(uint256)
func (_Staking *StakingSession) GetBaseReward(staker common.Address) (*big.Int, error) {
	return _Staking.Contract.GetBaseReward(&_Staking.CallOpts, staker)
}

// GetBaseReward is a free data retrieval call binding the contract method 0x9a109bc2.
//
// Solidity: function getBaseReward(address staker) view returns(uint256)
func (_Staking *StakingCallerSession) GetBaseReward(staker common.Address) (*big.Int, error) {
	return _Staking.Contract.GetBaseReward(&_Staking.CallOpts, staker)
}

// GetChainlinkToken is a free data retrieval call binding the contract method 0x165d35e1.
//
// Solidity: function getChainlinkToken() view returns(address)
func (_Staking *StakingCaller) GetChainlinkToken(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getChainlinkToken")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// GetChainlinkToken is a free data retrieval call binding the contract method 0x165d35e1.
//
// Solidity: function getChainlinkToken() view returns(address)
func (_Staking *StakingSession) GetChainlinkToken() (common.Address, error) {
	return _Staking.Contract.GetChainlinkToken(&_Staking.CallOpts)
}

// GetChainlinkToken is a free data retrieval call binding the contract method 0x165d35e1.
//
// Solidity: function getChainlinkToken() view returns(address)
func (_Staking *StakingCallerSession) GetChainlinkToken() (common.Address, error) {
	return _Staking.Contract.GetChainlinkToken(&_Staking.CallOpts)
}

// GetCommunityStakerLimits is a free data retrieval call binding the contract method 0x0641bdd8.
//
// Solidity: function getCommunityStakerLimits() view returns(uint256, uint256)
func (_Staking *StakingCaller) GetCommunityStakerLimits(opts *bind.CallOpts) (*big.Int, *big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getCommunityStakerLimits")

	if err != nil {
		return *new(*big.Int), *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)
	out1 := *abi.ConvertType(out[1], new(*big.Int)).(**big.Int)

	return out0, out1, err

}

// GetCommunityStakerLimits is a free data retrieval call binding the contract method 0x0641bdd8.
//
// Solidity: function getCommunityStakerLimits() view returns(uint256, uint256)
func (_Staking *StakingSession) GetCommunityStakerLimits() (*big.Int, *big.Int, error) {
	return _Staking.Contract.GetCommunityStakerLimits(&_Staking.CallOpts)
}

// GetCommunityStakerLimits is a free data retrieval call binding the contract method 0x0641bdd8.
//
// Solidity: function getCommunityStakerLimits() view returns(uint256, uint256)
func (_Staking *StakingCallerSession) GetCommunityStakerLimits() (*big.Int, *big.Int, error) {
	return _Staking.Contract.GetCommunityStakerLimits(&_Staking.CallOpts)
}

// GetDelegatesCount is a free data retrieval call binding the contract method 0x32e28850.
//
// Solidity: function getDelegatesCount() view returns(uint256)
func (_Staking *StakingCaller) GetDelegatesCount(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getDelegatesCount")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetDelegatesCount is a free data retrieval call binding the contract method 0x32e28850.
//
// Solidity: function getDelegatesCount() view returns(uint256)
func (_Staking *StakingSession) GetDelegatesCount() (*big.Int, error) {
	return _Staking.Contract.GetDelegatesCount(&_Staking.CallOpts)
}

// GetDelegatesCount is a free data retrieval call binding the contract method 0x32e28850.
//
// Solidity: function getDelegatesCount() view returns(uint256)
func (_Staking *StakingCallerSession) GetDelegatesCount() (*big.Int, error) {
	return _Staking.Contract.GetDelegatesCount(&_Staking.CallOpts)
}

// GetDelegationRateDenominator is a free data retrieval call binding the contract method 0x5e8b40d7.
//
// Solidity: function getDelegationRateDenominator() view returns(uint256)
func (_Staking *StakingCaller) GetDelegationRateDenominator(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getDelegationRateDenominator")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetDelegationRateDenominator is a free data retrieval call binding the contract method 0x5e8b40d7.
//
// Solidity: function getDelegationRateDenominator() view returns(uint256)
func (_Staking *StakingSession) GetDelegationRateDenominator() (*big.Int, error) {
	return _Staking.Contract.GetDelegationRateDenominator(&_Staking.CallOpts)
}

// GetDelegationRateDenominator is a free data retrieval call binding the contract method 0x5e8b40d7.
//
// Solidity: function getDelegationRateDenominator() view returns(uint256)
func (_Staking *StakingCallerSession) GetDelegationRateDenominator() (*big.Int, error) {
	return _Staking.Contract.GetDelegationRateDenominator(&_Staking.CallOpts)
}

// GetDelegationReward is a free data retrieval call binding the contract method 0x87e900b1.
//
// Solidity: function getDelegationReward(address staker) view returns(uint256)
func (_Staking *StakingCaller) GetDelegationReward(opts *bind.CallOpts, staker common.Address) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getDelegationReward", staker)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetDelegationReward is a free data retrieval call binding the contract method 0x87e900b1.
//
// Solidity: function getDelegationReward(address staker) view returns(uint256)
func (_Staking *StakingSession) GetDelegationReward(staker common.Address) (*big.Int, error) {
	return _Staking.Contract.GetDelegationReward(&_Staking.CallOpts, staker)
}

// GetDelegationReward is a free data retrieval call binding the contract method 0x87e900b1.
//
// Solidity: function getDelegationReward(address staker) view returns(uint256)
func (_Staking *StakingCallerSession) GetDelegationReward(staker common.Address) (*big.Int, error) {
	return _Staking.Contract.GetDelegationReward(&_Staking.CallOpts, staker)
}

// GetEarnedBaseRewards is a free data retrieval call binding the contract method 0x1a9d4c7c.
//
// Solidity: function getEarnedBaseRewards() view returns(uint256)
func (_Staking *StakingCaller) GetEarnedBaseRewards(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getEarnedBaseRewards")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetEarnedBaseRewards is a free data retrieval call binding the contract method 0x1a9d4c7c.
//
// Solidity: function getEarnedBaseRewards() view returns(uint256)
func (_Staking *StakingSession) GetEarnedBaseRewards() (*big.Int, error) {
	return _Staking.Contract.GetEarnedBaseRewards(&_Staking.CallOpts)
}

// GetEarnedBaseRewards is a free data retrieval call binding the contract method 0x1a9d4c7c.
//
// Solidity: function getEarnedBaseRewards() view returns(uint256)
func (_Staking *StakingCallerSession) GetEarnedBaseRewards() (*big.Int, error) {
	return _Staking.Contract.GetEarnedBaseRewards(&_Staking.CallOpts)
}

// GetEarnedDelegationRewards is a free data retrieval call binding the contract method 0x74104002.
//
// Solidity: function getEarnedDelegationRewards() view returns(uint256)
func (_Staking *StakingCaller) GetEarnedDelegationRewards(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getEarnedDelegationRewards")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetEarnedDelegationRewards is a free data retrieval call binding the contract method 0x74104002.
//
// Solidity: function getEarnedDelegationRewards() view returns(uint256)
func (_Staking *StakingSession) GetEarnedDelegationRewards() (*big.Int, error) {
	return _Staking.Contract.GetEarnedDelegationRewards(&_Staking.CallOpts)
}

// GetEarnedDelegationRewards is a free data retrieval call binding the contract method 0x74104002.
//
// Solidity: function getEarnedDelegationRewards() view returns(uint256)
func (_Staking *StakingCallerSession) GetEarnedDelegationRewards() (*big.Int, error) {
	return _Staking.Contract.GetEarnedDelegationRewards(&_Staking.CallOpts)
}

// GetFeedOperators is a free data retrieval call binding the contract method 0x5fec60f8.
//
// Solidity: function getFeedOperators() view returns(address[])
func (_Staking *StakingCaller) GetFeedOperators(opts *bind.CallOpts) ([]common.Address, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getFeedOperators")

	if err != nil {
		return *new([]common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new([]common.Address)).(*[]common.Address)

	return out0, err

}

// GetFeedOperators is a free data retrieval call binding the contract method 0x5fec60f8.
//
// Solidity: function getFeedOperators() view returns(address[])
func (_Staking *StakingSession) GetFeedOperators() ([]common.Address, error) {
	return _Staking.Contract.GetFeedOperators(&_Staking.CallOpts)
}

// GetFeedOperators is a free data retrieval call binding the contract method 0x5fec60f8.
//
// Solidity: function getFeedOperators() view returns(address[])
func (_Staking *StakingCallerSession) GetFeedOperators() ([]common.Address, error) {
	return _Staking.Contract.GetFeedOperators(&_Staking.CallOpts)
}

// GetMaxPoolSize is a free data retrieval call binding the contract method 0x0fbc8f5b.
//
// Solidity: function getMaxPoolSize() view returns(uint256)
func (_Staking *StakingCaller) GetMaxPoolSize(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getMaxPoolSize")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetMaxPoolSize is a free data retrieval call binding the contract method 0x0fbc8f5b.
//
// Solidity: function getMaxPoolSize() view returns(uint256)
func (_Staking *StakingSession) GetMaxPoolSize() (*big.Int, error) {
	return _Staking.Contract.GetMaxPoolSize(&_Staking.CallOpts)
}

// GetMaxPoolSize is a free data retrieval call binding the contract method 0x0fbc8f5b.
//
// Solidity: function getMaxPoolSize() view returns(uint256)
func (_Staking *StakingCallerSession) GetMaxPoolSize() (*big.Int, error) {
	return _Staking.Contract.GetMaxPoolSize(&_Staking.CallOpts)
}

// GetMerkleRoot is a free data retrieval call binding the contract method 0x49590657.
//
// Solidity: function getMerkleRoot() view returns(bytes32)
func (_Staking *StakingCaller) GetMerkleRoot(opts *bind.CallOpts) ([32]byte, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getMerkleRoot")

	if err != nil {
		return *new([32]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([32]byte)).(*[32]byte)

	return out0, err

}

// GetMerkleRoot is a free data retrieval call binding the contract method 0x49590657.
//
// Solidity: function getMerkleRoot() view returns(bytes32)
func (_Staking *StakingSession) GetMerkleRoot() ([32]byte, error) {
	return _Staking.Contract.GetMerkleRoot(&_Staking.CallOpts)
}

// GetMerkleRoot is a free data retrieval call binding the contract method 0x49590657.
//
// Solidity: function getMerkleRoot() view returns(bytes32)
func (_Staking *StakingCallerSession) GetMerkleRoot() ([32]byte, error) {
	return _Staking.Contract.GetMerkleRoot(&_Staking.CallOpts)
}

// GetMigrationTarget is a free data retrieval call binding the contract method 0x1ddb5552.
//
// Solidity: function getMigrationTarget() view returns(address)
func (_Staking *StakingCaller) GetMigrationTarget(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getMigrationTarget")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// GetMigrationTarget is a free data retrieval call binding the contract method 0x1ddb5552.
//
// Solidity: function getMigrationTarget() view returns(address)
func (_Staking *StakingSession) GetMigrationTarget() (common.Address, error) {
	return _Staking.Contract.GetMigrationTarget(&_Staking.CallOpts)
}

// GetMigrationTarget is a free data retrieval call binding the contract method 0x1ddb5552.
//
// Solidity: function getMigrationTarget() view returns(address)
func (_Staking *StakingCallerSession) GetMigrationTarget() (common.Address, error) {
	return _Staking.Contract.GetMigrationTarget(&_Staking.CallOpts)
}

// GetMonitoredFeed is a free data retrieval call binding the contract method 0x83db28a0.
//
// Solidity: function getMonitoredFeed() view returns(address)
func (_Staking *StakingCaller) GetMonitoredFeed(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getMonitoredFeed")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// GetMonitoredFeed is a free data retrieval call binding the contract method 0x83db28a0.
//
// Solidity: function getMonitoredFeed() view returns(address)
func (_Staking *StakingSession) GetMonitoredFeed() (common.Address, error) {
	return _Staking.Contract.GetMonitoredFeed(&_Staking.CallOpts)
}

// GetMonitoredFeed is a free data retrieval call binding the contract method 0x83db28a0.
//
// Solidity: function getMonitoredFeed() view returns(address)
func (_Staking *StakingCallerSession) GetMonitoredFeed() (common.Address, error) {
	return _Staking.Contract.GetMonitoredFeed(&_Staking.CallOpts)
}

// GetOperatorLimits is a free data retrieval call binding the contract method 0x8856398f.
//
// Solidity: function getOperatorLimits() view returns(uint256, uint256)
func (_Staking *StakingCaller) GetOperatorLimits(opts *bind.CallOpts) (*big.Int, *big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getOperatorLimits")

	if err != nil {
		return *new(*big.Int), *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)
	out1 := *abi.ConvertType(out[1], new(*big.Int)).(**big.Int)

	return out0, out1, err

}

// GetOperatorLimits is a free data retrieval call binding the contract method 0x8856398f.
//
// Solidity: function getOperatorLimits() view returns(uint256, uint256)
func (_Staking *StakingSession) GetOperatorLimits() (*big.Int, *big.Int, error) {
	return _Staking.Contract.GetOperatorLimits(&_Staking.CallOpts)
}

// GetOperatorLimits is a free data retrieval call binding the contract method 0x8856398f.
//
// Solidity: function getOperatorLimits() view returns(uint256, uint256)
func (_Staking *StakingCallerSession) GetOperatorLimits() (*big.Int, *big.Int, error) {
	return _Staking.Contract.GetOperatorLimits(&_Staking.CallOpts)
}

// GetRewardRate is a free data retrieval call binding the contract method 0x7e1a3786.
//
// Solidity: function getRewardRate() view returns(uint256)
func (_Staking *StakingCaller) GetRewardRate(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getRewardRate")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetRewardRate is a free data retrieval call binding the contract method 0x7e1a3786.
//
// Solidity: function getRewardRate() view returns(uint256)
func (_Staking *StakingSession) GetRewardRate() (*big.Int, error) {
	return _Staking.Contract.GetRewardRate(&_Staking.CallOpts)
}

// GetRewardRate is a free data retrieval call binding the contract method 0x7e1a3786.
//
// Solidity: function getRewardRate() view returns(uint256)
func (_Staking *StakingCallerSession) GetRewardRate() (*big.Int, error) {
	return _Staking.Contract.GetRewardRate(&_Staking.CallOpts)
}

// GetRewardTimestamps is a free data retrieval call binding the contract method 0x59f01879.
//
// Solidity: function getRewardTimestamps() view returns(uint256, uint256)
func (_Staking *StakingCaller) GetRewardTimestamps(opts *bind.CallOpts) (*big.Int, *big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getRewardTimestamps")

	if err != nil {
		return *new(*big.Int), *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)
	out1 := *abi.ConvertType(out[1], new(*big.Int)).(**big.Int)

	return out0, out1, err

}

// GetRewardTimestamps is a free data retrieval call binding the contract method 0x59f01879.
//
// Solidity: function getRewardTimestamps() view returns(uint256, uint256)
func (_Staking *StakingSession) GetRewardTimestamps() (*big.Int, *big.Int, error) {
	return _Staking.Contract.GetRewardTimestamps(&_Staking.CallOpts)
}

// GetRewardTimestamps is a free data retrieval call binding the contract method 0x59f01879.
//
// Solidity: function getRewardTimestamps() view returns(uint256, uint256)
func (_Staking *StakingCallerSession) GetRewardTimestamps() (*big.Int, *big.Int, error) {
	return _Staking.Contract.GetRewardTimestamps(&_Staking.CallOpts)
}

// GetStake is a free data retrieval call binding the contract method 0x7a766460.
//
// Solidity: function getStake(address staker) view returns(uint256)
func (_Staking *StakingCaller) GetStake(opts *bind.CallOpts, staker common.Address) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getStake", staker)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetStake is a free data retrieval call binding the contract method 0x7a766460.
//
// Solidity: function getStake(address staker) view returns(uint256)
func (_Staking *StakingSession) GetStake(staker common.Address) (*big.Int, error) {
	return _Staking.Contract.GetStake(&_Staking.CallOpts, staker)
}

// GetStake is a free data retrieval call binding the contract method 0x7a766460.
//
// Solidity: function getStake(address staker) view returns(uint256)
func (_Staking *StakingCallerSession) GetStake(staker common.Address) (*big.Int, error) {
	return _Staking.Contract.GetStake(&_Staking.CallOpts, staker)
}

// GetTotalCommunityStakedAmount is a free data retrieval call binding the contract method 0x049b2ca0.
//
// Solidity: function getTotalCommunityStakedAmount() view returns(uint256)
func (_Staking *StakingCaller) GetTotalCommunityStakedAmount(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getTotalCommunityStakedAmount")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetTotalCommunityStakedAmount is a free data retrieval call binding the contract method 0x049b2ca0.
//
// Solidity: function getTotalCommunityStakedAmount() view returns(uint256)
func (_Staking *StakingSession) GetTotalCommunityStakedAmount() (*big.Int, error) {
	return _Staking.Contract.GetTotalCommunityStakedAmount(&_Staking.CallOpts)
}

// GetTotalCommunityStakedAmount is a free data retrieval call binding the contract method 0x049b2ca0.
//
// Solidity: function getTotalCommunityStakedAmount() view returns(uint256)
func (_Staking *StakingCallerSession) GetTotalCommunityStakedAmount() (*big.Int, error) {
	return _Staking.Contract.GetTotalCommunityStakedAmount(&_Staking.CallOpts)
}

// GetTotalDelegatedAmount is a free data retrieval call binding the contract method 0xa7a2f5aa.
//
// Solidity: function getTotalDelegatedAmount() view returns(uint256)
func (_Staking *StakingCaller) GetTotalDelegatedAmount(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getTotalDelegatedAmount")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetTotalDelegatedAmount is a free data retrieval call binding the contract method 0xa7a2f5aa.
//
// Solidity: function getTotalDelegatedAmount() view returns(uint256)
func (_Staking *StakingSession) GetTotalDelegatedAmount() (*big.Int, error) {
	return _Staking.Contract.GetTotalDelegatedAmount(&_Staking.CallOpts)
}

// GetTotalDelegatedAmount is a free data retrieval call binding the contract method 0xa7a2f5aa.
//
// Solidity: function getTotalDelegatedAmount() view returns(uint256)
func (_Staking *StakingCallerSession) GetTotalDelegatedAmount() (*big.Int, error) {
	return _Staking.Contract.GetTotalDelegatedAmount(&_Staking.CallOpts)
}

// GetTotalRemovedAmount is a free data retrieval call binding the contract method 0x8019e7d0.
//
// Solidity: function getTotalRemovedAmount() view returns(uint256)
func (_Staking *StakingCaller) GetTotalRemovedAmount(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getTotalRemovedAmount")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetTotalRemovedAmount is a free data retrieval call binding the contract method 0x8019e7d0.
//
// Solidity: function getTotalRemovedAmount() view returns(uint256)
func (_Staking *StakingSession) GetTotalRemovedAmount() (*big.Int, error) {
	return _Staking.Contract.GetTotalRemovedAmount(&_Staking.CallOpts)
}

// GetTotalRemovedAmount is a free data retrieval call binding the contract method 0x8019e7d0.
//
// Solidity: function getTotalRemovedAmount() view returns(uint256)
func (_Staking *StakingCallerSession) GetTotalRemovedAmount() (*big.Int, error) {
	return _Staking.Contract.GetTotalRemovedAmount(&_Staking.CallOpts)
}

// GetTotalStakedAmount is a free data retrieval call binding the contract method 0x38adb6f0.
//
// Solidity: function getTotalStakedAmount() view returns(uint256)
func (_Staking *StakingCaller) GetTotalStakedAmount(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "getTotalStakedAmount")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetTotalStakedAmount is a free data retrieval call binding the contract method 0x38adb6f0.
//
// Solidity: function getTotalStakedAmount() view returns(uint256)
func (_Staking *StakingSession) GetTotalStakedAmount() (*big.Int, error) {
	return _Staking.Contract.GetTotalStakedAmount(&_Staking.CallOpts)
}

// GetTotalStakedAmount is a free data retrieval call binding the contract method 0x38adb6f0.
//
// Solidity: function getTotalStakedAmount() view returns(uint256)
func (_Staking *StakingCallerSession) GetTotalStakedAmount() (*big.Int, error) {
	return _Staking.Contract.GetTotalStakedAmount(&_Staking.CallOpts)
}

// HasAccess is a free data retrieval call binding the contract method 0x9d0a3864.
//
// Solidity: function hasAccess(address staker, bytes32[] proof) view returns(bool)
func (_Staking *StakingCaller) HasAccess(opts *bind.CallOpts, staker common.Address, proof [][32]byte) (bool, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "hasAccess", staker, proof)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// HasAccess is a free data retrieval call binding the contract method 0x9d0a3864.
//
// Solidity: function hasAccess(address staker, bytes32[] proof) view returns(bool)
func (_Staking *StakingSession) HasAccess(staker common.Address, proof [][32]byte) (bool, error) {
	return _Staking.Contract.HasAccess(&_Staking.CallOpts, staker, proof)
}

// HasAccess is a free data retrieval call binding the contract method 0x9d0a3864.
//
// Solidity: function hasAccess(address staker, bytes32[] proof) view returns(bool)
func (_Staking *StakingCallerSession) HasAccess(staker common.Address, proof [][32]byte) (bool, error) {
	return _Staking.Contract.HasAccess(&_Staking.CallOpts, staker, proof)
}

// IsActive is a free data retrieval call binding the contract method 0x22f3e2d4.
//
// Solidity: function isActive() view returns(bool)
func (_Staking *StakingCaller) IsActive(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "isActive")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// IsActive is a free data retrieval call binding the contract method 0x22f3e2d4.
//
// Solidity: function isActive() view returns(bool)
func (_Staking *StakingSession) IsActive() (bool, error) {
	return _Staking.Contract.IsActive(&_Staking.CallOpts)
}

// IsActive is a free data retrieval call binding the contract method 0x22f3e2d4.
//
// Solidity: function isActive() view returns(bool)
func (_Staking *StakingCallerSession) IsActive() (bool, error) {
	return _Staking.Contract.IsActive(&_Staking.CallOpts)
}

// IsOperator is a free data retrieval call binding the contract method 0x6d70f7ae.
//
// Solidity: function isOperator(address staker) view returns(bool)
func (_Staking *StakingCaller) IsOperator(opts *bind.CallOpts, staker common.Address) (bool, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "isOperator", staker)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// IsOperator is a free data retrieval call binding the contract method 0x6d70f7ae.
//
// Solidity: function isOperator(address staker) view returns(bool)
func (_Staking *StakingSession) IsOperator(staker common.Address) (bool, error) {
	return _Staking.Contract.IsOperator(&_Staking.CallOpts, staker)
}

// IsOperator is a free data retrieval call binding the contract method 0x6d70f7ae.
//
// Solidity: function isOperator(address staker) view returns(bool)
func (_Staking *StakingCallerSession) IsOperator(staker common.Address) (bool, error) {
	return _Staking.Contract.IsOperator(&_Staking.CallOpts, staker)
}

// IsPaused is a free data retrieval call binding the contract method 0xb187bd26.
//
// Solidity: function isPaused() view returns(bool)
func (_Staking *StakingCaller) IsPaused(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "isPaused")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// IsPaused is a free data retrieval call binding the contract method 0xb187bd26.
//
// Solidity: function isPaused() view returns(bool)
func (_Staking *StakingSession) IsPaused() (bool, error) {
	return _Staking.Contract.IsPaused(&_Staking.CallOpts)
}

// IsPaused is a free data retrieval call binding the contract method 0xb187bd26.
//
// Solidity: function isPaused() view returns(bool)
func (_Staking *StakingCallerSession) IsPaused() (bool, error) {
	return _Staking.Contract.IsPaused(&_Staking.CallOpts)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_Staking *StakingCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_Staking *StakingSession) Owner() (common.Address, error) {
	return _Staking.Contract.Owner(&_Staking.CallOpts)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_Staking *StakingCallerSession) Owner() (common.Address, error) {
	return _Staking.Contract.Owner(&_Staking.CallOpts)
}

// Paused is a free data retrieval call binding the contract method 0x5c975abb.
//
// Solidity: function paused() view returns(bool)
func (_Staking *StakingCaller) Paused(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "paused")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// Paused is a free data retrieval call binding the contract method 0x5c975abb.
//
// Solidity: function paused() view returns(bool)
func (_Staking *StakingSession) Paused() (bool, error) {
	return _Staking.Contract.Paused(&_Staking.CallOpts)
}

// Paused is a free data retrieval call binding the contract method 0x5c975abb.
//
// Solidity: function paused() view returns(bool)
func (_Staking *StakingCallerSession) Paused() (bool, error) {
	return _Staking.Contract.Paused(&_Staking.CallOpts)
}

// TypeAndVersion is a free data retrieval call binding the contract method 0x181f5a77.
//
// Solidity: function typeAndVersion() pure returns(string)
func (_Staking *StakingCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _Staking.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

// TypeAndVersion is a free data retrieval call binding the contract method 0x181f5a77.
//
// Solidity: function typeAndVersion() pure returns(string)
func (_Staking *StakingSession) TypeAndVersion() (string, error) {
	return _Staking.Contract.TypeAndVersion(&_Staking.CallOpts)
}

// TypeAndVersion is a free data retrieval call binding the contract method 0x181f5a77.
//
// Solidity: function typeAndVersion() pure returns(string)
func (_Staking *StakingCallerSession) TypeAndVersion() (string, error) {
	return _Staking.Contract.TypeAndVersion(&_Staking.CallOpts)
}

// AcceptMigrationTarget is a paid mutator transaction binding the contract method 0xe937fdaa.
//
// Solidity: function acceptMigrationTarget() returns()
func (_Staking *StakingTransactor) AcceptMigrationTarget(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "acceptMigrationTarget")
}

// AcceptMigrationTarget is a paid mutator transaction binding the contract method 0xe937fdaa.
//
// Solidity: function acceptMigrationTarget() returns()
func (_Staking *StakingSession) AcceptMigrationTarget() (*types.Transaction, error) {
	return _Staking.Contract.AcceptMigrationTarget(&_Staking.TransactOpts)
}

// AcceptMigrationTarget is a paid mutator transaction binding the contract method 0xe937fdaa.
//
// Solidity: function acceptMigrationTarget() returns()
func (_Staking *StakingTransactorSession) AcceptMigrationTarget() (*types.Transaction, error) {
	return _Staking.Contract.AcceptMigrationTarget(&_Staking.TransactOpts)
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_Staking *StakingTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "acceptOwnership")
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_Staking *StakingSession) AcceptOwnership() (*types.Transaction, error) {
	return _Staking.Contract.AcceptOwnership(&_Staking.TransactOpts)
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_Staking *StakingTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _Staking.Contract.AcceptOwnership(&_Staking.TransactOpts)
}

// AddOperators is a paid mutator transaction binding the contract method 0xa07aea1c.
//
// Solidity: function addOperators(address[] operators) returns()
func (_Staking *StakingTransactor) AddOperators(opts *bind.TransactOpts, operators []common.Address) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "addOperators", operators)
}

// AddOperators is a paid mutator transaction binding the contract method 0xa07aea1c.
//
// Solidity: function addOperators(address[] operators) returns()
func (_Staking *StakingSession) AddOperators(operators []common.Address) (*types.Transaction, error) {
	return _Staking.Contract.AddOperators(&_Staking.TransactOpts, operators)
}

// AddOperators is a paid mutator transaction binding the contract method 0xa07aea1c.
//
// Solidity: function addOperators(address[] operators) returns()
func (_Staking *StakingTransactorSession) AddOperators(operators []common.Address) (*types.Transaction, error) {
	return _Staking.Contract.AddOperators(&_Staking.TransactOpts, operators)
}

// AddReward is a paid mutator transaction binding the contract method 0x74de4ec4.
//
// Solidity: function addReward(uint256 amount) returns()
func (_Staking *StakingTransactor) AddReward(opts *bind.TransactOpts, amount *big.Int) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "addReward", amount)
}

// AddReward is a paid mutator transaction binding the contract method 0x74de4ec4.
//
// Solidity: function addReward(uint256 amount) returns()
func (_Staking *StakingSession) AddReward(amount *big.Int) (*types.Transaction, error) {
	return _Staking.Contract.AddReward(&_Staking.TransactOpts, amount)
}

// AddReward is a paid mutator transaction binding the contract method 0x74de4ec4.
//
// Solidity: function addReward(uint256 amount) returns()
func (_Staking *StakingTransactorSession) AddReward(amount *big.Int) (*types.Transaction, error) {
	return _Staking.Contract.AddReward(&_Staking.TransactOpts, amount)
}

// ChangeRewardRate is a paid mutator transaction binding the contract method 0x74f237c4.
//
// Solidity: function changeRewardRate(uint256 newRate) returns()
func (_Staking *StakingTransactor) ChangeRewardRate(opts *bind.TransactOpts, newRate *big.Int) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "changeRewardRate", newRate)
}

// ChangeRewardRate is a paid mutator transaction binding the contract method 0x74f237c4.
//
// Solidity: function changeRewardRate(uint256 newRate) returns()
func (_Staking *StakingSession) ChangeRewardRate(newRate *big.Int) (*types.Transaction, error) {
	return _Staking.Contract.ChangeRewardRate(&_Staking.TransactOpts, newRate)
}

// ChangeRewardRate is a paid mutator transaction binding the contract method 0x74f237c4.
//
// Solidity: function changeRewardRate(uint256 newRate) returns()
func (_Staking *StakingTransactorSession) ChangeRewardRate(newRate *big.Int) (*types.Transaction, error) {
	return _Staking.Contract.ChangeRewardRate(&_Staking.TransactOpts, newRate)
}

// Conclude is a paid mutator transaction binding the contract method 0xe5f92973.
//
// Solidity: function conclude() returns()
func (_Staking *StakingTransactor) Conclude(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "conclude")
}

// Conclude is a paid mutator transaction binding the contract method 0xe5f92973.
//
// Solidity: function conclude() returns()
func (_Staking *StakingSession) Conclude() (*types.Transaction, error) {
	return _Staking.Contract.Conclude(&_Staking.TransactOpts)
}

// Conclude is a paid mutator transaction binding the contract method 0xe5f92973.
//
// Solidity: function conclude() returns()
func (_Staking *StakingTransactorSession) Conclude() (*types.Transaction, error) {
	return _Staking.Contract.Conclude(&_Staking.TransactOpts)
}

// EmergencyPause is a paid mutator transaction binding the contract method 0x51858e27.
//
// Solidity: function emergencyPause() returns()
func (_Staking *StakingTransactor) EmergencyPause(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "emergencyPause")
}

// EmergencyPause is a paid mutator transaction binding the contract method 0x51858e27.
//
// Solidity: function emergencyPause() returns()
func (_Staking *StakingSession) EmergencyPause() (*types.Transaction, error) {
	return _Staking.Contract.EmergencyPause(&_Staking.TransactOpts)
}

// EmergencyPause is a paid mutator transaction binding the contract method 0x51858e27.
//
// Solidity: function emergencyPause() returns()
func (_Staking *StakingTransactorSession) EmergencyPause() (*types.Transaction, error) {
	return _Staking.Contract.EmergencyPause(&_Staking.TransactOpts)
}

// EmergencyUnpause is a paid mutator transaction binding the contract method 0x4a4e3bd5.
//
// Solidity: function emergencyUnpause() returns()
func (_Staking *StakingTransactor) EmergencyUnpause(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "emergencyUnpause")
}

// EmergencyUnpause is a paid mutator transaction binding the contract method 0x4a4e3bd5.
//
// Solidity: function emergencyUnpause() returns()
func (_Staking *StakingSession) EmergencyUnpause() (*types.Transaction, error) {
	return _Staking.Contract.EmergencyUnpause(&_Staking.TransactOpts)
}

// EmergencyUnpause is a paid mutator transaction binding the contract method 0x4a4e3bd5.
//
// Solidity: function emergencyUnpause() returns()
func (_Staking *StakingTransactorSession) EmergencyUnpause() (*types.Transaction, error) {
	return _Staking.Contract.EmergencyUnpause(&_Staking.TransactOpts)
}

// Migrate is a paid mutator transaction binding the contract method 0x8932a90d.
//
// Solidity: function migrate(bytes data) returns()
func (_Staking *StakingTransactor) Migrate(opts *bind.TransactOpts, data []byte) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "migrate", data)
}

// Migrate is a paid mutator transaction binding the contract method 0x8932a90d.
//
// Solidity: function migrate(bytes data) returns()
func (_Staking *StakingSession) Migrate(data []byte) (*types.Transaction, error) {
	return _Staking.Contract.Migrate(&_Staking.TransactOpts, data)
}

// Migrate is a paid mutator transaction binding the contract method 0x8932a90d.
//
// Solidity: function migrate(bytes data) returns()
func (_Staking *StakingTransactorSession) Migrate(data []byte) (*types.Transaction, error) {
	return _Staking.Contract.Migrate(&_Staking.TransactOpts, data)
}

// OnTokenTransfer is a paid mutator transaction binding the contract method 0xa4c0ed36.
//
// Solidity: function onTokenTransfer(address sender, uint256 amount, bytes data) returns()
func (_Staking *StakingTransactor) OnTokenTransfer(opts *bind.TransactOpts, sender common.Address, amount *big.Int, data []byte) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "onTokenTransfer", sender, amount, data)
}

// OnTokenTransfer is a paid mutator transaction binding the contract method 0xa4c0ed36.
//
// Solidity: function onTokenTransfer(address sender, uint256 amount, bytes data) returns()
func (_Staking *StakingSession) OnTokenTransfer(sender common.Address, amount *big.Int, data []byte) (*types.Transaction, error) {
	return _Staking.Contract.OnTokenTransfer(&_Staking.TransactOpts, sender, amount, data)
}

// OnTokenTransfer is a paid mutator transaction binding the contract method 0xa4c0ed36.
//
// Solidity: function onTokenTransfer(address sender, uint256 amount, bytes data) returns()
func (_Staking *StakingTransactorSession) OnTokenTransfer(sender common.Address, amount *big.Int, data []byte) (*types.Transaction, error) {
	return _Staking.Contract.OnTokenTransfer(&_Staking.TransactOpts, sender, amount, data)
}

// ProposeMigrationTarget is a paid mutator transaction binding the contract method 0x63b2c85a.
//
// Solidity: function proposeMigrationTarget(address migrationTarget) returns()
func (_Staking *StakingTransactor) ProposeMigrationTarget(opts *bind.TransactOpts, migrationTarget common.Address) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "proposeMigrationTarget", migrationTarget)
}

// ProposeMigrationTarget is a paid mutator transaction binding the contract method 0x63b2c85a.
//
// Solidity: function proposeMigrationTarget(address migrationTarget) returns()
func (_Staking *StakingSession) ProposeMigrationTarget(migrationTarget common.Address) (*types.Transaction, error) {
	return _Staking.Contract.ProposeMigrationTarget(&_Staking.TransactOpts, migrationTarget)
}

// ProposeMigrationTarget is a paid mutator transaction binding the contract method 0x63b2c85a.
//
// Solidity: function proposeMigrationTarget(address migrationTarget) returns()
func (_Staking *StakingTransactorSession) ProposeMigrationTarget(migrationTarget common.Address) (*types.Transaction, error) {
	return _Staking.Contract.ProposeMigrationTarget(&_Staking.TransactOpts, migrationTarget)
}

// RaiseAlert is a paid mutator transaction binding the contract method 0xda9c732f.
//
// Solidity: function raiseAlert() returns()
func (_Staking *StakingTransactor) RaiseAlert(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "raiseAlert")
}

// RaiseAlert is a paid mutator transaction binding the contract method 0xda9c732f.
//
// Solidity: function raiseAlert() returns()
func (_Staking *StakingSession) RaiseAlert() (*types.Transaction, error) {
	return _Staking.Contract.RaiseAlert(&_Staking.TransactOpts)
}

// RaiseAlert is a paid mutator transaction binding the contract method 0xda9c732f.
//
// Solidity: function raiseAlert() returns()
func (_Staking *StakingTransactorSession) RaiseAlert() (*types.Transaction, error) {
	return _Staking.Contract.RaiseAlert(&_Staking.TransactOpts)
}

// RemoveOperators is a paid mutator transaction binding the contract method 0xd365a377.
//
// Solidity: function removeOperators(address[] operators) returns()
func (_Staking *StakingTransactor) RemoveOperators(opts *bind.TransactOpts, operators []common.Address) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "removeOperators", operators)
}

// RemoveOperators is a paid mutator transaction binding the contract method 0xd365a377.
//
// Solidity: function removeOperators(address[] operators) returns()
func (_Staking *StakingSession) RemoveOperators(operators []common.Address) (*types.Transaction, error) {
	return _Staking.Contract.RemoveOperators(&_Staking.TransactOpts, operators)
}

// RemoveOperators is a paid mutator transaction binding the contract method 0xd365a377.
//
// Solidity: function removeOperators(address[] operators) returns()
func (_Staking *StakingTransactorSession) RemoveOperators(operators []common.Address) (*types.Transaction, error) {
	return _Staking.Contract.RemoveOperators(&_Staking.TransactOpts, operators)
}

// SetFeedOperators is a paid mutator transaction binding the contract method 0xbfbd9b1b.
//
// Solidity: function setFeedOperators(address[] operators) returns()
func (_Staking *StakingTransactor) SetFeedOperators(opts *bind.TransactOpts, operators []common.Address) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "setFeedOperators", operators)
}

// SetFeedOperators is a paid mutator transaction binding the contract method 0xbfbd9b1b.
//
// Solidity: function setFeedOperators(address[] operators) returns()
func (_Staking *StakingSession) SetFeedOperators(operators []common.Address) (*types.Transaction, error) {
	return _Staking.Contract.SetFeedOperators(&_Staking.TransactOpts, operators)
}

// SetFeedOperators is a paid mutator transaction binding the contract method 0xbfbd9b1b.
//
// Solidity: function setFeedOperators(address[] operators) returns()
func (_Staking *StakingTransactorSession) SetFeedOperators(operators []common.Address) (*types.Transaction, error) {
	return _Staking.Contract.SetFeedOperators(&_Staking.TransactOpts, operators)
}

// SetMerkleRoot is a paid mutator transaction binding the contract method 0x7cb64759.
//
// Solidity: function setMerkleRoot(bytes32 newMerkleRoot) returns()
func (_Staking *StakingTransactor) SetMerkleRoot(opts *bind.TransactOpts, newMerkleRoot [32]byte) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "setMerkleRoot", newMerkleRoot)
}

// SetMerkleRoot is a paid mutator transaction binding the contract method 0x7cb64759.
//
// Solidity: function setMerkleRoot(bytes32 newMerkleRoot) returns()
func (_Staking *StakingSession) SetMerkleRoot(newMerkleRoot [32]byte) (*types.Transaction, error) {
	return _Staking.Contract.SetMerkleRoot(&_Staking.TransactOpts, newMerkleRoot)
}

// SetMerkleRoot is a paid mutator transaction binding the contract method 0x7cb64759.
//
// Solidity: function setMerkleRoot(bytes32 newMerkleRoot) returns()
func (_Staking *StakingTransactorSession) SetMerkleRoot(newMerkleRoot [32]byte) (*types.Transaction, error) {
	return _Staking.Contract.SetMerkleRoot(&_Staking.TransactOpts, newMerkleRoot)
}

// SetPoolConfig is a paid mutator transaction binding the contract method 0x8a44f337.
//
// Solidity: function setPoolConfig(uint256 maxPoolSize, uint256 maxCommunityStakeAmount, uint256 maxOperatorStakeAmount) returns()
func (_Staking *StakingTransactor) SetPoolConfig(opts *bind.TransactOpts, maxPoolSize *big.Int, maxCommunityStakeAmount *big.Int, maxOperatorStakeAmount *big.Int) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "setPoolConfig", maxPoolSize, maxCommunityStakeAmount, maxOperatorStakeAmount)
}

// SetPoolConfig is a paid mutator transaction binding the contract method 0x8a44f337.
//
// Solidity: function setPoolConfig(uint256 maxPoolSize, uint256 maxCommunityStakeAmount, uint256 maxOperatorStakeAmount) returns()
func (_Staking *StakingSession) SetPoolConfig(maxPoolSize *big.Int, maxCommunityStakeAmount *big.Int, maxOperatorStakeAmount *big.Int) (*types.Transaction, error) {
	return _Staking.Contract.SetPoolConfig(&_Staking.TransactOpts, maxPoolSize, maxCommunityStakeAmount, maxOperatorStakeAmount)
}

// SetPoolConfig is a paid mutator transaction binding the contract method 0x8a44f337.
//
// Solidity: function setPoolConfig(uint256 maxPoolSize, uint256 maxCommunityStakeAmount, uint256 maxOperatorStakeAmount) returns()
func (_Staking *StakingTransactorSession) SetPoolConfig(maxPoolSize *big.Int, maxCommunityStakeAmount *big.Int, maxOperatorStakeAmount *big.Int) (*types.Transaction, error) {
	return _Staking.Contract.SetPoolConfig(&_Staking.TransactOpts, maxPoolSize, maxCommunityStakeAmount, maxOperatorStakeAmount)
}

// Start is a paid mutator transaction binding the contract method 0x8fb4b573.
//
// Solidity: function start(uint256 amount, uint256 initialRewardRate) returns()
func (_Staking *StakingTransactor) Start(opts *bind.TransactOpts, amount *big.Int, initialRewardRate *big.Int) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "start", amount, initialRewardRate)
}

// Start is a paid mutator transaction binding the contract method 0x8fb4b573.
//
// Solidity: function start(uint256 amount, uint256 initialRewardRate) returns()
func (_Staking *StakingSession) Start(amount *big.Int, initialRewardRate *big.Int) (*types.Transaction, error) {
	return _Staking.Contract.Start(&_Staking.TransactOpts, amount, initialRewardRate)
}

// Start is a paid mutator transaction binding the contract method 0x8fb4b573.
//
// Solidity: function start(uint256 amount, uint256 initialRewardRate) returns()
func (_Staking *StakingTransactorSession) Start(amount *big.Int, initialRewardRate *big.Int) (*types.Transaction, error) {
	return _Staking.Contract.Start(&_Staking.TransactOpts, amount, initialRewardRate)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address to) returns()
func (_Staking *StakingTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "transferOwnership", to)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address to) returns()
func (_Staking *StakingSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _Staking.Contract.TransferOwnership(&_Staking.TransactOpts, to)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address to) returns()
func (_Staking *StakingTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _Staking.Contract.TransferOwnership(&_Staking.TransactOpts, to)
}

// Unstake is a paid mutator transaction binding the contract method 0x2def6620.
//
// Solidity: function unstake() returns()
func (_Staking *StakingTransactor) Unstake(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "unstake")
}

// Unstake is a paid mutator transaction binding the contract method 0x2def6620.
//
// Solidity: function unstake() returns()
func (_Staking *StakingSession) Unstake() (*types.Transaction, error) {
	return _Staking.Contract.Unstake(&_Staking.TransactOpts)
}

// Unstake is a paid mutator transaction binding the contract method 0x2def6620.
//
// Solidity: function unstake() returns()
func (_Staking *StakingTransactorSession) Unstake() (*types.Transaction, error) {
	return _Staking.Contract.Unstake(&_Staking.TransactOpts)
}

// WithdrawRemovedStake is a paid mutator transaction binding the contract method 0x5aa6e013.
//
// Solidity: function withdrawRemovedStake() returns()
func (_Staking *StakingTransactor) WithdrawRemovedStake(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "withdrawRemovedStake")
}

// WithdrawRemovedStake is a paid mutator transaction binding the contract method 0x5aa6e013.
//
// Solidity: function withdrawRemovedStake() returns()
func (_Staking *StakingSession) WithdrawRemovedStake() (*types.Transaction, error) {
	return _Staking.Contract.WithdrawRemovedStake(&_Staking.TransactOpts)
}

// WithdrawRemovedStake is a paid mutator transaction binding the contract method 0x5aa6e013.
//
// Solidity: function withdrawRemovedStake() returns()
func (_Staking *StakingTransactorSession) WithdrawRemovedStake() (*types.Transaction, error) {
	return _Staking.Contract.WithdrawRemovedStake(&_Staking.TransactOpts)
}

// WithdrawUnusedReward is a paid mutator transaction binding the contract method 0xebdb56f3.
//
// Solidity: function withdrawUnusedReward() returns()
func (_Staking *StakingTransactor) WithdrawUnusedReward(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Staking.contract.Transact(opts, "withdrawUnusedReward")
}

// WithdrawUnusedReward is a paid mutator transaction binding the contract method 0xebdb56f3.
//
// Solidity: function withdrawUnusedReward() returns()
func (_Staking *StakingSession) WithdrawUnusedReward() (*types.Transaction, error) {
	return _Staking.Contract.WithdrawUnusedReward(&_Staking.TransactOpts)
}

// WithdrawUnusedReward is a paid mutator transaction binding the contract method 0xebdb56f3.
//
// Solidity: function withdrawUnusedReward() returns()
func (_Staking *StakingTransactorSession) WithdrawUnusedReward() (*types.Transaction, error) {
	return _Staking.Contract.WithdrawUnusedReward(&_Staking.TransactOpts)
}

// StakingAlertRaisedIterator is returned from FilterAlertRaised and is used to iterate over the raw logs and unpacked data for AlertRaised events raised by the Staking contract.
type StakingAlertRaisedIterator struct {
	Event *StakingAlertRaised // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingAlertRaisedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingAlertRaised)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingAlertRaised)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingAlertRaisedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingAlertRaisedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingAlertRaised represents a AlertRaised event raised by the Staking contract.
type StakingAlertRaised struct {
	Alerter      common.Address
	RoundId      *big.Int
	RewardAmount *big.Int
	Raw          types.Log // Blockchain specific contextual infos
}

// FilterAlertRaised is a free log retrieval operation binding the contract event 0xd2720e8f454493f612cc97499fe8cbce7fa4d4c18d346fe7104e9042df1c1edd.
//
// Solidity: event AlertRaised(address alerter, uint256 roundId, uint256 rewardAmount)
func (_Staking *StakingFilterer) FilterAlertRaised(opts *bind.FilterOpts) (*StakingAlertRaisedIterator, error) {

	logs, sub, err := _Staking.contract.FilterLogs(opts, "AlertRaised")
	if err != nil {
		return nil, err
	}
	return &StakingAlertRaisedIterator{contract: _Staking.contract, event: "AlertRaised", logs: logs, sub: sub}, nil
}

// WatchAlertRaised is a free log subscription operation binding the contract event 0xd2720e8f454493f612cc97499fe8cbce7fa4d4c18d346fe7104e9042df1c1edd.
//
// Solidity: event AlertRaised(address alerter, uint256 roundId, uint256 rewardAmount)
func (_Staking *StakingFilterer) WatchAlertRaised(opts *bind.WatchOpts, sink chan<- *StakingAlertRaised) (event.Subscription, error) {

	logs, sub, err := _Staking.contract.WatchLogs(opts, "AlertRaised")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingAlertRaised)
				if err := _Staking.contract.UnpackLog(event, "AlertRaised", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseAlertRaised is a log parse operation binding the contract event 0xd2720e8f454493f612cc97499fe8cbce7fa4d4c18d346fe7104e9042df1c1edd.
//
// Solidity: event AlertRaised(address alerter, uint256 roundId, uint256 rewardAmount)
func (_Staking *StakingFilterer) ParseAlertRaised(log types.Log) (*StakingAlertRaised, error) {
	event := new(StakingAlertRaised)
	if err := _Staking.contract.UnpackLog(event, "AlertRaised", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingMerkleRootChangedIterator is returned from FilterMerkleRootChanged and is used to iterate over the raw logs and unpacked data for MerkleRootChanged events raised by the Staking contract.
type StakingMerkleRootChangedIterator struct {
	Event *StakingMerkleRootChanged // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingMerkleRootChangedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingMerkleRootChanged)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingMerkleRootChanged)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingMerkleRootChangedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingMerkleRootChangedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingMerkleRootChanged represents a MerkleRootChanged event raised by the Staking contract.
type StakingMerkleRootChanged struct {
	NewMerkleRoot [32]byte
	Raw           types.Log // Blockchain specific contextual infos
}

// FilterMerkleRootChanged is a free log retrieval operation binding the contract event 0x1b930366dfeaa7eb3b325021e4ae81e36527063452ee55b86c95f85b36f4c31c.
//
// Solidity: event MerkleRootChanged(bytes32 newMerkleRoot)
func (_Staking *StakingFilterer) FilterMerkleRootChanged(opts *bind.FilterOpts) (*StakingMerkleRootChangedIterator, error) {

	logs, sub, err := _Staking.contract.FilterLogs(opts, "MerkleRootChanged")
	if err != nil {
		return nil, err
	}
	return &StakingMerkleRootChangedIterator{contract: _Staking.contract, event: "MerkleRootChanged", logs: logs, sub: sub}, nil
}

// WatchMerkleRootChanged is a free log subscription operation binding the contract event 0x1b930366dfeaa7eb3b325021e4ae81e36527063452ee55b86c95f85b36f4c31c.
//
// Solidity: event MerkleRootChanged(bytes32 newMerkleRoot)
func (_Staking *StakingFilterer) WatchMerkleRootChanged(opts *bind.WatchOpts, sink chan<- *StakingMerkleRootChanged) (event.Subscription, error) {

	logs, sub, err := _Staking.contract.WatchLogs(opts, "MerkleRootChanged")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingMerkleRootChanged)
				if err := _Staking.contract.UnpackLog(event, "MerkleRootChanged", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseMerkleRootChanged is a log parse operation binding the contract event 0x1b930366dfeaa7eb3b325021e4ae81e36527063452ee55b86c95f85b36f4c31c.
//
// Solidity: event MerkleRootChanged(bytes32 newMerkleRoot)
func (_Staking *StakingFilterer) ParseMerkleRootChanged(log types.Log) (*StakingMerkleRootChanged, error) {
	event := new(StakingMerkleRootChanged)
	if err := _Staking.contract.UnpackLog(event, "MerkleRootChanged", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingMigratedIterator is returned from FilterMigrated and is used to iterate over the raw logs and unpacked data for Migrated events raised by the Staking contract.
type StakingMigratedIterator struct {
	Event *StakingMigrated // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingMigratedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingMigrated)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingMigrated)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingMigratedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingMigratedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingMigrated represents a Migrated event raised by the Staking contract.
type StakingMigrated struct {
	Staker           common.Address
	Principal        *big.Int
	BaseReward       *big.Int
	DelegationReward *big.Int
	Data             []byte
	Raw              types.Log // Blockchain specific contextual infos
}

// FilterMigrated is a free log retrieval operation binding the contract event 0x667838b33bdc898470de09e0e746990f2adc11b965b7fe6828e502ebc39e0434.
//
// Solidity: event Migrated(address staker, uint256 principal, uint256 baseReward, uint256 delegationReward, bytes data)
func (_Staking *StakingFilterer) FilterMigrated(opts *bind.FilterOpts) (*StakingMigratedIterator, error) {

	logs, sub, err := _Staking.contract.FilterLogs(opts, "Migrated")
	if err != nil {
		return nil, err
	}
	return &StakingMigratedIterator{contract: _Staking.contract, event: "Migrated", logs: logs, sub: sub}, nil
}

// WatchMigrated is a free log subscription operation binding the contract event 0x667838b33bdc898470de09e0e746990f2adc11b965b7fe6828e502ebc39e0434.
//
// Solidity: event Migrated(address staker, uint256 principal, uint256 baseReward, uint256 delegationReward, bytes data)
func (_Staking *StakingFilterer) WatchMigrated(opts *bind.WatchOpts, sink chan<- *StakingMigrated) (event.Subscription, error) {

	logs, sub, err := _Staking.contract.WatchLogs(opts, "Migrated")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingMigrated)
				if err := _Staking.contract.UnpackLog(event, "Migrated", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseMigrated is a log parse operation binding the contract event 0x667838b33bdc898470de09e0e746990f2adc11b965b7fe6828e502ebc39e0434.
//
// Solidity: event Migrated(address staker, uint256 principal, uint256 baseReward, uint256 delegationReward, bytes data)
func (_Staking *StakingFilterer) ParseMigrated(log types.Log) (*StakingMigrated, error) {
	event := new(StakingMigrated)
	if err := _Staking.contract.UnpackLog(event, "Migrated", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingMigrationTargetAcceptedIterator is returned from FilterMigrationTargetAccepted and is used to iterate over the raw logs and unpacked data for MigrationTargetAccepted events raised by the Staking contract.
type StakingMigrationTargetAcceptedIterator struct {
	Event *StakingMigrationTargetAccepted // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingMigrationTargetAcceptedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingMigrationTargetAccepted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingMigrationTargetAccepted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingMigrationTargetAcceptedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingMigrationTargetAcceptedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingMigrationTargetAccepted represents a MigrationTargetAccepted event raised by the Staking contract.
type StakingMigrationTargetAccepted struct {
	MigrationTarget common.Address
	Raw             types.Log // Blockchain specific contextual infos
}

// FilterMigrationTargetAccepted is a free log retrieval operation binding the contract event 0xfa33c052bbee754f3c0482a89962daffe749191fa33c696a61e947fbfd68bd84.
//
// Solidity: event MigrationTargetAccepted(address migrationTarget)
func (_Staking *StakingFilterer) FilterMigrationTargetAccepted(opts *bind.FilterOpts) (*StakingMigrationTargetAcceptedIterator, error) {

	logs, sub, err := _Staking.contract.FilterLogs(opts, "MigrationTargetAccepted")
	if err != nil {
		return nil, err
	}
	return &StakingMigrationTargetAcceptedIterator{contract: _Staking.contract, event: "MigrationTargetAccepted", logs: logs, sub: sub}, nil
}

// WatchMigrationTargetAccepted is a free log subscription operation binding the contract event 0xfa33c052bbee754f3c0482a89962daffe749191fa33c696a61e947fbfd68bd84.
//
// Solidity: event MigrationTargetAccepted(address migrationTarget)
func (_Staking *StakingFilterer) WatchMigrationTargetAccepted(opts *bind.WatchOpts, sink chan<- *StakingMigrationTargetAccepted) (event.Subscription, error) {

	logs, sub, err := _Staking.contract.WatchLogs(opts, "MigrationTargetAccepted")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingMigrationTargetAccepted)
				if err := _Staking.contract.UnpackLog(event, "MigrationTargetAccepted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseMigrationTargetAccepted is a log parse operation binding the contract event 0xfa33c052bbee754f3c0482a89962daffe749191fa33c696a61e947fbfd68bd84.
//
// Solidity: event MigrationTargetAccepted(address migrationTarget)
func (_Staking *StakingFilterer) ParseMigrationTargetAccepted(log types.Log) (*StakingMigrationTargetAccepted, error) {
	event := new(StakingMigrationTargetAccepted)
	if err := _Staking.contract.UnpackLog(event, "MigrationTargetAccepted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingMigrationTargetProposedIterator is returned from FilterMigrationTargetProposed and is used to iterate over the raw logs and unpacked data for MigrationTargetProposed events raised by the Staking contract.
type StakingMigrationTargetProposedIterator struct {
	Event *StakingMigrationTargetProposed // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingMigrationTargetProposedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingMigrationTargetProposed)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingMigrationTargetProposed)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingMigrationTargetProposedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingMigrationTargetProposedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingMigrationTargetProposed represents a MigrationTargetProposed event raised by the Staking contract.
type StakingMigrationTargetProposed struct {
	MigrationTarget common.Address
	Raw             types.Log // Blockchain specific contextual infos
}

// FilterMigrationTargetProposed is a free log retrieval operation binding the contract event 0x5c74c441be501340b2713817a6c6975e6f3d4a4ae39fa1ac0bf75d3c54a0cad3.
//
// Solidity: event MigrationTargetProposed(address migrationTarget)
func (_Staking *StakingFilterer) FilterMigrationTargetProposed(opts *bind.FilterOpts) (*StakingMigrationTargetProposedIterator, error) {

	logs, sub, err := _Staking.contract.FilterLogs(opts, "MigrationTargetProposed")
	if err != nil {
		return nil, err
	}
	return &StakingMigrationTargetProposedIterator{contract: _Staking.contract, event: "MigrationTargetProposed", logs: logs, sub: sub}, nil
}

// WatchMigrationTargetProposed is a free log subscription operation binding the contract event 0x5c74c441be501340b2713817a6c6975e6f3d4a4ae39fa1ac0bf75d3c54a0cad3.
//
// Solidity: event MigrationTargetProposed(address migrationTarget)
func (_Staking *StakingFilterer) WatchMigrationTargetProposed(opts *bind.WatchOpts, sink chan<- *StakingMigrationTargetProposed) (event.Subscription, error) {

	logs, sub, err := _Staking.contract.WatchLogs(opts, "MigrationTargetProposed")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingMigrationTargetProposed)
				if err := _Staking.contract.UnpackLog(event, "MigrationTargetProposed", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseMigrationTargetProposed is a log parse operation binding the contract event 0x5c74c441be501340b2713817a6c6975e6f3d4a4ae39fa1ac0bf75d3c54a0cad3.
//
// Solidity: event MigrationTargetProposed(address migrationTarget)
func (_Staking *StakingFilterer) ParseMigrationTargetProposed(log types.Log) (*StakingMigrationTargetProposed, error) {
	event := new(StakingMigrationTargetProposed)
	if err := _Staking.contract.UnpackLog(event, "MigrationTargetProposed", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingOwnershipTransferRequestedIterator is returned from FilterOwnershipTransferRequested and is used to iterate over the raw logs and unpacked data for OwnershipTransferRequested events raised by the Staking contract.
type StakingOwnershipTransferRequestedIterator struct {
	Event *StakingOwnershipTransferRequested // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingOwnershipTransferRequestedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingOwnershipTransferRequested represents a OwnershipTransferRequested event raised by the Staking contract.
type StakingOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log // Blockchain specific contextual infos
}

// FilterOwnershipTransferRequested is a free log retrieval operation binding the contract event 0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278.
//
// Solidity: event OwnershipTransferRequested(address indexed from, address indexed to)
func (_Staking *StakingFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*StakingOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _Staking.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &StakingOwnershipTransferRequestedIterator{contract: _Staking.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

// WatchOwnershipTransferRequested is a free log subscription operation binding the contract event 0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278.
//
// Solidity: event OwnershipTransferRequested(address indexed from, address indexed to)
func (_Staking *StakingFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *StakingOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _Staking.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingOwnershipTransferRequested)
				if err := _Staking.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseOwnershipTransferRequested is a log parse operation binding the contract event 0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278.
//
// Solidity: event OwnershipTransferRequested(address indexed from, address indexed to)
func (_Staking *StakingFilterer) ParseOwnershipTransferRequested(log types.Log) (*StakingOwnershipTransferRequested, error) {
	event := new(StakingOwnershipTransferRequested)
	if err := _Staking.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingOwnershipTransferredIterator is returned from FilterOwnershipTransferred and is used to iterate over the raw logs and unpacked data for OwnershipTransferred events raised by the Staking contract.
type StakingOwnershipTransferredIterator struct {
	Event *StakingOwnershipTransferred // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingOwnershipTransferredIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingOwnershipTransferredIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingOwnershipTransferred represents a OwnershipTransferred event raised by the Staking contract.
type StakingOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log // Blockchain specific contextual infos
}

// FilterOwnershipTransferred is a free log retrieval operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed from, address indexed to)
func (_Staking *StakingFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*StakingOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _Staking.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &StakingOwnershipTransferredIterator{contract: _Staking.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

// WatchOwnershipTransferred is a free log subscription operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed from, address indexed to)
func (_Staking *StakingFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *StakingOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _Staking.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingOwnershipTransferred)
				if err := _Staking.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseOwnershipTransferred is a log parse operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed from, address indexed to)
func (_Staking *StakingFilterer) ParseOwnershipTransferred(log types.Log) (*StakingOwnershipTransferred, error) {
	event := new(StakingOwnershipTransferred)
	if err := _Staking.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingPausedIterator is returned from FilterPaused and is used to iterate over the raw logs and unpacked data for Paused events raised by the Staking contract.
type StakingPausedIterator struct {
	Event *StakingPaused // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingPausedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingPaused)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingPaused)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingPausedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingPausedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingPaused represents a Paused event raised by the Staking contract.
type StakingPaused struct {
	Account common.Address
	Raw     types.Log // Blockchain specific contextual infos
}

// FilterPaused is a free log retrieval operation binding the contract event 0x62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258.
//
// Solidity: event Paused(address account)
func (_Staking *StakingFilterer) FilterPaused(opts *bind.FilterOpts) (*StakingPausedIterator, error) {

	logs, sub, err := _Staking.contract.FilterLogs(opts, "Paused")
	if err != nil {
		return nil, err
	}
	return &StakingPausedIterator{contract: _Staking.contract, event: "Paused", logs: logs, sub: sub}, nil
}

// WatchPaused is a free log subscription operation binding the contract event 0x62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258.
//
// Solidity: event Paused(address account)
func (_Staking *StakingFilterer) WatchPaused(opts *bind.WatchOpts, sink chan<- *StakingPaused) (event.Subscription, error) {

	logs, sub, err := _Staking.contract.WatchLogs(opts, "Paused")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingPaused)
				if err := _Staking.contract.UnpackLog(event, "Paused", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParsePaused is a log parse operation binding the contract event 0x62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258.
//
// Solidity: event Paused(address account)
func (_Staking *StakingFilterer) ParsePaused(log types.Log) (*StakingPaused, error) {
	event := new(StakingPaused)
	if err := _Staking.contract.UnpackLog(event, "Paused", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingStakedIterator is returned from FilterStaked and is used to iterate over the raw logs and unpacked data for Staked events raised by the Staking contract.
type StakingStakedIterator struct {
	Event *StakingStaked // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingStakedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingStaked)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingStaked)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingStakedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingStakedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingStaked represents a Staked event raised by the Staking contract.
type StakingStaked struct {
	Staker     common.Address
	NewStake   *big.Int
	TotalStake *big.Int
	Raw        types.Log // Blockchain specific contextual infos
}

// FilterStaked is a free log retrieval operation binding the contract event 0x1449c6dd7851abc30abf37f57715f492010519147cc2652fbc38202c18a6ee90.
//
// Solidity: event Staked(address staker, uint256 newStake, uint256 totalStake)
func (_Staking *StakingFilterer) FilterStaked(opts *bind.FilterOpts) (*StakingStakedIterator, error) {

	logs, sub, err := _Staking.contract.FilterLogs(opts, "Staked")
	if err != nil {
		return nil, err
	}
	return &StakingStakedIterator{contract: _Staking.contract, event: "Staked", logs: logs, sub: sub}, nil
}

// WatchStaked is a free log subscription operation binding the contract event 0x1449c6dd7851abc30abf37f57715f492010519147cc2652fbc38202c18a6ee90.
//
// Solidity: event Staked(address staker, uint256 newStake, uint256 totalStake)
func (_Staking *StakingFilterer) WatchStaked(opts *bind.WatchOpts, sink chan<- *StakingStaked) (event.Subscription, error) {

	logs, sub, err := _Staking.contract.WatchLogs(opts, "Staked")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingStaked)
				if err := _Staking.contract.UnpackLog(event, "Staked", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseStaked is a log parse operation binding the contract event 0x1449c6dd7851abc30abf37f57715f492010519147cc2652fbc38202c18a6ee90.
//
// Solidity: event Staked(address staker, uint256 newStake, uint256 totalStake)
func (_Staking *StakingFilterer) ParseStaked(log types.Log) (*StakingStaked, error) {
	event := new(StakingStaked)
	if err := _Staking.contract.UnpackLog(event, "Staked", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingUnpausedIterator is returned from FilterUnpaused and is used to iterate over the raw logs and unpacked data for Unpaused events raised by the Staking contract.
type StakingUnpausedIterator struct {
	Event *StakingUnpaused // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingUnpausedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingUnpaused)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingUnpaused)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingUnpausedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingUnpausedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingUnpaused represents a Unpaused event raised by the Staking contract.
type StakingUnpaused struct {
	Account common.Address
	Raw     types.Log // Blockchain specific contextual infos
}

// FilterUnpaused is a free log retrieval operation binding the contract event 0x5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa.
//
// Solidity: event Unpaused(address account)
func (_Staking *StakingFilterer) FilterUnpaused(opts *bind.FilterOpts) (*StakingUnpausedIterator, error) {

	logs, sub, err := _Staking.contract.FilterLogs(opts, "Unpaused")
	if err != nil {
		return nil, err
	}
	return &StakingUnpausedIterator{contract: _Staking.contract, event: "Unpaused", logs: logs, sub: sub}, nil
}

// WatchUnpaused is a free log subscription operation binding the contract event 0x5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa.
//
// Solidity: event Unpaused(address account)
func (_Staking *StakingFilterer) WatchUnpaused(opts *bind.WatchOpts, sink chan<- *StakingUnpaused) (event.Subscription, error) {

	logs, sub, err := _Staking.contract.WatchLogs(opts, "Unpaused")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingUnpaused)
				if err := _Staking.contract.UnpackLog(event, "Unpaused", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseUnpaused is a log parse operation binding the contract event 0x5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa.
//
// Solidity: event Unpaused(address account)
func (_Staking *StakingFilterer) ParseUnpaused(log types.Log) (*StakingUnpaused, error) {
	event := new(StakingUnpaused)
	if err := _Staking.contract.UnpackLog(event, "Unpaused", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// StakingUnstakedIterator is returned from FilterUnstaked and is used to iterate over the raw logs and unpacked data for Unstaked events raised by the Staking contract.
type StakingUnstakedIterator struct {
	Event *StakingUnstaked // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *StakingUnstakedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(StakingUnstaked)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(StakingUnstaked)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *StakingUnstakedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *StakingUnstakedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// StakingUnstaked represents a Unstaked event raised by the Staking contract.
type StakingUnstaked struct {
	Staker           common.Address
	Principal        *big.Int
	BaseReward       *big.Int
	DelegationReward *big.Int
	Raw              types.Log // Blockchain specific contextual infos
}

// FilterUnstaked is a free log retrieval operation binding the contract event 0x204fccf0d92ed8d48f204adb39b2e81e92bad0dedb93f5716ca9478cfb57de00.
//
// Solidity: event Unstaked(address staker, uint256 principal, uint256 baseReward, uint256 delegationReward)
func (_Staking *StakingFilterer) FilterUnstaked(opts *bind.FilterOpts) (*StakingUnstakedIterator, error) {

	logs, sub, err := _Staking.contract.FilterLogs(opts, "Unstaked")
	if err != nil {
		return nil, err
	}
	return &StakingUnstakedIterator{contract: _Staking.contract, event: "Unstaked", logs: logs, sub: sub}, nil
}

// WatchUnstaked is a free log subscription operation binding the contract event 0x204fccf0d92ed8d48f204adb39b2e81e92bad0dedb93f5716ca9478cfb57de00.
//
// Solidity: event Unstaked(address staker, uint256 principal, uint256 baseReward, uint256 delegationReward)
func (_Staking *StakingFilterer) WatchUnstaked(opts *bind.WatchOpts, sink chan<- *StakingUnstaked) (event.Subscription, error) {

	logs, sub, err := _Staking.contract.WatchLogs(opts, "Unstaked")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(StakingUnstaked)
				if err := _Staking.contract.UnpackLog(event, "Unstaked", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseUnstaked is a log parse operation binding the contract event 0x204fccf0d92ed8d48f204adb39b2e81e92bad0dedb93f5716ca9478cfb57de00.
//
// Solidity: event Unstaked(address staker, uint256 principal, uint256 baseReward, uint256 delegationReward)
func (_Staking *StakingFilterer) ParseUnstaked(log types.Log) (*StakingUnstaked, error) {
	event := new(StakingUnstaked)
	if err := _Staking.contract.UnpackLog(event, "Unstaked", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}
