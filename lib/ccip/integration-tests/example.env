# An example template that you can use for your own .env file for integration test settings
# `source ./integration-tests/.env`

########## General Test Settings ##########
export CHAINLINK_ENV_USER="<PERSON><PERSON><PERSON><PERSON><PERSON>" # Name of the person running the tests (change to your own)
export TEST_LOG_LEVEL="info" # info | debug | trace

########## Soak/Chaos/Load Test Specific Settings ##########
# Remote Runner
export ENV_JOB_IMAGE="image-location/chainlink-tests:test-tag" # Image repo to pull the remote-test-runner image from. Check the Integration Tests workflow.
export DETACH_RUNNER="true" # true 99% of the time, false if you are debugging soak test issues
export TEST_SUITE="soak" # soak | chaos | load

# Slack Notification Settings
export SLACK_API_KEY="xoxb-example-key" # API key used to report soak test results to slack
export SLACK_CHANNEL="C000000000" # Channel ID for the slack bot to post test results
export SLACK_USER="U000000000" # User ID of the person running the soak tests to properly notify them

########## Network Settings ##########
# General EVM Settings, used only for quick prototyping when using GENERAL as the SELECTED_NETWORK
export EVM_NAME="General EVM"
export EVM_CHAIN_ID="1"
export EVM_SIMULATED="false"
export EVM_CHAINLINK_TRANSACTION_LIMIT="5000"
export EVM_TRANSACTION_TIMEOUT="2m"
export EVM_MINIMUM_CONFIRMATIONS="1"
export EVM_GAS_ESTIMATION_BUFFER="1000"