version: 2
project_name: ccip
env:
    - IMG_PRE={{ if index .Env "IMAGE_PREFIX"  }}{{ .Env.IMAGE_PREFIX }}{{ else }}localhost:5001{{ end }}
    - IMG_TAG={{ if index .Env "IMAGE_TAG" }}{{ .Env.IMAGE_TAG }}{{ else }}develop{{ end }}
    - CGO_ENABLED=1
release:
    disable: "true"
builds:
    - targets:
        - go_first_class
      binary: chainlink
      hooks:
        post:
            - cmd: ./tools/bin/goreleaser_utils build_post_hook {{ dir .Path }}
      no_unique_dist_dir: "true"
      ldflags:
        - -s -w -r=$ORIGIN/libs
        - -X github.com/smartcontractkit/chainlink/v2/core/static.Sha={{ .FullCommit }}
        - |-
          -extldflags "-Wl,--dynamic-linker={{ if contains .Runtime.Goarch "amd64" -}}
          /lib64/ld-linux-x86-64.so.2
          {{- else if contains .Runtime.Goarch "arm64" -}}
          /lib/ld-linux-aarch64.so.1
          {{- end }}"
        - -X github.com/smartcontractkit/chainlink/v2/core/static.Version={{ .Version }}
      flags:
        - -trimpath
        - -buildmode=pie
archives:
    - format: binary
snapshot:
    version_template: v0.0.0-{{ .Runtime.Goarch }}-{{ .Now.Format "2006-01-02-15-04-05Z" }}
checksum:
    name_template: checksums.txt
dockers:
    - id: linux-amd64
      goos: linux
      goarch: amd64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMAGE }}'
      extra_files:
        - tmp/libs
        - tmp/plugins
      build_flag_templates:
        - --platform=linux/amd64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --build-arg=CL_MEDIAN_CMD=chainlink-feeds
        - --build-arg=CL_MERCURY_CMD=chainlink-mercury
        - --build-arg=CL_SOLANA_CMD=chainlink-solana
        - --build-arg=CL_STARKNET_CMD=chainlink-starknet
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
      use: buildx
    - id: linux-arm64
      goos: linux
      goarch: arm64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMAGE }}'
      extra_files:
        - tmp/libs
        - tmp/plugins
      build_flag_templates:
        - --platform=linux/arm64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --build-arg=CL_MEDIAN_CMD=chainlink-feeds
        - --build-arg=CL_MERCURY_CMD=chainlink-mercury
        - --build-arg=CL_SOLANA_CMD=chainlink-solana
        - --build-arg=CL_STARKNET_CMD=chainlink-starknet
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
      use: buildx
docker_manifests:
    - name_template: '{{ .Env.IMAGE }}'
      image_templates:
        - '{{ .Env.IMAGE }}'
changelog:
    disable: "true"
before:
    hooks:
        - cmd: go mod tidy
        - cmd: ./tools/bin/goreleaser_utils before_hook
git:
    ignore_tags:
        - contracts-ccip/v1.5.0-beta.0
partial:
    by: target
nightly:
    version_template: v0.0.0-{{ .Runtime.Goarch }}-{{ .Now.Format "2006-01-02-15-04-05Z" }}
