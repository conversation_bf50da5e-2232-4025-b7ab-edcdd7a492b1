exec chainlink node start --help
cmp stdout out.txt

-- out.txt --
NAME:
   chainlink node start - Run the Chainlink node

USAGE:
   chainlink node start [command options] [arguments...]

OPTIONS:
   --api value, -a value            text file holding the API email and password, each on a line
   --debug, -d                      set logger level to debug
   --password value, -p value       text file holding the password for the node's account
   --vrfpassword value, --vp value  text file holding the password for the vrf keys; enables Chainlink VRF oracle
   
