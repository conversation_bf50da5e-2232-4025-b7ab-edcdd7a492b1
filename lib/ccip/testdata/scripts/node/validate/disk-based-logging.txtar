exec chainlink node -c config.toml -s secrets.toml validate
cmp stdout out.txt
exists $WORK/logs

exec chainlink -c config.toml -s secrets.toml node validate
cmp stdout out.txt
exists $WORK/logs

-- config.toml --
Log.Level = 'debug'

[[EVM]]
ChainID = '1'

[[EVM.Nodes]]
Name = 'fake'
WSURL = 'wss://foo.bar/ws'
HTTPURL = 'https://foo.bar'

[Log.File]
MaxSize = '1.00mb'
Dir = './logs'

-- secrets.toml --
[Database]
URL = 'postgresql://user:pass1234567890abcd@localhost:5432/dbname?sslmode=disable'

[Password]
Keystore = 'keystore_pass'

-- out.txt --
# Secrets:
[Database]
URL = 'xxxxx'
AllowSimplePasswords = false

[Password]
Keystore = 'xxxxx'

# Input Configuration:
[Log]
Level = 'debug'

[Log.File]
Dir = './logs'
MaxSize = '1.00mb'

[[EVM]]
ChainID = '1'

[[EVM.Nodes]]
Name = 'fake'
WSURL = 'wss://foo.bar/ws'
HTTPURL = 'https://foo.bar'

# Effective Configuration, with defaults applied:
InsecureFastScrypt = false
RootDir = '~/.chainlink'
ShutdownGracePeriod = '5s'

[Feature]
FeedsManager = true
LogPoller = false
UICSAKeys = false
CCIP = true
MultiFeedsManagers = false

[Database]
DefaultIdleInTxSessionTimeout = '1h0m0s'
DefaultLockTimeout = '15s'
DefaultQueryTimeout = '10s'
LogQueries = false
MaxIdleConns = 10
MaxOpenConns = 100
MigrateOnStartup = true

[Database.Backup]
Dir = ''
Frequency = '1h0m0s'
Mode = 'none'
OnVersionUpgrade = true

[Database.Listener]
MaxReconnectDuration = '10m0s'
MinReconnectInterval = '1m0s'
FallbackPollInterval = '30s'

[Database.Lock]
Enabled = true
LeaseDuration = '10s'
LeaseRefreshInterval = '1s'

[TelemetryIngress]
UniConn = false
Logging = false
BufferSize = 100
MaxBatchSize = 50
SendInterval = '500ms'
SendTimeout = '10s'
UseBatchSend = true

[AuditLogger]
Enabled = false
ForwardToUrl = ''
JsonWrapperKey = ''
Headers = []

[Log]
Level = 'debug'
JSONConsole = false
UnixTS = false

[Log.File]
Dir = './logs'
MaxSize = '1.00mb'
MaxAgeDays = 0
MaxBackups = 1

[WebServer]
AuthenticationMethod = 'local'
AllowOrigins = 'http://localhost:3000,http://localhost:6688'
BridgeResponseURL = ''
BridgeCacheTTL = '0s'
HTTPWriteTimeout = '10s'
HTTPPort = 6688
SecureCookies = true
SessionTimeout = '15m0s'
SessionReaperExpiration = '240h0m0s'
HTTPMaxSize = '32.77kb'
StartTimeout = '15s'
ListenIP = '0.0.0.0'

[WebServer.LDAP]
ServerTLS = true
SessionTimeout = '15m0s'
QueryTimeout = '2m0s'
BaseUserAttr = 'uid'
BaseDN = ''
UsersDN = 'ou=users'
GroupsDN = 'ou=groups'
ActiveAttribute = ''
ActiveAttributeAllowedValue = ''
AdminUserGroupCN = 'NodeAdmins'
EditUserGroupCN = 'NodeEditors'
RunUserGroupCN = 'NodeRunners'
ReadUserGroupCN = 'NodeReadOnly'
UserApiTokenEnabled = false
UserAPITokenDuration = '240h0m0s'
UpstreamSyncInterval = '0s'
UpstreamSyncRateLimit = '2m0s'

[WebServer.MFA]
RPID = ''
RPOrigin = ''

[WebServer.RateLimit]
Authenticated = 1000
AuthenticatedPeriod = '1m0s'
Unauthenticated = 5
UnauthenticatedPeriod = '20s'

[WebServer.TLS]
CertPath = ''
ForceRedirect = false
Host = ''
HTTPSPort = 6689
KeyPath = ''
ListenIP = '0.0.0.0'

[JobPipeline]
ExternalInitiatorsEnabled = false
MaxRunDuration = '10m0s'
MaxSuccessfulRuns = 10000
ReaperInterval = '1h0m0s'
ReaperThreshold = '24h0m0s'
ResultWriteQueueDepth = 100
VerboseLogging = true

[JobPipeline.HTTPRequest]
DefaultTimeout = '15s'
MaxSize = '32.77kb'

[FluxMonitor]
DefaultTransactionQueueDepth = 1
SimulateTransactions = false

[OCR2]
Enabled = false
ContractConfirmations = 3
BlockchainTimeout = '20s'
ContractPollInterval = '1m0s'
ContractSubscribeInterval = '2m0s'
ContractTransmitterTransmitTimeout = '10s'
DatabaseTimeout = '10s'
KeyBundleID = '0000000000000000000000000000000000000000000000000000000000000000'
CaptureEATelemetry = false
CaptureAutomationCustomTelemetry = true
DefaultTransactionQueueDepth = 1
SimulateTransactions = false
TraceLogging = false

[OCR]
Enabled = false
ObservationTimeout = '5s'
BlockchainTimeout = '20s'
ContractPollInterval = '1m0s'
ContractSubscribeInterval = '2m0s'
DefaultTransactionQueueDepth = 1
KeyBundleID = '0000000000000000000000000000000000000000000000000000000000000000'
SimulateTransactions = false
TransmitterAddress = ''
CaptureEATelemetry = false
TraceLogging = false

[P2P]
IncomingMessageBufferSize = 10
OutgoingMessageBufferSize = 10
PeerID = ''
TraceLogging = false

[P2P.V2]
Enabled = true
AnnounceAddresses = []
DefaultBootstrappers = []
DeltaDial = '15s'
DeltaReconcile = '1m0s'
ListenAddresses = []

[Keeper]
DefaultTransactionQueueDepth = 1
GasPriceBufferPercent = 20
GasTipCapBufferPercent = 20
BaseFeeBufferPercent = 20
MaxGracePeriod = 100
TurnLookBack = 1000

[Keeper.Registry]
CheckGasOverhead = 200000
PerformGasOverhead = 300000
MaxPerformDataSize = 5000
SyncInterval = '30m0s'
SyncUpkeepQueueSize = 10

[AutoPprof]
Enabled = false
ProfileRoot = ''
PollInterval = '10s'
GatherDuration = '10s'
GatherTraceDuration = '5s'
MaxProfileSize = '100.00mb'
CPUProfileRate = 1
MemProfileRate = 1
BlockProfileRate = 1
MutexProfileFraction = 1
MemThreshold = '4.00gb'
GoroutineThreshold = 5000

[Pyroscope]
ServerAddress = ''
Environment = 'mainnet'

[Sentry]
Debug = false
DSN = ''
Environment = ''
Release = ''

[Insecure]
DevWebServer = false
OCRDevelopmentMode = false
InfiniteDepthQueries = false
DisableRateLimiting = false

[Tracing]
Enabled = false
CollectorTarget = ''
NodeID = ''
SamplingRatio = 0.0
Mode = 'tls'
TLSCertPath = ''

[Mercury]
VerboseLogging = false

[Mercury.Cache]
LatestReportTTL = '1s'
MaxStaleAge = '1h0m0s'
LatestReportDeadline = '5s'

[Mercury.TLS]
CertFile = ''

[Mercury.Transmitter]
TransmitQueueMaxSize = 10000
TransmitTimeout = '5s'

[Capabilities]
[Capabilities.Peering]
IncomingMessageBufferSize = 10
OutgoingMessageBufferSize = 10
PeerID = ''
TraceLogging = false

[Capabilities.Peering.V2]
Enabled = false
AnnounceAddresses = []
DefaultBootstrappers = []
DeltaDial = '15s'
DeltaReconcile = '1m0s'
ListenAddresses = []

[Capabilities.Dispatcher]
SupportedVersion = 1
ReceiverBufferSize = 10000

[Capabilities.Dispatcher.RateLimit]
GlobalRPS = 800.0
GlobalBurst = 1000
PerSenderRPS = 10.0
PerSenderBurst = 50

[Capabilities.ExternalRegistry]
Address = ''
NetworkID = 'evm'
ChainID = '1'

[Capabilities.GatewayConnector]
ChainIDForNodeKey = ''
NodeAddress = ''
DonID = ''
WSHandshakeTimeoutMillis = 0
AuthMinChallengeLen = 0
AuthTimestampToleranceSec = 0

[[Capabilities.GatewayConnector.Gateways]]
ID = ''
URL = ''

[Telemetry]
Enabled = false
CACertFile = ''
Endpoint = ''
InsecureConnection = false
TraceSampleRatio = 0.01

[[EVM]]
ChainID = '1'
AutoCreateKey = true
BlockBackfillDepth = 10
BlockBackfillSkip = false
FinalityDepth = 50
FinalityTagEnabled = true
LinkContractAddress = '0x514910771AF9Ca656af840dff83E8264EcF986CA'
LogBackfillBatchSize = 1000
LogPollInterval = '15s'
LogKeepBlocksDepth = 100000
LogPrunePageSize = 10000
BackupLogPollerBlockDelay = 100
MinIncomingConfirmations = 3
MinContractPayment = '0.1 link'
NonceAutoSync = true
NoNewHeadsThreshold = '3m0s'
OperatorFactoryAddress = '0x3E64Cd889482443324F91bFA9c84fE72A511f48A'
LogBroadcasterEnabled = true
RPCDefaultBatchSize = 250
RPCBlockQueryDelay = 1
FinalizedBlockOffset = 0
NoNewFinalizedHeadsThreshold = '9m0s'

[EVM.Transactions]
ForwardersEnabled = false
MaxInFlight = 16
MaxQueued = 250
ReaperInterval = '1h0m0s'
ReaperThreshold = '168h0m0s'
ResendAfterThreshold = '1m0s'

[EVM.Transactions.AutoPurge]
Enabled = false

[EVM.BalanceMonitor]
Enabled = true

[EVM.GasEstimator]
Mode = 'BlockHistory'
PriceDefault = '20 gwei'
PriceMax = '115792089237316195423570985008687907853269984665.640564039457584007913129639935 tether'
PriceMin = '1 gwei'
LimitDefault = 8000000
LimitMax = 8000000
LimitMultiplier = '1'
LimitTransfer = 21000
EstimateLimit = false
BumpMin = '5 gwei'
BumpPercent = 20
BumpThreshold = 3
EIP1559DynamicFees = true
FeeCapDefault = '100 gwei'
TipCapDefault = '1 wei'
TipCapMin = '1 wei'

[EVM.GasEstimator.BlockHistory]
BatchSize = 25
BlockHistorySize = 4
CheckInclusionBlocks = 12
CheckInclusionPercentile = 90
TransactionPercentile = 50

[EVM.GasEstimator.FeeHistory]
CacheTimeout = '10s'

[EVM.HeadTracker]
HistoryDepth = 100
MaxBufferSize = 3
SamplingInterval = '1s'
MaxAllowedFinalityDepth = 10000
FinalityTagBypass = true

[EVM.NodePool]
PollFailureThreshold = 5
PollInterval = '10s'
SelectionMode = 'HighestHead'
SyncThreshold = 5
LeaseDuration = '0s'
NodeIsSyncingEnabled = false
FinalizedBlockPollInterval = '5s'
EnforceRepeatableRead = false
DeathDeclarationDelay = '10s'
NewHeadsPollInterval = '0s'

[EVM.OCR]
ContractConfirmations = 4
ContractTransmitterTransmitTimeout = '10s'
DatabaseTimeout = '10s'
DeltaCOverride = '168h0m0s'
DeltaCJitterOverride = '1h0m0s'
ObservationGracePeriod = '1s'

[EVM.OCR2]
[EVM.OCR2.Automation]
GasLimit = 10500000

[EVM.Workflow]
GasLimitDefault = 400000

[[EVM.Nodes]]
Name = 'fake'
WSURL = 'wss://foo.bar/ws'
HTTPURL = 'https://foo.bar'

Valid configuration.
