exec chainlink keys --help
cmp stdout out.txt

-- out.txt --
NAME:
   chainlink keys - Commands for managing various types of keys used by the Chainlink node

USAGE:
   chainlink keys command [command options] [arguments...]

COMMANDS:
   eth       Remote commands for administering the node's Ethereum keys
   p2p       Remote commands for administering the node's p2p keys
   csa       Remote commands for administering the node's CSA keys
   ocr       Remote commands for administering the node's legacy off chain reporting keys
   ocr2      Remote commands for administering the node's off chain reporting keys
   cosmos    Remote commands for administering the node's Cosmos keys
   solana    Remote commands for administering the node's Solana keys
   starknet  Remote commands for administering the node's StarkNet keys
   aptos     Remote commands for administering the node's Aptos keys
   vrf       Remote commands for administering the node's vrf keys

OPTIONS:
   --help, -h  show help
   
