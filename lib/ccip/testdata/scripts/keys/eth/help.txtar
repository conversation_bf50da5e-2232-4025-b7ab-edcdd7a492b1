exec chainlink keys eth --help
cmp stdout out.txt

-- out.txt --
NAME:
   chainlink keys eth - Remote commands for administering the node's Ethereum keys

USAGE:
   chainlink keys eth command [command options] [arguments...]

COMMANDS:
   create  Create a key in the node's keystore alongside the existing key; to create an original key, just run the node
   list    List available Ethereum accounts with their ETH & LINK balances and other metadata
   delete  Delete the ETH key by address (irreversible!)
   import  Import an ETH key from a JSON file
   export  Exports an ETH key to a JSON file
   chain   Update an EVM key for the given chain

OPTIONS:
   --help, -h  show help
   