# start node
exec sh -c 'eval "echo \"$(cat config.toml.tmpl)\" > config.toml"'
exec chainlink node -c config.toml start -p password -a creds &

# initialize client
env NODEURL=http://localhost:$PORT
exec curl --retry 10 --retry-max-time 60 --retry-connrefused $NODEURL
exec chainlink --remote-node-url $NODEURL admin login -file creds --bypass-version-check

exec chainlink --remote-node-url $NODEURL keys eth list
! stdout 'ETH:               <nil>'
! stdout 'LINK:              <nil>'
! stdout '<nil>'
stdout 'ETH:               Unknown'
stdout 'LINK:              Unknown'

-- testdb.txt --
CL_DATABASE_URL
-- testport.txt --
PORT

-- password --
T.tLHkcmwePT/p,]sYuntjwHKAsrhm#4eRs4LuKHwvHejWYAC2JP4M8HimwgmbaZ
-- creds --
<EMAIL>
fj293fbBnlQ!f9vNs

-- config.toml.tmpl --
[Webserver]
HTTPPort = $PORT

[[EVM]]
ChainID = '99'

[[EVM.Nodes]]
Name = 'fake'
WSURL = 'wss://foo.bar/ws'
HTTPURL = 'https://foo.bar'
