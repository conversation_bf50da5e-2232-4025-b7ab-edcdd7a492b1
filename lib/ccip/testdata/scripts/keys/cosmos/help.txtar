exec chainlink keys cosmos --help
cmp stdout out.txt

-- out.txt --
NAME:
   chainlink keys cosmos - Remote commands for administering the node's Cosmos keys

USAGE:
   chainlink keys cosmos command [command options] [arguments...]

COMMANDS:
   create  Create a Cosmos key
   import  Import Cosmos key from keyfile
   export  Export Cosmos key to keyfile
   delete  Delete Cosmos key if present
   list    List the Cosmos keys

OPTIONS:
   --help, -h  show help
   
