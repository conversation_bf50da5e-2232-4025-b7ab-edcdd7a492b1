skip 'High Flake Rate'
# Check that metrics specified in the expected_metrics are present in /metrics response
# start node
exec sh -c 'eval "echo \"$(cat config.toml.tmpl)\" > config.toml"'
exec chainlink node -c config.toml start -p password -a creds &

# ensure node is up and running
env NODEURL=http://localhost:$PORT
exec curl --retry 10 --retry-max-time 60 --retry-connrefused $NODEURL


# Check
chmod 700 ./script.sh
exec sh -c './script.sh'

-- script.sh --

maxRetries=5
for retriesNum in $(seq 1 $maxRetries); do
    passedAllChecks=true
    curl $NODEURL/metrics > metrics.txt
    while IFS= read -r expectedMetric; do
        grep -q $expectedMetric metrics.txt && continue

        if [[ $retriesNum -ge $maxRetries ]]; then
            cat metrics.txt
            echo "FAIL Expected metric $expectedMetric to be present in GET /metrics response"
            exit 1
        fi

        echo "Metric $expectedMetric is not present in GET /metrics response - retrying after 5s"
        passedAllChecks=false
        sleep 5
        break
    done < expected_metrics.txt

    $passedAllChecks && break
done

-- testdb.txt --
CL_DATABASE_URL
-- testport.txt --
PORT

-- password --
T.tLHkcmwePT/p,]sYuntjwHKAsrhm#4eRs4LuKHwvHejWYAC2JP4M8HimwgmbaZ
-- creds --
<EMAIL>
fj293fbBnlQ!f9vNs

-- config.toml.tmpl --
[Webserver]
HTTPPort = $PORT

[[EVM]]
ChainID = '68472'

[[EVM.Nodes]]
Name = 'BlueEVMPrimaryNode'
WSURL = 'wss://primaryfoo.bar/ws'
HTTPURL = 'https://primaryfoo.bar'

[[EVM.Nodes]]
Name = 'YellowEVMPrimaryNode'
WSURL = 'wss://sendonlyfoo.bar/ws'
HTTPURL = 'https://sendonlyfoo.bar'
SendOnly = true

-- expected_metrics.txt --
evm_pool_rpc_node_dials_total{evmChainID="68472",nodeName="BlueEVMPrimaryNode"}
evm_pool_rpc_node_dials_total{evmChainID="68472",nodeName="YellowEVMPrimaryNode"}
multi_node_states{chainId="68472",network="EVM",state="Alive"}
multi_node_states{chainId="68472",network="EVM",state="Closed"}
multi_node_states{chainId="68472",network="EVM",state="Dialed"}
multi_node_states{chainId="68472",network="EVM",state="InvalidChainID"}
multi_node_states{chainId="68472",network="EVM",state="OutOfSync"}
multi_node_states{chainId="68472",network="EVM",state="Undialed"}
multi_node_states{chainId="68472",network="EVM",state="Unreachable"}
multi_node_states{chainId="68472",network="EVM",state="Unusable"}
