// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	job "github.com/smartcontractkit/chainlink/v2/core/services/job"
	mock "github.com/stretchr/testify/mock"

	sqlutil "github.com/smartcontractkit/chainlink-common/pkg/sqlutil"
)

// Spawner is an autogenerated mock type for the Spawner type
type Spawner struct {
	mock.Mock
}

type Spawner_Expecter struct {
	mock *mock.Mock
}

func (_m *Spawner) EXPECT() *Spawner_Expecter {
	return &Spawner_Expecter{mock: &_m.Mock}
}

// ActiveJobs provides a mock function with given fields:
func (_m *Spawner) ActiveJobs() map[int32]job.Job {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ActiveJobs")
	}

	var r0 map[int32]job.Job
	if rf, ok := ret.Get(0).(func() map[int32]job.Job); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[int32]job.Job)
		}
	}

	return r0
}

// Spawner_ActiveJobs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ActiveJobs'
type Spawner_ActiveJobs_Call struct {
	*mock.Call
}

// ActiveJobs is a helper method to define mock.On call
func (_e *Spawner_Expecter) ActiveJobs() *Spawner_ActiveJobs_Call {
	return &Spawner_ActiveJobs_Call{Call: _e.mock.On("ActiveJobs")}
}

func (_c *Spawner_ActiveJobs_Call) Run(run func()) *Spawner_ActiveJobs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Spawner_ActiveJobs_Call) Return(_a0 map[int32]job.Job) *Spawner_ActiveJobs_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Spawner_ActiveJobs_Call) RunAndReturn(run func() map[int32]job.Job) *Spawner_ActiveJobs_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *Spawner) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Spawner_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Spawner_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *Spawner_Expecter) Close() *Spawner_Close_Call {
	return &Spawner_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *Spawner_Close_Call) Run(run func()) *Spawner_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Spawner_Close_Call) Return(_a0 error) *Spawner_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Spawner_Close_Call) RunAndReturn(run func() error) *Spawner_Close_Call {
	_c.Call.Return(run)
	return _c
}

// CreateJob provides a mock function with given fields: ctx, ds, jb
func (_m *Spawner) CreateJob(ctx context.Context, ds sqlutil.DataSource, jb *job.Job) error {
	ret := _m.Called(ctx, ds, jb)

	if len(ret) == 0 {
		panic("no return value specified for CreateJob")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, sqlutil.DataSource, *job.Job) error); ok {
		r0 = rf(ctx, ds, jb)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Spawner_CreateJob_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateJob'
type Spawner_CreateJob_Call struct {
	*mock.Call
}

// CreateJob is a helper method to define mock.On call
//   - ctx context.Context
//   - ds sqlutil.DataSource
//   - jb *job.Job
func (_e *Spawner_Expecter) CreateJob(ctx interface{}, ds interface{}, jb interface{}) *Spawner_CreateJob_Call {
	return &Spawner_CreateJob_Call{Call: _e.mock.On("CreateJob", ctx, ds, jb)}
}

func (_c *Spawner_CreateJob_Call) Run(run func(ctx context.Context, ds sqlutil.DataSource, jb *job.Job)) *Spawner_CreateJob_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(sqlutil.DataSource), args[2].(*job.Job))
	})
	return _c
}

func (_c *Spawner_CreateJob_Call) Return(err error) *Spawner_CreateJob_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *Spawner_CreateJob_Call) RunAndReturn(run func(context.Context, sqlutil.DataSource, *job.Job) error) *Spawner_CreateJob_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteJob provides a mock function with given fields: ctx, ds, jobID
func (_m *Spawner) DeleteJob(ctx context.Context, ds sqlutil.DataSource, jobID int32) error {
	ret := _m.Called(ctx, ds, jobID)

	if len(ret) == 0 {
		panic("no return value specified for DeleteJob")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, sqlutil.DataSource, int32) error); ok {
		r0 = rf(ctx, ds, jobID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Spawner_DeleteJob_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteJob'
type Spawner_DeleteJob_Call struct {
	*mock.Call
}

// DeleteJob is a helper method to define mock.On call
//   - ctx context.Context
//   - ds sqlutil.DataSource
//   - jobID int32
func (_e *Spawner_Expecter) DeleteJob(ctx interface{}, ds interface{}, jobID interface{}) *Spawner_DeleteJob_Call {
	return &Spawner_DeleteJob_Call{Call: _e.mock.On("DeleteJob", ctx, ds, jobID)}
}

func (_c *Spawner_DeleteJob_Call) Run(run func(ctx context.Context, ds sqlutil.DataSource, jobID int32)) *Spawner_DeleteJob_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(sqlutil.DataSource), args[2].(int32))
	})
	return _c
}

func (_c *Spawner_DeleteJob_Call) Return(_a0 error) *Spawner_DeleteJob_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Spawner_DeleteJob_Call) RunAndReturn(run func(context.Context, sqlutil.DataSource, int32) error) *Spawner_DeleteJob_Call {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with given fields:
func (_m *Spawner) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// Spawner_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type Spawner_HealthReport_Call struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *Spawner_Expecter) HealthReport() *Spawner_HealthReport_Call {
	return &Spawner_HealthReport_Call{Call: _e.mock.On("HealthReport")}
}

func (_c *Spawner_HealthReport_Call) Run(run func()) *Spawner_HealthReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Spawner_HealthReport_Call) Return(_a0 map[string]error) *Spawner_HealthReport_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Spawner_HealthReport_Call) RunAndReturn(run func() map[string]error) *Spawner_HealthReport_Call {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with given fields:
func (_m *Spawner) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// Spawner_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type Spawner_Name_Call struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *Spawner_Expecter) Name() *Spawner_Name_Call {
	return &Spawner_Name_Call{Call: _e.mock.On("Name")}
}

func (_c *Spawner_Name_Call) Run(run func()) *Spawner_Name_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Spawner_Name_Call) Return(_a0 string) *Spawner_Name_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Spawner_Name_Call) RunAndReturn(run func() string) *Spawner_Name_Call {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with given fields:
func (_m *Spawner) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Spawner_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type Spawner_Ready_Call struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *Spawner_Expecter) Ready() *Spawner_Ready_Call {
	return &Spawner_Ready_Call{Call: _e.mock.On("Ready")}
}

func (_c *Spawner_Ready_Call) Run(run func()) *Spawner_Ready_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Spawner_Ready_Call) Return(_a0 error) *Spawner_Ready_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Spawner_Ready_Call) RunAndReturn(run func() error) *Spawner_Ready_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *Spawner) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Spawner_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type Spawner_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *Spawner_Expecter) Start(_a0 interface{}) *Spawner_Start_Call {
	return &Spawner_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *Spawner_Start_Call) Run(run func(_a0 context.Context)) *Spawner_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Spawner_Start_Call) Return(_a0 error) *Spawner_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Spawner_Start_Call) RunAndReturn(run func(context.Context) error) *Spawner_Start_Call {
	_c.Call.Return(run)
	return _c
}

// StartService provides a mock function with given fields: ctx, spec
func (_m *Spawner) StartService(ctx context.Context, spec job.Job) error {
	ret := _m.Called(ctx, spec)

	if len(ret) == 0 {
		panic("no return value specified for StartService")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, job.Job) error); ok {
		r0 = rf(ctx, spec)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Spawner_StartService_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartService'
type Spawner_StartService_Call struct {
	*mock.Call
}

// StartService is a helper method to define mock.On call
//   - ctx context.Context
//   - spec job.Job
func (_e *Spawner_Expecter) StartService(ctx interface{}, spec interface{}) *Spawner_StartService_Call {
	return &Spawner_StartService_Call{Call: _e.mock.On("StartService", ctx, spec)}
}

func (_c *Spawner_StartService_Call) Run(run func(ctx context.Context, spec job.Job)) *Spawner_StartService_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(job.Job))
	})
	return _c
}

func (_c *Spawner_StartService_Call) Return(_a0 error) *Spawner_StartService_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Spawner_StartService_Call) RunAndReturn(run func(context.Context, job.Job) error) *Spawner_StartService_Call {
	_c.Call.Return(run)
	return _c
}

// NewSpawner creates a new instance of Spawner. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSpawner(t interface {
	mock.TestingT
	Cleanup(func())
}) *Spawner {
	mock := &Spawner{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
