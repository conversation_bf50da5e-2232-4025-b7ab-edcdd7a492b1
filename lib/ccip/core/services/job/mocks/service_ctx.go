// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// ServiceCtx is an autogenerated mock type for the ServiceCtx type
type ServiceCtx struct {
	mock.Mock
}

type ServiceCtx_Expecter struct {
	mock *mock.Mock
}

func (_m *ServiceCtx) EXPECT() *ServiceCtx_Expecter {
	return &ServiceCtx_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with given fields:
func (_m *ServiceCtx) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ServiceCtx_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type ServiceCtx_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *ServiceCtx_Expecter) Close() *ServiceCtx_Close_Call {
	return &ServiceCtx_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *ServiceCtx_Close_Call) Run(run func()) *ServiceCtx_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ServiceCtx_Close_Call) Return(_a0 error) *ServiceCtx_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ServiceCtx_Close_Call) RunAndReturn(run func() error) *ServiceCtx_Close_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *ServiceCtx) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ServiceCtx_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type ServiceCtx_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *ServiceCtx_Expecter) Start(_a0 interface{}) *ServiceCtx_Start_Call {
	return &ServiceCtx_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *ServiceCtx_Start_Call) Run(run func(_a0 context.Context)) *ServiceCtx_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *ServiceCtx_Start_Call) Return(_a0 error) *ServiceCtx_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ServiceCtx_Start_Call) RunAndReturn(run func(context.Context) error) *ServiceCtx_Start_Call {
	_c.Call.Return(run)
	return _c
}

// NewServiceCtx creates a new instance of ServiceCtx. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewServiceCtx(t interface {
	mock.TestingT
	Cleanup(func())
}) *ServiceCtx {
	mock := &ServiceCtx{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
