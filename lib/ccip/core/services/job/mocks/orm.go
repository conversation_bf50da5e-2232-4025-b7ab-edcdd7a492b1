// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	common "github.com/ethereum/go-ethereum/common"
	big "github.com/smartcontractkit/chainlink/v2/core/chains/evm/utils/big"

	context "context"

	job "github.com/smartcontractkit/chainlink/v2/core/services/job"

	mock "github.com/stretchr/testify/mock"

	pipeline "github.com/smartcontractkit/chainlink/v2/core/services/pipeline"

	sqlutil "github.com/smartcontractkit/chainlink-common/pkg/sqlutil"

	types "github.com/smartcontractkit/chainlink/v2/core/chains/evm/types"

	uuid "github.com/google/uuid"
)

// ORM is an autogenerated mock type for the ORM type
type ORM struct {
	mock.Mock
}

type ORM_Expecter struct {
	mock *mock.Mock
}

func (_m *ORM) EXPECT() *ORM_Expecter {
	return &ORM_Expecter{mock: &_m.Mock}
}

// AssertBridgesExist provides a mock function with given fields: ctx, p
func (_m *ORM) AssertBridgesExist(ctx context.Context, p pipeline.Pipeline) error {
	ret := _m.Called(ctx, p)

	if len(ret) == 0 {
		panic("no return value specified for AssertBridgesExist")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, pipeline.Pipeline) error); ok {
		r0 = rf(ctx, p)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_AssertBridgesExist_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AssertBridgesExist'
type ORM_AssertBridgesExist_Call struct {
	*mock.Call
}

// AssertBridgesExist is a helper method to define mock.On call
//   - ctx context.Context
//   - p pipeline.Pipeline
func (_e *ORM_Expecter) AssertBridgesExist(ctx interface{}, p interface{}) *ORM_AssertBridgesExist_Call {
	return &ORM_AssertBridgesExist_Call{Call: _e.mock.On("AssertBridgesExist", ctx, p)}
}

func (_c *ORM_AssertBridgesExist_Call) Run(run func(ctx context.Context, p pipeline.Pipeline)) *ORM_AssertBridgesExist_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(pipeline.Pipeline))
	})
	return _c
}

func (_c *ORM_AssertBridgesExist_Call) Return(_a0 error) *ORM_AssertBridgesExist_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_AssertBridgesExist_Call) RunAndReturn(run func(context.Context, pipeline.Pipeline) error) *ORM_AssertBridgesExist_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *ORM) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type ORM_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *ORM_Expecter) Close() *ORM_Close_Call {
	return &ORM_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *ORM_Close_Call) Run(run func()) *ORM_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ORM_Close_Call) Return(_a0 error) *ORM_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_Close_Call) RunAndReturn(run func() error) *ORM_Close_Call {
	_c.Call.Return(run)
	return _c
}

// CountPipelineRunsByJobID provides a mock function with given fields: ctx, jobID
func (_m *ORM) CountPipelineRunsByJobID(ctx context.Context, jobID int32) (int32, error) {
	ret := _m.Called(ctx, jobID)

	if len(ret) == 0 {
		panic("no return value specified for CountPipelineRunsByJobID")
	}

	var r0 int32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int32) (int32, error)); ok {
		return rf(ctx, jobID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32) int32); ok {
		r0 = rf(ctx, jobID)
	} else {
		r0 = ret.Get(0).(int32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32) error); ok {
		r1 = rf(ctx, jobID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_CountPipelineRunsByJobID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountPipelineRunsByJobID'
type ORM_CountPipelineRunsByJobID_Call struct {
	*mock.Call
}

// CountPipelineRunsByJobID is a helper method to define mock.On call
//   - ctx context.Context
//   - jobID int32
func (_e *ORM_Expecter) CountPipelineRunsByJobID(ctx interface{}, jobID interface{}) *ORM_CountPipelineRunsByJobID_Call {
	return &ORM_CountPipelineRunsByJobID_Call{Call: _e.mock.On("CountPipelineRunsByJobID", ctx, jobID)}
}

func (_c *ORM_CountPipelineRunsByJobID_Call) Run(run func(ctx context.Context, jobID int32)) *ORM_CountPipelineRunsByJobID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int32))
	})
	return _c
}

func (_c *ORM_CountPipelineRunsByJobID_Call) Return(count int32, err error) *ORM_CountPipelineRunsByJobID_Call {
	_c.Call.Return(count, err)
	return _c
}

func (_c *ORM_CountPipelineRunsByJobID_Call) RunAndReturn(run func(context.Context, int32) (int32, error)) *ORM_CountPipelineRunsByJobID_Call {
	_c.Call.Return(run)
	return _c
}

// CreateJob provides a mock function with given fields: ctx, jb
func (_m *ORM) CreateJob(ctx context.Context, jb *job.Job) error {
	ret := _m.Called(ctx, jb)

	if len(ret) == 0 {
		panic("no return value specified for CreateJob")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *job.Job) error); ok {
		r0 = rf(ctx, jb)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_CreateJob_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateJob'
type ORM_CreateJob_Call struct {
	*mock.Call
}

// CreateJob is a helper method to define mock.On call
//   - ctx context.Context
//   - jb *job.Job
func (_e *ORM_Expecter) CreateJob(ctx interface{}, jb interface{}) *ORM_CreateJob_Call {
	return &ORM_CreateJob_Call{Call: _e.mock.On("CreateJob", ctx, jb)}
}

func (_c *ORM_CreateJob_Call) Run(run func(ctx context.Context, jb *job.Job)) *ORM_CreateJob_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*job.Job))
	})
	return _c
}

func (_c *ORM_CreateJob_Call) Return(_a0 error) *ORM_CreateJob_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_CreateJob_Call) RunAndReturn(run func(context.Context, *job.Job) error) *ORM_CreateJob_Call {
	_c.Call.Return(run)
	return _c
}

// DataSource provides a mock function with given fields:
func (_m *ORM) DataSource() sqlutil.DataSource {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for DataSource")
	}

	var r0 sqlutil.DataSource
	if rf, ok := ret.Get(0).(func() sqlutil.DataSource); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(sqlutil.DataSource)
		}
	}

	return r0
}

// ORM_DataSource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DataSource'
type ORM_DataSource_Call struct {
	*mock.Call
}

// DataSource is a helper method to define mock.On call
func (_e *ORM_Expecter) DataSource() *ORM_DataSource_Call {
	return &ORM_DataSource_Call{Call: _e.mock.On("DataSource")}
}

func (_c *ORM_DataSource_Call) Run(run func()) *ORM_DataSource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ORM_DataSource_Call) Return(_a0 sqlutil.DataSource) *ORM_DataSource_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_DataSource_Call) RunAndReturn(run func() sqlutil.DataSource) *ORM_DataSource_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteJob provides a mock function with given fields: ctx, id
func (_m *ORM) DeleteJob(ctx context.Context, id int32) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteJob")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int32) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_DeleteJob_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteJob'
type ORM_DeleteJob_Call struct {
	*mock.Call
}

// DeleteJob is a helper method to define mock.On call
//   - ctx context.Context
//   - id int32
func (_e *ORM_Expecter) DeleteJob(ctx interface{}, id interface{}) *ORM_DeleteJob_Call {
	return &ORM_DeleteJob_Call{Call: _e.mock.On("DeleteJob", ctx, id)}
}

func (_c *ORM_DeleteJob_Call) Run(run func(ctx context.Context, id int32)) *ORM_DeleteJob_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int32))
	})
	return _c
}

func (_c *ORM_DeleteJob_Call) Return(_a0 error) *ORM_DeleteJob_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_DeleteJob_Call) RunAndReturn(run func(context.Context, int32) error) *ORM_DeleteJob_Call {
	_c.Call.Return(run)
	return _c
}

// DismissError provides a mock function with given fields: ctx, errorID
func (_m *ORM) DismissError(ctx context.Context, errorID int64) error {
	ret := _m.Called(ctx, errorID)

	if len(ret) == 0 {
		panic("no return value specified for DismissError")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, errorID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_DismissError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DismissError'
type ORM_DismissError_Call struct {
	*mock.Call
}

// DismissError is a helper method to define mock.On call
//   - ctx context.Context
//   - errorID int64
func (_e *ORM_Expecter) DismissError(ctx interface{}, errorID interface{}) *ORM_DismissError_Call {
	return &ORM_DismissError_Call{Call: _e.mock.On("DismissError", ctx, errorID)}
}

func (_c *ORM_DismissError_Call) Run(run func(ctx context.Context, errorID int64)) *ORM_DismissError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_DismissError_Call) Return(_a0 error) *ORM_DismissError_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_DismissError_Call) RunAndReturn(run func(context.Context, int64) error) *ORM_DismissError_Call {
	_c.Call.Return(run)
	return _c
}

// FindJob provides a mock function with given fields: ctx, id
func (_m *ORM) FindJob(ctx context.Context, id int32) (job.Job, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindJob")
	}

	var r0 job.Job
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int32) (job.Job, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32) job.Job); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(job.Job)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindJob_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJob'
type ORM_FindJob_Call struct {
	*mock.Call
}

// FindJob is a helper method to define mock.On call
//   - ctx context.Context
//   - id int32
func (_e *ORM_Expecter) FindJob(ctx interface{}, id interface{}) *ORM_FindJob_Call {
	return &ORM_FindJob_Call{Call: _e.mock.On("FindJob", ctx, id)}
}

func (_c *ORM_FindJob_Call) Run(run func(ctx context.Context, id int32)) *ORM_FindJob_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int32))
	})
	return _c
}

func (_c *ORM_FindJob_Call) Return(_a0 job.Job, _a1 error) *ORM_FindJob_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindJob_Call) RunAndReturn(run func(context.Context, int32) (job.Job, error)) *ORM_FindJob_Call {
	_c.Call.Return(run)
	return _c
}

// FindJobByExternalJobID provides a mock function with given fields: ctx, _a1
func (_m *ORM) FindJobByExternalJobID(ctx context.Context, _a1 uuid.UUID) (job.Job, error) {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for FindJobByExternalJobID")
	}

	var r0 job.Job
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (job.Job, error)); ok {
		return rf(ctx, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) job.Job); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Get(0).(job.Job)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindJobByExternalJobID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJobByExternalJobID'
type ORM_FindJobByExternalJobID_Call struct {
	*mock.Call
}

// FindJobByExternalJobID is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 uuid.UUID
func (_e *ORM_Expecter) FindJobByExternalJobID(ctx interface{}, _a1 interface{}) *ORM_FindJobByExternalJobID_Call {
	return &ORM_FindJobByExternalJobID_Call{Call: _e.mock.On("FindJobByExternalJobID", ctx, _a1)}
}

func (_c *ORM_FindJobByExternalJobID_Call) Run(run func(ctx context.Context, _a1 uuid.UUID)) *ORM_FindJobByExternalJobID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *ORM_FindJobByExternalJobID_Call) Return(_a0 job.Job, _a1 error) *ORM_FindJobByExternalJobID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindJobByExternalJobID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (job.Job, error)) *ORM_FindJobByExternalJobID_Call {
	_c.Call.Return(run)
	return _c
}

// FindJobIDByAddress provides a mock function with given fields: ctx, address, evmChainID
func (_m *ORM) FindJobIDByAddress(ctx context.Context, address types.EIP55Address, evmChainID *big.Big) (int32, error) {
	ret := _m.Called(ctx, address, evmChainID)

	if len(ret) == 0 {
		panic("no return value specified for FindJobIDByAddress")
	}

	var r0 int32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.EIP55Address, *big.Big) (int32, error)); ok {
		return rf(ctx, address, evmChainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.EIP55Address, *big.Big) int32); ok {
		r0 = rf(ctx, address, evmChainID)
	} else {
		r0 = ret.Get(0).(int32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.EIP55Address, *big.Big) error); ok {
		r1 = rf(ctx, address, evmChainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindJobIDByAddress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJobIDByAddress'
type ORM_FindJobIDByAddress_Call struct {
	*mock.Call
}

// FindJobIDByAddress is a helper method to define mock.On call
//   - ctx context.Context
//   - address types.EIP55Address
//   - evmChainID *big.Big
func (_e *ORM_Expecter) FindJobIDByAddress(ctx interface{}, address interface{}, evmChainID interface{}) *ORM_FindJobIDByAddress_Call {
	return &ORM_FindJobIDByAddress_Call{Call: _e.mock.On("FindJobIDByAddress", ctx, address, evmChainID)}
}

func (_c *ORM_FindJobIDByAddress_Call) Run(run func(ctx context.Context, address types.EIP55Address, evmChainID *big.Big)) *ORM_FindJobIDByAddress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.EIP55Address), args[2].(*big.Big))
	})
	return _c
}

func (_c *ORM_FindJobIDByAddress_Call) Return(_a0 int32, _a1 error) *ORM_FindJobIDByAddress_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindJobIDByAddress_Call) RunAndReturn(run func(context.Context, types.EIP55Address, *big.Big) (int32, error)) *ORM_FindJobIDByAddress_Call {
	_c.Call.Return(run)
	return _c
}

// FindJobIDByCapabilityNameAndVersion provides a mock function with given fields: ctx, spec
func (_m *ORM) FindJobIDByCapabilityNameAndVersion(ctx context.Context, spec job.CCIPSpec) (int32, error) {
	ret := _m.Called(ctx, spec)

	if len(ret) == 0 {
		panic("no return value specified for FindJobIDByCapabilityNameAndVersion")
	}

	var r0 int32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, job.CCIPSpec) (int32, error)); ok {
		return rf(ctx, spec)
	}
	if rf, ok := ret.Get(0).(func(context.Context, job.CCIPSpec) int32); ok {
		r0 = rf(ctx, spec)
	} else {
		r0 = ret.Get(0).(int32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, job.CCIPSpec) error); ok {
		r1 = rf(ctx, spec)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindJobIDByCapabilityNameAndVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJobIDByCapabilityNameAndVersion'
type ORM_FindJobIDByCapabilityNameAndVersion_Call struct {
	*mock.Call
}

// FindJobIDByCapabilityNameAndVersion is a helper method to define mock.On call
//   - ctx context.Context
//   - spec job.CCIPSpec
func (_e *ORM_Expecter) FindJobIDByCapabilityNameAndVersion(ctx interface{}, spec interface{}) *ORM_FindJobIDByCapabilityNameAndVersion_Call {
	return &ORM_FindJobIDByCapabilityNameAndVersion_Call{Call: _e.mock.On("FindJobIDByCapabilityNameAndVersion", ctx, spec)}
}

func (_c *ORM_FindJobIDByCapabilityNameAndVersion_Call) Run(run func(ctx context.Context, spec job.CCIPSpec)) *ORM_FindJobIDByCapabilityNameAndVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(job.CCIPSpec))
	})
	return _c
}

func (_c *ORM_FindJobIDByCapabilityNameAndVersion_Call) Return(_a0 int32, _a1 error) *ORM_FindJobIDByCapabilityNameAndVersion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindJobIDByCapabilityNameAndVersion_Call) RunAndReturn(run func(context.Context, job.CCIPSpec) (int32, error)) *ORM_FindJobIDByCapabilityNameAndVersion_Call {
	_c.Call.Return(run)
	return _c
}

// FindJobIDByWorkflow provides a mock function with given fields: ctx, spec
func (_m *ORM) FindJobIDByWorkflow(ctx context.Context, spec job.WorkflowSpec) (int32, error) {
	ret := _m.Called(ctx, spec)

	if len(ret) == 0 {
		panic("no return value specified for FindJobIDByWorkflow")
	}

	var r0 int32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, job.WorkflowSpec) (int32, error)); ok {
		return rf(ctx, spec)
	}
	if rf, ok := ret.Get(0).(func(context.Context, job.WorkflowSpec) int32); ok {
		r0 = rf(ctx, spec)
	} else {
		r0 = ret.Get(0).(int32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, job.WorkflowSpec) error); ok {
		r1 = rf(ctx, spec)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindJobIDByWorkflow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJobIDByWorkflow'
type ORM_FindJobIDByWorkflow_Call struct {
	*mock.Call
}

// FindJobIDByWorkflow is a helper method to define mock.On call
//   - ctx context.Context
//   - spec job.WorkflowSpec
func (_e *ORM_Expecter) FindJobIDByWorkflow(ctx interface{}, spec interface{}) *ORM_FindJobIDByWorkflow_Call {
	return &ORM_FindJobIDByWorkflow_Call{Call: _e.mock.On("FindJobIDByWorkflow", ctx, spec)}
}

func (_c *ORM_FindJobIDByWorkflow_Call) Run(run func(ctx context.Context, spec job.WorkflowSpec)) *ORM_FindJobIDByWorkflow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(job.WorkflowSpec))
	})
	return _c
}

func (_c *ORM_FindJobIDByWorkflow_Call) Return(_a0 int32, _a1 error) *ORM_FindJobIDByWorkflow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindJobIDByWorkflow_Call) RunAndReturn(run func(context.Context, job.WorkflowSpec) (int32, error)) *ORM_FindJobIDByWorkflow_Call {
	_c.Call.Return(run)
	return _c
}

// FindJobIDsWithBridge provides a mock function with given fields: ctx, name
func (_m *ORM) FindJobIDsWithBridge(ctx context.Context, name string) ([]int32, error) {
	ret := _m.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for FindJobIDsWithBridge")
	}

	var r0 []int32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]int32, error)); ok {
		return rf(ctx, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []int32); ok {
		r0 = rf(ctx, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int32)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindJobIDsWithBridge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJobIDsWithBridge'
type ORM_FindJobIDsWithBridge_Call struct {
	*mock.Call
}

// FindJobIDsWithBridge is a helper method to define mock.On call
//   - ctx context.Context
//   - name string
func (_e *ORM_Expecter) FindJobIDsWithBridge(ctx interface{}, name interface{}) *ORM_FindJobIDsWithBridge_Call {
	return &ORM_FindJobIDsWithBridge_Call{Call: _e.mock.On("FindJobIDsWithBridge", ctx, name)}
}

func (_c *ORM_FindJobIDsWithBridge_Call) Run(run func(ctx context.Context, name string)) *ORM_FindJobIDsWithBridge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *ORM_FindJobIDsWithBridge_Call) Return(_a0 []int32, _a1 error) *ORM_FindJobIDsWithBridge_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindJobIDsWithBridge_Call) RunAndReturn(run func(context.Context, string) ([]int32, error)) *ORM_FindJobIDsWithBridge_Call {
	_c.Call.Return(run)
	return _c
}

// FindJobTx provides a mock function with given fields: ctx, id
func (_m *ORM) FindJobTx(ctx context.Context, id int32) (job.Job, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindJobTx")
	}

	var r0 job.Job
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int32) (job.Job, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32) job.Job); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(job.Job)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindJobTx_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJobTx'
type ORM_FindJobTx_Call struct {
	*mock.Call
}

// FindJobTx is a helper method to define mock.On call
//   - ctx context.Context
//   - id int32
func (_e *ORM_Expecter) FindJobTx(ctx interface{}, id interface{}) *ORM_FindJobTx_Call {
	return &ORM_FindJobTx_Call{Call: _e.mock.On("FindJobTx", ctx, id)}
}

func (_c *ORM_FindJobTx_Call) Run(run func(ctx context.Context, id int32)) *ORM_FindJobTx_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int32))
	})
	return _c
}

func (_c *ORM_FindJobTx_Call) Return(_a0 job.Job, _a1 error) *ORM_FindJobTx_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindJobTx_Call) RunAndReturn(run func(context.Context, int32) (job.Job, error)) *ORM_FindJobTx_Call {
	_c.Call.Return(run)
	return _c
}

// FindJobWithoutSpecErrors provides a mock function with given fields: ctx, id
func (_m *ORM) FindJobWithoutSpecErrors(ctx context.Context, id int32) (job.Job, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindJobWithoutSpecErrors")
	}

	var r0 job.Job
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int32) (job.Job, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32) job.Job); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(job.Job)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindJobWithoutSpecErrors_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJobWithoutSpecErrors'
type ORM_FindJobWithoutSpecErrors_Call struct {
	*mock.Call
}

// FindJobWithoutSpecErrors is a helper method to define mock.On call
//   - ctx context.Context
//   - id int32
func (_e *ORM_Expecter) FindJobWithoutSpecErrors(ctx interface{}, id interface{}) *ORM_FindJobWithoutSpecErrors_Call {
	return &ORM_FindJobWithoutSpecErrors_Call{Call: _e.mock.On("FindJobWithoutSpecErrors", ctx, id)}
}

func (_c *ORM_FindJobWithoutSpecErrors_Call) Run(run func(ctx context.Context, id int32)) *ORM_FindJobWithoutSpecErrors_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int32))
	})
	return _c
}

func (_c *ORM_FindJobWithoutSpecErrors_Call) Return(jb job.Job, err error) *ORM_FindJobWithoutSpecErrors_Call {
	_c.Call.Return(jb, err)
	return _c
}

func (_c *ORM_FindJobWithoutSpecErrors_Call) RunAndReturn(run func(context.Context, int32) (job.Job, error)) *ORM_FindJobWithoutSpecErrors_Call {
	_c.Call.Return(run)
	return _c
}

// FindJobs provides a mock function with given fields: ctx, offset, limit
func (_m *ORM) FindJobs(ctx context.Context, offset int, limit int) ([]job.Job, int, error) {
	ret := _m.Called(ctx, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for FindJobs")
	}

	var r0 []job.Job
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) ([]job.Job, int, error)); ok {
		return rf(ctx, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) []job.Job); ok {
		r0 = rf(ctx, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]job.Job)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) int); ok {
		r1 = rf(ctx, offset, limit)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int, int) error); ok {
		r2 = rf(ctx, offset, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ORM_FindJobs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJobs'
type ORM_FindJobs_Call struct {
	*mock.Call
}

// FindJobs is a helper method to define mock.On call
//   - ctx context.Context
//   - offset int
//   - limit int
func (_e *ORM_Expecter) FindJobs(ctx interface{}, offset interface{}, limit interface{}) *ORM_FindJobs_Call {
	return &ORM_FindJobs_Call{Call: _e.mock.On("FindJobs", ctx, offset, limit)}
}

func (_c *ORM_FindJobs_Call) Run(run func(ctx context.Context, offset int, limit int)) *ORM_FindJobs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *ORM_FindJobs_Call) Return(_a0 []job.Job, _a1 int, _a2 error) *ORM_FindJobs_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *ORM_FindJobs_Call) RunAndReturn(run func(context.Context, int, int) ([]job.Job, int, error)) *ORM_FindJobs_Call {
	_c.Call.Return(run)
	return _c
}

// FindJobsByPipelineSpecIDs provides a mock function with given fields: ctx, ids
func (_m *ORM) FindJobsByPipelineSpecIDs(ctx context.Context, ids []int32) ([]job.Job, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for FindJobsByPipelineSpecIDs")
	}

	var r0 []job.Job
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int32) ([]job.Job, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int32) []job.Job); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]job.Job)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int32) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindJobsByPipelineSpecIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindJobsByPipelineSpecIDs'
type ORM_FindJobsByPipelineSpecIDs_Call struct {
	*mock.Call
}

// FindJobsByPipelineSpecIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int32
func (_e *ORM_Expecter) FindJobsByPipelineSpecIDs(ctx interface{}, ids interface{}) *ORM_FindJobsByPipelineSpecIDs_Call {
	return &ORM_FindJobsByPipelineSpecIDs_Call{Call: _e.mock.On("FindJobsByPipelineSpecIDs", ctx, ids)}
}

func (_c *ORM_FindJobsByPipelineSpecIDs_Call) Run(run func(ctx context.Context, ids []int32)) *ORM_FindJobsByPipelineSpecIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int32))
	})
	return _c
}

func (_c *ORM_FindJobsByPipelineSpecIDs_Call) Return(_a0 []job.Job, _a1 error) *ORM_FindJobsByPipelineSpecIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindJobsByPipelineSpecIDs_Call) RunAndReturn(run func(context.Context, []int32) ([]job.Job, error)) *ORM_FindJobsByPipelineSpecIDs_Call {
	_c.Call.Return(run)
	return _c
}

// FindOCR2JobIDByAddress provides a mock function with given fields: ctx, contractID, feedID
func (_m *ORM) FindOCR2JobIDByAddress(ctx context.Context, contractID string, feedID *common.Hash) (int32, error) {
	ret := _m.Called(ctx, contractID, feedID)

	if len(ret) == 0 {
		panic("no return value specified for FindOCR2JobIDByAddress")
	}

	var r0 int32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *common.Hash) (int32, error)); ok {
		return rf(ctx, contractID, feedID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *common.Hash) int32); ok {
		r0 = rf(ctx, contractID, feedID)
	} else {
		r0 = ret.Get(0).(int32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *common.Hash) error); ok {
		r1 = rf(ctx, contractID, feedID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindOCR2JobIDByAddress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOCR2JobIDByAddress'
type ORM_FindOCR2JobIDByAddress_Call struct {
	*mock.Call
}

// FindOCR2JobIDByAddress is a helper method to define mock.On call
//   - ctx context.Context
//   - contractID string
//   - feedID *common.Hash
func (_e *ORM_Expecter) FindOCR2JobIDByAddress(ctx interface{}, contractID interface{}, feedID interface{}) *ORM_FindOCR2JobIDByAddress_Call {
	return &ORM_FindOCR2JobIDByAddress_Call{Call: _e.mock.On("FindOCR2JobIDByAddress", ctx, contractID, feedID)}
}

func (_c *ORM_FindOCR2JobIDByAddress_Call) Run(run func(ctx context.Context, contractID string, feedID *common.Hash)) *ORM_FindOCR2JobIDByAddress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*common.Hash))
	})
	return _c
}

func (_c *ORM_FindOCR2JobIDByAddress_Call) Return(_a0 int32, _a1 error) *ORM_FindOCR2JobIDByAddress_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindOCR2JobIDByAddress_Call) RunAndReturn(run func(context.Context, string, *common.Hash) (int32, error)) *ORM_FindOCR2JobIDByAddress_Call {
	_c.Call.Return(run)
	return _c
}

// FindPipelineRunByID provides a mock function with given fields: ctx, id
func (_m *ORM) FindPipelineRunByID(ctx context.Context, id int64) (pipeline.Run, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindPipelineRunByID")
	}

	var r0 pipeline.Run
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (pipeline.Run, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) pipeline.Run); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(pipeline.Run)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindPipelineRunByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindPipelineRunByID'
type ORM_FindPipelineRunByID_Call struct {
	*mock.Call
}

// FindPipelineRunByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) FindPipelineRunByID(ctx interface{}, id interface{}) *ORM_FindPipelineRunByID_Call {
	return &ORM_FindPipelineRunByID_Call{Call: _e.mock.On("FindPipelineRunByID", ctx, id)}
}

func (_c *ORM_FindPipelineRunByID_Call) Run(run func(ctx context.Context, id int64)) *ORM_FindPipelineRunByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_FindPipelineRunByID_Call) Return(_a0 pipeline.Run, _a1 error) *ORM_FindPipelineRunByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindPipelineRunByID_Call) RunAndReturn(run func(context.Context, int64) (pipeline.Run, error)) *ORM_FindPipelineRunByID_Call {
	_c.Call.Return(run)
	return _c
}

// FindPipelineRunIDsByJobID provides a mock function with given fields: ctx, jobID, offset, limit
func (_m *ORM) FindPipelineRunIDsByJobID(ctx context.Context, jobID int32, offset int, limit int) ([]int64, error) {
	ret := _m.Called(ctx, jobID, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for FindPipelineRunIDsByJobID")
	}

	var r0 []int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int32, int, int) ([]int64, error)); ok {
		return rf(ctx, jobID, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32, int, int) []int64); ok {
		r0 = rf(ctx, jobID, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32, int, int) error); ok {
		r1 = rf(ctx, jobID, offset, limit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindPipelineRunIDsByJobID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindPipelineRunIDsByJobID'
type ORM_FindPipelineRunIDsByJobID_Call struct {
	*mock.Call
}

// FindPipelineRunIDsByJobID is a helper method to define mock.On call
//   - ctx context.Context
//   - jobID int32
//   - offset int
//   - limit int
func (_e *ORM_Expecter) FindPipelineRunIDsByJobID(ctx interface{}, jobID interface{}, offset interface{}, limit interface{}) *ORM_FindPipelineRunIDsByJobID_Call {
	return &ORM_FindPipelineRunIDsByJobID_Call{Call: _e.mock.On("FindPipelineRunIDsByJobID", ctx, jobID, offset, limit)}
}

func (_c *ORM_FindPipelineRunIDsByJobID_Call) Run(run func(ctx context.Context, jobID int32, offset int, limit int)) *ORM_FindPipelineRunIDsByJobID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int32), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *ORM_FindPipelineRunIDsByJobID_Call) Return(ids []int64, err error) *ORM_FindPipelineRunIDsByJobID_Call {
	_c.Call.Return(ids, err)
	return _c
}

func (_c *ORM_FindPipelineRunIDsByJobID_Call) RunAndReturn(run func(context.Context, int32, int, int) ([]int64, error)) *ORM_FindPipelineRunIDsByJobID_Call {
	_c.Call.Return(run)
	return _c
}

// FindPipelineRunsByIDs provides a mock function with given fields: ctx, ids
func (_m *ORM) FindPipelineRunsByIDs(ctx context.Context, ids []int64) ([]pipeline.Run, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for FindPipelineRunsByIDs")
	}

	var r0 []pipeline.Run
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]pipeline.Run, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []pipeline.Run); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]pipeline.Run)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindPipelineRunsByIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindPipelineRunsByIDs'
type ORM_FindPipelineRunsByIDs_Call struct {
	*mock.Call
}

// FindPipelineRunsByIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *ORM_Expecter) FindPipelineRunsByIDs(ctx interface{}, ids interface{}) *ORM_FindPipelineRunsByIDs_Call {
	return &ORM_FindPipelineRunsByIDs_Call{Call: _e.mock.On("FindPipelineRunsByIDs", ctx, ids)}
}

func (_c *ORM_FindPipelineRunsByIDs_Call) Run(run func(ctx context.Context, ids []int64)) *ORM_FindPipelineRunsByIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *ORM_FindPipelineRunsByIDs_Call) Return(runs []pipeline.Run, err error) *ORM_FindPipelineRunsByIDs_Call {
	_c.Call.Return(runs, err)
	return _c
}

func (_c *ORM_FindPipelineRunsByIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]pipeline.Run, error)) *ORM_FindPipelineRunsByIDs_Call {
	_c.Call.Return(run)
	return _c
}

// FindSpecError provides a mock function with given fields: ctx, id
func (_m *ORM) FindSpecError(ctx context.Context, id int64) (job.SpecError, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for FindSpecError")
	}

	var r0 job.SpecError
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (job.SpecError, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) job.SpecError); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(job.SpecError)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindSpecError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindSpecError'
type ORM_FindSpecError_Call struct {
	*mock.Call
}

// FindSpecError is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) FindSpecError(ctx interface{}, id interface{}) *ORM_FindSpecError_Call {
	return &ORM_FindSpecError_Call{Call: _e.mock.On("FindSpecError", ctx, id)}
}

func (_c *ORM_FindSpecError_Call) Run(run func(ctx context.Context, id int64)) *ORM_FindSpecError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_FindSpecError_Call) Return(_a0 job.SpecError, _a1 error) *ORM_FindSpecError_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindSpecError_Call) RunAndReturn(run func(context.Context, int64) (job.SpecError, error)) *ORM_FindSpecError_Call {
	_c.Call.Return(run)
	return _c
}

// FindSpecErrorsByJobIDs provides a mock function with given fields: ctx, ids
func (_m *ORM) FindSpecErrorsByJobIDs(ctx context.Context, ids []int32) ([]job.SpecError, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for FindSpecErrorsByJobIDs")
	}

	var r0 []job.SpecError
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int32) ([]job.SpecError, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int32) []job.SpecError); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]job.SpecError)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int32) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindSpecErrorsByJobIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindSpecErrorsByJobIDs'
type ORM_FindSpecErrorsByJobIDs_Call struct {
	*mock.Call
}

// FindSpecErrorsByJobIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int32
func (_e *ORM_Expecter) FindSpecErrorsByJobIDs(ctx interface{}, ids interface{}) *ORM_FindSpecErrorsByJobIDs_Call {
	return &ORM_FindSpecErrorsByJobIDs_Call{Call: _e.mock.On("FindSpecErrorsByJobIDs", ctx, ids)}
}

func (_c *ORM_FindSpecErrorsByJobIDs_Call) Run(run func(ctx context.Context, ids []int32)) *ORM_FindSpecErrorsByJobIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int32))
	})
	return _c
}

func (_c *ORM_FindSpecErrorsByJobIDs_Call) Return(_a0 []job.SpecError, _a1 error) *ORM_FindSpecErrorsByJobIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindSpecErrorsByJobIDs_Call) RunAndReturn(run func(context.Context, []int32) ([]job.SpecError, error)) *ORM_FindSpecErrorsByJobIDs_Call {
	_c.Call.Return(run)
	return _c
}

// FindTaskResultByRunIDAndTaskName provides a mock function with given fields: ctx, runID, taskName
func (_m *ORM) FindTaskResultByRunIDAndTaskName(ctx context.Context, runID int64, taskName string) ([]byte, error) {
	ret := _m.Called(ctx, runID, taskName)

	if len(ret) == 0 {
		panic("no return value specified for FindTaskResultByRunIDAndTaskName")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string) ([]byte, error)); ok {
		return rf(ctx, runID, taskName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, string) []byte); ok {
		r0 = rf(ctx, runID, taskName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, string) error); ok {
		r1 = rf(ctx, runID, taskName)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindTaskResultByRunIDAndTaskName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTaskResultByRunIDAndTaskName'
type ORM_FindTaskResultByRunIDAndTaskName_Call struct {
	*mock.Call
}

// FindTaskResultByRunIDAndTaskName is a helper method to define mock.On call
//   - ctx context.Context
//   - runID int64
//   - taskName string
func (_e *ORM_Expecter) FindTaskResultByRunIDAndTaskName(ctx interface{}, runID interface{}, taskName interface{}) *ORM_FindTaskResultByRunIDAndTaskName_Call {
	return &ORM_FindTaskResultByRunIDAndTaskName_Call{Call: _e.mock.On("FindTaskResultByRunIDAndTaskName", ctx, runID, taskName)}
}

func (_c *ORM_FindTaskResultByRunIDAndTaskName_Call) Run(run func(ctx context.Context, runID int64, taskName string)) *ORM_FindTaskResultByRunIDAndTaskName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(string))
	})
	return _c
}

func (_c *ORM_FindTaskResultByRunIDAndTaskName_Call) Return(_a0 []byte, _a1 error) *ORM_FindTaskResultByRunIDAndTaskName_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindTaskResultByRunIDAndTaskName_Call) RunAndReturn(run func(context.Context, int64, string) ([]byte, error)) *ORM_FindTaskResultByRunIDAndTaskName_Call {
	_c.Call.Return(run)
	return _c
}

// InsertJob provides a mock function with given fields: ctx, _a1
func (_m *ORM) InsertJob(ctx context.Context, _a1 *job.Job) error {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for InsertJob")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *job.Job) error); ok {
		r0 = rf(ctx, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_InsertJob_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertJob'
type ORM_InsertJob_Call struct {
	*mock.Call
}

// InsertJob is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 *job.Job
func (_e *ORM_Expecter) InsertJob(ctx interface{}, _a1 interface{}) *ORM_InsertJob_Call {
	return &ORM_InsertJob_Call{Call: _e.mock.On("InsertJob", ctx, _a1)}
}

func (_c *ORM_InsertJob_Call) Run(run func(ctx context.Context, _a1 *job.Job)) *ORM_InsertJob_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*job.Job))
	})
	return _c
}

func (_c *ORM_InsertJob_Call) Return(_a0 error) *ORM_InsertJob_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_InsertJob_Call) RunAndReturn(run func(context.Context, *job.Job) error) *ORM_InsertJob_Call {
	_c.Call.Return(run)
	return _c
}

// InsertWebhookSpec provides a mock function with given fields: ctx, webhookSpec
func (_m *ORM) InsertWebhookSpec(ctx context.Context, webhookSpec *job.WebhookSpec) error {
	ret := _m.Called(ctx, webhookSpec)

	if len(ret) == 0 {
		panic("no return value specified for InsertWebhookSpec")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *job.WebhookSpec) error); ok {
		r0 = rf(ctx, webhookSpec)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_InsertWebhookSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertWebhookSpec'
type ORM_InsertWebhookSpec_Call struct {
	*mock.Call
}

// InsertWebhookSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - webhookSpec *job.WebhookSpec
func (_e *ORM_Expecter) InsertWebhookSpec(ctx interface{}, webhookSpec interface{}) *ORM_InsertWebhookSpec_Call {
	return &ORM_InsertWebhookSpec_Call{Call: _e.mock.On("InsertWebhookSpec", ctx, webhookSpec)}
}

func (_c *ORM_InsertWebhookSpec_Call) Run(run func(ctx context.Context, webhookSpec *job.WebhookSpec)) *ORM_InsertWebhookSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*job.WebhookSpec))
	})
	return _c
}

func (_c *ORM_InsertWebhookSpec_Call) Return(_a0 error) *ORM_InsertWebhookSpec_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_InsertWebhookSpec_Call) RunAndReturn(run func(context.Context, *job.WebhookSpec) error) *ORM_InsertWebhookSpec_Call {
	_c.Call.Return(run)
	return _c
}

// PipelineRuns provides a mock function with given fields: ctx, jobID, offset, size
func (_m *ORM) PipelineRuns(ctx context.Context, jobID *int32, offset int, size int) ([]pipeline.Run, int, error) {
	ret := _m.Called(ctx, jobID, offset, size)

	if len(ret) == 0 {
		panic("no return value specified for PipelineRuns")
	}

	var r0 []pipeline.Run
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, *int32, int, int) ([]pipeline.Run, int, error)); ok {
		return rf(ctx, jobID, offset, size)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *int32, int, int) []pipeline.Run); ok {
		r0 = rf(ctx, jobID, offset, size)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]pipeline.Run)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *int32, int, int) int); ok {
		r1 = rf(ctx, jobID, offset, size)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(context.Context, *int32, int, int) error); ok {
		r2 = rf(ctx, jobID, offset, size)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ORM_PipelineRuns_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PipelineRuns'
type ORM_PipelineRuns_Call struct {
	*mock.Call
}

// PipelineRuns is a helper method to define mock.On call
//   - ctx context.Context
//   - jobID *int32
//   - offset int
//   - size int
func (_e *ORM_Expecter) PipelineRuns(ctx interface{}, jobID interface{}, offset interface{}, size interface{}) *ORM_PipelineRuns_Call {
	return &ORM_PipelineRuns_Call{Call: _e.mock.On("PipelineRuns", ctx, jobID, offset, size)}
}

func (_c *ORM_PipelineRuns_Call) Run(run func(ctx context.Context, jobID *int32, offset int, size int)) *ORM_PipelineRuns_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*int32), args[2].(int), args[3].(int))
	})
	return _c
}

func (_c *ORM_PipelineRuns_Call) Return(_a0 []pipeline.Run, _a1 int, _a2 error) *ORM_PipelineRuns_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *ORM_PipelineRuns_Call) RunAndReturn(run func(context.Context, *int32, int, int) ([]pipeline.Run, int, error)) *ORM_PipelineRuns_Call {
	_c.Call.Return(run)
	return _c
}

// RecordError provides a mock function with given fields: ctx, jobID, description
func (_m *ORM) RecordError(ctx context.Context, jobID int32, description string) error {
	ret := _m.Called(ctx, jobID, description)

	if len(ret) == 0 {
		panic("no return value specified for RecordError")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int32, string) error); ok {
		r0 = rf(ctx, jobID, description)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_RecordError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecordError'
type ORM_RecordError_Call struct {
	*mock.Call
}

// RecordError is a helper method to define mock.On call
//   - ctx context.Context
//   - jobID int32
//   - description string
func (_e *ORM_Expecter) RecordError(ctx interface{}, jobID interface{}, description interface{}) *ORM_RecordError_Call {
	return &ORM_RecordError_Call{Call: _e.mock.On("RecordError", ctx, jobID, description)}
}

func (_c *ORM_RecordError_Call) Run(run func(ctx context.Context, jobID int32, description string)) *ORM_RecordError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int32), args[2].(string))
	})
	return _c
}

func (_c *ORM_RecordError_Call) Return(_a0 error) *ORM_RecordError_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_RecordError_Call) RunAndReturn(run func(context.Context, int32, string) error) *ORM_RecordError_Call {
	_c.Call.Return(run)
	return _c
}

// TryRecordError provides a mock function with given fields: ctx, jobID, description
func (_m *ORM) TryRecordError(ctx context.Context, jobID int32, description string) {
	_m.Called(ctx, jobID, description)
}

// ORM_TryRecordError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TryRecordError'
type ORM_TryRecordError_Call struct {
	*mock.Call
}

// TryRecordError is a helper method to define mock.On call
//   - ctx context.Context
//   - jobID int32
//   - description string
func (_e *ORM_Expecter) TryRecordError(ctx interface{}, jobID interface{}, description interface{}) *ORM_TryRecordError_Call {
	return &ORM_TryRecordError_Call{Call: _e.mock.On("TryRecordError", ctx, jobID, description)}
}

func (_c *ORM_TryRecordError_Call) Run(run func(ctx context.Context, jobID int32, description string)) *ORM_TryRecordError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int32), args[2].(string))
	})
	return _c
}

func (_c *ORM_TryRecordError_Call) Return() *ORM_TryRecordError_Call {
	_c.Call.Return()
	return _c
}

func (_c *ORM_TryRecordError_Call) RunAndReturn(run func(context.Context, int32, string)) *ORM_TryRecordError_Call {
	_c.Call.Return(run)
	return _c
}

// WithDataSource provides a mock function with given fields: source
func (_m *ORM) WithDataSource(source sqlutil.DataSource) job.ORM {
	ret := _m.Called(source)

	if len(ret) == 0 {
		panic("no return value specified for WithDataSource")
	}

	var r0 job.ORM
	if rf, ok := ret.Get(0).(func(sqlutil.DataSource) job.ORM); ok {
		r0 = rf(source)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(job.ORM)
		}
	}

	return r0
}

// ORM_WithDataSource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithDataSource'
type ORM_WithDataSource_Call struct {
	*mock.Call
}

// WithDataSource is a helper method to define mock.On call
//   - source sqlutil.DataSource
func (_e *ORM_Expecter) WithDataSource(source interface{}) *ORM_WithDataSource_Call {
	return &ORM_WithDataSource_Call{Call: _e.mock.On("WithDataSource", source)}
}

func (_c *ORM_WithDataSource_Call) Run(run func(source sqlutil.DataSource)) *ORM_WithDataSource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(sqlutil.DataSource))
	})
	return _c
}

func (_c *ORM_WithDataSource_Call) Return(_a0 job.ORM) *ORM_WithDataSource_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_WithDataSource_Call) RunAndReturn(run func(sqlutil.DataSource) job.ORM) *ORM_WithDataSource_Call {
	_c.Call.Return(run)
	return _c
}

// NewORM creates a new instance of ORM. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewORM(t interface {
	mock.TestingT
	Cleanup(func())
}) *ORM {
	mock := &ORM{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
