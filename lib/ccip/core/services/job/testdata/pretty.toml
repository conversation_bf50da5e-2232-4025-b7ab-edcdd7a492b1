relay              = "evm"
pluginType         = "median"
contractID         = "foo"
ocrKeyBundleID     = "bar"
transmitterID      = "baz"
contractConfigConfirmations = 1
contractConfigTrackerPollInterval = "1s"

[relayConfig]
chainID = 1337
fromBlock = 42

[relayConfig.chainReader.contracts.median]
contractABI = '''
[
  {
    "anonymous": false,
    "inputs": [
      {
        "indexed": true,
        "internalType": "address",
        "name": "requester",
        "type": "address"
      },
      {
        "indexed": false,
        "internalType": "bytes32",
        "name": "configDigest",
        "type": "bytes32"
      },
      {
        "indexed": false,
        "internalType": "uint32",
        "name": "epoch",
        "type": "uint32"
      },
      {
        "indexed": false,
        "internalType": "uint8",
        "name": "round",
        "type": "uint8"
      }
    ],
    "name": "RoundRequested",
    "type": "event"
  },
  {
    "inputs": [],
    "name": "latestTransmissionDetails",
    "outputs": [
      {
        "internalType": "bytes32",
        "name": "configDigest",
        "type": "bytes32"
      },
      {
        "internalType": "uint32",
        "name": "epoch",
        "type": "uint32"
      },
      {
        "internalType": "uint8",
        "name": "round",
        "type": "uint8"
      },
      {
        "internalType": "int192",
        "name": "latestAnswer_",
        "type": "int192"
      },
      {
        "internalType": "uint64",
        "name": "latestTimestamp_",
        "type": "uint64"
      }
    ],
    "stateMutability": "view",
    "type": "function"
  }
]
'''

[relayConfig.chainReader.contracts.median.configs]
LatestRoundRequested = '''
{
  "chainSpecificName": "RoundRequested",
  "readType": "event"
}
'''
LatestTransmissionDetails = '''
{
  "chainSpecificName": "latestTransmissionDetails",
  "outputModifications": [
    {
      "Fields": [
        "LatestTimestamp_"
      ],
      "Type": "epoch to time"
    },
    {
      "Fields": {
        "LatestAnswer_": "LatestAnswer",
        "LatestTimestamp_": "LatestTimestamp"
      },
      "Type": "rename"
    }
  ]
}
'''

[relayConfig.codec.configs.MedianReport]
typeABI = '''
[
  {
    "Name": "Timestamp",
    "Type": "uint32"
  },
  {
    "Name": "Observers",
    "Type": "bytes32"
  },
  {
    "Name": "Observations",
    "Type": "int192[]"
  },
  {
    "Name": "JuelsPerFeeCoin",
    "Type": "int192"
  }
]
'''

[onchainSigningStrategy]
strategyName = 'single-chain'

[onchainSigningStrategy.config]
evm = ''
publicKey = '0xdeadbeef'

[pluginConfig]
juelsPerFeeCoinSource = """
  // data source 1
  ds1          [type=bridge name="%s"];
  ds1_parse    [type=jsonparse path="data"];
  ds1_multiply [type=multiply times=2];

  // data source 2
  ds2          [type=http method=GET url="%s"];
  ds2_parse    [type=jsonparse path="data"];
  ds2_multiply [type=multiply times=2];

  ds1 -> ds1_parse -> ds1_multiply -> answer1;
  ds2 -> ds2_parse -> ds2_multiply -> answer1;

  answer1 [type=median index=0];
"""