contractID = 'foo'
relay = 'evm'
chainID = ''
p2pv2Bootstrappers = []
ocrKeyBundleID = 'bar'
monitoringEndpoint = ''
transmitterID = 'baz'
blockchainTimeout = '0s'
contractConfigTrackerPollInterval = '1s'
contractConfigConfirmations = 1
pluginType = 'median'
captureEATelemetry = false
captureAutomationCustomTelemetry = false

[relayConfig]
chainID = 1337
fromBlock = 42

[relayConfig.chainReader]
[relayConfig.chainReader.contracts]
[relayConfig.chainReader.contracts.median]
contractABI = "[\n  {\n    \"anonymous\": false,\n    \"inputs\": [\n      {\n        \"indexed\": true,\n        \"internalType\": \"address\",\n        \"name\": \"requester\",\n        \"type\": \"address\"\n      },\n      {\n        \"indexed\": false,\n        \"internalType\": \"bytes32\",\n        \"name\": \"configDigest\",\n        \"type\": \"bytes32\"\n      },\n      {\n        \"indexed\": false,\n        \"internalType\": \"uint32\",\n        \"name\": \"epoch\",\n        \"type\": \"uint32\"\n      },\n      {\n        \"indexed\": false,\n        \"internalType\": \"uint8\",\n        \"name\": \"round\",\n        \"type\": \"uint8\"\n      }\n    ],\n    \"name\": \"RoundRequested\",\n    \"type\": \"event\"\n  },\n  {\n    \"inputs\": [],\n    \"name\": \"latestTransmissionDetails\",\n    \"outputs\": [\n      {\n        \"internalType\": \"bytes32\",\n        \"name\": \"configDigest\",\n        \"type\": \"bytes32\"\n      },\n      {\n        \"internalType\": \"uint32\",\n        \"name\": \"epoch\",\n        \"type\": \"uint32\"\n      },\n      {\n        \"internalType\": \"uint8\",\n        \"name\": \"round\",\n        \"type\": \"uint8\"\n      },\n      {\n        \"internalType\": \"int192\",\n        \"name\": \"latestAnswer_\",\n        \"type\": \"int192\"\n      },\n      {\n        \"internalType\": \"uint64\",\n        \"name\": \"latestTimestamp_\",\n        \"type\": \"uint64\"\n      }\n    ],\n    \"stateMutability\": \"view\",\n    \"type\": \"function\"\n  }\n]\n"

[relayConfig.chainReader.contracts.median.configs]
LatestRoundRequested = "{\n  \"chainSpecificName\": \"RoundRequested\",\n  \"readType\": \"event\"\n}\n"
LatestTransmissionDetails = "{\n  \"chainSpecificName\": \"latestTransmissionDetails\",\n  \"outputModifications\": [\n    {\n      \"Fields\": [\n        \"LatestTimestamp_\"\n      ],\n      \"Type\": \"epoch to time\"\n    },\n    {\n      \"Fields\": {\n        \"LatestAnswer_\": \"LatestAnswer\",\n        \"LatestTimestamp_\": \"LatestTimestamp\"\n      },\n      \"Type\": \"rename\"\n    }\n  ]\n}\n"

[relayConfig.codec]
[relayConfig.codec.configs]
[relayConfig.codec.configs.MedianReport]
typeABI = "[\n  {\n    \"Name\": \"Timestamp\",\n    \"Type\": \"uint32\"\n  },\n  {\n    \"Name\": \"Observers\",\n    \"Type\": \"bytes32\"\n  },\n  {\n    \"Name\": \"Observations\",\n    \"Type\": \"int192[]\"\n  },\n  {\n    \"Name\": \"JuelsPerFeeCoin\",\n    \"Type\": \"int192\"\n  }\n]\n"

[onchainSigningStrategy]
strategyName = 'single-chain'

[onchainSigningStrategy.config]
evm = ''
publicKey = '0xdeadbeef'

[pluginConfig]
juelsPerFeeCoinSource = "  // data source 1\n  ds1          [type=bridge name=\"%s\"];\n  ds1_parse    [type=jsonparse path=\"data\"];\n  ds1_multiply [type=multiply times=2];\n\n  // data source 2\n  ds2          [type=http method=GET url=\"%s\"];\n  ds2_parse    [type=jsonparse path=\"data\"];\n  ds2_multiply [type=multiply times=2];\n\n  ds1 -> ds1_parse -> ds1_multiply -> answer1;\n  ds2 -> ds2_parse -> ds2_multiply -> answer1;\n\n  answer1 [type=median index=0];\n"
