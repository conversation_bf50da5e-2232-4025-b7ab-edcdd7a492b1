package job_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	commonworkflows "github.com/smartcontractkit/chainlink-common/pkg/workflows"

	"github.com/smartcontractkit/chainlink/v2/core/internal/testutils"
	"github.com/smartcontractkit/chainlink/v2/core/services/job"
)

const anyYamlSpec = `
name: "wf-name"
owner: "0x00000000000000000000000000000000000000aa"
triggers:
  - id: "mercury-trigger@1.0.0"
    config:
      feedIds:
        - "0x1111111111111111111100000000000000000000000000000000000000000000"
        - "0x2222222222222222222200000000000000000000000000000000000000000000"
        - "0x3333333333333333333300000000000000000000000000000000000000000000"

consensus:
  - id: "offchain_reporting@2.0.0"
    ref: "evm_median"
    inputs:
      observations:
        - "$(trigger.outputs)"
    config:
      aggregation_method: "data_feeds_2_0"
      aggregation_config:
        "0x1111111111111111111100000000000000000000000000000000000000000000":
          deviation: "0.001"
          heartbeat: 3600
        "0x2222222222222222222200000000000000000000000000000000000000000000":
          deviation: "0.001"
          heartbeat: 3600
        "0x3333333333333333333300000000000000000000000000000000000000000000":
          deviation: "0.001"
          heartbeat: 3600
      encoder: "EVM"
      encoder_config:
        abi: "mercury_reports bytes[]"

targets:
  - id: "write_polygon-testnet-mumbai@3.0.0"
    inputs:
      report: "$(evm_median.outputs.report)"
    config:
      address: "******************************************"
      params: ["$(report)"]
      abi: "receive(report bytes)"
  - id: "write_ethereum-testnet-sepolia@4.0.0"
    inputs:
      report: "$(evm_median.outputs.report)"
    config:
      address: "******************************************"
      params: ["$(report)"]
      abi: "receive(report bytes)"
`

func TestYamlSpecFactory_GetSpec(t *testing.T) {
	t.Parallel()

	actual, err := job.YAMLSpecFactory{}.Spec(testutils.Context(t), []byte(anyYamlSpec), []byte{})
	require.NoError(t, err)

	expected, err := commonworkflows.ParseWorkflowSpecYaml(anyYamlSpec)
	require.NoError(t, err)

	require.Equal(t, expected, actual)
}

func TestYamlSpecFactory_GetRawSpec(t *testing.T) {
	t.Parallel()

	actual, err := job.YAMLSpecFactory{}.RawSpec(testutils.Context(t), anyYamlSpec)
	require.NoError(t, err)
	require.Equal(t, []byte(anyYamlSpec), actual)
}
