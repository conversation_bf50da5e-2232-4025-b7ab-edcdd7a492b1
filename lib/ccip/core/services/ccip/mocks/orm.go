// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	ccip "github.com/smartcontractkit/chainlink/v2/core/services/ccip"

	mock "github.com/stretchr/testify/mock"

	time "time"
)

// ORM is an autogenerated mock type for the ORM type
type ORM struct {
	mock.Mock
}

type ORM_Expecter struct {
	mock *mock.Mock
}

func (_m *ORM) EXPECT() *ORM_Expecter {
	return &ORM_Expecter{mock: &_m.Mock}
}

// GetGasPricesByDestChain provides a mock function with given fields: ctx, destChainSelector
func (_m *ORM) GetGasPricesByDestChain(ctx context.Context, destChainSelector uint64) ([]ccip.GasPrice, error) {
	ret := _m.Called(ctx, destChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for GetGasPricesByDestChain")
	}

	var r0 []ccip.GasPrice
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) ([]ccip.GasPrice, error)); ok {
		return rf(ctx, destChainSelector)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) []ccip.GasPrice); ok {
		r0 = rf(ctx, destChainSelector)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ccip.GasPrice)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, destChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetGasPricesByDestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGasPricesByDestChain'
type ORM_GetGasPricesByDestChain_Call struct {
	*mock.Call
}

// GetGasPricesByDestChain is a helper method to define mock.On call
//   - ctx context.Context
//   - destChainSelector uint64
func (_e *ORM_Expecter) GetGasPricesByDestChain(ctx interface{}, destChainSelector interface{}) *ORM_GetGasPricesByDestChain_Call {
	return &ORM_GetGasPricesByDestChain_Call{Call: _e.mock.On("GetGasPricesByDestChain", ctx, destChainSelector)}
}

func (_c *ORM_GetGasPricesByDestChain_Call) Run(run func(ctx context.Context, destChainSelector uint64)) *ORM_GetGasPricesByDestChain_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *ORM_GetGasPricesByDestChain_Call) Return(_a0 []ccip.GasPrice, _a1 error) *ORM_GetGasPricesByDestChain_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetGasPricesByDestChain_Call) RunAndReturn(run func(context.Context, uint64) ([]ccip.GasPrice, error)) *ORM_GetGasPricesByDestChain_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenPricesByDestChain provides a mock function with given fields: ctx, destChainSelector
func (_m *ORM) GetTokenPricesByDestChain(ctx context.Context, destChainSelector uint64) ([]ccip.TokenPrice, error) {
	ret := _m.Called(ctx, destChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenPricesByDestChain")
	}

	var r0 []ccip.TokenPrice
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64) ([]ccip.TokenPrice, error)); ok {
		return rf(ctx, destChainSelector)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64) []ccip.TokenPrice); ok {
		r0 = rf(ctx, destChainSelector)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ccip.TokenPrice)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64) error); ok {
		r1 = rf(ctx, destChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetTokenPricesByDestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenPricesByDestChain'
type ORM_GetTokenPricesByDestChain_Call struct {
	*mock.Call
}

// GetTokenPricesByDestChain is a helper method to define mock.On call
//   - ctx context.Context
//   - destChainSelector uint64
func (_e *ORM_Expecter) GetTokenPricesByDestChain(ctx interface{}, destChainSelector interface{}) *ORM_GetTokenPricesByDestChain_Call {
	return &ORM_GetTokenPricesByDestChain_Call{Call: _e.mock.On("GetTokenPricesByDestChain", ctx, destChainSelector)}
}

func (_c *ORM_GetTokenPricesByDestChain_Call) Run(run func(ctx context.Context, destChainSelector uint64)) *ORM_GetTokenPricesByDestChain_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64))
	})
	return _c
}

func (_c *ORM_GetTokenPricesByDestChain_Call) Return(_a0 []ccip.TokenPrice, _a1 error) *ORM_GetTokenPricesByDestChain_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetTokenPricesByDestChain_Call) RunAndReturn(run func(context.Context, uint64) ([]ccip.TokenPrice, error)) *ORM_GetTokenPricesByDestChain_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertGasPricesForDestChain provides a mock function with given fields: ctx, destChainSelector, gasPrices
func (_m *ORM) UpsertGasPricesForDestChain(ctx context.Context, destChainSelector uint64, gasPrices []ccip.GasPrice) (int64, error) {
	ret := _m.Called(ctx, destChainSelector, gasPrices)

	if len(ret) == 0 {
		panic("no return value specified for UpsertGasPricesForDestChain")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, []ccip.GasPrice) (int64, error)); ok {
		return rf(ctx, destChainSelector, gasPrices)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, []ccip.GasPrice) int64); ok {
		r0 = rf(ctx, destChainSelector, gasPrices)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, []ccip.GasPrice) error); ok {
		r1 = rf(ctx, destChainSelector, gasPrices)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_UpsertGasPricesForDestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertGasPricesForDestChain'
type ORM_UpsertGasPricesForDestChain_Call struct {
	*mock.Call
}

// UpsertGasPricesForDestChain is a helper method to define mock.On call
//   - ctx context.Context
//   - destChainSelector uint64
//   - gasPrices []ccip.GasPrice
func (_e *ORM_Expecter) UpsertGasPricesForDestChain(ctx interface{}, destChainSelector interface{}, gasPrices interface{}) *ORM_UpsertGasPricesForDestChain_Call {
	return &ORM_UpsertGasPricesForDestChain_Call{Call: _e.mock.On("UpsertGasPricesForDestChain", ctx, destChainSelector, gasPrices)}
}

func (_c *ORM_UpsertGasPricesForDestChain_Call) Run(run func(ctx context.Context, destChainSelector uint64, gasPrices []ccip.GasPrice)) *ORM_UpsertGasPricesForDestChain_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].([]ccip.GasPrice))
	})
	return _c
}

func (_c *ORM_UpsertGasPricesForDestChain_Call) Return(_a0 int64, _a1 error) *ORM_UpsertGasPricesForDestChain_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_UpsertGasPricesForDestChain_Call) RunAndReturn(run func(context.Context, uint64, []ccip.GasPrice) (int64, error)) *ORM_UpsertGasPricesForDestChain_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertTokenPricesForDestChain provides a mock function with given fields: ctx, destChainSelector, tokenPrices, interval
func (_m *ORM) UpsertTokenPricesForDestChain(ctx context.Context, destChainSelector uint64, tokenPrices []ccip.TokenPrice, interval time.Duration) (int64, error) {
	ret := _m.Called(ctx, destChainSelector, tokenPrices, interval)

	if len(ret) == 0 {
		panic("no return value specified for UpsertTokenPricesForDestChain")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, []ccip.TokenPrice, time.Duration) (int64, error)); ok {
		return rf(ctx, destChainSelector, tokenPrices, interval)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, []ccip.TokenPrice, time.Duration) int64); ok {
		r0 = rf(ctx, destChainSelector, tokenPrices, interval)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, []ccip.TokenPrice, time.Duration) error); ok {
		r1 = rf(ctx, destChainSelector, tokenPrices, interval)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_UpsertTokenPricesForDestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertTokenPricesForDestChain'
type ORM_UpsertTokenPricesForDestChain_Call struct {
	*mock.Call
}

// UpsertTokenPricesForDestChain is a helper method to define mock.On call
//   - ctx context.Context
//   - destChainSelector uint64
//   - tokenPrices []ccip.TokenPrice
//   - interval time.Duration
func (_e *ORM_Expecter) UpsertTokenPricesForDestChain(ctx interface{}, destChainSelector interface{}, tokenPrices interface{}, interval interface{}) *ORM_UpsertTokenPricesForDestChain_Call {
	return &ORM_UpsertTokenPricesForDestChain_Call{Call: _e.mock.On("UpsertTokenPricesForDestChain", ctx, destChainSelector, tokenPrices, interval)}
}

func (_c *ORM_UpsertTokenPricesForDestChain_Call) Run(run func(ctx context.Context, destChainSelector uint64, tokenPrices []ccip.TokenPrice, interval time.Duration)) *ORM_UpsertTokenPricesForDestChain_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].([]ccip.TokenPrice), args[3].(time.Duration))
	})
	return _c
}

func (_c *ORM_UpsertTokenPricesForDestChain_Call) Return(_a0 int64, _a1 error) *ORM_UpsertTokenPricesForDestChain_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_UpsertTokenPricesForDestChain_Call) RunAndReturn(run func(context.Context, uint64, []ccip.TokenPrice, time.Duration) (int64, error)) *ORM_UpsertTokenPricesForDestChain_Call {
	_c.Call.Return(run)
	return _c
}

// NewORM creates a new instance of ORM. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewORM(t interface {
	mock.TestingT
	Cleanup(func())
}) *ORM {
	mock := &ORM{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
