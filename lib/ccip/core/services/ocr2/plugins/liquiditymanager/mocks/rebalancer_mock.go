// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	graph "github.com/smartcontractkit/chainlink/v2/core/services/ocr2/plugins/liquiditymanager/graph"
	mock "github.com/stretchr/testify/mock"

	models "github.com/smartcontractkit/chainlink/v2/core/services/ocr2/plugins/liquiditymanager/models"

	rebalalgo "github.com/smartcontractkit/chainlink/v2/core/services/ocr2/plugins/liquiditymanager/rebalalgo"
)

// RebalancingAlgo is an autogenerated mock type for the RebalancingAlgo type
type RebalancingAlgo struct {
	mock.Mock
}

type RebalancingAlgo_Expecter struct {
	mock *mock.Mock
}

func (_m *RebalancingAlgo) EXPECT() *RebalancingAlgo_Expecter {
	return &RebalancingAlgo_Expecter{mock: &_m.Mock}
}

// ComputeTransfersToBalance provides a mock function with given fields: g, unexecuted
func (_m *RebalancingAlgo) ComputeTransfersToBalance(g graph.Graph, unexecuted []rebalalgo.UnexecutedTransfer) ([]models.ProposedTransfer, error) {
	ret := _m.Called(g, unexecuted)

	if len(ret) == 0 {
		panic("no return value specified for ComputeTransfersToBalance")
	}

	var r0 []models.ProposedTransfer
	var r1 error
	if rf, ok := ret.Get(0).(func(graph.Graph, []rebalalgo.UnexecutedTransfer) ([]models.ProposedTransfer, error)); ok {
		return rf(g, unexecuted)
	}
	if rf, ok := ret.Get(0).(func(graph.Graph, []rebalalgo.UnexecutedTransfer) []models.ProposedTransfer); ok {
		r0 = rf(g, unexecuted)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.ProposedTransfer)
		}
	}

	if rf, ok := ret.Get(1).(func(graph.Graph, []rebalalgo.UnexecutedTransfer) error); ok {
		r1 = rf(g, unexecuted)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RebalancingAlgo_ComputeTransfersToBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ComputeTransfersToBalance'
type RebalancingAlgo_ComputeTransfersToBalance_Call struct {
	*mock.Call
}

// ComputeTransfersToBalance is a helper method to define mock.On call
//   - g graph.Graph
//   - unexecuted []rebalalgo.UnexecutedTransfer
func (_e *RebalancingAlgo_Expecter) ComputeTransfersToBalance(g interface{}, unexecuted interface{}) *RebalancingAlgo_ComputeTransfersToBalance_Call {
	return &RebalancingAlgo_ComputeTransfersToBalance_Call{Call: _e.mock.On("ComputeTransfersToBalance", g, unexecuted)}
}

func (_c *RebalancingAlgo_ComputeTransfersToBalance_Call) Run(run func(g graph.Graph, unexecuted []rebalalgo.UnexecutedTransfer)) *RebalancingAlgo_ComputeTransfersToBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(graph.Graph), args[1].([]rebalalgo.UnexecutedTransfer))
	})
	return _c
}

func (_c *RebalancingAlgo_ComputeTransfersToBalance_Call) Return(_a0 []models.ProposedTransfer, _a1 error) *RebalancingAlgo_ComputeTransfersToBalance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RebalancingAlgo_ComputeTransfersToBalance_Call) RunAndReturn(run func(graph.Graph, []rebalalgo.UnexecutedTransfer) ([]models.ProposedTransfer, error)) *RebalancingAlgo_ComputeTransfersToBalance_Call {
	_c.Call.Return(run)
	return _c
}

// NewRebalancingAlgo creates a new instance of RebalancingAlgo. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRebalancingAlgo(t interface {
	mock.TestingT
	Cleanup(func())
}) *RebalancingAlgo {
	mock := &RebalancingAlgo{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
