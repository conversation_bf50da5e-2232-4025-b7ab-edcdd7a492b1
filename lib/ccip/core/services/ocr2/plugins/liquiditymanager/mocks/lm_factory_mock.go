// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	evmliquiditymanager "github.com/smartcontractkit/chainlink/v2/core/services/ocr2/plugins/liquiditymanager/chain/evm"
	mock "github.com/stretchr/testify/mock"

	models "github.com/smartcontractkit/chainlink/v2/core/services/ocr2/plugins/liquiditymanager/models"
)

// Factory is an autogenerated mock type for the Factory type
type Factory struct {
	mock.Mock
}

type Factory_Expecter struct {
	mock *mock.Mock
}

func (_m *Factory) EXPECT() *Factory_Expecter {
	return &Factory_Expecter{mock: &_m.Mock}
}

// GetLiquidityManager provides a mock function with given fields: networkID, address
func (_m *Factory) GetLiquidityManager(networkID models.NetworkSelector, address models.Address) (evmliquiditymanager.LiquidityManager, error) {
	ret := _m.Called(networkID, address)

	if len(ret) == 0 {
		panic("no return value specified for GetLiquidityManager")
	}

	var r0 evmliquiditymanager.LiquidityManager
	var r1 error
	if rf, ok := ret.Get(0).(func(models.NetworkSelector, models.Address) (evmliquiditymanager.LiquidityManager, error)); ok {
		return rf(networkID, address)
	}
	if rf, ok := ret.Get(0).(func(models.NetworkSelector, models.Address) evmliquiditymanager.LiquidityManager); ok {
		r0 = rf(networkID, address)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(evmliquiditymanager.LiquidityManager)
		}
	}

	if rf, ok := ret.Get(1).(func(models.NetworkSelector, models.Address) error); ok {
		r1 = rf(networkID, address)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Factory_GetLiquidityManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLiquidityManager'
type Factory_GetLiquidityManager_Call struct {
	*mock.Call
}

// GetLiquidityManager is a helper method to define mock.On call
//   - networkID models.NetworkSelector
//   - address models.Address
func (_e *Factory_Expecter) GetLiquidityManager(networkID interface{}, address interface{}) *Factory_GetLiquidityManager_Call {
	return &Factory_GetLiquidityManager_Call{Call: _e.mock.On("GetLiquidityManager", networkID, address)}
}

func (_c *Factory_GetLiquidityManager_Call) Run(run func(networkID models.NetworkSelector, address models.Address)) *Factory_GetLiquidityManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(models.NetworkSelector), args[1].(models.Address))
	})
	return _c
}

func (_c *Factory_GetLiquidityManager_Call) Return(_a0 evmliquiditymanager.LiquidityManager, _a1 error) *Factory_GetLiquidityManager_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Factory_GetLiquidityManager_Call) RunAndReturn(run func(models.NetworkSelector, models.Address) (evmliquiditymanager.LiquidityManager, error)) *Factory_GetLiquidityManager_Call {
	_c.Call.Return(run)
	return _c
}

// NewLiquidityManager provides a mock function with given fields: networkID, address
func (_m *Factory) NewLiquidityManager(networkID models.NetworkSelector, address models.Address) (evmliquiditymanager.LiquidityManager, error) {
	ret := _m.Called(networkID, address)

	if len(ret) == 0 {
		panic("no return value specified for NewLiquidityManager")
	}

	var r0 evmliquiditymanager.LiquidityManager
	var r1 error
	if rf, ok := ret.Get(0).(func(models.NetworkSelector, models.Address) (evmliquiditymanager.LiquidityManager, error)); ok {
		return rf(networkID, address)
	}
	if rf, ok := ret.Get(0).(func(models.NetworkSelector, models.Address) evmliquiditymanager.LiquidityManager); ok {
		r0 = rf(networkID, address)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(evmliquiditymanager.LiquidityManager)
		}
	}

	if rf, ok := ret.Get(1).(func(models.NetworkSelector, models.Address) error); ok {
		r1 = rf(networkID, address)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Factory_NewLiquidityManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewLiquidityManager'
type Factory_NewLiquidityManager_Call struct {
	*mock.Call
}

// NewLiquidityManager is a helper method to define mock.On call
//   - networkID models.NetworkSelector
//   - address models.Address
func (_e *Factory_Expecter) NewLiquidityManager(networkID interface{}, address interface{}) *Factory_NewLiquidityManager_Call {
	return &Factory_NewLiquidityManager_Call{Call: _e.mock.On("NewLiquidityManager", networkID, address)}
}

func (_c *Factory_NewLiquidityManager_Call) Run(run func(networkID models.NetworkSelector, address models.Address)) *Factory_NewLiquidityManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(models.NetworkSelector), args[1].(models.Address))
	})
	return _c
}

func (_c *Factory_NewLiquidityManager_Call) Return(_a0 evmliquiditymanager.LiquidityManager, _a1 error) *Factory_NewLiquidityManager_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Factory_NewLiquidityManager_Call) RunAndReturn(run func(models.NetworkSelector, models.Address) (evmliquiditymanager.LiquidityManager, error)) *Factory_NewLiquidityManager_Call {
	_c.Call.Return(run)
	return _c
}

// NewFactory creates a new instance of Factory. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFactory(t interface {
	mock.TestingT
	Cleanup(func())
}) *Factory {
	mock := &Factory{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
