package arb

import (
	"github.com/ethereum/go-ethereum/common"
	chainsel "github.com/smartcontractkit/chain-selectors"
)

var (
	// Arbitrum Contracts
	// See https://docs.arbitrum.io/for-devs/useful-addresses
	AllContracts map[uint64]map[string]common.Address
)

func init() {
	AllContracts = map[uint64]map[string]common.Address{
		chainsel.ETHEREUM_TESTNET_SEPOLIA.Selector: {
			"L1GatewayRouter": common.HexToAddress("******************************************"),
			"L1Outbox":        common.HexToAddress("******************************************"),
			// labeled "Delayed Inbox" in the arbitrum docs
			"L1Inbox": common.HexToAddress("******************************************"),
			"Rollup":  common.HexToAddress("******************************************"),
			"WETH":    common.HexToAddress("******************************************"),
		},
		chainsel.ETHEREUM_TESTNET_SEPOLIA_ARBITRUM_1.Selector: {
			"L2GatewayRouter": common.HexToAddress("******************************************"),
			"NodeInterface":   common.HexToAddress("******************************************"),
			"WETH":            common.HexToAddress("******************************************"),
		},
	}
}
