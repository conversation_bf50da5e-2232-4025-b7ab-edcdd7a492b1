// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	bridge "github.com/smartcontractkit/chainlink/v2/core/services/ocr2/plugins/liquiditymanager/bridge"

	mock "github.com/stretchr/testify/mock"

	models "github.com/smartcontractkit/chainlink/v2/core/services/ocr2/plugins/liquiditymanager/models"
)

// Factory is an autogenerated mock type for the Factory type
type Factory struct {
	mock.Mock
}

type Factory_Expecter struct {
	mock *mock.Mock
}

func (_m *Factory) EXPECT() *Factory_Expecter {
	return &Factory_Expecter{mock: &_m.Mock}
}

// GetBridge provides a mock function with given fields: source, dest
func (_m *Factory) GetBridge(source models.NetworkSelector, dest models.NetworkSelector) (bridge.Bridge, error) {
	ret := _m.Called(source, dest)

	if len(ret) == 0 {
		panic("no return value specified for GetBridge")
	}

	var r0 bridge.Bridge
	var r1 error
	if rf, ok := ret.Get(0).(func(models.NetworkSelector, models.NetworkSelector) (bridge.Bridge, error)); ok {
		return rf(source, dest)
	}
	if rf, ok := ret.Get(0).(func(models.NetworkSelector, models.NetworkSelector) bridge.Bridge); ok {
		r0 = rf(source, dest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(bridge.Bridge)
		}
	}

	if rf, ok := ret.Get(1).(func(models.NetworkSelector, models.NetworkSelector) error); ok {
		r1 = rf(source, dest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Factory_GetBridge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBridge'
type Factory_GetBridge_Call struct {
	*mock.Call
}

// GetBridge is a helper method to define mock.On call
//   - source models.NetworkSelector
//   - dest models.NetworkSelector
func (_e *Factory_Expecter) GetBridge(source interface{}, dest interface{}) *Factory_GetBridge_Call {
	return &Factory_GetBridge_Call{Call: _e.mock.On("GetBridge", source, dest)}
}

func (_c *Factory_GetBridge_Call) Run(run func(source models.NetworkSelector, dest models.NetworkSelector)) *Factory_GetBridge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(models.NetworkSelector), args[1].(models.NetworkSelector))
	})
	return _c
}

func (_c *Factory_GetBridge_Call) Return(_a0 bridge.Bridge, _a1 error) *Factory_GetBridge_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Factory_GetBridge_Call) RunAndReturn(run func(models.NetworkSelector, models.NetworkSelector) (bridge.Bridge, error)) *Factory_GetBridge_Call {
	_c.Call.Return(run)
	return _c
}

// NewBridge provides a mock function with given fields: ctx, source, dest
func (_m *Factory) NewBridge(ctx context.Context, source models.NetworkSelector, dest models.NetworkSelector) (bridge.Bridge, error) {
	ret := _m.Called(ctx, source, dest)

	if len(ret) == 0 {
		panic("no return value specified for NewBridge")
	}

	var r0 bridge.Bridge
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, models.NetworkSelector, models.NetworkSelector) (bridge.Bridge, error)); ok {
		return rf(ctx, source, dest)
	}
	if rf, ok := ret.Get(0).(func(context.Context, models.NetworkSelector, models.NetworkSelector) bridge.Bridge); ok {
		r0 = rf(ctx, source, dest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(bridge.Bridge)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, models.NetworkSelector, models.NetworkSelector) error); ok {
		r1 = rf(ctx, source, dest)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Factory_NewBridge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewBridge'
type Factory_NewBridge_Call struct {
	*mock.Call
}

// NewBridge is a helper method to define mock.On call
//   - ctx context.Context
//   - source models.NetworkSelector
//   - dest models.NetworkSelector
func (_e *Factory_Expecter) NewBridge(ctx interface{}, source interface{}, dest interface{}) *Factory_NewBridge_Call {
	return &Factory_NewBridge_Call{Call: _e.mock.On("NewBridge", ctx, source, dest)}
}

func (_c *Factory_NewBridge_Call) Run(run func(ctx context.Context, source models.NetworkSelector, dest models.NetworkSelector)) *Factory_NewBridge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(models.NetworkSelector), args[2].(models.NetworkSelector))
	})
	return _c
}

func (_c *Factory_NewBridge_Call) Return(_a0 bridge.Bridge, _a1 error) *Factory_NewBridge_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Factory_NewBridge_Call) RunAndReturn(run func(context.Context, models.NetworkSelector, models.NetworkSelector) (bridge.Bridge, error)) *Factory_NewBridge_Call {
	_c.Call.Return(run)
	return _c
}

// NewFactory creates a new instance of Factory. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFactory(t interface {
	mock.TestingT
	Cleanup(func())
}) *Factory {
	mock := &Factory{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
