package opstack

import (
	"errors"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	chainsel "github.com/smartcontractkit/chain-selectors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	lpmocks "github.com/smartcontractkit/chainlink/v2/core/chains/evm/logpoller/mocks"
	"github.com/smartcontractkit/chainlink/v2/core/gethwrappers/liquiditymanager/generated/liquiditymanager"
	"github.com/smartcontractkit/chainlink/v2/core/internal/testutils"
	"github.com/smartcontractkit/chainlink/v2/core/logger"
	bridgetestutils "github.com/smartcontractkit/chainlink/v2/core/services/ocr2/plugins/liquiditymanager/bridge/testutils"
	"github.com/smartcontractkit/chainlink/v2/core/services/ocr2/plugins/liquiditymanager/models"

	"github.com/stretchr/testify/mock"
)

func Test_L2ToL1Bridge_partitionTransfers(t *testing.T) {
	l1LiquidityManagerAddress := common.HexToAddress("0xabc")
	l1ChainSelector := chainsel.ETHEREUM_MAINNET.Selector
	l2ChainSelector := chainsel.ETHEREUM_MAINNET_OPTIMISM_1.Selector

	type args struct {
		localSelector             models.NetworkSelector
		l1LiquidityManagerAddress common.Address
		sentLogs                  []*liquiditymanager.LiquidityManagerLiquidityTransferred
		proveFinalizationStepLogs []*liquiditymanager.LiquidityManagerFinalizationStepCompleted
		receivedLogs              []*liquiditymanager.LiquidityManagerLiquidityTransferred
	}
	tests := []struct {
		name                   string
		args                   args
		wantNeedsToBeProven    []*liquiditymanager.LiquidityManagerLiquidityTransferred
		wantNeedsToBeFinalized []*liquiditymanager.LiquidityManagerLiquidityTransferred
		wantMissingSent        []*liquiditymanager.LiquidityManagerFinalizationStepCompleted
		wantErr                bool
	}{
		{
			name: "empty",
			args: args{
				localSelector:             models.NetworkSelector(uint64(0)),
				sentLogs:                  nil,
				proveFinalizationStepLogs: nil,
				receivedLogs:              nil,
			},
			wantNeedsToBeProven:    nil,
			wantNeedsToBeFinalized: nil,
			wantMissingSent:        nil,
			wantErr:                false,
		},
		{
			name: "happy path - one to be proven, one to be finalized, one missing sent, one done",
			args: args{
				localSelector:             models.NetworkSelector(l2ChainSelector),
				l1LiquidityManagerAddress: l1LiquidityManagerAddress,
				sentLogs: []*liquiditymanager.LiquidityManagerLiquidityTransferred{
					// This one needs to be proven
					{
						FromChainSelector:  l2ChainSelector,
						ToChainSelector:    l1ChainSelector,
						To:                 l1LiquidityManagerAddress,
						Amount:             big.NewInt(4),
						BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000004"),
						BridgeSpecificData: []byte{},
					},
					// This one needs to be finalized
					{
						FromChainSelector:  l2ChainSelector,
						ToChainSelector:    l1ChainSelector,
						To:                 l1LiquidityManagerAddress,
						Amount:             big.NewInt(7),
						BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000005"),
						BridgeSpecificData: []byte{},
					},
					// This one is done/already received (present in sent logs, proven logs, and received logs), should not be included in any output
					{
						FromChainSelector:  l2ChainSelector,
						ToChainSelector:    l1ChainSelector,
						To:                 l1LiquidityManagerAddress,
						Amount:             big.NewInt(10),
						BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000006"),
						BridgeSpecificData: []byte{},
					},
				},
				proveFinalizationStepLogs: []*liquiditymanager.LiquidityManagerFinalizationStepCompleted{
					// This one is ready to be finalized
					{
						OcrSeqNum:           uint64(1),
						RemoteChainSelector: l2ChainSelector,
						// prove logs from the 0x0...5 nonce (amount=7)
						BridgeSpecificData: bridgetestutils.MustConvertHexBridgeDataToBytes(t, "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"),
					},
					// This one is done/already received
					{
						OcrSeqNum:           uint64(2),
						RemoteChainSelector: l2ChainSelector,
						// prove logs from the 0x0...6 nonce (amount=10)
						BridgeSpecificData: bridgetestutils.MustConvertHexBridgeDataToBytes(t, "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"),
					},
					// This one is missing (not present in sent logs)
					{
						OcrSeqNum:           uint64(3),
						RemoteChainSelector: l2ChainSelector,
						BridgeSpecificData:  bridgetestutils.MustConvertHexBridgeDataToBytes(t, "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"),
					},
				},
				receivedLogs: []*liquiditymanager.LiquidityManagerLiquidityTransferred{
					{
						FromChainSelector: l2ChainSelector,
						ToChainSelector:   l1ChainSelector,
						To:                l1LiquidityManagerAddress,
						Amount:            big.NewInt(10),
						BridgeReturnData:  []byte{},
						// NOTE: remember, the nonce is in bridgeSpecificData in the received logs instead of bridgeReturnData
						BridgeSpecificData: bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000006"),
					},
				},
			},
			wantNeedsToBeProven: []*liquiditymanager.LiquidityManagerLiquidityTransferred{
				{
					FromChainSelector:  l2ChainSelector,
					ToChainSelector:    l1ChainSelector,
					To:                 l1LiquidityManagerAddress,
					Amount:             big.NewInt(4),
					BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000004"),
					BridgeSpecificData: []byte{},
				},
			},
			wantNeedsToBeFinalized: []*liquiditymanager.LiquidityManagerLiquidityTransferred{
				{
					FromChainSelector:  l2ChainSelector,
					ToChainSelector:    l1ChainSelector,
					To:                 l1LiquidityManagerAddress,
					Amount:             big.NewInt(7),
					BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000005"),
					BridgeSpecificData: []byte{},
				},
			},
			wantMissingSent: []*liquiditymanager.LiquidityManagerFinalizationStepCompleted{
				{
					OcrSeqNum:           uint64(3),
					RemoteChainSelector: l2ChainSelector,
					BridgeSpecificData:  bridgetestutils.MustConvertHexBridgeDataToBytes(t, "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"),
				},
			},
			wantErr: false,
		},
		{
			name: "sent logs - one valid log, two invalid logs with incorrect To and FromChainSelector fields",
			args: args{
				localSelector:             models.NetworkSelector(l2ChainSelector),
				l1LiquidityManagerAddress: l1LiquidityManagerAddress,
				sentLogs: []*liquiditymanager.LiquidityManagerLiquidityTransferred{
					// This one is valid and needs to be proven
					{
						FromChainSelector:  l2ChainSelector,
						ToChainSelector:    l1ChainSelector,
						To:                 l1LiquidityManagerAddress,
						Amount:             big.NewInt(4),
						BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000004"),
						BridgeSpecificData: []byte{},
					},
					// This one has an invalid FromChainSelector
					{
						FromChainSelector:  uint64(909090), // Non-existent chain selector
						ToChainSelector:    l1ChainSelector,
						To:                 l1LiquidityManagerAddress,
						Amount:             big.NewInt(7),
						BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000005"),
						BridgeSpecificData: []byte{},
					},
					// This one has an invalid To field
					{
						FromChainSelector:  l2ChainSelector,
						ToChainSelector:    l1ChainSelector,
						To:                 common.HexToAddress("0xdeaddeaddeaddeaddeaddeaddeaddeaddeaddead"),
						Amount:             big.NewInt(10),
						BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000006"),
						BridgeSpecificData: []byte{},
					},
				},
				proveFinalizationStepLogs: nil,
				receivedLogs:              nil,
			},
			wantNeedsToBeProven: []*liquiditymanager.LiquidityManagerLiquidityTransferred{
				{
					FromChainSelector:  l2ChainSelector,
					ToChainSelector:    l1ChainSelector,
					To:                 l1LiquidityManagerAddress,
					Amount:             big.NewInt(4),
					BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000004"),
					BridgeSpecificData: []byte{},
				},
			},
			wantNeedsToBeFinalized: nil,
			wantMissingSent:        nil,
			wantErr:                false,
		},
		{
			name: "prove finalization step logs - invalid remote chain selector",
			args: args{
				localSelector:             models.NetworkSelector(l2ChainSelector),
				l1LiquidityManagerAddress: l1LiquidityManagerAddress,
				sentLogs: []*liquiditymanager.LiquidityManagerLiquidityTransferred{
					{
						FromChainSelector:  l2ChainSelector,
						ToChainSelector:    l1ChainSelector,
						To:                 l1LiquidityManagerAddress,
						Amount:             big.NewInt(7),
						BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000005"),
						BridgeSpecificData: []byte{},
					},
				},
				proveFinalizationStepLogs: []*liquiditymanager.LiquidityManagerFinalizationStepCompleted{
					// This one would have been ready to be finalized, but it has an invalid remote chain selector
					{
						OcrSeqNum:           uint64(1),
						RemoteChainSelector: uint64(909090), // Non-existent chain selector
						// prove logs from the 0x0...5 nonce (amount=7)
						BridgeSpecificData: bridgetestutils.MustConvertHexBridgeDataToBytes(t, "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"),
					},
				},
				receivedLogs: nil,
			},
			wantNeedsToBeProven: []*liquiditymanager.LiquidityManagerLiquidityTransferred{
				{
					FromChainSelector:  l2ChainSelector,
					ToChainSelector:    l1ChainSelector,
					To:                 l1LiquidityManagerAddress,
					Amount:             big.NewInt(7),
					BridgeReturnData:   bridgetestutils.MustPackBridgeData(t, "0x0000000000000000000000000000000000000000000000000000000000000005"),
					BridgeSpecificData: []byte{},
				},
			},
			wantNeedsToBeFinalized: nil,
			wantMissingSent:        nil,
			wantErr:                false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNeedsToBeProven, gotNeedsToBeFinalized, gotMissingSent, err := partitionWithdrawalTransfers(
				tt.args.localSelector,
				tt.args.l1LiquidityManagerAddress,
				tt.args.sentLogs,
				tt.args.proveFinalizationStepLogs,
				tt.args.receivedLogs,
				logger.TestLogger(t),
			)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				bridgetestutils.AssertLiquidityTransferredEventSlicesEqual(t, tt.wantNeedsToBeProven, gotNeedsToBeProven, bridgetestutils.SortByBridgeReturnData)
				bridgetestutils.AssertLiquidityTransferredEventSlicesEqual(t, tt.wantNeedsToBeFinalized, gotNeedsToBeFinalized, bridgetestutils.SortByBridgeReturnData)
				assert.Equal(t, tt.wantMissingSent, gotMissingSent)
			}
		})
	}
}

func Test_L2ToL1Bridge_GetBridgePayloadAndFee(t *testing.T) {
	bridge := &l2ToL1Bridge{}
	payload, fee, err := bridge.GetBridgePayloadAndFee(testutils.Context(t), models.Transfer{})
	require.NoError(t, err)
	require.Empty(t, payload)
	require.Equal(t, big.NewInt(0), fee)
}

func Test_L2ToL1Bridge_QuorumizedBridgePayload(t *testing.T) {
	bridge := &l2ToL1Bridge{}
	payload, err := bridge.QuorumizedBridgePayload(make([][]byte, 0), 0)
	require.NoError(t, err)
	require.Empty(t, payload)
}

func Test_L21ToL1Bridge_Close(t *testing.T) {
	type fields struct {
		l1LogPoller  *lpmocks.LogPoller
		l2LogPoller  *lpmocks.LogPoller
		l1FilterName string
		l2FilterName string
	}
	tests := []struct {
		name       string
		fields     fields
		wantErr    bool
		before     func(*testing.T, fields)
		assertions func(*testing.T, fields)
	}{
		{
			"happy path",
			fields{
				l1LogPoller:  lpmocks.NewLogPoller(t),
				l2LogPoller:  lpmocks.NewLogPoller(t),
				l1FilterName: "l1FilterName",
				l2FilterName: "l2FilterName",
			},
			false,
			func(t *testing.T, f fields) {
				f.l1LogPoller.On("UnregisterFilter", mock.Anything, f.l1FilterName).Return(nil)
				f.l2LogPoller.On("UnregisterFilter", mock.Anything, f.l2FilterName).Return(nil)
			},
			func(t *testing.T, f fields) {
				f.l1LogPoller.AssertExpectations(t)
				f.l2LogPoller.AssertExpectations(t)
			},
		},
		{
			"l1 unregister error",
			fields{
				l1LogPoller:  lpmocks.NewLogPoller(t),
				l2LogPoller:  lpmocks.NewLogPoller(t),
				l1FilterName: "l1FilterName",
				l2FilterName: "l2FilterName",
			},
			true,
			func(t *testing.T, f fields) {
				f.l1LogPoller.On("UnregisterFilter", mock.Anything, f.l1FilterName).Return(errors.New("unregister error"))
				f.l2LogPoller.On("UnregisterFilter", mock.Anything, f.l2FilterName).Return(nil)
			},
			func(t *testing.T, f fields) {
				f.l1LogPoller.AssertExpectations(t)
				f.l2LogPoller.AssertExpectations(t)
			},
		},
		{
			"l2 unregister error",
			fields{
				l1LogPoller:  lpmocks.NewLogPoller(t),
				l2LogPoller:  lpmocks.NewLogPoller(t),
				l1FilterName: "l1FilterName",
				l2FilterName: "l2FilterName",
			},
			true,
			func(t *testing.T, f fields) {
				f.l1LogPoller.On("UnregisterFilter", mock.Anything, f.l1FilterName).Return(nil)
				f.l2LogPoller.On("UnregisterFilter", mock.Anything, f.l2FilterName).Return(errors.New("unregister error"))
			},
			func(t *testing.T, f fields) {
				f.l1LogPoller.AssertExpectations(t)
				f.l2LogPoller.AssertExpectations(t)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			l := &l1ToL2Bridge{
				l1LogPoller:  tt.fields.l1LogPoller,
				l2LogPoller:  tt.fields.l2LogPoller,
				l1FilterName: tt.fields.l1FilterName,
				l2FilterName: tt.fields.l2FilterName,
			}
			if tt.before != nil {
				tt.before(t, tt.fields)
				defer tt.assertions(t, tt.fields)
			}

			err := l.Close(testutils.Context(t))
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
