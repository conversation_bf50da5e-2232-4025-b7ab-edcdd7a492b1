package opstack

import (
	"github.com/ethereum/go-ethereum/common"
	chainsel "github.com/smartcontractkit/chain-selectors"
)

var (
	// Optimism contract addresses: https://docs.optimism.io/chain/addresses
	OptimismContractsByChainSelector map[uint64]map[string]common.Address
)

func init() {
	OptimismContractsByChainSelector = map[uint64]map[string]common.Address{
		chainsel.ETHEREUM_TESTNET_SEPOLIA.Selector: {
			"L1StandardBridgeProxy":  common.HexToAddress("******************************************"),
			"L1CrossDomainMessenger": common.HexToAddress("******************************************"),
			"WETH":                   common.HexToAddress("******************************************"),
			"FaucetTestingToken":     common.HexToAddress("******************************************"),
			"OptimismPortalProxy":    common.HexToAddress("******************************************"),
			"L2OutputOracle":         common.HexToAddress("******************************************"), // Removed after FPAC upgrade
		},
		chainsel.ETHEREUM_TESTNET_SEPOLIA_OPTIMISM_1.Selector: {
			"L2StandardBridge":    common.HexToAddress("******************************************"),
			"WETH":                common.HexToAddress("******************************************"),
			"FaucetTestingToken":  common.HexToAddress("******************************************"),
			"L2ToL1MessagePasser": common.HexToAddress("******************************************"),
		},
	}
}
