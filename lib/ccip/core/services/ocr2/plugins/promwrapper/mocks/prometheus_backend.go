// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// PrometheusBackend is an autogenerated mock type for the PrometheusBackend type
type PrometheusBackend struct {
	mock.Mock
}

type PrometheusBackend_Expecter struct {
	mock *mock.Mock
}

func (_m *PrometheusBackend) EXPECT() *PrometheusBackend_Expecter {
	return &PrometheusBackend_Expecter{mock: &_m.Mock}
}

// SetAcceptFinalizedReportToTransmitAcceptedReportLatency provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetAcceptFinalizedReportToTransmitAcceptedReportLatency(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetAcceptFinalizedReportToTransmitAcceptedReportLatency'
type PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call struct {
	*mock.Call
}

// SetAcceptFinalizedReportToTransmitAcceptedReportLatency is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetAcceptFinalizedReportToTransmitAcceptedReportLatency(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call {
	return &PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call{Call: _e.mock.On("SetAcceptFinalizedReportToTransmitAcceptedReportLatency", _a0, _a1)}
}

func (_c *PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call) Return() *PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetAcceptFinalizedReportToTransmitAcceptedReportLatency_Call {
	_c.Call.Return(run)
	return _c
}

// SetCloseDuration provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetCloseDuration(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetCloseDuration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetCloseDuration'
type PrometheusBackend_SetCloseDuration_Call struct {
	*mock.Call
}

// SetCloseDuration is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetCloseDuration(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetCloseDuration_Call {
	return &PrometheusBackend_SetCloseDuration_Call{Call: _e.mock.On("SetCloseDuration", _a0, _a1)}
}

func (_c *PrometheusBackend_SetCloseDuration_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetCloseDuration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetCloseDuration_Call) Return() *PrometheusBackend_SetCloseDuration_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetCloseDuration_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetCloseDuration_Call {
	_c.Call.Return(run)
	return _c
}

// SetObservationDuration provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetObservationDuration(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetObservationDuration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetObservationDuration'
type PrometheusBackend_SetObservationDuration_Call struct {
	*mock.Call
}

// SetObservationDuration is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetObservationDuration(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetObservationDuration_Call {
	return &PrometheusBackend_SetObservationDuration_Call{Call: _e.mock.On("SetObservationDuration", _a0, _a1)}
}

func (_c *PrometheusBackend_SetObservationDuration_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetObservationDuration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetObservationDuration_Call) Return() *PrometheusBackend_SetObservationDuration_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetObservationDuration_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetObservationDuration_Call {
	_c.Call.Return(run)
	return _c
}

// SetObservationToReportLatency provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetObservationToReportLatency(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetObservationToReportLatency_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetObservationToReportLatency'
type PrometheusBackend_SetObservationToReportLatency_Call struct {
	*mock.Call
}

// SetObservationToReportLatency is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetObservationToReportLatency(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetObservationToReportLatency_Call {
	return &PrometheusBackend_SetObservationToReportLatency_Call{Call: _e.mock.On("SetObservationToReportLatency", _a0, _a1)}
}

func (_c *PrometheusBackend_SetObservationToReportLatency_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetObservationToReportLatency_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetObservationToReportLatency_Call) Return() *PrometheusBackend_SetObservationToReportLatency_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetObservationToReportLatency_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetObservationToReportLatency_Call {
	_c.Call.Return(run)
	return _c
}

// SetQueryDuration provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetQueryDuration(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetQueryDuration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetQueryDuration'
type PrometheusBackend_SetQueryDuration_Call struct {
	*mock.Call
}

// SetQueryDuration is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetQueryDuration(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetQueryDuration_Call {
	return &PrometheusBackend_SetQueryDuration_Call{Call: _e.mock.On("SetQueryDuration", _a0, _a1)}
}

func (_c *PrometheusBackend_SetQueryDuration_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetQueryDuration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetQueryDuration_Call) Return() *PrometheusBackend_SetQueryDuration_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetQueryDuration_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetQueryDuration_Call {
	_c.Call.Return(run)
	return _c
}

// SetQueryToObservationLatency provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetQueryToObservationLatency(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetQueryToObservationLatency_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetQueryToObservationLatency'
type PrometheusBackend_SetQueryToObservationLatency_Call struct {
	*mock.Call
}

// SetQueryToObservationLatency is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetQueryToObservationLatency(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetQueryToObservationLatency_Call {
	return &PrometheusBackend_SetQueryToObservationLatency_Call{Call: _e.mock.On("SetQueryToObservationLatency", _a0, _a1)}
}

func (_c *PrometheusBackend_SetQueryToObservationLatency_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetQueryToObservationLatency_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetQueryToObservationLatency_Call) Return() *PrometheusBackend_SetQueryToObservationLatency_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetQueryToObservationLatency_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetQueryToObservationLatency_Call {
	_c.Call.Return(run)
	return _c
}

// SetReportDuration provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetReportDuration(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetReportDuration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetReportDuration'
type PrometheusBackend_SetReportDuration_Call struct {
	*mock.Call
}

// SetReportDuration is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetReportDuration(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetReportDuration_Call {
	return &PrometheusBackend_SetReportDuration_Call{Call: _e.mock.On("SetReportDuration", _a0, _a1)}
}

func (_c *PrometheusBackend_SetReportDuration_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetReportDuration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetReportDuration_Call) Return() *PrometheusBackend_SetReportDuration_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetReportDuration_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetReportDuration_Call {
	_c.Call.Return(run)
	return _c
}

// SetReportToAcceptFinalizedReportLatency provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetReportToAcceptFinalizedReportLatency(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetReportToAcceptFinalizedReportLatency'
type PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call struct {
	*mock.Call
}

// SetReportToAcceptFinalizedReportLatency is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetReportToAcceptFinalizedReportLatency(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call {
	return &PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call{Call: _e.mock.On("SetReportToAcceptFinalizedReportLatency", _a0, _a1)}
}

func (_c *PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call) Return() *PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetReportToAcceptFinalizedReportLatency_Call {
	_c.Call.Return(run)
	return _c
}

// SetShouldAcceptFinalizedReportDuration provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetShouldAcceptFinalizedReportDuration(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetShouldAcceptFinalizedReportDuration'
type PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call struct {
	*mock.Call
}

// SetShouldAcceptFinalizedReportDuration is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetShouldAcceptFinalizedReportDuration(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call {
	return &PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call{Call: _e.mock.On("SetShouldAcceptFinalizedReportDuration", _a0, _a1)}
}

func (_c *PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call) Return() *PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetShouldAcceptFinalizedReportDuration_Call {
	_c.Call.Return(run)
	return _c
}

// SetShouldTransmitAcceptedReportDuration provides a mock function with given fields: _a0, _a1
func (_m *PrometheusBackend) SetShouldTransmitAcceptedReportDuration(_a0 []string, _a1 float64) {
	_m.Called(_a0, _a1)
}

// PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetShouldTransmitAcceptedReportDuration'
type PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call struct {
	*mock.Call
}

// SetShouldTransmitAcceptedReportDuration is a helper method to define mock.On call
//   - _a0 []string
//   - _a1 float64
func (_e *PrometheusBackend_Expecter) SetShouldTransmitAcceptedReportDuration(_a0 interface{}, _a1 interface{}) *PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call {
	return &PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call{Call: _e.mock.On("SetShouldTransmitAcceptedReportDuration", _a0, _a1)}
}

func (_c *PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call) Run(run func(_a0 []string, _a1 float64)) *PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(float64))
	})
	return _c
}

func (_c *PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call) Return() *PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call {
	_c.Call.Return()
	return _c
}

func (_c *PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call) RunAndReturn(run func([]string, float64)) *PrometheusBackend_SetShouldTransmitAcceptedReportDuration_Call {
	_c.Call.Return(run)
	return _c
}

// NewPrometheusBackend creates a new instance of PrometheusBackend. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPrometheusBackend(t interface {
	mock.TestingT
	Cleanup(func())
}) *PrometheusBackend {
	mock := &PrometheusBackend{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
