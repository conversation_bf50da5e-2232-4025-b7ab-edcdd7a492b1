// Code generated by mockery v2.43.2. DO NOT EDIT.

package prices

import (
	context "context"
	big "math/big"

	ccip "github.com/smartcontractkit/chainlink-common/pkg/types/ccip"

	mock "github.com/stretchr/testify/mock"
)

// MockGasPriceEstimatorExec is an autogenerated mock type for the GasPriceEstimatorExec type
type MockGasPriceEstimatorExec struct {
	mock.Mock
}

type MockGasPriceEstimatorExec_Expecter struct {
	mock *mock.Mock
}

func (_m *MockGasPriceEstimatorExec) EXPECT() *MockGasPriceEstimatorExec_Expecter {
	return &MockGasPriceEstimatorExec_Expecter{mock: &_m.Mock}
}

// DenoteInUSD provides a mock function with given fields: p, wrappedNativePrice
func (_m *MockGasPriceEstimatorExec) DenoteInUSD(p *big.Int, wrappedNativePrice *big.Int) (*big.Int, error) {
	ret := _m.Called(p, wrappedNativePrice)

	if len(ret) == 0 {
		panic("no return value specified for DenoteInUSD")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*big.Int, *big.Int) (*big.Int, error)); ok {
		return rf(p, wrappedNativePrice)
	}
	if rf, ok := ret.Get(0).(func(*big.Int, *big.Int) *big.Int); ok {
		r0 = rf(p, wrappedNativePrice)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*big.Int, *big.Int) error); ok {
		r1 = rf(p, wrappedNativePrice)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGasPriceEstimatorExec_DenoteInUSD_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DenoteInUSD'
type MockGasPriceEstimatorExec_DenoteInUSD_Call struct {
	*mock.Call
}

// DenoteInUSD is a helper method to define mock.On call
//   - p *big.Int
//   - wrappedNativePrice *big.Int
func (_e *MockGasPriceEstimatorExec_Expecter) DenoteInUSD(p interface{}, wrappedNativePrice interface{}) *MockGasPriceEstimatorExec_DenoteInUSD_Call {
	return &MockGasPriceEstimatorExec_DenoteInUSD_Call{Call: _e.mock.On("DenoteInUSD", p, wrappedNativePrice)}
}

func (_c *MockGasPriceEstimatorExec_DenoteInUSD_Call) Run(run func(p *big.Int, wrappedNativePrice *big.Int)) *MockGasPriceEstimatorExec_DenoteInUSD_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*big.Int), args[1].(*big.Int))
	})
	return _c
}

func (_c *MockGasPriceEstimatorExec_DenoteInUSD_Call) Return(_a0 *big.Int, _a1 error) *MockGasPriceEstimatorExec_DenoteInUSD_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGasPriceEstimatorExec_DenoteInUSD_Call) RunAndReturn(run func(*big.Int, *big.Int) (*big.Int, error)) *MockGasPriceEstimatorExec_DenoteInUSD_Call {
	_c.Call.Return(run)
	return _c
}

// EstimateMsgCostUSD provides a mock function with given fields: p, wrappedNativePrice, msg
func (_m *MockGasPriceEstimatorExec) EstimateMsgCostUSD(p *big.Int, wrappedNativePrice *big.Int, msg ccip.EVM2EVMOnRampCCIPSendRequestedWithMeta) (*big.Int, error) {
	ret := _m.Called(p, wrappedNativePrice, msg)

	if len(ret) == 0 {
		panic("no return value specified for EstimateMsgCostUSD")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*big.Int, *big.Int, ccip.EVM2EVMOnRampCCIPSendRequestedWithMeta) (*big.Int, error)); ok {
		return rf(p, wrappedNativePrice, msg)
	}
	if rf, ok := ret.Get(0).(func(*big.Int, *big.Int, ccip.EVM2EVMOnRampCCIPSendRequestedWithMeta) *big.Int); ok {
		r0 = rf(p, wrappedNativePrice, msg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*big.Int, *big.Int, ccip.EVM2EVMOnRampCCIPSendRequestedWithMeta) error); ok {
		r1 = rf(p, wrappedNativePrice, msg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EstimateMsgCostUSD'
type MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call struct {
	*mock.Call
}

// EstimateMsgCostUSD is a helper method to define mock.On call
//   - p *big.Int
//   - wrappedNativePrice *big.Int
//   - msg ccip.EVM2EVMOnRampCCIPSendRequestedWithMeta
func (_e *MockGasPriceEstimatorExec_Expecter) EstimateMsgCostUSD(p interface{}, wrappedNativePrice interface{}, msg interface{}) *MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call {
	return &MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call{Call: _e.mock.On("EstimateMsgCostUSD", p, wrappedNativePrice, msg)}
}

func (_c *MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call) Run(run func(p *big.Int, wrappedNativePrice *big.Int, msg ccip.EVM2EVMOnRampCCIPSendRequestedWithMeta)) *MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*big.Int), args[1].(*big.Int), args[2].(ccip.EVM2EVMOnRampCCIPSendRequestedWithMeta))
	})
	return _c
}

func (_c *MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call) Return(_a0 *big.Int, _a1 error) *MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call) RunAndReturn(run func(*big.Int, *big.Int, ccip.EVM2EVMOnRampCCIPSendRequestedWithMeta) (*big.Int, error)) *MockGasPriceEstimatorExec_EstimateMsgCostUSD_Call {
	_c.Call.Return(run)
	return _c
}

// GetGasPrice provides a mock function with given fields: ctx
func (_m *MockGasPriceEstimatorExec) GetGasPrice(ctx context.Context) (*big.Int, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetGasPrice")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*big.Int, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *big.Int); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGasPriceEstimatorExec_GetGasPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGasPrice'
type MockGasPriceEstimatorExec_GetGasPrice_Call struct {
	*mock.Call
}

// GetGasPrice is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MockGasPriceEstimatorExec_Expecter) GetGasPrice(ctx interface{}) *MockGasPriceEstimatorExec_GetGasPrice_Call {
	return &MockGasPriceEstimatorExec_GetGasPrice_Call{Call: _e.mock.On("GetGasPrice", ctx)}
}

func (_c *MockGasPriceEstimatorExec_GetGasPrice_Call) Run(run func(ctx context.Context)) *MockGasPriceEstimatorExec_GetGasPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockGasPriceEstimatorExec_GetGasPrice_Call) Return(_a0 *big.Int, _a1 error) *MockGasPriceEstimatorExec_GetGasPrice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGasPriceEstimatorExec_GetGasPrice_Call) RunAndReturn(run func(context.Context) (*big.Int, error)) *MockGasPriceEstimatorExec_GetGasPrice_Call {
	_c.Call.Return(run)
	return _c
}

// Median provides a mock function with given fields: gasPrices
func (_m *MockGasPriceEstimatorExec) Median(gasPrices []*big.Int) (*big.Int, error) {
	ret := _m.Called(gasPrices)

	if len(ret) == 0 {
		panic("no return value specified for Median")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func([]*big.Int) (*big.Int, error)); ok {
		return rf(gasPrices)
	}
	if rf, ok := ret.Get(0).(func([]*big.Int) *big.Int); ok {
		r0 = rf(gasPrices)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func([]*big.Int) error); ok {
		r1 = rf(gasPrices)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockGasPriceEstimatorExec_Median_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Median'
type MockGasPriceEstimatorExec_Median_Call struct {
	*mock.Call
}

// Median is a helper method to define mock.On call
//   - gasPrices []*big.Int
func (_e *MockGasPriceEstimatorExec_Expecter) Median(gasPrices interface{}) *MockGasPriceEstimatorExec_Median_Call {
	return &MockGasPriceEstimatorExec_Median_Call{Call: _e.mock.On("Median", gasPrices)}
}

func (_c *MockGasPriceEstimatorExec_Median_Call) Run(run func(gasPrices []*big.Int)) *MockGasPriceEstimatorExec_Median_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]*big.Int))
	})
	return _c
}

func (_c *MockGasPriceEstimatorExec_Median_Call) Return(_a0 *big.Int, _a1 error) *MockGasPriceEstimatorExec_Median_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockGasPriceEstimatorExec_Median_Call) RunAndReturn(run func([]*big.Int) (*big.Int, error)) *MockGasPriceEstimatorExec_Median_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockGasPriceEstimatorExec creates a new instance of MockGasPriceEstimatorExec. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockGasPriceEstimatorExec(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockGasPriceEstimatorExec {
	mock := &MockGasPriceEstimatorExec{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
