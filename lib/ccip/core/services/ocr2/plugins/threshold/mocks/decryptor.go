// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	decryptionplugin "github.com/smartcontractkit/tdh2/go/ocr2/decryptionplugin"
	mock "github.com/stretchr/testify/mock"
)

// Decryptor is an autogenerated mock type for the Decryptor type
type Decryptor struct {
	mock.Mock
}

type Decryptor_Expecter struct {
	mock *mock.Mock
}

func (_m *Decryptor) EXPECT() *Decryptor_Expecter {
	return &Decryptor_Expecter{mock: &_m.Mock}
}

// Decrypt provides a mock function with given fields: ctx, ciphertextId, ciphertext
func (_m *Decryptor) Decrypt(ctx context.Context, ciphertextId decryptionplugin.CiphertextId, ciphertext []byte) ([]byte, error) {
	ret := _m.Called(ctx, ciphertextId, ciphertext)

	if len(ret) == 0 {
		panic("no return value specified for Decrypt")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, decryptionplugin.CiphertextId, []byte) ([]byte, error)); ok {
		return rf(ctx, ciphertextId, ciphertext)
	}
	if rf, ok := ret.Get(0).(func(context.Context, decryptionplugin.CiphertextId, []byte) []byte); ok {
		r0 = rf(ctx, ciphertextId, ciphertext)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, decryptionplugin.CiphertextId, []byte) error); ok {
		r1 = rf(ctx, ciphertextId, ciphertext)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Decryptor_Decrypt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Decrypt'
type Decryptor_Decrypt_Call struct {
	*mock.Call
}

// Decrypt is a helper method to define mock.On call
//   - ctx context.Context
//   - ciphertextId decryptionplugin.CiphertextId
//   - ciphertext []byte
func (_e *Decryptor_Expecter) Decrypt(ctx interface{}, ciphertextId interface{}, ciphertext interface{}) *Decryptor_Decrypt_Call {
	return &Decryptor_Decrypt_Call{Call: _e.mock.On("Decrypt", ctx, ciphertextId, ciphertext)}
}

func (_c *Decryptor_Decrypt_Call) Run(run func(ctx context.Context, ciphertextId decryptionplugin.CiphertextId, ciphertext []byte)) *Decryptor_Decrypt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(decryptionplugin.CiphertextId), args[2].([]byte))
	})
	return _c
}

func (_c *Decryptor_Decrypt_Call) Return(_a0 []byte, _a1 error) *Decryptor_Decrypt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Decryptor_Decrypt_Call) RunAndReturn(run func(context.Context, decryptionplugin.CiphertextId, []byte) ([]byte, error)) *Decryptor_Decrypt_Call {
	_c.Call.Return(run)
	return _c
}

// NewDecryptor creates a new instance of Decryptor. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDecryptor(t interface {
	mock.TestingT
	Cleanup(func())
}) *Decryptor {
	mock := &Decryptor{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
