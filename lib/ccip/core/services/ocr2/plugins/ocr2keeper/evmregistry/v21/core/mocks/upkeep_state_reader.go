// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	automation "github.com/smartcontractkit/chainlink-common/pkg/types/automation"

	mock "github.com/stretchr/testify/mock"
)

// UpkeepStateReader is an autogenerated mock type for the UpkeepStateReader type
type UpkeepStateReader struct {
	mock.Mock
}

type UpkeepStateReader_Expecter struct {
	mock *mock.Mock
}

func (_m *UpkeepStateReader) EXPECT() *UpkeepStateReader_Expecter {
	return &UpkeepStateReader_Expecter{mock: &_m.Mock}
}

// SelectByWorkIDs provides a mock function with given fields: ctx, workIDs
func (_m *UpkeepStateReader) SelectByWorkIDs(ctx context.Context, workIDs ...string) ([]automation.UpkeepState, error) {
	_va := make([]interface{}, len(workIDs))
	for _i := range workIDs {
		_va[_i] = workIDs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SelectByWorkIDs")
	}

	var r0 []automation.UpkeepState
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...string) ([]automation.UpkeepState, error)); ok {
		return rf(ctx, workIDs...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...string) []automation.UpkeepState); ok {
		r0 = rf(ctx, workIDs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]automation.UpkeepState)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...string) error); ok {
		r1 = rf(ctx, workIDs...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpkeepStateReader_SelectByWorkIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SelectByWorkIDs'
type UpkeepStateReader_SelectByWorkIDs_Call struct {
	*mock.Call
}

// SelectByWorkIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - workIDs ...string
func (_e *UpkeepStateReader_Expecter) SelectByWorkIDs(ctx interface{}, workIDs ...interface{}) *UpkeepStateReader_SelectByWorkIDs_Call {
	return &UpkeepStateReader_SelectByWorkIDs_Call{Call: _e.mock.On("SelectByWorkIDs",
		append([]interface{}{ctx}, workIDs...)...)}
}

func (_c *UpkeepStateReader_SelectByWorkIDs_Call) Run(run func(ctx context.Context, workIDs ...string)) *UpkeepStateReader_SelectByWorkIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]string, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(string)
			}
		}
		run(args[0].(context.Context), variadicArgs...)
	})
	return _c
}

func (_c *UpkeepStateReader_SelectByWorkIDs_Call) Return(_a0 []automation.UpkeepState, _a1 error) *UpkeepStateReader_SelectByWorkIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *UpkeepStateReader_SelectByWorkIDs_Call) RunAndReturn(run func(context.Context, ...string) ([]automation.UpkeepState, error)) *UpkeepStateReader_SelectByWorkIDs_Call {
	_c.Call.Return(run)
	return _c
}

// NewUpkeepStateReader creates a new instance of UpkeepStateReader. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewUpkeepStateReader(t interface {
	mock.TestingT
	Cleanup(func())
}) *UpkeepStateReader {
	mock := &UpkeepStateReader{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
