package testutils

var (
	DefaultSecretsBytes      = []byte{0xaa, 0xbb, 0xcc}
	DefaultSecretsBase64     = "qrvM"
	DefaultSecretsUrlsBytes  = []byte{0x01, 0x02, 0x03}
	DefaultSecretsUrlsBase64 = "AQID"
	DefaultArg1              = "arg1"
	DefaultArg2              = "arg2"
	DefaultDONId             = "1"
	DefaultGasPrice          = 1_000_000_000

	// Below are the corresponding values for the encrypted threshold keys used in the test
	// mockThresholdPubKey := `{"Group":"P256","G_bar":"BAnzIguQNKnA37Zh0b3Z3K5CcvxHjzfTIytt37ZgNQLaTeuiq9rrVhz+yaZcvNQ9EYw978KmmYOq6qd0NA/ERh8=","H":"BOyfKc8aowVjOK2qYf0kdeuLkPeqbjDnjDFGIj/2n7O+qHIvqKx3A07Oa92tP5DkcS5AL/tipXDIBJvVWvcvudk=","HArray":["BBhLQicdsIUgigmIW4l6Xi1jBkFFXEtm2wuvydoZCjZZdlDZt82pXtOI+vPQbd5iawQPX6u4HUrhEisqwhx5P0A=","BLTOIUViwoVJTAzCKIo2FgliIfK7w3jG6wjwf3LVkdsMYJ2ZiEJDA7YC1GwsVgutYdxrwOkAY+wnoh9j+AYF/rQ=","BH2vi5G9ftykpOJARMlziZuZKXSx5YiP131HpwWwsgFAquSpsNTRWHsjk4nc0lcQKf6x9E+7UUQpAPwDpyrh7Xc=","BHQBZMqVRvxQHtnC4tqfh9Qc632IfSCPCBDsePyLzD1nXOf/qJWrCpfsZ3T3PaRm/U30LSgnb1nsuXI9nDuTFsM="]}`
	// mockPlaintextThresholdMasterKey := `{"Group":"P256","S":"MTpFRrh8F5mih+92W0l51ZVZIiGJgpHNUXb4vzkzv8A="}`
	// mockPlaintextThresholdKeyShares := [][]byte{
	// 	[]byte(`{"PublicKey":{"Group":"P256","G_bar":"BAnzIguQNKnA37Zh0b3Z3K5CcvxHjzfTIytt37ZgNQLaTeuiq9rrVhz+yaZcvNQ9EYw978KmmYOq6qd0NA/ERh8=","H":"BOyfKc8aowVjOK2qYf0kdeuLkPeqbjDnjDFGIj/2n7O+qHIvqKx3A07Oa92tP5DkcS5AL/tipXDIBJvVWvcvudk=","HArray":["BBhLQicdsIUgigmIW4l6Xi1jBkFFXEtm2wuvydoZCjZZdlDZt82pXtOI+vPQbd5iawQPX6u4HUrhEisqwhx5P0A=","BLTOIUViwoVJTAzCKIo2FgliIfK7w3jG6wjwf3LVkdsMYJ2ZiEJDA7YC1GwsVgutYdxrwOkAY+wnoh9j+AYF/rQ=","BH2vi5G9ftykpOJARMlziZuZKXSx5YiP131HpwWwsgFAquSpsNTRWHsjk4nc0lcQKf6x9E+7UUQpAPwDpyrh7Xc=","BHQBZMqVRvxQHtnC4tqfh9Qc632IfSCPCBDsePyLzD1nXOf/qJWrCpfsZ3T3PaRm/U30LSgnb1nsuXI9nDuTFsM="]},"PrivateKeyShare":{"Group":"P256","Index":0,"V":"Jzuh+h/jgm0HIp6iKVJxc/vCUOz7Ea+Y0twvRzJDheg="}}`),
	// 	[]byte(`{"PublicKey":{"Group":"P256","G_bar":"BAnzIguQNKnA37Zh0b3Z3K5CcvxHjzfTIytt37ZgNQLaTeuiq9rrVhz+yaZcvNQ9EYw978KmmYOq6qd0NA/ERh8=","H":"BOyfKc8aowVjOK2qYf0kdeuLkPeqbjDnjDFGIj/2n7O+qHIvqKx3A07Oa92tP5DkcS5AL/tipXDIBJvVWvcvudk=","HArray":["BBhLQicdsIUgigmIW4l6Xi1jBkFFXEtm2wuvydoZCjZZdlDZt82pXtOI+vPQbd5iawQPX6u4HUrhEisqwhx5P0A=","BLTOIUViwoVJTAzCKIo2FgliIfK7w3jG6wjwf3LVkdsMYJ2ZiEJDA7YC1GwsVgutYdxrwOkAY+wnoh9j+AYF/rQ=","BH2vi5G9ftykpOJARMlziZuZKXSx5YiP131HpwWwsgFAquSpsNTRWHsjk4nc0lcQKf6x9E+7UUQpAPwDpyrh7Xc=","BHQBZMqVRvxQHtnC4tqfh9Qc632IfSCPCBDsePyLzD1nXOf/qJWrCpfsZ3T3PaRm/U30LSgnb1nsuXI9nDuTFsM="]},"PrivateKeyShare":{"Group":"P256","Index":1,"V":"HTz+rYdK7UBrvU3N91tpEmIrf7hsoM1kVEFlzytTTBA="}}`),
	// 	[]byte(`{"PublicKey":{"Group":"P256","G_bar":"BAnzIguQNKnA37Zh0b3Z3K5CcvxHjzfTIytt37ZgNQLaTeuiq9rrVhz+yaZcvNQ9EYw978KmmYOq6qd0NA/ERh8=","H":"BOyfKc8aowVjOK2qYf0kdeuLkPeqbjDnjDFGIj/2n7O+qHIvqKx3A07Oa92tP5DkcS5AL/tipXDIBJvVWvcvudk=","HArray":["BBhLQicdsIUgigmIW4l6Xi1jBkFFXEtm2wuvydoZCjZZdlDZt82pXtOI+vPQbd5iawQPX6u4HUrhEisqwhx5P0A=","BLTOIUViwoVJTAzCKIo2FgliIfK7w3jG6wjwf3LVkdsMYJ2ZiEJDA7YC1GwsVgutYdxrwOkAY+wnoh9j+AYF/rQ=","BH2vi5G9ftykpOJARMlziZuZKXSx5YiP131HpwWwsgFAquSpsNTRWHsjk4nc0lcQKf6x9E+7UUQpAPwDpyrh7Xc=","BHQBZMqVRvxQHtnC4tqfh9Qc632IfSCPCBDsePyLzD1nXOf/qJWrCpfsZ3T3PaRm/U30LSgnb1nsuXI9nDuTFsM="]},"PrivateKeyShare":{"Group":"P256","Index":2,"V":"Ez5bYO6yWBPQV/z5xWRgsMiUroPeL+sv1aacVyRjEjg="}}`),
	// 	[]byte(`{"PublicKey":{"Group":"P256","G_bar":"BAnzIguQNKnA37Zh0b3Z3K5CcvxHjzfTIytt37ZgNQLaTeuiq9rrVhz+yaZcvNQ9EYw978KmmYOq6qd0NA/ERh8=","H":"BOyfKc8aowVjOK2qYf0kdeuLkPeqbjDnjDFGIj/2n7O+qHIvqKx3A07Oa92tP5DkcS5AL/tipXDIBJvVWvcvudk=","HArray":["BBhLQicdsIUgigmIW4l6Xi1jBkFFXEtm2wuvydoZCjZZdlDZt82pXtOI+vPQbd5iawQPX6u4HUrhEisqwhx5P0A=","BLTOIUViwoVJTAzCKIo2FgliIfK7w3jG6wjwf3LVkdsMYJ2ZiEJDA7YC1GwsVgutYdxrwOkAY+wnoh9j+AYF/rQ=","BH2vi5G9ftykpOJARMlziZuZKXSx5YiP131HpwWwsgFAquSpsNTRWHsjk4nc0lcQKf6x9E+7UUQpAPwDpyrh7Xc=","BHQBZMqVRvxQHtnC4tqfh9Qc632IfSCPCBDsePyLzD1nXOf/qJWrCpfsZ3T3PaRm/U30LSgnb1nsuXI9nDuTFsM="]},"PrivateKeyShare":{"Group":"P256","Index":3,"V":"CT+4FFYZwuc08qwlk21YTy793U9Pvwj7VwvS3x1y2GA="}}`),
	// }
	// Since the threshold public keys are encrypted with each node's public OCR2 offchain config key, the OCR2 offchain config key must be known in advance instead of regenerated every time.
	ExportedOcr2Keystores = [][]byte{
		[]byte(`{"keyType":"OCR2","chainType":"evm","id":"d8d0363c218526d809b8570257e7822bfde559ed532fa976118fc2d994155e55","onchainPublicKey":"e1deb8516367e7715da19134d58528778d6aff04","offchainPublicKey":"91c021d592ae5949eafaf4b74d622b13ac5784db7de9591755c3d3d2ed7a19b4","configPublicKey":"677c7ba4487eabea0f3341a095b94b6f56a1df97a165e49b8b0a7864c0f66077","crypto":{"cipher":"aes-128-ctr","ciphertext":"6d111479822c5b6e5a2b5ebde4f7ab6e6f7809e07bf6451d416f5e3082833a3b34e9aa820658c03487a3bdde5cbbfff3198a951f36a5ef91d1274211273f7af2e3e17e258530c25aebb1099395ecc1d96e567adefa727f06fcb61d0df2829737998bdd959759c62342fbd74a3f231535bb413d9c4479e33ff294d66bfc90d2b6db9d12250ef8daab309bb914373a76da24e82dce5b0da428a19efc1cdda48719f898a91f21b4fc89458d126d1023ffc0efcc8cf3e0ee249bfa0b1b55fa151d5c2630be21be2ea27964b82816ee72885f7bc59d7d4157d5333ab8324cc65a78581437a8600e8c044fabe80ae76cbe39ae332c5c0f85ac1090b13a1525caca40459082e9ca762714e50543e6aa907b7286eafc93a51c2d67265393d86ad83d431bf068aa006c36f63356","cipherparams":{"iv":"11b7b2f3c33dfff2a3125fdb10048601"},"kdf":"scrypt","kdfparams":{"dklen":32,"n":2,"p":1,"r":8,"salt":"a3c5952aa41720097a50fea6957c994a9c39cb1dbe7bd59cff5b9354633836c1"},"mac":"df6d0d0b5f2cc95f1e1d4c9894f8487c56b814ca55d10e520d0c44e91d699012"}}`),
		[]byte(`{"keyType":"OCR2","chainType":"evm","id":"03a0713ffb5b506cbf12bd59fd7023c9dc3f1f56d4d2f9ed564f0361c3aa1119","onchainPublicKey":"3203c6923adc7ecff671e00632eb350fa354e957","offchainPublicKey":"c9aea40c0e5f13d9704d3051faa01718f1d4aa1419d3e6199f7deacee56a596e","configPublicKey":"23ccc8500fa3af447c7a8b3aa4e41ad7b34f6585bdc3e8c847c7ff12a8caa818","crypto":{"cipher":"aes-128-ctr","ciphertext":"5aa58bfd17209a4c9da966a1b175c74137536531ada19507434d6dbf146d71516c0ed2b52b5e664e69c4286a56f38be322206a33eacfaa854be313bade3b5876e5b40aec5be4dca761749b535ad9cd1575897d6736eb96fb9e118eb696c44d111b81757bd73a6d33c170febaf57705590f279625ec536d1656eac96a953de915693a9cd040a5d5948f3b288ebdbb178c955b5f0faa432d68e744f39dc621cbc4c5853627addc3dd2ed5e1ba4a514278d56d0cdef2e57688ad0752f3077656f16fa3144c33f3ed96ebdeb3009139f327d6c12d344a047147e71a95d5b08582bd8ee86c86e308d81f16357c79b50a48041994d98490a4b9d965525187901a2862d41bcc6822046f99ad481ae104aa75b816c5abac876bce0b07b2b73bb459830a5bc70f20c34b87ed6fc","cipherparams":{"iv":"d8fcf50fff54dfa082d40c0de90b6ab4"},"kdf":"scrypt","kdfparams":{"dklen":32,"n":2,"p":1,"r":8,"salt":"64ba7faf39b49c46d5360109ef23e3f4198a3f402cbb975686fe4b52cc7c0b1f"},"mac":"09520656cc08b5743f95d386a5b7c3d164ecaa2002a0b83f2cd8d44c97d99b8a"}}`),
		[]byte(`{"keyType":"OCR2","chainType":"evm","id":"ace8972c126601fdc5f9ed42c909a57d2a19dff2be7ce341913c01a4921c829f","onchainPublicKey":"d8699a99d2a3ab4ccfc61bc7df72b30605a05b7c","offchainPublicKey":"9c1fa3ce9d355a73ce2dcdece70e7314af8da13f5ca30ee251dbed201dc904ed","configPublicKey":"9a2b9087af305c187d1292842072e50d2c4e98cb4e26c6328021fdd992e78926","crypto":{"cipher":"aes-128-ctr","ciphertext":"c7d2da9c60779ad33dce870b1f8af94082dfdfe51d4f04f27cf505bba21b17e287c685880614e22df92ce7549230e8b1a327de0686d892f5c4dda546132b044bde2a58d60bf335931b78e42342e2578e54111592cad4301d22c192c87809e3cef6e6dd1a30a7a208c78461154d856213466c1afad4023001c1b45fdf7804c6a9a4eb64ac1ea59775a1e694c79fc492b4d7422cd777778549c817e735e8e5e5f45aea9c9e9a5a7ff1c71b7879af120a4aa467a3ac0cdb1ba04f893283cad533c4ac9723969a64bbfd69bb7ef88d88d8d96c5293fee35c14a00a8e6c0cb2e7cfa918d05ec9e092e4fdad2a41edf7a1cb9e1a1a12ec6fbb594eb1e07d5dbb79eda390b826aa7ca8ccfda1cee9c125eb8a1d07d9e899cf6e9c1ec0fd40d83a50656674cc5c9d0a88710c65","cipherparams":{"iv":"5b6fca38ff8c03e38fb6f56f9d90fe8d"},"kdf":"scrypt","kdfparams":{"dklen":32,"n":2,"p":1,"r":8,"salt":"6bfc5f03e8bd10efea3808d025c5769a49ade8bee6c608d713a6bf3f940a37ba"},"mac":"e1e4bcbe67a80d73ee12c8080ddd99bba9ca8774349a9cb5c5b9821983e460ca"}}`),
		[]byte(`{"keyType":"OCR2","chainType":"evm","id":"5b09af3b5a5a420436fa206eb266b6a4d796406030d022d4beaf6609e9da9274","onchainPublicKey":"c27e7957c243e061cc17d8f30e8f894280235499","offchainPublicKey":"f6c2fd84bb73252d49852b50420f5983b488609d9f73ef601e7641df24794dec","configPublicKey":"45a96a32b7339cd22da1a1c42c2ee71e698dd7dfbe7fd997949f1cdb3f2ba518","crypto":{"cipher":"aes-128-ctr","ciphertext":"f842a872d8a0033fc9b91673a1c2f095eda18c4716b53c743d04b82710f2bfa2c950356e50ea085b05975c36176a3dd2b10901508f297e3ff966cde02cf6ba077eebdb8708117c4c9f4498358e74fdc98bc1130a32bfa6bf8f46d9ea77d4e56952cc1ccbbf2bde05a48acf13fbb238675d640b6a74af70dfbaa63cad57108480238ce946a5af5f60b09794ff5b40bde982f32d7b48f5e9a51830a46cce1cf2f9a0c0ec220c062eb90602b2abbaf2a6a2da2d8b3dbc211db17d599789654a2a26f1fad52e22d7eed91365ce7d964731936307c39e66bd9a9ffc999f3174317786077ab83224d06e025b7de5974293b96eabcb3b9ffb17d2d0b49743cd7b4f1b222e32be3f32b9d2177b1c20cb7d56c10ae0789f8586ee99ccd9ac8b626df058b454686b2d8a663635b8","cipherparams":{"iv":"21a5417a99f37507bc2fbdda83c5856f"},"kdf":"scrypt","kdfparams":{"dklen":32,"n":2,"p":1,"r":8,"salt":"c98ac207e897658d7c42029c2858ba29f03abe9298745b0b909377085806895a"},"mac":"dc1ef1c284e1d001b63f1d545e308c4ef194757605a4b57b8c565c5c40e64146"}}`),
	}
	// Threshold key shares have been generated with the Functions admin tooling (https://github.com/smartcontractkit/functions-admin-tooling/tree/main/threshold_key_manager)
	MockThresholdKeyShares = []string{
		"40e99151aaa2002631cfdac741a0f206b9ebc492a2c5bd56deac64118148df6e3829a6f81b6df54694ca241f5bc12fd2b58bce9106767f4adbf7250e0a033386b56eb93159c3cf14748b6a0d1996a51039ce9ff98acaba758913fa2d671e86b909edbe27bdf441b68be2c2debbe8a29d814a04accd996b4df0fa8e0cea65ef47922c4aca8132fe4f2d6def9c4683b031267f60ccf896b6bf6b1957786deafebb5bc1663778ee893511306dda7833d01efe0d63224d0da0cea6d728953793e6b3e97f81cb64f80ed4ff4f3c349b90f11290adb0eff07f8ccb9b97d098903ce194f33f284d38832e12902354c3cda72189b3ac31b8206575391c1618dee6530b99fc60d667052e0065365842176b6b5d9b779be0be4f7fa27115d65e08d01f13db695581c1be4a1f96b980cba45bd528aaee23809fa9a601a229c7018eb6755f43a0d38125a872fcf8e109ca92abc55348cccc4f83beae8412425570b8aa9b6526a9ddd05a41e5c6d6865cf91ff1912490d67e6a99c3a91db433120b13768affef6b0cdeb33d085078fa27b90ef2663c69fcf9f3dad9a1fb67b9605f035b2cebf7808801e9ccd3eb83015aaefecf1fa97674923219c543accdc23d5f624cdbd673eba5f29cc66c647e1fe454e0c73efe8ff6605f78243e01aa42c14241f890388d6d35e3071ee89130f62317b8d624fc2b7c270769cf8e5c054cf3ed4163917f0af7372ab0eafdc4dd4faaf3cc30d406041404eae2ba942c800e4d9726e8e0d27e1488c5aaf47d977665c86d8574c17b69c8b31783ec479929e6b858ef57b072bcfaa3a747362235e142e9b73f19192d8878b832ff25b8cf29f0b5c130cb1d505ec1830a0901c0ac1f9b5542db33a9c39afc6ffa68d32a91dbb525cec9536647020cd22925baf63d173cd9b377fbcb509efcbf7830df459e080f5794186d628be938e9a53d3ea3182eb328d9956905dc6c22061c76118b208cdd7f063522d81ab4e404ef6d4a53b2099d2c94b8c05526b8367cfff6f08340bbe53cf2fbe6e7fae3b7062d27722085",
		"9956a87c447eb15b82906742ef84980ca1dc6846b2234468ed7399af5392243b14226ab2a6ff6220776d1b5a591cacfde0bd583f48683d4f38186300f919681aa0a886e7eddc556e1bb434c18994df214a5b6fe4bfcb09ae9fc5dba4cb7e027ce01121dbad0621fe35311e1015c7d4d006ababd5c7377a0f70aecafaf3f709c0571ca2dd89b6cd83867bd299e8ce01e04ecafc1230ab0aeb2812a693fadcba7b479a00da357e75876d0cd2a3d85774e056d63436623ff400c9fd76446f5b7fd90902bf4d4268fb5775e326a887bd59136692bb5b0400790dd7908f9d5c7e422c76831ac3876e67a8b6d66ceb49c9c0835660bbd90a1e18ab8486dc464b6fd4044c580d3082842ab655169cf46abe25ad4de04d0bdefc74fee9c2009b43eea48f976a449ceb805993383b06ddfaf5a3df112a1c5e72ea121b9fbeafe8b7666500abeb52f2bad6035295b66994c0d239ced38f32af4df161b7bd66886703c050531173ae6ace942a311a0077b601c025bb59a78b43558c09ae55a6ce70d408b7e0ae439542971b33fdf14a7c5a36fed8d310e8cd08fe7c6b45d1bffd351bad2fd1cfc8d964f8ec39db82a7fd4095771f6d5e84516598364bd0532d3b8ccd660fa73bc1e5840087eeac18c7fd52611deac5aa50a6a033d2aecd79ec8bba8045615ff4d7b77e0d087110b888426d138107236ed4a6a6b6a5b51e54149e2272896d0180a46cde8955a8adeb969c35c5b58a0958ce955f2634723d8bf38c2d1c3efe0ce2494b00947d3958ec3b5b9f5445acad30296a34060bd688c7b91e818f49ddfa560541682ce0ff6d245b9df5adee989ea6b9eedcf58b3d47fdc64e8c046efaad51e169a81fc22419be8666897f18d7c913d806764714c9a735cce3698da8ecb3b16c1a88f6ef2b4a52686d4d1b5cfc5859422a1b39f7fe46691ccd28050a8edf040f438b407bfa4cc48641155e3c7531301d5192148ab2ccba064afeed1ae8723702ea9793317a1989a2601a958fda5bb23222a983842a31553930c4364b661e03376fff2471ae",
		"4c19996b1ed254aba3f5d36a24a3a24a008cc864562610e15523087c2c05e63c57bc6c386d4f1b3cb53eb10ae26ab594a6693fa8ee5783ab4b4b040a0e4f27beda68405b72cbcbeee762114b23d1fe69aaf72bc81f70e76251df20b83fe6b82240fbe64be3b1a238bf3c5a8b7c8bac945fd25348b5618ff2409940f64bd86afb64695706c57340e72741af4e0d2d47407b041c57aac7ba9d35ce2316d6bff28ee5c22b007ec2c9b5914cb5baaa9e2ce6aafeb976c681dc08e1eb28514a7b7adfa0536ced06e3e23e9a14bad3f68b836442a952c39bfcaabdae0d3226dde590f3ba927b49a0c6f011b02abd14e5db8bf355432e3d3286c9aa5fcf2b6620cda13d56753a420555d0300de04c008d6cb9b62e4c7c44ea563445cccfc8266b06192c2981ce5b1157b6c16ca38e6b7036dd2e21e75646bbffc0be2f6c23257666a5679b8e35ac4382e31c05e4ef1170293617e6f77bd32b57f5ac617a7cbafa345c2137d8a7b70bf4a3b5c4010d73e7f064a9e5e51eaecf600bb837e0a648359aa4857b0875f14a62c648e7a7a13fac7e466bdd9ac89cf7f1f46081f5d107a6fffa5a0d8c70aa5aeaed3df35e9a5b33cabb98b22bf62d84cf4e57d7683457dbff3542a9299f9acd291225e4e8fc1255b305ca0e1404707053e50d55f69d8b709d9350db4f6f36ecf63037c6e00073df67b610c322e79ae4616314dc47d296b4570b659c21d659375fd1006af4d42838eb20f1682ffd19cf88b603790005b8c5ae4ab9c0415bcb0c8ce4a04d1f7d18fef5e16e9061aa56bc596bcdeae74ccdd1c95ab1607280602f75ddbb3a25cbe9af99197b0dab671a2e1069340cefcefef75e24e222c9e3d1563634eb3f3a8319bfd6f95b3a995d9dcad5a9aa2aea6bf46e6253080d8e4ec3b918d0012fe500edfba67e49d0520be57e7414629bb40e64312fb1d3e26bdef31cc8ec2426f71f91cfee0d3cd9390dceafe615106fd56703b85b377b812b8a6c24c9fc5d0c68464565d9b421a4cd296f00ddd7a062ea16c56232a4d2a69c2817bfa882",
		"ba1edea16bd7223b3a413af6b392662b9b01eb18f67d41defe1cc4190160cf12c462414df90299b0ca5fbe7e2cf11f902a23de8340d9a190f32de0199ce150f15c6ed658638fe33701e3cee11574865efe768ecefd53ba20833e1300d4cc84bf0291852031fe5ce6b7dbd1bfe46d6887e39b6466b6ef76cd5743349c4109176bd0e60074bb7d8ba1cf47609b628045ed64743be510d90a21b7022f88a08dcabd7bc600c26b228b94ee175268f8206df72f7a708c014d162231e9009af0acacfb115674eb6fbb873e0c47b7b66820c7999143da38a871610614c8fe45a0c0d83020ed7c1e3eb5ee14e3e3bf70917a7d7a96e356e52c3bb33607448f61f592142de315fd29a172ce686b028c47cfb936780e1beebbbd2fca706534060e8d7f2de973005229183cd5ceaef3ceb944ca98562f22deec110ff6993ff9512f9b8e1aac17c08fa0295315229596a87eb0935e9b28ff1aff4d71889fed76fa7021e3494155b3b34f28cf5e717b46351bd9b11b0ddceac9ff3bd0c962ac003f5062acdb50058d50546d71fbb50e21221addd61a36f9ef98611f77d1c3c3ecc344fcdd9008d37ebe15944375ab767b1f937cb647dea26d7c6c8f9832b4f8632febba8abb107c39ba85ce09719eb7d7607ca6d0cc491419f6d3863e5d6f446a3c291da01c16e58e65b4428f8e0c024df391e6864e879bfc749d9175654c04413cbe4a65a35cea5596d6dc2d81800b2458fc75bfec6fedbbba857c24a19a0e747dc1d089b9eebb9b5098b2bcd2720f52d28055611ceb7b26d0c4b2a20c3b3aa9da50872bd95ef8154d6fd28669ffb48792e234a6f8eba3e53b57d994b8efd594045e0b6afb764013c28a23934a1502d0a90fe853e0a95738f1cdff79c7ee4e91d57824cd5cea56e8a45d95ee0cb1fa8911ccbd1e14883873817109f9ca4d21be60e3966541d1e1aaa7de0a57a3437f8a24e41a941d20f03055cd74f4b3048d2d9da7406da46ea82b65fd7b18e9004474583ecc9a2759b12fdd770f47222ef2c02d42ed0d18837a3e259f009d1e",
	}
	// This has been generated using the Functions Toolkit (https://github.com/smartcontractkit/functions-toolkit/blob/main/src/SecretsManager.ts) and decrypts to the JSON string `{"0x0":"qrvM"}`
	DefaultThresholdSecretsHex = "0x7b225444483243747874223a2265794a48636d393163434936496c41794e5459694c434a44496a6f69533035305a5559325448593056553168543341766148637955584a4b65545a68626b3177527939794f464e78576a59356158646d636a6c4f535430694c434a4d59574a6c62434936496b464251554642515546425155464251554642515546425155464251554642515546425155464251554642515546425155464251554642515545394969776956534936496b4a45536c6c7a51334e7a623055334d6e6444574846474e557056634770585a573157596e565265544d796431526d4d32786c636c705a647a4671536e6c47627a5256615735744e6d773355456855546e6b7962324e746155686f626c51354d564a6a4e6e5230656c70766147644255326372545430694c434a5658324a6863694936496b4a4961544e69627a5a45536d396a4d324d344d6c46614d5852724c325645536b4a484d336c5a556d783555306834576d684954697472623264575a306f33546e4e456232314b5931646853544979616d63305657644f556c526e57465272655570325458706952306c4a617a466e534851314f4430694c434a46496a6f694d7a524956466c354d544e474b307836596e5a584e7a6c314d6d356c655574514e6b397a656e467859335253513239705a315534534652704e4430694c434a47496a6f69557a5132596d6c6952545a584b314176546d744252445677575459796148426862316c6c6330684853556869556c56614e303155556c6f345554306966513d3d222c2253796d43747874223a2253764237652f4a556a552b433358757873384e5378316967454e517759755051623730306a4a6144222c224e6f6e6365223a224d31714b557a6b306b77374767593538227d"
)
