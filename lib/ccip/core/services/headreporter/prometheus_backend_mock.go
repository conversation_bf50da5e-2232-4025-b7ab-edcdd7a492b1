// Code generated by mockery v2.43.2. DO NOT EDIT.

package headreporter

import (
	big "math/big"

	mock "github.com/stretchr/testify/mock"
)

// MockPrometheusBackend is an autogenerated mock type for the PrometheusBackend type
type MockPrometheusBackend struct {
	mock.Mock
}

type MockPrometheusBackend_Expecter struct {
	mock *mock.Mock
}

func (_m *MockPrometheusBackend) EXPECT() *MockPrometheusBackend_Expecter {
	return &MockPrometheusBackend_Expecter{mock: &_m.Mock}
}

// SetMaxUnconfirmedAge provides a mock function with given fields: _a0, _a1
func (_m *MockPrometheusBackend) SetMaxUnconfirmedAge(_a0 *big.Int, _a1 float64) {
	_m.Called(_a0, _a1)
}

// MockPrometheusBackend_SetMaxUnconfirmedAge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetMaxUnconfirmedAge'
type MockPrometheusBackend_SetMaxUnconfirmedAge_Call struct {
	*mock.Call
}

// SetMaxUnconfirmedAge is a helper method to define mock.On call
//   - _a0 *big.Int
//   - _a1 float64
func (_e *MockPrometheusBackend_Expecter) SetMaxUnconfirmedAge(_a0 interface{}, _a1 interface{}) *MockPrometheusBackend_SetMaxUnconfirmedAge_Call {
	return &MockPrometheusBackend_SetMaxUnconfirmedAge_Call{Call: _e.mock.On("SetMaxUnconfirmedAge", _a0, _a1)}
}

func (_c *MockPrometheusBackend_SetMaxUnconfirmedAge_Call) Run(run func(_a0 *big.Int, _a1 float64)) *MockPrometheusBackend_SetMaxUnconfirmedAge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*big.Int), args[1].(float64))
	})
	return _c
}

func (_c *MockPrometheusBackend_SetMaxUnconfirmedAge_Call) Return() *MockPrometheusBackend_SetMaxUnconfirmedAge_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockPrometheusBackend_SetMaxUnconfirmedAge_Call) RunAndReturn(run func(*big.Int, float64)) *MockPrometheusBackend_SetMaxUnconfirmedAge_Call {
	_c.Call.Return(run)
	return _c
}

// SetMaxUnconfirmedBlocks provides a mock function with given fields: _a0, _a1
func (_m *MockPrometheusBackend) SetMaxUnconfirmedBlocks(_a0 *big.Int, _a1 int64) {
	_m.Called(_a0, _a1)
}

// MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetMaxUnconfirmedBlocks'
type MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call struct {
	*mock.Call
}

// SetMaxUnconfirmedBlocks is a helper method to define mock.On call
//   - _a0 *big.Int
//   - _a1 int64
func (_e *MockPrometheusBackend_Expecter) SetMaxUnconfirmedBlocks(_a0 interface{}, _a1 interface{}) *MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call {
	return &MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call{Call: _e.mock.On("SetMaxUnconfirmedBlocks", _a0, _a1)}
}

func (_c *MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call) Run(run func(_a0 *big.Int, _a1 int64)) *MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*big.Int), args[1].(int64))
	})
	return _c
}

func (_c *MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call) Return() *MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call) RunAndReturn(run func(*big.Int, int64)) *MockPrometheusBackend_SetMaxUnconfirmedBlocks_Call {
	_c.Call.Return(run)
	return _c
}

// SetPipelineRunsQueued provides a mock function with given fields: n
func (_m *MockPrometheusBackend) SetPipelineRunsQueued(n int) {
	_m.Called(n)
}

// MockPrometheusBackend_SetPipelineRunsQueued_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetPipelineRunsQueued'
type MockPrometheusBackend_SetPipelineRunsQueued_Call struct {
	*mock.Call
}

// SetPipelineRunsQueued is a helper method to define mock.On call
//   - n int
func (_e *MockPrometheusBackend_Expecter) SetPipelineRunsQueued(n interface{}) *MockPrometheusBackend_SetPipelineRunsQueued_Call {
	return &MockPrometheusBackend_SetPipelineRunsQueued_Call{Call: _e.mock.On("SetPipelineRunsQueued", n)}
}

func (_c *MockPrometheusBackend_SetPipelineRunsQueued_Call) Run(run func(n int)) *MockPrometheusBackend_SetPipelineRunsQueued_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *MockPrometheusBackend_SetPipelineRunsQueued_Call) Return() *MockPrometheusBackend_SetPipelineRunsQueued_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockPrometheusBackend_SetPipelineRunsQueued_Call) RunAndReturn(run func(int)) *MockPrometheusBackend_SetPipelineRunsQueued_Call {
	_c.Call.Return(run)
	return _c
}

// SetPipelineTaskRunsQueued provides a mock function with given fields: n
func (_m *MockPrometheusBackend) SetPipelineTaskRunsQueued(n int) {
	_m.Called(n)
}

// MockPrometheusBackend_SetPipelineTaskRunsQueued_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetPipelineTaskRunsQueued'
type MockPrometheusBackend_SetPipelineTaskRunsQueued_Call struct {
	*mock.Call
}

// SetPipelineTaskRunsQueued is a helper method to define mock.On call
//   - n int
func (_e *MockPrometheusBackend_Expecter) SetPipelineTaskRunsQueued(n interface{}) *MockPrometheusBackend_SetPipelineTaskRunsQueued_Call {
	return &MockPrometheusBackend_SetPipelineTaskRunsQueued_Call{Call: _e.mock.On("SetPipelineTaskRunsQueued", n)}
}

func (_c *MockPrometheusBackend_SetPipelineTaskRunsQueued_Call) Run(run func(n int)) *MockPrometheusBackend_SetPipelineTaskRunsQueued_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *MockPrometheusBackend_SetPipelineTaskRunsQueued_Call) Return() *MockPrometheusBackend_SetPipelineTaskRunsQueued_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockPrometheusBackend_SetPipelineTaskRunsQueued_Call) RunAndReturn(run func(int)) *MockPrometheusBackend_SetPipelineTaskRunsQueued_Call {
	_c.Call.Return(run)
	return _c
}

// SetUnconfirmedTransactions provides a mock function with given fields: _a0, _a1
func (_m *MockPrometheusBackend) SetUnconfirmedTransactions(_a0 *big.Int, _a1 int64) {
	_m.Called(_a0, _a1)
}

// MockPrometheusBackend_SetUnconfirmedTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetUnconfirmedTransactions'
type MockPrometheusBackend_SetUnconfirmedTransactions_Call struct {
	*mock.Call
}

// SetUnconfirmedTransactions is a helper method to define mock.On call
//   - _a0 *big.Int
//   - _a1 int64
func (_e *MockPrometheusBackend_Expecter) SetUnconfirmedTransactions(_a0 interface{}, _a1 interface{}) *MockPrometheusBackend_SetUnconfirmedTransactions_Call {
	return &MockPrometheusBackend_SetUnconfirmedTransactions_Call{Call: _e.mock.On("SetUnconfirmedTransactions", _a0, _a1)}
}

func (_c *MockPrometheusBackend_SetUnconfirmedTransactions_Call) Run(run func(_a0 *big.Int, _a1 int64)) *MockPrometheusBackend_SetUnconfirmedTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*big.Int), args[1].(int64))
	})
	return _c
}

func (_c *MockPrometheusBackend_SetUnconfirmedTransactions_Call) Return() *MockPrometheusBackend_SetUnconfirmedTransactions_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockPrometheusBackend_SetUnconfirmedTransactions_Call) RunAndReturn(run func(*big.Int, int64)) *MockPrometheusBackend_SetUnconfirmedTransactions_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockPrometheusBackend creates a new instance of MockPrometheusBackend. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockPrometheusBackend(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockPrometheusBackend {
	mock := &MockPrometheusBackend{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
