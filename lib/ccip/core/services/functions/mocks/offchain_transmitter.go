// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	functions "github.com/smartcontractkit/chainlink/v2/core/services/functions"
	mock "github.com/stretchr/testify/mock"
)

// OffchainTransmitter is an autogenerated mock type for the OffchainTransmitter type
type OffchainTransmitter struct {
	mock.Mock
}

type OffchainTransmitter_Expecter struct {
	mock *mock.Mock
}

func (_m *OffchainTransmitter) EXPECT() *OffchainTransmitter_Expecter {
	return &OffchainTransmitter_Expecter{mock: &_m.Mock}
}

// ReportChannel provides a mock function with given fields:
func (_m *OffchainTransmitter) ReportChannel() chan *functions.OffchainResponse {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ReportChannel")
	}

	var r0 chan *functions.OffchainResponse
	if rf, ok := ret.Get(0).(func() chan *functions.OffchainResponse); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(chan *functions.OffchainResponse)
		}
	}

	return r0
}

// OffchainTransmitter_ReportChannel_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReportChannel'
type OffchainTransmitter_ReportChannel_Call struct {
	*mock.Call
}

// ReportChannel is a helper method to define mock.On call
func (_e *OffchainTransmitter_Expecter) ReportChannel() *OffchainTransmitter_ReportChannel_Call {
	return &OffchainTransmitter_ReportChannel_Call{Call: _e.mock.On("ReportChannel")}
}

func (_c *OffchainTransmitter_ReportChannel_Call) Run(run func()) *OffchainTransmitter_ReportChannel_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OffchainTransmitter_ReportChannel_Call) Return(_a0 chan *functions.OffchainResponse) *OffchainTransmitter_ReportChannel_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OffchainTransmitter_ReportChannel_Call) RunAndReturn(run func() chan *functions.OffchainResponse) *OffchainTransmitter_ReportChannel_Call {
	_c.Call.Return(run)
	return _c
}

// TransmitReport provides a mock function with given fields: ctx, report
func (_m *OffchainTransmitter) TransmitReport(ctx context.Context, report *functions.OffchainResponse) error {
	ret := _m.Called(ctx, report)

	if len(ret) == 0 {
		panic("no return value specified for TransmitReport")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *functions.OffchainResponse) error); ok {
		r0 = rf(ctx, report)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OffchainTransmitter_TransmitReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransmitReport'
type OffchainTransmitter_TransmitReport_Call struct {
	*mock.Call
}

// TransmitReport is a helper method to define mock.On call
//   - ctx context.Context
//   - report *functions.OffchainResponse
func (_e *OffchainTransmitter_Expecter) TransmitReport(ctx interface{}, report interface{}) *OffchainTransmitter_TransmitReport_Call {
	return &OffchainTransmitter_TransmitReport_Call{Call: _e.mock.On("TransmitReport", ctx, report)}
}

func (_c *OffchainTransmitter_TransmitReport_Call) Run(run func(ctx context.Context, report *functions.OffchainResponse)) *OffchainTransmitter_TransmitReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*functions.OffchainResponse))
	})
	return _c
}

func (_c *OffchainTransmitter_TransmitReport_Call) Return(_a0 error) *OffchainTransmitter_TransmitReport_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OffchainTransmitter_TransmitReport_Call) RunAndReturn(run func(context.Context, *functions.OffchainResponse) error) *OffchainTransmitter_TransmitReport_Call {
	_c.Call.Return(run)
	return _c
}

// NewOffchainTransmitter creates a new instance of OffchainTransmitter. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOffchainTransmitter(t interface {
	mock.TestingT
	Cleanup(func())
}) *OffchainTransmitter {
	mock := &OffchainTransmitter{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
