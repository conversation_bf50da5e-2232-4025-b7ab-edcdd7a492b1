// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	functions "github.com/smartcontractkit/chainlink/v2/core/services/functions"
	mock "github.com/stretchr/testify/mock"
)

// ExternalAdapterClient is an autogenerated mock type for the ExternalAdapterClient type
type ExternalAdapterClient struct {
	mock.Mock
}

type ExternalAdapterClient_Expecter struct {
	mock *mock.Mock
}

func (_m *ExternalAdapterClient) EXPECT() *ExternalAdapterClient_Expecter {
	return &ExternalAdapterClient_Expecter{mock: &_m.Mock}
}

// FetchEncryptedSecrets provides a mock function with given fields: ctx, encryptedSecretsUrls, requestId, jobName
func (_m *ExternalAdapterClient) FetchEncryptedSecrets(ctx context.Context, encryptedSecretsUrls []byte, requestId string, jobName string) ([]byte, []byte, error) {
	ret := _m.Called(ctx, encryptedSecretsUrls, requestId, jobName)

	if len(ret) == 0 {
		panic("no return value specified for FetchEncryptedSecrets")
	}

	var r0 []byte
	var r1 []byte
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, []byte, string, string) ([]byte, []byte, error)); ok {
		return rf(ctx, encryptedSecretsUrls, requestId, jobName)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []byte, string, string) []byte); ok {
		r0 = rf(ctx, encryptedSecretsUrls, requestId, jobName)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []byte, string, string) []byte); ok {
		r1 = rf(ctx, encryptedSecretsUrls, requestId, jobName)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]byte)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, []byte, string, string) error); ok {
		r2 = rf(ctx, encryptedSecretsUrls, requestId, jobName)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ExternalAdapterClient_FetchEncryptedSecrets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchEncryptedSecrets'
type ExternalAdapterClient_FetchEncryptedSecrets_Call struct {
	*mock.Call
}

// FetchEncryptedSecrets is a helper method to define mock.On call
//   - ctx context.Context
//   - encryptedSecretsUrls []byte
//   - requestId string
//   - jobName string
func (_e *ExternalAdapterClient_Expecter) FetchEncryptedSecrets(ctx interface{}, encryptedSecretsUrls interface{}, requestId interface{}, jobName interface{}) *ExternalAdapterClient_FetchEncryptedSecrets_Call {
	return &ExternalAdapterClient_FetchEncryptedSecrets_Call{Call: _e.mock.On("FetchEncryptedSecrets", ctx, encryptedSecretsUrls, requestId, jobName)}
}

func (_c *ExternalAdapterClient_FetchEncryptedSecrets_Call) Run(run func(ctx context.Context, encryptedSecretsUrls []byte, requestId string, jobName string)) *ExternalAdapterClient_FetchEncryptedSecrets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte), args[2].(string), args[3].(string))
	})
	return _c
}

func (_c *ExternalAdapterClient_FetchEncryptedSecrets_Call) Return(encryptedSecrets []byte, userError []byte, err error) *ExternalAdapterClient_FetchEncryptedSecrets_Call {
	_c.Call.Return(encryptedSecrets, userError, err)
	return _c
}

func (_c *ExternalAdapterClient_FetchEncryptedSecrets_Call) RunAndReturn(run func(context.Context, []byte, string, string) ([]byte, []byte, error)) *ExternalAdapterClient_FetchEncryptedSecrets_Call {
	_c.Call.Return(run)
	return _c
}

// RunComputation provides a mock function with given fields: ctx, requestId, jobName, subscriptionOwner, subscriptionId, flags, nodeProvidedSecrets, requestData
func (_m *ExternalAdapterClient) RunComputation(ctx context.Context, requestId string, jobName string, subscriptionOwner string, subscriptionId uint64, flags functions.RequestFlags, nodeProvidedSecrets string, requestData *functions.RequestData) ([]byte, []byte, []string, error) {
	ret := _m.Called(ctx, requestId, jobName, subscriptionOwner, subscriptionId, flags, nodeProvidedSecrets, requestData)

	if len(ret) == 0 {
		panic("no return value specified for RunComputation")
	}

	var r0 []byte
	var r1 []byte
	var r2 []string
	var r3 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, uint64, functions.RequestFlags, string, *functions.RequestData) ([]byte, []byte, []string, error)); ok {
		return rf(ctx, requestId, jobName, subscriptionOwner, subscriptionId, flags, nodeProvidedSecrets, requestData)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string, uint64, functions.RequestFlags, string, *functions.RequestData) []byte); ok {
		r0 = rf(ctx, requestId, jobName, subscriptionOwner, subscriptionId, flags, nodeProvidedSecrets, requestData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string, uint64, functions.RequestFlags, string, *functions.RequestData) []byte); ok {
		r1 = rf(ctx, requestId, jobName, subscriptionOwner, subscriptionId, flags, nodeProvidedSecrets, requestData)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]byte)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, string, string, uint64, functions.RequestFlags, string, *functions.RequestData) []string); ok {
		r2 = rf(ctx, requestId, jobName, subscriptionOwner, subscriptionId, flags, nodeProvidedSecrets, requestData)
	} else {
		if ret.Get(2) != nil {
			r2 = ret.Get(2).([]string)
		}
	}

	if rf, ok := ret.Get(3).(func(context.Context, string, string, string, uint64, functions.RequestFlags, string, *functions.RequestData) error); ok {
		r3 = rf(ctx, requestId, jobName, subscriptionOwner, subscriptionId, flags, nodeProvidedSecrets, requestData)
	} else {
		r3 = ret.Error(3)
	}

	return r0, r1, r2, r3
}

// ExternalAdapterClient_RunComputation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RunComputation'
type ExternalAdapterClient_RunComputation_Call struct {
	*mock.Call
}

// RunComputation is a helper method to define mock.On call
//   - ctx context.Context
//   - requestId string
//   - jobName string
//   - subscriptionOwner string
//   - subscriptionId uint64
//   - flags functions.RequestFlags
//   - nodeProvidedSecrets string
//   - requestData *functions.RequestData
func (_e *ExternalAdapterClient_Expecter) RunComputation(ctx interface{}, requestId interface{}, jobName interface{}, subscriptionOwner interface{}, subscriptionId interface{}, flags interface{}, nodeProvidedSecrets interface{}, requestData interface{}) *ExternalAdapterClient_RunComputation_Call {
	return &ExternalAdapterClient_RunComputation_Call{Call: _e.mock.On("RunComputation", ctx, requestId, jobName, subscriptionOwner, subscriptionId, flags, nodeProvidedSecrets, requestData)}
}

func (_c *ExternalAdapterClient_RunComputation_Call) Run(run func(ctx context.Context, requestId string, jobName string, subscriptionOwner string, subscriptionId uint64, flags functions.RequestFlags, nodeProvidedSecrets string, requestData *functions.RequestData)) *ExternalAdapterClient_RunComputation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(string), args[4].(uint64), args[5].(functions.RequestFlags), args[6].(string), args[7].(*functions.RequestData))
	})
	return _c
}

func (_c *ExternalAdapterClient_RunComputation_Call) Return(userResult []byte, userError []byte, domains []string, err error) *ExternalAdapterClient_RunComputation_Call {
	_c.Call.Return(userResult, userError, domains, err)
	return _c
}

func (_c *ExternalAdapterClient_RunComputation_Call) RunAndReturn(run func(context.Context, string, string, string, uint64, functions.RequestFlags, string, *functions.RequestData) ([]byte, []byte, []string, error)) *ExternalAdapterClient_RunComputation_Call {
	_c.Call.Return(run)
	return _c
}

// NewExternalAdapterClient creates a new instance of ExternalAdapterClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewExternalAdapterClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *ExternalAdapterClient {
	mock := &ExternalAdapterClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
