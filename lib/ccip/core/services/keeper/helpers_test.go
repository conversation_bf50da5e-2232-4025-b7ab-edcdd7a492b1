package keeper

import (
	"context"
	"math/big"

	"github.com/ethereum/go-ethereum/core/types"
	"github.com/pkg/errors"
)

func (rs *RegistrySynchronizer) ExportedFullSync(ctx context.Context) {
	rs.fullSync(ctx)
}

func (rs *RegistrySynchronizer) ExportedProcessLogs(ctx context.Context) {
	rs.processLogs(ctx)
}

func (rw *RegistryWrapper) GetUpkeepIdFromRawRegistrationLog(rawLog types.Log) (*big.Int, error) {
	switch rw.Version {
	case RegistryVersion_1_0, RegistryVersion_1_1:
		parsedLog, err := rw.contract1_1.ParseUpkeepRegistered(rawLog)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get parse UpkeepRegistered log")
		}
		return parsedLog.Id, nil
	case RegistryVersion_1_2:
		parsedLog, err := rw.contract1_2.ParseUpkeepRegistered(rawLog)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get parse UpkeepRegistered log")
		}
		return parsedLog.Id, nil
	case RegistryVersion_1_3:
		parsedLog, err := rw.contract1_3.ParseUpkeepRegistered(rawLog)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get parse UpkeepRegistered log")
		}
		return parsedLog.Id, nil
	default:
		return nil, newUnsupportedVersionError("GetUpkeepIdFromRawRegistrationLog", rw.Version)
	}
}
