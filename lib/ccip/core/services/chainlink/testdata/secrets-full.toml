[Database]
URL = "postgresql://user:pass@localhost:5432/dbname?sslmode=disable"
BackupURL = "postgresql://user:pass@localhost:5432/backupdbname?sslmode=disable"

[Password]
Keystore = "keystore_pass"
VRF = "VRF_pass"

[WebServer]
[WebServer.LDAP]
ServerAddress = 'ldaps://127.0.0.1' 
ReadOnlyUserLogin = '<EMAIL>' 
ReadOnlyUserPass = 'password' 

[Pyroscope]
AuthToken = "pyroscope-token"

[Prometheus]
AuthToken = "prometheus-token"

[Mercury.Credentials.cred1]
URL = "https://chain1.link"
Username = "username1"
Password = "password1"

[Mercury.Credentials.cred2]
URL = "https://chain2.link"
Username = "username2"
Password = "password2"

[Mercury.Credentials.cred3]
LegacyURL = "https://chain2.old.link"
URL = "https://chain2.link"
Username = "username2"
Password = "password2"
