[Database]
URL = 'xxxxx'
BackupURL = 'xxxxx'
AllowSimplePasswords = false

[Password]
Keystore = 'xxxxx'
VRF = 'xxxxx'

[WebServer]
[WebServer.LDAP]
ServerAddress = 'xxxxx'
ReadOnlyUserLogin = 'xxxxx'
ReadOnlyUserPass = 'xxxxx'

[Pyroscope]
AuthToken = 'xxxxx'

[Prometheus]
AuthToken = 'xxxxx'

[Mercury]
[Mercury.Credentials]
[Mercury.Credentials.cred1]
URL = 'xxxxx'
Username = 'xxxxx'
Password = 'xxxxx'

[Mercury.Credentials.cred2]
URL = 'xxxxx'
Username = 'xxxxx'
Password = 'xxxxx'

[Mercury.Credentials.cred3]
LegacyURL = 'xxxxx'
URL = 'xxxxx'
Username = 'xxxxx'
Password = 'xxxxx'
