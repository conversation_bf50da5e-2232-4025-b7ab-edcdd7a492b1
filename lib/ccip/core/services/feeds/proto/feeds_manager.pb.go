// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v4.25.3
// source: pkg/noderpc/proto/feeds_manager.proto

package proto

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Defines the allowed job types
type JobType int32

const (
	JobType_JOB_TYPE_UNSPECIFIED  JobType = 0
	JobType_JOB_TYPE_FLUX_MONITOR JobType = 1
	JobType_JOB_TYPE_OCR          JobType = 2
	JobType_JOB_TYPE_OCR2         JobType = 3
)

// Enum value maps for JobType.
var (
	JobType_name = map[int32]string{
		0: "JOB_TYPE_UNSPECIFIED",
		1: "JOB_TYPE_FLUX_MONITOR",
		2: "JOB_TYPE_OCR",
		3: "JOB_TYPE_OCR2",
	}
	JobType_value = map[string]int32{
		"JOB_TYPE_UNSPECIFIED":  0,
		"JOB_TYPE_FLUX_MONITOR": 1,
		"JOB_TYPE_OCR":          2,
		"JOB_TYPE_OCR2":         3,
	}
)

func (x JobType) Enum() *JobType {
	p := new(JobType)
	*p = x
	return p
}

func (x JobType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobType) Descriptor() protoreflect.EnumDescriptor {
	return file_pkg_noderpc_proto_feeds_manager_proto_enumTypes[0].Descriptor()
}

func (JobType) Type() protoreflect.EnumType {
	return &file_pkg_noderpc_proto_feeds_manager_proto_enumTypes[0]
}

func (x JobType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobType.Descriptor instead.
func (JobType) EnumDescriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{0}
}

type ChainType int32

const (
	ChainType_CHAIN_TYPE_UNSPECIFIED ChainType = 0
	ChainType_CHAIN_TYPE_EVM         ChainType = 1
	ChainType_CHAIN_TYPE_SOLANA      ChainType = 2
	ChainType_CHAIN_TYPE_STARKNET    ChainType = 3
)

// Enum value maps for ChainType.
var (
	ChainType_name = map[int32]string{
		0: "CHAIN_TYPE_UNSPECIFIED",
		1: "CHAIN_TYPE_EVM",
		2: "CHAIN_TYPE_SOLANA",
		3: "CHAIN_TYPE_STARKNET",
	}
	ChainType_value = map[string]int32{
		"CHAIN_TYPE_UNSPECIFIED": 0,
		"CHAIN_TYPE_EVM":         1,
		"CHAIN_TYPE_SOLANA":      2,
		"CHAIN_TYPE_STARKNET":    3,
	}
)

func (x ChainType) Enum() *ChainType {
	p := new(ChainType)
	*p = x
	return p
}

func (x ChainType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChainType) Descriptor() protoreflect.EnumDescriptor {
	return file_pkg_noderpc_proto_feeds_manager_proto_enumTypes[1].Descriptor()
}

func (ChainType) Type() protoreflect.EnumType {
	return &file_pkg_noderpc_proto_feeds_manager_proto_enumTypes[1]
}

func (x ChainType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChainType.Descriptor instead.
func (ChainType) EnumDescriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{1}
}

type Chain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type ChainType `protobuf:"varint,2,opt,name=type,proto3,enum=cfm.ChainType" json:"type,omitempty"`
}

func (x *Chain) Reset() {
	*x = Chain{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Chain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chain) ProtoMessage() {}

func (x *Chain) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chain.ProtoReflect.Descriptor instead.
func (*Chain) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{0}
}

func (x *Chain) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Chain) GetType() ChainType {
	if x != nil {
		return x.Type
	}
	return ChainType_CHAIN_TYPE_UNSPECIFIED
}

// An account on a specific blockchain
type Account struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChainType ChainType `protobuf:"varint,1,opt,name=chain_type,json=chainType,proto3,enum=cfm.ChainType" json:"chain_type,omitempty"`
	ChainId   string    `protobuf:"bytes,2,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	Address   string    `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *Account) Reset() {
	*x = Account{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{1}
}

func (x *Account) GetChainType() ChainType {
	if x != nil {
		return x.ChainType
	}
	return ChainType_CHAIN_TYPE_UNSPECIFIED
}

func (x *Account) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *Account) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

// The config for Flux Monitor on a specific chain
type FluxMonitorConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *FluxMonitorConfig) Reset() {
	*x = FluxMonitorConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FluxMonitorConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FluxMonitorConfig) ProtoMessage() {}

func (x *FluxMonitorConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FluxMonitorConfig.ProtoReflect.Descriptor instead.
func (*FluxMonitorConfig) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{2}
}

func (x *FluxMonitorConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// The config for OCR1 on a specific chain
type OCR1Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled      bool                     `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	IsBootstrap  bool                     `protobuf:"varint,2,opt,name=is_bootstrap,json=isBootstrap,proto3" json:"is_bootstrap,omitempty"`
	P2PKeyBundle *OCR1Config_P2PKeyBundle `protobuf:"bytes,3,opt,name=p2p_key_bundle,json=p2pKeyBundle,proto3" json:"p2p_key_bundle,omitempty"`
	OcrKeyBundle *OCR1Config_OCRKeyBundle `protobuf:"bytes,4,opt,name=ocr_key_bundle,json=ocrKeyBundle,proto3" json:"ocr_key_bundle,omitempty"`
	Multiaddr    string                   `protobuf:"bytes,5,opt,name=multiaddr,proto3" json:"multiaddr,omitempty"`
}

func (x *OCR1Config) Reset() {
	*x = OCR1Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCR1Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCR1Config) ProtoMessage() {}

func (x *OCR1Config) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCR1Config.ProtoReflect.Descriptor instead.
func (*OCR1Config) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{3}
}

func (x *OCR1Config) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *OCR1Config) GetIsBootstrap() bool {
	if x != nil {
		return x.IsBootstrap
	}
	return false
}

func (x *OCR1Config) GetP2PKeyBundle() *OCR1Config_P2PKeyBundle {
	if x != nil {
		return x.P2PKeyBundle
	}
	return nil
}

func (x *OCR1Config) GetOcrKeyBundle() *OCR1Config_OCRKeyBundle {
	if x != nil {
		return x.OcrKeyBundle
	}
	return nil
}

func (x *OCR1Config) GetMultiaddr() string {
	if x != nil {
		return x.Multiaddr
	}
	return ""
}

// The config for OCR2 on a specific chain
type OCR2Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled          bool                     `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	IsBootstrap      bool                     `protobuf:"varint,2,opt,name=is_bootstrap,json=isBootstrap,proto3" json:"is_bootstrap,omitempty"`
	P2PKeyBundle     *OCR2Config_P2PKeyBundle `protobuf:"bytes,3,opt,name=p2p_key_bundle,json=p2pKeyBundle,proto3" json:"p2p_key_bundle,omitempty"`
	OcrKeyBundle     *OCR2Config_OCRKeyBundle `protobuf:"bytes,4,opt,name=ocr_key_bundle,json=ocrKeyBundle,proto3" json:"ocr_key_bundle,omitempty"`
	Multiaddr        string                   `protobuf:"bytes,5,opt,name=multiaddr,proto3" json:"multiaddr,omitempty"`
	Plugins          *OCR2Config_Plugins      `protobuf:"bytes,6,opt,name=plugins,proto3" json:"plugins,omitempty"`
	ForwarderAddress *string                  `protobuf:"bytes,7,opt,name=forwarder_address,json=forwarderAddress,proto3,oneof" json:"forwarder_address,omitempty"`
}

func (x *OCR2Config) Reset() {
	*x = OCR2Config{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCR2Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCR2Config) ProtoMessage() {}

func (x *OCR2Config) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCR2Config.ProtoReflect.Descriptor instead.
func (*OCR2Config) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{4}
}

func (x *OCR2Config) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *OCR2Config) GetIsBootstrap() bool {
	if x != nil {
		return x.IsBootstrap
	}
	return false
}

func (x *OCR2Config) GetP2PKeyBundle() *OCR2Config_P2PKeyBundle {
	if x != nil {
		return x.P2PKeyBundle
	}
	return nil
}

func (x *OCR2Config) GetOcrKeyBundle() *OCR2Config_OCRKeyBundle {
	if x != nil {
		return x.OcrKeyBundle
	}
	return nil
}

func (x *OCR2Config) GetMultiaddr() string {
	if x != nil {
		return x.Multiaddr
	}
	return ""
}

func (x *OCR2Config) GetPlugins() *OCR2Config_Plugins {
	if x != nil {
		return x.Plugins
	}
	return nil
}

func (x *OCR2Config) GetForwarderAddress() string {
	if x != nil && x.ForwarderAddress != nil {
		return *x.ForwarderAddress
	}
	return ""
}

type ChainConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chain             *Chain             `protobuf:"bytes,1,opt,name=chain,proto3" json:"chain,omitempty"`
	AccountAddress    string             `protobuf:"bytes,2,opt,name=account_address,json=accountAddress,proto3" json:"account_address,omitempty"`
	AdminAddress      string             `protobuf:"bytes,3,opt,name=admin_address,json=adminAddress,proto3" json:"admin_address,omitempty"`
	FluxMonitorConfig *FluxMonitorConfig `protobuf:"bytes,4,opt,name=flux_monitor_config,json=fluxMonitorConfig,proto3" json:"flux_monitor_config,omitempty"`
	Ocr1Config        *OCR1Config        `protobuf:"bytes,5,opt,name=ocr1_config,json=ocr1Config,proto3" json:"ocr1_config,omitempty"`
	Ocr2Config        *OCR2Config        `protobuf:"bytes,6,opt,name=ocr2_config,json=ocr2Config,proto3" json:"ocr2_config,omitempty"`
	// For EVM chains, we do not need this value and it is kept in the node's
	// keystore. For starknet, because the wallet address needs to be deployed
	// using this value and this pub key needs to be passed into the starknet
	// relayer, we request the node to send this directly to CLO.
	AccountAddressPublicKey *string `protobuf:"bytes,7,opt,name=account_address_public_key,json=accountAddressPublicKey,proto3,oneof" json:"account_address_public_key,omitempty"`
}

func (x *ChainConfig) Reset() {
	*x = ChainConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainConfig) ProtoMessage() {}

func (x *ChainConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChainConfig.ProtoReflect.Descriptor instead.
func (*ChainConfig) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{5}
}

func (x *ChainConfig) GetChain() *Chain {
	if x != nil {
		return x.Chain
	}
	return nil
}

func (x *ChainConfig) GetAccountAddress() string {
	if x != nil {
		return x.AccountAddress
	}
	return ""
}

func (x *ChainConfig) GetAdminAddress() string {
	if x != nil {
		return x.AdminAddress
	}
	return ""
}

func (x *ChainConfig) GetFluxMonitorConfig() *FluxMonitorConfig {
	if x != nil {
		return x.FluxMonitorConfig
	}
	return nil
}

func (x *ChainConfig) GetOcr1Config() *OCR1Config {
	if x != nil {
		return x.Ocr1Config
	}
	return nil
}

func (x *ChainConfig) GetOcr2Config() *OCR2Config {
	if x != nil {
		return x.Ocr2Config
	}
	return nil
}

func (x *ChainConfig) GetAccountAddressPublicKey() string {
	if x != nil && x.AccountAddressPublicKey != nil {
		return *x.AccountAddressPublicKey
	}
	return ""
}

type UpdateNodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobTypes           []JobType      `protobuf:"varint,1,rep,packed,name=job_types,json=jobTypes,proto3,enum=cfm.JobType" json:"job_types,omitempty"`
	ChainId            int64          `protobuf:"varint,2,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"` // To be removed when all nodes are upgraded to 1.2
	AccountAddresses   []string       `protobuf:"bytes,3,rep,name=account_addresses,json=accountAddresses,proto3" json:"account_addresses,omitempty"`
	IsBootstrapPeer    bool           `protobuf:"varint,4,opt,name=is_bootstrap_peer,json=isBootstrapPeer,proto3" json:"is_bootstrap_peer,omitempty"`
	BootstrapMultiaddr string         `protobuf:"bytes,5,opt,name=bootstrap_multiaddr,json=bootstrapMultiaddr,proto3" json:"bootstrap_multiaddr,omitempty"`
	Version            string         `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	ChainIds           []int64        `protobuf:"varint,7,rep,packed,name=chain_ids,json=chainIds,proto3" json:"chain_ids,omitempty"`
	Accounts           []*Account     `protobuf:"bytes,8,rep,name=accounts,proto3" json:"accounts,omitempty"`
	Chains             []*Chain       `protobuf:"bytes,9,rep,name=chains,proto3" json:"chains,omitempty"`
	ChainConfigs       []*ChainConfig `protobuf:"bytes,10,rep,name=chain_configs,json=chainConfigs,proto3" json:"chain_configs,omitempty"`
}

func (x *UpdateNodeRequest) Reset() {
	*x = UpdateNodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNodeRequest) ProtoMessage() {}

func (x *UpdateNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNodeRequest.ProtoReflect.Descriptor instead.
func (*UpdateNodeRequest) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateNodeRequest) GetJobTypes() []JobType {
	if x != nil {
		return x.JobTypes
	}
	return nil
}

func (x *UpdateNodeRequest) GetChainId() int64 {
	if x != nil {
		return x.ChainId
	}
	return 0
}

func (x *UpdateNodeRequest) GetAccountAddresses() []string {
	if x != nil {
		return x.AccountAddresses
	}
	return nil
}

func (x *UpdateNodeRequest) GetIsBootstrapPeer() bool {
	if x != nil {
		return x.IsBootstrapPeer
	}
	return false
}

func (x *UpdateNodeRequest) GetBootstrapMultiaddr() string {
	if x != nil {
		return x.BootstrapMultiaddr
	}
	return ""
}

func (x *UpdateNodeRequest) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *UpdateNodeRequest) GetChainIds() []int64 {
	if x != nil {
		return x.ChainIds
	}
	return nil
}

func (x *UpdateNodeRequest) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *UpdateNodeRequest) GetChains() []*Chain {
	if x != nil {
		return x.Chains
	}
	return nil
}

func (x *UpdateNodeRequest) GetChainConfigs() []*ChainConfig {
	if x != nil {
		return x.ChainConfigs
	}
	return nil
}

type UpdateNodeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateNodeResponse) Reset() {
	*x = UpdateNodeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNodeResponse) ProtoMessage() {}

func (x *UpdateNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNodeResponse.ProtoReflect.Descriptor instead.
func (*UpdateNodeResponse) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{7}
}

type ApprovedJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid    string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Version int64  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ApprovedJobRequest) Reset() {
	*x = ApprovedJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApprovedJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovedJobRequest) ProtoMessage() {}

func (x *ApprovedJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovedJobRequest.ProtoReflect.Descriptor instead.
func (*ApprovedJobRequest) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{8}
}

func (x *ApprovedJobRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ApprovedJobRequest) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

type ApprovedJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ApprovedJobResponse) Reset() {
	*x = ApprovedJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApprovedJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApprovedJobResponse) ProtoMessage() {}

func (x *ApprovedJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApprovedJobResponse.ProtoReflect.Descriptor instead.
func (*ApprovedJobResponse) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{9}
}

type HealthcheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HealthcheckRequest) Reset() {
	*x = HealthcheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthcheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthcheckRequest) ProtoMessage() {}

func (x *HealthcheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthcheckRequest.ProtoReflect.Descriptor instead.
func (*HealthcheckRequest) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{10}
}

type HealthcheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HealthcheckResponse) Reset() {
	*x = HealthcheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthcheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthcheckResponse) ProtoMessage() {}

func (x *HealthcheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthcheckResponse.ProtoReflect.Descriptor instead.
func (*HealthcheckResponse) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{11}
}

type RejectedJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid    string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Version int64  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *RejectedJobRequest) Reset() {
	*x = RejectedJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RejectedJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectedJobRequest) ProtoMessage() {}

func (x *RejectedJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectedJobRequest.ProtoReflect.Descriptor instead.
func (*RejectedJobRequest) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{12}
}

func (x *RejectedJobRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *RejectedJobRequest) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

type RejectedJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RejectedJobResponse) Reset() {
	*x = RejectedJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RejectedJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RejectedJobResponse) ProtoMessage() {}

func (x *RejectedJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RejectedJobResponse.ProtoReflect.Descriptor instead.
func (*RejectedJobResponse) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{13}
}

type CancelledJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid    string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`
	Version int64  `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *CancelledJobRequest) Reset() {
	*x = CancelledJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelledJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelledJobRequest) ProtoMessage() {}

func (x *CancelledJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelledJobRequest.ProtoReflect.Descriptor instead.
func (*CancelledJobRequest) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{14}
}

func (x *CancelledJobRequest) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *CancelledJobRequest) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

type CancelledJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CancelledJobResponse) Reset() {
	*x = CancelledJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelledJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelledJobResponse) ProtoMessage() {}

func (x *CancelledJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelledJobResponse.ProtoReflect.Descriptor instead.
func (*CancelledJobResponse) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{15}
}

type ProposeJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Spec       string   `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec,omitempty"`
	Multiaddrs []string `protobuf:"bytes,3,rep,name=multiaddrs,proto3" json:"multiaddrs,omitempty"`
	Version    int64    `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ProposeJobRequest) Reset() {
	*x = ProposeJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProposeJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProposeJobRequest) ProtoMessage() {}

func (x *ProposeJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProposeJobRequest.ProtoReflect.Descriptor instead.
func (*ProposeJobRequest) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{16}
}

func (x *ProposeJobRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ProposeJobRequest) GetSpec() string {
	if x != nil {
		return x.Spec
	}
	return ""
}

func (x *ProposeJobRequest) GetMultiaddrs() []string {
	if x != nil {
		return x.Multiaddrs
	}
	return nil
}

func (x *ProposeJobRequest) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

type ProposeJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *ProposeJobResponse) Reset() {
	*x = ProposeJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProposeJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProposeJobResponse) ProtoMessage() {}

func (x *ProposeJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProposeJobResponse.ProtoReflect.Descriptor instead.
func (*ProposeJobResponse) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{17}
}

func (x *ProposeJobResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteJobRequest) Reset() {
	*x = DeleteJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobRequest) ProtoMessage() {}

func (x *DeleteJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobRequest.ProtoReflect.Descriptor instead.
func (*DeleteJobRequest) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteJobRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteJobResponse) Reset() {
	*x = DeleteJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobResponse) ProtoMessage() {}

func (x *DeleteJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobResponse.ProtoReflect.Descriptor instead.
func (*DeleteJobResponse) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteJobResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type RevokeJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *RevokeJobRequest) Reset() {
	*x = RevokeJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RevokeJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeJobRequest) ProtoMessage() {}

func (x *RevokeJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeJobRequest.ProtoReflect.Descriptor instead.
func (*RevokeJobRequest) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{20}
}

func (x *RevokeJobRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type RevokeJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *RevokeJobResponse) Reset() {
	*x = RevokeJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RevokeJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeJobResponse) ProtoMessage() {}

func (x *RevokeJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeJobResponse.ProtoReflect.Descriptor instead.
func (*RevokeJobResponse) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{21}
}

func (x *RevokeJobResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type OCR1Config_P2PKeyBundle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PeerId    string `protobuf:"bytes,1,opt,name=peer_id,json=peerId,proto3" json:"peer_id,omitempty"`
	PublicKey string `protobuf:"bytes,2,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
}

func (x *OCR1Config_P2PKeyBundle) Reset() {
	*x = OCR1Config_P2PKeyBundle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCR1Config_P2PKeyBundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCR1Config_P2PKeyBundle) ProtoMessage() {}

func (x *OCR1Config_P2PKeyBundle) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCR1Config_P2PKeyBundle.ProtoReflect.Descriptor instead.
func (*OCR1Config_P2PKeyBundle) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{3, 0}
}

func (x *OCR1Config_P2PKeyBundle) GetPeerId() string {
	if x != nil {
		return x.PeerId
	}
	return ""
}

func (x *OCR1Config_P2PKeyBundle) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type OCR1Config_OCRKeyBundle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BundleId              string `protobuf:"bytes,1,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	ConfigPublicKey       string `protobuf:"bytes,2,opt,name=config_public_key,json=configPublicKey,proto3" json:"config_public_key,omitempty"`
	OffchainPublicKey     string `protobuf:"bytes,3,opt,name=offchain_public_key,json=offchainPublicKey,proto3" json:"offchain_public_key,omitempty"`
	OnchainSigningAddress string `protobuf:"bytes,4,opt,name=onchain_signing_address,json=onchainSigningAddress,proto3" json:"onchain_signing_address,omitempty"`
}

func (x *OCR1Config_OCRKeyBundle) Reset() {
	*x = OCR1Config_OCRKeyBundle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCR1Config_OCRKeyBundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCR1Config_OCRKeyBundle) ProtoMessage() {}

func (x *OCR1Config_OCRKeyBundle) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCR1Config_OCRKeyBundle.ProtoReflect.Descriptor instead.
func (*OCR1Config_OCRKeyBundle) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{3, 1}
}

func (x *OCR1Config_OCRKeyBundle) GetBundleId() string {
	if x != nil {
		return x.BundleId
	}
	return ""
}

func (x *OCR1Config_OCRKeyBundle) GetConfigPublicKey() string {
	if x != nil {
		return x.ConfigPublicKey
	}
	return ""
}

func (x *OCR1Config_OCRKeyBundle) GetOffchainPublicKey() string {
	if x != nil {
		return x.OffchainPublicKey
	}
	return ""
}

func (x *OCR1Config_OCRKeyBundle) GetOnchainSigningAddress() string {
	if x != nil {
		return x.OnchainSigningAddress
	}
	return ""
}

type OCR2Config_P2PKeyBundle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PeerId    string `protobuf:"bytes,1,opt,name=peer_id,json=peerId,proto3" json:"peer_id,omitempty"`
	PublicKey string `protobuf:"bytes,2,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
}

func (x *OCR2Config_P2PKeyBundle) Reset() {
	*x = OCR2Config_P2PKeyBundle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCR2Config_P2PKeyBundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCR2Config_P2PKeyBundle) ProtoMessage() {}

func (x *OCR2Config_P2PKeyBundle) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCR2Config_P2PKeyBundle.ProtoReflect.Descriptor instead.
func (*OCR2Config_P2PKeyBundle) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{4, 0}
}

func (x *OCR2Config_P2PKeyBundle) GetPeerId() string {
	if x != nil {
		return x.PeerId
	}
	return ""
}

func (x *OCR2Config_P2PKeyBundle) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

type OCR2Config_OCRKeyBundle struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BundleId              string `protobuf:"bytes,1,opt,name=bundle_id,json=bundleId,proto3" json:"bundle_id,omitempty"`
	ConfigPublicKey       string `protobuf:"bytes,2,opt,name=config_public_key,json=configPublicKey,proto3" json:"config_public_key,omitempty"`
	OffchainPublicKey     string `protobuf:"bytes,3,opt,name=offchain_public_key,json=offchainPublicKey,proto3" json:"offchain_public_key,omitempty"`
	OnchainSigningAddress string `protobuf:"bytes,4,opt,name=onchain_signing_address,json=onchainSigningAddress,proto3" json:"onchain_signing_address,omitempty"`
}

func (x *OCR2Config_OCRKeyBundle) Reset() {
	*x = OCR2Config_OCRKeyBundle{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCR2Config_OCRKeyBundle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCR2Config_OCRKeyBundle) ProtoMessage() {}

func (x *OCR2Config_OCRKeyBundle) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCR2Config_OCRKeyBundle.ProtoReflect.Descriptor instead.
func (*OCR2Config_OCRKeyBundle) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{4, 1}
}

func (x *OCR2Config_OCRKeyBundle) GetBundleId() string {
	if x != nil {
		return x.BundleId
	}
	return ""
}

func (x *OCR2Config_OCRKeyBundle) GetConfigPublicKey() string {
	if x != nil {
		return x.ConfigPublicKey
	}
	return ""
}

func (x *OCR2Config_OCRKeyBundle) GetOffchainPublicKey() string {
	if x != nil {
		return x.OffchainPublicKey
	}
	return ""
}

func (x *OCR2Config_OCRKeyBundle) GetOnchainSigningAddress() string {
	if x != nil {
		return x.OnchainSigningAddress
	}
	return ""
}

type OCR2Config_Plugins struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Commit     bool `protobuf:"varint,1,opt,name=commit,proto3" json:"commit,omitempty"`
	Execute    bool `protobuf:"varint,2,opt,name=execute,proto3" json:"execute,omitempty"`
	Median     bool `protobuf:"varint,3,opt,name=median,proto3" json:"median,omitempty"`
	Mercury    bool `protobuf:"varint,4,opt,name=mercury,proto3" json:"mercury,omitempty"`
	Rebalancer bool `protobuf:"varint,5,opt,name=rebalancer,proto3" json:"rebalancer,omitempty"`
}

func (x *OCR2Config_Plugins) Reset() {
	*x = OCR2Config_Plugins{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OCR2Config_Plugins) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OCR2Config_Plugins) ProtoMessage() {}

func (x *OCR2Config_Plugins) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OCR2Config_Plugins.ProtoReflect.Descriptor instead.
func (*OCR2Config_Plugins) Descriptor() ([]byte, []int) {
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP(), []int{4, 2}
}

func (x *OCR2Config_Plugins) GetCommit() bool {
	if x != nil {
		return x.Commit
	}
	return false
}

func (x *OCR2Config_Plugins) GetExecute() bool {
	if x != nil {
		return x.Execute
	}
	return false
}

func (x *OCR2Config_Plugins) GetMedian() bool {
	if x != nil {
		return x.Median
	}
	return false
}

func (x *OCR2Config_Plugins) GetMercury() bool {
	if x != nil {
		return x.Mercury
	}
	return false
}

func (x *OCR2Config_Plugins) GetRebalancer() bool {
	if x != nil {
		return x.Rebalancer
	}
	return false
}

var File_pkg_noderpc_proto_feeds_manager_proto protoreflect.FileDescriptor

var file_pkg_noderpc_proto_feeds_manager_proto_rawDesc = []byte{
	0x0a, 0x25, 0x70, 0x6b, 0x67, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x65, 0x65, 0x64, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x63, 0x66, 0x6d, 0x22, 0x3b, 0x0a, 0x05,
	0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x6d, 0x0a, 0x07, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x2d, 0x0a, 0x11, 0x46, 0x6c, 0x75, 0x78,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0xf9, 0x03, 0x0a, 0x0a, 0x4f, 0x43, 0x52, 0x31,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x42, 0x6f, 0x6f, 0x74, 0x73, 0x74,
	0x72, 0x61, 0x70, 0x12, 0x42, 0x0a, 0x0e, 0x70, 0x32, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x66,
	0x6d, 0x2e, 0x4f, 0x43, 0x52, 0x31, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x32, 0x50,
	0x4b, 0x65, 0x79, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x0c, 0x70, 0x32, 0x70, 0x4b, 0x65,
	0x79, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x6f, 0x63, 0x72, 0x5f, 0x6b,
	0x65, 0x79, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x4f, 0x43, 0x52, 0x31, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x4f, 0x43, 0x52, 0x4b, 0x65, 0x79, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x0c, 0x6f,
	0x63, 0x72, 0x4b, 0x65, 0x79, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d,
	0x75, 0x6c, 0x74, 0x69, 0x61, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x64, 0x64, 0x72, 0x1a, 0x46, 0x0a, 0x0c, 0x50, 0x32, 0x50,
	0x4b, 0x65, 0x79, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x65, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x65, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65,
	0x79, 0x1a, 0xbf, 0x01, 0x0a, 0x0c, 0x4f, 0x43, 0x52, 0x4b, 0x65, 0x79, 0x42, 0x75, 0x6e, 0x64,
	0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64, 0x12,
	0x2a, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x13, 0x6f,
	0x66, 0x66, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x66, 0x66, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x17, 0x6f,
	0x6e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6f, 0x6e,
	0x63, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x22, 0x84, 0x06, 0x0a, 0x0a, 0x4f, 0x43, 0x52, 0x32, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x42, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x12,
	0x42, 0x0a, 0x0e, 0x70, 0x32, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x62, 0x75, 0x6e, 0x64, 0x6c,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x4f, 0x43,
	0x52, 0x32, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x32, 0x50, 0x4b, 0x65, 0x79, 0x42,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x0c, 0x70, 0x32, 0x70, 0x4b, 0x65, 0x79, 0x42, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x6f, 0x63, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x62,
	0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x66,
	0x6d, 0x2e, 0x4f, 0x43, 0x52, 0x32, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4f, 0x43, 0x52,
	0x4b, 0x65, 0x79, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x0c, 0x6f, 0x63, 0x72, 0x4b, 0x65,
	0x79, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x75, 0x6c, 0x74, 0x69,
	0x61, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x64, 0x64, 0x72, 0x12, 0x31, 0x0a, 0x07, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x4f, 0x43, 0x52,
	0x32, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x52,
	0x07, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73, 0x12, 0x30, 0x0a, 0x11, 0x66, 0x6f, 0x72, 0x77,
	0x61, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x10, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x65, 0x72,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x88, 0x01, 0x01, 0x1a, 0x46, 0x0a, 0x0c, 0x50, 0x32,
	0x50, 0x4b, 0x65, 0x79, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x65,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x65, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b,
	0x65, 0x79, 0x1a, 0xbf, 0x01, 0x0a, 0x0c, 0x4f, 0x43, 0x52, 0x4b, 0x65, 0x79, 0x42, 0x75, 0x6e,
	0x64, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69,
	0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x2e, 0x0a, 0x13,
	0x6f, 0x66, 0x66, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x66, 0x66, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x36, 0x0a, 0x17,
	0x6f, 0x6e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x6f,
	0x6e, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x1a, 0x8d, 0x01, 0x0a, 0x07, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65,
	0x72, 0x63, 0x75, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x6d, 0x65, 0x72,
	0x63, 0x75, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x72, 0x65, 0x62, 0x61, 0x6c, 0x61,
	0x6e, 0x63, 0x65, 0x72, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x8a, 0x03, 0x0a, 0x0b, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x20, 0x0a, 0x05, 0x63, 0x68,
	0x61, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x66, 0x6d, 0x2e,
	0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x27, 0x0a, 0x0f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x46, 0x0a, 0x13, 0x66, 0x6c,
	0x75, 0x78, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x46, 0x6c,
	0x75, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x11, 0x66, 0x6c, 0x75, 0x78, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x30, 0x0a, 0x0b, 0x6f, 0x63, 0x72, 0x31, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x4f, 0x43,
	0x52, 0x31, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6f, 0x63, 0x72, 0x31, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x30, 0x0a, 0x0b, 0x6f, 0x63, 0x72, 0x32, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x66, 0x6d, 0x2e,
	0x4f, 0x43, 0x52, 0x32, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6f, 0x63, 0x72, 0x32,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x40, 0x0a, 0x1a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x17, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x4b, 0x65, 0x79, 0x88, 0x01, 0x01, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x75, 0x62,
	0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x22, 0x9f, 0x03, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x29, 0x0a,
	0x09, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x0c, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x4a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x6a, 0x6f, 0x62, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70,
	0x5f, 0x70, 0x65, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x42,
	0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x50, 0x65, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x13,
	0x62, 0x6f, 0x6f, 0x74, 0x73, 0x74, 0x72, 0x61, 0x70, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x61,
	0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62, 0x6f, 0x6f, 0x74, 0x73,
	0x74, 0x72, 0x61, 0x70, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x61, 0x64, 0x64, 0x72, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x22,
	0x0a, 0x06, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0a,
	0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x06, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x73, 0x12, 0x35, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x66, 0x6d, 0x2e,
	0x43, 0x68, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x42, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x15, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x0a, 0x12, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x15, 0x0a, 0x13, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x42, 0x0a, 0x12, 0x52, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x15, 0x0a, 0x13, 0x52,
	0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x43, 0x0a, 0x13, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x6c, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x71, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x75, 0x6c, 0x74,
	0x69, 0x61, 0x64, 0x64, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x75,
	0x6c, 0x74, 0x69, 0x61, 0x64, 0x64, 0x72, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0x24, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x4a, 0x6f, 0x62,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x22, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x23, 0x0a, 0x11,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x22, 0x0a, 0x10, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x23, 0x0a, 0x11, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4a,
	0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x2a, 0x63, 0x0a, 0x07, 0x4a, 0x6f,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x4a, 0x4f, 0x42, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x19, 0x0a, 0x15, 0x4a, 0x4f, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x4c, 0x55, 0x58,
	0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4a, 0x4f,
	0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x43, 0x52, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d,
	0x4a, 0x4f, 0x42, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x43, 0x52, 0x32, 0x10, 0x03, 0x2a,
	0x6b, 0x0a, 0x09, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16,
	0x43, 0x48, 0x41, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x48, 0x41, 0x49,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x56, 0x4d, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x43, 0x48, 0x41, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x4f, 0x4c, 0x41, 0x4e,
	0x41, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x48, 0x41, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x4b, 0x4e, 0x45, 0x54, 0x10, 0x03, 0x32, 0xd8, 0x02, 0x0a,
	0x0c, 0x46, 0x65, 0x65, 0x64, 0x73, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x40, 0x0a,
	0x0b, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x12, 0x17, 0x2e, 0x63,
	0x66, 0x6d, 0x2e, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x41, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x17,
	0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x12,
	0x17, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4a, 0x6f,
	0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x52,
	0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x43, 0x0a, 0x0c, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x4a,
	0x6f, 0x62, 0x12, 0x18, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c,
	0x65, 0x64, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63,
	0x66, 0x6d, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xc4, 0x01, 0x0a, 0x0b, 0x4e, 0x6f, 0x64, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x70, 0x6f,
	0x73, 0x65, 0x4a, 0x6f, 0x62, 0x12, 0x16, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x70,
	0x6f, 0x73, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e,
	0x63, 0x66, 0x6d, 0x2e, 0x50, 0x72, 0x6f, 0x70, 0x6f, 0x73, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4a, 0x6f, 0x62, 0x12, 0x15, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x63, 0x66, 0x6d,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4a, 0x6f, 0x62, 0x12,
	0x15, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4a, 0x6f, 0x62, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x63, 0x66, 0x6d, 0x2e, 0x52, 0x65, 0x76,
	0x6f, 0x6b, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x3d,
	0x5a, 0x3b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6d, 0x61,
	0x72, 0x74, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6b, 0x69, 0x74, 0x2f, 0x66, 0x65,
	0x65, 0x64, 0x73, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x6e, 0x6f, 0x64, 0x65, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pkg_noderpc_proto_feeds_manager_proto_rawDescOnce sync.Once
	file_pkg_noderpc_proto_feeds_manager_proto_rawDescData = file_pkg_noderpc_proto_feeds_manager_proto_rawDesc
)

func file_pkg_noderpc_proto_feeds_manager_proto_rawDescGZIP() []byte {
	file_pkg_noderpc_proto_feeds_manager_proto_rawDescOnce.Do(func() {
		file_pkg_noderpc_proto_feeds_manager_proto_rawDescData = protoimpl.X.CompressGZIP(file_pkg_noderpc_proto_feeds_manager_proto_rawDescData)
	})
	return file_pkg_noderpc_proto_feeds_manager_proto_rawDescData
}

var file_pkg_noderpc_proto_feeds_manager_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pkg_noderpc_proto_feeds_manager_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_pkg_noderpc_proto_feeds_manager_proto_goTypes = []interface{}{
	(JobType)(0),                    // 0: cfm.JobType
	(ChainType)(0),                  // 1: cfm.ChainType
	(*Chain)(nil),                   // 2: cfm.Chain
	(*Account)(nil),                 // 3: cfm.Account
	(*FluxMonitorConfig)(nil),       // 4: cfm.FluxMonitorConfig
	(*OCR1Config)(nil),              // 5: cfm.OCR1Config
	(*OCR2Config)(nil),              // 6: cfm.OCR2Config
	(*ChainConfig)(nil),             // 7: cfm.ChainConfig
	(*UpdateNodeRequest)(nil),       // 8: cfm.UpdateNodeRequest
	(*UpdateNodeResponse)(nil),      // 9: cfm.UpdateNodeResponse
	(*ApprovedJobRequest)(nil),      // 10: cfm.ApprovedJobRequest
	(*ApprovedJobResponse)(nil),     // 11: cfm.ApprovedJobResponse
	(*HealthcheckRequest)(nil),      // 12: cfm.HealthcheckRequest
	(*HealthcheckResponse)(nil),     // 13: cfm.HealthcheckResponse
	(*RejectedJobRequest)(nil),      // 14: cfm.RejectedJobRequest
	(*RejectedJobResponse)(nil),     // 15: cfm.RejectedJobResponse
	(*CancelledJobRequest)(nil),     // 16: cfm.CancelledJobRequest
	(*CancelledJobResponse)(nil),    // 17: cfm.CancelledJobResponse
	(*ProposeJobRequest)(nil),       // 18: cfm.ProposeJobRequest
	(*ProposeJobResponse)(nil),      // 19: cfm.ProposeJobResponse
	(*DeleteJobRequest)(nil),        // 20: cfm.DeleteJobRequest
	(*DeleteJobResponse)(nil),       // 21: cfm.DeleteJobResponse
	(*RevokeJobRequest)(nil),        // 22: cfm.RevokeJobRequest
	(*RevokeJobResponse)(nil),       // 23: cfm.RevokeJobResponse
	(*OCR1Config_P2PKeyBundle)(nil), // 24: cfm.OCR1Config.P2PKeyBundle
	(*OCR1Config_OCRKeyBundle)(nil), // 25: cfm.OCR1Config.OCRKeyBundle
	(*OCR2Config_P2PKeyBundle)(nil), // 26: cfm.OCR2Config.P2PKeyBundle
	(*OCR2Config_OCRKeyBundle)(nil), // 27: cfm.OCR2Config.OCRKeyBundle
	(*OCR2Config_Plugins)(nil),      // 28: cfm.OCR2Config.Plugins
}
var file_pkg_noderpc_proto_feeds_manager_proto_depIdxs = []int32{
	1,  // 0: cfm.Chain.type:type_name -> cfm.ChainType
	1,  // 1: cfm.Account.chain_type:type_name -> cfm.ChainType
	24, // 2: cfm.OCR1Config.p2p_key_bundle:type_name -> cfm.OCR1Config.P2PKeyBundle
	25, // 3: cfm.OCR1Config.ocr_key_bundle:type_name -> cfm.OCR1Config.OCRKeyBundle
	26, // 4: cfm.OCR2Config.p2p_key_bundle:type_name -> cfm.OCR2Config.P2PKeyBundle
	27, // 5: cfm.OCR2Config.ocr_key_bundle:type_name -> cfm.OCR2Config.OCRKeyBundle
	28, // 6: cfm.OCR2Config.plugins:type_name -> cfm.OCR2Config.Plugins
	2,  // 7: cfm.ChainConfig.chain:type_name -> cfm.Chain
	4,  // 8: cfm.ChainConfig.flux_monitor_config:type_name -> cfm.FluxMonitorConfig
	5,  // 9: cfm.ChainConfig.ocr1_config:type_name -> cfm.OCR1Config
	6,  // 10: cfm.ChainConfig.ocr2_config:type_name -> cfm.OCR2Config
	0,  // 11: cfm.UpdateNodeRequest.job_types:type_name -> cfm.JobType
	3,  // 12: cfm.UpdateNodeRequest.accounts:type_name -> cfm.Account
	2,  // 13: cfm.UpdateNodeRequest.chains:type_name -> cfm.Chain
	7,  // 14: cfm.UpdateNodeRequest.chain_configs:type_name -> cfm.ChainConfig
	10, // 15: cfm.FeedsManager.ApprovedJob:input_type -> cfm.ApprovedJobRequest
	12, // 16: cfm.FeedsManager.Healthcheck:input_type -> cfm.HealthcheckRequest
	8,  // 17: cfm.FeedsManager.UpdateNode:input_type -> cfm.UpdateNodeRequest
	14, // 18: cfm.FeedsManager.RejectedJob:input_type -> cfm.RejectedJobRequest
	16, // 19: cfm.FeedsManager.CancelledJob:input_type -> cfm.CancelledJobRequest
	18, // 20: cfm.NodeService.ProposeJob:input_type -> cfm.ProposeJobRequest
	20, // 21: cfm.NodeService.DeleteJob:input_type -> cfm.DeleteJobRequest
	22, // 22: cfm.NodeService.RevokeJob:input_type -> cfm.RevokeJobRequest
	11, // 23: cfm.FeedsManager.ApprovedJob:output_type -> cfm.ApprovedJobResponse
	13, // 24: cfm.FeedsManager.Healthcheck:output_type -> cfm.HealthcheckResponse
	9,  // 25: cfm.FeedsManager.UpdateNode:output_type -> cfm.UpdateNodeResponse
	15, // 26: cfm.FeedsManager.RejectedJob:output_type -> cfm.RejectedJobResponse
	17, // 27: cfm.FeedsManager.CancelledJob:output_type -> cfm.CancelledJobResponse
	19, // 28: cfm.NodeService.ProposeJob:output_type -> cfm.ProposeJobResponse
	21, // 29: cfm.NodeService.DeleteJob:output_type -> cfm.DeleteJobResponse
	23, // 30: cfm.NodeService.RevokeJob:output_type -> cfm.RevokeJobResponse
	23, // [23:31] is the sub-list for method output_type
	15, // [15:23] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_pkg_noderpc_proto_feeds_manager_proto_init() }
func file_pkg_noderpc_proto_feeds_manager_proto_init() {
	if File_pkg_noderpc_proto_feeds_manager_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Chain); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Account); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FluxMonitorConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCR1Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCR2Config); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateNodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateNodeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApprovedJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApprovedJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthcheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthcheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RejectedJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RejectedJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelledJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelledJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProposeJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProposeJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RevokeJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RevokeJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCR1Config_P2PKeyBundle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCR1Config_OCRKeyBundle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCR2Config_P2PKeyBundle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCR2Config_OCRKeyBundle); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OCR2Config_Plugins); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_pkg_noderpc_proto_feeds_manager_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pkg_noderpc_proto_feeds_manager_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_pkg_noderpc_proto_feeds_manager_proto_goTypes,
		DependencyIndexes: file_pkg_noderpc_proto_feeds_manager_proto_depIdxs,
		EnumInfos:         file_pkg_noderpc_proto_feeds_manager_proto_enumTypes,
		MessageInfos:      file_pkg_noderpc_proto_feeds_manager_proto_msgTypes,
	}.Build()
	File_pkg_noderpc_proto_feeds_manager_proto = out.File
	file_pkg_noderpc_proto_feeds_manager_proto_rawDesc = nil
	file_pkg_noderpc_proto_feeds_manager_proto_goTypes = nil
	file_pkg_noderpc_proto_feeds_manager_proto_depIdxs = nil
}
