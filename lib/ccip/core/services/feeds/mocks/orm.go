// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	feeds "github.com/smartcontractkit/chainlink/v2/core/services/feeds"
	crypto "github.com/smartcontractkit/chainlink/v2/core/utils/crypto"

	mock "github.com/stretchr/testify/mock"

	sqlutil "github.com/smartcontractkit/chainlink-common/pkg/sqlutil"

	uuid "github.com/google/uuid"
)

// ORM is an autogenerated mock type for the ORM type
type ORM struct {
	mock.Mock
}

type ORM_Expecter struct {
	mock *mock.Mock
}

func (_m *ORM) EXPECT() *ORM_Expecter {
	return &ORM_Expecter{mock: &_m.Mock}
}

// ApproveSpec provides a mock function with given fields: ctx, id, externalJobID
func (_m *ORM) ApproveSpec(ctx context.Context, id int64, externalJobID uuid.UUID) error {
	ret := _m.Called(ctx, id, externalJobID)

	if len(ret) == 0 {
		panic("no return value specified for ApproveSpec")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, uuid.UUID) error); ok {
		r0 = rf(ctx, id, externalJobID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_ApproveSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ApproveSpec'
type ORM_ApproveSpec_Call struct {
	*mock.Call
}

// ApproveSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
//   - externalJobID uuid.UUID
func (_e *ORM_Expecter) ApproveSpec(ctx interface{}, id interface{}, externalJobID interface{}) *ORM_ApproveSpec_Call {
	return &ORM_ApproveSpec_Call{Call: _e.mock.On("ApproveSpec", ctx, id, externalJobID)}
}

func (_c *ORM_ApproveSpec_Call) Run(run func(ctx context.Context, id int64, externalJobID uuid.UUID)) *ORM_ApproveSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *ORM_ApproveSpec_Call) Return(_a0 error) *ORM_ApproveSpec_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_ApproveSpec_Call) RunAndReturn(run func(context.Context, int64, uuid.UUID) error) *ORM_ApproveSpec_Call {
	_c.Call.Return(run)
	return _c
}

// CancelSpec provides a mock function with given fields: ctx, id
func (_m *ORM) CancelSpec(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for CancelSpec")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_CancelSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelSpec'
type ORM_CancelSpec_Call struct {
	*mock.Call
}

// CancelSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) CancelSpec(ctx interface{}, id interface{}) *ORM_CancelSpec_Call {
	return &ORM_CancelSpec_Call{Call: _e.mock.On("CancelSpec", ctx, id)}
}

func (_c *ORM_CancelSpec_Call) Run(run func(ctx context.Context, id int64)) *ORM_CancelSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_CancelSpec_Call) Return(_a0 error) *ORM_CancelSpec_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_CancelSpec_Call) RunAndReturn(run func(context.Context, int64) error) *ORM_CancelSpec_Call {
	_c.Call.Return(run)
	return _c
}

// CountJobProposals provides a mock function with given fields: ctx
func (_m *ORM) CountJobProposals(ctx context.Context) (int64, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CountJobProposals")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_CountJobProposals_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountJobProposals'
type ORM_CountJobProposals_Call struct {
	*mock.Call
}

// CountJobProposals is a helper method to define mock.On call
//   - ctx context.Context
func (_e *ORM_Expecter) CountJobProposals(ctx interface{}) *ORM_CountJobProposals_Call {
	return &ORM_CountJobProposals_Call{Call: _e.mock.On("CountJobProposals", ctx)}
}

func (_c *ORM_CountJobProposals_Call) Run(run func(ctx context.Context)) *ORM_CountJobProposals_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *ORM_CountJobProposals_Call) Return(_a0 int64, _a1 error) *ORM_CountJobProposals_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_CountJobProposals_Call) RunAndReturn(run func(context.Context) (int64, error)) *ORM_CountJobProposals_Call {
	_c.Call.Return(run)
	return _c
}

// CountJobProposalsByStatus provides a mock function with given fields: ctx
func (_m *ORM) CountJobProposalsByStatus(ctx context.Context) (*feeds.JobProposalCounts, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CountJobProposalsByStatus")
	}

	var r0 *feeds.JobProposalCounts
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*feeds.JobProposalCounts, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *feeds.JobProposalCounts); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.JobProposalCounts)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_CountJobProposalsByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountJobProposalsByStatus'
type ORM_CountJobProposalsByStatus_Call struct {
	*mock.Call
}

// CountJobProposalsByStatus is a helper method to define mock.On call
//   - ctx context.Context
func (_e *ORM_Expecter) CountJobProposalsByStatus(ctx interface{}) *ORM_CountJobProposalsByStatus_Call {
	return &ORM_CountJobProposalsByStatus_Call{Call: _e.mock.On("CountJobProposalsByStatus", ctx)}
}

func (_c *ORM_CountJobProposalsByStatus_Call) Run(run func(ctx context.Context)) *ORM_CountJobProposalsByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *ORM_CountJobProposalsByStatus_Call) Return(counts *feeds.JobProposalCounts, err error) *ORM_CountJobProposalsByStatus_Call {
	_c.Call.Return(counts, err)
	return _c
}

func (_c *ORM_CountJobProposalsByStatus_Call) RunAndReturn(run func(context.Context) (*feeds.JobProposalCounts, error)) *ORM_CountJobProposalsByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// CountManagers provides a mock function with given fields: ctx
func (_m *ORM) CountManagers(ctx context.Context) (int64, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CountManagers")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (int64, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) int64); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_CountManagers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountManagers'
type ORM_CountManagers_Call struct {
	*mock.Call
}

// CountManagers is a helper method to define mock.On call
//   - ctx context.Context
func (_e *ORM_Expecter) CountManagers(ctx interface{}) *ORM_CountManagers_Call {
	return &ORM_CountManagers_Call{Call: _e.mock.On("CountManagers", ctx)}
}

func (_c *ORM_CountManagers_Call) Run(run func(ctx context.Context)) *ORM_CountManagers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *ORM_CountManagers_Call) Return(_a0 int64, _a1 error) *ORM_CountManagers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_CountManagers_Call) RunAndReturn(run func(context.Context) (int64, error)) *ORM_CountManagers_Call {
	_c.Call.Return(run)
	return _c
}

// CreateBatchChainConfig provides a mock function with given fields: ctx, cfgs
func (_m *ORM) CreateBatchChainConfig(ctx context.Context, cfgs []feeds.ChainConfig) ([]int64, error) {
	ret := _m.Called(ctx, cfgs)

	if len(ret) == 0 {
		panic("no return value specified for CreateBatchChainConfig")
	}

	var r0 []int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []feeds.ChainConfig) ([]int64, error)); ok {
		return rf(ctx, cfgs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []feeds.ChainConfig) []int64); ok {
		r0 = rf(ctx, cfgs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []feeds.ChainConfig) error); ok {
		r1 = rf(ctx, cfgs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_CreateBatchChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateBatchChainConfig'
type ORM_CreateBatchChainConfig_Call struct {
	*mock.Call
}

// CreateBatchChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - cfgs []feeds.ChainConfig
func (_e *ORM_Expecter) CreateBatchChainConfig(ctx interface{}, cfgs interface{}) *ORM_CreateBatchChainConfig_Call {
	return &ORM_CreateBatchChainConfig_Call{Call: _e.mock.On("CreateBatchChainConfig", ctx, cfgs)}
}

func (_c *ORM_CreateBatchChainConfig_Call) Run(run func(ctx context.Context, cfgs []feeds.ChainConfig)) *ORM_CreateBatchChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]feeds.ChainConfig))
	})
	return _c
}

func (_c *ORM_CreateBatchChainConfig_Call) Return(_a0 []int64, _a1 error) *ORM_CreateBatchChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_CreateBatchChainConfig_Call) RunAndReturn(run func(context.Context, []feeds.ChainConfig) ([]int64, error)) *ORM_CreateBatchChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// CreateChainConfig provides a mock function with given fields: ctx, cfg
func (_m *ORM) CreateChainConfig(ctx context.Context, cfg feeds.ChainConfig) (int64, error) {
	ret := _m.Called(ctx, cfg)

	if len(ret) == 0 {
		panic("no return value specified for CreateChainConfig")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, feeds.ChainConfig) (int64, error)); ok {
		return rf(ctx, cfg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, feeds.ChainConfig) int64); ok {
		r0 = rf(ctx, cfg)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, feeds.ChainConfig) error); ok {
		r1 = rf(ctx, cfg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_CreateChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateChainConfig'
type ORM_CreateChainConfig_Call struct {
	*mock.Call
}

// CreateChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - cfg feeds.ChainConfig
func (_e *ORM_Expecter) CreateChainConfig(ctx interface{}, cfg interface{}) *ORM_CreateChainConfig_Call {
	return &ORM_CreateChainConfig_Call{Call: _e.mock.On("CreateChainConfig", ctx, cfg)}
}

func (_c *ORM_CreateChainConfig_Call) Run(run func(ctx context.Context, cfg feeds.ChainConfig)) *ORM_CreateChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(feeds.ChainConfig))
	})
	return _c
}

func (_c *ORM_CreateChainConfig_Call) Return(_a0 int64, _a1 error) *ORM_CreateChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_CreateChainConfig_Call) RunAndReturn(run func(context.Context, feeds.ChainConfig) (int64, error)) *ORM_CreateChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// CreateJobProposal provides a mock function with given fields: ctx, jp
func (_m *ORM) CreateJobProposal(ctx context.Context, jp *feeds.JobProposal) (int64, error) {
	ret := _m.Called(ctx, jp)

	if len(ret) == 0 {
		panic("no return value specified for CreateJobProposal")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.JobProposal) (int64, error)); ok {
		return rf(ctx, jp)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.JobProposal) int64); ok {
		r0 = rf(ctx, jp)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *feeds.JobProposal) error); ok {
		r1 = rf(ctx, jp)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_CreateJobProposal_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateJobProposal'
type ORM_CreateJobProposal_Call struct {
	*mock.Call
}

// CreateJobProposal is a helper method to define mock.On call
//   - ctx context.Context
//   - jp *feeds.JobProposal
func (_e *ORM_Expecter) CreateJobProposal(ctx interface{}, jp interface{}) *ORM_CreateJobProposal_Call {
	return &ORM_CreateJobProposal_Call{Call: _e.mock.On("CreateJobProposal", ctx, jp)}
}

func (_c *ORM_CreateJobProposal_Call) Run(run func(ctx context.Context, jp *feeds.JobProposal)) *ORM_CreateJobProposal_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*feeds.JobProposal))
	})
	return _c
}

func (_c *ORM_CreateJobProposal_Call) Return(_a0 int64, _a1 error) *ORM_CreateJobProposal_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_CreateJobProposal_Call) RunAndReturn(run func(context.Context, *feeds.JobProposal) (int64, error)) *ORM_CreateJobProposal_Call {
	_c.Call.Return(run)
	return _c
}

// CreateManager provides a mock function with given fields: ctx, ms
func (_m *ORM) CreateManager(ctx context.Context, ms *feeds.FeedsManager) (int64, error) {
	ret := _m.Called(ctx, ms)

	if len(ret) == 0 {
		panic("no return value specified for CreateManager")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.FeedsManager) (int64, error)); ok {
		return rf(ctx, ms)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.FeedsManager) int64); ok {
		r0 = rf(ctx, ms)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *feeds.FeedsManager) error); ok {
		r1 = rf(ctx, ms)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_CreateManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateManager'
type ORM_CreateManager_Call struct {
	*mock.Call
}

// CreateManager is a helper method to define mock.On call
//   - ctx context.Context
//   - ms *feeds.FeedsManager
func (_e *ORM_Expecter) CreateManager(ctx interface{}, ms interface{}) *ORM_CreateManager_Call {
	return &ORM_CreateManager_Call{Call: _e.mock.On("CreateManager", ctx, ms)}
}

func (_c *ORM_CreateManager_Call) Run(run func(ctx context.Context, ms *feeds.FeedsManager)) *ORM_CreateManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*feeds.FeedsManager))
	})
	return _c
}

func (_c *ORM_CreateManager_Call) Return(_a0 int64, _a1 error) *ORM_CreateManager_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_CreateManager_Call) RunAndReturn(run func(context.Context, *feeds.FeedsManager) (int64, error)) *ORM_CreateManager_Call {
	_c.Call.Return(run)
	return _c
}

// CreateSpec provides a mock function with given fields: ctx, spec
func (_m *ORM) CreateSpec(ctx context.Context, spec feeds.JobProposalSpec) (int64, error) {
	ret := _m.Called(ctx, spec)

	if len(ret) == 0 {
		panic("no return value specified for CreateSpec")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, feeds.JobProposalSpec) (int64, error)); ok {
		return rf(ctx, spec)
	}
	if rf, ok := ret.Get(0).(func(context.Context, feeds.JobProposalSpec) int64); ok {
		r0 = rf(ctx, spec)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, feeds.JobProposalSpec) error); ok {
		r1 = rf(ctx, spec)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_CreateSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSpec'
type ORM_CreateSpec_Call struct {
	*mock.Call
}

// CreateSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - spec feeds.JobProposalSpec
func (_e *ORM_Expecter) CreateSpec(ctx interface{}, spec interface{}) *ORM_CreateSpec_Call {
	return &ORM_CreateSpec_Call{Call: _e.mock.On("CreateSpec", ctx, spec)}
}

func (_c *ORM_CreateSpec_Call) Run(run func(ctx context.Context, spec feeds.JobProposalSpec)) *ORM_CreateSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(feeds.JobProposalSpec))
	})
	return _c
}

func (_c *ORM_CreateSpec_Call) Return(_a0 int64, _a1 error) *ORM_CreateSpec_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_CreateSpec_Call) RunAndReturn(run func(context.Context, feeds.JobProposalSpec) (int64, error)) *ORM_CreateSpec_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteChainConfig provides a mock function with given fields: ctx, id
func (_m *ORM) DeleteChainConfig(ctx context.Context, id int64) (int64, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteChainConfig")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (int64, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) int64); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_DeleteChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteChainConfig'
type ORM_DeleteChainConfig_Call struct {
	*mock.Call
}

// DeleteChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) DeleteChainConfig(ctx interface{}, id interface{}) *ORM_DeleteChainConfig_Call {
	return &ORM_DeleteChainConfig_Call{Call: _e.mock.On("DeleteChainConfig", ctx, id)}
}

func (_c *ORM_DeleteChainConfig_Call) Run(run func(ctx context.Context, id int64)) *ORM_DeleteChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_DeleteChainConfig_Call) Return(_a0 int64, _a1 error) *ORM_DeleteChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_DeleteChainConfig_Call) RunAndReturn(run func(context.Context, int64) (int64, error)) *ORM_DeleteChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteProposal provides a mock function with given fields: ctx, id
func (_m *ORM) DeleteProposal(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteProposal")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_DeleteProposal_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteProposal'
type ORM_DeleteProposal_Call struct {
	*mock.Call
}

// DeleteProposal is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) DeleteProposal(ctx interface{}, id interface{}) *ORM_DeleteProposal_Call {
	return &ORM_DeleteProposal_Call{Call: _e.mock.On("DeleteProposal", ctx, id)}
}

func (_c *ORM_DeleteProposal_Call) Run(run func(ctx context.Context, id int64)) *ORM_DeleteProposal_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_DeleteProposal_Call) Return(_a0 error) *ORM_DeleteProposal_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_DeleteProposal_Call) RunAndReturn(run func(context.Context, int64) error) *ORM_DeleteProposal_Call {
	_c.Call.Return(run)
	return _c
}

// ExistsSpecByJobProposalIDAndVersion provides a mock function with given fields: ctx, jpID, version
func (_m *ORM) ExistsSpecByJobProposalIDAndVersion(ctx context.Context, jpID int64, version int32) (bool, error) {
	ret := _m.Called(ctx, jpID, version)

	if len(ret) == 0 {
		panic("no return value specified for ExistsSpecByJobProposalIDAndVersion")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int32) (bool, error)); ok {
		return rf(ctx, jpID, version)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int32) bool); ok {
		r0 = rf(ctx, jpID, version)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int32) error); ok {
		r1 = rf(ctx, jpID, version)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_ExistsSpecByJobProposalIDAndVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExistsSpecByJobProposalIDAndVersion'
type ORM_ExistsSpecByJobProposalIDAndVersion_Call struct {
	*mock.Call
}

// ExistsSpecByJobProposalIDAndVersion is a helper method to define mock.On call
//   - ctx context.Context
//   - jpID int64
//   - version int32
func (_e *ORM_Expecter) ExistsSpecByJobProposalIDAndVersion(ctx interface{}, jpID interface{}, version interface{}) *ORM_ExistsSpecByJobProposalIDAndVersion_Call {
	return &ORM_ExistsSpecByJobProposalIDAndVersion_Call{Call: _e.mock.On("ExistsSpecByJobProposalIDAndVersion", ctx, jpID, version)}
}

func (_c *ORM_ExistsSpecByJobProposalIDAndVersion_Call) Run(run func(ctx context.Context, jpID int64, version int32)) *ORM_ExistsSpecByJobProposalIDAndVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int32))
	})
	return _c
}

func (_c *ORM_ExistsSpecByJobProposalIDAndVersion_Call) Return(exists bool, err error) *ORM_ExistsSpecByJobProposalIDAndVersion_Call {
	_c.Call.Return(exists, err)
	return _c
}

func (_c *ORM_ExistsSpecByJobProposalIDAndVersion_Call) RunAndReturn(run func(context.Context, int64, int32) (bool, error)) *ORM_ExistsSpecByJobProposalIDAndVersion_Call {
	_c.Call.Return(run)
	return _c
}

// GetApprovedSpec provides a mock function with given fields: ctx, jpID
func (_m *ORM) GetApprovedSpec(ctx context.Context, jpID int64) (*feeds.JobProposalSpec, error) {
	ret := _m.Called(ctx, jpID)

	if len(ret) == 0 {
		panic("no return value specified for GetApprovedSpec")
	}

	var r0 *feeds.JobProposalSpec
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.JobProposalSpec, error)); ok {
		return rf(ctx, jpID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.JobProposalSpec); ok {
		r0 = rf(ctx, jpID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.JobProposalSpec)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, jpID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetApprovedSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetApprovedSpec'
type ORM_GetApprovedSpec_Call struct {
	*mock.Call
}

// GetApprovedSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - jpID int64
func (_e *ORM_Expecter) GetApprovedSpec(ctx interface{}, jpID interface{}) *ORM_GetApprovedSpec_Call {
	return &ORM_GetApprovedSpec_Call{Call: _e.mock.On("GetApprovedSpec", ctx, jpID)}
}

func (_c *ORM_GetApprovedSpec_Call) Run(run func(ctx context.Context, jpID int64)) *ORM_GetApprovedSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_GetApprovedSpec_Call) Return(_a0 *feeds.JobProposalSpec, _a1 error) *ORM_GetApprovedSpec_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetApprovedSpec_Call) RunAndReturn(run func(context.Context, int64) (*feeds.JobProposalSpec, error)) *ORM_GetApprovedSpec_Call {
	_c.Call.Return(run)
	return _c
}

// GetChainConfig provides a mock function with given fields: ctx, id
func (_m *ORM) GetChainConfig(ctx context.Context, id int64) (*feeds.ChainConfig, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetChainConfig")
	}

	var r0 *feeds.ChainConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.ChainConfig, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.ChainConfig); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.ChainConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChainConfig'
type ORM_GetChainConfig_Call struct {
	*mock.Call
}

// GetChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) GetChainConfig(ctx interface{}, id interface{}) *ORM_GetChainConfig_Call {
	return &ORM_GetChainConfig_Call{Call: _e.mock.On("GetChainConfig", ctx, id)}
}

func (_c *ORM_GetChainConfig_Call) Run(run func(ctx context.Context, id int64)) *ORM_GetChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_GetChainConfig_Call) Return(_a0 *feeds.ChainConfig, _a1 error) *ORM_GetChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetChainConfig_Call) RunAndReturn(run func(context.Context, int64) (*feeds.ChainConfig, error)) *ORM_GetChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetJobProposal provides a mock function with given fields: ctx, id
func (_m *ORM) GetJobProposal(ctx context.Context, id int64) (*feeds.JobProposal, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetJobProposal")
	}

	var r0 *feeds.JobProposal
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.JobProposal, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.JobProposal); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.JobProposal)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetJobProposal_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetJobProposal'
type ORM_GetJobProposal_Call struct {
	*mock.Call
}

// GetJobProposal is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) GetJobProposal(ctx interface{}, id interface{}) *ORM_GetJobProposal_Call {
	return &ORM_GetJobProposal_Call{Call: _e.mock.On("GetJobProposal", ctx, id)}
}

func (_c *ORM_GetJobProposal_Call) Run(run func(ctx context.Context, id int64)) *ORM_GetJobProposal_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_GetJobProposal_Call) Return(_a0 *feeds.JobProposal, _a1 error) *ORM_GetJobProposal_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetJobProposal_Call) RunAndReturn(run func(context.Context, int64) (*feeds.JobProposal, error)) *ORM_GetJobProposal_Call {
	_c.Call.Return(run)
	return _c
}

// GetJobProposalByRemoteUUID provides a mock function with given fields: ctx, _a1
func (_m *ORM) GetJobProposalByRemoteUUID(ctx context.Context, _a1 uuid.UUID) (*feeds.JobProposal, error) {
	ret := _m.Called(ctx, _a1)

	if len(ret) == 0 {
		panic("no return value specified for GetJobProposalByRemoteUUID")
	}

	var r0 *feeds.JobProposal
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*feeds.JobProposal, error)); ok {
		return rf(ctx, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *feeds.JobProposal); ok {
		r0 = rf(ctx, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.JobProposal)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetJobProposalByRemoteUUID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetJobProposalByRemoteUUID'
type ORM_GetJobProposalByRemoteUUID_Call struct {
	*mock.Call
}

// GetJobProposalByRemoteUUID is a helper method to define mock.On call
//   - ctx context.Context
//   - _a1 uuid.UUID
func (_e *ORM_Expecter) GetJobProposalByRemoteUUID(ctx interface{}, _a1 interface{}) *ORM_GetJobProposalByRemoteUUID_Call {
	return &ORM_GetJobProposalByRemoteUUID_Call{Call: _e.mock.On("GetJobProposalByRemoteUUID", ctx, _a1)}
}

func (_c *ORM_GetJobProposalByRemoteUUID_Call) Run(run func(ctx context.Context, _a1 uuid.UUID)) *ORM_GetJobProposalByRemoteUUID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *ORM_GetJobProposalByRemoteUUID_Call) Return(_a0 *feeds.JobProposal, _a1 error) *ORM_GetJobProposalByRemoteUUID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetJobProposalByRemoteUUID_Call) RunAndReturn(run func(context.Context, uuid.UUID) (*feeds.JobProposal, error)) *ORM_GetJobProposalByRemoteUUID_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestSpec provides a mock function with given fields: ctx, jpID
func (_m *ORM) GetLatestSpec(ctx context.Context, jpID int64) (*feeds.JobProposalSpec, error) {
	ret := _m.Called(ctx, jpID)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestSpec")
	}

	var r0 *feeds.JobProposalSpec
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.JobProposalSpec, error)); ok {
		return rf(ctx, jpID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.JobProposalSpec); ok {
		r0 = rf(ctx, jpID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.JobProposalSpec)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, jpID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetLatestSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestSpec'
type ORM_GetLatestSpec_Call struct {
	*mock.Call
}

// GetLatestSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - jpID int64
func (_e *ORM_Expecter) GetLatestSpec(ctx interface{}, jpID interface{}) *ORM_GetLatestSpec_Call {
	return &ORM_GetLatestSpec_Call{Call: _e.mock.On("GetLatestSpec", ctx, jpID)}
}

func (_c *ORM_GetLatestSpec_Call) Run(run func(ctx context.Context, jpID int64)) *ORM_GetLatestSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_GetLatestSpec_Call) Return(_a0 *feeds.JobProposalSpec, _a1 error) *ORM_GetLatestSpec_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetLatestSpec_Call) RunAndReturn(run func(context.Context, int64) (*feeds.JobProposalSpec, error)) *ORM_GetLatestSpec_Call {
	_c.Call.Return(run)
	return _c
}

// GetManager provides a mock function with given fields: ctx, id
func (_m *ORM) GetManager(ctx context.Context, id int64) (*feeds.FeedsManager, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetManager")
	}

	var r0 *feeds.FeedsManager
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.FeedsManager, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.FeedsManager); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.FeedsManager)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetManager'
type ORM_GetManager_Call struct {
	*mock.Call
}

// GetManager is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) GetManager(ctx interface{}, id interface{}) *ORM_GetManager_Call {
	return &ORM_GetManager_Call{Call: _e.mock.On("GetManager", ctx, id)}
}

func (_c *ORM_GetManager_Call) Run(run func(ctx context.Context, id int64)) *ORM_GetManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_GetManager_Call) Return(_a0 *feeds.FeedsManager, _a1 error) *ORM_GetManager_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetManager_Call) RunAndReturn(run func(context.Context, int64) (*feeds.FeedsManager, error)) *ORM_GetManager_Call {
	_c.Call.Return(run)
	return _c
}

// GetSpec provides a mock function with given fields: ctx, id
func (_m *ORM) GetSpec(ctx context.Context, id int64) (*feeds.JobProposalSpec, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetSpec")
	}

	var r0 *feeds.JobProposalSpec
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.JobProposalSpec, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.JobProposalSpec); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.JobProposalSpec)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSpec'
type ORM_GetSpec_Call struct {
	*mock.Call
}

// GetSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) GetSpec(ctx interface{}, id interface{}) *ORM_GetSpec_Call {
	return &ORM_GetSpec_Call{Call: _e.mock.On("GetSpec", ctx, id)}
}

func (_c *ORM_GetSpec_Call) Run(run func(ctx context.Context, id int64)) *ORM_GetSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_GetSpec_Call) Return(_a0 *feeds.JobProposalSpec, _a1 error) *ORM_GetSpec_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetSpec_Call) RunAndReturn(run func(context.Context, int64) (*feeds.JobProposalSpec, error)) *ORM_GetSpec_Call {
	_c.Call.Return(run)
	return _c
}

// IsJobManaged provides a mock function with given fields: ctx, jobID
func (_m *ORM) IsJobManaged(ctx context.Context, jobID int64) (bool, error) {
	ret := _m.Called(ctx, jobID)

	if len(ret) == 0 {
		panic("no return value specified for IsJobManaged")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (bool, error)); ok {
		return rf(ctx, jobID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) bool); ok {
		r0 = rf(ctx, jobID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, jobID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_IsJobManaged_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsJobManaged'
type ORM_IsJobManaged_Call struct {
	*mock.Call
}

// IsJobManaged is a helper method to define mock.On call
//   - ctx context.Context
//   - jobID int64
func (_e *ORM_Expecter) IsJobManaged(ctx interface{}, jobID interface{}) *ORM_IsJobManaged_Call {
	return &ORM_IsJobManaged_Call{Call: _e.mock.On("IsJobManaged", ctx, jobID)}
}

func (_c *ORM_IsJobManaged_Call) Run(run func(ctx context.Context, jobID int64)) *ORM_IsJobManaged_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_IsJobManaged_Call) Return(_a0 bool, _a1 error) *ORM_IsJobManaged_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_IsJobManaged_Call) RunAndReturn(run func(context.Context, int64) (bool, error)) *ORM_IsJobManaged_Call {
	_c.Call.Return(run)
	return _c
}

// ListChainConfigsByManagerIDs provides a mock function with given fields: ctx, mgrIDs
func (_m *ORM) ListChainConfigsByManagerIDs(ctx context.Context, mgrIDs []int64) ([]feeds.ChainConfig, error) {
	ret := _m.Called(ctx, mgrIDs)

	if len(ret) == 0 {
		panic("no return value specified for ListChainConfigsByManagerIDs")
	}

	var r0 []feeds.ChainConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]feeds.ChainConfig, error)); ok {
		return rf(ctx, mgrIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []feeds.ChainConfig); ok {
		r0 = rf(ctx, mgrIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.ChainConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, mgrIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_ListChainConfigsByManagerIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListChainConfigsByManagerIDs'
type ORM_ListChainConfigsByManagerIDs_Call struct {
	*mock.Call
}

// ListChainConfigsByManagerIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - mgrIDs []int64
func (_e *ORM_Expecter) ListChainConfigsByManagerIDs(ctx interface{}, mgrIDs interface{}) *ORM_ListChainConfigsByManagerIDs_Call {
	return &ORM_ListChainConfigsByManagerIDs_Call{Call: _e.mock.On("ListChainConfigsByManagerIDs", ctx, mgrIDs)}
}

func (_c *ORM_ListChainConfigsByManagerIDs_Call) Run(run func(ctx context.Context, mgrIDs []int64)) *ORM_ListChainConfigsByManagerIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *ORM_ListChainConfigsByManagerIDs_Call) Return(_a0 []feeds.ChainConfig, _a1 error) *ORM_ListChainConfigsByManagerIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_ListChainConfigsByManagerIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]feeds.ChainConfig, error)) *ORM_ListChainConfigsByManagerIDs_Call {
	_c.Call.Return(run)
	return _c
}

// ListJobProposalsByManagersIDs provides a mock function with given fields: ctx, ids
func (_m *ORM) ListJobProposalsByManagersIDs(ctx context.Context, ids []int64) ([]feeds.JobProposal, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for ListJobProposalsByManagersIDs")
	}

	var r0 []feeds.JobProposal
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]feeds.JobProposal, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []feeds.JobProposal); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.JobProposal)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_ListJobProposalsByManagersIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListJobProposalsByManagersIDs'
type ORM_ListJobProposalsByManagersIDs_Call struct {
	*mock.Call
}

// ListJobProposalsByManagersIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *ORM_Expecter) ListJobProposalsByManagersIDs(ctx interface{}, ids interface{}) *ORM_ListJobProposalsByManagersIDs_Call {
	return &ORM_ListJobProposalsByManagersIDs_Call{Call: _e.mock.On("ListJobProposalsByManagersIDs", ctx, ids)}
}

func (_c *ORM_ListJobProposalsByManagersIDs_Call) Run(run func(ctx context.Context, ids []int64)) *ORM_ListJobProposalsByManagersIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *ORM_ListJobProposalsByManagersIDs_Call) Return(_a0 []feeds.JobProposal, _a1 error) *ORM_ListJobProposalsByManagersIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_ListJobProposalsByManagersIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]feeds.JobProposal, error)) *ORM_ListJobProposalsByManagersIDs_Call {
	_c.Call.Return(run)
	return _c
}

// ListManagers provides a mock function with given fields: ctx
func (_m *ORM) ListManagers(ctx context.Context) ([]feeds.FeedsManager, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListManagers")
	}

	var r0 []feeds.FeedsManager
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]feeds.FeedsManager, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []feeds.FeedsManager); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.FeedsManager)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_ListManagers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListManagers'
type ORM_ListManagers_Call struct {
	*mock.Call
}

// ListManagers is a helper method to define mock.On call
//   - ctx context.Context
func (_e *ORM_Expecter) ListManagers(ctx interface{}) *ORM_ListManagers_Call {
	return &ORM_ListManagers_Call{Call: _e.mock.On("ListManagers", ctx)}
}

func (_c *ORM_ListManagers_Call) Run(run func(ctx context.Context)) *ORM_ListManagers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *ORM_ListManagers_Call) Return(mgrs []feeds.FeedsManager, err error) *ORM_ListManagers_Call {
	_c.Call.Return(mgrs, err)
	return _c
}

func (_c *ORM_ListManagers_Call) RunAndReturn(run func(context.Context) ([]feeds.FeedsManager, error)) *ORM_ListManagers_Call {
	_c.Call.Return(run)
	return _c
}

// ListManagersByIDs provides a mock function with given fields: ctx, ids
func (_m *ORM) ListManagersByIDs(ctx context.Context, ids []int64) ([]feeds.FeedsManager, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for ListManagersByIDs")
	}

	var r0 []feeds.FeedsManager
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]feeds.FeedsManager, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []feeds.FeedsManager); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.FeedsManager)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_ListManagersByIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListManagersByIDs'
type ORM_ListManagersByIDs_Call struct {
	*mock.Call
}

// ListManagersByIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *ORM_Expecter) ListManagersByIDs(ctx interface{}, ids interface{}) *ORM_ListManagersByIDs_Call {
	return &ORM_ListManagersByIDs_Call{Call: _e.mock.On("ListManagersByIDs", ctx, ids)}
}

func (_c *ORM_ListManagersByIDs_Call) Run(run func(ctx context.Context, ids []int64)) *ORM_ListManagersByIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *ORM_ListManagersByIDs_Call) Return(_a0 []feeds.FeedsManager, _a1 error) *ORM_ListManagersByIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_ListManagersByIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]feeds.FeedsManager, error)) *ORM_ListManagersByIDs_Call {
	_c.Call.Return(run)
	return _c
}

// ListSpecsByJobProposalIDs provides a mock function with given fields: ctx, ids
func (_m *ORM) ListSpecsByJobProposalIDs(ctx context.Context, ids []int64) ([]feeds.JobProposalSpec, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for ListSpecsByJobProposalIDs")
	}

	var r0 []feeds.JobProposalSpec
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]feeds.JobProposalSpec, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []feeds.JobProposalSpec); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.JobProposalSpec)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_ListSpecsByJobProposalIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSpecsByJobProposalIDs'
type ORM_ListSpecsByJobProposalIDs_Call struct {
	*mock.Call
}

// ListSpecsByJobProposalIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *ORM_Expecter) ListSpecsByJobProposalIDs(ctx interface{}, ids interface{}) *ORM_ListSpecsByJobProposalIDs_Call {
	return &ORM_ListSpecsByJobProposalIDs_Call{Call: _e.mock.On("ListSpecsByJobProposalIDs", ctx, ids)}
}

func (_c *ORM_ListSpecsByJobProposalIDs_Call) Run(run func(ctx context.Context, ids []int64)) *ORM_ListSpecsByJobProposalIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *ORM_ListSpecsByJobProposalIDs_Call) Return(_a0 []feeds.JobProposalSpec, _a1 error) *ORM_ListSpecsByJobProposalIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_ListSpecsByJobProposalIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]feeds.JobProposalSpec, error)) *ORM_ListSpecsByJobProposalIDs_Call {
	_c.Call.Return(run)
	return _c
}

// ManagerExists provides a mock function with given fields: ctx, publicKey
func (_m *ORM) ManagerExists(ctx context.Context, publicKey crypto.PublicKey) (bool, error) {
	ret := _m.Called(ctx, publicKey)

	if len(ret) == 0 {
		panic("no return value specified for ManagerExists")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, crypto.PublicKey) (bool, error)); ok {
		return rf(ctx, publicKey)
	}
	if rf, ok := ret.Get(0).(func(context.Context, crypto.PublicKey) bool); ok {
		r0 = rf(ctx, publicKey)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, crypto.PublicKey) error); ok {
		r1 = rf(ctx, publicKey)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_ManagerExists_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ManagerExists'
type ORM_ManagerExists_Call struct {
	*mock.Call
}

// ManagerExists is a helper method to define mock.On call
//   - ctx context.Context
//   - publicKey crypto.PublicKey
func (_e *ORM_Expecter) ManagerExists(ctx interface{}, publicKey interface{}) *ORM_ManagerExists_Call {
	return &ORM_ManagerExists_Call{Call: _e.mock.On("ManagerExists", ctx, publicKey)}
}

func (_c *ORM_ManagerExists_Call) Run(run func(ctx context.Context, publicKey crypto.PublicKey)) *ORM_ManagerExists_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(crypto.PublicKey))
	})
	return _c
}

func (_c *ORM_ManagerExists_Call) Return(_a0 bool, _a1 error) *ORM_ManagerExists_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_ManagerExists_Call) RunAndReturn(run func(context.Context, crypto.PublicKey) (bool, error)) *ORM_ManagerExists_Call {
	_c.Call.Return(run)
	return _c
}

// RejectSpec provides a mock function with given fields: ctx, id
func (_m *ORM) RejectSpec(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for RejectSpec")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_RejectSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectSpec'
type ORM_RejectSpec_Call struct {
	*mock.Call
}

// RejectSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) RejectSpec(ctx interface{}, id interface{}) *ORM_RejectSpec_Call {
	return &ORM_RejectSpec_Call{Call: _e.mock.On("RejectSpec", ctx, id)}
}

func (_c *ORM_RejectSpec_Call) Run(run func(ctx context.Context, id int64)) *ORM_RejectSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_RejectSpec_Call) Return(_a0 error) *ORM_RejectSpec_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_RejectSpec_Call) RunAndReturn(run func(context.Context, int64) error) *ORM_RejectSpec_Call {
	_c.Call.Return(run)
	return _c
}

// RevokeSpec provides a mock function with given fields: ctx, id
func (_m *ORM) RevokeSpec(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for RevokeSpec")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_RevokeSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RevokeSpec'
type ORM_RevokeSpec_Call struct {
	*mock.Call
}

// RevokeSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *ORM_Expecter) RevokeSpec(ctx interface{}, id interface{}) *ORM_RevokeSpec_Call {
	return &ORM_RevokeSpec_Call{Call: _e.mock.On("RevokeSpec", ctx, id)}
}

func (_c *ORM_RevokeSpec_Call) Run(run func(ctx context.Context, id int64)) *ORM_RevokeSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *ORM_RevokeSpec_Call) Return(_a0 error) *ORM_RevokeSpec_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_RevokeSpec_Call) RunAndReturn(run func(context.Context, int64) error) *ORM_RevokeSpec_Call {
	_c.Call.Return(run)
	return _c
}

// Transact provides a mock function with given fields: _a0, _a1
func (_m *ORM) Transact(_a0 context.Context, _a1 func(feeds.ORM) error) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Transact")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, func(feeds.ORM) error) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_Transact_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transact'
type ORM_Transact_Call struct {
	*mock.Call
}

// Transact is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 func(feeds.ORM) error
func (_e *ORM_Expecter) Transact(_a0 interface{}, _a1 interface{}) *ORM_Transact_Call {
	return &ORM_Transact_Call{Call: _e.mock.On("Transact", _a0, _a1)}
}

func (_c *ORM_Transact_Call) Run(run func(_a0 context.Context, _a1 func(feeds.ORM) error)) *ORM_Transact_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(func(feeds.ORM) error))
	})
	return _c
}

func (_c *ORM_Transact_Call) Return(_a0 error) *ORM_Transact_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_Transact_Call) RunAndReturn(run func(context.Context, func(feeds.ORM) error) error) *ORM_Transact_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateChainConfig provides a mock function with given fields: ctx, cfg
func (_m *ORM) UpdateChainConfig(ctx context.Context, cfg feeds.ChainConfig) (int64, error) {
	ret := _m.Called(ctx, cfg)

	if len(ret) == 0 {
		panic("no return value specified for UpdateChainConfig")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, feeds.ChainConfig) (int64, error)); ok {
		return rf(ctx, cfg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, feeds.ChainConfig) int64); ok {
		r0 = rf(ctx, cfg)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, feeds.ChainConfig) error); ok {
		r1 = rf(ctx, cfg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_UpdateChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateChainConfig'
type ORM_UpdateChainConfig_Call struct {
	*mock.Call
}

// UpdateChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - cfg feeds.ChainConfig
func (_e *ORM_Expecter) UpdateChainConfig(ctx interface{}, cfg interface{}) *ORM_UpdateChainConfig_Call {
	return &ORM_UpdateChainConfig_Call{Call: _e.mock.On("UpdateChainConfig", ctx, cfg)}
}

func (_c *ORM_UpdateChainConfig_Call) Run(run func(ctx context.Context, cfg feeds.ChainConfig)) *ORM_UpdateChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(feeds.ChainConfig))
	})
	return _c
}

func (_c *ORM_UpdateChainConfig_Call) Return(_a0 int64, _a1 error) *ORM_UpdateChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_UpdateChainConfig_Call) RunAndReturn(run func(context.Context, feeds.ChainConfig) (int64, error)) *ORM_UpdateChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateJobProposalStatus provides a mock function with given fields: ctx, id, status
func (_m *ORM) UpdateJobProposalStatus(ctx context.Context, id int64, status feeds.JobProposalStatus) error {
	ret := _m.Called(ctx, id, status)

	if len(ret) == 0 {
		panic("no return value specified for UpdateJobProposalStatus")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, feeds.JobProposalStatus) error); ok {
		r0 = rf(ctx, id, status)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_UpdateJobProposalStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateJobProposalStatus'
type ORM_UpdateJobProposalStatus_Call struct {
	*mock.Call
}

// UpdateJobProposalStatus is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
//   - status feeds.JobProposalStatus
func (_e *ORM_Expecter) UpdateJobProposalStatus(ctx interface{}, id interface{}, status interface{}) *ORM_UpdateJobProposalStatus_Call {
	return &ORM_UpdateJobProposalStatus_Call{Call: _e.mock.On("UpdateJobProposalStatus", ctx, id, status)}
}

func (_c *ORM_UpdateJobProposalStatus_Call) Run(run func(ctx context.Context, id int64, status feeds.JobProposalStatus)) *ORM_UpdateJobProposalStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(feeds.JobProposalStatus))
	})
	return _c
}

func (_c *ORM_UpdateJobProposalStatus_Call) Return(_a0 error) *ORM_UpdateJobProposalStatus_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_UpdateJobProposalStatus_Call) RunAndReturn(run func(context.Context, int64, feeds.JobProposalStatus) error) *ORM_UpdateJobProposalStatus_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateManager provides a mock function with given fields: ctx, mgr
func (_m *ORM) UpdateManager(ctx context.Context, mgr feeds.FeedsManager) error {
	ret := _m.Called(ctx, mgr)

	if len(ret) == 0 {
		panic("no return value specified for UpdateManager")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, feeds.FeedsManager) error); ok {
		r0 = rf(ctx, mgr)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_UpdateManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateManager'
type ORM_UpdateManager_Call struct {
	*mock.Call
}

// UpdateManager is a helper method to define mock.On call
//   - ctx context.Context
//   - mgr feeds.FeedsManager
func (_e *ORM_Expecter) UpdateManager(ctx interface{}, mgr interface{}) *ORM_UpdateManager_Call {
	return &ORM_UpdateManager_Call{Call: _e.mock.On("UpdateManager", ctx, mgr)}
}

func (_c *ORM_UpdateManager_Call) Run(run func(ctx context.Context, mgr feeds.FeedsManager)) *ORM_UpdateManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(feeds.FeedsManager))
	})
	return _c
}

func (_c *ORM_UpdateManager_Call) Return(_a0 error) *ORM_UpdateManager_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_UpdateManager_Call) RunAndReturn(run func(context.Context, feeds.FeedsManager) error) *ORM_UpdateManager_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateSpecDefinition provides a mock function with given fields: ctx, id, spec
func (_m *ORM) UpdateSpecDefinition(ctx context.Context, id int64, spec string) error {
	ret := _m.Called(ctx, id, spec)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSpecDefinition")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string) error); ok {
		r0 = rf(ctx, id, spec)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_UpdateSpecDefinition_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateSpecDefinition'
type ORM_UpdateSpecDefinition_Call struct {
	*mock.Call
}

// UpdateSpecDefinition is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
//   - spec string
func (_e *ORM_Expecter) UpdateSpecDefinition(ctx interface{}, id interface{}, spec interface{}) *ORM_UpdateSpecDefinition_Call {
	return &ORM_UpdateSpecDefinition_Call{Call: _e.mock.On("UpdateSpecDefinition", ctx, id, spec)}
}

func (_c *ORM_UpdateSpecDefinition_Call) Run(run func(ctx context.Context, id int64, spec string)) *ORM_UpdateSpecDefinition_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(string))
	})
	return _c
}

func (_c *ORM_UpdateSpecDefinition_Call) Return(_a0 error) *ORM_UpdateSpecDefinition_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_UpdateSpecDefinition_Call) RunAndReturn(run func(context.Context, int64, string) error) *ORM_UpdateSpecDefinition_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertJobProposal provides a mock function with given fields: ctx, jp
func (_m *ORM) UpsertJobProposal(ctx context.Context, jp *feeds.JobProposal) (int64, error) {
	ret := _m.Called(ctx, jp)

	if len(ret) == 0 {
		panic("no return value specified for UpsertJobProposal")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.JobProposal) (int64, error)); ok {
		return rf(ctx, jp)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.JobProposal) int64); ok {
		r0 = rf(ctx, jp)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *feeds.JobProposal) error); ok {
		r1 = rf(ctx, jp)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_UpsertJobProposal_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertJobProposal'
type ORM_UpsertJobProposal_Call struct {
	*mock.Call
}

// UpsertJobProposal is a helper method to define mock.On call
//   - ctx context.Context
//   - jp *feeds.JobProposal
func (_e *ORM_Expecter) UpsertJobProposal(ctx interface{}, jp interface{}) *ORM_UpsertJobProposal_Call {
	return &ORM_UpsertJobProposal_Call{Call: _e.mock.On("UpsertJobProposal", ctx, jp)}
}

func (_c *ORM_UpsertJobProposal_Call) Run(run func(ctx context.Context, jp *feeds.JobProposal)) *ORM_UpsertJobProposal_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*feeds.JobProposal))
	})
	return _c
}

func (_c *ORM_UpsertJobProposal_Call) Return(_a0 int64, _a1 error) *ORM_UpsertJobProposal_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_UpsertJobProposal_Call) RunAndReturn(run func(context.Context, *feeds.JobProposal) (int64, error)) *ORM_UpsertJobProposal_Call {
	_c.Call.Return(run)
	return _c
}

// WithDataSource provides a mock function with given fields: _a0
func (_m *ORM) WithDataSource(_a0 sqlutil.DataSource) feeds.ORM {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for WithDataSource")
	}

	var r0 feeds.ORM
	if rf, ok := ret.Get(0).(func(sqlutil.DataSource) feeds.ORM); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(feeds.ORM)
		}
	}

	return r0
}

// ORM_WithDataSource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithDataSource'
type ORM_WithDataSource_Call struct {
	*mock.Call
}

// WithDataSource is a helper method to define mock.On call
//   - _a0 sqlutil.DataSource
func (_e *ORM_Expecter) WithDataSource(_a0 interface{}) *ORM_WithDataSource_Call {
	return &ORM_WithDataSource_Call{Call: _e.mock.On("WithDataSource", _a0)}
}

func (_c *ORM_WithDataSource_Call) Run(run func(_a0 sqlutil.DataSource)) *ORM_WithDataSource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(sqlutil.DataSource))
	})
	return _c
}

func (_c *ORM_WithDataSource_Call) Return(_a0 feeds.ORM) *ORM_WithDataSource_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_WithDataSource_Call) RunAndReturn(run func(sqlutil.DataSource) feeds.ORM) *ORM_WithDataSource_Call {
	_c.Call.Return(run)
	return _c
}

// NewORM creates a new instance of ORM. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewORM(t interface {
	mock.TestingT
	Cleanup(func())
}) *ORM {
	mock := &ORM{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
