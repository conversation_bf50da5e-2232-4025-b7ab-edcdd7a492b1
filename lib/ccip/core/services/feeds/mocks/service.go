// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	feeds "github.com/smartcontractkit/chainlink/v2/core/services/feeds"
	mock "github.com/stretchr/testify/mock"
)

// Service is an autogenerated mock type for the Service type
type Service struct {
	mock.Mock
}

type Service_Expecter struct {
	mock *mock.Mock
}

func (_m *Service) EXPECT() *Service_Expecter {
	return &Service_Expecter{mock: &_m.Mock}
}

// ApproveSpec provides a mock function with given fields: ctx, id, force
func (_m *Service) ApproveSpec(ctx context.Context, id int64, force bool) error {
	ret := _m.Called(ctx, id, force)

	if len(ret) == 0 {
		panic("no return value specified for ApproveSpec")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, bool) error); ok {
		r0 = rf(ctx, id, force)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Service_ApproveSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ApproveSpec'
type Service_ApproveSpec_Call struct {
	*mock.Call
}

// ApproveSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
//   - force bool
func (_e *Service_Expecter) ApproveSpec(ctx interface{}, id interface{}, force interface{}) *Service_ApproveSpec_Call {
	return &Service_ApproveSpec_Call{Call: _e.mock.On("ApproveSpec", ctx, id, force)}
}

func (_c *Service_ApproveSpec_Call) Run(run func(ctx context.Context, id int64, force bool)) *Service_ApproveSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(bool))
	})
	return _c
}

func (_c *Service_ApproveSpec_Call) Return(_a0 error) *Service_ApproveSpec_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Service_ApproveSpec_Call) RunAndReturn(run func(context.Context, int64, bool) error) *Service_ApproveSpec_Call {
	_c.Call.Return(run)
	return _c
}

// CancelSpec provides a mock function with given fields: ctx, id
func (_m *Service) CancelSpec(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for CancelSpec")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Service_CancelSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelSpec'
type Service_CancelSpec_Call struct {
	*mock.Call
}

// CancelSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *Service_Expecter) CancelSpec(ctx interface{}, id interface{}) *Service_CancelSpec_Call {
	return &Service_CancelSpec_Call{Call: _e.mock.On("CancelSpec", ctx, id)}
}

func (_c *Service_CancelSpec_Call) Run(run func(ctx context.Context, id int64)) *Service_CancelSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Service_CancelSpec_Call) Return(_a0 error) *Service_CancelSpec_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Service_CancelSpec_Call) RunAndReturn(run func(context.Context, int64) error) *Service_CancelSpec_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *Service) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Service_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Service_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *Service_Expecter) Close() *Service_Close_Call {
	return &Service_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *Service_Close_Call) Run(run func()) *Service_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Service_Close_Call) Return(_a0 error) *Service_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Service_Close_Call) RunAndReturn(run func() error) *Service_Close_Call {
	_c.Call.Return(run)
	return _c
}

// CountJobProposalsByStatus provides a mock function with given fields: ctx
func (_m *Service) CountJobProposalsByStatus(ctx context.Context) (*feeds.JobProposalCounts, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CountJobProposalsByStatus")
	}

	var r0 *feeds.JobProposalCounts
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*feeds.JobProposalCounts, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *feeds.JobProposalCounts); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.JobProposalCounts)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_CountJobProposalsByStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountJobProposalsByStatus'
type Service_CountJobProposalsByStatus_Call struct {
	*mock.Call
}

// CountJobProposalsByStatus is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Service_Expecter) CountJobProposalsByStatus(ctx interface{}) *Service_CountJobProposalsByStatus_Call {
	return &Service_CountJobProposalsByStatus_Call{Call: _e.mock.On("CountJobProposalsByStatus", ctx)}
}

func (_c *Service_CountJobProposalsByStatus_Call) Run(run func(ctx context.Context)) *Service_CountJobProposalsByStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Service_CountJobProposalsByStatus_Call) Return(_a0 *feeds.JobProposalCounts, _a1 error) *Service_CountJobProposalsByStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_CountJobProposalsByStatus_Call) RunAndReturn(run func(context.Context) (*feeds.JobProposalCounts, error)) *Service_CountJobProposalsByStatus_Call {
	_c.Call.Return(run)
	return _c
}

// CreateChainConfig provides a mock function with given fields: ctx, cfg
func (_m *Service) CreateChainConfig(ctx context.Context, cfg feeds.ChainConfig) (int64, error) {
	ret := _m.Called(ctx, cfg)

	if len(ret) == 0 {
		panic("no return value specified for CreateChainConfig")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, feeds.ChainConfig) (int64, error)); ok {
		return rf(ctx, cfg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, feeds.ChainConfig) int64); ok {
		r0 = rf(ctx, cfg)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, feeds.ChainConfig) error); ok {
		r1 = rf(ctx, cfg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_CreateChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateChainConfig'
type Service_CreateChainConfig_Call struct {
	*mock.Call
}

// CreateChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - cfg feeds.ChainConfig
func (_e *Service_Expecter) CreateChainConfig(ctx interface{}, cfg interface{}) *Service_CreateChainConfig_Call {
	return &Service_CreateChainConfig_Call{Call: _e.mock.On("CreateChainConfig", ctx, cfg)}
}

func (_c *Service_CreateChainConfig_Call) Run(run func(ctx context.Context, cfg feeds.ChainConfig)) *Service_CreateChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(feeds.ChainConfig))
	})
	return _c
}

func (_c *Service_CreateChainConfig_Call) Return(_a0 int64, _a1 error) *Service_CreateChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_CreateChainConfig_Call) RunAndReturn(run func(context.Context, feeds.ChainConfig) (int64, error)) *Service_CreateChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteChainConfig provides a mock function with given fields: ctx, id
func (_m *Service) DeleteChainConfig(ctx context.Context, id int64) (int64, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteChainConfig")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (int64, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) int64); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_DeleteChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteChainConfig'
type Service_DeleteChainConfig_Call struct {
	*mock.Call
}

// DeleteChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *Service_Expecter) DeleteChainConfig(ctx interface{}, id interface{}) *Service_DeleteChainConfig_Call {
	return &Service_DeleteChainConfig_Call{Call: _e.mock.On("DeleteChainConfig", ctx, id)}
}

func (_c *Service_DeleteChainConfig_Call) Run(run func(ctx context.Context, id int64)) *Service_DeleteChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Service_DeleteChainConfig_Call) Return(_a0 int64, _a1 error) *Service_DeleteChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_DeleteChainConfig_Call) RunAndReturn(run func(context.Context, int64) (int64, error)) *Service_DeleteChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteJob provides a mock function with given fields: ctx, args
func (_m *Service) DeleteJob(ctx context.Context, args *feeds.DeleteJobArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for DeleteJob")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.DeleteJobArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.DeleteJobArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *feeds.DeleteJobArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_DeleteJob_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteJob'
type Service_DeleteJob_Call struct {
	*mock.Call
}

// DeleteJob is a helper method to define mock.On call
//   - ctx context.Context
//   - args *feeds.DeleteJobArgs
func (_e *Service_Expecter) DeleteJob(ctx interface{}, args interface{}) *Service_DeleteJob_Call {
	return &Service_DeleteJob_Call{Call: _e.mock.On("DeleteJob", ctx, args)}
}

func (_c *Service_DeleteJob_Call) Run(run func(ctx context.Context, args *feeds.DeleteJobArgs)) *Service_DeleteJob_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*feeds.DeleteJobArgs))
	})
	return _c
}

func (_c *Service_DeleteJob_Call) Return(_a0 int64, _a1 error) *Service_DeleteJob_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_DeleteJob_Call) RunAndReturn(run func(context.Context, *feeds.DeleteJobArgs) (int64, error)) *Service_DeleteJob_Call {
	_c.Call.Return(run)
	return _c
}

// GetChainConfig provides a mock function with given fields: ctx, id
func (_m *Service) GetChainConfig(ctx context.Context, id int64) (*feeds.ChainConfig, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetChainConfig")
	}

	var r0 *feeds.ChainConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.ChainConfig, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.ChainConfig); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.ChainConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_GetChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChainConfig'
type Service_GetChainConfig_Call struct {
	*mock.Call
}

// GetChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *Service_Expecter) GetChainConfig(ctx interface{}, id interface{}) *Service_GetChainConfig_Call {
	return &Service_GetChainConfig_Call{Call: _e.mock.On("GetChainConfig", ctx, id)}
}

func (_c *Service_GetChainConfig_Call) Run(run func(ctx context.Context, id int64)) *Service_GetChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Service_GetChainConfig_Call) Return(_a0 *feeds.ChainConfig, _a1 error) *Service_GetChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_GetChainConfig_Call) RunAndReturn(run func(context.Context, int64) (*feeds.ChainConfig, error)) *Service_GetChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetJobProposal provides a mock function with given fields: ctx, id
func (_m *Service) GetJobProposal(ctx context.Context, id int64) (*feeds.JobProposal, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetJobProposal")
	}

	var r0 *feeds.JobProposal
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.JobProposal, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.JobProposal); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.JobProposal)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_GetJobProposal_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetJobProposal'
type Service_GetJobProposal_Call struct {
	*mock.Call
}

// GetJobProposal is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *Service_Expecter) GetJobProposal(ctx interface{}, id interface{}) *Service_GetJobProposal_Call {
	return &Service_GetJobProposal_Call{Call: _e.mock.On("GetJobProposal", ctx, id)}
}

func (_c *Service_GetJobProposal_Call) Run(run func(ctx context.Context, id int64)) *Service_GetJobProposal_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Service_GetJobProposal_Call) Return(_a0 *feeds.JobProposal, _a1 error) *Service_GetJobProposal_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_GetJobProposal_Call) RunAndReturn(run func(context.Context, int64) (*feeds.JobProposal, error)) *Service_GetJobProposal_Call {
	_c.Call.Return(run)
	return _c
}

// GetManager provides a mock function with given fields: ctx, id
func (_m *Service) GetManager(ctx context.Context, id int64) (*feeds.FeedsManager, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetManager")
	}

	var r0 *feeds.FeedsManager
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.FeedsManager, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.FeedsManager); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.FeedsManager)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_GetManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetManager'
type Service_GetManager_Call struct {
	*mock.Call
}

// GetManager is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *Service_Expecter) GetManager(ctx interface{}, id interface{}) *Service_GetManager_Call {
	return &Service_GetManager_Call{Call: _e.mock.On("GetManager", ctx, id)}
}

func (_c *Service_GetManager_Call) Run(run func(ctx context.Context, id int64)) *Service_GetManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Service_GetManager_Call) Return(_a0 *feeds.FeedsManager, _a1 error) *Service_GetManager_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_GetManager_Call) RunAndReturn(run func(context.Context, int64) (*feeds.FeedsManager, error)) *Service_GetManager_Call {
	_c.Call.Return(run)
	return _c
}

// GetSpec provides a mock function with given fields: ctx, id
func (_m *Service) GetSpec(ctx context.Context, id int64) (*feeds.JobProposalSpec, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetSpec")
	}

	var r0 *feeds.JobProposalSpec
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*feeds.JobProposalSpec, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *feeds.JobProposalSpec); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*feeds.JobProposalSpec)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_GetSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSpec'
type Service_GetSpec_Call struct {
	*mock.Call
}

// GetSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *Service_Expecter) GetSpec(ctx interface{}, id interface{}) *Service_GetSpec_Call {
	return &Service_GetSpec_Call{Call: _e.mock.On("GetSpec", ctx, id)}
}

func (_c *Service_GetSpec_Call) Run(run func(ctx context.Context, id int64)) *Service_GetSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Service_GetSpec_Call) Return(_a0 *feeds.JobProposalSpec, _a1 error) *Service_GetSpec_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_GetSpec_Call) RunAndReturn(run func(context.Context, int64) (*feeds.JobProposalSpec, error)) *Service_GetSpec_Call {
	_c.Call.Return(run)
	return _c
}

// IsJobManaged provides a mock function with given fields: ctx, jobID
func (_m *Service) IsJobManaged(ctx context.Context, jobID int64) (bool, error) {
	ret := _m.Called(ctx, jobID)

	if len(ret) == 0 {
		panic("no return value specified for IsJobManaged")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (bool, error)); ok {
		return rf(ctx, jobID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) bool); ok {
		r0 = rf(ctx, jobID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, jobID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_IsJobManaged_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsJobManaged'
type Service_IsJobManaged_Call struct {
	*mock.Call
}

// IsJobManaged is a helper method to define mock.On call
//   - ctx context.Context
//   - jobID int64
func (_e *Service_Expecter) IsJobManaged(ctx interface{}, jobID interface{}) *Service_IsJobManaged_Call {
	return &Service_IsJobManaged_Call{Call: _e.mock.On("IsJobManaged", ctx, jobID)}
}

func (_c *Service_IsJobManaged_Call) Run(run func(ctx context.Context, jobID int64)) *Service_IsJobManaged_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Service_IsJobManaged_Call) Return(_a0 bool, _a1 error) *Service_IsJobManaged_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_IsJobManaged_Call) RunAndReturn(run func(context.Context, int64) (bool, error)) *Service_IsJobManaged_Call {
	_c.Call.Return(run)
	return _c
}

// ListChainConfigsByManagerIDs provides a mock function with given fields: ctx, mgrIDs
func (_m *Service) ListChainConfigsByManagerIDs(ctx context.Context, mgrIDs []int64) ([]feeds.ChainConfig, error) {
	ret := _m.Called(ctx, mgrIDs)

	if len(ret) == 0 {
		panic("no return value specified for ListChainConfigsByManagerIDs")
	}

	var r0 []feeds.ChainConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]feeds.ChainConfig, error)); ok {
		return rf(ctx, mgrIDs)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []feeds.ChainConfig); ok {
		r0 = rf(ctx, mgrIDs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.ChainConfig)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, mgrIDs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_ListChainConfigsByManagerIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListChainConfigsByManagerIDs'
type Service_ListChainConfigsByManagerIDs_Call struct {
	*mock.Call
}

// ListChainConfigsByManagerIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - mgrIDs []int64
func (_e *Service_Expecter) ListChainConfigsByManagerIDs(ctx interface{}, mgrIDs interface{}) *Service_ListChainConfigsByManagerIDs_Call {
	return &Service_ListChainConfigsByManagerIDs_Call{Call: _e.mock.On("ListChainConfigsByManagerIDs", ctx, mgrIDs)}
}

func (_c *Service_ListChainConfigsByManagerIDs_Call) Run(run func(ctx context.Context, mgrIDs []int64)) *Service_ListChainConfigsByManagerIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *Service_ListChainConfigsByManagerIDs_Call) Return(_a0 []feeds.ChainConfig, _a1 error) *Service_ListChainConfigsByManagerIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_ListChainConfigsByManagerIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]feeds.ChainConfig, error)) *Service_ListChainConfigsByManagerIDs_Call {
	_c.Call.Return(run)
	return _c
}

// ListJobProposalsByManagersIDs provides a mock function with given fields: ctx, ids
func (_m *Service) ListJobProposalsByManagersIDs(ctx context.Context, ids []int64) ([]feeds.JobProposal, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for ListJobProposalsByManagersIDs")
	}

	var r0 []feeds.JobProposal
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]feeds.JobProposal, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []feeds.JobProposal); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.JobProposal)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_ListJobProposalsByManagersIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListJobProposalsByManagersIDs'
type Service_ListJobProposalsByManagersIDs_Call struct {
	*mock.Call
}

// ListJobProposalsByManagersIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *Service_Expecter) ListJobProposalsByManagersIDs(ctx interface{}, ids interface{}) *Service_ListJobProposalsByManagersIDs_Call {
	return &Service_ListJobProposalsByManagersIDs_Call{Call: _e.mock.On("ListJobProposalsByManagersIDs", ctx, ids)}
}

func (_c *Service_ListJobProposalsByManagersIDs_Call) Run(run func(ctx context.Context, ids []int64)) *Service_ListJobProposalsByManagersIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *Service_ListJobProposalsByManagersIDs_Call) Return(_a0 []feeds.JobProposal, _a1 error) *Service_ListJobProposalsByManagersIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_ListJobProposalsByManagersIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]feeds.JobProposal, error)) *Service_ListJobProposalsByManagersIDs_Call {
	_c.Call.Return(run)
	return _c
}

// ListManagers provides a mock function with given fields: ctx
func (_m *Service) ListManagers(ctx context.Context) ([]feeds.FeedsManager, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListManagers")
	}

	var r0 []feeds.FeedsManager
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]feeds.FeedsManager, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []feeds.FeedsManager); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.FeedsManager)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_ListManagers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListManagers'
type Service_ListManagers_Call struct {
	*mock.Call
}

// ListManagers is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Service_Expecter) ListManagers(ctx interface{}) *Service_ListManagers_Call {
	return &Service_ListManagers_Call{Call: _e.mock.On("ListManagers", ctx)}
}

func (_c *Service_ListManagers_Call) Run(run func(ctx context.Context)) *Service_ListManagers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Service_ListManagers_Call) Return(_a0 []feeds.FeedsManager, _a1 error) *Service_ListManagers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_ListManagers_Call) RunAndReturn(run func(context.Context) ([]feeds.FeedsManager, error)) *Service_ListManagers_Call {
	_c.Call.Return(run)
	return _c
}

// ListManagersByIDs provides a mock function with given fields: ctx, ids
func (_m *Service) ListManagersByIDs(ctx context.Context, ids []int64) ([]feeds.FeedsManager, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for ListManagersByIDs")
	}

	var r0 []feeds.FeedsManager
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]feeds.FeedsManager, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []feeds.FeedsManager); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.FeedsManager)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_ListManagersByIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListManagersByIDs'
type Service_ListManagersByIDs_Call struct {
	*mock.Call
}

// ListManagersByIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *Service_Expecter) ListManagersByIDs(ctx interface{}, ids interface{}) *Service_ListManagersByIDs_Call {
	return &Service_ListManagersByIDs_Call{Call: _e.mock.On("ListManagersByIDs", ctx, ids)}
}

func (_c *Service_ListManagersByIDs_Call) Run(run func(ctx context.Context, ids []int64)) *Service_ListManagersByIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *Service_ListManagersByIDs_Call) Return(_a0 []feeds.FeedsManager, _a1 error) *Service_ListManagersByIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_ListManagersByIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]feeds.FeedsManager, error)) *Service_ListManagersByIDs_Call {
	_c.Call.Return(run)
	return _c
}

// ListSpecsByJobProposalIDs provides a mock function with given fields: ctx, ids
func (_m *Service) ListSpecsByJobProposalIDs(ctx context.Context, ids []int64) ([]feeds.JobProposalSpec, error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for ListSpecsByJobProposalIDs")
	}

	var r0 []feeds.JobProposalSpec
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]feeds.JobProposalSpec, error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []feeds.JobProposalSpec); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]feeds.JobProposalSpec)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_ListSpecsByJobProposalIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListSpecsByJobProposalIDs'
type Service_ListSpecsByJobProposalIDs_Call struct {
	*mock.Call
}

// ListSpecsByJobProposalIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *Service_Expecter) ListSpecsByJobProposalIDs(ctx interface{}, ids interface{}) *Service_ListSpecsByJobProposalIDs_Call {
	return &Service_ListSpecsByJobProposalIDs_Call{Call: _e.mock.On("ListSpecsByJobProposalIDs", ctx, ids)}
}

func (_c *Service_ListSpecsByJobProposalIDs_Call) Run(run func(ctx context.Context, ids []int64)) *Service_ListSpecsByJobProposalIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *Service_ListSpecsByJobProposalIDs_Call) Return(_a0 []feeds.JobProposalSpec, _a1 error) *Service_ListSpecsByJobProposalIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_ListSpecsByJobProposalIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]feeds.JobProposalSpec, error)) *Service_ListSpecsByJobProposalIDs_Call {
	_c.Call.Return(run)
	return _c
}

// ProposeJob provides a mock function with given fields: ctx, args
func (_m *Service) ProposeJob(ctx context.Context, args *feeds.ProposeJobArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for ProposeJob")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.ProposeJobArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.ProposeJobArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *feeds.ProposeJobArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_ProposeJob_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProposeJob'
type Service_ProposeJob_Call struct {
	*mock.Call
}

// ProposeJob is a helper method to define mock.On call
//   - ctx context.Context
//   - args *feeds.ProposeJobArgs
func (_e *Service_Expecter) ProposeJob(ctx interface{}, args interface{}) *Service_ProposeJob_Call {
	return &Service_ProposeJob_Call{Call: _e.mock.On("ProposeJob", ctx, args)}
}

func (_c *Service_ProposeJob_Call) Run(run func(ctx context.Context, args *feeds.ProposeJobArgs)) *Service_ProposeJob_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*feeds.ProposeJobArgs))
	})
	return _c
}

func (_c *Service_ProposeJob_Call) Return(_a0 int64, _a1 error) *Service_ProposeJob_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_ProposeJob_Call) RunAndReturn(run func(context.Context, *feeds.ProposeJobArgs) (int64, error)) *Service_ProposeJob_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterManager provides a mock function with given fields: ctx, params
func (_m *Service) RegisterManager(ctx context.Context, params feeds.RegisterManagerParams) (int64, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for RegisterManager")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, feeds.RegisterManagerParams) (int64, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, feeds.RegisterManagerParams) int64); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, feeds.RegisterManagerParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_RegisterManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterManager'
type Service_RegisterManager_Call struct {
	*mock.Call
}

// RegisterManager is a helper method to define mock.On call
//   - ctx context.Context
//   - params feeds.RegisterManagerParams
func (_e *Service_Expecter) RegisterManager(ctx interface{}, params interface{}) *Service_RegisterManager_Call {
	return &Service_RegisterManager_Call{Call: _e.mock.On("RegisterManager", ctx, params)}
}

func (_c *Service_RegisterManager_Call) Run(run func(ctx context.Context, params feeds.RegisterManagerParams)) *Service_RegisterManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(feeds.RegisterManagerParams))
	})
	return _c
}

func (_c *Service_RegisterManager_Call) Return(_a0 int64, _a1 error) *Service_RegisterManager_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_RegisterManager_Call) RunAndReturn(run func(context.Context, feeds.RegisterManagerParams) (int64, error)) *Service_RegisterManager_Call {
	_c.Call.Return(run)
	return _c
}

// RejectSpec provides a mock function with given fields: ctx, id
func (_m *Service) RejectSpec(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for RejectSpec")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Service_RejectSpec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RejectSpec'
type Service_RejectSpec_Call struct {
	*mock.Call
}

// RejectSpec is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *Service_Expecter) RejectSpec(ctx interface{}, id interface{}) *Service_RejectSpec_Call {
	return &Service_RejectSpec_Call{Call: _e.mock.On("RejectSpec", ctx, id)}
}

func (_c *Service_RejectSpec_Call) Run(run func(ctx context.Context, id int64)) *Service_RejectSpec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Service_RejectSpec_Call) Return(_a0 error) *Service_RejectSpec_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Service_RejectSpec_Call) RunAndReturn(run func(context.Context, int64) error) *Service_RejectSpec_Call {
	_c.Call.Return(run)
	return _c
}

// RevokeJob provides a mock function with given fields: ctx, args
func (_m *Service) RevokeJob(ctx context.Context, args *feeds.RevokeJobArgs) (int64, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for RevokeJob")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.RevokeJobArgs) (int64, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *feeds.RevokeJobArgs) int64); ok {
		r0 = rf(ctx, args)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *feeds.RevokeJobArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_RevokeJob_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RevokeJob'
type Service_RevokeJob_Call struct {
	*mock.Call
}

// RevokeJob is a helper method to define mock.On call
//   - ctx context.Context
//   - args *feeds.RevokeJobArgs
func (_e *Service_Expecter) RevokeJob(ctx interface{}, args interface{}) *Service_RevokeJob_Call {
	return &Service_RevokeJob_Call{Call: _e.mock.On("RevokeJob", ctx, args)}
}

func (_c *Service_RevokeJob_Call) Run(run func(ctx context.Context, args *feeds.RevokeJobArgs)) *Service_RevokeJob_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*feeds.RevokeJobArgs))
	})
	return _c
}

func (_c *Service_RevokeJob_Call) Return(_a0 int64, _a1 error) *Service_RevokeJob_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_RevokeJob_Call) RunAndReturn(run func(context.Context, *feeds.RevokeJobArgs) (int64, error)) *Service_RevokeJob_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: ctx
func (_m *Service) Start(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Service_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type Service_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Service_Expecter) Start(ctx interface{}) *Service_Start_Call {
	return &Service_Start_Call{Call: _e.mock.On("Start", ctx)}
}

func (_c *Service_Start_Call) Run(run func(ctx context.Context)) *Service_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Service_Start_Call) Return(_a0 error) *Service_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Service_Start_Call) RunAndReturn(run func(context.Context) error) *Service_Start_Call {
	_c.Call.Return(run)
	return _c
}

// SyncNodeInfo provides a mock function with given fields: ctx, id
func (_m *Service) SyncNodeInfo(ctx context.Context, id int64) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for SyncNodeInfo")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Service_SyncNodeInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SyncNodeInfo'
type Service_SyncNodeInfo_Call struct {
	*mock.Call
}

// SyncNodeInfo is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *Service_Expecter) SyncNodeInfo(ctx interface{}, id interface{}) *Service_SyncNodeInfo_Call {
	return &Service_SyncNodeInfo_Call{Call: _e.mock.On("SyncNodeInfo", ctx, id)}
}

func (_c *Service_SyncNodeInfo_Call) Run(run func(ctx context.Context, id int64)) *Service_SyncNodeInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *Service_SyncNodeInfo_Call) Return(_a0 error) *Service_SyncNodeInfo_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Service_SyncNodeInfo_Call) RunAndReturn(run func(context.Context, int64) error) *Service_SyncNodeInfo_Call {
	_c.Call.Return(run)
	return _c
}

// Unsafe_SetConnectionsManager provides a mock function with given fields: _a0
func (_m *Service) Unsafe_SetConnectionsManager(_a0 feeds.ConnectionsManager) {
	_m.Called(_a0)
}

// Service_Unsafe_SetConnectionsManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Unsafe_SetConnectionsManager'
type Service_Unsafe_SetConnectionsManager_Call struct {
	*mock.Call
}

// Unsafe_SetConnectionsManager is a helper method to define mock.On call
//   - _a0 feeds.ConnectionsManager
func (_e *Service_Expecter) Unsafe_SetConnectionsManager(_a0 interface{}) *Service_Unsafe_SetConnectionsManager_Call {
	return &Service_Unsafe_SetConnectionsManager_Call{Call: _e.mock.On("Unsafe_SetConnectionsManager", _a0)}
}

func (_c *Service_Unsafe_SetConnectionsManager_Call) Run(run func(_a0 feeds.ConnectionsManager)) *Service_Unsafe_SetConnectionsManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(feeds.ConnectionsManager))
	})
	return _c
}

func (_c *Service_Unsafe_SetConnectionsManager_Call) Return() *Service_Unsafe_SetConnectionsManager_Call {
	_c.Call.Return()
	return _c
}

func (_c *Service_Unsafe_SetConnectionsManager_Call) RunAndReturn(run func(feeds.ConnectionsManager)) *Service_Unsafe_SetConnectionsManager_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateChainConfig provides a mock function with given fields: ctx, cfg
func (_m *Service) UpdateChainConfig(ctx context.Context, cfg feeds.ChainConfig) (int64, error) {
	ret := _m.Called(ctx, cfg)

	if len(ret) == 0 {
		panic("no return value specified for UpdateChainConfig")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, feeds.ChainConfig) (int64, error)); ok {
		return rf(ctx, cfg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, feeds.ChainConfig) int64); ok {
		r0 = rf(ctx, cfg)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, feeds.ChainConfig) error); ok {
		r1 = rf(ctx, cfg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Service_UpdateChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateChainConfig'
type Service_UpdateChainConfig_Call struct {
	*mock.Call
}

// UpdateChainConfig is a helper method to define mock.On call
//   - ctx context.Context
//   - cfg feeds.ChainConfig
func (_e *Service_Expecter) UpdateChainConfig(ctx interface{}, cfg interface{}) *Service_UpdateChainConfig_Call {
	return &Service_UpdateChainConfig_Call{Call: _e.mock.On("UpdateChainConfig", ctx, cfg)}
}

func (_c *Service_UpdateChainConfig_Call) Run(run func(ctx context.Context, cfg feeds.ChainConfig)) *Service_UpdateChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(feeds.ChainConfig))
	})
	return _c
}

func (_c *Service_UpdateChainConfig_Call) Return(_a0 int64, _a1 error) *Service_UpdateChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Service_UpdateChainConfig_Call) RunAndReturn(run func(context.Context, feeds.ChainConfig) (int64, error)) *Service_UpdateChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateManager provides a mock function with given fields: ctx, mgr
func (_m *Service) UpdateManager(ctx context.Context, mgr feeds.FeedsManager) error {
	ret := _m.Called(ctx, mgr)

	if len(ret) == 0 {
		panic("no return value specified for UpdateManager")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, feeds.FeedsManager) error); ok {
		r0 = rf(ctx, mgr)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Service_UpdateManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateManager'
type Service_UpdateManager_Call struct {
	*mock.Call
}

// UpdateManager is a helper method to define mock.On call
//   - ctx context.Context
//   - mgr feeds.FeedsManager
func (_e *Service_Expecter) UpdateManager(ctx interface{}, mgr interface{}) *Service_UpdateManager_Call {
	return &Service_UpdateManager_Call{Call: _e.mock.On("UpdateManager", ctx, mgr)}
}

func (_c *Service_UpdateManager_Call) Run(run func(ctx context.Context, mgr feeds.FeedsManager)) *Service_UpdateManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(feeds.FeedsManager))
	})
	return _c
}

func (_c *Service_UpdateManager_Call) Return(_a0 error) *Service_UpdateManager_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Service_UpdateManager_Call) RunAndReturn(run func(context.Context, feeds.FeedsManager) error) *Service_UpdateManager_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateSpecDefinition provides a mock function with given fields: ctx, id, spec
func (_m *Service) UpdateSpecDefinition(ctx context.Context, id int64, spec string) error {
	ret := _m.Called(ctx, id, spec)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSpecDefinition")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, string) error); ok {
		r0 = rf(ctx, id, spec)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Service_UpdateSpecDefinition_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateSpecDefinition'
type Service_UpdateSpecDefinition_Call struct {
	*mock.Call
}

// UpdateSpecDefinition is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
//   - spec string
func (_e *Service_Expecter) UpdateSpecDefinition(ctx interface{}, id interface{}, spec interface{}) *Service_UpdateSpecDefinition_Call {
	return &Service_UpdateSpecDefinition_Call{Call: _e.mock.On("UpdateSpecDefinition", ctx, id, spec)}
}

func (_c *Service_UpdateSpecDefinition_Call) Run(run func(ctx context.Context, id int64, spec string)) *Service_UpdateSpecDefinition_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(string))
	})
	return _c
}

func (_c *Service_UpdateSpecDefinition_Call) Return(_a0 error) *Service_UpdateSpecDefinition_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Service_UpdateSpecDefinition_Call) RunAndReturn(run func(context.Context, int64, string) error) *Service_UpdateSpecDefinition_Call {
	_c.Call.Return(run)
	return _c
}

// NewService creates a new instance of Service. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewService(t interface {
	mock.TestingT
	Cleanup(func())
}) *Service {
	mock := &Service{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
