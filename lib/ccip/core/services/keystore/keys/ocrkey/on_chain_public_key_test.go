package ocrkey

import (
	"crypto/ecdsa"
	"crypto/rand"
	"testing"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOCRKeys_OnChainPublic<PERSON>ey(t *testing.T) {
	t.<PERSON>()

	pk, err := ecdsa.GenerateKey(crypto.S256(), rand.Reader)
	require.NoError(t, err)

	publicKey := OnChainPublicKey(pk.PublicKey)

	assert.NotEmpty(t, publicKey.Address())
}
