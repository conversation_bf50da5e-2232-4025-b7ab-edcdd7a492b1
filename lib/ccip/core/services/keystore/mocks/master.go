// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	keystore "github.com/smartcontractkit/chainlink/v2/core/services/keystore"
	mock "github.com/stretchr/testify/mock"
)

// Master is an autogenerated mock type for the Master type
type Master struct {
	mock.Mock
}

type Master_Expecter struct {
	mock *mock.Mock
}

func (_m *Master) EXPECT() *Master_Expecter {
	return &Master_Expecter{mock: &_m.Mock}
}

// Aptos provides a mock function with given fields:
func (_m *Master) Aptos() keystore.Aptos {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Aptos")
	}

	var r0 keystore.Aptos
	if rf, ok := ret.Get(0).(func() keystore.Aptos); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.Aptos)
		}
	}

	return r0
}

// Master_Aptos_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Aptos'
type Master_Aptos_Call struct {
	*mock.Call
}

// Aptos is a helper method to define mock.On call
func (_e *Master_Expecter) Aptos() *Master_Aptos_Call {
	return &Master_Aptos_Call{Call: _e.mock.On("Aptos")}
}

func (_c *Master_Aptos_Call) Run(run func()) *Master_Aptos_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_Aptos_Call) Return(_a0 keystore.Aptos) *Master_Aptos_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_Aptos_Call) RunAndReturn(run func() keystore.Aptos) *Master_Aptos_Call {
	_c.Call.Return(run)
	return _c
}

// CSA provides a mock function with given fields:
func (_m *Master) CSA() keystore.CSA {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for CSA")
	}

	var r0 keystore.CSA
	if rf, ok := ret.Get(0).(func() keystore.CSA); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.CSA)
		}
	}

	return r0
}

// Master_CSA_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CSA'
type Master_CSA_Call struct {
	*mock.Call
}

// CSA is a helper method to define mock.On call
func (_e *Master_Expecter) CSA() *Master_CSA_Call {
	return &Master_CSA_Call{Call: _e.mock.On("CSA")}
}

func (_c *Master_CSA_Call) Run(run func()) *Master_CSA_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_CSA_Call) Return(_a0 keystore.CSA) *Master_CSA_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_CSA_Call) RunAndReturn(run func() keystore.CSA) *Master_CSA_Call {
	_c.Call.Return(run)
	return _c
}

// Cosmos provides a mock function with given fields:
func (_m *Master) Cosmos() keystore.Cosmos {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Cosmos")
	}

	var r0 keystore.Cosmos
	if rf, ok := ret.Get(0).(func() keystore.Cosmos); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.Cosmos)
		}
	}

	return r0
}

// Master_Cosmos_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Cosmos'
type Master_Cosmos_Call struct {
	*mock.Call
}

// Cosmos is a helper method to define mock.On call
func (_e *Master_Expecter) Cosmos() *Master_Cosmos_Call {
	return &Master_Cosmos_Call{Call: _e.mock.On("Cosmos")}
}

func (_c *Master_Cosmos_Call) Run(run func()) *Master_Cosmos_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_Cosmos_Call) Return(_a0 keystore.Cosmos) *Master_Cosmos_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_Cosmos_Call) RunAndReturn(run func() keystore.Cosmos) *Master_Cosmos_Call {
	_c.Call.Return(run)
	return _c
}

// Eth provides a mock function with given fields:
func (_m *Master) Eth() keystore.Eth {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Eth")
	}

	var r0 keystore.Eth
	if rf, ok := ret.Get(0).(func() keystore.Eth); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.Eth)
		}
	}

	return r0
}

// Master_Eth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Eth'
type Master_Eth_Call struct {
	*mock.Call
}

// Eth is a helper method to define mock.On call
func (_e *Master_Expecter) Eth() *Master_Eth_Call {
	return &Master_Eth_Call{Call: _e.mock.On("Eth")}
}

func (_c *Master_Eth_Call) Run(run func()) *Master_Eth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_Eth_Call) Return(_a0 keystore.Eth) *Master_Eth_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_Eth_Call) RunAndReturn(run func() keystore.Eth) *Master_Eth_Call {
	_c.Call.Return(run)
	return _c
}

// IsEmpty provides a mock function with given fields: ctx
func (_m *Master) IsEmpty(ctx context.Context) (bool, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for IsEmpty")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (bool, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Master_IsEmpty_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsEmpty'
type Master_IsEmpty_Call struct {
	*mock.Call
}

// IsEmpty is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Master_Expecter) IsEmpty(ctx interface{}) *Master_IsEmpty_Call {
	return &Master_IsEmpty_Call{Call: _e.mock.On("IsEmpty", ctx)}
}

func (_c *Master_IsEmpty_Call) Run(run func(ctx context.Context)) *Master_IsEmpty_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Master_IsEmpty_Call) Return(_a0 bool, _a1 error) *Master_IsEmpty_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Master_IsEmpty_Call) RunAndReturn(run func(context.Context) (bool, error)) *Master_IsEmpty_Call {
	_c.Call.Return(run)
	return _c
}

// OCR provides a mock function with given fields:
func (_m *Master) OCR() keystore.OCR {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for OCR")
	}

	var r0 keystore.OCR
	if rf, ok := ret.Get(0).(func() keystore.OCR); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.OCR)
		}
	}

	return r0
}

// Master_OCR_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OCR'
type Master_OCR_Call struct {
	*mock.Call
}

// OCR is a helper method to define mock.On call
func (_e *Master_Expecter) OCR() *Master_OCR_Call {
	return &Master_OCR_Call{Call: _e.mock.On("OCR")}
}

func (_c *Master_OCR_Call) Run(run func()) *Master_OCR_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_OCR_Call) Return(_a0 keystore.OCR) *Master_OCR_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_OCR_Call) RunAndReturn(run func() keystore.OCR) *Master_OCR_Call {
	_c.Call.Return(run)
	return _c
}

// OCR2 provides a mock function with given fields:
func (_m *Master) OCR2() keystore.OCR2 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for OCR2")
	}

	var r0 keystore.OCR2
	if rf, ok := ret.Get(0).(func() keystore.OCR2); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.OCR2)
		}
	}

	return r0
}

// Master_OCR2_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OCR2'
type Master_OCR2_Call struct {
	*mock.Call
}

// OCR2 is a helper method to define mock.On call
func (_e *Master_Expecter) OCR2() *Master_OCR2_Call {
	return &Master_OCR2_Call{Call: _e.mock.On("OCR2")}
}

func (_c *Master_OCR2_Call) Run(run func()) *Master_OCR2_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_OCR2_Call) Return(_a0 keystore.OCR2) *Master_OCR2_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_OCR2_Call) RunAndReturn(run func() keystore.OCR2) *Master_OCR2_Call {
	_c.Call.Return(run)
	return _c
}

// P2P provides a mock function with given fields:
func (_m *Master) P2P() keystore.P2P {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for P2P")
	}

	var r0 keystore.P2P
	if rf, ok := ret.Get(0).(func() keystore.P2P); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.P2P)
		}
	}

	return r0
}

// Master_P2P_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'P2P'
type Master_P2P_Call struct {
	*mock.Call
}

// P2P is a helper method to define mock.On call
func (_e *Master_Expecter) P2P() *Master_P2P_Call {
	return &Master_P2P_Call{Call: _e.mock.On("P2P")}
}

func (_c *Master_P2P_Call) Run(run func()) *Master_P2P_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_P2P_Call) Return(_a0 keystore.P2P) *Master_P2P_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_P2P_Call) RunAndReturn(run func() keystore.P2P) *Master_P2P_Call {
	_c.Call.Return(run)
	return _c
}

// Solana provides a mock function with given fields:
func (_m *Master) Solana() keystore.Solana {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Solana")
	}

	var r0 keystore.Solana
	if rf, ok := ret.Get(0).(func() keystore.Solana); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.Solana)
		}
	}

	return r0
}

// Master_Solana_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Solana'
type Master_Solana_Call struct {
	*mock.Call
}

// Solana is a helper method to define mock.On call
func (_e *Master_Expecter) Solana() *Master_Solana_Call {
	return &Master_Solana_Call{Call: _e.mock.On("Solana")}
}

func (_c *Master_Solana_Call) Run(run func()) *Master_Solana_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_Solana_Call) Return(_a0 keystore.Solana) *Master_Solana_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_Solana_Call) RunAndReturn(run func() keystore.Solana) *Master_Solana_Call {
	_c.Call.Return(run)
	return _c
}

// StarkNet provides a mock function with given fields:
func (_m *Master) StarkNet() keystore.StarkNet {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for StarkNet")
	}

	var r0 keystore.StarkNet
	if rf, ok := ret.Get(0).(func() keystore.StarkNet); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.StarkNet)
		}
	}

	return r0
}

// Master_StarkNet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StarkNet'
type Master_StarkNet_Call struct {
	*mock.Call
}

// StarkNet is a helper method to define mock.On call
func (_e *Master_Expecter) StarkNet() *Master_StarkNet_Call {
	return &Master_StarkNet_Call{Call: _e.mock.On("StarkNet")}
}

func (_c *Master_StarkNet_Call) Run(run func()) *Master_StarkNet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_StarkNet_Call) Return(_a0 keystore.StarkNet) *Master_StarkNet_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_StarkNet_Call) RunAndReturn(run func() keystore.StarkNet) *Master_StarkNet_Call {
	_c.Call.Return(run)
	return _c
}

// Unlock provides a mock function with given fields: ctx, password
func (_m *Master) Unlock(ctx context.Context, password string) error {
	ret := _m.Called(ctx, password)

	if len(ret) == 0 {
		panic("no return value specified for Unlock")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, password)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Master_Unlock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Unlock'
type Master_Unlock_Call struct {
	*mock.Call
}

// Unlock is a helper method to define mock.On call
//   - ctx context.Context
//   - password string
func (_e *Master_Expecter) Unlock(ctx interface{}, password interface{}) *Master_Unlock_Call {
	return &Master_Unlock_Call{Call: _e.mock.On("Unlock", ctx, password)}
}

func (_c *Master_Unlock_Call) Run(run func(ctx context.Context, password string)) *Master_Unlock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *Master_Unlock_Call) Return(_a0 error) *Master_Unlock_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_Unlock_Call) RunAndReturn(run func(context.Context, string) error) *Master_Unlock_Call {
	_c.Call.Return(run)
	return _c
}

// VRF provides a mock function with given fields:
func (_m *Master) VRF() keystore.VRF {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for VRF")
	}

	var r0 keystore.VRF
	if rf, ok := ret.Get(0).(func() keystore.VRF); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(keystore.VRF)
		}
	}

	return r0
}

// Master_VRF_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'VRF'
type Master_VRF_Call struct {
	*mock.Call
}

// VRF is a helper method to define mock.On call
func (_e *Master_Expecter) VRF() *Master_VRF_Call {
	return &Master_VRF_Call{Call: _e.mock.On("VRF")}
}

func (_c *Master_VRF_Call) Run(run func()) *Master_VRF_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Master_VRF_Call) Return(_a0 keystore.VRF) *Master_VRF_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Master_VRF_Call) RunAndReturn(run func() keystore.VRF) *Master_VRF_Call {
	_c.Call.Return(run)
	return _c
}

// NewMaster creates a new instance of Master. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMaster(t interface {
	mock.TestingT
	Cleanup(func())
}) *Master {
	mock := &Master{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
