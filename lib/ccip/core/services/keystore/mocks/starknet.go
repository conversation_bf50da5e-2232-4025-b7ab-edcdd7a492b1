// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	starkkey "github.com/smartcontractkit/chainlink/v2/core/services/keystore/keys/starkkey"
)

// StarkNet is an autogenerated mock type for the StarkNet type
type StarkNet struct {
	mock.Mock
}

type StarkNet_Expecter struct {
	mock *mock.Mock
}

func (_m *StarkNet) EXPECT() *StarkNet_Expecter {
	return &StarkNet_Expecter{mock: &_m.Mock}
}

// Add provides a mock function with given fields: ctx, key
func (_m *StarkNet) Add(ctx context.Context, key starkkey.Key) error {
	ret := _m.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for Add")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, starkkey.Key) error); ok {
		r0 = rf(ctx, key)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StarkNet_Add_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Add'
type StarkNet_Add_Call struct {
	*mock.Call
}

// Add is a helper method to define mock.On call
//   - ctx context.Context
//   - key starkkey.Key
func (_e *StarkNet_Expecter) Add(ctx interface{}, key interface{}) *StarkNet_Add_Call {
	return &StarkNet_Add_Call{Call: _e.mock.On("Add", ctx, key)}
}

func (_c *StarkNet_Add_Call) Run(run func(ctx context.Context, key starkkey.Key)) *StarkNet_Add_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(starkkey.Key))
	})
	return _c
}

func (_c *StarkNet_Add_Call) Return(_a0 error) *StarkNet_Add_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *StarkNet_Add_Call) RunAndReturn(run func(context.Context, starkkey.Key) error) *StarkNet_Add_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: ctx
func (_m *StarkNet) Create(ctx context.Context) (starkkey.Key, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 starkkey.Key
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (starkkey.Key, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) starkkey.Key); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(starkkey.Key)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StarkNet_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type StarkNet_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - ctx context.Context
func (_e *StarkNet_Expecter) Create(ctx interface{}) *StarkNet_Create_Call {
	return &StarkNet_Create_Call{Call: _e.mock.On("Create", ctx)}
}

func (_c *StarkNet_Create_Call) Run(run func(ctx context.Context)) *StarkNet_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *StarkNet_Create_Call) Return(_a0 starkkey.Key, _a1 error) *StarkNet_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *StarkNet_Create_Call) RunAndReturn(run func(context.Context) (starkkey.Key, error)) *StarkNet_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, id
func (_m *StarkNet) Delete(ctx context.Context, id string) (starkkey.Key, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 starkkey.Key
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (starkkey.Key, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) starkkey.Key); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Get(0).(starkkey.Key)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StarkNet_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type StarkNet_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *StarkNet_Expecter) Delete(ctx interface{}, id interface{}) *StarkNet_Delete_Call {
	return &StarkNet_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *StarkNet_Delete_Call) Run(run func(ctx context.Context, id string)) *StarkNet_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *StarkNet_Delete_Call) Return(_a0 starkkey.Key, _a1 error) *StarkNet_Delete_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *StarkNet_Delete_Call) RunAndReturn(run func(context.Context, string) (starkkey.Key, error)) *StarkNet_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// EnsureKey provides a mock function with given fields: ctx
func (_m *StarkNet) EnsureKey(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for EnsureKey")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StarkNet_EnsureKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EnsureKey'
type StarkNet_EnsureKey_Call struct {
	*mock.Call
}

// EnsureKey is a helper method to define mock.On call
//   - ctx context.Context
func (_e *StarkNet_Expecter) EnsureKey(ctx interface{}) *StarkNet_EnsureKey_Call {
	return &StarkNet_EnsureKey_Call{Call: _e.mock.On("EnsureKey", ctx)}
}

func (_c *StarkNet_EnsureKey_Call) Run(run func(ctx context.Context)) *StarkNet_EnsureKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *StarkNet_EnsureKey_Call) Return(_a0 error) *StarkNet_EnsureKey_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *StarkNet_EnsureKey_Call) RunAndReturn(run func(context.Context) error) *StarkNet_EnsureKey_Call {
	_c.Call.Return(run)
	return _c
}

// Export provides a mock function with given fields: id, password
func (_m *StarkNet) Export(id string, password string) ([]byte, error) {
	ret := _m.Called(id, password)

	if len(ret) == 0 {
		panic("no return value specified for Export")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) ([]byte, error)); ok {
		return rf(id, password)
	}
	if rf, ok := ret.Get(0).(func(string, string) []byte); ok {
		r0 = rf(id, password)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(id, password)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StarkNet_Export_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Export'
type StarkNet_Export_Call struct {
	*mock.Call
}

// Export is a helper method to define mock.On call
//   - id string
//   - password string
func (_e *StarkNet_Expecter) Export(id interface{}, password interface{}) *StarkNet_Export_Call {
	return &StarkNet_Export_Call{Call: _e.mock.On("Export", id, password)}
}

func (_c *StarkNet_Export_Call) Run(run func(id string, password string)) *StarkNet_Export_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *StarkNet_Export_Call) Return(_a0 []byte, _a1 error) *StarkNet_Export_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *StarkNet_Export_Call) RunAndReturn(run func(string, string) ([]byte, error)) *StarkNet_Export_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *StarkNet) Get(id string) (starkkey.Key, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 starkkey.Key
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (starkkey.Key, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(string) starkkey.Key); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Get(0).(starkkey.Key)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StarkNet_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type StarkNet_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id string
func (_e *StarkNet_Expecter) Get(id interface{}) *StarkNet_Get_Call {
	return &StarkNet_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *StarkNet_Get_Call) Run(run func(id string)) *StarkNet_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *StarkNet_Get_Call) Return(_a0 starkkey.Key, _a1 error) *StarkNet_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *StarkNet_Get_Call) RunAndReturn(run func(string) (starkkey.Key, error)) *StarkNet_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetAll provides a mock function with given fields:
func (_m *StarkNet) GetAll() ([]starkkey.Key, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []starkkey.Key
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]starkkey.Key, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []starkkey.Key); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]starkkey.Key)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StarkNet_GetAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAll'
type StarkNet_GetAll_Call struct {
	*mock.Call
}

// GetAll is a helper method to define mock.On call
func (_e *StarkNet_Expecter) GetAll() *StarkNet_GetAll_Call {
	return &StarkNet_GetAll_Call{Call: _e.mock.On("GetAll")}
}

func (_c *StarkNet_GetAll_Call) Run(run func()) *StarkNet_GetAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *StarkNet_GetAll_Call) Return(_a0 []starkkey.Key, _a1 error) *StarkNet_GetAll_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *StarkNet_GetAll_Call) RunAndReturn(run func() ([]starkkey.Key, error)) *StarkNet_GetAll_Call {
	_c.Call.Return(run)
	return _c
}

// Import provides a mock function with given fields: ctx, keyJSON, password
func (_m *StarkNet) Import(ctx context.Context, keyJSON []byte, password string) (starkkey.Key, error) {
	ret := _m.Called(ctx, keyJSON, password)

	if len(ret) == 0 {
		panic("no return value specified for Import")
	}

	var r0 starkkey.Key
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []byte, string) (starkkey.Key, error)); ok {
		return rf(ctx, keyJSON, password)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []byte, string) starkkey.Key); ok {
		r0 = rf(ctx, keyJSON, password)
	} else {
		r0 = ret.Get(0).(starkkey.Key)
	}

	if rf, ok := ret.Get(1).(func(context.Context, []byte, string) error); ok {
		r1 = rf(ctx, keyJSON, password)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StarkNet_Import_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Import'
type StarkNet_Import_Call struct {
	*mock.Call
}

// Import is a helper method to define mock.On call
//   - ctx context.Context
//   - keyJSON []byte
//   - password string
func (_e *StarkNet_Expecter) Import(ctx interface{}, keyJSON interface{}, password interface{}) *StarkNet_Import_Call {
	return &StarkNet_Import_Call{Call: _e.mock.On("Import", ctx, keyJSON, password)}
}

func (_c *StarkNet_Import_Call) Run(run func(ctx context.Context, keyJSON []byte, password string)) *StarkNet_Import_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte), args[2].(string))
	})
	return _c
}

func (_c *StarkNet_Import_Call) Return(_a0 starkkey.Key, _a1 error) *StarkNet_Import_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *StarkNet_Import_Call) RunAndReturn(run func(context.Context, []byte, string) (starkkey.Key, error)) *StarkNet_Import_Call {
	_c.Call.Return(run)
	return _c
}

// NewStarkNet creates a new instance of StarkNet. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStarkNet(t interface {
	mock.TestingT
	Cleanup(func())
}) *StarkNet {
	mock := &StarkNet{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
