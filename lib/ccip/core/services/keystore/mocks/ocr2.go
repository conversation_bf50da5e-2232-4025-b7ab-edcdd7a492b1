// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	chaintype "github.com/smartcontractkit/chainlink/v2/core/services/keystore/chaintype"

	mock "github.com/stretchr/testify/mock"

	ocr2key "github.com/smartcontractkit/chainlink/v2/core/services/keystore/keys/ocr2key"
)

// OCR2 is an autogenerated mock type for the OCR2 type
type OCR2 struct {
	mock.Mock
}

type OCR2_Expecter struct {
	mock *mock.Mock
}

func (_m *OCR2) EXPECT() *OCR2_Expecter {
	return &OCR2_Expecter{mock: &_m.Mock}
}

// Add provides a mock function with given fields: ctx, key
func (_m *OCR2) Add(ctx context.Context, key ocr2key.KeyBundle) error {
	ret := _m.Called(ctx, key)

	if len(ret) == 0 {
		panic("no return value specified for Add")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, ocr2key.KeyBundle) error); ok {
		r0 = rf(ctx, key)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OCR2_Add_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Add'
type OCR2_Add_Call struct {
	*mock.Call
}

// Add is a helper method to define mock.On call
//   - ctx context.Context
//   - key ocr2key.KeyBundle
func (_e *OCR2_Expecter) Add(ctx interface{}, key interface{}) *OCR2_Add_Call {
	return &OCR2_Add_Call{Call: _e.mock.On("Add", ctx, key)}
}

func (_c *OCR2_Add_Call) Run(run func(ctx context.Context, key ocr2key.KeyBundle)) *OCR2_Add_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ocr2key.KeyBundle))
	})
	return _c
}

func (_c *OCR2_Add_Call) Return(_a0 error) *OCR2_Add_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OCR2_Add_Call) RunAndReturn(run func(context.Context, ocr2key.KeyBundle) error) *OCR2_Add_Call {
	_c.Call.Return(run)
	return _c
}

// Create provides a mock function with given fields: _a0, _a1
func (_m *OCR2) Create(_a0 context.Context, _a1 chaintype.ChainType) (ocr2key.KeyBundle, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 ocr2key.KeyBundle
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, chaintype.ChainType) (ocr2key.KeyBundle, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, chaintype.ChainType) ocr2key.KeyBundle); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ocr2key.KeyBundle)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, chaintype.ChainType) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OCR2_Create_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Create'
type OCR2_Create_Call struct {
	*mock.Call
}

// Create is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 chaintype.ChainType
func (_e *OCR2_Expecter) Create(_a0 interface{}, _a1 interface{}) *OCR2_Create_Call {
	return &OCR2_Create_Call{Call: _e.mock.On("Create", _a0, _a1)}
}

func (_c *OCR2_Create_Call) Run(run func(_a0 context.Context, _a1 chaintype.ChainType)) *OCR2_Create_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(chaintype.ChainType))
	})
	return _c
}

func (_c *OCR2_Create_Call) Return(_a0 ocr2key.KeyBundle, _a1 error) *OCR2_Create_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OCR2_Create_Call) RunAndReturn(run func(context.Context, chaintype.ChainType) (ocr2key.KeyBundle, error)) *OCR2_Create_Call {
	_c.Call.Return(run)
	return _c
}

// Delete provides a mock function with given fields: ctx, id
func (_m *OCR2) Delete(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for Delete")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OCR2_Delete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Delete'
type OCR2_Delete_Call struct {
	*mock.Call
}

// Delete is a helper method to define mock.On call
//   - ctx context.Context
//   - id string
func (_e *OCR2_Expecter) Delete(ctx interface{}, id interface{}) *OCR2_Delete_Call {
	return &OCR2_Delete_Call{Call: _e.mock.On("Delete", ctx, id)}
}

func (_c *OCR2_Delete_Call) Run(run func(ctx context.Context, id string)) *OCR2_Delete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *OCR2_Delete_Call) Return(_a0 error) *OCR2_Delete_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OCR2_Delete_Call) RunAndReturn(run func(context.Context, string) error) *OCR2_Delete_Call {
	_c.Call.Return(run)
	return _c
}

// EnsureKeys provides a mock function with given fields: ctx, enabledChains
func (_m *OCR2) EnsureKeys(ctx context.Context, enabledChains ...chaintype.ChainType) error {
	_va := make([]interface{}, len(enabledChains))
	for _i := range enabledChains {
		_va[_i] = enabledChains[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for EnsureKeys")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, ...chaintype.ChainType) error); ok {
		r0 = rf(ctx, enabledChains...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OCR2_EnsureKeys_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EnsureKeys'
type OCR2_EnsureKeys_Call struct {
	*mock.Call
}

// EnsureKeys is a helper method to define mock.On call
//   - ctx context.Context
//   - enabledChains ...chaintype.ChainType
func (_e *OCR2_Expecter) EnsureKeys(ctx interface{}, enabledChains ...interface{}) *OCR2_EnsureKeys_Call {
	return &OCR2_EnsureKeys_Call{Call: _e.mock.On("EnsureKeys",
		append([]interface{}{ctx}, enabledChains...)...)}
}

func (_c *OCR2_EnsureKeys_Call) Run(run func(ctx context.Context, enabledChains ...chaintype.ChainType)) *OCR2_EnsureKeys_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]chaintype.ChainType, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(chaintype.ChainType)
			}
		}
		run(args[0].(context.Context), variadicArgs...)
	})
	return _c
}

func (_c *OCR2_EnsureKeys_Call) Return(_a0 error) *OCR2_EnsureKeys_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OCR2_EnsureKeys_Call) RunAndReturn(run func(context.Context, ...chaintype.ChainType) error) *OCR2_EnsureKeys_Call {
	_c.Call.Return(run)
	return _c
}

// Export provides a mock function with given fields: id, password
func (_m *OCR2) Export(id string, password string) ([]byte, error) {
	ret := _m.Called(id, password)

	if len(ret) == 0 {
		panic("no return value specified for Export")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) ([]byte, error)); ok {
		return rf(id, password)
	}
	if rf, ok := ret.Get(0).(func(string, string) []byte); ok {
		r0 = rf(id, password)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(id, password)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OCR2_Export_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Export'
type OCR2_Export_Call struct {
	*mock.Call
}

// Export is a helper method to define mock.On call
//   - id string
//   - password string
func (_e *OCR2_Expecter) Export(id interface{}, password interface{}) *OCR2_Export_Call {
	return &OCR2_Export_Call{Call: _e.mock.On("Export", id, password)}
}

func (_c *OCR2_Export_Call) Run(run func(id string, password string)) *OCR2_Export_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *OCR2_Export_Call) Return(_a0 []byte, _a1 error) *OCR2_Export_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OCR2_Export_Call) RunAndReturn(run func(string, string) ([]byte, error)) *OCR2_Export_Call {
	_c.Call.Return(run)
	return _c
}

// Get provides a mock function with given fields: id
func (_m *OCR2) Get(id string) (ocr2key.KeyBundle, error) {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 ocr2key.KeyBundle
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (ocr2key.KeyBundle, error)); ok {
		return rf(id)
	}
	if rf, ok := ret.Get(0).(func(string) ocr2key.KeyBundle); ok {
		r0 = rf(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ocr2key.KeyBundle)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OCR2_Get_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Get'
type OCR2_Get_Call struct {
	*mock.Call
}

// Get is a helper method to define mock.On call
//   - id string
func (_e *OCR2_Expecter) Get(id interface{}) *OCR2_Get_Call {
	return &OCR2_Get_Call{Call: _e.mock.On("Get", id)}
}

func (_c *OCR2_Get_Call) Run(run func(id string)) *OCR2_Get_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *OCR2_Get_Call) Return(_a0 ocr2key.KeyBundle, _a1 error) *OCR2_Get_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OCR2_Get_Call) RunAndReturn(run func(string) (ocr2key.KeyBundle, error)) *OCR2_Get_Call {
	_c.Call.Return(run)
	return _c
}

// GetAll provides a mock function with given fields:
func (_m *OCR2) GetAll() ([]ocr2key.KeyBundle, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAll")
	}

	var r0 []ocr2key.KeyBundle
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]ocr2key.KeyBundle, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []ocr2key.KeyBundle); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ocr2key.KeyBundle)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OCR2_GetAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAll'
type OCR2_GetAll_Call struct {
	*mock.Call
}

// GetAll is a helper method to define mock.On call
func (_e *OCR2_Expecter) GetAll() *OCR2_GetAll_Call {
	return &OCR2_GetAll_Call{Call: _e.mock.On("GetAll")}
}

func (_c *OCR2_GetAll_Call) Run(run func()) *OCR2_GetAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OCR2_GetAll_Call) Return(_a0 []ocr2key.KeyBundle, _a1 error) *OCR2_GetAll_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OCR2_GetAll_Call) RunAndReturn(run func() ([]ocr2key.KeyBundle, error)) *OCR2_GetAll_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllOfType provides a mock function with given fields: _a0
func (_m *OCR2) GetAllOfType(_a0 chaintype.ChainType) ([]ocr2key.KeyBundle, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for GetAllOfType")
	}

	var r0 []ocr2key.KeyBundle
	var r1 error
	if rf, ok := ret.Get(0).(func(chaintype.ChainType) ([]ocr2key.KeyBundle, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(chaintype.ChainType) []ocr2key.KeyBundle); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]ocr2key.KeyBundle)
		}
	}

	if rf, ok := ret.Get(1).(func(chaintype.ChainType) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OCR2_GetAllOfType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllOfType'
type OCR2_GetAllOfType_Call struct {
	*mock.Call
}

// GetAllOfType is a helper method to define mock.On call
//   - _a0 chaintype.ChainType
func (_e *OCR2_Expecter) GetAllOfType(_a0 interface{}) *OCR2_GetAllOfType_Call {
	return &OCR2_GetAllOfType_Call{Call: _e.mock.On("GetAllOfType", _a0)}
}

func (_c *OCR2_GetAllOfType_Call) Run(run func(_a0 chaintype.ChainType)) *OCR2_GetAllOfType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(chaintype.ChainType))
	})
	return _c
}

func (_c *OCR2_GetAllOfType_Call) Return(_a0 []ocr2key.KeyBundle, _a1 error) *OCR2_GetAllOfType_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OCR2_GetAllOfType_Call) RunAndReturn(run func(chaintype.ChainType) ([]ocr2key.KeyBundle, error)) *OCR2_GetAllOfType_Call {
	_c.Call.Return(run)
	return _c
}

// Import provides a mock function with given fields: ctx, keyJSON, password
func (_m *OCR2) Import(ctx context.Context, keyJSON []byte, password string) (ocr2key.KeyBundle, error) {
	ret := _m.Called(ctx, keyJSON, password)

	if len(ret) == 0 {
		panic("no return value specified for Import")
	}

	var r0 ocr2key.KeyBundle
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []byte, string) (ocr2key.KeyBundle, error)); ok {
		return rf(ctx, keyJSON, password)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []byte, string) ocr2key.KeyBundle); ok {
		r0 = rf(ctx, keyJSON, password)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ocr2key.KeyBundle)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []byte, string) error); ok {
		r1 = rf(ctx, keyJSON, password)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OCR2_Import_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Import'
type OCR2_Import_Call struct {
	*mock.Call
}

// Import is a helper method to define mock.On call
//   - ctx context.Context
//   - keyJSON []byte
//   - password string
func (_e *OCR2_Expecter) Import(ctx interface{}, keyJSON interface{}, password interface{}) *OCR2_Import_Call {
	return &OCR2_Import_Call{Call: _e.mock.On("Import", ctx, keyJSON, password)}
}

func (_c *OCR2_Import_Call) Run(run func(ctx context.Context, keyJSON []byte, password string)) *OCR2_Import_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte), args[2].(string))
	})
	return _c
}

func (_c *OCR2_Import_Call) Return(_a0 ocr2key.KeyBundle, _a1 error) *OCR2_Import_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OCR2_Import_Call) RunAndReturn(run func(context.Context, []byte, string) (ocr2key.KeyBundle, error)) *OCR2_Import_Call {
	_c.Call.Return(run)
	return _c
}

// NewOCR2 creates a new instance of OCR2. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOCR2(t interface {
	mock.TestingT
	Cleanup(func())
}) *OCR2 {
	mock := &OCR2{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
