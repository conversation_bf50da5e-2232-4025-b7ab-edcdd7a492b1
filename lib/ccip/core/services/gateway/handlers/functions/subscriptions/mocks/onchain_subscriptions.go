// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"
	big "math/big"

	common "github.com/ethereum/go-ethereum/common"

	mock "github.com/stretchr/testify/mock"
)

// OnchainSubscriptions is an autogenerated mock type for the OnchainSubscriptions type
type OnchainSubscriptions struct {
	mock.Mock
}

type OnchainSubscriptions_Expecter struct {
	mock *mock.Mock
}

func (_m *OnchainSubscriptions) EXPECT() *OnchainSubscriptions_Expecter {
	return &OnchainSubscriptions_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with given fields:
func (_m *OnchainSubscriptions) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OnchainSubscriptions_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type OnchainSubscriptions_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *OnchainSubscriptions_Expecter) Close() *OnchainSubscriptions_Close_Call {
	return &OnchainSubscriptions_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *OnchainSubscriptions_Close_Call) Run(run func()) *OnchainSubscriptions_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OnchainSubscriptions_Close_Call) Return(_a0 error) *OnchainSubscriptions_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OnchainSubscriptions_Close_Call) RunAndReturn(run func() error) *OnchainSubscriptions_Close_Call {
	_c.Call.Return(run)
	return _c
}

// GetMaxUserBalance provides a mock function with given fields: _a0
func (_m *OnchainSubscriptions) GetMaxUserBalance(_a0 common.Address) (*big.Int, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for GetMaxUserBalance")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(common.Address) (*big.Int, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(common.Address) *big.Int); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(common.Address) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OnchainSubscriptions_GetMaxUserBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMaxUserBalance'
type OnchainSubscriptions_GetMaxUserBalance_Call struct {
	*mock.Call
}

// GetMaxUserBalance is a helper method to define mock.On call
//   - _a0 common.Address
func (_e *OnchainSubscriptions_Expecter) GetMaxUserBalance(_a0 interface{}) *OnchainSubscriptions_GetMaxUserBalance_Call {
	return &OnchainSubscriptions_GetMaxUserBalance_Call{Call: _e.mock.On("GetMaxUserBalance", _a0)}
}

func (_c *OnchainSubscriptions_GetMaxUserBalance_Call) Run(run func(_a0 common.Address)) *OnchainSubscriptions_GetMaxUserBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(common.Address))
	})
	return _c
}

func (_c *OnchainSubscriptions_GetMaxUserBalance_Call) Return(_a0 *big.Int, _a1 error) *OnchainSubscriptions_GetMaxUserBalance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OnchainSubscriptions_GetMaxUserBalance_Call) RunAndReturn(run func(common.Address) (*big.Int, error)) *OnchainSubscriptions_GetMaxUserBalance_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *OnchainSubscriptions) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OnchainSubscriptions_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type OnchainSubscriptions_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *OnchainSubscriptions_Expecter) Start(_a0 interface{}) *OnchainSubscriptions_Start_Call {
	return &OnchainSubscriptions_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *OnchainSubscriptions_Start_Call) Run(run func(_a0 context.Context)) *OnchainSubscriptions_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *OnchainSubscriptions_Start_Call) Return(_a0 error) *OnchainSubscriptions_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OnchainSubscriptions_Start_Call) RunAndReturn(run func(context.Context) error) *OnchainSubscriptions_Start_Call {
	_c.Call.Return(run)
	return _c
}

// NewOnchainSubscriptions creates a new instance of OnchainSubscriptions. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOnchainSubscriptions(t interface {
	mock.TestingT
	Cleanup(func())
}) *OnchainSubscriptions {
	mock := &OnchainSubscriptions{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
