// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	common "github.com/ethereum/go-ethereum/common"

	mock "github.com/stretchr/testify/mock"
)

// OnchainAllowlist is an autogenerated mock type for the OnchainAllowlist type
type OnchainAllowlist struct {
	mock.Mock
}

type OnchainAllowlist_Expecter struct {
	mock *mock.Mock
}

func (_m *OnchainAllowlist) EXPECT() *OnchainAllowlist_Expecter {
	return &OnchainAllowlist_Expecter{mock: &_m.Mock}
}

// Allow provides a mock function with given fields: _a0
func (_m *OnchainAllowlist) Allow(_a0 common.Address) bool {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Allow")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(common.Address) bool); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// OnchainAllowlist_Allow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Allow'
type OnchainAllowlist_Allow_Call struct {
	*mock.Call
}

// Allow is a helper method to define mock.On call
//   - _a0 common.Address
func (_e *OnchainAllowlist_Expecter) Allow(_a0 interface{}) *OnchainAllowlist_Allow_Call {
	return &OnchainAllowlist_Allow_Call{Call: _e.mock.On("Allow", _a0)}
}

func (_c *OnchainAllowlist_Allow_Call) Run(run func(_a0 common.Address)) *OnchainAllowlist_Allow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(common.Address))
	})
	return _c
}

func (_c *OnchainAllowlist_Allow_Call) Return(_a0 bool) *OnchainAllowlist_Allow_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OnchainAllowlist_Allow_Call) RunAndReturn(run func(common.Address) bool) *OnchainAllowlist_Allow_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *OnchainAllowlist) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OnchainAllowlist_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type OnchainAllowlist_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *OnchainAllowlist_Expecter) Close() *OnchainAllowlist_Close_Call {
	return &OnchainAllowlist_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *OnchainAllowlist_Close_Call) Run(run func()) *OnchainAllowlist_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OnchainAllowlist_Close_Call) Return(_a0 error) *OnchainAllowlist_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OnchainAllowlist_Close_Call) RunAndReturn(run func() error) *OnchainAllowlist_Close_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *OnchainAllowlist) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OnchainAllowlist_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type OnchainAllowlist_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *OnchainAllowlist_Expecter) Start(_a0 interface{}) *OnchainAllowlist_Start_Call {
	return &OnchainAllowlist_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *OnchainAllowlist_Start_Call) Run(run func(_a0 context.Context)) *OnchainAllowlist_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *OnchainAllowlist_Start_Call) Return(_a0 error) *OnchainAllowlist_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OnchainAllowlist_Start_Call) RunAndReturn(run func(context.Context) error) *OnchainAllowlist_Start_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateFromContract provides a mock function with given fields: ctx
func (_m *OnchainAllowlist) UpdateFromContract(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for UpdateFromContract")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// OnchainAllowlist_UpdateFromContract_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateFromContract'
type OnchainAllowlist_UpdateFromContract_Call struct {
	*mock.Call
}

// UpdateFromContract is a helper method to define mock.On call
//   - ctx context.Context
func (_e *OnchainAllowlist_Expecter) UpdateFromContract(ctx interface{}) *OnchainAllowlist_UpdateFromContract_Call {
	return &OnchainAllowlist_UpdateFromContract_Call{Call: _e.mock.On("UpdateFromContract", ctx)}
}

func (_c *OnchainAllowlist_UpdateFromContract_Call) Run(run func(ctx context.Context)) *OnchainAllowlist_UpdateFromContract_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *OnchainAllowlist_UpdateFromContract_Call) Return(_a0 error) *OnchainAllowlist_UpdateFromContract_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OnchainAllowlist_UpdateFromContract_Call) RunAndReturn(run func(context.Context) error) *OnchainAllowlist_UpdateFromContract_Call {
	_c.Call.Return(run)
	return _c
}

// NewOnchainAllowlist creates a new instance of OnchainAllowlist. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOnchainAllowlist(t interface {
	mock.TestingT
	Cleanup(func())
}) *OnchainAllowlist {
	mock := &OnchainAllowlist{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
