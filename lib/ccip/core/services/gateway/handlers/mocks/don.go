// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	api "github.com/smartcontractkit/chainlink/v2/core/services/gateway/api"

	mock "github.com/stretchr/testify/mock"
)

// DON is an autogenerated mock type for the DON type
type DON struct {
	mock.Mock
}

type DON_Expecter struct {
	mock *mock.Mock
}

func (_m *DON) EXPECT() *DON_Expecter {
	return &DON_Expecter{mock: &_m.Mock}
}

// SendToNode provides a mock function with given fields: ctx, nodeAddress, msg
func (_m *DON) SendToNode(ctx context.Context, nodeAddress string, msg *api.Message) error {
	ret := _m.Called(ctx, nodeAddress, msg)

	if len(ret) == 0 {
		panic("no return value specified for SendToNode")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *api.Message) error); ok {
		r0 = rf(ctx, nodeAddress, msg)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DON_SendToNode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendToNode'
type DON_SendToNode_Call struct {
	*mock.Call
}

// SendToNode is a helper method to define mock.On call
//   - ctx context.Context
//   - nodeAddress string
//   - msg *api.Message
func (_e *DON_Expecter) SendToNode(ctx interface{}, nodeAddress interface{}, msg interface{}) *DON_SendToNode_Call {
	return &DON_SendToNode_Call{Call: _e.mock.On("SendToNode", ctx, nodeAddress, msg)}
}

func (_c *DON_SendToNode_Call) Run(run func(ctx context.Context, nodeAddress string, msg *api.Message)) *DON_SendToNode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*api.Message))
	})
	return _c
}

func (_c *DON_SendToNode_Call) Return(_a0 error) *DON_SendToNode_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *DON_SendToNode_Call) RunAndReturn(run func(context.Context, string, *api.Message) error) *DON_SendToNode_Call {
	_c.Call.Return(run)
	return _c
}

// NewDON creates a new instance of DON. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDON(t interface {
	mock.TestingT
	Cleanup(func())
}) *DON {
	mock := &DON{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
