// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	api "github.com/smartcontractkit/chainlink/v2/core/services/gateway/api"
	connector "github.com/smartcontractkit/chainlink/v2/core/services/gateway/connector"

	context "context"

	mock "github.com/stretchr/testify/mock"

	url "net/url"
)

// GatewayConnector is an autogenerated mock type for the GatewayConnector type
type GatewayConnector struct {
	mock.Mock
}

type GatewayConnector_Expecter struct {
	mock *mock.Mock
}

func (_m *GatewayConnector) EXPECT() *GatewayConnector_Expecter {
	return &GatewayConnector_Expecter{mock: &_m.Mock}
}

// AddHandler provides a mock function with given fields: methods, handler
func (_m *GatewayConnector) AddHandler(methods []string, handler connector.GatewayConnectorHandler) error {
	ret := _m.Called(methods, handler)

	if len(ret) == 0 {
		panic("no return value specified for AddHandler")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func([]string, connector.GatewayConnectorHandler) error); ok {
		r0 = rf(methods, handler)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GatewayConnector_AddHandler_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddHandler'
type GatewayConnector_AddHandler_Call struct {
	*mock.Call
}

// AddHandler is a helper method to define mock.On call
//   - methods []string
//   - handler connector.GatewayConnectorHandler
func (_e *GatewayConnector_Expecter) AddHandler(methods interface{}, handler interface{}) *GatewayConnector_AddHandler_Call {
	return &GatewayConnector_AddHandler_Call{Call: _e.mock.On("AddHandler", methods, handler)}
}

func (_c *GatewayConnector_AddHandler_Call) Run(run func(methods []string, handler connector.GatewayConnectorHandler)) *GatewayConnector_AddHandler_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]string), args[1].(connector.GatewayConnectorHandler))
	})
	return _c
}

func (_c *GatewayConnector_AddHandler_Call) Return(_a0 error) *GatewayConnector_AddHandler_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *GatewayConnector_AddHandler_Call) RunAndReturn(run func([]string, connector.GatewayConnectorHandler) error) *GatewayConnector_AddHandler_Call {
	_c.Call.Return(run)
	return _c
}

// ChallengeResponse provides a mock function with given fields: _a0, challenge
func (_m *GatewayConnector) ChallengeResponse(_a0 *url.URL, challenge []byte) ([]byte, error) {
	ret := _m.Called(_a0, challenge)

	if len(ret) == 0 {
		panic("no return value specified for ChallengeResponse")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(*url.URL, []byte) ([]byte, error)); ok {
		return rf(_a0, challenge)
	}
	if rf, ok := ret.Get(0).(func(*url.URL, []byte) []byte); ok {
		r0 = rf(_a0, challenge)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(*url.URL, []byte) error); ok {
		r1 = rf(_a0, challenge)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GatewayConnector_ChallengeResponse_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChallengeResponse'
type GatewayConnector_ChallengeResponse_Call struct {
	*mock.Call
}

// ChallengeResponse is a helper method to define mock.On call
//   - _a0 *url.URL
//   - challenge []byte
func (_e *GatewayConnector_Expecter) ChallengeResponse(_a0 interface{}, challenge interface{}) *GatewayConnector_ChallengeResponse_Call {
	return &GatewayConnector_ChallengeResponse_Call{Call: _e.mock.On("ChallengeResponse", _a0, challenge)}
}

func (_c *GatewayConnector_ChallengeResponse_Call) Run(run func(_a0 *url.URL, challenge []byte)) *GatewayConnector_ChallengeResponse_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*url.URL), args[1].([]byte))
	})
	return _c
}

func (_c *GatewayConnector_ChallengeResponse_Call) Return(_a0 []byte, _a1 error) *GatewayConnector_ChallengeResponse_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *GatewayConnector_ChallengeResponse_Call) RunAndReturn(run func(*url.URL, []byte) ([]byte, error)) *GatewayConnector_ChallengeResponse_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *GatewayConnector) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GatewayConnector_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type GatewayConnector_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *GatewayConnector_Expecter) Close() *GatewayConnector_Close_Call {
	return &GatewayConnector_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *GatewayConnector_Close_Call) Run(run func()) *GatewayConnector_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *GatewayConnector_Close_Call) Return(_a0 error) *GatewayConnector_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *GatewayConnector_Close_Call) RunAndReturn(run func() error) *GatewayConnector_Close_Call {
	_c.Call.Return(run)
	return _c
}

// NewAuthHeader provides a mock function with given fields: _a0
func (_m *GatewayConnector) NewAuthHeader(_a0 *url.URL) ([]byte, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for NewAuthHeader")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(*url.URL) ([]byte, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(*url.URL) []byte); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(*url.URL) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GatewayConnector_NewAuthHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewAuthHeader'
type GatewayConnector_NewAuthHeader_Call struct {
	*mock.Call
}

// NewAuthHeader is a helper method to define mock.On call
//   - _a0 *url.URL
func (_e *GatewayConnector_Expecter) NewAuthHeader(_a0 interface{}) *GatewayConnector_NewAuthHeader_Call {
	return &GatewayConnector_NewAuthHeader_Call{Call: _e.mock.On("NewAuthHeader", _a0)}
}

func (_c *GatewayConnector_NewAuthHeader_Call) Run(run func(_a0 *url.URL)) *GatewayConnector_NewAuthHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*url.URL))
	})
	return _c
}

func (_c *GatewayConnector_NewAuthHeader_Call) Return(_a0 []byte, _a1 error) *GatewayConnector_NewAuthHeader_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *GatewayConnector_NewAuthHeader_Call) RunAndReturn(run func(*url.URL) ([]byte, error)) *GatewayConnector_NewAuthHeader_Call {
	_c.Call.Return(run)
	return _c
}

// SendToGateway provides a mock function with given fields: ctx, gatewayId, msg
func (_m *GatewayConnector) SendToGateway(ctx context.Context, gatewayId string, msg *api.Message) error {
	ret := _m.Called(ctx, gatewayId, msg)

	if len(ret) == 0 {
		panic("no return value specified for SendToGateway")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *api.Message) error); ok {
		r0 = rf(ctx, gatewayId, msg)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GatewayConnector_SendToGateway_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendToGateway'
type GatewayConnector_SendToGateway_Call struct {
	*mock.Call
}

// SendToGateway is a helper method to define mock.On call
//   - ctx context.Context
//   - gatewayId string
//   - msg *api.Message
func (_e *GatewayConnector_Expecter) SendToGateway(ctx interface{}, gatewayId interface{}, msg interface{}) *GatewayConnector_SendToGateway_Call {
	return &GatewayConnector_SendToGateway_Call{Call: _e.mock.On("SendToGateway", ctx, gatewayId, msg)}
}

func (_c *GatewayConnector_SendToGateway_Call) Run(run func(ctx context.Context, gatewayId string, msg *api.Message)) *GatewayConnector_SendToGateway_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*api.Message))
	})
	return _c
}

func (_c *GatewayConnector_SendToGateway_Call) Return(_a0 error) *GatewayConnector_SendToGateway_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *GatewayConnector_SendToGateway_Call) RunAndReturn(run func(context.Context, string, *api.Message) error) *GatewayConnector_SendToGateway_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *GatewayConnector) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GatewayConnector_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type GatewayConnector_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *GatewayConnector_Expecter) Start(_a0 interface{}) *GatewayConnector_Start_Call {
	return &GatewayConnector_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *GatewayConnector_Start_Call) Run(run func(_a0 context.Context)) *GatewayConnector_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *GatewayConnector_Start_Call) Return(_a0 error) *GatewayConnector_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *GatewayConnector_Start_Call) RunAndReturn(run func(context.Context) error) *GatewayConnector_Start_Call {
	_c.Call.Return(run)
	return _c
}

// NewGatewayConnector creates a new instance of GatewayConnector. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewGatewayConnector(t interface {
	mock.TestingT
	Cleanup(func())
}) *GatewayConnector {
	mock := &GatewayConnector{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
