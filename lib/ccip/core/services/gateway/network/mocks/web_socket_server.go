// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// WebSocketServer is an autogenerated mock type for the WebSocketServer type
type WebSocketServer struct {
	mock.Mock
}

type WebSocketServer_Expecter struct {
	mock *mock.Mock
}

func (_m *WebSocketServer) EXPECT() *WebSocketServer_Expecter {
	return &WebSocketServer_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with given fields:
func (_m *WebSocketServer) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// WebSocketServer_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type WebSocketServer_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *WebSocketServer_Expecter) Close() *WebSocketServer_Close_Call {
	return &WebSocketServer_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *WebSocketServer_Close_Call) Run(run func()) *WebSocketServer_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *WebSocketServer_Close_Call) Return(_a0 error) *WebSocketServer_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *WebSocketServer_Close_Call) RunAndReturn(run func() error) *WebSocketServer_Close_Call {
	_c.Call.Return(run)
	return _c
}

// GetPort provides a mock function with given fields:
func (_m *WebSocketServer) GetPort() int {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetPort")
	}

	var r0 int
	if rf, ok := ret.Get(0).(func() int); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int)
	}

	return r0
}

// WebSocketServer_GetPort_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPort'
type WebSocketServer_GetPort_Call struct {
	*mock.Call
}

// GetPort is a helper method to define mock.On call
func (_e *WebSocketServer_Expecter) GetPort() *WebSocketServer_GetPort_Call {
	return &WebSocketServer_GetPort_Call{Call: _e.mock.On("GetPort")}
}

func (_c *WebSocketServer_GetPort_Call) Run(run func()) *WebSocketServer_GetPort_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *WebSocketServer_GetPort_Call) Return(_a0 int) *WebSocketServer_GetPort_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *WebSocketServer_GetPort_Call) RunAndReturn(run func() int) *WebSocketServer_GetPort_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *WebSocketServer) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// WebSocketServer_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type WebSocketServer_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *WebSocketServer_Expecter) Start(_a0 interface{}) *WebSocketServer_Start_Call {
	return &WebSocketServer_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *WebSocketServer_Start_Call) Run(run func(_a0 context.Context)) *WebSocketServer_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *WebSocketServer_Start_Call) Return(_a0 error) *WebSocketServer_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *WebSocketServer_Start_Call) RunAndReturn(run func(context.Context) error) *WebSocketServer_Start_Call {
	_c.Call.Return(run)
	return _c
}

// NewWebSocketServer creates a new instance of WebSocketServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewWebSocketServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *WebSocketServer {
	mock := &WebSocketServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
