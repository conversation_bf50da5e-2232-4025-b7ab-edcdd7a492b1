// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	types "github.com/smartcontractkit/chainlink/v2/core/services/relay/evm/types"
	mock "github.com/stretchr/testify/mock"
)

// LogPollerWrapper is an autogenerated mock type for the LogPollerWrapper type
type LogPollerWrapper struct {
	mock.Mock
}

type LogPollerWrapper_Expecter struct {
	mock *mock.Mock
}

func (_m *LogPollerWrapper) EXPECT() *LogPollerWrapper_Expecter {
	return &LogPollerWrapper_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with given fields:
func (_m *LogPollerWrapper) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// LogPollerWrapper_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type LogPollerWrapper_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *LogPollerWrapper_Expecter) Close() *LogPollerWrapper_Close_Call {
	return &LogPollerWrapper_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *LogPollerWrapper_Close_Call) Run(run func()) *LogPollerWrapper_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LogPollerWrapper_Close_Call) Return(_a0 error) *LogPollerWrapper_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LogPollerWrapper_Close_Call) RunAndReturn(run func() error) *LogPollerWrapper_Close_Call {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with given fields:
func (_m *LogPollerWrapper) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// LogPollerWrapper_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type LogPollerWrapper_HealthReport_Call struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *LogPollerWrapper_Expecter) HealthReport() *LogPollerWrapper_HealthReport_Call {
	return &LogPollerWrapper_HealthReport_Call{Call: _e.mock.On("HealthReport")}
}

func (_c *LogPollerWrapper_HealthReport_Call) Run(run func()) *LogPollerWrapper_HealthReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LogPollerWrapper_HealthReport_Call) Return(_a0 map[string]error) *LogPollerWrapper_HealthReport_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LogPollerWrapper_HealthReport_Call) RunAndReturn(run func() map[string]error) *LogPollerWrapper_HealthReport_Call {
	_c.Call.Return(run)
	return _c
}

// LatestEvents provides a mock function with given fields: ctx
func (_m *LogPollerWrapper) LatestEvents(ctx context.Context) ([]types.OracleRequest, []types.OracleResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for LatestEvents")
	}

	var r0 []types.OracleRequest
	var r1 []types.OracleResponse
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]types.OracleRequest, []types.OracleResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []types.OracleRequest); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.OracleRequest)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) []types.OracleResponse); ok {
		r1 = rf(ctx)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).([]types.OracleResponse)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context) error); ok {
		r2 = rf(ctx)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// LogPollerWrapper_LatestEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestEvents'
type LogPollerWrapper_LatestEvents_Call struct {
	*mock.Call
}

// LatestEvents is a helper method to define mock.On call
//   - ctx context.Context
func (_e *LogPollerWrapper_Expecter) LatestEvents(ctx interface{}) *LogPollerWrapper_LatestEvents_Call {
	return &LogPollerWrapper_LatestEvents_Call{Call: _e.mock.On("LatestEvents", ctx)}
}

func (_c *LogPollerWrapper_LatestEvents_Call) Run(run func(ctx context.Context)) *LogPollerWrapper_LatestEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *LogPollerWrapper_LatestEvents_Call) Return(_a0 []types.OracleRequest, _a1 []types.OracleResponse, _a2 error) *LogPollerWrapper_LatestEvents_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *LogPollerWrapper_LatestEvents_Call) RunAndReturn(run func(context.Context) ([]types.OracleRequest, []types.OracleResponse, error)) *LogPollerWrapper_LatestEvents_Call {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with given fields:
func (_m *LogPollerWrapper) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// LogPollerWrapper_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type LogPollerWrapper_Name_Call struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *LogPollerWrapper_Expecter) Name() *LogPollerWrapper_Name_Call {
	return &LogPollerWrapper_Name_Call{Call: _e.mock.On("Name")}
}

func (_c *LogPollerWrapper_Name_Call) Run(run func()) *LogPollerWrapper_Name_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LogPollerWrapper_Name_Call) Return(_a0 string) *LogPollerWrapper_Name_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LogPollerWrapper_Name_Call) RunAndReturn(run func() string) *LogPollerWrapper_Name_Call {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with given fields:
func (_m *LogPollerWrapper) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// LogPollerWrapper_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type LogPollerWrapper_Ready_Call struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *LogPollerWrapper_Expecter) Ready() *LogPollerWrapper_Ready_Call {
	return &LogPollerWrapper_Ready_Call{Call: _e.mock.On("Ready")}
}

func (_c *LogPollerWrapper_Ready_Call) Run(run func()) *LogPollerWrapper_Ready_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LogPollerWrapper_Ready_Call) Return(_a0 error) *LogPollerWrapper_Ready_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LogPollerWrapper_Ready_Call) RunAndReturn(run func() error) *LogPollerWrapper_Ready_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *LogPollerWrapper) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// LogPollerWrapper_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type LogPollerWrapper_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *LogPollerWrapper_Expecter) Start(_a0 interface{}) *LogPollerWrapper_Start_Call {
	return &LogPollerWrapper_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *LogPollerWrapper_Start_Call) Run(run func(_a0 context.Context)) *LogPollerWrapper_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *LogPollerWrapper_Start_Call) Return(_a0 error) *LogPollerWrapper_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LogPollerWrapper_Start_Call) RunAndReturn(run func(context.Context) error) *LogPollerWrapper_Start_Call {
	_c.Call.Return(run)
	return _c
}

// SubscribeToUpdates provides a mock function with given fields: ctx, name, subscriber
func (_m *LogPollerWrapper) SubscribeToUpdates(ctx context.Context, name string, subscriber types.RouteUpdateSubscriber) {
	_m.Called(ctx, name, subscriber)
}

// LogPollerWrapper_SubscribeToUpdates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubscribeToUpdates'
type LogPollerWrapper_SubscribeToUpdates_Call struct {
	*mock.Call
}

// SubscribeToUpdates is a helper method to define mock.On call
//   - ctx context.Context
//   - name string
//   - subscriber types.RouteUpdateSubscriber
func (_e *LogPollerWrapper_Expecter) SubscribeToUpdates(ctx interface{}, name interface{}, subscriber interface{}) *LogPollerWrapper_SubscribeToUpdates_Call {
	return &LogPollerWrapper_SubscribeToUpdates_Call{Call: _e.mock.On("SubscribeToUpdates", ctx, name, subscriber)}
}

func (_c *LogPollerWrapper_SubscribeToUpdates_Call) Run(run func(ctx context.Context, name string, subscriber types.RouteUpdateSubscriber)) *LogPollerWrapper_SubscribeToUpdates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(types.RouteUpdateSubscriber))
	})
	return _c
}

func (_c *LogPollerWrapper_SubscribeToUpdates_Call) Return() *LogPollerWrapper_SubscribeToUpdates_Call {
	_c.Call.Return()
	return _c
}

func (_c *LogPollerWrapper_SubscribeToUpdates_Call) RunAndReturn(run func(context.Context, string, types.RouteUpdateSubscriber)) *LogPollerWrapper_SubscribeToUpdates_Call {
	_c.Call.Return(run)
	return _c
}

// NewLogPollerWrapper creates a new instance of LogPollerWrapper. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLogPollerWrapper(t interface {
	mock.TestingT
	Cleanup(func())
}) *LogPollerWrapper {
	mock := &LogPollerWrapper{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
