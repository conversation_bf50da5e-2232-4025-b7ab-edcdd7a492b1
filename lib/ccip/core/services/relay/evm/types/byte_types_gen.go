package types

import "reflect"

type bytes1 [1]byte

func init() {
	typeMap["bytes1"] = &ABIEncodingType{
		native:  reflect.TypeOf([1]byte{}),
		checked: reflect.TypeOf(bytes1{}),
	}
}

type bytes2 [2]byte

func init() {
	typeMap["bytes2"] = &ABIEncodingType{
		native:  reflect.TypeOf([2]byte{}),
		checked: reflect.TypeOf(bytes2{}),
	}
}

type bytes3 [3]byte

func init() {
	typeMap["bytes3"] = &ABIEncodingType{
		native:  reflect.TypeOf([3]byte{}),
		checked: reflect.TypeOf(bytes3{}),
	}
}

type bytes4 [4]byte

func init() {
	typeMap["bytes4"] = &ABIEncodingType{
		native:  reflect.TypeOf([4]byte{}),
		checked: reflect.TypeOf(bytes4{}),
	}
}

type bytes5 [5]byte

func init() {
	typeMap["bytes5"] = &ABIEncodingType{
		native:  reflect.TypeOf([5]byte{}),
		checked: reflect.TypeOf(bytes5{}),
	}
}

type bytes6 [6]byte

func init() {
	typeMap["bytes6"] = &ABIEncodingType{
		native:  reflect.TypeOf([6]byte{}),
		checked: reflect.TypeOf(bytes6{}),
	}
}

type bytes7 [7]byte

func init() {
	typeMap["bytes7"] = &ABIEncodingType{
		native:  reflect.TypeOf([7]byte{}),
		checked: reflect.TypeOf(bytes7{}),
	}
}

type bytes8 [8]byte

func init() {
	typeMap["bytes8"] = &ABIEncodingType{
		native:  reflect.TypeOf([8]byte{}),
		checked: reflect.TypeOf(bytes8{}),
	}
}

type bytes9 [9]byte

func init() {
	typeMap["bytes9"] = &ABIEncodingType{
		native:  reflect.TypeOf([9]byte{}),
		checked: reflect.TypeOf(bytes9{}),
	}
}

type bytes10 [10]byte

func init() {
	typeMap["bytes10"] = &ABIEncodingType{
		native:  reflect.TypeOf([10]byte{}),
		checked: reflect.TypeOf(bytes10{}),
	}
}

type bytes11 [11]byte

func init() {
	typeMap["bytes11"] = &ABIEncodingType{
		native:  reflect.TypeOf([11]byte{}),
		checked: reflect.TypeOf(bytes11{}),
	}
}

type bytes12 [12]byte

func init() {
	typeMap["bytes12"] = &ABIEncodingType{
		native:  reflect.TypeOf([12]byte{}),
		checked: reflect.TypeOf(bytes12{}),
	}
}

type bytes13 [13]byte

func init() {
	typeMap["bytes13"] = &ABIEncodingType{
		native:  reflect.TypeOf([13]byte{}),
		checked: reflect.TypeOf(bytes13{}),
	}
}

type bytes14 [14]byte

func init() {
	typeMap["bytes14"] = &ABIEncodingType{
		native:  reflect.TypeOf([14]byte{}),
		checked: reflect.TypeOf(bytes14{}),
	}
}

type bytes15 [15]byte

func init() {
	typeMap["bytes15"] = &ABIEncodingType{
		native:  reflect.TypeOf([15]byte{}),
		checked: reflect.TypeOf(bytes15{}),
	}
}

type bytes16 [16]byte

func init() {
	typeMap["bytes16"] = &ABIEncodingType{
		native:  reflect.TypeOf([16]byte{}),
		checked: reflect.TypeOf(bytes16{}),
	}
}

type bytes17 [17]byte

func init() {
	typeMap["bytes17"] = &ABIEncodingType{
		native:  reflect.TypeOf([17]byte{}),
		checked: reflect.TypeOf(bytes17{}),
	}
}

type bytes18 [18]byte

func init() {
	typeMap["bytes18"] = &ABIEncodingType{
		native:  reflect.TypeOf([18]byte{}),
		checked: reflect.TypeOf(bytes18{}),
	}
}

type bytes19 [19]byte

func init() {
	typeMap["bytes19"] = &ABIEncodingType{
		native:  reflect.TypeOf([19]byte{}),
		checked: reflect.TypeOf(bytes19{}),
	}
}

type bytes20 [20]byte

func init() {
	typeMap["bytes20"] = &ABIEncodingType{
		native:  reflect.TypeOf([20]byte{}),
		checked: reflect.TypeOf(bytes20{}),
	}
}

type bytes21 [21]byte

func init() {
	typeMap["bytes21"] = &ABIEncodingType{
		native:  reflect.TypeOf([21]byte{}),
		checked: reflect.TypeOf(bytes21{}),
	}
}

type bytes22 [22]byte

func init() {
	typeMap["bytes22"] = &ABIEncodingType{
		native:  reflect.TypeOf([22]byte{}),
		checked: reflect.TypeOf(bytes22{}),
	}
}

type bytes23 [23]byte

func init() {
	typeMap["bytes23"] = &ABIEncodingType{
		native:  reflect.TypeOf([23]byte{}),
		checked: reflect.TypeOf(bytes23{}),
	}
}

type bytes24 [24]byte

func init() {
	typeMap["bytes24"] = &ABIEncodingType{
		native:  reflect.TypeOf([24]byte{}),
		checked: reflect.TypeOf(bytes24{}),
	}
}

type bytes25 [25]byte

func init() {
	typeMap["bytes25"] = &ABIEncodingType{
		native:  reflect.TypeOf([25]byte{}),
		checked: reflect.TypeOf(bytes25{}),
	}
}

type bytes26 [26]byte

func init() {
	typeMap["bytes26"] = &ABIEncodingType{
		native:  reflect.TypeOf([26]byte{}),
		checked: reflect.TypeOf(bytes26{}),
	}
}

type bytes27 [27]byte

func init() {
	typeMap["bytes27"] = &ABIEncodingType{
		native:  reflect.TypeOf([27]byte{}),
		checked: reflect.TypeOf(bytes27{}),
	}
}

type bytes28 [28]byte

func init() {
	typeMap["bytes28"] = &ABIEncodingType{
		native:  reflect.TypeOf([28]byte{}),
		checked: reflect.TypeOf(bytes28{}),
	}
}

type bytes29 [29]byte

func init() {
	typeMap["bytes29"] = &ABIEncodingType{
		native:  reflect.TypeOf([29]byte{}),
		checked: reflect.TypeOf(bytes29{}),
	}
}

type bytes30 [30]byte

func init() {
	typeMap["bytes30"] = &ABIEncodingType{
		native:  reflect.TypeOf([30]byte{}),
		checked: reflect.TypeOf(bytes30{}),
	}
}

type bytes31 [31]byte

func init() {
	typeMap["bytes31"] = &ABIEncodingType{
		native:  reflect.TypeOf([31]byte{}),
		checked: reflect.TypeOf(bytes31{}),
	}
}

type bytes32 [32]byte

func init() {
	typeMap["bytes32"] = &ABIEncodingType{
		native:  reflect.TypeOf([32]byte{}),
		checked: reflect.TypeOf(bytes32{}),
	}
}

type bytes0 [0]byte

func init() {
	typeMap["bytes0"] = &ABIEncodingType{
		native:  reflect.TypeOf([0]byte{}),
		checked: reflect.TypeOf(bytes0{}),
	}
}
