// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	read "github.com/smartcontractkit/chainlink/v2/core/services/relay/evm/read"
	mock "github.com/stretchr/testify/mock"
)

// BatchCaller is an autogenerated mock type for the BatchCaller type
type BatchCaller struct {
	mock.Mock
}

type BatchCaller_Expecter struct {
	mock *mock.Mock
}

func (_m *BatchCaller) EXPECT() *BatchCaller_Expecter {
	return &BatchCaller_Expecter{mock: &_m.Mock}
}

// BatchCall provides a mock function with given fields: ctx, blockNumber, batchRequests
func (_m *BatchCaller) BatchCall(ctx context.Context, blockNumber uint64, batchRequests read.BatchCall) (read.BatchResult, error) {
	ret := _m.Called(ctx, blockNumber, batchRequests)

	if len(ret) == 0 {
		panic("no return value specified for BatchCall")
	}

	var r0 read.BatchResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, read.BatchCall) (read.BatchResult, error)); ok {
		return rf(ctx, blockNumber, batchRequests)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, read.BatchCall) read.BatchResult); ok {
		r0 = rf(ctx, blockNumber, batchRequests)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(read.BatchResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, read.BatchCall) error); ok {
		r1 = rf(ctx, blockNumber, batchRequests)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BatchCaller_BatchCall_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCall'
type BatchCaller_BatchCall_Call struct {
	*mock.Call
}

// BatchCall is a helper method to define mock.On call
//   - ctx context.Context
//   - blockNumber uint64
//   - batchRequests read.BatchCall
func (_e *BatchCaller_Expecter) BatchCall(ctx interface{}, blockNumber interface{}, batchRequests interface{}) *BatchCaller_BatchCall_Call {
	return &BatchCaller_BatchCall_Call{Call: _e.mock.On("BatchCall", ctx, blockNumber, batchRequests)}
}

func (_c *BatchCaller_BatchCall_Call) Run(run func(ctx context.Context, blockNumber uint64, batchRequests read.BatchCall)) *BatchCaller_BatchCall_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(read.BatchCall))
	})
	return _c
}

func (_c *BatchCaller_BatchCall_Call) Return(_a0 read.BatchResult, _a1 error) *BatchCaller_BatchCall_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BatchCaller_BatchCall_Call) RunAndReturn(run func(context.Context, uint64, read.BatchCall) (read.BatchResult, error)) *BatchCaller_BatchCall_Call {
	_c.Call.Return(run)
	return _c
}

// NewBatchCaller creates a new instance of BatchCaller. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBatchCaller(t interface {
	mock.TestingT
	Cleanup(func())
}) *BatchCaller {
	mock := &BatchCaller{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
