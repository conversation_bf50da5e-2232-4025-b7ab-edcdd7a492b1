// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	pb "github.com/smartcontractkit/chainlink/v2/core/services/relay/evm/mercury/wsrpc/pb"
	mock "github.com/stretchr/testify/mock"
)

// AsyncDeleter is an autogenerated mock type for the asyncDeleter type
type AsyncDeleter struct {
	mock.Mock
}

type AsyncDeleter_Expecter struct {
	mock *mock.Mock
}

func (_m *AsyncDeleter) EXPECT() *AsyncDeleter_Expecter {
	return &AsyncDeleter_Expecter{mock: &_m.Mock}
}

// AsyncDelete provides a mock function with given fields: req
func (_m *AsyncDeleter) AsyncDelete(req *pb.TransmitRequest) {
	_m.Called(req)
}

// AsyncDeleter_AsyncDelete_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AsyncDelete'
type AsyncDeleter_AsyncDelete_Call struct {
	*mock.Call
}

// AsyncDelete is a helper method to define mock.On call
//   - req *pb.TransmitRequest
func (_e *AsyncDeleter_Expecter) AsyncDelete(req interface{}) *AsyncDeleter_AsyncDelete_Call {
	return &AsyncDeleter_AsyncDelete_Call{Call: _e.mock.On("AsyncDelete", req)}
}

func (_c *AsyncDeleter_AsyncDelete_Call) Run(run func(req *pb.TransmitRequest)) *AsyncDeleter_AsyncDelete_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*pb.TransmitRequest))
	})
	return _c
}

func (_c *AsyncDeleter_AsyncDelete_Call) Return() *AsyncDeleter_AsyncDelete_Call {
	_c.Call.Return()
	return _c
}

func (_c *AsyncDeleter_AsyncDelete_Call) RunAndReturn(run func(*pb.TransmitRequest)) *AsyncDeleter_AsyncDelete_Call {
	_c.Call.Return(run)
	return _c
}

// NewAsyncDeleter creates a new instance of AsyncDeleter. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAsyncDeleter(t interface {
	mock.TestingT
	Cleanup(func())
}) *AsyncDeleter {
	mock := &AsyncDeleter{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
