// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"
	big "math/big"

	legacyevm "github.com/smartcontractkit/chainlink/v2/core/chains/legacyevm"

	mock "github.com/stretchr/testify/mock"

	types "github.com/smartcontractkit/chainlink-common/pkg/types"
)

// LoopRelayAdapter is an autogenerated mock type for the LoopRelayAdapter type
type LoopRelayAdapter struct {
	mock.Mock
}

type LoopRelayAdapter_Expecter struct {
	mock *mock.Mock
}

func (_m *LoopRelayAdapter) EXPECT() *LoopRelayAdapter_Expecter {
	return &LoopRelayAdapter_Expecter{mock: &_m.Mock}
}

// Chain provides a mock function with given fields:
func (_m *LoopRelayAdapter) Chain() legacyevm.Chain {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Chain")
	}

	var r0 legacyevm.Chain
	if rf, ok := ret.Get(0).(func() legacyevm.Chain); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(legacyevm.Chain)
		}
	}

	return r0
}

// LoopRelayAdapter_Chain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Chain'
type LoopRelayAdapter_Chain_Call struct {
	*mock.Call
}

// Chain is a helper method to define mock.On call
func (_e *LoopRelayAdapter_Expecter) Chain() *LoopRelayAdapter_Chain_Call {
	return &LoopRelayAdapter_Chain_Call{Call: _e.mock.On("Chain")}
}

func (_c *LoopRelayAdapter_Chain_Call) Run(run func()) *LoopRelayAdapter_Chain_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LoopRelayAdapter_Chain_Call) Return(_a0 legacyevm.Chain) *LoopRelayAdapter_Chain_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LoopRelayAdapter_Chain_Call) RunAndReturn(run func() legacyevm.Chain) *LoopRelayAdapter_Chain_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *LoopRelayAdapter) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// LoopRelayAdapter_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type LoopRelayAdapter_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *LoopRelayAdapter_Expecter) Close() *LoopRelayAdapter_Close_Call {
	return &LoopRelayAdapter_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *LoopRelayAdapter_Close_Call) Run(run func()) *LoopRelayAdapter_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LoopRelayAdapter_Close_Call) Return(_a0 error) *LoopRelayAdapter_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LoopRelayAdapter_Close_Call) RunAndReturn(run func() error) *LoopRelayAdapter_Close_Call {
	_c.Call.Return(run)
	return _c
}

// GetChainStatus provides a mock function with given fields: ctx
func (_m *LoopRelayAdapter) GetChainStatus(ctx context.Context) (types.ChainStatus, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetChainStatus")
	}

	var r0 types.ChainStatus
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (types.ChainStatus, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) types.ChainStatus); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(types.ChainStatus)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoopRelayAdapter_GetChainStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetChainStatus'
type LoopRelayAdapter_GetChainStatus_Call struct {
	*mock.Call
}

// GetChainStatus is a helper method to define mock.On call
//   - ctx context.Context
func (_e *LoopRelayAdapter_Expecter) GetChainStatus(ctx interface{}) *LoopRelayAdapter_GetChainStatus_Call {
	return &LoopRelayAdapter_GetChainStatus_Call{Call: _e.mock.On("GetChainStatus", ctx)}
}

func (_c *LoopRelayAdapter_GetChainStatus_Call) Run(run func(ctx context.Context)) *LoopRelayAdapter_GetChainStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *LoopRelayAdapter_GetChainStatus_Call) Return(_a0 types.ChainStatus, _a1 error) *LoopRelayAdapter_GetChainStatus_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LoopRelayAdapter_GetChainStatus_Call) RunAndReturn(run func(context.Context) (types.ChainStatus, error)) *LoopRelayAdapter_GetChainStatus_Call {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with given fields:
func (_m *LoopRelayAdapter) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// LoopRelayAdapter_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type LoopRelayAdapter_HealthReport_Call struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *LoopRelayAdapter_Expecter) HealthReport() *LoopRelayAdapter_HealthReport_Call {
	return &LoopRelayAdapter_HealthReport_Call{Call: _e.mock.On("HealthReport")}
}

func (_c *LoopRelayAdapter_HealthReport_Call) Run(run func()) *LoopRelayAdapter_HealthReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LoopRelayAdapter_HealthReport_Call) Return(_a0 map[string]error) *LoopRelayAdapter_HealthReport_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LoopRelayAdapter_HealthReport_Call) RunAndReturn(run func() map[string]error) *LoopRelayAdapter_HealthReport_Call {
	_c.Call.Return(run)
	return _c
}

// LatestHead provides a mock function with given fields: ctx
func (_m *LoopRelayAdapter) LatestHead(ctx context.Context) (types.Head, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for LatestHead")
	}

	var r0 types.Head
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (types.Head, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) types.Head); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(types.Head)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoopRelayAdapter_LatestHead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestHead'
type LoopRelayAdapter_LatestHead_Call struct {
	*mock.Call
}

// LatestHead is a helper method to define mock.On call
//   - ctx context.Context
func (_e *LoopRelayAdapter_Expecter) LatestHead(ctx interface{}) *LoopRelayAdapter_LatestHead_Call {
	return &LoopRelayAdapter_LatestHead_Call{Call: _e.mock.On("LatestHead", ctx)}
}

func (_c *LoopRelayAdapter_LatestHead_Call) Run(run func(ctx context.Context)) *LoopRelayAdapter_LatestHead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *LoopRelayAdapter_LatestHead_Call) Return(_a0 types.Head, _a1 error) *LoopRelayAdapter_LatestHead_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LoopRelayAdapter_LatestHead_Call) RunAndReturn(run func(context.Context) (types.Head, error)) *LoopRelayAdapter_LatestHead_Call {
	_c.Call.Return(run)
	return _c
}

// ListNodeStatuses provides a mock function with given fields: ctx, pageSize, pageToken
func (_m *LoopRelayAdapter) ListNodeStatuses(ctx context.Context, pageSize int32, pageToken string) ([]types.NodeStatus, string, int, error) {
	ret := _m.Called(ctx, pageSize, pageToken)

	if len(ret) == 0 {
		panic("no return value specified for ListNodeStatuses")
	}

	var r0 []types.NodeStatus
	var r1 string
	var r2 int
	var r3 error
	if rf, ok := ret.Get(0).(func(context.Context, int32, string) ([]types.NodeStatus, string, int, error)); ok {
		return rf(ctx, pageSize, pageToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int32, string) []types.NodeStatus); ok {
		r0 = rf(ctx, pageSize, pageToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.NodeStatus)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int32, string) string); ok {
		r1 = rf(ctx, pageSize, pageToken)
	} else {
		r1 = ret.Get(1).(string)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int32, string) int); ok {
		r2 = rf(ctx, pageSize, pageToken)
	} else {
		r2 = ret.Get(2).(int)
	}

	if rf, ok := ret.Get(3).(func(context.Context, int32, string) error); ok {
		r3 = rf(ctx, pageSize, pageToken)
	} else {
		r3 = ret.Error(3)
	}

	return r0, r1, r2, r3
}

// LoopRelayAdapter_ListNodeStatuses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListNodeStatuses'
type LoopRelayAdapter_ListNodeStatuses_Call struct {
	*mock.Call
}

// ListNodeStatuses is a helper method to define mock.On call
//   - ctx context.Context
//   - pageSize int32
//   - pageToken string
func (_e *LoopRelayAdapter_Expecter) ListNodeStatuses(ctx interface{}, pageSize interface{}, pageToken interface{}) *LoopRelayAdapter_ListNodeStatuses_Call {
	return &LoopRelayAdapter_ListNodeStatuses_Call{Call: _e.mock.On("ListNodeStatuses", ctx, pageSize, pageToken)}
}

func (_c *LoopRelayAdapter_ListNodeStatuses_Call) Run(run func(ctx context.Context, pageSize int32, pageToken string)) *LoopRelayAdapter_ListNodeStatuses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int32), args[2].(string))
	})
	return _c
}

func (_c *LoopRelayAdapter_ListNodeStatuses_Call) Return(stats []types.NodeStatus, nextPageToken string, total int, err error) *LoopRelayAdapter_ListNodeStatuses_Call {
	_c.Call.Return(stats, nextPageToken, total, err)
	return _c
}

func (_c *LoopRelayAdapter_ListNodeStatuses_Call) RunAndReturn(run func(context.Context, int32, string) ([]types.NodeStatus, string, int, error)) *LoopRelayAdapter_ListNodeStatuses_Call {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with given fields:
func (_m *LoopRelayAdapter) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// LoopRelayAdapter_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type LoopRelayAdapter_Name_Call struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *LoopRelayAdapter_Expecter) Name() *LoopRelayAdapter_Name_Call {
	return &LoopRelayAdapter_Name_Call{Call: _e.mock.On("Name")}
}

func (_c *LoopRelayAdapter_Name_Call) Run(run func()) *LoopRelayAdapter_Name_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LoopRelayAdapter_Name_Call) Return(_a0 string) *LoopRelayAdapter_Name_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LoopRelayAdapter_Name_Call) RunAndReturn(run func() string) *LoopRelayAdapter_Name_Call {
	_c.Call.Return(run)
	return _c
}

// NewChainWriter provides a mock function with given fields: ctx, chainWriterConfig
func (_m *LoopRelayAdapter) NewChainWriter(ctx context.Context, chainWriterConfig []byte) (types.ChainWriter, error) {
	ret := _m.Called(ctx, chainWriterConfig)

	if len(ret) == 0 {
		panic("no return value specified for NewChainWriter")
	}

	var r0 types.ChainWriter
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []byte) (types.ChainWriter, error)); ok {
		return rf(ctx, chainWriterConfig)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []byte) types.ChainWriter); ok {
		r0 = rf(ctx, chainWriterConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.ChainWriter)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []byte) error); ok {
		r1 = rf(ctx, chainWriterConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoopRelayAdapter_NewChainWriter_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewChainWriter'
type LoopRelayAdapter_NewChainWriter_Call struct {
	*mock.Call
}

// NewChainWriter is a helper method to define mock.On call
//   - ctx context.Context
//   - chainWriterConfig []byte
func (_e *LoopRelayAdapter_Expecter) NewChainWriter(ctx interface{}, chainWriterConfig interface{}) *LoopRelayAdapter_NewChainWriter_Call {
	return &LoopRelayAdapter_NewChainWriter_Call{Call: _e.mock.On("NewChainWriter", ctx, chainWriterConfig)}
}

func (_c *LoopRelayAdapter_NewChainWriter_Call) Run(run func(ctx context.Context, chainWriterConfig []byte)) *LoopRelayAdapter_NewChainWriter_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte))
	})
	return _c
}

func (_c *LoopRelayAdapter_NewChainWriter_Call) Return(_a0 types.ChainWriter, _a1 error) *LoopRelayAdapter_NewChainWriter_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LoopRelayAdapter_NewChainWriter_Call) RunAndReturn(run func(context.Context, []byte) (types.ChainWriter, error)) *LoopRelayAdapter_NewChainWriter_Call {
	_c.Call.Return(run)
	return _c
}

// NewConfigProvider provides a mock function with given fields: _a0, _a1
func (_m *LoopRelayAdapter) NewConfigProvider(_a0 context.Context, _a1 types.RelayArgs) (types.ConfigProvider, error) {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for NewConfigProvider")
	}

	var r0 types.ConfigProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RelayArgs) (types.ConfigProvider, error)); ok {
		return rf(_a0, _a1)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.RelayArgs) types.ConfigProvider); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.ConfigProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.RelayArgs) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoopRelayAdapter_NewConfigProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewConfigProvider'
type LoopRelayAdapter_NewConfigProvider_Call struct {
	*mock.Call
}

// NewConfigProvider is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 types.RelayArgs
func (_e *LoopRelayAdapter_Expecter) NewConfigProvider(_a0 interface{}, _a1 interface{}) *LoopRelayAdapter_NewConfigProvider_Call {
	return &LoopRelayAdapter_NewConfigProvider_Call{Call: _e.mock.On("NewConfigProvider", _a0, _a1)}
}

func (_c *LoopRelayAdapter_NewConfigProvider_Call) Run(run func(_a0 context.Context, _a1 types.RelayArgs)) *LoopRelayAdapter_NewConfigProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RelayArgs))
	})
	return _c
}

func (_c *LoopRelayAdapter_NewConfigProvider_Call) Return(_a0 types.ConfigProvider, _a1 error) *LoopRelayAdapter_NewConfigProvider_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LoopRelayAdapter_NewConfigProvider_Call) RunAndReturn(run func(context.Context, types.RelayArgs) (types.ConfigProvider, error)) *LoopRelayAdapter_NewConfigProvider_Call {
	_c.Call.Return(run)
	return _c
}

// NewContractReader provides a mock function with given fields: ctx, contractReaderConfig
func (_m *LoopRelayAdapter) NewContractReader(ctx context.Context, contractReaderConfig []byte) (types.ContractReader, error) {
	ret := _m.Called(ctx, contractReaderConfig)

	if len(ret) == 0 {
		panic("no return value specified for NewContractReader")
	}

	var r0 types.ContractReader
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []byte) (types.ContractReader, error)); ok {
		return rf(ctx, contractReaderConfig)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []byte) types.ContractReader); ok {
		r0 = rf(ctx, contractReaderConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.ContractReader)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []byte) error); ok {
		r1 = rf(ctx, contractReaderConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoopRelayAdapter_NewContractReader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewContractReader'
type LoopRelayAdapter_NewContractReader_Call struct {
	*mock.Call
}

// NewContractReader is a helper method to define mock.On call
//   - ctx context.Context
//   - contractReaderConfig []byte
func (_e *LoopRelayAdapter_Expecter) NewContractReader(ctx interface{}, contractReaderConfig interface{}) *LoopRelayAdapter_NewContractReader_Call {
	return &LoopRelayAdapter_NewContractReader_Call{Call: _e.mock.On("NewContractReader", ctx, contractReaderConfig)}
}

func (_c *LoopRelayAdapter_NewContractReader_Call) Run(run func(ctx context.Context, contractReaderConfig []byte)) *LoopRelayAdapter_NewContractReader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte))
	})
	return _c
}

func (_c *LoopRelayAdapter_NewContractReader_Call) Return(_a0 types.ContractReader, _a1 error) *LoopRelayAdapter_NewContractReader_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LoopRelayAdapter_NewContractReader_Call) RunAndReturn(run func(context.Context, []byte) (types.ContractReader, error)) *LoopRelayAdapter_NewContractReader_Call {
	_c.Call.Return(run)
	return _c
}

// NewLLOProvider provides a mock function with given fields: _a0, _a1, _a2
func (_m *LoopRelayAdapter) NewLLOProvider(_a0 context.Context, _a1 types.RelayArgs, _a2 types.PluginArgs) (types.LLOProvider, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for NewLLOProvider")
	}

	var r0 types.LLOProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RelayArgs, types.PluginArgs) (types.LLOProvider, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.RelayArgs, types.PluginArgs) types.LLOProvider); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.LLOProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.RelayArgs, types.PluginArgs) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoopRelayAdapter_NewLLOProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewLLOProvider'
type LoopRelayAdapter_NewLLOProvider_Call struct {
	*mock.Call
}

// NewLLOProvider is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 types.RelayArgs
//   - _a2 types.PluginArgs
func (_e *LoopRelayAdapter_Expecter) NewLLOProvider(_a0 interface{}, _a1 interface{}, _a2 interface{}) *LoopRelayAdapter_NewLLOProvider_Call {
	return &LoopRelayAdapter_NewLLOProvider_Call{Call: _e.mock.On("NewLLOProvider", _a0, _a1, _a2)}
}

func (_c *LoopRelayAdapter_NewLLOProvider_Call) Run(run func(_a0 context.Context, _a1 types.RelayArgs, _a2 types.PluginArgs)) *LoopRelayAdapter_NewLLOProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RelayArgs), args[2].(types.PluginArgs))
	})
	return _c
}

func (_c *LoopRelayAdapter_NewLLOProvider_Call) Return(_a0 types.LLOProvider, _a1 error) *LoopRelayAdapter_NewLLOProvider_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LoopRelayAdapter_NewLLOProvider_Call) RunAndReturn(run func(context.Context, types.RelayArgs, types.PluginArgs) (types.LLOProvider, error)) *LoopRelayAdapter_NewLLOProvider_Call {
	_c.Call.Return(run)
	return _c
}

// NewPluginProvider provides a mock function with given fields: _a0, _a1, _a2
func (_m *LoopRelayAdapter) NewPluginProvider(_a0 context.Context, _a1 types.RelayArgs, _a2 types.PluginArgs) (types.PluginProvider, error) {
	ret := _m.Called(_a0, _a1, _a2)

	if len(ret) == 0 {
		panic("no return value specified for NewPluginProvider")
	}

	var r0 types.PluginProvider
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.RelayArgs, types.PluginArgs) (types.PluginProvider, error)); ok {
		return rf(_a0, _a1, _a2)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.RelayArgs, types.PluginArgs) types.PluginProvider); ok {
		r0 = rf(_a0, _a1, _a2)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.PluginProvider)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.RelayArgs, types.PluginArgs) error); ok {
		r1 = rf(_a0, _a1, _a2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LoopRelayAdapter_NewPluginProvider_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewPluginProvider'
type LoopRelayAdapter_NewPluginProvider_Call struct {
	*mock.Call
}

// NewPluginProvider is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 types.RelayArgs
//   - _a2 types.PluginArgs
func (_e *LoopRelayAdapter_Expecter) NewPluginProvider(_a0 interface{}, _a1 interface{}, _a2 interface{}) *LoopRelayAdapter_NewPluginProvider_Call {
	return &LoopRelayAdapter_NewPluginProvider_Call{Call: _e.mock.On("NewPluginProvider", _a0, _a1, _a2)}
}

func (_c *LoopRelayAdapter_NewPluginProvider_Call) Run(run func(_a0 context.Context, _a1 types.RelayArgs, _a2 types.PluginArgs)) *LoopRelayAdapter_NewPluginProvider_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.RelayArgs), args[2].(types.PluginArgs))
	})
	return _c
}

func (_c *LoopRelayAdapter_NewPluginProvider_Call) Return(_a0 types.PluginProvider, _a1 error) *LoopRelayAdapter_NewPluginProvider_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LoopRelayAdapter_NewPluginProvider_Call) RunAndReturn(run func(context.Context, types.RelayArgs, types.PluginArgs) (types.PluginProvider, error)) *LoopRelayAdapter_NewPluginProvider_Call {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with given fields:
func (_m *LoopRelayAdapter) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// LoopRelayAdapter_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type LoopRelayAdapter_Ready_Call struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *LoopRelayAdapter_Expecter) Ready() *LoopRelayAdapter_Ready_Call {
	return &LoopRelayAdapter_Ready_Call{Call: _e.mock.On("Ready")}
}

func (_c *LoopRelayAdapter_Ready_Call) Run(run func()) *LoopRelayAdapter_Ready_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LoopRelayAdapter_Ready_Call) Return(_a0 error) *LoopRelayAdapter_Ready_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LoopRelayAdapter_Ready_Call) RunAndReturn(run func() error) *LoopRelayAdapter_Ready_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *LoopRelayAdapter) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// LoopRelayAdapter_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type LoopRelayAdapter_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *LoopRelayAdapter_Expecter) Start(_a0 interface{}) *LoopRelayAdapter_Start_Call {
	return &LoopRelayAdapter_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *LoopRelayAdapter_Start_Call) Run(run func(_a0 context.Context)) *LoopRelayAdapter_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *LoopRelayAdapter_Start_Call) Return(_a0 error) *LoopRelayAdapter_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LoopRelayAdapter_Start_Call) RunAndReturn(run func(context.Context) error) *LoopRelayAdapter_Start_Call {
	_c.Call.Return(run)
	return _c
}

// Transact provides a mock function with given fields: ctx, from, to, amount, balanceCheck
func (_m *LoopRelayAdapter) Transact(ctx context.Context, from string, to string, amount *big.Int, balanceCheck bool) error {
	ret := _m.Called(ctx, from, to, amount, balanceCheck)

	if len(ret) == 0 {
		panic("no return value specified for Transact")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, *big.Int, bool) error); ok {
		r0 = rf(ctx, from, to, amount, balanceCheck)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// LoopRelayAdapter_Transact_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transact'
type LoopRelayAdapter_Transact_Call struct {
	*mock.Call
}

// Transact is a helper method to define mock.On call
//   - ctx context.Context
//   - from string
//   - to string
//   - amount *big.Int
//   - balanceCheck bool
func (_e *LoopRelayAdapter_Expecter) Transact(ctx interface{}, from interface{}, to interface{}, amount interface{}, balanceCheck interface{}) *LoopRelayAdapter_Transact_Call {
	return &LoopRelayAdapter_Transact_Call{Call: _e.mock.On("Transact", ctx, from, to, amount, balanceCheck)}
}

func (_c *LoopRelayAdapter_Transact_Call) Run(run func(ctx context.Context, from string, to string, amount *big.Int, balanceCheck bool)) *LoopRelayAdapter_Transact_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].(*big.Int), args[4].(bool))
	})
	return _c
}

func (_c *LoopRelayAdapter_Transact_Call) Return(_a0 error) *LoopRelayAdapter_Transact_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LoopRelayAdapter_Transact_Call) RunAndReturn(run func(context.Context, string, string, *big.Int, bool) error) *LoopRelayAdapter_Transact_Call {
	_c.Call.Return(run)
	return _c
}

// NewLoopRelayAdapter creates a new instance of LoopRelayAdapter. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLoopRelayAdapter(t interface {
	mock.TestingT
	Cleanup(func())
}) *LoopRelayAdapter {
	mock := &LoopRelayAdapter{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
