// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// Codec is an autogenerated mock type for the Codec type
type Codec struct {
	mock.Mock
}

type Codec_Expecter struct {
	mock *mock.Mock
}

func (_m *Codec) EXPECT() *Codec_Expecter {
	return &Codec_Expecter{mock: &_m.Mock}
}

// Decode provides a mock function with given fields: ctx, raw, into, itemType
func (_m *Codec) Decode(ctx context.Context, raw []byte, into interface{}, itemType string) error {
	ret := _m.Called(ctx, raw, into, itemType)

	if len(ret) == 0 {
		panic("no return value specified for Decode")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []byte, interface{}, string) error); ok {
		r0 = rf(ctx, raw, into, itemType)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Codec_Decode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Decode'
type Codec_Decode_Call struct {
	*mock.Call
}

// Decode is a helper method to define mock.On call
//   - ctx context.Context
//   - raw []byte
//   - into interface{}
//   - itemType string
func (_e *Codec_Expecter) Decode(ctx interface{}, raw interface{}, into interface{}, itemType interface{}) *Codec_Decode_Call {
	return &Codec_Decode_Call{Call: _e.mock.On("Decode", ctx, raw, into, itemType)}
}

func (_c *Codec_Decode_Call) Run(run func(ctx context.Context, raw []byte, into interface{}, itemType string)) *Codec_Decode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte), args[2].(interface{}), args[3].(string))
	})
	return _c
}

func (_c *Codec_Decode_Call) Return(_a0 error) *Codec_Decode_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Codec_Decode_Call) RunAndReturn(run func(context.Context, []byte, interface{}, string) error) *Codec_Decode_Call {
	_c.Call.Return(run)
	return _c
}

// Encode provides a mock function with given fields: ctx, item, itemType
func (_m *Codec) Encode(ctx context.Context, item interface{}, itemType string) ([]byte, error) {
	ret := _m.Called(ctx, item, itemType)

	if len(ret) == 0 {
		panic("no return value specified for Encode")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}, string) ([]byte, error)); ok {
		return rf(ctx, item, itemType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, interface{}, string) []byte); ok {
		r0 = rf(ctx, item, itemType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, interface{}, string) error); ok {
		r1 = rf(ctx, item, itemType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Codec_Encode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Encode'
type Codec_Encode_Call struct {
	*mock.Call
}

// Encode is a helper method to define mock.On call
//   - ctx context.Context
//   - item interface{}
//   - itemType string
func (_e *Codec_Expecter) Encode(ctx interface{}, item interface{}, itemType interface{}) *Codec_Encode_Call {
	return &Codec_Encode_Call{Call: _e.mock.On("Encode", ctx, item, itemType)}
}

func (_c *Codec_Encode_Call) Run(run func(ctx context.Context, item interface{}, itemType string)) *Codec_Encode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}), args[2].(string))
	})
	return _c
}

func (_c *Codec_Encode_Call) Return(_a0 []byte, _a1 error) *Codec_Encode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Codec_Encode_Call) RunAndReturn(run func(context.Context, interface{}, string) ([]byte, error)) *Codec_Encode_Call {
	_c.Call.Return(run)
	return _c
}

// GetMaxDecodingSize provides a mock function with given fields: ctx, n, itemType
func (_m *Codec) GetMaxDecodingSize(ctx context.Context, n int, itemType string) (int, error) {
	ret := _m.Called(ctx, n, itemType)

	if len(ret) == 0 {
		panic("no return value specified for GetMaxDecodingSize")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, string) (int, error)); ok {
		return rf(ctx, n, itemType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, string) int); ok {
		r0 = rf(ctx, n, itemType)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, string) error); ok {
		r1 = rf(ctx, n, itemType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Codec_GetMaxDecodingSize_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMaxDecodingSize'
type Codec_GetMaxDecodingSize_Call struct {
	*mock.Call
}

// GetMaxDecodingSize is a helper method to define mock.On call
//   - ctx context.Context
//   - n int
//   - itemType string
func (_e *Codec_Expecter) GetMaxDecodingSize(ctx interface{}, n interface{}, itemType interface{}) *Codec_GetMaxDecodingSize_Call {
	return &Codec_GetMaxDecodingSize_Call{Call: _e.mock.On("GetMaxDecodingSize", ctx, n, itemType)}
}

func (_c *Codec_GetMaxDecodingSize_Call) Run(run func(ctx context.Context, n int, itemType string)) *Codec_GetMaxDecodingSize_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(string))
	})
	return _c
}

func (_c *Codec_GetMaxDecodingSize_Call) Return(_a0 int, _a1 error) *Codec_GetMaxDecodingSize_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Codec_GetMaxDecodingSize_Call) RunAndReturn(run func(context.Context, int, string) (int, error)) *Codec_GetMaxDecodingSize_Call {
	_c.Call.Return(run)
	return _c
}

// GetMaxEncodingSize provides a mock function with given fields: ctx, n, itemType
func (_m *Codec) GetMaxEncodingSize(ctx context.Context, n int, itemType string) (int, error) {
	ret := _m.Called(ctx, n, itemType)

	if len(ret) == 0 {
		panic("no return value specified for GetMaxEncodingSize")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int, string) (int, error)); ok {
		return rf(ctx, n, itemType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, string) int); ok {
		r0 = rf(ctx, n, itemType)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, string) error); ok {
		r1 = rf(ctx, n, itemType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Codec_GetMaxEncodingSize_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMaxEncodingSize'
type Codec_GetMaxEncodingSize_Call struct {
	*mock.Call
}

// GetMaxEncodingSize is a helper method to define mock.On call
//   - ctx context.Context
//   - n int
//   - itemType string
func (_e *Codec_Expecter) GetMaxEncodingSize(ctx interface{}, n interface{}, itemType interface{}) *Codec_GetMaxEncodingSize_Call {
	return &Codec_GetMaxEncodingSize_Call{Call: _e.mock.On("GetMaxEncodingSize", ctx, n, itemType)}
}

func (_c *Codec_GetMaxEncodingSize_Call) Run(run func(ctx context.Context, n int, itemType string)) *Codec_GetMaxEncodingSize_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(string))
	})
	return _c
}

func (_c *Codec_GetMaxEncodingSize_Call) Return(_a0 int, _a1 error) *Codec_GetMaxEncodingSize_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Codec_GetMaxEncodingSize_Call) RunAndReturn(run func(context.Context, int, string) (int, error)) *Codec_GetMaxEncodingSize_Call {
	_c.Call.Return(run)
	return _c
}

// NewCodec creates a new instance of Codec. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCodec(t interface {
	mock.TestingT
	Cleanup(func())
}) *Codec {
	mock := &Codec{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
