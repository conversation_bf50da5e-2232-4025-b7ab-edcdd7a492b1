// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	synchronization "github.com/smartcontractkit/chainlink/v2/core/services/synchronization"
	mock "github.com/stretchr/testify/mock"
)

// TelemetryService is an autogenerated mock type for the TelemetryService type
type TelemetryService struct {
	mock.Mock
}

type TelemetryService_Expecter struct {
	mock *mock.Mock
}

func (_m *TelemetryService) EXPECT() *TelemetryService_Expecter {
	return &TelemetryService_Expecter{mock: &_m.Mock}
}

// Close provides a mock function with given fields:
func (_m *TelemetryService) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TelemetryService_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type TelemetryService_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *TelemetryService_Expecter) Close() *TelemetryService_Close_Call {
	return &TelemetryService_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *TelemetryService_Close_Call) Run(run func()) *TelemetryService_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TelemetryService_Close_Call) Return(_a0 error) *TelemetryService_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TelemetryService_Close_Call) RunAndReturn(run func() error) *TelemetryService_Close_Call {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with given fields:
func (_m *TelemetryService) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// TelemetryService_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type TelemetryService_HealthReport_Call struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *TelemetryService_Expecter) HealthReport() *TelemetryService_HealthReport_Call {
	return &TelemetryService_HealthReport_Call{Call: _e.mock.On("HealthReport")}
}

func (_c *TelemetryService_HealthReport_Call) Run(run func()) *TelemetryService_HealthReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TelemetryService_HealthReport_Call) Return(_a0 map[string]error) *TelemetryService_HealthReport_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TelemetryService_HealthReport_Call) RunAndReturn(run func() map[string]error) *TelemetryService_HealthReport_Call {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with given fields:
func (_m *TelemetryService) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// TelemetryService_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type TelemetryService_Name_Call struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *TelemetryService_Expecter) Name() *TelemetryService_Name_Call {
	return &TelemetryService_Name_Call{Call: _e.mock.On("Name")}
}

func (_c *TelemetryService_Name_Call) Run(run func()) *TelemetryService_Name_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TelemetryService_Name_Call) Return(_a0 string) *TelemetryService_Name_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TelemetryService_Name_Call) RunAndReturn(run func() string) *TelemetryService_Name_Call {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with given fields:
func (_m *TelemetryService) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TelemetryService_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type TelemetryService_Ready_Call struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *TelemetryService_Expecter) Ready() *TelemetryService_Ready_Call {
	return &TelemetryService_Ready_Call{Call: _e.mock.On("Ready")}
}

func (_c *TelemetryService_Ready_Call) Run(run func()) *TelemetryService_Ready_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *TelemetryService_Ready_Call) Return(_a0 error) *TelemetryService_Ready_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TelemetryService_Ready_Call) RunAndReturn(run func() error) *TelemetryService_Ready_Call {
	_c.Call.Return(run)
	return _c
}

// Send provides a mock function with given fields: ctx, telemetry, contractID, telemType
func (_m *TelemetryService) Send(ctx context.Context, telemetry []byte, contractID string, telemType synchronization.TelemetryType) {
	_m.Called(ctx, telemetry, contractID, telemType)
}

// TelemetryService_Send_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Send'
type TelemetryService_Send_Call struct {
	*mock.Call
}

// Send is a helper method to define mock.On call
//   - ctx context.Context
//   - telemetry []byte
//   - contractID string
//   - telemType synchronization.TelemetryType
func (_e *TelemetryService_Expecter) Send(ctx interface{}, telemetry interface{}, contractID interface{}, telemType interface{}) *TelemetryService_Send_Call {
	return &TelemetryService_Send_Call{Call: _e.mock.On("Send", ctx, telemetry, contractID, telemType)}
}

func (_c *TelemetryService_Send_Call) Run(run func(ctx context.Context, telemetry []byte, contractID string, telemType synchronization.TelemetryType)) *TelemetryService_Send_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]byte), args[2].(string), args[3].(synchronization.TelemetryType))
	})
	return _c
}

func (_c *TelemetryService_Send_Call) Return() *TelemetryService_Send_Call {
	_c.Call.Return()
	return _c
}

func (_c *TelemetryService_Send_Call) RunAndReturn(run func(context.Context, []byte, string, synchronization.TelemetryType)) *TelemetryService_Send_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *TelemetryService) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// TelemetryService_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type TelemetryService_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *TelemetryService_Expecter) Start(_a0 interface{}) *TelemetryService_Start_Call {
	return &TelemetryService_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *TelemetryService_Start_Call) Run(run func(_a0 context.Context)) *TelemetryService_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *TelemetryService_Start_Call) Return(_a0 error) *TelemetryService_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *TelemetryService_Start_Call) RunAndReturn(run func(context.Context) error) *TelemetryService_Start_Call {
	_c.Call.Return(run)
	return _c
}

// NewTelemetryService creates a new instance of TelemetryService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTelemetryService(t interface {
	mock.TestingT
	Cleanup(func())
}) *TelemetryService {
	mock := &TelemetryService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
