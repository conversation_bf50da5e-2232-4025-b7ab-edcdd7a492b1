// Code generated by protoc-gen-go-wsrpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-wsrpc v0.0.1
// - protoc             v5.28.0

package telem

import (
	context "context"
	wsrpc "github.com/smartcontractkit/wsrpc"
)

// TelemClient is the client API for Telem service.
type TelemClient interface {
	Telem(ctx context.Context, in *TelemRequest) (*TelemResponse, error)
	TelemBatch(ctx context.Context, in *TelemBatchRequest) (*TelemResponse, error)
}

type telemClient struct {
	cc wsrpc.ClientInterface
}

func NewTelemClient(cc wsrpc.ClientInterface) TelemClient {
	return &telemClient{cc}
}

func (c *telemClient) Telem(ctx context.Context, in *TelemRequest) (*TelemResponse, error) {
	out := new(TelemResponse)
	err := c.cc.Invoke(ctx, "Telem", in, out)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *telemClient) TelemBatch(ctx context.Context, in *TelemBatchRequest) (*TelemResponse, error) {
	out := new(TelemResponse)
	err := c.cc.Invoke(ctx, "TelemBatch", in, out)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TelemServer is the server API for Telem service.
type TelemServer interface {
	Telem(context.Context, *TelemRequest) (*TelemResponse, error)
	TelemBatch(context.Context, *TelemBatchRequest) (*TelemResponse, error)
}

func RegisterTelemServer(s wsrpc.ServiceRegistrar, srv TelemServer) {
	s.RegisterService(&Telem_ServiceDesc, srv)
}

func _Telem_Telem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error) (interface{}, error) {
	in := new(TelemRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	return srv.(TelemServer).Telem(ctx, in)
}

func _Telem_TelemBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error) (interface{}, error) {
	in := new(TelemBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	return srv.(TelemServer).TelemBatch(ctx, in)
}

// Telem_ServiceDesc is the wsrpc.ServiceDesc for Telem service.
// It's only intended for direct use with wsrpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Telem_ServiceDesc = wsrpc.ServiceDesc{
	ServiceName: "telem.Telem",
	HandlerType: (*TelemServer)(nil),
	Methods: []wsrpc.MethodDesc{
		{
			MethodName: "Telem",
			Handler:    _Telem_Telem_Handler,
		},
		{
			MethodName: "TelemBatch",
			Handler:    _Telem_TelemBatch_Handler,
		},
	},
}
