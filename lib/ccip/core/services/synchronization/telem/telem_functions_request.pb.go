// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.0
// source: core/services/synchronization/telem/telem_functions_request.proto

package telem

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FunctionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId   string   `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	NodeAddress string   `protobuf:"bytes,2,opt,name=node_address,json=nodeAddress,proto3" json:"node_address,omitempty"`
	Domains     []string `protobuf:"bytes,3,rep,name=domains,proto3" json:"domains,omitempty"`
}

func (x *FunctionsRequest) Reset() {
	*x = FunctionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_services_synchronization_telem_telem_functions_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunctionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunctionsRequest) ProtoMessage() {}

func (x *FunctionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_core_services_synchronization_telem_telem_functions_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunctionsRequest.ProtoReflect.Descriptor instead.
func (*FunctionsRequest) Descriptor() ([]byte, []int) {
	return file_core_services_synchronization_telem_telem_functions_request_proto_rawDescGZIP(), []int{0}
}

func (x *FunctionsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *FunctionsRequest) GetNodeAddress() string {
	if x != nil {
		return x.NodeAddress
	}
	return ""
}

func (x *FunctionsRequest) GetDomains() []string {
	if x != nil {
		return x.Domains
	}
	return nil
}

var File_core_services_synchronization_telem_telem_functions_request_proto protoreflect.FileDescriptor

var file_core_services_synchronization_telem_telem_functions_request_proto_rawDesc = []byte{
	0x0a, 0x41, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x73, 0x79, 0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x74, 0x65, 0x6c, 0x65, 0x6d, 0x2f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x66, 0x75, 0x6e, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x22, 0x6e, 0x0a, 0x10, 0x46, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x73, 0x42, 0x4e, 0x5a, 0x4c, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6b, 0x69, 0x74, 0x2f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x6c,
	0x69, 0x6e, 0x6b, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_core_services_synchronization_telem_telem_functions_request_proto_rawDescOnce sync.Once
	file_core_services_synchronization_telem_telem_functions_request_proto_rawDescData = file_core_services_synchronization_telem_telem_functions_request_proto_rawDesc
)

func file_core_services_synchronization_telem_telem_functions_request_proto_rawDescGZIP() []byte {
	file_core_services_synchronization_telem_telem_functions_request_proto_rawDescOnce.Do(func() {
		file_core_services_synchronization_telem_telem_functions_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_core_services_synchronization_telem_telem_functions_request_proto_rawDescData)
	})
	return file_core_services_synchronization_telem_telem_functions_request_proto_rawDescData
}

var file_core_services_synchronization_telem_telem_functions_request_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_core_services_synchronization_telem_telem_functions_request_proto_goTypes = []any{
	(*FunctionsRequest)(nil), // 0: telem.FunctionsRequest
}
var file_core_services_synchronization_telem_telem_functions_request_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_core_services_synchronization_telem_telem_functions_request_proto_init() }
func file_core_services_synchronization_telem_telem_functions_request_proto_init() {
	if File_core_services_synchronization_telem_telem_functions_request_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_core_services_synchronization_telem_telem_functions_request_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*FunctionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_core_services_synchronization_telem_telem_functions_request_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_core_services_synchronization_telem_telem_functions_request_proto_goTypes,
		DependencyIndexes: file_core_services_synchronization_telem_telem_functions_request_proto_depIdxs,
		MessageInfos:      file_core_services_synchronization_telem_telem_functions_request_proto_msgTypes,
	}.Build()
	File_core_services_synchronization_telem_telem_functions_request_proto = out.File
	file_core_services_synchronization_telem_telem_functions_request_proto_rawDesc = nil
	file_core_services_synchronization_telem_telem_functions_request_proto_goTypes = nil
	file_core_services_synchronization_telem_telem_functions_request_proto_depIdxs = nil
}
