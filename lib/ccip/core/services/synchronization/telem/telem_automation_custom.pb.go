// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.28.0
// source: core/services/synchronization/telem/telem_automation_custom.proto

package telem

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BlockNumber struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp    uint64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	BlockNumber  uint64 `protobuf:"varint,2,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	BlockHash    string `protobuf:"bytes,3,opt,name=block_hash,json=blockHash,proto3" json:"block_hash,omitempty"`
	ConfigDigest []byte `protobuf:"bytes,4,opt,name=config_digest,json=configDigest,proto3" json:"config_digest,omitempty"`
}

func (x *BlockNumber) Reset() {
	*x = BlockNumber{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockNumber) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockNumber) ProtoMessage() {}

func (x *BlockNumber) ProtoReflect() protoreflect.Message {
	mi := &file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockNumber.ProtoReflect.Descriptor instead.
func (*BlockNumber) Descriptor() ([]byte, []int) {
	return file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescGZIP(), []int{0}
}

func (x *BlockNumber) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *BlockNumber) GetBlockNumber() uint64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *BlockNumber) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *BlockNumber) GetConfigDigest() []byte {
	if x != nil {
		return x.ConfigDigest
	}
	return nil
}

type NodeVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp    uint64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	NodeVersion  string `protobuf:"bytes,2,opt,name=node_version,json=nodeVersion,proto3" json:"node_version,omitempty"`
	ConfigDigest []byte `protobuf:"bytes,3,opt,name=config_digest,json=configDigest,proto3" json:"config_digest,omitempty"`
}

func (x *NodeVersion) Reset() {
	*x = NodeVersion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeVersion) ProtoMessage() {}

func (x *NodeVersion) ProtoReflect() protoreflect.Message {
	mi := &file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeVersion.ProtoReflect.Descriptor instead.
func (*NodeVersion) Descriptor() ([]byte, []int) {
	return file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescGZIP(), []int{1}
}

func (x *NodeVersion) GetTimestamp() uint64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *NodeVersion) GetNodeVersion() string {
	if x != nil {
		return x.NodeVersion
	}
	return ""
}

func (x *NodeVersion) GetConfigDigest() []byte {
	if x != nil {
		return x.ConfigDigest
	}
	return nil
}

type AutomationTelemWrapper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Msg:
	//
	//	*AutomationTelemWrapper_BlockNumber
	//	*AutomationTelemWrapper_NodeVersion
	Msg isAutomationTelemWrapper_Msg `protobuf_oneof:"msg"`
}

func (x *AutomationTelemWrapper) Reset() {
	*x = AutomationTelemWrapper{}
	if protoimpl.UnsafeEnabled {
		mi := &file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AutomationTelemWrapper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutomationTelemWrapper) ProtoMessage() {}

func (x *AutomationTelemWrapper) ProtoReflect() protoreflect.Message {
	mi := &file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutomationTelemWrapper.ProtoReflect.Descriptor instead.
func (*AutomationTelemWrapper) Descriptor() ([]byte, []int) {
	return file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescGZIP(), []int{2}
}

func (m *AutomationTelemWrapper) GetMsg() isAutomationTelemWrapper_Msg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (x *AutomationTelemWrapper) GetBlockNumber() *BlockNumber {
	if x, ok := x.GetMsg().(*AutomationTelemWrapper_BlockNumber); ok {
		return x.BlockNumber
	}
	return nil
}

func (x *AutomationTelemWrapper) GetNodeVersion() *NodeVersion {
	if x, ok := x.GetMsg().(*AutomationTelemWrapper_NodeVersion); ok {
		return x.NodeVersion
	}
	return nil
}

type isAutomationTelemWrapper_Msg interface {
	isAutomationTelemWrapper_Msg()
}

type AutomationTelemWrapper_BlockNumber struct {
	BlockNumber *BlockNumber `protobuf:"bytes,1,opt,name=block_number,json=blockNumber,proto3,oneof"`
}

type AutomationTelemWrapper_NodeVersion struct {
	NodeVersion *NodeVersion `protobuf:"bytes,2,opt,name=node_version,json=nodeVersion,proto3,oneof"`
}

func (*AutomationTelemWrapper_BlockNumber) isAutomationTelemWrapper_Msg() {}

func (*AutomationTelemWrapper_NodeVersion) isAutomationTelemWrapper_Msg() {}

var File_core_services_synchronization_telem_telem_automation_custom_proto protoreflect.FileDescriptor

var file_core_services_synchronization_telem_telem_automation_custom_proto_rawDesc = []byte{
	0x0a, 0x41, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x73, 0x79, 0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x74, 0x65, 0x6c, 0x65, 0x6d, 0x2f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x22, 0x92, 0x01, 0x0a, 0x0b, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x61, 0x73, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x69, 0x67, 0x65, 0x73, 0x74, 0x22,
	0x73, 0x0a, 0x0b, 0x4e, 0x6f, 0x64, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c,
	0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x21, 0x0a, 0x0c,
	0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x64, 0x69, 0x67, 0x65, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x69,
	0x67, 0x65, 0x73, 0x74, 0x22, 0x91, 0x01, 0x0a, 0x16, 0x41, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x65, 0x6c, 0x65, 0x6d, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x12,
	0x37, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x2e, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x62, 0x6c, 0x6f,
	0x63, 0x6b, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x0c, 0x6e, 0x6f, 0x64, 0x65,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x42, 0x05, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x42, 0x4e, 0x5a, 0x4c, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73, 0x6d, 0x61, 0x72, 0x74, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6b, 0x69, 0x74, 0x2f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x6c, 0x69, 0x6e,
	0x6b, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x68, 0x72, 0x6f, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescOnce sync.Once
	file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescData = file_core_services_synchronization_telem_telem_automation_custom_proto_rawDesc
)

func file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescGZIP() []byte {
	file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescOnce.Do(func() {
		file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescData = protoimpl.X.CompressGZIP(file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescData)
	})
	return file_core_services_synchronization_telem_telem_automation_custom_proto_rawDescData
}

var file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_core_services_synchronization_telem_telem_automation_custom_proto_goTypes = []any{
	(*BlockNumber)(nil),            // 0: telem.BlockNumber
	(*NodeVersion)(nil),            // 1: telem.NodeVersion
	(*AutomationTelemWrapper)(nil), // 2: telem.AutomationTelemWrapper
}
var file_core_services_synchronization_telem_telem_automation_custom_proto_depIdxs = []int32{
	0, // 0: telem.AutomationTelemWrapper.block_number:type_name -> telem.BlockNumber
	1, // 1: telem.AutomationTelemWrapper.node_version:type_name -> telem.NodeVersion
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_core_services_synchronization_telem_telem_automation_custom_proto_init() }
func file_core_services_synchronization_telem_telem_automation_custom_proto_init() {
	if File_core_services_synchronization_telem_telem_automation_custom_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*BlockNumber); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*NodeVersion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*AutomationTelemWrapper); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes[2].OneofWrappers = []any{
		(*AutomationTelemWrapper_BlockNumber)(nil),
		(*AutomationTelemWrapper_NodeVersion)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_core_services_synchronization_telem_telem_automation_custom_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_core_services_synchronization_telem_telem_automation_custom_proto_goTypes,
		DependencyIndexes: file_core_services_synchronization_telem_telem_automation_custom_proto_depIdxs,
		MessageInfos:      file_core_services_synchronization_telem_telem_automation_custom_proto_msgTypes,
	}.Build()
	File_core_services_synchronization_telem_telem_automation_custom_proto = out.File
	file_core_services_synchronization_telem_telem_automation_custom_proto_rawDesc = nil
	file_core_services_synchronization_telem_telem_automation_custom_proto_goTypes = nil
	file_core_services_synchronization_telem_telem_automation_custom_proto_depIdxs = nil
}
