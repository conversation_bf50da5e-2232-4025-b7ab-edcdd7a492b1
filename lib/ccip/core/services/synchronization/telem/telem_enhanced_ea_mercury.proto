syntax = "proto3";

option go_package = "github.com/smartcontractkit/chainlink/v2/core/services/synchronization/telem";

package telem;

enum MarketStatus {
  // Same values as those used by OCR.
  UNKNOWN = 0;
  CLOSED = 1;
  OPEN = 2;
}

message EnhancedEAMercury {
  uint32 version = 32;

  string data_source=1;
  double dp_benchmark_price=2;
  double dp_bid=3;
  double dp_ask=4;
  bool dp_invariant_violation_detected=33;
  string bridge_request_data = 35;

  // v1 fields (block range)
  int64 current_block_number=5;
  string current_block_hash=6;
  uint64 current_block_timestamp=7;

  // v2+v3 fields (timestamp range)
  bool fetch_max_finalized_timestamp = 25;
  int64 max_finalized_timestamp=26;
  uint32 observation_timestamp=27;
  bool is_link_feed=28;
  int64 link_price=29;
  bool is_native_feed=30;
  int64 native_price=31;

  int64 bridge_task_run_started_timestamp=8;
  int64 bridge_task_run_ended_timestamp=9;
  int64 provider_requested_timestamp=10;
  int64 provider_received_timestamp=11;
  int64 provider_data_stream_established=12;
  int64 provider_indicated_time=13;

  string feed=14;

  // v1+v2+v3+v4
  int64 observation_benchmark_price=15; // This value overflows, will be reserved and removed in future versions
  string observation_benchmark_price_string = 22;
  // v1+v3
  int64 observation_bid=16; // This value overflows, will be reserved and removed in future versions
  int64 observation_ask=17; // This value overflows, will be reserved and removed in future versions
  string observation_bid_string = 23;
  string observation_ask_string = 24;
  // v4
  MarketStatus observation_market_status=34;

  string config_digest = 18;
  int64 round=19;
  int64 epoch=20;
  string asset_symbol=21;
}
