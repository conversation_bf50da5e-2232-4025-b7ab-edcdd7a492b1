// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	big "math/big"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	event "github.com/ethereum/go-ethereum/event"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"

	vrf_coordinator_v2 "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated/vrf_coordinator_v2"
)

// VRFCoordinatorV2Interface is an autogenerated mock type for the VRFCoordinatorV2Interface type
type VRFCoordinatorV2Interface struct {
	mock.Mock
}

type VRFCoordinatorV2Interface_Expecter struct {
	mock *mock.Mock
}

func (_m *VRFCoordinatorV2Interface) EXPECT() *VRFCoordinatorV2Interface_Expecter {
	return &VRFCoordinatorV2Interface_Expecter{mock: &_m.Mock}
}

// AcceptOwnership provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for AcceptOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_AcceptOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptOwnership'
type VRFCoordinatorV2Interface_AcceptOwnership_Call struct {
	*mock.Call
}

// AcceptOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *VRFCoordinatorV2Interface_Expecter) AcceptOwnership(opts interface{}) *VRFCoordinatorV2Interface_AcceptOwnership_Call {
	return &VRFCoordinatorV2Interface_AcceptOwnership_Call{Call: _e.mock.On("AcceptOwnership", opts)}
}

func (_c *VRFCoordinatorV2Interface_AcceptOwnership_Call) Run(run func(opts *bind.TransactOpts)) *VRFCoordinatorV2Interface_AcceptOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_AcceptOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_AcceptOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_AcceptOwnership_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *VRFCoordinatorV2Interface_AcceptOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// AcceptSubscriptionOwnerTransfer provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) AcceptSubscriptionOwnerTransfer(opts *bind.TransactOpts, subId uint64) (*types.Transaction, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for AcceptSubscriptionOwnerTransfer")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64) (*types.Transaction, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64) *types.Transaction); ok {
		r0 = rf(opts, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptSubscriptionOwnerTransfer'
type VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call struct {
	*mock.Call
}

// AcceptSubscriptionOwnerTransfer is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - subId uint64
func (_e *VRFCoordinatorV2Interface_Expecter) AcceptSubscriptionOwnerTransfer(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call {
	return &VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call{Call: _e.mock.On("AcceptSubscriptionOwnerTransfer", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call) Run(run func(opts *bind.TransactOpts, subId uint64)) *VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call) RunAndReturn(run func(*bind.TransactOpts, uint64) (*types.Transaction, error)) *VRFCoordinatorV2Interface_AcceptSubscriptionOwnerTransfer_Call {
	_c.Call.Return(run)
	return _c
}

// AddConsumer provides a mock function with given fields: opts, subId, consumer
func (_m *VRFCoordinatorV2Interface) AddConsumer(opts *bind.TransactOpts, subId uint64, consumer common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, subId, consumer)

	if len(ret) == 0 {
		panic("no return value specified for AddConsumer")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, subId, consumer)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, common.Address) *types.Transaction); ok {
		r0 = rf(opts, subId, consumer)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, uint64, common.Address) error); ok {
		r1 = rf(opts, subId, consumer)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_AddConsumer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddConsumer'
type VRFCoordinatorV2Interface_AddConsumer_Call struct {
	*mock.Call
}

// AddConsumer is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - subId uint64
//   - consumer common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) AddConsumer(opts interface{}, subId interface{}, consumer interface{}) *VRFCoordinatorV2Interface_AddConsumer_Call {
	return &VRFCoordinatorV2Interface_AddConsumer_Call{Call: _e.mock.On("AddConsumer", opts, subId, consumer)}
}

func (_c *VRFCoordinatorV2Interface_AddConsumer_Call) Run(run func(opts *bind.TransactOpts, subId uint64, consumer common.Address)) *VRFCoordinatorV2Interface_AddConsumer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(uint64), args[2].(common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_AddConsumer_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_AddConsumer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_AddConsumer_Call) RunAndReturn(run func(*bind.TransactOpts, uint64, common.Address) (*types.Transaction, error)) *VRFCoordinatorV2Interface_AddConsumer_Call {
	_c.Call.Return(run)
	return _c
}

// Address provides a mock function with given fields:
func (_m *VRFCoordinatorV2Interface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// VRFCoordinatorV2Interface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type VRFCoordinatorV2Interface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *VRFCoordinatorV2Interface_Expecter) Address() *VRFCoordinatorV2Interface_Address_Call {
	return &VRFCoordinatorV2Interface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *VRFCoordinatorV2Interface_Address_Call) Run(run func()) *VRFCoordinatorV2Interface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_Address_Call) Return(_a0 common.Address) *VRFCoordinatorV2Interface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *VRFCoordinatorV2Interface_Address_Call) RunAndReturn(run func() common.Address) *VRFCoordinatorV2Interface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// BLOCKHASHSTORE provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) BLOCKHASHSTORE(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for BLOCKHASHSTORE")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BLOCKHASHSTORE'
type VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call struct {
	*mock.Call
}

// BLOCKHASHSTORE is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) BLOCKHASHSTORE(opts interface{}) *VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call {
	return &VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call{Call: _e.mock.On("BLOCKHASHSTORE", opts)}
}

func (_c *VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call) Return(_a0 common.Address, _a1 error) *VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *VRFCoordinatorV2Interface_BLOCKHASHSTORE_Call {
	_c.Call.Return(run)
	return _c
}

// CancelSubscription provides a mock function with given fields: opts, subId, to
func (_m *VRFCoordinatorV2Interface) CancelSubscription(opts *bind.TransactOpts, subId uint64, to common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, subId, to)

	if len(ret) == 0 {
		panic("no return value specified for CancelSubscription")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, subId, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, common.Address) *types.Transaction); ok {
		r0 = rf(opts, subId, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, uint64, common.Address) error); ok {
		r1 = rf(opts, subId, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_CancelSubscription_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CancelSubscription'
type VRFCoordinatorV2Interface_CancelSubscription_Call struct {
	*mock.Call
}

// CancelSubscription is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - subId uint64
//   - to common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) CancelSubscription(opts interface{}, subId interface{}, to interface{}) *VRFCoordinatorV2Interface_CancelSubscription_Call {
	return &VRFCoordinatorV2Interface_CancelSubscription_Call{Call: _e.mock.On("CancelSubscription", opts, subId, to)}
}

func (_c *VRFCoordinatorV2Interface_CancelSubscription_Call) Run(run func(opts *bind.TransactOpts, subId uint64, to common.Address)) *VRFCoordinatorV2Interface_CancelSubscription_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(uint64), args[2].(common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_CancelSubscription_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_CancelSubscription_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_CancelSubscription_Call) RunAndReturn(run func(*bind.TransactOpts, uint64, common.Address) (*types.Transaction, error)) *VRFCoordinatorV2Interface_CancelSubscription_Call {
	_c.Call.Return(run)
	return _c
}

// CreateSubscription provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) CreateSubscription(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for CreateSubscription")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_CreateSubscription_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateSubscription'
type VRFCoordinatorV2Interface_CreateSubscription_Call struct {
	*mock.Call
}

// CreateSubscription is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *VRFCoordinatorV2Interface_Expecter) CreateSubscription(opts interface{}) *VRFCoordinatorV2Interface_CreateSubscription_Call {
	return &VRFCoordinatorV2Interface_CreateSubscription_Call{Call: _e.mock.On("CreateSubscription", opts)}
}

func (_c *VRFCoordinatorV2Interface_CreateSubscription_Call) Run(run func(opts *bind.TransactOpts)) *VRFCoordinatorV2Interface_CreateSubscription_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_CreateSubscription_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_CreateSubscription_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_CreateSubscription_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *VRFCoordinatorV2Interface_CreateSubscription_Call {
	_c.Call.Return(run)
	return _c
}

// DeregisterProvingKey provides a mock function with given fields: opts, publicProvingKey
func (_m *VRFCoordinatorV2Interface) DeregisterProvingKey(opts *bind.TransactOpts, publicProvingKey [2]*big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, publicProvingKey)

	if len(ret) == 0 {
		panic("no return value specified for DeregisterProvingKey")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [2]*big.Int) (*types.Transaction, error)); ok {
		return rf(opts, publicProvingKey)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [2]*big.Int) *types.Transaction); ok {
		r0 = rf(opts, publicProvingKey)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, [2]*big.Int) error); ok {
		r1 = rf(opts, publicProvingKey)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_DeregisterProvingKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeregisterProvingKey'
type VRFCoordinatorV2Interface_DeregisterProvingKey_Call struct {
	*mock.Call
}

// DeregisterProvingKey is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - publicProvingKey [2]*big.Int
func (_e *VRFCoordinatorV2Interface_Expecter) DeregisterProvingKey(opts interface{}, publicProvingKey interface{}) *VRFCoordinatorV2Interface_DeregisterProvingKey_Call {
	return &VRFCoordinatorV2Interface_DeregisterProvingKey_Call{Call: _e.mock.On("DeregisterProvingKey", opts, publicProvingKey)}
}

func (_c *VRFCoordinatorV2Interface_DeregisterProvingKey_Call) Run(run func(opts *bind.TransactOpts, publicProvingKey [2]*big.Int)) *VRFCoordinatorV2Interface_DeregisterProvingKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([2]*big.Int))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_DeregisterProvingKey_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_DeregisterProvingKey_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_DeregisterProvingKey_Call) RunAndReturn(run func(*bind.TransactOpts, [2]*big.Int) (*types.Transaction, error)) *VRFCoordinatorV2Interface_DeregisterProvingKey_Call {
	_c.Call.Return(run)
	return _c
}

// FilterConfigSet provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) FilterConfigSet(opts *bind.FilterOpts) (*vrf_coordinator_v2.VRFCoordinatorV2ConfigSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterConfigSet")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2ConfigSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*vrf_coordinator_v2.VRFCoordinatorV2ConfigSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *vrf_coordinator_v2.VRFCoordinatorV2ConfigSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2ConfigSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterConfigSet'
type VRFCoordinatorV2Interface_FilterConfigSet_Call struct {
	*mock.Call
}

// FilterConfigSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *VRFCoordinatorV2Interface_Expecter) FilterConfigSet(opts interface{}) *VRFCoordinatorV2Interface_FilterConfigSet_Call {
	return &VRFCoordinatorV2Interface_FilterConfigSet_Call{Call: _e.mock.On("FilterConfigSet", opts)}
}

func (_c *VRFCoordinatorV2Interface_FilterConfigSet_Call) Run(run func(opts *bind.FilterOpts)) *VRFCoordinatorV2Interface_FilterConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterConfigSet_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2ConfigSetIterator, _a1 error) *VRFCoordinatorV2Interface_FilterConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterConfigSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*vrf_coordinator_v2.VRFCoordinatorV2ConfigSetIterator, error)) *VRFCoordinatorV2Interface_FilterConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterFundsRecovered provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) FilterFundsRecovered(opts *bind.FilterOpts) (*vrf_coordinator_v2.VRFCoordinatorV2FundsRecoveredIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterFundsRecovered")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2FundsRecoveredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*vrf_coordinator_v2.VRFCoordinatorV2FundsRecoveredIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *vrf_coordinator_v2.VRFCoordinatorV2FundsRecoveredIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2FundsRecoveredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterFundsRecovered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterFundsRecovered'
type VRFCoordinatorV2Interface_FilterFundsRecovered_Call struct {
	*mock.Call
}

// FilterFundsRecovered is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *VRFCoordinatorV2Interface_Expecter) FilterFundsRecovered(opts interface{}) *VRFCoordinatorV2Interface_FilterFundsRecovered_Call {
	return &VRFCoordinatorV2Interface_FilterFundsRecovered_Call{Call: _e.mock.On("FilterFundsRecovered", opts)}
}

func (_c *VRFCoordinatorV2Interface_FilterFundsRecovered_Call) Run(run func(opts *bind.FilterOpts)) *VRFCoordinatorV2Interface_FilterFundsRecovered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterFundsRecovered_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2FundsRecoveredIterator, _a1 error) *VRFCoordinatorV2Interface_FilterFundsRecovered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterFundsRecovered_Call) RunAndReturn(run func(*bind.FilterOpts) (*vrf_coordinator_v2.VRFCoordinatorV2FundsRecoveredIterator, error)) *VRFCoordinatorV2Interface_FilterFundsRecovered_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferRequested provides a mock function with given fields: opts, from, to
func (_m *VRFCoordinatorV2Interface) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequestedIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferRequested")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequestedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequestedIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequestedIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequestedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferRequested'
type VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call struct {
	*mock.Call
}

// FilterOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) FilterOwnershipTransferRequested(opts interface{}, from interface{}, to interface{}) *VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call {
	return &VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call{Call: _e.mock.On("FilterOwnershipTransferRequested", opts, from, to)}
}

func (_c *VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequestedIterator, _a1 error) *VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequestedIterator, error)) *VRFCoordinatorV2Interface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferred provides a mock function with given fields: opts, from, to
func (_m *VRFCoordinatorV2Interface) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferredIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferred")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferredIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferredIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferred'
type VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call struct {
	*mock.Call
}

// FilterOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) FilterOwnershipTransferred(opts interface{}, from interface{}, to interface{}) *VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call {
	return &VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call{Call: _e.mock.On("FilterOwnershipTransferred", opts, from, to)}
}

func (_c *VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferredIterator, _a1 error) *VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferredIterator, error)) *VRFCoordinatorV2Interface_FilterOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// FilterProvingKeyDeregistered provides a mock function with given fields: opts, oracle
func (_m *VRFCoordinatorV2Interface) FilterProvingKeyDeregistered(opts *bind.FilterOpts, oracle []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregisteredIterator, error) {
	ret := _m.Called(opts, oracle)

	if len(ret) == 0 {
		panic("no return value specified for FilterProvingKeyDeregistered")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregisteredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregisteredIterator, error)); ok {
		return rf(opts, oracle)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregisteredIterator); ok {
		r0 = rf(opts, oracle)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregisteredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, oracle)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterProvingKeyDeregistered'
type VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call struct {
	*mock.Call
}

// FilterProvingKeyDeregistered is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - oracle []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) FilterProvingKeyDeregistered(opts interface{}, oracle interface{}) *VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call {
	return &VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call{Call: _e.mock.On("FilterProvingKeyDeregistered", opts, oracle)}
}

func (_c *VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call) Run(run func(opts *bind.FilterOpts, oracle []common.Address)) *VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregisteredIterator, _a1 error) *VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregisteredIterator, error)) *VRFCoordinatorV2Interface_FilterProvingKeyDeregistered_Call {
	_c.Call.Return(run)
	return _c
}

// FilterProvingKeyRegistered provides a mock function with given fields: opts, oracle
func (_m *VRFCoordinatorV2Interface) FilterProvingKeyRegistered(opts *bind.FilterOpts, oracle []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegisteredIterator, error) {
	ret := _m.Called(opts, oracle)

	if len(ret) == 0 {
		panic("no return value specified for FilterProvingKeyRegistered")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegisteredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegisteredIterator, error)); ok {
		return rf(opts, oracle)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegisteredIterator); ok {
		r0 = rf(opts, oracle)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegisteredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, oracle)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterProvingKeyRegistered'
type VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call struct {
	*mock.Call
}

// FilterProvingKeyRegistered is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - oracle []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) FilterProvingKeyRegistered(opts interface{}, oracle interface{}) *VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call {
	return &VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call{Call: _e.mock.On("FilterProvingKeyRegistered", opts, oracle)}
}

func (_c *VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call) Run(run func(opts *bind.FilterOpts, oracle []common.Address)) *VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegisteredIterator, _a1 error) *VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegisteredIterator, error)) *VRFCoordinatorV2Interface_FilterProvingKeyRegistered_Call {
	_c.Call.Return(run)
	return _c
}

// FilterRandomWordsFulfilled provides a mock function with given fields: opts, requestId
func (_m *VRFCoordinatorV2Interface) FilterRandomWordsFulfilled(opts *bind.FilterOpts, requestId []*big.Int) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilledIterator, error) {
	ret := _m.Called(opts, requestId)

	if len(ret) == 0 {
		panic("no return value specified for FilterRandomWordsFulfilled")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilledIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []*big.Int) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilledIterator, error)); ok {
		return rf(opts, requestId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []*big.Int) *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilledIterator); ok {
		r0 = rf(opts, requestId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilledIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []*big.Int) error); ok {
		r1 = rf(opts, requestId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterRandomWordsFulfilled'
type VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call struct {
	*mock.Call
}

// FilterRandomWordsFulfilled is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - requestId []*big.Int
func (_e *VRFCoordinatorV2Interface_Expecter) FilterRandomWordsFulfilled(opts interface{}, requestId interface{}) *VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call {
	return &VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call{Call: _e.mock.On("FilterRandomWordsFulfilled", opts, requestId)}
}

func (_c *VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call) Run(run func(opts *bind.FilterOpts, requestId []*big.Int)) *VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]*big.Int))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilledIterator, _a1 error) *VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call) RunAndReturn(run func(*bind.FilterOpts, []*big.Int) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilledIterator, error)) *VRFCoordinatorV2Interface_FilterRandomWordsFulfilled_Call {
	_c.Call.Return(run)
	return _c
}

// FilterRandomWordsRequested provides a mock function with given fields: opts, keyHash, subId, sender
func (_m *VRFCoordinatorV2Interface) FilterRandomWordsRequested(opts *bind.FilterOpts, keyHash [][32]byte, subId []uint64, sender []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequestedIterator, error) {
	ret := _m.Called(opts, keyHash, subId, sender)

	if len(ret) == 0 {
		panic("no return value specified for FilterRandomWordsRequested")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequestedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, [][32]byte, []uint64, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequestedIterator, error)); ok {
		return rf(opts, keyHash, subId, sender)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, [][32]byte, []uint64, []common.Address) *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequestedIterator); ok {
		r0 = rf(opts, keyHash, subId, sender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequestedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, [][32]byte, []uint64, []common.Address) error); ok {
		r1 = rf(opts, keyHash, subId, sender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterRandomWordsRequested'
type VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call struct {
	*mock.Call
}

// FilterRandomWordsRequested is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - keyHash [][32]byte
//   - subId []uint64
//   - sender []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) FilterRandomWordsRequested(opts interface{}, keyHash interface{}, subId interface{}, sender interface{}) *VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call {
	return &VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call{Call: _e.mock.On("FilterRandomWordsRequested", opts, keyHash, subId, sender)}
}

func (_c *VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call) Run(run func(opts *bind.FilterOpts, keyHash [][32]byte, subId []uint64, sender []common.Address)) *VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([][32]byte), args[2].([]uint64), args[3].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequestedIterator, _a1 error) *VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call) RunAndReturn(run func(*bind.FilterOpts, [][32]byte, []uint64, []common.Address) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequestedIterator, error)) *VRFCoordinatorV2Interface_FilterRandomWordsRequested_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSubscriptionCanceled provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) FilterSubscriptionCanceled(opts *bind.FilterOpts, subId []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceledIterator, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for FilterSubscriptionCanceled")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceledIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceledIterator, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceledIterator); ok {
		r0 = rf(opts, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceledIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSubscriptionCanceled'
type VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call struct {
	*mock.Call
}

// FilterSubscriptionCanceled is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) FilterSubscriptionCanceled(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call {
	return &VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call{Call: _e.mock.On("FilterSubscriptionCanceled", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call) Run(run func(opts *bind.FilterOpts, subId []uint64)) *VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceledIterator, _a1 error) *VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceledIterator, error)) *VRFCoordinatorV2Interface_FilterSubscriptionCanceled_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSubscriptionConsumerAdded provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) FilterSubscriptionConsumerAdded(opts *bind.FilterOpts, subId []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAddedIterator, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for FilterSubscriptionConsumerAdded")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAddedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAddedIterator, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAddedIterator); ok {
		r0 = rf(opts, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAddedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSubscriptionConsumerAdded'
type VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call struct {
	*mock.Call
}

// FilterSubscriptionConsumerAdded is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) FilterSubscriptionConsumerAdded(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call {
	return &VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call{Call: _e.mock.On("FilterSubscriptionConsumerAdded", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call) Run(run func(opts *bind.FilterOpts, subId []uint64)) *VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAddedIterator, _a1 error) *VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAddedIterator, error)) *VRFCoordinatorV2Interface_FilterSubscriptionConsumerAdded_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSubscriptionConsumerRemoved provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) FilterSubscriptionConsumerRemoved(opts *bind.FilterOpts, subId []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemovedIterator, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for FilterSubscriptionConsumerRemoved")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemovedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemovedIterator, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemovedIterator); ok {
		r0 = rf(opts, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemovedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSubscriptionConsumerRemoved'
type VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call struct {
	*mock.Call
}

// FilterSubscriptionConsumerRemoved is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) FilterSubscriptionConsumerRemoved(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call {
	return &VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call{Call: _e.mock.On("FilterSubscriptionConsumerRemoved", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call) Run(run func(opts *bind.FilterOpts, subId []uint64)) *VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemovedIterator, _a1 error) *VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemovedIterator, error)) *VRFCoordinatorV2Interface_FilterSubscriptionConsumerRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSubscriptionCreated provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) FilterSubscriptionCreated(opts *bind.FilterOpts, subId []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreatedIterator, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for FilterSubscriptionCreated")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreatedIterator, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreatedIterator); ok {
		r0 = rf(opts, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSubscriptionCreated'
type VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call struct {
	*mock.Call
}

// FilterSubscriptionCreated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) FilterSubscriptionCreated(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call {
	return &VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call{Call: _e.mock.On("FilterSubscriptionCreated", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call) Run(run func(opts *bind.FilterOpts, subId []uint64)) *VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreatedIterator, _a1 error) *VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreatedIterator, error)) *VRFCoordinatorV2Interface_FilterSubscriptionCreated_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSubscriptionFunded provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) FilterSubscriptionFunded(opts *bind.FilterOpts, subId []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFundedIterator, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for FilterSubscriptionFunded")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFundedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFundedIterator, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFundedIterator); ok {
		r0 = rf(opts, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFundedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSubscriptionFunded'
type VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call struct {
	*mock.Call
}

// FilterSubscriptionFunded is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) FilterSubscriptionFunded(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call {
	return &VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call{Call: _e.mock.On("FilterSubscriptionFunded", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call) Run(run func(opts *bind.FilterOpts, subId []uint64)) *VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFundedIterator, _a1 error) *VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFundedIterator, error)) *VRFCoordinatorV2Interface_FilterSubscriptionFunded_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSubscriptionOwnerTransferRequested provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) FilterSubscriptionOwnerTransferRequested(opts *bind.FilterOpts, subId []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequestedIterator, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for FilterSubscriptionOwnerTransferRequested")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequestedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequestedIterator, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequestedIterator); ok {
		r0 = rf(opts, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequestedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSubscriptionOwnerTransferRequested'
type VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call struct {
	*mock.Call
}

// FilterSubscriptionOwnerTransferRequested is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) FilterSubscriptionOwnerTransferRequested(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call {
	return &VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call{Call: _e.mock.On("FilterSubscriptionOwnerTransferRequested", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call) Run(run func(opts *bind.FilterOpts, subId []uint64)) *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequestedIterator, _a1 error) *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequestedIterator, error)) *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSubscriptionOwnerTransferred provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) FilterSubscriptionOwnerTransferred(opts *bind.FilterOpts, subId []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferredIterator, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for FilterSubscriptionOwnerTransferred")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferredIterator, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferredIterator); ok {
		r0 = rf(opts, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSubscriptionOwnerTransferred'
type VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call struct {
	*mock.Call
}

// FilterSubscriptionOwnerTransferred is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) FilterSubscriptionOwnerTransferred(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call {
	return &VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call{Call: _e.mock.On("FilterSubscriptionOwnerTransferred", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call) Run(run func(opts *bind.FilterOpts, subId []uint64)) *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferredIterator, _a1 error) *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferredIterator, error)) *VRFCoordinatorV2Interface_FilterSubscriptionOwnerTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// FulfillRandomWords provides a mock function with given fields: opts, proof, rc
func (_m *VRFCoordinatorV2Interface) FulfillRandomWords(opts *bind.TransactOpts, proof vrf_coordinator_v2.VRFProof, rc vrf_coordinator_v2.VRFCoordinatorV2RequestCommitment) (*types.Transaction, error) {
	ret := _m.Called(opts, proof, rc)

	if len(ret) == 0 {
		panic("no return value specified for FulfillRandomWords")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, vrf_coordinator_v2.VRFProof, vrf_coordinator_v2.VRFCoordinatorV2RequestCommitment) (*types.Transaction, error)); ok {
		return rf(opts, proof, rc)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, vrf_coordinator_v2.VRFProof, vrf_coordinator_v2.VRFCoordinatorV2RequestCommitment) *types.Transaction); ok {
		r0 = rf(opts, proof, rc)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, vrf_coordinator_v2.VRFProof, vrf_coordinator_v2.VRFCoordinatorV2RequestCommitment) error); ok {
		r1 = rf(opts, proof, rc)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_FulfillRandomWords_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FulfillRandomWords'
type VRFCoordinatorV2Interface_FulfillRandomWords_Call struct {
	*mock.Call
}

// FulfillRandomWords is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - proof vrf_coordinator_v2.VRFProof
//   - rc vrf_coordinator_v2.VRFCoordinatorV2RequestCommitment
func (_e *VRFCoordinatorV2Interface_Expecter) FulfillRandomWords(opts interface{}, proof interface{}, rc interface{}) *VRFCoordinatorV2Interface_FulfillRandomWords_Call {
	return &VRFCoordinatorV2Interface_FulfillRandomWords_Call{Call: _e.mock.On("FulfillRandomWords", opts, proof, rc)}
}

func (_c *VRFCoordinatorV2Interface_FulfillRandomWords_Call) Run(run func(opts *bind.TransactOpts, proof vrf_coordinator_v2.VRFProof, rc vrf_coordinator_v2.VRFCoordinatorV2RequestCommitment)) *VRFCoordinatorV2Interface_FulfillRandomWords_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(vrf_coordinator_v2.VRFProof), args[2].(vrf_coordinator_v2.VRFCoordinatorV2RequestCommitment))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_FulfillRandomWords_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_FulfillRandomWords_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_FulfillRandomWords_Call) RunAndReturn(run func(*bind.TransactOpts, vrf_coordinator_v2.VRFProof, vrf_coordinator_v2.VRFCoordinatorV2RequestCommitment) (*types.Transaction, error)) *VRFCoordinatorV2Interface_FulfillRandomWords_Call {
	_c.Call.Return(run)
	return _c
}

// GetCommitment provides a mock function with given fields: opts, requestId
func (_m *VRFCoordinatorV2Interface) GetCommitment(opts *bind.CallOpts, requestId *big.Int) ([32]byte, error) {
	ret := _m.Called(opts, requestId)

	if len(ret) == 0 {
		panic("no return value specified for GetCommitment")
	}

	var r0 [32]byte
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) ([32]byte, error)); ok {
		return rf(opts, requestId)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) [32]byte); ok {
		r0 = rf(opts, requestId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([32]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, *big.Int) error); ok {
		r1 = rf(opts, requestId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_GetCommitment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCommitment'
type VRFCoordinatorV2Interface_GetCommitment_Call struct {
	*mock.Call
}

// GetCommitment is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - requestId *big.Int
func (_e *VRFCoordinatorV2Interface_Expecter) GetCommitment(opts interface{}, requestId interface{}) *VRFCoordinatorV2Interface_GetCommitment_Call {
	return &VRFCoordinatorV2Interface_GetCommitment_Call{Call: _e.mock.On("GetCommitment", opts, requestId)}
}

func (_c *VRFCoordinatorV2Interface_GetCommitment_Call) Run(run func(opts *bind.CallOpts, requestId *big.Int)) *VRFCoordinatorV2Interface_GetCommitment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(*big.Int))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetCommitment_Call) Return(_a0 [32]byte, _a1 error) *VRFCoordinatorV2Interface_GetCommitment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetCommitment_Call) RunAndReturn(run func(*bind.CallOpts, *big.Int) ([32]byte, error)) *VRFCoordinatorV2Interface_GetCommitment_Call {
	_c.Call.Return(run)
	return _c
}

// GetConfig provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) GetConfig(opts *bind.CallOpts) (vrf_coordinator_v2.GetConfig, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetConfig")
	}

	var r0 vrf_coordinator_v2.GetConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (vrf_coordinator_v2.GetConfig, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) vrf_coordinator_v2.GetConfig); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(vrf_coordinator_v2.GetConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_GetConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetConfig'
type VRFCoordinatorV2Interface_GetConfig_Call struct {
	*mock.Call
}

// GetConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) GetConfig(opts interface{}) *VRFCoordinatorV2Interface_GetConfig_Call {
	return &VRFCoordinatorV2Interface_GetConfig_Call{Call: _e.mock.On("GetConfig", opts)}
}

func (_c *VRFCoordinatorV2Interface_GetConfig_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_GetConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetConfig_Call) Return(_a0 vrf_coordinator_v2.GetConfig, _a1 error) *VRFCoordinatorV2Interface_GetConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetConfig_Call) RunAndReturn(run func(*bind.CallOpts) (vrf_coordinator_v2.GetConfig, error)) *VRFCoordinatorV2Interface_GetConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetCurrentSubId provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) GetCurrentSubId(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetCurrentSubId")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_GetCurrentSubId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCurrentSubId'
type VRFCoordinatorV2Interface_GetCurrentSubId_Call struct {
	*mock.Call
}

// GetCurrentSubId is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) GetCurrentSubId(opts interface{}) *VRFCoordinatorV2Interface_GetCurrentSubId_Call {
	return &VRFCoordinatorV2Interface_GetCurrentSubId_Call{Call: _e.mock.On("GetCurrentSubId", opts)}
}

func (_c *VRFCoordinatorV2Interface_GetCurrentSubId_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_GetCurrentSubId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetCurrentSubId_Call) Return(_a0 uint64, _a1 error) *VRFCoordinatorV2Interface_GetCurrentSubId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetCurrentSubId_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *VRFCoordinatorV2Interface_GetCurrentSubId_Call {
	_c.Call.Return(run)
	return _c
}

// GetFallbackWeiPerUnitLink provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) GetFallbackWeiPerUnitLink(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetFallbackWeiPerUnitLink")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFallbackWeiPerUnitLink'
type VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call struct {
	*mock.Call
}

// GetFallbackWeiPerUnitLink is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) GetFallbackWeiPerUnitLink(opts interface{}) *VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call {
	return &VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call{Call: _e.mock.On("GetFallbackWeiPerUnitLink", opts)}
}

func (_c *VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call) Return(_a0 *big.Int, _a1 error) *VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *VRFCoordinatorV2Interface_GetFallbackWeiPerUnitLink_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeeConfig provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) GetFeeConfig(opts *bind.CallOpts) (vrf_coordinator_v2.GetFeeConfig, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetFeeConfig")
	}

	var r0 vrf_coordinator_v2.GetFeeConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (vrf_coordinator_v2.GetFeeConfig, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) vrf_coordinator_v2.GetFeeConfig); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(vrf_coordinator_v2.GetFeeConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_GetFeeConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeeConfig'
type VRFCoordinatorV2Interface_GetFeeConfig_Call struct {
	*mock.Call
}

// GetFeeConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) GetFeeConfig(opts interface{}) *VRFCoordinatorV2Interface_GetFeeConfig_Call {
	return &VRFCoordinatorV2Interface_GetFeeConfig_Call{Call: _e.mock.On("GetFeeConfig", opts)}
}

func (_c *VRFCoordinatorV2Interface_GetFeeConfig_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_GetFeeConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetFeeConfig_Call) Return(_a0 vrf_coordinator_v2.GetFeeConfig, _a1 error) *VRFCoordinatorV2Interface_GetFeeConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetFeeConfig_Call) RunAndReturn(run func(*bind.CallOpts) (vrf_coordinator_v2.GetFeeConfig, error)) *VRFCoordinatorV2Interface_GetFeeConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeeTier provides a mock function with given fields: opts, reqCount
func (_m *VRFCoordinatorV2Interface) GetFeeTier(opts *bind.CallOpts, reqCount uint64) (uint32, error) {
	ret := _m.Called(opts, reqCount)

	if len(ret) == 0 {
		panic("no return value specified for GetFeeTier")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) (uint32, error)); ok {
		return rf(opts, reqCount)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) uint32); ok {
		r0 = rf(opts, reqCount)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, reqCount)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_GetFeeTier_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeeTier'
type VRFCoordinatorV2Interface_GetFeeTier_Call struct {
	*mock.Call
}

// GetFeeTier is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - reqCount uint64
func (_e *VRFCoordinatorV2Interface_Expecter) GetFeeTier(opts interface{}, reqCount interface{}) *VRFCoordinatorV2Interface_GetFeeTier_Call {
	return &VRFCoordinatorV2Interface_GetFeeTier_Call{Call: _e.mock.On("GetFeeTier", opts, reqCount)}
}

func (_c *VRFCoordinatorV2Interface_GetFeeTier_Call) Run(run func(opts *bind.CallOpts, reqCount uint64)) *VRFCoordinatorV2Interface_GetFeeTier_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetFeeTier_Call) Return(_a0 uint32, _a1 error) *VRFCoordinatorV2Interface_GetFeeTier_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetFeeTier_Call) RunAndReturn(run func(*bind.CallOpts, uint64) (uint32, error)) *VRFCoordinatorV2Interface_GetFeeTier_Call {
	_c.Call.Return(run)
	return _c
}

// GetRequestConfig provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) GetRequestConfig(opts *bind.CallOpts) (uint16, uint32, [][32]byte, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetRequestConfig")
	}

	var r0 uint16
	var r1 uint32
	var r2 [][32]byte
	var r3 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint16, uint32, [][32]byte, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint16); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint16)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) uint32); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Get(1).(uint32)
	}

	if rf, ok := ret.Get(2).(func(*bind.CallOpts) [][32]byte); ok {
		r2 = rf(opts)
	} else {
		if ret.Get(2) != nil {
			r2 = ret.Get(2).([][32]byte)
		}
	}

	if rf, ok := ret.Get(3).(func(*bind.CallOpts) error); ok {
		r3 = rf(opts)
	} else {
		r3 = ret.Error(3)
	}

	return r0, r1, r2, r3
}

// VRFCoordinatorV2Interface_GetRequestConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRequestConfig'
type VRFCoordinatorV2Interface_GetRequestConfig_Call struct {
	*mock.Call
}

// GetRequestConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) GetRequestConfig(opts interface{}) *VRFCoordinatorV2Interface_GetRequestConfig_Call {
	return &VRFCoordinatorV2Interface_GetRequestConfig_Call{Call: _e.mock.On("GetRequestConfig", opts)}
}

func (_c *VRFCoordinatorV2Interface_GetRequestConfig_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_GetRequestConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetRequestConfig_Call) Return(_a0 uint16, _a1 uint32, _a2 [][32]byte, _a3 error) *VRFCoordinatorV2Interface_GetRequestConfig_Call {
	_c.Call.Return(_a0, _a1, _a2, _a3)
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetRequestConfig_Call) RunAndReturn(run func(*bind.CallOpts) (uint16, uint32, [][32]byte, error)) *VRFCoordinatorV2Interface_GetRequestConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetSubscription provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) GetSubscription(opts *bind.CallOpts, subId uint64) (vrf_coordinator_v2.GetSubscription, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for GetSubscription")
	}

	var r0 vrf_coordinator_v2.GetSubscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) (vrf_coordinator_v2.GetSubscription, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) vrf_coordinator_v2.GetSubscription); ok {
		r0 = rf(opts, subId)
	} else {
		r0 = ret.Get(0).(vrf_coordinator_v2.GetSubscription)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_GetSubscription_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSubscription'
type VRFCoordinatorV2Interface_GetSubscription_Call struct {
	*mock.Call
}

// GetSubscription is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - subId uint64
func (_e *VRFCoordinatorV2Interface_Expecter) GetSubscription(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_GetSubscription_Call {
	return &VRFCoordinatorV2Interface_GetSubscription_Call{Call: _e.mock.On("GetSubscription", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_GetSubscription_Call) Run(run func(opts *bind.CallOpts, subId uint64)) *VRFCoordinatorV2Interface_GetSubscription_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetSubscription_Call) Return(_a0 vrf_coordinator_v2.GetSubscription, _a1 error) *VRFCoordinatorV2Interface_GetSubscription_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetSubscription_Call) RunAndReturn(run func(*bind.CallOpts, uint64) (vrf_coordinator_v2.GetSubscription, error)) *VRFCoordinatorV2Interface_GetSubscription_Call {
	_c.Call.Return(run)
	return _c
}

// GetTotalBalance provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) GetTotalBalance(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetTotalBalance")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_GetTotalBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTotalBalance'
type VRFCoordinatorV2Interface_GetTotalBalance_Call struct {
	*mock.Call
}

// GetTotalBalance is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) GetTotalBalance(opts interface{}) *VRFCoordinatorV2Interface_GetTotalBalance_Call {
	return &VRFCoordinatorV2Interface_GetTotalBalance_Call{Call: _e.mock.On("GetTotalBalance", opts)}
}

func (_c *VRFCoordinatorV2Interface_GetTotalBalance_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_GetTotalBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetTotalBalance_Call) Return(_a0 *big.Int, _a1 error) *VRFCoordinatorV2Interface_GetTotalBalance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_GetTotalBalance_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *VRFCoordinatorV2Interface_GetTotalBalance_Call {
	_c.Call.Return(run)
	return _c
}

// HashOfKey provides a mock function with given fields: opts, publicKey
func (_m *VRFCoordinatorV2Interface) HashOfKey(opts *bind.CallOpts, publicKey [2]*big.Int) ([32]byte, error) {
	ret := _m.Called(opts, publicKey)

	if len(ret) == 0 {
		panic("no return value specified for HashOfKey")
	}

	var r0 [32]byte
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, [2]*big.Int) ([32]byte, error)); ok {
		return rf(opts, publicKey)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, [2]*big.Int) [32]byte); ok {
		r0 = rf(opts, publicKey)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([32]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, [2]*big.Int) error); ok {
		r1 = rf(opts, publicKey)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_HashOfKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HashOfKey'
type VRFCoordinatorV2Interface_HashOfKey_Call struct {
	*mock.Call
}

// HashOfKey is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - publicKey [2]*big.Int
func (_e *VRFCoordinatorV2Interface_Expecter) HashOfKey(opts interface{}, publicKey interface{}) *VRFCoordinatorV2Interface_HashOfKey_Call {
	return &VRFCoordinatorV2Interface_HashOfKey_Call{Call: _e.mock.On("HashOfKey", opts, publicKey)}
}

func (_c *VRFCoordinatorV2Interface_HashOfKey_Call) Run(run func(opts *bind.CallOpts, publicKey [2]*big.Int)) *VRFCoordinatorV2Interface_HashOfKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].([2]*big.Int))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_HashOfKey_Call) Return(_a0 [32]byte, _a1 error) *VRFCoordinatorV2Interface_HashOfKey_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_HashOfKey_Call) RunAndReturn(run func(*bind.CallOpts, [2]*big.Int) ([32]byte, error)) *VRFCoordinatorV2Interface_HashOfKey_Call {
	_c.Call.Return(run)
	return _c
}

// LINK provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) LINK(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LINK")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_LINK_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LINK'
type VRFCoordinatorV2Interface_LINK_Call struct {
	*mock.Call
}

// LINK is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) LINK(opts interface{}) *VRFCoordinatorV2Interface_LINK_Call {
	return &VRFCoordinatorV2Interface_LINK_Call{Call: _e.mock.On("LINK", opts)}
}

func (_c *VRFCoordinatorV2Interface_LINK_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_LINK_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_LINK_Call) Return(_a0 common.Address, _a1 error) *VRFCoordinatorV2Interface_LINK_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_LINK_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *VRFCoordinatorV2Interface_LINK_Call {
	_c.Call.Return(run)
	return _c
}

// LINKETHFEED provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) LINKETHFEED(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LINKETHFEED")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_LINKETHFEED_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LINKETHFEED'
type VRFCoordinatorV2Interface_LINKETHFEED_Call struct {
	*mock.Call
}

// LINKETHFEED is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) LINKETHFEED(opts interface{}) *VRFCoordinatorV2Interface_LINKETHFEED_Call {
	return &VRFCoordinatorV2Interface_LINKETHFEED_Call{Call: _e.mock.On("LINKETHFEED", opts)}
}

func (_c *VRFCoordinatorV2Interface_LINKETHFEED_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_LINKETHFEED_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_LINKETHFEED_Call) Return(_a0 common.Address, _a1 error) *VRFCoordinatorV2Interface_LINKETHFEED_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_LINKETHFEED_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *VRFCoordinatorV2Interface_LINKETHFEED_Call {
	_c.Call.Return(run)
	return _c
}

// MAXCONSUMERS provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) MAXCONSUMERS(opts *bind.CallOpts) (uint16, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for MAXCONSUMERS")
	}

	var r0 uint16
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint16, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint16); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint16)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_MAXCONSUMERS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MAXCONSUMERS'
type VRFCoordinatorV2Interface_MAXCONSUMERS_Call struct {
	*mock.Call
}

// MAXCONSUMERS is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) MAXCONSUMERS(opts interface{}) *VRFCoordinatorV2Interface_MAXCONSUMERS_Call {
	return &VRFCoordinatorV2Interface_MAXCONSUMERS_Call{Call: _e.mock.On("MAXCONSUMERS", opts)}
}

func (_c *VRFCoordinatorV2Interface_MAXCONSUMERS_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_MAXCONSUMERS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_MAXCONSUMERS_Call) Return(_a0 uint16, _a1 error) *VRFCoordinatorV2Interface_MAXCONSUMERS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_MAXCONSUMERS_Call) RunAndReturn(run func(*bind.CallOpts) (uint16, error)) *VRFCoordinatorV2Interface_MAXCONSUMERS_Call {
	_c.Call.Return(run)
	return _c
}

// MAXNUMWORDS provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) MAXNUMWORDS(opts *bind.CallOpts) (uint32, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for MAXNUMWORDS")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint32, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint32); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_MAXNUMWORDS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MAXNUMWORDS'
type VRFCoordinatorV2Interface_MAXNUMWORDS_Call struct {
	*mock.Call
}

// MAXNUMWORDS is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) MAXNUMWORDS(opts interface{}) *VRFCoordinatorV2Interface_MAXNUMWORDS_Call {
	return &VRFCoordinatorV2Interface_MAXNUMWORDS_Call{Call: _e.mock.On("MAXNUMWORDS", opts)}
}

func (_c *VRFCoordinatorV2Interface_MAXNUMWORDS_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_MAXNUMWORDS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_MAXNUMWORDS_Call) Return(_a0 uint32, _a1 error) *VRFCoordinatorV2Interface_MAXNUMWORDS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_MAXNUMWORDS_Call) RunAndReturn(run func(*bind.CallOpts) (uint32, error)) *VRFCoordinatorV2Interface_MAXNUMWORDS_Call {
	_c.Call.Return(run)
	return _c
}

// MAXREQUESTCONFIRMATIONS provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) MAXREQUESTCONFIRMATIONS(opts *bind.CallOpts) (uint16, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for MAXREQUESTCONFIRMATIONS")
	}

	var r0 uint16
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint16, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint16); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint16)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MAXREQUESTCONFIRMATIONS'
type VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call struct {
	*mock.Call
}

// MAXREQUESTCONFIRMATIONS is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) MAXREQUESTCONFIRMATIONS(opts interface{}) *VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call {
	return &VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call{Call: _e.mock.On("MAXREQUESTCONFIRMATIONS", opts)}
}

func (_c *VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call) Return(_a0 uint16, _a1 error) *VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call) RunAndReturn(run func(*bind.CallOpts) (uint16, error)) *VRFCoordinatorV2Interface_MAXREQUESTCONFIRMATIONS_Call {
	_c.Call.Return(run)
	return _c
}

// OnTokenTransfer provides a mock function with given fields: opts, arg0, amount, data
func (_m *VRFCoordinatorV2Interface) OnTokenTransfer(opts *bind.TransactOpts, arg0 common.Address, amount *big.Int, data []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, arg0, amount, data)

	if len(ret) == 0 {
		panic("no return value specified for OnTokenTransfer")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int, []byte) (*types.Transaction, error)); ok {
		return rf(opts, arg0, amount, data)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int, []byte) *types.Transaction); ok {
		r0 = rf(opts, arg0, amount, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, *big.Int, []byte) error); ok {
		r1 = rf(opts, arg0, amount, data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_OnTokenTransfer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnTokenTransfer'
type VRFCoordinatorV2Interface_OnTokenTransfer_Call struct {
	*mock.Call
}

// OnTokenTransfer is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - arg0 common.Address
//   - amount *big.Int
//   - data []byte
func (_e *VRFCoordinatorV2Interface_Expecter) OnTokenTransfer(opts interface{}, arg0 interface{}, amount interface{}, data interface{}) *VRFCoordinatorV2Interface_OnTokenTransfer_Call {
	return &VRFCoordinatorV2Interface_OnTokenTransfer_Call{Call: _e.mock.On("OnTokenTransfer", opts, arg0, amount, data)}
}

func (_c *VRFCoordinatorV2Interface_OnTokenTransfer_Call) Run(run func(opts *bind.TransactOpts, arg0 common.Address, amount *big.Int, data []byte)) *VRFCoordinatorV2Interface_OnTokenTransfer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(*big.Int), args[3].([]byte))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_OnTokenTransfer_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_OnTokenTransfer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_OnTokenTransfer_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, *big.Int, []byte) (*types.Transaction, error)) *VRFCoordinatorV2Interface_OnTokenTransfer_Call {
	_c.Call.Return(run)
	return _c
}

// OracleWithdraw provides a mock function with given fields: opts, recipient, amount
func (_m *VRFCoordinatorV2Interface) OracleWithdraw(opts *bind.TransactOpts, recipient common.Address, amount *big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, recipient, amount)

	if len(ret) == 0 {
		panic("no return value specified for OracleWithdraw")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)); ok {
		return rf(opts, recipient, amount)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) *types.Transaction); ok {
		r0 = rf(opts, recipient, amount)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, *big.Int) error); ok {
		r1 = rf(opts, recipient, amount)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_OracleWithdraw_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OracleWithdraw'
type VRFCoordinatorV2Interface_OracleWithdraw_Call struct {
	*mock.Call
}

// OracleWithdraw is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - recipient common.Address
//   - amount *big.Int
func (_e *VRFCoordinatorV2Interface_Expecter) OracleWithdraw(opts interface{}, recipient interface{}, amount interface{}) *VRFCoordinatorV2Interface_OracleWithdraw_Call {
	return &VRFCoordinatorV2Interface_OracleWithdraw_Call{Call: _e.mock.On("OracleWithdraw", opts, recipient, amount)}
}

func (_c *VRFCoordinatorV2Interface_OracleWithdraw_Call) Run(run func(opts *bind.TransactOpts, recipient common.Address, amount *big.Int)) *VRFCoordinatorV2Interface_OracleWithdraw_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_OracleWithdraw_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_OracleWithdraw_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_OracleWithdraw_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)) *VRFCoordinatorV2Interface_OracleWithdraw_Call {
	_c.Call.Return(run)
	return _c
}

// Owner provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) Owner(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Owner")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_Owner_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Owner'
type VRFCoordinatorV2Interface_Owner_Call struct {
	*mock.Call
}

// Owner is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) Owner(opts interface{}) *VRFCoordinatorV2Interface_Owner_Call {
	return &VRFCoordinatorV2Interface_Owner_Call{Call: _e.mock.On("Owner", opts)}
}

func (_c *VRFCoordinatorV2Interface_Owner_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_Owner_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_Owner_Call) Return(_a0 common.Address, _a1 error) *VRFCoordinatorV2Interface_Owner_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_Owner_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *VRFCoordinatorV2Interface_Owner_Call {
	_c.Call.Return(run)
	return _c
}

// OwnerCancelSubscription provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) OwnerCancelSubscription(opts *bind.TransactOpts, subId uint64) (*types.Transaction, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for OwnerCancelSubscription")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64) (*types.Transaction, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64) *types.Transaction); ok {
		r0 = rf(opts, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_OwnerCancelSubscription_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OwnerCancelSubscription'
type VRFCoordinatorV2Interface_OwnerCancelSubscription_Call struct {
	*mock.Call
}

// OwnerCancelSubscription is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - subId uint64
func (_e *VRFCoordinatorV2Interface_Expecter) OwnerCancelSubscription(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_OwnerCancelSubscription_Call {
	return &VRFCoordinatorV2Interface_OwnerCancelSubscription_Call{Call: _e.mock.On("OwnerCancelSubscription", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_OwnerCancelSubscription_Call) Run(run func(opts *bind.TransactOpts, subId uint64)) *VRFCoordinatorV2Interface_OwnerCancelSubscription_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_OwnerCancelSubscription_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_OwnerCancelSubscription_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_OwnerCancelSubscription_Call) RunAndReturn(run func(*bind.TransactOpts, uint64) (*types.Transaction, error)) *VRFCoordinatorV2Interface_OwnerCancelSubscription_Call {
	_c.Call.Return(run)
	return _c
}

// ParseConfigSet provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseConfigSet(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2ConfigSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseConfigSet")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2ConfigSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2ConfigSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseConfigSet'
type VRFCoordinatorV2Interface_ParseConfigSet_Call struct {
	*mock.Call
}

// ParseConfigSet is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseConfigSet(log interface{}) *VRFCoordinatorV2Interface_ParseConfigSet_Call {
	return &VRFCoordinatorV2Interface_ParseConfigSet_Call{Call: _e.mock.On("ParseConfigSet", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseConfigSet_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseConfigSet_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet, _a1 error) *VRFCoordinatorV2Interface_ParseConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseConfigSet_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2ConfigSet, error)) *VRFCoordinatorV2Interface_ParseConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseFundsRecovered provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseFundsRecovered(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseFundsRecovered")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseFundsRecovered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseFundsRecovered'
type VRFCoordinatorV2Interface_ParseFundsRecovered_Call struct {
	*mock.Call
}

// ParseFundsRecovered is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseFundsRecovered(log interface{}) *VRFCoordinatorV2Interface_ParseFundsRecovered_Call {
	return &VRFCoordinatorV2Interface_ParseFundsRecovered_Call{Call: _e.mock.On("ParseFundsRecovered", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseFundsRecovered_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseFundsRecovered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseFundsRecovered_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered, _a1 error) *VRFCoordinatorV2Interface_ParseFundsRecovered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseFundsRecovered_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered, error)) *VRFCoordinatorV2Interface_ParseFundsRecovered_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type VRFCoordinatorV2Interface_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseLog(log interface{}) *VRFCoordinatorV2Interface_ParseLog_Call {
	return &VRFCoordinatorV2Interface_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseLog_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *VRFCoordinatorV2Interface_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *VRFCoordinatorV2Interface_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferRequested provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseOwnershipTransferRequested(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferRequested")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferRequested'
type VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call struct {
	*mock.Call
}

// ParseOwnershipTransferRequested is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseOwnershipTransferRequested(log interface{}) *VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call {
	return &VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call{Call: _e.mock.On("ParseOwnershipTransferRequested", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, _a1 error) *VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, error)) *VRFCoordinatorV2Interface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferred provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseOwnershipTransferred(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferred")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferred'
type VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call struct {
	*mock.Call
}

// ParseOwnershipTransferred is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseOwnershipTransferred(log interface{}) *VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call {
	return &VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call{Call: _e.mock.On("ParseOwnershipTransferred", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, _a1 error) *VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, error)) *VRFCoordinatorV2Interface_ParseOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// ParseProvingKeyDeregistered provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseProvingKeyDeregistered(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseProvingKeyDeregistered")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseProvingKeyDeregistered'
type VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call struct {
	*mock.Call
}

// ParseProvingKeyDeregistered is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseProvingKeyDeregistered(log interface{}) *VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call {
	return &VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call{Call: _e.mock.On("ParseProvingKeyDeregistered", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, _a1 error) *VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, error)) *VRFCoordinatorV2Interface_ParseProvingKeyDeregistered_Call {
	_c.Call.Return(run)
	return _c
}

// ParseProvingKeyRegistered provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseProvingKeyRegistered(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseProvingKeyRegistered")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseProvingKeyRegistered'
type VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call struct {
	*mock.Call
}

// ParseProvingKeyRegistered is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseProvingKeyRegistered(log interface{}) *VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call {
	return &VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call{Call: _e.mock.On("ParseProvingKeyRegistered", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, _a1 error) *VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, error)) *VRFCoordinatorV2Interface_ParseProvingKeyRegistered_Call {
	_c.Call.Return(run)
	return _c
}

// ParseRandomWordsFulfilled provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseRandomWordsFulfilled(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseRandomWordsFulfilled")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseRandomWordsFulfilled'
type VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call struct {
	*mock.Call
}

// ParseRandomWordsFulfilled is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseRandomWordsFulfilled(log interface{}) *VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call {
	return &VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call{Call: _e.mock.On("ParseRandomWordsFulfilled", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, _a1 error) *VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, error)) *VRFCoordinatorV2Interface_ParseRandomWordsFulfilled_Call {
	_c.Call.Return(run)
	return _c
}

// ParseRandomWordsRequested provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseRandomWordsRequested(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseRandomWordsRequested")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseRandomWordsRequested'
type VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call struct {
	*mock.Call
}

// ParseRandomWordsRequested is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseRandomWordsRequested(log interface{}) *VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call {
	return &VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call{Call: _e.mock.On("ParseRandomWordsRequested", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, _a1 error) *VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, error)) *VRFCoordinatorV2Interface_ParseRandomWordsRequested_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSubscriptionCanceled provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseSubscriptionCanceled(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSubscriptionCanceled")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSubscriptionCanceled'
type VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call struct {
	*mock.Call
}

// ParseSubscriptionCanceled is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseSubscriptionCanceled(log interface{}) *VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call {
	return &VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call{Call: _e.mock.On("ParseSubscriptionCanceled", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, _a1 error) *VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, error)) *VRFCoordinatorV2Interface_ParseSubscriptionCanceled_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSubscriptionConsumerAdded provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseSubscriptionConsumerAdded(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSubscriptionConsumerAdded")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSubscriptionConsumerAdded'
type VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call struct {
	*mock.Call
}

// ParseSubscriptionConsumerAdded is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseSubscriptionConsumerAdded(log interface{}) *VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call {
	return &VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call{Call: _e.mock.On("ParseSubscriptionConsumerAdded", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, _a1 error) *VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, error)) *VRFCoordinatorV2Interface_ParseSubscriptionConsumerAdded_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSubscriptionConsumerRemoved provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseSubscriptionConsumerRemoved(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSubscriptionConsumerRemoved")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSubscriptionConsumerRemoved'
type VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call struct {
	*mock.Call
}

// ParseSubscriptionConsumerRemoved is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseSubscriptionConsumerRemoved(log interface{}) *VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call {
	return &VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call{Call: _e.mock.On("ParseSubscriptionConsumerRemoved", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, _a1 error) *VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, error)) *VRFCoordinatorV2Interface_ParseSubscriptionConsumerRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSubscriptionCreated provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseSubscriptionCreated(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSubscriptionCreated")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSubscriptionCreated'
type VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call struct {
	*mock.Call
}

// ParseSubscriptionCreated is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseSubscriptionCreated(log interface{}) *VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call {
	return &VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call{Call: _e.mock.On("ParseSubscriptionCreated", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, _a1 error) *VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, error)) *VRFCoordinatorV2Interface_ParseSubscriptionCreated_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSubscriptionFunded provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseSubscriptionFunded(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSubscriptionFunded")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSubscriptionFunded'
type VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call struct {
	*mock.Call
}

// ParseSubscriptionFunded is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseSubscriptionFunded(log interface{}) *VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call {
	return &VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call{Call: _e.mock.On("ParseSubscriptionFunded", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, _a1 error) *VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, error)) *VRFCoordinatorV2Interface_ParseSubscriptionFunded_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSubscriptionOwnerTransferRequested provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseSubscriptionOwnerTransferRequested(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSubscriptionOwnerTransferRequested")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSubscriptionOwnerTransferRequested'
type VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call struct {
	*mock.Call
}

// ParseSubscriptionOwnerTransferRequested is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseSubscriptionOwnerTransferRequested(log interface{}) *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call {
	return &VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call{Call: _e.mock.On("ParseSubscriptionOwnerTransferRequested", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, _a1 error) *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, error)) *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSubscriptionOwnerTransferred provides a mock function with given fields: log
func (_m *VRFCoordinatorV2Interface) ParseSubscriptionOwnerTransferred(log types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSubscriptionOwnerTransferred")
	}

	var r0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSubscriptionOwnerTransferred'
type VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call struct {
	*mock.Call
}

// ParseSubscriptionOwnerTransferred is a helper method to define mock.On call
//   - log types.Log
func (_e *VRFCoordinatorV2Interface_Expecter) ParseSubscriptionOwnerTransferred(log interface{}) *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call {
	return &VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call{Call: _e.mock.On("ParseSubscriptionOwnerTransferred", log)}
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call) Run(run func(log types.Log)) *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call) Return(_a0 *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, _a1 error) *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call) RunAndReturn(run func(types.Log) (*vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, error)) *VRFCoordinatorV2Interface_ParseSubscriptionOwnerTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// PendingRequestExists provides a mock function with given fields: opts, subId
func (_m *VRFCoordinatorV2Interface) PendingRequestExists(opts *bind.CallOpts, subId uint64) (bool, error) {
	ret := _m.Called(opts, subId)

	if len(ret) == 0 {
		panic("no return value specified for PendingRequestExists")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) (bool, error)); ok {
		return rf(opts, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) bool); ok {
		r0 = rf(opts, subId)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_PendingRequestExists_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PendingRequestExists'
type VRFCoordinatorV2Interface_PendingRequestExists_Call struct {
	*mock.Call
}

// PendingRequestExists is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - subId uint64
func (_e *VRFCoordinatorV2Interface_Expecter) PendingRequestExists(opts interface{}, subId interface{}) *VRFCoordinatorV2Interface_PendingRequestExists_Call {
	return &VRFCoordinatorV2Interface_PendingRequestExists_Call{Call: _e.mock.On("PendingRequestExists", opts, subId)}
}

func (_c *VRFCoordinatorV2Interface_PendingRequestExists_Call) Run(run func(opts *bind.CallOpts, subId uint64)) *VRFCoordinatorV2Interface_PendingRequestExists_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_PendingRequestExists_Call) Return(_a0 bool, _a1 error) *VRFCoordinatorV2Interface_PendingRequestExists_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_PendingRequestExists_Call) RunAndReturn(run func(*bind.CallOpts, uint64) (bool, error)) *VRFCoordinatorV2Interface_PendingRequestExists_Call {
	_c.Call.Return(run)
	return _c
}

// RecoverFunds provides a mock function with given fields: opts, to
func (_m *VRFCoordinatorV2Interface) RecoverFunds(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, to)

	if len(ret) == 0 {
		panic("no return value specified for RecoverFunds")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_RecoverFunds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecoverFunds'
type VRFCoordinatorV2Interface_RecoverFunds_Call struct {
	*mock.Call
}

// RecoverFunds is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - to common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) RecoverFunds(opts interface{}, to interface{}) *VRFCoordinatorV2Interface_RecoverFunds_Call {
	return &VRFCoordinatorV2Interface_RecoverFunds_Call{Call: _e.mock.On("RecoverFunds", opts, to)}
}

func (_c *VRFCoordinatorV2Interface_RecoverFunds_Call) Run(run func(opts *bind.TransactOpts, to common.Address)) *VRFCoordinatorV2Interface_RecoverFunds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_RecoverFunds_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_RecoverFunds_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_RecoverFunds_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *VRFCoordinatorV2Interface_RecoverFunds_Call {
	_c.Call.Return(run)
	return _c
}

// RegisterProvingKey provides a mock function with given fields: opts, oracle, publicProvingKey
func (_m *VRFCoordinatorV2Interface) RegisterProvingKey(opts *bind.TransactOpts, oracle common.Address, publicProvingKey [2]*big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, oracle, publicProvingKey)

	if len(ret) == 0 {
		panic("no return value specified for RegisterProvingKey")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, [2]*big.Int) (*types.Transaction, error)); ok {
		return rf(opts, oracle, publicProvingKey)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, [2]*big.Int) *types.Transaction); ok {
		r0 = rf(opts, oracle, publicProvingKey)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, [2]*big.Int) error); ok {
		r1 = rf(opts, oracle, publicProvingKey)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_RegisterProvingKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RegisterProvingKey'
type VRFCoordinatorV2Interface_RegisterProvingKey_Call struct {
	*mock.Call
}

// RegisterProvingKey is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - oracle common.Address
//   - publicProvingKey [2]*big.Int
func (_e *VRFCoordinatorV2Interface_Expecter) RegisterProvingKey(opts interface{}, oracle interface{}, publicProvingKey interface{}) *VRFCoordinatorV2Interface_RegisterProvingKey_Call {
	return &VRFCoordinatorV2Interface_RegisterProvingKey_Call{Call: _e.mock.On("RegisterProvingKey", opts, oracle, publicProvingKey)}
}

func (_c *VRFCoordinatorV2Interface_RegisterProvingKey_Call) Run(run func(opts *bind.TransactOpts, oracle common.Address, publicProvingKey [2]*big.Int)) *VRFCoordinatorV2Interface_RegisterProvingKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].([2]*big.Int))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_RegisterProvingKey_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_RegisterProvingKey_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_RegisterProvingKey_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, [2]*big.Int) (*types.Transaction, error)) *VRFCoordinatorV2Interface_RegisterProvingKey_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveConsumer provides a mock function with given fields: opts, subId, consumer
func (_m *VRFCoordinatorV2Interface) RemoveConsumer(opts *bind.TransactOpts, subId uint64, consumer common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, subId, consumer)

	if len(ret) == 0 {
		panic("no return value specified for RemoveConsumer")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, subId, consumer)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, common.Address) *types.Transaction); ok {
		r0 = rf(opts, subId, consumer)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, uint64, common.Address) error); ok {
		r1 = rf(opts, subId, consumer)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_RemoveConsumer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveConsumer'
type VRFCoordinatorV2Interface_RemoveConsumer_Call struct {
	*mock.Call
}

// RemoveConsumer is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - subId uint64
//   - consumer common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) RemoveConsumer(opts interface{}, subId interface{}, consumer interface{}) *VRFCoordinatorV2Interface_RemoveConsumer_Call {
	return &VRFCoordinatorV2Interface_RemoveConsumer_Call{Call: _e.mock.On("RemoveConsumer", opts, subId, consumer)}
}

func (_c *VRFCoordinatorV2Interface_RemoveConsumer_Call) Run(run func(opts *bind.TransactOpts, subId uint64, consumer common.Address)) *VRFCoordinatorV2Interface_RemoveConsumer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(uint64), args[2].(common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_RemoveConsumer_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_RemoveConsumer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_RemoveConsumer_Call) RunAndReturn(run func(*bind.TransactOpts, uint64, common.Address) (*types.Transaction, error)) *VRFCoordinatorV2Interface_RemoveConsumer_Call {
	_c.Call.Return(run)
	return _c
}

// RequestRandomWords provides a mock function with given fields: opts, keyHash, subId, requestConfirmations, callbackGasLimit, numWords
func (_m *VRFCoordinatorV2Interface) RequestRandomWords(opts *bind.TransactOpts, keyHash [32]byte, subId uint64, requestConfirmations uint16, callbackGasLimit uint32, numWords uint32) (*types.Transaction, error) {
	ret := _m.Called(opts, keyHash, subId, requestConfirmations, callbackGasLimit, numWords)

	if len(ret) == 0 {
		panic("no return value specified for RequestRandomWords")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [32]byte, uint64, uint16, uint32, uint32) (*types.Transaction, error)); ok {
		return rf(opts, keyHash, subId, requestConfirmations, callbackGasLimit, numWords)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [32]byte, uint64, uint16, uint32, uint32) *types.Transaction); ok {
		r0 = rf(opts, keyHash, subId, requestConfirmations, callbackGasLimit, numWords)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, [32]byte, uint64, uint16, uint32, uint32) error); ok {
		r1 = rf(opts, keyHash, subId, requestConfirmations, callbackGasLimit, numWords)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_RequestRandomWords_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RequestRandomWords'
type VRFCoordinatorV2Interface_RequestRandomWords_Call struct {
	*mock.Call
}

// RequestRandomWords is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - keyHash [32]byte
//   - subId uint64
//   - requestConfirmations uint16
//   - callbackGasLimit uint32
//   - numWords uint32
func (_e *VRFCoordinatorV2Interface_Expecter) RequestRandomWords(opts interface{}, keyHash interface{}, subId interface{}, requestConfirmations interface{}, callbackGasLimit interface{}, numWords interface{}) *VRFCoordinatorV2Interface_RequestRandomWords_Call {
	return &VRFCoordinatorV2Interface_RequestRandomWords_Call{Call: _e.mock.On("RequestRandomWords", opts, keyHash, subId, requestConfirmations, callbackGasLimit, numWords)}
}

func (_c *VRFCoordinatorV2Interface_RequestRandomWords_Call) Run(run func(opts *bind.TransactOpts, keyHash [32]byte, subId uint64, requestConfirmations uint16, callbackGasLimit uint32, numWords uint32)) *VRFCoordinatorV2Interface_RequestRandomWords_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([32]byte), args[2].(uint64), args[3].(uint16), args[4].(uint32), args[5].(uint32))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_RequestRandomWords_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_RequestRandomWords_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_RequestRandomWords_Call) RunAndReturn(run func(*bind.TransactOpts, [32]byte, uint64, uint16, uint32, uint32) (*types.Transaction, error)) *VRFCoordinatorV2Interface_RequestRandomWords_Call {
	_c.Call.Return(run)
	return _c
}

// RequestSubscriptionOwnerTransfer provides a mock function with given fields: opts, subId, newOwner
func (_m *VRFCoordinatorV2Interface) RequestSubscriptionOwnerTransfer(opts *bind.TransactOpts, subId uint64, newOwner common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, subId, newOwner)

	if len(ret) == 0 {
		panic("no return value specified for RequestSubscriptionOwnerTransfer")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, subId, newOwner)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, common.Address) *types.Transaction); ok {
		r0 = rf(opts, subId, newOwner)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, uint64, common.Address) error); ok {
		r1 = rf(opts, subId, newOwner)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RequestSubscriptionOwnerTransfer'
type VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call struct {
	*mock.Call
}

// RequestSubscriptionOwnerTransfer is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - subId uint64
//   - newOwner common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) RequestSubscriptionOwnerTransfer(opts interface{}, subId interface{}, newOwner interface{}) *VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call {
	return &VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call{Call: _e.mock.On("RequestSubscriptionOwnerTransfer", opts, subId, newOwner)}
}

func (_c *VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call) Run(run func(opts *bind.TransactOpts, subId uint64, newOwner common.Address)) *VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(uint64), args[2].(common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call) RunAndReturn(run func(*bind.TransactOpts, uint64, common.Address) (*types.Transaction, error)) *VRFCoordinatorV2Interface_RequestSubscriptionOwnerTransfer_Call {
	_c.Call.Return(run)
	return _c
}

// SetConfig provides a mock function with given fields: opts, minimumRequestConfirmations, maxGasLimit, stalenessSeconds, gasAfterPaymentCalculation, fallbackWeiPerUnitLink, feeConfig
func (_m *VRFCoordinatorV2Interface) SetConfig(opts *bind.TransactOpts, minimumRequestConfirmations uint16, maxGasLimit uint32, stalenessSeconds uint32, gasAfterPaymentCalculation uint32, fallbackWeiPerUnitLink *big.Int, feeConfig vrf_coordinator_v2.VRFCoordinatorV2FeeConfig) (*types.Transaction, error) {
	ret := _m.Called(opts, minimumRequestConfirmations, maxGasLimit, stalenessSeconds, gasAfterPaymentCalculation, fallbackWeiPerUnitLink, feeConfig)

	if len(ret) == 0 {
		panic("no return value specified for SetConfig")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint16, uint32, uint32, uint32, *big.Int, vrf_coordinator_v2.VRFCoordinatorV2FeeConfig) (*types.Transaction, error)); ok {
		return rf(opts, minimumRequestConfirmations, maxGasLimit, stalenessSeconds, gasAfterPaymentCalculation, fallbackWeiPerUnitLink, feeConfig)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint16, uint32, uint32, uint32, *big.Int, vrf_coordinator_v2.VRFCoordinatorV2FeeConfig) *types.Transaction); ok {
		r0 = rf(opts, minimumRequestConfirmations, maxGasLimit, stalenessSeconds, gasAfterPaymentCalculation, fallbackWeiPerUnitLink, feeConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, uint16, uint32, uint32, uint32, *big.Int, vrf_coordinator_v2.VRFCoordinatorV2FeeConfig) error); ok {
		r1 = rf(opts, minimumRequestConfirmations, maxGasLimit, stalenessSeconds, gasAfterPaymentCalculation, fallbackWeiPerUnitLink, feeConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_SetConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetConfig'
type VRFCoordinatorV2Interface_SetConfig_Call struct {
	*mock.Call
}

// SetConfig is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - minimumRequestConfirmations uint16
//   - maxGasLimit uint32
//   - stalenessSeconds uint32
//   - gasAfterPaymentCalculation uint32
//   - fallbackWeiPerUnitLink *big.Int
//   - feeConfig vrf_coordinator_v2.VRFCoordinatorV2FeeConfig
func (_e *VRFCoordinatorV2Interface_Expecter) SetConfig(opts interface{}, minimumRequestConfirmations interface{}, maxGasLimit interface{}, stalenessSeconds interface{}, gasAfterPaymentCalculation interface{}, fallbackWeiPerUnitLink interface{}, feeConfig interface{}) *VRFCoordinatorV2Interface_SetConfig_Call {
	return &VRFCoordinatorV2Interface_SetConfig_Call{Call: _e.mock.On("SetConfig", opts, minimumRequestConfirmations, maxGasLimit, stalenessSeconds, gasAfterPaymentCalculation, fallbackWeiPerUnitLink, feeConfig)}
}

func (_c *VRFCoordinatorV2Interface_SetConfig_Call) Run(run func(opts *bind.TransactOpts, minimumRequestConfirmations uint16, maxGasLimit uint32, stalenessSeconds uint32, gasAfterPaymentCalculation uint32, fallbackWeiPerUnitLink *big.Int, feeConfig vrf_coordinator_v2.VRFCoordinatorV2FeeConfig)) *VRFCoordinatorV2Interface_SetConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(uint16), args[2].(uint32), args[3].(uint32), args[4].(uint32), args[5].(*big.Int), args[6].(vrf_coordinator_v2.VRFCoordinatorV2FeeConfig))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_SetConfig_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_SetConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_SetConfig_Call) RunAndReturn(run func(*bind.TransactOpts, uint16, uint32, uint32, uint32, *big.Int, vrf_coordinator_v2.VRFCoordinatorV2FeeConfig) (*types.Transaction, error)) *VRFCoordinatorV2Interface_SetConfig_Call {
	_c.Call.Return(run)
	return _c
}

// TransferOwnership provides a mock function with given fields: opts, to
func (_m *VRFCoordinatorV2Interface) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, to)

	if len(ret) == 0 {
		panic("no return value specified for TransferOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_TransferOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransferOwnership'
type VRFCoordinatorV2Interface_TransferOwnership_Call struct {
	*mock.Call
}

// TransferOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - to common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) TransferOwnership(opts interface{}, to interface{}) *VRFCoordinatorV2Interface_TransferOwnership_Call {
	return &VRFCoordinatorV2Interface_TransferOwnership_Call{Call: _e.mock.On("TransferOwnership", opts, to)}
}

func (_c *VRFCoordinatorV2Interface_TransferOwnership_Call) Run(run func(opts *bind.TransactOpts, to common.Address)) *VRFCoordinatorV2Interface_TransferOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_TransferOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *VRFCoordinatorV2Interface_TransferOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_TransferOwnership_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *VRFCoordinatorV2Interface_TransferOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// TypeAndVersion provides a mock function with given fields: opts
func (_m *VRFCoordinatorV2Interface) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for TypeAndVersion")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (string, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) string); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_TypeAndVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TypeAndVersion'
type VRFCoordinatorV2Interface_TypeAndVersion_Call struct {
	*mock.Call
}

// TypeAndVersion is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *VRFCoordinatorV2Interface_Expecter) TypeAndVersion(opts interface{}) *VRFCoordinatorV2Interface_TypeAndVersion_Call {
	return &VRFCoordinatorV2Interface_TypeAndVersion_Call{Call: _e.mock.On("TypeAndVersion", opts)}
}

func (_c *VRFCoordinatorV2Interface_TypeAndVersion_Call) Run(run func(opts *bind.CallOpts)) *VRFCoordinatorV2Interface_TypeAndVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_TypeAndVersion_Call) Return(_a0 string, _a1 error) *VRFCoordinatorV2Interface_TypeAndVersion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_TypeAndVersion_Call) RunAndReturn(run func(*bind.CallOpts) (string, error)) *VRFCoordinatorV2Interface_TypeAndVersion_Call {
	_c.Call.Return(run)
	return _c
}

// WatchConfigSet provides a mock function with given fields: opts, sink
func (_m *VRFCoordinatorV2Interface) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchConfigSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchConfigSet'
type VRFCoordinatorV2Interface_WatchConfigSet_Call struct {
	*mock.Call
}

// WatchConfigSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet
func (_e *VRFCoordinatorV2Interface_Expecter) WatchConfigSet(opts interface{}, sink interface{}) *VRFCoordinatorV2Interface_WatchConfigSet_Call {
	return &VRFCoordinatorV2Interface_WatchConfigSet_Call{Call: _e.mock.On("WatchConfigSet", opts, sink)}
}

func (_c *VRFCoordinatorV2Interface_WatchConfigSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet)) *VRFCoordinatorV2Interface_WatchConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchConfigSet_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchConfigSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ConfigSet) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchFundsRecovered provides a mock function with given fields: opts, sink
func (_m *VRFCoordinatorV2Interface) WatchFundsRecovered(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchFundsRecovered")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchFundsRecovered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchFundsRecovered'
type VRFCoordinatorV2Interface_WatchFundsRecovered_Call struct {
	*mock.Call
}

// WatchFundsRecovered is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered
func (_e *VRFCoordinatorV2Interface_Expecter) WatchFundsRecovered(opts interface{}, sink interface{}) *VRFCoordinatorV2Interface_WatchFundsRecovered_Call {
	return &VRFCoordinatorV2Interface_WatchFundsRecovered_Call{Call: _e.mock.On("WatchFundsRecovered", opts, sink)}
}

func (_c *VRFCoordinatorV2Interface_WatchFundsRecovered_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered)) *VRFCoordinatorV2Interface_WatchFundsRecovered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchFundsRecovered_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchFundsRecovered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchFundsRecovered_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2FundsRecovered) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchFundsRecovered_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferRequested provides a mock function with given fields: opts, sink, from, to
func (_m *VRFCoordinatorV2Interface) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferRequested")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferRequested'
type VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call struct {
	*mock.Call
}

// WatchOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested
//   - from []common.Address
//   - to []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) WatchOwnershipTransferRequested(opts interface{}, sink interface{}, from interface{}, to interface{}) *VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call {
	return &VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call{Call: _e.mock.On("WatchOwnershipTransferRequested", opts, sink, from, to)}
}

func (_c *VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, from []common.Address, to []common.Address)) *VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferred provides a mock function with given fields: opts, sink, from, to
func (_m *VRFCoordinatorV2Interface) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferred")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferred'
type VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call struct {
	*mock.Call
}

// WatchOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred
//   - from []common.Address
//   - to []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) WatchOwnershipTransferred(opts interface{}, sink interface{}, from interface{}, to interface{}) *VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call {
	return &VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call{Call: _e.mock.On("WatchOwnershipTransferred", opts, sink, from, to)}
}

func (_c *VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, from []common.Address, to []common.Address)) *VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2OwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// WatchProvingKeyDeregistered provides a mock function with given fields: opts, sink, oracle
func (_m *VRFCoordinatorV2Interface) WatchProvingKeyDeregistered(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, oracle []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, oracle)

	if len(ret) == 0 {
		panic("no return value specified for WatchProvingKeyDeregistered")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, oracle)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, oracle)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, []common.Address) error); ok {
		r1 = rf(opts, sink, oracle)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchProvingKeyDeregistered'
type VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call struct {
	*mock.Call
}

// WatchProvingKeyDeregistered is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered
//   - oracle []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) WatchProvingKeyDeregistered(opts interface{}, sink interface{}, oracle interface{}) *VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call {
	return &VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call{Call: _e.mock.On("WatchProvingKeyDeregistered", opts, sink, oracle)}
}

func (_c *VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, oracle []common.Address)) *VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered), args[2].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyDeregistered, []common.Address) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchProvingKeyDeregistered_Call {
	_c.Call.Return(run)
	return _c
}

// WatchProvingKeyRegistered provides a mock function with given fields: opts, sink, oracle
func (_m *VRFCoordinatorV2Interface) WatchProvingKeyRegistered(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, oracle []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, oracle)

	if len(ret) == 0 {
		panic("no return value specified for WatchProvingKeyRegistered")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, oracle)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, oracle)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, []common.Address) error); ok {
		r1 = rf(opts, sink, oracle)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchProvingKeyRegistered'
type VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call struct {
	*mock.Call
}

// WatchProvingKeyRegistered is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered
//   - oracle []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) WatchProvingKeyRegistered(opts interface{}, sink interface{}, oracle interface{}) *VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call {
	return &VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call{Call: _e.mock.On("WatchProvingKeyRegistered", opts, sink, oracle)}
}

func (_c *VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, oracle []common.Address)) *VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered), args[2].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2ProvingKeyRegistered, []common.Address) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchProvingKeyRegistered_Call {
	_c.Call.Return(run)
	return _c
}

// WatchRandomWordsFulfilled provides a mock function with given fields: opts, sink, requestId
func (_m *VRFCoordinatorV2Interface) WatchRandomWordsFulfilled(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, requestId []*big.Int) (event.Subscription, error) {
	ret := _m.Called(opts, sink, requestId)

	if len(ret) == 0 {
		panic("no return value specified for WatchRandomWordsFulfilled")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, []*big.Int) (event.Subscription, error)); ok {
		return rf(opts, sink, requestId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, []*big.Int) event.Subscription); ok {
		r0 = rf(opts, sink, requestId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, []*big.Int) error); ok {
		r1 = rf(opts, sink, requestId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchRandomWordsFulfilled'
type VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call struct {
	*mock.Call
}

// WatchRandomWordsFulfilled is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled
//   - requestId []*big.Int
func (_e *VRFCoordinatorV2Interface_Expecter) WatchRandomWordsFulfilled(opts interface{}, sink interface{}, requestId interface{}) *VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call {
	return &VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call{Call: _e.mock.On("WatchRandomWordsFulfilled", opts, sink, requestId)}
}

func (_c *VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, requestId []*big.Int)) *VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled), args[2].([]*big.Int))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsFulfilled, []*big.Int) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchRandomWordsFulfilled_Call {
	_c.Call.Return(run)
	return _c
}

// WatchRandomWordsRequested provides a mock function with given fields: opts, sink, keyHash, subId, sender
func (_m *VRFCoordinatorV2Interface) WatchRandomWordsRequested(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, keyHash [][32]byte, subId []uint64, sender []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, keyHash, subId, sender)

	if len(ret) == 0 {
		panic("no return value specified for WatchRandomWordsRequested")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, [][32]byte, []uint64, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, keyHash, subId, sender)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, [][32]byte, []uint64, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, keyHash, subId, sender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, [][32]byte, []uint64, []common.Address) error); ok {
		r1 = rf(opts, sink, keyHash, subId, sender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchRandomWordsRequested'
type VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call struct {
	*mock.Call
}

// WatchRandomWordsRequested is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested
//   - keyHash [][32]byte
//   - subId []uint64
//   - sender []common.Address
func (_e *VRFCoordinatorV2Interface_Expecter) WatchRandomWordsRequested(opts interface{}, sink interface{}, keyHash interface{}, subId interface{}, sender interface{}) *VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call {
	return &VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call{Call: _e.mock.On("WatchRandomWordsRequested", opts, sink, keyHash, subId, sender)}
}

func (_c *VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, keyHash [][32]byte, subId []uint64, sender []common.Address)) *VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested), args[2].([][32]byte), args[3].([]uint64), args[4].([]common.Address))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2RandomWordsRequested, [][32]byte, []uint64, []common.Address) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchRandomWordsRequested_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSubscriptionCanceled provides a mock function with given fields: opts, sink, subId
func (_m *VRFCoordinatorV2Interface) WatchSubscriptionCanceled(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, subId []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, subId)

	if len(ret) == 0 {
		panic("no return value specified for WatchSubscriptionCanceled")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, []uint64) error); ok {
		r1 = rf(opts, sink, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSubscriptionCanceled'
type VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call struct {
	*mock.Call
}

// WatchSubscriptionCanceled is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) WatchSubscriptionCanceled(opts interface{}, sink interface{}, subId interface{}) *VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call {
	return &VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call{Call: _e.mock.On("WatchSubscriptionCanceled", opts, sink, subId)}
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, subId []uint64)) *VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled), args[2].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCanceled, []uint64) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchSubscriptionCanceled_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSubscriptionConsumerAdded provides a mock function with given fields: opts, sink, subId
func (_m *VRFCoordinatorV2Interface) WatchSubscriptionConsumerAdded(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, subId []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, subId)

	if len(ret) == 0 {
		panic("no return value specified for WatchSubscriptionConsumerAdded")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, []uint64) error); ok {
		r1 = rf(opts, sink, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSubscriptionConsumerAdded'
type VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call struct {
	*mock.Call
}

// WatchSubscriptionConsumerAdded is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) WatchSubscriptionConsumerAdded(opts interface{}, sink interface{}, subId interface{}) *VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call {
	return &VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call{Call: _e.mock.On("WatchSubscriptionConsumerAdded", opts, sink, subId)}
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, subId []uint64)) *VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded), args[2].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerAdded, []uint64) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchSubscriptionConsumerAdded_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSubscriptionConsumerRemoved provides a mock function with given fields: opts, sink, subId
func (_m *VRFCoordinatorV2Interface) WatchSubscriptionConsumerRemoved(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, subId []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, subId)

	if len(ret) == 0 {
		panic("no return value specified for WatchSubscriptionConsumerRemoved")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, []uint64) error); ok {
		r1 = rf(opts, sink, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSubscriptionConsumerRemoved'
type VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call struct {
	*mock.Call
}

// WatchSubscriptionConsumerRemoved is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) WatchSubscriptionConsumerRemoved(opts interface{}, sink interface{}, subId interface{}) *VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call {
	return &VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call{Call: _e.mock.On("WatchSubscriptionConsumerRemoved", opts, sink, subId)}
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, subId []uint64)) *VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved), args[2].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionConsumerRemoved, []uint64) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchSubscriptionConsumerRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSubscriptionCreated provides a mock function with given fields: opts, sink, subId
func (_m *VRFCoordinatorV2Interface) WatchSubscriptionCreated(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, subId []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, subId)

	if len(ret) == 0 {
		panic("no return value specified for WatchSubscriptionCreated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, []uint64) error); ok {
		r1 = rf(opts, sink, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSubscriptionCreated'
type VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call struct {
	*mock.Call
}

// WatchSubscriptionCreated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) WatchSubscriptionCreated(opts interface{}, sink interface{}, subId interface{}) *VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call {
	return &VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call{Call: _e.mock.On("WatchSubscriptionCreated", opts, sink, subId)}
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, subId []uint64)) *VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated), args[2].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionCreated, []uint64) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchSubscriptionCreated_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSubscriptionFunded provides a mock function with given fields: opts, sink, subId
func (_m *VRFCoordinatorV2Interface) WatchSubscriptionFunded(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, subId []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, subId)

	if len(ret) == 0 {
		panic("no return value specified for WatchSubscriptionFunded")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, []uint64) error); ok {
		r1 = rf(opts, sink, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSubscriptionFunded'
type VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call struct {
	*mock.Call
}

// WatchSubscriptionFunded is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) WatchSubscriptionFunded(opts interface{}, sink interface{}, subId interface{}) *VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call {
	return &VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call{Call: _e.mock.On("WatchSubscriptionFunded", opts, sink, subId)}
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, subId []uint64)) *VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded), args[2].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionFunded, []uint64) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchSubscriptionFunded_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSubscriptionOwnerTransferRequested provides a mock function with given fields: opts, sink, subId
func (_m *VRFCoordinatorV2Interface) WatchSubscriptionOwnerTransferRequested(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, subId []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, subId)

	if len(ret) == 0 {
		panic("no return value specified for WatchSubscriptionOwnerTransferRequested")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, []uint64) error); ok {
		r1 = rf(opts, sink, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSubscriptionOwnerTransferRequested'
type VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call struct {
	*mock.Call
}

// WatchSubscriptionOwnerTransferRequested is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) WatchSubscriptionOwnerTransferRequested(opts interface{}, sink interface{}, subId interface{}) *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call {
	return &VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call{Call: _e.mock.On("WatchSubscriptionOwnerTransferRequested", opts, sink, subId)}
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, subId []uint64)) *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested), args[2].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferRequested, []uint64) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSubscriptionOwnerTransferred provides a mock function with given fields: opts, sink, subId
func (_m *VRFCoordinatorV2Interface) WatchSubscriptionOwnerTransferred(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, subId []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, subId)

	if len(ret) == 0 {
		panic("no return value specified for WatchSubscriptionOwnerTransferred")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, subId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, subId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, []uint64) error); ok {
		r1 = rf(opts, sink, subId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSubscriptionOwnerTransferred'
type VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call struct {
	*mock.Call
}

// WatchSubscriptionOwnerTransferred is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred
//   - subId []uint64
func (_e *VRFCoordinatorV2Interface_Expecter) WatchSubscriptionOwnerTransferred(opts interface{}, sink interface{}, subId interface{}) *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call {
	return &VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call{Call: _e.mock.On("WatchSubscriptionOwnerTransferred", opts, sink, subId)}
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, subId []uint64)) *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred), args[2].([]uint64))
	})
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call) Return(_a0 event.Subscription, _a1 error) *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *vrf_coordinator_v2.VRFCoordinatorV2SubscriptionOwnerTransferred, []uint64) (event.Subscription, error)) *VRFCoordinatorV2Interface_WatchSubscriptionOwnerTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// NewVRFCoordinatorV2Interface creates a new instance of VRFCoordinatorV2Interface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewVRFCoordinatorV2Interface(t interface {
	mock.TestingT
	Cleanup(func())
}) *VRFCoordinatorV2Interface {
	mock := &VRFCoordinatorV2Interface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
