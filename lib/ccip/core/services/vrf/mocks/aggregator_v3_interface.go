// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	big "math/big"

	aggregator_v3_interface "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated/aggregator_v3_interface"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"

	common "github.com/ethereum/go-ethereum/common"

	mock "github.com/stretchr/testify/mock"
)

// AggregatorV3InterfaceInterface is an autogenerated mock type for the AggregatorV3InterfaceInterface type
type AggregatorV3InterfaceInterface struct {
	mock.Mock
}

type AggregatorV3InterfaceInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *AggregatorV3InterfaceInterface) EXPECT() *AggregatorV3InterfaceInterface_Expecter {
	return &AggregatorV3InterfaceInterface_Expecter{mock: &_m.Mock}
}

// Address provides a mock function with given fields:
func (_m *AggregatorV3InterfaceInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// AggregatorV3InterfaceInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type AggregatorV3InterfaceInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *AggregatorV3InterfaceInterface_Expecter) Address() *AggregatorV3InterfaceInterface_Address_Call {
	return &AggregatorV3InterfaceInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *AggregatorV3InterfaceInterface_Address_Call) Run(run func()) *AggregatorV3InterfaceInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *AggregatorV3InterfaceInterface_Address_Call) Return(_a0 common.Address) *AggregatorV3InterfaceInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *AggregatorV3InterfaceInterface_Address_Call) RunAndReturn(run func() common.Address) *AggregatorV3InterfaceInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// Decimals provides a mock function with given fields: opts
func (_m *AggregatorV3InterfaceInterface) Decimals(opts *bind.CallOpts) (uint8, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Decimals")
	}

	var r0 uint8
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint8, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint8); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint8)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AggregatorV3InterfaceInterface_Decimals_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Decimals'
type AggregatorV3InterfaceInterface_Decimals_Call struct {
	*mock.Call
}

// Decimals is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *AggregatorV3InterfaceInterface_Expecter) Decimals(opts interface{}) *AggregatorV3InterfaceInterface_Decimals_Call {
	return &AggregatorV3InterfaceInterface_Decimals_Call{Call: _e.mock.On("Decimals", opts)}
}

func (_c *AggregatorV3InterfaceInterface_Decimals_Call) Run(run func(opts *bind.CallOpts)) *AggregatorV3InterfaceInterface_Decimals_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *AggregatorV3InterfaceInterface_Decimals_Call) Return(_a0 uint8, _a1 error) *AggregatorV3InterfaceInterface_Decimals_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *AggregatorV3InterfaceInterface_Decimals_Call) RunAndReturn(run func(*bind.CallOpts) (uint8, error)) *AggregatorV3InterfaceInterface_Decimals_Call {
	_c.Call.Return(run)
	return _c
}

// Description provides a mock function with given fields: opts
func (_m *AggregatorV3InterfaceInterface) Description(opts *bind.CallOpts) (string, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Description")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (string, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) string); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AggregatorV3InterfaceInterface_Description_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Description'
type AggregatorV3InterfaceInterface_Description_Call struct {
	*mock.Call
}

// Description is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *AggregatorV3InterfaceInterface_Expecter) Description(opts interface{}) *AggregatorV3InterfaceInterface_Description_Call {
	return &AggregatorV3InterfaceInterface_Description_Call{Call: _e.mock.On("Description", opts)}
}

func (_c *AggregatorV3InterfaceInterface_Description_Call) Run(run func(opts *bind.CallOpts)) *AggregatorV3InterfaceInterface_Description_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *AggregatorV3InterfaceInterface_Description_Call) Return(_a0 string, _a1 error) *AggregatorV3InterfaceInterface_Description_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *AggregatorV3InterfaceInterface_Description_Call) RunAndReturn(run func(*bind.CallOpts) (string, error)) *AggregatorV3InterfaceInterface_Description_Call {
	_c.Call.Return(run)
	return _c
}

// GetRoundData provides a mock function with given fields: opts, _roundId
func (_m *AggregatorV3InterfaceInterface) GetRoundData(opts *bind.CallOpts, _roundId *big.Int) (aggregator_v3_interface.GetRoundData, error) {
	ret := _m.Called(opts, _roundId)

	if len(ret) == 0 {
		panic("no return value specified for GetRoundData")
	}

	var r0 aggregator_v3_interface.GetRoundData
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) (aggregator_v3_interface.GetRoundData, error)); ok {
		return rf(opts, _roundId)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) aggregator_v3_interface.GetRoundData); ok {
		r0 = rf(opts, _roundId)
	} else {
		r0 = ret.Get(0).(aggregator_v3_interface.GetRoundData)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, *big.Int) error); ok {
		r1 = rf(opts, _roundId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AggregatorV3InterfaceInterface_GetRoundData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRoundData'
type AggregatorV3InterfaceInterface_GetRoundData_Call struct {
	*mock.Call
}

// GetRoundData is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - _roundId *big.Int
func (_e *AggregatorV3InterfaceInterface_Expecter) GetRoundData(opts interface{}, _roundId interface{}) *AggregatorV3InterfaceInterface_GetRoundData_Call {
	return &AggregatorV3InterfaceInterface_GetRoundData_Call{Call: _e.mock.On("GetRoundData", opts, _roundId)}
}

func (_c *AggregatorV3InterfaceInterface_GetRoundData_Call) Run(run func(opts *bind.CallOpts, _roundId *big.Int)) *AggregatorV3InterfaceInterface_GetRoundData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(*big.Int))
	})
	return _c
}

func (_c *AggregatorV3InterfaceInterface_GetRoundData_Call) Return(_a0 aggregator_v3_interface.GetRoundData, _a1 error) *AggregatorV3InterfaceInterface_GetRoundData_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *AggregatorV3InterfaceInterface_GetRoundData_Call) RunAndReturn(run func(*bind.CallOpts, *big.Int) (aggregator_v3_interface.GetRoundData, error)) *AggregatorV3InterfaceInterface_GetRoundData_Call {
	_c.Call.Return(run)
	return _c
}

// LatestRoundData provides a mock function with given fields: opts
func (_m *AggregatorV3InterfaceInterface) LatestRoundData(opts *bind.CallOpts) (aggregator_v3_interface.LatestRoundData, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LatestRoundData")
	}

	var r0 aggregator_v3_interface.LatestRoundData
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (aggregator_v3_interface.LatestRoundData, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) aggregator_v3_interface.LatestRoundData); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(aggregator_v3_interface.LatestRoundData)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AggregatorV3InterfaceInterface_LatestRoundData_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestRoundData'
type AggregatorV3InterfaceInterface_LatestRoundData_Call struct {
	*mock.Call
}

// LatestRoundData is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *AggregatorV3InterfaceInterface_Expecter) LatestRoundData(opts interface{}) *AggregatorV3InterfaceInterface_LatestRoundData_Call {
	return &AggregatorV3InterfaceInterface_LatestRoundData_Call{Call: _e.mock.On("LatestRoundData", opts)}
}

func (_c *AggregatorV3InterfaceInterface_LatestRoundData_Call) Run(run func(opts *bind.CallOpts)) *AggregatorV3InterfaceInterface_LatestRoundData_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *AggregatorV3InterfaceInterface_LatestRoundData_Call) Return(_a0 aggregator_v3_interface.LatestRoundData, _a1 error) *AggregatorV3InterfaceInterface_LatestRoundData_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *AggregatorV3InterfaceInterface_LatestRoundData_Call) RunAndReturn(run func(*bind.CallOpts) (aggregator_v3_interface.LatestRoundData, error)) *AggregatorV3InterfaceInterface_LatestRoundData_Call {
	_c.Call.Return(run)
	return _c
}

// Version provides a mock function with given fields: opts
func (_m *AggregatorV3InterfaceInterface) Version(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Version")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// AggregatorV3InterfaceInterface_Version_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Version'
type AggregatorV3InterfaceInterface_Version_Call struct {
	*mock.Call
}

// Version is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *AggregatorV3InterfaceInterface_Expecter) Version(opts interface{}) *AggregatorV3InterfaceInterface_Version_Call {
	return &AggregatorV3InterfaceInterface_Version_Call{Call: _e.mock.On("Version", opts)}
}

func (_c *AggregatorV3InterfaceInterface_Version_Call) Run(run func(opts *bind.CallOpts)) *AggregatorV3InterfaceInterface_Version_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *AggregatorV3InterfaceInterface_Version_Call) Return(_a0 *big.Int, _a1 error) *AggregatorV3InterfaceInterface_Version_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *AggregatorV3InterfaceInterface_Version_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *AggregatorV3InterfaceInterface_Version_Call {
	_c.Call.Return(run)
	return _c
}

// NewAggregatorV3InterfaceInterface creates a new instance of AggregatorV3InterfaceInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewAggregatorV3InterfaceInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *AggregatorV3InterfaceInterface {
	mock := &AggregatorV3InterfaceInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
