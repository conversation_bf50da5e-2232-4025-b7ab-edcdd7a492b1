RootDir = 'my/root/dir'

[Database]
[Database.Listener]
FallbackPollInterval = '2m0s'

[AuditLogger]
Enabled = true
ForwardToUrl = 'http://localhost:9898'
JsonWrapperKey = 'event'
Headers = ['Authorization: token', 'X-SomeOther-Header: value with spaces | and a bar+*']

[Log]
Level = 'panic'
JSONConsole = true

[JobPipeline]
[JobPipeline.HTTPRequest]
DefaultTimeout = '30s'

[OCR2]
Enabled = true
DatabaseTimeout = '20s'

[OCR]
Enabled = true
BlockchainTimeout = '5s'

[P2P]
IncomingMessageBufferSize = 999

[Keeper]
GasPriceBufferPercent = 10

[AutoPprof]
CPUProfileRate = 7

[[EVM]]
ChainID = '1'
FinalityDepth = 26
FinalityTagEnabled = false
FinalizedBlockOffset = 0

[EVM.OCR2]
[EVM.OCR2.Automation]
GasLimit = 10500000

[[EVM.Nodes]]
Name = 'primary'
WSURL = 'wss://web.socket/mainnet'

[[EVM.Nodes]]
Name = 'secondary'
HTTPURL = 'http://broadcast.mirror'
SendOnly = true

[[EVM]]
ChainID = '42'

[EVM.GasEstimator]
PriceDefault = '9.223372036854775807 ether'

[[EVM.Nodes]]
Name = 'foo'
WSURL = 'wss://web.socket/test/foo'

[[EVM]]
ChainID = '137'

[EVM.GasEstimator]
Mode = 'FixedPrice'

[[EVM.Nodes]]
Name = 'bar'
WSURL = 'wss://web.socket/test/bar'

[[Cosmos]]
ChainID = 'Ibiza-808'
Bech32Prefix = 'wasm'
GasToken = 'ucosm'
MaxMsgsPerBatch = 13

[[Cosmos.Nodes]]
Name = 'primary'
TendermintURL = 'http://columbus.cosmos.com'

[[Cosmos]]
ChainID = 'Malaga-420'
Bech32Prefix = 'wasm'
BlocksUntilTxTimeout = 20
GasToken = 'ucosm'

[[Cosmos.Nodes]]
Name = 'secondary'
TendermintURL = 'http://bombay.cosmos.com'

[[Solana]]
ChainID = 'mainnet'
MaxRetries = 12

[[Solana.Nodes]]
Name = 'primary'
URL = 'http://mainnet.solana.com'

[[Solana]]
ChainID = 'testnet'
OCR2CachePollPeriod = '1m0s'

[[Solana.Nodes]]
Name = 'secondary'
URL = 'http://testnet.solana.com'

[[Starknet]]
ChainID = 'foobar'
FeederURL = 'http://feeder.url'
ConfirmationPoll = '1h0m0s'

[[Starknet.Nodes]]
Name = 'primary'
URL = 'http://stark.node'
APIKey = 'key'
