package web

import (
	"github.com/smartcontractkit/chainlink/v2/core/services/chainlink"
	"github.com/smartcontractkit/chainlink/v2/core/services/keystore/keys/solkey"
	"github.com/smartcontractkit/chainlink/v2/core/web/presenters"
)

func NewSolanaKeysController(app chainlink.Application) KeysController {
	return NewKeysController[solkey.Key, presenters.SolanaKeyResource](app.GetKeyStore().<PERSON><PERSON>(), app.GetLogger(), app.GetAuditLogger(),
		"solana<PERSON><PERSON>", presenters.NewSolanaKeyResource, presenters.NewSolanaKeyResources)
}
