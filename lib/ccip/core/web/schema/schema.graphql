scalar Time
scalar Map
scalar Bytes

schema {
    query: Query
    mutation: Mutation
}

type Query {
    bridge(id: ID!): BridgePayload!
    bridges(offset: Int, limit: Int): BridgesPayload!
    chain(id: ID!): ChainPayload!
    chains(offset: Int, limit: Int): ChainsPayload!
    configv2: ConfigV2Payload!
    csaKeys: CSAKeysPayload!
    ethKeys: EthKeysPayload!
    ethTransaction(hash: ID!): EthTransactionPayload!
    ethTransactions(offset: Int, limit: Int): EthTransactionsPayload!
    ethTransactionsAttempts(offset: Int, limit: Int): EthTransactionAttemptsPayload!
    features: FeaturesPayload!
    feedsManager(id: ID!): FeedsManagerPayload!
    feedsManagers: FeedsManagersPayload!
    globalLogLevel: GlobalLogLevelPayload!
    job(id: ID!): JobPayload!
    jobs(offset: Int, limit: Int): JobsPayload!
    jobProposal(id: ID!): JobProposalPayload!
    jobRun(id: ID!): JobRunPayload!
    jobRuns(offset: Int, limit: Int): JobRunsPayload!
    node(id: ID!): NodePayload!
    nodes(offset: Int, limit: Int): NodesPayload!
    ocrKeyBundles: OCRKeyBundlesPayload!
    ocr2KeyBundles: OCR2KeyBundlesPayload!
    p2pKeys: P2PKeysPayload!
    solanaKeys: SolanaKeysPayload!
    sqlLogging: GetSQLLoggingPayload!
    vrfKey(id: ID!): VRFKeyPayload!
    vrfKeys: VRFKeysPayload!
}

type Mutation {
    approveJobProposalSpec(id: ID!, force: Boolean): ApproveJobProposalSpecPayload!
    cancelJobProposalSpec(id: ID!): CancelJobProposalSpecPayload!
    createAPIToken(input: CreateAPITokenInput!): CreateAPITokenPayload!
    createBridge(input: CreateBridgeInput!): CreateBridgePayload!
    createCSAKey: CreateCSAKeyPayload!
    createFeedsManager(input: CreateFeedsManagerInput!): CreateFeedsManagerPayload!
    createFeedsManagerChainConfig(input: CreateFeedsManagerChainConfigInput!): CreateFeedsManagerChainConfigPayload!
    createJob(input: CreateJobInput!): CreateJobPayload!
    createOCRKeyBundle: CreateOCRKeyBundlePayload!
    createOCR2KeyBundle(chainType: OCR2ChainType!): CreateOCR2KeyBundlePayload!
    createP2PKey: CreateP2PKeyPayload!
    deleteAPIToken(input: DeleteAPITokenInput!): DeleteAPITokenPayload!
    deleteBridge(id: ID!): DeleteBridgePayload!
    deleteCSAKey(id: ID!): DeleteCSAKeyPayload!
    deleteFeedsManagerChainConfig(id: ID!): DeleteFeedsManagerChainConfigPayload!
    deleteJob(id: ID!): DeleteJobPayload!
    deleteOCRKeyBundle(id: ID!): DeleteOCRKeyBundlePayload!
    deleteOCR2KeyBundle(id: ID!): DeleteOCR2KeyBundlePayload!
    deleteP2PKey(id: ID!): DeleteP2PKeyPayload!
    createVRFKey: CreateVRFKeyPayload!
    deleteVRFKey(id: ID!): DeleteVRFKeyPayload!
    dismissJobError(id: ID!): DismissJobErrorPayload!
    rejectJobProposalSpec(id: ID!): RejectJobProposalSpecPayload!
    runJob(id: ID!): RunJobPayload!
    setGlobalLogLevel(level: LogLevel!): SetGlobalLogLevelPayload!
    setSQLLogging(input: SetSQLLoggingInput!): SetSQLLoggingPayload!
    updateBridge(id: ID!, input: UpdateBridgeInput!): UpdateBridgePayload!
    updateFeedsManager(id: ID!, input: UpdateFeedsManagerInput!): UpdateFeedsManagerPayload!
    updateFeedsManagerChainConfig(id: ID!, input: UpdateFeedsManagerChainConfigInput!): UpdateFeedsManagerChainConfigPayload!
    updateJobProposalSpecDefinition(id: ID!, input: UpdateJobProposalSpecDefinitionInput!): UpdateJobProposalSpecDefinitionPayload!
    updateUserPassword(input: UpdatePasswordInput!): UpdatePasswordPayload!
}
