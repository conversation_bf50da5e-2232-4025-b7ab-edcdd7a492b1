package logger_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zapcore"

	"github.com/smartcontractkit/chainlink/v2/core/logger"
)

func TestNullLogger(t *testing.T) {
	t.<PERSON>()

	t.<PERSON>("names", func(t *testing.T) {
		t.<PERSON>()

		l := logger.NullLogger
		assert.Equal(t, l, l.<PERSON>("foo"))
		assert.Equal(t, l, l.<PERSON>("foo"))
		assert.Equal(t, l, l.<PERSON>(123))
	})

	t.<PERSON>("no-op", func(t *testing.T) {
		t.<PERSON>()

		l := logger.NullLogger
		l.SetLogLevel(zapcore.DebugLevel)
		l.Trace()
		l.Debug()
		l.Info()
		l.Warn()
		l.<PERSON>()
		l.<PERSON>()
		l.Panic()
		l.<PERSON>()
		l.<PERSON>("msg")
		l.Debugf("msg")
		l.Infof("msg")
		l.Warnf("msg")
		l.<PERSON><PERSON>("msg")
		l.<PERSON>("msg")
		l.<PERSON>("msg")
		l.<PERSON>("msg")
		l.<PERSON>("msg")
		l.Debugw("msg")
		l.Infow("msg")
		l.Warnw("msg")
		l.Errorw("msg")
		l.Criticalw("msg")
		l.Panicw("msg")
		l.Fatalw("msg")
		l.Recover(nil)
		assert.Nil(t, l.Sync())
	})
}
