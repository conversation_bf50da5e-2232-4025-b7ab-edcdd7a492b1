// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	log "github.com/smartcontractkit/chainlink/v2/core/chains/evm/log"
	mock "github.com/stretchr/testify/mock"

	sqlutil "github.com/smartcontractkit/chainlink-common/pkg/sqlutil"

	types "github.com/smartcontractkit/chainlink/v2/core/chains/evm/types"
)

// Broadcaster is an autogenerated mock type for the Broadcaster type
type Broadcaster struct {
	mock.Mock
}

type Broadcaster_Expecter struct {
	mock *mock.Mock
}

func (_m *Broadcaster) EXPECT() *Broadcaster_Expecter {
	return &Broadcaster_Expecter{mock: &_m.Mock}
}

// AddDependents provides a mock function with given fields: n
func (_m *Broadcaster) AddDependents(n int) {
	_m.Called(n)
}

// Broadcaster_AddDependents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddDependents'
type Broadcaster_AddDependents_Call struct {
	*mock.Call
}

// AddDependents is a helper method to define mock.On call
//   - n int
func (_e *Broadcaster_Expecter) AddDependents(n interface{}) *Broadcaster_AddDependents_Call {
	return &Broadcaster_AddDependents_Call{Call: _e.mock.On("AddDependents", n)}
}

func (_c *Broadcaster_AddDependents_Call) Run(run func(n int)) *Broadcaster_AddDependents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Broadcaster_AddDependents_Call) Return() *Broadcaster_AddDependents_Call {
	_c.Call.Return()
	return _c
}

func (_c *Broadcaster_AddDependents_Call) RunAndReturn(run func(int)) *Broadcaster_AddDependents_Call {
	_c.Call.Return(run)
	return _c
}

// AwaitDependents provides a mock function with given fields:
func (_m *Broadcaster) AwaitDependents() <-chan struct{} {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for AwaitDependents")
	}

	var r0 <-chan struct{}
	if rf, ok := ret.Get(0).(func() <-chan struct{}); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan struct{})
		}
	}

	return r0
}

// Broadcaster_AwaitDependents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AwaitDependents'
type Broadcaster_AwaitDependents_Call struct {
	*mock.Call
}

// AwaitDependents is a helper method to define mock.On call
func (_e *Broadcaster_Expecter) AwaitDependents() *Broadcaster_AwaitDependents_Call {
	return &Broadcaster_AwaitDependents_Call{Call: _e.mock.On("AwaitDependents")}
}

func (_c *Broadcaster_AwaitDependents_Call) Run(run func()) *Broadcaster_AwaitDependents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_AwaitDependents_Call) Return(_a0 <-chan struct{}) *Broadcaster_AwaitDependents_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_AwaitDependents_Call) RunAndReturn(run func() <-chan struct{}) *Broadcaster_AwaitDependents_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *Broadcaster) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Broadcaster_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Broadcaster_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *Broadcaster_Expecter) Close() *Broadcaster_Close_Call {
	return &Broadcaster_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *Broadcaster_Close_Call) Run(run func()) *Broadcaster_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_Close_Call) Return(_a0 error) *Broadcaster_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_Close_Call) RunAndReturn(run func() error) *Broadcaster_Close_Call {
	_c.Call.Return(run)
	return _c
}

// DependentReady provides a mock function with given fields:
func (_m *Broadcaster) DependentReady() {
	_m.Called()
}

// Broadcaster_DependentReady_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DependentReady'
type Broadcaster_DependentReady_Call struct {
	*mock.Call
}

// DependentReady is a helper method to define mock.On call
func (_e *Broadcaster_Expecter) DependentReady() *Broadcaster_DependentReady_Call {
	return &Broadcaster_DependentReady_Call{Call: _e.mock.On("DependentReady")}
}

func (_c *Broadcaster_DependentReady_Call) Run(run func()) *Broadcaster_DependentReady_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_DependentReady_Call) Return() *Broadcaster_DependentReady_Call {
	_c.Call.Return()
	return _c
}

func (_c *Broadcaster_DependentReady_Call) RunAndReturn(run func()) *Broadcaster_DependentReady_Call {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with given fields:
func (_m *Broadcaster) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// Broadcaster_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type Broadcaster_HealthReport_Call struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *Broadcaster_Expecter) HealthReport() *Broadcaster_HealthReport_Call {
	return &Broadcaster_HealthReport_Call{Call: _e.mock.On("HealthReport")}
}

func (_c *Broadcaster_HealthReport_Call) Run(run func()) *Broadcaster_HealthReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_HealthReport_Call) Return(_a0 map[string]error) *Broadcaster_HealthReport_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_HealthReport_Call) RunAndReturn(run func() map[string]error) *Broadcaster_HealthReport_Call {
	_c.Call.Return(run)
	return _c
}

// IsConnected provides a mock function with given fields:
func (_m *Broadcaster) IsConnected() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsConnected")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// Broadcaster_IsConnected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsConnected'
type Broadcaster_IsConnected_Call struct {
	*mock.Call
}

// IsConnected is a helper method to define mock.On call
func (_e *Broadcaster_Expecter) IsConnected() *Broadcaster_IsConnected_Call {
	return &Broadcaster_IsConnected_Call{Call: _e.mock.On("IsConnected")}
}

func (_c *Broadcaster_IsConnected_Call) Run(run func()) *Broadcaster_IsConnected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_IsConnected_Call) Return(_a0 bool) *Broadcaster_IsConnected_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_IsConnected_Call) RunAndReturn(run func() bool) *Broadcaster_IsConnected_Call {
	_c.Call.Return(run)
	return _c
}

// MarkConsumed provides a mock function with given fields: ctx, ds, lb
func (_m *Broadcaster) MarkConsumed(ctx context.Context, ds sqlutil.DataSource, lb log.Broadcast) error {
	ret := _m.Called(ctx, ds, lb)

	if len(ret) == 0 {
		panic("no return value specified for MarkConsumed")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, sqlutil.DataSource, log.Broadcast) error); ok {
		r0 = rf(ctx, ds, lb)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Broadcaster_MarkConsumed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkConsumed'
type Broadcaster_MarkConsumed_Call struct {
	*mock.Call
}

// MarkConsumed is a helper method to define mock.On call
//   - ctx context.Context
//   - ds sqlutil.DataSource
//   - lb log.Broadcast
func (_e *Broadcaster_Expecter) MarkConsumed(ctx interface{}, ds interface{}, lb interface{}) *Broadcaster_MarkConsumed_Call {
	return &Broadcaster_MarkConsumed_Call{Call: _e.mock.On("MarkConsumed", ctx, ds, lb)}
}

func (_c *Broadcaster_MarkConsumed_Call) Run(run func(ctx context.Context, ds sqlutil.DataSource, lb log.Broadcast)) *Broadcaster_MarkConsumed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(sqlutil.DataSource), args[2].(log.Broadcast))
	})
	return _c
}

func (_c *Broadcaster_MarkConsumed_Call) Return(_a0 error) *Broadcaster_MarkConsumed_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_MarkConsumed_Call) RunAndReturn(run func(context.Context, sqlutil.DataSource, log.Broadcast) error) *Broadcaster_MarkConsumed_Call {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with given fields:
func (_m *Broadcaster) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// Broadcaster_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type Broadcaster_Name_Call struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *Broadcaster_Expecter) Name() *Broadcaster_Name_Call {
	return &Broadcaster_Name_Call{Call: _e.mock.On("Name")}
}

func (_c *Broadcaster_Name_Call) Run(run func()) *Broadcaster_Name_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_Name_Call) Return(_a0 string) *Broadcaster_Name_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_Name_Call) RunAndReturn(run func() string) *Broadcaster_Name_Call {
	_c.Call.Return(run)
	return _c
}

// OnNewLongestChain provides a mock function with given fields: ctx, head
func (_m *Broadcaster) OnNewLongestChain(ctx context.Context, head *types.Head) {
	_m.Called(ctx, head)
}

// Broadcaster_OnNewLongestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnNewLongestChain'
type Broadcaster_OnNewLongestChain_Call struct {
	*mock.Call
}

// OnNewLongestChain is a helper method to define mock.On call
//   - ctx context.Context
//   - head *types.Head
func (_e *Broadcaster_Expecter) OnNewLongestChain(ctx interface{}, head interface{}) *Broadcaster_OnNewLongestChain_Call {
	return &Broadcaster_OnNewLongestChain_Call{Call: _e.mock.On("OnNewLongestChain", ctx, head)}
}

func (_c *Broadcaster_OnNewLongestChain_Call) Run(run func(ctx context.Context, head *types.Head)) *Broadcaster_OnNewLongestChain_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*types.Head))
	})
	return _c
}

func (_c *Broadcaster_OnNewLongestChain_Call) Return() *Broadcaster_OnNewLongestChain_Call {
	_c.Call.Return()
	return _c
}

func (_c *Broadcaster_OnNewLongestChain_Call) RunAndReturn(run func(context.Context, *types.Head)) *Broadcaster_OnNewLongestChain_Call {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with given fields:
func (_m *Broadcaster) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Broadcaster_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type Broadcaster_Ready_Call struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *Broadcaster_Expecter) Ready() *Broadcaster_Ready_Call {
	return &Broadcaster_Ready_Call{Call: _e.mock.On("Ready")}
}

func (_c *Broadcaster_Ready_Call) Run(run func()) *Broadcaster_Ready_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_Ready_Call) Return(_a0 error) *Broadcaster_Ready_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_Ready_Call) RunAndReturn(run func() error) *Broadcaster_Ready_Call {
	_c.Call.Return(run)
	return _c
}

// Register provides a mock function with given fields: listener, opts
func (_m *Broadcaster) Register(listener log.Listener, opts log.ListenerOpts) func() {
	ret := _m.Called(listener, opts)

	if len(ret) == 0 {
		panic("no return value specified for Register")
	}

	var r0 func()
	if rf, ok := ret.Get(0).(func(log.Listener, log.ListenerOpts) func()); ok {
		r0 = rf(listener, opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(func())
		}
	}

	return r0
}

// Broadcaster_Register_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Register'
type Broadcaster_Register_Call struct {
	*mock.Call
}

// Register is a helper method to define mock.On call
//   - listener log.Listener
//   - opts log.ListenerOpts
func (_e *Broadcaster_Expecter) Register(listener interface{}, opts interface{}) *Broadcaster_Register_Call {
	return &Broadcaster_Register_Call{Call: _e.mock.On("Register", listener, opts)}
}

func (_c *Broadcaster_Register_Call) Run(run func(listener log.Listener, opts log.ListenerOpts)) *Broadcaster_Register_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(log.Listener), args[1].(log.ListenerOpts))
	})
	return _c
}

func (_c *Broadcaster_Register_Call) Return(unsubscribe func()) *Broadcaster_Register_Call {
	_c.Call.Return(unsubscribe)
	return _c
}

func (_c *Broadcaster_Register_Call) RunAndReturn(run func(log.Listener, log.ListenerOpts) func()) *Broadcaster_Register_Call {
	_c.Call.Return(run)
	return _c
}

// ReplayFromBlock provides a mock function with given fields: number, forceBroadcast
func (_m *Broadcaster) ReplayFromBlock(number int64, forceBroadcast bool) {
	_m.Called(number, forceBroadcast)
}

// Broadcaster_ReplayFromBlock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReplayFromBlock'
type Broadcaster_ReplayFromBlock_Call struct {
	*mock.Call
}

// ReplayFromBlock is a helper method to define mock.On call
//   - number int64
//   - forceBroadcast bool
func (_e *Broadcaster_Expecter) ReplayFromBlock(number interface{}, forceBroadcast interface{}) *Broadcaster_ReplayFromBlock_Call {
	return &Broadcaster_ReplayFromBlock_Call{Call: _e.mock.On("ReplayFromBlock", number, forceBroadcast)}
}

func (_c *Broadcaster_ReplayFromBlock_Call) Run(run func(number int64, forceBroadcast bool)) *Broadcaster_ReplayFromBlock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int64), args[1].(bool))
	})
	return _c
}

func (_c *Broadcaster_ReplayFromBlock_Call) Return() *Broadcaster_ReplayFromBlock_Call {
	_c.Call.Return()
	return _c
}

func (_c *Broadcaster_ReplayFromBlock_Call) RunAndReturn(run func(int64, bool)) *Broadcaster_ReplayFromBlock_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *Broadcaster) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Broadcaster_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type Broadcaster_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *Broadcaster_Expecter) Start(_a0 interface{}) *Broadcaster_Start_Call {
	return &Broadcaster_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *Broadcaster_Start_Call) Run(run func(_a0 context.Context)) *Broadcaster_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Broadcaster_Start_Call) Return(_a0 error) *Broadcaster_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_Start_Call) RunAndReturn(run func(context.Context) error) *Broadcaster_Start_Call {
	_c.Call.Return(run)
	return _c
}

// WasAlreadyConsumed provides a mock function with given fields: ctx, lb
func (_m *Broadcaster) WasAlreadyConsumed(ctx context.Context, lb log.Broadcast) (bool, error) {
	ret := _m.Called(ctx, lb)

	if len(ret) == 0 {
		panic("no return value specified for WasAlreadyConsumed")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, log.Broadcast) (bool, error)); ok {
		return rf(ctx, lb)
	}
	if rf, ok := ret.Get(0).(func(context.Context, log.Broadcast) bool); ok {
		r0 = rf(ctx, lb)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, log.Broadcast) error); ok {
		r1 = rf(ctx, lb)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Broadcaster_WasAlreadyConsumed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WasAlreadyConsumed'
type Broadcaster_WasAlreadyConsumed_Call struct {
	*mock.Call
}

// WasAlreadyConsumed is a helper method to define mock.On call
//   - ctx context.Context
//   - lb log.Broadcast
func (_e *Broadcaster_Expecter) WasAlreadyConsumed(ctx interface{}, lb interface{}) *Broadcaster_WasAlreadyConsumed_Call {
	return &Broadcaster_WasAlreadyConsumed_Call{Call: _e.mock.On("WasAlreadyConsumed", ctx, lb)}
}

func (_c *Broadcaster_WasAlreadyConsumed_Call) Run(run func(ctx context.Context, lb log.Broadcast)) *Broadcaster_WasAlreadyConsumed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(log.Broadcast))
	})
	return _c
}

func (_c *Broadcaster_WasAlreadyConsumed_Call) Return(_a0 bool, _a1 error) *Broadcaster_WasAlreadyConsumed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Broadcaster_WasAlreadyConsumed_Call) RunAndReturn(run func(context.Context, log.Broadcast) (bool, error)) *Broadcaster_WasAlreadyConsumed_Call {
	_c.Call.Return(run)
	return _c
}

// NewBroadcaster creates a new instance of Broadcaster. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBroadcaster(t interface {
	mock.TestingT
	Cleanup(func())
}) *Broadcaster {
	mock := &Broadcaster{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
