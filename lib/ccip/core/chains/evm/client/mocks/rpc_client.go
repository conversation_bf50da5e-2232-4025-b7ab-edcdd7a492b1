// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	big "math/big"

	assets "github.com/smartcontractkit/chainlink-common/pkg/assets"

	common "github.com/ethereum/go-ethereum/common"

	commonclient "github.com/smartcontractkit/chainlink/v2/common/client"

	commontypes "github.com/smartcontractkit/chainlink/v2/common/types"

	context "context"

	coretypes "github.com/ethereum/go-ethereum/core/types"

	ethereum "github.com/ethereum/go-ethereum"

	evmassets "github.com/smartcontractkit/chainlink/v2/core/chains/evm/assets"

	mock "github.com/stretchr/testify/mock"

	rpc "github.com/ethereum/go-ethereum/rpc"

	types "github.com/smartcontractkit/chainlink/v2/core/chains/evm/types"
)

// RPCClient is an autogenerated mock type for the RPCClient type
type RPCClient struct {
	mock.Mock
}

type RPCClient_Expecter struct {
	mock *mock.Mock
}

func (_m *RPCClient) EXPECT() *RPCClient_Expecter {
	return &RPCClient_Expecter{mock: &_m.Mock}
}

// BalanceAt provides a mock function with given fields: ctx, accountAddress, blockNumber
func (_m *RPCClient) BalanceAt(ctx context.Context, accountAddress common.Address, blockNumber *big.Int) (*big.Int, error) {
	ret := _m.Called(ctx, accountAddress, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for BalanceAt")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) (*big.Int, error)); ok {
		return rf(ctx, accountAddress, blockNumber)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) *big.Int); ok {
		r0 = rf(ctx, accountAddress, blockNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, accountAddress, blockNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_BalanceAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BalanceAt'
type RPCClient_BalanceAt_Call struct {
	*mock.Call
}

// BalanceAt is a helper method to define mock.On call
//   - ctx context.Context
//   - accountAddress common.Address
//   - blockNumber *big.Int
func (_e *RPCClient_Expecter) BalanceAt(ctx interface{}, accountAddress interface{}, blockNumber interface{}) *RPCClient_BalanceAt_Call {
	return &RPCClient_BalanceAt_Call{Call: _e.mock.On("BalanceAt", ctx, accountAddress, blockNumber)}
}

func (_c *RPCClient_BalanceAt_Call) Run(run func(ctx context.Context, accountAddress common.Address, blockNumber *big.Int)) *RPCClient_BalanceAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *RPCClient_BalanceAt_Call) Return(_a0 *big.Int, _a1 error) *RPCClient_BalanceAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_BalanceAt_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) (*big.Int, error)) *RPCClient_BalanceAt_Call {
	_c.Call.Return(run)
	return _c
}

// BatchCallContext provides a mock function with given fields: ctx, b
func (_m *RPCClient) BatchCallContext(ctx context.Context, b []rpc.BatchElem) error {
	ret := _m.Called(ctx, b)

	if len(ret) == 0 {
		panic("no return value specified for BatchCallContext")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []rpc.BatchElem) error); ok {
		r0 = rf(ctx, b)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RPCClient_BatchCallContext_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCallContext'
type RPCClient_BatchCallContext_Call struct {
	*mock.Call
}

// BatchCallContext is a helper method to define mock.On call
//   - ctx context.Context
//   - b []rpc.BatchElem
func (_e *RPCClient_Expecter) BatchCallContext(ctx interface{}, b interface{}) *RPCClient_BatchCallContext_Call {
	return &RPCClient_BatchCallContext_Call{Call: _e.mock.On("BatchCallContext", ctx, b)}
}

func (_c *RPCClient_BatchCallContext_Call) Run(run func(ctx context.Context, b []rpc.BatchElem)) *RPCClient_BatchCallContext_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]rpc.BatchElem))
	})
	return _c
}

func (_c *RPCClient_BatchCallContext_Call) Return(_a0 error) *RPCClient_BatchCallContext_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RPCClient_BatchCallContext_Call) RunAndReturn(run func(context.Context, []rpc.BatchElem) error) *RPCClient_BatchCallContext_Call {
	_c.Call.Return(run)
	return _c
}

// BlockByHash provides a mock function with given fields: ctx, hash
func (_m *RPCClient) BlockByHash(ctx context.Context, hash common.Hash) (*types.Head, error) {
	ret := _m.Called(ctx, hash)

	if len(ret) == 0 {
		panic("no return value specified for BlockByHash")
	}

	var r0 *types.Head
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*types.Head, error)); ok {
		return rf(ctx, hash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *types.Head); ok {
		r0 = rf(ctx, hash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Head)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, hash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_BlockByHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BlockByHash'
type RPCClient_BlockByHash_Call struct {
	*mock.Call
}

// BlockByHash is a helper method to define mock.On call
//   - ctx context.Context
//   - hash common.Hash
func (_e *RPCClient_Expecter) BlockByHash(ctx interface{}, hash interface{}) *RPCClient_BlockByHash_Call {
	return &RPCClient_BlockByHash_Call{Call: _e.mock.On("BlockByHash", ctx, hash)}
}

func (_c *RPCClient_BlockByHash_Call) Run(run func(ctx context.Context, hash common.Hash)) *RPCClient_BlockByHash_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *RPCClient_BlockByHash_Call) Return(_a0 *types.Head, _a1 error) *RPCClient_BlockByHash_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_BlockByHash_Call) RunAndReturn(run func(context.Context, common.Hash) (*types.Head, error)) *RPCClient_BlockByHash_Call {
	_c.Call.Return(run)
	return _c
}

// BlockByHashGeth provides a mock function with given fields: ctx, hash
func (_m *RPCClient) BlockByHashGeth(ctx context.Context, hash common.Hash) (*coretypes.Block, error) {
	ret := _m.Called(ctx, hash)

	if len(ret) == 0 {
		panic("no return value specified for BlockByHashGeth")
	}

	var r0 *coretypes.Block
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*coretypes.Block, error)); ok {
		return rf(ctx, hash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *coretypes.Block); ok {
		r0 = rf(ctx, hash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*coretypes.Block)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, hash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_BlockByHashGeth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BlockByHashGeth'
type RPCClient_BlockByHashGeth_Call struct {
	*mock.Call
}

// BlockByHashGeth is a helper method to define mock.On call
//   - ctx context.Context
//   - hash common.Hash
func (_e *RPCClient_Expecter) BlockByHashGeth(ctx interface{}, hash interface{}) *RPCClient_BlockByHashGeth_Call {
	return &RPCClient_BlockByHashGeth_Call{Call: _e.mock.On("BlockByHashGeth", ctx, hash)}
}

func (_c *RPCClient_BlockByHashGeth_Call) Run(run func(ctx context.Context, hash common.Hash)) *RPCClient_BlockByHashGeth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *RPCClient_BlockByHashGeth_Call) Return(b *coretypes.Block, err error) *RPCClient_BlockByHashGeth_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *RPCClient_BlockByHashGeth_Call) RunAndReturn(run func(context.Context, common.Hash) (*coretypes.Block, error)) *RPCClient_BlockByHashGeth_Call {
	_c.Call.Return(run)
	return _c
}

// BlockByNumber provides a mock function with given fields: ctx, number
func (_m *RPCClient) BlockByNumber(ctx context.Context, number *big.Int) (*types.Head, error) {
	ret := _m.Called(ctx, number)

	if len(ret) == 0 {
		panic("no return value specified for BlockByNumber")
	}

	var r0 *types.Head
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) (*types.Head, error)); ok {
		return rf(ctx, number)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) *types.Head); ok {
		r0 = rf(ctx, number)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Head)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, number)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_BlockByNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BlockByNumber'
type RPCClient_BlockByNumber_Call struct {
	*mock.Call
}

// BlockByNumber is a helper method to define mock.On call
//   - ctx context.Context
//   - number *big.Int
func (_e *RPCClient_Expecter) BlockByNumber(ctx interface{}, number interface{}) *RPCClient_BlockByNumber_Call {
	return &RPCClient_BlockByNumber_Call{Call: _e.mock.On("BlockByNumber", ctx, number)}
}

func (_c *RPCClient_BlockByNumber_Call) Run(run func(ctx context.Context, number *big.Int)) *RPCClient_BlockByNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *RPCClient_BlockByNumber_Call) Return(_a0 *types.Head, _a1 error) *RPCClient_BlockByNumber_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_BlockByNumber_Call) RunAndReturn(run func(context.Context, *big.Int) (*types.Head, error)) *RPCClient_BlockByNumber_Call {
	_c.Call.Return(run)
	return _c
}

// BlockByNumberGeth provides a mock function with given fields: ctx, number
func (_m *RPCClient) BlockByNumberGeth(ctx context.Context, number *big.Int) (*coretypes.Block, error) {
	ret := _m.Called(ctx, number)

	if len(ret) == 0 {
		panic("no return value specified for BlockByNumberGeth")
	}

	var r0 *coretypes.Block
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) (*coretypes.Block, error)); ok {
		return rf(ctx, number)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) *coretypes.Block); ok {
		r0 = rf(ctx, number)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*coretypes.Block)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, number)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_BlockByNumberGeth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BlockByNumberGeth'
type RPCClient_BlockByNumberGeth_Call struct {
	*mock.Call
}

// BlockByNumberGeth is a helper method to define mock.On call
//   - ctx context.Context
//   - number *big.Int
func (_e *RPCClient_Expecter) BlockByNumberGeth(ctx interface{}, number interface{}) *RPCClient_BlockByNumberGeth_Call {
	return &RPCClient_BlockByNumberGeth_Call{Call: _e.mock.On("BlockByNumberGeth", ctx, number)}
}

func (_c *RPCClient_BlockByNumberGeth_Call) Run(run func(ctx context.Context, number *big.Int)) *RPCClient_BlockByNumberGeth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *RPCClient_BlockByNumberGeth_Call) Return(b *coretypes.Block, err error) *RPCClient_BlockByNumberGeth_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *RPCClient_BlockByNumberGeth_Call) RunAndReturn(run func(context.Context, *big.Int) (*coretypes.Block, error)) *RPCClient_BlockByNumberGeth_Call {
	_c.Call.Return(run)
	return _c
}

// CallContext provides a mock function with given fields: ctx, result, method, args
func (_m *RPCClient) CallContext(ctx context.Context, result interface{}, method string, args ...interface{}) error {
	var _ca []interface{}
	_ca = append(_ca, ctx, result, method)
	_ca = append(_ca, args...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CallContext")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}, string, ...interface{}) error); ok {
		r0 = rf(ctx, result, method, args...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RPCClient_CallContext_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CallContext'
type RPCClient_CallContext_Call struct {
	*mock.Call
}

// CallContext is a helper method to define mock.On call
//   - ctx context.Context
//   - result interface{}
//   - method string
//   - args ...interface{}
func (_e *RPCClient_Expecter) CallContext(ctx interface{}, result interface{}, method interface{}, args ...interface{}) *RPCClient_CallContext_Call {
	return &RPCClient_CallContext_Call{Call: _e.mock.On("CallContext",
		append([]interface{}{ctx, result, method}, args...)...)}
}

func (_c *RPCClient_CallContext_Call) Run(run func(ctx context.Context, result interface{}, method string, args ...interface{})) *RPCClient_CallContext_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]interface{}, len(args)-3)
		for i, a := range args[3:] {
			if a != nil {
				variadicArgs[i] = a.(interface{})
			}
		}
		run(args[0].(context.Context), args[1].(interface{}), args[2].(string), variadicArgs...)
	})
	return _c
}

func (_c *RPCClient_CallContext_Call) Return(_a0 error) *RPCClient_CallContext_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RPCClient_CallContext_Call) RunAndReturn(run func(context.Context, interface{}, string, ...interface{}) error) *RPCClient_CallContext_Call {
	_c.Call.Return(run)
	return _c
}

// CallContract provides a mock function with given fields: ctx, msg, blockNumber
func (_m *RPCClient) CallContract(ctx context.Context, msg interface{}, blockNumber *big.Int) ([]byte, error) {
	ret := _m.Called(ctx, msg, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for CallContract")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}, *big.Int) ([]byte, error)); ok {
		return rf(ctx, msg, blockNumber)
	}
	if rf, ok := ret.Get(0).(func(context.Context, interface{}, *big.Int) []byte); ok {
		r0 = rf(ctx, msg, blockNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, interface{}, *big.Int) error); ok {
		r1 = rf(ctx, msg, blockNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_CallContract_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CallContract'
type RPCClient_CallContract_Call struct {
	*mock.Call
}

// CallContract is a helper method to define mock.On call
//   - ctx context.Context
//   - msg interface{}
//   - blockNumber *big.Int
func (_e *RPCClient_Expecter) CallContract(ctx interface{}, msg interface{}, blockNumber interface{}) *RPCClient_CallContract_Call {
	return &RPCClient_CallContract_Call{Call: _e.mock.On("CallContract", ctx, msg, blockNumber)}
}

func (_c *RPCClient_CallContract_Call) Run(run func(ctx context.Context, msg interface{}, blockNumber *big.Int)) *RPCClient_CallContract_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}), args[2].(*big.Int))
	})
	return _c
}

func (_c *RPCClient_CallContract_Call) Return(rpcErr []byte, extractErr error) *RPCClient_CallContract_Call {
	_c.Call.Return(rpcErr, extractErr)
	return _c
}

func (_c *RPCClient_CallContract_Call) RunAndReturn(run func(context.Context, interface{}, *big.Int) ([]byte, error)) *RPCClient_CallContract_Call {
	_c.Call.Return(run)
	return _c
}

// ChainID provides a mock function with given fields: ctx
func (_m *RPCClient) ChainID(ctx context.Context) (*big.Int, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ChainID")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*big.Int, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *big.Int); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_ChainID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChainID'
type RPCClient_ChainID_Call struct {
	*mock.Call
}

// ChainID is a helper method to define mock.On call
//   - ctx context.Context
func (_e *RPCClient_Expecter) ChainID(ctx interface{}) *RPCClient_ChainID_Call {
	return &RPCClient_ChainID_Call{Call: _e.mock.On("ChainID", ctx)}
}

func (_c *RPCClient_ChainID_Call) Run(run func(ctx context.Context)) *RPCClient_ChainID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_ChainID_Call) Return(_a0 *big.Int, _a1 error) *RPCClient_ChainID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_ChainID_Call) RunAndReturn(run func(context.Context) (*big.Int, error)) *RPCClient_ChainID_Call {
	_c.Call.Return(run)
	return _c
}

// ClientVersion provides a mock function with given fields: _a0
func (_m *RPCClient) ClientVersion(_a0 context.Context) (string, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for ClientVersion")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (string, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_ClientVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ClientVersion'
type RPCClient_ClientVersion_Call struct {
	*mock.Call
}

// ClientVersion is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *RPCClient_Expecter) ClientVersion(_a0 interface{}) *RPCClient_ClientVersion_Call {
	return &RPCClient_ClientVersion_Call{Call: _e.mock.On("ClientVersion", _a0)}
}

func (_c *RPCClient_ClientVersion_Call) Run(run func(_a0 context.Context)) *RPCClient_ClientVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_ClientVersion_Call) Return(_a0 string, _a1 error) *RPCClient_ClientVersion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_ClientVersion_Call) RunAndReturn(run func(context.Context) (string, error)) *RPCClient_ClientVersion_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *RPCClient) Close() {
	_m.Called()
}

// RPCClient_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type RPCClient_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *RPCClient_Expecter) Close() *RPCClient_Close_Call {
	return &RPCClient_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *RPCClient_Close_Call) Run(run func()) *RPCClient_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RPCClient_Close_Call) Return() *RPCClient_Close_Call {
	_c.Call.Return()
	return _c
}

func (_c *RPCClient_Close_Call) RunAndReturn(run func()) *RPCClient_Close_Call {
	_c.Call.Return(run)
	return _c
}

// CodeAt provides a mock function with given fields: ctx, account, blockNumber
func (_m *RPCClient) CodeAt(ctx context.Context, account common.Address, blockNumber *big.Int) ([]byte, error) {
	ret := _m.Called(ctx, account, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for CodeAt")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) ([]byte, error)); ok {
		return rf(ctx, account, blockNumber)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) []byte); ok {
		r0 = rf(ctx, account, blockNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, account, blockNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_CodeAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CodeAt'
type RPCClient_CodeAt_Call struct {
	*mock.Call
}

// CodeAt is a helper method to define mock.On call
//   - ctx context.Context
//   - account common.Address
//   - blockNumber *big.Int
func (_e *RPCClient_Expecter) CodeAt(ctx interface{}, account interface{}, blockNumber interface{}) *RPCClient_CodeAt_Call {
	return &RPCClient_CodeAt_Call{Call: _e.mock.On("CodeAt", ctx, account, blockNumber)}
}

func (_c *RPCClient_CodeAt_Call) Run(run func(ctx context.Context, account common.Address, blockNumber *big.Int)) *RPCClient_CodeAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *RPCClient_CodeAt_Call) Return(_a0 []byte, _a1 error) *RPCClient_CodeAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_CodeAt_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) ([]byte, error)) *RPCClient_CodeAt_Call {
	_c.Call.Return(run)
	return _c
}

// Dial provides a mock function with given fields: ctx
func (_m *RPCClient) Dial(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Dial")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RPCClient_Dial_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Dial'
type RPCClient_Dial_Call struct {
	*mock.Call
}

// Dial is a helper method to define mock.On call
//   - ctx context.Context
func (_e *RPCClient_Expecter) Dial(ctx interface{}) *RPCClient_Dial_Call {
	return &RPCClient_Dial_Call{Call: _e.mock.On("Dial", ctx)}
}

func (_c *RPCClient_Dial_Call) Run(run func(ctx context.Context)) *RPCClient_Dial_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_Dial_Call) Return(_a0 error) *RPCClient_Dial_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RPCClient_Dial_Call) RunAndReturn(run func(context.Context) error) *RPCClient_Dial_Call {
	_c.Call.Return(run)
	return _c
}

// DialHTTP provides a mock function with given fields:
func (_m *RPCClient) DialHTTP() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for DialHTTP")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RPCClient_DialHTTP_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DialHTTP'
type RPCClient_DialHTTP_Call struct {
	*mock.Call
}

// DialHTTP is a helper method to define mock.On call
func (_e *RPCClient_Expecter) DialHTTP() *RPCClient_DialHTTP_Call {
	return &RPCClient_DialHTTP_Call{Call: _e.mock.On("DialHTTP")}
}

func (_c *RPCClient_DialHTTP_Call) Run(run func()) *RPCClient_DialHTTP_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RPCClient_DialHTTP_Call) Return(_a0 error) *RPCClient_DialHTTP_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RPCClient_DialHTTP_Call) RunAndReturn(run func() error) *RPCClient_DialHTTP_Call {
	_c.Call.Return(run)
	return _c
}

// DisconnectAll provides a mock function with given fields:
func (_m *RPCClient) DisconnectAll() {
	_m.Called()
}

// RPCClient_DisconnectAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DisconnectAll'
type RPCClient_DisconnectAll_Call struct {
	*mock.Call
}

// DisconnectAll is a helper method to define mock.On call
func (_e *RPCClient_Expecter) DisconnectAll() *RPCClient_DisconnectAll_Call {
	return &RPCClient_DisconnectAll_Call{Call: _e.mock.On("DisconnectAll")}
}

func (_c *RPCClient_DisconnectAll_Call) Run(run func()) *RPCClient_DisconnectAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RPCClient_DisconnectAll_Call) Return() *RPCClient_DisconnectAll_Call {
	_c.Call.Return()
	return _c
}

func (_c *RPCClient_DisconnectAll_Call) RunAndReturn(run func()) *RPCClient_DisconnectAll_Call {
	_c.Call.Return(run)
	return _c
}

// EstimateGas provides a mock function with given fields: ctx, call
func (_m *RPCClient) EstimateGas(ctx context.Context, call interface{}) (uint64, error) {
	ret := _m.Called(ctx, call)

	if len(ret) == 0 {
		panic("no return value specified for EstimateGas")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) (uint64, error)); ok {
		return rf(ctx, call)
	}
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) uint64); ok {
		r0 = rf(ctx, call)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, interface{}) error); ok {
		r1 = rf(ctx, call)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_EstimateGas_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EstimateGas'
type RPCClient_EstimateGas_Call struct {
	*mock.Call
}

// EstimateGas is a helper method to define mock.On call
//   - ctx context.Context
//   - call interface{}
func (_e *RPCClient_Expecter) EstimateGas(ctx interface{}, call interface{}) *RPCClient_EstimateGas_Call {
	return &RPCClient_EstimateGas_Call{Call: _e.mock.On("EstimateGas", ctx, call)}
}

func (_c *RPCClient_EstimateGas_Call) Run(run func(ctx context.Context, call interface{})) *RPCClient_EstimateGas_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *RPCClient_EstimateGas_Call) Return(gas uint64, err error) *RPCClient_EstimateGas_Call {
	_c.Call.Return(gas, err)
	return _c
}

func (_c *RPCClient_EstimateGas_Call) RunAndReturn(run func(context.Context, interface{}) (uint64, error)) *RPCClient_EstimateGas_Call {
	_c.Call.Return(run)
	return _c
}

// FeeHistory provides a mock function with given fields: ctx, blockCount, rewardPercentiles
func (_m *RPCClient) FeeHistory(ctx context.Context, blockCount uint64, rewardPercentiles []float64) (*ethereum.FeeHistory, error) {
	ret := _m.Called(ctx, blockCount, rewardPercentiles)

	if len(ret) == 0 {
		panic("no return value specified for FeeHistory")
	}

	var r0 *ethereum.FeeHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, []float64) (*ethereum.FeeHistory, error)); ok {
		return rf(ctx, blockCount, rewardPercentiles)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, []float64) *ethereum.FeeHistory); ok {
		r0 = rf(ctx, blockCount, rewardPercentiles)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ethereum.FeeHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, []float64) error); ok {
		r1 = rf(ctx, blockCount, rewardPercentiles)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_FeeHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FeeHistory'
type RPCClient_FeeHistory_Call struct {
	*mock.Call
}

// FeeHistory is a helper method to define mock.On call
//   - ctx context.Context
//   - blockCount uint64
//   - rewardPercentiles []float64
func (_e *RPCClient_Expecter) FeeHistory(ctx interface{}, blockCount interface{}, rewardPercentiles interface{}) *RPCClient_FeeHistory_Call {
	return &RPCClient_FeeHistory_Call{Call: _e.mock.On("FeeHistory", ctx, blockCount, rewardPercentiles)}
}

func (_c *RPCClient_FeeHistory_Call) Run(run func(ctx context.Context, blockCount uint64, rewardPercentiles []float64)) *RPCClient_FeeHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].([]float64))
	})
	return _c
}

func (_c *RPCClient_FeeHistory_Call) Return(feeHistory *ethereum.FeeHistory, err error) *RPCClient_FeeHistory_Call {
	_c.Call.Return(feeHistory, err)
	return _c
}

func (_c *RPCClient_FeeHistory_Call) RunAndReturn(run func(context.Context, uint64, []float64) (*ethereum.FeeHistory, error)) *RPCClient_FeeHistory_Call {
	_c.Call.Return(run)
	return _c
}

// FilterEvents provides a mock function with given fields: ctx, query
func (_m *RPCClient) FilterEvents(ctx context.Context, query ethereum.FilterQuery) ([]coretypes.Log, error) {
	ret := _m.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FilterEvents")
	}

	var r0 []coretypes.Log
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.FilterQuery) ([]coretypes.Log, error)); ok {
		return rf(ctx, query)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.FilterQuery) []coretypes.Log); ok {
		r0 = rf(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]coretypes.Log)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ethereum.FilterQuery) error); ok {
		r1 = rf(ctx, query)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_FilterEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterEvents'
type RPCClient_FilterEvents_Call struct {
	*mock.Call
}

// FilterEvents is a helper method to define mock.On call
//   - ctx context.Context
//   - query ethereum.FilterQuery
func (_e *RPCClient_Expecter) FilterEvents(ctx interface{}, query interface{}) *RPCClient_FilterEvents_Call {
	return &RPCClient_FilterEvents_Call{Call: _e.mock.On("FilterEvents", ctx, query)}
}

func (_c *RPCClient_FilterEvents_Call) Run(run func(ctx context.Context, query ethereum.FilterQuery)) *RPCClient_FilterEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ethereum.FilterQuery))
	})
	return _c
}

func (_c *RPCClient_FilterEvents_Call) Return(_a0 []coretypes.Log, _a1 error) *RPCClient_FilterEvents_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_FilterEvents_Call) RunAndReturn(run func(context.Context, ethereum.FilterQuery) ([]coretypes.Log, error)) *RPCClient_FilterEvents_Call {
	_c.Call.Return(run)
	return _c
}

// GetInterceptedChainInfo provides a mock function with given fields:
func (_m *RPCClient) GetInterceptedChainInfo() (commonclient.ChainInfo, commonclient.ChainInfo) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetInterceptedChainInfo")
	}

	var r0 commonclient.ChainInfo
	var r1 commonclient.ChainInfo
	if rf, ok := ret.Get(0).(func() (commonclient.ChainInfo, commonclient.ChainInfo)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() commonclient.ChainInfo); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(commonclient.ChainInfo)
	}

	if rf, ok := ret.Get(1).(func() commonclient.ChainInfo); ok {
		r1 = rf()
	} else {
		r1 = ret.Get(1).(commonclient.ChainInfo)
	}

	return r0, r1
}

// RPCClient_GetInterceptedChainInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetInterceptedChainInfo'
type RPCClient_GetInterceptedChainInfo_Call struct {
	*mock.Call
}

// GetInterceptedChainInfo is a helper method to define mock.On call
func (_e *RPCClient_Expecter) GetInterceptedChainInfo() *RPCClient_GetInterceptedChainInfo_Call {
	return &RPCClient_GetInterceptedChainInfo_Call{Call: _e.mock.On("GetInterceptedChainInfo")}
}

func (_c *RPCClient_GetInterceptedChainInfo_Call) Run(run func()) *RPCClient_GetInterceptedChainInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RPCClient_GetInterceptedChainInfo_Call) Return(latest commonclient.ChainInfo, highestUserObservations commonclient.ChainInfo) *RPCClient_GetInterceptedChainInfo_Call {
	_c.Call.Return(latest, highestUserObservations)
	return _c
}

func (_c *RPCClient_GetInterceptedChainInfo_Call) RunAndReturn(run func() (commonclient.ChainInfo, commonclient.ChainInfo)) *RPCClient_GetInterceptedChainInfo_Call {
	_c.Call.Return(run)
	return _c
}

// HeaderByHash provides a mock function with given fields: ctx, h
func (_m *RPCClient) HeaderByHash(ctx context.Context, h common.Hash) (*coretypes.Header, error) {
	ret := _m.Called(ctx, h)

	if len(ret) == 0 {
		panic("no return value specified for HeaderByHash")
	}

	var r0 *coretypes.Header
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*coretypes.Header, error)); ok {
		return rf(ctx, h)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *coretypes.Header); ok {
		r0 = rf(ctx, h)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*coretypes.Header)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, h)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_HeaderByHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HeaderByHash'
type RPCClient_HeaderByHash_Call struct {
	*mock.Call
}

// HeaderByHash is a helper method to define mock.On call
//   - ctx context.Context
//   - h common.Hash
func (_e *RPCClient_Expecter) HeaderByHash(ctx interface{}, h interface{}) *RPCClient_HeaderByHash_Call {
	return &RPCClient_HeaderByHash_Call{Call: _e.mock.On("HeaderByHash", ctx, h)}
}

func (_c *RPCClient_HeaderByHash_Call) Run(run func(ctx context.Context, h common.Hash)) *RPCClient_HeaderByHash_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *RPCClient_HeaderByHash_Call) Return(head *coretypes.Header, err error) *RPCClient_HeaderByHash_Call {
	_c.Call.Return(head, err)
	return _c
}

func (_c *RPCClient_HeaderByHash_Call) RunAndReturn(run func(context.Context, common.Hash) (*coretypes.Header, error)) *RPCClient_HeaderByHash_Call {
	_c.Call.Return(run)
	return _c
}

// HeaderByNumber provides a mock function with given fields: ctx, n
func (_m *RPCClient) HeaderByNumber(ctx context.Context, n *big.Int) (*coretypes.Header, error) {
	ret := _m.Called(ctx, n)

	if len(ret) == 0 {
		panic("no return value specified for HeaderByNumber")
	}

	var r0 *coretypes.Header
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) (*coretypes.Header, error)); ok {
		return rf(ctx, n)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) *coretypes.Header); ok {
		r0 = rf(ctx, n)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*coretypes.Header)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, n)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_HeaderByNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HeaderByNumber'
type RPCClient_HeaderByNumber_Call struct {
	*mock.Call
}

// HeaderByNumber is a helper method to define mock.On call
//   - ctx context.Context
//   - n *big.Int
func (_e *RPCClient_Expecter) HeaderByNumber(ctx interface{}, n interface{}) *RPCClient_HeaderByNumber_Call {
	return &RPCClient_HeaderByNumber_Call{Call: _e.mock.On("HeaderByNumber", ctx, n)}
}

func (_c *RPCClient_HeaderByNumber_Call) Run(run func(ctx context.Context, n *big.Int)) *RPCClient_HeaderByNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *RPCClient_HeaderByNumber_Call) Return(head *coretypes.Header, err error) *RPCClient_HeaderByNumber_Call {
	_c.Call.Return(head, err)
	return _c
}

func (_c *RPCClient_HeaderByNumber_Call) RunAndReturn(run func(context.Context, *big.Int) (*coretypes.Header, error)) *RPCClient_HeaderByNumber_Call {
	_c.Call.Return(run)
	return _c
}

// IsSyncing provides a mock function with given fields: ctx
func (_m *RPCClient) IsSyncing(ctx context.Context) (bool, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for IsSyncing")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (bool, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_IsSyncing_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsSyncing'
type RPCClient_IsSyncing_Call struct {
	*mock.Call
}

// IsSyncing is a helper method to define mock.On call
//   - ctx context.Context
func (_e *RPCClient_Expecter) IsSyncing(ctx interface{}) *RPCClient_IsSyncing_Call {
	return &RPCClient_IsSyncing_Call{Call: _e.mock.On("IsSyncing", ctx)}
}

func (_c *RPCClient_IsSyncing_Call) Run(run func(ctx context.Context)) *RPCClient_IsSyncing_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_IsSyncing_Call) Return(_a0 bool, _a1 error) *RPCClient_IsSyncing_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_IsSyncing_Call) RunAndReturn(run func(context.Context) (bool, error)) *RPCClient_IsSyncing_Call {
	_c.Call.Return(run)
	return _c
}

// LINKBalance provides a mock function with given fields: ctx, accountAddress, linkAddress
func (_m *RPCClient) LINKBalance(ctx context.Context, accountAddress common.Address, linkAddress common.Address) (*assets.Link, error) {
	ret := _m.Called(ctx, accountAddress, linkAddress)

	if len(ret) == 0 {
		panic("no return value specified for LINKBalance")
	}

	var r0 *assets.Link
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, common.Address) (*assets.Link, error)); ok {
		return rf(ctx, accountAddress, linkAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, common.Address) *assets.Link); ok {
		r0 = rf(ctx, accountAddress, linkAddress)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*assets.Link)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, common.Address) error); ok {
		r1 = rf(ctx, accountAddress, linkAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_LINKBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LINKBalance'
type RPCClient_LINKBalance_Call struct {
	*mock.Call
}

// LINKBalance is a helper method to define mock.On call
//   - ctx context.Context
//   - accountAddress common.Address
//   - linkAddress common.Address
func (_e *RPCClient_Expecter) LINKBalance(ctx interface{}, accountAddress interface{}, linkAddress interface{}) *RPCClient_LINKBalance_Call {
	return &RPCClient_LINKBalance_Call{Call: _e.mock.On("LINKBalance", ctx, accountAddress, linkAddress)}
}

func (_c *RPCClient_LINKBalance_Call) Run(run func(ctx context.Context, accountAddress common.Address, linkAddress common.Address)) *RPCClient_LINKBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(common.Address))
	})
	return _c
}

func (_c *RPCClient_LINKBalance_Call) Return(_a0 *assets.Link, _a1 error) *RPCClient_LINKBalance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_LINKBalance_Call) RunAndReturn(run func(context.Context, common.Address, common.Address) (*assets.Link, error)) *RPCClient_LINKBalance_Call {
	_c.Call.Return(run)
	return _c
}

// LatestBlockHeight provides a mock function with given fields: _a0
func (_m *RPCClient) LatestBlockHeight(_a0 context.Context) (*big.Int, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for LatestBlockHeight")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*big.Int, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *big.Int); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_LatestBlockHeight_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestBlockHeight'
type RPCClient_LatestBlockHeight_Call struct {
	*mock.Call
}

// LatestBlockHeight is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *RPCClient_Expecter) LatestBlockHeight(_a0 interface{}) *RPCClient_LatestBlockHeight_Call {
	return &RPCClient_LatestBlockHeight_Call{Call: _e.mock.On("LatestBlockHeight", _a0)}
}

func (_c *RPCClient_LatestBlockHeight_Call) Run(run func(_a0 context.Context)) *RPCClient_LatestBlockHeight_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_LatestBlockHeight_Call) Return(_a0 *big.Int, _a1 error) *RPCClient_LatestBlockHeight_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_LatestBlockHeight_Call) RunAndReturn(run func(context.Context) (*big.Int, error)) *RPCClient_LatestBlockHeight_Call {
	_c.Call.Return(run)
	return _c
}

// LatestFinalizedBlock provides a mock function with given fields: ctx
func (_m *RPCClient) LatestFinalizedBlock(ctx context.Context) (*types.Head, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for LatestFinalizedBlock")
	}

	var r0 *types.Head
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*types.Head, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *types.Head); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Head)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_LatestFinalizedBlock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestFinalizedBlock'
type RPCClient_LatestFinalizedBlock_Call struct {
	*mock.Call
}

// LatestFinalizedBlock is a helper method to define mock.On call
//   - ctx context.Context
func (_e *RPCClient_Expecter) LatestFinalizedBlock(ctx interface{}) *RPCClient_LatestFinalizedBlock_Call {
	return &RPCClient_LatestFinalizedBlock_Call{Call: _e.mock.On("LatestFinalizedBlock", ctx)}
}

func (_c *RPCClient_LatestFinalizedBlock_Call) Run(run func(ctx context.Context)) *RPCClient_LatestFinalizedBlock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_LatestFinalizedBlock_Call) Return(_a0 *types.Head, _a1 error) *RPCClient_LatestFinalizedBlock_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_LatestFinalizedBlock_Call) RunAndReturn(run func(context.Context) (*types.Head, error)) *RPCClient_LatestFinalizedBlock_Call {
	_c.Call.Return(run)
	return _c
}

// PendingCallContract provides a mock function with given fields: ctx, msg
func (_m *RPCClient) PendingCallContract(ctx context.Context, msg interface{}) ([]byte, error) {
	ret := _m.Called(ctx, msg)

	if len(ret) == 0 {
		panic("no return value specified for PendingCallContract")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) ([]byte, error)); ok {
		return rf(ctx, msg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, interface{}) []byte); ok {
		r0 = rf(ctx, msg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, interface{}) error); ok {
		r1 = rf(ctx, msg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_PendingCallContract_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PendingCallContract'
type RPCClient_PendingCallContract_Call struct {
	*mock.Call
}

// PendingCallContract is a helper method to define mock.On call
//   - ctx context.Context
//   - msg interface{}
func (_e *RPCClient_Expecter) PendingCallContract(ctx interface{}, msg interface{}) *RPCClient_PendingCallContract_Call {
	return &RPCClient_PendingCallContract_Call{Call: _e.mock.On("PendingCallContract", ctx, msg)}
}

func (_c *RPCClient_PendingCallContract_Call) Run(run func(ctx context.Context, msg interface{})) *RPCClient_PendingCallContract_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(interface{}))
	})
	return _c
}

func (_c *RPCClient_PendingCallContract_Call) Return(rpcErr []byte, extractErr error) *RPCClient_PendingCallContract_Call {
	_c.Call.Return(rpcErr, extractErr)
	return _c
}

func (_c *RPCClient_PendingCallContract_Call) RunAndReturn(run func(context.Context, interface{}) ([]byte, error)) *RPCClient_PendingCallContract_Call {
	_c.Call.Return(run)
	return _c
}

// PendingCodeAt provides a mock function with given fields: ctx, account
func (_m *RPCClient) PendingCodeAt(ctx context.Context, account common.Address) ([]byte, error) {
	ret := _m.Called(ctx, account)

	if len(ret) == 0 {
		panic("no return value specified for PendingCodeAt")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) ([]byte, error)); ok {
		return rf(ctx, account)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) []byte); ok {
		r0 = rf(ctx, account)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address) error); ok {
		r1 = rf(ctx, account)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_PendingCodeAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PendingCodeAt'
type RPCClient_PendingCodeAt_Call struct {
	*mock.Call
}

// PendingCodeAt is a helper method to define mock.On call
//   - ctx context.Context
//   - account common.Address
func (_e *RPCClient_Expecter) PendingCodeAt(ctx interface{}, account interface{}) *RPCClient_PendingCodeAt_Call {
	return &RPCClient_PendingCodeAt_Call{Call: _e.mock.On("PendingCodeAt", ctx, account)}
}

func (_c *RPCClient_PendingCodeAt_Call) Run(run func(ctx context.Context, account common.Address)) *RPCClient_PendingCodeAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address))
	})
	return _c
}

func (_c *RPCClient_PendingCodeAt_Call) Return(b []byte, err error) *RPCClient_PendingCodeAt_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *RPCClient_PendingCodeAt_Call) RunAndReturn(run func(context.Context, common.Address) ([]byte, error)) *RPCClient_PendingCodeAt_Call {
	_c.Call.Return(run)
	return _c
}

// PendingSequenceAt provides a mock function with given fields: ctx, addr
func (_m *RPCClient) PendingSequenceAt(ctx context.Context, addr common.Address) (types.Nonce, error) {
	ret := _m.Called(ctx, addr)

	if len(ret) == 0 {
		panic("no return value specified for PendingSequenceAt")
	}

	var r0 types.Nonce
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) (types.Nonce, error)); ok {
		return rf(ctx, addr)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) types.Nonce); ok {
		r0 = rf(ctx, addr)
	} else {
		r0 = ret.Get(0).(types.Nonce)
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address) error); ok {
		r1 = rf(ctx, addr)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_PendingSequenceAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PendingSequenceAt'
type RPCClient_PendingSequenceAt_Call struct {
	*mock.Call
}

// PendingSequenceAt is a helper method to define mock.On call
//   - ctx context.Context
//   - addr common.Address
func (_e *RPCClient_Expecter) PendingSequenceAt(ctx interface{}, addr interface{}) *RPCClient_PendingSequenceAt_Call {
	return &RPCClient_PendingSequenceAt_Call{Call: _e.mock.On("PendingSequenceAt", ctx, addr)}
}

func (_c *RPCClient_PendingSequenceAt_Call) Run(run func(ctx context.Context, addr common.Address)) *RPCClient_PendingSequenceAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address))
	})
	return _c
}

func (_c *RPCClient_PendingSequenceAt_Call) Return(_a0 types.Nonce, _a1 error) *RPCClient_PendingSequenceAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_PendingSequenceAt_Call) RunAndReturn(run func(context.Context, common.Address) (types.Nonce, error)) *RPCClient_PendingSequenceAt_Call {
	_c.Call.Return(run)
	return _c
}

// SendEmptyTransaction provides a mock function with given fields: ctx, newTxAttempt, seq, gasLimit, fee, fromAddress
func (_m *RPCClient) SendEmptyTransaction(ctx context.Context, newTxAttempt func(types.Nonce, uint32, *evmassets.Wei, common.Address) (interface{}, error), seq types.Nonce, gasLimit uint32, fee *evmassets.Wei, fromAddress common.Address) (string, error) {
	ret := _m.Called(ctx, newTxAttempt, seq, gasLimit, fee, fromAddress)

	if len(ret) == 0 {
		panic("no return value specified for SendEmptyTransaction")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, func(types.Nonce, uint32, *evmassets.Wei, common.Address) (interface{}, error), types.Nonce, uint32, *evmassets.Wei, common.Address) (string, error)); ok {
		return rf(ctx, newTxAttempt, seq, gasLimit, fee, fromAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, func(types.Nonce, uint32, *evmassets.Wei, common.Address) (interface{}, error), types.Nonce, uint32, *evmassets.Wei, common.Address) string); ok {
		r0 = rf(ctx, newTxAttempt, seq, gasLimit, fee, fromAddress)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, func(types.Nonce, uint32, *evmassets.Wei, common.Address) (interface{}, error), types.Nonce, uint32, *evmassets.Wei, common.Address) error); ok {
		r1 = rf(ctx, newTxAttempt, seq, gasLimit, fee, fromAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_SendEmptyTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendEmptyTransaction'
type RPCClient_SendEmptyTransaction_Call struct {
	*mock.Call
}

// SendEmptyTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - newTxAttempt func(types.Nonce , uint32 , *evmassets.Wei , common.Address)(interface{} , error)
//   - seq types.Nonce
//   - gasLimit uint32
//   - fee *evmassets.Wei
//   - fromAddress common.Address
func (_e *RPCClient_Expecter) SendEmptyTransaction(ctx interface{}, newTxAttempt interface{}, seq interface{}, gasLimit interface{}, fee interface{}, fromAddress interface{}) *RPCClient_SendEmptyTransaction_Call {
	return &RPCClient_SendEmptyTransaction_Call{Call: _e.mock.On("SendEmptyTransaction", ctx, newTxAttempt, seq, gasLimit, fee, fromAddress)}
}

func (_c *RPCClient_SendEmptyTransaction_Call) Run(run func(ctx context.Context, newTxAttempt func(types.Nonce, uint32, *evmassets.Wei, common.Address) (interface{}, error), seq types.Nonce, gasLimit uint32, fee *evmassets.Wei, fromAddress common.Address)) *RPCClient_SendEmptyTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(func(types.Nonce, uint32, *evmassets.Wei, common.Address) (interface{}, error)), args[2].(types.Nonce), args[3].(uint32), args[4].(*evmassets.Wei), args[5].(common.Address))
	})
	return _c
}

func (_c *RPCClient_SendEmptyTransaction_Call) Return(txhash string, err error) *RPCClient_SendEmptyTransaction_Call {
	_c.Call.Return(txhash, err)
	return _c
}

func (_c *RPCClient_SendEmptyTransaction_Call) RunAndReturn(run func(context.Context, func(types.Nonce, uint32, *evmassets.Wei, common.Address) (interface{}, error), types.Nonce, uint32, *evmassets.Wei, common.Address) (string, error)) *RPCClient_SendEmptyTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// SendTransaction provides a mock function with given fields: ctx, tx
func (_m *RPCClient) SendTransaction(ctx context.Context, tx *coretypes.Transaction) error {
	ret := _m.Called(ctx, tx)

	if len(ret) == 0 {
		panic("no return value specified for SendTransaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *coretypes.Transaction) error); ok {
		r0 = rf(ctx, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RPCClient_SendTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendTransaction'
type RPCClient_SendTransaction_Call struct {
	*mock.Call
}

// SendTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - tx *coretypes.Transaction
func (_e *RPCClient_Expecter) SendTransaction(ctx interface{}, tx interface{}) *RPCClient_SendTransaction_Call {
	return &RPCClient_SendTransaction_Call{Call: _e.mock.On("SendTransaction", ctx, tx)}
}

func (_c *RPCClient_SendTransaction_Call) Run(run func(ctx context.Context, tx *coretypes.Transaction)) *RPCClient_SendTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*coretypes.Transaction))
	})
	return _c
}

func (_c *RPCClient_SendTransaction_Call) Return(_a0 error) *RPCClient_SendTransaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RPCClient_SendTransaction_Call) RunAndReturn(run func(context.Context, *coretypes.Transaction) error) *RPCClient_SendTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// SequenceAt provides a mock function with given fields: ctx, accountAddress, blockNumber
func (_m *RPCClient) SequenceAt(ctx context.Context, accountAddress common.Address, blockNumber *big.Int) (types.Nonce, error) {
	ret := _m.Called(ctx, accountAddress, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for SequenceAt")
	}

	var r0 types.Nonce
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) (types.Nonce, error)); ok {
		return rf(ctx, accountAddress, blockNumber)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) types.Nonce); ok {
		r0 = rf(ctx, accountAddress, blockNumber)
	} else {
		r0 = ret.Get(0).(types.Nonce)
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, accountAddress, blockNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_SequenceAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SequenceAt'
type RPCClient_SequenceAt_Call struct {
	*mock.Call
}

// SequenceAt is a helper method to define mock.On call
//   - ctx context.Context
//   - accountAddress common.Address
//   - blockNumber *big.Int
func (_e *RPCClient_Expecter) SequenceAt(ctx interface{}, accountAddress interface{}, blockNumber interface{}) *RPCClient_SequenceAt_Call {
	return &RPCClient_SequenceAt_Call{Call: _e.mock.On("SequenceAt", ctx, accountAddress, blockNumber)}
}

func (_c *RPCClient_SequenceAt_Call) Run(run func(ctx context.Context, accountAddress common.Address, blockNumber *big.Int)) *RPCClient_SequenceAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *RPCClient_SequenceAt_Call) Return(_a0 types.Nonce, _a1 error) *RPCClient_SequenceAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_SequenceAt_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) (types.Nonce, error)) *RPCClient_SequenceAt_Call {
	_c.Call.Return(run)
	return _c
}

// SetAliveLoopSub provides a mock function with given fields: _a0
func (_m *RPCClient) SetAliveLoopSub(_a0 commontypes.Subscription) {
	_m.Called(_a0)
}

// RPCClient_SetAliveLoopSub_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetAliveLoopSub'
type RPCClient_SetAliveLoopSub_Call struct {
	*mock.Call
}

// SetAliveLoopSub is a helper method to define mock.On call
//   - _a0 commontypes.Subscription
func (_e *RPCClient_Expecter) SetAliveLoopSub(_a0 interface{}) *RPCClient_SetAliveLoopSub_Call {
	return &RPCClient_SetAliveLoopSub_Call{Call: _e.mock.On("SetAliveLoopSub", _a0)}
}

func (_c *RPCClient_SetAliveLoopSub_Call) Run(run func(_a0 commontypes.Subscription)) *RPCClient_SetAliveLoopSub_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(commontypes.Subscription))
	})
	return _c
}

func (_c *RPCClient_SetAliveLoopSub_Call) Return() *RPCClient_SetAliveLoopSub_Call {
	_c.Call.Return()
	return _c
}

func (_c *RPCClient_SetAliveLoopSub_Call) RunAndReturn(run func(commontypes.Subscription)) *RPCClient_SetAliveLoopSub_Call {
	_c.Call.Return(run)
	return _c
}

// SimulateTransaction provides a mock function with given fields: ctx, tx
func (_m *RPCClient) SimulateTransaction(ctx context.Context, tx *coretypes.Transaction) error {
	ret := _m.Called(ctx, tx)

	if len(ret) == 0 {
		panic("no return value specified for SimulateTransaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *coretypes.Transaction) error); ok {
		r0 = rf(ctx, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RPCClient_SimulateTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SimulateTransaction'
type RPCClient_SimulateTransaction_Call struct {
	*mock.Call
}

// SimulateTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - tx *coretypes.Transaction
func (_e *RPCClient_Expecter) SimulateTransaction(ctx interface{}, tx interface{}) *RPCClient_SimulateTransaction_Call {
	return &RPCClient_SimulateTransaction_Call{Call: _e.mock.On("SimulateTransaction", ctx, tx)}
}

func (_c *RPCClient_SimulateTransaction_Call) Run(run func(ctx context.Context, tx *coretypes.Transaction)) *RPCClient_SimulateTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*coretypes.Transaction))
	})
	return _c
}

func (_c *RPCClient_SimulateTransaction_Call) Return(_a0 error) *RPCClient_SimulateTransaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RPCClient_SimulateTransaction_Call) RunAndReturn(run func(context.Context, *coretypes.Transaction) error) *RPCClient_SimulateTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// SubscribeFilterLogs provides a mock function with given fields: ctx, q, ch
func (_m *RPCClient) SubscribeFilterLogs(ctx context.Context, q ethereum.FilterQuery, ch chan<- coretypes.Log) (ethereum.Subscription, error) {
	ret := _m.Called(ctx, q, ch)

	if len(ret) == 0 {
		panic("no return value specified for SubscribeFilterLogs")
	}

	var r0 ethereum.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.FilterQuery, chan<- coretypes.Log) (ethereum.Subscription, error)); ok {
		return rf(ctx, q, ch)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.FilterQuery, chan<- coretypes.Log) ethereum.Subscription); ok {
		r0 = rf(ctx, q, ch)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ethereum.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ethereum.FilterQuery, chan<- coretypes.Log) error); ok {
		r1 = rf(ctx, q, ch)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_SubscribeFilterLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubscribeFilterLogs'
type RPCClient_SubscribeFilterLogs_Call struct {
	*mock.Call
}

// SubscribeFilterLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - q ethereum.FilterQuery
//   - ch chan<- coretypes.Log
func (_e *RPCClient_Expecter) SubscribeFilterLogs(ctx interface{}, q interface{}, ch interface{}) *RPCClient_SubscribeFilterLogs_Call {
	return &RPCClient_SubscribeFilterLogs_Call{Call: _e.mock.On("SubscribeFilterLogs", ctx, q, ch)}
}

func (_c *RPCClient_SubscribeFilterLogs_Call) Run(run func(ctx context.Context, q ethereum.FilterQuery, ch chan<- coretypes.Log)) *RPCClient_SubscribeFilterLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ethereum.FilterQuery), args[2].(chan<- coretypes.Log))
	})
	return _c
}

func (_c *RPCClient_SubscribeFilterLogs_Call) Return(s ethereum.Subscription, err error) *RPCClient_SubscribeFilterLogs_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *RPCClient_SubscribeFilterLogs_Call) RunAndReturn(run func(context.Context, ethereum.FilterQuery, chan<- coretypes.Log) (ethereum.Subscription, error)) *RPCClient_SubscribeFilterLogs_Call {
	_c.Call.Return(run)
	return _c
}

// SubscribeNewHead provides a mock function with given fields: ctx, channel
func (_m *RPCClient) SubscribeNewHead(ctx context.Context, channel chan<- *types.Head) (commontypes.Subscription, error) {
	ret := _m.Called(ctx, channel)

	if len(ret) == 0 {
		panic("no return value specified for SubscribeNewHead")
	}

	var r0 commontypes.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, chan<- *types.Head) (commontypes.Subscription, error)); ok {
		return rf(ctx, channel)
	}
	if rf, ok := ret.Get(0).(func(context.Context, chan<- *types.Head) commontypes.Subscription); ok {
		r0 = rf(ctx, channel)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(commontypes.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, chan<- *types.Head) error); ok {
		r1 = rf(ctx, channel)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_SubscribeNewHead_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubscribeNewHead'
type RPCClient_SubscribeNewHead_Call struct {
	*mock.Call
}

// SubscribeNewHead is a helper method to define mock.On call
//   - ctx context.Context
//   - channel chan<- *types.Head
func (_e *RPCClient_Expecter) SubscribeNewHead(ctx interface{}, channel interface{}) *RPCClient_SubscribeNewHead_Call {
	return &RPCClient_SubscribeNewHead_Call{Call: _e.mock.On("SubscribeNewHead", ctx, channel)}
}

func (_c *RPCClient_SubscribeNewHead_Call) Run(run func(ctx context.Context, channel chan<- *types.Head)) *RPCClient_SubscribeNewHead_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(chan<- *types.Head))
	})
	return _c
}

func (_c *RPCClient_SubscribeNewHead_Call) Return(s commontypes.Subscription, err error) *RPCClient_SubscribeNewHead_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *RPCClient_SubscribeNewHead_Call) RunAndReturn(run func(context.Context, chan<- *types.Head) (commontypes.Subscription, error)) *RPCClient_SubscribeNewHead_Call {
	_c.Call.Return(run)
	return _c
}

// SubscribeToFinalizedHeads provides a mock function with given fields: _a0
func (_m *RPCClient) SubscribeToFinalizedHeads(_a0 context.Context) (<-chan *types.Head, commontypes.Subscription, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for SubscribeToFinalizedHeads")
	}

	var r0 <-chan *types.Head
	var r1 commontypes.Subscription
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context) (<-chan *types.Head, commontypes.Subscription, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(context.Context) <-chan *types.Head); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan *types.Head)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) commontypes.Subscription); ok {
		r1 = rf(_a0)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(commontypes.Subscription)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context) error); ok {
		r2 = rf(_a0)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// RPCClient_SubscribeToFinalizedHeads_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubscribeToFinalizedHeads'
type RPCClient_SubscribeToFinalizedHeads_Call struct {
	*mock.Call
}

// SubscribeToFinalizedHeads is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *RPCClient_Expecter) SubscribeToFinalizedHeads(_a0 interface{}) *RPCClient_SubscribeToFinalizedHeads_Call {
	return &RPCClient_SubscribeToFinalizedHeads_Call{Call: _e.mock.On("SubscribeToFinalizedHeads", _a0)}
}

func (_c *RPCClient_SubscribeToFinalizedHeads_Call) Run(run func(_a0 context.Context)) *RPCClient_SubscribeToFinalizedHeads_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_SubscribeToFinalizedHeads_Call) Return(_a0 <-chan *types.Head, _a1 commontypes.Subscription, _a2 error) *RPCClient_SubscribeToFinalizedHeads_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *RPCClient_SubscribeToFinalizedHeads_Call) RunAndReturn(run func(context.Context) (<-chan *types.Head, commontypes.Subscription, error)) *RPCClient_SubscribeToFinalizedHeads_Call {
	_c.Call.Return(run)
	return _c
}

// SubscribeToHeads provides a mock function with given fields: ctx
func (_m *RPCClient) SubscribeToHeads(ctx context.Context) (<-chan *types.Head, commontypes.Subscription, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for SubscribeToHeads")
	}

	var r0 <-chan *types.Head
	var r1 commontypes.Subscription
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context) (<-chan *types.Head, commontypes.Subscription, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) <-chan *types.Head); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan *types.Head)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) commontypes.Subscription); ok {
		r1 = rf(ctx)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(commontypes.Subscription)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context) error); ok {
		r2 = rf(ctx)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// RPCClient_SubscribeToHeads_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubscribeToHeads'
type RPCClient_SubscribeToHeads_Call struct {
	*mock.Call
}

// SubscribeToHeads is a helper method to define mock.On call
//   - ctx context.Context
func (_e *RPCClient_Expecter) SubscribeToHeads(ctx interface{}) *RPCClient_SubscribeToHeads_Call {
	return &RPCClient_SubscribeToHeads_Call{Call: _e.mock.On("SubscribeToHeads", ctx)}
}

func (_c *RPCClient_SubscribeToHeads_Call) Run(run func(ctx context.Context)) *RPCClient_SubscribeToHeads_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_SubscribeToHeads_Call) Return(ch <-chan *types.Head, sub commontypes.Subscription, err error) *RPCClient_SubscribeToHeads_Call {
	_c.Call.Return(ch, sub, err)
	return _c
}

func (_c *RPCClient_SubscribeToHeads_Call) RunAndReturn(run func(context.Context) (<-chan *types.Head, commontypes.Subscription, error)) *RPCClient_SubscribeToHeads_Call {
	_c.Call.Return(run)
	return _c
}

// SubscribersCount provides a mock function with given fields:
func (_m *RPCClient) SubscribersCount() int32 {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for SubscribersCount")
	}

	var r0 int32
	if rf, ok := ret.Get(0).(func() int32); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(int32)
	}

	return r0
}

// RPCClient_SubscribersCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubscribersCount'
type RPCClient_SubscribersCount_Call struct {
	*mock.Call
}

// SubscribersCount is a helper method to define mock.On call
func (_e *RPCClient_Expecter) SubscribersCount() *RPCClient_SubscribersCount_Call {
	return &RPCClient_SubscribersCount_Call{Call: _e.mock.On("SubscribersCount")}
}

func (_c *RPCClient_SubscribersCount_Call) Run(run func()) *RPCClient_SubscribersCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RPCClient_SubscribersCount_Call) Return(_a0 int32) *RPCClient_SubscribersCount_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *RPCClient_SubscribersCount_Call) RunAndReturn(run func() int32) *RPCClient_SubscribersCount_Call {
	_c.Call.Return(run)
	return _c
}

// SuggestGasPrice provides a mock function with given fields: ctx
func (_m *RPCClient) SuggestGasPrice(ctx context.Context) (*big.Int, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for SuggestGasPrice")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*big.Int, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *big.Int); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_SuggestGasPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SuggestGasPrice'
type RPCClient_SuggestGasPrice_Call struct {
	*mock.Call
}

// SuggestGasPrice is a helper method to define mock.On call
//   - ctx context.Context
func (_e *RPCClient_Expecter) SuggestGasPrice(ctx interface{}) *RPCClient_SuggestGasPrice_Call {
	return &RPCClient_SuggestGasPrice_Call{Call: _e.mock.On("SuggestGasPrice", ctx)}
}

func (_c *RPCClient_SuggestGasPrice_Call) Run(run func(ctx context.Context)) *RPCClient_SuggestGasPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_SuggestGasPrice_Call) Return(p *big.Int, err error) *RPCClient_SuggestGasPrice_Call {
	_c.Call.Return(p, err)
	return _c
}

func (_c *RPCClient_SuggestGasPrice_Call) RunAndReturn(run func(context.Context) (*big.Int, error)) *RPCClient_SuggestGasPrice_Call {
	_c.Call.Return(run)
	return _c
}

// SuggestGasTipCap provides a mock function with given fields: ctx
func (_m *RPCClient) SuggestGasTipCap(ctx context.Context) (*big.Int, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for SuggestGasTipCap")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*big.Int, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *big.Int); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_SuggestGasTipCap_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SuggestGasTipCap'
type RPCClient_SuggestGasTipCap_Call struct {
	*mock.Call
}

// SuggestGasTipCap is a helper method to define mock.On call
//   - ctx context.Context
func (_e *RPCClient_Expecter) SuggestGasTipCap(ctx interface{}) *RPCClient_SuggestGasTipCap_Call {
	return &RPCClient_SuggestGasTipCap_Call{Call: _e.mock.On("SuggestGasTipCap", ctx)}
}

func (_c *RPCClient_SuggestGasTipCap_Call) Run(run func(ctx context.Context)) *RPCClient_SuggestGasTipCap_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *RPCClient_SuggestGasTipCap_Call) Return(t *big.Int, err error) *RPCClient_SuggestGasTipCap_Call {
	_c.Call.Return(t, err)
	return _c
}

func (_c *RPCClient_SuggestGasTipCap_Call) RunAndReturn(run func(context.Context) (*big.Int, error)) *RPCClient_SuggestGasTipCap_Call {
	_c.Call.Return(run)
	return _c
}

// TokenBalance provides a mock function with given fields: ctx, accountAddress, tokenAddress
func (_m *RPCClient) TokenBalance(ctx context.Context, accountAddress common.Address, tokenAddress common.Address) (*big.Int, error) {
	ret := _m.Called(ctx, accountAddress, tokenAddress)

	if len(ret) == 0 {
		panic("no return value specified for TokenBalance")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, common.Address) (*big.Int, error)); ok {
		return rf(ctx, accountAddress, tokenAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, common.Address) *big.Int); ok {
		r0 = rf(ctx, accountAddress, tokenAddress)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, common.Address) error); ok {
		r1 = rf(ctx, accountAddress, tokenAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_TokenBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TokenBalance'
type RPCClient_TokenBalance_Call struct {
	*mock.Call
}

// TokenBalance is a helper method to define mock.On call
//   - ctx context.Context
//   - accountAddress common.Address
//   - tokenAddress common.Address
func (_e *RPCClient_Expecter) TokenBalance(ctx interface{}, accountAddress interface{}, tokenAddress interface{}) *RPCClient_TokenBalance_Call {
	return &RPCClient_TokenBalance_Call{Call: _e.mock.On("TokenBalance", ctx, accountAddress, tokenAddress)}
}

func (_c *RPCClient_TokenBalance_Call) Run(run func(ctx context.Context, accountAddress common.Address, tokenAddress common.Address)) *RPCClient_TokenBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(common.Address))
	})
	return _c
}

func (_c *RPCClient_TokenBalance_Call) Return(_a0 *big.Int, _a1 error) *RPCClient_TokenBalance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_TokenBalance_Call) RunAndReturn(run func(context.Context, common.Address, common.Address) (*big.Int, error)) *RPCClient_TokenBalance_Call {
	_c.Call.Return(run)
	return _c
}

// TransactionByHash provides a mock function with given fields: ctx, txHash
func (_m *RPCClient) TransactionByHash(ctx context.Context, txHash common.Hash) (*coretypes.Transaction, error) {
	ret := _m.Called(ctx, txHash)

	if len(ret) == 0 {
		panic("no return value specified for TransactionByHash")
	}

	var r0 *coretypes.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*coretypes.Transaction, error)); ok {
		return rf(ctx, txHash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *coretypes.Transaction); ok {
		r0 = rf(ctx, txHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*coretypes.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, txHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_TransactionByHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransactionByHash'
type RPCClient_TransactionByHash_Call struct {
	*mock.Call
}

// TransactionByHash is a helper method to define mock.On call
//   - ctx context.Context
//   - txHash common.Hash
func (_e *RPCClient_Expecter) TransactionByHash(ctx interface{}, txHash interface{}) *RPCClient_TransactionByHash_Call {
	return &RPCClient_TransactionByHash_Call{Call: _e.mock.On("TransactionByHash", ctx, txHash)}
}

func (_c *RPCClient_TransactionByHash_Call) Run(run func(ctx context.Context, txHash common.Hash)) *RPCClient_TransactionByHash_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *RPCClient_TransactionByHash_Call) Return(_a0 *coretypes.Transaction, _a1 error) *RPCClient_TransactionByHash_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_TransactionByHash_Call) RunAndReturn(run func(context.Context, common.Hash) (*coretypes.Transaction, error)) *RPCClient_TransactionByHash_Call {
	_c.Call.Return(run)
	return _c
}

// TransactionReceipt provides a mock function with given fields: ctx, txHash
func (_m *RPCClient) TransactionReceipt(ctx context.Context, txHash common.Hash) (*types.Receipt, error) {
	ret := _m.Called(ctx, txHash)

	if len(ret) == 0 {
		panic("no return value specified for TransactionReceipt")
	}

	var r0 *types.Receipt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*types.Receipt, error)); ok {
		return rf(ctx, txHash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *types.Receipt); ok {
		r0 = rf(ctx, txHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Receipt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, txHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_TransactionReceipt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransactionReceipt'
type RPCClient_TransactionReceipt_Call struct {
	*mock.Call
}

// TransactionReceipt is a helper method to define mock.On call
//   - ctx context.Context
//   - txHash common.Hash
func (_e *RPCClient_Expecter) TransactionReceipt(ctx interface{}, txHash interface{}) *RPCClient_TransactionReceipt_Call {
	return &RPCClient_TransactionReceipt_Call{Call: _e.mock.On("TransactionReceipt", ctx, txHash)}
}

func (_c *RPCClient_TransactionReceipt_Call) Run(run func(ctx context.Context, txHash common.Hash)) *RPCClient_TransactionReceipt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *RPCClient_TransactionReceipt_Call) Return(_a0 *types.Receipt, _a1 error) *RPCClient_TransactionReceipt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *RPCClient_TransactionReceipt_Call) RunAndReturn(run func(context.Context, common.Hash) (*types.Receipt, error)) *RPCClient_TransactionReceipt_Call {
	_c.Call.Return(run)
	return _c
}

// TransactionReceiptGeth provides a mock function with given fields: ctx, txHash
func (_m *RPCClient) TransactionReceiptGeth(ctx context.Context, txHash common.Hash) (*coretypes.Receipt, error) {
	ret := _m.Called(ctx, txHash)

	if len(ret) == 0 {
		panic("no return value specified for TransactionReceiptGeth")
	}

	var r0 *coretypes.Receipt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*coretypes.Receipt, error)); ok {
		return rf(ctx, txHash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *coretypes.Receipt); ok {
		r0 = rf(ctx, txHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*coretypes.Receipt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, txHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// RPCClient_TransactionReceiptGeth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransactionReceiptGeth'
type RPCClient_TransactionReceiptGeth_Call struct {
	*mock.Call
}

// TransactionReceiptGeth is a helper method to define mock.On call
//   - ctx context.Context
//   - txHash common.Hash
func (_e *RPCClient_Expecter) TransactionReceiptGeth(ctx interface{}, txHash interface{}) *RPCClient_TransactionReceiptGeth_Call {
	return &RPCClient_TransactionReceiptGeth_Call{Call: _e.mock.On("TransactionReceiptGeth", ctx, txHash)}
}

func (_c *RPCClient_TransactionReceiptGeth_Call) Run(run func(ctx context.Context, txHash common.Hash)) *RPCClient_TransactionReceiptGeth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *RPCClient_TransactionReceiptGeth_Call) Return(r *coretypes.Receipt, err error) *RPCClient_TransactionReceiptGeth_Call {
	_c.Call.Return(r, err)
	return _c
}

func (_c *RPCClient_TransactionReceiptGeth_Call) RunAndReturn(run func(context.Context, common.Hash) (*coretypes.Receipt, error)) *RPCClient_TransactionReceiptGeth_Call {
	_c.Call.Return(run)
	return _c
}

// UnsubscribeAllExceptAliveLoop provides a mock function with given fields:
func (_m *RPCClient) UnsubscribeAllExceptAliveLoop() {
	_m.Called()
}

// RPCClient_UnsubscribeAllExceptAliveLoop_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UnsubscribeAllExceptAliveLoop'
type RPCClient_UnsubscribeAllExceptAliveLoop_Call struct {
	*mock.Call
}

// UnsubscribeAllExceptAliveLoop is a helper method to define mock.On call
func (_e *RPCClient_Expecter) UnsubscribeAllExceptAliveLoop() *RPCClient_UnsubscribeAllExceptAliveLoop_Call {
	return &RPCClient_UnsubscribeAllExceptAliveLoop_Call{Call: _e.mock.On("UnsubscribeAllExceptAliveLoop")}
}

func (_c *RPCClient_UnsubscribeAllExceptAliveLoop_Call) Run(run func()) *RPCClient_UnsubscribeAllExceptAliveLoop_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *RPCClient_UnsubscribeAllExceptAliveLoop_Call) Return() *RPCClient_UnsubscribeAllExceptAliveLoop_Call {
	_c.Call.Return()
	return _c
}

func (_c *RPCClient_UnsubscribeAllExceptAliveLoop_Call) RunAndReturn(run func()) *RPCClient_UnsubscribeAllExceptAliveLoop_Call {
	_c.Call.Return(run)
	return _c
}

// NewRPCClient creates a new instance of RPCClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRPCClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *RPCClient {
	mock := &RPCClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
