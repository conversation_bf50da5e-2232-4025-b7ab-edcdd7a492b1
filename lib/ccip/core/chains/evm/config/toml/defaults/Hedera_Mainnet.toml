ChainID = '295'
ChainType = 'hedera'
# Considering the 3-5 (6 including a buffer) seconds of finality and 2 seconds block production
# We set the depth to 6/2 = 3 blocks, setting to 10 for safety
FinalityDepth = 10
# Hedera has high TPS, so polling less often 
LogPollInterval = '10s'
MinIncomingConfirmations = 1

[BalanceMonitor]
Enabled = true

[GasEstimator]
Mode = 'SuggestedPrice'
# Since Hedera dont have mempool and there's no way for a node to front run or a user to bribe a node to submit the transaction earlier than it's consensus timestamp,
# But they have automated congesting pricing throttling which would mean at high sustained level the gasPrice itself could be increased to prevent malicious behaviour.
# Disabling the Bumpthreshold as TXM now implicity handles the bumping after checking on-chain nonce & re-broadcast for Hedera chain type
BumpThreshold = 0
BumpMin = '10 gwei'
BumpPercent = 20

[Transactions]
# To hit throttling you'd need to maintain 15 m gas /sec over a prolonged period of time.
# Because Hedera's block times are every 2 secs it's less less likely to happen as compared to other chains
# Setting this to little higher even though <PERSON><PERSON><PERSON> has High TPS, We have seen 10-12s to get the trasaction mined & 20-25s incase of failures
# Accounting for Node syncs & avoid re-sending txns before fetching the receipt, setting to 2m
ResendAfterThreshold = '2m'


[NodePool]
SyncThreshold = 10