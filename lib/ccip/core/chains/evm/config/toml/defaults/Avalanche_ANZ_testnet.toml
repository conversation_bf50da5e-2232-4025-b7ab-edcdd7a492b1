ChainID = '76578'
FinalityDepth = 1
FinalityTagEnabled = false
LinkContractAddress = '******************************************'
LogPollInterval = '3s'
MinIncomingConfirmations = 1
# Avax subnet only emits blocks when a new tx is received, so this method of liveness detection is not useful.
NoNewHeadsThreshold = '0'
OCR.ContractConfirmations = 1
RPCBlockQueryDelay = 2

[GasEstimator]
Mode = 'BlockHistory'
PriceDefault = '25 gwei'
PriceMax = '115792089237316195423570985008687907853269984665.640564039457584007913129639935 tether'
PriceMin = '25 gwei'

[GasEstimator.BlockHistory]
BlockHistorySize = 24
