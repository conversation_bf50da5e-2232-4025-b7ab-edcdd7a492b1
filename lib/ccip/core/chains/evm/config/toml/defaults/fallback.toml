AutoCreateKey = true
BlockBackfillDepth = 10
BlockBackfillSkip = false
FinalityDepth = 50
FinalityTagEnabled = false
LogBackfillBatchSize = 1000
LogPollInterval = '15s'
LogKeepBlocksDepth = 100000
# CCIP uses paging when removing logs to avoid pushing too much pressure on the database
LogPrunePageSize = 10000
BackupLogPollerBlockDelay = 100
MinContractPayment = '.00001 link'
MinIncomingConfirmations = 3
NonceAutoSync = true
NoNewHeadsThreshold = '3m'
RPCDefaultBatchSize = 250
RPCBlockQueryDelay = 1
FinalizedBlockOffset = 0
NoNewFinalizedHeadsThreshold = '0'
LogBroadcasterEnabled = true

[Transactions]
ForwardersEnabled = false
MaxInFlight = 16
MaxQueued = 250
ReaperInterval = '1h'
ReaperThreshold = '168h'
ResendAfterThreshold = '1m'

[Transactions.AutoPurge]
Enabled = false

[BalanceMonitor]
Enabled = true

[GasEstimator]
Mode = 'BlockHistory'
PriceDefault = '20 gwei'
PriceMax = '115792089237316195423570985008687907853269984665.640564039457584007913129639935 tether'
PriceMin = '1 gwei'
LimitDefault = 8_000_000
LimitMax = 8_000_000
LimitMultiplier = '1'
LimitTransfer = 21_000
BumpMin = '5 gwei'
BumpPercent = 20
BumpThreshold = 3
EIP1559DynamicFees = false
FeeCapDefault = '100 gwei'
TipCapDefault = '1'
TipCapMin = '1'
EstimateLimit = false

[GasEstimator.BlockHistory]
BatchSize = 25
BlockHistorySize = 8
CheckInclusionBlocks = 12
CheckInclusionPercentile = 90
TransactionPercentile = 60

[GasEstimator.FeeHistory]
CacheTimeout = '10s'

[HeadTracker]
HistoryDepth = 100
MaxBufferSize = 3
SamplingInterval = '1s'
FinalityTagBypass = true
MaxAllowedFinalityDepth = 10000

[NodePool]
PollFailureThreshold = 5
PollInterval = '10s'
SelectionMode = 'HighestHead'
SyncThreshold = 5
LeaseDuration = '0s'
NodeIsSyncingEnabled = false
FinalizedBlockPollInterval = '5s'
EnforceRepeatableRead = false
DeathDeclarationDelay = '10s'
NewHeadsPollInterval = '0s'

[OCR]
ContractConfirmations = 4
ContractTransmitterTransmitTimeout = '10s'
DatabaseTimeout = '10s'
DeltaCOverride = '168h'
DeltaCJitterOverride = '1h'
ObservationGracePeriod = '1s'

[OCR2.Automation]
GasLimit = 5400000

[Workflow]
GasLimitDefault = 400_000
