ChainID = '2358'
ChainType = 'kroma' # Kroma is based on the Optimism Bedrock architechture
# FT and FD are both present here because the dev effort rely only on FinalityTagEnabled are still in progress.
# We expect to be able to rely only on FinalityTagEnabled=true in the short future.
# https://chainlink-core.slack.com/archives/C05CS33N08N/p1715102940763339?thread_ts=1715102478.537529&cid=C05CS33N08N
FinalityDepth = 700
FinalityTagEnabled = true
LogPollInterval = '2s'
NoNewHeadsThreshold = '40s'
MinIncomingConfirmations = 1

[GasEstimator]
EIP1559DynamicFees = true
PriceMin = '1 wei'
BumpMin = '100 wei'

[GasEstimator.BlockHistory]
BlockHistorySize = 24

[Transactions]
ResendAfterThreshold = '30s'

[HeadTracker]
HistoryDepth = 400

[NodePool]
SyncThreshold = 10

[OCR]
ContractConfirmations = 1
