// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"
	big "math/big"

	common "github.com/ethereum/go-ethereum/common"

	evmtypes "github.com/smartcontractkit/chainlink/v2/core/chains/evm/types"

	gas "github.com/smartcontractkit/chainlink/v2/core/chains/evm/gas"

	mock "github.com/stretchr/testify/mock"

	null "gopkg.in/guregu/null.v4"

	time "time"

	txmgr "github.com/smartcontractkit/chainlink/v2/core/chains/evm/txmgr"

	types "github.com/smartcontractkit/chainlink/v2/common/txmgr/types"

	uuid "github.com/google/uuid"
)

// EvmTxStore is an autogenerated mock type for the EvmTxStore type
type EvmTxStore struct {
	mock.Mock
}

type EvmTxStore_Expecter struct {
	mock *mock.Mock
}

func (_m *EvmTxStore) EXPECT() *EvmTxStore_Expecter {
	return &EvmTxStore_Expecter{mock: &_m.Mock}
}

// Abandon provides a mock function with given fields: ctx, id, addr
func (_m *EvmTxStore) Abandon(ctx context.Context, id *big.Int, addr common.Address) error {
	ret := _m.Called(ctx, id, addr)

	if len(ret) == 0 {
		panic("no return value specified for Abandon")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int, common.Address) error); ok {
		r0 = rf(ctx, id, addr)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_Abandon_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Abandon'
type EvmTxStore_Abandon_Call struct {
	*mock.Call
}

// Abandon is a helper method to define mock.On call
//   - ctx context.Context
//   - id *big.Int
//   - addr common.Address
func (_e *EvmTxStore_Expecter) Abandon(ctx interface{}, id interface{}, addr interface{}) *EvmTxStore_Abandon_Call {
	return &EvmTxStore_Abandon_Call{Call: _e.mock.On("Abandon", ctx, id, addr)}
}

func (_c *EvmTxStore_Abandon_Call) Run(run func(ctx context.Context, id *big.Int, addr common.Address)) *EvmTxStore_Abandon_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int), args[2].(common.Address))
	})
	return _c
}

func (_c *EvmTxStore_Abandon_Call) Return(_a0 error) *EvmTxStore_Abandon_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_Abandon_Call) RunAndReturn(run func(context.Context, *big.Int, common.Address) error) *EvmTxStore_Abandon_Call {
	_c.Call.Return(run)
	return _c
}

// CheckTxQueueCapacity provides a mock function with given fields: ctx, fromAddress, maxQueuedTransactions, chainID
func (_m *EvmTxStore) CheckTxQueueCapacity(ctx context.Context, fromAddress common.Address, maxQueuedTransactions uint64, chainID *big.Int) error {
	ret := _m.Called(ctx, fromAddress, maxQueuedTransactions, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CheckTxQueueCapacity")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, uint64, *big.Int) error); ok {
		r0 = rf(ctx, fromAddress, maxQueuedTransactions, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_CheckTxQueueCapacity_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckTxQueueCapacity'
type EvmTxStore_CheckTxQueueCapacity_Call struct {
	*mock.Call
}

// CheckTxQueueCapacity is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress common.Address
//   - maxQueuedTransactions uint64
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) CheckTxQueueCapacity(ctx interface{}, fromAddress interface{}, maxQueuedTransactions interface{}, chainID interface{}) *EvmTxStore_CheckTxQueueCapacity_Call {
	return &EvmTxStore_CheckTxQueueCapacity_Call{Call: _e.mock.On("CheckTxQueueCapacity", ctx, fromAddress, maxQueuedTransactions, chainID)}
}

func (_c *EvmTxStore_CheckTxQueueCapacity_Call) Run(run func(ctx context.Context, fromAddress common.Address, maxQueuedTransactions uint64, chainID *big.Int)) *EvmTxStore_CheckTxQueueCapacity_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(uint64), args[3].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_CheckTxQueueCapacity_Call) Return(err error) *EvmTxStore_CheckTxQueueCapacity_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *EvmTxStore_CheckTxQueueCapacity_Call) RunAndReturn(run func(context.Context, common.Address, uint64, *big.Int) error) *EvmTxStore_CheckTxQueueCapacity_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *EvmTxStore) Close() {
	_m.Called()
}

// EvmTxStore_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type EvmTxStore_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *EvmTxStore_Expecter) Close() *EvmTxStore_Close_Call {
	return &EvmTxStore_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *EvmTxStore_Close_Call) Run(run func()) *EvmTxStore_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *EvmTxStore_Close_Call) Return() *EvmTxStore_Close_Call {
	_c.Call.Return()
	return _c
}

func (_c *EvmTxStore_Close_Call) RunAndReturn(run func()) *EvmTxStore_Close_Call {
	_c.Call.Return(run)
	return _c
}

// CountTransactionsByState provides a mock function with given fields: ctx, state, chainID
func (_m *EvmTxStore) CountTransactionsByState(ctx context.Context, state types.TxState, chainID *big.Int) (uint32, error) {
	ret := _m.Called(ctx, state, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CountTransactionsByState")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.TxState, *big.Int) (uint32, error)); ok {
		return rf(ctx, state, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.TxState, *big.Int) uint32); ok {
		r0 = rf(ctx, state, chainID)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.TxState, *big.Int) error); ok {
		r1 = rf(ctx, state, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_CountTransactionsByState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountTransactionsByState'
type EvmTxStore_CountTransactionsByState_Call struct {
	*mock.Call
}

// CountTransactionsByState is a helper method to define mock.On call
//   - ctx context.Context
//   - state types.TxState
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) CountTransactionsByState(ctx interface{}, state interface{}, chainID interface{}) *EvmTxStore_CountTransactionsByState_Call {
	return &EvmTxStore_CountTransactionsByState_Call{Call: _e.mock.On("CountTransactionsByState", ctx, state, chainID)}
}

func (_c *EvmTxStore_CountTransactionsByState_Call) Run(run func(ctx context.Context, state types.TxState, chainID *big.Int)) *EvmTxStore_CountTransactionsByState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.TxState), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_CountTransactionsByState_Call) Return(count uint32, err error) *EvmTxStore_CountTransactionsByState_Call {
	_c.Call.Return(count, err)
	return _c
}

func (_c *EvmTxStore_CountTransactionsByState_Call) RunAndReturn(run func(context.Context, types.TxState, *big.Int) (uint32, error)) *EvmTxStore_CountTransactionsByState_Call {
	_c.Call.Return(run)
	return _c
}

// CountUnconfirmedTransactions provides a mock function with given fields: ctx, fromAddress, chainID
func (_m *EvmTxStore) CountUnconfirmedTransactions(ctx context.Context, fromAddress common.Address, chainID *big.Int) (uint32, error) {
	ret := _m.Called(ctx, fromAddress, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CountUnconfirmedTransactions")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) (uint32, error)); ok {
		return rf(ctx, fromAddress, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) uint32); ok {
		r0 = rf(ctx, fromAddress, chainID)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, fromAddress, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_CountUnconfirmedTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountUnconfirmedTransactions'
type EvmTxStore_CountUnconfirmedTransactions_Call struct {
	*mock.Call
}

// CountUnconfirmedTransactions is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress common.Address
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) CountUnconfirmedTransactions(ctx interface{}, fromAddress interface{}, chainID interface{}) *EvmTxStore_CountUnconfirmedTransactions_Call {
	return &EvmTxStore_CountUnconfirmedTransactions_Call{Call: _e.mock.On("CountUnconfirmedTransactions", ctx, fromAddress, chainID)}
}

func (_c *EvmTxStore_CountUnconfirmedTransactions_Call) Run(run func(ctx context.Context, fromAddress common.Address, chainID *big.Int)) *EvmTxStore_CountUnconfirmedTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_CountUnconfirmedTransactions_Call) Return(count uint32, err error) *EvmTxStore_CountUnconfirmedTransactions_Call {
	_c.Call.Return(count, err)
	return _c
}

func (_c *EvmTxStore_CountUnconfirmedTransactions_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) (uint32, error)) *EvmTxStore_CountUnconfirmedTransactions_Call {
	_c.Call.Return(run)
	return _c
}

// CountUnstartedTransactions provides a mock function with given fields: ctx, fromAddress, chainID
func (_m *EvmTxStore) CountUnstartedTransactions(ctx context.Context, fromAddress common.Address, chainID *big.Int) (uint32, error) {
	ret := _m.Called(ctx, fromAddress, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CountUnstartedTransactions")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) (uint32, error)); ok {
		return rf(ctx, fromAddress, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) uint32); ok {
		r0 = rf(ctx, fromAddress, chainID)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, fromAddress, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_CountUnstartedTransactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountUnstartedTransactions'
type EvmTxStore_CountUnstartedTransactions_Call struct {
	*mock.Call
}

// CountUnstartedTransactions is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress common.Address
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) CountUnstartedTransactions(ctx interface{}, fromAddress interface{}, chainID interface{}) *EvmTxStore_CountUnstartedTransactions_Call {
	return &EvmTxStore_CountUnstartedTransactions_Call{Call: _e.mock.On("CountUnstartedTransactions", ctx, fromAddress, chainID)}
}

func (_c *EvmTxStore_CountUnstartedTransactions_Call) Run(run func(ctx context.Context, fromAddress common.Address, chainID *big.Int)) *EvmTxStore_CountUnstartedTransactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_CountUnstartedTransactions_Call) Return(count uint32, err error) *EvmTxStore_CountUnstartedTransactions_Call {
	_c.Call.Return(count, err)
	return _c
}

func (_c *EvmTxStore_CountUnstartedTransactions_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) (uint32, error)) *EvmTxStore_CountUnstartedTransactions_Call {
	_c.Call.Return(run)
	return _c
}

// CreateTransaction provides a mock function with given fields: ctx, txRequest, chainID
func (_m *EvmTxStore) CreateTransaction(ctx context.Context, txRequest types.TxRequest[common.Address, common.Hash], chainID *big.Int) (types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, txRequest, chainID)

	if len(ret) == 0 {
		panic("no return value specified for CreateTransaction")
	}

	var r0 types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.TxRequest[common.Address, common.Hash], *big.Int) (types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, txRequest, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.TxRequest[common.Address, common.Hash], *big.Int) types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, txRequest, chainID)
	} else {
		r0 = ret.Get(0).(types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.TxRequest[common.Address, common.Hash], *big.Int) error); ok {
		r1 = rf(ctx, txRequest, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_CreateTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTransaction'
type EvmTxStore_CreateTransaction_Call struct {
	*mock.Call
}

// CreateTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - txRequest types.TxRequest[common.Address,common.Hash]
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) CreateTransaction(ctx interface{}, txRequest interface{}, chainID interface{}) *EvmTxStore_CreateTransaction_Call {
	return &EvmTxStore_CreateTransaction_Call{Call: _e.mock.On("CreateTransaction", ctx, txRequest, chainID)}
}

func (_c *EvmTxStore_CreateTransaction_Call) Run(run func(ctx context.Context, txRequest types.TxRequest[common.Address, common.Hash], chainID *big.Int)) *EvmTxStore_CreateTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.TxRequest[common.Address, common.Hash]), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_CreateTransaction_Call) Return(tx types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_CreateTransaction_Call {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *EvmTxStore_CreateTransaction_Call) RunAndReturn(run func(context.Context, types.TxRequest[common.Address, common.Hash], *big.Int) (types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_CreateTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteInProgressAttempt provides a mock function with given fields: ctx, attempt
func (_m *EvmTxStore) DeleteInProgressAttempt(ctx context.Context, attempt types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error {
	ret := _m.Called(ctx, attempt)

	if len(ret) == 0 {
		panic("no return value specified for DeleteInProgressAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error); ok {
		r0 = rf(ctx, attempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_DeleteInProgressAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteInProgressAttempt'
type EvmTxStore_DeleteInProgressAttempt_Call struct {
	*mock.Call
}

// DeleteInProgressAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - attempt types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
func (_e *EvmTxStore_Expecter) DeleteInProgressAttempt(ctx interface{}, attempt interface{}) *EvmTxStore_DeleteInProgressAttempt_Call {
	return &EvmTxStore_DeleteInProgressAttempt_Call{Call: _e.mock.On("DeleteInProgressAttempt", ctx, attempt)}
}

func (_c *EvmTxStore_DeleteInProgressAttempt_Call) Run(run func(ctx context.Context, attempt types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])) *EvmTxStore_DeleteInProgressAttempt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]))
	})
	return _c
}

func (_c *EvmTxStore_DeleteInProgressAttempt_Call) Return(_a0 error) *EvmTxStore_DeleteInProgressAttempt_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_DeleteInProgressAttempt_Call) RunAndReturn(run func(context.Context, types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error) *EvmTxStore_DeleteInProgressAttempt_Call {
	_c.Call.Return(run)
	return _c
}

// FindConfirmedTxesReceipts provides a mock function with given fields: ctx, finalizedBlockNum, chainID
func (_m *EvmTxStore) FindConfirmedTxesReceipts(ctx context.Context, finalizedBlockNum int64, chainID *big.Int) ([]txmgr.DbReceipt, error) {
	ret := _m.Called(ctx, finalizedBlockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindConfirmedTxesReceipts")
	}

	var r0 []txmgr.DbReceipt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *big.Int) ([]txmgr.DbReceipt, error)); ok {
		return rf(ctx, finalizedBlockNum, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, *big.Int) []txmgr.DbReceipt); ok {
		r0 = rf(ctx, finalizedBlockNum, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]txmgr.DbReceipt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, *big.Int) error); ok {
		r1 = rf(ctx, finalizedBlockNum, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindConfirmedTxesReceipts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindConfirmedTxesReceipts'
type EvmTxStore_FindConfirmedTxesReceipts_Call struct {
	*mock.Call
}

// FindConfirmedTxesReceipts is a helper method to define mock.On call
//   - ctx context.Context
//   - finalizedBlockNum int64
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindConfirmedTxesReceipts(ctx interface{}, finalizedBlockNum interface{}, chainID interface{}) *EvmTxStore_FindConfirmedTxesReceipts_Call {
	return &EvmTxStore_FindConfirmedTxesReceipts_Call{Call: _e.mock.On("FindConfirmedTxesReceipts", ctx, finalizedBlockNum, chainID)}
}

func (_c *EvmTxStore_FindConfirmedTxesReceipts_Call) Run(run func(ctx context.Context, finalizedBlockNum int64, chainID *big.Int)) *EvmTxStore_FindConfirmedTxesReceipts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindConfirmedTxesReceipts_Call) Return(receipts []txmgr.DbReceipt, err error) *EvmTxStore_FindConfirmedTxesReceipts_Call {
	_c.Call.Return(receipts, err)
	return _c
}

func (_c *EvmTxStore_FindConfirmedTxesReceipts_Call) RunAndReturn(run func(context.Context, int64, *big.Int) ([]txmgr.DbReceipt, error)) *EvmTxStore_FindConfirmedTxesReceipts_Call {
	_c.Call.Return(run)
	return _c
}

// FindEarliestUnconfirmedBroadcastTime provides a mock function with given fields: ctx, chainID
func (_m *EvmTxStore) FindEarliestUnconfirmedBroadcastTime(ctx context.Context, chainID *big.Int) (null.Time, error) {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindEarliestUnconfirmedBroadcastTime")
	}

	var r0 null.Time
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) (null.Time, error)); ok {
		return rf(ctx, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) null.Time); ok {
		r0 = rf(ctx, chainID)
	} else {
		r0 = ret.Get(0).(null.Time)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindEarliestUnconfirmedBroadcastTime'
type EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call struct {
	*mock.Call
}

// FindEarliestUnconfirmedBroadcastTime is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindEarliestUnconfirmedBroadcastTime(ctx interface{}, chainID interface{}) *EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call {
	return &EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call{Call: _e.mock.On("FindEarliestUnconfirmedBroadcastTime", ctx, chainID)}
}

func (_c *EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call) Run(run func(ctx context.Context, chainID *big.Int)) *EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call) Return(_a0 null.Time, _a1 error) *EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call) RunAndReturn(run func(context.Context, *big.Int) (null.Time, error)) *EvmTxStore_FindEarliestUnconfirmedBroadcastTime_Call {
	_c.Call.Return(run)
	return _c
}

// FindEarliestUnconfirmedTxAttemptBlock provides a mock function with given fields: ctx, chainID
func (_m *EvmTxStore) FindEarliestUnconfirmedTxAttemptBlock(ctx context.Context, chainID *big.Int) (null.Int, error) {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindEarliestUnconfirmedTxAttemptBlock")
	}

	var r0 null.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) (null.Int, error)); ok {
		return rf(ctx, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) null.Int); ok {
		r0 = rf(ctx, chainID)
	} else {
		r0 = ret.Get(0).(null.Int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindEarliestUnconfirmedTxAttemptBlock'
type EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call struct {
	*mock.Call
}

// FindEarliestUnconfirmedTxAttemptBlock is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindEarliestUnconfirmedTxAttemptBlock(ctx interface{}, chainID interface{}) *EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call {
	return &EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call{Call: _e.mock.On("FindEarliestUnconfirmedTxAttemptBlock", ctx, chainID)}
}

func (_c *EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call) Run(run func(ctx context.Context, chainID *big.Int)) *EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call) Return(_a0 null.Int, _a1 error) *EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call) RunAndReturn(run func(context.Context, *big.Int) (null.Int, error)) *EvmTxStore_FindEarliestUnconfirmedTxAttemptBlock_Call {
	_c.Call.Return(run)
	return _c
}

// FindLatestSequence provides a mock function with given fields: ctx, fromAddress, chainId
func (_m *EvmTxStore) FindLatestSequence(ctx context.Context, fromAddress common.Address, chainId *big.Int) (evmtypes.Nonce, error) {
	ret := _m.Called(ctx, fromAddress, chainId)

	if len(ret) == 0 {
		panic("no return value specified for FindLatestSequence")
	}

	var r0 evmtypes.Nonce
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) (evmtypes.Nonce, error)); ok {
		return rf(ctx, fromAddress, chainId)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) evmtypes.Nonce); ok {
		r0 = rf(ctx, fromAddress, chainId)
	} else {
		r0 = ret.Get(0).(evmtypes.Nonce)
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, fromAddress, chainId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindLatestSequence_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindLatestSequence'
type EvmTxStore_FindLatestSequence_Call struct {
	*mock.Call
}

// FindLatestSequence is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress common.Address
//   - chainId *big.Int
func (_e *EvmTxStore_Expecter) FindLatestSequence(ctx interface{}, fromAddress interface{}, chainId interface{}) *EvmTxStore_FindLatestSequence_Call {
	return &EvmTxStore_FindLatestSequence_Call{Call: _e.mock.On("FindLatestSequence", ctx, fromAddress, chainId)}
}

func (_c *EvmTxStore_FindLatestSequence_Call) Run(run func(ctx context.Context, fromAddress common.Address, chainId *big.Int)) *EvmTxStore_FindLatestSequence_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindLatestSequence_Call) Return(_a0 evmtypes.Nonce, _a1 error) *EvmTxStore_FindLatestSequence_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EvmTxStore_FindLatestSequence_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) (evmtypes.Nonce, error)) *EvmTxStore_FindLatestSequence_Call {
	_c.Call.Return(run)
	return _c
}

// FindNextUnstartedTransactionFromAddress provides a mock function with given fields: ctx, fromAddress, chainID
func (_m *EvmTxStore) FindNextUnstartedTransactionFromAddress(ctx context.Context, fromAddress common.Address, chainID *big.Int) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, fromAddress, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindNextUnstartedTransactionFromAddress")
	}

	var r0 *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, fromAddress, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, fromAddress, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, fromAddress, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindNextUnstartedTransactionFromAddress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindNextUnstartedTransactionFromAddress'
type EvmTxStore_FindNextUnstartedTransactionFromAddress_Call struct {
	*mock.Call
}

// FindNextUnstartedTransactionFromAddress is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress common.Address
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindNextUnstartedTransactionFromAddress(ctx interface{}, fromAddress interface{}, chainID interface{}) *EvmTxStore_FindNextUnstartedTransactionFromAddress_Call {
	return &EvmTxStore_FindNextUnstartedTransactionFromAddress_Call{Call: _e.mock.On("FindNextUnstartedTransactionFromAddress", ctx, fromAddress, chainID)}
}

func (_c *EvmTxStore_FindNextUnstartedTransactionFromAddress_Call) Run(run func(ctx context.Context, fromAddress common.Address, chainID *big.Int)) *EvmTxStore_FindNextUnstartedTransactionFromAddress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindNextUnstartedTransactionFromAddress_Call) Return(_a0 *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], _a1 error) *EvmTxStore_FindNextUnstartedTransactionFromAddress_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EvmTxStore_FindNextUnstartedTransactionFromAddress_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindNextUnstartedTransactionFromAddress_Call {
	_c.Call.Return(run)
	return _c
}

// FindTransactionsConfirmedInBlockRange provides a mock function with given fields: ctx, highBlockNumber, lowBlockNumber, chainID
func (_m *EvmTxStore) FindTransactionsConfirmedInBlockRange(ctx context.Context, highBlockNumber int64, lowBlockNumber int64, chainID *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, highBlockNumber, lowBlockNumber, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTransactionsConfirmedInBlockRange")
	}

	var r0 []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, highBlockNumber, lowBlockNumber, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, *big.Int) []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, highBlockNumber, lowBlockNumber, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, int64, *big.Int) error); ok {
		r1 = rf(ctx, highBlockNumber, lowBlockNumber, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTransactionsConfirmedInBlockRange_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTransactionsConfirmedInBlockRange'
type EvmTxStore_FindTransactionsConfirmedInBlockRange_Call struct {
	*mock.Call
}

// FindTransactionsConfirmedInBlockRange is a helper method to define mock.On call
//   - ctx context.Context
//   - highBlockNumber int64
//   - lowBlockNumber int64
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTransactionsConfirmedInBlockRange(ctx interface{}, highBlockNumber interface{}, lowBlockNumber interface{}, chainID interface{}) *EvmTxStore_FindTransactionsConfirmedInBlockRange_Call {
	return &EvmTxStore_FindTransactionsConfirmedInBlockRange_Call{Call: _e.mock.On("FindTransactionsConfirmedInBlockRange", ctx, highBlockNumber, lowBlockNumber, chainID)}
}

func (_c *EvmTxStore_FindTransactionsConfirmedInBlockRange_Call) Run(run func(ctx context.Context, highBlockNumber int64, lowBlockNumber int64, chainID *big.Int)) *EvmTxStore_FindTransactionsConfirmedInBlockRange_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTransactionsConfirmedInBlockRange_Call) Return(etxs []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTransactionsConfirmedInBlockRange_Call {
	_c.Call.Return(etxs, err)
	return _c
}

func (_c *EvmTxStore_FindTransactionsConfirmedInBlockRange_Call) RunAndReturn(run func(context.Context, int64, int64, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTransactionsConfirmedInBlockRange_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxAttempt provides a mock function with given fields: ctx, hash
func (_m *EvmTxStore) FindTxAttempt(ctx context.Context, hash common.Hash) (*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, hash)

	if len(ret) == 0 {
		panic("no return value specified for FindTxAttempt")
	}

	var r0 *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, hash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, hash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, hash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxAttempt'
type EvmTxStore_FindTxAttempt_Call struct {
	*mock.Call
}

// FindTxAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - hash common.Hash
func (_e *EvmTxStore_Expecter) FindTxAttempt(ctx interface{}, hash interface{}) *EvmTxStore_FindTxAttempt_Call {
	return &EvmTxStore_FindTxAttempt_Call{Call: _e.mock.On("FindTxAttempt", ctx, hash)}
}

func (_c *EvmTxStore_FindTxAttempt_Call) Run(run func(ctx context.Context, hash common.Hash)) *EvmTxStore_FindTxAttempt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *EvmTxStore_FindTxAttempt_Call) Return(_a0 *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], _a1 error) *EvmTxStore_FindTxAttempt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EvmTxStore_FindTxAttempt_Call) RunAndReturn(run func(context.Context, common.Hash) (*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxAttempt_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxAttemptConfirmedByTxIDs provides a mock function with given fields: ctx, ids
func (_m *EvmTxStore) FindTxAttemptConfirmedByTxIDs(ctx context.Context, ids []int64) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for FindTxAttemptConfirmedByTxIDs")
	}

	var r0 []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, ids)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64) []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, ids)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64) error); ok {
		r1 = rf(ctx, ids)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxAttemptConfirmedByTxIDs'
type EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call struct {
	*mock.Call
}

// FindTxAttemptConfirmedByTxIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *EvmTxStore_Expecter) FindTxAttemptConfirmedByTxIDs(ctx interface{}, ids interface{}) *EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call {
	return &EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call{Call: _e.mock.On("FindTxAttemptConfirmedByTxIDs", ctx, ids)}
}

func (_c *EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call) Run(run func(ctx context.Context, ids []int64)) *EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call) Return(_a0 []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], _a1 error) *EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call) RunAndReturn(run func(context.Context, []int64) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxAttemptConfirmedByTxIDs_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxAttemptsConfirmedMissingReceipt provides a mock function with given fields: ctx, chainID
func (_m *EvmTxStore) FindTxAttemptsConfirmedMissingReceipt(ctx context.Context, chainID *big.Int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxAttemptsConfirmedMissingReceipt")
	}

	var r0 []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxAttemptsConfirmedMissingReceipt'
type EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call struct {
	*mock.Call
}

// FindTxAttemptsConfirmedMissingReceipt is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxAttemptsConfirmedMissingReceipt(ctx interface{}, chainID interface{}) *EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call {
	return &EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call{Call: _e.mock.On("FindTxAttemptsConfirmedMissingReceipt", ctx, chainID)}
}

func (_c *EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call) Run(run func(ctx context.Context, chainID *big.Int)) *EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call) Return(attempts []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call {
	_c.Call.Return(attempts, err)
	return _c
}

func (_c *EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call) RunAndReturn(run func(context.Context, *big.Int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxAttemptsConfirmedMissingReceipt_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxAttemptsRequiringReceiptFetch provides a mock function with given fields: ctx, chainID
func (_m *EvmTxStore) FindTxAttemptsRequiringReceiptFetch(ctx context.Context, chainID *big.Int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxAttemptsRequiringReceiptFetch")
	}

	var r0 []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxAttemptsRequiringReceiptFetch'
type EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call struct {
	*mock.Call
}

// FindTxAttemptsRequiringReceiptFetch is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxAttemptsRequiringReceiptFetch(ctx interface{}, chainID interface{}) *EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call {
	return &EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call{Call: _e.mock.On("FindTxAttemptsRequiringReceiptFetch", ctx, chainID)}
}

func (_c *EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call) Run(run func(ctx context.Context, chainID *big.Int)) *EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call) Return(attempts []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call {
	_c.Call.Return(attempts, err)
	return _c
}

func (_c *EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call) RunAndReturn(run func(context.Context, *big.Int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxAttemptsRequiringReceiptFetch_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxAttemptsRequiringResend provides a mock function with given fields: ctx, olderThan, maxInFlightTransactions, chainID, address
func (_m *EvmTxStore) FindTxAttemptsRequiringResend(ctx context.Context, olderThan time.Time, maxInFlightTransactions uint32, chainID *big.Int, address common.Address) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, olderThan, maxInFlightTransactions, chainID, address)

	if len(ret) == 0 {
		panic("no return value specified for FindTxAttemptsRequiringResend")
	}

	var r0 []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, uint32, *big.Int, common.Address) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, olderThan, maxInFlightTransactions, chainID, address)
	}
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, uint32, *big.Int, common.Address) []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, olderThan, maxInFlightTransactions, chainID, address)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, time.Time, uint32, *big.Int, common.Address) error); ok {
		r1 = rf(ctx, olderThan, maxInFlightTransactions, chainID, address)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxAttemptsRequiringResend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxAttemptsRequiringResend'
type EvmTxStore_FindTxAttemptsRequiringResend_Call struct {
	*mock.Call
}

// FindTxAttemptsRequiringResend is a helper method to define mock.On call
//   - ctx context.Context
//   - olderThan time.Time
//   - maxInFlightTransactions uint32
//   - chainID *big.Int
//   - address common.Address
func (_e *EvmTxStore_Expecter) FindTxAttemptsRequiringResend(ctx interface{}, olderThan interface{}, maxInFlightTransactions interface{}, chainID interface{}, address interface{}) *EvmTxStore_FindTxAttemptsRequiringResend_Call {
	return &EvmTxStore_FindTxAttemptsRequiringResend_Call{Call: _e.mock.On("FindTxAttemptsRequiringResend", ctx, olderThan, maxInFlightTransactions, chainID, address)}
}

func (_c *EvmTxStore_FindTxAttemptsRequiringResend_Call) Run(run func(ctx context.Context, olderThan time.Time, maxInFlightTransactions uint32, chainID *big.Int, address common.Address)) *EvmTxStore_FindTxAttemptsRequiringResend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Time), args[2].(uint32), args[3].(*big.Int), args[4].(common.Address))
	})
	return _c
}

func (_c *EvmTxStore_FindTxAttemptsRequiringResend_Call) Return(attempts []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxAttemptsRequiringResend_Call {
	_c.Call.Return(attempts, err)
	return _c
}

func (_c *EvmTxStore_FindTxAttemptsRequiringResend_Call) RunAndReturn(run func(context.Context, time.Time, uint32, *big.Int, common.Address) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxAttemptsRequiringResend_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxByHash provides a mock function with given fields: ctx, hash
func (_m *EvmTxStore) FindTxByHash(ctx context.Context, hash common.Hash) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, hash)

	if len(ret) == 0 {
		panic("no return value specified for FindTxByHash")
	}

	var r0 *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, hash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, hash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, hash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxByHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxByHash'
type EvmTxStore_FindTxByHash_Call struct {
	*mock.Call
}

// FindTxByHash is a helper method to define mock.On call
//   - ctx context.Context
//   - hash common.Hash
func (_e *EvmTxStore_Expecter) FindTxByHash(ctx interface{}, hash interface{}) *EvmTxStore_FindTxByHash_Call {
	return &EvmTxStore_FindTxByHash_Call{Call: _e.mock.On("FindTxByHash", ctx, hash)}
}

func (_c *EvmTxStore_FindTxByHash_Call) Run(run func(ctx context.Context, hash common.Hash)) *EvmTxStore_FindTxByHash_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *EvmTxStore_FindTxByHash_Call) Return(_a0 *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], _a1 error) *EvmTxStore_FindTxByHash_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EvmTxStore_FindTxByHash_Call) RunAndReturn(run func(context.Context, common.Hash) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxByHash_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxWithAttempts provides a mock function with given fields: ctx, etxID
func (_m *EvmTxStore) FindTxWithAttempts(ctx context.Context, etxID int64) (types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, etxID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxWithAttempts")
	}

	var r0 types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, etxID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, etxID)
	} else {
		r0 = ret.Get(0).(types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, etxID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxWithAttempts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxWithAttempts'
type EvmTxStore_FindTxWithAttempts_Call struct {
	*mock.Call
}

// FindTxWithAttempts is a helper method to define mock.On call
//   - ctx context.Context
//   - etxID int64
func (_e *EvmTxStore_Expecter) FindTxWithAttempts(ctx interface{}, etxID interface{}) *EvmTxStore_FindTxWithAttempts_Call {
	return &EvmTxStore_FindTxWithAttempts_Call{Call: _e.mock.On("FindTxWithAttempts", ctx, etxID)}
}

func (_c *EvmTxStore_FindTxWithAttempts_Call) Run(run func(ctx context.Context, etxID int64)) *EvmTxStore_FindTxWithAttempts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *EvmTxStore_FindTxWithAttempts_Call) Return(etx types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxWithAttempts_Call {
	_c.Call.Return(etx, err)
	return _c
}

func (_c *EvmTxStore_FindTxWithAttempts_Call) RunAndReturn(run func(context.Context, int64) (types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxWithAttempts_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxWithIdempotencyKey provides a mock function with given fields: ctx, idempotencyKey, chainID
func (_m *EvmTxStore) FindTxWithIdempotencyKey(ctx context.Context, idempotencyKey string, chainID *big.Int) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, idempotencyKey, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxWithIdempotencyKey")
	}

	var r0 *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *big.Int) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, idempotencyKey, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *big.Int) *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, idempotencyKey, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *big.Int) error); ok {
		r1 = rf(ctx, idempotencyKey, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxWithIdempotencyKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxWithIdempotencyKey'
type EvmTxStore_FindTxWithIdempotencyKey_Call struct {
	*mock.Call
}

// FindTxWithIdempotencyKey is a helper method to define mock.On call
//   - ctx context.Context
//   - idempotencyKey string
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxWithIdempotencyKey(ctx interface{}, idempotencyKey interface{}, chainID interface{}) *EvmTxStore_FindTxWithIdempotencyKey_Call {
	return &EvmTxStore_FindTxWithIdempotencyKey_Call{Call: _e.mock.On("FindTxWithIdempotencyKey", ctx, idempotencyKey, chainID)}
}

func (_c *EvmTxStore_FindTxWithIdempotencyKey_Call) Run(run func(ctx context.Context, idempotencyKey string, chainID *big.Int)) *EvmTxStore_FindTxWithIdempotencyKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxWithIdempotencyKey_Call) Return(tx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxWithIdempotencyKey_Call {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *EvmTxStore_FindTxWithIdempotencyKey_Call) RunAndReturn(run func(context.Context, string, *big.Int) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxWithIdempotencyKey_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxWithSequence provides a mock function with given fields: ctx, fromAddress, seq
func (_m *EvmTxStore) FindTxWithSequence(ctx context.Context, fromAddress common.Address, seq evmtypes.Nonce) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, fromAddress, seq)

	if len(ret) == 0 {
		panic("no return value specified for FindTxWithSequence")
	}

	var r0 *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, evmtypes.Nonce) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, fromAddress, seq)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, evmtypes.Nonce) *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, fromAddress, seq)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, evmtypes.Nonce) error); ok {
		r1 = rf(ctx, fromAddress, seq)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxWithSequence_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxWithSequence'
type EvmTxStore_FindTxWithSequence_Call struct {
	*mock.Call
}

// FindTxWithSequence is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress common.Address
//   - seq evmtypes.Nonce
func (_e *EvmTxStore_Expecter) FindTxWithSequence(ctx interface{}, fromAddress interface{}, seq interface{}) *EvmTxStore_FindTxWithSequence_Call {
	return &EvmTxStore_FindTxWithSequence_Call{Call: _e.mock.On("FindTxWithSequence", ctx, fromAddress, seq)}
}

func (_c *EvmTxStore_FindTxWithSequence_Call) Run(run func(ctx context.Context, fromAddress common.Address, seq evmtypes.Nonce)) *EvmTxStore_FindTxWithSequence_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(evmtypes.Nonce))
	})
	return _c
}

func (_c *EvmTxStore_FindTxWithSequence_Call) Return(etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxWithSequence_Call {
	_c.Call.Return(etx, err)
	return _c
}

func (_c *EvmTxStore_FindTxWithSequence_Call) RunAndReturn(run func(context.Context, common.Address, evmtypes.Nonce) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxWithSequence_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxesByMetaFieldAndStates provides a mock function with given fields: ctx, metaField, metaValue, states, chainID
func (_m *EvmTxStore) FindTxesByMetaFieldAndStates(ctx context.Context, metaField string, metaValue string, states []types.TxState, chainID *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, metaField, metaValue, states, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesByMetaFieldAndStates")
	}

	var r0 []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, []types.TxState, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, metaField, metaValue, states, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, []types.TxState, *big.Int) []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, metaField, metaValue, states, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, []types.TxState, *big.Int) error); ok {
		r1 = rf(ctx, metaField, metaValue, states, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxesByMetaFieldAndStates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesByMetaFieldAndStates'
type EvmTxStore_FindTxesByMetaFieldAndStates_Call struct {
	*mock.Call
}

// FindTxesByMetaFieldAndStates is a helper method to define mock.On call
//   - ctx context.Context
//   - metaField string
//   - metaValue string
//   - states []types.TxState
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxesByMetaFieldAndStates(ctx interface{}, metaField interface{}, metaValue interface{}, states interface{}, chainID interface{}) *EvmTxStore_FindTxesByMetaFieldAndStates_Call {
	return &EvmTxStore_FindTxesByMetaFieldAndStates_Call{Call: _e.mock.On("FindTxesByMetaFieldAndStates", ctx, metaField, metaValue, states, chainID)}
}

func (_c *EvmTxStore_FindTxesByMetaFieldAndStates_Call) Run(run func(ctx context.Context, metaField string, metaValue string, states []types.TxState, chainID *big.Int)) *EvmTxStore_FindTxesByMetaFieldAndStates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(string), args[3].([]types.TxState), args[4].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxesByMetaFieldAndStates_Call) Return(tx []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxesByMetaFieldAndStates_Call {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *EvmTxStore_FindTxesByMetaFieldAndStates_Call) RunAndReturn(run func(context.Context, string, string, []types.TxState, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxesByMetaFieldAndStates_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxesPendingCallback provides a mock function with given fields: ctx, blockNum, chainID
func (_m *EvmTxStore) FindTxesPendingCallback(ctx context.Context, blockNum int64, chainID *big.Int) ([]types.ReceiptPlus[*evmtypes.Receipt], error) {
	ret := _m.Called(ctx, blockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesPendingCallback")
	}

	var r0 []types.ReceiptPlus[*evmtypes.Receipt]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *big.Int) ([]types.ReceiptPlus[*evmtypes.Receipt], error)); ok {
		return rf(ctx, blockNum, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64, *big.Int) []types.ReceiptPlus[*evmtypes.Receipt]); ok {
		r0 = rf(ctx, blockNum, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.ReceiptPlus[*evmtypes.Receipt])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64, *big.Int) error); ok {
		r1 = rf(ctx, blockNum, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxesPendingCallback_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesPendingCallback'
type EvmTxStore_FindTxesPendingCallback_Call struct {
	*mock.Call
}

// FindTxesPendingCallback is a helper method to define mock.On call
//   - ctx context.Context
//   - blockNum int64
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxesPendingCallback(ctx interface{}, blockNum interface{}, chainID interface{}) *EvmTxStore_FindTxesPendingCallback_Call {
	return &EvmTxStore_FindTxesPendingCallback_Call{Call: _e.mock.On("FindTxesPendingCallback", ctx, blockNum, chainID)}
}

func (_c *EvmTxStore_FindTxesPendingCallback_Call) Run(run func(ctx context.Context, blockNum int64, chainID *big.Int)) *EvmTxStore_FindTxesPendingCallback_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxesPendingCallback_Call) Return(receiptsPlus []types.ReceiptPlus[*evmtypes.Receipt], err error) *EvmTxStore_FindTxesPendingCallback_Call {
	_c.Call.Return(receiptsPlus, err)
	return _c
}

func (_c *EvmTxStore_FindTxesPendingCallback_Call) RunAndReturn(run func(context.Context, int64, *big.Int) ([]types.ReceiptPlus[*evmtypes.Receipt], error)) *EvmTxStore_FindTxesPendingCallback_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxesWithAttemptsAndReceiptsByIdsAndState provides a mock function with given fields: ctx, ids, states, chainID
func (_m *EvmTxStore) FindTxesWithAttemptsAndReceiptsByIdsAndState(ctx context.Context, ids []int64, states []types.TxState, chainID *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, ids, states, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesWithAttemptsAndReceiptsByIdsAndState")
	}

	var r0 []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64, []types.TxState, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, ids, states, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []int64, []types.TxState, *big.Int) []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, ids, states, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []int64, []types.TxState, *big.Int) error); ok {
		r1 = rf(ctx, ids, states, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesWithAttemptsAndReceiptsByIdsAndState'
type EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call struct {
	*mock.Call
}

// FindTxesWithAttemptsAndReceiptsByIdsAndState is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
//   - states []types.TxState
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxesWithAttemptsAndReceiptsByIdsAndState(ctx interface{}, ids interface{}, states interface{}, chainID interface{}) *EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call {
	return &EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call{Call: _e.mock.On("FindTxesWithAttemptsAndReceiptsByIdsAndState", ctx, ids, states, chainID)}
}

func (_c *EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call) Run(run func(ctx context.Context, ids []int64, states []types.TxState, chainID *big.Int)) *EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64), args[2].([]types.TxState), args[3].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call) Return(tx []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call) RunAndReturn(run func(context.Context, []int64, []types.TxState, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxesWithAttemptsAndReceiptsByIdsAndState_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxesWithMetaFieldByReceiptBlockNum provides a mock function with given fields: ctx, metaField, blockNum, chainID
func (_m *EvmTxStore) FindTxesWithMetaFieldByReceiptBlockNum(ctx context.Context, metaField string, blockNum int64, chainID *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, metaField, blockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesWithMetaFieldByReceiptBlockNum")
	}

	var r0 []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, metaField, blockNum, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, *big.Int) []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, metaField, blockNum, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int64, *big.Int) error); ok {
		r1 = rf(ctx, metaField, blockNum, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesWithMetaFieldByReceiptBlockNum'
type EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call struct {
	*mock.Call
}

// FindTxesWithMetaFieldByReceiptBlockNum is a helper method to define mock.On call
//   - ctx context.Context
//   - metaField string
//   - blockNum int64
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxesWithMetaFieldByReceiptBlockNum(ctx interface{}, metaField interface{}, blockNum interface{}, chainID interface{}) *EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call {
	return &EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call{Call: _e.mock.On("FindTxesWithMetaFieldByReceiptBlockNum", ctx, metaField, blockNum, chainID)}
}

func (_c *EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call) Run(run func(ctx context.Context, metaField string, blockNum int64, chainID *big.Int)) *EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int64), args[3].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call) Return(tx []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call) RunAndReturn(run func(context.Context, string, int64, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxesWithMetaFieldByReceiptBlockNum_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxesWithMetaFieldByStates provides a mock function with given fields: ctx, metaField, states, chainID
func (_m *EvmTxStore) FindTxesWithMetaFieldByStates(ctx context.Context, metaField string, states []types.TxState, chainID *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, metaField, states, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxesWithMetaFieldByStates")
	}

	var r0 []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []types.TxState, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, metaField, states, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, []types.TxState, *big.Int) []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, metaField, states, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, []types.TxState, *big.Int) error); ok {
		r1 = rf(ctx, metaField, states, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxesWithMetaFieldByStates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxesWithMetaFieldByStates'
type EvmTxStore_FindTxesWithMetaFieldByStates_Call struct {
	*mock.Call
}

// FindTxesWithMetaFieldByStates is a helper method to define mock.On call
//   - ctx context.Context
//   - metaField string
//   - states []types.TxState
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxesWithMetaFieldByStates(ctx interface{}, metaField interface{}, states interface{}, chainID interface{}) *EvmTxStore_FindTxesWithMetaFieldByStates_Call {
	return &EvmTxStore_FindTxesWithMetaFieldByStates_Call{Call: _e.mock.On("FindTxesWithMetaFieldByStates", ctx, metaField, states, chainID)}
}

func (_c *EvmTxStore_FindTxesWithMetaFieldByStates_Call) Run(run func(ctx context.Context, metaField string, states []types.TxState, chainID *big.Int)) *EvmTxStore_FindTxesWithMetaFieldByStates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].([]types.TxState), args[3].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxesWithMetaFieldByStates_Call) Return(tx []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxesWithMetaFieldByStates_Call {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *EvmTxStore_FindTxesWithMetaFieldByStates_Call) RunAndReturn(run func(context.Context, string, []types.TxState, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxesWithMetaFieldByStates_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxsByStateAndFromAddresses provides a mock function with given fields: ctx, addresses, state, chainID
func (_m *EvmTxStore) FindTxsByStateAndFromAddresses(ctx context.Context, addresses []common.Address, state types.TxState, chainID *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, addresses, state, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxsByStateAndFromAddresses")
	}

	var r0 []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []common.Address, types.TxState, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, addresses, state, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []common.Address, types.TxState, *big.Int) []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, addresses, state, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []common.Address, types.TxState, *big.Int) error); ok {
		r1 = rf(ctx, addresses, state, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxsByStateAndFromAddresses_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxsByStateAndFromAddresses'
type EvmTxStore_FindTxsByStateAndFromAddresses_Call struct {
	*mock.Call
}

// FindTxsByStateAndFromAddresses is a helper method to define mock.On call
//   - ctx context.Context
//   - addresses []common.Address
//   - state types.TxState
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxsByStateAndFromAddresses(ctx interface{}, addresses interface{}, state interface{}, chainID interface{}) *EvmTxStore_FindTxsByStateAndFromAddresses_Call {
	return &EvmTxStore_FindTxsByStateAndFromAddresses_Call{Call: _e.mock.On("FindTxsByStateAndFromAddresses", ctx, addresses, state, chainID)}
}

func (_c *EvmTxStore_FindTxsByStateAndFromAddresses_Call) Run(run func(ctx context.Context, addresses []common.Address, state types.TxState, chainID *big.Int)) *EvmTxStore_FindTxsByStateAndFromAddresses_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]common.Address), args[2].(types.TxState), args[3].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxsByStateAndFromAddresses_Call) Return(txs []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxsByStateAndFromAddresses_Call {
	_c.Call.Return(txs, err)
	return _c
}

func (_c *EvmTxStore_FindTxsByStateAndFromAddresses_Call) RunAndReturn(run func(context.Context, []common.Address, types.TxState, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxsByStateAndFromAddresses_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxsRequiringGasBump provides a mock function with given fields: ctx, address, blockNum, gasBumpThreshold, depth, chainID
func (_m *EvmTxStore) FindTxsRequiringGasBump(ctx context.Context, address common.Address, blockNum int64, gasBumpThreshold int64, depth int64, chainID *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, address, blockNum, gasBumpThreshold, depth, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxsRequiringGasBump")
	}

	var r0 []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, int64, int64, int64, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, address, blockNum, gasBumpThreshold, depth, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, int64, int64, int64, *big.Int) []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, address, blockNum, gasBumpThreshold, depth, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, int64, int64, int64, *big.Int) error); ok {
		r1 = rf(ctx, address, blockNum, gasBumpThreshold, depth, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxsRequiringGasBump_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxsRequiringGasBump'
type EvmTxStore_FindTxsRequiringGasBump_Call struct {
	*mock.Call
}

// FindTxsRequiringGasBump is a helper method to define mock.On call
//   - ctx context.Context
//   - address common.Address
//   - blockNum int64
//   - gasBumpThreshold int64
//   - depth int64
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxsRequiringGasBump(ctx interface{}, address interface{}, blockNum interface{}, gasBumpThreshold interface{}, depth interface{}, chainID interface{}) *EvmTxStore_FindTxsRequiringGasBump_Call {
	return &EvmTxStore_FindTxsRequiringGasBump_Call{Call: _e.mock.On("FindTxsRequiringGasBump", ctx, address, blockNum, gasBumpThreshold, depth, chainID)}
}

func (_c *EvmTxStore_FindTxsRequiringGasBump_Call) Run(run func(ctx context.Context, address common.Address, blockNum int64, gasBumpThreshold int64, depth int64, chainID *big.Int)) *EvmTxStore_FindTxsRequiringGasBump_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(int64), args[3].(int64), args[4].(int64), args[5].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxsRequiringGasBump_Call) Return(etxs []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxsRequiringGasBump_Call {
	_c.Call.Return(etxs, err)
	return _c
}

func (_c *EvmTxStore_FindTxsRequiringGasBump_Call) RunAndReturn(run func(context.Context, common.Address, int64, int64, int64, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxsRequiringGasBump_Call {
	_c.Call.Return(run)
	return _c
}

// FindTxsRequiringResubmissionDueToInsufficientFunds provides a mock function with given fields: ctx, address, chainID
func (_m *EvmTxStore) FindTxsRequiringResubmissionDueToInsufficientFunds(ctx context.Context, address common.Address, chainID *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, address, chainID)

	if len(ret) == 0 {
		panic("no return value specified for FindTxsRequiringResubmissionDueToInsufficientFunds")
	}

	var r0 []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, address, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, address, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, address, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTxsRequiringResubmissionDueToInsufficientFunds'
type EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call struct {
	*mock.Call
}

// FindTxsRequiringResubmissionDueToInsufficientFunds is a helper method to define mock.On call
//   - ctx context.Context
//   - address common.Address
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) FindTxsRequiringResubmissionDueToInsufficientFunds(ctx interface{}, address interface{}, chainID interface{}) *EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call {
	return &EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call{Call: _e.mock.On("FindTxsRequiringResubmissionDueToInsufficientFunds", ctx, address, chainID)}
}

func (_c *EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call) Run(run func(ctx context.Context, address common.Address, chainID *big.Int)) *EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call) Return(etxs []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call {
	_c.Call.Return(etxs, err)
	return _c
}

func (_c *EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_FindTxsRequiringResubmissionDueToInsufficientFunds_Call {
	_c.Call.Return(run)
	return _c
}

// GetAbandonedTransactionsByBatch provides a mock function with given fields: ctx, chainID, enabledAddrs, offset, limit
func (_m *EvmTxStore) GetAbandonedTransactionsByBatch(ctx context.Context, chainID *big.Int, enabledAddrs []common.Address, offset uint, limit uint) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, chainID, enabledAddrs, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for GetAbandonedTransactionsByBatch")
	}

	var r0 []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int, []common.Address, uint, uint) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, chainID, enabledAddrs, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int, []common.Address, uint, uint) []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, chainID, enabledAddrs, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int, []common.Address, uint, uint) error); ok {
		r1 = rf(ctx, chainID, enabledAddrs, offset, limit)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_GetAbandonedTransactionsByBatch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAbandonedTransactionsByBatch'
type EvmTxStore_GetAbandonedTransactionsByBatch_Call struct {
	*mock.Call
}

// GetAbandonedTransactionsByBatch is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID *big.Int
//   - enabledAddrs []common.Address
//   - offset uint
//   - limit uint
func (_e *EvmTxStore_Expecter) GetAbandonedTransactionsByBatch(ctx interface{}, chainID interface{}, enabledAddrs interface{}, offset interface{}, limit interface{}) *EvmTxStore_GetAbandonedTransactionsByBatch_Call {
	return &EvmTxStore_GetAbandonedTransactionsByBatch_Call{Call: _e.mock.On("GetAbandonedTransactionsByBatch", ctx, chainID, enabledAddrs, offset, limit)}
}

func (_c *EvmTxStore_GetAbandonedTransactionsByBatch_Call) Run(run func(ctx context.Context, chainID *big.Int, enabledAddrs []common.Address, offset uint, limit uint)) *EvmTxStore_GetAbandonedTransactionsByBatch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int), args[2].([]common.Address), args[3].(uint), args[4].(uint))
	})
	return _c
}

func (_c *EvmTxStore_GetAbandonedTransactionsByBatch_Call) Return(txs []*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_GetAbandonedTransactionsByBatch_Call {
	_c.Call.Return(txs, err)
	return _c
}

func (_c *EvmTxStore_GetAbandonedTransactionsByBatch_Call) RunAndReturn(run func(context.Context, *big.Int, []common.Address, uint, uint) ([]*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_GetAbandonedTransactionsByBatch_Call {
	_c.Call.Return(run)
	return _c
}

// GetInProgressTxAttempts provides a mock function with given fields: ctx, address, chainID
func (_m *EvmTxStore) GetInProgressTxAttempts(ctx context.Context, address common.Address, chainID *big.Int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, address, chainID)

	if len(ret) == 0 {
		panic("no return value specified for GetInProgressTxAttempts")
	}

	var r0 []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, address, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, address, chainID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, address, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_GetInProgressTxAttempts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetInProgressTxAttempts'
type EvmTxStore_GetInProgressTxAttempts_Call struct {
	*mock.Call
}

// GetInProgressTxAttempts is a helper method to define mock.On call
//   - ctx context.Context
//   - address common.Address
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) GetInProgressTxAttempts(ctx interface{}, address interface{}, chainID interface{}) *EvmTxStore_GetInProgressTxAttempts_Call {
	return &EvmTxStore_GetInProgressTxAttempts_Call{Call: _e.mock.On("GetInProgressTxAttempts", ctx, address, chainID)}
}

func (_c *EvmTxStore_GetInProgressTxAttempts_Call) Run(run func(ctx context.Context, address common.Address, chainID *big.Int)) *EvmTxStore_GetInProgressTxAttempts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_GetInProgressTxAttempts_Call) Return(attempts []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_GetInProgressTxAttempts_Call {
	_c.Call.Return(attempts, err)
	return _c
}

func (_c *EvmTxStore_GetInProgressTxAttempts_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_GetInProgressTxAttempts_Call {
	_c.Call.Return(run)
	return _c
}

// GetTxByID provides a mock function with given fields: ctx, id
func (_m *EvmTxStore) GetTxByID(ctx context.Context, id int64) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetTxByID")
	}

	var r0 *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, int64) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int64) *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int64) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_GetTxByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTxByID'
type EvmTxStore_GetTxByID_Call struct {
	*mock.Call
}

// GetTxByID is a helper method to define mock.On call
//   - ctx context.Context
//   - id int64
func (_e *EvmTxStore_Expecter) GetTxByID(ctx interface{}, id interface{}) *EvmTxStore_GetTxByID_Call {
	return &EvmTxStore_GetTxByID_Call{Call: _e.mock.On("GetTxByID", ctx, id)}
}

func (_c *EvmTxStore_GetTxByID_Call) Run(run func(ctx context.Context, id int64)) *EvmTxStore_GetTxByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64))
	})
	return _c
}

func (_c *EvmTxStore_GetTxByID_Call) Return(tx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_GetTxByID_Call {
	_c.Call.Return(tx, err)
	return _c
}

func (_c *EvmTxStore_GetTxByID_Call) RunAndReturn(run func(context.Context, int64) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_GetTxByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetTxInProgress provides a mock function with given fields: ctx, fromAddress
func (_m *EvmTxStore) GetTxInProgress(ctx context.Context, fromAddress common.Address) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error) {
	ret := _m.Called(ctx, fromAddress)

	if len(ret) == 0 {
		panic("no return value specified for GetTxInProgress")
	}

	var r0 *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)); ok {
		return rf(ctx, fromAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, fromAddress)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address) error); ok {
		r1 = rf(ctx, fromAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_GetTxInProgress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTxInProgress'
type EvmTxStore_GetTxInProgress_Call struct {
	*mock.Call
}

// GetTxInProgress is a helper method to define mock.On call
//   - ctx context.Context
//   - fromAddress common.Address
func (_e *EvmTxStore_Expecter) GetTxInProgress(ctx interface{}, fromAddress interface{}) *EvmTxStore_GetTxInProgress_Call {
	return &EvmTxStore_GetTxInProgress_Call{Call: _e.mock.On("GetTxInProgress", ctx, fromAddress)}
}

func (_c *EvmTxStore_GetTxInProgress_Call) Run(run func(ctx context.Context, fromAddress common.Address)) *EvmTxStore_GetTxInProgress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address))
	})
	return _c
}

func (_c *EvmTxStore_GetTxInProgress_Call) Return(etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], err error) *EvmTxStore_GetTxInProgress_Call {
	_c.Call.Return(etx, err)
	return _c
}

func (_c *EvmTxStore_GetTxInProgress_Call) RunAndReturn(run func(context.Context, common.Address) (*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], error)) *EvmTxStore_GetTxInProgress_Call {
	_c.Call.Return(run)
	return _c
}

// HasInProgressTransaction provides a mock function with given fields: ctx, account, chainID
func (_m *EvmTxStore) HasInProgressTransaction(ctx context.Context, account common.Address, chainID *big.Int) (bool, error) {
	ret := _m.Called(ctx, account, chainID)

	if len(ret) == 0 {
		panic("no return value specified for HasInProgressTransaction")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) (bool, error)); ok {
		return rf(ctx, account, chainID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) bool); ok {
		r0 = rf(ctx, account, chainID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, account, chainID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_HasInProgressTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HasInProgressTransaction'
type EvmTxStore_HasInProgressTransaction_Call struct {
	*mock.Call
}

// HasInProgressTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - account common.Address
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) HasInProgressTransaction(ctx interface{}, account interface{}, chainID interface{}) *EvmTxStore_HasInProgressTransaction_Call {
	return &EvmTxStore_HasInProgressTransaction_Call{Call: _e.mock.On("HasInProgressTransaction", ctx, account, chainID)}
}

func (_c *EvmTxStore_HasInProgressTransaction_Call) Run(run func(ctx context.Context, account common.Address, chainID *big.Int)) *EvmTxStore_HasInProgressTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_HasInProgressTransaction_Call) Return(exists bool, err error) *EvmTxStore_HasInProgressTransaction_Call {
	_c.Call.Return(exists, err)
	return _c
}

func (_c *EvmTxStore_HasInProgressTransaction_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) (bool, error)) *EvmTxStore_HasInProgressTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// LoadTxAttempts provides a mock function with given fields: ctx, etx
func (_m *EvmTxStore) LoadTxAttempts(ctx context.Context, etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error {
	ret := _m.Called(ctx, etx)

	if len(ret) == 0 {
		panic("no return value specified for LoadTxAttempts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error); ok {
		r0 = rf(ctx, etx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_LoadTxAttempts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LoadTxAttempts'
type EvmTxStore_LoadTxAttempts_Call struct {
	*mock.Call
}

// LoadTxAttempts is a helper method to define mock.On call
//   - ctx context.Context
//   - etx *types.Tx[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
func (_e *EvmTxStore_Expecter) LoadTxAttempts(ctx interface{}, etx interface{}) *EvmTxStore_LoadTxAttempts_Call {
	return &EvmTxStore_LoadTxAttempts_Call{Call: _e.mock.On("LoadTxAttempts", ctx, etx)}
}

func (_c *EvmTxStore_LoadTxAttempts_Call) Run(run func(ctx context.Context, etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])) *EvmTxStore_LoadTxAttempts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]))
	})
	return _c
}

func (_c *EvmTxStore_LoadTxAttempts_Call) Return(_a0 error) *EvmTxStore_LoadTxAttempts_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_LoadTxAttempts_Call) RunAndReturn(run func(context.Context, *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error) *EvmTxStore_LoadTxAttempts_Call {
	_c.Call.Return(run)
	return _c
}

// MarkAllConfirmedMissingReceipt provides a mock function with given fields: ctx, chainID
func (_m *EvmTxStore) MarkAllConfirmedMissingReceipt(ctx context.Context, chainID *big.Int) error {
	ret := _m.Called(ctx, chainID)

	if len(ret) == 0 {
		panic("no return value specified for MarkAllConfirmedMissingReceipt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) error); ok {
		r0 = rf(ctx, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_MarkAllConfirmedMissingReceipt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkAllConfirmedMissingReceipt'
type EvmTxStore_MarkAllConfirmedMissingReceipt_Call struct {
	*mock.Call
}

// MarkAllConfirmedMissingReceipt is a helper method to define mock.On call
//   - ctx context.Context
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) MarkAllConfirmedMissingReceipt(ctx interface{}, chainID interface{}) *EvmTxStore_MarkAllConfirmedMissingReceipt_Call {
	return &EvmTxStore_MarkAllConfirmedMissingReceipt_Call{Call: _e.mock.On("MarkAllConfirmedMissingReceipt", ctx, chainID)}
}

func (_c *EvmTxStore_MarkAllConfirmedMissingReceipt_Call) Run(run func(ctx context.Context, chainID *big.Int)) *EvmTxStore_MarkAllConfirmedMissingReceipt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_MarkAllConfirmedMissingReceipt_Call) Return(err error) *EvmTxStore_MarkAllConfirmedMissingReceipt_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *EvmTxStore_MarkAllConfirmedMissingReceipt_Call) RunAndReturn(run func(context.Context, *big.Int) error) *EvmTxStore_MarkAllConfirmedMissingReceipt_Call {
	_c.Call.Return(run)
	return _c
}

// MarkOldTxesMissingReceiptAsErrored provides a mock function with given fields: ctx, blockNum, latestFinalizedBlockNum, chainID
func (_m *EvmTxStore) MarkOldTxesMissingReceiptAsErrored(ctx context.Context, blockNum int64, latestFinalizedBlockNum int64, chainID *big.Int) error {
	ret := _m.Called(ctx, blockNum, latestFinalizedBlockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for MarkOldTxesMissingReceiptAsErrored")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, int64, *big.Int) error); ok {
		r0 = rf(ctx, blockNum, latestFinalizedBlockNum, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MarkOldTxesMissingReceiptAsErrored'
type EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call struct {
	*mock.Call
}

// MarkOldTxesMissingReceiptAsErrored is a helper method to define mock.On call
//   - ctx context.Context
//   - blockNum int64
//   - latestFinalizedBlockNum int64
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) MarkOldTxesMissingReceiptAsErrored(ctx interface{}, blockNum interface{}, latestFinalizedBlockNum interface{}, chainID interface{}) *EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call {
	return &EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call{Call: _e.mock.On("MarkOldTxesMissingReceiptAsErrored", ctx, blockNum, latestFinalizedBlockNum, chainID)}
}

func (_c *EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call) Run(run func(ctx context.Context, blockNum int64, latestFinalizedBlockNum int64, chainID *big.Int)) *EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(int64), args[3].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call) Return(_a0 error) *EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call) RunAndReturn(run func(context.Context, int64, int64, *big.Int) error) *EvmTxStore_MarkOldTxesMissingReceiptAsErrored_Call {
	_c.Call.Return(run)
	return _c
}

// PreloadTxes provides a mock function with given fields: ctx, attempts
func (_m *EvmTxStore) PreloadTxes(ctx context.Context, attempts []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error {
	ret := _m.Called(ctx, attempts)

	if len(ret) == 0 {
		panic("no return value specified for PreloadTxes")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error); ok {
		r0 = rf(ctx, attempts)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_PreloadTxes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PreloadTxes'
type EvmTxStore_PreloadTxes_Call struct {
	*mock.Call
}

// PreloadTxes is a helper method to define mock.On call
//   - ctx context.Context
//   - attempts []types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
func (_e *EvmTxStore_Expecter) PreloadTxes(ctx interface{}, attempts interface{}) *EvmTxStore_PreloadTxes_Call {
	return &EvmTxStore_PreloadTxes_Call{Call: _e.mock.On("PreloadTxes", ctx, attempts)}
}

func (_c *EvmTxStore_PreloadTxes_Call) Run(run func(ctx context.Context, attempts []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])) *EvmTxStore_PreloadTxes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]))
	})
	return _c
}

func (_c *EvmTxStore_PreloadTxes_Call) Return(_a0 error) *EvmTxStore_PreloadTxes_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_PreloadTxes_Call) RunAndReturn(run func(context.Context, []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error) *EvmTxStore_PreloadTxes_Call {
	_c.Call.Return(run)
	return _c
}

// PruneUnstartedTxQueue provides a mock function with given fields: ctx, queueSize, subject
func (_m *EvmTxStore) PruneUnstartedTxQueue(ctx context.Context, queueSize uint32, subject uuid.UUID) ([]int64, error) {
	ret := _m.Called(ctx, queueSize, subject)

	if len(ret) == 0 {
		panic("no return value specified for PruneUnstartedTxQueue")
	}

	var r0 []int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint32, uuid.UUID) ([]int64, error)); ok {
		return rf(ctx, queueSize, subject)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint32, uuid.UUID) []int64); ok {
		r0 = rf(ctx, queueSize, subject)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint32, uuid.UUID) error); ok {
		r1 = rf(ctx, queueSize, subject)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EvmTxStore_PruneUnstartedTxQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PruneUnstartedTxQueue'
type EvmTxStore_PruneUnstartedTxQueue_Call struct {
	*mock.Call
}

// PruneUnstartedTxQueue is a helper method to define mock.On call
//   - ctx context.Context
//   - queueSize uint32
//   - subject uuid.UUID
func (_e *EvmTxStore_Expecter) PruneUnstartedTxQueue(ctx interface{}, queueSize interface{}, subject interface{}) *EvmTxStore_PruneUnstartedTxQueue_Call {
	return &EvmTxStore_PruneUnstartedTxQueue_Call{Call: _e.mock.On("PruneUnstartedTxQueue", ctx, queueSize, subject)}
}

func (_c *EvmTxStore_PruneUnstartedTxQueue_Call) Run(run func(ctx context.Context, queueSize uint32, subject uuid.UUID)) *EvmTxStore_PruneUnstartedTxQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint32), args[2].(uuid.UUID))
	})
	return _c
}

func (_c *EvmTxStore_PruneUnstartedTxQueue_Call) Return(ids []int64, err error) *EvmTxStore_PruneUnstartedTxQueue_Call {
	_c.Call.Return(ids, err)
	return _c
}

func (_c *EvmTxStore_PruneUnstartedTxQueue_Call) RunAndReturn(run func(context.Context, uint32, uuid.UUID) ([]int64, error)) *EvmTxStore_PruneUnstartedTxQueue_Call {
	_c.Call.Return(run)
	return _c
}

// ReapTxHistory provides a mock function with given fields: ctx, timeThreshold, chainID
func (_m *EvmTxStore) ReapTxHistory(ctx context.Context, timeThreshold time.Time, chainID *big.Int) error {
	ret := _m.Called(ctx, timeThreshold, chainID)

	if len(ret) == 0 {
		panic("no return value specified for ReapTxHistory")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, *big.Int) error); ok {
		r0 = rf(ctx, timeThreshold, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_ReapTxHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReapTxHistory'
type EvmTxStore_ReapTxHistory_Call struct {
	*mock.Call
}

// ReapTxHistory is a helper method to define mock.On call
//   - ctx context.Context
//   - timeThreshold time.Time
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) ReapTxHistory(ctx interface{}, timeThreshold interface{}, chainID interface{}) *EvmTxStore_ReapTxHistory_Call {
	return &EvmTxStore_ReapTxHistory_Call{Call: _e.mock.On("ReapTxHistory", ctx, timeThreshold, chainID)}
}

func (_c *EvmTxStore_ReapTxHistory_Call) Run(run func(ctx context.Context, timeThreshold time.Time, chainID *big.Int)) *EvmTxStore_ReapTxHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Time), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_ReapTxHistory_Call) Return(_a0 error) *EvmTxStore_ReapTxHistory_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_ReapTxHistory_Call) RunAndReturn(run func(context.Context, time.Time, *big.Int) error) *EvmTxStore_ReapTxHistory_Call {
	_c.Call.Return(run)
	return _c
}

// SaveConfirmedMissingReceiptAttempt provides a mock function with given fields: ctx, timeout, attempt, broadcastAt
func (_m *EvmTxStore) SaveConfirmedMissingReceiptAttempt(ctx context.Context, timeout time.Duration, attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], broadcastAt time.Time) error {
	ret := _m.Called(ctx, timeout, attempt, broadcastAt)

	if len(ret) == 0 {
		panic("no return value specified for SaveConfirmedMissingReceiptAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Duration, *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], time.Time) error); ok {
		r0 = rf(ctx, timeout, attempt, broadcastAt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveConfirmedMissingReceiptAttempt'
type EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call struct {
	*mock.Call
}

// SaveConfirmedMissingReceiptAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - timeout time.Duration
//   - attempt *types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
//   - broadcastAt time.Time
func (_e *EvmTxStore_Expecter) SaveConfirmedMissingReceiptAttempt(ctx interface{}, timeout interface{}, attempt interface{}, broadcastAt interface{}) *EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call {
	return &EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call{Call: _e.mock.On("SaveConfirmedMissingReceiptAttempt", ctx, timeout, attempt, broadcastAt)}
}

func (_c *EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call) Run(run func(ctx context.Context, timeout time.Duration, attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], broadcastAt time.Time)) *EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Duration), args[2].(*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]), args[3].(time.Time))
	})
	return _c
}

func (_c *EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call) Return(_a0 error) *EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call) RunAndReturn(run func(context.Context, time.Duration, *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], time.Time) error) *EvmTxStore_SaveConfirmedMissingReceiptAttempt_Call {
	_c.Call.Return(run)
	return _c
}

// SaveFetchedReceipts provides a mock function with given fields: ctx, r, state, errorMsg, chainID
func (_m *EvmTxStore) SaveFetchedReceipts(ctx context.Context, r []*evmtypes.Receipt, state types.TxState, errorMsg *string, chainID *big.Int) error {
	ret := _m.Called(ctx, r, state, errorMsg, chainID)

	if len(ret) == 0 {
		panic("no return value specified for SaveFetchedReceipts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*evmtypes.Receipt, types.TxState, *string, *big.Int) error); ok {
		r0 = rf(ctx, r, state, errorMsg, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_SaveFetchedReceipts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveFetchedReceipts'
type EvmTxStore_SaveFetchedReceipts_Call struct {
	*mock.Call
}

// SaveFetchedReceipts is a helper method to define mock.On call
//   - ctx context.Context
//   - r []*evmtypes.Receipt
//   - state types.TxState
//   - errorMsg *string
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) SaveFetchedReceipts(ctx interface{}, r interface{}, state interface{}, errorMsg interface{}, chainID interface{}) *EvmTxStore_SaveFetchedReceipts_Call {
	return &EvmTxStore_SaveFetchedReceipts_Call{Call: _e.mock.On("SaveFetchedReceipts", ctx, r, state, errorMsg, chainID)}
}

func (_c *EvmTxStore_SaveFetchedReceipts_Call) Run(run func(ctx context.Context, r []*evmtypes.Receipt, state types.TxState, errorMsg *string, chainID *big.Int)) *EvmTxStore_SaveFetchedReceipts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]*evmtypes.Receipt), args[2].(types.TxState), args[3].(*string), args[4].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_SaveFetchedReceipts_Call) Return(_a0 error) *EvmTxStore_SaveFetchedReceipts_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_SaveFetchedReceipts_Call) RunAndReturn(run func(context.Context, []*evmtypes.Receipt, types.TxState, *string, *big.Int) error) *EvmTxStore_SaveFetchedReceipts_Call {
	_c.Call.Return(run)
	return _c
}

// SaveInProgressAttempt provides a mock function with given fields: ctx, attempt
func (_m *EvmTxStore) SaveInProgressAttempt(ctx context.Context, attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error {
	ret := _m.Called(ctx, attempt)

	if len(ret) == 0 {
		panic("no return value specified for SaveInProgressAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error); ok {
		r0 = rf(ctx, attempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_SaveInProgressAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveInProgressAttempt'
type EvmTxStore_SaveInProgressAttempt_Call struct {
	*mock.Call
}

// SaveInProgressAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - attempt *types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
func (_e *EvmTxStore_Expecter) SaveInProgressAttempt(ctx interface{}, attempt interface{}) *EvmTxStore_SaveInProgressAttempt_Call {
	return &EvmTxStore_SaveInProgressAttempt_Call{Call: _e.mock.On("SaveInProgressAttempt", ctx, attempt)}
}

func (_c *EvmTxStore_SaveInProgressAttempt_Call) Run(run func(ctx context.Context, attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])) *EvmTxStore_SaveInProgressAttempt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]))
	})
	return _c
}

func (_c *EvmTxStore_SaveInProgressAttempt_Call) Return(_a0 error) *EvmTxStore_SaveInProgressAttempt_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_SaveInProgressAttempt_Call) RunAndReturn(run func(context.Context, *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error) *EvmTxStore_SaveInProgressAttempt_Call {
	_c.Call.Return(run)
	return _c
}

// SaveInsufficientFundsAttempt provides a mock function with given fields: ctx, timeout, attempt, broadcastAt
func (_m *EvmTxStore) SaveInsufficientFundsAttempt(ctx context.Context, timeout time.Duration, attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], broadcastAt time.Time) error {
	ret := _m.Called(ctx, timeout, attempt, broadcastAt)

	if len(ret) == 0 {
		panic("no return value specified for SaveInsufficientFundsAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Duration, *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], time.Time) error); ok {
		r0 = rf(ctx, timeout, attempt, broadcastAt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_SaveInsufficientFundsAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveInsufficientFundsAttempt'
type EvmTxStore_SaveInsufficientFundsAttempt_Call struct {
	*mock.Call
}

// SaveInsufficientFundsAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - timeout time.Duration
//   - attempt *types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
//   - broadcastAt time.Time
func (_e *EvmTxStore_Expecter) SaveInsufficientFundsAttempt(ctx interface{}, timeout interface{}, attempt interface{}, broadcastAt interface{}) *EvmTxStore_SaveInsufficientFundsAttempt_Call {
	return &EvmTxStore_SaveInsufficientFundsAttempt_Call{Call: _e.mock.On("SaveInsufficientFundsAttempt", ctx, timeout, attempt, broadcastAt)}
}

func (_c *EvmTxStore_SaveInsufficientFundsAttempt_Call) Run(run func(ctx context.Context, timeout time.Duration, attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], broadcastAt time.Time)) *EvmTxStore_SaveInsufficientFundsAttempt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Duration), args[2].(*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]), args[3].(time.Time))
	})
	return _c
}

func (_c *EvmTxStore_SaveInsufficientFundsAttempt_Call) Return(_a0 error) *EvmTxStore_SaveInsufficientFundsAttempt_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_SaveInsufficientFundsAttempt_Call) RunAndReturn(run func(context.Context, time.Duration, *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], time.Time) error) *EvmTxStore_SaveInsufficientFundsAttempt_Call {
	_c.Call.Return(run)
	return _c
}

// SaveReplacementInProgressAttempt provides a mock function with given fields: ctx, oldAttempt, replacementAttempt
func (_m *EvmTxStore) SaveReplacementInProgressAttempt(ctx context.Context, oldAttempt types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], replacementAttempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error {
	ret := _m.Called(ctx, oldAttempt, replacementAttempt)

	if len(ret) == 0 {
		panic("no return value specified for SaveReplacementInProgressAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error); ok {
		r0 = rf(ctx, oldAttempt, replacementAttempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_SaveReplacementInProgressAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveReplacementInProgressAttempt'
type EvmTxStore_SaveReplacementInProgressAttempt_Call struct {
	*mock.Call
}

// SaveReplacementInProgressAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - oldAttempt types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
//   - replacementAttempt *types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
func (_e *EvmTxStore_Expecter) SaveReplacementInProgressAttempt(ctx interface{}, oldAttempt interface{}, replacementAttempt interface{}) *EvmTxStore_SaveReplacementInProgressAttempt_Call {
	return &EvmTxStore_SaveReplacementInProgressAttempt_Call{Call: _e.mock.On("SaveReplacementInProgressAttempt", ctx, oldAttempt, replacementAttempt)}
}

func (_c *EvmTxStore_SaveReplacementInProgressAttempt_Call) Run(run func(ctx context.Context, oldAttempt types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], replacementAttempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])) *EvmTxStore_SaveReplacementInProgressAttempt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]), args[2].(*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]))
	})
	return _c
}

func (_c *EvmTxStore_SaveReplacementInProgressAttempt_Call) Return(_a0 error) *EvmTxStore_SaveReplacementInProgressAttempt_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_SaveReplacementInProgressAttempt_Call) RunAndReturn(run func(context.Context, types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error) *EvmTxStore_SaveReplacementInProgressAttempt_Call {
	_c.Call.Return(run)
	return _c
}

// SaveSentAttempt provides a mock function with given fields: ctx, timeout, attempt, broadcastAt
func (_m *EvmTxStore) SaveSentAttempt(ctx context.Context, timeout time.Duration, attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], broadcastAt time.Time) error {
	ret := _m.Called(ctx, timeout, attempt, broadcastAt)

	if len(ret) == 0 {
		panic("no return value specified for SaveSentAttempt")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Duration, *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], time.Time) error); ok {
		r0 = rf(ctx, timeout, attempt, broadcastAt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_SaveSentAttempt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SaveSentAttempt'
type EvmTxStore_SaveSentAttempt_Call struct {
	*mock.Call
}

// SaveSentAttempt is a helper method to define mock.On call
//   - ctx context.Context
//   - timeout time.Duration
//   - attempt *types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
//   - broadcastAt time.Time
func (_e *EvmTxStore_Expecter) SaveSentAttempt(ctx interface{}, timeout interface{}, attempt interface{}, broadcastAt interface{}) *EvmTxStore_SaveSentAttempt_Call {
	return &EvmTxStore_SaveSentAttempt_Call{Call: _e.mock.On("SaveSentAttempt", ctx, timeout, attempt, broadcastAt)}
}

func (_c *EvmTxStore_SaveSentAttempt_Call) Run(run func(ctx context.Context, timeout time.Duration, attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], broadcastAt time.Time)) *EvmTxStore_SaveSentAttempt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Duration), args[2].(*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]), args[3].(time.Time))
	})
	return _c
}

func (_c *EvmTxStore_SaveSentAttempt_Call) Return(_a0 error) *EvmTxStore_SaveSentAttempt_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_SaveSentAttempt_Call) RunAndReturn(run func(context.Context, time.Duration, *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], time.Time) error) *EvmTxStore_SaveSentAttempt_Call {
	_c.Call.Return(run)
	return _c
}

// SetBroadcastBeforeBlockNum provides a mock function with given fields: ctx, blockNum, chainID
func (_m *EvmTxStore) SetBroadcastBeforeBlockNum(ctx context.Context, blockNum int64, chainID *big.Int) error {
	ret := _m.Called(ctx, blockNum, chainID)

	if len(ret) == 0 {
		panic("no return value specified for SetBroadcastBeforeBlockNum")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, int64, *big.Int) error); ok {
		r0 = rf(ctx, blockNum, chainID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_SetBroadcastBeforeBlockNum_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetBroadcastBeforeBlockNum'
type EvmTxStore_SetBroadcastBeforeBlockNum_Call struct {
	*mock.Call
}

// SetBroadcastBeforeBlockNum is a helper method to define mock.On call
//   - ctx context.Context
//   - blockNum int64
//   - chainID *big.Int
func (_e *EvmTxStore_Expecter) SetBroadcastBeforeBlockNum(ctx interface{}, blockNum interface{}, chainID interface{}) *EvmTxStore_SetBroadcastBeforeBlockNum_Call {
	return &EvmTxStore_SetBroadcastBeforeBlockNum_Call{Call: _e.mock.On("SetBroadcastBeforeBlockNum", ctx, blockNum, chainID)}
}

func (_c *EvmTxStore_SetBroadcastBeforeBlockNum_Call) Run(run func(ctx context.Context, blockNum int64, chainID *big.Int)) *EvmTxStore_SetBroadcastBeforeBlockNum_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int64), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_SetBroadcastBeforeBlockNum_Call) Return(_a0 error) *EvmTxStore_SetBroadcastBeforeBlockNum_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_SetBroadcastBeforeBlockNum_Call) RunAndReturn(run func(context.Context, int64, *big.Int) error) *EvmTxStore_SetBroadcastBeforeBlockNum_Call {
	_c.Call.Return(run)
	return _c
}

// Transactions provides a mock function with given fields: ctx, offset, limit
func (_m *EvmTxStore) Transactions(ctx context.Context, offset int, limit int) ([]types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], int, error) {
	ret := _m.Called(ctx, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for Transactions")
	}

	var r0 []types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) ([]types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], int, error)); ok {
		return rf(ctx, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) []types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) int); ok {
		r1 = rf(ctx, offset, limit)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int, int) error); ok {
		r2 = rf(ctx, offset, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// EvmTxStore_Transactions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transactions'
type EvmTxStore_Transactions_Call struct {
	*mock.Call
}

// Transactions is a helper method to define mock.On call
//   - ctx context.Context
//   - offset int
//   - limit int
func (_e *EvmTxStore_Expecter) Transactions(ctx interface{}, offset interface{}, limit interface{}) *EvmTxStore_Transactions_Call {
	return &EvmTxStore_Transactions_Call{Call: _e.mock.On("Transactions", ctx, offset, limit)}
}

func (_c *EvmTxStore_Transactions_Call) Run(run func(ctx context.Context, offset int, limit int)) *EvmTxStore_Transactions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *EvmTxStore_Transactions_Call) Return(_a0 []types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], _a1 int, _a2 error) *EvmTxStore_Transactions_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *EvmTxStore_Transactions_Call) RunAndReturn(run func(context.Context, int, int) ([]types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], int, error)) *EvmTxStore_Transactions_Call {
	_c.Call.Return(run)
	return _c
}

// TransactionsWithAttempts provides a mock function with given fields: ctx, offset, limit
func (_m *EvmTxStore) TransactionsWithAttempts(ctx context.Context, offset int, limit int) ([]types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], int, error) {
	ret := _m.Called(ctx, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for TransactionsWithAttempts")
	}

	var r0 []types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) ([]types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], int, error)); ok {
		return rf(ctx, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) []types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) int); ok {
		r1 = rf(ctx, offset, limit)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int, int) error); ok {
		r2 = rf(ctx, offset, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// EvmTxStore_TransactionsWithAttempts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransactionsWithAttempts'
type EvmTxStore_TransactionsWithAttempts_Call struct {
	*mock.Call
}

// TransactionsWithAttempts is a helper method to define mock.On call
//   - ctx context.Context
//   - offset int
//   - limit int
func (_e *EvmTxStore_Expecter) TransactionsWithAttempts(ctx interface{}, offset interface{}, limit interface{}) *EvmTxStore_TransactionsWithAttempts_Call {
	return &EvmTxStore_TransactionsWithAttempts_Call{Call: _e.mock.On("TransactionsWithAttempts", ctx, offset, limit)}
}

func (_c *EvmTxStore_TransactionsWithAttempts_Call) Run(run func(ctx context.Context, offset int, limit int)) *EvmTxStore_TransactionsWithAttempts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *EvmTxStore_TransactionsWithAttempts_Call) Return(_a0 []types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], _a1 int, _a2 error) *EvmTxStore_TransactionsWithAttempts_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *EvmTxStore_TransactionsWithAttempts_Call) RunAndReturn(run func(context.Context, int, int) ([]types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], int, error)) *EvmTxStore_TransactionsWithAttempts_Call {
	_c.Call.Return(run)
	return _c
}

// TxAttempts provides a mock function with given fields: ctx, offset, limit
func (_m *EvmTxStore) TxAttempts(ctx context.Context, offset int, limit int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], int, error) {
	ret := _m.Called(ctx, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for TxAttempts")
	}

	var r0 []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], int, error)); ok {
		return rf(ctx, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]); ok {
		r0 = rf(ctx, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) int); ok {
		r1 = rf(ctx, offset, limit)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int, int) error); ok {
		r2 = rf(ctx, offset, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// EvmTxStore_TxAttempts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TxAttempts'
type EvmTxStore_TxAttempts_Call struct {
	*mock.Call
}

// TxAttempts is a helper method to define mock.On call
//   - ctx context.Context
//   - offset int
//   - limit int
func (_e *EvmTxStore_Expecter) TxAttempts(ctx interface{}, offset interface{}, limit interface{}) *EvmTxStore_TxAttempts_Call {
	return &EvmTxStore_TxAttempts_Call{Call: _e.mock.On("TxAttempts", ctx, offset, limit)}
}

func (_c *EvmTxStore_TxAttempts_Call) Run(run func(ctx context.Context, offset int, limit int)) *EvmTxStore_TxAttempts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *EvmTxStore_TxAttempts_Call) Return(_a0 []types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], _a1 int, _a2 error) *EvmTxStore_TxAttempts_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *EvmTxStore_TxAttempts_Call) RunAndReturn(run func(context.Context, int, int) ([]types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], int, error)) *EvmTxStore_TxAttempts_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateBroadcastAts provides a mock function with given fields: ctx, now, etxIDs
func (_m *EvmTxStore) UpdateBroadcastAts(ctx context.Context, now time.Time, etxIDs []int64) error {
	ret := _m.Called(ctx, now, etxIDs)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBroadcastAts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, []int64) error); ok {
		r0 = rf(ctx, now, etxIDs)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_UpdateBroadcastAts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateBroadcastAts'
type EvmTxStore_UpdateBroadcastAts_Call struct {
	*mock.Call
}

// UpdateBroadcastAts is a helper method to define mock.On call
//   - ctx context.Context
//   - now time.Time
//   - etxIDs []int64
func (_e *EvmTxStore_Expecter) UpdateBroadcastAts(ctx interface{}, now interface{}, etxIDs interface{}) *EvmTxStore_UpdateBroadcastAts_Call {
	return &EvmTxStore_UpdateBroadcastAts_Call{Call: _e.mock.On("UpdateBroadcastAts", ctx, now, etxIDs)}
}

func (_c *EvmTxStore_UpdateBroadcastAts_Call) Run(run func(ctx context.Context, now time.Time, etxIDs []int64)) *EvmTxStore_UpdateBroadcastAts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(time.Time), args[2].([]int64))
	})
	return _c
}

func (_c *EvmTxStore_UpdateBroadcastAts_Call) Return(_a0 error) *EvmTxStore_UpdateBroadcastAts_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_UpdateBroadcastAts_Call) RunAndReturn(run func(context.Context, time.Time, []int64) error) *EvmTxStore_UpdateBroadcastAts_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTxAttemptInProgressToBroadcast provides a mock function with given fields: ctx, etx, attempt, NewAttemptState
func (_m *EvmTxStore) UpdateTxAttemptInProgressToBroadcast(ctx context.Context, etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], attempt types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], NewAttemptState types.TxAttemptState) error {
	ret := _m.Called(ctx, etx, attempt, NewAttemptState)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxAttemptInProgressToBroadcast")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], types.TxAttemptState) error); ok {
		r0 = rf(ctx, etx, attempt, NewAttemptState)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxAttemptInProgressToBroadcast'
type EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call struct {
	*mock.Call
}

// UpdateTxAttemptInProgressToBroadcast is a helper method to define mock.On call
//   - ctx context.Context
//   - etx *types.Tx[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
//   - attempt types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
//   - NewAttemptState types.TxAttemptState
func (_e *EvmTxStore_Expecter) UpdateTxAttemptInProgressToBroadcast(ctx interface{}, etx interface{}, attempt interface{}, NewAttemptState interface{}) *EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call {
	return &EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call{Call: _e.mock.On("UpdateTxAttemptInProgressToBroadcast", ctx, etx, attempt, NewAttemptState)}
}

func (_c *EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call) Run(run func(ctx context.Context, etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], attempt types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], NewAttemptState types.TxAttemptState)) *EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]), args[2].(types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]), args[3].(types.TxAttemptState))
	})
	return _c
}

func (_c *EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call) Return(_a0 error) *EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call) RunAndReturn(run func(context.Context, *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], types.TxAttemptState) error) *EvmTxStore_UpdateTxAttemptInProgressToBroadcast_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTxCallbackCompleted provides a mock function with given fields: ctx, pipelineTaskRunRid, chainId
func (_m *EvmTxStore) UpdateTxCallbackCompleted(ctx context.Context, pipelineTaskRunRid uuid.UUID, chainId *big.Int) error {
	ret := _m.Called(ctx, pipelineTaskRunRid, chainId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxCallbackCompleted")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, *big.Int) error); ok {
		r0 = rf(ctx, pipelineTaskRunRid, chainId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_UpdateTxCallbackCompleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxCallbackCompleted'
type EvmTxStore_UpdateTxCallbackCompleted_Call struct {
	*mock.Call
}

// UpdateTxCallbackCompleted is a helper method to define mock.On call
//   - ctx context.Context
//   - pipelineTaskRunRid uuid.UUID
//   - chainId *big.Int
func (_e *EvmTxStore_Expecter) UpdateTxCallbackCompleted(ctx interface{}, pipelineTaskRunRid interface{}, chainId interface{}) *EvmTxStore_UpdateTxCallbackCompleted_Call {
	return &EvmTxStore_UpdateTxCallbackCompleted_Call{Call: _e.mock.On("UpdateTxCallbackCompleted", ctx, pipelineTaskRunRid, chainId)}
}

func (_c *EvmTxStore_UpdateTxCallbackCompleted_Call) Run(run func(ctx context.Context, pipelineTaskRunRid uuid.UUID, chainId *big.Int)) *EvmTxStore_UpdateTxCallbackCompleted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_UpdateTxCallbackCompleted_Call) Return(_a0 error) *EvmTxStore_UpdateTxCallbackCompleted_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_UpdateTxCallbackCompleted_Call) RunAndReturn(run func(context.Context, uuid.UUID, *big.Int) error) *EvmTxStore_UpdateTxCallbackCompleted_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTxFatalError provides a mock function with given fields: ctx, etx
func (_m *EvmTxStore) UpdateTxFatalError(ctx context.Context, etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error {
	ret := _m.Called(ctx, etx)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxFatalError")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error); ok {
		r0 = rf(ctx, etx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_UpdateTxFatalError_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxFatalError'
type EvmTxStore_UpdateTxFatalError_Call struct {
	*mock.Call
}

// UpdateTxFatalError is a helper method to define mock.On call
//   - ctx context.Context
//   - etx *types.Tx[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
func (_e *EvmTxStore_Expecter) UpdateTxFatalError(ctx interface{}, etx interface{}) *EvmTxStore_UpdateTxFatalError_Call {
	return &EvmTxStore_UpdateTxFatalError_Call{Call: _e.mock.On("UpdateTxFatalError", ctx, etx)}
}

func (_c *EvmTxStore_UpdateTxFatalError_Call) Run(run func(ctx context.Context, etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])) *EvmTxStore_UpdateTxFatalError_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]))
	})
	return _c
}

func (_c *EvmTxStore_UpdateTxFatalError_Call) Return(_a0 error) *EvmTxStore_UpdateTxFatalError_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_UpdateTxFatalError_Call) RunAndReturn(run func(context.Context, *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error) *EvmTxStore_UpdateTxFatalError_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTxForRebroadcast provides a mock function with given fields: ctx, etx, etxAttempt
func (_m *EvmTxStore) UpdateTxForRebroadcast(ctx context.Context, etx types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], etxAttempt types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error {
	ret := _m.Called(ctx, etx, etxAttempt)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxForRebroadcast")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error); ok {
		r0 = rf(ctx, etx, etxAttempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_UpdateTxForRebroadcast_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxForRebroadcast'
type EvmTxStore_UpdateTxForRebroadcast_Call struct {
	*mock.Call
}

// UpdateTxForRebroadcast is a helper method to define mock.On call
//   - ctx context.Context
//   - etx types.Tx[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
//   - etxAttempt types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
func (_e *EvmTxStore_Expecter) UpdateTxForRebroadcast(ctx interface{}, etx interface{}, etxAttempt interface{}) *EvmTxStore_UpdateTxForRebroadcast_Call {
	return &EvmTxStore_UpdateTxForRebroadcast_Call{Call: _e.mock.On("UpdateTxForRebroadcast", ctx, etx, etxAttempt)}
}

func (_c *EvmTxStore_UpdateTxForRebroadcast_Call) Run(run func(ctx context.Context, etx types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], etxAttempt types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])) *EvmTxStore_UpdateTxForRebroadcast_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]), args[2].(types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]))
	})
	return _c
}

func (_c *EvmTxStore_UpdateTxForRebroadcast_Call) Return(_a0 error) *EvmTxStore_UpdateTxForRebroadcast_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_UpdateTxForRebroadcast_Call) RunAndReturn(run func(context.Context, types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error) *EvmTxStore_UpdateTxForRebroadcast_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTxStatesToFinalizedUsingReceiptIds provides a mock function with given fields: ctx, etxIDs, chainId
func (_m *EvmTxStore) UpdateTxStatesToFinalizedUsingReceiptIds(ctx context.Context, etxIDs []int64, chainId *big.Int) error {
	ret := _m.Called(ctx, etxIDs, chainId)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxStatesToFinalizedUsingReceiptIds")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64, *big.Int) error); ok {
		r0 = rf(ctx, etxIDs, chainId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxStatesToFinalizedUsingReceiptIds'
type EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call struct {
	*mock.Call
}

// UpdateTxStatesToFinalizedUsingReceiptIds is a helper method to define mock.On call
//   - ctx context.Context
//   - etxIDs []int64
//   - chainId *big.Int
func (_e *EvmTxStore_Expecter) UpdateTxStatesToFinalizedUsingReceiptIds(ctx interface{}, etxIDs interface{}, chainId interface{}) *EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call {
	return &EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call{Call: _e.mock.On("UpdateTxStatesToFinalizedUsingReceiptIds", ctx, etxIDs, chainId)}
}

func (_c *EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call) Run(run func(ctx context.Context, etxIDs []int64, chainId *big.Int)) *EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64), args[2].(*big.Int))
	})
	return _c
}

func (_c *EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call) Return(_a0 error) *EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call) RunAndReturn(run func(context.Context, []int64, *big.Int) error) *EvmTxStore_UpdateTxStatesToFinalizedUsingReceiptIds_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTxUnstartedToInProgress provides a mock function with given fields: ctx, etx, attempt
func (_m *EvmTxStore) UpdateTxUnstartedToInProgress(ctx context.Context, etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error {
	ret := _m.Called(ctx, etx, attempt)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxUnstartedToInProgress")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error); ok {
		r0 = rf(ctx, etx, attempt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_UpdateTxUnstartedToInProgress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxUnstartedToInProgress'
type EvmTxStore_UpdateTxUnstartedToInProgress_Call struct {
	*mock.Call
}

// UpdateTxUnstartedToInProgress is a helper method to define mock.On call
//   - ctx context.Context
//   - etx *types.Tx[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
//   - attempt *types.TxAttempt[*big.Int,common.Address,common.Hash,common.Hash,evmtypes.Nonce,gas.EvmFee]
func (_e *EvmTxStore_Expecter) UpdateTxUnstartedToInProgress(ctx interface{}, etx interface{}, attempt interface{}) *EvmTxStore_UpdateTxUnstartedToInProgress_Call {
	return &EvmTxStore_UpdateTxUnstartedToInProgress_Call{Call: _e.mock.On("UpdateTxUnstartedToInProgress", ctx, etx, attempt)}
}

func (_c *EvmTxStore_UpdateTxUnstartedToInProgress_Call) Run(run func(ctx context.Context, etx *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], attempt *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee])) *EvmTxStore_UpdateTxUnstartedToInProgress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]), args[2].(*types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]))
	})
	return _c
}

func (_c *EvmTxStore_UpdateTxUnstartedToInProgress_Call) Return(_a0 error) *EvmTxStore_UpdateTxUnstartedToInProgress_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_UpdateTxUnstartedToInProgress_Call) RunAndReturn(run func(context.Context, *types.Tx[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee], *types.TxAttempt[*big.Int, common.Address, common.Hash, common.Hash, evmtypes.Nonce, gas.EvmFee]) error) *EvmTxStore_UpdateTxUnstartedToInProgress_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTxsUnconfirmed provides a mock function with given fields: ctx, ids
func (_m *EvmTxStore) UpdateTxsUnconfirmed(ctx context.Context, ids []int64) error {
	ret := _m.Called(ctx, ids)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTxsUnconfirmed")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []int64) error); ok {
		r0 = rf(ctx, ids)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EvmTxStore_UpdateTxsUnconfirmed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTxsUnconfirmed'
type EvmTxStore_UpdateTxsUnconfirmed_Call struct {
	*mock.Call
}

// UpdateTxsUnconfirmed is a helper method to define mock.On call
//   - ctx context.Context
//   - ids []int64
func (_e *EvmTxStore_Expecter) UpdateTxsUnconfirmed(ctx interface{}, ids interface{}) *EvmTxStore_UpdateTxsUnconfirmed_Call {
	return &EvmTxStore_UpdateTxsUnconfirmed_Call{Call: _e.mock.On("UpdateTxsUnconfirmed", ctx, ids)}
}

func (_c *EvmTxStore_UpdateTxsUnconfirmed_Call) Run(run func(ctx context.Context, ids []int64)) *EvmTxStore_UpdateTxsUnconfirmed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]int64))
	})
	return _c
}

func (_c *EvmTxStore_UpdateTxsUnconfirmed_Call) Return(_a0 error) *EvmTxStore_UpdateTxsUnconfirmed_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EvmTxStore_UpdateTxsUnconfirmed_Call) RunAndReturn(run func(context.Context, []int64) error) *EvmTxStore_UpdateTxsUnconfirmed_Call {
	_c.Call.Return(run)
	return _c
}

// NewEvmTxStore creates a new instance of EvmTxStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewEvmTxStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *EvmTxStore {
	mock := &EvmTxStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
