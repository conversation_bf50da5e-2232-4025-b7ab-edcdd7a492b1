// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	query "github.com/smartcontractkit/chainlink-common/pkg/types/query"
	primitives "github.com/smartcontractkit/chainlink-common/pkg/types/query/primitives"
	mock "github.com/stretchr/testify/mock"

	types "github.com/smartcontractkit/chainlink-common/pkg/types"
)

// ContractReader is an autogenerated mock type for the ContractReader type
type ContractReader struct {
	mock.Mock
}

type ContractReader_Expecter struct {
	mock *mock.Mock
}

func (_m *ContractReader) EXPECT() *ContractReader_Expecter {
	return &ContractReader_Expecter{mock: &_m.Mock}
}

// BatchGetLatestValues provides a mock function with given fields: ctx, request
func (_m *ContractReader) BatchGetLatestValues(ctx context.Context, request types.BatchGetLatestValuesRequest) (types.BatchGetLatestValuesResult, error) {
	ret := _m.Called(ctx, request)

	if len(ret) == 0 {
		panic("no return value specified for BatchGetLatestValues")
	}

	var r0 types.BatchGetLatestValuesResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.BatchGetLatestValuesRequest) (types.BatchGetLatestValuesResult, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.BatchGetLatestValuesRequest) types.BatchGetLatestValuesResult); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(types.BatchGetLatestValuesResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.BatchGetLatestValuesRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ContractReader_BatchGetLatestValues_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchGetLatestValues'
type ContractReader_BatchGetLatestValues_Call struct {
	*mock.Call
}

// BatchGetLatestValues is a helper method to define mock.On call
//   - ctx context.Context
//   - request types.BatchGetLatestValuesRequest
func (_e *ContractReader_Expecter) BatchGetLatestValues(ctx interface{}, request interface{}) *ContractReader_BatchGetLatestValues_Call {
	return &ContractReader_BatchGetLatestValues_Call{Call: _e.mock.On("BatchGetLatestValues", ctx, request)}
}

func (_c *ContractReader_BatchGetLatestValues_Call) Run(run func(ctx context.Context, request types.BatchGetLatestValuesRequest)) *ContractReader_BatchGetLatestValues_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.BatchGetLatestValuesRequest))
	})
	return _c
}

func (_c *ContractReader_BatchGetLatestValues_Call) Return(_a0 types.BatchGetLatestValuesResult, _a1 error) *ContractReader_BatchGetLatestValues_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ContractReader_BatchGetLatestValues_Call) RunAndReturn(run func(context.Context, types.BatchGetLatestValuesRequest) (types.BatchGetLatestValuesResult, error)) *ContractReader_BatchGetLatestValues_Call {
	_c.Call.Return(run)
	return _c
}

// Bind provides a mock function with given fields: ctx, bindings
func (_m *ContractReader) Bind(ctx context.Context, bindings []types.BoundContract) error {
	ret := _m.Called(ctx, bindings)

	if len(ret) == 0 {
		panic("no return value specified for Bind")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []types.BoundContract) error); ok {
		r0 = rf(ctx, bindings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContractReader_Bind_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Bind'
type ContractReader_Bind_Call struct {
	*mock.Call
}

// Bind is a helper method to define mock.On call
//   - ctx context.Context
//   - bindings []types.BoundContract
func (_e *ContractReader_Expecter) Bind(ctx interface{}, bindings interface{}) *ContractReader_Bind_Call {
	return &ContractReader_Bind_Call{Call: _e.mock.On("Bind", ctx, bindings)}
}

func (_c *ContractReader_Bind_Call) Run(run func(ctx context.Context, bindings []types.BoundContract)) *ContractReader_Bind_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]types.BoundContract))
	})
	return _c
}

func (_c *ContractReader_Bind_Call) Return(_a0 error) *ContractReader_Bind_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractReader_Bind_Call) RunAndReturn(run func(context.Context, []types.BoundContract) error) *ContractReader_Bind_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with given fields:
func (_m *ContractReader) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContractReader_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type ContractReader_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *ContractReader_Expecter) Close() *ContractReader_Close_Call {
	return &ContractReader_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *ContractReader_Close_Call) Run(run func()) *ContractReader_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ContractReader_Close_Call) Return(_a0 error) *ContractReader_Close_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractReader_Close_Call) RunAndReturn(run func() error) *ContractReader_Close_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestValue provides a mock function with given fields: ctx, readIdentifier, confidenceLevel, params, returnVal
func (_m *ContractReader) GetLatestValue(ctx context.Context, readIdentifier string, confidenceLevel primitives.ConfidenceLevel, params interface{}, returnVal interface{}) error {
	ret := _m.Called(ctx, readIdentifier, confidenceLevel, params, returnVal)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestValue")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, primitives.ConfidenceLevel, interface{}, interface{}) error); ok {
		r0 = rf(ctx, readIdentifier, confidenceLevel, params, returnVal)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContractReader_GetLatestValue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestValue'
type ContractReader_GetLatestValue_Call struct {
	*mock.Call
}

// GetLatestValue is a helper method to define mock.On call
//   - ctx context.Context
//   - readIdentifier string
//   - confidenceLevel primitives.ConfidenceLevel
//   - params interface{}
//   - returnVal interface{}
func (_e *ContractReader_Expecter) GetLatestValue(ctx interface{}, readIdentifier interface{}, confidenceLevel interface{}, params interface{}, returnVal interface{}) *ContractReader_GetLatestValue_Call {
	return &ContractReader_GetLatestValue_Call{Call: _e.mock.On("GetLatestValue", ctx, readIdentifier, confidenceLevel, params, returnVal)}
}

func (_c *ContractReader_GetLatestValue_Call) Run(run func(ctx context.Context, readIdentifier string, confidenceLevel primitives.ConfidenceLevel, params interface{}, returnVal interface{})) *ContractReader_GetLatestValue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(primitives.ConfidenceLevel), args[3].(interface{}), args[4].(interface{}))
	})
	return _c
}

func (_c *ContractReader_GetLatestValue_Call) Return(_a0 error) *ContractReader_GetLatestValue_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractReader_GetLatestValue_Call) RunAndReturn(run func(context.Context, string, primitives.ConfidenceLevel, interface{}, interface{}) error) *ContractReader_GetLatestValue_Call {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with given fields:
func (_m *ContractReader) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// ContractReader_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type ContractReader_HealthReport_Call struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *ContractReader_Expecter) HealthReport() *ContractReader_HealthReport_Call {
	return &ContractReader_HealthReport_Call{Call: _e.mock.On("HealthReport")}
}

func (_c *ContractReader_HealthReport_Call) Run(run func()) *ContractReader_HealthReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ContractReader_HealthReport_Call) Return(_a0 map[string]error) *ContractReader_HealthReport_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractReader_HealthReport_Call) RunAndReturn(run func() map[string]error) *ContractReader_HealthReport_Call {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with given fields:
func (_m *ContractReader) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// ContractReader_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type ContractReader_Name_Call struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *ContractReader_Expecter) Name() *ContractReader_Name_Call {
	return &ContractReader_Name_Call{Call: _e.mock.On("Name")}
}

func (_c *ContractReader_Name_Call) Run(run func()) *ContractReader_Name_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ContractReader_Name_Call) Return(_a0 string) *ContractReader_Name_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractReader_Name_Call) RunAndReturn(run func() string) *ContractReader_Name_Call {
	_c.Call.Return(run)
	return _c
}

// QueryKey provides a mock function with given fields: ctx, contract, filter, limitAndSort, sequenceDataType
func (_m *ContractReader) QueryKey(ctx context.Context, contract types.BoundContract, filter query.KeyFilter, limitAndSort query.LimitAndSort, sequenceDataType interface{}) ([]types.Sequence, error) {
	ret := _m.Called(ctx, contract, filter, limitAndSort, sequenceDataType)

	if len(ret) == 0 {
		panic("no return value specified for QueryKey")
	}

	var r0 []types.Sequence
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, types.BoundContract, query.KeyFilter, query.LimitAndSort, interface{}) ([]types.Sequence, error)); ok {
		return rf(ctx, contract, filter, limitAndSort, sequenceDataType)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.BoundContract, query.KeyFilter, query.LimitAndSort, interface{}) []types.Sequence); ok {
		r0 = rf(ctx, contract, filter, limitAndSort, sequenceDataType)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.Sequence)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.BoundContract, query.KeyFilter, query.LimitAndSort, interface{}) error); ok {
		r1 = rf(ctx, contract, filter, limitAndSort, sequenceDataType)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ContractReader_QueryKey_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'QueryKey'
type ContractReader_QueryKey_Call struct {
	*mock.Call
}

// QueryKey is a helper method to define mock.On call
//   - ctx context.Context
//   - contract types.BoundContract
//   - filter query.KeyFilter
//   - limitAndSort query.LimitAndSort
//   - sequenceDataType interface{}
func (_e *ContractReader_Expecter) QueryKey(ctx interface{}, contract interface{}, filter interface{}, limitAndSort interface{}, sequenceDataType interface{}) *ContractReader_QueryKey_Call {
	return &ContractReader_QueryKey_Call{Call: _e.mock.On("QueryKey", ctx, contract, filter, limitAndSort, sequenceDataType)}
}

func (_c *ContractReader_QueryKey_Call) Run(run func(ctx context.Context, contract types.BoundContract, filter query.KeyFilter, limitAndSort query.LimitAndSort, sequenceDataType interface{})) *ContractReader_QueryKey_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.BoundContract), args[2].(query.KeyFilter), args[3].(query.LimitAndSort), args[4].(interface{}))
	})
	return _c
}

func (_c *ContractReader_QueryKey_Call) Return(_a0 []types.Sequence, _a1 error) *ContractReader_QueryKey_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ContractReader_QueryKey_Call) RunAndReturn(run func(context.Context, types.BoundContract, query.KeyFilter, query.LimitAndSort, interface{}) ([]types.Sequence, error)) *ContractReader_QueryKey_Call {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with given fields:
func (_m *ContractReader) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContractReader_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type ContractReader_Ready_Call struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *ContractReader_Expecter) Ready() *ContractReader_Ready_Call {
	return &ContractReader_Ready_Call{Call: _e.mock.On("Ready")}
}

func (_c *ContractReader_Ready_Call) Run(run func()) *ContractReader_Ready_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ContractReader_Ready_Call) Return(_a0 error) *ContractReader_Ready_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractReader_Ready_Call) RunAndReturn(run func() error) *ContractReader_Ready_Call {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *ContractReader) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContractReader_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type ContractReader_Start_Call struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *ContractReader_Expecter) Start(_a0 interface{}) *ContractReader_Start_Call {
	return &ContractReader_Start_Call{Call: _e.mock.On("Start", _a0)}
}

func (_c *ContractReader_Start_Call) Run(run func(_a0 context.Context)) *ContractReader_Start_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *ContractReader_Start_Call) Return(_a0 error) *ContractReader_Start_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractReader_Start_Call) RunAndReturn(run func(context.Context) error) *ContractReader_Start_Call {
	_c.Call.Return(run)
	return _c
}

// Unbind provides a mock function with given fields: ctx, bindings
func (_m *ContractReader) Unbind(ctx context.Context, bindings []types.BoundContract) error {
	ret := _m.Called(ctx, bindings)

	if len(ret) == 0 {
		panic("no return value specified for Unbind")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []types.BoundContract) error); ok {
		r0 = rf(ctx, bindings)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContractReader_Unbind_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Unbind'
type ContractReader_Unbind_Call struct {
	*mock.Call
}

// Unbind is a helper method to define mock.On call
//   - ctx context.Context
//   - bindings []types.BoundContract
func (_e *ContractReader_Expecter) Unbind(ctx interface{}, bindings interface{}) *ContractReader_Unbind_Call {
	return &ContractReader_Unbind_Call{Call: _e.mock.On("Unbind", ctx, bindings)}
}

func (_c *ContractReader_Unbind_Call) Run(run func(ctx context.Context, bindings []types.BoundContract)) *ContractReader_Unbind_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]types.BoundContract))
	})
	return _c
}

func (_c *ContractReader_Unbind_Call) Return(_a0 error) *ContractReader_Unbind_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractReader_Unbind_Call) RunAndReturn(run func(context.Context, []types.BoundContract) error) *ContractReader_Unbind_Call {
	_c.Call.Return(run)
	return _c
}

// NewContractReader creates a new instance of ContractReader. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewContractReader(t interface {
	mock.TestingT
	Cleanup(func())
}) *ContractReader {
	mock := &ContractReader{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
