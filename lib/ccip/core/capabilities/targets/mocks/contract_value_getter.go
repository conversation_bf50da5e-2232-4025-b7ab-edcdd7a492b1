// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	primitives "github.com/smartcontractkit/chainlink-common/pkg/types/query/primitives"
	mock "github.com/stretchr/testify/mock"

	types "github.com/smartcontractkit/chainlink-common/pkg/types"
)

// ContractValueGetter is an autogenerated mock type for the ContractValueGetter type
type ContractValueGetter struct {
	mock.Mock
}

type ContractValueGetter_Expecter struct {
	mock *mock.Mock
}

func (_m *ContractValueGetter) EXPECT() *ContractValueGetter_Expecter {
	return &ContractValueGetter_Expecter{mock: &_m.Mock}
}

// Bind provides a mock function with given fields: _a0, _a1
func (_m *ContractValueGetter) Bind(_a0 context.Context, _a1 []types.BoundContract) error {
	ret := _m.Called(_a0, _a1)

	if len(ret) == 0 {
		panic("no return value specified for Bind")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []types.BoundContract) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContractValueGetter_Bind_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Bind'
type ContractValueGetter_Bind_Call struct {
	*mock.Call
}

// Bind is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 []types.BoundContract
func (_e *ContractValueGetter_Expecter) Bind(_a0 interface{}, _a1 interface{}) *ContractValueGetter_Bind_Call {
	return &ContractValueGetter_Bind_Call{Call: _e.mock.On("Bind", _a0, _a1)}
}

func (_c *ContractValueGetter_Bind_Call) Run(run func(_a0 context.Context, _a1 []types.BoundContract)) *ContractValueGetter_Bind_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]types.BoundContract))
	})
	return _c
}

func (_c *ContractValueGetter_Bind_Call) Return(_a0 error) *ContractValueGetter_Bind_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractValueGetter_Bind_Call) RunAndReturn(run func(context.Context, []types.BoundContract) error) *ContractValueGetter_Bind_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestValue provides a mock function with given fields: _a0, _a1, _a2, _a3, _a4
func (_m *ContractValueGetter) GetLatestValue(_a0 context.Context, _a1 string, _a2 primitives.ConfidenceLevel, _a3 interface{}, _a4 interface{}) error {
	ret := _m.Called(_a0, _a1, _a2, _a3, _a4)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestValue")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, primitives.ConfidenceLevel, interface{}, interface{}) error); ok {
		r0 = rf(_a0, _a1, _a2, _a3, _a4)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ContractValueGetter_GetLatestValue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestValue'
type ContractValueGetter_GetLatestValue_Call struct {
	*mock.Call
}

// GetLatestValue is a helper method to define mock.On call
//   - _a0 context.Context
//   - _a1 string
//   - _a2 primitives.ConfidenceLevel
//   - _a3 interface{}
//   - _a4 interface{}
func (_e *ContractValueGetter_Expecter) GetLatestValue(_a0 interface{}, _a1 interface{}, _a2 interface{}, _a3 interface{}, _a4 interface{}) *ContractValueGetter_GetLatestValue_Call {
	return &ContractValueGetter_GetLatestValue_Call{Call: _e.mock.On("GetLatestValue", _a0, _a1, _a2, _a3, _a4)}
}

func (_c *ContractValueGetter_GetLatestValue_Call) Run(run func(_a0 context.Context, _a1 string, _a2 primitives.ConfidenceLevel, _a3 interface{}, _a4 interface{})) *ContractValueGetter_GetLatestValue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(primitives.ConfidenceLevel), args[3].(interface{}), args[4].(interface{}))
	})
	return _c
}

func (_c *ContractValueGetter_GetLatestValue_Call) Return(_a0 error) *ContractValueGetter_GetLatestValue_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ContractValueGetter_GetLatestValue_Call) RunAndReturn(run func(context.Context, string, primitives.ConfidenceLevel, interface{}, interface{}) error) *ContractValueGetter_GetLatestValue_Call {
	_c.Call.Return(run)
	return _c
}

// NewContractValueGetter creates a new instance of ContractValueGetter. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewContractValueGetter(t interface {
	mock.TestingT
	Cleanup(func())
}) *ContractValueGetter {
	mock := &ContractValueGetter{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
