// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package ocr2dr_oracle

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
	"github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

type IFunctionsBillingRegistryRequestBilling struct {
	SubscriptionId uint64
	Client         common.Address
	GasLimit       uint32
	GasPrice       *big.Int
}

var OCR2DROracleMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AlreadySet\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CannotSelfTransfer\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EmptyBillingRegistry\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EmptyPublicKey\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EmptyRequestData\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"EmptySendersList\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InconsistentReportData\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotAllowedToSetSenders\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NotProposedOwner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyCallableByOwner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OwnerMustBeSet\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReportInvalid\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedPublicKeyChange\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedSender\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AuthorizedSendersActive\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"senders\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"changedBy\",\"type\":\"address\"}],\"name\":\"AuthorizedSendersChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"AuthorizedSendersDeactive\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"previousConfigBlockNumber\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"configCount\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"signers\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"transmitters\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"f\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"onchainConfig\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"version\",\"type\":\"uint8\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"requestId\",\"type\":\"bytes32\"}],\"name\":\"InvalidRequestID\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"requestId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"requestingContract\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"requestInitiator\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"subscriptionId\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"subscriptionOwner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"OracleRequest\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"requestId\",\"type\":\"bytes32\"}],\"name\":\"OracleResponse\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"requestId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"transmitter\",\"type\":\"address\"}],\"name\":\"ResponseTransmitted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"name\":\"Transmitted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"requestId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"reason\",\"type\":\"string\"}],\"name\":\"UserCallbackError\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"requestId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"lowLevelData\",\"type\":\"bytes\"}],\"name\":\"UserCallbackRawError\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"activateAuthorizedReceiver\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"senders\",\"type\":\"address[]\"}],\"name\":\"addAuthorizedSenders\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"authorizedReceiverActive\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"deactivateAuthorizedReceiver\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"node\",\"type\":\"address\"}],\"name\":\"deleteNodePublicKey\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint64\",\"name\":\"subscriptionId\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"internalType\":\"uint32\",\"name\":\"gasLimit\",\"type\":\"uint32\"},{\"internalType\":\"uint256\",\"name\":\"gasPrice\",\"type\":\"uint256\"}],\"name\":\"estimateCost\",\"outputs\":[{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAllNodePublicKeys\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"},{\"internalType\":\"bytes[]\",\"name\":\"\",\"type\":\"bytes[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getAuthorizedSenders\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDONPublicKey\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getRegistry\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"uint64\",\"name\":\"subscriptionId\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"client\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"gasLimit\",\"type\":\"uint32\"},{\"internalType\":\"uint256\",\"name\":\"gasPrice\",\"type\":\"uint256\"}],\"internalType\":\"structIFunctionsBillingRegistry.RequestBilling\",\"name\":\"\",\"type\":\"tuple\"}],\"name\":\"getRequiredFee\",\"outputs\":[{\"internalType\":\"uint96\",\"name\":\"\",\"type\":\"uint96\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getThresholdPublicKey\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"isAuthorizedSender\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestConfigDetails\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"configCount\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"blockNumber\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestConfigDigestAndEpoch\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"scanLogs\",\"type\":\"bool\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"senders\",\"type\":\"address[]\"}],\"name\":\"removeAuthorizedSenders\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint64\",\"name\":\"subscriptionId\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"},{\"internalType\":\"uint32\",\"name\":\"gasLimit\",\"type\":\"uint32\"}],\"name\":\"sendRequest\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"_signers\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"_transmitters\",\"type\":\"address[]\"},{\"internalType\":\"uint8\",\"name\":\"_f\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"_onchainConfig\",\"type\":\"bytes\"},{\"internalType\":\"uint64\",\"name\":\"_offchainConfigVersion\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"_offchainConfig\",\"type\":\"bytes\"}],\"name\":\"setConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"donPublicKey\",\"type\":\"bytes\"}],\"name\":\"setDONPublicKey\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"node\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"publicKey\",\"type\":\"bytes\"}],\"name\":\"setNodePublicKey\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"registryAddress\",\"type\":\"address\"}],\"name\":\"setRegistry\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"thresholdPublicKey\",\"type\":\"bytes\"}],\"name\":\"setThresholdPublicKey\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[3]\",\"name\":\"reportContext\",\"type\":\"bytes32[3]\"},{\"internalType\":\"bytes\",\"name\":\"report\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"rs\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"ss\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32\",\"name\":\"rawVs\",\"type\":\"bytes32\"}],\"name\":\"transmit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"transmitters\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"typeAndVersion\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"}]",
	Bin: "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",
}

var OCR2DROracleABI = OCR2DROracleMetaData.ABI

var OCR2DROracleBin = OCR2DROracleMetaData.Bin

func DeployOCR2DROracle(auth *bind.TransactOpts, backend bind.ContractBackend) (common.Address, *types.Transaction, *OCR2DROracle, error) {
	parsed, err := OCR2DROracleMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(OCR2DROracleBin), backend)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &OCR2DROracle{OCR2DROracleCaller: OCR2DROracleCaller{contract: contract}, OCR2DROracleTransactor: OCR2DROracleTransactor{contract: contract}, OCR2DROracleFilterer: OCR2DROracleFilterer{contract: contract}}, nil
}

type OCR2DROracle struct {
	address common.Address
	abi     abi.ABI
	OCR2DROracleCaller
	OCR2DROracleTransactor
	OCR2DROracleFilterer
}

type OCR2DROracleCaller struct {
	contract *bind.BoundContract
}

type OCR2DROracleTransactor struct {
	contract *bind.BoundContract
}

type OCR2DROracleFilterer struct {
	contract *bind.BoundContract
}

type OCR2DROracleSession struct {
	Contract     *OCR2DROracle
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type OCR2DROracleCallerSession struct {
	Contract *OCR2DROracleCaller
	CallOpts bind.CallOpts
}

type OCR2DROracleTransactorSession struct {
	Contract     *OCR2DROracleTransactor
	TransactOpts bind.TransactOpts
}

type OCR2DROracleRaw struct {
	Contract *OCR2DROracle
}

type OCR2DROracleCallerRaw struct {
	Contract *OCR2DROracleCaller
}

type OCR2DROracleTransactorRaw struct {
	Contract *OCR2DROracleTransactor
}

func NewOCR2DROracle(address common.Address, backend bind.ContractBackend) (*OCR2DROracle, error) {
	abi, err := abi.JSON(strings.NewReader(OCR2DROracleABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindOCR2DROracle(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracle{address: address, abi: abi, OCR2DROracleCaller: OCR2DROracleCaller{contract: contract}, OCR2DROracleTransactor: OCR2DROracleTransactor{contract: contract}, OCR2DROracleFilterer: OCR2DROracleFilterer{contract: contract}}, nil
}

func NewOCR2DROracleCaller(address common.Address, caller bind.ContractCaller) (*OCR2DROracleCaller, error) {
	contract, err := bindOCR2DROracle(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleCaller{contract: contract}, nil
}

func NewOCR2DROracleTransactor(address common.Address, transactor bind.ContractTransactor) (*OCR2DROracleTransactor, error) {
	contract, err := bindOCR2DROracle(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleTransactor{contract: contract}, nil
}

func NewOCR2DROracleFilterer(address common.Address, filterer bind.ContractFilterer) (*OCR2DROracleFilterer, error) {
	contract, err := bindOCR2DROracle(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleFilterer{contract: contract}, nil
}

func bindOCR2DROracle(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := OCR2DROracleMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_OCR2DROracle *OCR2DROracleRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _OCR2DROracle.Contract.OCR2DROracleCaller.contract.Call(opts, result, method, params...)
}

func (_OCR2DROracle *OCR2DROracleRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.OCR2DROracleTransactor.contract.Transfer(opts)
}

func (_OCR2DROracle *OCR2DROracleRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.OCR2DROracleTransactor.contract.Transact(opts, method, params...)
}

func (_OCR2DROracle *OCR2DROracleCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _OCR2DROracle.Contract.contract.Call(opts, result, method, params...)
}

func (_OCR2DROracle *OCR2DROracleTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.contract.Transfer(opts)
}

func (_OCR2DROracle *OCR2DROracleTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.contract.Transact(opts, method, params...)
}

func (_OCR2DROracle *OCR2DROracleCaller) AuthorizedReceiverActive(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "authorizedReceiverActive")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) AuthorizedReceiverActive() (bool, error) {
	return _OCR2DROracle.Contract.AuthorizedReceiverActive(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) AuthorizedReceiverActive() (bool, error) {
	return _OCR2DROracle.Contract.AuthorizedReceiverActive(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) EstimateCost(opts *bind.CallOpts, subscriptionId uint64, data []byte, gasLimit uint32, gasPrice *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "estimateCost", subscriptionId, data, gasLimit, gasPrice)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) EstimateCost(subscriptionId uint64, data []byte, gasLimit uint32, gasPrice *big.Int) (*big.Int, error) {
	return _OCR2DROracle.Contract.EstimateCost(&_OCR2DROracle.CallOpts, subscriptionId, data, gasLimit, gasPrice)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) EstimateCost(subscriptionId uint64, data []byte, gasLimit uint32, gasPrice *big.Int) (*big.Int, error) {
	return _OCR2DROracle.Contract.EstimateCost(&_OCR2DROracle.CallOpts, subscriptionId, data, gasLimit, gasPrice)
}

func (_OCR2DROracle *OCR2DROracleCaller) GetAllNodePublicKeys(opts *bind.CallOpts) ([]common.Address, [][]byte, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "getAllNodePublicKeys")

	if err != nil {
		return *new([]common.Address), *new([][]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([]common.Address)).(*[]common.Address)
	out1 := *abi.ConvertType(out[1], new([][]byte)).(*[][]byte)

	return out0, out1, err

}

func (_OCR2DROracle *OCR2DROracleSession) GetAllNodePublicKeys() ([]common.Address, [][]byte, error) {
	return _OCR2DROracle.Contract.GetAllNodePublicKeys(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) GetAllNodePublicKeys() ([]common.Address, [][]byte, error) {
	return _OCR2DROracle.Contract.GetAllNodePublicKeys(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) GetAuthorizedSenders(opts *bind.CallOpts) ([]common.Address, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "getAuthorizedSenders")

	if err != nil {
		return *new([]common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new([]common.Address)).(*[]common.Address)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) GetAuthorizedSenders() ([]common.Address, error) {
	return _OCR2DROracle.Contract.GetAuthorizedSenders(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) GetAuthorizedSenders() ([]common.Address, error) {
	return _OCR2DROracle.Contract.GetAuthorizedSenders(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) GetDONPublicKey(opts *bind.CallOpts) ([]byte, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "getDONPublicKey")

	if err != nil {
		return *new([]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([]byte)).(*[]byte)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) GetDONPublicKey() ([]byte, error) {
	return _OCR2DROracle.Contract.GetDONPublicKey(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) GetDONPublicKey() ([]byte, error) {
	return _OCR2DROracle.Contract.GetDONPublicKey(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) GetRegistry(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "getRegistry")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) GetRegistry() (common.Address, error) {
	return _OCR2DROracle.Contract.GetRegistry(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) GetRegistry() (common.Address, error) {
	return _OCR2DROracle.Contract.GetRegistry(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) GetRequiredFee(opts *bind.CallOpts, arg0 []byte, arg1 IFunctionsBillingRegistryRequestBilling) (*big.Int, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "getRequiredFee", arg0, arg1)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) GetRequiredFee(arg0 []byte, arg1 IFunctionsBillingRegistryRequestBilling) (*big.Int, error) {
	return _OCR2DROracle.Contract.GetRequiredFee(&_OCR2DROracle.CallOpts, arg0, arg1)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) GetRequiredFee(arg0 []byte, arg1 IFunctionsBillingRegistryRequestBilling) (*big.Int, error) {
	return _OCR2DROracle.Contract.GetRequiredFee(&_OCR2DROracle.CallOpts, arg0, arg1)
}

func (_OCR2DROracle *OCR2DROracleCaller) GetThresholdPublicKey(opts *bind.CallOpts) ([]byte, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "getThresholdPublicKey")

	if err != nil {
		return *new([]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([]byte)).(*[]byte)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) GetThresholdPublicKey() ([]byte, error) {
	return _OCR2DROracle.Contract.GetThresholdPublicKey(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) GetThresholdPublicKey() ([]byte, error) {
	return _OCR2DROracle.Contract.GetThresholdPublicKey(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) IsAuthorizedSender(opts *bind.CallOpts, sender common.Address) (bool, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "isAuthorizedSender", sender)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) IsAuthorizedSender(sender common.Address) (bool, error) {
	return _OCR2DROracle.Contract.IsAuthorizedSender(&_OCR2DROracle.CallOpts, sender)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) IsAuthorizedSender(sender common.Address) (bool, error) {
	return _OCR2DROracle.Contract.IsAuthorizedSender(&_OCR2DROracle.CallOpts, sender)
}

func (_OCR2DROracle *OCR2DROracleCaller) LatestConfigDetails(opts *bind.CallOpts) (LatestConfigDetails,

	error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "latestConfigDetails")

	outstruct := new(LatestConfigDetails)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ConfigCount = *abi.ConvertType(out[0], new(uint32)).(*uint32)
	outstruct.BlockNumber = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.ConfigDigest = *abi.ConvertType(out[2], new([32]byte)).(*[32]byte)

	return *outstruct, err

}

func (_OCR2DROracle *OCR2DROracleSession) LatestConfigDetails() (LatestConfigDetails,

	error) {
	return _OCR2DROracle.Contract.LatestConfigDetails(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) LatestConfigDetails() (LatestConfigDetails,

	error) {
	return _OCR2DROracle.Contract.LatestConfigDetails(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) LatestConfigDigestAndEpoch(opts *bind.CallOpts) (LatestConfigDigestAndEpoch,

	error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "latestConfigDigestAndEpoch")

	outstruct := new(LatestConfigDigestAndEpoch)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ScanLogs = *abi.ConvertType(out[0], new(bool)).(*bool)
	outstruct.ConfigDigest = *abi.ConvertType(out[1], new([32]byte)).(*[32]byte)
	outstruct.Epoch = *abi.ConvertType(out[2], new(uint32)).(*uint32)

	return *outstruct, err

}

func (_OCR2DROracle *OCR2DROracleSession) LatestConfigDigestAndEpoch() (LatestConfigDigestAndEpoch,

	error) {
	return _OCR2DROracle.Contract.LatestConfigDigestAndEpoch(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) LatestConfigDigestAndEpoch() (LatestConfigDigestAndEpoch,

	error) {
	return _OCR2DROracle.Contract.LatestConfigDigestAndEpoch(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) Owner() (common.Address, error) {
	return _OCR2DROracle.Contract.Owner(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) Owner() (common.Address, error) {
	return _OCR2DROracle.Contract.Owner(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) Transmitters(opts *bind.CallOpts) ([]common.Address, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "transmitters")

	if err != nil {
		return *new([]common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new([]common.Address)).(*[]common.Address)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) Transmitters() ([]common.Address, error) {
	return _OCR2DROracle.Contract.Transmitters(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) Transmitters() ([]common.Address, error) {
	return _OCR2DROracle.Contract.Transmitters(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _OCR2DROracle.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_OCR2DROracle *OCR2DROracleSession) TypeAndVersion() (string, error) {
	return _OCR2DROracle.Contract.TypeAndVersion(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleCallerSession) TypeAndVersion() (string, error) {
	return _OCR2DROracle.Contract.TypeAndVersion(&_OCR2DROracle.CallOpts)
}

func (_OCR2DROracle *OCR2DROracleTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "acceptOwnership")
}

func (_OCR2DROracle *OCR2DROracleSession) AcceptOwnership() (*types.Transaction, error) {
	return _OCR2DROracle.Contract.AcceptOwnership(&_OCR2DROracle.TransactOpts)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _OCR2DROracle.Contract.AcceptOwnership(&_OCR2DROracle.TransactOpts)
}

func (_OCR2DROracle *OCR2DROracleTransactor) ActivateAuthorizedReceiver(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "activateAuthorizedReceiver")
}

func (_OCR2DROracle *OCR2DROracleSession) ActivateAuthorizedReceiver() (*types.Transaction, error) {
	return _OCR2DROracle.Contract.ActivateAuthorizedReceiver(&_OCR2DROracle.TransactOpts)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) ActivateAuthorizedReceiver() (*types.Transaction, error) {
	return _OCR2DROracle.Contract.ActivateAuthorizedReceiver(&_OCR2DROracle.TransactOpts)
}

func (_OCR2DROracle *OCR2DROracleTransactor) AddAuthorizedSenders(opts *bind.TransactOpts, senders []common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "addAuthorizedSenders", senders)
}

func (_OCR2DROracle *OCR2DROracleSession) AddAuthorizedSenders(senders []common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.AddAuthorizedSenders(&_OCR2DROracle.TransactOpts, senders)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) AddAuthorizedSenders(senders []common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.AddAuthorizedSenders(&_OCR2DROracle.TransactOpts, senders)
}

func (_OCR2DROracle *OCR2DROracleTransactor) DeactivateAuthorizedReceiver(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "deactivateAuthorizedReceiver")
}

func (_OCR2DROracle *OCR2DROracleSession) DeactivateAuthorizedReceiver() (*types.Transaction, error) {
	return _OCR2DROracle.Contract.DeactivateAuthorizedReceiver(&_OCR2DROracle.TransactOpts)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) DeactivateAuthorizedReceiver() (*types.Transaction, error) {
	return _OCR2DROracle.Contract.DeactivateAuthorizedReceiver(&_OCR2DROracle.TransactOpts)
}

func (_OCR2DROracle *OCR2DROracleTransactor) DeleteNodePublicKey(opts *bind.TransactOpts, node common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "deleteNodePublicKey", node)
}

func (_OCR2DROracle *OCR2DROracleSession) DeleteNodePublicKey(node common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.DeleteNodePublicKey(&_OCR2DROracle.TransactOpts, node)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) DeleteNodePublicKey(node common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.DeleteNodePublicKey(&_OCR2DROracle.TransactOpts, node)
}

func (_OCR2DROracle *OCR2DROracleTransactor) Initialize(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "initialize")
}

func (_OCR2DROracle *OCR2DROracleSession) Initialize() (*types.Transaction, error) {
	return _OCR2DROracle.Contract.Initialize(&_OCR2DROracle.TransactOpts)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) Initialize() (*types.Transaction, error) {
	return _OCR2DROracle.Contract.Initialize(&_OCR2DROracle.TransactOpts)
}

func (_OCR2DROracle *OCR2DROracleTransactor) RemoveAuthorizedSenders(opts *bind.TransactOpts, senders []common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "removeAuthorizedSenders", senders)
}

func (_OCR2DROracle *OCR2DROracleSession) RemoveAuthorizedSenders(senders []common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.RemoveAuthorizedSenders(&_OCR2DROracle.TransactOpts, senders)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) RemoveAuthorizedSenders(senders []common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.RemoveAuthorizedSenders(&_OCR2DROracle.TransactOpts, senders)
}

func (_OCR2DROracle *OCR2DROracleTransactor) SendRequest(opts *bind.TransactOpts, subscriptionId uint64, data []byte, gasLimit uint32) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "sendRequest", subscriptionId, data, gasLimit)
}

func (_OCR2DROracle *OCR2DROracleSession) SendRequest(subscriptionId uint64, data []byte, gasLimit uint32) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SendRequest(&_OCR2DROracle.TransactOpts, subscriptionId, data, gasLimit)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) SendRequest(subscriptionId uint64, data []byte, gasLimit uint32) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SendRequest(&_OCR2DROracle.TransactOpts, subscriptionId, data, gasLimit)
}

func (_OCR2DROracle *OCR2DROracleTransactor) SetConfig(opts *bind.TransactOpts, _signers []common.Address, _transmitters []common.Address, _f uint8, _onchainConfig []byte, _offchainConfigVersion uint64, _offchainConfig []byte) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "setConfig", _signers, _transmitters, _f, _onchainConfig, _offchainConfigVersion, _offchainConfig)
}

func (_OCR2DROracle *OCR2DROracleSession) SetConfig(_signers []common.Address, _transmitters []common.Address, _f uint8, _onchainConfig []byte, _offchainConfigVersion uint64, _offchainConfig []byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetConfig(&_OCR2DROracle.TransactOpts, _signers, _transmitters, _f, _onchainConfig, _offchainConfigVersion, _offchainConfig)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) SetConfig(_signers []common.Address, _transmitters []common.Address, _f uint8, _onchainConfig []byte, _offchainConfigVersion uint64, _offchainConfig []byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetConfig(&_OCR2DROracle.TransactOpts, _signers, _transmitters, _f, _onchainConfig, _offchainConfigVersion, _offchainConfig)
}

func (_OCR2DROracle *OCR2DROracleTransactor) SetDONPublicKey(opts *bind.TransactOpts, donPublicKey []byte) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "setDONPublicKey", donPublicKey)
}

func (_OCR2DROracle *OCR2DROracleSession) SetDONPublicKey(donPublicKey []byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetDONPublicKey(&_OCR2DROracle.TransactOpts, donPublicKey)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) SetDONPublicKey(donPublicKey []byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetDONPublicKey(&_OCR2DROracle.TransactOpts, donPublicKey)
}

func (_OCR2DROracle *OCR2DROracleTransactor) SetNodePublicKey(opts *bind.TransactOpts, node common.Address, publicKey []byte) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "setNodePublicKey", node, publicKey)
}

func (_OCR2DROracle *OCR2DROracleSession) SetNodePublicKey(node common.Address, publicKey []byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetNodePublicKey(&_OCR2DROracle.TransactOpts, node, publicKey)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) SetNodePublicKey(node common.Address, publicKey []byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetNodePublicKey(&_OCR2DROracle.TransactOpts, node, publicKey)
}

func (_OCR2DROracle *OCR2DROracleTransactor) SetRegistry(opts *bind.TransactOpts, registryAddress common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "setRegistry", registryAddress)
}

func (_OCR2DROracle *OCR2DROracleSession) SetRegistry(registryAddress common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetRegistry(&_OCR2DROracle.TransactOpts, registryAddress)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) SetRegistry(registryAddress common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetRegistry(&_OCR2DROracle.TransactOpts, registryAddress)
}

func (_OCR2DROracle *OCR2DROracleTransactor) SetThresholdPublicKey(opts *bind.TransactOpts, thresholdPublicKey []byte) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "setThresholdPublicKey", thresholdPublicKey)
}

func (_OCR2DROracle *OCR2DROracleSession) SetThresholdPublicKey(thresholdPublicKey []byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetThresholdPublicKey(&_OCR2DROracle.TransactOpts, thresholdPublicKey)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) SetThresholdPublicKey(thresholdPublicKey []byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.SetThresholdPublicKey(&_OCR2DROracle.TransactOpts, thresholdPublicKey)
}

func (_OCR2DROracle *OCR2DROracleTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "transferOwnership", to)
}

func (_OCR2DROracle *OCR2DROracleSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.TransferOwnership(&_OCR2DROracle.TransactOpts, to)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.TransferOwnership(&_OCR2DROracle.TransactOpts, to)
}

func (_OCR2DROracle *OCR2DROracleTransactor) Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _OCR2DROracle.contract.Transact(opts, "transmit", reportContext, report, rs, ss, rawVs)
}

func (_OCR2DROracle *OCR2DROracleSession) Transmit(reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.Transmit(&_OCR2DROracle.TransactOpts, reportContext, report, rs, ss, rawVs)
}

func (_OCR2DROracle *OCR2DROracleTransactorSession) Transmit(reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _OCR2DROracle.Contract.Transmit(&_OCR2DROracle.TransactOpts, reportContext, report, rs, ss, rawVs)
}

type OCR2DROracleAuthorizedSendersActiveIterator struct {
	Event *OCR2DROracleAuthorizedSendersActive

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleAuthorizedSendersActiveIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleAuthorizedSendersActive)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleAuthorizedSendersActive)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleAuthorizedSendersActiveIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleAuthorizedSendersActiveIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleAuthorizedSendersActive struct {
	Account common.Address
	Raw     types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterAuthorizedSendersActive(opts *bind.FilterOpts) (*OCR2DROracleAuthorizedSendersActiveIterator, error) {

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "AuthorizedSendersActive")
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleAuthorizedSendersActiveIterator{contract: _OCR2DROracle.contract, event: "AuthorizedSendersActive", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchAuthorizedSendersActive(opts *bind.WatchOpts, sink chan<- *OCR2DROracleAuthorizedSendersActive) (event.Subscription, error) {

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "AuthorizedSendersActive")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleAuthorizedSendersActive)
				if err := _OCR2DROracle.contract.UnpackLog(event, "AuthorizedSendersActive", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseAuthorizedSendersActive(log types.Log) (*OCR2DROracleAuthorizedSendersActive, error) {
	event := new(OCR2DROracleAuthorizedSendersActive)
	if err := _OCR2DROracle.contract.UnpackLog(event, "AuthorizedSendersActive", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleAuthorizedSendersChangedIterator struct {
	Event *OCR2DROracleAuthorizedSendersChanged

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleAuthorizedSendersChangedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleAuthorizedSendersChanged)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleAuthorizedSendersChanged)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleAuthorizedSendersChangedIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleAuthorizedSendersChangedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleAuthorizedSendersChanged struct {
	Senders   []common.Address
	ChangedBy common.Address
	Raw       types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterAuthorizedSendersChanged(opts *bind.FilterOpts) (*OCR2DROracleAuthorizedSendersChangedIterator, error) {

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "AuthorizedSendersChanged")
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleAuthorizedSendersChangedIterator{contract: _OCR2DROracle.contract, event: "AuthorizedSendersChanged", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchAuthorizedSendersChanged(opts *bind.WatchOpts, sink chan<- *OCR2DROracleAuthorizedSendersChanged) (event.Subscription, error) {

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "AuthorizedSendersChanged")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleAuthorizedSendersChanged)
				if err := _OCR2DROracle.contract.UnpackLog(event, "AuthorizedSendersChanged", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseAuthorizedSendersChanged(log types.Log) (*OCR2DROracleAuthorizedSendersChanged, error) {
	event := new(OCR2DROracleAuthorizedSendersChanged)
	if err := _OCR2DROracle.contract.UnpackLog(event, "AuthorizedSendersChanged", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleAuthorizedSendersDeactiveIterator struct {
	Event *OCR2DROracleAuthorizedSendersDeactive

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleAuthorizedSendersDeactiveIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleAuthorizedSendersDeactive)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleAuthorizedSendersDeactive)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleAuthorizedSendersDeactiveIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleAuthorizedSendersDeactiveIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleAuthorizedSendersDeactive struct {
	Account common.Address
	Raw     types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterAuthorizedSendersDeactive(opts *bind.FilterOpts) (*OCR2DROracleAuthorizedSendersDeactiveIterator, error) {

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "AuthorizedSendersDeactive")
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleAuthorizedSendersDeactiveIterator{contract: _OCR2DROracle.contract, event: "AuthorizedSendersDeactive", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchAuthorizedSendersDeactive(opts *bind.WatchOpts, sink chan<- *OCR2DROracleAuthorizedSendersDeactive) (event.Subscription, error) {

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "AuthorizedSendersDeactive")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleAuthorizedSendersDeactive)
				if err := _OCR2DROracle.contract.UnpackLog(event, "AuthorizedSendersDeactive", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseAuthorizedSendersDeactive(log types.Log) (*OCR2DROracleAuthorizedSendersDeactive, error) {
	event := new(OCR2DROracleAuthorizedSendersDeactive)
	if err := _OCR2DROracle.contract.UnpackLog(event, "AuthorizedSendersDeactive", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleConfigSetIterator struct {
	Event *OCR2DROracleConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleConfigSetIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleConfigSet struct {
	PreviousConfigBlockNumber uint32
	ConfigDigest              [32]byte
	ConfigCount               uint64
	Signers                   []common.Address
	Transmitters              []common.Address
	F                         uint8
	OnchainConfig             []byte
	OffchainConfigVersion     uint64
	OffchainConfig            []byte
	Raw                       types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterConfigSet(opts *bind.FilterOpts) (*OCR2DROracleConfigSetIterator, error) {

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleConfigSetIterator{contract: _OCR2DROracle.contract, event: "ConfigSet", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *OCR2DROracleConfigSet) (event.Subscription, error) {

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleConfigSet)
				if err := _OCR2DROracle.contract.UnpackLog(event, "ConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseConfigSet(log types.Log) (*OCR2DROracleConfigSet, error) {
	event := new(OCR2DROracleConfigSet)
	if err := _OCR2DROracle.contract.UnpackLog(event, "ConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleInitializedIterator struct {
	Event *OCR2DROracleInitialized

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleInitializedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleInitialized)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleInitialized)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleInitializedIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleInitializedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleInitialized struct {
	Version uint8
	Raw     types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterInitialized(opts *bind.FilterOpts) (*OCR2DROracleInitializedIterator, error) {

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "Initialized")
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleInitializedIterator{contract: _OCR2DROracle.contract, event: "Initialized", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchInitialized(opts *bind.WatchOpts, sink chan<- *OCR2DROracleInitialized) (event.Subscription, error) {

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "Initialized")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleInitialized)
				if err := _OCR2DROracle.contract.UnpackLog(event, "Initialized", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseInitialized(log types.Log) (*OCR2DROracleInitialized, error) {
	event := new(OCR2DROracleInitialized)
	if err := _OCR2DROracle.contract.UnpackLog(event, "Initialized", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleInvalidRequestIDIterator struct {
	Event *OCR2DROracleInvalidRequestID

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleInvalidRequestIDIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleInvalidRequestID)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleInvalidRequestID)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleInvalidRequestIDIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleInvalidRequestIDIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleInvalidRequestID struct {
	RequestId [32]byte
	Raw       types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterInvalidRequestID(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleInvalidRequestIDIterator, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "InvalidRequestID", requestIdRule)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleInvalidRequestIDIterator{contract: _OCR2DROracle.contract, event: "InvalidRequestID", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchInvalidRequestID(opts *bind.WatchOpts, sink chan<- *OCR2DROracleInvalidRequestID, requestId [][32]byte) (event.Subscription, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "InvalidRequestID", requestIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleInvalidRequestID)
				if err := _OCR2DROracle.contract.UnpackLog(event, "InvalidRequestID", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseInvalidRequestID(log types.Log) (*OCR2DROracleInvalidRequestID, error) {
	event := new(OCR2DROracleInvalidRequestID)
	if err := _OCR2DROracle.contract.UnpackLog(event, "InvalidRequestID", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleOracleRequestIterator struct {
	Event *OCR2DROracleOracleRequest

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleOracleRequestIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleOracleRequest)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleOracleRequest)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleOracleRequestIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleOracleRequestIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleOracleRequest struct {
	RequestId          [32]byte
	RequestingContract common.Address
	RequestInitiator   common.Address
	SubscriptionId     uint64
	SubscriptionOwner  common.Address
	Data               []byte
	Raw                types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterOracleRequest(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleOracleRequestIterator, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "OracleRequest", requestIdRule)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleOracleRequestIterator{contract: _OCR2DROracle.contract, event: "OracleRequest", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchOracleRequest(opts *bind.WatchOpts, sink chan<- *OCR2DROracleOracleRequest, requestId [][32]byte) (event.Subscription, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "OracleRequest", requestIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleOracleRequest)
				if err := _OCR2DROracle.contract.UnpackLog(event, "OracleRequest", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseOracleRequest(log types.Log) (*OCR2DROracleOracleRequest, error) {
	event := new(OCR2DROracleOracleRequest)
	if err := _OCR2DROracle.contract.UnpackLog(event, "OracleRequest", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleOracleResponseIterator struct {
	Event *OCR2DROracleOracleResponse

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleOracleResponseIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleOracleResponse)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleOracleResponse)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleOracleResponseIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleOracleResponseIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleOracleResponse struct {
	RequestId [32]byte
	Raw       types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterOracleResponse(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleOracleResponseIterator, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "OracleResponse", requestIdRule)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleOracleResponseIterator{contract: _OCR2DROracle.contract, event: "OracleResponse", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchOracleResponse(opts *bind.WatchOpts, sink chan<- *OCR2DROracleOracleResponse, requestId [][32]byte) (event.Subscription, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "OracleResponse", requestIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleOracleResponse)
				if err := _OCR2DROracle.contract.UnpackLog(event, "OracleResponse", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseOracleResponse(log types.Log) (*OCR2DROracleOracleResponse, error) {
	event := new(OCR2DROracleOracleResponse)
	if err := _OCR2DROracle.contract.UnpackLog(event, "OracleResponse", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleOwnershipTransferRequestedIterator struct {
	Event *OCR2DROracleOwnershipTransferRequested

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleOwnershipTransferRequestedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*OCR2DROracleOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleOwnershipTransferRequestedIterator{contract: _OCR2DROracle.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *OCR2DROracleOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleOwnershipTransferRequested)
				if err := _OCR2DROracle.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseOwnershipTransferRequested(log types.Log) (*OCR2DROracleOwnershipTransferRequested, error) {
	event := new(OCR2DROracleOwnershipTransferRequested)
	if err := _OCR2DROracle.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleOwnershipTransferredIterator struct {
	Event *OCR2DROracleOwnershipTransferred

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleOwnershipTransferredIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleOwnershipTransferredIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*OCR2DROracleOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleOwnershipTransferredIterator{contract: _OCR2DROracle.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *OCR2DROracleOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleOwnershipTransferred)
				if err := _OCR2DROracle.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseOwnershipTransferred(log types.Log) (*OCR2DROracleOwnershipTransferred, error) {
	event := new(OCR2DROracleOwnershipTransferred)
	if err := _OCR2DROracle.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleResponseTransmittedIterator struct {
	Event *OCR2DROracleResponseTransmitted

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleResponseTransmittedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleResponseTransmitted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleResponseTransmitted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleResponseTransmittedIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleResponseTransmittedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleResponseTransmitted struct {
	RequestId   [32]byte
	Transmitter common.Address
	Raw         types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterResponseTransmitted(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleResponseTransmittedIterator, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "ResponseTransmitted", requestIdRule)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleResponseTransmittedIterator{contract: _OCR2DROracle.contract, event: "ResponseTransmitted", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchResponseTransmitted(opts *bind.WatchOpts, sink chan<- *OCR2DROracleResponseTransmitted, requestId [][32]byte) (event.Subscription, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "ResponseTransmitted", requestIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleResponseTransmitted)
				if err := _OCR2DROracle.contract.UnpackLog(event, "ResponseTransmitted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseResponseTransmitted(log types.Log) (*OCR2DROracleResponseTransmitted, error) {
	event := new(OCR2DROracleResponseTransmitted)
	if err := _OCR2DROracle.contract.UnpackLog(event, "ResponseTransmitted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleTransmittedIterator struct {
	Event *OCR2DROracleTransmitted

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleTransmittedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleTransmitted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleTransmitted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleTransmittedIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleTransmittedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleTransmitted struct {
	ConfigDigest [32]byte
	Epoch        uint32
	Raw          types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterTransmitted(opts *bind.FilterOpts) (*OCR2DROracleTransmittedIterator, error) {

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "Transmitted")
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleTransmittedIterator{contract: _OCR2DROracle.contract, event: "Transmitted", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchTransmitted(opts *bind.WatchOpts, sink chan<- *OCR2DROracleTransmitted) (event.Subscription, error) {

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "Transmitted")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleTransmitted)
				if err := _OCR2DROracle.contract.UnpackLog(event, "Transmitted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseTransmitted(log types.Log) (*OCR2DROracleTransmitted, error) {
	event := new(OCR2DROracleTransmitted)
	if err := _OCR2DROracle.contract.UnpackLog(event, "Transmitted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleUserCallbackErrorIterator struct {
	Event *OCR2DROracleUserCallbackError

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleUserCallbackErrorIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleUserCallbackError)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleUserCallbackError)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleUserCallbackErrorIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleUserCallbackErrorIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleUserCallbackError struct {
	RequestId [32]byte
	Reason    string
	Raw       types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterUserCallbackError(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleUserCallbackErrorIterator, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "UserCallbackError", requestIdRule)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleUserCallbackErrorIterator{contract: _OCR2DROracle.contract, event: "UserCallbackError", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchUserCallbackError(opts *bind.WatchOpts, sink chan<- *OCR2DROracleUserCallbackError, requestId [][32]byte) (event.Subscription, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "UserCallbackError", requestIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleUserCallbackError)
				if err := _OCR2DROracle.contract.UnpackLog(event, "UserCallbackError", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseUserCallbackError(log types.Log) (*OCR2DROracleUserCallbackError, error) {
	event := new(OCR2DROracleUserCallbackError)
	if err := _OCR2DROracle.contract.UnpackLog(event, "UserCallbackError", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OCR2DROracleUserCallbackRawErrorIterator struct {
	Event *OCR2DROracleUserCallbackRawError

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OCR2DROracleUserCallbackRawErrorIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OCR2DROracleUserCallbackRawError)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OCR2DROracleUserCallbackRawError)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OCR2DROracleUserCallbackRawErrorIterator) Error() error {
	return it.fail
}

func (it *OCR2DROracleUserCallbackRawErrorIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OCR2DROracleUserCallbackRawError struct {
	RequestId    [32]byte
	LowLevelData []byte
	Raw          types.Log
}

func (_OCR2DROracle *OCR2DROracleFilterer) FilterUserCallbackRawError(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleUserCallbackRawErrorIterator, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.FilterLogs(opts, "UserCallbackRawError", requestIdRule)
	if err != nil {
		return nil, err
	}
	return &OCR2DROracleUserCallbackRawErrorIterator{contract: _OCR2DROracle.contract, event: "UserCallbackRawError", logs: logs, sub: sub}, nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) WatchUserCallbackRawError(opts *bind.WatchOpts, sink chan<- *OCR2DROracleUserCallbackRawError, requestId [][32]byte) (event.Subscription, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}

	logs, sub, err := _OCR2DROracle.contract.WatchLogs(opts, "UserCallbackRawError", requestIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OCR2DROracleUserCallbackRawError)
				if err := _OCR2DROracle.contract.UnpackLog(event, "UserCallbackRawError", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OCR2DROracle *OCR2DROracleFilterer) ParseUserCallbackRawError(log types.Log) (*OCR2DROracleUserCallbackRawError, error) {
	event := new(OCR2DROracleUserCallbackRawError)
	if err := _OCR2DROracle.contract.UnpackLog(event, "UserCallbackRawError", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type LatestConfigDetails struct {
	ConfigCount  uint32
	BlockNumber  uint32
	ConfigDigest [32]byte
}
type LatestConfigDigestAndEpoch struct {
	ScanLogs     bool
	ConfigDigest [32]byte
	Epoch        uint32
}

func (_OCR2DROracle *OCR2DROracle) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _OCR2DROracle.abi.Events["AuthorizedSendersActive"].ID:
		return _OCR2DROracle.ParseAuthorizedSendersActive(log)
	case _OCR2DROracle.abi.Events["AuthorizedSendersChanged"].ID:
		return _OCR2DROracle.ParseAuthorizedSendersChanged(log)
	case _OCR2DROracle.abi.Events["AuthorizedSendersDeactive"].ID:
		return _OCR2DROracle.ParseAuthorizedSendersDeactive(log)
	case _OCR2DROracle.abi.Events["ConfigSet"].ID:
		return _OCR2DROracle.ParseConfigSet(log)
	case _OCR2DROracle.abi.Events["Initialized"].ID:
		return _OCR2DROracle.ParseInitialized(log)
	case _OCR2DROracle.abi.Events["InvalidRequestID"].ID:
		return _OCR2DROracle.ParseInvalidRequestID(log)
	case _OCR2DROracle.abi.Events["OracleRequest"].ID:
		return _OCR2DROracle.ParseOracleRequest(log)
	case _OCR2DROracle.abi.Events["OracleResponse"].ID:
		return _OCR2DROracle.ParseOracleResponse(log)
	case _OCR2DROracle.abi.Events["OwnershipTransferRequested"].ID:
		return _OCR2DROracle.ParseOwnershipTransferRequested(log)
	case _OCR2DROracle.abi.Events["OwnershipTransferred"].ID:
		return _OCR2DROracle.ParseOwnershipTransferred(log)
	case _OCR2DROracle.abi.Events["ResponseTransmitted"].ID:
		return _OCR2DROracle.ParseResponseTransmitted(log)
	case _OCR2DROracle.abi.Events["Transmitted"].ID:
		return _OCR2DROracle.ParseTransmitted(log)
	case _OCR2DROracle.abi.Events["UserCallbackError"].ID:
		return _OCR2DROracle.ParseUserCallbackError(log)
	case _OCR2DROracle.abi.Events["UserCallbackRawError"].ID:
		return _OCR2DROracle.ParseUserCallbackRawError(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (OCR2DROracleAuthorizedSendersActive) Topic() common.Hash {
	return common.HexToHash("0xae51766a982895b0c444fc99fc1a560762b464d709e6c78376c85617f7eeb5ce")
}

func (OCR2DROracleAuthorizedSendersChanged) Topic() common.Hash {
	return common.HexToHash("0xf263cfb3e4298332e776194610cf9fdc09ccb3ada8b9aa39764d882e11fbf0a0")
}

func (OCR2DROracleAuthorizedSendersDeactive) Topic() common.Hash {
	return common.HexToHash("0xea3828816a323b8d7ff49d755efd105e7719166d6c76fad97a28eee5eccc3d9a")
}

func (OCR2DROracleConfigSet) Topic() common.Hash {
	return common.HexToHash("0x1591690b8638f5fb2dbec82ac741805ac5da8b45dc5263f4875b0496fdce4e05")
}

func (OCR2DROracleInitialized) Topic() common.Hash {
	return common.HexToHash("0x7f26b83ff96e1f2b6a682f133852f6798a09c465da95921460cefb3847402498")
}

func (OCR2DROracleInvalidRequestID) Topic() common.Hash {
	return common.HexToHash("0xa1c120e327c9ad8b075793878c88d59b8934b97ae37117faa3bb21616237f7be")
}

func (OCR2DROracleOracleRequest) Topic() common.Hash {
	return common.HexToHash("0xa1ec73989d79578cd6f67d4f593ac3e0a4d1020e5c0164db52108d7ff785406c")
}

func (OCR2DROracleOracleResponse) Topic() common.Hash {
	return common.HexToHash("0x9e9bc7616d42c2835d05ae617e508454e63b30b934be8aa932ebc125e0e58a64")
}

func (OCR2DROracleOwnershipTransferRequested) Topic() common.Hash {
	return common.HexToHash("0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278")
}

func (OCR2DROracleOwnershipTransferred) Topic() common.Hash {
	return common.HexToHash("0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0")
}

func (OCR2DROracleResponseTransmitted) Topic() common.Hash {
	return common.HexToHash("0xdc941eddab34a6109ab77798299c6b1f035b125fd6f774d266ecbf9541d630a6")
}

func (OCR2DROracleTransmitted) Topic() common.Hash {
	return common.HexToHash("0xb04e63db38c49950639fa09d29872f21f5d49d614f3a969d8adf3d4b52e41a62")
}

func (OCR2DROracleUserCallbackError) Topic() common.Hash {
	return common.HexToHash("0xb2931868c372fe17a25643458add467d60ec5c51125a99b7309f41f5bcd2da6c")
}

func (OCR2DROracleUserCallbackRawError) Topic() common.Hash {
	return common.HexToHash("0xe0b838ffe6ee22a0d3acf19a85db6a41b34a1ab739e2d6c759a2e42d95bdccb2")
}

func (_OCR2DROracle *OCR2DROracle) Address() common.Address {
	return _OCR2DROracle.address
}

type OCR2DROracleInterface interface {
	AuthorizedReceiverActive(opts *bind.CallOpts) (bool, error)

	EstimateCost(opts *bind.CallOpts, subscriptionId uint64, data []byte, gasLimit uint32, gasPrice *big.Int) (*big.Int, error)

	GetAllNodePublicKeys(opts *bind.CallOpts) ([]common.Address, [][]byte, error)

	GetAuthorizedSenders(opts *bind.CallOpts) ([]common.Address, error)

	GetDONPublicKey(opts *bind.CallOpts) ([]byte, error)

	GetRegistry(opts *bind.CallOpts) (common.Address, error)

	GetRequiredFee(opts *bind.CallOpts, arg0 []byte, arg1 IFunctionsBillingRegistryRequestBilling) (*big.Int, error)

	GetThresholdPublicKey(opts *bind.CallOpts) ([]byte, error)

	IsAuthorizedSender(opts *bind.CallOpts, sender common.Address) (bool, error)

	LatestConfigDetails(opts *bind.CallOpts) (LatestConfigDetails,

		error)

	LatestConfigDigestAndEpoch(opts *bind.CallOpts) (LatestConfigDigestAndEpoch,

		error)

	Owner(opts *bind.CallOpts) (common.Address, error)

	Transmitters(opts *bind.CallOpts) ([]common.Address, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error)

	ActivateAuthorizedReceiver(opts *bind.TransactOpts) (*types.Transaction, error)

	AddAuthorizedSenders(opts *bind.TransactOpts, senders []common.Address) (*types.Transaction, error)

	DeactivateAuthorizedReceiver(opts *bind.TransactOpts) (*types.Transaction, error)

	DeleteNodePublicKey(opts *bind.TransactOpts, node common.Address) (*types.Transaction, error)

	Initialize(opts *bind.TransactOpts) (*types.Transaction, error)

	RemoveAuthorizedSenders(opts *bind.TransactOpts, senders []common.Address) (*types.Transaction, error)

	SendRequest(opts *bind.TransactOpts, subscriptionId uint64, data []byte, gasLimit uint32) (*types.Transaction, error)

	SetConfig(opts *bind.TransactOpts, _signers []common.Address, _transmitters []common.Address, _f uint8, _onchainConfig []byte, _offchainConfigVersion uint64, _offchainConfig []byte) (*types.Transaction, error)

	SetDONPublicKey(opts *bind.TransactOpts, donPublicKey []byte) (*types.Transaction, error)

	SetNodePublicKey(opts *bind.TransactOpts, node common.Address, publicKey []byte) (*types.Transaction, error)

	SetRegistry(opts *bind.TransactOpts, registryAddress common.Address) (*types.Transaction, error)

	SetThresholdPublicKey(opts *bind.TransactOpts, thresholdPublicKey []byte) (*types.Transaction, error)

	TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error)

	Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error)

	FilterAuthorizedSendersActive(opts *bind.FilterOpts) (*OCR2DROracleAuthorizedSendersActiveIterator, error)

	WatchAuthorizedSendersActive(opts *bind.WatchOpts, sink chan<- *OCR2DROracleAuthorizedSendersActive) (event.Subscription, error)

	ParseAuthorizedSendersActive(log types.Log) (*OCR2DROracleAuthorizedSendersActive, error)

	FilterAuthorizedSendersChanged(opts *bind.FilterOpts) (*OCR2DROracleAuthorizedSendersChangedIterator, error)

	WatchAuthorizedSendersChanged(opts *bind.WatchOpts, sink chan<- *OCR2DROracleAuthorizedSendersChanged) (event.Subscription, error)

	ParseAuthorizedSendersChanged(log types.Log) (*OCR2DROracleAuthorizedSendersChanged, error)

	FilterAuthorizedSendersDeactive(opts *bind.FilterOpts) (*OCR2DROracleAuthorizedSendersDeactiveIterator, error)

	WatchAuthorizedSendersDeactive(opts *bind.WatchOpts, sink chan<- *OCR2DROracleAuthorizedSendersDeactive) (event.Subscription, error)

	ParseAuthorizedSendersDeactive(log types.Log) (*OCR2DROracleAuthorizedSendersDeactive, error)

	FilterConfigSet(opts *bind.FilterOpts) (*OCR2DROracleConfigSetIterator, error)

	WatchConfigSet(opts *bind.WatchOpts, sink chan<- *OCR2DROracleConfigSet) (event.Subscription, error)

	ParseConfigSet(log types.Log) (*OCR2DROracleConfigSet, error)

	FilterInitialized(opts *bind.FilterOpts) (*OCR2DROracleInitializedIterator, error)

	WatchInitialized(opts *bind.WatchOpts, sink chan<- *OCR2DROracleInitialized) (event.Subscription, error)

	ParseInitialized(log types.Log) (*OCR2DROracleInitialized, error)

	FilterInvalidRequestID(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleInvalidRequestIDIterator, error)

	WatchInvalidRequestID(opts *bind.WatchOpts, sink chan<- *OCR2DROracleInvalidRequestID, requestId [][32]byte) (event.Subscription, error)

	ParseInvalidRequestID(log types.Log) (*OCR2DROracleInvalidRequestID, error)

	FilterOracleRequest(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleOracleRequestIterator, error)

	WatchOracleRequest(opts *bind.WatchOpts, sink chan<- *OCR2DROracleOracleRequest, requestId [][32]byte) (event.Subscription, error)

	ParseOracleRequest(log types.Log) (*OCR2DROracleOracleRequest, error)

	FilterOracleResponse(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleOracleResponseIterator, error)

	WatchOracleResponse(opts *bind.WatchOpts, sink chan<- *OCR2DROracleOracleResponse, requestId [][32]byte) (event.Subscription, error)

	ParseOracleResponse(log types.Log) (*OCR2DROracleOracleResponse, error)

	FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*OCR2DROracleOwnershipTransferRequestedIterator, error)

	WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *OCR2DROracleOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferRequested(log types.Log) (*OCR2DROracleOwnershipTransferRequested, error)

	FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*OCR2DROracleOwnershipTransferredIterator, error)

	WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *OCR2DROracleOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferred(log types.Log) (*OCR2DROracleOwnershipTransferred, error)

	FilterResponseTransmitted(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleResponseTransmittedIterator, error)

	WatchResponseTransmitted(opts *bind.WatchOpts, sink chan<- *OCR2DROracleResponseTransmitted, requestId [][32]byte) (event.Subscription, error)

	ParseResponseTransmitted(log types.Log) (*OCR2DROracleResponseTransmitted, error)

	FilterTransmitted(opts *bind.FilterOpts) (*OCR2DROracleTransmittedIterator, error)

	WatchTransmitted(opts *bind.WatchOpts, sink chan<- *OCR2DROracleTransmitted) (event.Subscription, error)

	ParseTransmitted(log types.Log) (*OCR2DROracleTransmitted, error)

	FilterUserCallbackError(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleUserCallbackErrorIterator, error)

	WatchUserCallbackError(opts *bind.WatchOpts, sink chan<- *OCR2DROracleUserCallbackError, requestId [][32]byte) (event.Subscription, error)

	ParseUserCallbackError(log types.Log) (*OCR2DROracleUserCallbackError, error)

	FilterUserCallbackRawError(opts *bind.FilterOpts, requestId [][32]byte) (*OCR2DROracleUserCallbackRawErrorIterator, error)

	WatchUserCallbackRawError(opts *bind.WatchOpts, sink chan<- *OCR2DROracleUserCallbackRawError, requestId [][32]byte) (event.Subscription, error)

	ParseUserCallbackRawError(log types.Log) (*OCR2DROracleUserCallbackRawError, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
