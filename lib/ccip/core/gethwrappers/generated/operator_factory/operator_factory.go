// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package operator_factory

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
	"github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

var OperatorFactoryMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"linkAddress\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"forwarder\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"AuthorizedForwarderCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"operator\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"OperatorCreated\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"query\",\"type\":\"address\"}],\"name\":\"created\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"deployNewForwarder\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"message\",\"type\":\"bytes\"}],\"name\":\"deployNewForwarderAndTransferOwnership\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"deployNewOperator\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"deployNewOperatorAndForwarder\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"linkToken\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"typeAndVersion\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"}]",
	Bin: "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",
}

var OperatorFactoryABI = OperatorFactoryMetaData.ABI

var OperatorFactoryBin = OperatorFactoryMetaData.Bin

func DeployOperatorFactory(auth *bind.TransactOpts, backend bind.ContractBackend, linkAddress common.Address) (common.Address, *types.Transaction, *OperatorFactory, error) {
	parsed, err := OperatorFactoryMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(OperatorFactoryBin), backend, linkAddress)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &OperatorFactory{address: address, abi: *parsed, OperatorFactoryCaller: OperatorFactoryCaller{contract: contract}, OperatorFactoryTransactor: OperatorFactoryTransactor{contract: contract}, OperatorFactoryFilterer: OperatorFactoryFilterer{contract: contract}}, nil
}

type OperatorFactory struct {
	address common.Address
	abi     abi.ABI
	OperatorFactoryCaller
	OperatorFactoryTransactor
	OperatorFactoryFilterer
}

type OperatorFactoryCaller struct {
	contract *bind.BoundContract
}

type OperatorFactoryTransactor struct {
	contract *bind.BoundContract
}

type OperatorFactoryFilterer struct {
	contract *bind.BoundContract
}

type OperatorFactorySession struct {
	Contract     *OperatorFactory
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type OperatorFactoryCallerSession struct {
	Contract *OperatorFactoryCaller
	CallOpts bind.CallOpts
}

type OperatorFactoryTransactorSession struct {
	Contract     *OperatorFactoryTransactor
	TransactOpts bind.TransactOpts
}

type OperatorFactoryRaw struct {
	Contract *OperatorFactory
}

type OperatorFactoryCallerRaw struct {
	Contract *OperatorFactoryCaller
}

type OperatorFactoryTransactorRaw struct {
	Contract *OperatorFactoryTransactor
}

func NewOperatorFactory(address common.Address, backend bind.ContractBackend) (*OperatorFactory, error) {
	abi, err := abi.JSON(strings.NewReader(OperatorFactoryABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindOperatorFactory(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &OperatorFactory{address: address, abi: abi, OperatorFactoryCaller: OperatorFactoryCaller{contract: contract}, OperatorFactoryTransactor: OperatorFactoryTransactor{contract: contract}, OperatorFactoryFilterer: OperatorFactoryFilterer{contract: contract}}, nil
}

func NewOperatorFactoryCaller(address common.Address, caller bind.ContractCaller) (*OperatorFactoryCaller, error) {
	contract, err := bindOperatorFactory(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &OperatorFactoryCaller{contract: contract}, nil
}

func NewOperatorFactoryTransactor(address common.Address, transactor bind.ContractTransactor) (*OperatorFactoryTransactor, error) {
	contract, err := bindOperatorFactory(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &OperatorFactoryTransactor{contract: contract}, nil
}

func NewOperatorFactoryFilterer(address common.Address, filterer bind.ContractFilterer) (*OperatorFactoryFilterer, error) {
	contract, err := bindOperatorFactory(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &OperatorFactoryFilterer{contract: contract}, nil
}

func bindOperatorFactory(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := OperatorFactoryMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_OperatorFactory *OperatorFactoryRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _OperatorFactory.Contract.OperatorFactoryCaller.contract.Call(opts, result, method, params...)
}

func (_OperatorFactory *OperatorFactoryRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OperatorFactory.Contract.OperatorFactoryTransactor.contract.Transfer(opts)
}

func (_OperatorFactory *OperatorFactoryRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _OperatorFactory.Contract.OperatorFactoryTransactor.contract.Transact(opts, method, params...)
}

func (_OperatorFactory *OperatorFactoryCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _OperatorFactory.Contract.contract.Call(opts, result, method, params...)
}

func (_OperatorFactory *OperatorFactoryTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OperatorFactory.Contract.contract.Transfer(opts)
}

func (_OperatorFactory *OperatorFactoryTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _OperatorFactory.Contract.contract.Transact(opts, method, params...)
}

func (_OperatorFactory *OperatorFactoryCaller) Created(opts *bind.CallOpts, query common.Address) (bool, error) {
	var out []interface{}
	err := _OperatorFactory.contract.Call(opts, &out, "created", query)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_OperatorFactory *OperatorFactorySession) Created(query common.Address) (bool, error) {
	return _OperatorFactory.Contract.Created(&_OperatorFactory.CallOpts, query)
}

func (_OperatorFactory *OperatorFactoryCallerSession) Created(query common.Address) (bool, error) {
	return _OperatorFactory.Contract.Created(&_OperatorFactory.CallOpts, query)
}

func (_OperatorFactory *OperatorFactoryCaller) LinkToken(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _OperatorFactory.contract.Call(opts, &out, "linkToken")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_OperatorFactory *OperatorFactorySession) LinkToken() (common.Address, error) {
	return _OperatorFactory.Contract.LinkToken(&_OperatorFactory.CallOpts)
}

func (_OperatorFactory *OperatorFactoryCallerSession) LinkToken() (common.Address, error) {
	return _OperatorFactory.Contract.LinkToken(&_OperatorFactory.CallOpts)
}

func (_OperatorFactory *OperatorFactoryCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _OperatorFactory.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_OperatorFactory *OperatorFactorySession) TypeAndVersion() (string, error) {
	return _OperatorFactory.Contract.TypeAndVersion(&_OperatorFactory.CallOpts)
}

func (_OperatorFactory *OperatorFactoryCallerSession) TypeAndVersion() (string, error) {
	return _OperatorFactory.Contract.TypeAndVersion(&_OperatorFactory.CallOpts)
}

func (_OperatorFactory *OperatorFactoryTransactor) DeployNewForwarder(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OperatorFactory.contract.Transact(opts, "deployNewForwarder")
}

func (_OperatorFactory *OperatorFactorySession) DeployNewForwarder() (*types.Transaction, error) {
	return _OperatorFactory.Contract.DeployNewForwarder(&_OperatorFactory.TransactOpts)
}

func (_OperatorFactory *OperatorFactoryTransactorSession) DeployNewForwarder() (*types.Transaction, error) {
	return _OperatorFactory.Contract.DeployNewForwarder(&_OperatorFactory.TransactOpts)
}

func (_OperatorFactory *OperatorFactoryTransactor) DeployNewForwarderAndTransferOwnership(opts *bind.TransactOpts, to common.Address, message []byte) (*types.Transaction, error) {
	return _OperatorFactory.contract.Transact(opts, "deployNewForwarderAndTransferOwnership", to, message)
}

func (_OperatorFactory *OperatorFactorySession) DeployNewForwarderAndTransferOwnership(to common.Address, message []byte) (*types.Transaction, error) {
	return _OperatorFactory.Contract.DeployNewForwarderAndTransferOwnership(&_OperatorFactory.TransactOpts, to, message)
}

func (_OperatorFactory *OperatorFactoryTransactorSession) DeployNewForwarderAndTransferOwnership(to common.Address, message []byte) (*types.Transaction, error) {
	return _OperatorFactory.Contract.DeployNewForwarderAndTransferOwnership(&_OperatorFactory.TransactOpts, to, message)
}

func (_OperatorFactory *OperatorFactoryTransactor) DeployNewOperator(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OperatorFactory.contract.Transact(opts, "deployNewOperator")
}

func (_OperatorFactory *OperatorFactorySession) DeployNewOperator() (*types.Transaction, error) {
	return _OperatorFactory.Contract.DeployNewOperator(&_OperatorFactory.TransactOpts)
}

func (_OperatorFactory *OperatorFactoryTransactorSession) DeployNewOperator() (*types.Transaction, error) {
	return _OperatorFactory.Contract.DeployNewOperator(&_OperatorFactory.TransactOpts)
}

func (_OperatorFactory *OperatorFactoryTransactor) DeployNewOperatorAndForwarder(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _OperatorFactory.contract.Transact(opts, "deployNewOperatorAndForwarder")
}

func (_OperatorFactory *OperatorFactorySession) DeployNewOperatorAndForwarder() (*types.Transaction, error) {
	return _OperatorFactory.Contract.DeployNewOperatorAndForwarder(&_OperatorFactory.TransactOpts)
}

func (_OperatorFactory *OperatorFactoryTransactorSession) DeployNewOperatorAndForwarder() (*types.Transaction, error) {
	return _OperatorFactory.Contract.DeployNewOperatorAndForwarder(&_OperatorFactory.TransactOpts)
}

type OperatorFactoryAuthorizedForwarderCreatedIterator struct {
	Event *OperatorFactoryAuthorizedForwarderCreated

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OperatorFactoryAuthorizedForwarderCreatedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OperatorFactoryAuthorizedForwarderCreated)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OperatorFactoryAuthorizedForwarderCreated)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OperatorFactoryAuthorizedForwarderCreatedIterator) Error() error {
	return it.fail
}

func (it *OperatorFactoryAuthorizedForwarderCreatedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OperatorFactoryAuthorizedForwarderCreated struct {
	Forwarder common.Address
	Owner     common.Address
	Sender    common.Address
	Raw       types.Log
}

func (_OperatorFactory *OperatorFactoryFilterer) FilterAuthorizedForwarderCreated(opts *bind.FilterOpts, forwarder []common.Address, owner []common.Address, sender []common.Address) (*OperatorFactoryAuthorizedForwarderCreatedIterator, error) {

	var forwarderRule []interface{}
	for _, forwarderItem := range forwarder {
		forwarderRule = append(forwarderRule, forwarderItem)
	}
	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var senderRule []interface{}
	for _, senderItem := range sender {
		senderRule = append(senderRule, senderItem)
	}

	logs, sub, err := _OperatorFactory.contract.FilterLogs(opts, "AuthorizedForwarderCreated", forwarderRule, ownerRule, senderRule)
	if err != nil {
		return nil, err
	}
	return &OperatorFactoryAuthorizedForwarderCreatedIterator{contract: _OperatorFactory.contract, event: "AuthorizedForwarderCreated", logs: logs, sub: sub}, nil
}

func (_OperatorFactory *OperatorFactoryFilterer) WatchAuthorizedForwarderCreated(opts *bind.WatchOpts, sink chan<- *OperatorFactoryAuthorizedForwarderCreated, forwarder []common.Address, owner []common.Address, sender []common.Address) (event.Subscription, error) {

	var forwarderRule []interface{}
	for _, forwarderItem := range forwarder {
		forwarderRule = append(forwarderRule, forwarderItem)
	}
	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var senderRule []interface{}
	for _, senderItem := range sender {
		senderRule = append(senderRule, senderItem)
	}

	logs, sub, err := _OperatorFactory.contract.WatchLogs(opts, "AuthorizedForwarderCreated", forwarderRule, ownerRule, senderRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OperatorFactoryAuthorizedForwarderCreated)
				if err := _OperatorFactory.contract.UnpackLog(event, "AuthorizedForwarderCreated", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OperatorFactory *OperatorFactoryFilterer) ParseAuthorizedForwarderCreated(log types.Log) (*OperatorFactoryAuthorizedForwarderCreated, error) {
	event := new(OperatorFactoryAuthorizedForwarderCreated)
	if err := _OperatorFactory.contract.UnpackLog(event, "AuthorizedForwarderCreated", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type OperatorFactoryOperatorCreatedIterator struct {
	Event *OperatorFactoryOperatorCreated

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *OperatorFactoryOperatorCreatedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(OperatorFactoryOperatorCreated)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(OperatorFactoryOperatorCreated)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *OperatorFactoryOperatorCreatedIterator) Error() error {
	return it.fail
}

func (it *OperatorFactoryOperatorCreatedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type OperatorFactoryOperatorCreated struct {
	Operator common.Address
	Owner    common.Address
	Sender   common.Address
	Raw      types.Log
}

func (_OperatorFactory *OperatorFactoryFilterer) FilterOperatorCreated(opts *bind.FilterOpts, operator []common.Address, owner []common.Address, sender []common.Address) (*OperatorFactoryOperatorCreatedIterator, error) {

	var operatorRule []interface{}
	for _, operatorItem := range operator {
		operatorRule = append(operatorRule, operatorItem)
	}
	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var senderRule []interface{}
	for _, senderItem := range sender {
		senderRule = append(senderRule, senderItem)
	}

	logs, sub, err := _OperatorFactory.contract.FilterLogs(opts, "OperatorCreated", operatorRule, ownerRule, senderRule)
	if err != nil {
		return nil, err
	}
	return &OperatorFactoryOperatorCreatedIterator{contract: _OperatorFactory.contract, event: "OperatorCreated", logs: logs, sub: sub}, nil
}

func (_OperatorFactory *OperatorFactoryFilterer) WatchOperatorCreated(opts *bind.WatchOpts, sink chan<- *OperatorFactoryOperatorCreated, operator []common.Address, owner []common.Address, sender []common.Address) (event.Subscription, error) {

	var operatorRule []interface{}
	for _, operatorItem := range operator {
		operatorRule = append(operatorRule, operatorItem)
	}
	var ownerRule []interface{}
	for _, ownerItem := range owner {
		ownerRule = append(ownerRule, ownerItem)
	}
	var senderRule []interface{}
	for _, senderItem := range sender {
		senderRule = append(senderRule, senderItem)
	}

	logs, sub, err := _OperatorFactory.contract.WatchLogs(opts, "OperatorCreated", operatorRule, ownerRule, senderRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(OperatorFactoryOperatorCreated)
				if err := _OperatorFactory.contract.UnpackLog(event, "OperatorCreated", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_OperatorFactory *OperatorFactoryFilterer) ParseOperatorCreated(log types.Log) (*OperatorFactoryOperatorCreated, error) {
	event := new(OperatorFactoryOperatorCreated)
	if err := _OperatorFactory.contract.UnpackLog(event, "OperatorCreated", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

func (_OperatorFactory *OperatorFactory) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _OperatorFactory.abi.Events["AuthorizedForwarderCreated"].ID:
		return _OperatorFactory.ParseAuthorizedForwarderCreated(log)
	case _OperatorFactory.abi.Events["OperatorCreated"].ID:
		return _OperatorFactory.ParseOperatorCreated(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (OperatorFactoryAuthorizedForwarderCreated) Topic() common.Hash {
	return common.HexToHash("0x1c9576ab03e40fdf23673f82d904a0f029c8a6629272a4edad4be877e83af64b")
}

func (OperatorFactoryOperatorCreated) Topic() common.Hash {
	return common.HexToHash("0xd3bb727b2e716a1f142bc9c63c66fe0ae4c5fbc89234f8aa77d0c864a7b63bab")
}

func (_OperatorFactory *OperatorFactory) Address() common.Address {
	return _OperatorFactory.address
}

type OperatorFactoryInterface interface {
	Created(opts *bind.CallOpts, query common.Address) (bool, error)

	LinkToken(opts *bind.CallOpts) (common.Address, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	DeployNewForwarder(opts *bind.TransactOpts) (*types.Transaction, error)

	DeployNewForwarderAndTransferOwnership(opts *bind.TransactOpts, to common.Address, message []byte) (*types.Transaction, error)

	DeployNewOperator(opts *bind.TransactOpts) (*types.Transaction, error)

	DeployNewOperatorAndForwarder(opts *bind.TransactOpts) (*types.Transaction, error)

	FilterAuthorizedForwarderCreated(opts *bind.FilterOpts, forwarder []common.Address, owner []common.Address, sender []common.Address) (*OperatorFactoryAuthorizedForwarderCreatedIterator, error)

	WatchAuthorizedForwarderCreated(opts *bind.WatchOpts, sink chan<- *OperatorFactoryAuthorizedForwarderCreated, forwarder []common.Address, owner []common.Address, sender []common.Address) (event.Subscription, error)

	ParseAuthorizedForwarderCreated(log types.Log) (*OperatorFactoryAuthorizedForwarderCreated, error)

	FilterOperatorCreated(opts *bind.FilterOpts, operator []common.Address, owner []common.Address, sender []common.Address) (*OperatorFactoryOperatorCreatedIterator, error)

	WatchOperatorCreated(opts *bind.WatchOpts, sink chan<- *OperatorFactoryOperatorCreated, operator []common.Address, owner []common.Address, sender []common.Address) (event.Subscription, error)

	ParseOperatorCreated(log types.Log) (*OperatorFactoryOperatorCreated, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
