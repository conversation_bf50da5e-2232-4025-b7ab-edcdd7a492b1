// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package vrfv2plus_wrapper_optimism

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
	"github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

var VRFV2PlusWrapperOptimismMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_link\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_linkNativeFeed\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_coordinator\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_subId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"FailedToTransferLink\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint16\",\"name\":\"expectedMinimumLength\",\"type\":\"uint16\"},{\"internalType\":\"uint16\",\"name\":\"actualLength\",\"type\":\"uint16\"}],\"name\":\"IncorrectExtraArgsLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"mode\",\"type\":\"uint8\"}],\"name\":\"InvalidL1FeeCalculationMode\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"coefficient\",\"type\":\"uint8\"}],\"name\":\"InvalidL1FeeCoefficient\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"premiumPercentage\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"max\",\"type\":\"uint8\"}],\"name\":\"InvalidPremiumPercentage\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LINKPaymentInRequestRandomWordsInNative\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LinkAlreadySet\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"flatFeeLinkDiscountPPM\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"flatFeeNativePPM\",\"type\":\"uint32\"}],\"name\":\"LinkDiscountTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NativePaymentInOnTokenTransfer\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"have\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"want\",\"type\":\"address\"}],\"name\":\"OnlyCoordinatorCanFulfill\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"have\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"coordinator\",\"type\":\"address\"}],\"name\":\"OnlyOwnerOrCoordinator\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SubscriptionIdMissing\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"mode\",\"type\":\"uint8\"}],\"name\":\"UnsupportedL1FeeCalculationMode\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddress\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"wrapperGasOverhead\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"coordinatorGasOverheadNative\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"coordinatorGasOverheadLink\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint16\",\"name\":\"coordinatorGasOverheadPerWord\",\"type\":\"uint16\"},{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"coordinatorNativePremiumPercentage\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"coordinatorLinkPremiumPercentage\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"keyHash\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"maxNumWords\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"stalenessSeconds\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"fallbackWeiPerUnitLink\",\"type\":\"int256\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"fulfillmentFlatFeeNativePPM\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"fulfillmentFlatFeeLinkDiscountPPM\",\"type\":\"uint32\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"vrfCoordinator\",\"type\":\"address\"}],\"name\":\"CoordinatorSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"Disabled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"Enabled\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"int256\",\"name\":\"fallbackWeiPerUnitLink\",\"type\":\"int256\"}],\"name\":\"FallbackWeiPerUnitLinkUsed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"size\",\"type\":\"uint32\"}],\"name\":\"FulfillmentTxSizeSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"mode\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"coefficient\",\"type\":\"uint8\"}],\"name\":\"L1FeeCalculationSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"NativeWithdrawn\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"Withdrawn\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"consumer\",\"type\":\"address\"}],\"name\":\"WrapperFulfillmentFailed\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"SUBSCRIPTION_ID\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_callbackGasLimit\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_numWords\",\"type\":\"uint32\"}],\"name\":\"calculateRequestPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_callbackGasLimit\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_numWords\",\"type\":\"uint32\"}],\"name\":\"calculateRequestPriceNative\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"extraArgs\",\"type\":\"bytes\"},{\"internalType\":\"bool\",\"name\":\"isLinkMode\",\"type\":\"bool\"}],\"name\":\"checkPaymentMode\",\"outputs\":[],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"disable\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"enable\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_callbackGasLimit\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_numWords\",\"type\":\"uint32\"},{\"internalType\":\"uint256\",\"name\":\"_requestGasPriceWei\",\"type\":\"uint256\"}],\"name\":\"estimateRequestPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_callbackGasLimit\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_numWords\",\"type\":\"uint32\"},{\"internalType\":\"uint256\",\"name\":\"_requestGasPriceWei\",\"type\":\"uint256\"}],\"name\":\"estimateRequestPriceNative\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getConfig\",\"outputs\":[{\"internalType\":\"int256\",\"name\":\"fallbackWeiPerUnitLink\",\"type\":\"int256\"},{\"internalType\":\"uint32\",\"name\":\"stalenessSeconds\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"fulfillmentFlatFeeNativePPM\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"fulfillmentFlatFeeLinkDiscountPPM\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"wrapperGasOverhead\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"coordinatorGasOverheadNative\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"coordinatorGasOverheadLink\",\"type\":\"uint32\"},{\"internalType\":\"uint16\",\"name\":\"coordinatorGasOverheadPerWord\",\"type\":\"uint16\"},{\"internalType\":\"uint8\",\"name\":\"wrapperNativePremiumPercentage\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"wrapperLinkPremiumPercentage\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"keyHash\",\"type\":\"bytes32\"},{\"internalType\":\"uint8\",\"name\":\"maxNumWords\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"lastRequestId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"link\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"linkNativeFeed\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"_amount\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"_data\",\"type\":\"bytes\"}],\"name\":\"onTokenTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"},{\"internalType\":\"uint256[]\",\"name\":\"randomWords\",\"type\":\"uint256[]\"}],\"name\":\"rawFulfillRandomWords\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_callbackGasLimit\",\"type\":\"uint32\"},{\"internalType\":\"uint16\",\"name\":\"_requestConfirmations\",\"type\":\"uint16\"},{\"internalType\":\"uint32\",\"name\":\"_numWords\",\"type\":\"uint32\"},{\"internalType\":\"bytes\",\"name\":\"extraArgs\",\"type\":\"bytes\"}],\"name\":\"requestRandomWordsInNative\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"requestId\",\"type\":\"uint256\"}],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"s_callbacks\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"callbackAddress\",\"type\":\"address\"},{\"internalType\":\"uint32\",\"name\":\"callbackGasLimit\",\"type\":\"uint32\"},{\"internalType\":\"uint64\",\"name\":\"requestGasPrice\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"s_configured\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"s_disabled\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"s_fulfillmentTxSizeBytes\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"s_l1FeeCalculationMode\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"s_l1FeeCoefficient\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"s_vrfCoordinator\",\"outputs\":[{\"internalType\":\"contractIVRFCoordinatorV2Plus\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_wrapperGasOverhead\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_coordinatorGasOverheadNative\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_coordinatorGasOverheadLink\",\"type\":\"uint32\"},{\"internalType\":\"uint16\",\"name\":\"_coordinatorGasOverheadPerWord\",\"type\":\"uint16\"},{\"internalType\":\"uint8\",\"name\":\"_coordinatorNativePremiumPercentage\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"_coordinatorLinkPremiumPercentage\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"_keyHash\",\"type\":\"bytes32\"},{\"internalType\":\"uint8\",\"name\":\"_maxNumWords\",\"type\":\"uint8\"},{\"internalType\":\"uint32\",\"name\":\"_stalenessSeconds\",\"type\":\"uint32\"},{\"internalType\":\"int256\",\"name\":\"_fallbackWeiPerUnitLink\",\"type\":\"int256\"},{\"internalType\":\"uint32\",\"name\":\"_fulfillmentFlatFeeNativePPM\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"_fulfillmentFlatFeeLinkDiscountPPM\",\"type\":\"uint32\"}],\"name\":\"setConfig\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_vrfCoordinator\",\"type\":\"address\"}],\"name\":\"setCoordinator\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"_size\",\"type\":\"uint32\"}],\"name\":\"setFulfillmentTxSize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint8\",\"name\":\"mode\",\"type\":\"uint8\"},{\"internalType\":\"uint8\",\"name\":\"coefficient\",\"type\":\"uint8\"}],\"name\":\"setL1FeeCalculation\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"typeAndVersion\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_recipient\",\"type\":\"address\"}],\"name\":\"withdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_recipient\",\"type\":\"address\"}],\"name\":\"withdrawNative\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}]",
	Bin: "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",
}

var VRFV2PlusWrapperOptimismABI = VRFV2PlusWrapperOptimismMetaData.ABI

var VRFV2PlusWrapperOptimismBin = VRFV2PlusWrapperOptimismMetaData.Bin

func DeployVRFV2PlusWrapperOptimism(auth *bind.TransactOpts, backend bind.ContractBackend, _link common.Address, _linkNativeFeed common.Address, _coordinator common.Address, _subId *big.Int) (common.Address, *types.Transaction, *VRFV2PlusWrapperOptimism, error) {
	parsed, err := VRFV2PlusWrapperOptimismMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(VRFV2PlusWrapperOptimismBin), backend, _link, _linkNativeFeed, _coordinator, _subId)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &VRFV2PlusWrapperOptimism{address: address, abi: *parsed, VRFV2PlusWrapperOptimismCaller: VRFV2PlusWrapperOptimismCaller{contract: contract}, VRFV2PlusWrapperOptimismTransactor: VRFV2PlusWrapperOptimismTransactor{contract: contract}, VRFV2PlusWrapperOptimismFilterer: VRFV2PlusWrapperOptimismFilterer{contract: contract}}, nil
}

type VRFV2PlusWrapperOptimism struct {
	address common.Address
	abi     abi.ABI
	VRFV2PlusWrapperOptimismCaller
	VRFV2PlusWrapperOptimismTransactor
	VRFV2PlusWrapperOptimismFilterer
}

type VRFV2PlusWrapperOptimismCaller struct {
	contract *bind.BoundContract
}

type VRFV2PlusWrapperOptimismTransactor struct {
	contract *bind.BoundContract
}

type VRFV2PlusWrapperOptimismFilterer struct {
	contract *bind.BoundContract
}

type VRFV2PlusWrapperOptimismSession struct {
	Contract     *VRFV2PlusWrapperOptimism
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type VRFV2PlusWrapperOptimismCallerSession struct {
	Contract *VRFV2PlusWrapperOptimismCaller
	CallOpts bind.CallOpts
}

type VRFV2PlusWrapperOptimismTransactorSession struct {
	Contract     *VRFV2PlusWrapperOptimismTransactor
	TransactOpts bind.TransactOpts
}

type VRFV2PlusWrapperOptimismRaw struct {
	Contract *VRFV2PlusWrapperOptimism
}

type VRFV2PlusWrapperOptimismCallerRaw struct {
	Contract *VRFV2PlusWrapperOptimismCaller
}

type VRFV2PlusWrapperOptimismTransactorRaw struct {
	Contract *VRFV2PlusWrapperOptimismTransactor
}

func NewVRFV2PlusWrapperOptimism(address common.Address, backend bind.ContractBackend) (*VRFV2PlusWrapperOptimism, error) {
	abi, err := abi.JSON(strings.NewReader(VRFV2PlusWrapperOptimismABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindVRFV2PlusWrapperOptimism(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimism{address: address, abi: abi, VRFV2PlusWrapperOptimismCaller: VRFV2PlusWrapperOptimismCaller{contract: contract}, VRFV2PlusWrapperOptimismTransactor: VRFV2PlusWrapperOptimismTransactor{contract: contract}, VRFV2PlusWrapperOptimismFilterer: VRFV2PlusWrapperOptimismFilterer{contract: contract}}, nil
}

func NewVRFV2PlusWrapperOptimismCaller(address common.Address, caller bind.ContractCaller) (*VRFV2PlusWrapperOptimismCaller, error) {
	contract, err := bindVRFV2PlusWrapperOptimism(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismCaller{contract: contract}, nil
}

func NewVRFV2PlusWrapperOptimismTransactor(address common.Address, transactor bind.ContractTransactor) (*VRFV2PlusWrapperOptimismTransactor, error) {
	contract, err := bindVRFV2PlusWrapperOptimism(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismTransactor{contract: contract}, nil
}

func NewVRFV2PlusWrapperOptimismFilterer(address common.Address, filterer bind.ContractFilterer) (*VRFV2PlusWrapperOptimismFilterer, error) {
	contract, err := bindVRFV2PlusWrapperOptimism(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismFilterer{contract: contract}, nil
}

func bindVRFV2PlusWrapperOptimism(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := VRFV2PlusWrapperOptimismMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _VRFV2PlusWrapperOptimism.Contract.VRFV2PlusWrapperOptimismCaller.contract.Call(opts, result, method, params...)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.VRFV2PlusWrapperOptimismTransactor.contract.Transfer(opts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.VRFV2PlusWrapperOptimismTransactor.contract.Transact(opts, method, params...)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _VRFV2PlusWrapperOptimism.Contract.contract.Call(opts, result, method, params...)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.contract.Transfer(opts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.contract.Transact(opts, method, params...)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) SUBSCRIPTIONID(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "SUBSCRIPTION_ID")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SUBSCRIPTIONID() (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SUBSCRIPTIONID(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) SUBSCRIPTIONID() (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SUBSCRIPTIONID(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) CalculateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "calculateRequestPrice", _callbackGasLimit, _numWords)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) CalculateRequestPrice(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.CalculateRequestPrice(&_VRFV2PlusWrapperOptimism.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) CalculateRequestPrice(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.CalculateRequestPrice(&_VRFV2PlusWrapperOptimism.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) CalculateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "calculateRequestPriceNative", _callbackGasLimit, _numWords)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) CalculateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.CalculateRequestPriceNative(&_VRFV2PlusWrapperOptimism.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) CalculateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32) (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.CalculateRequestPriceNative(&_VRFV2PlusWrapperOptimism.CallOpts, _callbackGasLimit, _numWords)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) CheckPaymentMode(opts *bind.CallOpts, extraArgs []byte, isLinkMode bool) error {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "checkPaymentMode", extraArgs, isLinkMode)

	if err != nil {
		return err
	}

	return err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) CheckPaymentMode(extraArgs []byte, isLinkMode bool) error {
	return _VRFV2PlusWrapperOptimism.Contract.CheckPaymentMode(&_VRFV2PlusWrapperOptimism.CallOpts, extraArgs, isLinkMode)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) CheckPaymentMode(extraArgs []byte, isLinkMode bool) error {
	return _VRFV2PlusWrapperOptimism.Contract.CheckPaymentMode(&_VRFV2PlusWrapperOptimism.CallOpts, extraArgs, isLinkMode)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) EstimateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "estimateRequestPrice", _callbackGasLimit, _numWords, _requestGasPriceWei)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) EstimateRequestPrice(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.EstimateRequestPrice(&_VRFV2PlusWrapperOptimism.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) EstimateRequestPrice(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.EstimateRequestPrice(&_VRFV2PlusWrapperOptimism.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) EstimateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "estimateRequestPriceNative", _callbackGasLimit, _numWords, _requestGasPriceWei)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) EstimateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.EstimateRequestPriceNative(&_VRFV2PlusWrapperOptimism.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) EstimateRequestPriceNative(_callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.EstimateRequestPriceNative(&_VRFV2PlusWrapperOptimism.CallOpts, _callbackGasLimit, _numWords, _requestGasPriceWei)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) GetConfig(opts *bind.CallOpts) (GetConfig,

	error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "getConfig")

	outstruct := new(GetConfig)
	if err != nil {
		return *outstruct, err
	}

	outstruct.FallbackWeiPerUnitLink = *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)
	outstruct.StalenessSeconds = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.FulfillmentFlatFeeNativePPM = *abi.ConvertType(out[2], new(uint32)).(*uint32)
	outstruct.FulfillmentFlatFeeLinkDiscountPPM = *abi.ConvertType(out[3], new(uint32)).(*uint32)
	outstruct.WrapperGasOverhead = *abi.ConvertType(out[4], new(uint32)).(*uint32)
	outstruct.CoordinatorGasOverheadNative = *abi.ConvertType(out[5], new(uint32)).(*uint32)
	outstruct.CoordinatorGasOverheadLink = *abi.ConvertType(out[6], new(uint32)).(*uint32)
	outstruct.CoordinatorGasOverheadPerWord = *abi.ConvertType(out[7], new(uint16)).(*uint16)
	outstruct.WrapperNativePremiumPercentage = *abi.ConvertType(out[8], new(uint8)).(*uint8)
	outstruct.WrapperLinkPremiumPercentage = *abi.ConvertType(out[9], new(uint8)).(*uint8)
	outstruct.KeyHash = *abi.ConvertType(out[10], new([32]byte)).(*[32]byte)
	outstruct.MaxNumWords = *abi.ConvertType(out[11], new(uint8)).(*uint8)

	return *outstruct, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) GetConfig() (GetConfig,

	error) {
	return _VRFV2PlusWrapperOptimism.Contract.GetConfig(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) GetConfig() (GetConfig,

	error) {
	return _VRFV2PlusWrapperOptimism.Contract.GetConfig(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) LastRequestId(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "lastRequestId")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) LastRequestId() (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.LastRequestId(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) LastRequestId() (*big.Int, error) {
	return _VRFV2PlusWrapperOptimism.Contract.LastRequestId(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) Link(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "link")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) Link() (common.Address, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Link(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) Link() (common.Address, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Link(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) LinkNativeFeed(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "linkNativeFeed")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) LinkNativeFeed() (common.Address, error) {
	return _VRFV2PlusWrapperOptimism.Contract.LinkNativeFeed(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) LinkNativeFeed() (common.Address, error) {
	return _VRFV2PlusWrapperOptimism.Contract.LinkNativeFeed(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) Owner() (common.Address, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Owner(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) Owner() (common.Address, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Owner(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) SCallbacks(opts *bind.CallOpts, arg0 *big.Int) (SCallbacks,

	error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "s_callbacks", arg0)

	outstruct := new(SCallbacks)
	if err != nil {
		return *outstruct, err
	}

	outstruct.CallbackAddress = *abi.ConvertType(out[0], new(common.Address)).(*common.Address)
	outstruct.CallbackGasLimit = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.RequestGasPrice = *abi.ConvertType(out[2], new(uint64)).(*uint64)

	return *outstruct, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SCallbacks(arg0 *big.Int) (SCallbacks,

	error) {
	return _VRFV2PlusWrapperOptimism.Contract.SCallbacks(&_VRFV2PlusWrapperOptimism.CallOpts, arg0)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) SCallbacks(arg0 *big.Int) (SCallbacks,

	error) {
	return _VRFV2PlusWrapperOptimism.Contract.SCallbacks(&_VRFV2PlusWrapperOptimism.CallOpts, arg0)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) SConfigured(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "s_configured")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SConfigured() (bool, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SConfigured(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) SConfigured() (bool, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SConfigured(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) SDisabled(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "s_disabled")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SDisabled() (bool, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SDisabled(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) SDisabled() (bool, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SDisabled(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) SFulfillmentTxSizeBytes(opts *bind.CallOpts) (uint32, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "s_fulfillmentTxSizeBytes")

	if err != nil {
		return *new(uint32), err
	}

	out0 := *abi.ConvertType(out[0], new(uint32)).(*uint32)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SFulfillmentTxSizeBytes() (uint32, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SFulfillmentTxSizeBytes(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) SFulfillmentTxSizeBytes() (uint32, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SFulfillmentTxSizeBytes(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) SL1FeeCalculationMode(opts *bind.CallOpts) (uint8, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "s_l1FeeCalculationMode")

	if err != nil {
		return *new(uint8), err
	}

	out0 := *abi.ConvertType(out[0], new(uint8)).(*uint8)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SL1FeeCalculationMode() (uint8, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SL1FeeCalculationMode(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) SL1FeeCalculationMode() (uint8, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SL1FeeCalculationMode(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) SL1FeeCoefficient(opts *bind.CallOpts) (uint8, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "s_l1FeeCoefficient")

	if err != nil {
		return *new(uint8), err
	}

	out0 := *abi.ConvertType(out[0], new(uint8)).(*uint8)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SL1FeeCoefficient() (uint8, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SL1FeeCoefficient(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) SL1FeeCoefficient() (uint8, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SL1FeeCoefficient(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) SVrfCoordinator(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "s_vrfCoordinator")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SVrfCoordinator() (common.Address, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SVrfCoordinator(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) SVrfCoordinator() (common.Address, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SVrfCoordinator(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _VRFV2PlusWrapperOptimism.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) TypeAndVersion() (string, error) {
	return _VRFV2PlusWrapperOptimism.Contract.TypeAndVersion(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismCallerSession) TypeAndVersion() (string, error) {
	return _VRFV2PlusWrapperOptimism.Contract.TypeAndVersion(&_VRFV2PlusWrapperOptimism.CallOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "acceptOwnership")
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) AcceptOwnership() (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.AcceptOwnership(&_VRFV2PlusWrapperOptimism.TransactOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.AcceptOwnership(&_VRFV2PlusWrapperOptimism.TransactOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) Disable(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "disable")
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) Disable() (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Disable(&_VRFV2PlusWrapperOptimism.TransactOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) Disable() (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Disable(&_VRFV2PlusWrapperOptimism.TransactOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) Enable(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "enable")
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) Enable() (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Enable(&_VRFV2PlusWrapperOptimism.TransactOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) Enable() (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Enable(&_VRFV2PlusWrapperOptimism.TransactOpts)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) OnTokenTransfer(opts *bind.TransactOpts, _sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "onTokenTransfer", _sender, _amount, _data)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) OnTokenTransfer(_sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.OnTokenTransfer(&_VRFV2PlusWrapperOptimism.TransactOpts, _sender, _amount, _data)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) OnTokenTransfer(_sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.OnTokenTransfer(&_VRFV2PlusWrapperOptimism.TransactOpts, _sender, _amount, _data)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) RawFulfillRandomWords(opts *bind.TransactOpts, requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "rawFulfillRandomWords", requestId, randomWords)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) RawFulfillRandomWords(requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.RawFulfillRandomWords(&_VRFV2PlusWrapperOptimism.TransactOpts, requestId, randomWords)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) RawFulfillRandomWords(requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.RawFulfillRandomWords(&_VRFV2PlusWrapperOptimism.TransactOpts, requestId, randomWords)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) RequestRandomWordsInNative(opts *bind.TransactOpts, _callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "requestRandomWordsInNative", _callbackGasLimit, _requestConfirmations, _numWords, extraArgs)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) RequestRandomWordsInNative(_callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.RequestRandomWordsInNative(&_VRFV2PlusWrapperOptimism.TransactOpts, _callbackGasLimit, _requestConfirmations, _numWords, extraArgs)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) RequestRandomWordsInNative(_callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.RequestRandomWordsInNative(&_VRFV2PlusWrapperOptimism.TransactOpts, _callbackGasLimit, _requestConfirmations, _numWords, extraArgs)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) SetConfig(opts *bind.TransactOpts, _wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "setConfig", _wrapperGasOverhead, _coordinatorGasOverheadNative, _coordinatorGasOverheadLink, _coordinatorGasOverheadPerWord, _coordinatorNativePremiumPercentage, _coordinatorLinkPremiumPercentage, _keyHash, _maxNumWords, _stalenessSeconds, _fallbackWeiPerUnitLink, _fulfillmentFlatFeeNativePPM, _fulfillmentFlatFeeLinkDiscountPPM)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SetConfig(_wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SetConfig(&_VRFV2PlusWrapperOptimism.TransactOpts, _wrapperGasOverhead, _coordinatorGasOverheadNative, _coordinatorGasOverheadLink, _coordinatorGasOverheadPerWord, _coordinatorNativePremiumPercentage, _coordinatorLinkPremiumPercentage, _keyHash, _maxNumWords, _stalenessSeconds, _fallbackWeiPerUnitLink, _fulfillmentFlatFeeNativePPM, _fulfillmentFlatFeeLinkDiscountPPM)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) SetConfig(_wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SetConfig(&_VRFV2PlusWrapperOptimism.TransactOpts, _wrapperGasOverhead, _coordinatorGasOverheadNative, _coordinatorGasOverheadLink, _coordinatorGasOverheadPerWord, _coordinatorNativePremiumPercentage, _coordinatorLinkPremiumPercentage, _keyHash, _maxNumWords, _stalenessSeconds, _fallbackWeiPerUnitLink, _fulfillmentFlatFeeNativePPM, _fulfillmentFlatFeeLinkDiscountPPM)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) SetCoordinator(opts *bind.TransactOpts, _vrfCoordinator common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "setCoordinator", _vrfCoordinator)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SetCoordinator(_vrfCoordinator common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SetCoordinator(&_VRFV2PlusWrapperOptimism.TransactOpts, _vrfCoordinator)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) SetCoordinator(_vrfCoordinator common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SetCoordinator(&_VRFV2PlusWrapperOptimism.TransactOpts, _vrfCoordinator)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) SetFulfillmentTxSize(opts *bind.TransactOpts, _size uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "setFulfillmentTxSize", _size)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SetFulfillmentTxSize(_size uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SetFulfillmentTxSize(&_VRFV2PlusWrapperOptimism.TransactOpts, _size)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) SetFulfillmentTxSize(_size uint32) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SetFulfillmentTxSize(&_VRFV2PlusWrapperOptimism.TransactOpts, _size)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) SetL1FeeCalculation(opts *bind.TransactOpts, mode uint8, coefficient uint8) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "setL1FeeCalculation", mode, coefficient)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) SetL1FeeCalculation(mode uint8, coefficient uint8) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SetL1FeeCalculation(&_VRFV2PlusWrapperOptimism.TransactOpts, mode, coefficient)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) SetL1FeeCalculation(mode uint8, coefficient uint8) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.SetL1FeeCalculation(&_VRFV2PlusWrapperOptimism.TransactOpts, mode, coefficient)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "transferOwnership", to)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.TransferOwnership(&_VRFV2PlusWrapperOptimism.TransactOpts, to)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.TransferOwnership(&_VRFV2PlusWrapperOptimism.TransactOpts, to)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) Withdraw(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "withdraw", _recipient)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) Withdraw(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Withdraw(&_VRFV2PlusWrapperOptimism.TransactOpts, _recipient)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) Withdraw(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.Withdraw(&_VRFV2PlusWrapperOptimism.TransactOpts, _recipient)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactor) WithdrawNative(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.contract.Transact(opts, "withdrawNative", _recipient)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismSession) WithdrawNative(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.WithdrawNative(&_VRFV2PlusWrapperOptimism.TransactOpts, _recipient)
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismTransactorSession) WithdrawNative(_recipient common.Address) (*types.Transaction, error) {
	return _VRFV2PlusWrapperOptimism.Contract.WithdrawNative(&_VRFV2PlusWrapperOptimism.TransactOpts, _recipient)
}

type VRFV2PlusWrapperOptimismConfigSetIterator struct {
	Event *VRFV2PlusWrapperOptimismConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismConfigSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismConfigSet struct {
	WrapperGasOverhead                 uint32
	CoordinatorGasOverheadNative       uint32
	CoordinatorGasOverheadLink         uint32
	CoordinatorGasOverheadPerWord      uint16
	CoordinatorNativePremiumPercentage uint8
	CoordinatorLinkPremiumPercentage   uint8
	KeyHash                            [32]byte
	MaxNumWords                        uint8
	StalenessSeconds                   uint32
	FallbackWeiPerUnitLink             *big.Int
	FulfillmentFlatFeeNativePPM        uint32
	FulfillmentFlatFeeLinkDiscountPPM  uint32
	Raw                                types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterConfigSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismConfigSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismConfigSetIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "ConfigSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismConfigSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismConfigSet)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "ConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseConfigSet(log types.Log) (*VRFV2PlusWrapperOptimismConfigSet, error) {
	event := new(VRFV2PlusWrapperOptimismConfigSet)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "ConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismCoordinatorSetIterator struct {
	Event *VRFV2PlusWrapperOptimismCoordinatorSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismCoordinatorSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismCoordinatorSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismCoordinatorSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismCoordinatorSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismCoordinatorSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismCoordinatorSet struct {
	VrfCoordinator common.Address
	Raw            types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterCoordinatorSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismCoordinatorSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "CoordinatorSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismCoordinatorSetIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "CoordinatorSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchCoordinatorSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismCoordinatorSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "CoordinatorSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismCoordinatorSet)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "CoordinatorSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseCoordinatorSet(log types.Log) (*VRFV2PlusWrapperOptimismCoordinatorSet, error) {
	event := new(VRFV2PlusWrapperOptimismCoordinatorSet)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "CoordinatorSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismDisabledIterator struct {
	Event *VRFV2PlusWrapperOptimismDisabled

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismDisabledIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismDisabled)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismDisabled)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismDisabledIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismDisabledIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismDisabled struct {
	Raw types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterDisabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismDisabledIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "Disabled")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismDisabledIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "Disabled", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchDisabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismDisabled) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "Disabled")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismDisabled)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "Disabled", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseDisabled(log types.Log) (*VRFV2PlusWrapperOptimismDisabled, error) {
	event := new(VRFV2PlusWrapperOptimismDisabled)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "Disabled", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismEnabledIterator struct {
	Event *VRFV2PlusWrapperOptimismEnabled

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismEnabledIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismEnabled)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismEnabled)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismEnabledIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismEnabledIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismEnabled struct {
	Raw types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterEnabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismEnabledIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "Enabled")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismEnabledIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "Enabled", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchEnabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismEnabled) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "Enabled")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismEnabled)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "Enabled", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseEnabled(log types.Log) (*VRFV2PlusWrapperOptimismEnabled, error) {
	event := new(VRFV2PlusWrapperOptimismEnabled)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "Enabled", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsedIterator struct {
	Event *VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsedIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed struct {
	RequestId              *big.Int
	FallbackWeiPerUnitLink *big.Int
	Raw                    types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterFallbackWeiPerUnitLinkUsed(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsedIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "FallbackWeiPerUnitLinkUsed")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsedIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "FallbackWeiPerUnitLinkUsed", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchFallbackWeiPerUnitLinkUsed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "FallbackWeiPerUnitLinkUsed")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "FallbackWeiPerUnitLinkUsed", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseFallbackWeiPerUnitLinkUsed(log types.Log) (*VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed, error) {
	event := new(VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "FallbackWeiPerUnitLinkUsed", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismFulfillmentTxSizeSetIterator struct {
	Event *VRFV2PlusWrapperOptimismFulfillmentTxSizeSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismFulfillmentTxSizeSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismFulfillmentTxSizeSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismFulfillmentTxSizeSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismFulfillmentTxSizeSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismFulfillmentTxSizeSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismFulfillmentTxSizeSet struct {
	Size uint32
	Raw  types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterFulfillmentTxSizeSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismFulfillmentTxSizeSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "FulfillmentTxSizeSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismFulfillmentTxSizeSetIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "FulfillmentTxSizeSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchFulfillmentTxSizeSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismFulfillmentTxSizeSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "FulfillmentTxSizeSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismFulfillmentTxSizeSet)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "FulfillmentTxSizeSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseFulfillmentTxSizeSet(log types.Log) (*VRFV2PlusWrapperOptimismFulfillmentTxSizeSet, error) {
	event := new(VRFV2PlusWrapperOptimismFulfillmentTxSizeSet)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "FulfillmentTxSizeSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismL1FeeCalculationSetIterator struct {
	Event *VRFV2PlusWrapperOptimismL1FeeCalculationSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismL1FeeCalculationSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismL1FeeCalculationSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismL1FeeCalculationSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismL1FeeCalculationSetIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismL1FeeCalculationSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismL1FeeCalculationSet struct {
	Mode        uint8
	Coefficient uint8
	Raw         types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterL1FeeCalculationSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismL1FeeCalculationSetIterator, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "L1FeeCalculationSet")
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismL1FeeCalculationSetIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "L1FeeCalculationSet", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchL1FeeCalculationSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismL1FeeCalculationSet) (event.Subscription, error) {

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "L1FeeCalculationSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismL1FeeCalculationSet)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "L1FeeCalculationSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseL1FeeCalculationSet(log types.Log) (*VRFV2PlusWrapperOptimismL1FeeCalculationSet, error) {
	event := new(VRFV2PlusWrapperOptimismL1FeeCalculationSet)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "L1FeeCalculationSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismNativeWithdrawnIterator struct {
	Event *VRFV2PlusWrapperOptimismNativeWithdrawn

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismNativeWithdrawnIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismNativeWithdrawn)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismNativeWithdrawn)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismNativeWithdrawnIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismNativeWithdrawnIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismNativeWithdrawn struct {
	To     common.Address
	Amount *big.Int
	Raw    types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterNativeWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperOptimismNativeWithdrawnIterator, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "NativeWithdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismNativeWithdrawnIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "NativeWithdrawn", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchNativeWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismNativeWithdrawn, to []common.Address) (event.Subscription, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "NativeWithdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismNativeWithdrawn)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "NativeWithdrawn", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseNativeWithdrawn(log types.Log) (*VRFV2PlusWrapperOptimismNativeWithdrawn, error) {
	event := new(VRFV2PlusWrapperOptimismNativeWithdrawn)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "NativeWithdrawn", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismOwnershipTransferRequestedIterator struct {
	Event *VRFV2PlusWrapperOptimismOwnershipTransferRequested

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismOwnershipTransferRequestedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperOptimismOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismOwnershipTransferRequestedIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismOwnershipTransferRequested)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseOwnershipTransferRequested(log types.Log) (*VRFV2PlusWrapperOptimismOwnershipTransferRequested, error) {
	event := new(VRFV2PlusWrapperOptimismOwnershipTransferRequested)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismOwnershipTransferredIterator struct {
	Event *VRFV2PlusWrapperOptimismOwnershipTransferred

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismOwnershipTransferredIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismOwnershipTransferredIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperOptimismOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismOwnershipTransferredIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismOwnershipTransferred)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseOwnershipTransferred(log types.Log) (*VRFV2PlusWrapperOptimismOwnershipTransferred, error) {
	event := new(VRFV2PlusWrapperOptimismOwnershipTransferred)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismWithdrawnIterator struct {
	Event *VRFV2PlusWrapperOptimismWithdrawn

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismWithdrawnIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismWithdrawn)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismWithdrawn)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismWithdrawnIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismWithdrawnIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismWithdrawn struct {
	To     common.Address
	Amount *big.Int
	Raw    types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperOptimismWithdrawnIterator, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "Withdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismWithdrawnIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "Withdrawn", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismWithdrawn, to []common.Address) (event.Subscription, error) {

	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "Withdrawn", toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismWithdrawn)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "Withdrawn", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseWithdrawn(log types.Log) (*VRFV2PlusWrapperOptimismWithdrawn, error) {
	event := new(VRFV2PlusWrapperOptimismWithdrawn)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "Withdrawn", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type VRFV2PlusWrapperOptimismWrapperFulfillmentFailedIterator struct {
	Event *VRFV2PlusWrapperOptimismWrapperFulfillmentFailed

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *VRFV2PlusWrapperOptimismWrapperFulfillmentFailedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(VRFV2PlusWrapperOptimismWrapperFulfillmentFailed)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(VRFV2PlusWrapperOptimismWrapperFulfillmentFailed)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *VRFV2PlusWrapperOptimismWrapperFulfillmentFailedIterator) Error() error {
	return it.fail
}

func (it *VRFV2PlusWrapperOptimismWrapperFulfillmentFailedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type VRFV2PlusWrapperOptimismWrapperFulfillmentFailed struct {
	RequestId *big.Int
	Consumer  common.Address
	Raw       types.Log
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) FilterWrapperFulfillmentFailed(opts *bind.FilterOpts, requestId []*big.Int, consumer []common.Address) (*VRFV2PlusWrapperOptimismWrapperFulfillmentFailedIterator, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}
	var consumerRule []interface{}
	for _, consumerItem := range consumer {
		consumerRule = append(consumerRule, consumerItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.FilterLogs(opts, "WrapperFulfillmentFailed", requestIdRule, consumerRule)
	if err != nil {
		return nil, err
	}
	return &VRFV2PlusWrapperOptimismWrapperFulfillmentFailedIterator{contract: _VRFV2PlusWrapperOptimism.contract, event: "WrapperFulfillmentFailed", logs: logs, sub: sub}, nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) WatchWrapperFulfillmentFailed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismWrapperFulfillmentFailed, requestId []*big.Int, consumer []common.Address) (event.Subscription, error) {

	var requestIdRule []interface{}
	for _, requestIdItem := range requestId {
		requestIdRule = append(requestIdRule, requestIdItem)
	}
	var consumerRule []interface{}
	for _, consumerItem := range consumer {
		consumerRule = append(consumerRule, consumerItem)
	}

	logs, sub, err := _VRFV2PlusWrapperOptimism.contract.WatchLogs(opts, "WrapperFulfillmentFailed", requestIdRule, consumerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(VRFV2PlusWrapperOptimismWrapperFulfillmentFailed)
				if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "WrapperFulfillmentFailed", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimismFilterer) ParseWrapperFulfillmentFailed(log types.Log) (*VRFV2PlusWrapperOptimismWrapperFulfillmentFailed, error) {
	event := new(VRFV2PlusWrapperOptimismWrapperFulfillmentFailed)
	if err := _VRFV2PlusWrapperOptimism.contract.UnpackLog(event, "WrapperFulfillmentFailed", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type GetConfig struct {
	FallbackWeiPerUnitLink            *big.Int
	StalenessSeconds                  uint32
	FulfillmentFlatFeeNativePPM       uint32
	FulfillmentFlatFeeLinkDiscountPPM uint32
	WrapperGasOverhead                uint32
	CoordinatorGasOverheadNative      uint32
	CoordinatorGasOverheadLink        uint32
	CoordinatorGasOverheadPerWord     uint16
	WrapperNativePremiumPercentage    uint8
	WrapperLinkPremiumPercentage      uint8
	KeyHash                           [32]byte
	MaxNumWords                       uint8
}
type SCallbacks struct {
	CallbackAddress  common.Address
	CallbackGasLimit uint32
	RequestGasPrice  uint64
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimism) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _VRFV2PlusWrapperOptimism.abi.Events["ConfigSet"].ID:
		return _VRFV2PlusWrapperOptimism.ParseConfigSet(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["CoordinatorSet"].ID:
		return _VRFV2PlusWrapperOptimism.ParseCoordinatorSet(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["Disabled"].ID:
		return _VRFV2PlusWrapperOptimism.ParseDisabled(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["Enabled"].ID:
		return _VRFV2PlusWrapperOptimism.ParseEnabled(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["FallbackWeiPerUnitLinkUsed"].ID:
		return _VRFV2PlusWrapperOptimism.ParseFallbackWeiPerUnitLinkUsed(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["FulfillmentTxSizeSet"].ID:
		return _VRFV2PlusWrapperOptimism.ParseFulfillmentTxSizeSet(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["L1FeeCalculationSet"].ID:
		return _VRFV2PlusWrapperOptimism.ParseL1FeeCalculationSet(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["NativeWithdrawn"].ID:
		return _VRFV2PlusWrapperOptimism.ParseNativeWithdrawn(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["OwnershipTransferRequested"].ID:
		return _VRFV2PlusWrapperOptimism.ParseOwnershipTransferRequested(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["OwnershipTransferred"].ID:
		return _VRFV2PlusWrapperOptimism.ParseOwnershipTransferred(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["Withdrawn"].ID:
		return _VRFV2PlusWrapperOptimism.ParseWithdrawn(log)
	case _VRFV2PlusWrapperOptimism.abi.Events["WrapperFulfillmentFailed"].ID:
		return _VRFV2PlusWrapperOptimism.ParseWrapperFulfillmentFailed(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (VRFV2PlusWrapperOptimismConfigSet) Topic() common.Hash {
	return common.HexToHash("0x8aee1a8c131eaf1a5bd30594737b6926a7c5cb29281a97639f6ac93947c8995e")
}

func (VRFV2PlusWrapperOptimismCoordinatorSet) Topic() common.Hash {
	return common.HexToHash("0xd1a6a14209a385a964d036e404cb5cfb71f4000cdb03c9366292430787261be6")
}

func (VRFV2PlusWrapperOptimismDisabled) Topic() common.Hash {
	return common.HexToHash("0x75884cdadc4a89e8b545db800057f06ec7f5338a08183c7ba515f2bfdd9fe1e1")
}

func (VRFV2PlusWrapperOptimismEnabled) Topic() common.Hash {
	return common.HexToHash("0xc0f961051f97b04c496472d11cb6170d844e4b2c9dfd3b602a4fa0139712d484")
}

func (VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed) Topic() common.Hash {
	return common.HexToHash("0x6ca648a381f22ead7e37773d934e64885dcf861fbfbb26c40354cbf0c4662d1a")
}

func (VRFV2PlusWrapperOptimismFulfillmentTxSizeSet) Topic() common.Hash {
	return common.HexToHash("0x697b48b8b76cebb09a54ec4ff810e8a181c96f65395d51c744db09c115d1d5d0")
}

func (VRFV2PlusWrapperOptimismL1FeeCalculationSet) Topic() common.Hash {
	return common.HexToHash("0x8e63dc2f2e669ce73bebd2580bb9dd9a5d17fa2d046ac02057d8349fc0b0c2f3")
}

func (VRFV2PlusWrapperOptimismNativeWithdrawn) Topic() common.Hash {
	return common.HexToHash("0xc303ca808382409472acbbf899c316cf439f409f6584aae22df86dfa3c9ed504")
}

func (VRFV2PlusWrapperOptimismOwnershipTransferRequested) Topic() common.Hash {
	return common.HexToHash("0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278")
}

func (VRFV2PlusWrapperOptimismOwnershipTransferred) Topic() common.Hash {
	return common.HexToHash("0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0")
}

func (VRFV2PlusWrapperOptimismWithdrawn) Topic() common.Hash {
	return common.HexToHash("0x7084f5476618d8e60b11ef0d7d3f06914655adb8793e28ff7f018d4c76d505d5")
}

func (VRFV2PlusWrapperOptimismWrapperFulfillmentFailed) Topic() common.Hash {
	return common.HexToHash("0xc551b83c151f2d1c7eeb938ac59008e0409f1c1dc1e2f112449d4d79b4589022")
}

func (_VRFV2PlusWrapperOptimism *VRFV2PlusWrapperOptimism) Address() common.Address {
	return _VRFV2PlusWrapperOptimism.address
}

type VRFV2PlusWrapperOptimismInterface interface {
	SUBSCRIPTIONID(opts *bind.CallOpts) (*big.Int, error)

	CalculateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error)

	CalculateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32) (*big.Int, error)

	CheckPaymentMode(opts *bind.CallOpts, extraArgs []byte, isLinkMode bool) error

	EstimateRequestPrice(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error)

	EstimateRequestPriceNative(opts *bind.CallOpts, _callbackGasLimit uint32, _numWords uint32, _requestGasPriceWei *big.Int) (*big.Int, error)

	GetConfig(opts *bind.CallOpts) (GetConfig,

		error)

	LastRequestId(opts *bind.CallOpts) (*big.Int, error)

	Link(opts *bind.CallOpts) (common.Address, error)

	LinkNativeFeed(opts *bind.CallOpts) (common.Address, error)

	Owner(opts *bind.CallOpts) (common.Address, error)

	SCallbacks(opts *bind.CallOpts, arg0 *big.Int) (SCallbacks,

		error)

	SConfigured(opts *bind.CallOpts) (bool, error)

	SDisabled(opts *bind.CallOpts) (bool, error)

	SFulfillmentTxSizeBytes(opts *bind.CallOpts) (uint32, error)

	SL1FeeCalculationMode(opts *bind.CallOpts) (uint8, error)

	SL1FeeCoefficient(opts *bind.CallOpts) (uint8, error)

	SVrfCoordinator(opts *bind.CallOpts) (common.Address, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error)

	Disable(opts *bind.TransactOpts) (*types.Transaction, error)

	Enable(opts *bind.TransactOpts) (*types.Transaction, error)

	OnTokenTransfer(opts *bind.TransactOpts, _sender common.Address, _amount *big.Int, _data []byte) (*types.Transaction, error)

	RawFulfillRandomWords(opts *bind.TransactOpts, requestId *big.Int, randomWords []*big.Int) (*types.Transaction, error)

	RequestRandomWordsInNative(opts *bind.TransactOpts, _callbackGasLimit uint32, _requestConfirmations uint16, _numWords uint32, extraArgs []byte) (*types.Transaction, error)

	SetConfig(opts *bind.TransactOpts, _wrapperGasOverhead uint32, _coordinatorGasOverheadNative uint32, _coordinatorGasOverheadLink uint32, _coordinatorGasOverheadPerWord uint16, _coordinatorNativePremiumPercentage uint8, _coordinatorLinkPremiumPercentage uint8, _keyHash [32]byte, _maxNumWords uint8, _stalenessSeconds uint32, _fallbackWeiPerUnitLink *big.Int, _fulfillmentFlatFeeNativePPM uint32, _fulfillmentFlatFeeLinkDiscountPPM uint32) (*types.Transaction, error)

	SetCoordinator(opts *bind.TransactOpts, _vrfCoordinator common.Address) (*types.Transaction, error)

	SetFulfillmentTxSize(opts *bind.TransactOpts, _size uint32) (*types.Transaction, error)

	SetL1FeeCalculation(opts *bind.TransactOpts, mode uint8, coefficient uint8) (*types.Transaction, error)

	TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error)

	Withdraw(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error)

	WithdrawNative(opts *bind.TransactOpts, _recipient common.Address) (*types.Transaction, error)

	FilterConfigSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismConfigSetIterator, error)

	WatchConfigSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismConfigSet) (event.Subscription, error)

	ParseConfigSet(log types.Log) (*VRFV2PlusWrapperOptimismConfigSet, error)

	FilterCoordinatorSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismCoordinatorSetIterator, error)

	WatchCoordinatorSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismCoordinatorSet) (event.Subscription, error)

	ParseCoordinatorSet(log types.Log) (*VRFV2PlusWrapperOptimismCoordinatorSet, error)

	FilterDisabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismDisabledIterator, error)

	WatchDisabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismDisabled) (event.Subscription, error)

	ParseDisabled(log types.Log) (*VRFV2PlusWrapperOptimismDisabled, error)

	FilterEnabled(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismEnabledIterator, error)

	WatchEnabled(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismEnabled) (event.Subscription, error)

	ParseEnabled(log types.Log) (*VRFV2PlusWrapperOptimismEnabled, error)

	FilterFallbackWeiPerUnitLinkUsed(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsedIterator, error)

	WatchFallbackWeiPerUnitLinkUsed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed) (event.Subscription, error)

	ParseFallbackWeiPerUnitLinkUsed(log types.Log) (*VRFV2PlusWrapperOptimismFallbackWeiPerUnitLinkUsed, error)

	FilterFulfillmentTxSizeSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismFulfillmentTxSizeSetIterator, error)

	WatchFulfillmentTxSizeSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismFulfillmentTxSizeSet) (event.Subscription, error)

	ParseFulfillmentTxSizeSet(log types.Log) (*VRFV2PlusWrapperOptimismFulfillmentTxSizeSet, error)

	FilterL1FeeCalculationSet(opts *bind.FilterOpts) (*VRFV2PlusWrapperOptimismL1FeeCalculationSetIterator, error)

	WatchL1FeeCalculationSet(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismL1FeeCalculationSet) (event.Subscription, error)

	ParseL1FeeCalculationSet(log types.Log) (*VRFV2PlusWrapperOptimismL1FeeCalculationSet, error)

	FilterNativeWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperOptimismNativeWithdrawnIterator, error)

	WatchNativeWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismNativeWithdrawn, to []common.Address) (event.Subscription, error)

	ParseNativeWithdrawn(log types.Log) (*VRFV2PlusWrapperOptimismNativeWithdrawn, error)

	FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperOptimismOwnershipTransferRequestedIterator, error)

	WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferRequested(log types.Log) (*VRFV2PlusWrapperOptimismOwnershipTransferRequested, error)

	FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*VRFV2PlusWrapperOptimismOwnershipTransferredIterator, error)

	WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferred(log types.Log) (*VRFV2PlusWrapperOptimismOwnershipTransferred, error)

	FilterWithdrawn(opts *bind.FilterOpts, to []common.Address) (*VRFV2PlusWrapperOptimismWithdrawnIterator, error)

	WatchWithdrawn(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismWithdrawn, to []common.Address) (event.Subscription, error)

	ParseWithdrawn(log types.Log) (*VRFV2PlusWrapperOptimismWithdrawn, error)

	FilterWrapperFulfillmentFailed(opts *bind.FilterOpts, requestId []*big.Int, consumer []common.Address) (*VRFV2PlusWrapperOptimismWrapperFulfillmentFailedIterator, error)

	WatchWrapperFulfillmentFailed(opts *bind.WatchOpts, sink chan<- *VRFV2PlusWrapperOptimismWrapperFulfillmentFailed, requestId []*big.Int, consumer []common.Address) (event.Subscription, error)

	ParseWrapperFulfillmentFailed(log types.Log) (*VRFV2PlusWrapperOptimismWrapperFulfillmentFailed, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
