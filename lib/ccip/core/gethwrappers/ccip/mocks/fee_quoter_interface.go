// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_contracts

import (
	big "math/big"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	event "github.com/ethereum/go-ethereum/event"

	fee_quoter "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/ccip/generated/fee_quoter"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// FeeQuoterInterface is an autogenerated mock type for the FeeQuoterInterface type
type FeeQuoterInterface struct {
	mock.Mock
}

type FeeQuoterInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *FeeQuoterInterface) EXPECT() *FeeQuoterInterface_Expecter {
	return &FeeQuoterInterface_Expecter{mock: &_m.<PERSON>ck}
}

// AcceptOwnership provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for AcceptOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_AcceptOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptOwnership'
type FeeQuoterInterface_AcceptOwnership_Call struct {
	*mock.Call
}

// AcceptOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *FeeQuoterInterface_Expecter) AcceptOwnership(opts interface{}) *FeeQuoterInterface_AcceptOwnership_Call {
	return &FeeQuoterInterface_AcceptOwnership_Call{Call: _e.mock.On("AcceptOwnership", opts)}
}

func (_c *FeeQuoterInterface_AcceptOwnership_Call) Run(run func(opts *bind.TransactOpts)) *FeeQuoterInterface_AcceptOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_AcceptOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_AcceptOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_AcceptOwnership_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *FeeQuoterInterface_AcceptOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// Address provides a mock function with given fields:
func (_m *FeeQuoterInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// FeeQuoterInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type FeeQuoterInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *FeeQuoterInterface_Expecter) Address() *FeeQuoterInterface_Address_Call {
	return &FeeQuoterInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *FeeQuoterInterface_Address_Call) Run(run func()) *FeeQuoterInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *FeeQuoterInterface_Address_Call) Return(_a0 common.Address) *FeeQuoterInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *FeeQuoterInterface_Address_Call) RunAndReturn(run func() common.Address) *FeeQuoterInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// ApplyAuthorizedCallerUpdates provides a mock function with given fields: opts, authorizedCallerArgs
func (_m *FeeQuoterInterface) ApplyAuthorizedCallerUpdates(opts *bind.TransactOpts, authorizedCallerArgs fee_quoter.AuthorizedCallersAuthorizedCallerArgs) (*types.Transaction, error) {
	ret := _m.Called(opts, authorizedCallerArgs)

	if len(ret) == 0 {
		panic("no return value specified for ApplyAuthorizedCallerUpdates")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, fee_quoter.AuthorizedCallersAuthorizedCallerArgs) (*types.Transaction, error)); ok {
		return rf(opts, authorizedCallerArgs)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, fee_quoter.AuthorizedCallersAuthorizedCallerArgs) *types.Transaction); ok {
		r0 = rf(opts, authorizedCallerArgs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, fee_quoter.AuthorizedCallersAuthorizedCallerArgs) error); ok {
		r1 = rf(opts, authorizedCallerArgs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ApplyAuthorizedCallerUpdates'
type FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call struct {
	*mock.Call
}

// ApplyAuthorizedCallerUpdates is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - authorizedCallerArgs fee_quoter.AuthorizedCallersAuthorizedCallerArgs
func (_e *FeeQuoterInterface_Expecter) ApplyAuthorizedCallerUpdates(opts interface{}, authorizedCallerArgs interface{}) *FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call {
	return &FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call{Call: _e.mock.On("ApplyAuthorizedCallerUpdates", opts, authorizedCallerArgs)}
}

func (_c *FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call) Run(run func(opts *bind.TransactOpts, authorizedCallerArgs fee_quoter.AuthorizedCallersAuthorizedCallerArgs)) *FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(fee_quoter.AuthorizedCallersAuthorizedCallerArgs))
	})
	return _c
}

func (_c *FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call) RunAndReturn(run func(*bind.TransactOpts, fee_quoter.AuthorizedCallersAuthorizedCallerArgs) (*types.Transaction, error)) *FeeQuoterInterface_ApplyAuthorizedCallerUpdates_Call {
	_c.Call.Return(run)
	return _c
}

// ApplyDestChainConfigUpdates provides a mock function with given fields: opts, destChainConfigArgs
func (_m *FeeQuoterInterface) ApplyDestChainConfigUpdates(opts *bind.TransactOpts, destChainConfigArgs []fee_quoter.FeeQuoterDestChainConfigArgs) (*types.Transaction, error) {
	ret := _m.Called(opts, destChainConfigArgs)

	if len(ret) == 0 {
		panic("no return value specified for ApplyDestChainConfigUpdates")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterDestChainConfigArgs) (*types.Transaction, error)); ok {
		return rf(opts, destChainConfigArgs)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterDestChainConfigArgs) *types.Transaction); ok {
		r0 = rf(opts, destChainConfigArgs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterDestChainConfigArgs) error); ok {
		r1 = rf(opts, destChainConfigArgs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ApplyDestChainConfigUpdates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ApplyDestChainConfigUpdates'
type FeeQuoterInterface_ApplyDestChainConfigUpdates_Call struct {
	*mock.Call
}

// ApplyDestChainConfigUpdates is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - destChainConfigArgs []fee_quoter.FeeQuoterDestChainConfigArgs
func (_e *FeeQuoterInterface_Expecter) ApplyDestChainConfigUpdates(opts interface{}, destChainConfigArgs interface{}) *FeeQuoterInterface_ApplyDestChainConfigUpdates_Call {
	return &FeeQuoterInterface_ApplyDestChainConfigUpdates_Call{Call: _e.mock.On("ApplyDestChainConfigUpdates", opts, destChainConfigArgs)}
}

func (_c *FeeQuoterInterface_ApplyDestChainConfigUpdates_Call) Run(run func(opts *bind.TransactOpts, destChainConfigArgs []fee_quoter.FeeQuoterDestChainConfigArgs)) *FeeQuoterInterface_ApplyDestChainConfigUpdates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]fee_quoter.FeeQuoterDestChainConfigArgs))
	})
	return _c
}

func (_c *FeeQuoterInterface_ApplyDestChainConfigUpdates_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_ApplyDestChainConfigUpdates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ApplyDestChainConfigUpdates_Call) RunAndReturn(run func(*bind.TransactOpts, []fee_quoter.FeeQuoterDestChainConfigArgs) (*types.Transaction, error)) *FeeQuoterInterface_ApplyDestChainConfigUpdates_Call {
	_c.Call.Return(run)
	return _c
}

// ApplyFeeTokensUpdates provides a mock function with given fields: opts, feeTokensToAdd, feeTokensToRemove
func (_m *FeeQuoterInterface) ApplyFeeTokensUpdates(opts *bind.TransactOpts, feeTokensToAdd []common.Address, feeTokensToRemove []common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, feeTokensToAdd, feeTokensToRemove)

	if len(ret) == 0 {
		panic("no return value specified for ApplyFeeTokensUpdates")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address, []common.Address) (*types.Transaction, error)); ok {
		return rf(opts, feeTokensToAdd, feeTokensToRemove)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address, []common.Address) *types.Transaction); ok {
		r0 = rf(opts, feeTokensToAdd, feeTokensToRemove)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, feeTokensToAdd, feeTokensToRemove)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ApplyFeeTokensUpdates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ApplyFeeTokensUpdates'
type FeeQuoterInterface_ApplyFeeTokensUpdates_Call struct {
	*mock.Call
}

// ApplyFeeTokensUpdates is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - feeTokensToAdd []common.Address
//   - feeTokensToRemove []common.Address
func (_e *FeeQuoterInterface_Expecter) ApplyFeeTokensUpdates(opts interface{}, feeTokensToAdd interface{}, feeTokensToRemove interface{}) *FeeQuoterInterface_ApplyFeeTokensUpdates_Call {
	return &FeeQuoterInterface_ApplyFeeTokensUpdates_Call{Call: _e.mock.On("ApplyFeeTokensUpdates", opts, feeTokensToAdd, feeTokensToRemove)}
}

func (_c *FeeQuoterInterface_ApplyFeeTokensUpdates_Call) Run(run func(opts *bind.TransactOpts, feeTokensToAdd []common.Address, feeTokensToRemove []common.Address)) *FeeQuoterInterface_ApplyFeeTokensUpdates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_ApplyFeeTokensUpdates_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_ApplyFeeTokensUpdates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ApplyFeeTokensUpdates_Call) RunAndReturn(run func(*bind.TransactOpts, []common.Address, []common.Address) (*types.Transaction, error)) *FeeQuoterInterface_ApplyFeeTokensUpdates_Call {
	_c.Call.Return(run)
	return _c
}

// ApplyPremiumMultiplierWeiPerEthUpdates provides a mock function with given fields: opts, premiumMultiplierWeiPerEthArgs
func (_m *FeeQuoterInterface) ApplyPremiumMultiplierWeiPerEthUpdates(opts *bind.TransactOpts, premiumMultiplierWeiPerEthArgs []fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthArgs) (*types.Transaction, error) {
	ret := _m.Called(opts, premiumMultiplierWeiPerEthArgs)

	if len(ret) == 0 {
		panic("no return value specified for ApplyPremiumMultiplierWeiPerEthUpdates")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthArgs) (*types.Transaction, error)); ok {
		return rf(opts, premiumMultiplierWeiPerEthArgs)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthArgs) *types.Transaction); ok {
		r0 = rf(opts, premiumMultiplierWeiPerEthArgs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthArgs) error); ok {
		r1 = rf(opts, premiumMultiplierWeiPerEthArgs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ApplyPremiumMultiplierWeiPerEthUpdates'
type FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call struct {
	*mock.Call
}

// ApplyPremiumMultiplierWeiPerEthUpdates is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - premiumMultiplierWeiPerEthArgs []fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthArgs
func (_e *FeeQuoterInterface_Expecter) ApplyPremiumMultiplierWeiPerEthUpdates(opts interface{}, premiumMultiplierWeiPerEthArgs interface{}) *FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call {
	return &FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call{Call: _e.mock.On("ApplyPremiumMultiplierWeiPerEthUpdates", opts, premiumMultiplierWeiPerEthArgs)}
}

func (_c *FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call) Run(run func(opts *bind.TransactOpts, premiumMultiplierWeiPerEthArgs []fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthArgs)) *FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthArgs))
	})
	return _c
}

func (_c *FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call) RunAndReturn(run func(*bind.TransactOpts, []fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthArgs) (*types.Transaction, error)) *FeeQuoterInterface_ApplyPremiumMultiplierWeiPerEthUpdates_Call {
	_c.Call.Return(run)
	return _c
}

// ApplyTokenTransferFeeConfigUpdates provides a mock function with given fields: opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs
func (_m *FeeQuoterInterface) ApplyTokenTransferFeeConfigUpdates(opts *bind.TransactOpts, tokenTransferFeeConfigArgs []fee_quoter.FeeQuoterTokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs []fee_quoter.FeeQuoterTokenTransferFeeConfigRemoveArgs) (*types.Transaction, error) {
	ret := _m.Called(opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)

	if len(ret) == 0 {
		panic("no return value specified for ApplyTokenTransferFeeConfigUpdates")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterTokenTransferFeeConfigArgs, []fee_quoter.FeeQuoterTokenTransferFeeConfigRemoveArgs) (*types.Transaction, error)); ok {
		return rf(opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterTokenTransferFeeConfigArgs, []fee_quoter.FeeQuoterTokenTransferFeeConfigRemoveArgs) *types.Transaction); ok {
		r0 = rf(opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterTokenTransferFeeConfigArgs, []fee_quoter.FeeQuoterTokenTransferFeeConfigRemoveArgs) error); ok {
		r1 = rf(opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ApplyTokenTransferFeeConfigUpdates'
type FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call struct {
	*mock.Call
}

// ApplyTokenTransferFeeConfigUpdates is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - tokenTransferFeeConfigArgs []fee_quoter.FeeQuoterTokenTransferFeeConfigArgs
//   - tokensToUseDefaultFeeConfigs []fee_quoter.FeeQuoterTokenTransferFeeConfigRemoveArgs
func (_e *FeeQuoterInterface_Expecter) ApplyTokenTransferFeeConfigUpdates(opts interface{}, tokenTransferFeeConfigArgs interface{}, tokensToUseDefaultFeeConfigs interface{}) *FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call {
	return &FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call{Call: _e.mock.On("ApplyTokenTransferFeeConfigUpdates", opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)}
}

func (_c *FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call) Run(run func(opts *bind.TransactOpts, tokenTransferFeeConfigArgs []fee_quoter.FeeQuoterTokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs []fee_quoter.FeeQuoterTokenTransferFeeConfigRemoveArgs)) *FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]fee_quoter.FeeQuoterTokenTransferFeeConfigArgs), args[2].([]fee_quoter.FeeQuoterTokenTransferFeeConfigRemoveArgs))
	})
	return _c
}

func (_c *FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call) RunAndReturn(run func(*bind.TransactOpts, []fee_quoter.FeeQuoterTokenTransferFeeConfigArgs, []fee_quoter.FeeQuoterTokenTransferFeeConfigRemoveArgs) (*types.Transaction, error)) *FeeQuoterInterface_ApplyTokenTransferFeeConfigUpdates_Call {
	_c.Call.Return(run)
	return _c
}

// ConvertTokenAmount provides a mock function with given fields: opts, fromToken, fromTokenAmount, toToken
func (_m *FeeQuoterInterface) ConvertTokenAmount(opts *bind.CallOpts, fromToken common.Address, fromTokenAmount *big.Int, toToken common.Address) (*big.Int, error) {
	ret := _m.Called(opts, fromToken, fromTokenAmount, toToken)

	if len(ret) == 0 {
		panic("no return value specified for ConvertTokenAmount")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, *big.Int, common.Address) (*big.Int, error)); ok {
		return rf(opts, fromToken, fromTokenAmount, toToken)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, *big.Int, common.Address) *big.Int); ok {
		r0 = rf(opts, fromToken, fromTokenAmount, toToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address, *big.Int, common.Address) error); ok {
		r1 = rf(opts, fromToken, fromTokenAmount, toToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ConvertTokenAmount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ConvertTokenAmount'
type FeeQuoterInterface_ConvertTokenAmount_Call struct {
	*mock.Call
}

// ConvertTokenAmount is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - fromToken common.Address
//   - fromTokenAmount *big.Int
//   - toToken common.Address
func (_e *FeeQuoterInterface_Expecter) ConvertTokenAmount(opts interface{}, fromToken interface{}, fromTokenAmount interface{}, toToken interface{}) *FeeQuoterInterface_ConvertTokenAmount_Call {
	return &FeeQuoterInterface_ConvertTokenAmount_Call{Call: _e.mock.On("ConvertTokenAmount", opts, fromToken, fromTokenAmount, toToken)}
}

func (_c *FeeQuoterInterface_ConvertTokenAmount_Call) Run(run func(opts *bind.CallOpts, fromToken common.Address, fromTokenAmount *big.Int, toToken common.Address)) *FeeQuoterInterface_ConvertTokenAmount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address), args[2].(*big.Int), args[3].(common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_ConvertTokenAmount_Call) Return(_a0 *big.Int, _a1 error) *FeeQuoterInterface_ConvertTokenAmount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ConvertTokenAmount_Call) RunAndReturn(run func(*bind.CallOpts, common.Address, *big.Int, common.Address) (*big.Int, error)) *FeeQuoterInterface_ConvertTokenAmount_Call {
	_c.Call.Return(run)
	return _c
}

// FEEBASEDECIMALS provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) FEEBASEDECIMALS(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FEEBASEDECIMALS")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FEEBASEDECIMALS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FEEBASEDECIMALS'
type FeeQuoterInterface_FEEBASEDECIMALS_Call struct {
	*mock.Call
}

// FEEBASEDECIMALS is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *FeeQuoterInterface_Expecter) FEEBASEDECIMALS(opts interface{}) *FeeQuoterInterface_FEEBASEDECIMALS_Call {
	return &FeeQuoterInterface_FEEBASEDECIMALS_Call{Call: _e.mock.On("FEEBASEDECIMALS", opts)}
}

func (_c *FeeQuoterInterface_FEEBASEDECIMALS_Call) Run(run func(opts *bind.CallOpts)) *FeeQuoterInterface_FEEBASEDECIMALS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_FEEBASEDECIMALS_Call) Return(_a0 *big.Int, _a1 error) *FeeQuoterInterface_FEEBASEDECIMALS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FEEBASEDECIMALS_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *FeeQuoterInterface_FEEBASEDECIMALS_Call {
	_c.Call.Return(run)
	return _c
}

// FilterAuthorizedCallerAdded provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) FilterAuthorizedCallerAdded(opts *bind.FilterOpts) (*fee_quoter.FeeQuoterAuthorizedCallerAddedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterAuthorizedCallerAdded")
	}

	var r0 *fee_quoter.FeeQuoterAuthorizedCallerAddedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*fee_quoter.FeeQuoterAuthorizedCallerAddedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *fee_quoter.FeeQuoterAuthorizedCallerAddedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterAuthorizedCallerAddedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterAuthorizedCallerAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterAuthorizedCallerAdded'
type FeeQuoterInterface_FilterAuthorizedCallerAdded_Call struct {
	*mock.Call
}

// FilterAuthorizedCallerAdded is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *FeeQuoterInterface_Expecter) FilterAuthorizedCallerAdded(opts interface{}) *FeeQuoterInterface_FilterAuthorizedCallerAdded_Call {
	return &FeeQuoterInterface_FilterAuthorizedCallerAdded_Call{Call: _e.mock.On("FilterAuthorizedCallerAdded", opts)}
}

func (_c *FeeQuoterInterface_FilterAuthorizedCallerAdded_Call) Run(run func(opts *bind.FilterOpts)) *FeeQuoterInterface_FilterAuthorizedCallerAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterAuthorizedCallerAdded_Call) Return(_a0 *fee_quoter.FeeQuoterAuthorizedCallerAddedIterator, _a1 error) *FeeQuoterInterface_FilterAuthorizedCallerAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterAuthorizedCallerAdded_Call) RunAndReturn(run func(*bind.FilterOpts) (*fee_quoter.FeeQuoterAuthorizedCallerAddedIterator, error)) *FeeQuoterInterface_FilterAuthorizedCallerAdded_Call {
	_c.Call.Return(run)
	return _c
}

// FilterAuthorizedCallerRemoved provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) FilterAuthorizedCallerRemoved(opts *bind.FilterOpts) (*fee_quoter.FeeQuoterAuthorizedCallerRemovedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterAuthorizedCallerRemoved")
	}

	var r0 *fee_quoter.FeeQuoterAuthorizedCallerRemovedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*fee_quoter.FeeQuoterAuthorizedCallerRemovedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *fee_quoter.FeeQuoterAuthorizedCallerRemovedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterAuthorizedCallerRemovedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterAuthorizedCallerRemoved'
type FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call struct {
	*mock.Call
}

// FilterAuthorizedCallerRemoved is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *FeeQuoterInterface_Expecter) FilterAuthorizedCallerRemoved(opts interface{}) *FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call {
	return &FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call{Call: _e.mock.On("FilterAuthorizedCallerRemoved", opts)}
}

func (_c *FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call) Run(run func(opts *bind.FilterOpts)) *FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call) Return(_a0 *fee_quoter.FeeQuoterAuthorizedCallerRemovedIterator, _a1 error) *FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call) RunAndReturn(run func(*bind.FilterOpts) (*fee_quoter.FeeQuoterAuthorizedCallerRemovedIterator, error)) *FeeQuoterInterface_FilterAuthorizedCallerRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// FilterDestChainAdded provides a mock function with given fields: opts, destChainSelector
func (_m *FeeQuoterInterface) FilterDestChainAdded(opts *bind.FilterOpts, destChainSelector []uint64) (*fee_quoter.FeeQuoterDestChainAddedIterator, error) {
	ret := _m.Called(opts, destChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for FilterDestChainAdded")
	}

	var r0 *fee_quoter.FeeQuoterDestChainAddedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*fee_quoter.FeeQuoterDestChainAddedIterator, error)); ok {
		return rf(opts, destChainSelector)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *fee_quoter.FeeQuoterDestChainAddedIterator); ok {
		r0 = rf(opts, destChainSelector)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterDestChainAddedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, destChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterDestChainAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterDestChainAdded'
type FeeQuoterInterface_FilterDestChainAdded_Call struct {
	*mock.Call
}

// FilterDestChainAdded is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - destChainSelector []uint64
func (_e *FeeQuoterInterface_Expecter) FilterDestChainAdded(opts interface{}, destChainSelector interface{}) *FeeQuoterInterface_FilterDestChainAdded_Call {
	return &FeeQuoterInterface_FilterDestChainAdded_Call{Call: _e.mock.On("FilterDestChainAdded", opts, destChainSelector)}
}

func (_c *FeeQuoterInterface_FilterDestChainAdded_Call) Run(run func(opts *bind.FilterOpts, destChainSelector []uint64)) *FeeQuoterInterface_FilterDestChainAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterDestChainAdded_Call) Return(_a0 *fee_quoter.FeeQuoterDestChainAddedIterator, _a1 error) *FeeQuoterInterface_FilterDestChainAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterDestChainAdded_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*fee_quoter.FeeQuoterDestChainAddedIterator, error)) *FeeQuoterInterface_FilterDestChainAdded_Call {
	_c.Call.Return(run)
	return _c
}

// FilterDestChainConfigUpdated provides a mock function with given fields: opts, destChainSelector
func (_m *FeeQuoterInterface) FilterDestChainConfigUpdated(opts *bind.FilterOpts, destChainSelector []uint64) (*fee_quoter.FeeQuoterDestChainConfigUpdatedIterator, error) {
	ret := _m.Called(opts, destChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for FilterDestChainConfigUpdated")
	}

	var r0 *fee_quoter.FeeQuoterDestChainConfigUpdatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*fee_quoter.FeeQuoterDestChainConfigUpdatedIterator, error)); ok {
		return rf(opts, destChainSelector)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *fee_quoter.FeeQuoterDestChainConfigUpdatedIterator); ok {
		r0 = rf(opts, destChainSelector)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterDestChainConfigUpdatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, destChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterDestChainConfigUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterDestChainConfigUpdated'
type FeeQuoterInterface_FilterDestChainConfigUpdated_Call struct {
	*mock.Call
}

// FilterDestChainConfigUpdated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - destChainSelector []uint64
func (_e *FeeQuoterInterface_Expecter) FilterDestChainConfigUpdated(opts interface{}, destChainSelector interface{}) *FeeQuoterInterface_FilterDestChainConfigUpdated_Call {
	return &FeeQuoterInterface_FilterDestChainConfigUpdated_Call{Call: _e.mock.On("FilterDestChainConfigUpdated", opts, destChainSelector)}
}

func (_c *FeeQuoterInterface_FilterDestChainConfigUpdated_Call) Run(run func(opts *bind.FilterOpts, destChainSelector []uint64)) *FeeQuoterInterface_FilterDestChainConfigUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterDestChainConfigUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterDestChainConfigUpdatedIterator, _a1 error) *FeeQuoterInterface_FilterDestChainConfigUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterDestChainConfigUpdated_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*fee_quoter.FeeQuoterDestChainConfigUpdatedIterator, error)) *FeeQuoterInterface_FilterDestChainConfigUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// FilterFeeTokenAdded provides a mock function with given fields: opts, feeToken
func (_m *FeeQuoterInterface) FilterFeeTokenAdded(opts *bind.FilterOpts, feeToken []common.Address) (*fee_quoter.FeeQuoterFeeTokenAddedIterator, error) {
	ret := _m.Called(opts, feeToken)

	if len(ret) == 0 {
		panic("no return value specified for FilterFeeTokenAdded")
	}

	var r0 *fee_quoter.FeeQuoterFeeTokenAddedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterFeeTokenAddedIterator, error)); ok {
		return rf(opts, feeToken)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *fee_quoter.FeeQuoterFeeTokenAddedIterator); ok {
		r0 = rf(opts, feeToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterFeeTokenAddedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, feeToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterFeeTokenAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterFeeTokenAdded'
type FeeQuoterInterface_FilterFeeTokenAdded_Call struct {
	*mock.Call
}

// FilterFeeTokenAdded is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - feeToken []common.Address
func (_e *FeeQuoterInterface_Expecter) FilterFeeTokenAdded(opts interface{}, feeToken interface{}) *FeeQuoterInterface_FilterFeeTokenAdded_Call {
	return &FeeQuoterInterface_FilterFeeTokenAdded_Call{Call: _e.mock.On("FilterFeeTokenAdded", opts, feeToken)}
}

func (_c *FeeQuoterInterface_FilterFeeTokenAdded_Call) Run(run func(opts *bind.FilterOpts, feeToken []common.Address)) *FeeQuoterInterface_FilterFeeTokenAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterFeeTokenAdded_Call) Return(_a0 *fee_quoter.FeeQuoterFeeTokenAddedIterator, _a1 error) *FeeQuoterInterface_FilterFeeTokenAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterFeeTokenAdded_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterFeeTokenAddedIterator, error)) *FeeQuoterInterface_FilterFeeTokenAdded_Call {
	_c.Call.Return(run)
	return _c
}

// FilterFeeTokenRemoved provides a mock function with given fields: opts, feeToken
func (_m *FeeQuoterInterface) FilterFeeTokenRemoved(opts *bind.FilterOpts, feeToken []common.Address) (*fee_quoter.FeeQuoterFeeTokenRemovedIterator, error) {
	ret := _m.Called(opts, feeToken)

	if len(ret) == 0 {
		panic("no return value specified for FilterFeeTokenRemoved")
	}

	var r0 *fee_quoter.FeeQuoterFeeTokenRemovedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterFeeTokenRemovedIterator, error)); ok {
		return rf(opts, feeToken)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *fee_quoter.FeeQuoterFeeTokenRemovedIterator); ok {
		r0 = rf(opts, feeToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterFeeTokenRemovedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, feeToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterFeeTokenRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterFeeTokenRemoved'
type FeeQuoterInterface_FilterFeeTokenRemoved_Call struct {
	*mock.Call
}

// FilterFeeTokenRemoved is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - feeToken []common.Address
func (_e *FeeQuoterInterface_Expecter) FilterFeeTokenRemoved(opts interface{}, feeToken interface{}) *FeeQuoterInterface_FilterFeeTokenRemoved_Call {
	return &FeeQuoterInterface_FilterFeeTokenRemoved_Call{Call: _e.mock.On("FilterFeeTokenRemoved", opts, feeToken)}
}

func (_c *FeeQuoterInterface_FilterFeeTokenRemoved_Call) Run(run func(opts *bind.FilterOpts, feeToken []common.Address)) *FeeQuoterInterface_FilterFeeTokenRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterFeeTokenRemoved_Call) Return(_a0 *fee_quoter.FeeQuoterFeeTokenRemovedIterator, _a1 error) *FeeQuoterInterface_FilterFeeTokenRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterFeeTokenRemoved_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterFeeTokenRemovedIterator, error)) *FeeQuoterInterface_FilterFeeTokenRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferRequested provides a mock function with given fields: opts, from, to
func (_m *FeeQuoterInterface) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*fee_quoter.FeeQuoterOwnershipTransferRequestedIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferRequested")
	}

	var r0 *fee_quoter.FeeQuoterOwnershipTransferRequestedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*fee_quoter.FeeQuoterOwnershipTransferRequestedIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *fee_quoter.FeeQuoterOwnershipTransferRequestedIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterOwnershipTransferRequestedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferRequested'
type FeeQuoterInterface_FilterOwnershipTransferRequested_Call struct {
	*mock.Call
}

// FilterOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *FeeQuoterInterface_Expecter) FilterOwnershipTransferRequested(opts interface{}, from interface{}, to interface{}) *FeeQuoterInterface_FilterOwnershipTransferRequested_Call {
	return &FeeQuoterInterface_FilterOwnershipTransferRequested_Call{Call: _e.mock.On("FilterOwnershipTransferRequested", opts, from, to)}
}

func (_c *FeeQuoterInterface_FilterOwnershipTransferRequested_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *FeeQuoterInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterOwnershipTransferRequested_Call) Return(_a0 *fee_quoter.FeeQuoterOwnershipTransferRequestedIterator, _a1 error) *FeeQuoterInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*fee_quoter.FeeQuoterOwnershipTransferRequestedIterator, error)) *FeeQuoterInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferred provides a mock function with given fields: opts, from, to
func (_m *FeeQuoterInterface) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*fee_quoter.FeeQuoterOwnershipTransferredIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferred")
	}

	var r0 *fee_quoter.FeeQuoterOwnershipTransferredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*fee_quoter.FeeQuoterOwnershipTransferredIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *fee_quoter.FeeQuoterOwnershipTransferredIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterOwnershipTransferredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferred'
type FeeQuoterInterface_FilterOwnershipTransferred_Call struct {
	*mock.Call
}

// FilterOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *FeeQuoterInterface_Expecter) FilterOwnershipTransferred(opts interface{}, from interface{}, to interface{}) *FeeQuoterInterface_FilterOwnershipTransferred_Call {
	return &FeeQuoterInterface_FilterOwnershipTransferred_Call{Call: _e.mock.On("FilterOwnershipTransferred", opts, from, to)}
}

func (_c *FeeQuoterInterface_FilterOwnershipTransferred_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *FeeQuoterInterface_FilterOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterOwnershipTransferred_Call) Return(_a0 *fee_quoter.FeeQuoterOwnershipTransferredIterator, _a1 error) *FeeQuoterInterface_FilterOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterOwnershipTransferred_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*fee_quoter.FeeQuoterOwnershipTransferredIterator, error)) *FeeQuoterInterface_FilterOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// FilterPremiumMultiplierWeiPerEthUpdated provides a mock function with given fields: opts, token
func (_m *FeeQuoterInterface) FilterPremiumMultiplierWeiPerEthUpdated(opts *bind.FilterOpts, token []common.Address) (*fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdatedIterator, error) {
	ret := _m.Called(opts, token)

	if len(ret) == 0 {
		panic("no return value specified for FilterPremiumMultiplierWeiPerEthUpdated")
	}

	var r0 *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdatedIterator, error)); ok {
		return rf(opts, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdatedIterator); ok {
		r0 = rf(opts, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterPremiumMultiplierWeiPerEthUpdated'
type FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call struct {
	*mock.Call
}

// FilterPremiumMultiplierWeiPerEthUpdated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) FilterPremiumMultiplierWeiPerEthUpdated(opts interface{}, token interface{}) *FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call {
	return &FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call{Call: _e.mock.On("FilterPremiumMultiplierWeiPerEthUpdated", opts, token)}
}

func (_c *FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call) Run(run func(opts *bind.FilterOpts, token []common.Address)) *FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdatedIterator, _a1 error) *FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdatedIterator, error)) *FeeQuoterInterface_FilterPremiumMultiplierWeiPerEthUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// FilterPriceFeedPerTokenUpdated provides a mock function with given fields: opts, token
func (_m *FeeQuoterInterface) FilterPriceFeedPerTokenUpdated(opts *bind.FilterOpts, token []common.Address) (*fee_quoter.FeeQuoterPriceFeedPerTokenUpdatedIterator, error) {
	ret := _m.Called(opts, token)

	if len(ret) == 0 {
		panic("no return value specified for FilterPriceFeedPerTokenUpdated")
	}

	var r0 *fee_quoter.FeeQuoterPriceFeedPerTokenUpdatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterPriceFeedPerTokenUpdatedIterator, error)); ok {
		return rf(opts, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *fee_quoter.FeeQuoterPriceFeedPerTokenUpdatedIterator); ok {
		r0 = rf(opts, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterPriceFeedPerTokenUpdatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterPriceFeedPerTokenUpdated'
type FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call struct {
	*mock.Call
}

// FilterPriceFeedPerTokenUpdated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) FilterPriceFeedPerTokenUpdated(opts interface{}, token interface{}) *FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call {
	return &FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call{Call: _e.mock.On("FilterPriceFeedPerTokenUpdated", opts, token)}
}

func (_c *FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call) Run(run func(opts *bind.FilterOpts, token []common.Address)) *FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterPriceFeedPerTokenUpdatedIterator, _a1 error) *FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterPriceFeedPerTokenUpdatedIterator, error)) *FeeQuoterInterface_FilterPriceFeedPerTokenUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// FilterReportPermissionSet provides a mock function with given fields: opts, reportId
func (_m *FeeQuoterInterface) FilterReportPermissionSet(opts *bind.FilterOpts, reportId [][32]byte) (*fee_quoter.FeeQuoterReportPermissionSetIterator, error) {
	ret := _m.Called(opts, reportId)

	if len(ret) == 0 {
		panic("no return value specified for FilterReportPermissionSet")
	}

	var r0 *fee_quoter.FeeQuoterReportPermissionSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, [][32]byte) (*fee_quoter.FeeQuoterReportPermissionSetIterator, error)); ok {
		return rf(opts, reportId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, [][32]byte) *fee_quoter.FeeQuoterReportPermissionSetIterator); ok {
		r0 = rf(opts, reportId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterReportPermissionSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, [][32]byte) error); ok {
		r1 = rf(opts, reportId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterReportPermissionSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterReportPermissionSet'
type FeeQuoterInterface_FilterReportPermissionSet_Call struct {
	*mock.Call
}

// FilterReportPermissionSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - reportId [][32]byte
func (_e *FeeQuoterInterface_Expecter) FilterReportPermissionSet(opts interface{}, reportId interface{}) *FeeQuoterInterface_FilterReportPermissionSet_Call {
	return &FeeQuoterInterface_FilterReportPermissionSet_Call{Call: _e.mock.On("FilterReportPermissionSet", opts, reportId)}
}

func (_c *FeeQuoterInterface_FilterReportPermissionSet_Call) Run(run func(opts *bind.FilterOpts, reportId [][32]byte)) *FeeQuoterInterface_FilterReportPermissionSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([][32]byte))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterReportPermissionSet_Call) Return(_a0 *fee_quoter.FeeQuoterReportPermissionSetIterator, _a1 error) *FeeQuoterInterface_FilterReportPermissionSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterReportPermissionSet_Call) RunAndReturn(run func(*bind.FilterOpts, [][32]byte) (*fee_quoter.FeeQuoterReportPermissionSetIterator, error)) *FeeQuoterInterface_FilterReportPermissionSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterTokenTransferFeeConfigDeleted provides a mock function with given fields: opts, destChainSelector, token
func (_m *FeeQuoterInterface) FilterTokenTransferFeeConfigDeleted(opts *bind.FilterOpts, destChainSelector []uint64, token []common.Address) (*fee_quoter.FeeQuoterTokenTransferFeeConfigDeletedIterator, error) {
	ret := _m.Called(opts, destChainSelector, token)

	if len(ret) == 0 {
		panic("no return value specified for FilterTokenTransferFeeConfigDeleted")
	}

	var r0 *fee_quoter.FeeQuoterTokenTransferFeeConfigDeletedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, []common.Address) (*fee_quoter.FeeQuoterTokenTransferFeeConfigDeletedIterator, error)); ok {
		return rf(opts, destChainSelector, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, []common.Address) *fee_quoter.FeeQuoterTokenTransferFeeConfigDeletedIterator); ok {
		r0 = rf(opts, destChainSelector, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterTokenTransferFeeConfigDeletedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64, []common.Address) error); ok {
		r1 = rf(opts, destChainSelector, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterTokenTransferFeeConfigDeleted'
type FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call struct {
	*mock.Call
}

// FilterTokenTransferFeeConfigDeleted is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - destChainSelector []uint64
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) FilterTokenTransferFeeConfigDeleted(opts interface{}, destChainSelector interface{}, token interface{}) *FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call {
	return &FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call{Call: _e.mock.On("FilterTokenTransferFeeConfigDeleted", opts, destChainSelector, token)}
}

func (_c *FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call) Run(run func(opts *bind.FilterOpts, destChainSelector []uint64, token []common.Address)) *FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call) Return(_a0 *fee_quoter.FeeQuoterTokenTransferFeeConfigDeletedIterator, _a1 error) *FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64, []common.Address) (*fee_quoter.FeeQuoterTokenTransferFeeConfigDeletedIterator, error)) *FeeQuoterInterface_FilterTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(run)
	return _c
}

// FilterTokenTransferFeeConfigUpdated provides a mock function with given fields: opts, destChainSelector, token
func (_m *FeeQuoterInterface) FilterTokenTransferFeeConfigUpdated(opts *bind.FilterOpts, destChainSelector []uint64, token []common.Address) (*fee_quoter.FeeQuoterTokenTransferFeeConfigUpdatedIterator, error) {
	ret := _m.Called(opts, destChainSelector, token)

	if len(ret) == 0 {
		panic("no return value specified for FilterTokenTransferFeeConfigUpdated")
	}

	var r0 *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, []common.Address) (*fee_quoter.FeeQuoterTokenTransferFeeConfigUpdatedIterator, error)); ok {
		return rf(opts, destChainSelector, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, []common.Address) *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdatedIterator); ok {
		r0 = rf(opts, destChainSelector, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterTokenTransferFeeConfigUpdatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64, []common.Address) error); ok {
		r1 = rf(opts, destChainSelector, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterTokenTransferFeeConfigUpdated'
type FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call struct {
	*mock.Call
}

// FilterTokenTransferFeeConfigUpdated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - destChainSelector []uint64
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) FilterTokenTransferFeeConfigUpdated(opts interface{}, destChainSelector interface{}, token interface{}) *FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call {
	return &FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call{Call: _e.mock.On("FilterTokenTransferFeeConfigUpdated", opts, destChainSelector, token)}
}

func (_c *FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call) Run(run func(opts *bind.FilterOpts, destChainSelector []uint64, token []common.Address)) *FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdatedIterator, _a1 error) *FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64, []common.Address) (*fee_quoter.FeeQuoterTokenTransferFeeConfigUpdatedIterator, error)) *FeeQuoterInterface_FilterTokenTransferFeeConfigUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// FilterUsdPerTokenUpdated provides a mock function with given fields: opts, token
func (_m *FeeQuoterInterface) FilterUsdPerTokenUpdated(opts *bind.FilterOpts, token []common.Address) (*fee_quoter.FeeQuoterUsdPerTokenUpdatedIterator, error) {
	ret := _m.Called(opts, token)

	if len(ret) == 0 {
		panic("no return value specified for FilterUsdPerTokenUpdated")
	}

	var r0 *fee_quoter.FeeQuoterUsdPerTokenUpdatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterUsdPerTokenUpdatedIterator, error)); ok {
		return rf(opts, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *fee_quoter.FeeQuoterUsdPerTokenUpdatedIterator); ok {
		r0 = rf(opts, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterUsdPerTokenUpdatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterUsdPerTokenUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterUsdPerTokenUpdated'
type FeeQuoterInterface_FilterUsdPerTokenUpdated_Call struct {
	*mock.Call
}

// FilterUsdPerTokenUpdated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) FilterUsdPerTokenUpdated(opts interface{}, token interface{}) *FeeQuoterInterface_FilterUsdPerTokenUpdated_Call {
	return &FeeQuoterInterface_FilterUsdPerTokenUpdated_Call{Call: _e.mock.On("FilterUsdPerTokenUpdated", opts, token)}
}

func (_c *FeeQuoterInterface_FilterUsdPerTokenUpdated_Call) Run(run func(opts *bind.FilterOpts, token []common.Address)) *FeeQuoterInterface_FilterUsdPerTokenUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterUsdPerTokenUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterUsdPerTokenUpdatedIterator, _a1 error) *FeeQuoterInterface_FilterUsdPerTokenUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterUsdPerTokenUpdated_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*fee_quoter.FeeQuoterUsdPerTokenUpdatedIterator, error)) *FeeQuoterInterface_FilterUsdPerTokenUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// FilterUsdPerUnitGasUpdated provides a mock function with given fields: opts, destChain
func (_m *FeeQuoterInterface) FilterUsdPerUnitGasUpdated(opts *bind.FilterOpts, destChain []uint64) (*fee_quoter.FeeQuoterUsdPerUnitGasUpdatedIterator, error) {
	ret := _m.Called(opts, destChain)

	if len(ret) == 0 {
		panic("no return value specified for FilterUsdPerUnitGasUpdated")
	}

	var r0 *fee_quoter.FeeQuoterUsdPerUnitGasUpdatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*fee_quoter.FeeQuoterUsdPerUnitGasUpdatedIterator, error)); ok {
		return rf(opts, destChain)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *fee_quoter.FeeQuoterUsdPerUnitGasUpdatedIterator); ok {
		r0 = rf(opts, destChain)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterUsdPerUnitGasUpdatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, destChain)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterUsdPerUnitGasUpdated'
type FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call struct {
	*mock.Call
}

// FilterUsdPerUnitGasUpdated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - destChain []uint64
func (_e *FeeQuoterInterface_Expecter) FilterUsdPerUnitGasUpdated(opts interface{}, destChain interface{}) *FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call {
	return &FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call{Call: _e.mock.On("FilterUsdPerUnitGasUpdated", opts, destChain)}
}

func (_c *FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call) Run(run func(opts *bind.FilterOpts, destChain []uint64)) *FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterUsdPerUnitGasUpdatedIterator, _a1 error) *FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*fee_quoter.FeeQuoterUsdPerUnitGasUpdatedIterator, error)) *FeeQuoterInterface_FilterUsdPerUnitGasUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllAuthorizedCallers provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) GetAllAuthorizedCallers(opts *bind.CallOpts) ([]common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetAllAuthorizedCallers")
	}

	var r0 []common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) ([]common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) []common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetAllAuthorizedCallers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllAuthorizedCallers'
type FeeQuoterInterface_GetAllAuthorizedCallers_Call struct {
	*mock.Call
}

// GetAllAuthorizedCallers is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *FeeQuoterInterface_Expecter) GetAllAuthorizedCallers(opts interface{}) *FeeQuoterInterface_GetAllAuthorizedCallers_Call {
	return &FeeQuoterInterface_GetAllAuthorizedCallers_Call{Call: _e.mock.On("GetAllAuthorizedCallers", opts)}
}

func (_c *FeeQuoterInterface_GetAllAuthorizedCallers_Call) Run(run func(opts *bind.CallOpts)) *FeeQuoterInterface_GetAllAuthorizedCallers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetAllAuthorizedCallers_Call) Return(_a0 []common.Address, _a1 error) *FeeQuoterInterface_GetAllAuthorizedCallers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetAllAuthorizedCallers_Call) RunAndReturn(run func(*bind.CallOpts) ([]common.Address, error)) *FeeQuoterInterface_GetAllAuthorizedCallers_Call {
	_c.Call.Return(run)
	return _c
}

// GetDestChainConfig provides a mock function with given fields: opts, destChainSelector
func (_m *FeeQuoterInterface) GetDestChainConfig(opts *bind.CallOpts, destChainSelector uint64) (fee_quoter.FeeQuoterDestChainConfig, error) {
	ret := _m.Called(opts, destChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for GetDestChainConfig")
	}

	var r0 fee_quoter.FeeQuoterDestChainConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) (fee_quoter.FeeQuoterDestChainConfig, error)); ok {
		return rf(opts, destChainSelector)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) fee_quoter.FeeQuoterDestChainConfig); ok {
		r0 = rf(opts, destChainSelector)
	} else {
		r0 = ret.Get(0).(fee_quoter.FeeQuoterDestChainConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, destChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetDestChainConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDestChainConfig'
type FeeQuoterInterface_GetDestChainConfig_Call struct {
	*mock.Call
}

// GetDestChainConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - destChainSelector uint64
func (_e *FeeQuoterInterface_Expecter) GetDestChainConfig(opts interface{}, destChainSelector interface{}) *FeeQuoterInterface_GetDestChainConfig_Call {
	return &FeeQuoterInterface_GetDestChainConfig_Call{Call: _e.mock.On("GetDestChainConfig", opts, destChainSelector)}
}

func (_c *FeeQuoterInterface_GetDestChainConfig_Call) Run(run func(opts *bind.CallOpts, destChainSelector uint64)) *FeeQuoterInterface_GetDestChainConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetDestChainConfig_Call) Return(_a0 fee_quoter.FeeQuoterDestChainConfig, _a1 error) *FeeQuoterInterface_GetDestChainConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetDestChainConfig_Call) RunAndReturn(run func(*bind.CallOpts, uint64) (fee_quoter.FeeQuoterDestChainConfig, error)) *FeeQuoterInterface_GetDestChainConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetDestinationChainGasPrice provides a mock function with given fields: opts, destChainSelector
func (_m *FeeQuoterInterface) GetDestinationChainGasPrice(opts *bind.CallOpts, destChainSelector uint64) (fee_quoter.InternalTimestampedPackedUint224, error) {
	ret := _m.Called(opts, destChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for GetDestinationChainGasPrice")
	}

	var r0 fee_quoter.InternalTimestampedPackedUint224
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) (fee_quoter.InternalTimestampedPackedUint224, error)); ok {
		return rf(opts, destChainSelector)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) fee_quoter.InternalTimestampedPackedUint224); ok {
		r0 = rf(opts, destChainSelector)
	} else {
		r0 = ret.Get(0).(fee_quoter.InternalTimestampedPackedUint224)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, destChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetDestinationChainGasPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDestinationChainGasPrice'
type FeeQuoterInterface_GetDestinationChainGasPrice_Call struct {
	*mock.Call
}

// GetDestinationChainGasPrice is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - destChainSelector uint64
func (_e *FeeQuoterInterface_Expecter) GetDestinationChainGasPrice(opts interface{}, destChainSelector interface{}) *FeeQuoterInterface_GetDestinationChainGasPrice_Call {
	return &FeeQuoterInterface_GetDestinationChainGasPrice_Call{Call: _e.mock.On("GetDestinationChainGasPrice", opts, destChainSelector)}
}

func (_c *FeeQuoterInterface_GetDestinationChainGasPrice_Call) Run(run func(opts *bind.CallOpts, destChainSelector uint64)) *FeeQuoterInterface_GetDestinationChainGasPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetDestinationChainGasPrice_Call) Return(_a0 fee_quoter.InternalTimestampedPackedUint224, _a1 error) *FeeQuoterInterface_GetDestinationChainGasPrice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetDestinationChainGasPrice_Call) RunAndReturn(run func(*bind.CallOpts, uint64) (fee_quoter.InternalTimestampedPackedUint224, error)) *FeeQuoterInterface_GetDestinationChainGasPrice_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeeTokens provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) GetFeeTokens(opts *bind.CallOpts) ([]common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetFeeTokens")
	}

	var r0 []common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) ([]common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) []common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetFeeTokens_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeeTokens'
type FeeQuoterInterface_GetFeeTokens_Call struct {
	*mock.Call
}

// GetFeeTokens is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *FeeQuoterInterface_Expecter) GetFeeTokens(opts interface{}) *FeeQuoterInterface_GetFeeTokens_Call {
	return &FeeQuoterInterface_GetFeeTokens_Call{Call: _e.mock.On("GetFeeTokens", opts)}
}

func (_c *FeeQuoterInterface_GetFeeTokens_Call) Run(run func(opts *bind.CallOpts)) *FeeQuoterInterface_GetFeeTokens_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetFeeTokens_Call) Return(_a0 []common.Address, _a1 error) *FeeQuoterInterface_GetFeeTokens_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetFeeTokens_Call) RunAndReturn(run func(*bind.CallOpts) ([]common.Address, error)) *FeeQuoterInterface_GetFeeTokens_Call {
	_c.Call.Return(run)
	return _c
}

// GetPremiumMultiplierWeiPerEth provides a mock function with given fields: opts, token
func (_m *FeeQuoterInterface) GetPremiumMultiplierWeiPerEth(opts *bind.CallOpts, token common.Address) (uint64, error) {
	ret := _m.Called(opts, token)

	if len(ret) == 0 {
		panic("no return value specified for GetPremiumMultiplierWeiPerEth")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (uint64, error)); ok {
		return rf(opts, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) uint64); ok {
		r0 = rf(opts, token)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPremiumMultiplierWeiPerEth'
type FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call struct {
	*mock.Call
}

// GetPremiumMultiplierWeiPerEth is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - token common.Address
func (_e *FeeQuoterInterface_Expecter) GetPremiumMultiplierWeiPerEth(opts interface{}, token interface{}) *FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call {
	return &FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call{Call: _e.mock.On("GetPremiumMultiplierWeiPerEth", opts, token)}
}

func (_c *FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call) Run(run func(opts *bind.CallOpts, token common.Address)) *FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call) Return(_a0 uint64, _a1 error) *FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (uint64, error)) *FeeQuoterInterface_GetPremiumMultiplierWeiPerEth_Call {
	_c.Call.Return(run)
	return _c
}

// GetStaticConfig provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) GetStaticConfig(opts *bind.CallOpts) (fee_quoter.FeeQuoterStaticConfig, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetStaticConfig")
	}

	var r0 fee_quoter.FeeQuoterStaticConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (fee_quoter.FeeQuoterStaticConfig, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) fee_quoter.FeeQuoterStaticConfig); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(fee_quoter.FeeQuoterStaticConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetStaticConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStaticConfig'
type FeeQuoterInterface_GetStaticConfig_Call struct {
	*mock.Call
}

// GetStaticConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *FeeQuoterInterface_Expecter) GetStaticConfig(opts interface{}) *FeeQuoterInterface_GetStaticConfig_Call {
	return &FeeQuoterInterface_GetStaticConfig_Call{Call: _e.mock.On("GetStaticConfig", opts)}
}

func (_c *FeeQuoterInterface_GetStaticConfig_Call) Run(run func(opts *bind.CallOpts)) *FeeQuoterInterface_GetStaticConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetStaticConfig_Call) Return(_a0 fee_quoter.FeeQuoterStaticConfig, _a1 error) *FeeQuoterInterface_GetStaticConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetStaticConfig_Call) RunAndReturn(run func(*bind.CallOpts) (fee_quoter.FeeQuoterStaticConfig, error)) *FeeQuoterInterface_GetStaticConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenAndGasPrices provides a mock function with given fields: opts, token, destChainSelector
func (_m *FeeQuoterInterface) GetTokenAndGasPrices(opts *bind.CallOpts, token common.Address, destChainSelector uint64) (fee_quoter.GetTokenAndGasPrices, error) {
	ret := _m.Called(opts, token, destChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenAndGasPrices")
	}

	var r0 fee_quoter.GetTokenAndGasPrices
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, uint64) (fee_quoter.GetTokenAndGasPrices, error)); ok {
		return rf(opts, token, destChainSelector)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, uint64) fee_quoter.GetTokenAndGasPrices); ok {
		r0 = rf(opts, token, destChainSelector)
	} else {
		r0 = ret.Get(0).(fee_quoter.GetTokenAndGasPrices)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address, uint64) error); ok {
		r1 = rf(opts, token, destChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetTokenAndGasPrices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenAndGasPrices'
type FeeQuoterInterface_GetTokenAndGasPrices_Call struct {
	*mock.Call
}

// GetTokenAndGasPrices is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - token common.Address
//   - destChainSelector uint64
func (_e *FeeQuoterInterface_Expecter) GetTokenAndGasPrices(opts interface{}, token interface{}, destChainSelector interface{}) *FeeQuoterInterface_GetTokenAndGasPrices_Call {
	return &FeeQuoterInterface_GetTokenAndGasPrices_Call{Call: _e.mock.On("GetTokenAndGasPrices", opts, token, destChainSelector)}
}

func (_c *FeeQuoterInterface_GetTokenAndGasPrices_Call) Run(run func(opts *bind.CallOpts, token common.Address, destChainSelector uint64)) *FeeQuoterInterface_GetTokenAndGasPrices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address), args[2].(uint64))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetTokenAndGasPrices_Call) Return(_a0 fee_quoter.GetTokenAndGasPrices, _a1 error) *FeeQuoterInterface_GetTokenAndGasPrices_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetTokenAndGasPrices_Call) RunAndReturn(run func(*bind.CallOpts, common.Address, uint64) (fee_quoter.GetTokenAndGasPrices, error)) *FeeQuoterInterface_GetTokenAndGasPrices_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenPrice provides a mock function with given fields: opts, token
func (_m *FeeQuoterInterface) GetTokenPrice(opts *bind.CallOpts, token common.Address) (fee_quoter.InternalTimestampedPackedUint224, error) {
	ret := _m.Called(opts, token)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenPrice")
	}

	var r0 fee_quoter.InternalTimestampedPackedUint224
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (fee_quoter.InternalTimestampedPackedUint224, error)); ok {
		return rf(opts, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) fee_quoter.InternalTimestampedPackedUint224); ok {
		r0 = rf(opts, token)
	} else {
		r0 = ret.Get(0).(fee_quoter.InternalTimestampedPackedUint224)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetTokenPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenPrice'
type FeeQuoterInterface_GetTokenPrice_Call struct {
	*mock.Call
}

// GetTokenPrice is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - token common.Address
func (_e *FeeQuoterInterface_Expecter) GetTokenPrice(opts interface{}, token interface{}) *FeeQuoterInterface_GetTokenPrice_Call {
	return &FeeQuoterInterface_GetTokenPrice_Call{Call: _e.mock.On("GetTokenPrice", opts, token)}
}

func (_c *FeeQuoterInterface_GetTokenPrice_Call) Run(run func(opts *bind.CallOpts, token common.Address)) *FeeQuoterInterface_GetTokenPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetTokenPrice_Call) Return(_a0 fee_quoter.InternalTimestampedPackedUint224, _a1 error) *FeeQuoterInterface_GetTokenPrice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetTokenPrice_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (fee_quoter.InternalTimestampedPackedUint224, error)) *FeeQuoterInterface_GetTokenPrice_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenPriceFeedConfig provides a mock function with given fields: opts, token
func (_m *FeeQuoterInterface) GetTokenPriceFeedConfig(opts *bind.CallOpts, token common.Address) (fee_quoter.FeeQuoterTokenPriceFeedConfig, error) {
	ret := _m.Called(opts, token)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenPriceFeedConfig")
	}

	var r0 fee_quoter.FeeQuoterTokenPriceFeedConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (fee_quoter.FeeQuoterTokenPriceFeedConfig, error)); ok {
		return rf(opts, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) fee_quoter.FeeQuoterTokenPriceFeedConfig); ok {
		r0 = rf(opts, token)
	} else {
		r0 = ret.Get(0).(fee_quoter.FeeQuoterTokenPriceFeedConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetTokenPriceFeedConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenPriceFeedConfig'
type FeeQuoterInterface_GetTokenPriceFeedConfig_Call struct {
	*mock.Call
}

// GetTokenPriceFeedConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - token common.Address
func (_e *FeeQuoterInterface_Expecter) GetTokenPriceFeedConfig(opts interface{}, token interface{}) *FeeQuoterInterface_GetTokenPriceFeedConfig_Call {
	return &FeeQuoterInterface_GetTokenPriceFeedConfig_Call{Call: _e.mock.On("GetTokenPriceFeedConfig", opts, token)}
}

func (_c *FeeQuoterInterface_GetTokenPriceFeedConfig_Call) Run(run func(opts *bind.CallOpts, token common.Address)) *FeeQuoterInterface_GetTokenPriceFeedConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetTokenPriceFeedConfig_Call) Return(_a0 fee_quoter.FeeQuoterTokenPriceFeedConfig, _a1 error) *FeeQuoterInterface_GetTokenPriceFeedConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetTokenPriceFeedConfig_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (fee_quoter.FeeQuoterTokenPriceFeedConfig, error)) *FeeQuoterInterface_GetTokenPriceFeedConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenPrices provides a mock function with given fields: opts, tokens
func (_m *FeeQuoterInterface) GetTokenPrices(opts *bind.CallOpts, tokens []common.Address) ([]fee_quoter.InternalTimestampedPackedUint224, error) {
	ret := _m.Called(opts, tokens)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenPrices")
	}

	var r0 []fee_quoter.InternalTimestampedPackedUint224
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, []common.Address) ([]fee_quoter.InternalTimestampedPackedUint224, error)); ok {
		return rf(opts, tokens)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, []common.Address) []fee_quoter.InternalTimestampedPackedUint224); ok {
		r0 = rf(opts, tokens)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]fee_quoter.InternalTimestampedPackedUint224)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, []common.Address) error); ok {
		r1 = rf(opts, tokens)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetTokenPrices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenPrices'
type FeeQuoterInterface_GetTokenPrices_Call struct {
	*mock.Call
}

// GetTokenPrices is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - tokens []common.Address
func (_e *FeeQuoterInterface_Expecter) GetTokenPrices(opts interface{}, tokens interface{}) *FeeQuoterInterface_GetTokenPrices_Call {
	return &FeeQuoterInterface_GetTokenPrices_Call{Call: _e.mock.On("GetTokenPrices", opts, tokens)}
}

func (_c *FeeQuoterInterface_GetTokenPrices_Call) Run(run func(opts *bind.CallOpts, tokens []common.Address)) *FeeQuoterInterface_GetTokenPrices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetTokenPrices_Call) Return(_a0 []fee_quoter.InternalTimestampedPackedUint224, _a1 error) *FeeQuoterInterface_GetTokenPrices_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetTokenPrices_Call) RunAndReturn(run func(*bind.CallOpts, []common.Address) ([]fee_quoter.InternalTimestampedPackedUint224, error)) *FeeQuoterInterface_GetTokenPrices_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenTransferFeeConfig provides a mock function with given fields: opts, destChainSelector, token
func (_m *FeeQuoterInterface) GetTokenTransferFeeConfig(opts *bind.CallOpts, destChainSelector uint64, token common.Address) (fee_quoter.FeeQuoterTokenTransferFeeConfig, error) {
	ret := _m.Called(opts, destChainSelector, token)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenTransferFeeConfig")
	}

	var r0 fee_quoter.FeeQuoterTokenTransferFeeConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, common.Address) (fee_quoter.FeeQuoterTokenTransferFeeConfig, error)); ok {
		return rf(opts, destChainSelector, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, common.Address) fee_quoter.FeeQuoterTokenTransferFeeConfig); ok {
		r0 = rf(opts, destChainSelector, token)
	} else {
		r0 = ret.Get(0).(fee_quoter.FeeQuoterTokenTransferFeeConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64, common.Address) error); ok {
		r1 = rf(opts, destChainSelector, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetTokenTransferFeeConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenTransferFeeConfig'
type FeeQuoterInterface_GetTokenTransferFeeConfig_Call struct {
	*mock.Call
}

// GetTokenTransferFeeConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - destChainSelector uint64
//   - token common.Address
func (_e *FeeQuoterInterface_Expecter) GetTokenTransferFeeConfig(opts interface{}, destChainSelector interface{}, token interface{}) *FeeQuoterInterface_GetTokenTransferFeeConfig_Call {
	return &FeeQuoterInterface_GetTokenTransferFeeConfig_Call{Call: _e.mock.On("GetTokenTransferFeeConfig", opts, destChainSelector, token)}
}

func (_c *FeeQuoterInterface_GetTokenTransferFeeConfig_Call) Run(run func(opts *bind.CallOpts, destChainSelector uint64, token common.Address)) *FeeQuoterInterface_GetTokenTransferFeeConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64), args[2].(common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetTokenTransferFeeConfig_Call) Return(_a0 fee_quoter.FeeQuoterTokenTransferFeeConfig, _a1 error) *FeeQuoterInterface_GetTokenTransferFeeConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetTokenTransferFeeConfig_Call) RunAndReturn(run func(*bind.CallOpts, uint64, common.Address) (fee_quoter.FeeQuoterTokenTransferFeeConfig, error)) *FeeQuoterInterface_GetTokenTransferFeeConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetValidatedFee provides a mock function with given fields: opts, destChainSelector, message
func (_m *FeeQuoterInterface) GetValidatedFee(opts *bind.CallOpts, destChainSelector uint64, message fee_quoter.ClientEVM2AnyMessage) (*big.Int, error) {
	ret := _m.Called(opts, destChainSelector, message)

	if len(ret) == 0 {
		panic("no return value specified for GetValidatedFee")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, fee_quoter.ClientEVM2AnyMessage) (*big.Int, error)); ok {
		return rf(opts, destChainSelector, message)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, fee_quoter.ClientEVM2AnyMessage) *big.Int); ok {
		r0 = rf(opts, destChainSelector, message)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64, fee_quoter.ClientEVM2AnyMessage) error); ok {
		r1 = rf(opts, destChainSelector, message)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetValidatedFee_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetValidatedFee'
type FeeQuoterInterface_GetValidatedFee_Call struct {
	*mock.Call
}

// GetValidatedFee is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - destChainSelector uint64
//   - message fee_quoter.ClientEVM2AnyMessage
func (_e *FeeQuoterInterface_Expecter) GetValidatedFee(opts interface{}, destChainSelector interface{}, message interface{}) *FeeQuoterInterface_GetValidatedFee_Call {
	return &FeeQuoterInterface_GetValidatedFee_Call{Call: _e.mock.On("GetValidatedFee", opts, destChainSelector, message)}
}

func (_c *FeeQuoterInterface_GetValidatedFee_Call) Run(run func(opts *bind.CallOpts, destChainSelector uint64, message fee_quoter.ClientEVM2AnyMessage)) *FeeQuoterInterface_GetValidatedFee_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64), args[2].(fee_quoter.ClientEVM2AnyMessage))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetValidatedFee_Call) Return(_a0 *big.Int, _a1 error) *FeeQuoterInterface_GetValidatedFee_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetValidatedFee_Call) RunAndReturn(run func(*bind.CallOpts, uint64, fee_quoter.ClientEVM2AnyMessage) (*big.Int, error)) *FeeQuoterInterface_GetValidatedFee_Call {
	_c.Call.Return(run)
	return _c
}

// GetValidatedTokenPrice provides a mock function with given fields: opts, token
func (_m *FeeQuoterInterface) GetValidatedTokenPrice(opts *bind.CallOpts, token common.Address) (*big.Int, error) {
	ret := _m.Called(opts, token)

	if len(ret) == 0 {
		panic("no return value specified for GetValidatedTokenPrice")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (*big.Int, error)); ok {
		return rf(opts, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) *big.Int); ok {
		r0 = rf(opts, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_GetValidatedTokenPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetValidatedTokenPrice'
type FeeQuoterInterface_GetValidatedTokenPrice_Call struct {
	*mock.Call
}

// GetValidatedTokenPrice is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - token common.Address
func (_e *FeeQuoterInterface_Expecter) GetValidatedTokenPrice(opts interface{}, token interface{}) *FeeQuoterInterface_GetValidatedTokenPrice_Call {
	return &FeeQuoterInterface_GetValidatedTokenPrice_Call{Call: _e.mock.On("GetValidatedTokenPrice", opts, token)}
}

func (_c *FeeQuoterInterface_GetValidatedTokenPrice_Call) Run(run func(opts *bind.CallOpts, token common.Address)) *FeeQuoterInterface_GetValidatedTokenPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_GetValidatedTokenPrice_Call) Return(_a0 *big.Int, _a1 error) *FeeQuoterInterface_GetValidatedTokenPrice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_GetValidatedTokenPrice_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (*big.Int, error)) *FeeQuoterInterface_GetValidatedTokenPrice_Call {
	_c.Call.Return(run)
	return _c
}

// KEYSTONEPRICEDECIMALS provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) KEYSTONEPRICEDECIMALS(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for KEYSTONEPRICEDECIMALS")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'KEYSTONEPRICEDECIMALS'
type FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call struct {
	*mock.Call
}

// KEYSTONEPRICEDECIMALS is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *FeeQuoterInterface_Expecter) KEYSTONEPRICEDECIMALS(opts interface{}) *FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call {
	return &FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call{Call: _e.mock.On("KEYSTONEPRICEDECIMALS", opts)}
}

func (_c *FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call) Run(run func(opts *bind.CallOpts)) *FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call) Return(_a0 *big.Int, _a1 error) *FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *FeeQuoterInterface_KEYSTONEPRICEDECIMALS_Call {
	_c.Call.Return(run)
	return _c
}

// OnReport provides a mock function with given fields: opts, metadata, report
func (_m *FeeQuoterInterface) OnReport(opts *bind.TransactOpts, metadata []byte, report []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, metadata, report)

	if len(ret) == 0 {
		panic("no return value specified for OnReport")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []byte, []byte) (*types.Transaction, error)); ok {
		return rf(opts, metadata, report)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []byte, []byte) *types.Transaction); ok {
		r0 = rf(opts, metadata, report)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []byte, []byte) error); ok {
		r1 = rf(opts, metadata, report)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_OnReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnReport'
type FeeQuoterInterface_OnReport_Call struct {
	*mock.Call
}

// OnReport is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - metadata []byte
//   - report []byte
func (_e *FeeQuoterInterface_Expecter) OnReport(opts interface{}, metadata interface{}, report interface{}) *FeeQuoterInterface_OnReport_Call {
	return &FeeQuoterInterface_OnReport_Call{Call: _e.mock.On("OnReport", opts, metadata, report)}
}

func (_c *FeeQuoterInterface_OnReport_Call) Run(run func(opts *bind.TransactOpts, metadata []byte, report []byte)) *FeeQuoterInterface_OnReport_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]byte), args[2].([]byte))
	})
	return _c
}

func (_c *FeeQuoterInterface_OnReport_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_OnReport_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_OnReport_Call) RunAndReturn(run func(*bind.TransactOpts, []byte, []byte) (*types.Transaction, error)) *FeeQuoterInterface_OnReport_Call {
	_c.Call.Return(run)
	return _c
}

// Owner provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) Owner(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Owner")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_Owner_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Owner'
type FeeQuoterInterface_Owner_Call struct {
	*mock.Call
}

// Owner is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *FeeQuoterInterface_Expecter) Owner(opts interface{}) *FeeQuoterInterface_Owner_Call {
	return &FeeQuoterInterface_Owner_Call{Call: _e.mock.On("Owner", opts)}
}

func (_c *FeeQuoterInterface_Owner_Call) Run(run func(opts *bind.CallOpts)) *FeeQuoterInterface_Owner_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_Owner_Call) Return(_a0 common.Address, _a1 error) *FeeQuoterInterface_Owner_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_Owner_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *FeeQuoterInterface_Owner_Call {
	_c.Call.Return(run)
	return _c
}

// ParseAuthorizedCallerAdded provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseAuthorizedCallerAdded(log types.Log) (*fee_quoter.FeeQuoterAuthorizedCallerAdded, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseAuthorizedCallerAdded")
	}

	var r0 *fee_quoter.FeeQuoterAuthorizedCallerAdded
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterAuthorizedCallerAdded, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterAuthorizedCallerAdded); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterAuthorizedCallerAdded)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseAuthorizedCallerAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseAuthorizedCallerAdded'
type FeeQuoterInterface_ParseAuthorizedCallerAdded_Call struct {
	*mock.Call
}

// ParseAuthorizedCallerAdded is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseAuthorizedCallerAdded(log interface{}) *FeeQuoterInterface_ParseAuthorizedCallerAdded_Call {
	return &FeeQuoterInterface_ParseAuthorizedCallerAdded_Call{Call: _e.mock.On("ParseAuthorizedCallerAdded", log)}
}

func (_c *FeeQuoterInterface_ParseAuthorizedCallerAdded_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseAuthorizedCallerAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseAuthorizedCallerAdded_Call) Return(_a0 *fee_quoter.FeeQuoterAuthorizedCallerAdded, _a1 error) *FeeQuoterInterface_ParseAuthorizedCallerAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseAuthorizedCallerAdded_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterAuthorizedCallerAdded, error)) *FeeQuoterInterface_ParseAuthorizedCallerAdded_Call {
	_c.Call.Return(run)
	return _c
}

// ParseAuthorizedCallerRemoved provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseAuthorizedCallerRemoved(log types.Log) (*fee_quoter.FeeQuoterAuthorizedCallerRemoved, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseAuthorizedCallerRemoved")
	}

	var r0 *fee_quoter.FeeQuoterAuthorizedCallerRemoved
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterAuthorizedCallerRemoved, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterAuthorizedCallerRemoved); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterAuthorizedCallerRemoved)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseAuthorizedCallerRemoved'
type FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call struct {
	*mock.Call
}

// ParseAuthorizedCallerRemoved is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseAuthorizedCallerRemoved(log interface{}) *FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call {
	return &FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call{Call: _e.mock.On("ParseAuthorizedCallerRemoved", log)}
}

func (_c *FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call) Return(_a0 *fee_quoter.FeeQuoterAuthorizedCallerRemoved, _a1 error) *FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterAuthorizedCallerRemoved, error)) *FeeQuoterInterface_ParseAuthorizedCallerRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// ParseDestChainAdded provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseDestChainAdded(log types.Log) (*fee_quoter.FeeQuoterDestChainAdded, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseDestChainAdded")
	}

	var r0 *fee_quoter.FeeQuoterDestChainAdded
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterDestChainAdded, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterDestChainAdded); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterDestChainAdded)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseDestChainAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseDestChainAdded'
type FeeQuoterInterface_ParseDestChainAdded_Call struct {
	*mock.Call
}

// ParseDestChainAdded is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseDestChainAdded(log interface{}) *FeeQuoterInterface_ParseDestChainAdded_Call {
	return &FeeQuoterInterface_ParseDestChainAdded_Call{Call: _e.mock.On("ParseDestChainAdded", log)}
}

func (_c *FeeQuoterInterface_ParseDestChainAdded_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseDestChainAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseDestChainAdded_Call) Return(_a0 *fee_quoter.FeeQuoterDestChainAdded, _a1 error) *FeeQuoterInterface_ParseDestChainAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseDestChainAdded_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterDestChainAdded, error)) *FeeQuoterInterface_ParseDestChainAdded_Call {
	_c.Call.Return(run)
	return _c
}

// ParseDestChainConfigUpdated provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseDestChainConfigUpdated(log types.Log) (*fee_quoter.FeeQuoterDestChainConfigUpdated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseDestChainConfigUpdated")
	}

	var r0 *fee_quoter.FeeQuoterDestChainConfigUpdated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterDestChainConfigUpdated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterDestChainConfigUpdated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterDestChainConfigUpdated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseDestChainConfigUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseDestChainConfigUpdated'
type FeeQuoterInterface_ParseDestChainConfigUpdated_Call struct {
	*mock.Call
}

// ParseDestChainConfigUpdated is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseDestChainConfigUpdated(log interface{}) *FeeQuoterInterface_ParseDestChainConfigUpdated_Call {
	return &FeeQuoterInterface_ParseDestChainConfigUpdated_Call{Call: _e.mock.On("ParseDestChainConfigUpdated", log)}
}

func (_c *FeeQuoterInterface_ParseDestChainConfigUpdated_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseDestChainConfigUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseDestChainConfigUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterDestChainConfigUpdated, _a1 error) *FeeQuoterInterface_ParseDestChainConfigUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseDestChainConfigUpdated_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterDestChainConfigUpdated, error)) *FeeQuoterInterface_ParseDestChainConfigUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// ParseFeeTokenAdded provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseFeeTokenAdded(log types.Log) (*fee_quoter.FeeQuoterFeeTokenAdded, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseFeeTokenAdded")
	}

	var r0 *fee_quoter.FeeQuoterFeeTokenAdded
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterFeeTokenAdded, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterFeeTokenAdded); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterFeeTokenAdded)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseFeeTokenAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseFeeTokenAdded'
type FeeQuoterInterface_ParseFeeTokenAdded_Call struct {
	*mock.Call
}

// ParseFeeTokenAdded is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseFeeTokenAdded(log interface{}) *FeeQuoterInterface_ParseFeeTokenAdded_Call {
	return &FeeQuoterInterface_ParseFeeTokenAdded_Call{Call: _e.mock.On("ParseFeeTokenAdded", log)}
}

func (_c *FeeQuoterInterface_ParseFeeTokenAdded_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseFeeTokenAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseFeeTokenAdded_Call) Return(_a0 *fee_quoter.FeeQuoterFeeTokenAdded, _a1 error) *FeeQuoterInterface_ParseFeeTokenAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseFeeTokenAdded_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterFeeTokenAdded, error)) *FeeQuoterInterface_ParseFeeTokenAdded_Call {
	_c.Call.Return(run)
	return _c
}

// ParseFeeTokenRemoved provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseFeeTokenRemoved(log types.Log) (*fee_quoter.FeeQuoterFeeTokenRemoved, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseFeeTokenRemoved")
	}

	var r0 *fee_quoter.FeeQuoterFeeTokenRemoved
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterFeeTokenRemoved, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterFeeTokenRemoved); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterFeeTokenRemoved)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseFeeTokenRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseFeeTokenRemoved'
type FeeQuoterInterface_ParseFeeTokenRemoved_Call struct {
	*mock.Call
}

// ParseFeeTokenRemoved is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseFeeTokenRemoved(log interface{}) *FeeQuoterInterface_ParseFeeTokenRemoved_Call {
	return &FeeQuoterInterface_ParseFeeTokenRemoved_Call{Call: _e.mock.On("ParseFeeTokenRemoved", log)}
}

func (_c *FeeQuoterInterface_ParseFeeTokenRemoved_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseFeeTokenRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseFeeTokenRemoved_Call) Return(_a0 *fee_quoter.FeeQuoterFeeTokenRemoved, _a1 error) *FeeQuoterInterface_ParseFeeTokenRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseFeeTokenRemoved_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterFeeTokenRemoved, error)) *FeeQuoterInterface_ParseFeeTokenRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type FeeQuoterInterface_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseLog(log interface{}) *FeeQuoterInterface_ParseLog_Call {
	return &FeeQuoterInterface_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *FeeQuoterInterface_ParseLog_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *FeeQuoterInterface_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *FeeQuoterInterface_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferRequested provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseOwnershipTransferRequested(log types.Log) (*fee_quoter.FeeQuoterOwnershipTransferRequested, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferRequested")
	}

	var r0 *fee_quoter.FeeQuoterOwnershipTransferRequested
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterOwnershipTransferRequested, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterOwnershipTransferRequested); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterOwnershipTransferRequested)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferRequested'
type FeeQuoterInterface_ParseOwnershipTransferRequested_Call struct {
	*mock.Call
}

// ParseOwnershipTransferRequested is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseOwnershipTransferRequested(log interface{}) *FeeQuoterInterface_ParseOwnershipTransferRequested_Call {
	return &FeeQuoterInterface_ParseOwnershipTransferRequested_Call{Call: _e.mock.On("ParseOwnershipTransferRequested", log)}
}

func (_c *FeeQuoterInterface_ParseOwnershipTransferRequested_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseOwnershipTransferRequested_Call) Return(_a0 *fee_quoter.FeeQuoterOwnershipTransferRequested, _a1 error) *FeeQuoterInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseOwnershipTransferRequested_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterOwnershipTransferRequested, error)) *FeeQuoterInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferred provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseOwnershipTransferred(log types.Log) (*fee_quoter.FeeQuoterOwnershipTransferred, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferred")
	}

	var r0 *fee_quoter.FeeQuoterOwnershipTransferred
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterOwnershipTransferred, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterOwnershipTransferred); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterOwnershipTransferred)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferred'
type FeeQuoterInterface_ParseOwnershipTransferred_Call struct {
	*mock.Call
}

// ParseOwnershipTransferred is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseOwnershipTransferred(log interface{}) *FeeQuoterInterface_ParseOwnershipTransferred_Call {
	return &FeeQuoterInterface_ParseOwnershipTransferred_Call{Call: _e.mock.On("ParseOwnershipTransferred", log)}
}

func (_c *FeeQuoterInterface_ParseOwnershipTransferred_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseOwnershipTransferred_Call) Return(_a0 *fee_quoter.FeeQuoterOwnershipTransferred, _a1 error) *FeeQuoterInterface_ParseOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseOwnershipTransferred_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterOwnershipTransferred, error)) *FeeQuoterInterface_ParseOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// ParsePremiumMultiplierWeiPerEthUpdated provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParsePremiumMultiplierWeiPerEthUpdated(log types.Log) (*fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParsePremiumMultiplierWeiPerEthUpdated")
	}

	var r0 *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParsePremiumMultiplierWeiPerEthUpdated'
type FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call struct {
	*mock.Call
}

// ParsePremiumMultiplierWeiPerEthUpdated is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParsePremiumMultiplierWeiPerEthUpdated(log interface{}) *FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call {
	return &FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call{Call: _e.mock.On("ParsePremiumMultiplierWeiPerEthUpdated", log)}
}

func (_c *FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, _a1 error) *FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, error)) *FeeQuoterInterface_ParsePremiumMultiplierWeiPerEthUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// ParsePriceFeedPerTokenUpdated provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParsePriceFeedPerTokenUpdated(log types.Log) (*fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParsePriceFeedPerTokenUpdated")
	}

	var r0 *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterPriceFeedPerTokenUpdated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParsePriceFeedPerTokenUpdated'
type FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call struct {
	*mock.Call
}

// ParsePriceFeedPerTokenUpdated is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParsePriceFeedPerTokenUpdated(log interface{}) *FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call {
	return &FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call{Call: _e.mock.On("ParsePriceFeedPerTokenUpdated", log)}
}

func (_c *FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, _a1 error) *FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, error)) *FeeQuoterInterface_ParsePriceFeedPerTokenUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// ParseReportPermissionSet provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseReportPermissionSet(log types.Log) (*fee_quoter.FeeQuoterReportPermissionSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseReportPermissionSet")
	}

	var r0 *fee_quoter.FeeQuoterReportPermissionSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterReportPermissionSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterReportPermissionSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterReportPermissionSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseReportPermissionSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseReportPermissionSet'
type FeeQuoterInterface_ParseReportPermissionSet_Call struct {
	*mock.Call
}

// ParseReportPermissionSet is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseReportPermissionSet(log interface{}) *FeeQuoterInterface_ParseReportPermissionSet_Call {
	return &FeeQuoterInterface_ParseReportPermissionSet_Call{Call: _e.mock.On("ParseReportPermissionSet", log)}
}

func (_c *FeeQuoterInterface_ParseReportPermissionSet_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseReportPermissionSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseReportPermissionSet_Call) Return(_a0 *fee_quoter.FeeQuoterReportPermissionSet, _a1 error) *FeeQuoterInterface_ParseReportPermissionSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseReportPermissionSet_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterReportPermissionSet, error)) *FeeQuoterInterface_ParseReportPermissionSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseTokenTransferFeeConfigDeleted provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseTokenTransferFeeConfigDeleted(log types.Log) (*fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseTokenTransferFeeConfigDeleted")
	}

	var r0 *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseTokenTransferFeeConfigDeleted'
type FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call struct {
	*mock.Call
}

// ParseTokenTransferFeeConfigDeleted is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseTokenTransferFeeConfigDeleted(log interface{}) *FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call {
	return &FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call{Call: _e.mock.On("ParseTokenTransferFeeConfigDeleted", log)}
}

func (_c *FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call) Return(_a0 *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, _a1 error) *FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, error)) *FeeQuoterInterface_ParseTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(run)
	return _c
}

// ParseTokenTransferFeeConfigUpdated provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseTokenTransferFeeConfigUpdated(log types.Log) (*fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseTokenTransferFeeConfigUpdated")
	}

	var r0 *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseTokenTransferFeeConfigUpdated'
type FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call struct {
	*mock.Call
}

// ParseTokenTransferFeeConfigUpdated is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseTokenTransferFeeConfigUpdated(log interface{}) *FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call {
	return &FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call{Call: _e.mock.On("ParseTokenTransferFeeConfigUpdated", log)}
}

func (_c *FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, _a1 error) *FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, error)) *FeeQuoterInterface_ParseTokenTransferFeeConfigUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// ParseUsdPerTokenUpdated provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseUsdPerTokenUpdated(log types.Log) (*fee_quoter.FeeQuoterUsdPerTokenUpdated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseUsdPerTokenUpdated")
	}

	var r0 *fee_quoter.FeeQuoterUsdPerTokenUpdated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterUsdPerTokenUpdated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterUsdPerTokenUpdated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterUsdPerTokenUpdated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseUsdPerTokenUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseUsdPerTokenUpdated'
type FeeQuoterInterface_ParseUsdPerTokenUpdated_Call struct {
	*mock.Call
}

// ParseUsdPerTokenUpdated is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseUsdPerTokenUpdated(log interface{}) *FeeQuoterInterface_ParseUsdPerTokenUpdated_Call {
	return &FeeQuoterInterface_ParseUsdPerTokenUpdated_Call{Call: _e.mock.On("ParseUsdPerTokenUpdated", log)}
}

func (_c *FeeQuoterInterface_ParseUsdPerTokenUpdated_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseUsdPerTokenUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseUsdPerTokenUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterUsdPerTokenUpdated, _a1 error) *FeeQuoterInterface_ParseUsdPerTokenUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseUsdPerTokenUpdated_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterUsdPerTokenUpdated, error)) *FeeQuoterInterface_ParseUsdPerTokenUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// ParseUsdPerUnitGasUpdated provides a mock function with given fields: log
func (_m *FeeQuoterInterface) ParseUsdPerUnitGasUpdated(log types.Log) (*fee_quoter.FeeQuoterUsdPerUnitGasUpdated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseUsdPerUnitGasUpdated")
	}

	var r0 *fee_quoter.FeeQuoterUsdPerUnitGasUpdated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*fee_quoter.FeeQuoterUsdPerUnitGasUpdated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *fee_quoter.FeeQuoterUsdPerUnitGasUpdated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*fee_quoter.FeeQuoterUsdPerUnitGasUpdated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseUsdPerUnitGasUpdated'
type FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call struct {
	*mock.Call
}

// ParseUsdPerUnitGasUpdated is a helper method to define mock.On call
//   - log types.Log
func (_e *FeeQuoterInterface_Expecter) ParseUsdPerUnitGasUpdated(log interface{}) *FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call {
	return &FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call{Call: _e.mock.On("ParseUsdPerUnitGasUpdated", log)}
}

func (_c *FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call) Run(run func(log types.Log)) *FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call) Return(_a0 *fee_quoter.FeeQuoterUsdPerUnitGasUpdated, _a1 error) *FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call) RunAndReturn(run func(types.Log) (*fee_quoter.FeeQuoterUsdPerUnitGasUpdated, error)) *FeeQuoterInterface_ParseUsdPerUnitGasUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// ProcessMessageArgs provides a mock function with given fields: opts, destChainSelector, feeToken, feeTokenAmount, extraArgs, onRampTokenTransfers, sourceTokenAmounts
func (_m *FeeQuoterInterface) ProcessMessageArgs(opts *bind.CallOpts, destChainSelector uint64, feeToken common.Address, feeTokenAmount *big.Int, extraArgs []byte, onRampTokenTransfers []fee_quoter.InternalEVM2AnyTokenTransfer, sourceTokenAmounts []fee_quoter.ClientEVMTokenAmount) (fee_quoter.ProcessMessageArgs, error) {
	ret := _m.Called(opts, destChainSelector, feeToken, feeTokenAmount, extraArgs, onRampTokenTransfers, sourceTokenAmounts)

	if len(ret) == 0 {
		panic("no return value specified for ProcessMessageArgs")
	}

	var r0 fee_quoter.ProcessMessageArgs
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, common.Address, *big.Int, []byte, []fee_quoter.InternalEVM2AnyTokenTransfer, []fee_quoter.ClientEVMTokenAmount) (fee_quoter.ProcessMessageArgs, error)); ok {
		return rf(opts, destChainSelector, feeToken, feeTokenAmount, extraArgs, onRampTokenTransfers, sourceTokenAmounts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, common.Address, *big.Int, []byte, []fee_quoter.InternalEVM2AnyTokenTransfer, []fee_quoter.ClientEVMTokenAmount) fee_quoter.ProcessMessageArgs); ok {
		r0 = rf(opts, destChainSelector, feeToken, feeTokenAmount, extraArgs, onRampTokenTransfers, sourceTokenAmounts)
	} else {
		r0 = ret.Get(0).(fee_quoter.ProcessMessageArgs)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64, common.Address, *big.Int, []byte, []fee_quoter.InternalEVM2AnyTokenTransfer, []fee_quoter.ClientEVMTokenAmount) error); ok {
		r1 = rf(opts, destChainSelector, feeToken, feeTokenAmount, extraArgs, onRampTokenTransfers, sourceTokenAmounts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_ProcessMessageArgs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessMessageArgs'
type FeeQuoterInterface_ProcessMessageArgs_Call struct {
	*mock.Call
}

// ProcessMessageArgs is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - destChainSelector uint64
//   - feeToken common.Address
//   - feeTokenAmount *big.Int
//   - extraArgs []byte
//   - onRampTokenTransfers []fee_quoter.InternalEVM2AnyTokenTransfer
//   - sourceTokenAmounts []fee_quoter.ClientEVMTokenAmount
func (_e *FeeQuoterInterface_Expecter) ProcessMessageArgs(opts interface{}, destChainSelector interface{}, feeToken interface{}, feeTokenAmount interface{}, extraArgs interface{}, onRampTokenTransfers interface{}, sourceTokenAmounts interface{}) *FeeQuoterInterface_ProcessMessageArgs_Call {
	return &FeeQuoterInterface_ProcessMessageArgs_Call{Call: _e.mock.On("ProcessMessageArgs", opts, destChainSelector, feeToken, feeTokenAmount, extraArgs, onRampTokenTransfers, sourceTokenAmounts)}
}

func (_c *FeeQuoterInterface_ProcessMessageArgs_Call) Run(run func(opts *bind.CallOpts, destChainSelector uint64, feeToken common.Address, feeTokenAmount *big.Int, extraArgs []byte, onRampTokenTransfers []fee_quoter.InternalEVM2AnyTokenTransfer, sourceTokenAmounts []fee_quoter.ClientEVMTokenAmount)) *FeeQuoterInterface_ProcessMessageArgs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64), args[2].(common.Address), args[3].(*big.Int), args[4].([]byte), args[5].([]fee_quoter.InternalEVM2AnyTokenTransfer), args[6].([]fee_quoter.ClientEVMTokenAmount))
	})
	return _c
}

func (_c *FeeQuoterInterface_ProcessMessageArgs_Call) Return(_a0 fee_quoter.ProcessMessageArgs, _a1 error) *FeeQuoterInterface_ProcessMessageArgs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_ProcessMessageArgs_Call) RunAndReturn(run func(*bind.CallOpts, uint64, common.Address, *big.Int, []byte, []fee_quoter.InternalEVM2AnyTokenTransfer, []fee_quoter.ClientEVMTokenAmount) (fee_quoter.ProcessMessageArgs, error)) *FeeQuoterInterface_ProcessMessageArgs_Call {
	_c.Call.Return(run)
	return _c
}

// SetReportPermissions provides a mock function with given fields: opts, permissions
func (_m *FeeQuoterInterface) SetReportPermissions(opts *bind.TransactOpts, permissions []fee_quoter.KeystoneFeedsPermissionHandlerPermission) (*types.Transaction, error) {
	ret := _m.Called(opts, permissions)

	if len(ret) == 0 {
		panic("no return value specified for SetReportPermissions")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.KeystoneFeedsPermissionHandlerPermission) (*types.Transaction, error)); ok {
		return rf(opts, permissions)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.KeystoneFeedsPermissionHandlerPermission) *types.Transaction); ok {
		r0 = rf(opts, permissions)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []fee_quoter.KeystoneFeedsPermissionHandlerPermission) error); ok {
		r1 = rf(opts, permissions)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_SetReportPermissions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetReportPermissions'
type FeeQuoterInterface_SetReportPermissions_Call struct {
	*mock.Call
}

// SetReportPermissions is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - permissions []fee_quoter.KeystoneFeedsPermissionHandlerPermission
func (_e *FeeQuoterInterface_Expecter) SetReportPermissions(opts interface{}, permissions interface{}) *FeeQuoterInterface_SetReportPermissions_Call {
	return &FeeQuoterInterface_SetReportPermissions_Call{Call: _e.mock.On("SetReportPermissions", opts, permissions)}
}

func (_c *FeeQuoterInterface_SetReportPermissions_Call) Run(run func(opts *bind.TransactOpts, permissions []fee_quoter.KeystoneFeedsPermissionHandlerPermission)) *FeeQuoterInterface_SetReportPermissions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]fee_quoter.KeystoneFeedsPermissionHandlerPermission))
	})
	return _c
}

func (_c *FeeQuoterInterface_SetReportPermissions_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_SetReportPermissions_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_SetReportPermissions_Call) RunAndReturn(run func(*bind.TransactOpts, []fee_quoter.KeystoneFeedsPermissionHandlerPermission) (*types.Transaction, error)) *FeeQuoterInterface_SetReportPermissions_Call {
	_c.Call.Return(run)
	return _c
}

// TransferOwnership provides a mock function with given fields: opts, to
func (_m *FeeQuoterInterface) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, to)

	if len(ret) == 0 {
		panic("no return value specified for TransferOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_TransferOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransferOwnership'
type FeeQuoterInterface_TransferOwnership_Call struct {
	*mock.Call
}

// TransferOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - to common.Address
func (_e *FeeQuoterInterface_Expecter) TransferOwnership(opts interface{}, to interface{}) *FeeQuoterInterface_TransferOwnership_Call {
	return &FeeQuoterInterface_TransferOwnership_Call{Call: _e.mock.On("TransferOwnership", opts, to)}
}

func (_c *FeeQuoterInterface_TransferOwnership_Call) Run(run func(opts *bind.TransactOpts, to common.Address)) *FeeQuoterInterface_TransferOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_TransferOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_TransferOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_TransferOwnership_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *FeeQuoterInterface_TransferOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// TypeAndVersion provides a mock function with given fields: opts
func (_m *FeeQuoterInterface) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for TypeAndVersion")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (string, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) string); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_TypeAndVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TypeAndVersion'
type FeeQuoterInterface_TypeAndVersion_Call struct {
	*mock.Call
}

// TypeAndVersion is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *FeeQuoterInterface_Expecter) TypeAndVersion(opts interface{}) *FeeQuoterInterface_TypeAndVersion_Call {
	return &FeeQuoterInterface_TypeAndVersion_Call{Call: _e.mock.On("TypeAndVersion", opts)}
}

func (_c *FeeQuoterInterface_TypeAndVersion_Call) Run(run func(opts *bind.CallOpts)) *FeeQuoterInterface_TypeAndVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *FeeQuoterInterface_TypeAndVersion_Call) Return(_a0 string, _a1 error) *FeeQuoterInterface_TypeAndVersion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_TypeAndVersion_Call) RunAndReturn(run func(*bind.CallOpts) (string, error)) *FeeQuoterInterface_TypeAndVersion_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePrices provides a mock function with given fields: opts, priceUpdates
func (_m *FeeQuoterInterface) UpdatePrices(opts *bind.TransactOpts, priceUpdates fee_quoter.InternalPriceUpdates) (*types.Transaction, error) {
	ret := _m.Called(opts, priceUpdates)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePrices")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, fee_quoter.InternalPriceUpdates) (*types.Transaction, error)); ok {
		return rf(opts, priceUpdates)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, fee_quoter.InternalPriceUpdates) *types.Transaction); ok {
		r0 = rf(opts, priceUpdates)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, fee_quoter.InternalPriceUpdates) error); ok {
		r1 = rf(opts, priceUpdates)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_UpdatePrices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePrices'
type FeeQuoterInterface_UpdatePrices_Call struct {
	*mock.Call
}

// UpdatePrices is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - priceUpdates fee_quoter.InternalPriceUpdates
func (_e *FeeQuoterInterface_Expecter) UpdatePrices(opts interface{}, priceUpdates interface{}) *FeeQuoterInterface_UpdatePrices_Call {
	return &FeeQuoterInterface_UpdatePrices_Call{Call: _e.mock.On("UpdatePrices", opts, priceUpdates)}
}

func (_c *FeeQuoterInterface_UpdatePrices_Call) Run(run func(opts *bind.TransactOpts, priceUpdates fee_quoter.InternalPriceUpdates)) *FeeQuoterInterface_UpdatePrices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(fee_quoter.InternalPriceUpdates))
	})
	return _c
}

func (_c *FeeQuoterInterface_UpdatePrices_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_UpdatePrices_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_UpdatePrices_Call) RunAndReturn(run func(*bind.TransactOpts, fee_quoter.InternalPriceUpdates) (*types.Transaction, error)) *FeeQuoterInterface_UpdatePrices_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateTokenPriceFeeds provides a mock function with given fields: opts, tokenPriceFeedUpdates
func (_m *FeeQuoterInterface) UpdateTokenPriceFeeds(opts *bind.TransactOpts, tokenPriceFeedUpdates []fee_quoter.FeeQuoterTokenPriceFeedUpdate) (*types.Transaction, error) {
	ret := _m.Called(opts, tokenPriceFeedUpdates)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTokenPriceFeeds")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterTokenPriceFeedUpdate) (*types.Transaction, error)); ok {
		return rf(opts, tokenPriceFeedUpdates)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterTokenPriceFeedUpdate) *types.Transaction); ok {
		r0 = rf(opts, tokenPriceFeedUpdates)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []fee_quoter.FeeQuoterTokenPriceFeedUpdate) error); ok {
		r1 = rf(opts, tokenPriceFeedUpdates)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_UpdateTokenPriceFeeds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateTokenPriceFeeds'
type FeeQuoterInterface_UpdateTokenPriceFeeds_Call struct {
	*mock.Call
}

// UpdateTokenPriceFeeds is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - tokenPriceFeedUpdates []fee_quoter.FeeQuoterTokenPriceFeedUpdate
func (_e *FeeQuoterInterface_Expecter) UpdateTokenPriceFeeds(opts interface{}, tokenPriceFeedUpdates interface{}) *FeeQuoterInterface_UpdateTokenPriceFeeds_Call {
	return &FeeQuoterInterface_UpdateTokenPriceFeeds_Call{Call: _e.mock.On("UpdateTokenPriceFeeds", opts, tokenPriceFeedUpdates)}
}

func (_c *FeeQuoterInterface_UpdateTokenPriceFeeds_Call) Run(run func(opts *bind.TransactOpts, tokenPriceFeedUpdates []fee_quoter.FeeQuoterTokenPriceFeedUpdate)) *FeeQuoterInterface_UpdateTokenPriceFeeds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]fee_quoter.FeeQuoterTokenPriceFeedUpdate))
	})
	return _c
}

func (_c *FeeQuoterInterface_UpdateTokenPriceFeeds_Call) Return(_a0 *types.Transaction, _a1 error) *FeeQuoterInterface_UpdateTokenPriceFeeds_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_UpdateTokenPriceFeeds_Call) RunAndReturn(run func(*bind.TransactOpts, []fee_quoter.FeeQuoterTokenPriceFeedUpdate) (*types.Transaction, error)) *FeeQuoterInterface_UpdateTokenPriceFeeds_Call {
	_c.Call.Return(run)
	return _c
}

// WatchAuthorizedCallerAdded provides a mock function with given fields: opts, sink
func (_m *FeeQuoterInterface) WatchAuthorizedCallerAdded(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterAuthorizedCallerAdded) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchAuthorizedCallerAdded")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterAuthorizedCallerAdded) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterAuthorizedCallerAdded) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterAuthorizedCallerAdded) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchAuthorizedCallerAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchAuthorizedCallerAdded'
type FeeQuoterInterface_WatchAuthorizedCallerAdded_Call struct {
	*mock.Call
}

// WatchAuthorizedCallerAdded is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterAuthorizedCallerAdded
func (_e *FeeQuoterInterface_Expecter) WatchAuthorizedCallerAdded(opts interface{}, sink interface{}) *FeeQuoterInterface_WatchAuthorizedCallerAdded_Call {
	return &FeeQuoterInterface_WatchAuthorizedCallerAdded_Call{Call: _e.mock.On("WatchAuthorizedCallerAdded", opts, sink)}
}

func (_c *FeeQuoterInterface_WatchAuthorizedCallerAdded_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterAuthorizedCallerAdded)) *FeeQuoterInterface_WatchAuthorizedCallerAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterAuthorizedCallerAdded))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchAuthorizedCallerAdded_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchAuthorizedCallerAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchAuthorizedCallerAdded_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterAuthorizedCallerAdded) (event.Subscription, error)) *FeeQuoterInterface_WatchAuthorizedCallerAdded_Call {
	_c.Call.Return(run)
	return _c
}

// WatchAuthorizedCallerRemoved provides a mock function with given fields: opts, sink
func (_m *FeeQuoterInterface) WatchAuthorizedCallerRemoved(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterAuthorizedCallerRemoved) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchAuthorizedCallerRemoved")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterAuthorizedCallerRemoved) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterAuthorizedCallerRemoved) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterAuthorizedCallerRemoved) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchAuthorizedCallerRemoved'
type FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call struct {
	*mock.Call
}

// WatchAuthorizedCallerRemoved is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterAuthorizedCallerRemoved
func (_e *FeeQuoterInterface_Expecter) WatchAuthorizedCallerRemoved(opts interface{}, sink interface{}) *FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call {
	return &FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call{Call: _e.mock.On("WatchAuthorizedCallerRemoved", opts, sink)}
}

func (_c *FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterAuthorizedCallerRemoved)) *FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterAuthorizedCallerRemoved))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterAuthorizedCallerRemoved) (event.Subscription, error)) *FeeQuoterInterface_WatchAuthorizedCallerRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// WatchDestChainAdded provides a mock function with given fields: opts, sink, destChainSelector
func (_m *FeeQuoterInterface) WatchDestChainAdded(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterDestChainAdded, destChainSelector []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, destChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for WatchDestChainAdded")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterDestChainAdded, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, destChainSelector)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterDestChainAdded, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, destChainSelector)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterDestChainAdded, []uint64) error); ok {
		r1 = rf(opts, sink, destChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchDestChainAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchDestChainAdded'
type FeeQuoterInterface_WatchDestChainAdded_Call struct {
	*mock.Call
}

// WatchDestChainAdded is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterDestChainAdded
//   - destChainSelector []uint64
func (_e *FeeQuoterInterface_Expecter) WatchDestChainAdded(opts interface{}, sink interface{}, destChainSelector interface{}) *FeeQuoterInterface_WatchDestChainAdded_Call {
	return &FeeQuoterInterface_WatchDestChainAdded_Call{Call: _e.mock.On("WatchDestChainAdded", opts, sink, destChainSelector)}
}

func (_c *FeeQuoterInterface_WatchDestChainAdded_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterDestChainAdded, destChainSelector []uint64)) *FeeQuoterInterface_WatchDestChainAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterDestChainAdded), args[2].([]uint64))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchDestChainAdded_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchDestChainAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchDestChainAdded_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterDestChainAdded, []uint64) (event.Subscription, error)) *FeeQuoterInterface_WatchDestChainAdded_Call {
	_c.Call.Return(run)
	return _c
}

// WatchDestChainConfigUpdated provides a mock function with given fields: opts, sink, destChainSelector
func (_m *FeeQuoterInterface) WatchDestChainConfigUpdated(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterDestChainConfigUpdated, destChainSelector []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, destChainSelector)

	if len(ret) == 0 {
		panic("no return value specified for WatchDestChainConfigUpdated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterDestChainConfigUpdated, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, destChainSelector)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterDestChainConfigUpdated, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, destChainSelector)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterDestChainConfigUpdated, []uint64) error); ok {
		r1 = rf(opts, sink, destChainSelector)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchDestChainConfigUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchDestChainConfigUpdated'
type FeeQuoterInterface_WatchDestChainConfigUpdated_Call struct {
	*mock.Call
}

// WatchDestChainConfigUpdated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterDestChainConfigUpdated
//   - destChainSelector []uint64
func (_e *FeeQuoterInterface_Expecter) WatchDestChainConfigUpdated(opts interface{}, sink interface{}, destChainSelector interface{}) *FeeQuoterInterface_WatchDestChainConfigUpdated_Call {
	return &FeeQuoterInterface_WatchDestChainConfigUpdated_Call{Call: _e.mock.On("WatchDestChainConfigUpdated", opts, sink, destChainSelector)}
}

func (_c *FeeQuoterInterface_WatchDestChainConfigUpdated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterDestChainConfigUpdated, destChainSelector []uint64)) *FeeQuoterInterface_WatchDestChainConfigUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterDestChainConfigUpdated), args[2].([]uint64))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchDestChainConfigUpdated_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchDestChainConfigUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchDestChainConfigUpdated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterDestChainConfigUpdated, []uint64) (event.Subscription, error)) *FeeQuoterInterface_WatchDestChainConfigUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// WatchFeeTokenAdded provides a mock function with given fields: opts, sink, feeToken
func (_m *FeeQuoterInterface) WatchFeeTokenAdded(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterFeeTokenAdded, feeToken []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, feeToken)

	if len(ret) == 0 {
		panic("no return value specified for WatchFeeTokenAdded")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterFeeTokenAdded, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, feeToken)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterFeeTokenAdded, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, feeToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterFeeTokenAdded, []common.Address) error); ok {
		r1 = rf(opts, sink, feeToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchFeeTokenAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchFeeTokenAdded'
type FeeQuoterInterface_WatchFeeTokenAdded_Call struct {
	*mock.Call
}

// WatchFeeTokenAdded is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterFeeTokenAdded
//   - feeToken []common.Address
func (_e *FeeQuoterInterface_Expecter) WatchFeeTokenAdded(opts interface{}, sink interface{}, feeToken interface{}) *FeeQuoterInterface_WatchFeeTokenAdded_Call {
	return &FeeQuoterInterface_WatchFeeTokenAdded_Call{Call: _e.mock.On("WatchFeeTokenAdded", opts, sink, feeToken)}
}

func (_c *FeeQuoterInterface_WatchFeeTokenAdded_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterFeeTokenAdded, feeToken []common.Address)) *FeeQuoterInterface_WatchFeeTokenAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterFeeTokenAdded), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchFeeTokenAdded_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchFeeTokenAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchFeeTokenAdded_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterFeeTokenAdded, []common.Address) (event.Subscription, error)) *FeeQuoterInterface_WatchFeeTokenAdded_Call {
	_c.Call.Return(run)
	return _c
}

// WatchFeeTokenRemoved provides a mock function with given fields: opts, sink, feeToken
func (_m *FeeQuoterInterface) WatchFeeTokenRemoved(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterFeeTokenRemoved, feeToken []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, feeToken)

	if len(ret) == 0 {
		panic("no return value specified for WatchFeeTokenRemoved")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterFeeTokenRemoved, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, feeToken)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterFeeTokenRemoved, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, feeToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterFeeTokenRemoved, []common.Address) error); ok {
		r1 = rf(opts, sink, feeToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchFeeTokenRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchFeeTokenRemoved'
type FeeQuoterInterface_WatchFeeTokenRemoved_Call struct {
	*mock.Call
}

// WatchFeeTokenRemoved is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterFeeTokenRemoved
//   - feeToken []common.Address
func (_e *FeeQuoterInterface_Expecter) WatchFeeTokenRemoved(opts interface{}, sink interface{}, feeToken interface{}) *FeeQuoterInterface_WatchFeeTokenRemoved_Call {
	return &FeeQuoterInterface_WatchFeeTokenRemoved_Call{Call: _e.mock.On("WatchFeeTokenRemoved", opts, sink, feeToken)}
}

func (_c *FeeQuoterInterface_WatchFeeTokenRemoved_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterFeeTokenRemoved, feeToken []common.Address)) *FeeQuoterInterface_WatchFeeTokenRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterFeeTokenRemoved), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchFeeTokenRemoved_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchFeeTokenRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchFeeTokenRemoved_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterFeeTokenRemoved, []common.Address) (event.Subscription, error)) *FeeQuoterInterface_WatchFeeTokenRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferRequested provides a mock function with given fields: opts, sink, from, to
func (_m *FeeQuoterInterface) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferRequested")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterOwnershipTransferRequested, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterOwnershipTransferRequested, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferRequested'
type FeeQuoterInterface_WatchOwnershipTransferRequested_Call struct {
	*mock.Call
}

// WatchOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterOwnershipTransferRequested
//   - from []common.Address
//   - to []common.Address
func (_e *FeeQuoterInterface_Expecter) WatchOwnershipTransferRequested(opts interface{}, sink interface{}, from interface{}, to interface{}) *FeeQuoterInterface_WatchOwnershipTransferRequested_Call {
	return &FeeQuoterInterface_WatchOwnershipTransferRequested_Call{Call: _e.mock.On("WatchOwnershipTransferRequested", opts, sink, from, to)}
}

func (_c *FeeQuoterInterface_WatchOwnershipTransferRequested_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterOwnershipTransferRequested, from []common.Address, to []common.Address)) *FeeQuoterInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterOwnershipTransferRequested), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchOwnershipTransferRequested_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)) *FeeQuoterInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferred provides a mock function with given fields: opts, sink, from, to
func (_m *FeeQuoterInterface) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferred")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterOwnershipTransferred, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterOwnershipTransferred, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferred'
type FeeQuoterInterface_WatchOwnershipTransferred_Call struct {
	*mock.Call
}

// WatchOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterOwnershipTransferred
//   - from []common.Address
//   - to []common.Address
func (_e *FeeQuoterInterface_Expecter) WatchOwnershipTransferred(opts interface{}, sink interface{}, from interface{}, to interface{}) *FeeQuoterInterface_WatchOwnershipTransferred_Call {
	return &FeeQuoterInterface_WatchOwnershipTransferred_Call{Call: _e.mock.On("WatchOwnershipTransferred", opts, sink, from, to)}
}

func (_c *FeeQuoterInterface_WatchOwnershipTransferred_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterOwnershipTransferred, from []common.Address, to []common.Address)) *FeeQuoterInterface_WatchOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterOwnershipTransferred), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchOwnershipTransferred_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchOwnershipTransferred_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)) *FeeQuoterInterface_WatchOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// WatchPremiumMultiplierWeiPerEthUpdated provides a mock function with given fields: opts, sink, token
func (_m *FeeQuoterInterface) WatchPremiumMultiplierWeiPerEthUpdated(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, token []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, token)

	if len(ret) == 0 {
		panic("no return value specified for WatchPremiumMultiplierWeiPerEthUpdated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, []common.Address) error); ok {
		r1 = rf(opts, sink, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchPremiumMultiplierWeiPerEthUpdated'
type FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call struct {
	*mock.Call
}

// WatchPremiumMultiplierWeiPerEthUpdated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) WatchPremiumMultiplierWeiPerEthUpdated(opts interface{}, sink interface{}, token interface{}) *FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call {
	return &FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call{Call: _e.mock.On("WatchPremiumMultiplierWeiPerEthUpdated", opts, sink, token)}
}

func (_c *FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, token []common.Address)) *FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterPremiumMultiplierWeiPerEthUpdated, []common.Address) (event.Subscription, error)) *FeeQuoterInterface_WatchPremiumMultiplierWeiPerEthUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// WatchPriceFeedPerTokenUpdated provides a mock function with given fields: opts, sink, token
func (_m *FeeQuoterInterface) WatchPriceFeedPerTokenUpdated(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, token []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, token)

	if len(ret) == 0 {
		panic("no return value specified for WatchPriceFeedPerTokenUpdated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, []common.Address) error); ok {
		r1 = rf(opts, sink, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchPriceFeedPerTokenUpdated'
type FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call struct {
	*mock.Call
}

// WatchPriceFeedPerTokenUpdated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) WatchPriceFeedPerTokenUpdated(opts interface{}, sink interface{}, token interface{}) *FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call {
	return &FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call{Call: _e.mock.On("WatchPriceFeedPerTokenUpdated", opts, sink, token)}
}

func (_c *FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, token []common.Address)) *FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterPriceFeedPerTokenUpdated, []common.Address) (event.Subscription, error)) *FeeQuoterInterface_WatchPriceFeedPerTokenUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// WatchReportPermissionSet provides a mock function with given fields: opts, sink, reportId
func (_m *FeeQuoterInterface) WatchReportPermissionSet(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterReportPermissionSet, reportId [][32]byte) (event.Subscription, error) {
	ret := _m.Called(opts, sink, reportId)

	if len(ret) == 0 {
		panic("no return value specified for WatchReportPermissionSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterReportPermissionSet, [][32]byte) (event.Subscription, error)); ok {
		return rf(opts, sink, reportId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterReportPermissionSet, [][32]byte) event.Subscription); ok {
		r0 = rf(opts, sink, reportId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterReportPermissionSet, [][32]byte) error); ok {
		r1 = rf(opts, sink, reportId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchReportPermissionSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchReportPermissionSet'
type FeeQuoterInterface_WatchReportPermissionSet_Call struct {
	*mock.Call
}

// WatchReportPermissionSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterReportPermissionSet
//   - reportId [][32]byte
func (_e *FeeQuoterInterface_Expecter) WatchReportPermissionSet(opts interface{}, sink interface{}, reportId interface{}) *FeeQuoterInterface_WatchReportPermissionSet_Call {
	return &FeeQuoterInterface_WatchReportPermissionSet_Call{Call: _e.mock.On("WatchReportPermissionSet", opts, sink, reportId)}
}

func (_c *FeeQuoterInterface_WatchReportPermissionSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterReportPermissionSet, reportId [][32]byte)) *FeeQuoterInterface_WatchReportPermissionSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterReportPermissionSet), args[2].([][32]byte))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchReportPermissionSet_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchReportPermissionSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchReportPermissionSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterReportPermissionSet, [][32]byte) (event.Subscription, error)) *FeeQuoterInterface_WatchReportPermissionSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchTokenTransferFeeConfigDeleted provides a mock function with given fields: opts, sink, destChainSelector, token
func (_m *FeeQuoterInterface) WatchTokenTransferFeeConfigDeleted(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, destChainSelector []uint64, token []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, destChainSelector, token)

	if len(ret) == 0 {
		panic("no return value specified for WatchTokenTransferFeeConfigDeleted")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, []uint64, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, destChainSelector, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, []uint64, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, destChainSelector, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, []uint64, []common.Address) error); ok {
		r1 = rf(opts, sink, destChainSelector, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchTokenTransferFeeConfigDeleted'
type FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call struct {
	*mock.Call
}

// WatchTokenTransferFeeConfigDeleted is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted
//   - destChainSelector []uint64
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) WatchTokenTransferFeeConfigDeleted(opts interface{}, sink interface{}, destChainSelector interface{}, token interface{}) *FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call {
	return &FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call{Call: _e.mock.On("WatchTokenTransferFeeConfigDeleted", opts, sink, destChainSelector, token)}
}

func (_c *FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, destChainSelector []uint64, token []common.Address)) *FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted), args[2].([]uint64), args[3].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigDeleted, []uint64, []common.Address) (event.Subscription, error)) *FeeQuoterInterface_WatchTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(run)
	return _c
}

// WatchTokenTransferFeeConfigUpdated provides a mock function with given fields: opts, sink, destChainSelector, token
func (_m *FeeQuoterInterface) WatchTokenTransferFeeConfigUpdated(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, destChainSelector []uint64, token []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, destChainSelector, token)

	if len(ret) == 0 {
		panic("no return value specified for WatchTokenTransferFeeConfigUpdated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, []uint64, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, destChainSelector, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, []uint64, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, destChainSelector, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, []uint64, []common.Address) error); ok {
		r1 = rf(opts, sink, destChainSelector, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchTokenTransferFeeConfigUpdated'
type FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call struct {
	*mock.Call
}

// WatchTokenTransferFeeConfigUpdated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated
//   - destChainSelector []uint64
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) WatchTokenTransferFeeConfigUpdated(opts interface{}, sink interface{}, destChainSelector interface{}, token interface{}) *FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call {
	return &FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call{Call: _e.mock.On("WatchTokenTransferFeeConfigUpdated", opts, sink, destChainSelector, token)}
}

func (_c *FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, destChainSelector []uint64, token []common.Address)) *FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated), args[2].([]uint64), args[3].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterTokenTransferFeeConfigUpdated, []uint64, []common.Address) (event.Subscription, error)) *FeeQuoterInterface_WatchTokenTransferFeeConfigUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// WatchUsdPerTokenUpdated provides a mock function with given fields: opts, sink, token
func (_m *FeeQuoterInterface) WatchUsdPerTokenUpdated(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterUsdPerTokenUpdated, token []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, token)

	if len(ret) == 0 {
		panic("no return value specified for WatchUsdPerTokenUpdated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterUsdPerTokenUpdated, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterUsdPerTokenUpdated, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, token)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterUsdPerTokenUpdated, []common.Address) error); ok {
		r1 = rf(opts, sink, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchUsdPerTokenUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchUsdPerTokenUpdated'
type FeeQuoterInterface_WatchUsdPerTokenUpdated_Call struct {
	*mock.Call
}

// WatchUsdPerTokenUpdated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterUsdPerTokenUpdated
//   - token []common.Address
func (_e *FeeQuoterInterface_Expecter) WatchUsdPerTokenUpdated(opts interface{}, sink interface{}, token interface{}) *FeeQuoterInterface_WatchUsdPerTokenUpdated_Call {
	return &FeeQuoterInterface_WatchUsdPerTokenUpdated_Call{Call: _e.mock.On("WatchUsdPerTokenUpdated", opts, sink, token)}
}

func (_c *FeeQuoterInterface_WatchUsdPerTokenUpdated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterUsdPerTokenUpdated, token []common.Address)) *FeeQuoterInterface_WatchUsdPerTokenUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterUsdPerTokenUpdated), args[2].([]common.Address))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchUsdPerTokenUpdated_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchUsdPerTokenUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchUsdPerTokenUpdated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterUsdPerTokenUpdated, []common.Address) (event.Subscription, error)) *FeeQuoterInterface_WatchUsdPerTokenUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// WatchUsdPerUnitGasUpdated provides a mock function with given fields: opts, sink, destChain
func (_m *FeeQuoterInterface) WatchUsdPerUnitGasUpdated(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterUsdPerUnitGasUpdated, destChain []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, destChain)

	if len(ret) == 0 {
		panic("no return value specified for WatchUsdPerUnitGasUpdated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterUsdPerUnitGasUpdated, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, destChain)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterUsdPerUnitGasUpdated, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, destChain)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterUsdPerUnitGasUpdated, []uint64) error); ok {
		r1 = rf(opts, sink, destChain)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchUsdPerUnitGasUpdated'
type FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call struct {
	*mock.Call
}

// WatchUsdPerUnitGasUpdated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *fee_quoter.FeeQuoterUsdPerUnitGasUpdated
//   - destChain []uint64
func (_e *FeeQuoterInterface_Expecter) WatchUsdPerUnitGasUpdated(opts interface{}, sink interface{}, destChain interface{}) *FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call {
	return &FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call{Call: _e.mock.On("WatchUsdPerUnitGasUpdated", opts, sink, destChain)}
}

func (_c *FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *fee_quoter.FeeQuoterUsdPerUnitGasUpdated, destChain []uint64)) *FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *fee_quoter.FeeQuoterUsdPerUnitGasUpdated), args[2].([]uint64))
	})
	return _c
}

func (_c *FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call) Return(_a0 event.Subscription, _a1 error) *FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *fee_quoter.FeeQuoterUsdPerUnitGasUpdated, []uint64) (event.Subscription, error)) *FeeQuoterInterface_WatchUsdPerUnitGasUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// NewFeeQuoterInterface creates a new instance of FeeQuoterInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFeeQuoterInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *FeeQuoterInterface {
	mock := &FeeQuoterInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
