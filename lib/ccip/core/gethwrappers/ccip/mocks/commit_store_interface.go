// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_contracts

import (
	big "math/big"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"
	commit_store "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/ccip/generated/commit_store"

	event "github.com/ethereum/go-ethereum/event"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// CommitStoreInterface is an autogenerated mock type for the CommitStoreInterface type
type CommitStoreInterface struct {
	mock.Mock
}

type CommitStoreInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *CommitStoreInterface) EXPECT() *CommitStoreInterface_Expecter {
	return &CommitStoreInterface_Expecter{mock: &_m.<PERSON>ck}
}

// AcceptOwnership provides a mock function with given fields: opts
func (_m *CommitStoreInterface) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for AcceptOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_AcceptOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptOwnership'
type CommitStoreInterface_AcceptOwnership_Call struct {
	*mock.Call
}

// AcceptOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *CommitStoreInterface_Expecter) AcceptOwnership(opts interface{}) *CommitStoreInterface_AcceptOwnership_Call {
	return &CommitStoreInterface_AcceptOwnership_Call{Call: _e.mock.On("AcceptOwnership", opts)}
}

func (_c *CommitStoreInterface_AcceptOwnership_Call) Run(run func(opts *bind.TransactOpts)) *CommitStoreInterface_AcceptOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_AcceptOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *CommitStoreInterface_AcceptOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_AcceptOwnership_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *CommitStoreInterface_AcceptOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// Address provides a mock function with given fields:
func (_m *CommitStoreInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// CommitStoreInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type CommitStoreInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *CommitStoreInterface_Expecter) Address() *CommitStoreInterface_Address_Call {
	return &CommitStoreInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *CommitStoreInterface_Address_Call) Run(run func()) *CommitStoreInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *CommitStoreInterface_Address_Call) Return(_a0 common.Address) *CommitStoreInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *CommitStoreInterface_Address_Call) RunAndReturn(run func() common.Address) *CommitStoreInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// FilterConfigSet provides a mock function with given fields: opts
func (_m *CommitStoreInterface) FilterConfigSet(opts *bind.FilterOpts) (*commit_store.CommitStoreConfigSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterConfigSet")
	}

	var r0 *commit_store.CommitStoreConfigSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*commit_store.CommitStoreConfigSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *commit_store.CommitStoreConfigSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreConfigSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterConfigSet'
type CommitStoreInterface_FilterConfigSet_Call struct {
	*mock.Call
}

// FilterConfigSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *CommitStoreInterface_Expecter) FilterConfigSet(opts interface{}) *CommitStoreInterface_FilterConfigSet_Call {
	return &CommitStoreInterface_FilterConfigSet_Call{Call: _e.mock.On("FilterConfigSet", opts)}
}

func (_c *CommitStoreInterface_FilterConfigSet_Call) Run(run func(opts *bind.FilterOpts)) *CommitStoreInterface_FilterConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterConfigSet_Call) Return(_a0 *commit_store.CommitStoreConfigSetIterator, _a1 error) *CommitStoreInterface_FilterConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterConfigSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*commit_store.CommitStoreConfigSetIterator, error)) *CommitStoreInterface_FilterConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterConfigSet0 provides a mock function with given fields: opts
func (_m *CommitStoreInterface) FilterConfigSet0(opts *bind.FilterOpts) (*commit_store.CommitStoreConfigSet0Iterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterConfigSet0")
	}

	var r0 *commit_store.CommitStoreConfigSet0Iterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*commit_store.CommitStoreConfigSet0Iterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *commit_store.CommitStoreConfigSet0Iterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreConfigSet0Iterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterConfigSet0_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterConfigSet0'
type CommitStoreInterface_FilterConfigSet0_Call struct {
	*mock.Call
}

// FilterConfigSet0 is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *CommitStoreInterface_Expecter) FilterConfigSet0(opts interface{}) *CommitStoreInterface_FilterConfigSet0_Call {
	return &CommitStoreInterface_FilterConfigSet0_Call{Call: _e.mock.On("FilterConfigSet0", opts)}
}

func (_c *CommitStoreInterface_FilterConfigSet0_Call) Run(run func(opts *bind.FilterOpts)) *CommitStoreInterface_FilterConfigSet0_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterConfigSet0_Call) Return(_a0 *commit_store.CommitStoreConfigSet0Iterator, _a1 error) *CommitStoreInterface_FilterConfigSet0_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterConfigSet0_Call) RunAndReturn(run func(*bind.FilterOpts) (*commit_store.CommitStoreConfigSet0Iterator, error)) *CommitStoreInterface_FilterConfigSet0_Call {
	_c.Call.Return(run)
	return _c
}

// FilterLatestPriceEpochAndRoundSet provides a mock function with given fields: opts
func (_m *CommitStoreInterface) FilterLatestPriceEpochAndRoundSet(opts *bind.FilterOpts) (*commit_store.CommitStoreLatestPriceEpochAndRoundSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterLatestPriceEpochAndRoundSet")
	}

	var r0 *commit_store.CommitStoreLatestPriceEpochAndRoundSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*commit_store.CommitStoreLatestPriceEpochAndRoundSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *commit_store.CommitStoreLatestPriceEpochAndRoundSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreLatestPriceEpochAndRoundSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterLatestPriceEpochAndRoundSet'
type CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call struct {
	*mock.Call
}

// FilterLatestPriceEpochAndRoundSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *CommitStoreInterface_Expecter) FilterLatestPriceEpochAndRoundSet(opts interface{}) *CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call {
	return &CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call{Call: _e.mock.On("FilterLatestPriceEpochAndRoundSet", opts)}
}

func (_c *CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call) Run(run func(opts *bind.FilterOpts)) *CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call) Return(_a0 *commit_store.CommitStoreLatestPriceEpochAndRoundSetIterator, _a1 error) *CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*commit_store.CommitStoreLatestPriceEpochAndRoundSetIterator, error)) *CommitStoreInterface_FilterLatestPriceEpochAndRoundSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferRequested provides a mock function with given fields: opts, from, to
func (_m *CommitStoreInterface) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*commit_store.CommitStoreOwnershipTransferRequestedIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferRequested")
	}

	var r0 *commit_store.CommitStoreOwnershipTransferRequestedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*commit_store.CommitStoreOwnershipTransferRequestedIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *commit_store.CommitStoreOwnershipTransferRequestedIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreOwnershipTransferRequestedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferRequested'
type CommitStoreInterface_FilterOwnershipTransferRequested_Call struct {
	*mock.Call
}

// FilterOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *CommitStoreInterface_Expecter) FilterOwnershipTransferRequested(opts interface{}, from interface{}, to interface{}) *CommitStoreInterface_FilterOwnershipTransferRequested_Call {
	return &CommitStoreInterface_FilterOwnershipTransferRequested_Call{Call: _e.mock.On("FilterOwnershipTransferRequested", opts, from, to)}
}

func (_c *CommitStoreInterface_FilterOwnershipTransferRequested_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *CommitStoreInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterOwnershipTransferRequested_Call) Return(_a0 *commit_store.CommitStoreOwnershipTransferRequestedIterator, _a1 error) *CommitStoreInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*commit_store.CommitStoreOwnershipTransferRequestedIterator, error)) *CommitStoreInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferred provides a mock function with given fields: opts, from, to
func (_m *CommitStoreInterface) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*commit_store.CommitStoreOwnershipTransferredIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferred")
	}

	var r0 *commit_store.CommitStoreOwnershipTransferredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*commit_store.CommitStoreOwnershipTransferredIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *commit_store.CommitStoreOwnershipTransferredIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreOwnershipTransferredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferred'
type CommitStoreInterface_FilterOwnershipTransferred_Call struct {
	*mock.Call
}

// FilterOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *CommitStoreInterface_Expecter) FilterOwnershipTransferred(opts interface{}, from interface{}, to interface{}) *CommitStoreInterface_FilterOwnershipTransferred_Call {
	return &CommitStoreInterface_FilterOwnershipTransferred_Call{Call: _e.mock.On("FilterOwnershipTransferred", opts, from, to)}
}

func (_c *CommitStoreInterface_FilterOwnershipTransferred_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *CommitStoreInterface_FilterOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterOwnershipTransferred_Call) Return(_a0 *commit_store.CommitStoreOwnershipTransferredIterator, _a1 error) *CommitStoreInterface_FilterOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterOwnershipTransferred_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*commit_store.CommitStoreOwnershipTransferredIterator, error)) *CommitStoreInterface_FilterOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// FilterPaused provides a mock function with given fields: opts
func (_m *CommitStoreInterface) FilterPaused(opts *bind.FilterOpts) (*commit_store.CommitStorePausedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterPaused")
	}

	var r0 *commit_store.CommitStorePausedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*commit_store.CommitStorePausedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *commit_store.CommitStorePausedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStorePausedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterPaused_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterPaused'
type CommitStoreInterface_FilterPaused_Call struct {
	*mock.Call
}

// FilterPaused is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *CommitStoreInterface_Expecter) FilterPaused(opts interface{}) *CommitStoreInterface_FilterPaused_Call {
	return &CommitStoreInterface_FilterPaused_Call{Call: _e.mock.On("FilterPaused", opts)}
}

func (_c *CommitStoreInterface_FilterPaused_Call) Run(run func(opts *bind.FilterOpts)) *CommitStoreInterface_FilterPaused_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterPaused_Call) Return(_a0 *commit_store.CommitStorePausedIterator, _a1 error) *CommitStoreInterface_FilterPaused_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterPaused_Call) RunAndReturn(run func(*bind.FilterOpts) (*commit_store.CommitStorePausedIterator, error)) *CommitStoreInterface_FilterPaused_Call {
	_c.Call.Return(run)
	return _c
}

// FilterReportAccepted provides a mock function with given fields: opts
func (_m *CommitStoreInterface) FilterReportAccepted(opts *bind.FilterOpts) (*commit_store.CommitStoreReportAcceptedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterReportAccepted")
	}

	var r0 *commit_store.CommitStoreReportAcceptedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*commit_store.CommitStoreReportAcceptedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *commit_store.CommitStoreReportAcceptedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreReportAcceptedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterReportAccepted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterReportAccepted'
type CommitStoreInterface_FilterReportAccepted_Call struct {
	*mock.Call
}

// FilterReportAccepted is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *CommitStoreInterface_Expecter) FilterReportAccepted(opts interface{}) *CommitStoreInterface_FilterReportAccepted_Call {
	return &CommitStoreInterface_FilterReportAccepted_Call{Call: _e.mock.On("FilterReportAccepted", opts)}
}

func (_c *CommitStoreInterface_FilterReportAccepted_Call) Run(run func(opts *bind.FilterOpts)) *CommitStoreInterface_FilterReportAccepted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterReportAccepted_Call) Return(_a0 *commit_store.CommitStoreReportAcceptedIterator, _a1 error) *CommitStoreInterface_FilterReportAccepted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterReportAccepted_Call) RunAndReturn(run func(*bind.FilterOpts) (*commit_store.CommitStoreReportAcceptedIterator, error)) *CommitStoreInterface_FilterReportAccepted_Call {
	_c.Call.Return(run)
	return _c
}

// FilterRootRemoved provides a mock function with given fields: opts
func (_m *CommitStoreInterface) FilterRootRemoved(opts *bind.FilterOpts) (*commit_store.CommitStoreRootRemovedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterRootRemoved")
	}

	var r0 *commit_store.CommitStoreRootRemovedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*commit_store.CommitStoreRootRemovedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *commit_store.CommitStoreRootRemovedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreRootRemovedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterRootRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterRootRemoved'
type CommitStoreInterface_FilterRootRemoved_Call struct {
	*mock.Call
}

// FilterRootRemoved is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *CommitStoreInterface_Expecter) FilterRootRemoved(opts interface{}) *CommitStoreInterface_FilterRootRemoved_Call {
	return &CommitStoreInterface_FilterRootRemoved_Call{Call: _e.mock.On("FilterRootRemoved", opts)}
}

func (_c *CommitStoreInterface_FilterRootRemoved_Call) Run(run func(opts *bind.FilterOpts)) *CommitStoreInterface_FilterRootRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterRootRemoved_Call) Return(_a0 *commit_store.CommitStoreRootRemovedIterator, _a1 error) *CommitStoreInterface_FilterRootRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterRootRemoved_Call) RunAndReturn(run func(*bind.FilterOpts) (*commit_store.CommitStoreRootRemovedIterator, error)) *CommitStoreInterface_FilterRootRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSequenceNumberSet provides a mock function with given fields: opts
func (_m *CommitStoreInterface) FilterSequenceNumberSet(opts *bind.FilterOpts) (*commit_store.CommitStoreSequenceNumberSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterSequenceNumberSet")
	}

	var r0 *commit_store.CommitStoreSequenceNumberSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*commit_store.CommitStoreSequenceNumberSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *commit_store.CommitStoreSequenceNumberSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreSequenceNumberSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterSequenceNumberSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSequenceNumberSet'
type CommitStoreInterface_FilterSequenceNumberSet_Call struct {
	*mock.Call
}

// FilterSequenceNumberSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *CommitStoreInterface_Expecter) FilterSequenceNumberSet(opts interface{}) *CommitStoreInterface_FilterSequenceNumberSet_Call {
	return &CommitStoreInterface_FilterSequenceNumberSet_Call{Call: _e.mock.On("FilterSequenceNumberSet", opts)}
}

func (_c *CommitStoreInterface_FilterSequenceNumberSet_Call) Run(run func(opts *bind.FilterOpts)) *CommitStoreInterface_FilterSequenceNumberSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterSequenceNumberSet_Call) Return(_a0 *commit_store.CommitStoreSequenceNumberSetIterator, _a1 error) *CommitStoreInterface_FilterSequenceNumberSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterSequenceNumberSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*commit_store.CommitStoreSequenceNumberSetIterator, error)) *CommitStoreInterface_FilterSequenceNumberSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterTransmitted provides a mock function with given fields: opts
func (_m *CommitStoreInterface) FilterTransmitted(opts *bind.FilterOpts) (*commit_store.CommitStoreTransmittedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterTransmitted")
	}

	var r0 *commit_store.CommitStoreTransmittedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*commit_store.CommitStoreTransmittedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *commit_store.CommitStoreTransmittedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreTransmittedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterTransmitted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterTransmitted'
type CommitStoreInterface_FilterTransmitted_Call struct {
	*mock.Call
}

// FilterTransmitted is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *CommitStoreInterface_Expecter) FilterTransmitted(opts interface{}) *CommitStoreInterface_FilterTransmitted_Call {
	return &CommitStoreInterface_FilterTransmitted_Call{Call: _e.mock.On("FilterTransmitted", opts)}
}

func (_c *CommitStoreInterface_FilterTransmitted_Call) Run(run func(opts *bind.FilterOpts)) *CommitStoreInterface_FilterTransmitted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterTransmitted_Call) Return(_a0 *commit_store.CommitStoreTransmittedIterator, _a1 error) *CommitStoreInterface_FilterTransmitted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterTransmitted_Call) RunAndReturn(run func(*bind.FilterOpts) (*commit_store.CommitStoreTransmittedIterator, error)) *CommitStoreInterface_FilterTransmitted_Call {
	_c.Call.Return(run)
	return _c
}

// FilterUnpaused provides a mock function with given fields: opts
func (_m *CommitStoreInterface) FilterUnpaused(opts *bind.FilterOpts) (*commit_store.CommitStoreUnpausedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterUnpaused")
	}

	var r0 *commit_store.CommitStoreUnpausedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*commit_store.CommitStoreUnpausedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *commit_store.CommitStoreUnpausedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreUnpausedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_FilterUnpaused_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterUnpaused'
type CommitStoreInterface_FilterUnpaused_Call struct {
	*mock.Call
}

// FilterUnpaused is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *CommitStoreInterface_Expecter) FilterUnpaused(opts interface{}) *CommitStoreInterface_FilterUnpaused_Call {
	return &CommitStoreInterface_FilterUnpaused_Call{Call: _e.mock.On("FilterUnpaused", opts)}
}

func (_c *CommitStoreInterface_FilterUnpaused_Call) Run(run func(opts *bind.FilterOpts)) *CommitStoreInterface_FilterUnpaused_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_FilterUnpaused_Call) Return(_a0 *commit_store.CommitStoreUnpausedIterator, _a1 error) *CommitStoreInterface_FilterUnpaused_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_FilterUnpaused_Call) RunAndReturn(run func(*bind.FilterOpts) (*commit_store.CommitStoreUnpausedIterator, error)) *CommitStoreInterface_FilterUnpaused_Call {
	_c.Call.Return(run)
	return _c
}

// GetDynamicConfig provides a mock function with given fields: opts
func (_m *CommitStoreInterface) GetDynamicConfig(opts *bind.CallOpts) (commit_store.CommitStoreDynamicConfig, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetDynamicConfig")
	}

	var r0 commit_store.CommitStoreDynamicConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (commit_store.CommitStoreDynamicConfig, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) commit_store.CommitStoreDynamicConfig); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(commit_store.CommitStoreDynamicConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_GetDynamicConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDynamicConfig'
type CommitStoreInterface_GetDynamicConfig_Call struct {
	*mock.Call
}

// GetDynamicConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) GetDynamicConfig(opts interface{}) *CommitStoreInterface_GetDynamicConfig_Call {
	return &CommitStoreInterface_GetDynamicConfig_Call{Call: _e.mock.On("GetDynamicConfig", opts)}
}

func (_c *CommitStoreInterface_GetDynamicConfig_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_GetDynamicConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_GetDynamicConfig_Call) Return(_a0 commit_store.CommitStoreDynamicConfig, _a1 error) *CommitStoreInterface_GetDynamicConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_GetDynamicConfig_Call) RunAndReturn(run func(*bind.CallOpts) (commit_store.CommitStoreDynamicConfig, error)) *CommitStoreInterface_GetDynamicConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetExpectedNextSequenceNumber provides a mock function with given fields: opts
func (_m *CommitStoreInterface) GetExpectedNextSequenceNumber(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetExpectedNextSequenceNumber")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_GetExpectedNextSequenceNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExpectedNextSequenceNumber'
type CommitStoreInterface_GetExpectedNextSequenceNumber_Call struct {
	*mock.Call
}

// GetExpectedNextSequenceNumber is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) GetExpectedNextSequenceNumber(opts interface{}) *CommitStoreInterface_GetExpectedNextSequenceNumber_Call {
	return &CommitStoreInterface_GetExpectedNextSequenceNumber_Call{Call: _e.mock.On("GetExpectedNextSequenceNumber", opts)}
}

func (_c *CommitStoreInterface_GetExpectedNextSequenceNumber_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_GetExpectedNextSequenceNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_GetExpectedNextSequenceNumber_Call) Return(_a0 uint64, _a1 error) *CommitStoreInterface_GetExpectedNextSequenceNumber_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_GetExpectedNextSequenceNumber_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *CommitStoreInterface_GetExpectedNextSequenceNumber_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestPriceEpochAndRound provides a mock function with given fields: opts
func (_m *CommitStoreInterface) GetLatestPriceEpochAndRound(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestPriceEpochAndRound")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_GetLatestPriceEpochAndRound_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestPriceEpochAndRound'
type CommitStoreInterface_GetLatestPriceEpochAndRound_Call struct {
	*mock.Call
}

// GetLatestPriceEpochAndRound is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) GetLatestPriceEpochAndRound(opts interface{}) *CommitStoreInterface_GetLatestPriceEpochAndRound_Call {
	return &CommitStoreInterface_GetLatestPriceEpochAndRound_Call{Call: _e.mock.On("GetLatestPriceEpochAndRound", opts)}
}

func (_c *CommitStoreInterface_GetLatestPriceEpochAndRound_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_GetLatestPriceEpochAndRound_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_GetLatestPriceEpochAndRound_Call) Return(_a0 uint64, _a1 error) *CommitStoreInterface_GetLatestPriceEpochAndRound_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_GetLatestPriceEpochAndRound_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *CommitStoreInterface_GetLatestPriceEpochAndRound_Call {
	_c.Call.Return(run)
	return _c
}

// GetMerkleRoot provides a mock function with given fields: opts, root
func (_m *CommitStoreInterface) GetMerkleRoot(opts *bind.CallOpts, root [32]byte) (*big.Int, error) {
	ret := _m.Called(opts, root)

	if len(ret) == 0 {
		panic("no return value specified for GetMerkleRoot")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, [32]byte) (*big.Int, error)); ok {
		return rf(opts, root)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, [32]byte) *big.Int); ok {
		r0 = rf(opts, root)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, [32]byte) error); ok {
		r1 = rf(opts, root)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_GetMerkleRoot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMerkleRoot'
type CommitStoreInterface_GetMerkleRoot_Call struct {
	*mock.Call
}

// GetMerkleRoot is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - root [32]byte
func (_e *CommitStoreInterface_Expecter) GetMerkleRoot(opts interface{}, root interface{}) *CommitStoreInterface_GetMerkleRoot_Call {
	return &CommitStoreInterface_GetMerkleRoot_Call{Call: _e.mock.On("GetMerkleRoot", opts, root)}
}

func (_c *CommitStoreInterface_GetMerkleRoot_Call) Run(run func(opts *bind.CallOpts, root [32]byte)) *CommitStoreInterface_GetMerkleRoot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].([32]byte))
	})
	return _c
}

func (_c *CommitStoreInterface_GetMerkleRoot_Call) Return(_a0 *big.Int, _a1 error) *CommitStoreInterface_GetMerkleRoot_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_GetMerkleRoot_Call) RunAndReturn(run func(*bind.CallOpts, [32]byte) (*big.Int, error)) *CommitStoreInterface_GetMerkleRoot_Call {
	_c.Call.Return(run)
	return _c
}

// GetStaticConfig provides a mock function with given fields: opts
func (_m *CommitStoreInterface) GetStaticConfig(opts *bind.CallOpts) (commit_store.CommitStoreStaticConfig, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetStaticConfig")
	}

	var r0 commit_store.CommitStoreStaticConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (commit_store.CommitStoreStaticConfig, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) commit_store.CommitStoreStaticConfig); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(commit_store.CommitStoreStaticConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_GetStaticConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStaticConfig'
type CommitStoreInterface_GetStaticConfig_Call struct {
	*mock.Call
}

// GetStaticConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) GetStaticConfig(opts interface{}) *CommitStoreInterface_GetStaticConfig_Call {
	return &CommitStoreInterface_GetStaticConfig_Call{Call: _e.mock.On("GetStaticConfig", opts)}
}

func (_c *CommitStoreInterface_GetStaticConfig_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_GetStaticConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_GetStaticConfig_Call) Return(_a0 commit_store.CommitStoreStaticConfig, _a1 error) *CommitStoreInterface_GetStaticConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_GetStaticConfig_Call) RunAndReturn(run func(*bind.CallOpts) (commit_store.CommitStoreStaticConfig, error)) *CommitStoreInterface_GetStaticConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetTransmitters provides a mock function with given fields: opts
func (_m *CommitStoreInterface) GetTransmitters(opts *bind.CallOpts) ([]common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetTransmitters")
	}

	var r0 []common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) ([]common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) []common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_GetTransmitters_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTransmitters'
type CommitStoreInterface_GetTransmitters_Call struct {
	*mock.Call
}

// GetTransmitters is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) GetTransmitters(opts interface{}) *CommitStoreInterface_GetTransmitters_Call {
	return &CommitStoreInterface_GetTransmitters_Call{Call: _e.mock.On("GetTransmitters", opts)}
}

func (_c *CommitStoreInterface_GetTransmitters_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_GetTransmitters_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_GetTransmitters_Call) Return(_a0 []common.Address, _a1 error) *CommitStoreInterface_GetTransmitters_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_GetTransmitters_Call) RunAndReturn(run func(*bind.CallOpts) ([]common.Address, error)) *CommitStoreInterface_GetTransmitters_Call {
	_c.Call.Return(run)
	return _c
}

// IsBlessed provides a mock function with given fields: opts, root
func (_m *CommitStoreInterface) IsBlessed(opts *bind.CallOpts, root [32]byte) (bool, error) {
	ret := _m.Called(opts, root)

	if len(ret) == 0 {
		panic("no return value specified for IsBlessed")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, [32]byte) (bool, error)); ok {
		return rf(opts, root)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, [32]byte) bool); ok {
		r0 = rf(opts, root)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, [32]byte) error); ok {
		r1 = rf(opts, root)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_IsBlessed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsBlessed'
type CommitStoreInterface_IsBlessed_Call struct {
	*mock.Call
}

// IsBlessed is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - root [32]byte
func (_e *CommitStoreInterface_Expecter) IsBlessed(opts interface{}, root interface{}) *CommitStoreInterface_IsBlessed_Call {
	return &CommitStoreInterface_IsBlessed_Call{Call: _e.mock.On("IsBlessed", opts, root)}
}

func (_c *CommitStoreInterface_IsBlessed_Call) Run(run func(opts *bind.CallOpts, root [32]byte)) *CommitStoreInterface_IsBlessed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].([32]byte))
	})
	return _c
}

func (_c *CommitStoreInterface_IsBlessed_Call) Return(_a0 bool, _a1 error) *CommitStoreInterface_IsBlessed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_IsBlessed_Call) RunAndReturn(run func(*bind.CallOpts, [32]byte) (bool, error)) *CommitStoreInterface_IsBlessed_Call {
	_c.Call.Return(run)
	return _c
}

// IsUnpausedAndNotCursed provides a mock function with given fields: opts
func (_m *CommitStoreInterface) IsUnpausedAndNotCursed(opts *bind.CallOpts) (bool, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for IsUnpausedAndNotCursed")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (bool, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) bool); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_IsUnpausedAndNotCursed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsUnpausedAndNotCursed'
type CommitStoreInterface_IsUnpausedAndNotCursed_Call struct {
	*mock.Call
}

// IsUnpausedAndNotCursed is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) IsUnpausedAndNotCursed(opts interface{}) *CommitStoreInterface_IsUnpausedAndNotCursed_Call {
	return &CommitStoreInterface_IsUnpausedAndNotCursed_Call{Call: _e.mock.On("IsUnpausedAndNotCursed", opts)}
}

func (_c *CommitStoreInterface_IsUnpausedAndNotCursed_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_IsUnpausedAndNotCursed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_IsUnpausedAndNotCursed_Call) Return(_a0 bool, _a1 error) *CommitStoreInterface_IsUnpausedAndNotCursed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_IsUnpausedAndNotCursed_Call) RunAndReturn(run func(*bind.CallOpts) (bool, error)) *CommitStoreInterface_IsUnpausedAndNotCursed_Call {
	_c.Call.Return(run)
	return _c
}

// LatestConfigDetails provides a mock function with given fields: opts
func (_m *CommitStoreInterface) LatestConfigDetails(opts *bind.CallOpts) (commit_store.LatestConfigDetails, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LatestConfigDetails")
	}

	var r0 commit_store.LatestConfigDetails
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (commit_store.LatestConfigDetails, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) commit_store.LatestConfigDetails); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(commit_store.LatestConfigDetails)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_LatestConfigDetails_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestConfigDetails'
type CommitStoreInterface_LatestConfigDetails_Call struct {
	*mock.Call
}

// LatestConfigDetails is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) LatestConfigDetails(opts interface{}) *CommitStoreInterface_LatestConfigDetails_Call {
	return &CommitStoreInterface_LatestConfigDetails_Call{Call: _e.mock.On("LatestConfigDetails", opts)}
}

func (_c *CommitStoreInterface_LatestConfigDetails_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_LatestConfigDetails_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_LatestConfigDetails_Call) Return(_a0 commit_store.LatestConfigDetails, _a1 error) *CommitStoreInterface_LatestConfigDetails_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_LatestConfigDetails_Call) RunAndReturn(run func(*bind.CallOpts) (commit_store.LatestConfigDetails, error)) *CommitStoreInterface_LatestConfigDetails_Call {
	_c.Call.Return(run)
	return _c
}

// LatestConfigDigestAndEpoch provides a mock function with given fields: opts
func (_m *CommitStoreInterface) LatestConfigDigestAndEpoch(opts *bind.CallOpts) (commit_store.LatestConfigDigestAndEpoch, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LatestConfigDigestAndEpoch")
	}

	var r0 commit_store.LatestConfigDigestAndEpoch
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (commit_store.LatestConfigDigestAndEpoch, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) commit_store.LatestConfigDigestAndEpoch); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(commit_store.LatestConfigDigestAndEpoch)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_LatestConfigDigestAndEpoch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestConfigDigestAndEpoch'
type CommitStoreInterface_LatestConfigDigestAndEpoch_Call struct {
	*mock.Call
}

// LatestConfigDigestAndEpoch is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) LatestConfigDigestAndEpoch(opts interface{}) *CommitStoreInterface_LatestConfigDigestAndEpoch_Call {
	return &CommitStoreInterface_LatestConfigDigestAndEpoch_Call{Call: _e.mock.On("LatestConfigDigestAndEpoch", opts)}
}

func (_c *CommitStoreInterface_LatestConfigDigestAndEpoch_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_LatestConfigDigestAndEpoch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_LatestConfigDigestAndEpoch_Call) Return(_a0 commit_store.LatestConfigDigestAndEpoch, _a1 error) *CommitStoreInterface_LatestConfigDigestAndEpoch_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_LatestConfigDigestAndEpoch_Call) RunAndReturn(run func(*bind.CallOpts) (commit_store.LatestConfigDigestAndEpoch, error)) *CommitStoreInterface_LatestConfigDigestAndEpoch_Call {
	_c.Call.Return(run)
	return _c
}

// Owner provides a mock function with given fields: opts
func (_m *CommitStoreInterface) Owner(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Owner")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_Owner_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Owner'
type CommitStoreInterface_Owner_Call struct {
	*mock.Call
}

// Owner is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) Owner(opts interface{}) *CommitStoreInterface_Owner_Call {
	return &CommitStoreInterface_Owner_Call{Call: _e.mock.On("Owner", opts)}
}

func (_c *CommitStoreInterface_Owner_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_Owner_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_Owner_Call) Return(_a0 common.Address, _a1 error) *CommitStoreInterface_Owner_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_Owner_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *CommitStoreInterface_Owner_Call {
	_c.Call.Return(run)
	return _c
}

// ParseConfigSet provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseConfigSet(log types.Log) (*commit_store.CommitStoreConfigSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseConfigSet")
	}

	var r0 *commit_store.CommitStoreConfigSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreConfigSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreConfigSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreConfigSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseConfigSet'
type CommitStoreInterface_ParseConfigSet_Call struct {
	*mock.Call
}

// ParseConfigSet is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseConfigSet(log interface{}) *CommitStoreInterface_ParseConfigSet_Call {
	return &CommitStoreInterface_ParseConfigSet_Call{Call: _e.mock.On("ParseConfigSet", log)}
}

func (_c *CommitStoreInterface_ParseConfigSet_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseConfigSet_Call) Return(_a0 *commit_store.CommitStoreConfigSet, _a1 error) *CommitStoreInterface_ParseConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseConfigSet_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreConfigSet, error)) *CommitStoreInterface_ParseConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseConfigSet0 provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseConfigSet0(log types.Log) (*commit_store.CommitStoreConfigSet0, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseConfigSet0")
	}

	var r0 *commit_store.CommitStoreConfigSet0
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreConfigSet0, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreConfigSet0); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreConfigSet0)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseConfigSet0_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseConfigSet0'
type CommitStoreInterface_ParseConfigSet0_Call struct {
	*mock.Call
}

// ParseConfigSet0 is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseConfigSet0(log interface{}) *CommitStoreInterface_ParseConfigSet0_Call {
	return &CommitStoreInterface_ParseConfigSet0_Call{Call: _e.mock.On("ParseConfigSet0", log)}
}

func (_c *CommitStoreInterface_ParseConfigSet0_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseConfigSet0_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseConfigSet0_Call) Return(_a0 *commit_store.CommitStoreConfigSet0, _a1 error) *CommitStoreInterface_ParseConfigSet0_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseConfigSet0_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreConfigSet0, error)) *CommitStoreInterface_ParseConfigSet0_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLatestPriceEpochAndRoundSet provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseLatestPriceEpochAndRoundSet(log types.Log) (*commit_store.CommitStoreLatestPriceEpochAndRoundSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLatestPriceEpochAndRoundSet")
	}

	var r0 *commit_store.CommitStoreLatestPriceEpochAndRoundSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreLatestPriceEpochAndRoundSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreLatestPriceEpochAndRoundSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreLatestPriceEpochAndRoundSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLatestPriceEpochAndRoundSet'
type CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call struct {
	*mock.Call
}

// ParseLatestPriceEpochAndRoundSet is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseLatestPriceEpochAndRoundSet(log interface{}) *CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call {
	return &CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call{Call: _e.mock.On("ParseLatestPriceEpochAndRoundSet", log)}
}

func (_c *CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call) Return(_a0 *commit_store.CommitStoreLatestPriceEpochAndRoundSet, _a1 error) *CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreLatestPriceEpochAndRoundSet, error)) *CommitStoreInterface_ParseLatestPriceEpochAndRoundSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type CommitStoreInterface_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseLog(log interface{}) *CommitStoreInterface_ParseLog_Call {
	return &CommitStoreInterface_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *CommitStoreInterface_ParseLog_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *CommitStoreInterface_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *CommitStoreInterface_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferRequested provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseOwnershipTransferRequested(log types.Log) (*commit_store.CommitStoreOwnershipTransferRequested, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferRequested")
	}

	var r0 *commit_store.CommitStoreOwnershipTransferRequested
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreOwnershipTransferRequested, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreOwnershipTransferRequested); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreOwnershipTransferRequested)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferRequested'
type CommitStoreInterface_ParseOwnershipTransferRequested_Call struct {
	*mock.Call
}

// ParseOwnershipTransferRequested is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseOwnershipTransferRequested(log interface{}) *CommitStoreInterface_ParseOwnershipTransferRequested_Call {
	return &CommitStoreInterface_ParseOwnershipTransferRequested_Call{Call: _e.mock.On("ParseOwnershipTransferRequested", log)}
}

func (_c *CommitStoreInterface_ParseOwnershipTransferRequested_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseOwnershipTransferRequested_Call) Return(_a0 *commit_store.CommitStoreOwnershipTransferRequested, _a1 error) *CommitStoreInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseOwnershipTransferRequested_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreOwnershipTransferRequested, error)) *CommitStoreInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferred provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseOwnershipTransferred(log types.Log) (*commit_store.CommitStoreOwnershipTransferred, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferred")
	}

	var r0 *commit_store.CommitStoreOwnershipTransferred
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreOwnershipTransferred, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreOwnershipTransferred); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreOwnershipTransferred)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferred'
type CommitStoreInterface_ParseOwnershipTransferred_Call struct {
	*mock.Call
}

// ParseOwnershipTransferred is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseOwnershipTransferred(log interface{}) *CommitStoreInterface_ParseOwnershipTransferred_Call {
	return &CommitStoreInterface_ParseOwnershipTransferred_Call{Call: _e.mock.On("ParseOwnershipTransferred", log)}
}

func (_c *CommitStoreInterface_ParseOwnershipTransferred_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseOwnershipTransferred_Call) Return(_a0 *commit_store.CommitStoreOwnershipTransferred, _a1 error) *CommitStoreInterface_ParseOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseOwnershipTransferred_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreOwnershipTransferred, error)) *CommitStoreInterface_ParseOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// ParsePaused provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParsePaused(log types.Log) (*commit_store.CommitStorePaused, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParsePaused")
	}

	var r0 *commit_store.CommitStorePaused
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStorePaused, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStorePaused); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStorePaused)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParsePaused_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParsePaused'
type CommitStoreInterface_ParsePaused_Call struct {
	*mock.Call
}

// ParsePaused is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParsePaused(log interface{}) *CommitStoreInterface_ParsePaused_Call {
	return &CommitStoreInterface_ParsePaused_Call{Call: _e.mock.On("ParsePaused", log)}
}

func (_c *CommitStoreInterface_ParsePaused_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParsePaused_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParsePaused_Call) Return(_a0 *commit_store.CommitStorePaused, _a1 error) *CommitStoreInterface_ParsePaused_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParsePaused_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStorePaused, error)) *CommitStoreInterface_ParsePaused_Call {
	_c.Call.Return(run)
	return _c
}

// ParseReportAccepted provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseReportAccepted(log types.Log) (*commit_store.CommitStoreReportAccepted, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseReportAccepted")
	}

	var r0 *commit_store.CommitStoreReportAccepted
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreReportAccepted, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreReportAccepted); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreReportAccepted)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseReportAccepted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseReportAccepted'
type CommitStoreInterface_ParseReportAccepted_Call struct {
	*mock.Call
}

// ParseReportAccepted is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseReportAccepted(log interface{}) *CommitStoreInterface_ParseReportAccepted_Call {
	return &CommitStoreInterface_ParseReportAccepted_Call{Call: _e.mock.On("ParseReportAccepted", log)}
}

func (_c *CommitStoreInterface_ParseReportAccepted_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseReportAccepted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseReportAccepted_Call) Return(_a0 *commit_store.CommitStoreReportAccepted, _a1 error) *CommitStoreInterface_ParseReportAccepted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseReportAccepted_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreReportAccepted, error)) *CommitStoreInterface_ParseReportAccepted_Call {
	_c.Call.Return(run)
	return _c
}

// ParseRootRemoved provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseRootRemoved(log types.Log) (*commit_store.CommitStoreRootRemoved, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseRootRemoved")
	}

	var r0 *commit_store.CommitStoreRootRemoved
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreRootRemoved, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreRootRemoved); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreRootRemoved)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseRootRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseRootRemoved'
type CommitStoreInterface_ParseRootRemoved_Call struct {
	*mock.Call
}

// ParseRootRemoved is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseRootRemoved(log interface{}) *CommitStoreInterface_ParseRootRemoved_Call {
	return &CommitStoreInterface_ParseRootRemoved_Call{Call: _e.mock.On("ParseRootRemoved", log)}
}

func (_c *CommitStoreInterface_ParseRootRemoved_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseRootRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseRootRemoved_Call) Return(_a0 *commit_store.CommitStoreRootRemoved, _a1 error) *CommitStoreInterface_ParseRootRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseRootRemoved_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreRootRemoved, error)) *CommitStoreInterface_ParseRootRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSequenceNumberSet provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseSequenceNumberSet(log types.Log) (*commit_store.CommitStoreSequenceNumberSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSequenceNumberSet")
	}

	var r0 *commit_store.CommitStoreSequenceNumberSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreSequenceNumberSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreSequenceNumberSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreSequenceNumberSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseSequenceNumberSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSequenceNumberSet'
type CommitStoreInterface_ParseSequenceNumberSet_Call struct {
	*mock.Call
}

// ParseSequenceNumberSet is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseSequenceNumberSet(log interface{}) *CommitStoreInterface_ParseSequenceNumberSet_Call {
	return &CommitStoreInterface_ParseSequenceNumberSet_Call{Call: _e.mock.On("ParseSequenceNumberSet", log)}
}

func (_c *CommitStoreInterface_ParseSequenceNumberSet_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseSequenceNumberSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseSequenceNumberSet_Call) Return(_a0 *commit_store.CommitStoreSequenceNumberSet, _a1 error) *CommitStoreInterface_ParseSequenceNumberSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseSequenceNumberSet_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreSequenceNumberSet, error)) *CommitStoreInterface_ParseSequenceNumberSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseTransmitted provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseTransmitted(log types.Log) (*commit_store.CommitStoreTransmitted, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseTransmitted")
	}

	var r0 *commit_store.CommitStoreTransmitted
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreTransmitted, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreTransmitted); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreTransmitted)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseTransmitted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseTransmitted'
type CommitStoreInterface_ParseTransmitted_Call struct {
	*mock.Call
}

// ParseTransmitted is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseTransmitted(log interface{}) *CommitStoreInterface_ParseTransmitted_Call {
	return &CommitStoreInterface_ParseTransmitted_Call{Call: _e.mock.On("ParseTransmitted", log)}
}

func (_c *CommitStoreInterface_ParseTransmitted_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseTransmitted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseTransmitted_Call) Return(_a0 *commit_store.CommitStoreTransmitted, _a1 error) *CommitStoreInterface_ParseTransmitted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseTransmitted_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreTransmitted, error)) *CommitStoreInterface_ParseTransmitted_Call {
	_c.Call.Return(run)
	return _c
}

// ParseUnpaused provides a mock function with given fields: log
func (_m *CommitStoreInterface) ParseUnpaused(log types.Log) (*commit_store.CommitStoreUnpaused, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseUnpaused")
	}

	var r0 *commit_store.CommitStoreUnpaused
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*commit_store.CommitStoreUnpaused, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *commit_store.CommitStoreUnpaused); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*commit_store.CommitStoreUnpaused)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ParseUnpaused_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseUnpaused'
type CommitStoreInterface_ParseUnpaused_Call struct {
	*mock.Call
}

// ParseUnpaused is a helper method to define mock.On call
//   - log types.Log
func (_e *CommitStoreInterface_Expecter) ParseUnpaused(log interface{}) *CommitStoreInterface_ParseUnpaused_Call {
	return &CommitStoreInterface_ParseUnpaused_Call{Call: _e.mock.On("ParseUnpaused", log)}
}

func (_c *CommitStoreInterface_ParseUnpaused_Call) Run(run func(log types.Log)) *CommitStoreInterface_ParseUnpaused_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *CommitStoreInterface_ParseUnpaused_Call) Return(_a0 *commit_store.CommitStoreUnpaused, _a1 error) *CommitStoreInterface_ParseUnpaused_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ParseUnpaused_Call) RunAndReturn(run func(types.Log) (*commit_store.CommitStoreUnpaused, error)) *CommitStoreInterface_ParseUnpaused_Call {
	_c.Call.Return(run)
	return _c
}

// Pause provides a mock function with given fields: opts
func (_m *CommitStoreInterface) Pause(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Pause")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_Pause_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Pause'
type CommitStoreInterface_Pause_Call struct {
	*mock.Call
}

// Pause is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *CommitStoreInterface_Expecter) Pause(opts interface{}) *CommitStoreInterface_Pause_Call {
	return &CommitStoreInterface_Pause_Call{Call: _e.mock.On("Pause", opts)}
}

func (_c *CommitStoreInterface_Pause_Call) Run(run func(opts *bind.TransactOpts)) *CommitStoreInterface_Pause_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_Pause_Call) Return(_a0 *types.Transaction, _a1 error) *CommitStoreInterface_Pause_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_Pause_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *CommitStoreInterface_Pause_Call {
	_c.Call.Return(run)
	return _c
}

// Paused provides a mock function with given fields: opts
func (_m *CommitStoreInterface) Paused(opts *bind.CallOpts) (bool, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Paused")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (bool, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) bool); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_Paused_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Paused'
type CommitStoreInterface_Paused_Call struct {
	*mock.Call
}

// Paused is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) Paused(opts interface{}) *CommitStoreInterface_Paused_Call {
	return &CommitStoreInterface_Paused_Call{Call: _e.mock.On("Paused", opts)}
}

func (_c *CommitStoreInterface_Paused_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_Paused_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_Paused_Call) Return(_a0 bool, _a1 error) *CommitStoreInterface_Paused_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_Paused_Call) RunAndReturn(run func(*bind.CallOpts) (bool, error)) *CommitStoreInterface_Paused_Call {
	_c.Call.Return(run)
	return _c
}

// ResetUnblessedRoots provides a mock function with given fields: opts, rootToReset
func (_m *CommitStoreInterface) ResetUnblessedRoots(opts *bind.TransactOpts, rootToReset [][32]byte) (*types.Transaction, error) {
	ret := _m.Called(opts, rootToReset)

	if len(ret) == 0 {
		panic("no return value specified for ResetUnblessedRoots")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [][32]byte) (*types.Transaction, error)); ok {
		return rf(opts, rootToReset)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [][32]byte) *types.Transaction); ok {
		r0 = rf(opts, rootToReset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, [][32]byte) error); ok {
		r1 = rf(opts, rootToReset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_ResetUnblessedRoots_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResetUnblessedRoots'
type CommitStoreInterface_ResetUnblessedRoots_Call struct {
	*mock.Call
}

// ResetUnblessedRoots is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - rootToReset [][32]byte
func (_e *CommitStoreInterface_Expecter) ResetUnblessedRoots(opts interface{}, rootToReset interface{}) *CommitStoreInterface_ResetUnblessedRoots_Call {
	return &CommitStoreInterface_ResetUnblessedRoots_Call{Call: _e.mock.On("ResetUnblessedRoots", opts, rootToReset)}
}

func (_c *CommitStoreInterface_ResetUnblessedRoots_Call) Run(run func(opts *bind.TransactOpts, rootToReset [][32]byte)) *CommitStoreInterface_ResetUnblessedRoots_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([][32]byte))
	})
	return _c
}

func (_c *CommitStoreInterface_ResetUnblessedRoots_Call) Return(_a0 *types.Transaction, _a1 error) *CommitStoreInterface_ResetUnblessedRoots_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_ResetUnblessedRoots_Call) RunAndReturn(run func(*bind.TransactOpts, [][32]byte) (*types.Transaction, error)) *CommitStoreInterface_ResetUnblessedRoots_Call {
	_c.Call.Return(run)
	return _c
}

// SetLatestPriceEpochAndRound provides a mock function with given fields: opts, latestPriceEpochAndRound
func (_m *CommitStoreInterface) SetLatestPriceEpochAndRound(opts *bind.TransactOpts, latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, latestPriceEpochAndRound)

	if len(ret) == 0 {
		panic("no return value specified for SetLatestPriceEpochAndRound")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, *big.Int) (*types.Transaction, error)); ok {
		return rf(opts, latestPriceEpochAndRound)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, *big.Int) *types.Transaction); ok {
		r0 = rf(opts, latestPriceEpochAndRound)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, *big.Int) error); ok {
		r1 = rf(opts, latestPriceEpochAndRound)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_SetLatestPriceEpochAndRound_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetLatestPriceEpochAndRound'
type CommitStoreInterface_SetLatestPriceEpochAndRound_Call struct {
	*mock.Call
}

// SetLatestPriceEpochAndRound is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - latestPriceEpochAndRound *big.Int
func (_e *CommitStoreInterface_Expecter) SetLatestPriceEpochAndRound(opts interface{}, latestPriceEpochAndRound interface{}) *CommitStoreInterface_SetLatestPriceEpochAndRound_Call {
	return &CommitStoreInterface_SetLatestPriceEpochAndRound_Call{Call: _e.mock.On("SetLatestPriceEpochAndRound", opts, latestPriceEpochAndRound)}
}

func (_c *CommitStoreInterface_SetLatestPriceEpochAndRound_Call) Run(run func(opts *bind.TransactOpts, latestPriceEpochAndRound *big.Int)) *CommitStoreInterface_SetLatestPriceEpochAndRound_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(*big.Int))
	})
	return _c
}

func (_c *CommitStoreInterface_SetLatestPriceEpochAndRound_Call) Return(_a0 *types.Transaction, _a1 error) *CommitStoreInterface_SetLatestPriceEpochAndRound_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_SetLatestPriceEpochAndRound_Call) RunAndReturn(run func(*bind.TransactOpts, *big.Int) (*types.Transaction, error)) *CommitStoreInterface_SetLatestPriceEpochAndRound_Call {
	_c.Call.Return(run)
	return _c
}

// SetMinSeqNr provides a mock function with given fields: opts, minSeqNr
func (_m *CommitStoreInterface) SetMinSeqNr(opts *bind.TransactOpts, minSeqNr uint64) (*types.Transaction, error) {
	ret := _m.Called(opts, minSeqNr)

	if len(ret) == 0 {
		panic("no return value specified for SetMinSeqNr")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64) (*types.Transaction, error)); ok {
		return rf(opts, minSeqNr)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64) *types.Transaction); ok {
		r0 = rf(opts, minSeqNr)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, uint64) error); ok {
		r1 = rf(opts, minSeqNr)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_SetMinSeqNr_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetMinSeqNr'
type CommitStoreInterface_SetMinSeqNr_Call struct {
	*mock.Call
}

// SetMinSeqNr is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - minSeqNr uint64
func (_e *CommitStoreInterface_Expecter) SetMinSeqNr(opts interface{}, minSeqNr interface{}) *CommitStoreInterface_SetMinSeqNr_Call {
	return &CommitStoreInterface_SetMinSeqNr_Call{Call: _e.mock.On("SetMinSeqNr", opts, minSeqNr)}
}

func (_c *CommitStoreInterface_SetMinSeqNr_Call) Run(run func(opts *bind.TransactOpts, minSeqNr uint64)) *CommitStoreInterface_SetMinSeqNr_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(uint64))
	})
	return _c
}

func (_c *CommitStoreInterface_SetMinSeqNr_Call) Return(_a0 *types.Transaction, _a1 error) *CommitStoreInterface_SetMinSeqNr_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_SetMinSeqNr_Call) RunAndReturn(run func(*bind.TransactOpts, uint64) (*types.Transaction, error)) *CommitStoreInterface_SetMinSeqNr_Call {
	_c.Call.Return(run)
	return _c
}

// SetOCR2Config provides a mock function with given fields: opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig
func (_m *CommitStoreInterface) SetOCR2Config(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)

	if len(ret) == 0 {
		panic("no return value specified for SetOCR2Config")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address, []common.Address, uint8, []byte, uint64, []byte) (*types.Transaction, error)); ok {
		return rf(opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address, []common.Address, uint8, []byte, uint64, []byte) *types.Transaction); ok {
		r0 = rf(opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []common.Address, []common.Address, uint8, []byte, uint64, []byte) error); ok {
		r1 = rf(opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_SetOCR2Config_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetOCR2Config'
type CommitStoreInterface_SetOCR2Config_Call struct {
	*mock.Call
}

// SetOCR2Config is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - signers []common.Address
//   - transmitters []common.Address
//   - f uint8
//   - onchainConfig []byte
//   - offchainConfigVersion uint64
//   - offchainConfig []byte
func (_e *CommitStoreInterface_Expecter) SetOCR2Config(opts interface{}, signers interface{}, transmitters interface{}, f interface{}, onchainConfig interface{}, offchainConfigVersion interface{}, offchainConfig interface{}) *CommitStoreInterface_SetOCR2Config_Call {
	return &CommitStoreInterface_SetOCR2Config_Call{Call: _e.mock.On("SetOCR2Config", opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)}
}

func (_c *CommitStoreInterface_SetOCR2Config_Call) Run(run func(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte)) *CommitStoreInterface_SetOCR2Config_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]common.Address), args[2].([]common.Address), args[3].(uint8), args[4].([]byte), args[5].(uint64), args[6].([]byte))
	})
	return _c
}

func (_c *CommitStoreInterface_SetOCR2Config_Call) Return(_a0 *types.Transaction, _a1 error) *CommitStoreInterface_SetOCR2Config_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_SetOCR2Config_Call) RunAndReturn(run func(*bind.TransactOpts, []common.Address, []common.Address, uint8, []byte, uint64, []byte) (*types.Transaction, error)) *CommitStoreInterface_SetOCR2Config_Call {
	_c.Call.Return(run)
	return _c
}

// TransferOwnership provides a mock function with given fields: opts, to
func (_m *CommitStoreInterface) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, to)

	if len(ret) == 0 {
		panic("no return value specified for TransferOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_TransferOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransferOwnership'
type CommitStoreInterface_TransferOwnership_Call struct {
	*mock.Call
}

// TransferOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - to common.Address
func (_e *CommitStoreInterface_Expecter) TransferOwnership(opts interface{}, to interface{}) *CommitStoreInterface_TransferOwnership_Call {
	return &CommitStoreInterface_TransferOwnership_Call{Call: _e.mock.On("TransferOwnership", opts, to)}
}

func (_c *CommitStoreInterface_TransferOwnership_Call) Run(run func(opts *bind.TransactOpts, to common.Address)) *CommitStoreInterface_TransferOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *CommitStoreInterface_TransferOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *CommitStoreInterface_TransferOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_TransferOwnership_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *CommitStoreInterface_TransferOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// Transmit provides a mock function with given fields: opts, reportContext, report, rs, ss, rawVs
func (_m *CommitStoreInterface) Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	ret := _m.Called(opts, reportContext, report, rs, ss, rawVs)

	if len(ret) == 0 {
		panic("no return value specified for Transmit")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [3][32]byte, []byte, [][32]byte, [][32]byte, [32]byte) (*types.Transaction, error)); ok {
		return rf(opts, reportContext, report, rs, ss, rawVs)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [3][32]byte, []byte, [][32]byte, [][32]byte, [32]byte) *types.Transaction); ok {
		r0 = rf(opts, reportContext, report, rs, ss, rawVs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, [3][32]byte, []byte, [][32]byte, [][32]byte, [32]byte) error); ok {
		r1 = rf(opts, reportContext, report, rs, ss, rawVs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_Transmit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transmit'
type CommitStoreInterface_Transmit_Call struct {
	*mock.Call
}

// Transmit is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - reportContext [3][32]byte
//   - report []byte
//   - rs [][32]byte
//   - ss [][32]byte
//   - rawVs [32]byte
func (_e *CommitStoreInterface_Expecter) Transmit(opts interface{}, reportContext interface{}, report interface{}, rs interface{}, ss interface{}, rawVs interface{}) *CommitStoreInterface_Transmit_Call {
	return &CommitStoreInterface_Transmit_Call{Call: _e.mock.On("Transmit", opts, reportContext, report, rs, ss, rawVs)}
}

func (_c *CommitStoreInterface_Transmit_Call) Run(run func(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte)) *CommitStoreInterface_Transmit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([3][32]byte), args[2].([]byte), args[3].([][32]byte), args[4].([][32]byte), args[5].([32]byte))
	})
	return _c
}

func (_c *CommitStoreInterface_Transmit_Call) Return(_a0 *types.Transaction, _a1 error) *CommitStoreInterface_Transmit_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_Transmit_Call) RunAndReturn(run func(*bind.TransactOpts, [3][32]byte, []byte, [][32]byte, [][32]byte, [32]byte) (*types.Transaction, error)) *CommitStoreInterface_Transmit_Call {
	_c.Call.Return(run)
	return _c
}

// TypeAndVersion provides a mock function with given fields: opts
func (_m *CommitStoreInterface) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for TypeAndVersion")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (string, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) string); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_TypeAndVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TypeAndVersion'
type CommitStoreInterface_TypeAndVersion_Call struct {
	*mock.Call
}

// TypeAndVersion is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *CommitStoreInterface_Expecter) TypeAndVersion(opts interface{}) *CommitStoreInterface_TypeAndVersion_Call {
	return &CommitStoreInterface_TypeAndVersion_Call{Call: _e.mock.On("TypeAndVersion", opts)}
}

func (_c *CommitStoreInterface_TypeAndVersion_Call) Run(run func(opts *bind.CallOpts)) *CommitStoreInterface_TypeAndVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_TypeAndVersion_Call) Return(_a0 string, _a1 error) *CommitStoreInterface_TypeAndVersion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_TypeAndVersion_Call) RunAndReturn(run func(*bind.CallOpts) (string, error)) *CommitStoreInterface_TypeAndVersion_Call {
	_c.Call.Return(run)
	return _c
}

// Unpause provides a mock function with given fields: opts
func (_m *CommitStoreInterface) Unpause(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Unpause")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_Unpause_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Unpause'
type CommitStoreInterface_Unpause_Call struct {
	*mock.Call
}

// Unpause is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *CommitStoreInterface_Expecter) Unpause(opts interface{}) *CommitStoreInterface_Unpause_Call {
	return &CommitStoreInterface_Unpause_Call{Call: _e.mock.On("Unpause", opts)}
}

func (_c *CommitStoreInterface_Unpause_Call) Run(run func(opts *bind.TransactOpts)) *CommitStoreInterface_Unpause_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *CommitStoreInterface_Unpause_Call) Return(_a0 *types.Transaction, _a1 error) *CommitStoreInterface_Unpause_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_Unpause_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *CommitStoreInterface_Unpause_Call {
	_c.Call.Return(run)
	return _c
}

// Verify provides a mock function with given fields: opts, hashedLeaves, proofs, proofFlagBits
func (_m *CommitStoreInterface) Verify(opts *bind.CallOpts, hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	ret := _m.Called(opts, hashedLeaves, proofs, proofFlagBits)

	if len(ret) == 0 {
		panic("no return value specified for Verify")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, [][32]byte, [][32]byte, *big.Int) (*big.Int, error)); ok {
		return rf(opts, hashedLeaves, proofs, proofFlagBits)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, [][32]byte, [][32]byte, *big.Int) *big.Int); ok {
		r0 = rf(opts, hashedLeaves, proofs, proofFlagBits)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, [][32]byte, [][32]byte, *big.Int) error); ok {
		r1 = rf(opts, hashedLeaves, proofs, proofFlagBits)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_Verify_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Verify'
type CommitStoreInterface_Verify_Call struct {
	*mock.Call
}

// Verify is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - hashedLeaves [][32]byte
//   - proofs [][32]byte
//   - proofFlagBits *big.Int
func (_e *CommitStoreInterface_Expecter) Verify(opts interface{}, hashedLeaves interface{}, proofs interface{}, proofFlagBits interface{}) *CommitStoreInterface_Verify_Call {
	return &CommitStoreInterface_Verify_Call{Call: _e.mock.On("Verify", opts, hashedLeaves, proofs, proofFlagBits)}
}

func (_c *CommitStoreInterface_Verify_Call) Run(run func(opts *bind.CallOpts, hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int)) *CommitStoreInterface_Verify_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].([][32]byte), args[2].([][32]byte), args[3].(*big.Int))
	})
	return _c
}

func (_c *CommitStoreInterface_Verify_Call) Return(_a0 *big.Int, _a1 error) *CommitStoreInterface_Verify_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_Verify_Call) RunAndReturn(run func(*bind.CallOpts, [][32]byte, [][32]byte, *big.Int) (*big.Int, error)) *CommitStoreInterface_Verify_Call {
	_c.Call.Return(run)
	return _c
}

// WatchConfigSet provides a mock function with given fields: opts, sink
func (_m *CommitStoreInterface) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreConfigSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchConfigSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreConfigSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreConfigSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreConfigSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchConfigSet'
type CommitStoreInterface_WatchConfigSet_Call struct {
	*mock.Call
}

// WatchConfigSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreConfigSet
func (_e *CommitStoreInterface_Expecter) WatchConfigSet(opts interface{}, sink interface{}) *CommitStoreInterface_WatchConfigSet_Call {
	return &CommitStoreInterface_WatchConfigSet_Call{Call: _e.mock.On("WatchConfigSet", opts, sink)}
}

func (_c *CommitStoreInterface_WatchConfigSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreConfigSet)) *CommitStoreInterface_WatchConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreConfigSet))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchConfigSet_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchConfigSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreConfigSet) (event.Subscription, error)) *CommitStoreInterface_WatchConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchConfigSet0 provides a mock function with given fields: opts, sink
func (_m *CommitStoreInterface) WatchConfigSet0(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreConfigSet0) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchConfigSet0")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreConfigSet0) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreConfigSet0) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreConfigSet0) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchConfigSet0_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchConfigSet0'
type CommitStoreInterface_WatchConfigSet0_Call struct {
	*mock.Call
}

// WatchConfigSet0 is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreConfigSet0
func (_e *CommitStoreInterface_Expecter) WatchConfigSet0(opts interface{}, sink interface{}) *CommitStoreInterface_WatchConfigSet0_Call {
	return &CommitStoreInterface_WatchConfigSet0_Call{Call: _e.mock.On("WatchConfigSet0", opts, sink)}
}

func (_c *CommitStoreInterface_WatchConfigSet0_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreConfigSet0)) *CommitStoreInterface_WatchConfigSet0_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreConfigSet0))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchConfigSet0_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchConfigSet0_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchConfigSet0_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreConfigSet0) (event.Subscription, error)) *CommitStoreInterface_WatchConfigSet0_Call {
	_c.Call.Return(run)
	return _c
}

// WatchLatestPriceEpochAndRoundSet provides a mock function with given fields: opts, sink
func (_m *CommitStoreInterface) WatchLatestPriceEpochAndRoundSet(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreLatestPriceEpochAndRoundSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchLatestPriceEpochAndRoundSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreLatestPriceEpochAndRoundSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreLatestPriceEpochAndRoundSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreLatestPriceEpochAndRoundSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchLatestPriceEpochAndRoundSet'
type CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call struct {
	*mock.Call
}

// WatchLatestPriceEpochAndRoundSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreLatestPriceEpochAndRoundSet
func (_e *CommitStoreInterface_Expecter) WatchLatestPriceEpochAndRoundSet(opts interface{}, sink interface{}) *CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call {
	return &CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call{Call: _e.mock.On("WatchLatestPriceEpochAndRoundSet", opts, sink)}
}

func (_c *CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreLatestPriceEpochAndRoundSet)) *CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreLatestPriceEpochAndRoundSet))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreLatestPriceEpochAndRoundSet) (event.Subscription, error)) *CommitStoreInterface_WatchLatestPriceEpochAndRoundSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferRequested provides a mock function with given fields: opts, sink, from, to
func (_m *CommitStoreInterface) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferRequested")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreOwnershipTransferRequested, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreOwnershipTransferRequested, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferRequested'
type CommitStoreInterface_WatchOwnershipTransferRequested_Call struct {
	*mock.Call
}

// WatchOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreOwnershipTransferRequested
//   - from []common.Address
//   - to []common.Address
func (_e *CommitStoreInterface_Expecter) WatchOwnershipTransferRequested(opts interface{}, sink interface{}, from interface{}, to interface{}) *CommitStoreInterface_WatchOwnershipTransferRequested_Call {
	return &CommitStoreInterface_WatchOwnershipTransferRequested_Call{Call: _e.mock.On("WatchOwnershipTransferRequested", opts, sink, from, to)}
}

func (_c *CommitStoreInterface_WatchOwnershipTransferRequested_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreOwnershipTransferRequested, from []common.Address, to []common.Address)) *CommitStoreInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreOwnershipTransferRequested), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchOwnershipTransferRequested_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)) *CommitStoreInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferred provides a mock function with given fields: opts, sink, from, to
func (_m *CommitStoreInterface) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferred")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreOwnershipTransferred, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreOwnershipTransferred, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferred'
type CommitStoreInterface_WatchOwnershipTransferred_Call struct {
	*mock.Call
}

// WatchOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreOwnershipTransferred
//   - from []common.Address
//   - to []common.Address
func (_e *CommitStoreInterface_Expecter) WatchOwnershipTransferred(opts interface{}, sink interface{}, from interface{}, to interface{}) *CommitStoreInterface_WatchOwnershipTransferred_Call {
	return &CommitStoreInterface_WatchOwnershipTransferred_Call{Call: _e.mock.On("WatchOwnershipTransferred", opts, sink, from, to)}
}

func (_c *CommitStoreInterface_WatchOwnershipTransferred_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreOwnershipTransferred, from []common.Address, to []common.Address)) *CommitStoreInterface_WatchOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreOwnershipTransferred), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchOwnershipTransferred_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchOwnershipTransferred_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)) *CommitStoreInterface_WatchOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// WatchPaused provides a mock function with given fields: opts, sink
func (_m *CommitStoreInterface) WatchPaused(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStorePaused) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchPaused")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStorePaused) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStorePaused) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStorePaused) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchPaused_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchPaused'
type CommitStoreInterface_WatchPaused_Call struct {
	*mock.Call
}

// WatchPaused is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStorePaused
func (_e *CommitStoreInterface_Expecter) WatchPaused(opts interface{}, sink interface{}) *CommitStoreInterface_WatchPaused_Call {
	return &CommitStoreInterface_WatchPaused_Call{Call: _e.mock.On("WatchPaused", opts, sink)}
}

func (_c *CommitStoreInterface_WatchPaused_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStorePaused)) *CommitStoreInterface_WatchPaused_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStorePaused))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchPaused_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchPaused_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchPaused_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStorePaused) (event.Subscription, error)) *CommitStoreInterface_WatchPaused_Call {
	_c.Call.Return(run)
	return _c
}

// WatchReportAccepted provides a mock function with given fields: opts, sink
func (_m *CommitStoreInterface) WatchReportAccepted(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreReportAccepted) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchReportAccepted")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreReportAccepted) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreReportAccepted) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreReportAccepted) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchReportAccepted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchReportAccepted'
type CommitStoreInterface_WatchReportAccepted_Call struct {
	*mock.Call
}

// WatchReportAccepted is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreReportAccepted
func (_e *CommitStoreInterface_Expecter) WatchReportAccepted(opts interface{}, sink interface{}) *CommitStoreInterface_WatchReportAccepted_Call {
	return &CommitStoreInterface_WatchReportAccepted_Call{Call: _e.mock.On("WatchReportAccepted", opts, sink)}
}

func (_c *CommitStoreInterface_WatchReportAccepted_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreReportAccepted)) *CommitStoreInterface_WatchReportAccepted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreReportAccepted))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchReportAccepted_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchReportAccepted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchReportAccepted_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreReportAccepted) (event.Subscription, error)) *CommitStoreInterface_WatchReportAccepted_Call {
	_c.Call.Return(run)
	return _c
}

// WatchRootRemoved provides a mock function with given fields: opts, sink
func (_m *CommitStoreInterface) WatchRootRemoved(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreRootRemoved) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchRootRemoved")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreRootRemoved) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreRootRemoved) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreRootRemoved) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchRootRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchRootRemoved'
type CommitStoreInterface_WatchRootRemoved_Call struct {
	*mock.Call
}

// WatchRootRemoved is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreRootRemoved
func (_e *CommitStoreInterface_Expecter) WatchRootRemoved(opts interface{}, sink interface{}) *CommitStoreInterface_WatchRootRemoved_Call {
	return &CommitStoreInterface_WatchRootRemoved_Call{Call: _e.mock.On("WatchRootRemoved", opts, sink)}
}

func (_c *CommitStoreInterface_WatchRootRemoved_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreRootRemoved)) *CommitStoreInterface_WatchRootRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreRootRemoved))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchRootRemoved_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchRootRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchRootRemoved_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreRootRemoved) (event.Subscription, error)) *CommitStoreInterface_WatchRootRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSequenceNumberSet provides a mock function with given fields: opts, sink
func (_m *CommitStoreInterface) WatchSequenceNumberSet(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreSequenceNumberSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchSequenceNumberSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreSequenceNumberSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreSequenceNumberSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreSequenceNumberSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchSequenceNumberSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSequenceNumberSet'
type CommitStoreInterface_WatchSequenceNumberSet_Call struct {
	*mock.Call
}

// WatchSequenceNumberSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreSequenceNumberSet
func (_e *CommitStoreInterface_Expecter) WatchSequenceNumberSet(opts interface{}, sink interface{}) *CommitStoreInterface_WatchSequenceNumberSet_Call {
	return &CommitStoreInterface_WatchSequenceNumberSet_Call{Call: _e.mock.On("WatchSequenceNumberSet", opts, sink)}
}

func (_c *CommitStoreInterface_WatchSequenceNumberSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreSequenceNumberSet)) *CommitStoreInterface_WatchSequenceNumberSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreSequenceNumberSet))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchSequenceNumberSet_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchSequenceNumberSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchSequenceNumberSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreSequenceNumberSet) (event.Subscription, error)) *CommitStoreInterface_WatchSequenceNumberSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchTransmitted provides a mock function with given fields: opts, sink
func (_m *CommitStoreInterface) WatchTransmitted(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreTransmitted) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchTransmitted")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreTransmitted) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreTransmitted) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreTransmitted) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchTransmitted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchTransmitted'
type CommitStoreInterface_WatchTransmitted_Call struct {
	*mock.Call
}

// WatchTransmitted is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreTransmitted
func (_e *CommitStoreInterface_Expecter) WatchTransmitted(opts interface{}, sink interface{}) *CommitStoreInterface_WatchTransmitted_Call {
	return &CommitStoreInterface_WatchTransmitted_Call{Call: _e.mock.On("WatchTransmitted", opts, sink)}
}

func (_c *CommitStoreInterface_WatchTransmitted_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreTransmitted)) *CommitStoreInterface_WatchTransmitted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreTransmitted))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchTransmitted_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchTransmitted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchTransmitted_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreTransmitted) (event.Subscription, error)) *CommitStoreInterface_WatchTransmitted_Call {
	_c.Call.Return(run)
	return _c
}

// WatchUnpaused provides a mock function with given fields: opts, sink
func (_m *CommitStoreInterface) WatchUnpaused(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreUnpaused) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchUnpaused")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreUnpaused) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreUnpaused) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *commit_store.CommitStoreUnpaused) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CommitStoreInterface_WatchUnpaused_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchUnpaused'
type CommitStoreInterface_WatchUnpaused_Call struct {
	*mock.Call
}

// WatchUnpaused is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *commit_store.CommitStoreUnpaused
func (_e *CommitStoreInterface_Expecter) WatchUnpaused(opts interface{}, sink interface{}) *CommitStoreInterface_WatchUnpaused_Call {
	return &CommitStoreInterface_WatchUnpaused_Call{Call: _e.mock.On("WatchUnpaused", opts, sink)}
}

func (_c *CommitStoreInterface_WatchUnpaused_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *commit_store.CommitStoreUnpaused)) *CommitStoreInterface_WatchUnpaused_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *commit_store.CommitStoreUnpaused))
	})
	return _c
}

func (_c *CommitStoreInterface_WatchUnpaused_Call) Return(_a0 event.Subscription, _a1 error) *CommitStoreInterface_WatchUnpaused_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *CommitStoreInterface_WatchUnpaused_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *commit_store.CommitStoreUnpaused) (event.Subscription, error)) *CommitStoreInterface_WatchUnpaused_Call {
	_c.Call.Return(run)
	return _c
}

// NewCommitStoreInterface creates a new instance of CommitStoreInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCommitStoreInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *CommitStoreInterface {
	mock := &CommitStoreInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
