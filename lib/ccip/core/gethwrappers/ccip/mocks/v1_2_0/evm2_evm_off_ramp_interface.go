// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_contracts

import (
	big "math/big"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	event "github.com/ethereum/go-ethereum/event"

	evm_2_evm_offramp_1_2_0 "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/ccip/generated/evm_2_evm_offramp_1_2_0"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// EVM2EVMOffRampInterface is an autogenerated mock type for the EVM2EVMOffRampInterface type
type EVM2EVMOffRampInterface struct {
	mock.Mock
}

type EVM2EVMOffRampInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *EVM2EVMOffRampInterface) EXPECT() *EVM2EVMOffRampInterface_Expecter {
	return &EVM2EVMOffRampInterface_Expecter{mock: &_m.Mock}
}

// AcceptOwnership provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for AcceptOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_AcceptOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptOwnership'
type EVM2EVMOffRampInterface_AcceptOwnership_Call struct {
	*mock.Call
}

// AcceptOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *EVM2EVMOffRampInterface_Expecter) AcceptOwnership(opts interface{}) *EVM2EVMOffRampInterface_AcceptOwnership_Call {
	return &EVM2EVMOffRampInterface_AcceptOwnership_Call{Call: _e.mock.On("AcceptOwnership", opts)}
}

func (_c *EVM2EVMOffRampInterface_AcceptOwnership_Call) Run(run func(opts *bind.TransactOpts)) *EVM2EVMOffRampInterface_AcceptOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_AcceptOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOffRampInterface_AcceptOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_AcceptOwnership_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *EVM2EVMOffRampInterface_AcceptOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// Address provides a mock function with given fields:
func (_m *EVM2EVMOffRampInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// EVM2EVMOffRampInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type EVM2EVMOffRampInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *EVM2EVMOffRampInterface_Expecter) Address() *EVM2EVMOffRampInterface_Address_Call {
	return &EVM2EVMOffRampInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *EVM2EVMOffRampInterface_Address_Call) Run(run func()) *EVM2EVMOffRampInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_Address_Call) Return(_a0 common.Address) *EVM2EVMOffRampInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EVM2EVMOffRampInterface_Address_Call) RunAndReturn(run func() common.Address) *EVM2EVMOffRampInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// ApplyPoolUpdates provides a mock function with given fields: opts, removes, adds
func (_m *EVM2EVMOffRampInterface) ApplyPoolUpdates(opts *bind.TransactOpts, removes []evm_2_evm_offramp_1_2_0.InternalPoolUpdate, adds []evm_2_evm_offramp_1_2_0.InternalPoolUpdate) (*types.Transaction, error) {
	ret := _m.Called(opts, removes, adds)

	if len(ret) == 0 {
		panic("no return value specified for ApplyPoolUpdates")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []evm_2_evm_offramp_1_2_0.InternalPoolUpdate, []evm_2_evm_offramp_1_2_0.InternalPoolUpdate) (*types.Transaction, error)); ok {
		return rf(opts, removes, adds)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []evm_2_evm_offramp_1_2_0.InternalPoolUpdate, []evm_2_evm_offramp_1_2_0.InternalPoolUpdate) *types.Transaction); ok {
		r0 = rf(opts, removes, adds)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []evm_2_evm_offramp_1_2_0.InternalPoolUpdate, []evm_2_evm_offramp_1_2_0.InternalPoolUpdate) error); ok {
		r1 = rf(opts, removes, adds)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ApplyPoolUpdates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ApplyPoolUpdates'
type EVM2EVMOffRampInterface_ApplyPoolUpdates_Call struct {
	*mock.Call
}

// ApplyPoolUpdates is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - removes []evm_2_evm_offramp_1_2_0.InternalPoolUpdate
//   - adds []evm_2_evm_offramp_1_2_0.InternalPoolUpdate
func (_e *EVM2EVMOffRampInterface_Expecter) ApplyPoolUpdates(opts interface{}, removes interface{}, adds interface{}) *EVM2EVMOffRampInterface_ApplyPoolUpdates_Call {
	return &EVM2EVMOffRampInterface_ApplyPoolUpdates_Call{Call: _e.mock.On("ApplyPoolUpdates", opts, removes, adds)}
}

func (_c *EVM2EVMOffRampInterface_ApplyPoolUpdates_Call) Run(run func(opts *bind.TransactOpts, removes []evm_2_evm_offramp_1_2_0.InternalPoolUpdate, adds []evm_2_evm_offramp_1_2_0.InternalPoolUpdate)) *EVM2EVMOffRampInterface_ApplyPoolUpdates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]evm_2_evm_offramp_1_2_0.InternalPoolUpdate), args[2].([]evm_2_evm_offramp_1_2_0.InternalPoolUpdate))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ApplyPoolUpdates_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOffRampInterface_ApplyPoolUpdates_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ApplyPoolUpdates_Call) RunAndReturn(run func(*bind.TransactOpts, []evm_2_evm_offramp_1_2_0.InternalPoolUpdate, []evm_2_evm_offramp_1_2_0.InternalPoolUpdate) (*types.Transaction, error)) *EVM2EVMOffRampInterface_ApplyPoolUpdates_Call {
	_c.Call.Return(run)
	return _c
}

// CcipReceive provides a mock function with given fields: opts, arg0
func (_m *EVM2EVMOffRampInterface) CcipReceive(opts *bind.CallOpts, arg0 evm_2_evm_offramp_1_2_0.ClientAny2EVMMessage) error {
	ret := _m.Called(opts, arg0)

	if len(ret) == 0 {
		panic("no return value specified for CcipReceive")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, evm_2_evm_offramp_1_2_0.ClientAny2EVMMessage) error); ok {
		r0 = rf(opts, arg0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// EVM2EVMOffRampInterface_CcipReceive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CcipReceive'
type EVM2EVMOffRampInterface_CcipReceive_Call struct {
	*mock.Call
}

// CcipReceive is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - arg0 evm_2_evm_offramp_1_2_0.ClientAny2EVMMessage
func (_e *EVM2EVMOffRampInterface_Expecter) CcipReceive(opts interface{}, arg0 interface{}) *EVM2EVMOffRampInterface_CcipReceive_Call {
	return &EVM2EVMOffRampInterface_CcipReceive_Call{Call: _e.mock.On("CcipReceive", opts, arg0)}
}

func (_c *EVM2EVMOffRampInterface_CcipReceive_Call) Run(run func(opts *bind.CallOpts, arg0 evm_2_evm_offramp_1_2_0.ClientAny2EVMMessage)) *EVM2EVMOffRampInterface_CcipReceive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(evm_2_evm_offramp_1_2_0.ClientAny2EVMMessage))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_CcipReceive_Call) Return(_a0 error) *EVM2EVMOffRampInterface_CcipReceive_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EVM2EVMOffRampInterface_CcipReceive_Call) RunAndReturn(run func(*bind.CallOpts, evm_2_evm_offramp_1_2_0.ClientAny2EVMMessage) error) *EVM2EVMOffRampInterface_CcipReceive_Call {
	_c.Call.Return(run)
	return _c
}

// CurrentRateLimiterState provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) CurrentRateLimiterState(opts *bind.CallOpts) (evm_2_evm_offramp_1_2_0.RateLimiterTokenBucket, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for CurrentRateLimiterState")
	}

	var r0 evm_2_evm_offramp_1_2_0.RateLimiterTokenBucket
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.RateLimiterTokenBucket, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) evm_2_evm_offramp_1_2_0.RateLimiterTokenBucket); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(evm_2_evm_offramp_1_2_0.RateLimiterTokenBucket)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_CurrentRateLimiterState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CurrentRateLimiterState'
type EVM2EVMOffRampInterface_CurrentRateLimiterState_Call struct {
	*mock.Call
}

// CurrentRateLimiterState is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) CurrentRateLimiterState(opts interface{}) *EVM2EVMOffRampInterface_CurrentRateLimiterState_Call {
	return &EVM2EVMOffRampInterface_CurrentRateLimiterState_Call{Call: _e.mock.On("CurrentRateLimiterState", opts)}
}

func (_c *EVM2EVMOffRampInterface_CurrentRateLimiterState_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_CurrentRateLimiterState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_CurrentRateLimiterState_Call) Return(_a0 evm_2_evm_offramp_1_2_0.RateLimiterTokenBucket, _a1 error) *EVM2EVMOffRampInterface_CurrentRateLimiterState_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_CurrentRateLimiterState_Call) RunAndReturn(run func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.RateLimiterTokenBucket, error)) *EVM2EVMOffRampInterface_CurrentRateLimiterState_Call {
	_c.Call.Return(run)
	return _c
}

// ExecuteSingleMessage provides a mock function with given fields: opts, message, offchainTokenData
func (_m *EVM2EVMOffRampInterface) ExecuteSingleMessage(opts *bind.TransactOpts, message evm_2_evm_offramp_1_2_0.InternalEVM2EVMMessage, offchainTokenData [][]byte) (*types.Transaction, error) {
	ret := _m.Called(opts, message, offchainTokenData)

	if len(ret) == 0 {
		panic("no return value specified for ExecuteSingleMessage")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.InternalEVM2EVMMessage, [][]byte) (*types.Transaction, error)); ok {
		return rf(opts, message, offchainTokenData)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.InternalEVM2EVMMessage, [][]byte) *types.Transaction); ok {
		r0 = rf(opts, message, offchainTokenData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.InternalEVM2EVMMessage, [][]byte) error); ok {
		r1 = rf(opts, message, offchainTokenData)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ExecuteSingleMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExecuteSingleMessage'
type EVM2EVMOffRampInterface_ExecuteSingleMessage_Call struct {
	*mock.Call
}

// ExecuteSingleMessage is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - message evm_2_evm_offramp_1_2_0.InternalEVM2EVMMessage
//   - offchainTokenData [][]byte
func (_e *EVM2EVMOffRampInterface_Expecter) ExecuteSingleMessage(opts interface{}, message interface{}, offchainTokenData interface{}) *EVM2EVMOffRampInterface_ExecuteSingleMessage_Call {
	return &EVM2EVMOffRampInterface_ExecuteSingleMessage_Call{Call: _e.mock.On("ExecuteSingleMessage", opts, message, offchainTokenData)}
}

func (_c *EVM2EVMOffRampInterface_ExecuteSingleMessage_Call) Run(run func(opts *bind.TransactOpts, message evm_2_evm_offramp_1_2_0.InternalEVM2EVMMessage, offchainTokenData [][]byte)) *EVM2EVMOffRampInterface_ExecuteSingleMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(evm_2_evm_offramp_1_2_0.InternalEVM2EVMMessage), args[2].([][]byte))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ExecuteSingleMessage_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOffRampInterface_ExecuteSingleMessage_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ExecuteSingleMessage_Call) RunAndReturn(run func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.InternalEVM2EVMMessage, [][]byte) (*types.Transaction, error)) *EVM2EVMOffRampInterface_ExecuteSingleMessage_Call {
	_c.Call.Return(run)
	return _c
}

// FilterAdminSet provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) FilterAdminSet(opts *bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterAdminSet")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterAdminSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterAdminSet'
type EVM2EVMOffRampInterface_FilterAdminSet_Call struct {
	*mock.Call
}

// FilterAdminSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOffRampInterface_Expecter) FilterAdminSet(opts interface{}) *EVM2EVMOffRampInterface_FilterAdminSet_Call {
	return &EVM2EVMOffRampInterface_FilterAdminSet_Call{Call: _e.mock.On("FilterAdminSet", opts)}
}

func (_c *EVM2EVMOffRampInterface_FilterAdminSet_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOffRampInterface_FilterAdminSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterAdminSet_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSetIterator, _a1 error) *EVM2EVMOffRampInterface_FilterAdminSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterAdminSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSetIterator, error)) *EVM2EVMOffRampInterface_FilterAdminSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterConfigSet provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) FilterConfigSet(opts *bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterConfigSet")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterConfigSet'
type EVM2EVMOffRampInterface_FilterConfigSet_Call struct {
	*mock.Call
}

// FilterConfigSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOffRampInterface_Expecter) FilterConfigSet(opts interface{}) *EVM2EVMOffRampInterface_FilterConfigSet_Call {
	return &EVM2EVMOffRampInterface_FilterConfigSet_Call{Call: _e.mock.On("FilterConfigSet", opts)}
}

func (_c *EVM2EVMOffRampInterface_FilterConfigSet_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOffRampInterface_FilterConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterConfigSet_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSetIterator, _a1 error) *EVM2EVMOffRampInterface_FilterConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterConfigSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSetIterator, error)) *EVM2EVMOffRampInterface_FilterConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterConfigSet0 provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) FilterConfigSet0(opts *bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0Iterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterConfigSet0")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0Iterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0Iterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0Iterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0Iterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterConfigSet0_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterConfigSet0'
type EVM2EVMOffRampInterface_FilterConfigSet0_Call struct {
	*mock.Call
}

// FilterConfigSet0 is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOffRampInterface_Expecter) FilterConfigSet0(opts interface{}) *EVM2EVMOffRampInterface_FilterConfigSet0_Call {
	return &EVM2EVMOffRampInterface_FilterConfigSet0_Call{Call: _e.mock.On("FilterConfigSet0", opts)}
}

func (_c *EVM2EVMOffRampInterface_FilterConfigSet0_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOffRampInterface_FilterConfigSet0_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterConfigSet0_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0Iterator, _a1 error) *EVM2EVMOffRampInterface_FilterConfigSet0_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterConfigSet0_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0Iterator, error)) *EVM2EVMOffRampInterface_FilterConfigSet0_Call {
	_c.Call.Return(run)
	return _c
}

// FilterExecutionStateChanged provides a mock function with given fields: opts, sequenceNumber, messageId
func (_m *EVM2EVMOffRampInterface) FilterExecutionStateChanged(opts *bind.FilterOpts, sequenceNumber []uint64, messageId [][32]byte) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChangedIterator, error) {
	ret := _m.Called(opts, sequenceNumber, messageId)

	if len(ret) == 0 {
		panic("no return value specified for FilterExecutionStateChanged")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChangedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, [][32]byte) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChangedIterator, error)); ok {
		return rf(opts, sequenceNumber, messageId)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, [][32]byte) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChangedIterator); ok {
		r0 = rf(opts, sequenceNumber, messageId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChangedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64, [][32]byte) error); ok {
		r1 = rf(opts, sequenceNumber, messageId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterExecutionStateChanged'
type EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call struct {
	*mock.Call
}

// FilterExecutionStateChanged is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - sequenceNumber []uint64
//   - messageId [][32]byte
func (_e *EVM2EVMOffRampInterface_Expecter) FilterExecutionStateChanged(opts interface{}, sequenceNumber interface{}, messageId interface{}) *EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call {
	return &EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call{Call: _e.mock.On("FilterExecutionStateChanged", opts, sequenceNumber, messageId)}
}

func (_c *EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call) Run(run func(opts *bind.FilterOpts, sequenceNumber []uint64, messageId [][32]byte)) *EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64), args[2].([][32]byte))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChangedIterator, _a1 error) *EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64, [][32]byte) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChangedIterator, error)) *EVM2EVMOffRampInterface_FilterExecutionStateChanged_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferRequested provides a mock function with given fields: opts, from, to
func (_m *EVM2EVMOffRampInterface) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequestedIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferRequested")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequestedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequestedIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequestedIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequestedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferRequested'
type EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call struct {
	*mock.Call
}

// FilterOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) FilterOwnershipTransferRequested(opts interface{}, from interface{}, to interface{}) *EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call {
	return &EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call{Call: _e.mock.On("FilterOwnershipTransferRequested", opts, from, to)}
}

func (_c *EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequestedIterator, _a1 error) *EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequestedIterator, error)) *EVM2EVMOffRampInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferred provides a mock function with given fields: opts, from, to
func (_m *EVM2EVMOffRampInterface) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferredIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferred")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferredIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferredIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferred'
type EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call struct {
	*mock.Call
}

// FilterOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) FilterOwnershipTransferred(opts interface{}, from interface{}, to interface{}) *EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call {
	return &EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call{Call: _e.mock.On("FilterOwnershipTransferred", opts, from, to)}
}

func (_c *EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferredIterator, _a1 error) *EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferredIterator, error)) *EVM2EVMOffRampInterface_FilterOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// FilterPoolAdded provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) FilterPoolAdded(opts *bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAddedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterPoolAdded")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAddedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAddedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAddedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAddedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterPoolAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterPoolAdded'
type EVM2EVMOffRampInterface_FilterPoolAdded_Call struct {
	*mock.Call
}

// FilterPoolAdded is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOffRampInterface_Expecter) FilterPoolAdded(opts interface{}) *EVM2EVMOffRampInterface_FilterPoolAdded_Call {
	return &EVM2EVMOffRampInterface_FilterPoolAdded_Call{Call: _e.mock.On("FilterPoolAdded", opts)}
}

func (_c *EVM2EVMOffRampInterface_FilterPoolAdded_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOffRampInterface_FilterPoolAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterPoolAdded_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAddedIterator, _a1 error) *EVM2EVMOffRampInterface_FilterPoolAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterPoolAdded_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAddedIterator, error)) *EVM2EVMOffRampInterface_FilterPoolAdded_Call {
	_c.Call.Return(run)
	return _c
}

// FilterPoolRemoved provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) FilterPoolRemoved(opts *bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemovedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterPoolRemoved")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemovedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemovedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemovedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemovedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterPoolRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterPoolRemoved'
type EVM2EVMOffRampInterface_FilterPoolRemoved_Call struct {
	*mock.Call
}

// FilterPoolRemoved is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOffRampInterface_Expecter) FilterPoolRemoved(opts interface{}) *EVM2EVMOffRampInterface_FilterPoolRemoved_Call {
	return &EVM2EVMOffRampInterface_FilterPoolRemoved_Call{Call: _e.mock.On("FilterPoolRemoved", opts)}
}

func (_c *EVM2EVMOffRampInterface_FilterPoolRemoved_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOffRampInterface_FilterPoolRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterPoolRemoved_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemovedIterator, _a1 error) *EVM2EVMOffRampInterface_FilterPoolRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterPoolRemoved_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemovedIterator, error)) *EVM2EVMOffRampInterface_FilterPoolRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSkippedIncorrectNonce provides a mock function with given fields: opts, nonce, sender
func (_m *EVM2EVMOffRampInterface) FilterSkippedIncorrectNonce(opts *bind.FilterOpts, nonce []uint64, sender []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonceIterator, error) {
	ret := _m.Called(opts, nonce, sender)

	if len(ret) == 0 {
		panic("no return value specified for FilterSkippedIncorrectNonce")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonceIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonceIterator, error)); ok {
		return rf(opts, nonce, sender)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, []common.Address) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonceIterator); ok {
		r0 = rf(opts, nonce, sender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonceIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64, []common.Address) error); ok {
		r1 = rf(opts, nonce, sender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSkippedIncorrectNonce'
type EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call struct {
	*mock.Call
}

// FilterSkippedIncorrectNonce is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - nonce []uint64
//   - sender []common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) FilterSkippedIncorrectNonce(opts interface{}, nonce interface{}, sender interface{}) *EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call {
	return &EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call{Call: _e.mock.On("FilterSkippedIncorrectNonce", opts, nonce, sender)}
}

func (_c *EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call) Run(run func(opts *bind.FilterOpts, nonce []uint64, sender []common.Address)) *EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64), args[2].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonceIterator, _a1 error) *EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64, []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonceIterator, error)) *EVM2EVMOffRampInterface_FilterSkippedIncorrectNonce_Call {
	_c.Call.Return(run)
	return _c
}

// FilterSkippedSenderWithPreviousRampMessageInflight provides a mock function with given fields: opts, nonce, sender
func (_m *EVM2EVMOffRampInterface) FilterSkippedSenderWithPreviousRampMessageInflight(opts *bind.FilterOpts, nonce []uint64, sender []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflightIterator, error) {
	ret := _m.Called(opts, nonce, sender)

	if len(ret) == 0 {
		panic("no return value specified for FilterSkippedSenderWithPreviousRampMessageInflight")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflightIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflightIterator, error)); ok {
		return rf(opts, nonce, sender)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, []common.Address) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflightIterator); ok {
		r0 = rf(opts, nonce, sender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflightIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64, []common.Address) error); ok {
		r1 = rf(opts, nonce, sender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterSkippedSenderWithPreviousRampMessageInflight'
type EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call struct {
	*mock.Call
}

// FilterSkippedSenderWithPreviousRampMessageInflight is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - nonce []uint64
//   - sender []common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) FilterSkippedSenderWithPreviousRampMessageInflight(opts interface{}, nonce interface{}, sender interface{}) *EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call {
	return &EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call{Call: _e.mock.On("FilterSkippedSenderWithPreviousRampMessageInflight", opts, nonce, sender)}
}

func (_c *EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call) Run(run func(opts *bind.FilterOpts, nonce []uint64, sender []common.Address)) *EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64), args[2].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflightIterator, _a1 error) *EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64, []common.Address) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflightIterator, error)) *EVM2EVMOffRampInterface_FilterSkippedSenderWithPreviousRampMessageInflight_Call {
	_c.Call.Return(run)
	return _c
}

// FilterTransmitted provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) FilterTransmitted(opts *bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmittedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterTransmitted")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmittedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmittedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmittedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmittedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_FilterTransmitted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterTransmitted'
type EVM2EVMOffRampInterface_FilterTransmitted_Call struct {
	*mock.Call
}

// FilterTransmitted is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOffRampInterface_Expecter) FilterTransmitted(opts interface{}) *EVM2EVMOffRampInterface_FilterTransmitted_Call {
	return &EVM2EVMOffRampInterface_FilterTransmitted_Call{Call: _e.mock.On("FilterTransmitted", opts)}
}

func (_c *EVM2EVMOffRampInterface_FilterTransmitted_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOffRampInterface_FilterTransmitted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterTransmitted_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmittedIterator, _a1 error) *EVM2EVMOffRampInterface_FilterTransmitted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_FilterTransmitted_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmittedIterator, error)) *EVM2EVMOffRampInterface_FilterTransmitted_Call {
	_c.Call.Return(run)
	return _c
}

// GetDestinationToken provides a mock function with given fields: opts, sourceToken
func (_m *EVM2EVMOffRampInterface) GetDestinationToken(opts *bind.CallOpts, sourceToken common.Address) (common.Address, error) {
	ret := _m.Called(opts, sourceToken)

	if len(ret) == 0 {
		panic("no return value specified for GetDestinationToken")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (common.Address, error)); ok {
		return rf(opts, sourceToken)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) common.Address); ok {
		r0 = rf(opts, sourceToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, sourceToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetDestinationToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDestinationToken'
type EVM2EVMOffRampInterface_GetDestinationToken_Call struct {
	*mock.Call
}

// GetDestinationToken is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - sourceToken common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) GetDestinationToken(opts interface{}, sourceToken interface{}) *EVM2EVMOffRampInterface_GetDestinationToken_Call {
	return &EVM2EVMOffRampInterface_GetDestinationToken_Call{Call: _e.mock.On("GetDestinationToken", opts, sourceToken)}
}

func (_c *EVM2EVMOffRampInterface_GetDestinationToken_Call) Run(run func(opts *bind.CallOpts, sourceToken common.Address)) *EVM2EVMOffRampInterface_GetDestinationToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetDestinationToken_Call) Return(_a0 common.Address, _a1 error) *EVM2EVMOffRampInterface_GetDestinationToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetDestinationToken_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (common.Address, error)) *EVM2EVMOffRampInterface_GetDestinationToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetDestinationTokens provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) GetDestinationTokens(opts *bind.CallOpts) ([]common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetDestinationTokens")
	}

	var r0 []common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) ([]common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) []common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetDestinationTokens_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDestinationTokens'
type EVM2EVMOffRampInterface_GetDestinationTokens_Call struct {
	*mock.Call
}

// GetDestinationTokens is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) GetDestinationTokens(opts interface{}) *EVM2EVMOffRampInterface_GetDestinationTokens_Call {
	return &EVM2EVMOffRampInterface_GetDestinationTokens_Call{Call: _e.mock.On("GetDestinationTokens", opts)}
}

func (_c *EVM2EVMOffRampInterface_GetDestinationTokens_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_GetDestinationTokens_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetDestinationTokens_Call) Return(_a0 []common.Address, _a1 error) *EVM2EVMOffRampInterface_GetDestinationTokens_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetDestinationTokens_Call) RunAndReturn(run func(*bind.CallOpts) ([]common.Address, error)) *EVM2EVMOffRampInterface_GetDestinationTokens_Call {
	_c.Call.Return(run)
	return _c
}

// GetDynamicConfig provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) GetDynamicConfig(opts *bind.CallOpts) (evm_2_evm_offramp_1_2_0.EVM2EVMOffRampDynamicConfig, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetDynamicConfig")
	}

	var r0 evm_2_evm_offramp_1_2_0.EVM2EVMOffRampDynamicConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.EVM2EVMOffRampDynamicConfig, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) evm_2_evm_offramp_1_2_0.EVM2EVMOffRampDynamicConfig); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(evm_2_evm_offramp_1_2_0.EVM2EVMOffRampDynamicConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetDynamicConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDynamicConfig'
type EVM2EVMOffRampInterface_GetDynamicConfig_Call struct {
	*mock.Call
}

// GetDynamicConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) GetDynamicConfig(opts interface{}) *EVM2EVMOffRampInterface_GetDynamicConfig_Call {
	return &EVM2EVMOffRampInterface_GetDynamicConfig_Call{Call: _e.mock.On("GetDynamicConfig", opts)}
}

func (_c *EVM2EVMOffRampInterface_GetDynamicConfig_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_GetDynamicConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetDynamicConfig_Call) Return(_a0 evm_2_evm_offramp_1_2_0.EVM2EVMOffRampDynamicConfig, _a1 error) *EVM2EVMOffRampInterface_GetDynamicConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetDynamicConfig_Call) RunAndReturn(run func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.EVM2EVMOffRampDynamicConfig, error)) *EVM2EVMOffRampInterface_GetDynamicConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetExecutionState provides a mock function with given fields: opts, sequenceNumber
func (_m *EVM2EVMOffRampInterface) GetExecutionState(opts *bind.CallOpts, sequenceNumber uint64) (uint8, error) {
	ret := _m.Called(opts, sequenceNumber)

	if len(ret) == 0 {
		panic("no return value specified for GetExecutionState")
	}

	var r0 uint8
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) (uint8, error)); ok {
		return rf(opts, sequenceNumber)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) uint8); ok {
		r0 = rf(opts, sequenceNumber)
	} else {
		r0 = ret.Get(0).(uint8)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, sequenceNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetExecutionState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExecutionState'
type EVM2EVMOffRampInterface_GetExecutionState_Call struct {
	*mock.Call
}

// GetExecutionState is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - sequenceNumber uint64
func (_e *EVM2EVMOffRampInterface_Expecter) GetExecutionState(opts interface{}, sequenceNumber interface{}) *EVM2EVMOffRampInterface_GetExecutionState_Call {
	return &EVM2EVMOffRampInterface_GetExecutionState_Call{Call: _e.mock.On("GetExecutionState", opts, sequenceNumber)}
}

func (_c *EVM2EVMOffRampInterface_GetExecutionState_Call) Run(run func(opts *bind.CallOpts, sequenceNumber uint64)) *EVM2EVMOffRampInterface_GetExecutionState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetExecutionState_Call) Return(_a0 uint8, _a1 error) *EVM2EVMOffRampInterface_GetExecutionState_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetExecutionState_Call) RunAndReturn(run func(*bind.CallOpts, uint64) (uint8, error)) *EVM2EVMOffRampInterface_GetExecutionState_Call {
	_c.Call.Return(run)
	return _c
}

// GetPoolByDestToken provides a mock function with given fields: opts, destToken
func (_m *EVM2EVMOffRampInterface) GetPoolByDestToken(opts *bind.CallOpts, destToken common.Address) (common.Address, error) {
	ret := _m.Called(opts, destToken)

	if len(ret) == 0 {
		panic("no return value specified for GetPoolByDestToken")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (common.Address, error)); ok {
		return rf(opts, destToken)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) common.Address); ok {
		r0 = rf(opts, destToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, destToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetPoolByDestToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPoolByDestToken'
type EVM2EVMOffRampInterface_GetPoolByDestToken_Call struct {
	*mock.Call
}

// GetPoolByDestToken is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - destToken common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) GetPoolByDestToken(opts interface{}, destToken interface{}) *EVM2EVMOffRampInterface_GetPoolByDestToken_Call {
	return &EVM2EVMOffRampInterface_GetPoolByDestToken_Call{Call: _e.mock.On("GetPoolByDestToken", opts, destToken)}
}

func (_c *EVM2EVMOffRampInterface_GetPoolByDestToken_Call) Run(run func(opts *bind.CallOpts, destToken common.Address)) *EVM2EVMOffRampInterface_GetPoolByDestToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetPoolByDestToken_Call) Return(_a0 common.Address, _a1 error) *EVM2EVMOffRampInterface_GetPoolByDestToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetPoolByDestToken_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (common.Address, error)) *EVM2EVMOffRampInterface_GetPoolByDestToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetPoolBySourceToken provides a mock function with given fields: opts, sourceToken
func (_m *EVM2EVMOffRampInterface) GetPoolBySourceToken(opts *bind.CallOpts, sourceToken common.Address) (common.Address, error) {
	ret := _m.Called(opts, sourceToken)

	if len(ret) == 0 {
		panic("no return value specified for GetPoolBySourceToken")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (common.Address, error)); ok {
		return rf(opts, sourceToken)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) common.Address); ok {
		r0 = rf(opts, sourceToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, sourceToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetPoolBySourceToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPoolBySourceToken'
type EVM2EVMOffRampInterface_GetPoolBySourceToken_Call struct {
	*mock.Call
}

// GetPoolBySourceToken is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - sourceToken common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) GetPoolBySourceToken(opts interface{}, sourceToken interface{}) *EVM2EVMOffRampInterface_GetPoolBySourceToken_Call {
	return &EVM2EVMOffRampInterface_GetPoolBySourceToken_Call{Call: _e.mock.On("GetPoolBySourceToken", opts, sourceToken)}
}

func (_c *EVM2EVMOffRampInterface_GetPoolBySourceToken_Call) Run(run func(opts *bind.CallOpts, sourceToken common.Address)) *EVM2EVMOffRampInterface_GetPoolBySourceToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetPoolBySourceToken_Call) Return(_a0 common.Address, _a1 error) *EVM2EVMOffRampInterface_GetPoolBySourceToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetPoolBySourceToken_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (common.Address, error)) *EVM2EVMOffRampInterface_GetPoolBySourceToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetSenderNonce provides a mock function with given fields: opts, sender
func (_m *EVM2EVMOffRampInterface) GetSenderNonce(opts *bind.CallOpts, sender common.Address) (uint64, error) {
	ret := _m.Called(opts, sender)

	if len(ret) == 0 {
		panic("no return value specified for GetSenderNonce")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (uint64, error)); ok {
		return rf(opts, sender)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) uint64); ok {
		r0 = rf(opts, sender)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, sender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetSenderNonce_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSenderNonce'
type EVM2EVMOffRampInterface_GetSenderNonce_Call struct {
	*mock.Call
}

// GetSenderNonce is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - sender common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) GetSenderNonce(opts interface{}, sender interface{}) *EVM2EVMOffRampInterface_GetSenderNonce_Call {
	return &EVM2EVMOffRampInterface_GetSenderNonce_Call{Call: _e.mock.On("GetSenderNonce", opts, sender)}
}

func (_c *EVM2EVMOffRampInterface_GetSenderNonce_Call) Run(run func(opts *bind.CallOpts, sender common.Address)) *EVM2EVMOffRampInterface_GetSenderNonce_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetSenderNonce_Call) Return(_a0 uint64, _a1 error) *EVM2EVMOffRampInterface_GetSenderNonce_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetSenderNonce_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (uint64, error)) *EVM2EVMOffRampInterface_GetSenderNonce_Call {
	_c.Call.Return(run)
	return _c
}

// GetStaticConfig provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) GetStaticConfig(opts *bind.CallOpts) (evm_2_evm_offramp_1_2_0.EVM2EVMOffRampStaticConfig, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetStaticConfig")
	}

	var r0 evm_2_evm_offramp_1_2_0.EVM2EVMOffRampStaticConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.EVM2EVMOffRampStaticConfig, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) evm_2_evm_offramp_1_2_0.EVM2EVMOffRampStaticConfig); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(evm_2_evm_offramp_1_2_0.EVM2EVMOffRampStaticConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetStaticConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStaticConfig'
type EVM2EVMOffRampInterface_GetStaticConfig_Call struct {
	*mock.Call
}

// GetStaticConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) GetStaticConfig(opts interface{}) *EVM2EVMOffRampInterface_GetStaticConfig_Call {
	return &EVM2EVMOffRampInterface_GetStaticConfig_Call{Call: _e.mock.On("GetStaticConfig", opts)}
}

func (_c *EVM2EVMOffRampInterface_GetStaticConfig_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_GetStaticConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetStaticConfig_Call) Return(_a0 evm_2_evm_offramp_1_2_0.EVM2EVMOffRampStaticConfig, _a1 error) *EVM2EVMOffRampInterface_GetStaticConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetStaticConfig_Call) RunAndReturn(run func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.EVM2EVMOffRampStaticConfig, error)) *EVM2EVMOffRampInterface_GetStaticConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetSupportedTokens provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) GetSupportedTokens(opts *bind.CallOpts) ([]common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetSupportedTokens")
	}

	var r0 []common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) ([]common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) []common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetSupportedTokens_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSupportedTokens'
type EVM2EVMOffRampInterface_GetSupportedTokens_Call struct {
	*mock.Call
}

// GetSupportedTokens is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) GetSupportedTokens(opts interface{}) *EVM2EVMOffRampInterface_GetSupportedTokens_Call {
	return &EVM2EVMOffRampInterface_GetSupportedTokens_Call{Call: _e.mock.On("GetSupportedTokens", opts)}
}

func (_c *EVM2EVMOffRampInterface_GetSupportedTokens_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_GetSupportedTokens_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetSupportedTokens_Call) Return(_a0 []common.Address, _a1 error) *EVM2EVMOffRampInterface_GetSupportedTokens_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetSupportedTokens_Call) RunAndReturn(run func(*bind.CallOpts) ([]common.Address, error)) *EVM2EVMOffRampInterface_GetSupportedTokens_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenLimitAdmin provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) GetTokenLimitAdmin(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenLimitAdmin")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenLimitAdmin'
type EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call struct {
	*mock.Call
}

// GetTokenLimitAdmin is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) GetTokenLimitAdmin(opts interface{}) *EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call {
	return &EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call{Call: _e.mock.On("GetTokenLimitAdmin", opts)}
}

func (_c *EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call) Return(_a0 common.Address, _a1 error) *EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *EVM2EVMOffRampInterface_GetTokenLimitAdmin_Call {
	_c.Call.Return(run)
	return _c
}

// GetTransmitters provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) GetTransmitters(opts *bind.CallOpts) ([]common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetTransmitters")
	}

	var r0 []common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) ([]common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) []common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_GetTransmitters_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTransmitters'
type EVM2EVMOffRampInterface_GetTransmitters_Call struct {
	*mock.Call
}

// GetTransmitters is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) GetTransmitters(opts interface{}) *EVM2EVMOffRampInterface_GetTransmitters_Call {
	return &EVM2EVMOffRampInterface_GetTransmitters_Call{Call: _e.mock.On("GetTransmitters", opts)}
}

func (_c *EVM2EVMOffRampInterface_GetTransmitters_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_GetTransmitters_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetTransmitters_Call) Return(_a0 []common.Address, _a1 error) *EVM2EVMOffRampInterface_GetTransmitters_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_GetTransmitters_Call) RunAndReturn(run func(*bind.CallOpts) ([]common.Address, error)) *EVM2EVMOffRampInterface_GetTransmitters_Call {
	_c.Call.Return(run)
	return _c
}

// LatestConfigDetails provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) LatestConfigDetails(opts *bind.CallOpts) (evm_2_evm_offramp_1_2_0.LatestConfigDetails, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LatestConfigDetails")
	}

	var r0 evm_2_evm_offramp_1_2_0.LatestConfigDetails
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.LatestConfigDetails, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) evm_2_evm_offramp_1_2_0.LatestConfigDetails); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(evm_2_evm_offramp_1_2_0.LatestConfigDetails)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_LatestConfigDetails_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestConfigDetails'
type EVM2EVMOffRampInterface_LatestConfigDetails_Call struct {
	*mock.Call
}

// LatestConfigDetails is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) LatestConfigDetails(opts interface{}) *EVM2EVMOffRampInterface_LatestConfigDetails_Call {
	return &EVM2EVMOffRampInterface_LatestConfigDetails_Call{Call: _e.mock.On("LatestConfigDetails", opts)}
}

func (_c *EVM2EVMOffRampInterface_LatestConfigDetails_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_LatestConfigDetails_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_LatestConfigDetails_Call) Return(_a0 evm_2_evm_offramp_1_2_0.LatestConfigDetails, _a1 error) *EVM2EVMOffRampInterface_LatestConfigDetails_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_LatestConfigDetails_Call) RunAndReturn(run func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.LatestConfigDetails, error)) *EVM2EVMOffRampInterface_LatestConfigDetails_Call {
	_c.Call.Return(run)
	return _c
}

// LatestConfigDigestAndEpoch provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) LatestConfigDigestAndEpoch(opts *bind.CallOpts) (evm_2_evm_offramp_1_2_0.LatestConfigDigestAndEpoch, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LatestConfigDigestAndEpoch")
	}

	var r0 evm_2_evm_offramp_1_2_0.LatestConfigDigestAndEpoch
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.LatestConfigDigestAndEpoch, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) evm_2_evm_offramp_1_2_0.LatestConfigDigestAndEpoch); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(evm_2_evm_offramp_1_2_0.LatestConfigDigestAndEpoch)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestConfigDigestAndEpoch'
type EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call struct {
	*mock.Call
}

// LatestConfigDigestAndEpoch is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) LatestConfigDigestAndEpoch(opts interface{}) *EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call {
	return &EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call{Call: _e.mock.On("LatestConfigDigestAndEpoch", opts)}
}

func (_c *EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call) Return(_a0 evm_2_evm_offramp_1_2_0.LatestConfigDigestAndEpoch, _a1 error) *EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call) RunAndReturn(run func(*bind.CallOpts) (evm_2_evm_offramp_1_2_0.LatestConfigDigestAndEpoch, error)) *EVM2EVMOffRampInterface_LatestConfigDigestAndEpoch_Call {
	_c.Call.Return(run)
	return _c
}

// ManuallyExecute provides a mock function with given fields: opts, report, gasLimitOverrides
func (_m *EVM2EVMOffRampInterface) ManuallyExecute(opts *bind.TransactOpts, report evm_2_evm_offramp_1_2_0.InternalExecutionReport, gasLimitOverrides []*big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, report, gasLimitOverrides)

	if len(ret) == 0 {
		panic("no return value specified for ManuallyExecute")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.InternalExecutionReport, []*big.Int) (*types.Transaction, error)); ok {
		return rf(opts, report, gasLimitOverrides)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.InternalExecutionReport, []*big.Int) *types.Transaction); ok {
		r0 = rf(opts, report, gasLimitOverrides)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.InternalExecutionReport, []*big.Int) error); ok {
		r1 = rf(opts, report, gasLimitOverrides)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ManuallyExecute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ManuallyExecute'
type EVM2EVMOffRampInterface_ManuallyExecute_Call struct {
	*mock.Call
}

// ManuallyExecute is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - report evm_2_evm_offramp_1_2_0.InternalExecutionReport
//   - gasLimitOverrides []*big.Int
func (_e *EVM2EVMOffRampInterface_Expecter) ManuallyExecute(opts interface{}, report interface{}, gasLimitOverrides interface{}) *EVM2EVMOffRampInterface_ManuallyExecute_Call {
	return &EVM2EVMOffRampInterface_ManuallyExecute_Call{Call: _e.mock.On("ManuallyExecute", opts, report, gasLimitOverrides)}
}

func (_c *EVM2EVMOffRampInterface_ManuallyExecute_Call) Run(run func(opts *bind.TransactOpts, report evm_2_evm_offramp_1_2_0.InternalExecutionReport, gasLimitOverrides []*big.Int)) *EVM2EVMOffRampInterface_ManuallyExecute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(evm_2_evm_offramp_1_2_0.InternalExecutionReport), args[2].([]*big.Int))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ManuallyExecute_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOffRampInterface_ManuallyExecute_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ManuallyExecute_Call) RunAndReturn(run func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.InternalExecutionReport, []*big.Int) (*types.Transaction, error)) *EVM2EVMOffRampInterface_ManuallyExecute_Call {
	_c.Call.Return(run)
	return _c
}

// Owner provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) Owner(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Owner")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_Owner_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Owner'
type EVM2EVMOffRampInterface_Owner_Call struct {
	*mock.Call
}

// Owner is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) Owner(opts interface{}) *EVM2EVMOffRampInterface_Owner_Call {
	return &EVM2EVMOffRampInterface_Owner_Call{Call: _e.mock.On("Owner", opts)}
}

func (_c *EVM2EVMOffRampInterface_Owner_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_Owner_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_Owner_Call) Return(_a0 common.Address, _a1 error) *EVM2EVMOffRampInterface_Owner_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_Owner_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *EVM2EVMOffRampInterface_Owner_Call {
	_c.Call.Return(run)
	return _c
}

// ParseAdminSet provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseAdminSet(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseAdminSet")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseAdminSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseAdminSet'
type EVM2EVMOffRampInterface_ParseAdminSet_Call struct {
	*mock.Call
}

// ParseAdminSet is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseAdminSet(log interface{}) *EVM2EVMOffRampInterface_ParseAdminSet_Call {
	return &EVM2EVMOffRampInterface_ParseAdminSet_Call{Call: _e.mock.On("ParseAdminSet", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseAdminSet_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseAdminSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseAdminSet_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet, _a1 error) *EVM2EVMOffRampInterface_ParseAdminSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseAdminSet_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet, error)) *EVM2EVMOffRampInterface_ParseAdminSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseConfigSet provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseConfigSet(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseConfigSet")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseConfigSet'
type EVM2EVMOffRampInterface_ParseConfigSet_Call struct {
	*mock.Call
}

// ParseConfigSet is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseConfigSet(log interface{}) *EVM2EVMOffRampInterface_ParseConfigSet_Call {
	return &EVM2EVMOffRampInterface_ParseConfigSet_Call{Call: _e.mock.On("ParseConfigSet", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseConfigSet_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseConfigSet_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet, _a1 error) *EVM2EVMOffRampInterface_ParseConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseConfigSet_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet, error)) *EVM2EVMOffRampInterface_ParseConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseConfigSet0 provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseConfigSet0(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseConfigSet0")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseConfigSet0_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseConfigSet0'
type EVM2EVMOffRampInterface_ParseConfigSet0_Call struct {
	*mock.Call
}

// ParseConfigSet0 is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseConfigSet0(log interface{}) *EVM2EVMOffRampInterface_ParseConfigSet0_Call {
	return &EVM2EVMOffRampInterface_ParseConfigSet0_Call{Call: _e.mock.On("ParseConfigSet0", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseConfigSet0_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseConfigSet0_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseConfigSet0_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0, _a1 error) *EVM2EVMOffRampInterface_ParseConfigSet0_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseConfigSet0_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0, error)) *EVM2EVMOffRampInterface_ParseConfigSet0_Call {
	_c.Call.Return(run)
	return _c
}

// ParseExecutionStateChanged provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseExecutionStateChanged(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseExecutionStateChanged")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseExecutionStateChanged'
type EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call struct {
	*mock.Call
}

// ParseExecutionStateChanged is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseExecutionStateChanged(log interface{}) *EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call {
	return &EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call{Call: _e.mock.On("ParseExecutionStateChanged", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, _a1 error) *EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, error)) *EVM2EVMOffRampInterface_ParseExecutionStateChanged_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type EVM2EVMOffRampInterface_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseLog(log interface{}) *EVM2EVMOffRampInterface_ParseLog_Call {
	return &EVM2EVMOffRampInterface_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseLog_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *EVM2EVMOffRampInterface_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *EVM2EVMOffRampInterface_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferRequested provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseOwnershipTransferRequested(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferRequested")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferRequested'
type EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call struct {
	*mock.Call
}

// ParseOwnershipTransferRequested is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseOwnershipTransferRequested(log interface{}) *EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call {
	return &EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call{Call: _e.mock.On("ParseOwnershipTransferRequested", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, _a1 error) *EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, error)) *EVM2EVMOffRampInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferred provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseOwnershipTransferred(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferred")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferred'
type EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call struct {
	*mock.Call
}

// ParseOwnershipTransferred is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseOwnershipTransferred(log interface{}) *EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call {
	return &EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call{Call: _e.mock.On("ParseOwnershipTransferred", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, _a1 error) *EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, error)) *EVM2EVMOffRampInterface_ParseOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// ParsePoolAdded provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParsePoolAdded(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParsePoolAdded")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParsePoolAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParsePoolAdded'
type EVM2EVMOffRampInterface_ParsePoolAdded_Call struct {
	*mock.Call
}

// ParsePoolAdded is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParsePoolAdded(log interface{}) *EVM2EVMOffRampInterface_ParsePoolAdded_Call {
	return &EVM2EVMOffRampInterface_ParsePoolAdded_Call{Call: _e.mock.On("ParsePoolAdded", log)}
}

func (_c *EVM2EVMOffRampInterface_ParsePoolAdded_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParsePoolAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParsePoolAdded_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded, _a1 error) *EVM2EVMOffRampInterface_ParsePoolAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParsePoolAdded_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded, error)) *EVM2EVMOffRampInterface_ParsePoolAdded_Call {
	_c.Call.Return(run)
	return _c
}

// ParsePoolRemoved provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParsePoolRemoved(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParsePoolRemoved")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParsePoolRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParsePoolRemoved'
type EVM2EVMOffRampInterface_ParsePoolRemoved_Call struct {
	*mock.Call
}

// ParsePoolRemoved is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParsePoolRemoved(log interface{}) *EVM2EVMOffRampInterface_ParsePoolRemoved_Call {
	return &EVM2EVMOffRampInterface_ParsePoolRemoved_Call{Call: _e.mock.On("ParsePoolRemoved", log)}
}

func (_c *EVM2EVMOffRampInterface_ParsePoolRemoved_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParsePoolRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParsePoolRemoved_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved, _a1 error) *EVM2EVMOffRampInterface_ParsePoolRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParsePoolRemoved_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved, error)) *EVM2EVMOffRampInterface_ParsePoolRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSkippedIncorrectNonce provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseSkippedIncorrectNonce(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSkippedIncorrectNonce")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSkippedIncorrectNonce'
type EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call struct {
	*mock.Call
}

// ParseSkippedIncorrectNonce is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseSkippedIncorrectNonce(log interface{}) *EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call {
	return &EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call{Call: _e.mock.On("ParseSkippedIncorrectNonce", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, _a1 error) *EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, error)) *EVM2EVMOffRampInterface_ParseSkippedIncorrectNonce_Call {
	_c.Call.Return(run)
	return _c
}

// ParseSkippedSenderWithPreviousRampMessageInflight provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseSkippedSenderWithPreviousRampMessageInflight(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseSkippedSenderWithPreviousRampMessageInflight")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseSkippedSenderWithPreviousRampMessageInflight'
type EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call struct {
	*mock.Call
}

// ParseSkippedSenderWithPreviousRampMessageInflight is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseSkippedSenderWithPreviousRampMessageInflight(log interface{}) *EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call {
	return &EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call{Call: _e.mock.On("ParseSkippedSenderWithPreviousRampMessageInflight", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, _a1 error) *EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, error)) *EVM2EVMOffRampInterface_ParseSkippedSenderWithPreviousRampMessageInflight_Call {
	_c.Call.Return(run)
	return _c
}

// ParseTransmitted provides a mock function with given fields: log
func (_m *EVM2EVMOffRampInterface) ParseTransmitted(log types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseTransmitted")
	}

	var r0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_ParseTransmitted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseTransmitted'
type EVM2EVMOffRampInterface_ParseTransmitted_Call struct {
	*mock.Call
}

// ParseTransmitted is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOffRampInterface_Expecter) ParseTransmitted(log interface{}) *EVM2EVMOffRampInterface_ParseTransmitted_Call {
	return &EVM2EVMOffRampInterface_ParseTransmitted_Call{Call: _e.mock.On("ParseTransmitted", log)}
}

func (_c *EVM2EVMOffRampInterface_ParseTransmitted_Call) Run(run func(log types.Log)) *EVM2EVMOffRampInterface_ParseTransmitted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseTransmitted_Call) Return(_a0 *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted, _a1 error) *EVM2EVMOffRampInterface_ParseTransmitted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_ParseTransmitted_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted, error)) *EVM2EVMOffRampInterface_ParseTransmitted_Call {
	_c.Call.Return(run)
	return _c
}

// SetAdmin provides a mock function with given fields: opts, newAdmin
func (_m *EVM2EVMOffRampInterface) SetAdmin(opts *bind.TransactOpts, newAdmin common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, newAdmin)

	if len(ret) == 0 {
		panic("no return value specified for SetAdmin")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, newAdmin)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, newAdmin)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, newAdmin)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_SetAdmin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetAdmin'
type EVM2EVMOffRampInterface_SetAdmin_Call struct {
	*mock.Call
}

// SetAdmin is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - newAdmin common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) SetAdmin(opts interface{}, newAdmin interface{}) *EVM2EVMOffRampInterface_SetAdmin_Call {
	return &EVM2EVMOffRampInterface_SetAdmin_Call{Call: _e.mock.On("SetAdmin", opts, newAdmin)}
}

func (_c *EVM2EVMOffRampInterface_SetAdmin_Call) Run(run func(opts *bind.TransactOpts, newAdmin common.Address)) *EVM2EVMOffRampInterface_SetAdmin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_SetAdmin_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOffRampInterface_SetAdmin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_SetAdmin_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *EVM2EVMOffRampInterface_SetAdmin_Call {
	_c.Call.Return(run)
	return _c
}

// SetOCR2Config provides a mock function with given fields: opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig
func (_m *EVM2EVMOffRampInterface) SetOCR2Config(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)

	if len(ret) == 0 {
		panic("no return value specified for SetOCR2Config")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address, []common.Address, uint8, []byte, uint64, []byte) (*types.Transaction, error)); ok {
		return rf(opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address, []common.Address, uint8, []byte, uint64, []byte) *types.Transaction); ok {
		r0 = rf(opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []common.Address, []common.Address, uint8, []byte, uint64, []byte) error); ok {
		r1 = rf(opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_SetOCR2Config_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetOCR2Config'
type EVM2EVMOffRampInterface_SetOCR2Config_Call struct {
	*mock.Call
}

// SetOCR2Config is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - signers []common.Address
//   - transmitters []common.Address
//   - f uint8
//   - onchainConfig []byte
//   - offchainConfigVersion uint64
//   - offchainConfig []byte
func (_e *EVM2EVMOffRampInterface_Expecter) SetOCR2Config(opts interface{}, signers interface{}, transmitters interface{}, f interface{}, onchainConfig interface{}, offchainConfigVersion interface{}, offchainConfig interface{}) *EVM2EVMOffRampInterface_SetOCR2Config_Call {
	return &EVM2EVMOffRampInterface_SetOCR2Config_Call{Call: _e.mock.On("SetOCR2Config", opts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)}
}

func (_c *EVM2EVMOffRampInterface_SetOCR2Config_Call) Run(run func(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte)) *EVM2EVMOffRampInterface_SetOCR2Config_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]common.Address), args[2].([]common.Address), args[3].(uint8), args[4].([]byte), args[5].(uint64), args[6].([]byte))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_SetOCR2Config_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOffRampInterface_SetOCR2Config_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_SetOCR2Config_Call) RunAndReturn(run func(*bind.TransactOpts, []common.Address, []common.Address, uint8, []byte, uint64, []byte) (*types.Transaction, error)) *EVM2EVMOffRampInterface_SetOCR2Config_Call {
	_c.Call.Return(run)
	return _c
}

// SetRateLimiterConfig provides a mock function with given fields: opts, config
func (_m *EVM2EVMOffRampInterface) SetRateLimiterConfig(opts *bind.TransactOpts, config evm_2_evm_offramp_1_2_0.RateLimiterConfig) (*types.Transaction, error) {
	ret := _m.Called(opts, config)

	if len(ret) == 0 {
		panic("no return value specified for SetRateLimiterConfig")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.RateLimiterConfig) (*types.Transaction, error)); ok {
		return rf(opts, config)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.RateLimiterConfig) *types.Transaction); ok {
		r0 = rf(opts, config)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.RateLimiterConfig) error); ok {
		r1 = rf(opts, config)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_SetRateLimiterConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetRateLimiterConfig'
type EVM2EVMOffRampInterface_SetRateLimiterConfig_Call struct {
	*mock.Call
}

// SetRateLimiterConfig is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - config evm_2_evm_offramp_1_2_0.RateLimiterConfig
func (_e *EVM2EVMOffRampInterface_Expecter) SetRateLimiterConfig(opts interface{}, config interface{}) *EVM2EVMOffRampInterface_SetRateLimiterConfig_Call {
	return &EVM2EVMOffRampInterface_SetRateLimiterConfig_Call{Call: _e.mock.On("SetRateLimiterConfig", opts, config)}
}

func (_c *EVM2EVMOffRampInterface_SetRateLimiterConfig_Call) Run(run func(opts *bind.TransactOpts, config evm_2_evm_offramp_1_2_0.RateLimiterConfig)) *EVM2EVMOffRampInterface_SetRateLimiterConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(evm_2_evm_offramp_1_2_0.RateLimiterConfig))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_SetRateLimiterConfig_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOffRampInterface_SetRateLimiterConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_SetRateLimiterConfig_Call) RunAndReturn(run func(*bind.TransactOpts, evm_2_evm_offramp_1_2_0.RateLimiterConfig) (*types.Transaction, error)) *EVM2EVMOffRampInterface_SetRateLimiterConfig_Call {
	_c.Call.Return(run)
	return _c
}

// TransferOwnership provides a mock function with given fields: opts, to
func (_m *EVM2EVMOffRampInterface) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, to)

	if len(ret) == 0 {
		panic("no return value specified for TransferOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_TransferOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransferOwnership'
type EVM2EVMOffRampInterface_TransferOwnership_Call struct {
	*mock.Call
}

// TransferOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - to common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) TransferOwnership(opts interface{}, to interface{}) *EVM2EVMOffRampInterface_TransferOwnership_Call {
	return &EVM2EVMOffRampInterface_TransferOwnership_Call{Call: _e.mock.On("TransferOwnership", opts, to)}
}

func (_c *EVM2EVMOffRampInterface_TransferOwnership_Call) Run(run func(opts *bind.TransactOpts, to common.Address)) *EVM2EVMOffRampInterface_TransferOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_TransferOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOffRampInterface_TransferOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_TransferOwnership_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *EVM2EVMOffRampInterface_TransferOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// Transmit provides a mock function with given fields: opts, reportContext, report, rs, ss, arg4
func (_m *EVM2EVMOffRampInterface) Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, arg4 [32]byte) (*types.Transaction, error) {
	ret := _m.Called(opts, reportContext, report, rs, ss, arg4)

	if len(ret) == 0 {
		panic("no return value specified for Transmit")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [3][32]byte, []byte, [][32]byte, [][32]byte, [32]byte) (*types.Transaction, error)); ok {
		return rf(opts, reportContext, report, rs, ss, arg4)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, [3][32]byte, []byte, [][32]byte, [][32]byte, [32]byte) *types.Transaction); ok {
		r0 = rf(opts, reportContext, report, rs, ss, arg4)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, [3][32]byte, []byte, [][32]byte, [][32]byte, [32]byte) error); ok {
		r1 = rf(opts, reportContext, report, rs, ss, arg4)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_Transmit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transmit'
type EVM2EVMOffRampInterface_Transmit_Call struct {
	*mock.Call
}

// Transmit is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - reportContext [3][32]byte
//   - report []byte
//   - rs [][32]byte
//   - ss [][32]byte
//   - arg4 [32]byte
func (_e *EVM2EVMOffRampInterface_Expecter) Transmit(opts interface{}, reportContext interface{}, report interface{}, rs interface{}, ss interface{}, arg4 interface{}) *EVM2EVMOffRampInterface_Transmit_Call {
	return &EVM2EVMOffRampInterface_Transmit_Call{Call: _e.mock.On("Transmit", opts, reportContext, report, rs, ss, arg4)}
}

func (_c *EVM2EVMOffRampInterface_Transmit_Call) Run(run func(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, arg4 [32]byte)) *EVM2EVMOffRampInterface_Transmit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([3][32]byte), args[2].([]byte), args[3].([][32]byte), args[4].([][32]byte), args[5].([32]byte))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_Transmit_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOffRampInterface_Transmit_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_Transmit_Call) RunAndReturn(run func(*bind.TransactOpts, [3][32]byte, []byte, [][32]byte, [][32]byte, [32]byte) (*types.Transaction, error)) *EVM2EVMOffRampInterface_Transmit_Call {
	_c.Call.Return(run)
	return _c
}

// TypeAndVersion provides a mock function with given fields: opts
func (_m *EVM2EVMOffRampInterface) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for TypeAndVersion")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (string, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) string); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_TypeAndVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TypeAndVersion'
type EVM2EVMOffRampInterface_TypeAndVersion_Call struct {
	*mock.Call
}

// TypeAndVersion is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOffRampInterface_Expecter) TypeAndVersion(opts interface{}) *EVM2EVMOffRampInterface_TypeAndVersion_Call {
	return &EVM2EVMOffRampInterface_TypeAndVersion_Call{Call: _e.mock.On("TypeAndVersion", opts)}
}

func (_c *EVM2EVMOffRampInterface_TypeAndVersion_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOffRampInterface_TypeAndVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_TypeAndVersion_Call) Return(_a0 string, _a1 error) *EVM2EVMOffRampInterface_TypeAndVersion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_TypeAndVersion_Call) RunAndReturn(run func(*bind.CallOpts) (string, error)) *EVM2EVMOffRampInterface_TypeAndVersion_Call {
	_c.Call.Return(run)
	return _c
}

// WatchAdminSet provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOffRampInterface) WatchAdminSet(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchAdminSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchAdminSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchAdminSet'
type EVM2EVMOffRampInterface_WatchAdminSet_Call struct {
	*mock.Call
}

// WatchAdminSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet
func (_e *EVM2EVMOffRampInterface_Expecter) WatchAdminSet(opts interface{}, sink interface{}) *EVM2EVMOffRampInterface_WatchAdminSet_Call {
	return &EVM2EVMOffRampInterface_WatchAdminSet_Call{Call: _e.mock.On("WatchAdminSet", opts, sink)}
}

func (_c *EVM2EVMOffRampInterface_WatchAdminSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet)) *EVM2EVMOffRampInterface_WatchAdminSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchAdminSet_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchAdminSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchAdminSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampAdminSet) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchAdminSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchConfigSet provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOffRampInterface) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchConfigSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchConfigSet'
type EVM2EVMOffRampInterface_WatchConfigSet_Call struct {
	*mock.Call
}

// WatchConfigSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet
func (_e *EVM2EVMOffRampInterface_Expecter) WatchConfigSet(opts interface{}, sink interface{}) *EVM2EVMOffRampInterface_WatchConfigSet_Call {
	return &EVM2EVMOffRampInterface_WatchConfigSet_Call{Call: _e.mock.On("WatchConfigSet", opts, sink)}
}

func (_c *EVM2EVMOffRampInterface_WatchConfigSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet)) *EVM2EVMOffRampInterface_WatchConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchConfigSet_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchConfigSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchConfigSet0 provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOffRampInterface) WatchConfigSet0(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchConfigSet0")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchConfigSet0_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchConfigSet0'
type EVM2EVMOffRampInterface_WatchConfigSet0_Call struct {
	*mock.Call
}

// WatchConfigSet0 is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0
func (_e *EVM2EVMOffRampInterface_Expecter) WatchConfigSet0(opts interface{}, sink interface{}) *EVM2EVMOffRampInterface_WatchConfigSet0_Call {
	return &EVM2EVMOffRampInterface_WatchConfigSet0_Call{Call: _e.mock.On("WatchConfigSet0", opts, sink)}
}

func (_c *EVM2EVMOffRampInterface_WatchConfigSet0_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0)) *EVM2EVMOffRampInterface_WatchConfigSet0_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchConfigSet0_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchConfigSet0_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchConfigSet0_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampConfigSet0) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchConfigSet0_Call {
	_c.Call.Return(run)
	return _c
}

// WatchExecutionStateChanged provides a mock function with given fields: opts, sink, sequenceNumber, messageId
func (_m *EVM2EVMOffRampInterface) WatchExecutionStateChanged(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, sequenceNumber []uint64, messageId [][32]byte) (event.Subscription, error) {
	ret := _m.Called(opts, sink, sequenceNumber, messageId)

	if len(ret) == 0 {
		panic("no return value specified for WatchExecutionStateChanged")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, []uint64, [][32]byte) (event.Subscription, error)); ok {
		return rf(opts, sink, sequenceNumber, messageId)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, []uint64, [][32]byte) event.Subscription); ok {
		r0 = rf(opts, sink, sequenceNumber, messageId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, []uint64, [][32]byte) error); ok {
		r1 = rf(opts, sink, sequenceNumber, messageId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchExecutionStateChanged'
type EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call struct {
	*mock.Call
}

// WatchExecutionStateChanged is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged
//   - sequenceNumber []uint64
//   - messageId [][32]byte
func (_e *EVM2EVMOffRampInterface_Expecter) WatchExecutionStateChanged(opts interface{}, sink interface{}, sequenceNumber interface{}, messageId interface{}) *EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call {
	return &EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call{Call: _e.mock.On("WatchExecutionStateChanged", opts, sink, sequenceNumber, messageId)}
}

func (_c *EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, sequenceNumber []uint64, messageId [][32]byte)) *EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged), args[2].([]uint64), args[3].([][32]byte))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampExecutionStateChanged, []uint64, [][32]byte) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchExecutionStateChanged_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferRequested provides a mock function with given fields: opts, sink, from, to
func (_m *EVM2EVMOffRampInterface) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferRequested")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferRequested'
type EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call struct {
	*mock.Call
}

// WatchOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested
//   - from []common.Address
//   - to []common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) WatchOwnershipTransferRequested(opts interface{}, sink interface{}, from interface{}, to interface{}) *EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call {
	return &EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call{Call: _e.mock.On("WatchOwnershipTransferRequested", opts, sink, from, to)}
}

func (_c *EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, from []common.Address, to []common.Address)) *EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferred provides a mock function with given fields: opts, sink, from, to
func (_m *EVM2EVMOffRampInterface) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferred")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferred'
type EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call struct {
	*mock.Call
}

// WatchOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred
//   - from []common.Address
//   - to []common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) WatchOwnershipTransferred(opts interface{}, sink interface{}, from interface{}, to interface{}) *EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call {
	return &EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call{Call: _e.mock.On("WatchOwnershipTransferred", opts, sink, from, to)}
}

func (_c *EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, from []common.Address, to []common.Address)) *EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// WatchPoolAdded provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOffRampInterface) WatchPoolAdded(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchPoolAdded")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchPoolAdded_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchPoolAdded'
type EVM2EVMOffRampInterface_WatchPoolAdded_Call struct {
	*mock.Call
}

// WatchPoolAdded is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded
func (_e *EVM2EVMOffRampInterface_Expecter) WatchPoolAdded(opts interface{}, sink interface{}) *EVM2EVMOffRampInterface_WatchPoolAdded_Call {
	return &EVM2EVMOffRampInterface_WatchPoolAdded_Call{Call: _e.mock.On("WatchPoolAdded", opts, sink)}
}

func (_c *EVM2EVMOffRampInterface_WatchPoolAdded_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded)) *EVM2EVMOffRampInterface_WatchPoolAdded_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchPoolAdded_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchPoolAdded_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchPoolAdded_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolAdded) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchPoolAdded_Call {
	_c.Call.Return(run)
	return _c
}

// WatchPoolRemoved provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOffRampInterface) WatchPoolRemoved(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchPoolRemoved")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchPoolRemoved_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchPoolRemoved'
type EVM2EVMOffRampInterface_WatchPoolRemoved_Call struct {
	*mock.Call
}

// WatchPoolRemoved is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved
func (_e *EVM2EVMOffRampInterface_Expecter) WatchPoolRemoved(opts interface{}, sink interface{}) *EVM2EVMOffRampInterface_WatchPoolRemoved_Call {
	return &EVM2EVMOffRampInterface_WatchPoolRemoved_Call{Call: _e.mock.On("WatchPoolRemoved", opts, sink)}
}

func (_c *EVM2EVMOffRampInterface_WatchPoolRemoved_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved)) *EVM2EVMOffRampInterface_WatchPoolRemoved_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchPoolRemoved_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchPoolRemoved_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchPoolRemoved_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampPoolRemoved) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchPoolRemoved_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSkippedIncorrectNonce provides a mock function with given fields: opts, sink, nonce, sender
func (_m *EVM2EVMOffRampInterface) WatchSkippedIncorrectNonce(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, nonce []uint64, sender []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, nonce, sender)

	if len(ret) == 0 {
		panic("no return value specified for WatchSkippedIncorrectNonce")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, []uint64, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, nonce, sender)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, []uint64, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, nonce, sender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, []uint64, []common.Address) error); ok {
		r1 = rf(opts, sink, nonce, sender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSkippedIncorrectNonce'
type EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call struct {
	*mock.Call
}

// WatchSkippedIncorrectNonce is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce
//   - nonce []uint64
//   - sender []common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) WatchSkippedIncorrectNonce(opts interface{}, sink interface{}, nonce interface{}, sender interface{}) *EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call {
	return &EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call{Call: _e.mock.On("WatchSkippedIncorrectNonce", opts, sink, nonce, sender)}
}

func (_c *EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, nonce []uint64, sender []common.Address)) *EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce), args[2].([]uint64), args[3].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedIncorrectNonce, []uint64, []common.Address) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchSkippedIncorrectNonce_Call {
	_c.Call.Return(run)
	return _c
}

// WatchSkippedSenderWithPreviousRampMessageInflight provides a mock function with given fields: opts, sink, nonce, sender
func (_m *EVM2EVMOffRampInterface) WatchSkippedSenderWithPreviousRampMessageInflight(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, nonce []uint64, sender []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, nonce, sender)

	if len(ret) == 0 {
		panic("no return value specified for WatchSkippedSenderWithPreviousRampMessageInflight")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, []uint64, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, nonce, sender)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, []uint64, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, nonce, sender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, []uint64, []common.Address) error); ok {
		r1 = rf(opts, sink, nonce, sender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchSkippedSenderWithPreviousRampMessageInflight'
type EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call struct {
	*mock.Call
}

// WatchSkippedSenderWithPreviousRampMessageInflight is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight
//   - nonce []uint64
//   - sender []common.Address
func (_e *EVM2EVMOffRampInterface_Expecter) WatchSkippedSenderWithPreviousRampMessageInflight(opts interface{}, sink interface{}, nonce interface{}, sender interface{}) *EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call {
	return &EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call{Call: _e.mock.On("WatchSkippedSenderWithPreviousRampMessageInflight", opts, sink, nonce, sender)}
}

func (_c *EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, nonce []uint64, sender []common.Address)) *EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight), args[2].([]uint64), args[3].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampSkippedSenderWithPreviousRampMessageInflight, []uint64, []common.Address) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchSkippedSenderWithPreviousRampMessageInflight_Call {
	_c.Call.Return(run)
	return _c
}

// WatchTransmitted provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOffRampInterface) WatchTransmitted(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchTransmitted")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOffRampInterface_WatchTransmitted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchTransmitted'
type EVM2EVMOffRampInterface_WatchTransmitted_Call struct {
	*mock.Call
}

// WatchTransmitted is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted
func (_e *EVM2EVMOffRampInterface_Expecter) WatchTransmitted(opts interface{}, sink interface{}) *EVM2EVMOffRampInterface_WatchTransmitted_Call {
	return &EVM2EVMOffRampInterface_WatchTransmitted_Call{Call: _e.mock.On("WatchTransmitted", opts, sink)}
}

func (_c *EVM2EVMOffRampInterface_WatchTransmitted_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted)) *EVM2EVMOffRampInterface_WatchTransmitted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted))
	})
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchTransmitted_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOffRampInterface_WatchTransmitted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOffRampInterface_WatchTransmitted_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_offramp_1_2_0.EVM2EVMOffRampTransmitted) (event.Subscription, error)) *EVM2EVMOffRampInterface_WatchTransmitted_Call {
	_c.Call.Return(run)
	return _c
}

// NewEVM2EVMOffRampInterface creates a new instance of EVM2EVMOffRampInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewEVM2EVMOffRampInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *EVM2EVMOffRampInterface {
	mock := &EVM2EVMOffRampInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
