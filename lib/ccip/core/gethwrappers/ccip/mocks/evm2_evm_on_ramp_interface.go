// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_contracts

import (
	big "math/big"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	event "github.com/ethereum/go-ethereum/event"

	evm_2_evm_onramp "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/ccip/generated/evm_2_evm_onramp"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// EVM2EVMOnRampInterface is an autogenerated mock type for the EVM2EVMOnRampInterface type
type EVM2EVMOnRampInterface struct {
	mock.Mock
}

type EVM2EVMOnRampInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *EVM2EVMOnRampInterface) EXPECT() *EVM2EVMOnRampInterface_Expecter {
	return &EVM2EVMOnRampInterface_Expecter{mock: &_m.Mock}
}

// AcceptOwnership provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for AcceptOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_AcceptOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptOwnership'
type EVM2EVMOnRampInterface_AcceptOwnership_Call struct {
	*mock.Call
}

// AcceptOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *EVM2EVMOnRampInterface_Expecter) AcceptOwnership(opts interface{}) *EVM2EVMOnRampInterface_AcceptOwnership_Call {
	return &EVM2EVMOnRampInterface_AcceptOwnership_Call{Call: _e.mock.On("AcceptOwnership", opts)}
}

func (_c *EVM2EVMOnRampInterface_AcceptOwnership_Call) Run(run func(opts *bind.TransactOpts)) *EVM2EVMOnRampInterface_AcceptOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_AcceptOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_AcceptOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_AcceptOwnership_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *EVM2EVMOnRampInterface_AcceptOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// Address provides a mock function with given fields:
func (_m *EVM2EVMOnRampInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// EVM2EVMOnRampInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type EVM2EVMOnRampInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *EVM2EVMOnRampInterface_Expecter) Address() *EVM2EVMOnRampInterface_Address_Call {
	return &EVM2EVMOnRampInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *EVM2EVMOnRampInterface_Address_Call) Run(run func()) *EVM2EVMOnRampInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_Address_Call) Return(_a0 common.Address) *EVM2EVMOnRampInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *EVM2EVMOnRampInterface_Address_Call) RunAndReturn(run func() common.Address) *EVM2EVMOnRampInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// CurrentRateLimiterState provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) CurrentRateLimiterState(opts *bind.CallOpts) (evm_2_evm_onramp.RateLimiterTokenBucket, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for CurrentRateLimiterState")
	}

	var r0 evm_2_evm_onramp.RateLimiterTokenBucket
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (evm_2_evm_onramp.RateLimiterTokenBucket, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) evm_2_evm_onramp.RateLimiterTokenBucket); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(evm_2_evm_onramp.RateLimiterTokenBucket)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_CurrentRateLimiterState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CurrentRateLimiterState'
type EVM2EVMOnRampInterface_CurrentRateLimiterState_Call struct {
	*mock.Call
}

// CurrentRateLimiterState is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) CurrentRateLimiterState(opts interface{}) *EVM2EVMOnRampInterface_CurrentRateLimiterState_Call {
	return &EVM2EVMOnRampInterface_CurrentRateLimiterState_Call{Call: _e.mock.On("CurrentRateLimiterState", opts)}
}

func (_c *EVM2EVMOnRampInterface_CurrentRateLimiterState_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_CurrentRateLimiterState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_CurrentRateLimiterState_Call) Return(_a0 evm_2_evm_onramp.RateLimiterTokenBucket, _a1 error) *EVM2EVMOnRampInterface_CurrentRateLimiterState_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_CurrentRateLimiterState_Call) RunAndReturn(run func(*bind.CallOpts) (evm_2_evm_onramp.RateLimiterTokenBucket, error)) *EVM2EVMOnRampInterface_CurrentRateLimiterState_Call {
	_c.Call.Return(run)
	return _c
}

// FilterAdminSet provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) FilterAdminSet(opts *bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampAdminSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterAdminSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampAdminSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampAdminSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_onramp.EVM2EVMOnRampAdminSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampAdminSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterAdminSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterAdminSet'
type EVM2EVMOnRampInterface_FilterAdminSet_Call struct {
	*mock.Call
}

// FilterAdminSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOnRampInterface_Expecter) FilterAdminSet(opts interface{}) *EVM2EVMOnRampInterface_FilterAdminSet_Call {
	return &EVM2EVMOnRampInterface_FilterAdminSet_Call{Call: _e.mock.On("FilterAdminSet", opts)}
}

func (_c *EVM2EVMOnRampInterface_FilterAdminSet_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOnRampInterface_FilterAdminSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterAdminSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampAdminSetIterator, _a1 error) *EVM2EVMOnRampInterface_FilterAdminSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterAdminSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampAdminSetIterator, error)) *EVM2EVMOnRampInterface_FilterAdminSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterCCIPSendRequested provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) FilterCCIPSendRequested(opts *bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequestedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterCCIPSendRequested")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequestedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequestedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequestedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequestedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterCCIPSendRequested'
type EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call struct {
	*mock.Call
}

// FilterCCIPSendRequested is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOnRampInterface_Expecter) FilterCCIPSendRequested(opts interface{}) *EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call {
	return &EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call{Call: _e.mock.On("FilterCCIPSendRequested", opts)}
}

func (_c *EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequestedIterator, _a1 error) *EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequestedIterator, error)) *EVM2EVMOnRampInterface_FilterCCIPSendRequested_Call {
	_c.Call.Return(run)
	return _c
}

// FilterConfigChanged provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) FilterConfigChanged(opts *bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampConfigChangedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterConfigChanged")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampConfigChangedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampConfigChangedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_onramp.EVM2EVMOnRampConfigChangedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampConfigChangedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterConfigChanged_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterConfigChanged'
type EVM2EVMOnRampInterface_FilterConfigChanged_Call struct {
	*mock.Call
}

// FilterConfigChanged is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOnRampInterface_Expecter) FilterConfigChanged(opts interface{}) *EVM2EVMOnRampInterface_FilterConfigChanged_Call {
	return &EVM2EVMOnRampInterface_FilterConfigChanged_Call{Call: _e.mock.On("FilterConfigChanged", opts)}
}

func (_c *EVM2EVMOnRampInterface_FilterConfigChanged_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOnRampInterface_FilterConfigChanged_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterConfigChanged_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampConfigChangedIterator, _a1 error) *EVM2EVMOnRampInterface_FilterConfigChanged_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterConfigChanged_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampConfigChangedIterator, error)) *EVM2EVMOnRampInterface_FilterConfigChanged_Call {
	_c.Call.Return(run)
	return _c
}

// FilterConfigSet provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) FilterConfigSet(opts *bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampConfigSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterConfigSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampConfigSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampConfigSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_onramp.EVM2EVMOnRampConfigSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampConfigSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterConfigSet'
type EVM2EVMOnRampInterface_FilterConfigSet_Call struct {
	*mock.Call
}

// FilterConfigSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOnRampInterface_Expecter) FilterConfigSet(opts interface{}) *EVM2EVMOnRampInterface_FilterConfigSet_Call {
	return &EVM2EVMOnRampInterface_FilterConfigSet_Call{Call: _e.mock.On("FilterConfigSet", opts)}
}

func (_c *EVM2EVMOnRampInterface_FilterConfigSet_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOnRampInterface_FilterConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterConfigSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampConfigSetIterator, _a1 error) *EVM2EVMOnRampInterface_FilterConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterConfigSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampConfigSetIterator, error)) *EVM2EVMOnRampInterface_FilterConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterFeeConfigSet provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) FilterFeeConfigSet(opts *bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterFeeConfigSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterFeeConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterFeeConfigSet'
type EVM2EVMOnRampInterface_FilterFeeConfigSet_Call struct {
	*mock.Call
}

// FilterFeeConfigSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOnRampInterface_Expecter) FilterFeeConfigSet(opts interface{}) *EVM2EVMOnRampInterface_FilterFeeConfigSet_Call {
	return &EVM2EVMOnRampInterface_FilterFeeConfigSet_Call{Call: _e.mock.On("FilterFeeConfigSet", opts)}
}

func (_c *EVM2EVMOnRampInterface_FilterFeeConfigSet_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOnRampInterface_FilterFeeConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterFeeConfigSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSetIterator, _a1 error) *EVM2EVMOnRampInterface_FilterFeeConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterFeeConfigSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSetIterator, error)) *EVM2EVMOnRampInterface_FilterFeeConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterNopPaid provides a mock function with given fields: opts, nop
func (_m *EVM2EVMOnRampInterface) FilterNopPaid(opts *bind.FilterOpts, nop []common.Address) (*evm_2_evm_onramp.EVM2EVMOnRampNopPaidIterator, error) {
	ret := _m.Called(opts, nop)

	if len(ret) == 0 {
		panic("no return value specified for FilterNopPaid")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampNopPaidIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*evm_2_evm_onramp.EVM2EVMOnRampNopPaidIterator, error)); ok {
		return rf(opts, nop)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *evm_2_evm_onramp.EVM2EVMOnRampNopPaidIterator); ok {
		r0 = rf(opts, nop)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampNopPaidIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, nop)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterNopPaid_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterNopPaid'
type EVM2EVMOnRampInterface_FilterNopPaid_Call struct {
	*mock.Call
}

// FilterNopPaid is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - nop []common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) FilterNopPaid(opts interface{}, nop interface{}) *EVM2EVMOnRampInterface_FilterNopPaid_Call {
	return &EVM2EVMOnRampInterface_FilterNopPaid_Call{Call: _e.mock.On("FilterNopPaid", opts, nop)}
}

func (_c *EVM2EVMOnRampInterface_FilterNopPaid_Call) Run(run func(opts *bind.FilterOpts, nop []common.Address)) *EVM2EVMOnRampInterface_FilterNopPaid_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterNopPaid_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampNopPaidIterator, _a1 error) *EVM2EVMOnRampInterface_FilterNopPaid_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterNopPaid_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*evm_2_evm_onramp.EVM2EVMOnRampNopPaidIterator, error)) *EVM2EVMOnRampInterface_FilterNopPaid_Call {
	_c.Call.Return(run)
	return _c
}

// FilterNopsSet provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) FilterNopsSet(opts *bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampNopsSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterNopsSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampNopsSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampNopsSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_onramp.EVM2EVMOnRampNopsSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampNopsSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterNopsSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterNopsSet'
type EVM2EVMOnRampInterface_FilterNopsSet_Call struct {
	*mock.Call
}

// FilterNopsSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOnRampInterface_Expecter) FilterNopsSet(opts interface{}) *EVM2EVMOnRampInterface_FilterNopsSet_Call {
	return &EVM2EVMOnRampInterface_FilterNopsSet_Call{Call: _e.mock.On("FilterNopsSet", opts)}
}

func (_c *EVM2EVMOnRampInterface_FilterNopsSet_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOnRampInterface_FilterNopsSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterNopsSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampNopsSetIterator, _a1 error) *EVM2EVMOnRampInterface_FilterNopsSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterNopsSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampNopsSetIterator, error)) *EVM2EVMOnRampInterface_FilterNopsSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferRequested provides a mock function with given fields: opts, from, to
func (_m *EVM2EVMOnRampInterface) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequestedIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferRequested")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequestedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequestedIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequestedIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequestedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferRequested'
type EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call struct {
	*mock.Call
}

// FilterOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) FilterOwnershipTransferRequested(opts interface{}, from interface{}, to interface{}) *EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call {
	return &EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call{Call: _e.mock.On("FilterOwnershipTransferRequested", opts, from, to)}
}

func (_c *EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequestedIterator, _a1 error) *EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequestedIterator, error)) *EVM2EVMOnRampInterface_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferred provides a mock function with given fields: opts, from, to
func (_m *EVM2EVMOnRampInterface) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferredIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferred")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferredIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferredIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferred'
type EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call struct {
	*mock.Call
}

// FilterOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) FilterOwnershipTransferred(opts interface{}, from interface{}, to interface{}) *EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call {
	return &EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call{Call: _e.mock.On("FilterOwnershipTransferred", opts, from, to)}
}

func (_c *EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferredIterator, _a1 error) *EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferredIterator, error)) *EVM2EVMOnRampInterface_FilterOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// FilterTokenTransferFeeConfigDeleted provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) FilterTokenTransferFeeConfigDeleted(opts *bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeletedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterTokenTransferFeeConfigDeleted")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeletedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeletedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeletedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeletedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterTokenTransferFeeConfigDeleted'
type EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call struct {
	*mock.Call
}

// FilterTokenTransferFeeConfigDeleted is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOnRampInterface_Expecter) FilterTokenTransferFeeConfigDeleted(opts interface{}) *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call {
	return &EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call{Call: _e.mock.On("FilterTokenTransferFeeConfigDeleted", opts)}
}

func (_c *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeletedIterator, _a1 error) *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeletedIterator, error)) *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(run)
	return _c
}

// FilterTokenTransferFeeConfigSet provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) FilterTokenTransferFeeConfigSet(opts *bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSetIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterTokenTransferFeeConfigSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSetIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSetIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSetIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSetIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterTokenTransferFeeConfigSet'
type EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call struct {
	*mock.Call
}

// FilterTokenTransferFeeConfigSet is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOnRampInterface_Expecter) FilterTokenTransferFeeConfigSet(opts interface{}) *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call {
	return &EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call{Call: _e.mock.On("FilterTokenTransferFeeConfigSet", opts)}
}

func (_c *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSetIterator, _a1 error) *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSetIterator, error)) *EVM2EVMOnRampInterface_FilterTokenTransferFeeConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// FilterTokensConsumed provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) FilterTokensConsumed(opts *bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampTokensConsumedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterTokensConsumed")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampTokensConsumedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampTokensConsumedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_FilterTokensConsumed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterTokensConsumed'
type EVM2EVMOnRampInterface_FilterTokensConsumed_Call struct {
	*mock.Call
}

// FilterTokensConsumed is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *EVM2EVMOnRampInterface_Expecter) FilterTokensConsumed(opts interface{}) *EVM2EVMOnRampInterface_FilterTokensConsumed_Call {
	return &EVM2EVMOnRampInterface_FilterTokensConsumed_Call{Call: _e.mock.On("FilterTokensConsumed", opts)}
}

func (_c *EVM2EVMOnRampInterface_FilterTokensConsumed_Call) Run(run func(opts *bind.FilterOpts)) *EVM2EVMOnRampInterface_FilterTokensConsumed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterTokensConsumed_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumedIterator, _a1 error) *EVM2EVMOnRampInterface_FilterTokensConsumed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_FilterTokensConsumed_Call) RunAndReturn(run func(*bind.FilterOpts) (*evm_2_evm_onramp.EVM2EVMOnRampTokensConsumedIterator, error)) *EVM2EVMOnRampInterface_FilterTokensConsumed_Call {
	_c.Call.Return(run)
	return _c
}

// ForwardFromRouter provides a mock function with given fields: opts, destChainSelector, message, feeTokenAmount, originalSender
func (_m *EVM2EVMOnRampInterface) ForwardFromRouter(opts *bind.TransactOpts, destChainSelector uint64, message evm_2_evm_onramp.ClientEVM2AnyMessage, feeTokenAmount *big.Int, originalSender common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, destChainSelector, message, feeTokenAmount, originalSender)

	if len(ret) == 0 {
		panic("no return value specified for ForwardFromRouter")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, evm_2_evm_onramp.ClientEVM2AnyMessage, *big.Int, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, destChainSelector, message, feeTokenAmount, originalSender)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, uint64, evm_2_evm_onramp.ClientEVM2AnyMessage, *big.Int, common.Address) *types.Transaction); ok {
		r0 = rf(opts, destChainSelector, message, feeTokenAmount, originalSender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, uint64, evm_2_evm_onramp.ClientEVM2AnyMessage, *big.Int, common.Address) error); ok {
		r1 = rf(opts, destChainSelector, message, feeTokenAmount, originalSender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ForwardFromRouter_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ForwardFromRouter'
type EVM2EVMOnRampInterface_ForwardFromRouter_Call struct {
	*mock.Call
}

// ForwardFromRouter is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - destChainSelector uint64
//   - message evm_2_evm_onramp.ClientEVM2AnyMessage
//   - feeTokenAmount *big.Int
//   - originalSender common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) ForwardFromRouter(opts interface{}, destChainSelector interface{}, message interface{}, feeTokenAmount interface{}, originalSender interface{}) *EVM2EVMOnRampInterface_ForwardFromRouter_Call {
	return &EVM2EVMOnRampInterface_ForwardFromRouter_Call{Call: _e.mock.On("ForwardFromRouter", opts, destChainSelector, message, feeTokenAmount, originalSender)}
}

func (_c *EVM2EVMOnRampInterface_ForwardFromRouter_Call) Run(run func(opts *bind.TransactOpts, destChainSelector uint64, message evm_2_evm_onramp.ClientEVM2AnyMessage, feeTokenAmount *big.Int, originalSender common.Address)) *EVM2EVMOnRampInterface_ForwardFromRouter_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(uint64), args[2].(evm_2_evm_onramp.ClientEVM2AnyMessage), args[3].(*big.Int), args[4].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ForwardFromRouter_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_ForwardFromRouter_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ForwardFromRouter_Call) RunAndReturn(run func(*bind.TransactOpts, uint64, evm_2_evm_onramp.ClientEVM2AnyMessage, *big.Int, common.Address) (*types.Transaction, error)) *EVM2EVMOnRampInterface_ForwardFromRouter_Call {
	_c.Call.Return(run)
	return _c
}

// GetDynamicConfig provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) GetDynamicConfig(opts *bind.CallOpts) (evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetDynamicConfig")
	}

	var r0 evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetDynamicConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDynamicConfig'
type EVM2EVMOnRampInterface_GetDynamicConfig_Call struct {
	*mock.Call
}

// GetDynamicConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) GetDynamicConfig(opts interface{}) *EVM2EVMOnRampInterface_GetDynamicConfig_Call {
	return &EVM2EVMOnRampInterface_GetDynamicConfig_Call{Call: _e.mock.On("GetDynamicConfig", opts)}
}

func (_c *EVM2EVMOnRampInterface_GetDynamicConfig_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_GetDynamicConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetDynamicConfig_Call) Return(_a0 evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig, _a1 error) *EVM2EVMOnRampInterface_GetDynamicConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetDynamicConfig_Call) RunAndReturn(run func(*bind.CallOpts) (evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig, error)) *EVM2EVMOnRampInterface_GetDynamicConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetExpectedNextSequenceNumber provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) GetExpectedNextSequenceNumber(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetExpectedNextSequenceNumber")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetExpectedNextSequenceNumber'
type EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call struct {
	*mock.Call
}

// GetExpectedNextSequenceNumber is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) GetExpectedNextSequenceNumber(opts interface{}) *EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call {
	return &EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call{Call: _e.mock.On("GetExpectedNextSequenceNumber", opts)}
}

func (_c *EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call) Return(_a0 uint64, _a1 error) *EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *EVM2EVMOnRampInterface_GetExpectedNextSequenceNumber_Call {
	_c.Call.Return(run)
	return _c
}

// GetFee provides a mock function with given fields: opts, destChainSelector, message
func (_m *EVM2EVMOnRampInterface) GetFee(opts *bind.CallOpts, destChainSelector uint64, message evm_2_evm_onramp.ClientEVM2AnyMessage) (*big.Int, error) {
	ret := _m.Called(opts, destChainSelector, message)

	if len(ret) == 0 {
		panic("no return value specified for GetFee")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, evm_2_evm_onramp.ClientEVM2AnyMessage) (*big.Int, error)); ok {
		return rf(opts, destChainSelector, message)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, evm_2_evm_onramp.ClientEVM2AnyMessage) *big.Int); ok {
		r0 = rf(opts, destChainSelector, message)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64, evm_2_evm_onramp.ClientEVM2AnyMessage) error); ok {
		r1 = rf(opts, destChainSelector, message)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetFee_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFee'
type EVM2EVMOnRampInterface_GetFee_Call struct {
	*mock.Call
}

// GetFee is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - destChainSelector uint64
//   - message evm_2_evm_onramp.ClientEVM2AnyMessage
func (_e *EVM2EVMOnRampInterface_Expecter) GetFee(opts interface{}, destChainSelector interface{}, message interface{}) *EVM2EVMOnRampInterface_GetFee_Call {
	return &EVM2EVMOnRampInterface_GetFee_Call{Call: _e.mock.On("GetFee", opts, destChainSelector, message)}
}

func (_c *EVM2EVMOnRampInterface_GetFee_Call) Run(run func(opts *bind.CallOpts, destChainSelector uint64, message evm_2_evm_onramp.ClientEVM2AnyMessage)) *EVM2EVMOnRampInterface_GetFee_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64), args[2].(evm_2_evm_onramp.ClientEVM2AnyMessage))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetFee_Call) Return(_a0 *big.Int, _a1 error) *EVM2EVMOnRampInterface_GetFee_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetFee_Call) RunAndReturn(run func(*bind.CallOpts, uint64, evm_2_evm_onramp.ClientEVM2AnyMessage) (*big.Int, error)) *EVM2EVMOnRampInterface_GetFee_Call {
	_c.Call.Return(run)
	return _c
}

// GetFeeTokenConfig provides a mock function with given fields: opts, token
func (_m *EVM2EVMOnRampInterface) GetFeeTokenConfig(opts *bind.CallOpts, token common.Address) (evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfig, error) {
	ret := _m.Called(opts, token)

	if len(ret) == 0 {
		panic("no return value specified for GetFeeTokenConfig")
	}

	var r0 evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfig, error)); ok {
		return rf(opts, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfig); ok {
		r0 = rf(opts, token)
	} else {
		r0 = ret.Get(0).(evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetFeeTokenConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFeeTokenConfig'
type EVM2EVMOnRampInterface_GetFeeTokenConfig_Call struct {
	*mock.Call
}

// GetFeeTokenConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - token common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) GetFeeTokenConfig(opts interface{}, token interface{}) *EVM2EVMOnRampInterface_GetFeeTokenConfig_Call {
	return &EVM2EVMOnRampInterface_GetFeeTokenConfig_Call{Call: _e.mock.On("GetFeeTokenConfig", opts, token)}
}

func (_c *EVM2EVMOnRampInterface_GetFeeTokenConfig_Call) Run(run func(opts *bind.CallOpts, token common.Address)) *EVM2EVMOnRampInterface_GetFeeTokenConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetFeeTokenConfig_Call) Return(_a0 evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfig, _a1 error) *EVM2EVMOnRampInterface_GetFeeTokenConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetFeeTokenConfig_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfig, error)) *EVM2EVMOnRampInterface_GetFeeTokenConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetNopFeesJuels provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) GetNopFeesJuels(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetNopFeesJuels")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetNopFeesJuels_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNopFeesJuels'
type EVM2EVMOnRampInterface_GetNopFeesJuels_Call struct {
	*mock.Call
}

// GetNopFeesJuels is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) GetNopFeesJuels(opts interface{}) *EVM2EVMOnRampInterface_GetNopFeesJuels_Call {
	return &EVM2EVMOnRampInterface_GetNopFeesJuels_Call{Call: _e.mock.On("GetNopFeesJuels", opts)}
}

func (_c *EVM2EVMOnRampInterface_GetNopFeesJuels_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_GetNopFeesJuels_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetNopFeesJuels_Call) Return(_a0 *big.Int, _a1 error) *EVM2EVMOnRampInterface_GetNopFeesJuels_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetNopFeesJuels_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *EVM2EVMOnRampInterface_GetNopFeesJuels_Call {
	_c.Call.Return(run)
	return _c
}

// GetNops provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) GetNops(opts *bind.CallOpts) (evm_2_evm_onramp.GetNops, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetNops")
	}

	var r0 evm_2_evm_onramp.GetNops
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (evm_2_evm_onramp.GetNops, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) evm_2_evm_onramp.GetNops); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(evm_2_evm_onramp.GetNops)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetNops_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNops'
type EVM2EVMOnRampInterface_GetNops_Call struct {
	*mock.Call
}

// GetNops is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) GetNops(opts interface{}) *EVM2EVMOnRampInterface_GetNops_Call {
	return &EVM2EVMOnRampInterface_GetNops_Call{Call: _e.mock.On("GetNops", opts)}
}

func (_c *EVM2EVMOnRampInterface_GetNops_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_GetNops_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetNops_Call) Return(_a0 evm_2_evm_onramp.GetNops, _a1 error) *EVM2EVMOnRampInterface_GetNops_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetNops_Call) RunAndReturn(run func(*bind.CallOpts) (evm_2_evm_onramp.GetNops, error)) *EVM2EVMOnRampInterface_GetNops_Call {
	_c.Call.Return(run)
	return _c
}

// GetPoolBySourceToken provides a mock function with given fields: opts, arg0, sourceToken
func (_m *EVM2EVMOnRampInterface) GetPoolBySourceToken(opts *bind.CallOpts, arg0 uint64, sourceToken common.Address) (common.Address, error) {
	ret := _m.Called(opts, arg0, sourceToken)

	if len(ret) == 0 {
		panic("no return value specified for GetPoolBySourceToken")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, common.Address) (common.Address, error)); ok {
		return rf(opts, arg0, sourceToken)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, common.Address) common.Address); ok {
		r0 = rf(opts, arg0, sourceToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64, common.Address) error); ok {
		r1 = rf(opts, arg0, sourceToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetPoolBySourceToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetPoolBySourceToken'
type EVM2EVMOnRampInterface_GetPoolBySourceToken_Call struct {
	*mock.Call
}

// GetPoolBySourceToken is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - arg0 uint64
//   - sourceToken common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) GetPoolBySourceToken(opts interface{}, arg0 interface{}, sourceToken interface{}) *EVM2EVMOnRampInterface_GetPoolBySourceToken_Call {
	return &EVM2EVMOnRampInterface_GetPoolBySourceToken_Call{Call: _e.mock.On("GetPoolBySourceToken", opts, arg0, sourceToken)}
}

func (_c *EVM2EVMOnRampInterface_GetPoolBySourceToken_Call) Run(run func(opts *bind.CallOpts, arg0 uint64, sourceToken common.Address)) *EVM2EVMOnRampInterface_GetPoolBySourceToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64), args[2].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetPoolBySourceToken_Call) Return(_a0 common.Address, _a1 error) *EVM2EVMOnRampInterface_GetPoolBySourceToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetPoolBySourceToken_Call) RunAndReturn(run func(*bind.CallOpts, uint64, common.Address) (common.Address, error)) *EVM2EVMOnRampInterface_GetPoolBySourceToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetSenderNonce provides a mock function with given fields: opts, sender
func (_m *EVM2EVMOnRampInterface) GetSenderNonce(opts *bind.CallOpts, sender common.Address) (uint64, error) {
	ret := _m.Called(opts, sender)

	if len(ret) == 0 {
		panic("no return value specified for GetSenderNonce")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (uint64, error)); ok {
		return rf(opts, sender)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) uint64); ok {
		r0 = rf(opts, sender)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, sender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetSenderNonce_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSenderNonce'
type EVM2EVMOnRampInterface_GetSenderNonce_Call struct {
	*mock.Call
}

// GetSenderNonce is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - sender common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) GetSenderNonce(opts interface{}, sender interface{}) *EVM2EVMOnRampInterface_GetSenderNonce_Call {
	return &EVM2EVMOnRampInterface_GetSenderNonce_Call{Call: _e.mock.On("GetSenderNonce", opts, sender)}
}

func (_c *EVM2EVMOnRampInterface_GetSenderNonce_Call) Run(run func(opts *bind.CallOpts, sender common.Address)) *EVM2EVMOnRampInterface_GetSenderNonce_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetSenderNonce_Call) Return(_a0 uint64, _a1 error) *EVM2EVMOnRampInterface_GetSenderNonce_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetSenderNonce_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (uint64, error)) *EVM2EVMOnRampInterface_GetSenderNonce_Call {
	_c.Call.Return(run)
	return _c
}

// GetStaticConfig provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) GetStaticConfig(opts *bind.CallOpts) (evm_2_evm_onramp.EVM2EVMOnRampStaticConfig, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetStaticConfig")
	}

	var r0 evm_2_evm_onramp.EVM2EVMOnRampStaticConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (evm_2_evm_onramp.EVM2EVMOnRampStaticConfig, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) evm_2_evm_onramp.EVM2EVMOnRampStaticConfig); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(evm_2_evm_onramp.EVM2EVMOnRampStaticConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetStaticConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStaticConfig'
type EVM2EVMOnRampInterface_GetStaticConfig_Call struct {
	*mock.Call
}

// GetStaticConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) GetStaticConfig(opts interface{}) *EVM2EVMOnRampInterface_GetStaticConfig_Call {
	return &EVM2EVMOnRampInterface_GetStaticConfig_Call{Call: _e.mock.On("GetStaticConfig", opts)}
}

func (_c *EVM2EVMOnRampInterface_GetStaticConfig_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_GetStaticConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetStaticConfig_Call) Return(_a0 evm_2_evm_onramp.EVM2EVMOnRampStaticConfig, _a1 error) *EVM2EVMOnRampInterface_GetStaticConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetStaticConfig_Call) RunAndReturn(run func(*bind.CallOpts) (evm_2_evm_onramp.EVM2EVMOnRampStaticConfig, error)) *EVM2EVMOnRampInterface_GetStaticConfig_Call {
	_c.Call.Return(run)
	return _c
}

// GetSupportedTokens provides a mock function with given fields: opts, arg0
func (_m *EVM2EVMOnRampInterface) GetSupportedTokens(opts *bind.CallOpts, arg0 uint64) ([]common.Address, error) {
	ret := _m.Called(opts, arg0)

	if len(ret) == 0 {
		panic("no return value specified for GetSupportedTokens")
	}

	var r0 []common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) ([]common.Address, error)); ok {
		return rf(opts, arg0)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) []common.Address); ok {
		r0 = rf(opts, arg0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, arg0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetSupportedTokens_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSupportedTokens'
type EVM2EVMOnRampInterface_GetSupportedTokens_Call struct {
	*mock.Call
}

// GetSupportedTokens is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - arg0 uint64
func (_e *EVM2EVMOnRampInterface_Expecter) GetSupportedTokens(opts interface{}, arg0 interface{}) *EVM2EVMOnRampInterface_GetSupportedTokens_Call {
	return &EVM2EVMOnRampInterface_GetSupportedTokens_Call{Call: _e.mock.On("GetSupportedTokens", opts, arg0)}
}

func (_c *EVM2EVMOnRampInterface_GetSupportedTokens_Call) Run(run func(opts *bind.CallOpts, arg0 uint64)) *EVM2EVMOnRampInterface_GetSupportedTokens_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetSupportedTokens_Call) Return(_a0 []common.Address, _a1 error) *EVM2EVMOnRampInterface_GetSupportedTokens_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetSupportedTokens_Call) RunAndReturn(run func(*bind.CallOpts, uint64) ([]common.Address, error)) *EVM2EVMOnRampInterface_GetSupportedTokens_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenLimitAdmin provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) GetTokenLimitAdmin(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenLimitAdmin")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenLimitAdmin'
type EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call struct {
	*mock.Call
}

// GetTokenLimitAdmin is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) GetTokenLimitAdmin(opts interface{}) *EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call {
	return &EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call{Call: _e.mock.On("GetTokenLimitAdmin", opts)}
}

func (_c *EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call) Return(_a0 common.Address, _a1 error) *EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *EVM2EVMOnRampInterface_GetTokenLimitAdmin_Call {
	_c.Call.Return(run)
	return _c
}

// GetTokenTransferFeeConfig provides a mock function with given fields: opts, token
func (_m *EVM2EVMOnRampInterface) GetTokenTransferFeeConfig(opts *bind.CallOpts, token common.Address) (evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfig, error) {
	ret := _m.Called(opts, token)

	if len(ret) == 0 {
		panic("no return value specified for GetTokenTransferFeeConfig")
	}

	var r0 evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfig
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfig, error)); ok {
		return rf(opts, token)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfig); ok {
		r0 = rf(opts, token)
	} else {
		r0 = ret.Get(0).(evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfig)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, token)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTokenTransferFeeConfig'
type EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call struct {
	*mock.Call
}

// GetTokenTransferFeeConfig is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - token common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) GetTokenTransferFeeConfig(opts interface{}, token interface{}) *EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call {
	return &EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call{Call: _e.mock.On("GetTokenTransferFeeConfig", opts, token)}
}

func (_c *EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call) Run(run func(opts *bind.CallOpts, token common.Address)) *EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call) Return(_a0 evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfig, _a1 error) *EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfig, error)) *EVM2EVMOnRampInterface_GetTokenTransferFeeConfig_Call {
	_c.Call.Return(run)
	return _c
}

// LinkAvailableForPayment provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) LinkAvailableForPayment(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LinkAvailableForPayment")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_LinkAvailableForPayment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LinkAvailableForPayment'
type EVM2EVMOnRampInterface_LinkAvailableForPayment_Call struct {
	*mock.Call
}

// LinkAvailableForPayment is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) LinkAvailableForPayment(opts interface{}) *EVM2EVMOnRampInterface_LinkAvailableForPayment_Call {
	return &EVM2EVMOnRampInterface_LinkAvailableForPayment_Call{Call: _e.mock.On("LinkAvailableForPayment", opts)}
}

func (_c *EVM2EVMOnRampInterface_LinkAvailableForPayment_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_LinkAvailableForPayment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_LinkAvailableForPayment_Call) Return(_a0 *big.Int, _a1 error) *EVM2EVMOnRampInterface_LinkAvailableForPayment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_LinkAvailableForPayment_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *EVM2EVMOnRampInterface_LinkAvailableForPayment_Call {
	_c.Call.Return(run)
	return _c
}

// Owner provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) Owner(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Owner")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_Owner_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Owner'
type EVM2EVMOnRampInterface_Owner_Call struct {
	*mock.Call
}

// Owner is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) Owner(opts interface{}) *EVM2EVMOnRampInterface_Owner_Call {
	return &EVM2EVMOnRampInterface_Owner_Call{Call: _e.mock.On("Owner", opts)}
}

func (_c *EVM2EVMOnRampInterface_Owner_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_Owner_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_Owner_Call) Return(_a0 common.Address, _a1 error) *EVM2EVMOnRampInterface_Owner_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_Owner_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *EVM2EVMOnRampInterface_Owner_Call {
	_c.Call.Return(run)
	return _c
}

// ParseAdminSet provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseAdminSet(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampAdminSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseAdminSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampAdminSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampAdminSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampAdminSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampAdminSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseAdminSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseAdminSet'
type EVM2EVMOnRampInterface_ParseAdminSet_Call struct {
	*mock.Call
}

// ParseAdminSet is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseAdminSet(log interface{}) *EVM2EVMOnRampInterface_ParseAdminSet_Call {
	return &EVM2EVMOnRampInterface_ParseAdminSet_Call{Call: _e.mock.On("ParseAdminSet", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseAdminSet_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseAdminSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseAdminSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampAdminSet, _a1 error) *EVM2EVMOnRampInterface_ParseAdminSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseAdminSet_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampAdminSet, error)) *EVM2EVMOnRampInterface_ParseAdminSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseCCIPSendRequested provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseCCIPSendRequested(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseCCIPSendRequested")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseCCIPSendRequested'
type EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call struct {
	*mock.Call
}

// ParseCCIPSendRequested is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseCCIPSendRequested(log interface{}) *EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call {
	return &EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call{Call: _e.mock.On("ParseCCIPSendRequested", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested, _a1 error) *EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested, error)) *EVM2EVMOnRampInterface_ParseCCIPSendRequested_Call {
	_c.Call.Return(run)
	return _c
}

// ParseConfigChanged provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseConfigChanged(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampConfigChanged, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseConfigChanged")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampConfigChanged, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampConfigChanged)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseConfigChanged_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseConfigChanged'
type EVM2EVMOnRampInterface_ParseConfigChanged_Call struct {
	*mock.Call
}

// ParseConfigChanged is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseConfigChanged(log interface{}) *EVM2EVMOnRampInterface_ParseConfigChanged_Call {
	return &EVM2EVMOnRampInterface_ParseConfigChanged_Call{Call: _e.mock.On("ParseConfigChanged", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseConfigChanged_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseConfigChanged_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseConfigChanged_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged, _a1 error) *EVM2EVMOnRampInterface_ParseConfigChanged_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseConfigChanged_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampConfigChanged, error)) *EVM2EVMOnRampInterface_ParseConfigChanged_Call {
	_c.Call.Return(run)
	return _c
}

// ParseConfigSet provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseConfigSet(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampConfigSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseConfigSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampConfigSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampConfigSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampConfigSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampConfigSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseConfigSet'
type EVM2EVMOnRampInterface_ParseConfigSet_Call struct {
	*mock.Call
}

// ParseConfigSet is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseConfigSet(log interface{}) *EVM2EVMOnRampInterface_ParseConfigSet_Call {
	return &EVM2EVMOnRampInterface_ParseConfigSet_Call{Call: _e.mock.On("ParseConfigSet", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseConfigSet_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseConfigSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampConfigSet, _a1 error) *EVM2EVMOnRampInterface_ParseConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseConfigSet_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampConfigSet, error)) *EVM2EVMOnRampInterface_ParseConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseFeeConfigSet provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseFeeConfigSet(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseFeeConfigSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseFeeConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseFeeConfigSet'
type EVM2EVMOnRampInterface_ParseFeeConfigSet_Call struct {
	*mock.Call
}

// ParseFeeConfigSet is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseFeeConfigSet(log interface{}) *EVM2EVMOnRampInterface_ParseFeeConfigSet_Call {
	return &EVM2EVMOnRampInterface_ParseFeeConfigSet_Call{Call: _e.mock.On("ParseFeeConfigSet", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseFeeConfigSet_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseFeeConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseFeeConfigSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet, _a1 error) *EVM2EVMOnRampInterface_ParseFeeConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseFeeConfigSet_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet, error)) *EVM2EVMOnRampInterface_ParseFeeConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type EVM2EVMOnRampInterface_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseLog(log interface{}) *EVM2EVMOnRampInterface_ParseLog_Call {
	return &EVM2EVMOnRampInterface_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseLog_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *EVM2EVMOnRampInterface_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *EVM2EVMOnRampInterface_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// ParseNopPaid provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseNopPaid(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampNopPaid, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseNopPaid")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampNopPaid
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampNopPaid, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampNopPaid); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampNopPaid)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseNopPaid_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseNopPaid'
type EVM2EVMOnRampInterface_ParseNopPaid_Call struct {
	*mock.Call
}

// ParseNopPaid is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseNopPaid(log interface{}) *EVM2EVMOnRampInterface_ParseNopPaid_Call {
	return &EVM2EVMOnRampInterface_ParseNopPaid_Call{Call: _e.mock.On("ParseNopPaid", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseNopPaid_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseNopPaid_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseNopPaid_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampNopPaid, _a1 error) *EVM2EVMOnRampInterface_ParseNopPaid_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseNopPaid_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampNopPaid, error)) *EVM2EVMOnRampInterface_ParseNopPaid_Call {
	_c.Call.Return(run)
	return _c
}

// ParseNopsSet provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseNopsSet(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampNopsSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseNopsSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampNopsSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampNopsSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampNopsSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampNopsSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseNopsSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseNopsSet'
type EVM2EVMOnRampInterface_ParseNopsSet_Call struct {
	*mock.Call
}

// ParseNopsSet is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseNopsSet(log interface{}) *EVM2EVMOnRampInterface_ParseNopsSet_Call {
	return &EVM2EVMOnRampInterface_ParseNopsSet_Call{Call: _e.mock.On("ParseNopsSet", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseNopsSet_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseNopsSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseNopsSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampNopsSet, _a1 error) *EVM2EVMOnRampInterface_ParseNopsSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseNopsSet_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampNopsSet, error)) *EVM2EVMOnRampInterface_ParseNopsSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferRequested provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseOwnershipTransferRequested(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferRequested")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferRequested'
type EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call struct {
	*mock.Call
}

// ParseOwnershipTransferRequested is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseOwnershipTransferRequested(log interface{}) *EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call {
	return &EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call{Call: _e.mock.On("ParseOwnershipTransferRequested", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, _a1 error) *EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, error)) *EVM2EVMOnRampInterface_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferred provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseOwnershipTransferred(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferred")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferred'
type EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call struct {
	*mock.Call
}

// ParseOwnershipTransferred is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseOwnershipTransferred(log interface{}) *EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call {
	return &EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call{Call: _e.mock.On("ParseOwnershipTransferred", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, _a1 error) *EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, error)) *EVM2EVMOnRampInterface_ParseOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// ParseTokenTransferFeeConfigDeleted provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseTokenTransferFeeConfigDeleted(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseTokenTransferFeeConfigDeleted")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseTokenTransferFeeConfigDeleted'
type EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call struct {
	*mock.Call
}

// ParseTokenTransferFeeConfigDeleted is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseTokenTransferFeeConfigDeleted(log interface{}) *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call {
	return &EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call{Call: _e.mock.On("ParseTokenTransferFeeConfigDeleted", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted, _a1 error) *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted, error)) *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(run)
	return _c
}

// ParseTokenTransferFeeConfigSet provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseTokenTransferFeeConfigSet(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseTokenTransferFeeConfigSet")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseTokenTransferFeeConfigSet'
type EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call struct {
	*mock.Call
}

// ParseTokenTransferFeeConfigSet is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseTokenTransferFeeConfigSet(log interface{}) *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call {
	return &EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call{Call: _e.mock.On("ParseTokenTransferFeeConfigSet", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet, _a1 error) *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet, error)) *EVM2EVMOnRampInterface_ParseTokenTransferFeeConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// ParseTokensConsumed provides a mock function with given fields: log
func (_m *EVM2EVMOnRampInterface) ParseTokensConsumed(log types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseTokensConsumed")
	}

	var r0 *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_ParseTokensConsumed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseTokensConsumed'
type EVM2EVMOnRampInterface_ParseTokensConsumed_Call struct {
	*mock.Call
}

// ParseTokensConsumed is a helper method to define mock.On call
//   - log types.Log
func (_e *EVM2EVMOnRampInterface_Expecter) ParseTokensConsumed(log interface{}) *EVM2EVMOnRampInterface_ParseTokensConsumed_Call {
	return &EVM2EVMOnRampInterface_ParseTokensConsumed_Call{Call: _e.mock.On("ParseTokensConsumed", log)}
}

func (_c *EVM2EVMOnRampInterface_ParseTokensConsumed_Call) Run(run func(log types.Log)) *EVM2EVMOnRampInterface_ParseTokensConsumed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseTokensConsumed_Call) Return(_a0 *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed, _a1 error) *EVM2EVMOnRampInterface_ParseTokensConsumed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_ParseTokensConsumed_Call) RunAndReturn(run func(types.Log) (*evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed, error)) *EVM2EVMOnRampInterface_ParseTokensConsumed_Call {
	_c.Call.Return(run)
	return _c
}

// PayNops provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) PayNops(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for PayNops")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_PayNops_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PayNops'
type EVM2EVMOnRampInterface_PayNops_Call struct {
	*mock.Call
}

// PayNops is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *EVM2EVMOnRampInterface_Expecter) PayNops(opts interface{}) *EVM2EVMOnRampInterface_PayNops_Call {
	return &EVM2EVMOnRampInterface_PayNops_Call{Call: _e.mock.On("PayNops", opts)}
}

func (_c *EVM2EVMOnRampInterface_PayNops_Call) Run(run func(opts *bind.TransactOpts)) *EVM2EVMOnRampInterface_PayNops_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_PayNops_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_PayNops_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_PayNops_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *EVM2EVMOnRampInterface_PayNops_Call {
	_c.Call.Return(run)
	return _c
}

// SetAdmin provides a mock function with given fields: opts, newAdmin
func (_m *EVM2EVMOnRampInterface) SetAdmin(opts *bind.TransactOpts, newAdmin common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, newAdmin)

	if len(ret) == 0 {
		panic("no return value specified for SetAdmin")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, newAdmin)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, newAdmin)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, newAdmin)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_SetAdmin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetAdmin'
type EVM2EVMOnRampInterface_SetAdmin_Call struct {
	*mock.Call
}

// SetAdmin is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - newAdmin common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) SetAdmin(opts interface{}, newAdmin interface{}) *EVM2EVMOnRampInterface_SetAdmin_Call {
	return &EVM2EVMOnRampInterface_SetAdmin_Call{Call: _e.mock.On("SetAdmin", opts, newAdmin)}
}

func (_c *EVM2EVMOnRampInterface_SetAdmin_Call) Run(run func(opts *bind.TransactOpts, newAdmin common.Address)) *EVM2EVMOnRampInterface_SetAdmin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetAdmin_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_SetAdmin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetAdmin_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *EVM2EVMOnRampInterface_SetAdmin_Call {
	_c.Call.Return(run)
	return _c
}

// SetDynamicConfig provides a mock function with given fields: opts, dynamicConfig
func (_m *EVM2EVMOnRampInterface) SetDynamicConfig(opts *bind.TransactOpts, dynamicConfig evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig) (*types.Transaction, error) {
	ret := _m.Called(opts, dynamicConfig)

	if len(ret) == 0 {
		panic("no return value specified for SetDynamicConfig")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig) (*types.Transaction, error)); ok {
		return rf(opts, dynamicConfig)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig) *types.Transaction); ok {
		r0 = rf(opts, dynamicConfig)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig) error); ok {
		r1 = rf(opts, dynamicConfig)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_SetDynamicConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetDynamicConfig'
type EVM2EVMOnRampInterface_SetDynamicConfig_Call struct {
	*mock.Call
}

// SetDynamicConfig is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - dynamicConfig evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig
func (_e *EVM2EVMOnRampInterface_Expecter) SetDynamicConfig(opts interface{}, dynamicConfig interface{}) *EVM2EVMOnRampInterface_SetDynamicConfig_Call {
	return &EVM2EVMOnRampInterface_SetDynamicConfig_Call{Call: _e.mock.On("SetDynamicConfig", opts, dynamicConfig)}
}

func (_c *EVM2EVMOnRampInterface_SetDynamicConfig_Call) Run(run func(opts *bind.TransactOpts, dynamicConfig evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig)) *EVM2EVMOnRampInterface_SetDynamicConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetDynamicConfig_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_SetDynamicConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetDynamicConfig_Call) RunAndReturn(run func(*bind.TransactOpts, evm_2_evm_onramp.EVM2EVMOnRampDynamicConfig) (*types.Transaction, error)) *EVM2EVMOnRampInterface_SetDynamicConfig_Call {
	_c.Call.Return(run)
	return _c
}

// SetFeeTokenConfig provides a mock function with given fields: opts, feeTokenConfigArgs
func (_m *EVM2EVMOnRampInterface) SetFeeTokenConfig(opts *bind.TransactOpts, feeTokenConfigArgs []evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfigArgs) (*types.Transaction, error) {
	ret := _m.Called(opts, feeTokenConfigArgs)

	if len(ret) == 0 {
		panic("no return value specified for SetFeeTokenConfig")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfigArgs) (*types.Transaction, error)); ok {
		return rf(opts, feeTokenConfigArgs)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfigArgs) *types.Transaction); ok {
		r0 = rf(opts, feeTokenConfigArgs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfigArgs) error); ok {
		r1 = rf(opts, feeTokenConfigArgs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_SetFeeTokenConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetFeeTokenConfig'
type EVM2EVMOnRampInterface_SetFeeTokenConfig_Call struct {
	*mock.Call
}

// SetFeeTokenConfig is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - feeTokenConfigArgs []evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfigArgs
func (_e *EVM2EVMOnRampInterface_Expecter) SetFeeTokenConfig(opts interface{}, feeTokenConfigArgs interface{}) *EVM2EVMOnRampInterface_SetFeeTokenConfig_Call {
	return &EVM2EVMOnRampInterface_SetFeeTokenConfig_Call{Call: _e.mock.On("SetFeeTokenConfig", opts, feeTokenConfigArgs)}
}

func (_c *EVM2EVMOnRampInterface_SetFeeTokenConfig_Call) Run(run func(opts *bind.TransactOpts, feeTokenConfigArgs []evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfigArgs)) *EVM2EVMOnRampInterface_SetFeeTokenConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfigArgs))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetFeeTokenConfig_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_SetFeeTokenConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetFeeTokenConfig_Call) RunAndReturn(run func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampFeeTokenConfigArgs) (*types.Transaction, error)) *EVM2EVMOnRampInterface_SetFeeTokenConfig_Call {
	_c.Call.Return(run)
	return _c
}

// SetNops provides a mock function with given fields: opts, nopsAndWeights
func (_m *EVM2EVMOnRampInterface) SetNops(opts *bind.TransactOpts, nopsAndWeights []evm_2_evm_onramp.EVM2EVMOnRampNopAndWeight) (*types.Transaction, error) {
	ret := _m.Called(opts, nopsAndWeights)

	if len(ret) == 0 {
		panic("no return value specified for SetNops")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampNopAndWeight) (*types.Transaction, error)); ok {
		return rf(opts, nopsAndWeights)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampNopAndWeight) *types.Transaction); ok {
		r0 = rf(opts, nopsAndWeights)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampNopAndWeight) error); ok {
		r1 = rf(opts, nopsAndWeights)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_SetNops_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetNops'
type EVM2EVMOnRampInterface_SetNops_Call struct {
	*mock.Call
}

// SetNops is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - nopsAndWeights []evm_2_evm_onramp.EVM2EVMOnRampNopAndWeight
func (_e *EVM2EVMOnRampInterface_Expecter) SetNops(opts interface{}, nopsAndWeights interface{}) *EVM2EVMOnRampInterface_SetNops_Call {
	return &EVM2EVMOnRampInterface_SetNops_Call{Call: _e.mock.On("SetNops", opts, nopsAndWeights)}
}

func (_c *EVM2EVMOnRampInterface_SetNops_Call) Run(run func(opts *bind.TransactOpts, nopsAndWeights []evm_2_evm_onramp.EVM2EVMOnRampNopAndWeight)) *EVM2EVMOnRampInterface_SetNops_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]evm_2_evm_onramp.EVM2EVMOnRampNopAndWeight))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetNops_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_SetNops_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetNops_Call) RunAndReturn(run func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampNopAndWeight) (*types.Transaction, error)) *EVM2EVMOnRampInterface_SetNops_Call {
	_c.Call.Return(run)
	return _c
}

// SetRateLimiterConfig provides a mock function with given fields: opts, config
func (_m *EVM2EVMOnRampInterface) SetRateLimiterConfig(opts *bind.TransactOpts, config evm_2_evm_onramp.RateLimiterConfig) (*types.Transaction, error) {
	ret := _m.Called(opts, config)

	if len(ret) == 0 {
		panic("no return value specified for SetRateLimiterConfig")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_onramp.RateLimiterConfig) (*types.Transaction, error)); ok {
		return rf(opts, config)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, evm_2_evm_onramp.RateLimiterConfig) *types.Transaction); ok {
		r0 = rf(opts, config)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, evm_2_evm_onramp.RateLimiterConfig) error); ok {
		r1 = rf(opts, config)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_SetRateLimiterConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetRateLimiterConfig'
type EVM2EVMOnRampInterface_SetRateLimiterConfig_Call struct {
	*mock.Call
}

// SetRateLimiterConfig is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - config evm_2_evm_onramp.RateLimiterConfig
func (_e *EVM2EVMOnRampInterface_Expecter) SetRateLimiterConfig(opts interface{}, config interface{}) *EVM2EVMOnRampInterface_SetRateLimiterConfig_Call {
	return &EVM2EVMOnRampInterface_SetRateLimiterConfig_Call{Call: _e.mock.On("SetRateLimiterConfig", opts, config)}
}

func (_c *EVM2EVMOnRampInterface_SetRateLimiterConfig_Call) Run(run func(opts *bind.TransactOpts, config evm_2_evm_onramp.RateLimiterConfig)) *EVM2EVMOnRampInterface_SetRateLimiterConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(evm_2_evm_onramp.RateLimiterConfig))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetRateLimiterConfig_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_SetRateLimiterConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetRateLimiterConfig_Call) RunAndReturn(run func(*bind.TransactOpts, evm_2_evm_onramp.RateLimiterConfig) (*types.Transaction, error)) *EVM2EVMOnRampInterface_SetRateLimiterConfig_Call {
	_c.Call.Return(run)
	return _c
}

// SetTokenTransferFeeConfig provides a mock function with given fields: opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs
func (_m *EVM2EVMOnRampInterface) SetTokenTransferFeeConfig(opts *bind.TransactOpts, tokenTransferFeeConfigArgs []evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs []common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)

	if len(ret) == 0 {
		panic("no return value specified for SetTokenTransferFeeConfig")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigArgs, []common.Address) (*types.Transaction, error)); ok {
		return rf(opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigArgs, []common.Address) *types.Transaction); ok {
		r0 = rf(opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigArgs, []common.Address) error); ok {
		r1 = rf(opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetTokenTransferFeeConfig'
type EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call struct {
	*mock.Call
}

// SetTokenTransferFeeConfig is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - tokenTransferFeeConfigArgs []evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigArgs
//   - tokensToUseDefaultFeeConfigs []common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) SetTokenTransferFeeConfig(opts interface{}, tokenTransferFeeConfigArgs interface{}, tokensToUseDefaultFeeConfigs interface{}) *EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call {
	return &EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call{Call: _e.mock.On("SetTokenTransferFeeConfig", opts, tokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs)}
}

func (_c *EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call) Run(run func(opts *bind.TransactOpts, tokenTransferFeeConfigArgs []evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigArgs, tokensToUseDefaultFeeConfigs []common.Address)) *EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigArgs), args[2].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call) RunAndReturn(run func(*bind.TransactOpts, []evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigArgs, []common.Address) (*types.Transaction, error)) *EVM2EVMOnRampInterface_SetTokenTransferFeeConfig_Call {
	_c.Call.Return(run)
	return _c
}

// TransferOwnership provides a mock function with given fields: opts, to
func (_m *EVM2EVMOnRampInterface) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, to)

	if len(ret) == 0 {
		panic("no return value specified for TransferOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_TransferOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransferOwnership'
type EVM2EVMOnRampInterface_TransferOwnership_Call struct {
	*mock.Call
}

// TransferOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - to common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) TransferOwnership(opts interface{}, to interface{}) *EVM2EVMOnRampInterface_TransferOwnership_Call {
	return &EVM2EVMOnRampInterface_TransferOwnership_Call{Call: _e.mock.On("TransferOwnership", opts, to)}
}

func (_c *EVM2EVMOnRampInterface_TransferOwnership_Call) Run(run func(opts *bind.TransactOpts, to common.Address)) *EVM2EVMOnRampInterface_TransferOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_TransferOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_TransferOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_TransferOwnership_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *EVM2EVMOnRampInterface_TransferOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// TypeAndVersion provides a mock function with given fields: opts
func (_m *EVM2EVMOnRampInterface) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for TypeAndVersion")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (string, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) string); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_TypeAndVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TypeAndVersion'
type EVM2EVMOnRampInterface_TypeAndVersion_Call struct {
	*mock.Call
}

// TypeAndVersion is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *EVM2EVMOnRampInterface_Expecter) TypeAndVersion(opts interface{}) *EVM2EVMOnRampInterface_TypeAndVersion_Call {
	return &EVM2EVMOnRampInterface_TypeAndVersion_Call{Call: _e.mock.On("TypeAndVersion", opts)}
}

func (_c *EVM2EVMOnRampInterface_TypeAndVersion_Call) Run(run func(opts *bind.CallOpts)) *EVM2EVMOnRampInterface_TypeAndVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_TypeAndVersion_Call) Return(_a0 string, _a1 error) *EVM2EVMOnRampInterface_TypeAndVersion_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_TypeAndVersion_Call) RunAndReturn(run func(*bind.CallOpts) (string, error)) *EVM2EVMOnRampInterface_TypeAndVersion_Call {
	_c.Call.Return(run)
	return _c
}

// WatchAdminSet provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOnRampInterface) WatchAdminSet(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampAdminSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchAdminSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampAdminSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampAdminSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampAdminSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchAdminSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchAdminSet'
type EVM2EVMOnRampInterface_WatchAdminSet_Call struct {
	*mock.Call
}

// WatchAdminSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampAdminSet
func (_e *EVM2EVMOnRampInterface_Expecter) WatchAdminSet(opts interface{}, sink interface{}) *EVM2EVMOnRampInterface_WatchAdminSet_Call {
	return &EVM2EVMOnRampInterface_WatchAdminSet_Call{Call: _e.mock.On("WatchAdminSet", opts, sink)}
}

func (_c *EVM2EVMOnRampInterface_WatchAdminSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampAdminSet)) *EVM2EVMOnRampInterface_WatchAdminSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampAdminSet))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchAdminSet_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchAdminSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchAdminSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampAdminSet) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchAdminSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchCCIPSendRequested provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOnRampInterface) WatchCCIPSendRequested(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchCCIPSendRequested")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchCCIPSendRequested'
type EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call struct {
	*mock.Call
}

// WatchCCIPSendRequested is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested
func (_e *EVM2EVMOnRampInterface_Expecter) WatchCCIPSendRequested(opts interface{}, sink interface{}) *EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call {
	return &EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call{Call: _e.mock.On("WatchCCIPSendRequested", opts, sink)}
}

func (_c *EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested)) *EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampCCIPSendRequested) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchCCIPSendRequested_Call {
	_c.Call.Return(run)
	return _c
}

// WatchConfigChanged provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOnRampInterface) WatchConfigChanged(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchConfigChanged")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchConfigChanged_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchConfigChanged'
type EVM2EVMOnRampInterface_WatchConfigChanged_Call struct {
	*mock.Call
}

// WatchConfigChanged is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged
func (_e *EVM2EVMOnRampInterface_Expecter) WatchConfigChanged(opts interface{}, sink interface{}) *EVM2EVMOnRampInterface_WatchConfigChanged_Call {
	return &EVM2EVMOnRampInterface_WatchConfigChanged_Call{Call: _e.mock.On("WatchConfigChanged", opts, sink)}
}

func (_c *EVM2EVMOnRampInterface_WatchConfigChanged_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged)) *EVM2EVMOnRampInterface_WatchConfigChanged_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchConfigChanged_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchConfigChanged_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchConfigChanged_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigChanged) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchConfigChanged_Call {
	_c.Call.Return(run)
	return _c
}

// WatchConfigSet provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOnRampInterface) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchConfigSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchConfigSet'
type EVM2EVMOnRampInterface_WatchConfigSet_Call struct {
	*mock.Call
}

// WatchConfigSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigSet
func (_e *EVM2EVMOnRampInterface_Expecter) WatchConfigSet(opts interface{}, sink interface{}) *EVM2EVMOnRampInterface_WatchConfigSet_Call {
	return &EVM2EVMOnRampInterface_WatchConfigSet_Call{Call: _e.mock.On("WatchConfigSet", opts, sink)}
}

func (_c *EVM2EVMOnRampInterface_WatchConfigSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigSet)) *EVM2EVMOnRampInterface_WatchConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigSet))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchConfigSet_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchConfigSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampConfigSet) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchFeeConfigSet provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOnRampInterface) WatchFeeConfigSet(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchFeeConfigSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchFeeConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchFeeConfigSet'
type EVM2EVMOnRampInterface_WatchFeeConfigSet_Call struct {
	*mock.Call
}

// WatchFeeConfigSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet
func (_e *EVM2EVMOnRampInterface_Expecter) WatchFeeConfigSet(opts interface{}, sink interface{}) *EVM2EVMOnRampInterface_WatchFeeConfigSet_Call {
	return &EVM2EVMOnRampInterface_WatchFeeConfigSet_Call{Call: _e.mock.On("WatchFeeConfigSet", opts, sink)}
}

func (_c *EVM2EVMOnRampInterface_WatchFeeConfigSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet)) *EVM2EVMOnRampInterface_WatchFeeConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchFeeConfigSet_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchFeeConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchFeeConfigSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampFeeConfigSet) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchFeeConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchNopPaid provides a mock function with given fields: opts, sink, nop
func (_m *EVM2EVMOnRampInterface) WatchNopPaid(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopPaid, nop []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, nop)

	if len(ret) == 0 {
		panic("no return value specified for WatchNopPaid")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopPaid, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, nop)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopPaid, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, nop)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopPaid, []common.Address) error); ok {
		r1 = rf(opts, sink, nop)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchNopPaid_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchNopPaid'
type EVM2EVMOnRampInterface_WatchNopPaid_Call struct {
	*mock.Call
}

// WatchNopPaid is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopPaid
//   - nop []common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) WatchNopPaid(opts interface{}, sink interface{}, nop interface{}) *EVM2EVMOnRampInterface_WatchNopPaid_Call {
	return &EVM2EVMOnRampInterface_WatchNopPaid_Call{Call: _e.mock.On("WatchNopPaid", opts, sink, nop)}
}

func (_c *EVM2EVMOnRampInterface_WatchNopPaid_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopPaid, nop []common.Address)) *EVM2EVMOnRampInterface_WatchNopPaid_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopPaid), args[2].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchNopPaid_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchNopPaid_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchNopPaid_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopPaid, []common.Address) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchNopPaid_Call {
	_c.Call.Return(run)
	return _c
}

// WatchNopsSet provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOnRampInterface) WatchNopsSet(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopsSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchNopsSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopsSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopsSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopsSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchNopsSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchNopsSet'
type EVM2EVMOnRampInterface_WatchNopsSet_Call struct {
	*mock.Call
}

// WatchNopsSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopsSet
func (_e *EVM2EVMOnRampInterface_Expecter) WatchNopsSet(opts interface{}, sink interface{}) *EVM2EVMOnRampInterface_WatchNopsSet_Call {
	return &EVM2EVMOnRampInterface_WatchNopsSet_Call{Call: _e.mock.On("WatchNopsSet", opts, sink)}
}

func (_c *EVM2EVMOnRampInterface_WatchNopsSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopsSet)) *EVM2EVMOnRampInterface_WatchNopsSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopsSet))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchNopsSet_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchNopsSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchNopsSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampNopsSet) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchNopsSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferRequested provides a mock function with given fields: opts, sink, from, to
func (_m *EVM2EVMOnRampInterface) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferRequested")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferRequested'
type EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call struct {
	*mock.Call
}

// WatchOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested
//   - from []common.Address
//   - to []common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) WatchOwnershipTransferRequested(opts interface{}, sink interface{}, from interface{}, to interface{}) *EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call {
	return &EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call{Call: _e.mock.On("WatchOwnershipTransferRequested", opts, sink, from, to)}
}

func (_c *EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, from []common.Address, to []common.Address)) *EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferred provides a mock function with given fields: opts, sink, from, to
func (_m *EVM2EVMOnRampInterface) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferred")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferred'
type EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call struct {
	*mock.Call
}

// WatchOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred
//   - from []common.Address
//   - to []common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) WatchOwnershipTransferred(opts interface{}, sink interface{}, from interface{}, to interface{}) *EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call {
	return &EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call{Call: _e.mock.On("WatchOwnershipTransferred", opts, sink, from, to)}
}

func (_c *EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, from []common.Address, to []common.Address)) *EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// WatchTokenTransferFeeConfigDeleted provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOnRampInterface) WatchTokenTransferFeeConfigDeleted(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchTokenTransferFeeConfigDeleted")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchTokenTransferFeeConfigDeleted'
type EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call struct {
	*mock.Call
}

// WatchTokenTransferFeeConfigDeleted is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted
func (_e *EVM2EVMOnRampInterface_Expecter) WatchTokenTransferFeeConfigDeleted(opts interface{}, sink interface{}) *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call {
	return &EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call{Call: _e.mock.On("WatchTokenTransferFeeConfigDeleted", opts, sink)}
}

func (_c *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted)) *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigDeleted) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigDeleted_Call {
	_c.Call.Return(run)
	return _c
}

// WatchTokenTransferFeeConfigSet provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOnRampInterface) WatchTokenTransferFeeConfigSet(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchTokenTransferFeeConfigSet")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchTokenTransferFeeConfigSet'
type EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call struct {
	*mock.Call
}

// WatchTokenTransferFeeConfigSet is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet
func (_e *EVM2EVMOnRampInterface_Expecter) WatchTokenTransferFeeConfigSet(opts interface{}, sink interface{}) *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call {
	return &EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call{Call: _e.mock.On("WatchTokenTransferFeeConfigSet", opts, sink)}
}

func (_c *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet)) *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokenTransferFeeConfigSet) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchTokenTransferFeeConfigSet_Call {
	_c.Call.Return(run)
	return _c
}

// WatchTokensConsumed provides a mock function with given fields: opts, sink
func (_m *EVM2EVMOnRampInterface) WatchTokensConsumed(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchTokensConsumed")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WatchTokensConsumed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchTokensConsumed'
type EVM2EVMOnRampInterface_WatchTokensConsumed_Call struct {
	*mock.Call
}

// WatchTokensConsumed is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed
func (_e *EVM2EVMOnRampInterface_Expecter) WatchTokensConsumed(opts interface{}, sink interface{}) *EVM2EVMOnRampInterface_WatchTokensConsumed_Call {
	return &EVM2EVMOnRampInterface_WatchTokensConsumed_Call{Call: _e.mock.On("WatchTokensConsumed", opts, sink)}
}

func (_c *EVM2EVMOnRampInterface_WatchTokensConsumed_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed)) *EVM2EVMOnRampInterface_WatchTokensConsumed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchTokensConsumed_Call) Return(_a0 event.Subscription, _a1 error) *EVM2EVMOnRampInterface_WatchTokensConsumed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WatchTokensConsumed_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *evm_2_evm_onramp.EVM2EVMOnRampTokensConsumed) (event.Subscription, error)) *EVM2EVMOnRampInterface_WatchTokensConsumed_Call {
	_c.Call.Return(run)
	return _c
}

// WithdrawNonLinkFees provides a mock function with given fields: opts, feeToken, to
func (_m *EVM2EVMOnRampInterface) WithdrawNonLinkFees(opts *bind.TransactOpts, feeToken common.Address, to common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, feeToken, to)

	if len(ret) == 0 {
		panic("no return value specified for WithdrawNonLinkFees")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, feeToken, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, common.Address) *types.Transaction); ok {
		r0 = rf(opts, feeToken, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, common.Address) error); ok {
		r1 = rf(opts, feeToken, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithdrawNonLinkFees'
type EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call struct {
	*mock.Call
}

// WithdrawNonLinkFees is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - feeToken common.Address
//   - to common.Address
func (_e *EVM2EVMOnRampInterface_Expecter) WithdrawNonLinkFees(opts interface{}, feeToken interface{}, to interface{}) *EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call {
	return &EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call{Call: _e.mock.On("WithdrawNonLinkFees", opts, feeToken, to)}
}

func (_c *EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call) Run(run func(opts *bind.TransactOpts, feeToken common.Address, to common.Address)) *EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(common.Address))
	})
	return _c
}

func (_c *EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call) Return(_a0 *types.Transaction, _a1 error) *EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, common.Address) (*types.Transaction, error)) *EVM2EVMOnRampInterface_WithdrawNonLinkFees_Call {
	_c.Call.Return(run)
	return _c
}

// NewEVM2EVMOnRampInterface creates a new instance of EVM2EVMOnRampInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewEVM2EVMOnRampInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *EVM2EVMOnRampInterface {
	mock := &EVM2EVMOnRampInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
