// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_contracts

import (
	big "math/big"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	event "github.com/ethereum/go-ethereum/event"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	link_token_interface "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated/link_token_interface"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// LinkTokenInterface is an autogenerated mock type for the LinkTokenInterface type
type LinkTokenInterface struct {
	mock.Mock
}

type LinkTokenInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *LinkTokenInterface) EXPECT() *LinkTokenInterface_Expecter {
	return &LinkTokenInterface_Expecter{mock: &_m.Mock}
}

// Address provides a mock function with given fields:
func (_m *LinkTokenInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// LinkTokenInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type LinkTokenInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *LinkTokenInterface_Expecter) Address() *LinkTokenInterface_Address_Call {
	return &LinkTokenInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *LinkTokenInterface_Address_Call) Run(run func()) *LinkTokenInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LinkTokenInterface_Address_Call) Return(_a0 common.Address) *LinkTokenInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *LinkTokenInterface_Address_Call) RunAndReturn(run func() common.Address) *LinkTokenInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// Allowance provides a mock function with given fields: opts, _owner, _spender
func (_m *LinkTokenInterface) Allowance(opts *bind.CallOpts, _owner common.Address, _spender common.Address) (*big.Int, error) {
	ret := _m.Called(opts, _owner, _spender)

	if len(ret) == 0 {
		panic("no return value specified for Allowance")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, common.Address) (*big.Int, error)); ok {
		return rf(opts, _owner, _spender)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, common.Address) *big.Int); ok {
		r0 = rf(opts, _owner, _spender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address, common.Address) error); ok {
		r1 = rf(opts, _owner, _spender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_Allowance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Allowance'
type LinkTokenInterface_Allowance_Call struct {
	*mock.Call
}

// Allowance is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - _owner common.Address
//   - _spender common.Address
func (_e *LinkTokenInterface_Expecter) Allowance(opts interface{}, _owner interface{}, _spender interface{}) *LinkTokenInterface_Allowance_Call {
	return &LinkTokenInterface_Allowance_Call{Call: _e.mock.On("Allowance", opts, _owner, _spender)}
}

func (_c *LinkTokenInterface_Allowance_Call) Run(run func(opts *bind.CallOpts, _owner common.Address, _spender common.Address)) *LinkTokenInterface_Allowance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address), args[2].(common.Address))
	})
	return _c
}

func (_c *LinkTokenInterface_Allowance_Call) Return(_a0 *big.Int, _a1 error) *LinkTokenInterface_Allowance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_Allowance_Call) RunAndReturn(run func(*bind.CallOpts, common.Address, common.Address) (*big.Int, error)) *LinkTokenInterface_Allowance_Call {
	_c.Call.Return(run)
	return _c
}

// Approve provides a mock function with given fields: opts, _spender, _value
func (_m *LinkTokenInterface) Approve(opts *bind.TransactOpts, _spender common.Address, _value *big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, _spender, _value)

	if len(ret) == 0 {
		panic("no return value specified for Approve")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)); ok {
		return rf(opts, _spender, _value)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) *types.Transaction); ok {
		r0 = rf(opts, _spender, _value)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, *big.Int) error); ok {
		r1 = rf(opts, _spender, _value)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_Approve_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Approve'
type LinkTokenInterface_Approve_Call struct {
	*mock.Call
}

// Approve is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _spender common.Address
//   - _value *big.Int
func (_e *LinkTokenInterface_Expecter) Approve(opts interface{}, _spender interface{}, _value interface{}) *LinkTokenInterface_Approve_Call {
	return &LinkTokenInterface_Approve_Call{Call: _e.mock.On("Approve", opts, _spender, _value)}
}

func (_c *LinkTokenInterface_Approve_Call) Run(run func(opts *bind.TransactOpts, _spender common.Address, _value *big.Int)) *LinkTokenInterface_Approve_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *LinkTokenInterface_Approve_Call) Return(_a0 *types.Transaction, _a1 error) *LinkTokenInterface_Approve_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_Approve_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)) *LinkTokenInterface_Approve_Call {
	_c.Call.Return(run)
	return _c
}

// BalanceOf provides a mock function with given fields: opts, _owner
func (_m *LinkTokenInterface) BalanceOf(opts *bind.CallOpts, _owner common.Address) (*big.Int, error) {
	ret := _m.Called(opts, _owner)

	if len(ret) == 0 {
		panic("no return value specified for BalanceOf")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (*big.Int, error)); ok {
		return rf(opts, _owner)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) *big.Int); ok {
		r0 = rf(opts, _owner)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, _owner)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_BalanceOf_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BalanceOf'
type LinkTokenInterface_BalanceOf_Call struct {
	*mock.Call
}

// BalanceOf is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - _owner common.Address
func (_e *LinkTokenInterface_Expecter) BalanceOf(opts interface{}, _owner interface{}) *LinkTokenInterface_BalanceOf_Call {
	return &LinkTokenInterface_BalanceOf_Call{Call: _e.mock.On("BalanceOf", opts, _owner)}
}

func (_c *LinkTokenInterface_BalanceOf_Call) Run(run func(opts *bind.CallOpts, _owner common.Address)) *LinkTokenInterface_BalanceOf_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *LinkTokenInterface_BalanceOf_Call) Return(_a0 *big.Int, _a1 error) *LinkTokenInterface_BalanceOf_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_BalanceOf_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (*big.Int, error)) *LinkTokenInterface_BalanceOf_Call {
	_c.Call.Return(run)
	return _c
}

// Decimals provides a mock function with given fields: opts
func (_m *LinkTokenInterface) Decimals(opts *bind.CallOpts) (uint8, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Decimals")
	}

	var r0 uint8
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint8, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint8); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint8)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_Decimals_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Decimals'
type LinkTokenInterface_Decimals_Call struct {
	*mock.Call
}

// Decimals is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *LinkTokenInterface_Expecter) Decimals(opts interface{}) *LinkTokenInterface_Decimals_Call {
	return &LinkTokenInterface_Decimals_Call{Call: _e.mock.On("Decimals", opts)}
}

func (_c *LinkTokenInterface_Decimals_Call) Run(run func(opts *bind.CallOpts)) *LinkTokenInterface_Decimals_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *LinkTokenInterface_Decimals_Call) Return(_a0 uint8, _a1 error) *LinkTokenInterface_Decimals_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_Decimals_Call) RunAndReturn(run func(*bind.CallOpts) (uint8, error)) *LinkTokenInterface_Decimals_Call {
	_c.Call.Return(run)
	return _c
}

// DecreaseApproval provides a mock function with given fields: opts, _spender, _subtractedValue
func (_m *LinkTokenInterface) DecreaseApproval(opts *bind.TransactOpts, _spender common.Address, _subtractedValue *big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, _spender, _subtractedValue)

	if len(ret) == 0 {
		panic("no return value specified for DecreaseApproval")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)); ok {
		return rf(opts, _spender, _subtractedValue)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) *types.Transaction); ok {
		r0 = rf(opts, _spender, _subtractedValue)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, *big.Int) error); ok {
		r1 = rf(opts, _spender, _subtractedValue)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_DecreaseApproval_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DecreaseApproval'
type LinkTokenInterface_DecreaseApproval_Call struct {
	*mock.Call
}

// DecreaseApproval is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _spender common.Address
//   - _subtractedValue *big.Int
func (_e *LinkTokenInterface_Expecter) DecreaseApproval(opts interface{}, _spender interface{}, _subtractedValue interface{}) *LinkTokenInterface_DecreaseApproval_Call {
	return &LinkTokenInterface_DecreaseApproval_Call{Call: _e.mock.On("DecreaseApproval", opts, _spender, _subtractedValue)}
}

func (_c *LinkTokenInterface_DecreaseApproval_Call) Run(run func(opts *bind.TransactOpts, _spender common.Address, _subtractedValue *big.Int)) *LinkTokenInterface_DecreaseApproval_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *LinkTokenInterface_DecreaseApproval_Call) Return(_a0 *types.Transaction, _a1 error) *LinkTokenInterface_DecreaseApproval_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_DecreaseApproval_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)) *LinkTokenInterface_DecreaseApproval_Call {
	_c.Call.Return(run)
	return _c
}

// FilterApproval provides a mock function with given fields: opts, owner, spender
func (_m *LinkTokenInterface) FilterApproval(opts *bind.FilterOpts, owner []common.Address, spender []common.Address) (*link_token_interface.LinkTokenApprovalIterator, error) {
	ret := _m.Called(opts, owner, spender)

	if len(ret) == 0 {
		panic("no return value specified for FilterApproval")
	}

	var r0 *link_token_interface.LinkTokenApprovalIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*link_token_interface.LinkTokenApprovalIterator, error)); ok {
		return rf(opts, owner, spender)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *link_token_interface.LinkTokenApprovalIterator); ok {
		r0 = rf(opts, owner, spender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*link_token_interface.LinkTokenApprovalIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, owner, spender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_FilterApproval_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterApproval'
type LinkTokenInterface_FilterApproval_Call struct {
	*mock.Call
}

// FilterApproval is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - owner []common.Address
//   - spender []common.Address
func (_e *LinkTokenInterface_Expecter) FilterApproval(opts interface{}, owner interface{}, spender interface{}) *LinkTokenInterface_FilterApproval_Call {
	return &LinkTokenInterface_FilterApproval_Call{Call: _e.mock.On("FilterApproval", opts, owner, spender)}
}

func (_c *LinkTokenInterface_FilterApproval_Call) Run(run func(opts *bind.FilterOpts, owner []common.Address, spender []common.Address)) *LinkTokenInterface_FilterApproval_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *LinkTokenInterface_FilterApproval_Call) Return(_a0 *link_token_interface.LinkTokenApprovalIterator, _a1 error) *LinkTokenInterface_FilterApproval_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_FilterApproval_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*link_token_interface.LinkTokenApprovalIterator, error)) *LinkTokenInterface_FilterApproval_Call {
	_c.Call.Return(run)
	return _c
}

// FilterTransfer provides a mock function with given fields: opts, from, to
func (_m *LinkTokenInterface) FilterTransfer(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*link_token_interface.LinkTokenTransferIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterTransfer")
	}

	var r0 *link_token_interface.LinkTokenTransferIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*link_token_interface.LinkTokenTransferIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *link_token_interface.LinkTokenTransferIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*link_token_interface.LinkTokenTransferIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_FilterTransfer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterTransfer'
type LinkTokenInterface_FilterTransfer_Call struct {
	*mock.Call
}

// FilterTransfer is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *LinkTokenInterface_Expecter) FilterTransfer(opts interface{}, from interface{}, to interface{}) *LinkTokenInterface_FilterTransfer_Call {
	return &LinkTokenInterface_FilterTransfer_Call{Call: _e.mock.On("FilterTransfer", opts, from, to)}
}

func (_c *LinkTokenInterface_FilterTransfer_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *LinkTokenInterface_FilterTransfer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *LinkTokenInterface_FilterTransfer_Call) Return(_a0 *link_token_interface.LinkTokenTransferIterator, _a1 error) *LinkTokenInterface_FilterTransfer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_FilterTransfer_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*link_token_interface.LinkTokenTransferIterator, error)) *LinkTokenInterface_FilterTransfer_Call {
	_c.Call.Return(run)
	return _c
}

// IncreaseApproval provides a mock function with given fields: opts, _spender, _addedValue
func (_m *LinkTokenInterface) IncreaseApproval(opts *bind.TransactOpts, _spender common.Address, _addedValue *big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, _spender, _addedValue)

	if len(ret) == 0 {
		panic("no return value specified for IncreaseApproval")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)); ok {
		return rf(opts, _spender, _addedValue)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) *types.Transaction); ok {
		r0 = rf(opts, _spender, _addedValue)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, *big.Int) error); ok {
		r1 = rf(opts, _spender, _addedValue)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_IncreaseApproval_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncreaseApproval'
type LinkTokenInterface_IncreaseApproval_Call struct {
	*mock.Call
}

// IncreaseApproval is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _spender common.Address
//   - _addedValue *big.Int
func (_e *LinkTokenInterface_Expecter) IncreaseApproval(opts interface{}, _spender interface{}, _addedValue interface{}) *LinkTokenInterface_IncreaseApproval_Call {
	return &LinkTokenInterface_IncreaseApproval_Call{Call: _e.mock.On("IncreaseApproval", opts, _spender, _addedValue)}
}

func (_c *LinkTokenInterface_IncreaseApproval_Call) Run(run func(opts *bind.TransactOpts, _spender common.Address, _addedValue *big.Int)) *LinkTokenInterface_IncreaseApproval_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *LinkTokenInterface_IncreaseApproval_Call) Return(_a0 *types.Transaction, _a1 error) *LinkTokenInterface_IncreaseApproval_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_IncreaseApproval_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)) *LinkTokenInterface_IncreaseApproval_Call {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with given fields: opts
func (_m *LinkTokenInterface) Name(opts *bind.CallOpts) (string, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (string, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) string); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type LinkTokenInterface_Name_Call struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *LinkTokenInterface_Expecter) Name(opts interface{}) *LinkTokenInterface_Name_Call {
	return &LinkTokenInterface_Name_Call{Call: _e.mock.On("Name", opts)}
}

func (_c *LinkTokenInterface_Name_Call) Run(run func(opts *bind.CallOpts)) *LinkTokenInterface_Name_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *LinkTokenInterface_Name_Call) Return(_a0 string, _a1 error) *LinkTokenInterface_Name_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_Name_Call) RunAndReturn(run func(*bind.CallOpts) (string, error)) *LinkTokenInterface_Name_Call {
	_c.Call.Return(run)
	return _c
}

// ParseApproval provides a mock function with given fields: log
func (_m *LinkTokenInterface) ParseApproval(log types.Log) (*link_token_interface.LinkTokenApproval, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseApproval")
	}

	var r0 *link_token_interface.LinkTokenApproval
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*link_token_interface.LinkTokenApproval, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *link_token_interface.LinkTokenApproval); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*link_token_interface.LinkTokenApproval)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_ParseApproval_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseApproval'
type LinkTokenInterface_ParseApproval_Call struct {
	*mock.Call
}

// ParseApproval is a helper method to define mock.On call
//   - log types.Log
func (_e *LinkTokenInterface_Expecter) ParseApproval(log interface{}) *LinkTokenInterface_ParseApproval_Call {
	return &LinkTokenInterface_ParseApproval_Call{Call: _e.mock.On("ParseApproval", log)}
}

func (_c *LinkTokenInterface_ParseApproval_Call) Run(run func(log types.Log)) *LinkTokenInterface_ParseApproval_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *LinkTokenInterface_ParseApproval_Call) Return(_a0 *link_token_interface.LinkTokenApproval, _a1 error) *LinkTokenInterface_ParseApproval_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_ParseApproval_Call) RunAndReturn(run func(types.Log) (*link_token_interface.LinkTokenApproval, error)) *LinkTokenInterface_ParseApproval_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *LinkTokenInterface) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type LinkTokenInterface_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *LinkTokenInterface_Expecter) ParseLog(log interface{}) *LinkTokenInterface_ParseLog_Call {
	return &LinkTokenInterface_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *LinkTokenInterface_ParseLog_Call) Run(run func(log types.Log)) *LinkTokenInterface_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *LinkTokenInterface_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *LinkTokenInterface_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *LinkTokenInterface_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// ParseTransfer provides a mock function with given fields: log
func (_m *LinkTokenInterface) ParseTransfer(log types.Log) (*link_token_interface.LinkTokenTransfer, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseTransfer")
	}

	var r0 *link_token_interface.LinkTokenTransfer
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*link_token_interface.LinkTokenTransfer, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *link_token_interface.LinkTokenTransfer); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*link_token_interface.LinkTokenTransfer)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_ParseTransfer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseTransfer'
type LinkTokenInterface_ParseTransfer_Call struct {
	*mock.Call
}

// ParseTransfer is a helper method to define mock.On call
//   - log types.Log
func (_e *LinkTokenInterface_Expecter) ParseTransfer(log interface{}) *LinkTokenInterface_ParseTransfer_Call {
	return &LinkTokenInterface_ParseTransfer_Call{Call: _e.mock.On("ParseTransfer", log)}
}

func (_c *LinkTokenInterface_ParseTransfer_Call) Run(run func(log types.Log)) *LinkTokenInterface_ParseTransfer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *LinkTokenInterface_ParseTransfer_Call) Return(_a0 *link_token_interface.LinkTokenTransfer, _a1 error) *LinkTokenInterface_ParseTransfer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_ParseTransfer_Call) RunAndReturn(run func(types.Log) (*link_token_interface.LinkTokenTransfer, error)) *LinkTokenInterface_ParseTransfer_Call {
	_c.Call.Return(run)
	return _c
}

// Symbol provides a mock function with given fields: opts
func (_m *LinkTokenInterface) Symbol(opts *bind.CallOpts) (string, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Symbol")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (string, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) string); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_Symbol_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Symbol'
type LinkTokenInterface_Symbol_Call struct {
	*mock.Call
}

// Symbol is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *LinkTokenInterface_Expecter) Symbol(opts interface{}) *LinkTokenInterface_Symbol_Call {
	return &LinkTokenInterface_Symbol_Call{Call: _e.mock.On("Symbol", opts)}
}

func (_c *LinkTokenInterface_Symbol_Call) Run(run func(opts *bind.CallOpts)) *LinkTokenInterface_Symbol_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *LinkTokenInterface_Symbol_Call) Return(_a0 string, _a1 error) *LinkTokenInterface_Symbol_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_Symbol_Call) RunAndReturn(run func(*bind.CallOpts) (string, error)) *LinkTokenInterface_Symbol_Call {
	_c.Call.Return(run)
	return _c
}

// TotalSupply provides a mock function with given fields: opts
func (_m *LinkTokenInterface) TotalSupply(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for TotalSupply")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_TotalSupply_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TotalSupply'
type LinkTokenInterface_TotalSupply_Call struct {
	*mock.Call
}

// TotalSupply is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *LinkTokenInterface_Expecter) TotalSupply(opts interface{}) *LinkTokenInterface_TotalSupply_Call {
	return &LinkTokenInterface_TotalSupply_Call{Call: _e.mock.On("TotalSupply", opts)}
}

func (_c *LinkTokenInterface_TotalSupply_Call) Run(run func(opts *bind.CallOpts)) *LinkTokenInterface_TotalSupply_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *LinkTokenInterface_TotalSupply_Call) Return(_a0 *big.Int, _a1 error) *LinkTokenInterface_TotalSupply_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_TotalSupply_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *LinkTokenInterface_TotalSupply_Call {
	_c.Call.Return(run)
	return _c
}

// Transfer provides a mock function with given fields: opts, _to, _value
func (_m *LinkTokenInterface) Transfer(opts *bind.TransactOpts, _to common.Address, _value *big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, _to, _value)

	if len(ret) == 0 {
		panic("no return value specified for Transfer")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)); ok {
		return rf(opts, _to, _value)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int) *types.Transaction); ok {
		r0 = rf(opts, _to, _value)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, *big.Int) error); ok {
		r1 = rf(opts, _to, _value)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_Transfer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transfer'
type LinkTokenInterface_Transfer_Call struct {
	*mock.Call
}

// Transfer is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _to common.Address
//   - _value *big.Int
func (_e *LinkTokenInterface_Expecter) Transfer(opts interface{}, _to interface{}, _value interface{}) *LinkTokenInterface_Transfer_Call {
	return &LinkTokenInterface_Transfer_Call{Call: _e.mock.On("Transfer", opts, _to, _value)}
}

func (_c *LinkTokenInterface_Transfer_Call) Run(run func(opts *bind.TransactOpts, _to common.Address, _value *big.Int)) *LinkTokenInterface_Transfer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *LinkTokenInterface_Transfer_Call) Return(_a0 *types.Transaction, _a1 error) *LinkTokenInterface_Transfer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_Transfer_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, *big.Int) (*types.Transaction, error)) *LinkTokenInterface_Transfer_Call {
	_c.Call.Return(run)
	return _c
}

// TransferAndCall provides a mock function with given fields: opts, _to, _value, _data
func (_m *LinkTokenInterface) TransferAndCall(opts *bind.TransactOpts, _to common.Address, _value *big.Int, _data []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, _to, _value, _data)

	if len(ret) == 0 {
		panic("no return value specified for TransferAndCall")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int, []byte) (*types.Transaction, error)); ok {
		return rf(opts, _to, _value, _data)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, *big.Int, []byte) *types.Transaction); ok {
		r0 = rf(opts, _to, _value, _data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, *big.Int, []byte) error); ok {
		r1 = rf(opts, _to, _value, _data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_TransferAndCall_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransferAndCall'
type LinkTokenInterface_TransferAndCall_Call struct {
	*mock.Call
}

// TransferAndCall is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _to common.Address
//   - _value *big.Int
//   - _data []byte
func (_e *LinkTokenInterface_Expecter) TransferAndCall(opts interface{}, _to interface{}, _value interface{}, _data interface{}) *LinkTokenInterface_TransferAndCall_Call {
	return &LinkTokenInterface_TransferAndCall_Call{Call: _e.mock.On("TransferAndCall", opts, _to, _value, _data)}
}

func (_c *LinkTokenInterface_TransferAndCall_Call) Run(run func(opts *bind.TransactOpts, _to common.Address, _value *big.Int, _data []byte)) *LinkTokenInterface_TransferAndCall_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(*big.Int), args[3].([]byte))
	})
	return _c
}

func (_c *LinkTokenInterface_TransferAndCall_Call) Return(_a0 *types.Transaction, _a1 error) *LinkTokenInterface_TransferAndCall_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_TransferAndCall_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, *big.Int, []byte) (*types.Transaction, error)) *LinkTokenInterface_TransferAndCall_Call {
	_c.Call.Return(run)
	return _c
}

// TransferFrom provides a mock function with given fields: opts, _from, _to, _value
func (_m *LinkTokenInterface) TransferFrom(opts *bind.TransactOpts, _from common.Address, _to common.Address, _value *big.Int) (*types.Transaction, error) {
	ret := _m.Called(opts, _from, _to, _value)

	if len(ret) == 0 {
		panic("no return value specified for TransferFrom")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, common.Address, *big.Int) (*types.Transaction, error)); ok {
		return rf(opts, _from, _to, _value)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, common.Address, *big.Int) *types.Transaction); ok {
		r0 = rf(opts, _from, _to, _value)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, common.Address, *big.Int) error); ok {
		r1 = rf(opts, _from, _to, _value)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_TransferFrom_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransferFrom'
type LinkTokenInterface_TransferFrom_Call struct {
	*mock.Call
}

// TransferFrom is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _from common.Address
//   - _to common.Address
//   - _value *big.Int
func (_e *LinkTokenInterface_Expecter) TransferFrom(opts interface{}, _from interface{}, _to interface{}, _value interface{}) *LinkTokenInterface_TransferFrom_Call {
	return &LinkTokenInterface_TransferFrom_Call{Call: _e.mock.On("TransferFrom", opts, _from, _to, _value)}
}

func (_c *LinkTokenInterface_TransferFrom_Call) Run(run func(opts *bind.TransactOpts, _from common.Address, _to common.Address, _value *big.Int)) *LinkTokenInterface_TransferFrom_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(common.Address), args[3].(*big.Int))
	})
	return _c
}

func (_c *LinkTokenInterface_TransferFrom_Call) Return(_a0 *types.Transaction, _a1 error) *LinkTokenInterface_TransferFrom_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_TransferFrom_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, common.Address, *big.Int) (*types.Transaction, error)) *LinkTokenInterface_TransferFrom_Call {
	_c.Call.Return(run)
	return _c
}

// WatchApproval provides a mock function with given fields: opts, sink, owner, spender
func (_m *LinkTokenInterface) WatchApproval(opts *bind.WatchOpts, sink chan<- *link_token_interface.LinkTokenApproval, owner []common.Address, spender []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, owner, spender)

	if len(ret) == 0 {
		panic("no return value specified for WatchApproval")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *link_token_interface.LinkTokenApproval, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, owner, spender)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *link_token_interface.LinkTokenApproval, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, owner, spender)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *link_token_interface.LinkTokenApproval, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, owner, spender)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_WatchApproval_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchApproval'
type LinkTokenInterface_WatchApproval_Call struct {
	*mock.Call
}

// WatchApproval is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *link_token_interface.LinkTokenApproval
//   - owner []common.Address
//   - spender []common.Address
func (_e *LinkTokenInterface_Expecter) WatchApproval(opts interface{}, sink interface{}, owner interface{}, spender interface{}) *LinkTokenInterface_WatchApproval_Call {
	return &LinkTokenInterface_WatchApproval_Call{Call: _e.mock.On("WatchApproval", opts, sink, owner, spender)}
}

func (_c *LinkTokenInterface_WatchApproval_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *link_token_interface.LinkTokenApproval, owner []common.Address, spender []common.Address)) *LinkTokenInterface_WatchApproval_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *link_token_interface.LinkTokenApproval), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *LinkTokenInterface_WatchApproval_Call) Return(_a0 event.Subscription, _a1 error) *LinkTokenInterface_WatchApproval_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_WatchApproval_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *link_token_interface.LinkTokenApproval, []common.Address, []common.Address) (event.Subscription, error)) *LinkTokenInterface_WatchApproval_Call {
	_c.Call.Return(run)
	return _c
}

// WatchTransfer provides a mock function with given fields: opts, sink, from, to
func (_m *LinkTokenInterface) WatchTransfer(opts *bind.WatchOpts, sink chan<- *link_token_interface.LinkTokenTransfer, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchTransfer")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *link_token_interface.LinkTokenTransfer, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *link_token_interface.LinkTokenTransfer, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *link_token_interface.LinkTokenTransfer, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LinkTokenInterface_WatchTransfer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchTransfer'
type LinkTokenInterface_WatchTransfer_Call struct {
	*mock.Call
}

// WatchTransfer is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *link_token_interface.LinkTokenTransfer
//   - from []common.Address
//   - to []common.Address
func (_e *LinkTokenInterface_Expecter) WatchTransfer(opts interface{}, sink interface{}, from interface{}, to interface{}) *LinkTokenInterface_WatchTransfer_Call {
	return &LinkTokenInterface_WatchTransfer_Call{Call: _e.mock.On("WatchTransfer", opts, sink, from, to)}
}

func (_c *LinkTokenInterface_WatchTransfer_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *link_token_interface.LinkTokenTransfer, from []common.Address, to []common.Address)) *LinkTokenInterface_WatchTransfer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *link_token_interface.LinkTokenTransfer), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *LinkTokenInterface_WatchTransfer_Call) Return(_a0 event.Subscription, _a1 error) *LinkTokenInterface_WatchTransfer_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LinkTokenInterface_WatchTransfer_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *link_token_interface.LinkTokenTransfer, []common.Address, []common.Address) (event.Subscription, error)) *LinkTokenInterface_WatchTransfer_Call {
	_c.Call.Return(run)
	return _c
}

// NewLinkTokenInterface creates a new instance of LinkTokenInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLinkTokenInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *LinkTokenInterface {
	mock := &LinkTokenInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
