// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package commit_store_helper

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
	"github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

type CommitStoreCommitReport struct {
	PriceUpdates InternalPriceUpdates
	Interval     CommitStoreInterval
	MerkleRoot   [32]byte
}

type CommitStoreDynamicConfig struct {
	PriceRegistry common.Address
}

type CommitStoreInterval struct {
	Min uint64
	Max uint64
}

type CommitStoreStaticConfig struct {
	ChainSelector       uint64
	SourceChainSelector uint64
	OnRamp              common.Address
	RmnProxy            common.Address
}

type InternalGasPriceUpdate struct {
	DestChainSelector uint64
	UsdPerUnitGas     *big.Int
}

type InternalPriceUpdates struct {
	TokenPriceUpdates []InternalTokenPriceUpdate
	GasPriceUpdates   []InternalGasPriceUpdate
}

type InternalTokenPriceUpdate struct {
	SourceToken common.Address
	UsdPerToken *big.Int
}

var CommitStoreHelperMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"sourceChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"onRamp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"rmnProxy\",\"type\":\"address\"}],\"internalType\":\"structCommitStore.StaticConfig\",\"name\":\"staticConfig\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"expected\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"actual\",\"type\":\"bytes32\"}],\"name\":\"ConfigDigestMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"CursedByRMN\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"expected\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"actual\",\"type\":\"uint256\"}],\"name\":\"ForkedChain\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidCommitStoreConfig\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"enumOCR2Base.InvalidConfigErrorType\",\"name\":\"errorType\",\"type\":\"uint8\"}],\"name\":\"InvalidConfig\",\"type\":\"error\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"min\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"max\",\"type\":\"uint64\"}],\"internalType\":\"structCommitStore.Interval\",\"name\":\"interval\",\"type\":\"tuple\"}],\"name\":\"InvalidInterval\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidProof\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidRoot\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LeavesCannotBeEmpty\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NonUniqueSignatures\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OracleCannotBeZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PausedError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RootAlreadyCommitted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SignaturesOutOfRegistration\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StaleReport\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedSigner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedTransmitter\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"expected\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"actual\",\"type\":\"uint256\"}],\"name\":\"WrongMessageLength\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"WrongNumberOfSignatures\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"sourceChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"onRamp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"rmnProxy\",\"type\":\"address\"}],\"indexed\":false,\"internalType\":\"structCommitStore.StaticConfig\",\"name\":\"staticConfig\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"priceRegistry\",\"type\":\"address\"}],\"indexed\":false,\"internalType\":\"structCommitStore.DynamicConfig\",\"name\":\"dynamicConfig\",\"type\":\"tuple\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"previousConfigBlockNumber\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"configCount\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"signers\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"transmitters\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"f\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"onchainConfig\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"oldEpochAndRound\",\"type\":\"uint40\"},{\"indexed\":false,\"internalType\":\"uint40\",\"name\":\"newEpochAndRound\",\"type\":\"uint40\"}],\"name\":\"LatestPriceEpochAndRoundSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"sourceToken\",\"type\":\"address\"},{\"internalType\":\"uint224\",\"name\":\"usdPerToken\",\"type\":\"uint224\"}],\"internalType\":\"structInternal.TokenPriceUpdate[]\",\"name\":\"tokenPriceUpdates\",\"type\":\"tuple[]\"},{\"components\":[{\"internalType\":\"uint64\",\"name\":\"destChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint224\",\"name\":\"usdPerUnitGas\",\"type\":\"uint224\"}],\"internalType\":\"structInternal.GasPriceUpdate[]\",\"name\":\"gasPriceUpdates\",\"type\":\"tuple[]\"}],\"internalType\":\"structInternal.PriceUpdates\",\"name\":\"priceUpdates\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint64\",\"name\":\"min\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"max\",\"type\":\"uint64\"}],\"internalType\":\"structCommitStore.Interval\",\"name\":\"interval\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"merkleRoot\",\"type\":\"bytes32\"}],\"indexed\":false,\"internalType\":\"structCommitStore.CommitReport\",\"name\":\"report\",\"type\":\"tuple\"}],\"name\":\"ReportAccepted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"}],\"name\":\"RootRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"oldSeqNum\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"newSeqNum\",\"type\":\"uint64\"}],\"name\":\"SequenceNumberSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"name\":\"Transmitted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDynamicConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"priceRegistry\",\"type\":\"address\"}],\"internalType\":\"structCommitStore.DynamicConfig\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getExpectedNextSequenceNumber\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getLatestPriceEpochAndRound\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"}],\"name\":\"getMerkleRoot\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getStaticConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"sourceChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"onRamp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"rmnProxy\",\"type\":\"address\"}],\"internalType\":\"structCommitStore.StaticConfig\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTransmitters\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"}],\"name\":\"isBlessed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isUnpausedAndNotCursed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestConfigDetails\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"configCount\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"blockNumber\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestConfigDigestAndEpoch\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"scanLogs\",\"type\":\"bool\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"commitReport\",\"type\":\"bytes\"},{\"internalType\":\"uint40\",\"name\":\"epochAndRound\",\"type\":\"uint40\"}],\"name\":\"report\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"rootToReset\",\"type\":\"bytes32[]\"}],\"name\":\"resetUnblessedRoots\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint40\",\"name\":\"latestPriceEpochAndRound\",\"type\":\"uint40\"}],\"name\":\"setLatestPriceEpochAndRound\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint64\",\"name\":\"minSeqNr\",\"type\":\"uint64\"}],\"name\":\"setMinSeqNr\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"signers\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"transmitters\",\"type\":\"address[]\"},{\"internalType\":\"uint8\",\"name\":\"f\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"onchainConfig\",\"type\":\"bytes\"},{\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"name\":\"setOCR2Config\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[3]\",\"name\":\"reportContext\",\"type\":\"bytes32[3]\"},{\"internalType\":\"bytes\",\"name\":\"report\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"rs\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"ss\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32\",\"name\":\"rawVs\",\"type\":\"bytes32\"}],\"name\":\"transmit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"typeAndVersion\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"hashedLeaves\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"proofs\",\"type\":\"bytes32[]\"},{\"internalType\":\"uint256\",\"name\":\"proofFlagBits\",\"type\":\"uint256\"}],\"name\":\"verify\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}]",
	Bin: "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",
}

var CommitStoreHelperABI = CommitStoreHelperMetaData.ABI

var CommitStoreHelperBin = CommitStoreHelperMetaData.Bin

func DeployCommitStoreHelper(auth *bind.TransactOpts, backend bind.ContractBackend, staticConfig CommitStoreStaticConfig) (common.Address, *types.Transaction, *CommitStoreHelper, error) {
	parsed, err := CommitStoreHelperMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(CommitStoreHelperBin), backend, staticConfig)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &CommitStoreHelper{address: address, abi: *parsed, CommitStoreHelperCaller: CommitStoreHelperCaller{contract: contract}, CommitStoreHelperTransactor: CommitStoreHelperTransactor{contract: contract}, CommitStoreHelperFilterer: CommitStoreHelperFilterer{contract: contract}}, nil
}

type CommitStoreHelper struct {
	address common.Address
	abi     abi.ABI
	CommitStoreHelperCaller
	CommitStoreHelperTransactor
	CommitStoreHelperFilterer
}

type CommitStoreHelperCaller struct {
	contract *bind.BoundContract
}

type CommitStoreHelperTransactor struct {
	contract *bind.BoundContract
}

type CommitStoreHelperFilterer struct {
	contract *bind.BoundContract
}

type CommitStoreHelperSession struct {
	Contract     *CommitStoreHelper
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type CommitStoreHelperCallerSession struct {
	Contract *CommitStoreHelperCaller
	CallOpts bind.CallOpts
}

type CommitStoreHelperTransactorSession struct {
	Contract     *CommitStoreHelperTransactor
	TransactOpts bind.TransactOpts
}

type CommitStoreHelperRaw struct {
	Contract *CommitStoreHelper
}

type CommitStoreHelperCallerRaw struct {
	Contract *CommitStoreHelperCaller
}

type CommitStoreHelperTransactorRaw struct {
	Contract *CommitStoreHelperTransactor
}

func NewCommitStoreHelper(address common.Address, backend bind.ContractBackend) (*CommitStoreHelper, error) {
	abi, err := abi.JSON(strings.NewReader(CommitStoreHelperABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindCommitStoreHelper(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelper{address: address, abi: abi, CommitStoreHelperCaller: CommitStoreHelperCaller{contract: contract}, CommitStoreHelperTransactor: CommitStoreHelperTransactor{contract: contract}, CommitStoreHelperFilterer: CommitStoreHelperFilterer{contract: contract}}, nil
}

func NewCommitStoreHelperCaller(address common.Address, caller bind.ContractCaller) (*CommitStoreHelperCaller, error) {
	contract, err := bindCommitStoreHelper(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperCaller{contract: contract}, nil
}

func NewCommitStoreHelperTransactor(address common.Address, transactor bind.ContractTransactor) (*CommitStoreHelperTransactor, error) {
	contract, err := bindCommitStoreHelper(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperTransactor{contract: contract}, nil
}

func NewCommitStoreHelperFilterer(address common.Address, filterer bind.ContractFilterer) (*CommitStoreHelperFilterer, error) {
	contract, err := bindCommitStoreHelper(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperFilterer{contract: contract}, nil
}

func bindCommitStoreHelper(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := CommitStoreHelperMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_CommitStoreHelper *CommitStoreHelperRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CommitStoreHelper.Contract.CommitStoreHelperCaller.contract.Call(opts, result, method, params...)
}

func (_CommitStoreHelper *CommitStoreHelperRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.CommitStoreHelperTransactor.contract.Transfer(opts)
}

func (_CommitStoreHelper *CommitStoreHelperRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.CommitStoreHelperTransactor.contract.Transact(opts, method, params...)
}

func (_CommitStoreHelper *CommitStoreHelperCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CommitStoreHelper.Contract.contract.Call(opts, result, method, params...)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.contract.Transfer(opts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.contract.Transact(opts, method, params...)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetDynamicConfig(opts *bind.CallOpts) (CommitStoreDynamicConfig, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getDynamicConfig")

	if err != nil {
		return *new(CommitStoreDynamicConfig), err
	}

	out0 := *abi.ConvertType(out[0], new(CommitStoreDynamicConfig)).(*CommitStoreDynamicConfig)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetDynamicConfig() (CommitStoreDynamicConfig, error) {
	return _CommitStoreHelper.Contract.GetDynamicConfig(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetDynamicConfig() (CommitStoreDynamicConfig, error) {
	return _CommitStoreHelper.Contract.GetDynamicConfig(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetExpectedNextSequenceNumber(opts *bind.CallOpts) (uint64, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getExpectedNextSequenceNumber")

	if err != nil {
		return *new(uint64), err
	}

	out0 := *abi.ConvertType(out[0], new(uint64)).(*uint64)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetExpectedNextSequenceNumber() (uint64, error) {
	return _CommitStoreHelper.Contract.GetExpectedNextSequenceNumber(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetExpectedNextSequenceNumber() (uint64, error) {
	return _CommitStoreHelper.Contract.GetExpectedNextSequenceNumber(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetLatestPriceEpochAndRound(opts *bind.CallOpts) (uint64, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getLatestPriceEpochAndRound")

	if err != nil {
		return *new(uint64), err
	}

	out0 := *abi.ConvertType(out[0], new(uint64)).(*uint64)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetLatestPriceEpochAndRound() (uint64, error) {
	return _CommitStoreHelper.Contract.GetLatestPriceEpochAndRound(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetLatestPriceEpochAndRound() (uint64, error) {
	return _CommitStoreHelper.Contract.GetLatestPriceEpochAndRound(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetMerkleRoot(opts *bind.CallOpts, root [32]byte) (*big.Int, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getMerkleRoot", root)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetMerkleRoot(root [32]byte) (*big.Int, error) {
	return _CommitStoreHelper.Contract.GetMerkleRoot(&_CommitStoreHelper.CallOpts, root)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetMerkleRoot(root [32]byte) (*big.Int, error) {
	return _CommitStoreHelper.Contract.GetMerkleRoot(&_CommitStoreHelper.CallOpts, root)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetStaticConfig(opts *bind.CallOpts) (CommitStoreStaticConfig, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getStaticConfig")

	if err != nil {
		return *new(CommitStoreStaticConfig), err
	}

	out0 := *abi.ConvertType(out[0], new(CommitStoreStaticConfig)).(*CommitStoreStaticConfig)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetStaticConfig() (CommitStoreStaticConfig, error) {
	return _CommitStoreHelper.Contract.GetStaticConfig(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetStaticConfig() (CommitStoreStaticConfig, error) {
	return _CommitStoreHelper.Contract.GetStaticConfig(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetTransmitters(opts *bind.CallOpts) ([]common.Address, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getTransmitters")

	if err != nil {
		return *new([]common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new([]common.Address)).(*[]common.Address)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetTransmitters() ([]common.Address, error) {
	return _CommitStoreHelper.Contract.GetTransmitters(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetTransmitters() ([]common.Address, error) {
	return _CommitStoreHelper.Contract.GetTransmitters(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) IsBlessed(opts *bind.CallOpts, root [32]byte) (bool, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "isBlessed", root)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) IsBlessed(root [32]byte) (bool, error) {
	return _CommitStoreHelper.Contract.IsBlessed(&_CommitStoreHelper.CallOpts, root)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) IsBlessed(root [32]byte) (bool, error) {
	return _CommitStoreHelper.Contract.IsBlessed(&_CommitStoreHelper.CallOpts, root)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) IsUnpausedAndNotCursed(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "isUnpausedAndNotCursed")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) IsUnpausedAndNotCursed() (bool, error) {
	return _CommitStoreHelper.Contract.IsUnpausedAndNotCursed(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) IsUnpausedAndNotCursed() (bool, error) {
	return _CommitStoreHelper.Contract.IsUnpausedAndNotCursed(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) LatestConfigDetails(opts *bind.CallOpts) (LatestConfigDetails,

	error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "latestConfigDetails")

	outstruct := new(LatestConfigDetails)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ConfigCount = *abi.ConvertType(out[0], new(uint32)).(*uint32)
	outstruct.BlockNumber = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.ConfigDigest = *abi.ConvertType(out[2], new([32]byte)).(*[32]byte)

	return *outstruct, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) LatestConfigDetails() (LatestConfigDetails,

	error) {
	return _CommitStoreHelper.Contract.LatestConfigDetails(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) LatestConfigDetails() (LatestConfigDetails,

	error) {
	return _CommitStoreHelper.Contract.LatestConfigDetails(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) LatestConfigDigestAndEpoch(opts *bind.CallOpts) (LatestConfigDigestAndEpoch,

	error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "latestConfigDigestAndEpoch")

	outstruct := new(LatestConfigDigestAndEpoch)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ScanLogs = *abi.ConvertType(out[0], new(bool)).(*bool)
	outstruct.ConfigDigest = *abi.ConvertType(out[1], new([32]byte)).(*[32]byte)
	outstruct.Epoch = *abi.ConvertType(out[2], new(uint32)).(*uint32)

	return *outstruct, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) LatestConfigDigestAndEpoch() (LatestConfigDigestAndEpoch,

	error) {
	return _CommitStoreHelper.Contract.LatestConfigDigestAndEpoch(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) LatestConfigDigestAndEpoch() (LatestConfigDigestAndEpoch,

	error) {
	return _CommitStoreHelper.Contract.LatestConfigDigestAndEpoch(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) Owner() (common.Address, error) {
	return _CommitStoreHelper.Contract.Owner(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) Owner() (common.Address, error) {
	return _CommitStoreHelper.Contract.Owner(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) Paused(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "paused")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) Paused() (bool, error) {
	return _CommitStoreHelper.Contract.Paused(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) Paused() (bool, error) {
	return _CommitStoreHelper.Contract.Paused(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) TypeAndVersion() (string, error) {
	return _CommitStoreHelper.Contract.TypeAndVersion(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) TypeAndVersion() (string, error) {
	return _CommitStoreHelper.Contract.TypeAndVersion(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) Verify(opts *bind.CallOpts, hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "verify", hashedLeaves, proofs, proofFlagBits)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) Verify(hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	return _CommitStoreHelper.Contract.Verify(&_CommitStoreHelper.CallOpts, hashedLeaves, proofs, proofFlagBits)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) Verify(hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	return _CommitStoreHelper.Contract.Verify(&_CommitStoreHelper.CallOpts, hashedLeaves, proofs, proofFlagBits)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "acceptOwnership")
}

func (_CommitStoreHelper *CommitStoreHelperSession) AcceptOwnership() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.AcceptOwnership(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.AcceptOwnership(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) Pause(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "pause")
}

func (_CommitStoreHelper *CommitStoreHelperSession) Pause() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Pause(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) Pause() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Pause(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) Report(opts *bind.TransactOpts, commitReport []byte, epochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "report", commitReport, epochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperSession) Report(commitReport []byte, epochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Report(&_CommitStoreHelper.TransactOpts, commitReport, epochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) Report(commitReport []byte, epochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Report(&_CommitStoreHelper.TransactOpts, commitReport, epochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) ResetUnblessedRoots(opts *bind.TransactOpts, rootToReset [][32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "resetUnblessedRoots", rootToReset)
}

func (_CommitStoreHelper *CommitStoreHelperSession) ResetUnblessedRoots(rootToReset [][32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.ResetUnblessedRoots(&_CommitStoreHelper.TransactOpts, rootToReset)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) ResetUnblessedRoots(rootToReset [][32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.ResetUnblessedRoots(&_CommitStoreHelper.TransactOpts, rootToReset)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) SetLatestPriceEpochAndRound(opts *bind.TransactOpts, latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "setLatestPriceEpochAndRound", latestPriceEpochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperSession) SetLatestPriceEpochAndRound(latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetLatestPriceEpochAndRound(&_CommitStoreHelper.TransactOpts, latestPriceEpochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) SetLatestPriceEpochAndRound(latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetLatestPriceEpochAndRound(&_CommitStoreHelper.TransactOpts, latestPriceEpochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) SetMinSeqNr(opts *bind.TransactOpts, minSeqNr uint64) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "setMinSeqNr", minSeqNr)
}

func (_CommitStoreHelper *CommitStoreHelperSession) SetMinSeqNr(minSeqNr uint64) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetMinSeqNr(&_CommitStoreHelper.TransactOpts, minSeqNr)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) SetMinSeqNr(minSeqNr uint64) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetMinSeqNr(&_CommitStoreHelper.TransactOpts, minSeqNr)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) SetOCR2Config(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "setOCR2Config", signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
}

func (_CommitStoreHelper *CommitStoreHelperSession) SetOCR2Config(signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetOCR2Config(&_CommitStoreHelper.TransactOpts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) SetOCR2Config(signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetOCR2Config(&_CommitStoreHelper.TransactOpts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "transferOwnership", to)
}

func (_CommitStoreHelper *CommitStoreHelperSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.TransferOwnership(&_CommitStoreHelper.TransactOpts, to)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.TransferOwnership(&_CommitStoreHelper.TransactOpts, to)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "transmit", reportContext, report, rs, ss, rawVs)
}

func (_CommitStoreHelper *CommitStoreHelperSession) Transmit(reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Transmit(&_CommitStoreHelper.TransactOpts, reportContext, report, rs, ss, rawVs)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) Transmit(reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Transmit(&_CommitStoreHelper.TransactOpts, reportContext, report, rs, ss, rawVs)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) Unpause(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "unpause")
}

func (_CommitStoreHelper *CommitStoreHelperSession) Unpause() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Unpause(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) Unpause() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Unpause(&_CommitStoreHelper.TransactOpts)
}

type CommitStoreHelperConfigSetIterator struct {
	Event *CommitStoreHelperConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperConfigSetIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperConfigSet struct {
	StaticConfig  CommitStoreStaticConfig
	DynamicConfig CommitStoreDynamicConfig
	Raw           types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterConfigSet(opts *bind.FilterOpts) (*CommitStoreHelperConfigSetIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperConfigSetIterator{contract: _CommitStoreHelper.contract, event: "ConfigSet", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperConfigSet) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperConfigSet)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "ConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseConfigSet(log types.Log) (*CommitStoreHelperConfigSet, error) {
	event := new(CommitStoreHelperConfigSet)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "ConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperConfigSet0Iterator struct {
	Event *CommitStoreHelperConfigSet0

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperConfigSet0Iterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperConfigSet0)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperConfigSet0)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperConfigSet0Iterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperConfigSet0Iterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperConfigSet0 struct {
	PreviousConfigBlockNumber uint32
	ConfigDigest              [32]byte
	ConfigCount               uint64
	Signers                   []common.Address
	Transmitters              []common.Address
	F                         uint8
	OnchainConfig             []byte
	OffchainConfigVersion     uint64
	OffchainConfig            []byte
	Raw                       types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterConfigSet0(opts *bind.FilterOpts) (*CommitStoreHelperConfigSet0Iterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "ConfigSet0")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperConfigSet0Iterator{contract: _CommitStoreHelper.contract, event: "ConfigSet0", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchConfigSet0(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperConfigSet0) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "ConfigSet0")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperConfigSet0)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "ConfigSet0", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseConfigSet0(log types.Log) (*CommitStoreHelperConfigSet0, error) {
	event := new(CommitStoreHelperConfigSet0)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "ConfigSet0", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperLatestPriceEpochAndRoundSetIterator struct {
	Event *CommitStoreHelperLatestPriceEpochAndRoundSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperLatestPriceEpochAndRoundSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperLatestPriceEpochAndRoundSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperLatestPriceEpochAndRoundSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperLatestPriceEpochAndRoundSetIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperLatestPriceEpochAndRoundSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperLatestPriceEpochAndRoundSet struct {
	OldEpochAndRound *big.Int
	NewEpochAndRound *big.Int
	Raw              types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterLatestPriceEpochAndRoundSet(opts *bind.FilterOpts) (*CommitStoreHelperLatestPriceEpochAndRoundSetIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "LatestPriceEpochAndRoundSet")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperLatestPriceEpochAndRoundSetIterator{contract: _CommitStoreHelper.contract, event: "LatestPriceEpochAndRoundSet", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchLatestPriceEpochAndRoundSet(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperLatestPriceEpochAndRoundSet) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "LatestPriceEpochAndRoundSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperLatestPriceEpochAndRoundSet)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "LatestPriceEpochAndRoundSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseLatestPriceEpochAndRoundSet(log types.Log) (*CommitStoreHelperLatestPriceEpochAndRoundSet, error) {
	event := new(CommitStoreHelperLatestPriceEpochAndRoundSet)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "LatestPriceEpochAndRoundSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperOwnershipTransferRequestedIterator struct {
	Event *CommitStoreHelperOwnershipTransferRequested

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperOwnershipTransferRequestedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreHelperOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperOwnershipTransferRequestedIterator{contract: _CommitStoreHelper.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperOwnershipTransferRequested)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseOwnershipTransferRequested(log types.Log) (*CommitStoreHelperOwnershipTransferRequested, error) {
	event := new(CommitStoreHelperOwnershipTransferRequested)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperOwnershipTransferredIterator struct {
	Event *CommitStoreHelperOwnershipTransferred

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperOwnershipTransferredIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperOwnershipTransferredIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreHelperOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperOwnershipTransferredIterator{contract: _CommitStoreHelper.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperOwnershipTransferred)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseOwnershipTransferred(log types.Log) (*CommitStoreHelperOwnershipTransferred, error) {
	event := new(CommitStoreHelperOwnershipTransferred)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperPausedIterator struct {
	Event *CommitStoreHelperPaused

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperPausedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperPaused)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperPaused)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperPausedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperPausedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperPaused struct {
	Account common.Address
	Raw     types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterPaused(opts *bind.FilterOpts) (*CommitStoreHelperPausedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "Paused")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperPausedIterator{contract: _CommitStoreHelper.contract, event: "Paused", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchPaused(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperPaused) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "Paused")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperPaused)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "Paused", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParsePaused(log types.Log) (*CommitStoreHelperPaused, error) {
	event := new(CommitStoreHelperPaused)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "Paused", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperReportAcceptedIterator struct {
	Event *CommitStoreHelperReportAccepted

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperReportAcceptedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperReportAccepted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperReportAccepted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperReportAcceptedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperReportAcceptedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperReportAccepted struct {
	Report CommitStoreCommitReport
	Raw    types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterReportAccepted(opts *bind.FilterOpts) (*CommitStoreHelperReportAcceptedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "ReportAccepted")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperReportAcceptedIterator{contract: _CommitStoreHelper.contract, event: "ReportAccepted", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchReportAccepted(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperReportAccepted) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "ReportAccepted")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperReportAccepted)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "ReportAccepted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseReportAccepted(log types.Log) (*CommitStoreHelperReportAccepted, error) {
	event := new(CommitStoreHelperReportAccepted)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "ReportAccepted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperRootRemovedIterator struct {
	Event *CommitStoreHelperRootRemoved

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperRootRemovedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperRootRemoved)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperRootRemoved)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperRootRemovedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperRootRemovedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperRootRemoved struct {
	Root [32]byte
	Raw  types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterRootRemoved(opts *bind.FilterOpts) (*CommitStoreHelperRootRemovedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "RootRemoved")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperRootRemovedIterator{contract: _CommitStoreHelper.contract, event: "RootRemoved", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchRootRemoved(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperRootRemoved) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "RootRemoved")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperRootRemoved)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "RootRemoved", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseRootRemoved(log types.Log) (*CommitStoreHelperRootRemoved, error) {
	event := new(CommitStoreHelperRootRemoved)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "RootRemoved", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperSequenceNumberSetIterator struct {
	Event *CommitStoreHelperSequenceNumberSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperSequenceNumberSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperSequenceNumberSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperSequenceNumberSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperSequenceNumberSetIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperSequenceNumberSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperSequenceNumberSet struct {
	OldSeqNum uint64
	NewSeqNum uint64
	Raw       types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterSequenceNumberSet(opts *bind.FilterOpts) (*CommitStoreHelperSequenceNumberSetIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "SequenceNumberSet")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperSequenceNumberSetIterator{contract: _CommitStoreHelper.contract, event: "SequenceNumberSet", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchSequenceNumberSet(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperSequenceNumberSet) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "SequenceNumberSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperSequenceNumberSet)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "SequenceNumberSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseSequenceNumberSet(log types.Log) (*CommitStoreHelperSequenceNumberSet, error) {
	event := new(CommitStoreHelperSequenceNumberSet)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "SequenceNumberSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperTransmittedIterator struct {
	Event *CommitStoreHelperTransmitted

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperTransmittedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperTransmitted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperTransmitted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperTransmittedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperTransmittedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperTransmitted struct {
	ConfigDigest [32]byte
	Epoch        uint32
	Raw          types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterTransmitted(opts *bind.FilterOpts) (*CommitStoreHelperTransmittedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "Transmitted")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperTransmittedIterator{contract: _CommitStoreHelper.contract, event: "Transmitted", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchTransmitted(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperTransmitted) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "Transmitted")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperTransmitted)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "Transmitted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseTransmitted(log types.Log) (*CommitStoreHelperTransmitted, error) {
	event := new(CommitStoreHelperTransmitted)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "Transmitted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperUnpausedIterator struct {
	Event *CommitStoreHelperUnpaused

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperUnpausedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperUnpaused)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperUnpaused)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperUnpausedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperUnpausedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperUnpaused struct {
	Account common.Address
	Raw     types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterUnpaused(opts *bind.FilterOpts) (*CommitStoreHelperUnpausedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "Unpaused")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperUnpausedIterator{contract: _CommitStoreHelper.contract, event: "Unpaused", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchUnpaused(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperUnpaused) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "Unpaused")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperUnpaused)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "Unpaused", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseUnpaused(log types.Log) (*CommitStoreHelperUnpaused, error) {
	event := new(CommitStoreHelperUnpaused)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "Unpaused", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type LatestConfigDetails struct {
	ConfigCount  uint32
	BlockNumber  uint32
	ConfigDigest [32]byte
}
type LatestConfigDigestAndEpoch struct {
	ScanLogs     bool
	ConfigDigest [32]byte
	Epoch        uint32
}

func (_CommitStoreHelper *CommitStoreHelper) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _CommitStoreHelper.abi.Events["ConfigSet"].ID:
		return _CommitStoreHelper.ParseConfigSet(log)
	case _CommitStoreHelper.abi.Events["ConfigSet0"].ID:
		return _CommitStoreHelper.ParseConfigSet0(log)
	case _CommitStoreHelper.abi.Events["LatestPriceEpochAndRoundSet"].ID:
		return _CommitStoreHelper.ParseLatestPriceEpochAndRoundSet(log)
	case _CommitStoreHelper.abi.Events["OwnershipTransferRequested"].ID:
		return _CommitStoreHelper.ParseOwnershipTransferRequested(log)
	case _CommitStoreHelper.abi.Events["OwnershipTransferred"].ID:
		return _CommitStoreHelper.ParseOwnershipTransferred(log)
	case _CommitStoreHelper.abi.Events["Paused"].ID:
		return _CommitStoreHelper.ParsePaused(log)
	case _CommitStoreHelper.abi.Events["ReportAccepted"].ID:
		return _CommitStoreHelper.ParseReportAccepted(log)
	case _CommitStoreHelper.abi.Events["RootRemoved"].ID:
		return _CommitStoreHelper.ParseRootRemoved(log)
	case _CommitStoreHelper.abi.Events["SequenceNumberSet"].ID:
		return _CommitStoreHelper.ParseSequenceNumberSet(log)
	case _CommitStoreHelper.abi.Events["Transmitted"].ID:
		return _CommitStoreHelper.ParseTransmitted(log)
	case _CommitStoreHelper.abi.Events["Unpaused"].ID:
		return _CommitStoreHelper.ParseUnpaused(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (CommitStoreHelperConfigSet) Topic() common.Hash {
	return common.HexToHash("0xc9d7123efd4203e60b0f0a4b1dbc4800fc97ce63679f71c3a27279b24a7ddec3")
}

func (CommitStoreHelperConfigSet0) Topic() common.Hash {
	return common.HexToHash("0x1591690b8638f5fb2dbec82ac741805ac5da8b45dc5263f4875b0496fdce4e05")
}

func (CommitStoreHelperLatestPriceEpochAndRoundSet) Topic() common.Hash {
	return common.HexToHash("0xf0d557bfce33e354b41885eb9264448726cfe51f486ffa69809d2bf565456444")
}

func (CommitStoreHelperOwnershipTransferRequested) Topic() common.Hash {
	return common.HexToHash("0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278")
}

func (CommitStoreHelperOwnershipTransferred) Topic() common.Hash {
	return common.HexToHash("0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0")
}

func (CommitStoreHelperPaused) Topic() common.Hash {
	return common.HexToHash("0x62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258")
}

func (CommitStoreHelperReportAccepted) Topic() common.Hash {
	return common.HexToHash("0x291698c01aa71f912280535d88a00d2c59fb63530a3f5d0098560468acb9ebf5")
}

func (CommitStoreHelperRootRemoved) Topic() common.Hash {
	return common.HexToHash("0x202f1139a3e334b6056064c0e9b19fd07e44a88d8f6e5ded571b24cf8c371f12")
}

func (CommitStoreHelperSequenceNumberSet) Topic() common.Hash {
	return common.HexToHash("0xea59e8027e41fda1525220008cf2416797405065eb21b0ebd417bfc6d361b8de")
}

func (CommitStoreHelperTransmitted) Topic() common.Hash {
	return common.HexToHash("0xb04e63db38c49950639fa09d29872f21f5d49d614f3a969d8adf3d4b52e41a62")
}

func (CommitStoreHelperUnpaused) Topic() common.Hash {
	return common.HexToHash("0x5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa")
}

func (_CommitStoreHelper *CommitStoreHelper) Address() common.Address {
	return _CommitStoreHelper.address
}

type CommitStoreHelperInterface interface {
	GetDynamicConfig(opts *bind.CallOpts) (CommitStoreDynamicConfig, error)

	GetExpectedNextSequenceNumber(opts *bind.CallOpts) (uint64, error)

	GetLatestPriceEpochAndRound(opts *bind.CallOpts) (uint64, error)

	GetMerkleRoot(opts *bind.CallOpts, root [32]byte) (*big.Int, error)

	GetStaticConfig(opts *bind.CallOpts) (CommitStoreStaticConfig, error)

	GetTransmitters(opts *bind.CallOpts) ([]common.Address, error)

	IsBlessed(opts *bind.CallOpts, root [32]byte) (bool, error)

	IsUnpausedAndNotCursed(opts *bind.CallOpts) (bool, error)

	LatestConfigDetails(opts *bind.CallOpts) (LatestConfigDetails,

		error)

	LatestConfigDigestAndEpoch(opts *bind.CallOpts) (LatestConfigDigestAndEpoch,

		error)

	Owner(opts *bind.CallOpts) (common.Address, error)

	Paused(opts *bind.CallOpts) (bool, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	Verify(opts *bind.CallOpts, hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error)

	AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error)

	Pause(opts *bind.TransactOpts) (*types.Transaction, error)

	Report(opts *bind.TransactOpts, commitReport []byte, epochAndRound *big.Int) (*types.Transaction, error)

	ResetUnblessedRoots(opts *bind.TransactOpts, rootToReset [][32]byte) (*types.Transaction, error)

	SetLatestPriceEpochAndRound(opts *bind.TransactOpts, latestPriceEpochAndRound *big.Int) (*types.Transaction, error)

	SetMinSeqNr(opts *bind.TransactOpts, minSeqNr uint64) (*types.Transaction, error)

	SetOCR2Config(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error)

	TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error)

	Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error)

	Unpause(opts *bind.TransactOpts) (*types.Transaction, error)

	FilterConfigSet(opts *bind.FilterOpts) (*CommitStoreHelperConfigSetIterator, error)

	WatchConfigSet(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperConfigSet) (event.Subscription, error)

	ParseConfigSet(log types.Log) (*CommitStoreHelperConfigSet, error)

	FilterConfigSet0(opts *bind.FilterOpts) (*CommitStoreHelperConfigSet0Iterator, error)

	WatchConfigSet0(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperConfigSet0) (event.Subscription, error)

	ParseConfigSet0(log types.Log) (*CommitStoreHelperConfigSet0, error)

	FilterLatestPriceEpochAndRoundSet(opts *bind.FilterOpts) (*CommitStoreHelperLatestPriceEpochAndRoundSetIterator, error)

	WatchLatestPriceEpochAndRoundSet(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperLatestPriceEpochAndRoundSet) (event.Subscription, error)

	ParseLatestPriceEpochAndRoundSet(log types.Log) (*CommitStoreHelperLatestPriceEpochAndRoundSet, error)

	FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreHelperOwnershipTransferRequestedIterator, error)

	WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferRequested(log types.Log) (*CommitStoreHelperOwnershipTransferRequested, error)

	FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreHelperOwnershipTransferredIterator, error)

	WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferred(log types.Log) (*CommitStoreHelperOwnershipTransferred, error)

	FilterPaused(opts *bind.FilterOpts) (*CommitStoreHelperPausedIterator, error)

	WatchPaused(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperPaused) (event.Subscription, error)

	ParsePaused(log types.Log) (*CommitStoreHelperPaused, error)

	FilterReportAccepted(opts *bind.FilterOpts) (*CommitStoreHelperReportAcceptedIterator, error)

	WatchReportAccepted(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperReportAccepted) (event.Subscription, error)

	ParseReportAccepted(log types.Log) (*CommitStoreHelperReportAccepted, error)

	FilterRootRemoved(opts *bind.FilterOpts) (*CommitStoreHelperRootRemovedIterator, error)

	WatchRootRemoved(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperRootRemoved) (event.Subscription, error)

	ParseRootRemoved(log types.Log) (*CommitStoreHelperRootRemoved, error)

	FilterSequenceNumberSet(opts *bind.FilterOpts) (*CommitStoreHelperSequenceNumberSetIterator, error)

	WatchSequenceNumberSet(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperSequenceNumberSet) (event.Subscription, error)

	ParseSequenceNumberSet(log types.Log) (*CommitStoreHelperSequenceNumberSet, error)

	FilterTransmitted(opts *bind.FilterOpts) (*CommitStoreHelperTransmittedIterator, error)

	WatchTransmitted(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperTransmitted) (event.Subscription, error)

	ParseTransmitted(log types.Log) (*CommitStoreHelperTransmitted, error)

	FilterUnpaused(opts *bind.FilterOpts) (*CommitStoreHelperUnpausedIterator, error)

	WatchUnpaused(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperUnpaused) (event.Subscription, error)

	ParseUnpaused(log types.Log) (*CommitStoreHelperUnpaused, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
