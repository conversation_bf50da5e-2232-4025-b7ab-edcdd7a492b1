// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package commit_store_helper_1_0_0

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"

	"github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

type CommitStoreCommitReport struct {
	PriceUpdates InternalPriceUpdates
	Interval     CommitStoreInterval
	MerkleRoot   [32]byte
}

type CommitStoreDynamicConfig struct {
	PriceRegistry common.Address
}

type CommitStoreInterval struct {
	Min uint64
	Max uint64
}

type CommitStoreStaticConfig struct {
	ChainSelector       uint64
	SourceChainSelector uint64
	OnRamp              common.Address
	ArmProxy            common.Address
}

type InternalPriceUpdates struct {
	TokenPriceUpdates []InternalTokenPriceUpdate
	DestChainSelector uint64
	UsdPerUnitGas     *big.Int
}

type InternalTokenPriceUpdate struct {
	SourceToken common.Address
	UsdPerToken *big.Int
}

var CommitStoreHelperMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"sourceChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"onRamp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"armProxy\",\"type\":\"address\"}],\"internalType\":\"structCommitStore.StaticConfig\",\"name\":\"staticConfig\",\"type\":\"tuple\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"BadARMSignal\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"expected\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"actual\",\"type\":\"bytes32\"}],\"name\":\"ConfigDigestMismatch\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"expected\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"actual\",\"type\":\"uint256\"}],\"name\":\"ForkedChain\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidCommitStoreConfig\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"message\",\"type\":\"string\"}],\"name\":\"InvalidConfig\",\"type\":\"error\"},{\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"min\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"max\",\"type\":\"uint64\"}],\"internalType\":\"structCommitStore.Interval\",\"name\":\"interval\",\"type\":\"tuple\"}],\"name\":\"InvalidInterval\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidProof\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidRoot\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"LeavesCannotBeEmpty\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NonUniqueSignatures\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OracleCannotBeZeroAddress\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PausedError\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RootAlreadyCommitted\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"SignaturesOutOfRegistration\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"StaleReport\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedSigner\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedTransmitter\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"expected\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"actual\",\"type\":\"uint256\"}],\"name\":\"WrongMessageLength\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"WrongNumberOfSignatures\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"sourceChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"onRamp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"armProxy\",\"type\":\"address\"}],\"indexed\":false,\"internalType\":\"structCommitStore.StaticConfig\",\"name\":\"staticConfig\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"address\",\"name\":\"priceRegistry\",\"type\":\"address\"}],\"indexed\":false,\"internalType\":\"structCommitStore.DynamicConfig\",\"name\":\"dynamicConfig\",\"type\":\"tuple\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"previousConfigBlockNumber\",\"type\":\"uint32\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"configCount\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"signers\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"address[]\",\"name\":\"transmitters\",\"type\":\"address[]\"},{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"f\",\"type\":\"uint8\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"onchainConfig\",\"type\":\"bytes\"},{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"indexed\":false,\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"components\":[{\"components\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"sourceToken\",\"type\":\"address\"},{\"internalType\":\"uint192\",\"name\":\"usdPerToken\",\"type\":\"uint192\"}],\"internalType\":\"structInternal.TokenPriceUpdate[]\",\"name\":\"tokenPriceUpdates\",\"type\":\"tuple[]\"},{\"internalType\":\"uint64\",\"name\":\"destChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint192\",\"name\":\"usdPerUnitGas\",\"type\":\"uint192\"}],\"internalType\":\"structInternal.PriceUpdates\",\"name\":\"priceUpdates\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint64\",\"name\":\"min\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"max\",\"type\":\"uint64\"}],\"internalType\":\"structCommitStore.Interval\",\"name\":\"interval\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"merkleRoot\",\"type\":\"bytes32\"}],\"indexed\":false,\"internalType\":\"structCommitStore.CommitReport\",\"name\":\"report\",\"type\":\"tuple\"}],\"name\":\"ReportAccepted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"}],\"name\":\"RootRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"name\":\"Transmitted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDynamicConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"priceRegistry\",\"type\":\"address\"}],\"internalType\":\"structCommitStore.DynamicConfig\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getExpectedNextSequenceNumber\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getLatestPriceEpochAndRound\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"}],\"name\":\"getMerkleRoot\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getStaticConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"sourceChainSelector\",\"type\":\"uint64\"},{\"internalType\":\"address\",\"name\":\"onRamp\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"armProxy\",\"type\":\"address\"}],\"internalType\":\"structCommitStore.StaticConfig\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getTransmitters\",\"outputs\":[{\"internalType\":\"address[]\",\"name\":\"\",\"type\":\"address[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isARMHealthy\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"root\",\"type\":\"bytes32\"}],\"name\":\"isBlessed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"isUnpausedAndARMHealthy\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestConfigDetails\",\"outputs\":[{\"internalType\":\"uint32\",\"name\":\"configCount\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"blockNumber\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"latestConfigDigestAndEpoch\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"scanLogs\",\"type\":\"bool\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"internalType\":\"uint32\",\"name\":\"epoch\",\"type\":\"uint32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"commitReport\",\"type\":\"bytes\"},{\"internalType\":\"uint40\",\"name\":\"epochAndRound\",\"type\":\"uint40\"}],\"name\":\"report\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"rootToReset\",\"type\":\"bytes32[]\"}],\"name\":\"resetUnblessedRoots\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint40\",\"name\":\"latestPriceEpochAndRound\",\"type\":\"uint40\"}],\"name\":\"setLatestPriceEpochAndRound\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint64\",\"name\":\"minSeqNr\",\"type\":\"uint64\"}],\"name\":\"setMinSeqNr\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"signers\",\"type\":\"address[]\"},{\"internalType\":\"address[]\",\"name\":\"transmitters\",\"type\":\"address[]\"},{\"internalType\":\"uint8\",\"name\":\"f\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"onchainConfig\",\"type\":\"bytes\"},{\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"name\":\"setOCR2Config\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[3]\",\"name\":\"reportContext\",\"type\":\"bytes32[3]\"},{\"internalType\":\"bytes\",\"name\":\"report\",\"type\":\"bytes\"},{\"internalType\":\"bytes32[]\",\"name\":\"rs\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"ss\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32\",\"name\":\"rawVs\",\"type\":\"bytes32\"}],\"name\":\"transmit\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"typeAndVersion\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"hashedLeaves\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"proofs\",\"type\":\"bytes32[]\"},{\"internalType\":\"uint256\",\"name\":\"proofFlagBits\",\"type\":\"uint256\"}],\"name\":\"verify\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}]",
	Bin: "0x61014060405260098054600165ff000000000160401b03191660011790553480156200002a57600080fd5b5060405162003824380380620038248339810160408190526200004d9162000274565b80600033808281620000a65760405162461bcd60e51b815260206004820152601860248201527f43616e6e6f7420736574206f776e657220746f207a65726f000000000000000060448201526064015b60405180910390fd5b600080546001600160a01b0319166001600160a01b0384811691909117909155811615620000d957620000d98162000194565b50505015156080524660a05260408101516001600160a01b0316158062000108575080516001600160401b0316155b806200011f575060208101516001600160401b0316155b8062000136575060608101516001600160a01b0316155b156200015557604051631fc5f15f60e11b815260040160405180910390fd5b80516001600160401b0390811660c05260208201511660e05260408101516001600160a01b039081166101005260609091015116610120525062000308565b336001600160a01b03821603620001ee5760405162461bcd60e51b815260206004820152601760248201527f43616e6e6f74207472616e7366657220746f2073656c6600000000000000000060448201526064016200009d565b600180546001600160a01b0319166001600160a01b0383811691821790925560008054604051929316917fed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae12789190a350565b80516001600160401b03811681146200025757600080fd5b919050565b80516001600160a01b03811681146200025757600080fd5b6000608082840312156200028757600080fd5b604051608081016001600160401b0381118282101715620002b857634e487b7160e01b600052604160045260246000fd5b604052620002c6836200023f565b8152620002d6602084016200023f565b6020820152620002e9604084016200025c565b6040820152620002fc606084016200025c565b60608201529392505050565b60805160a05160c05160e051610100516101205161348d620003976000396000818161027801528181610555015281816111a1015281816119c801528181611a89015261202d01526000818161023c015261200601526000818161020c0152611fdf0152600081816101dc0152611fb001526000818161131c0152611368015260006113e3015261348d6000f3fe608060405234801561001057600080fd5b50600436106101a35760003560e01c80637437ff9f116100ee578063a7206cd611610097578063b1dc65a411610071578063b1dc65a414610505578063f2fde38b14610518578063f47a86901461052b578063ff888fb11461053e57600080fd5b8063a7206cd6146104b2578063ad7a22f8146104d2578063afcb95d7146104e557600080fd5b80638456cb59116100c85780638456cb591461047a5780638da5cb5b146104825780638db94e44146104aa57600080fd5b80637437ff9f1461040357806379ba50971461044257806381ff70481461044a57600080fd5b806329b980e4116101505780634120fccd1161012a5780634120fccd146103c05780635c975abb146103d2578063666cab8d146103ee57600080fd5b806329b980e41461038457806332048875146103975780633f4ba83a146103b857600080fd5b8063181f5a7711610181578063181f5a77146103135780631dc18e561461035c5780631ef381741461037157600080fd5b806306285c69146101a85780630a6cd30d146102cb57806310c374ed146102e3575b600080fd5b6102b560408051608081018252600080825260208201819052918101829052606081019190915260405180608001604052807f000000000000000000000000000000000000000000000000000000000000000067ffffffffffffffff1681526020017f000000000000000000000000000000000000000000000000000000000000000067ffffffffffffffff1681526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1681526020017f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff16815250905090565b6040516102c29190612688565b60405180910390f35b6102d3610551565b60405190151581526020016102c2565b60095468010000000000000000900464ffffffffff165b60405167ffffffffffffffff90911681526020016102c2565b61034f6040518060400160405280601181526020017f436f6d6d697453746f726520312e302e3000000000000000000000000000000081525081565b6040516102c29190612745565b61036f61036a3660046127bb565b6105e8565b005b61036f61037f366004612a4a565b6105f8565b61036f610392366004612b17565b610e19565b6103aa6103a5366004612b77565b610e65565b6040519081526020016102c2565b61036f610f5b565b60095467ffffffffffffffff166102fa565b6009546d0100000000000000000000000000900460ff166102d3565b6103f6610fc1565b6040516102c29190612c3c565b604080516020808201835260009091528151808201835260085473ffffffffffffffffffffffffffffffffffffffff16908190529151918252016102c2565b61036f611030565b6004546002546040805163ffffffff808516825264010000000090940490931660208401528201526060016102c2565b61036f61112d565b60005460405173ffffffffffffffffffffffffffffffffffffffff90911681526020016102c2565b6102d361119d565b6103aa6104c0366004612c4f565b6000908152600a602052604090205490565b61036f6104e0366004612c68565b611254565b6040805160018152600060208201819052918101919091526060016102c2565b61036f610513366004612c83565b611297565b61036f610526366004612d3a565b6118b7565b61036f610539366004612d57565b6118cb565b6102d361054c366004612c4f565b611965565b60007f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663397796f76040518163ffffffff1660e01b8152600401602060405180830381865afa1580156105be573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906105e29190612d99565b15905090565b6105f3838383611a39565b505050565b855185518560ff16601f831115610670576040517f89a6198900000000000000000000000000000000000000000000000000000000815260206004820152601060248201527f746f6f206d616e79207369676e6572730000000000000000000000000000000060448201526064015b60405180910390fd5b806000036106da576040517f89a6198900000000000000000000000000000000000000000000000000000000815260206004820152601260248201527f66206d75737420626520706f73697469766500000000000000000000000000006044820152606401610667565b818314610768576040517f89a61989000000000000000000000000000000000000000000000000000000008152602060048201526024808201527f6f7261636c6520616464726573736573206f7574206f6620726567697374726160448201527f74696f6e000000000000000000000000000000000000000000000000000000006064820152608401610667565b610773816003612dea565b83116107db576040517f89a6198900000000000000000000000000000000000000000000000000000000815260206004820152601860248201527f6661756c74792d6f7261636c65206620746f6f206869676800000000000000006044820152606401610667565b6107e3611e4f565b6107ec86611ed2565b60065460005b818110156108e857600560006006838154811061081157610811612e01565b600091825260208083209091015473ffffffffffffffffffffffffffffffffffffffff168352820192909252604001812080547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00001690556007805460059291908490811061088157610881612e01565b600091825260208083209091015473ffffffffffffffffffffffffffffffffffffffff168352820192909252604001902080547fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00001690556108e181612e30565b90506107f2565b50895160005b81811015610cc15760008c828151811061090a5761090a612e01565b602002602001015190506000600281111561092757610927612e68565b73ffffffffffffffffffffffffffffffffffffffff8216600090815260056020526040902054610100900460ff16600281111561096657610966612e68565b146109cd576040517f89a6198900000000000000000000000000000000000000000000000000000000815260206004820152601760248201527f7265706561746564207369676e657220616464726573730000000000000000006044820152606401610667565b73ffffffffffffffffffffffffffffffffffffffff8116610a1a576040517fd6c62c9b00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b6040805180820190915260ff83168152602081016001905273ffffffffffffffffffffffffffffffffffffffff821660009081526005602090815260409091208251815460ff9091167fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0082168117835592840151919283917fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00001617610100836002811115610aca57610aca612e68565b021790555090505060008c8381518110610ae657610ae6612e01565b6020026020010151905060006002811115610b0357610b03612e68565b73ffffffffffffffffffffffffffffffffffffffff8216600090815260056020526040902054610100900460ff166002811115610b4257610b42612e68565b14610ba9576040517f89a6198900000000000000000000000000000000000000000000000000000000815260206004820152601c60248201527f7265706561746564207472616e736d69747465722061646472657373000000006044820152606401610667565b73ffffffffffffffffffffffffffffffffffffffff8116610bf6576040517fd6c62c9b00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b6040805180820190915260ff84168152602081016002905273ffffffffffffffffffffffffffffffffffffffff821660009081526005602090815260409091208251815460ff9091167fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0082168117835592840151919283917fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff00001617610100836002811115610ca657610ca6612e68565b0217905550905050505080610cba90612e30565b90506108ee565b508a51610cd59060069060208e01906125ca565b508951610ce99060079060208d01906125ca565b506003805460ff838116610100027fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff0000909216908c161717905560048054610d6f914691309190600090610d419063ffffffff16612e97565b91906101000a81548163ffffffff021916908363ffffffff160217905563ffffffff168e8e8e8e8e8e61208e565b600260000181905550600060048054906101000a900463ffffffff169050436004806101000a81548163ffffffff021916908363ffffffff1602179055507f1591690b8638f5fb2dbec82ac741805ac5da8b45dc5263f4875b0496fdce4e0581600260000154600460009054906101000a900463ffffffff168f8f8f8f8f8f604051610e0399989796959493929190612eba565b60405180910390a1505050505050505050505050565b610e21611e4f565b6009805464ffffffffff90921668010000000000000000027fffffffffffffffffffffffffffffffffffffff0000000000ffffffffffffffff909216919091179055565b6009546000906d0100000000000000000000000000900460ff1615610eb6576040517feced32bc00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b6000610f2787878080602002602001604051908101604052809392919081815260200183836020028082843760009201919091525050604080516020808b0282810182019093528a82529093508a925089918291850190849080828437600092019190915250889250612139915050565b9050610f3281611965565b610f40576000915050610f52565b6000908152600a602052604090205490505b95945050505050565b610f63611e4f565b600980547fffffffffffffffffffffffffffffffffffff00ffffffffffffffffffffffffff1690556040513381527f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa906020015b60405180910390a1565b6060600780548060200260200160405190810160405280929190818152602001828054801561102657602002820191906000526020600020905b815473ffffffffffffffffffffffffffffffffffffffff168152600190910190602001808311610ffb575b5050505050905090565b60015473ffffffffffffffffffffffffffffffffffffffff1633146110b1576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152601660248201527f4d7573742062652070726f706f736564206f776e6572000000000000000000006044820152606401610667565b60008054337fffffffffffffffffffffffff00000000000000000000000000000000000000008083168217845560018054909116905560405173ffffffffffffffffffffffffffffffffffffffff90921692909183917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e091a350565b611135611e4f565b600980547fffffffffffffffffffffffffffffffffffff00ffffffffffffffffffffffffff166d01000000000000000000000000001790556040513381527f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a25890602001610fb7565b60007f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663397796f76040518163ffffffff1660e01b8152600401602060405180830381865afa15801561120a573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061122e9190612d99565b15801561124f57506009546d0100000000000000000000000000900460ff16155b905090565b61125c611e4f565b600980547fffffffffffffffffffffffffffffffffffffffffffffffff00000000000000001667ffffffffffffffff92909216919091179055565b6112a6878760208b0135611a39565b6040805160608101825260025480825260035460ff8082166020850152610100909104169282019290925289359182146113195780516040517f93df584c000000000000000000000000000000000000000000000000000000008152600481019190915260248101839052604401610667565b467f00000000000000000000000000000000000000000000000000000000000000001461139a576040517f0f01ce850000000000000000000000000000000000000000000000000000000081527f00000000000000000000000000000000000000000000000000000000000000006004820152466024820152604401610667565b6040805183815260208c81013560081c63ffffffff16908201527fb04e63db38c49950639fa09d29872f21f5d49d614f3a969d8adf3d4b52e41a62910160405180910390a160007f00000000000000000000000000000000000000000000000000000000000000001561143c5760028260200151836040015161141d9190612f50565b6114279190612f69565b611432906001612f50565b60ff169050611452565b602082015161144c906001612f50565b60ff1690505b86811461148b576040517f71253a2500000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b8685146114c4576040517fa75d88af00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b3360009081526005602090815260408083208151808301909252805460ff8082168452929391929184019161010090910416600281111561150757611507612e68565b600281111561151857611518612e68565b905250905060028160200151600281111561153557611535612e68565b14801561157c57506007816000015160ff168154811061155757611557612e01565b60009182526020909120015473ffffffffffffffffffffffffffffffffffffffff1633145b6115b2576040517fda0f08e800000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b5060006115c0866020612dea565b6115cb896020612dea565b6115d78c610144612fb2565b6115e19190612fb2565b6115eb9190612fb2565b905036811461162f576040517f8e1192e100000000000000000000000000000000000000000000000000000000815260048101829052366024820152604401610667565b5060008a8a604051611642929190612fc5565b604051908190038120611659918e90602001612fd5565b604051602081830303815290604052805190602001209050611679612654565b8860005b818110156118a65760006001858a846020811061169c5761169c612e01565b6116a991901a601b612f50565b8f8f868181106116bb576116bb612e01565b905060200201358e8e878181106116d4576116d4612e01565b9050602002013560405160008152602001604052604051611711949392919093845260ff9290921660208401526040830152606082015260800190565b6020604051602081039080840390855afa158015611733573d6000803e3d6000fd5b5050604080517fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe081015173ffffffffffffffffffffffffffffffffffffffff8116600090815260056020908152848220848601909552845460ff80821686529397509195509293928401916101009091041660028111156117b6576117b6612e68565b60028111156117c7576117c7612e68565b90525090506001816020015160028111156117e4576117e4612e68565b1461181b576040517fca31867a00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b8051859060ff16601f811061183257611832612e01565b60200201511561186e576040517ff67bc7c400000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b600185826000015160ff16601f811061188957611889612e01565b911515602090920201525061189f905081612e30565b905061167d565b505050505050505050505050505050565b6118bf611e4f565b6118c88161245a565b50565b6118d3611e4f565b60005b818110156105f35760008383838181106118f2576118f2612e01565b90506020020135905061190481611965565b611954576000818152600a602052604080822091909155517f202f1139a3e334b6056064c0e9b19fd07e44a88d8f6e5ded571b24cf8c371f129061194b9083815260200190565b60405180910390a15b5061195e81612e30565b90506118d6565b6040805180820182523081526020810183815291517f4d616771000000000000000000000000000000000000000000000000000000008152905173ffffffffffffffffffffffffffffffffffffffff9081166004830152915160248201526000917f00000000000000000000000000000000000000000000000000000000000000001690634d61677190604401602060405180830381865afa158015611a0f573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611a339190612d99565b92915050565b6009546d0100000000000000000000000000900460ff1615611a87576040517feced32bc00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b7f000000000000000000000000000000000000000000000000000000000000000073ffffffffffffffffffffffffffffffffffffffff1663397796f76040518163ffffffff1660e01b8152600401602060405180830381865afa158015611af2573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190611b169190612d99565b15611b4d576040517fc148371500000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b6000611b5b8385018561304f565b80515151909150151580611b7d575080516020015167ffffffffffffffff1615155b15611cb55760095464ffffffffff80841668010000000000000000909204161015611c7a57600980547fffffffffffffffffffffffffffffffffffffff0000000000ffffffffffffffff166801000000000000000064ffffffffff85160217905560085481516040517f866548c900000000000000000000000000000000000000000000000000000000815273ffffffffffffffffffffffffffffffffffffffff9092169163866548c991611c3491600401613266565b600060405180830381600087803b158015611c4e57600080fd5b505af1158015611c62573d6000803e3d6000fd5b505050506040810151611c755750505050565b611cb5565b6040810151611cb5576040517ff803a2ca00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b60208101515160095467ffffffffffffffff9081169116141580611cf0575060208082015190810151905167ffffffffffffffff9182169116115b15611d2d5780602001516040517fbb1ae18d0000000000000000000000000000000000000000000000000000000081526004016106679190613279565b6040810151611d68576040517f504570e300000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b6040808201516000908152600a602052205415611db1576040517fa0bce24f00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b6020808201510151611dc490600161329e565b600980547fffffffffffffffffffffffffffffffffffffffffffffffff00000000000000001667ffffffffffffffff929092169190911790556040818101516000908152600a602052819020429055517fe81b49e583122eb290c46fc255c962b9a2dec468816c00fb7a2e6ebc42dc92d490611e419083906132c6565b60405180910390a150505050565b60005473ffffffffffffffffffffffffffffffffffffffff163314611ed0576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152601660248201527f4f6e6c792063616c6c61626c65206279206f776e6572000000000000000000006044820152606401610667565b565b600081806020019051810190611ee89190613322565b805190915073ffffffffffffffffffffffffffffffffffffffff16611f39576040517f3f8be2be00000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b8051600880547fffffffffffffffffffffffff00000000000000000000000000000000000000001673ffffffffffffffffffffffffffffffffffffffff928316179055600980547fffffffffffffffffffffffffffffffffffffff0000000000ffffffffffffffff169055604080516080810182527f000000000000000000000000000000000000000000000000000000000000000067ffffffffffffffff90811682527f00000000000000000000000000000000000000000000000000000000000000001660208201527f00000000000000000000000000000000000000000000000000000000000000008316818301527f00000000000000000000000000000000000000000000000000000000000000009092166060830152517fc9d7123efd4203e60b0f0a4b1dbc4800fc97ce63679f71c3a27279b24a7ddec39161208291849061336e565b60405180910390a15050565b6000808a8a8a8a8a8a8a8a8a6040516020016120b2999897969594939291906133eb565b604080517fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe081840301815291905280516020909101207dffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff167e01000000000000000000000000000000000000000000000000000000000000179150509998505050505050505050565b825182516000919081830361217a576040517f11a6b26400000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b610101821180159061218e57506101018111155b6121c4576040517f09bde33900000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff82820101610100811115612225576040517f09bde33900000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b80600003612252578660008151811061224057612240612e01565b60200260200101519350505050612453565b60008167ffffffffffffffff81111561226d5761226d61280f565b604051908082528060200260200182016040528015612296578160200160208202803683370190505b50905060008080805b858110156123d95760006001821b8b8116036122fa57888510156122e3578c5160018601958e9181106122d4576122d4612e01565b6020026020010151905061231c565b85516001850194879181106122d4576122d4612e01565b8b5160018401938d91811061231157612311612e01565b602002602001015190505b60008986101561234c578d5160018701968f91811061233d5761233d612e01565b6020026020010151905061236e565b865160018601958891811061236357612363612e01565b602002602001015190505b828511156123a8576040517f09bde33900000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b6123b2828261254f565b8784815181106123c4576123c4612e01565b6020908102919091010152505060010161229f565b5060018503821480156123eb57508683145b80156123f657508581145b61242c576040517f09bde33900000000000000000000000000000000000000000000000000000000815260040160405180910390fd5b83600186038151811061244157612441612e01565b60200260200101519750505050505050505b9392505050565b3373ffffffffffffffffffffffffffffffffffffffff8216036124d9576040517f08c379a000000000000000000000000000000000000000000000000000000000815260206004820152601760248201527f43616e6e6f74207472616e7366657220746f2073656c660000000000000000006044820152606401610667565b600180547fffffffffffffffffffffffff00000000000000000000000000000000000000001673ffffffffffffffffffffffffffffffffffffffff83811691821790925560008054604051929316917fed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae12789190a350565b60008183106125915760408051600160208083019190915281830185905260608083018790528351808403909101815260809092019092528051910120612453565b60408051600160208083019190915281830186905260608083018690528351808403909101815260809092019092528051910120612453565b828054828255906000526020600020908101928215612644579160200282015b8281111561264457825182547fffffffffffffffffffffffff00000000000000000000000000000000000000001673ffffffffffffffffffffffffffffffffffffffff9091161782556020909201916001909101906125ea565b50612650929150612673565b5090565b604051806103e00160405280601f906020820280368337509192915050565b5b808211156126505760008155600101612674565b60808101611a33828467ffffffffffffffff80825116835280602083015116602084015250604081015173ffffffffffffffffffffffffffffffffffffffff808216604085015280606084015116606085015250505050565b6000815180845260005b81811015612707576020818501810151868301820152016126eb565b5060006020828601015260207fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0601f83011685010191505092915050565b60208152600061245360208301846126e1565b60008083601f84011261276a57600080fd5b50813567ffffffffffffffff81111561278257600080fd5b60208301915083602082850101111561279a57600080fd5b9250929050565b803564ffffffffff811681146127b657600080fd5b919050565b6000806000604084860312156127d057600080fd5b833567ffffffffffffffff8111156127e757600080fd5b6127f386828701612758565b90945092506128069050602085016127a1565b90509250925092565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052604160045260246000fd5b6040805190810167ffffffffffffffff811182821017156128615761286161280f565b60405290565b6040516060810167ffffffffffffffff811182821017156128615761286161280f565b604051601f82017fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe016810167ffffffffffffffff811182821017156128d1576128d161280f565b604052919050565b600067ffffffffffffffff8211156128f3576128f361280f565b5060051b60200190565b73ffffffffffffffffffffffffffffffffffffffff811681146118c857600080fd5b600082601f83011261293057600080fd5b81356020612945612940836128d9565b61288a565b82815260059290921b8401810191818101908684111561296457600080fd5b8286015b8481101561298857803561297b816128fd565b8352918301918301612968565b509695505050505050565b803560ff811681146127b657600080fd5b600082601f8301126129b557600080fd5b813567ffffffffffffffff8111156129cf576129cf61280f565b612a0060207fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe0601f8401160161288a565b818152846020838601011115612a1557600080fd5b816020850160208301376000918101602001919091529392505050565b803567ffffffffffffffff811681146127b657600080fd5b60008060008060008060c08789031215612a6357600080fd5b863567ffffffffffffffff80821115612a7b57600080fd5b612a878a838b0161291f565b97506020890135915080821115612a9d57600080fd5b612aa98a838b0161291f565b9650612ab760408a01612993565b95506060890135915080821115612acd57600080fd5b612ad98a838b016129a4565b9450612ae760808a01612a32565b935060a0890135915080821115612afd57600080fd5b50612b0a89828a016129a4565b9150509295509295509295565b600060208284031215612b2957600080fd5b612453826127a1565b60008083601f840112612b4457600080fd5b50813567ffffffffffffffff811115612b5c57600080fd5b6020830191508360208260051b850101111561279a57600080fd5b600080600080600060608688031215612b8f57600080fd5b853567ffffffffffffffff80821115612ba757600080fd5b612bb389838a01612b32565b90975095506020880135915080821115612bcc57600080fd5b50612bd988828901612b32565b96999598509660400135949350505050565b600081518084526020808501945080840160005b83811015612c3157815173ffffffffffffffffffffffffffffffffffffffff1687529582019590820190600101612bff565b509495945050505050565b6020815260006124536020830184612beb565b600060208284031215612c6157600080fd5b5035919050565b600060208284031215612c7a57600080fd5b61245382612a32565b60008060008060008060008060e0898b031215612c9f57600080fd5b606089018a811115612cb057600080fd5b8998503567ffffffffffffffff80821115612cca57600080fd5b612cd68c838d01612758565b909950975060808b0135915080821115612cef57600080fd5b612cfb8c838d01612b32565b909750955060a08b0135915080821115612d1457600080fd5b50612d218b828c01612b32565b999c989b50969995989497949560c00135949350505050565b600060208284031215612d4c57600080fd5b8135612453816128fd565b60008060208385031215612d6a57600080fd5b823567ffffffffffffffff811115612d8157600080fd5b612d8d85828601612b32565b90969095509350505050565b600060208284031215612dab57600080fd5b8151801515811461245357600080fd5b7f4e487b7100000000000000000000000000000000000000000000000000000000600052601160045260246000fd5b8082028115828204841417611a3357611a33612dbb565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052603260045260246000fd5b60007fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8203612e6157612e61612dbb565b5060010190565b7f4e487b7100000000000000000000000000000000000000000000000000000000600052602160045260246000fd5b600063ffffffff808316818103612eb057612eb0612dbb565b6001019392505050565b600061012063ffffffff808d1684528b6020850152808b16604085015250806060840152612eea8184018a612beb565b90508281036080840152612efe8189612beb565b905060ff871660a084015282810360c0840152612f1b81876126e1565b905067ffffffffffffffff851660e0840152828103610100840152612f4081856126e1565b9c9b505050505050505050505050565b60ff8181168382160190811115611a3357611a33612dbb565b600060ff831680612fa3577f4e487b7100000000000000000000000000000000000000000000000000000000600052601260045260246000fd5b8060ff84160491505092915050565b80820180821115611a3357611a33612dbb565b8183823760009101908152919050565b828152606082602083013760800192915050565b803577ffffffffffffffffffffffffffffffffffffffffffffffff811681146127b657600080fd5b60006040828403121561302357600080fd5b61302b61283e565b905061303682612a32565b815261304460208301612a32565b602082015292915050565b6000602080838503121561306257600080fd5b823567ffffffffffffffff8082111561307a57600080fd5b908401906080828703121561308e57600080fd5b613096612867565b8235828111156130a557600080fd5b8301606081890312156130b757600080fd5b6130bf612867565b8135848111156130ce57600080fd5b82019350601f840189136130e157600080fd5b83356130ef612940826128d9565b81815260069190911b8501870190878101908b83111561310e57600080fd5b958801955b82871015613162576040878d03121561312c5760008081fd5b61313461283e565b873561313f816128fd565b815261314c888b01612fe9565b818b015282526040969096019590880190613113565b8352506131729050828701612a32565b8682015261318260408301612fe9565b604082015282525061319687848601613011565b93810193909352506060013560408201529392505050565b805160608084528151908401819052600091602091908201906080860190845b81811015613225578351805173ffffffffffffffffffffffffffffffffffffffff16845285015177ffffffffffffffffffffffffffffffffffffffffffffffff1685840152928401926040909201916001016131ce565b505067ffffffffffffffff83860151168387015260408501519250610f52604087018477ffffffffffffffffffffffffffffffffffffffffffffffff169052565b60208152600061245360208301846131ae565b60408101611a338284805167ffffffffffffffff908116835260209182015116910152565b67ffffffffffffffff8181168382160190808211156132bf576132bf612dbb565b5092915050565b6020815260008251608060208401526132e260a08401826131ae565b9050602084015161330d6040850182805167ffffffffffffffff908116835260209182015116910152565b50604084015160808401528091505092915050565b60006020828403121561333457600080fd5b6040516020810181811067ffffffffffffffff821117156133575761335761280f565b6040528251613365816128fd565b81529392505050565b60a081016133c7828567ffffffffffffffff80825116835280602083015116602084015250604081015173ffffffffffffffffffffffffffffffffffffffff808216604085015280606084015116606085015250505050565b73ffffffffffffffffffffffffffffffffffffffff83511660808301529392505050565b60006101208b835273ffffffffffffffffffffffffffffffffffffffff8b16602084015267ffffffffffffffff808b1660408501528160608501526134328285018b612beb565b91508382036080850152613446828a612beb565b915060ff881660a085015283820360c085015261346382886126e1565b90861660e08501528381036101008501529050612f4081856126e156fea164736f6c6343000813000a",
}

var CommitStoreHelperABI = CommitStoreHelperMetaData.ABI

var CommitStoreHelperBin = CommitStoreHelperMetaData.Bin

func DeployCommitStoreHelper(auth *bind.TransactOpts, backend bind.ContractBackend, staticConfig CommitStoreStaticConfig) (common.Address, *types.Transaction, *CommitStoreHelper, error) {
	parsed, err := CommitStoreHelperMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(CommitStoreHelperBin), backend, staticConfig)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &CommitStoreHelper{CommitStoreHelperCaller: CommitStoreHelperCaller{contract: contract}, CommitStoreHelperTransactor: CommitStoreHelperTransactor{contract: contract}, CommitStoreHelperFilterer: CommitStoreHelperFilterer{contract: contract}}, nil
}

type CommitStoreHelper struct {
	address common.Address
	abi     abi.ABI
	CommitStoreHelperCaller
	CommitStoreHelperTransactor
	CommitStoreHelperFilterer
}

type CommitStoreHelperCaller struct {
	contract *bind.BoundContract
}

type CommitStoreHelperTransactor struct {
	contract *bind.BoundContract
}

type CommitStoreHelperFilterer struct {
	contract *bind.BoundContract
}

type CommitStoreHelperSession struct {
	Contract     *CommitStoreHelper
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type CommitStoreHelperCallerSession struct {
	Contract *CommitStoreHelperCaller
	CallOpts bind.CallOpts
}

type CommitStoreHelperTransactorSession struct {
	Contract     *CommitStoreHelperTransactor
	TransactOpts bind.TransactOpts
}

type CommitStoreHelperRaw struct {
	Contract *CommitStoreHelper
}

type CommitStoreHelperCallerRaw struct {
	Contract *CommitStoreHelperCaller
}

type CommitStoreHelperTransactorRaw struct {
	Contract *CommitStoreHelperTransactor
}

func NewCommitStoreHelper(address common.Address, backend bind.ContractBackend) (*CommitStoreHelper, error) {
	abi, err := abi.JSON(strings.NewReader(CommitStoreHelperABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindCommitStoreHelper(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelper{address: address, abi: abi, CommitStoreHelperCaller: CommitStoreHelperCaller{contract: contract}, CommitStoreHelperTransactor: CommitStoreHelperTransactor{contract: contract}, CommitStoreHelperFilterer: CommitStoreHelperFilterer{contract: contract}}, nil
}

func NewCommitStoreHelperCaller(address common.Address, caller bind.ContractCaller) (*CommitStoreHelperCaller, error) {
	contract, err := bindCommitStoreHelper(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperCaller{contract: contract}, nil
}

func NewCommitStoreHelperTransactor(address common.Address, transactor bind.ContractTransactor) (*CommitStoreHelperTransactor, error) {
	contract, err := bindCommitStoreHelper(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperTransactor{contract: contract}, nil
}

func NewCommitStoreHelperFilterer(address common.Address, filterer bind.ContractFilterer) (*CommitStoreHelperFilterer, error) {
	contract, err := bindCommitStoreHelper(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperFilterer{contract: contract}, nil
}

func bindCommitStoreHelper(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := CommitStoreHelperMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_CommitStoreHelper *CommitStoreHelperRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CommitStoreHelper.Contract.CommitStoreHelperCaller.contract.Call(opts, result, method, params...)
}

func (_CommitStoreHelper *CommitStoreHelperRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.CommitStoreHelperTransactor.contract.Transfer(opts)
}

func (_CommitStoreHelper *CommitStoreHelperRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.CommitStoreHelperTransactor.contract.Transact(opts, method, params...)
}

func (_CommitStoreHelper *CommitStoreHelperCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CommitStoreHelper.Contract.contract.Call(opts, result, method, params...)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.contract.Transfer(opts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.contract.Transact(opts, method, params...)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetDynamicConfig(opts *bind.CallOpts) (CommitStoreDynamicConfig, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getDynamicConfig")

	if err != nil {
		return *new(CommitStoreDynamicConfig), err
	}

	out0 := *abi.ConvertType(out[0], new(CommitStoreDynamicConfig)).(*CommitStoreDynamicConfig)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetDynamicConfig() (CommitStoreDynamicConfig, error) {
	return _CommitStoreHelper.Contract.GetDynamicConfig(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetDynamicConfig() (CommitStoreDynamicConfig, error) {
	return _CommitStoreHelper.Contract.GetDynamicConfig(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetExpectedNextSequenceNumber(opts *bind.CallOpts) (uint64, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getExpectedNextSequenceNumber")

	if err != nil {
		return *new(uint64), err
	}

	out0 := *abi.ConvertType(out[0], new(uint64)).(*uint64)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetExpectedNextSequenceNumber() (uint64, error) {
	return _CommitStoreHelper.Contract.GetExpectedNextSequenceNumber(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetExpectedNextSequenceNumber() (uint64, error) {
	return _CommitStoreHelper.Contract.GetExpectedNextSequenceNumber(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetLatestPriceEpochAndRound(opts *bind.CallOpts) (uint64, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getLatestPriceEpochAndRound")

	if err != nil {
		return *new(uint64), err
	}

	out0 := *abi.ConvertType(out[0], new(uint64)).(*uint64)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetLatestPriceEpochAndRound() (uint64, error) {
	return _CommitStoreHelper.Contract.GetLatestPriceEpochAndRound(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetLatestPriceEpochAndRound() (uint64, error) {
	return _CommitStoreHelper.Contract.GetLatestPriceEpochAndRound(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetMerkleRoot(opts *bind.CallOpts, root [32]byte) (*big.Int, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getMerkleRoot", root)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetMerkleRoot(root [32]byte) (*big.Int, error) {
	return _CommitStoreHelper.Contract.GetMerkleRoot(&_CommitStoreHelper.CallOpts, root)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetMerkleRoot(root [32]byte) (*big.Int, error) {
	return _CommitStoreHelper.Contract.GetMerkleRoot(&_CommitStoreHelper.CallOpts, root)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetStaticConfig(opts *bind.CallOpts) (CommitStoreStaticConfig, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getStaticConfig")

	if err != nil {
		return *new(CommitStoreStaticConfig), err
	}

	out0 := *abi.ConvertType(out[0], new(CommitStoreStaticConfig)).(*CommitStoreStaticConfig)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetStaticConfig() (CommitStoreStaticConfig, error) {
	return _CommitStoreHelper.Contract.GetStaticConfig(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetStaticConfig() (CommitStoreStaticConfig, error) {
	return _CommitStoreHelper.Contract.GetStaticConfig(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) GetTransmitters(opts *bind.CallOpts) ([]common.Address, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "getTransmitters")

	if err != nil {
		return *new([]common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new([]common.Address)).(*[]common.Address)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) GetTransmitters() ([]common.Address, error) {
	return _CommitStoreHelper.Contract.GetTransmitters(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) GetTransmitters() ([]common.Address, error) {
	return _CommitStoreHelper.Contract.GetTransmitters(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) IsARMHealthy(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "isARMHealthy")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) IsARMHealthy() (bool, error) {
	return _CommitStoreHelper.Contract.IsARMHealthy(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) IsARMHealthy() (bool, error) {
	return _CommitStoreHelper.Contract.IsARMHealthy(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) IsBlessed(opts *bind.CallOpts, root [32]byte) (bool, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "isBlessed", root)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) IsBlessed(root [32]byte) (bool, error) {
	return _CommitStoreHelper.Contract.IsBlessed(&_CommitStoreHelper.CallOpts, root)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) IsBlessed(root [32]byte) (bool, error) {
	return _CommitStoreHelper.Contract.IsBlessed(&_CommitStoreHelper.CallOpts, root)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) IsUnpausedAndARMHealthy(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "isUnpausedAndARMHealthy")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) IsUnpausedAndARMHealthy() (bool, error) {
	return _CommitStoreHelper.Contract.IsUnpausedAndARMHealthy(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) IsUnpausedAndARMHealthy() (bool, error) {
	return _CommitStoreHelper.Contract.IsUnpausedAndARMHealthy(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) LatestConfigDetails(opts *bind.CallOpts) (LatestConfigDetails,

	error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "latestConfigDetails")

	outstruct := new(LatestConfigDetails)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ConfigCount = *abi.ConvertType(out[0], new(uint32)).(*uint32)
	outstruct.BlockNumber = *abi.ConvertType(out[1], new(uint32)).(*uint32)
	outstruct.ConfigDigest = *abi.ConvertType(out[2], new([32]byte)).(*[32]byte)

	return *outstruct, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) LatestConfigDetails() (LatestConfigDetails,

	error) {
	return _CommitStoreHelper.Contract.LatestConfigDetails(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) LatestConfigDetails() (LatestConfigDetails,

	error) {
	return _CommitStoreHelper.Contract.LatestConfigDetails(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) LatestConfigDigestAndEpoch(opts *bind.CallOpts) (LatestConfigDigestAndEpoch,

	error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "latestConfigDigestAndEpoch")

	outstruct := new(LatestConfigDigestAndEpoch)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ScanLogs = *abi.ConvertType(out[0], new(bool)).(*bool)
	outstruct.ConfigDigest = *abi.ConvertType(out[1], new([32]byte)).(*[32]byte)
	outstruct.Epoch = *abi.ConvertType(out[2], new(uint32)).(*uint32)

	return *outstruct, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) LatestConfigDigestAndEpoch() (LatestConfigDigestAndEpoch,

	error) {
	return _CommitStoreHelper.Contract.LatestConfigDigestAndEpoch(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) LatestConfigDigestAndEpoch() (LatestConfigDigestAndEpoch,

	error) {
	return _CommitStoreHelper.Contract.LatestConfigDigestAndEpoch(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) Owner() (common.Address, error) {
	return _CommitStoreHelper.Contract.Owner(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) Owner() (common.Address, error) {
	return _CommitStoreHelper.Contract.Owner(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) Paused(opts *bind.CallOpts) (bool, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "paused")

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) Paused() (bool, error) {
	return _CommitStoreHelper.Contract.Paused(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) Paused() (bool, error) {
	return _CommitStoreHelper.Contract.Paused(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) TypeAndVersion() (string, error) {
	return _CommitStoreHelper.Contract.TypeAndVersion(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) TypeAndVersion() (string, error) {
	return _CommitStoreHelper.Contract.TypeAndVersion(&_CommitStoreHelper.CallOpts)
}

func (_CommitStoreHelper *CommitStoreHelperCaller) Verify(opts *bind.CallOpts, hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	var out []interface{}
	err := _CommitStoreHelper.contract.Call(opts, &out, "verify", hashedLeaves, proofs, proofFlagBits)

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_CommitStoreHelper *CommitStoreHelperSession) Verify(hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	return _CommitStoreHelper.Contract.Verify(&_CommitStoreHelper.CallOpts, hashedLeaves, proofs, proofFlagBits)
}

func (_CommitStoreHelper *CommitStoreHelperCallerSession) Verify(hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error) {
	return _CommitStoreHelper.Contract.Verify(&_CommitStoreHelper.CallOpts, hashedLeaves, proofs, proofFlagBits)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "acceptOwnership")
}

func (_CommitStoreHelper *CommitStoreHelperSession) AcceptOwnership() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.AcceptOwnership(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.AcceptOwnership(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) Pause(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "pause")
}

func (_CommitStoreHelper *CommitStoreHelperSession) Pause() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Pause(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) Pause() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Pause(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) Report(opts *bind.TransactOpts, commitReport []byte, epochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "report", commitReport, epochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperSession) Report(commitReport []byte, epochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Report(&_CommitStoreHelper.TransactOpts, commitReport, epochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) Report(commitReport []byte, epochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Report(&_CommitStoreHelper.TransactOpts, commitReport, epochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) ResetUnblessedRoots(opts *bind.TransactOpts, rootToReset [][32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "resetUnblessedRoots", rootToReset)
}

func (_CommitStoreHelper *CommitStoreHelperSession) ResetUnblessedRoots(rootToReset [][32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.ResetUnblessedRoots(&_CommitStoreHelper.TransactOpts, rootToReset)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) ResetUnblessedRoots(rootToReset [][32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.ResetUnblessedRoots(&_CommitStoreHelper.TransactOpts, rootToReset)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) SetLatestPriceEpochAndRound(opts *bind.TransactOpts, latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "setLatestPriceEpochAndRound", latestPriceEpochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperSession) SetLatestPriceEpochAndRound(latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetLatestPriceEpochAndRound(&_CommitStoreHelper.TransactOpts, latestPriceEpochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) SetLatestPriceEpochAndRound(latestPriceEpochAndRound *big.Int) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetLatestPriceEpochAndRound(&_CommitStoreHelper.TransactOpts, latestPriceEpochAndRound)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) SetMinSeqNr(opts *bind.TransactOpts, minSeqNr uint64) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "setMinSeqNr", minSeqNr)
}

func (_CommitStoreHelper *CommitStoreHelperSession) SetMinSeqNr(minSeqNr uint64) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetMinSeqNr(&_CommitStoreHelper.TransactOpts, minSeqNr)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) SetMinSeqNr(minSeqNr uint64) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetMinSeqNr(&_CommitStoreHelper.TransactOpts, minSeqNr)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) SetOCR2Config(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "setOCR2Config", signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
}

func (_CommitStoreHelper *CommitStoreHelperSession) SetOCR2Config(signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetOCR2Config(&_CommitStoreHelper.TransactOpts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) SetOCR2Config(signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.SetOCR2Config(&_CommitStoreHelper.TransactOpts, signers, transmitters, f, onchainConfig, offchainConfigVersion, offchainConfig)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "transferOwnership", to)
}

func (_CommitStoreHelper *CommitStoreHelperSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.TransferOwnership(&_CommitStoreHelper.TransactOpts, to)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.TransferOwnership(&_CommitStoreHelper.TransactOpts, to)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "transmit", reportContext, report, rs, ss, rawVs)
}

func (_CommitStoreHelper *CommitStoreHelperSession) Transmit(reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Transmit(&_CommitStoreHelper.TransactOpts, reportContext, report, rs, ss, rawVs)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) Transmit(reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Transmit(&_CommitStoreHelper.TransactOpts, reportContext, report, rs, ss, rawVs)
}

func (_CommitStoreHelper *CommitStoreHelperTransactor) Unpause(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CommitStoreHelper.contract.Transact(opts, "unpause")
}

func (_CommitStoreHelper *CommitStoreHelperSession) Unpause() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Unpause(&_CommitStoreHelper.TransactOpts)
}

func (_CommitStoreHelper *CommitStoreHelperTransactorSession) Unpause() (*types.Transaction, error) {
	return _CommitStoreHelper.Contract.Unpause(&_CommitStoreHelper.TransactOpts)
}

type CommitStoreHelperConfigSetIterator struct {
	Event *CommitStoreHelperConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperConfigSetIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperConfigSet struct {
	StaticConfig  CommitStoreStaticConfig
	DynamicConfig CommitStoreDynamicConfig
	Raw           types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterConfigSet(opts *bind.FilterOpts) (*CommitStoreHelperConfigSetIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperConfigSetIterator{contract: _CommitStoreHelper.contract, event: "ConfigSet", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperConfigSet) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "ConfigSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperConfigSet)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "ConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseConfigSet(log types.Log) (*CommitStoreHelperConfigSet, error) {
	event := new(CommitStoreHelperConfigSet)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "ConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperConfigSet0Iterator struct {
	Event *CommitStoreHelperConfigSet0

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperConfigSet0Iterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperConfigSet0)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperConfigSet0)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperConfigSet0Iterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperConfigSet0Iterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperConfigSet0 struct {
	PreviousConfigBlockNumber uint32
	ConfigDigest              [32]byte
	ConfigCount               uint64
	Signers                   []common.Address
	Transmitters              []common.Address
	F                         uint8
	OnchainConfig             []byte
	OffchainConfigVersion     uint64
	OffchainConfig            []byte
	Raw                       types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterConfigSet0(opts *bind.FilterOpts) (*CommitStoreHelperConfigSet0Iterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "ConfigSet0")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperConfigSet0Iterator{contract: _CommitStoreHelper.contract, event: "ConfigSet0", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchConfigSet0(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperConfigSet0) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "ConfigSet0")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperConfigSet0)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "ConfigSet0", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseConfigSet0(log types.Log) (*CommitStoreHelperConfigSet0, error) {
	event := new(CommitStoreHelperConfigSet0)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "ConfigSet0", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperOwnershipTransferRequestedIterator struct {
	Event *CommitStoreHelperOwnershipTransferRequested

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperOwnershipTransferRequestedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreHelperOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperOwnershipTransferRequestedIterator{contract: _CommitStoreHelper.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperOwnershipTransferRequested)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseOwnershipTransferRequested(log types.Log) (*CommitStoreHelperOwnershipTransferRequested, error) {
	event := new(CommitStoreHelperOwnershipTransferRequested)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperOwnershipTransferredIterator struct {
	Event *CommitStoreHelperOwnershipTransferred

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperOwnershipTransferredIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperOwnershipTransferredIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreHelperOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperOwnershipTransferredIterator{contract: _CommitStoreHelper.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperOwnershipTransferred)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseOwnershipTransferred(log types.Log) (*CommitStoreHelperOwnershipTransferred, error) {
	event := new(CommitStoreHelperOwnershipTransferred)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperPausedIterator struct {
	Event *CommitStoreHelperPaused

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperPausedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperPaused)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperPaused)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperPausedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperPausedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperPaused struct {
	Account common.Address
	Raw     types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterPaused(opts *bind.FilterOpts) (*CommitStoreHelperPausedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "Paused")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperPausedIterator{contract: _CommitStoreHelper.contract, event: "Paused", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchPaused(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperPaused) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "Paused")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperPaused)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "Paused", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParsePaused(log types.Log) (*CommitStoreHelperPaused, error) {
	event := new(CommitStoreHelperPaused)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "Paused", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperReportAcceptedIterator struct {
	Event *CommitStoreHelperReportAccepted

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperReportAcceptedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperReportAccepted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperReportAccepted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperReportAcceptedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperReportAcceptedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperReportAccepted struct {
	Report CommitStoreCommitReport
	Raw    types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterReportAccepted(opts *bind.FilterOpts) (*CommitStoreHelperReportAcceptedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "ReportAccepted")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperReportAcceptedIterator{contract: _CommitStoreHelper.contract, event: "ReportAccepted", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchReportAccepted(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperReportAccepted) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "ReportAccepted")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperReportAccepted)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "ReportAccepted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseReportAccepted(log types.Log) (*CommitStoreHelperReportAccepted, error) {
	event := new(CommitStoreHelperReportAccepted)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "ReportAccepted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperRootRemovedIterator struct {
	Event *CommitStoreHelperRootRemoved

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperRootRemovedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperRootRemoved)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperRootRemoved)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperRootRemovedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperRootRemovedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperRootRemoved struct {
	Root [32]byte
	Raw  types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterRootRemoved(opts *bind.FilterOpts) (*CommitStoreHelperRootRemovedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "RootRemoved")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperRootRemovedIterator{contract: _CommitStoreHelper.contract, event: "RootRemoved", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchRootRemoved(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperRootRemoved) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "RootRemoved")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperRootRemoved)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "RootRemoved", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseRootRemoved(log types.Log) (*CommitStoreHelperRootRemoved, error) {
	event := new(CommitStoreHelperRootRemoved)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "RootRemoved", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperTransmittedIterator struct {
	Event *CommitStoreHelperTransmitted

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperTransmittedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperTransmitted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperTransmitted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperTransmittedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperTransmittedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperTransmitted struct {
	ConfigDigest [32]byte
	Epoch        uint32
	Raw          types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterTransmitted(opts *bind.FilterOpts) (*CommitStoreHelperTransmittedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "Transmitted")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperTransmittedIterator{contract: _CommitStoreHelper.contract, event: "Transmitted", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchTransmitted(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperTransmitted) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "Transmitted")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperTransmitted)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "Transmitted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseTransmitted(log types.Log) (*CommitStoreHelperTransmitted, error) {
	event := new(CommitStoreHelperTransmitted)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "Transmitted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CommitStoreHelperUnpausedIterator struct {
	Event *CommitStoreHelperUnpaused

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CommitStoreHelperUnpausedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CommitStoreHelperUnpaused)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CommitStoreHelperUnpaused)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CommitStoreHelperUnpausedIterator) Error() error {
	return it.fail
}

func (it *CommitStoreHelperUnpausedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CommitStoreHelperUnpaused struct {
	Account common.Address
	Raw     types.Log
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) FilterUnpaused(opts *bind.FilterOpts) (*CommitStoreHelperUnpausedIterator, error) {

	logs, sub, err := _CommitStoreHelper.contract.FilterLogs(opts, "Unpaused")
	if err != nil {
		return nil, err
	}
	return &CommitStoreHelperUnpausedIterator{contract: _CommitStoreHelper.contract, event: "Unpaused", logs: logs, sub: sub}, nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) WatchUnpaused(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperUnpaused) (event.Subscription, error) {

	logs, sub, err := _CommitStoreHelper.contract.WatchLogs(opts, "Unpaused")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CommitStoreHelperUnpaused)
				if err := _CommitStoreHelper.contract.UnpackLog(event, "Unpaused", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CommitStoreHelper *CommitStoreHelperFilterer) ParseUnpaused(log types.Log) (*CommitStoreHelperUnpaused, error) {
	event := new(CommitStoreHelperUnpaused)
	if err := _CommitStoreHelper.contract.UnpackLog(event, "Unpaused", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type LatestConfigDetails struct {
	ConfigCount  uint32
	BlockNumber  uint32
	ConfigDigest [32]byte
}
type LatestConfigDigestAndEpoch struct {
	ScanLogs     bool
	ConfigDigest [32]byte
	Epoch        uint32
}

func (_CommitStoreHelper *CommitStoreHelper) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _CommitStoreHelper.abi.Events["ConfigSet"].ID:
		return _CommitStoreHelper.ParseConfigSet(log)
	case _CommitStoreHelper.abi.Events["ConfigSet0"].ID:
		return _CommitStoreHelper.ParseConfigSet0(log)
	case _CommitStoreHelper.abi.Events["OwnershipTransferRequested"].ID:
		return _CommitStoreHelper.ParseOwnershipTransferRequested(log)
	case _CommitStoreHelper.abi.Events["OwnershipTransferred"].ID:
		return _CommitStoreHelper.ParseOwnershipTransferred(log)
	case _CommitStoreHelper.abi.Events["Paused"].ID:
		return _CommitStoreHelper.ParsePaused(log)
	case _CommitStoreHelper.abi.Events["ReportAccepted"].ID:
		return _CommitStoreHelper.ParseReportAccepted(log)
	case _CommitStoreHelper.abi.Events["RootRemoved"].ID:
		return _CommitStoreHelper.ParseRootRemoved(log)
	case _CommitStoreHelper.abi.Events["Transmitted"].ID:
		return _CommitStoreHelper.ParseTransmitted(log)
	case _CommitStoreHelper.abi.Events["Unpaused"].ID:
		return _CommitStoreHelper.ParseUnpaused(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (CommitStoreHelperConfigSet) Topic() common.Hash {
	return common.HexToHash("0xc9d7123efd4203e60b0f0a4b1dbc4800fc97ce63679f71c3a27279b24a7ddec3")
}

func (CommitStoreHelperConfigSet0) Topic() common.Hash {
	return common.HexToHash("0x1591690b8638f5fb2dbec82ac741805ac5da8b45dc5263f4875b0496fdce4e05")
}

func (CommitStoreHelperOwnershipTransferRequested) Topic() common.Hash {
	return common.HexToHash("0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278")
}

func (CommitStoreHelperOwnershipTransferred) Topic() common.Hash {
	return common.HexToHash("0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0")
}

func (CommitStoreHelperPaused) Topic() common.Hash {
	return common.HexToHash("0x62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258")
}

func (CommitStoreHelperReportAccepted) Topic() common.Hash {
	return common.HexToHash("0xe81b49e583122eb290c46fc255c962b9a2dec468816c00fb7a2e6ebc42dc92d4")
}

func (CommitStoreHelperRootRemoved) Topic() common.Hash {
	return common.HexToHash("0x202f1139a3e334b6056064c0e9b19fd07e44a88d8f6e5ded571b24cf8c371f12")
}

func (CommitStoreHelperTransmitted) Topic() common.Hash {
	return common.HexToHash("0xb04e63db38c49950639fa09d29872f21f5d49d614f3a969d8adf3d4b52e41a62")
}

func (CommitStoreHelperUnpaused) Topic() common.Hash {
	return common.HexToHash("0x5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa")
}

func (_CommitStoreHelper *CommitStoreHelper) Address() common.Address {
	return _CommitStoreHelper.address
}

type CommitStoreHelperInterface interface {
	GetDynamicConfig(opts *bind.CallOpts) (CommitStoreDynamicConfig, error)

	GetExpectedNextSequenceNumber(opts *bind.CallOpts) (uint64, error)

	GetLatestPriceEpochAndRound(opts *bind.CallOpts) (uint64, error)

	GetMerkleRoot(opts *bind.CallOpts, root [32]byte) (*big.Int, error)

	GetStaticConfig(opts *bind.CallOpts) (CommitStoreStaticConfig, error)

	GetTransmitters(opts *bind.CallOpts) ([]common.Address, error)

	IsARMHealthy(opts *bind.CallOpts) (bool, error)

	IsBlessed(opts *bind.CallOpts, root [32]byte) (bool, error)

	IsUnpausedAndARMHealthy(opts *bind.CallOpts) (bool, error)

	LatestConfigDetails(opts *bind.CallOpts) (LatestConfigDetails,

		error)

	LatestConfigDigestAndEpoch(opts *bind.CallOpts) (LatestConfigDigestAndEpoch,

		error)

	Owner(opts *bind.CallOpts) (common.Address, error)

	Paused(opts *bind.CallOpts) (bool, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	Verify(opts *bind.CallOpts, hashedLeaves [][32]byte, proofs [][32]byte, proofFlagBits *big.Int) (*big.Int, error)

	AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error)

	Pause(opts *bind.TransactOpts) (*types.Transaction, error)

	Report(opts *bind.TransactOpts, commitReport []byte, epochAndRound *big.Int) (*types.Transaction, error)

	ResetUnblessedRoots(opts *bind.TransactOpts, rootToReset [][32]byte) (*types.Transaction, error)

	SetLatestPriceEpochAndRound(opts *bind.TransactOpts, latestPriceEpochAndRound *big.Int) (*types.Transaction, error)

	SetMinSeqNr(opts *bind.TransactOpts, minSeqNr uint64) (*types.Transaction, error)

	SetOCR2Config(opts *bind.TransactOpts, signers []common.Address, transmitters []common.Address, f uint8, onchainConfig []byte, offchainConfigVersion uint64, offchainConfig []byte) (*types.Transaction, error)

	TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error)

	Transmit(opts *bind.TransactOpts, reportContext [3][32]byte, report []byte, rs [][32]byte, ss [][32]byte, rawVs [32]byte) (*types.Transaction, error)

	Unpause(opts *bind.TransactOpts) (*types.Transaction, error)

	FilterConfigSet(opts *bind.FilterOpts) (*CommitStoreHelperConfigSetIterator, error)

	WatchConfigSet(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperConfigSet) (event.Subscription, error)

	ParseConfigSet(log types.Log) (*CommitStoreHelperConfigSet, error)

	FilterConfigSet0(opts *bind.FilterOpts) (*CommitStoreHelperConfigSet0Iterator, error)

	WatchConfigSet0(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperConfigSet0) (event.Subscription, error)

	ParseConfigSet0(log types.Log) (*CommitStoreHelperConfigSet0, error)

	FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreHelperOwnershipTransferRequestedIterator, error)

	WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferRequested(log types.Log) (*CommitStoreHelperOwnershipTransferRequested, error)

	FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CommitStoreHelperOwnershipTransferredIterator, error)

	WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferred(log types.Log) (*CommitStoreHelperOwnershipTransferred, error)

	FilterPaused(opts *bind.FilterOpts) (*CommitStoreHelperPausedIterator, error)

	WatchPaused(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperPaused) (event.Subscription, error)

	ParsePaused(log types.Log) (*CommitStoreHelperPaused, error)

	FilterReportAccepted(opts *bind.FilterOpts) (*CommitStoreHelperReportAcceptedIterator, error)

	WatchReportAccepted(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperReportAccepted) (event.Subscription, error)

	ParseReportAccepted(log types.Log) (*CommitStoreHelperReportAccepted, error)

	FilterRootRemoved(opts *bind.FilterOpts) (*CommitStoreHelperRootRemovedIterator, error)

	WatchRootRemoved(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperRootRemoved) (event.Subscription, error)

	ParseRootRemoved(log types.Log) (*CommitStoreHelperRootRemoved, error)

	FilterTransmitted(opts *bind.FilterOpts) (*CommitStoreHelperTransmittedIterator, error)

	WatchTransmitted(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperTransmitted) (event.Subscription, error)

	ParseTransmitted(log types.Log) (*CommitStoreHelperTransmitted, error)

	FilterUnpaused(opts *bind.FilterOpts) (*CommitStoreHelperUnpausedIterator, error)

	WatchUnpaused(opts *bind.WatchOpts, sink chan<- *CommitStoreHelperUnpaused) (event.Subscription, error)

	ParseUnpaused(log types.Log) (*CommitStoreHelperUnpaused, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
