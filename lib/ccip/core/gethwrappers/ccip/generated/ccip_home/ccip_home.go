// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package ccip_home

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
	"github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

type CCIPHomeChainConfig struct {
	Readers [][32]byte
	FChain  uint8
	Config  []byte
}

type CCIPHomeChainConfigArgs struct {
	ChainSelector uint64
	ChainConfig   CCIPHomeChainConfig
}

type CCIPHomeOCR3Config struct {
	PluginType            uint8
	ChainSelector         uint64
	FRoleDON              uint8
	OffchainConfigVersion uint64
	OfframpAddress        []byte
	RmnHomeAddress        []byte
	Nodes                 []CCIPHomeOCR3Node
	OffchainConfig        []byte
}

type CCIPHomeOCR3Node struct {
	P2pId          [32]byte
	SignerKey      []byte
	TransmitterKey []byte
}

type CCIPHomeVersionedConfig struct {
	Version      uint32
	ConfigDigest [32]byte
	Config       CCIPHomeOCR3Config
}

var CCIPHomeMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"capabilitiesRegistry\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"CanOnlySelfCall\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"}],\"name\":\"ChainSelectorNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ChainSelectorNotSet\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"expectedConfigDigest\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"gotConfigDigest\",\"type\":\"bytes32\"}],\"name\":\"ConfigDigestMismatch\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"callDonId\",\"type\":\"uint32\"},{\"internalType\":\"uint32\",\"name\":\"capabilityRegistryDonId\",\"type\":\"uint32\"}],\"name\":\"DONIdMismatch\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FChainMustBePositive\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"fChain\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"FRoleDON\",\"type\":\"uint256\"}],\"name\":\"FChainTooHigh\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FTooHigh\",\"type\":\"error\"},{\"inputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"p2pId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"signerKey\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"transmitterKey\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Node\",\"name\":\"node\",\"type\":\"tuple\"}],\"name\":\"InvalidNode\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidPluginType\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"selector\",\"type\":\"bytes4\"}],\"name\":\"InvalidSelector\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"currentActiveDigest\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"currentCandidateDigest\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"proposedActiveDigest\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"proposedCandidateDigest\",\"type\":\"bytes32\"}],\"name\":\"InvalidStateTransition\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoOpStateTransitionNotAllowed\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"p2pId\",\"type\":\"bytes32\"}],\"name\":\"NodeNotInRegistry\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"got\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"minimum\",\"type\":\"uint256\"}],\"name\":\"NotEnoughTransmitters\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OfframpAddressCannotBeZero\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"OnlyCapabilitiesRegistryCanCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RMNHomeAddressCannotBeZero\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RevokingZeroDigestNotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TooManySigners\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ZeroAddressNotAllowed\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"}],\"name\":\"ActiveConfigRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"}],\"name\":\"CandidateConfigRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[],\"name\":\"CapabilityConfigurationSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"}],\"name\":\"ChainConfigRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"components\":[{\"internalType\":\"bytes32[]\",\"name\":\"readers\",\"type\":\"bytes32[]\"},{\"internalType\":\"uint8\",\"name\":\"fChain\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"config\",\"type\":\"bytes\"}],\"indexed\":false,\"internalType\":\"structCCIPHome.ChainConfig\",\"name\":\"chainConfig\",\"type\":\"tuple\"}],\"name\":\"ChainConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"}],\"name\":\"ConfigPromoted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint32\",\"name\":\"version\",\"type\":\"uint32\"},{\"components\":[{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint8\",\"name\":\"FRoleDON\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"offrampAddress\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"rmnHomeAddress\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"p2pId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"signerKey\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"transmitterKey\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Node[]\",\"name\":\"nodes\",\"type\":\"tuple[]\"},{\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"indexed\":false,\"internalType\":\"structCCIPHome.OCR3Config\",\"name\":\"config\",\"type\":\"tuple\"}],\"name\":\"ConfigSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint64[]\",\"name\":\"chainSelectorRemoves\",\"type\":\"uint64[]\"},{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"components\":[{\"internalType\":\"bytes32[]\",\"name\":\"readers\",\"type\":\"bytes32[]\"},{\"internalType\":\"uint8\",\"name\":\"fChain\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"config\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.ChainConfig\",\"name\":\"chainConfig\",\"type\":\"tuple\"}],\"internalType\":\"structCCIPHome.ChainConfigArgs[]\",\"name\":\"chainConfigAdds\",\"type\":\"tuple[]\"}],\"name\":\"applyChainConfigUpdates\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32[]\",\"name\":\"\",\"type\":\"bytes32[]\"},{\"internalType\":\"bytes\",\"name\":\"update\",\"type\":\"bytes\"},{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"donId\",\"type\":\"uint32\"}],\"name\":\"beforeCapabilityConfigSet\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"donId\",\"type\":\"uint32\"},{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"}],\"name\":\"getActiveDigest\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"pageIndex\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"pageSize\",\"type\":\"uint256\"}],\"name\":\"getAllChainConfigs\",\"outputs\":[{\"components\":[{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"components\":[{\"internalType\":\"bytes32[]\",\"name\":\"readers\",\"type\":\"bytes32[]\"},{\"internalType\":\"uint8\",\"name\":\"fChain\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"config\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.ChainConfig\",\"name\":\"chainConfig\",\"type\":\"tuple\"}],\"internalType\":\"structCCIPHome.ChainConfigArgs[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"donId\",\"type\":\"uint32\"},{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"}],\"name\":\"getAllConfigs\",\"outputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"version\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint8\",\"name\":\"FRoleDON\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"offrampAddress\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"rmnHomeAddress\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"p2pId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"signerKey\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"transmitterKey\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Node[]\",\"name\":\"nodes\",\"type\":\"tuple[]\"},{\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Config\",\"name\":\"config\",\"type\":\"tuple\"}],\"internalType\":\"structCCIPHome.VersionedConfig\",\"name\":\"activeConfig\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint32\",\"name\":\"version\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint8\",\"name\":\"FRoleDON\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"offrampAddress\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"rmnHomeAddress\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"p2pId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"signerKey\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"transmitterKey\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Node[]\",\"name\":\"nodes\",\"type\":\"tuple[]\"},{\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Config\",\"name\":\"config\",\"type\":\"tuple\"}],\"internalType\":\"structCCIPHome.VersionedConfig\",\"name\":\"candidateConfig\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"donId\",\"type\":\"uint32\"},{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"}],\"name\":\"getCandidateDigest\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"\",\"type\":\"uint32\"}],\"name\":\"getCapabilityConfiguration\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"configuration\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCapabilityRegistry\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"donId\",\"type\":\"uint32\"},{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"}],\"name\":\"getConfig\",\"outputs\":[{\"components\":[{\"internalType\":\"uint32\",\"name\":\"version\",\"type\":\"uint32\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"},{\"components\":[{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint8\",\"name\":\"FRoleDON\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"offrampAddress\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"rmnHomeAddress\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"p2pId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"signerKey\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"transmitterKey\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Node[]\",\"name\":\"nodes\",\"type\":\"tuple[]\"},{\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Config\",\"name\":\"config\",\"type\":\"tuple\"}],\"internalType\":\"structCCIPHome.VersionedConfig\",\"name\":\"versionedConfig\",\"type\":\"tuple\"},{\"internalType\":\"bool\",\"name\":\"ok\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"donId\",\"type\":\"uint32\"},{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"}],\"name\":\"getConfigDigests\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"activeConfigDigest\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"candidateConfigDigest\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getNumChainConfigurations\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"donId\",\"type\":\"uint32\"},{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"digestToPromote\",\"type\":\"bytes32\"},{\"internalType\":\"bytes32\",\"name\":\"digestToRevoke\",\"type\":\"bytes32\"}],\"name\":\"promoteCandidateAndRevokeActive\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"donId\",\"type\":\"uint32\"},{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"},{\"internalType\":\"bytes32\",\"name\":\"configDigest\",\"type\":\"bytes32\"}],\"name\":\"revokeCandidate\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"donId\",\"type\":\"uint32\"},{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"},{\"components\":[{\"internalType\":\"enumInternal.OCRPluginType\",\"name\":\"pluginType\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"chainSelector\",\"type\":\"uint64\"},{\"internalType\":\"uint8\",\"name\":\"FRoleDON\",\"type\":\"uint8\"},{\"internalType\":\"uint64\",\"name\":\"offchainConfigVersion\",\"type\":\"uint64\"},{\"internalType\":\"bytes\",\"name\":\"offrampAddress\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"rmnHomeAddress\",\"type\":\"bytes\"},{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"p2pId\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"signerKey\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"transmitterKey\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Node[]\",\"name\":\"nodes\",\"type\":\"tuple[]\"},{\"internalType\":\"bytes\",\"name\":\"offchainConfig\",\"type\":\"bytes\"}],\"internalType\":\"structCCIPHome.OCR3Config\",\"name\":\"config\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"digestToOverwrite\",\"type\":\"bytes32\"}],\"name\":\"setCandidate\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"newConfigDigest\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"typeAndVersion\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"}]",
	Bin: "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",
}

var CCIPHomeABI = CCIPHomeMetaData.ABI

var CCIPHomeBin = CCIPHomeMetaData.Bin

func DeployCCIPHome(auth *bind.TransactOpts, backend bind.ContractBackend, capabilitiesRegistry common.Address) (common.Address, *types.Transaction, *CCIPHome, error) {
	parsed, err := CCIPHomeMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(CCIPHomeBin), backend, capabilitiesRegistry)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &CCIPHome{address: address, abi: *parsed, CCIPHomeCaller: CCIPHomeCaller{contract: contract}, CCIPHomeTransactor: CCIPHomeTransactor{contract: contract}, CCIPHomeFilterer: CCIPHomeFilterer{contract: contract}}, nil
}

type CCIPHome struct {
	address common.Address
	abi     abi.ABI
	CCIPHomeCaller
	CCIPHomeTransactor
	CCIPHomeFilterer
}

type CCIPHomeCaller struct {
	contract *bind.BoundContract
}

type CCIPHomeTransactor struct {
	contract *bind.BoundContract
}

type CCIPHomeFilterer struct {
	contract *bind.BoundContract
}

type CCIPHomeSession struct {
	Contract     *CCIPHome
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type CCIPHomeCallerSession struct {
	Contract *CCIPHomeCaller
	CallOpts bind.CallOpts
}

type CCIPHomeTransactorSession struct {
	Contract     *CCIPHomeTransactor
	TransactOpts bind.TransactOpts
}

type CCIPHomeRaw struct {
	Contract *CCIPHome
}

type CCIPHomeCallerRaw struct {
	Contract *CCIPHomeCaller
}

type CCIPHomeTransactorRaw struct {
	Contract *CCIPHomeTransactor
}

func NewCCIPHome(address common.Address, backend bind.ContractBackend) (*CCIPHome, error) {
	abi, err := abi.JSON(strings.NewReader(CCIPHomeABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindCCIPHome(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &CCIPHome{address: address, abi: abi, CCIPHomeCaller: CCIPHomeCaller{contract: contract}, CCIPHomeTransactor: CCIPHomeTransactor{contract: contract}, CCIPHomeFilterer: CCIPHomeFilterer{contract: contract}}, nil
}

func NewCCIPHomeCaller(address common.Address, caller bind.ContractCaller) (*CCIPHomeCaller, error) {
	contract, err := bindCCIPHome(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &CCIPHomeCaller{contract: contract}, nil
}

func NewCCIPHomeTransactor(address common.Address, transactor bind.ContractTransactor) (*CCIPHomeTransactor, error) {
	contract, err := bindCCIPHome(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &CCIPHomeTransactor{contract: contract}, nil
}

func NewCCIPHomeFilterer(address common.Address, filterer bind.ContractFilterer) (*CCIPHomeFilterer, error) {
	contract, err := bindCCIPHome(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &CCIPHomeFilterer{contract: contract}, nil
}

func bindCCIPHome(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := CCIPHomeMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_CCIPHome *CCIPHomeRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CCIPHome.Contract.CCIPHomeCaller.contract.Call(opts, result, method, params...)
}

func (_CCIPHome *CCIPHomeRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CCIPHome.Contract.CCIPHomeTransactor.contract.Transfer(opts)
}

func (_CCIPHome *CCIPHomeRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CCIPHome.Contract.CCIPHomeTransactor.contract.Transact(opts, method, params...)
}

func (_CCIPHome *CCIPHomeCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _CCIPHome.Contract.contract.Call(opts, result, method, params...)
}

func (_CCIPHome *CCIPHomeTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CCIPHome.Contract.contract.Transfer(opts)
}

func (_CCIPHome *CCIPHomeTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _CCIPHome.Contract.contract.Transact(opts, method, params...)
}

func (_CCIPHome *CCIPHomeCaller) GetActiveDigest(opts *bind.CallOpts, donId uint32, pluginType uint8) ([32]byte, error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "getActiveDigest", donId, pluginType)

	if err != nil {
		return *new([32]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([32]byte)).(*[32]byte)

	return out0, err

}

func (_CCIPHome *CCIPHomeSession) GetActiveDigest(donId uint32, pluginType uint8) ([32]byte, error) {
	return _CCIPHome.Contract.GetActiveDigest(&_CCIPHome.CallOpts, donId, pluginType)
}

func (_CCIPHome *CCIPHomeCallerSession) GetActiveDigest(donId uint32, pluginType uint8) ([32]byte, error) {
	return _CCIPHome.Contract.GetActiveDigest(&_CCIPHome.CallOpts, donId, pluginType)
}

func (_CCIPHome *CCIPHomeCaller) GetAllChainConfigs(opts *bind.CallOpts, pageIndex *big.Int, pageSize *big.Int) ([]CCIPHomeChainConfigArgs, error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "getAllChainConfigs", pageIndex, pageSize)

	if err != nil {
		return *new([]CCIPHomeChainConfigArgs), err
	}

	out0 := *abi.ConvertType(out[0], new([]CCIPHomeChainConfigArgs)).(*[]CCIPHomeChainConfigArgs)

	return out0, err

}

func (_CCIPHome *CCIPHomeSession) GetAllChainConfigs(pageIndex *big.Int, pageSize *big.Int) ([]CCIPHomeChainConfigArgs, error) {
	return _CCIPHome.Contract.GetAllChainConfigs(&_CCIPHome.CallOpts, pageIndex, pageSize)
}

func (_CCIPHome *CCIPHomeCallerSession) GetAllChainConfigs(pageIndex *big.Int, pageSize *big.Int) ([]CCIPHomeChainConfigArgs, error) {
	return _CCIPHome.Contract.GetAllChainConfigs(&_CCIPHome.CallOpts, pageIndex, pageSize)
}

func (_CCIPHome *CCIPHomeCaller) GetAllConfigs(opts *bind.CallOpts, donId uint32, pluginType uint8) (GetAllConfigs,

	error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "getAllConfigs", donId, pluginType)

	outstruct := new(GetAllConfigs)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ActiveConfig = *abi.ConvertType(out[0], new(CCIPHomeVersionedConfig)).(*CCIPHomeVersionedConfig)
	outstruct.CandidateConfig = *abi.ConvertType(out[1], new(CCIPHomeVersionedConfig)).(*CCIPHomeVersionedConfig)

	return *outstruct, err

}

func (_CCIPHome *CCIPHomeSession) GetAllConfigs(donId uint32, pluginType uint8) (GetAllConfigs,

	error) {
	return _CCIPHome.Contract.GetAllConfigs(&_CCIPHome.CallOpts, donId, pluginType)
}

func (_CCIPHome *CCIPHomeCallerSession) GetAllConfigs(donId uint32, pluginType uint8) (GetAllConfigs,

	error) {
	return _CCIPHome.Contract.GetAllConfigs(&_CCIPHome.CallOpts, donId, pluginType)
}

func (_CCIPHome *CCIPHomeCaller) GetCandidateDigest(opts *bind.CallOpts, donId uint32, pluginType uint8) ([32]byte, error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "getCandidateDigest", donId, pluginType)

	if err != nil {
		return *new([32]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([32]byte)).(*[32]byte)

	return out0, err

}

func (_CCIPHome *CCIPHomeSession) GetCandidateDigest(donId uint32, pluginType uint8) ([32]byte, error) {
	return _CCIPHome.Contract.GetCandidateDigest(&_CCIPHome.CallOpts, donId, pluginType)
}

func (_CCIPHome *CCIPHomeCallerSession) GetCandidateDigest(donId uint32, pluginType uint8) ([32]byte, error) {
	return _CCIPHome.Contract.GetCandidateDigest(&_CCIPHome.CallOpts, donId, pluginType)
}

func (_CCIPHome *CCIPHomeCaller) GetCapabilityConfiguration(opts *bind.CallOpts, arg0 uint32) ([]byte, error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "getCapabilityConfiguration", arg0)

	if err != nil {
		return *new([]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([]byte)).(*[]byte)

	return out0, err

}

func (_CCIPHome *CCIPHomeSession) GetCapabilityConfiguration(arg0 uint32) ([]byte, error) {
	return _CCIPHome.Contract.GetCapabilityConfiguration(&_CCIPHome.CallOpts, arg0)
}

func (_CCIPHome *CCIPHomeCallerSession) GetCapabilityConfiguration(arg0 uint32) ([]byte, error) {
	return _CCIPHome.Contract.GetCapabilityConfiguration(&_CCIPHome.CallOpts, arg0)
}

func (_CCIPHome *CCIPHomeCaller) GetCapabilityRegistry(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "getCapabilityRegistry")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_CCIPHome *CCIPHomeSession) GetCapabilityRegistry() (common.Address, error) {
	return _CCIPHome.Contract.GetCapabilityRegistry(&_CCIPHome.CallOpts)
}

func (_CCIPHome *CCIPHomeCallerSession) GetCapabilityRegistry() (common.Address, error) {
	return _CCIPHome.Contract.GetCapabilityRegistry(&_CCIPHome.CallOpts)
}

func (_CCIPHome *CCIPHomeCaller) GetConfig(opts *bind.CallOpts, donId uint32, pluginType uint8, configDigest [32]byte) (GetConfig,

	error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "getConfig", donId, pluginType, configDigest)

	outstruct := new(GetConfig)
	if err != nil {
		return *outstruct, err
	}

	outstruct.VersionedConfig = *abi.ConvertType(out[0], new(CCIPHomeVersionedConfig)).(*CCIPHomeVersionedConfig)
	outstruct.Ok = *abi.ConvertType(out[1], new(bool)).(*bool)

	return *outstruct, err

}

func (_CCIPHome *CCIPHomeSession) GetConfig(donId uint32, pluginType uint8, configDigest [32]byte) (GetConfig,

	error) {
	return _CCIPHome.Contract.GetConfig(&_CCIPHome.CallOpts, donId, pluginType, configDigest)
}

func (_CCIPHome *CCIPHomeCallerSession) GetConfig(donId uint32, pluginType uint8, configDigest [32]byte) (GetConfig,

	error) {
	return _CCIPHome.Contract.GetConfig(&_CCIPHome.CallOpts, donId, pluginType, configDigest)
}

func (_CCIPHome *CCIPHomeCaller) GetConfigDigests(opts *bind.CallOpts, donId uint32, pluginType uint8) (GetConfigDigests,

	error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "getConfigDigests", donId, pluginType)

	outstruct := new(GetConfigDigests)
	if err != nil {
		return *outstruct, err
	}

	outstruct.ActiveConfigDigest = *abi.ConvertType(out[0], new([32]byte)).(*[32]byte)
	outstruct.CandidateConfigDigest = *abi.ConvertType(out[1], new([32]byte)).(*[32]byte)

	return *outstruct, err

}

func (_CCIPHome *CCIPHomeSession) GetConfigDigests(donId uint32, pluginType uint8) (GetConfigDigests,

	error) {
	return _CCIPHome.Contract.GetConfigDigests(&_CCIPHome.CallOpts, donId, pluginType)
}

func (_CCIPHome *CCIPHomeCallerSession) GetConfigDigests(donId uint32, pluginType uint8) (GetConfigDigests,

	error) {
	return _CCIPHome.Contract.GetConfigDigests(&_CCIPHome.CallOpts, donId, pluginType)
}

func (_CCIPHome *CCIPHomeCaller) GetNumChainConfigurations(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "getNumChainConfigurations")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

func (_CCIPHome *CCIPHomeSession) GetNumChainConfigurations() (*big.Int, error) {
	return _CCIPHome.Contract.GetNumChainConfigurations(&_CCIPHome.CallOpts)
}

func (_CCIPHome *CCIPHomeCallerSession) GetNumChainConfigurations() (*big.Int, error) {
	return _CCIPHome.Contract.GetNumChainConfigurations(&_CCIPHome.CallOpts)
}

func (_CCIPHome *CCIPHomeCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_CCIPHome *CCIPHomeSession) Owner() (common.Address, error) {
	return _CCIPHome.Contract.Owner(&_CCIPHome.CallOpts)
}

func (_CCIPHome *CCIPHomeCallerSession) Owner() (common.Address, error) {
	return _CCIPHome.Contract.Owner(&_CCIPHome.CallOpts)
}

func (_CCIPHome *CCIPHomeCaller) SupportsInterface(opts *bind.CallOpts, interfaceId [4]byte) (bool, error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "supportsInterface", interfaceId)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_CCIPHome *CCIPHomeSession) SupportsInterface(interfaceId [4]byte) (bool, error) {
	return _CCIPHome.Contract.SupportsInterface(&_CCIPHome.CallOpts, interfaceId)
}

func (_CCIPHome *CCIPHomeCallerSession) SupportsInterface(interfaceId [4]byte) (bool, error) {
	return _CCIPHome.Contract.SupportsInterface(&_CCIPHome.CallOpts, interfaceId)
}

func (_CCIPHome *CCIPHomeCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _CCIPHome.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_CCIPHome *CCIPHomeSession) TypeAndVersion() (string, error) {
	return _CCIPHome.Contract.TypeAndVersion(&_CCIPHome.CallOpts)
}

func (_CCIPHome *CCIPHomeCallerSession) TypeAndVersion() (string, error) {
	return _CCIPHome.Contract.TypeAndVersion(&_CCIPHome.CallOpts)
}

func (_CCIPHome *CCIPHomeTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _CCIPHome.contract.Transact(opts, "acceptOwnership")
}

func (_CCIPHome *CCIPHomeSession) AcceptOwnership() (*types.Transaction, error) {
	return _CCIPHome.Contract.AcceptOwnership(&_CCIPHome.TransactOpts)
}

func (_CCIPHome *CCIPHomeTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _CCIPHome.Contract.AcceptOwnership(&_CCIPHome.TransactOpts)
}

func (_CCIPHome *CCIPHomeTransactor) ApplyChainConfigUpdates(opts *bind.TransactOpts, chainSelectorRemoves []uint64, chainConfigAdds []CCIPHomeChainConfigArgs) (*types.Transaction, error) {
	return _CCIPHome.contract.Transact(opts, "applyChainConfigUpdates", chainSelectorRemoves, chainConfigAdds)
}

func (_CCIPHome *CCIPHomeSession) ApplyChainConfigUpdates(chainSelectorRemoves []uint64, chainConfigAdds []CCIPHomeChainConfigArgs) (*types.Transaction, error) {
	return _CCIPHome.Contract.ApplyChainConfigUpdates(&_CCIPHome.TransactOpts, chainSelectorRemoves, chainConfigAdds)
}

func (_CCIPHome *CCIPHomeTransactorSession) ApplyChainConfigUpdates(chainSelectorRemoves []uint64, chainConfigAdds []CCIPHomeChainConfigArgs) (*types.Transaction, error) {
	return _CCIPHome.Contract.ApplyChainConfigUpdates(&_CCIPHome.TransactOpts, chainSelectorRemoves, chainConfigAdds)
}

func (_CCIPHome *CCIPHomeTransactor) BeforeCapabilityConfigSet(opts *bind.TransactOpts, arg0 [][32]byte, update []byte, arg2 uint64, donId uint32) (*types.Transaction, error) {
	return _CCIPHome.contract.Transact(opts, "beforeCapabilityConfigSet", arg0, update, arg2, donId)
}

func (_CCIPHome *CCIPHomeSession) BeforeCapabilityConfigSet(arg0 [][32]byte, update []byte, arg2 uint64, donId uint32) (*types.Transaction, error) {
	return _CCIPHome.Contract.BeforeCapabilityConfigSet(&_CCIPHome.TransactOpts, arg0, update, arg2, donId)
}

func (_CCIPHome *CCIPHomeTransactorSession) BeforeCapabilityConfigSet(arg0 [][32]byte, update []byte, arg2 uint64, donId uint32) (*types.Transaction, error) {
	return _CCIPHome.Contract.BeforeCapabilityConfigSet(&_CCIPHome.TransactOpts, arg0, update, arg2, donId)
}

func (_CCIPHome *CCIPHomeTransactor) PromoteCandidateAndRevokeActive(opts *bind.TransactOpts, donId uint32, pluginType uint8, digestToPromote [32]byte, digestToRevoke [32]byte) (*types.Transaction, error) {
	return _CCIPHome.contract.Transact(opts, "promoteCandidateAndRevokeActive", donId, pluginType, digestToPromote, digestToRevoke)
}

func (_CCIPHome *CCIPHomeSession) PromoteCandidateAndRevokeActive(donId uint32, pluginType uint8, digestToPromote [32]byte, digestToRevoke [32]byte) (*types.Transaction, error) {
	return _CCIPHome.Contract.PromoteCandidateAndRevokeActive(&_CCIPHome.TransactOpts, donId, pluginType, digestToPromote, digestToRevoke)
}

func (_CCIPHome *CCIPHomeTransactorSession) PromoteCandidateAndRevokeActive(donId uint32, pluginType uint8, digestToPromote [32]byte, digestToRevoke [32]byte) (*types.Transaction, error) {
	return _CCIPHome.Contract.PromoteCandidateAndRevokeActive(&_CCIPHome.TransactOpts, donId, pluginType, digestToPromote, digestToRevoke)
}

func (_CCIPHome *CCIPHomeTransactor) RevokeCandidate(opts *bind.TransactOpts, donId uint32, pluginType uint8, configDigest [32]byte) (*types.Transaction, error) {
	return _CCIPHome.contract.Transact(opts, "revokeCandidate", donId, pluginType, configDigest)
}

func (_CCIPHome *CCIPHomeSession) RevokeCandidate(donId uint32, pluginType uint8, configDigest [32]byte) (*types.Transaction, error) {
	return _CCIPHome.Contract.RevokeCandidate(&_CCIPHome.TransactOpts, donId, pluginType, configDigest)
}

func (_CCIPHome *CCIPHomeTransactorSession) RevokeCandidate(donId uint32, pluginType uint8, configDigest [32]byte) (*types.Transaction, error) {
	return _CCIPHome.Contract.RevokeCandidate(&_CCIPHome.TransactOpts, donId, pluginType, configDigest)
}

func (_CCIPHome *CCIPHomeTransactor) SetCandidate(opts *bind.TransactOpts, donId uint32, pluginType uint8, config CCIPHomeOCR3Config, digestToOverwrite [32]byte) (*types.Transaction, error) {
	return _CCIPHome.contract.Transact(opts, "setCandidate", donId, pluginType, config, digestToOverwrite)
}

func (_CCIPHome *CCIPHomeSession) SetCandidate(donId uint32, pluginType uint8, config CCIPHomeOCR3Config, digestToOverwrite [32]byte) (*types.Transaction, error) {
	return _CCIPHome.Contract.SetCandidate(&_CCIPHome.TransactOpts, donId, pluginType, config, digestToOverwrite)
}

func (_CCIPHome *CCIPHomeTransactorSession) SetCandidate(donId uint32, pluginType uint8, config CCIPHomeOCR3Config, digestToOverwrite [32]byte) (*types.Transaction, error) {
	return _CCIPHome.Contract.SetCandidate(&_CCIPHome.TransactOpts, donId, pluginType, config, digestToOverwrite)
}

func (_CCIPHome *CCIPHomeTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _CCIPHome.contract.Transact(opts, "transferOwnership", to)
}

func (_CCIPHome *CCIPHomeSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _CCIPHome.Contract.TransferOwnership(&_CCIPHome.TransactOpts, to)
}

func (_CCIPHome *CCIPHomeTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _CCIPHome.Contract.TransferOwnership(&_CCIPHome.TransactOpts, to)
}

type CCIPHomeActiveConfigRevokedIterator struct {
	Event *CCIPHomeActiveConfigRevoked

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CCIPHomeActiveConfigRevokedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CCIPHomeActiveConfigRevoked)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CCIPHomeActiveConfigRevoked)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CCIPHomeActiveConfigRevokedIterator) Error() error {
	return it.fail
}

func (it *CCIPHomeActiveConfigRevokedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CCIPHomeActiveConfigRevoked struct {
	ConfigDigest [32]byte
	Raw          types.Log
}

func (_CCIPHome *CCIPHomeFilterer) FilterActiveConfigRevoked(opts *bind.FilterOpts, configDigest [][32]byte) (*CCIPHomeActiveConfigRevokedIterator, error) {

	var configDigestRule []interface{}
	for _, configDigestItem := range configDigest {
		configDigestRule = append(configDigestRule, configDigestItem)
	}

	logs, sub, err := _CCIPHome.contract.FilterLogs(opts, "ActiveConfigRevoked", configDigestRule)
	if err != nil {
		return nil, err
	}
	return &CCIPHomeActiveConfigRevokedIterator{contract: _CCIPHome.contract, event: "ActiveConfigRevoked", logs: logs, sub: sub}, nil
}

func (_CCIPHome *CCIPHomeFilterer) WatchActiveConfigRevoked(opts *bind.WatchOpts, sink chan<- *CCIPHomeActiveConfigRevoked, configDigest [][32]byte) (event.Subscription, error) {

	var configDigestRule []interface{}
	for _, configDigestItem := range configDigest {
		configDigestRule = append(configDigestRule, configDigestItem)
	}

	logs, sub, err := _CCIPHome.contract.WatchLogs(opts, "ActiveConfigRevoked", configDigestRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CCIPHomeActiveConfigRevoked)
				if err := _CCIPHome.contract.UnpackLog(event, "ActiveConfigRevoked", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CCIPHome *CCIPHomeFilterer) ParseActiveConfigRevoked(log types.Log) (*CCIPHomeActiveConfigRevoked, error) {
	event := new(CCIPHomeActiveConfigRevoked)
	if err := _CCIPHome.contract.UnpackLog(event, "ActiveConfigRevoked", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CCIPHomeCandidateConfigRevokedIterator struct {
	Event *CCIPHomeCandidateConfigRevoked

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CCIPHomeCandidateConfigRevokedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CCIPHomeCandidateConfigRevoked)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CCIPHomeCandidateConfigRevoked)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CCIPHomeCandidateConfigRevokedIterator) Error() error {
	return it.fail
}

func (it *CCIPHomeCandidateConfigRevokedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CCIPHomeCandidateConfigRevoked struct {
	ConfigDigest [32]byte
	Raw          types.Log
}

func (_CCIPHome *CCIPHomeFilterer) FilterCandidateConfigRevoked(opts *bind.FilterOpts, configDigest [][32]byte) (*CCIPHomeCandidateConfigRevokedIterator, error) {

	var configDigestRule []interface{}
	for _, configDigestItem := range configDigest {
		configDigestRule = append(configDigestRule, configDigestItem)
	}

	logs, sub, err := _CCIPHome.contract.FilterLogs(opts, "CandidateConfigRevoked", configDigestRule)
	if err != nil {
		return nil, err
	}
	return &CCIPHomeCandidateConfigRevokedIterator{contract: _CCIPHome.contract, event: "CandidateConfigRevoked", logs: logs, sub: sub}, nil
}

func (_CCIPHome *CCIPHomeFilterer) WatchCandidateConfigRevoked(opts *bind.WatchOpts, sink chan<- *CCIPHomeCandidateConfigRevoked, configDigest [][32]byte) (event.Subscription, error) {

	var configDigestRule []interface{}
	for _, configDigestItem := range configDigest {
		configDigestRule = append(configDigestRule, configDigestItem)
	}

	logs, sub, err := _CCIPHome.contract.WatchLogs(opts, "CandidateConfigRevoked", configDigestRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CCIPHomeCandidateConfigRevoked)
				if err := _CCIPHome.contract.UnpackLog(event, "CandidateConfigRevoked", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CCIPHome *CCIPHomeFilterer) ParseCandidateConfigRevoked(log types.Log) (*CCIPHomeCandidateConfigRevoked, error) {
	event := new(CCIPHomeCandidateConfigRevoked)
	if err := _CCIPHome.contract.UnpackLog(event, "CandidateConfigRevoked", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CCIPHomeCapabilityConfigurationSetIterator struct {
	Event *CCIPHomeCapabilityConfigurationSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CCIPHomeCapabilityConfigurationSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CCIPHomeCapabilityConfigurationSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CCIPHomeCapabilityConfigurationSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CCIPHomeCapabilityConfigurationSetIterator) Error() error {
	return it.fail
}

func (it *CCIPHomeCapabilityConfigurationSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CCIPHomeCapabilityConfigurationSet struct {
	Raw types.Log
}

func (_CCIPHome *CCIPHomeFilterer) FilterCapabilityConfigurationSet(opts *bind.FilterOpts) (*CCIPHomeCapabilityConfigurationSetIterator, error) {

	logs, sub, err := _CCIPHome.contract.FilterLogs(opts, "CapabilityConfigurationSet")
	if err != nil {
		return nil, err
	}
	return &CCIPHomeCapabilityConfigurationSetIterator{contract: _CCIPHome.contract, event: "CapabilityConfigurationSet", logs: logs, sub: sub}, nil
}

func (_CCIPHome *CCIPHomeFilterer) WatchCapabilityConfigurationSet(opts *bind.WatchOpts, sink chan<- *CCIPHomeCapabilityConfigurationSet) (event.Subscription, error) {

	logs, sub, err := _CCIPHome.contract.WatchLogs(opts, "CapabilityConfigurationSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CCIPHomeCapabilityConfigurationSet)
				if err := _CCIPHome.contract.UnpackLog(event, "CapabilityConfigurationSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CCIPHome *CCIPHomeFilterer) ParseCapabilityConfigurationSet(log types.Log) (*CCIPHomeCapabilityConfigurationSet, error) {
	event := new(CCIPHomeCapabilityConfigurationSet)
	if err := _CCIPHome.contract.UnpackLog(event, "CapabilityConfigurationSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CCIPHomeChainConfigRemovedIterator struct {
	Event *CCIPHomeChainConfigRemoved

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CCIPHomeChainConfigRemovedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CCIPHomeChainConfigRemoved)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CCIPHomeChainConfigRemoved)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CCIPHomeChainConfigRemovedIterator) Error() error {
	return it.fail
}

func (it *CCIPHomeChainConfigRemovedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CCIPHomeChainConfigRemoved struct {
	ChainSelector uint64
	Raw           types.Log
}

func (_CCIPHome *CCIPHomeFilterer) FilterChainConfigRemoved(opts *bind.FilterOpts) (*CCIPHomeChainConfigRemovedIterator, error) {

	logs, sub, err := _CCIPHome.contract.FilterLogs(opts, "ChainConfigRemoved")
	if err != nil {
		return nil, err
	}
	return &CCIPHomeChainConfigRemovedIterator{contract: _CCIPHome.contract, event: "ChainConfigRemoved", logs: logs, sub: sub}, nil
}

func (_CCIPHome *CCIPHomeFilterer) WatchChainConfigRemoved(opts *bind.WatchOpts, sink chan<- *CCIPHomeChainConfigRemoved) (event.Subscription, error) {

	logs, sub, err := _CCIPHome.contract.WatchLogs(opts, "ChainConfigRemoved")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CCIPHomeChainConfigRemoved)
				if err := _CCIPHome.contract.UnpackLog(event, "ChainConfigRemoved", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CCIPHome *CCIPHomeFilterer) ParseChainConfigRemoved(log types.Log) (*CCIPHomeChainConfigRemoved, error) {
	event := new(CCIPHomeChainConfigRemoved)
	if err := _CCIPHome.contract.UnpackLog(event, "ChainConfigRemoved", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CCIPHomeChainConfigSetIterator struct {
	Event *CCIPHomeChainConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CCIPHomeChainConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CCIPHomeChainConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CCIPHomeChainConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CCIPHomeChainConfigSetIterator) Error() error {
	return it.fail
}

func (it *CCIPHomeChainConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CCIPHomeChainConfigSet struct {
	ChainSelector uint64
	ChainConfig   CCIPHomeChainConfig
	Raw           types.Log
}

func (_CCIPHome *CCIPHomeFilterer) FilterChainConfigSet(opts *bind.FilterOpts) (*CCIPHomeChainConfigSetIterator, error) {

	logs, sub, err := _CCIPHome.contract.FilterLogs(opts, "ChainConfigSet")
	if err != nil {
		return nil, err
	}
	return &CCIPHomeChainConfigSetIterator{contract: _CCIPHome.contract, event: "ChainConfigSet", logs: logs, sub: sub}, nil
}

func (_CCIPHome *CCIPHomeFilterer) WatchChainConfigSet(opts *bind.WatchOpts, sink chan<- *CCIPHomeChainConfigSet) (event.Subscription, error) {

	logs, sub, err := _CCIPHome.contract.WatchLogs(opts, "ChainConfigSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CCIPHomeChainConfigSet)
				if err := _CCIPHome.contract.UnpackLog(event, "ChainConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CCIPHome *CCIPHomeFilterer) ParseChainConfigSet(log types.Log) (*CCIPHomeChainConfigSet, error) {
	event := new(CCIPHomeChainConfigSet)
	if err := _CCIPHome.contract.UnpackLog(event, "ChainConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CCIPHomeConfigPromotedIterator struct {
	Event *CCIPHomeConfigPromoted

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CCIPHomeConfigPromotedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CCIPHomeConfigPromoted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CCIPHomeConfigPromoted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CCIPHomeConfigPromotedIterator) Error() error {
	return it.fail
}

func (it *CCIPHomeConfigPromotedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CCIPHomeConfigPromoted struct {
	ConfigDigest [32]byte
	Raw          types.Log
}

func (_CCIPHome *CCIPHomeFilterer) FilterConfigPromoted(opts *bind.FilterOpts, configDigest [][32]byte) (*CCIPHomeConfigPromotedIterator, error) {

	var configDigestRule []interface{}
	for _, configDigestItem := range configDigest {
		configDigestRule = append(configDigestRule, configDigestItem)
	}

	logs, sub, err := _CCIPHome.contract.FilterLogs(opts, "ConfigPromoted", configDigestRule)
	if err != nil {
		return nil, err
	}
	return &CCIPHomeConfigPromotedIterator{contract: _CCIPHome.contract, event: "ConfigPromoted", logs: logs, sub: sub}, nil
}

func (_CCIPHome *CCIPHomeFilterer) WatchConfigPromoted(opts *bind.WatchOpts, sink chan<- *CCIPHomeConfigPromoted, configDigest [][32]byte) (event.Subscription, error) {

	var configDigestRule []interface{}
	for _, configDigestItem := range configDigest {
		configDigestRule = append(configDigestRule, configDigestItem)
	}

	logs, sub, err := _CCIPHome.contract.WatchLogs(opts, "ConfigPromoted", configDigestRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CCIPHomeConfigPromoted)
				if err := _CCIPHome.contract.UnpackLog(event, "ConfigPromoted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CCIPHome *CCIPHomeFilterer) ParseConfigPromoted(log types.Log) (*CCIPHomeConfigPromoted, error) {
	event := new(CCIPHomeConfigPromoted)
	if err := _CCIPHome.contract.UnpackLog(event, "ConfigPromoted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CCIPHomeConfigSetIterator struct {
	Event *CCIPHomeConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CCIPHomeConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CCIPHomeConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CCIPHomeConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CCIPHomeConfigSetIterator) Error() error {
	return it.fail
}

func (it *CCIPHomeConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CCIPHomeConfigSet struct {
	ConfigDigest [32]byte
	Version      uint32
	Config       CCIPHomeOCR3Config
	Raw          types.Log
}

func (_CCIPHome *CCIPHomeFilterer) FilterConfigSet(opts *bind.FilterOpts, configDigest [][32]byte) (*CCIPHomeConfigSetIterator, error) {

	var configDigestRule []interface{}
	for _, configDigestItem := range configDigest {
		configDigestRule = append(configDigestRule, configDigestItem)
	}

	logs, sub, err := _CCIPHome.contract.FilterLogs(opts, "ConfigSet", configDigestRule)
	if err != nil {
		return nil, err
	}
	return &CCIPHomeConfigSetIterator{contract: _CCIPHome.contract, event: "ConfigSet", logs: logs, sub: sub}, nil
}

func (_CCIPHome *CCIPHomeFilterer) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *CCIPHomeConfigSet, configDigest [][32]byte) (event.Subscription, error) {

	var configDigestRule []interface{}
	for _, configDigestItem := range configDigest {
		configDigestRule = append(configDigestRule, configDigestItem)
	}

	logs, sub, err := _CCIPHome.contract.WatchLogs(opts, "ConfigSet", configDigestRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CCIPHomeConfigSet)
				if err := _CCIPHome.contract.UnpackLog(event, "ConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CCIPHome *CCIPHomeFilterer) ParseConfigSet(log types.Log) (*CCIPHomeConfigSet, error) {
	event := new(CCIPHomeConfigSet)
	if err := _CCIPHome.contract.UnpackLog(event, "ConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CCIPHomeOwnershipTransferRequestedIterator struct {
	Event *CCIPHomeOwnershipTransferRequested

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CCIPHomeOwnershipTransferRequestedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CCIPHomeOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CCIPHomeOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CCIPHomeOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

func (it *CCIPHomeOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CCIPHomeOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_CCIPHome *CCIPHomeFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CCIPHomeOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CCIPHome.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &CCIPHomeOwnershipTransferRequestedIterator{contract: _CCIPHome.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

func (_CCIPHome *CCIPHomeFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *CCIPHomeOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CCIPHome.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CCIPHomeOwnershipTransferRequested)
				if err := _CCIPHome.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CCIPHome *CCIPHomeFilterer) ParseOwnershipTransferRequested(log types.Log) (*CCIPHomeOwnershipTransferRequested, error) {
	event := new(CCIPHomeOwnershipTransferRequested)
	if err := _CCIPHome.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type CCIPHomeOwnershipTransferredIterator struct {
	Event *CCIPHomeOwnershipTransferred

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *CCIPHomeOwnershipTransferredIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(CCIPHomeOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(CCIPHomeOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *CCIPHomeOwnershipTransferredIterator) Error() error {
	return it.fail
}

func (it *CCIPHomeOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type CCIPHomeOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_CCIPHome *CCIPHomeFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CCIPHomeOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CCIPHome.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &CCIPHomeOwnershipTransferredIterator{contract: _CCIPHome.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

func (_CCIPHome *CCIPHomeFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CCIPHomeOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _CCIPHome.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(CCIPHomeOwnershipTransferred)
				if err := _CCIPHome.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_CCIPHome *CCIPHomeFilterer) ParseOwnershipTransferred(log types.Log) (*CCIPHomeOwnershipTransferred, error) {
	event := new(CCIPHomeOwnershipTransferred)
	if err := _CCIPHome.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type GetAllConfigs struct {
	ActiveConfig    CCIPHomeVersionedConfig
	CandidateConfig CCIPHomeVersionedConfig
}
type GetConfig struct {
	VersionedConfig CCIPHomeVersionedConfig
	Ok              bool
}
type GetConfigDigests struct {
	ActiveConfigDigest    [32]byte
	CandidateConfigDigest [32]byte
}

func (_CCIPHome *CCIPHome) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _CCIPHome.abi.Events["ActiveConfigRevoked"].ID:
		return _CCIPHome.ParseActiveConfigRevoked(log)
	case _CCIPHome.abi.Events["CandidateConfigRevoked"].ID:
		return _CCIPHome.ParseCandidateConfigRevoked(log)
	case _CCIPHome.abi.Events["CapabilityConfigurationSet"].ID:
		return _CCIPHome.ParseCapabilityConfigurationSet(log)
	case _CCIPHome.abi.Events["ChainConfigRemoved"].ID:
		return _CCIPHome.ParseChainConfigRemoved(log)
	case _CCIPHome.abi.Events["ChainConfigSet"].ID:
		return _CCIPHome.ParseChainConfigSet(log)
	case _CCIPHome.abi.Events["ConfigPromoted"].ID:
		return _CCIPHome.ParseConfigPromoted(log)
	case _CCIPHome.abi.Events["ConfigSet"].ID:
		return _CCIPHome.ParseConfigSet(log)
	case _CCIPHome.abi.Events["OwnershipTransferRequested"].ID:
		return _CCIPHome.ParseOwnershipTransferRequested(log)
	case _CCIPHome.abi.Events["OwnershipTransferred"].ID:
		return _CCIPHome.ParseOwnershipTransferred(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (CCIPHomeActiveConfigRevoked) Topic() common.Hash {
	return common.HexToHash("0x0b31c0055e2d464bef7781994b98c4ff9ef4ae0d05f59feb6a68c42de5e201b8")
}

func (CCIPHomeCandidateConfigRevoked) Topic() common.Hash {
	return common.HexToHash("0x53f5d9228f0a4173bea6e5931c9b3afe6eeb6692ede1d182952970f152534e3b")
}

func (CCIPHomeCapabilityConfigurationSet) Topic() common.Hash {
	return common.HexToHash("0x84ad7751b744c9e2ee77da1d902b428aec7f0a343d67a24bbe2142e6f58a8d0f")
}

func (CCIPHomeChainConfigRemoved) Topic() common.Hash {
	return common.HexToHash("0x2a680691fef3b2d105196805935232c661ce703e92d464ef0b94a7bc62d714f0")
}

func (CCIPHomeChainConfigSet) Topic() common.Hash {
	return common.HexToHash("0x05dd57854af2c291a94ea52e7c43d80bc3be7fa73022f98b735dea86642fa5e0")
}

func (CCIPHomeConfigPromoted) Topic() common.Hash {
	return common.HexToHash("0xfc3e98dbbd47c3fa7c1c05b6ec711caeaf70eca4554192b9ada8fc11a37f298e")
}

func (CCIPHomeConfigSet) Topic() common.Hash {
	return common.HexToHash("0x94f085b7c57ec2a270befd0b7b2ec7452580040edee8bb0fb04609c81f0359c6")
}

func (CCIPHomeOwnershipTransferRequested) Topic() common.Hash {
	return common.HexToHash("0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278")
}

func (CCIPHomeOwnershipTransferred) Topic() common.Hash {
	return common.HexToHash("0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0")
}

func (_CCIPHome *CCIPHome) Address() common.Address {
	return _CCIPHome.address
}

type CCIPHomeInterface interface {
	GetActiveDigest(opts *bind.CallOpts, donId uint32, pluginType uint8) ([32]byte, error)

	GetAllChainConfigs(opts *bind.CallOpts, pageIndex *big.Int, pageSize *big.Int) ([]CCIPHomeChainConfigArgs, error)

	GetAllConfigs(opts *bind.CallOpts, donId uint32, pluginType uint8) (GetAllConfigs,

		error)

	GetCandidateDigest(opts *bind.CallOpts, donId uint32, pluginType uint8) ([32]byte, error)

	GetCapabilityConfiguration(opts *bind.CallOpts, arg0 uint32) ([]byte, error)

	GetCapabilityRegistry(opts *bind.CallOpts) (common.Address, error)

	GetConfig(opts *bind.CallOpts, donId uint32, pluginType uint8, configDigest [32]byte) (GetConfig,

		error)

	GetConfigDigests(opts *bind.CallOpts, donId uint32, pluginType uint8) (GetConfigDigests,

		error)

	GetNumChainConfigurations(opts *bind.CallOpts) (*big.Int, error)

	Owner(opts *bind.CallOpts) (common.Address, error)

	SupportsInterface(opts *bind.CallOpts, interfaceId [4]byte) (bool, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error)

	ApplyChainConfigUpdates(opts *bind.TransactOpts, chainSelectorRemoves []uint64, chainConfigAdds []CCIPHomeChainConfigArgs) (*types.Transaction, error)

	BeforeCapabilityConfigSet(opts *bind.TransactOpts, arg0 [][32]byte, update []byte, arg2 uint64, donId uint32) (*types.Transaction, error)

	PromoteCandidateAndRevokeActive(opts *bind.TransactOpts, donId uint32, pluginType uint8, digestToPromote [32]byte, digestToRevoke [32]byte) (*types.Transaction, error)

	RevokeCandidate(opts *bind.TransactOpts, donId uint32, pluginType uint8, configDigest [32]byte) (*types.Transaction, error)

	SetCandidate(opts *bind.TransactOpts, donId uint32, pluginType uint8, config CCIPHomeOCR3Config, digestToOverwrite [32]byte) (*types.Transaction, error)

	TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error)

	FilterActiveConfigRevoked(opts *bind.FilterOpts, configDigest [][32]byte) (*CCIPHomeActiveConfigRevokedIterator, error)

	WatchActiveConfigRevoked(opts *bind.WatchOpts, sink chan<- *CCIPHomeActiveConfigRevoked, configDigest [][32]byte) (event.Subscription, error)

	ParseActiveConfigRevoked(log types.Log) (*CCIPHomeActiveConfigRevoked, error)

	FilterCandidateConfigRevoked(opts *bind.FilterOpts, configDigest [][32]byte) (*CCIPHomeCandidateConfigRevokedIterator, error)

	WatchCandidateConfigRevoked(opts *bind.WatchOpts, sink chan<- *CCIPHomeCandidateConfigRevoked, configDigest [][32]byte) (event.Subscription, error)

	ParseCandidateConfigRevoked(log types.Log) (*CCIPHomeCandidateConfigRevoked, error)

	FilterCapabilityConfigurationSet(opts *bind.FilterOpts) (*CCIPHomeCapabilityConfigurationSetIterator, error)

	WatchCapabilityConfigurationSet(opts *bind.WatchOpts, sink chan<- *CCIPHomeCapabilityConfigurationSet) (event.Subscription, error)

	ParseCapabilityConfigurationSet(log types.Log) (*CCIPHomeCapabilityConfigurationSet, error)

	FilterChainConfigRemoved(opts *bind.FilterOpts) (*CCIPHomeChainConfigRemovedIterator, error)

	WatchChainConfigRemoved(opts *bind.WatchOpts, sink chan<- *CCIPHomeChainConfigRemoved) (event.Subscription, error)

	ParseChainConfigRemoved(log types.Log) (*CCIPHomeChainConfigRemoved, error)

	FilterChainConfigSet(opts *bind.FilterOpts) (*CCIPHomeChainConfigSetIterator, error)

	WatchChainConfigSet(opts *bind.WatchOpts, sink chan<- *CCIPHomeChainConfigSet) (event.Subscription, error)

	ParseChainConfigSet(log types.Log) (*CCIPHomeChainConfigSet, error)

	FilterConfigPromoted(opts *bind.FilterOpts, configDigest [][32]byte) (*CCIPHomeConfigPromotedIterator, error)

	WatchConfigPromoted(opts *bind.WatchOpts, sink chan<- *CCIPHomeConfigPromoted, configDigest [][32]byte) (event.Subscription, error)

	ParseConfigPromoted(log types.Log) (*CCIPHomeConfigPromoted, error)

	FilterConfigSet(opts *bind.FilterOpts, configDigest [][32]byte) (*CCIPHomeConfigSetIterator, error)

	WatchConfigSet(opts *bind.WatchOpts, sink chan<- *CCIPHomeConfigSet, configDigest [][32]byte) (event.Subscription, error)

	ParseConfigSet(log types.Log) (*CCIPHomeConfigSet, error)

	FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CCIPHomeOwnershipTransferRequestedIterator, error)

	WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *CCIPHomeOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferRequested(log types.Log) (*CCIPHomeOwnershipTransferRequested, error)

	FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*CCIPHomeOwnershipTransferredIterator, error)

	WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *CCIPHomeOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferred(log types.Log) (*CCIPHomeOwnershipTransferred, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
