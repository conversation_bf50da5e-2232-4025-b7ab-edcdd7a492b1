// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_arbitrum_l2_bridge_adapter

import (
	big "math/big"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// ArbitrumL2BridgeAdapterInterface is an autogenerated mock type for the ArbitrumL2BridgeAdapterInterface type
type ArbitrumL2BridgeAdapterInterface struct {
	mock.Mock
}

type ArbitrumL2BridgeAdapterInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *ArbitrumL2BridgeAdapterInterface) EXPECT() *ArbitrumL2BridgeAdapterInterface_Expecter {
	return &ArbitrumL2BridgeAdapterInterface_Expecter{mock: &_m.Mock}
}

// Address provides a mock function with given fields:
func (_m *ArbitrumL2BridgeAdapterInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// ArbitrumL2BridgeAdapterInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type ArbitrumL2BridgeAdapterInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *ArbitrumL2BridgeAdapterInterface_Expecter) Address() *ArbitrumL2BridgeAdapterInterface_Address_Call {
	return &ArbitrumL2BridgeAdapterInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *ArbitrumL2BridgeAdapterInterface_Address_Call) Run(run func()) *ArbitrumL2BridgeAdapterInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_Address_Call) Return(_a0 common.Address) *ArbitrumL2BridgeAdapterInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_Address_Call) RunAndReturn(run func() common.Address) *ArbitrumL2BridgeAdapterInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// DepositNativeToL1 provides a mock function with given fields: opts, recipient
func (_m *ArbitrumL2BridgeAdapterInterface) DepositNativeToL1(opts *bind.TransactOpts, recipient common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, recipient)

	if len(ret) == 0 {
		panic("no return value specified for DepositNativeToL1")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, recipient)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, recipient)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, recipient)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DepositNativeToL1'
type ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call struct {
	*mock.Call
}

// DepositNativeToL1 is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - recipient common.Address
func (_e *ArbitrumL2BridgeAdapterInterface_Expecter) DepositNativeToL1(opts interface{}, recipient interface{}) *ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call {
	return &ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call{Call: _e.mock.On("DepositNativeToL1", opts, recipient)}
}

func (_c *ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call) Run(run func(opts *bind.TransactOpts, recipient common.Address)) *ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *ArbitrumL2BridgeAdapterInterface_DepositNativeToL1_Call {
	_c.Call.Return(run)
	return _c
}

// FinalizeWithdrawERC20 provides a mock function with given fields: opts, arg0, arg1, arg2
func (_m *ArbitrumL2BridgeAdapterInterface) FinalizeWithdrawERC20(opts *bind.CallOpts, arg0 common.Address, arg1 common.Address, arg2 []byte) (bool, error) {
	ret := _m.Called(opts, arg0, arg1, arg2)

	if len(ret) == 0 {
		panic("no return value specified for FinalizeWithdrawERC20")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, common.Address, []byte) (bool, error)); ok {
		return rf(opts, arg0, arg1, arg2)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, common.Address, []byte) bool); ok {
		r0 = rf(opts, arg0, arg1, arg2)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address, common.Address, []byte) error); ok {
		r1 = rf(opts, arg0, arg1, arg2)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FinalizeWithdrawERC20'
type ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call struct {
	*mock.Call
}

// FinalizeWithdrawERC20 is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - arg0 common.Address
//   - arg1 common.Address
//   - arg2 []byte
func (_e *ArbitrumL2BridgeAdapterInterface_Expecter) FinalizeWithdrawERC20(opts interface{}, arg0 interface{}, arg1 interface{}, arg2 interface{}) *ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call {
	return &ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call{Call: _e.mock.On("FinalizeWithdrawERC20", opts, arg0, arg1, arg2)}
}

func (_c *ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call) Run(run func(opts *bind.CallOpts, arg0 common.Address, arg1 common.Address, arg2 []byte)) *ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address), args[2].(common.Address), args[3].([]byte))
	})
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call) Return(_a0 bool, _a1 error) *ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call) RunAndReturn(run func(*bind.CallOpts, common.Address, common.Address, []byte) (bool, error)) *ArbitrumL2BridgeAdapterInterface_FinalizeWithdrawERC20_Call {
	_c.Call.Return(run)
	return _c
}

// GetBridgeFeeInNative provides a mock function with given fields: opts
func (_m *ArbitrumL2BridgeAdapterInterface) GetBridgeFeeInNative(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetBridgeFeeInNative")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetBridgeFeeInNative'
type ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call struct {
	*mock.Call
}

// GetBridgeFeeInNative is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbitrumL2BridgeAdapterInterface_Expecter) GetBridgeFeeInNative(opts interface{}) *ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call {
	return &ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call{Call: _e.mock.On("GetBridgeFeeInNative", opts)}
}

func (_c *ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call) Run(run func(opts *bind.CallOpts)) *ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call) Return(_a0 *big.Int, _a1 error) *ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *ArbitrumL2BridgeAdapterInterface_GetBridgeFeeInNative_Call {
	_c.Call.Return(run)
	return _c
}

// SendERC20 provides a mock function with given fields: opts, localToken, remoteToken, recipient, amount, arg4
func (_m *ArbitrumL2BridgeAdapterInterface) SendERC20(opts *bind.TransactOpts, localToken common.Address, remoteToken common.Address, recipient common.Address, amount *big.Int, arg4 []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, localToken, remoteToken, recipient, amount, arg4)

	if len(ret) == 0 {
		panic("no return value specified for SendERC20")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, common.Address, common.Address, *big.Int, []byte) (*types.Transaction, error)); ok {
		return rf(opts, localToken, remoteToken, recipient, amount, arg4)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, common.Address, common.Address, *big.Int, []byte) *types.Transaction); ok {
		r0 = rf(opts, localToken, remoteToken, recipient, amount, arg4)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, common.Address, common.Address, *big.Int, []byte) error); ok {
		r1 = rf(opts, localToken, remoteToken, recipient, amount, arg4)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumL2BridgeAdapterInterface_SendERC20_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendERC20'
type ArbitrumL2BridgeAdapterInterface_SendERC20_Call struct {
	*mock.Call
}

// SendERC20 is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - localToken common.Address
//   - remoteToken common.Address
//   - recipient common.Address
//   - amount *big.Int
//   - arg4 []byte
func (_e *ArbitrumL2BridgeAdapterInterface_Expecter) SendERC20(opts interface{}, localToken interface{}, remoteToken interface{}, recipient interface{}, amount interface{}, arg4 interface{}) *ArbitrumL2BridgeAdapterInterface_SendERC20_Call {
	return &ArbitrumL2BridgeAdapterInterface_SendERC20_Call{Call: _e.mock.On("SendERC20", opts, localToken, remoteToken, recipient, amount, arg4)}
}

func (_c *ArbitrumL2BridgeAdapterInterface_SendERC20_Call) Run(run func(opts *bind.TransactOpts, localToken common.Address, remoteToken common.Address, recipient common.Address, amount *big.Int, arg4 []byte)) *ArbitrumL2BridgeAdapterInterface_SendERC20_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(common.Address), args[3].(common.Address), args[4].(*big.Int), args[5].([]byte))
	})
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_SendERC20_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumL2BridgeAdapterInterface_SendERC20_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumL2BridgeAdapterInterface_SendERC20_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, common.Address, common.Address, *big.Int, []byte) (*types.Transaction, error)) *ArbitrumL2BridgeAdapterInterface_SendERC20_Call {
	_c.Call.Return(run)
	return _c
}

// NewArbitrumL2BridgeAdapterInterface creates a new instance of ArbitrumL2BridgeAdapterInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewArbitrumL2BridgeAdapterInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *ArbitrumL2BridgeAdapterInterface {
	mock := &ArbitrumL2BridgeAdapterInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
