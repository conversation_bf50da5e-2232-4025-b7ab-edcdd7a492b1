// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_optimism_l2_output_oracle

import (
	big "math/big"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	mock "github.com/stretchr/testify/mock"

	optimism_l2_output_oracle "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/liquiditymanager/generated/optimism_l2_output_oracle"
)

// OptimismL2OutputOracleInterface is an autogenerated mock type for the OptimismL2OutputOracleInterface type
type OptimismL2OutputOracleInterface struct {
	mock.Mock
}

type OptimismL2OutputOracleInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *OptimismL2OutputOracleInterface) EXPECT() *OptimismL2OutputOracleInterface_Expecter {
	return &OptimismL2OutputOracleInterface_Expecter{mock: &_m.<PERSON>ck}
}

// Address provides a mock function with given fields:
func (_m *OptimismL2OutputOracleInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// OptimismL2OutputOracleInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type OptimismL2OutputOracleInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *OptimismL2OutputOracleInterface_Expecter) Address() *OptimismL2OutputOracleInterface_Address_Call {
	return &OptimismL2OutputOracleInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *OptimismL2OutputOracleInterface_Address_Call) Run(run func()) *OptimismL2OutputOracleInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OptimismL2OutputOracleInterface_Address_Call) Return(_a0 common.Address) *OptimismL2OutputOracleInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OptimismL2OutputOracleInterface_Address_Call) RunAndReturn(run func() common.Address) *OptimismL2OutputOracleInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// GetL2Output provides a mock function with given fields: opts, _l2OutputIndex
func (_m *OptimismL2OutputOracleInterface) GetL2Output(opts *bind.CallOpts, _l2OutputIndex *big.Int) (optimism_l2_output_oracle.TypesOutputProposal, error) {
	ret := _m.Called(opts, _l2OutputIndex)

	if len(ret) == 0 {
		panic("no return value specified for GetL2Output")
	}

	var r0 optimism_l2_output_oracle.TypesOutputProposal
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) (optimism_l2_output_oracle.TypesOutputProposal, error)); ok {
		return rf(opts, _l2OutputIndex)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) optimism_l2_output_oracle.TypesOutputProposal); ok {
		r0 = rf(opts, _l2OutputIndex)
	} else {
		r0 = ret.Get(0).(optimism_l2_output_oracle.TypesOutputProposal)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, *big.Int) error); ok {
		r1 = rf(opts, _l2OutputIndex)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OptimismL2OutputOracleInterface_GetL2Output_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetL2Output'
type OptimismL2OutputOracleInterface_GetL2Output_Call struct {
	*mock.Call
}

// GetL2Output is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - _l2OutputIndex *big.Int
func (_e *OptimismL2OutputOracleInterface_Expecter) GetL2Output(opts interface{}, _l2OutputIndex interface{}) *OptimismL2OutputOracleInterface_GetL2Output_Call {
	return &OptimismL2OutputOracleInterface_GetL2Output_Call{Call: _e.mock.On("GetL2Output", opts, _l2OutputIndex)}
}

func (_c *OptimismL2OutputOracleInterface_GetL2Output_Call) Run(run func(opts *bind.CallOpts, _l2OutputIndex *big.Int)) *OptimismL2OutputOracleInterface_GetL2Output_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(*big.Int))
	})
	return _c
}

func (_c *OptimismL2OutputOracleInterface_GetL2Output_Call) Return(_a0 optimism_l2_output_oracle.TypesOutputProposal, _a1 error) *OptimismL2OutputOracleInterface_GetL2Output_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OptimismL2OutputOracleInterface_GetL2Output_Call) RunAndReturn(run func(*bind.CallOpts, *big.Int) (optimism_l2_output_oracle.TypesOutputProposal, error)) *OptimismL2OutputOracleInterface_GetL2Output_Call {
	_c.Call.Return(run)
	return _c
}

// GetL2OutputIndexAfter provides a mock function with given fields: opts, _l2BlockNumber
func (_m *OptimismL2OutputOracleInterface) GetL2OutputIndexAfter(opts *bind.CallOpts, _l2BlockNumber *big.Int) (*big.Int, error) {
	ret := _m.Called(opts, _l2BlockNumber)

	if len(ret) == 0 {
		panic("no return value specified for GetL2OutputIndexAfter")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) (*big.Int, error)); ok {
		return rf(opts, _l2BlockNumber)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) *big.Int); ok {
		r0 = rf(opts, _l2BlockNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, *big.Int) error); ok {
		r1 = rf(opts, _l2BlockNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetL2OutputIndexAfter'
type OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call struct {
	*mock.Call
}

// GetL2OutputIndexAfter is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - _l2BlockNumber *big.Int
func (_e *OptimismL2OutputOracleInterface_Expecter) GetL2OutputIndexAfter(opts interface{}, _l2BlockNumber interface{}) *OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call {
	return &OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call{Call: _e.mock.On("GetL2OutputIndexAfter", opts, _l2BlockNumber)}
}

func (_c *OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call) Run(run func(opts *bind.CallOpts, _l2BlockNumber *big.Int)) *OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(*big.Int))
	})
	return _c
}

func (_c *OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call) Return(_a0 *big.Int, _a1 error) *OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call) RunAndReturn(run func(*bind.CallOpts, *big.Int) (*big.Int, error)) *OptimismL2OutputOracleInterface_GetL2OutputIndexAfter_Call {
	_c.Call.Return(run)
	return _c
}

// NewOptimismL2OutputOracleInterface creates a new instance of OptimismL2OutputOracleInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOptimismL2OutputOracleInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *OptimismL2OutputOracleInterface {
	mock := &OptimismL2OutputOracleInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
