// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_arbitrum_rollup_core

import (
	big "math/big"

	arbitrum_rollup_core "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/liquiditymanager/generated/arbitrum_rollup_core"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"

	common "github.com/ethereum/go-ethereum/common"

	event "github.com/ethereum/go-ethereum/event"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// ArbRollupCoreInterface is an autogenerated mock type for the ArbRollupCoreInterface type
type ArbRollupCoreInterface struct {
	mock.Mock
}

type ArbRollupCoreInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *ArbRollupCoreInterface) EXPECT() *ArbRollupCoreInterface_Expecter {
	return &ArbRollupCoreInterface_Expecter{mock: &_m.Mock}
}

// Address provides a mock function with given fields:
func (_m *ArbRollupCoreInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// ArbRollupCoreInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type ArbRollupCoreInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *ArbRollupCoreInterface_Expecter) Address() *ArbRollupCoreInterface_Address_Call {
	return &ArbRollupCoreInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *ArbRollupCoreInterface_Address_Call) Run(run func()) *ArbRollupCoreInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ArbRollupCoreInterface_Address_Call) Return(_a0 common.Address) *ArbRollupCoreInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ArbRollupCoreInterface_Address_Call) RunAndReturn(run func() common.Address) *ArbRollupCoreInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// AmountStaked provides a mock function with given fields: opts, staker
func (_m *ArbRollupCoreInterface) AmountStaked(opts *bind.CallOpts, staker common.Address) (*big.Int, error) {
	ret := _m.Called(opts, staker)

	if len(ret) == 0 {
		panic("no return value specified for AmountStaked")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (*big.Int, error)); ok {
		return rf(opts, staker)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) *big.Int); ok {
		r0 = rf(opts, staker)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, staker)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_AmountStaked_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AmountStaked'
type ArbRollupCoreInterface_AmountStaked_Call struct {
	*mock.Call
}

// AmountStaked is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - staker common.Address
func (_e *ArbRollupCoreInterface_Expecter) AmountStaked(opts interface{}, staker interface{}) *ArbRollupCoreInterface_AmountStaked_Call {
	return &ArbRollupCoreInterface_AmountStaked_Call{Call: _e.mock.On("AmountStaked", opts, staker)}
}

func (_c *ArbRollupCoreInterface_AmountStaked_Call) Run(run func(opts *bind.CallOpts, staker common.Address)) *ArbRollupCoreInterface_AmountStaked_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_AmountStaked_Call) Return(_a0 *big.Int, _a1 error) *ArbRollupCoreInterface_AmountStaked_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_AmountStaked_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (*big.Int, error)) *ArbRollupCoreInterface_AmountStaked_Call {
	_c.Call.Return(run)
	return _c
}

// BaseStake provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) BaseStake(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for BaseStake")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_BaseStake_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BaseStake'
type ArbRollupCoreInterface_BaseStake_Call struct {
	*mock.Call
}

// BaseStake is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) BaseStake(opts interface{}) *ArbRollupCoreInterface_BaseStake_Call {
	return &ArbRollupCoreInterface_BaseStake_Call{Call: _e.mock.On("BaseStake", opts)}
}

func (_c *ArbRollupCoreInterface_BaseStake_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_BaseStake_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_BaseStake_Call) Return(_a0 *big.Int, _a1 error) *ArbRollupCoreInterface_BaseStake_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_BaseStake_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *ArbRollupCoreInterface_BaseStake_Call {
	_c.Call.Return(run)
	return _c
}

// Bridge provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) Bridge(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Bridge")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_Bridge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Bridge'
type ArbRollupCoreInterface_Bridge_Call struct {
	*mock.Call
}

// Bridge is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) Bridge(opts interface{}) *ArbRollupCoreInterface_Bridge_Call {
	return &ArbRollupCoreInterface_Bridge_Call{Call: _e.mock.On("Bridge", opts)}
}

func (_c *ArbRollupCoreInterface_Bridge_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_Bridge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_Bridge_Call) Return(_a0 common.Address, _a1 error) *ArbRollupCoreInterface_Bridge_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_Bridge_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbRollupCoreInterface_Bridge_Call {
	_c.Call.Return(run)
	return _c
}

// ChainId provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) ChainId(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for ChainId")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ChainId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChainId'
type ArbRollupCoreInterface_ChainId_Call struct {
	*mock.Call
}

// ChainId is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) ChainId(opts interface{}) *ArbRollupCoreInterface_ChainId_Call {
	return &ArbRollupCoreInterface_ChainId_Call{Call: _e.mock.On("ChainId", opts)}
}

func (_c *ArbRollupCoreInterface_ChainId_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_ChainId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ChainId_Call) Return(_a0 *big.Int, _a1 error) *ArbRollupCoreInterface_ChainId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ChainId_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *ArbRollupCoreInterface_ChainId_Call {
	_c.Call.Return(run)
	return _c
}

// ChallengeManager provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) ChallengeManager(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for ChallengeManager")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ChallengeManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ChallengeManager'
type ArbRollupCoreInterface_ChallengeManager_Call struct {
	*mock.Call
}

// ChallengeManager is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) ChallengeManager(opts interface{}) *ArbRollupCoreInterface_ChallengeManager_Call {
	return &ArbRollupCoreInterface_ChallengeManager_Call{Call: _e.mock.On("ChallengeManager", opts)}
}

func (_c *ArbRollupCoreInterface_ChallengeManager_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_ChallengeManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ChallengeManager_Call) Return(_a0 common.Address, _a1 error) *ArbRollupCoreInterface_ChallengeManager_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ChallengeManager_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbRollupCoreInterface_ChallengeManager_Call {
	_c.Call.Return(run)
	return _c
}

// ConfirmPeriodBlocks provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) ConfirmPeriodBlocks(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for ConfirmPeriodBlocks")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ConfirmPeriodBlocks_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ConfirmPeriodBlocks'
type ArbRollupCoreInterface_ConfirmPeriodBlocks_Call struct {
	*mock.Call
}

// ConfirmPeriodBlocks is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) ConfirmPeriodBlocks(opts interface{}) *ArbRollupCoreInterface_ConfirmPeriodBlocks_Call {
	return &ArbRollupCoreInterface_ConfirmPeriodBlocks_Call{Call: _e.mock.On("ConfirmPeriodBlocks", opts)}
}

func (_c *ArbRollupCoreInterface_ConfirmPeriodBlocks_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_ConfirmPeriodBlocks_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ConfirmPeriodBlocks_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_ConfirmPeriodBlocks_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ConfirmPeriodBlocks_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *ArbRollupCoreInterface_ConfirmPeriodBlocks_Call {
	_c.Call.Return(run)
	return _c
}

// CurrentChallenge provides a mock function with given fields: opts, staker
func (_m *ArbRollupCoreInterface) CurrentChallenge(opts *bind.CallOpts, staker common.Address) (uint64, error) {
	ret := _m.Called(opts, staker)

	if len(ret) == 0 {
		panic("no return value specified for CurrentChallenge")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (uint64, error)); ok {
		return rf(opts, staker)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) uint64); ok {
		r0 = rf(opts, staker)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, staker)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_CurrentChallenge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CurrentChallenge'
type ArbRollupCoreInterface_CurrentChallenge_Call struct {
	*mock.Call
}

// CurrentChallenge is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - staker common.Address
func (_e *ArbRollupCoreInterface_Expecter) CurrentChallenge(opts interface{}, staker interface{}) *ArbRollupCoreInterface_CurrentChallenge_Call {
	return &ArbRollupCoreInterface_CurrentChallenge_Call{Call: _e.mock.On("CurrentChallenge", opts, staker)}
}

func (_c *ArbRollupCoreInterface_CurrentChallenge_Call) Run(run func(opts *bind.CallOpts, staker common.Address)) *ArbRollupCoreInterface_CurrentChallenge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_CurrentChallenge_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_CurrentChallenge_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_CurrentChallenge_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (uint64, error)) *ArbRollupCoreInterface_CurrentChallenge_Call {
	_c.Call.Return(run)
	return _c
}

// ExtraChallengeTimeBlocks provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) ExtraChallengeTimeBlocks(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for ExtraChallengeTimeBlocks")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExtraChallengeTimeBlocks'
type ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call struct {
	*mock.Call
}

// ExtraChallengeTimeBlocks is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) ExtraChallengeTimeBlocks(opts interface{}) *ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call {
	return &ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call{Call: _e.mock.On("ExtraChallengeTimeBlocks", opts)}
}

func (_c *ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *ArbRollupCoreInterface_ExtraChallengeTimeBlocks_Call {
	_c.Call.Return(run)
	return _c
}

// FilterNodeConfirmed provides a mock function with given fields: opts, nodeNum
func (_m *ArbRollupCoreInterface) FilterNodeConfirmed(opts *bind.FilterOpts, nodeNum []uint64) (*arbitrum_rollup_core.ArbRollupCoreNodeConfirmedIterator, error) {
	ret := _m.Called(opts, nodeNum)

	if len(ret) == 0 {
		panic("no return value specified for FilterNodeConfirmed")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreNodeConfirmedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*arbitrum_rollup_core.ArbRollupCoreNodeConfirmedIterator, error)); ok {
		return rf(opts, nodeNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *arbitrum_rollup_core.ArbRollupCoreNodeConfirmedIterator); ok {
		r0 = rf(opts, nodeNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreNodeConfirmedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, nodeNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_FilterNodeConfirmed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterNodeConfirmed'
type ArbRollupCoreInterface_FilterNodeConfirmed_Call struct {
	*mock.Call
}

// FilterNodeConfirmed is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - nodeNum []uint64
func (_e *ArbRollupCoreInterface_Expecter) FilterNodeConfirmed(opts interface{}, nodeNum interface{}) *ArbRollupCoreInterface_FilterNodeConfirmed_Call {
	return &ArbRollupCoreInterface_FilterNodeConfirmed_Call{Call: _e.mock.On("FilterNodeConfirmed", opts, nodeNum)}
}

func (_c *ArbRollupCoreInterface_FilterNodeConfirmed_Call) Run(run func(opts *bind.FilterOpts, nodeNum []uint64)) *ArbRollupCoreInterface_FilterNodeConfirmed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_FilterNodeConfirmed_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreNodeConfirmedIterator, _a1 error) *ArbRollupCoreInterface_FilterNodeConfirmed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_FilterNodeConfirmed_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*arbitrum_rollup_core.ArbRollupCoreNodeConfirmedIterator, error)) *ArbRollupCoreInterface_FilterNodeConfirmed_Call {
	_c.Call.Return(run)
	return _c
}

// FilterNodeCreated provides a mock function with given fields: opts, nodeNum, parentNodeHash, nodeHash
func (_m *ArbRollupCoreInterface) FilterNodeCreated(opts *bind.FilterOpts, nodeNum []uint64, parentNodeHash [][32]byte, nodeHash [][32]byte) (*arbitrum_rollup_core.ArbRollupCoreNodeCreatedIterator, error) {
	ret := _m.Called(opts, nodeNum, parentNodeHash, nodeHash)

	if len(ret) == 0 {
		panic("no return value specified for FilterNodeCreated")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreNodeCreatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, [][32]byte, [][32]byte) (*arbitrum_rollup_core.ArbRollupCoreNodeCreatedIterator, error)); ok {
		return rf(opts, nodeNum, parentNodeHash, nodeHash)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64, [][32]byte, [][32]byte) *arbitrum_rollup_core.ArbRollupCoreNodeCreatedIterator); ok {
		r0 = rf(opts, nodeNum, parentNodeHash, nodeHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreNodeCreatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64, [][32]byte, [][32]byte) error); ok {
		r1 = rf(opts, nodeNum, parentNodeHash, nodeHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_FilterNodeCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterNodeCreated'
type ArbRollupCoreInterface_FilterNodeCreated_Call struct {
	*mock.Call
}

// FilterNodeCreated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - nodeNum []uint64
//   - parentNodeHash [][32]byte
//   - nodeHash [][32]byte
func (_e *ArbRollupCoreInterface_Expecter) FilterNodeCreated(opts interface{}, nodeNum interface{}, parentNodeHash interface{}, nodeHash interface{}) *ArbRollupCoreInterface_FilterNodeCreated_Call {
	return &ArbRollupCoreInterface_FilterNodeCreated_Call{Call: _e.mock.On("FilterNodeCreated", opts, nodeNum, parentNodeHash, nodeHash)}
}

func (_c *ArbRollupCoreInterface_FilterNodeCreated_Call) Run(run func(opts *bind.FilterOpts, nodeNum []uint64, parentNodeHash [][32]byte, nodeHash [][32]byte)) *ArbRollupCoreInterface_FilterNodeCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64), args[2].([][32]byte), args[3].([][32]byte))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_FilterNodeCreated_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreNodeCreatedIterator, _a1 error) *ArbRollupCoreInterface_FilterNodeCreated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_FilterNodeCreated_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64, [][32]byte, [][32]byte) (*arbitrum_rollup_core.ArbRollupCoreNodeCreatedIterator, error)) *ArbRollupCoreInterface_FilterNodeCreated_Call {
	_c.Call.Return(run)
	return _c
}

// FilterNodeRejected provides a mock function with given fields: opts, nodeNum
func (_m *ArbRollupCoreInterface) FilterNodeRejected(opts *bind.FilterOpts, nodeNum []uint64) (*arbitrum_rollup_core.ArbRollupCoreNodeRejectedIterator, error) {
	ret := _m.Called(opts, nodeNum)

	if len(ret) == 0 {
		panic("no return value specified for FilterNodeRejected")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreNodeRejectedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*arbitrum_rollup_core.ArbRollupCoreNodeRejectedIterator, error)); ok {
		return rf(opts, nodeNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *arbitrum_rollup_core.ArbRollupCoreNodeRejectedIterator); ok {
		r0 = rf(opts, nodeNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreNodeRejectedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, nodeNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_FilterNodeRejected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterNodeRejected'
type ArbRollupCoreInterface_FilterNodeRejected_Call struct {
	*mock.Call
}

// FilterNodeRejected is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - nodeNum []uint64
func (_e *ArbRollupCoreInterface_Expecter) FilterNodeRejected(opts interface{}, nodeNum interface{}) *ArbRollupCoreInterface_FilterNodeRejected_Call {
	return &ArbRollupCoreInterface_FilterNodeRejected_Call{Call: _e.mock.On("FilterNodeRejected", opts, nodeNum)}
}

func (_c *ArbRollupCoreInterface_FilterNodeRejected_Call) Run(run func(opts *bind.FilterOpts, nodeNum []uint64)) *ArbRollupCoreInterface_FilterNodeRejected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_FilterNodeRejected_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreNodeRejectedIterator, _a1 error) *ArbRollupCoreInterface_FilterNodeRejected_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_FilterNodeRejected_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*arbitrum_rollup_core.ArbRollupCoreNodeRejectedIterator, error)) *ArbRollupCoreInterface_FilterNodeRejected_Call {
	_c.Call.Return(run)
	return _c
}

// FilterRollupChallengeStarted provides a mock function with given fields: opts, challengeIndex
func (_m *ArbRollupCoreInterface) FilterRollupChallengeStarted(opts *bind.FilterOpts, challengeIndex []uint64) (*arbitrum_rollup_core.ArbRollupCoreRollupChallengeStartedIterator, error) {
	ret := _m.Called(opts, challengeIndex)

	if len(ret) == 0 {
		panic("no return value specified for FilterRollupChallengeStarted")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStartedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) (*arbitrum_rollup_core.ArbRollupCoreRollupChallengeStartedIterator, error)); ok {
		return rf(opts, challengeIndex)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []uint64) *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStartedIterator); ok {
		r0 = rf(opts, challengeIndex)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreRollupChallengeStartedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []uint64) error); ok {
		r1 = rf(opts, challengeIndex)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_FilterRollupChallengeStarted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterRollupChallengeStarted'
type ArbRollupCoreInterface_FilterRollupChallengeStarted_Call struct {
	*mock.Call
}

// FilterRollupChallengeStarted is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - challengeIndex []uint64
func (_e *ArbRollupCoreInterface_Expecter) FilterRollupChallengeStarted(opts interface{}, challengeIndex interface{}) *ArbRollupCoreInterface_FilterRollupChallengeStarted_Call {
	return &ArbRollupCoreInterface_FilterRollupChallengeStarted_Call{Call: _e.mock.On("FilterRollupChallengeStarted", opts, challengeIndex)}
}

func (_c *ArbRollupCoreInterface_FilterRollupChallengeStarted_Call) Run(run func(opts *bind.FilterOpts, challengeIndex []uint64)) *ArbRollupCoreInterface_FilterRollupChallengeStarted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]uint64))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_FilterRollupChallengeStarted_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStartedIterator, _a1 error) *ArbRollupCoreInterface_FilterRollupChallengeStarted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_FilterRollupChallengeStarted_Call) RunAndReturn(run func(*bind.FilterOpts, []uint64) (*arbitrum_rollup_core.ArbRollupCoreRollupChallengeStartedIterator, error)) *ArbRollupCoreInterface_FilterRollupChallengeStarted_Call {
	_c.Call.Return(run)
	return _c
}

// FilterRollupInitialized provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) FilterRollupInitialized(opts *bind.FilterOpts) (*arbitrum_rollup_core.ArbRollupCoreRollupInitializedIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterRollupInitialized")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreRollupInitializedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*arbitrum_rollup_core.ArbRollupCoreRollupInitializedIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *arbitrum_rollup_core.ArbRollupCoreRollupInitializedIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreRollupInitializedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_FilterRollupInitialized_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterRollupInitialized'
type ArbRollupCoreInterface_FilterRollupInitialized_Call struct {
	*mock.Call
}

// FilterRollupInitialized is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *ArbRollupCoreInterface_Expecter) FilterRollupInitialized(opts interface{}) *ArbRollupCoreInterface_FilterRollupInitialized_Call {
	return &ArbRollupCoreInterface_FilterRollupInitialized_Call{Call: _e.mock.On("FilterRollupInitialized", opts)}
}

func (_c *ArbRollupCoreInterface_FilterRollupInitialized_Call) Run(run func(opts *bind.FilterOpts)) *ArbRollupCoreInterface_FilterRollupInitialized_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_FilterRollupInitialized_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreRollupInitializedIterator, _a1 error) *ArbRollupCoreInterface_FilterRollupInitialized_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_FilterRollupInitialized_Call) RunAndReturn(run func(*bind.FilterOpts) (*arbitrum_rollup_core.ArbRollupCoreRollupInitializedIterator, error)) *ArbRollupCoreInterface_FilterRollupInitialized_Call {
	_c.Call.Return(run)
	return _c
}

// FilterUserStakeUpdated provides a mock function with given fields: opts, user
func (_m *ArbRollupCoreInterface) FilterUserStakeUpdated(opts *bind.FilterOpts, user []common.Address) (*arbitrum_rollup_core.ArbRollupCoreUserStakeUpdatedIterator, error) {
	ret := _m.Called(opts, user)

	if len(ret) == 0 {
		panic("no return value specified for FilterUserStakeUpdated")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*arbitrum_rollup_core.ArbRollupCoreUserStakeUpdatedIterator, error)); ok {
		return rf(opts, user)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdatedIterator); ok {
		r0 = rf(opts, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreUserStakeUpdatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_FilterUserStakeUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterUserStakeUpdated'
type ArbRollupCoreInterface_FilterUserStakeUpdated_Call struct {
	*mock.Call
}

// FilterUserStakeUpdated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - user []common.Address
func (_e *ArbRollupCoreInterface_Expecter) FilterUserStakeUpdated(opts interface{}, user interface{}) *ArbRollupCoreInterface_FilterUserStakeUpdated_Call {
	return &ArbRollupCoreInterface_FilterUserStakeUpdated_Call{Call: _e.mock.On("FilterUserStakeUpdated", opts, user)}
}

func (_c *ArbRollupCoreInterface_FilterUserStakeUpdated_Call) Run(run func(opts *bind.FilterOpts, user []common.Address)) *ArbRollupCoreInterface_FilterUserStakeUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_FilterUserStakeUpdated_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdatedIterator, _a1 error) *ArbRollupCoreInterface_FilterUserStakeUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_FilterUserStakeUpdated_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*arbitrum_rollup_core.ArbRollupCoreUserStakeUpdatedIterator, error)) *ArbRollupCoreInterface_FilterUserStakeUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// FilterUserWithdrawableFundsUpdated provides a mock function with given fields: opts, user
func (_m *ArbRollupCoreInterface) FilterUserWithdrawableFundsUpdated(opts *bind.FilterOpts, user []common.Address) (*arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdatedIterator, error) {
	ret := _m.Called(opts, user)

	if len(ret) == 0 {
		panic("no return value specified for FilterUserWithdrawableFundsUpdated")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdatedIterator, error)); ok {
		return rf(opts, user)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdatedIterator); ok {
		r0 = rf(opts, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterUserWithdrawableFundsUpdated'
type ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call struct {
	*mock.Call
}

// FilterUserWithdrawableFundsUpdated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - user []common.Address
func (_e *ArbRollupCoreInterface_Expecter) FilterUserWithdrawableFundsUpdated(opts interface{}, user interface{}) *ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call {
	return &ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call{Call: _e.mock.On("FilterUserWithdrawableFundsUpdated", opts, user)}
}

func (_c *ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call) Run(run func(opts *bind.FilterOpts, user []common.Address)) *ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdatedIterator, _a1 error) *ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdatedIterator, error)) *ArbRollupCoreInterface_FilterUserWithdrawableFundsUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// FirstUnresolvedNode provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) FirstUnresolvedNode(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FirstUnresolvedNode")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_FirstUnresolvedNode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FirstUnresolvedNode'
type ArbRollupCoreInterface_FirstUnresolvedNode_Call struct {
	*mock.Call
}

// FirstUnresolvedNode is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) FirstUnresolvedNode(opts interface{}) *ArbRollupCoreInterface_FirstUnresolvedNode_Call {
	return &ArbRollupCoreInterface_FirstUnresolvedNode_Call{Call: _e.mock.On("FirstUnresolvedNode", opts)}
}

func (_c *ArbRollupCoreInterface_FirstUnresolvedNode_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_FirstUnresolvedNode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_FirstUnresolvedNode_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_FirstUnresolvedNode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_FirstUnresolvedNode_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *ArbRollupCoreInterface_FirstUnresolvedNode_Call {
	_c.Call.Return(run)
	return _c
}

// GetNode provides a mock function with given fields: opts, nodeNum
func (_m *ArbRollupCoreInterface) GetNode(opts *bind.CallOpts, nodeNum uint64) (arbitrum_rollup_core.Node, error) {
	ret := _m.Called(opts, nodeNum)

	if len(ret) == 0 {
		panic("no return value specified for GetNode")
	}

	var r0 arbitrum_rollup_core.Node
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) (arbitrum_rollup_core.Node, error)); ok {
		return rf(opts, nodeNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) arbitrum_rollup_core.Node); ok {
		r0 = rf(opts, nodeNum)
	} else {
		r0 = ret.Get(0).(arbitrum_rollup_core.Node)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, nodeNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_GetNode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNode'
type ArbRollupCoreInterface_GetNode_Call struct {
	*mock.Call
}

// GetNode is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - nodeNum uint64
func (_e *ArbRollupCoreInterface_Expecter) GetNode(opts interface{}, nodeNum interface{}) *ArbRollupCoreInterface_GetNode_Call {
	return &ArbRollupCoreInterface_GetNode_Call{Call: _e.mock.On("GetNode", opts, nodeNum)}
}

func (_c *ArbRollupCoreInterface_GetNode_Call) Run(run func(opts *bind.CallOpts, nodeNum uint64)) *ArbRollupCoreInterface_GetNode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_GetNode_Call) Return(_a0 arbitrum_rollup_core.Node, _a1 error) *ArbRollupCoreInterface_GetNode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_GetNode_Call) RunAndReturn(run func(*bind.CallOpts, uint64) (arbitrum_rollup_core.Node, error)) *ArbRollupCoreInterface_GetNode_Call {
	_c.Call.Return(run)
	return _c
}

// GetNodeCreationBlockForLogLookup provides a mock function with given fields: opts, nodeNum
func (_m *ArbRollupCoreInterface) GetNodeCreationBlockForLogLookup(opts *bind.CallOpts, nodeNum uint64) (*big.Int, error) {
	ret := _m.Called(opts, nodeNum)

	if len(ret) == 0 {
		panic("no return value specified for GetNodeCreationBlockForLogLookup")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) (*big.Int, error)); ok {
		return rf(opts, nodeNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) *big.Int); ok {
		r0 = rf(opts, nodeNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, nodeNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNodeCreationBlockForLogLookup'
type ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call struct {
	*mock.Call
}

// GetNodeCreationBlockForLogLookup is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - nodeNum uint64
func (_e *ArbRollupCoreInterface_Expecter) GetNodeCreationBlockForLogLookup(opts interface{}, nodeNum interface{}) *ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call {
	return &ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call{Call: _e.mock.On("GetNodeCreationBlockForLogLookup", opts, nodeNum)}
}

func (_c *ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call) Run(run func(opts *bind.CallOpts, nodeNum uint64)) *ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call) Return(_a0 *big.Int, _a1 error) *ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call) RunAndReturn(run func(*bind.CallOpts, uint64) (*big.Int, error)) *ArbRollupCoreInterface_GetNodeCreationBlockForLogLookup_Call {
	_c.Call.Return(run)
	return _c
}

// GetStaker provides a mock function with given fields: opts, staker
func (_m *ArbRollupCoreInterface) GetStaker(opts *bind.CallOpts, staker common.Address) (arbitrum_rollup_core.IRollupCoreStaker, error) {
	ret := _m.Called(opts, staker)

	if len(ret) == 0 {
		panic("no return value specified for GetStaker")
	}

	var r0 arbitrum_rollup_core.IRollupCoreStaker
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (arbitrum_rollup_core.IRollupCoreStaker, error)); ok {
		return rf(opts, staker)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) arbitrum_rollup_core.IRollupCoreStaker); ok {
		r0 = rf(opts, staker)
	} else {
		r0 = ret.Get(0).(arbitrum_rollup_core.IRollupCoreStaker)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, staker)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_GetStaker_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStaker'
type ArbRollupCoreInterface_GetStaker_Call struct {
	*mock.Call
}

// GetStaker is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - staker common.Address
func (_e *ArbRollupCoreInterface_Expecter) GetStaker(opts interface{}, staker interface{}) *ArbRollupCoreInterface_GetStaker_Call {
	return &ArbRollupCoreInterface_GetStaker_Call{Call: _e.mock.On("GetStaker", opts, staker)}
}

func (_c *ArbRollupCoreInterface_GetStaker_Call) Run(run func(opts *bind.CallOpts, staker common.Address)) *ArbRollupCoreInterface_GetStaker_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_GetStaker_Call) Return(_a0 arbitrum_rollup_core.IRollupCoreStaker, _a1 error) *ArbRollupCoreInterface_GetStaker_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_GetStaker_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (arbitrum_rollup_core.IRollupCoreStaker, error)) *ArbRollupCoreInterface_GetStaker_Call {
	_c.Call.Return(run)
	return _c
}

// GetStakerAddress provides a mock function with given fields: opts, stakerNum
func (_m *ArbRollupCoreInterface) GetStakerAddress(opts *bind.CallOpts, stakerNum uint64) (common.Address, error) {
	ret := _m.Called(opts, stakerNum)

	if len(ret) == 0 {
		panic("no return value specified for GetStakerAddress")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) (common.Address, error)); ok {
		return rf(opts, stakerNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64) common.Address); ok {
		r0 = rf(opts, stakerNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64) error); ok {
		r1 = rf(opts, stakerNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_GetStakerAddress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetStakerAddress'
type ArbRollupCoreInterface_GetStakerAddress_Call struct {
	*mock.Call
}

// GetStakerAddress is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - stakerNum uint64
func (_e *ArbRollupCoreInterface_Expecter) GetStakerAddress(opts interface{}, stakerNum interface{}) *ArbRollupCoreInterface_GetStakerAddress_Call {
	return &ArbRollupCoreInterface_GetStakerAddress_Call{Call: _e.mock.On("GetStakerAddress", opts, stakerNum)}
}

func (_c *ArbRollupCoreInterface_GetStakerAddress_Call) Run(run func(opts *bind.CallOpts, stakerNum uint64)) *ArbRollupCoreInterface_GetStakerAddress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_GetStakerAddress_Call) Return(_a0 common.Address, _a1 error) *ArbRollupCoreInterface_GetStakerAddress_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_GetStakerAddress_Call) RunAndReturn(run func(*bind.CallOpts, uint64) (common.Address, error)) *ArbRollupCoreInterface_GetStakerAddress_Call {
	_c.Call.Return(run)
	return _c
}

// IsStaked provides a mock function with given fields: opts, staker
func (_m *ArbRollupCoreInterface) IsStaked(opts *bind.CallOpts, staker common.Address) (bool, error) {
	ret := _m.Called(opts, staker)

	if len(ret) == 0 {
		panic("no return value specified for IsStaked")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (bool, error)); ok {
		return rf(opts, staker)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) bool); ok {
		r0 = rf(opts, staker)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, staker)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_IsStaked_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsStaked'
type ArbRollupCoreInterface_IsStaked_Call struct {
	*mock.Call
}

// IsStaked is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - staker common.Address
func (_e *ArbRollupCoreInterface_Expecter) IsStaked(opts interface{}, staker interface{}) *ArbRollupCoreInterface_IsStaked_Call {
	return &ArbRollupCoreInterface_IsStaked_Call{Call: _e.mock.On("IsStaked", opts, staker)}
}

func (_c *ArbRollupCoreInterface_IsStaked_Call) Run(run func(opts *bind.CallOpts, staker common.Address)) *ArbRollupCoreInterface_IsStaked_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_IsStaked_Call) Return(_a0 bool, _a1 error) *ArbRollupCoreInterface_IsStaked_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_IsStaked_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (bool, error)) *ArbRollupCoreInterface_IsStaked_Call {
	_c.Call.Return(run)
	return _c
}

// IsValidator provides a mock function with given fields: opts, arg0
func (_m *ArbRollupCoreInterface) IsValidator(opts *bind.CallOpts, arg0 common.Address) (bool, error) {
	ret := _m.Called(opts, arg0)

	if len(ret) == 0 {
		panic("no return value specified for IsValidator")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (bool, error)); ok {
		return rf(opts, arg0)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) bool); ok {
		r0 = rf(opts, arg0)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, arg0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_IsValidator_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsValidator'
type ArbRollupCoreInterface_IsValidator_Call struct {
	*mock.Call
}

// IsValidator is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - arg0 common.Address
func (_e *ArbRollupCoreInterface_Expecter) IsValidator(opts interface{}, arg0 interface{}) *ArbRollupCoreInterface_IsValidator_Call {
	return &ArbRollupCoreInterface_IsValidator_Call{Call: _e.mock.On("IsValidator", opts, arg0)}
}

func (_c *ArbRollupCoreInterface_IsValidator_Call) Run(run func(opts *bind.CallOpts, arg0 common.Address)) *ArbRollupCoreInterface_IsValidator_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_IsValidator_Call) Return(_a0 bool, _a1 error) *ArbRollupCoreInterface_IsValidator_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_IsValidator_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (bool, error)) *ArbRollupCoreInterface_IsValidator_Call {
	_c.Call.Return(run)
	return _c
}

// IsZombie provides a mock function with given fields: opts, staker
func (_m *ArbRollupCoreInterface) IsZombie(opts *bind.CallOpts, staker common.Address) (bool, error) {
	ret := _m.Called(opts, staker)

	if len(ret) == 0 {
		panic("no return value specified for IsZombie")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (bool, error)); ok {
		return rf(opts, staker)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) bool); ok {
		r0 = rf(opts, staker)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, staker)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_IsZombie_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsZombie'
type ArbRollupCoreInterface_IsZombie_Call struct {
	*mock.Call
}

// IsZombie is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - staker common.Address
func (_e *ArbRollupCoreInterface_Expecter) IsZombie(opts interface{}, staker interface{}) *ArbRollupCoreInterface_IsZombie_Call {
	return &ArbRollupCoreInterface_IsZombie_Call{Call: _e.mock.On("IsZombie", opts, staker)}
}

func (_c *ArbRollupCoreInterface_IsZombie_Call) Run(run func(opts *bind.CallOpts, staker common.Address)) *ArbRollupCoreInterface_IsZombie_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_IsZombie_Call) Return(_a0 bool, _a1 error) *ArbRollupCoreInterface_IsZombie_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_IsZombie_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (bool, error)) *ArbRollupCoreInterface_IsZombie_Call {
	_c.Call.Return(run)
	return _c
}

// LastStakeBlock provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) LastStakeBlock(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LastStakeBlock")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_LastStakeBlock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LastStakeBlock'
type ArbRollupCoreInterface_LastStakeBlock_Call struct {
	*mock.Call
}

// LastStakeBlock is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) LastStakeBlock(opts interface{}) *ArbRollupCoreInterface_LastStakeBlock_Call {
	return &ArbRollupCoreInterface_LastStakeBlock_Call{Call: _e.mock.On("LastStakeBlock", opts)}
}

func (_c *ArbRollupCoreInterface_LastStakeBlock_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_LastStakeBlock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_LastStakeBlock_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_LastStakeBlock_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_LastStakeBlock_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *ArbRollupCoreInterface_LastStakeBlock_Call {
	_c.Call.Return(run)
	return _c
}

// LatestConfirmed provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) LatestConfirmed(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LatestConfirmed")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_LatestConfirmed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestConfirmed'
type ArbRollupCoreInterface_LatestConfirmed_Call struct {
	*mock.Call
}

// LatestConfirmed is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) LatestConfirmed(opts interface{}) *ArbRollupCoreInterface_LatestConfirmed_Call {
	return &ArbRollupCoreInterface_LatestConfirmed_Call{Call: _e.mock.On("LatestConfirmed", opts)}
}

func (_c *ArbRollupCoreInterface_LatestConfirmed_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_LatestConfirmed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_LatestConfirmed_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_LatestConfirmed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_LatestConfirmed_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *ArbRollupCoreInterface_LatestConfirmed_Call {
	_c.Call.Return(run)
	return _c
}

// LatestNodeCreated provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) LatestNodeCreated(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LatestNodeCreated")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_LatestNodeCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestNodeCreated'
type ArbRollupCoreInterface_LatestNodeCreated_Call struct {
	*mock.Call
}

// LatestNodeCreated is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) LatestNodeCreated(opts interface{}) *ArbRollupCoreInterface_LatestNodeCreated_Call {
	return &ArbRollupCoreInterface_LatestNodeCreated_Call{Call: _e.mock.On("LatestNodeCreated", opts)}
}

func (_c *ArbRollupCoreInterface_LatestNodeCreated_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_LatestNodeCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_LatestNodeCreated_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_LatestNodeCreated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_LatestNodeCreated_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *ArbRollupCoreInterface_LatestNodeCreated_Call {
	_c.Call.Return(run)
	return _c
}

// LatestStakedNode provides a mock function with given fields: opts, staker
func (_m *ArbRollupCoreInterface) LatestStakedNode(opts *bind.CallOpts, staker common.Address) (uint64, error) {
	ret := _m.Called(opts, staker)

	if len(ret) == 0 {
		panic("no return value specified for LatestStakedNode")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (uint64, error)); ok {
		return rf(opts, staker)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) uint64); ok {
		r0 = rf(opts, staker)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, staker)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_LatestStakedNode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestStakedNode'
type ArbRollupCoreInterface_LatestStakedNode_Call struct {
	*mock.Call
}

// LatestStakedNode is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - staker common.Address
func (_e *ArbRollupCoreInterface_Expecter) LatestStakedNode(opts interface{}, staker interface{}) *ArbRollupCoreInterface_LatestStakedNode_Call {
	return &ArbRollupCoreInterface_LatestStakedNode_Call{Call: _e.mock.On("LatestStakedNode", opts, staker)}
}

func (_c *ArbRollupCoreInterface_LatestStakedNode_Call) Run(run func(opts *bind.CallOpts, staker common.Address)) *ArbRollupCoreInterface_LatestStakedNode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_LatestStakedNode_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_LatestStakedNode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_LatestStakedNode_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (uint64, error)) *ArbRollupCoreInterface_LatestStakedNode_Call {
	_c.Call.Return(run)
	return _c
}

// LoserStakeEscrow provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) LoserStakeEscrow(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for LoserStakeEscrow")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_LoserStakeEscrow_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LoserStakeEscrow'
type ArbRollupCoreInterface_LoserStakeEscrow_Call struct {
	*mock.Call
}

// LoserStakeEscrow is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) LoserStakeEscrow(opts interface{}) *ArbRollupCoreInterface_LoserStakeEscrow_Call {
	return &ArbRollupCoreInterface_LoserStakeEscrow_Call{Call: _e.mock.On("LoserStakeEscrow", opts)}
}

func (_c *ArbRollupCoreInterface_LoserStakeEscrow_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_LoserStakeEscrow_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_LoserStakeEscrow_Call) Return(_a0 common.Address, _a1 error) *ArbRollupCoreInterface_LoserStakeEscrow_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_LoserStakeEscrow_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbRollupCoreInterface_LoserStakeEscrow_Call {
	_c.Call.Return(run)
	return _c
}

// MinimumAssertionPeriod provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) MinimumAssertionPeriod(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for MinimumAssertionPeriod")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_MinimumAssertionPeriod_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MinimumAssertionPeriod'
type ArbRollupCoreInterface_MinimumAssertionPeriod_Call struct {
	*mock.Call
}

// MinimumAssertionPeriod is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) MinimumAssertionPeriod(opts interface{}) *ArbRollupCoreInterface_MinimumAssertionPeriod_Call {
	return &ArbRollupCoreInterface_MinimumAssertionPeriod_Call{Call: _e.mock.On("MinimumAssertionPeriod", opts)}
}

func (_c *ArbRollupCoreInterface_MinimumAssertionPeriod_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_MinimumAssertionPeriod_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_MinimumAssertionPeriod_Call) Return(_a0 *big.Int, _a1 error) *ArbRollupCoreInterface_MinimumAssertionPeriod_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_MinimumAssertionPeriod_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *ArbRollupCoreInterface_MinimumAssertionPeriod_Call {
	_c.Call.Return(run)
	return _c
}

// NodeHasStaker provides a mock function with given fields: opts, nodeNum, staker
func (_m *ArbRollupCoreInterface) NodeHasStaker(opts *bind.CallOpts, nodeNum uint64, staker common.Address) (bool, error) {
	ret := _m.Called(opts, nodeNum, staker)

	if len(ret) == 0 {
		panic("no return value specified for NodeHasStaker")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, common.Address) (bool, error)); ok {
		return rf(opts, nodeNum, staker)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, uint64, common.Address) bool); ok {
		r0 = rf(opts, nodeNum, staker)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, uint64, common.Address) error); ok {
		r1 = rf(opts, nodeNum, staker)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_NodeHasStaker_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NodeHasStaker'
type ArbRollupCoreInterface_NodeHasStaker_Call struct {
	*mock.Call
}

// NodeHasStaker is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - nodeNum uint64
//   - staker common.Address
func (_e *ArbRollupCoreInterface_Expecter) NodeHasStaker(opts interface{}, nodeNum interface{}, staker interface{}) *ArbRollupCoreInterface_NodeHasStaker_Call {
	return &ArbRollupCoreInterface_NodeHasStaker_Call{Call: _e.mock.On("NodeHasStaker", opts, nodeNum, staker)}
}

func (_c *ArbRollupCoreInterface_NodeHasStaker_Call) Run(run func(opts *bind.CallOpts, nodeNum uint64, staker common.Address)) *ArbRollupCoreInterface_NodeHasStaker_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(uint64), args[2].(common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_NodeHasStaker_Call) Return(_a0 bool, _a1 error) *ArbRollupCoreInterface_NodeHasStaker_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_NodeHasStaker_Call) RunAndReturn(run func(*bind.CallOpts, uint64, common.Address) (bool, error)) *ArbRollupCoreInterface_NodeHasStaker_Call {
	_c.Call.Return(run)
	return _c
}

// Outbox provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) Outbox(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Outbox")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_Outbox_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Outbox'
type ArbRollupCoreInterface_Outbox_Call struct {
	*mock.Call
}

// Outbox is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) Outbox(opts interface{}) *ArbRollupCoreInterface_Outbox_Call {
	return &ArbRollupCoreInterface_Outbox_Call{Call: _e.mock.On("Outbox", opts)}
}

func (_c *ArbRollupCoreInterface_Outbox_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_Outbox_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_Outbox_Call) Return(_a0 common.Address, _a1 error) *ArbRollupCoreInterface_Outbox_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_Outbox_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbRollupCoreInterface_Outbox_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *ArbRollupCoreInterface) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type ArbRollupCoreInterface_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbRollupCoreInterface_Expecter) ParseLog(log interface{}) *ArbRollupCoreInterface_ParseLog_Call {
	return &ArbRollupCoreInterface_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *ArbRollupCoreInterface_ParseLog_Call) Run(run func(log types.Log)) *ArbRollupCoreInterface_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *ArbRollupCoreInterface_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *ArbRollupCoreInterface_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// ParseNodeConfirmed provides a mock function with given fields: log
func (_m *ArbRollupCoreInterface) ParseNodeConfirmed(log types.Log) (*arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseNodeConfirmed")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreNodeConfirmed)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ParseNodeConfirmed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseNodeConfirmed'
type ArbRollupCoreInterface_ParseNodeConfirmed_Call struct {
	*mock.Call
}

// ParseNodeConfirmed is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbRollupCoreInterface_Expecter) ParseNodeConfirmed(log interface{}) *ArbRollupCoreInterface_ParseNodeConfirmed_Call {
	return &ArbRollupCoreInterface_ParseNodeConfirmed_Call{Call: _e.mock.On("ParseNodeConfirmed", log)}
}

func (_c *ArbRollupCoreInterface_ParseNodeConfirmed_Call) Run(run func(log types.Log)) *ArbRollupCoreInterface_ParseNodeConfirmed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ParseNodeConfirmed_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, _a1 error) *ArbRollupCoreInterface_ParseNodeConfirmed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ParseNodeConfirmed_Call) RunAndReturn(run func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, error)) *ArbRollupCoreInterface_ParseNodeConfirmed_Call {
	_c.Call.Return(run)
	return _c
}

// ParseNodeCreated provides a mock function with given fields: log
func (_m *ArbRollupCoreInterface) ParseNodeCreated(log types.Log) (*arbitrum_rollup_core.ArbRollupCoreNodeCreated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseNodeCreated")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreNodeCreated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreNodeCreated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *arbitrum_rollup_core.ArbRollupCoreNodeCreated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreNodeCreated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ParseNodeCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseNodeCreated'
type ArbRollupCoreInterface_ParseNodeCreated_Call struct {
	*mock.Call
}

// ParseNodeCreated is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbRollupCoreInterface_Expecter) ParseNodeCreated(log interface{}) *ArbRollupCoreInterface_ParseNodeCreated_Call {
	return &ArbRollupCoreInterface_ParseNodeCreated_Call{Call: _e.mock.On("ParseNodeCreated", log)}
}

func (_c *ArbRollupCoreInterface_ParseNodeCreated_Call) Run(run func(log types.Log)) *ArbRollupCoreInterface_ParseNodeCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ParseNodeCreated_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreNodeCreated, _a1 error) *ArbRollupCoreInterface_ParseNodeCreated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ParseNodeCreated_Call) RunAndReturn(run func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreNodeCreated, error)) *ArbRollupCoreInterface_ParseNodeCreated_Call {
	_c.Call.Return(run)
	return _c
}

// ParseNodeRejected provides a mock function with given fields: log
func (_m *ArbRollupCoreInterface) ParseNodeRejected(log types.Log) (*arbitrum_rollup_core.ArbRollupCoreNodeRejected, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseNodeRejected")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreNodeRejected
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreNodeRejected, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *arbitrum_rollup_core.ArbRollupCoreNodeRejected); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreNodeRejected)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ParseNodeRejected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseNodeRejected'
type ArbRollupCoreInterface_ParseNodeRejected_Call struct {
	*mock.Call
}

// ParseNodeRejected is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbRollupCoreInterface_Expecter) ParseNodeRejected(log interface{}) *ArbRollupCoreInterface_ParseNodeRejected_Call {
	return &ArbRollupCoreInterface_ParseNodeRejected_Call{Call: _e.mock.On("ParseNodeRejected", log)}
}

func (_c *ArbRollupCoreInterface_ParseNodeRejected_Call) Run(run func(log types.Log)) *ArbRollupCoreInterface_ParseNodeRejected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ParseNodeRejected_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreNodeRejected, _a1 error) *ArbRollupCoreInterface_ParseNodeRejected_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ParseNodeRejected_Call) RunAndReturn(run func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreNodeRejected, error)) *ArbRollupCoreInterface_ParseNodeRejected_Call {
	_c.Call.Return(run)
	return _c
}

// ParseRollupChallengeStarted provides a mock function with given fields: log
func (_m *ArbRollupCoreInterface) ParseRollupChallengeStarted(log types.Log) (*arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseRollupChallengeStarted")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ParseRollupChallengeStarted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseRollupChallengeStarted'
type ArbRollupCoreInterface_ParseRollupChallengeStarted_Call struct {
	*mock.Call
}

// ParseRollupChallengeStarted is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbRollupCoreInterface_Expecter) ParseRollupChallengeStarted(log interface{}) *ArbRollupCoreInterface_ParseRollupChallengeStarted_Call {
	return &ArbRollupCoreInterface_ParseRollupChallengeStarted_Call{Call: _e.mock.On("ParseRollupChallengeStarted", log)}
}

func (_c *ArbRollupCoreInterface_ParseRollupChallengeStarted_Call) Run(run func(log types.Log)) *ArbRollupCoreInterface_ParseRollupChallengeStarted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ParseRollupChallengeStarted_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, _a1 error) *ArbRollupCoreInterface_ParseRollupChallengeStarted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ParseRollupChallengeStarted_Call) RunAndReturn(run func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, error)) *ArbRollupCoreInterface_ParseRollupChallengeStarted_Call {
	_c.Call.Return(run)
	return _c
}

// ParseRollupInitialized provides a mock function with given fields: log
func (_m *ArbRollupCoreInterface) ParseRollupInitialized(log types.Log) (*arbitrum_rollup_core.ArbRollupCoreRollupInitialized, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseRollupInitialized")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreRollupInitialized
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreRollupInitialized, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *arbitrum_rollup_core.ArbRollupCoreRollupInitialized); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreRollupInitialized)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ParseRollupInitialized_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseRollupInitialized'
type ArbRollupCoreInterface_ParseRollupInitialized_Call struct {
	*mock.Call
}

// ParseRollupInitialized is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbRollupCoreInterface_Expecter) ParseRollupInitialized(log interface{}) *ArbRollupCoreInterface_ParseRollupInitialized_Call {
	return &ArbRollupCoreInterface_ParseRollupInitialized_Call{Call: _e.mock.On("ParseRollupInitialized", log)}
}

func (_c *ArbRollupCoreInterface_ParseRollupInitialized_Call) Run(run func(log types.Log)) *ArbRollupCoreInterface_ParseRollupInitialized_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ParseRollupInitialized_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreRollupInitialized, _a1 error) *ArbRollupCoreInterface_ParseRollupInitialized_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ParseRollupInitialized_Call) RunAndReturn(run func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreRollupInitialized, error)) *ArbRollupCoreInterface_ParseRollupInitialized_Call {
	_c.Call.Return(run)
	return _c
}

// ParseUserStakeUpdated provides a mock function with given fields: log
func (_m *ArbRollupCoreInterface) ParseUserStakeUpdated(log types.Log) (*arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseUserStakeUpdated")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ParseUserStakeUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseUserStakeUpdated'
type ArbRollupCoreInterface_ParseUserStakeUpdated_Call struct {
	*mock.Call
}

// ParseUserStakeUpdated is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbRollupCoreInterface_Expecter) ParseUserStakeUpdated(log interface{}) *ArbRollupCoreInterface_ParseUserStakeUpdated_Call {
	return &ArbRollupCoreInterface_ParseUserStakeUpdated_Call{Call: _e.mock.On("ParseUserStakeUpdated", log)}
}

func (_c *ArbRollupCoreInterface_ParseUserStakeUpdated_Call) Run(run func(log types.Log)) *ArbRollupCoreInterface_ParseUserStakeUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ParseUserStakeUpdated_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, _a1 error) *ArbRollupCoreInterface_ParseUserStakeUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ParseUserStakeUpdated_Call) RunAndReturn(run func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, error)) *ArbRollupCoreInterface_ParseUserStakeUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// ParseUserWithdrawableFundsUpdated provides a mock function with given fields: log
func (_m *ArbRollupCoreInterface) ParseUserWithdrawableFundsUpdated(log types.Log) (*arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseUserWithdrawableFundsUpdated")
	}

	var r0 *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseUserWithdrawableFundsUpdated'
type ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call struct {
	*mock.Call
}

// ParseUserWithdrawableFundsUpdated is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbRollupCoreInterface_Expecter) ParseUserWithdrawableFundsUpdated(log interface{}) *ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call {
	return &ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call{Call: _e.mock.On("ParseUserWithdrawableFundsUpdated", log)}
}

func (_c *ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call) Run(run func(log types.Log)) *ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call) Return(_a0 *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, _a1 error) *ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call) RunAndReturn(run func(types.Log) (*arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, error)) *ArbRollupCoreInterface_ParseUserWithdrawableFundsUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// RollupEventInbox provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) RollupEventInbox(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for RollupEventInbox")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_RollupEventInbox_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RollupEventInbox'
type ArbRollupCoreInterface_RollupEventInbox_Call struct {
	*mock.Call
}

// RollupEventInbox is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) RollupEventInbox(opts interface{}) *ArbRollupCoreInterface_RollupEventInbox_Call {
	return &ArbRollupCoreInterface_RollupEventInbox_Call{Call: _e.mock.On("RollupEventInbox", opts)}
}

func (_c *ArbRollupCoreInterface_RollupEventInbox_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_RollupEventInbox_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_RollupEventInbox_Call) Return(_a0 common.Address, _a1 error) *ArbRollupCoreInterface_RollupEventInbox_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_RollupEventInbox_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbRollupCoreInterface_RollupEventInbox_Call {
	_c.Call.Return(run)
	return _c
}

// SequencerInbox provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) SequencerInbox(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for SequencerInbox")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_SequencerInbox_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SequencerInbox'
type ArbRollupCoreInterface_SequencerInbox_Call struct {
	*mock.Call
}

// SequencerInbox is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) SequencerInbox(opts interface{}) *ArbRollupCoreInterface_SequencerInbox_Call {
	return &ArbRollupCoreInterface_SequencerInbox_Call{Call: _e.mock.On("SequencerInbox", opts)}
}

func (_c *ArbRollupCoreInterface_SequencerInbox_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_SequencerInbox_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_SequencerInbox_Call) Return(_a0 common.Address, _a1 error) *ArbRollupCoreInterface_SequencerInbox_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_SequencerInbox_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbRollupCoreInterface_SequencerInbox_Call {
	_c.Call.Return(run)
	return _c
}

// StakeToken provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) StakeToken(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for StakeToken")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_StakeToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StakeToken'
type ArbRollupCoreInterface_StakeToken_Call struct {
	*mock.Call
}

// StakeToken is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) StakeToken(opts interface{}) *ArbRollupCoreInterface_StakeToken_Call {
	return &ArbRollupCoreInterface_StakeToken_Call{Call: _e.mock.On("StakeToken", opts)}
}

func (_c *ArbRollupCoreInterface_StakeToken_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_StakeToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_StakeToken_Call) Return(_a0 common.Address, _a1 error) *ArbRollupCoreInterface_StakeToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_StakeToken_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbRollupCoreInterface_StakeToken_Call {
	_c.Call.Return(run)
	return _c
}

// StakerCount provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) StakerCount(opts *bind.CallOpts) (uint64, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for StakerCount")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint64, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint64); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_StakerCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StakerCount'
type ArbRollupCoreInterface_StakerCount_Call struct {
	*mock.Call
}

// StakerCount is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) StakerCount(opts interface{}) *ArbRollupCoreInterface_StakerCount_Call {
	return &ArbRollupCoreInterface_StakerCount_Call{Call: _e.mock.On("StakerCount", opts)}
}

func (_c *ArbRollupCoreInterface_StakerCount_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_StakerCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_StakerCount_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_StakerCount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_StakerCount_Call) RunAndReturn(run func(*bind.CallOpts) (uint64, error)) *ArbRollupCoreInterface_StakerCount_Call {
	_c.Call.Return(run)
	return _c
}

// ValidatorWhitelistDisabled provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) ValidatorWhitelistDisabled(opts *bind.CallOpts) (bool, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for ValidatorWhitelistDisabled")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (bool, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) bool); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ValidatorWhitelistDisabled'
type ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call struct {
	*mock.Call
}

// ValidatorWhitelistDisabled is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) ValidatorWhitelistDisabled(opts interface{}) *ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call {
	return &ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call{Call: _e.mock.On("ValidatorWhitelistDisabled", opts)}
}

func (_c *ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call) Return(_a0 bool, _a1 error) *ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call) RunAndReturn(run func(*bind.CallOpts) (bool, error)) *ArbRollupCoreInterface_ValidatorWhitelistDisabled_Call {
	_c.Call.Return(run)
	return _c
}

// WasmModuleRoot provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) WasmModuleRoot(opts *bind.CallOpts) ([32]byte, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for WasmModuleRoot")
	}

	var r0 [32]byte
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) ([32]byte, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) [32]byte); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([32]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_WasmModuleRoot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WasmModuleRoot'
type ArbRollupCoreInterface_WasmModuleRoot_Call struct {
	*mock.Call
}

// WasmModuleRoot is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) WasmModuleRoot(opts interface{}) *ArbRollupCoreInterface_WasmModuleRoot_Call {
	return &ArbRollupCoreInterface_WasmModuleRoot_Call{Call: _e.mock.On("WasmModuleRoot", opts)}
}

func (_c *ArbRollupCoreInterface_WasmModuleRoot_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_WasmModuleRoot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_WasmModuleRoot_Call) Return(_a0 [32]byte, _a1 error) *ArbRollupCoreInterface_WasmModuleRoot_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_WasmModuleRoot_Call) RunAndReturn(run func(*bind.CallOpts) ([32]byte, error)) *ArbRollupCoreInterface_WasmModuleRoot_Call {
	_c.Call.Return(run)
	return _c
}

// WatchNodeConfirmed provides a mock function with given fields: opts, sink, nodeNum
func (_m *ArbRollupCoreInterface) WatchNodeConfirmed(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, nodeNum []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, nodeNum)

	if len(ret) == 0 {
		panic("no return value specified for WatchNodeConfirmed")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, nodeNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, nodeNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, []uint64) error); ok {
		r1 = rf(opts, sink, nodeNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_WatchNodeConfirmed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchNodeConfirmed'
type ArbRollupCoreInterface_WatchNodeConfirmed_Call struct {
	*mock.Call
}

// WatchNodeConfirmed is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed
//   - nodeNum []uint64
func (_e *ArbRollupCoreInterface_Expecter) WatchNodeConfirmed(opts interface{}, sink interface{}, nodeNum interface{}) *ArbRollupCoreInterface_WatchNodeConfirmed_Call {
	return &ArbRollupCoreInterface_WatchNodeConfirmed_Call{Call: _e.mock.On("WatchNodeConfirmed", opts, sink, nodeNum)}
}

func (_c *ArbRollupCoreInterface_WatchNodeConfirmed_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, nodeNum []uint64)) *ArbRollupCoreInterface_WatchNodeConfirmed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed), args[2].([]uint64))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_WatchNodeConfirmed_Call) Return(_a0 event.Subscription, _a1 error) *ArbRollupCoreInterface_WatchNodeConfirmed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_WatchNodeConfirmed_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeConfirmed, []uint64) (event.Subscription, error)) *ArbRollupCoreInterface_WatchNodeConfirmed_Call {
	_c.Call.Return(run)
	return _c
}

// WatchNodeCreated provides a mock function with given fields: opts, sink, nodeNum, parentNodeHash, nodeHash
func (_m *ArbRollupCoreInterface) WatchNodeCreated(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreNodeCreated, nodeNum []uint64, parentNodeHash [][32]byte, nodeHash [][32]byte) (event.Subscription, error) {
	ret := _m.Called(opts, sink, nodeNum, parentNodeHash, nodeHash)

	if len(ret) == 0 {
		panic("no return value specified for WatchNodeCreated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeCreated, []uint64, [][32]byte, [][32]byte) (event.Subscription, error)); ok {
		return rf(opts, sink, nodeNum, parentNodeHash, nodeHash)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeCreated, []uint64, [][32]byte, [][32]byte) event.Subscription); ok {
		r0 = rf(opts, sink, nodeNum, parentNodeHash, nodeHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeCreated, []uint64, [][32]byte, [][32]byte) error); ok {
		r1 = rf(opts, sink, nodeNum, parentNodeHash, nodeHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_WatchNodeCreated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchNodeCreated'
type ArbRollupCoreInterface_WatchNodeCreated_Call struct {
	*mock.Call
}

// WatchNodeCreated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *arbitrum_rollup_core.ArbRollupCoreNodeCreated
//   - nodeNum []uint64
//   - parentNodeHash [][32]byte
//   - nodeHash [][32]byte
func (_e *ArbRollupCoreInterface_Expecter) WatchNodeCreated(opts interface{}, sink interface{}, nodeNum interface{}, parentNodeHash interface{}, nodeHash interface{}) *ArbRollupCoreInterface_WatchNodeCreated_Call {
	return &ArbRollupCoreInterface_WatchNodeCreated_Call{Call: _e.mock.On("WatchNodeCreated", opts, sink, nodeNum, parentNodeHash, nodeHash)}
}

func (_c *ArbRollupCoreInterface_WatchNodeCreated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreNodeCreated, nodeNum []uint64, parentNodeHash [][32]byte, nodeHash [][32]byte)) *ArbRollupCoreInterface_WatchNodeCreated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *arbitrum_rollup_core.ArbRollupCoreNodeCreated), args[2].([]uint64), args[3].([][32]byte), args[4].([][32]byte))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_WatchNodeCreated_Call) Return(_a0 event.Subscription, _a1 error) *ArbRollupCoreInterface_WatchNodeCreated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_WatchNodeCreated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeCreated, []uint64, [][32]byte, [][32]byte) (event.Subscription, error)) *ArbRollupCoreInterface_WatchNodeCreated_Call {
	_c.Call.Return(run)
	return _c
}

// WatchNodeRejected provides a mock function with given fields: opts, sink, nodeNum
func (_m *ArbRollupCoreInterface) WatchNodeRejected(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreNodeRejected, nodeNum []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, nodeNum)

	if len(ret) == 0 {
		panic("no return value specified for WatchNodeRejected")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeRejected, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, nodeNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeRejected, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, nodeNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeRejected, []uint64) error); ok {
		r1 = rf(opts, sink, nodeNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_WatchNodeRejected_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchNodeRejected'
type ArbRollupCoreInterface_WatchNodeRejected_Call struct {
	*mock.Call
}

// WatchNodeRejected is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *arbitrum_rollup_core.ArbRollupCoreNodeRejected
//   - nodeNum []uint64
func (_e *ArbRollupCoreInterface_Expecter) WatchNodeRejected(opts interface{}, sink interface{}, nodeNum interface{}) *ArbRollupCoreInterface_WatchNodeRejected_Call {
	return &ArbRollupCoreInterface_WatchNodeRejected_Call{Call: _e.mock.On("WatchNodeRejected", opts, sink, nodeNum)}
}

func (_c *ArbRollupCoreInterface_WatchNodeRejected_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreNodeRejected, nodeNum []uint64)) *ArbRollupCoreInterface_WatchNodeRejected_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *arbitrum_rollup_core.ArbRollupCoreNodeRejected), args[2].([]uint64))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_WatchNodeRejected_Call) Return(_a0 event.Subscription, _a1 error) *ArbRollupCoreInterface_WatchNodeRejected_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_WatchNodeRejected_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreNodeRejected, []uint64) (event.Subscription, error)) *ArbRollupCoreInterface_WatchNodeRejected_Call {
	_c.Call.Return(run)
	return _c
}

// WatchRollupChallengeStarted provides a mock function with given fields: opts, sink, challengeIndex
func (_m *ArbRollupCoreInterface) WatchRollupChallengeStarted(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, challengeIndex []uint64) (event.Subscription, error) {
	ret := _m.Called(opts, sink, challengeIndex)

	if len(ret) == 0 {
		panic("no return value specified for WatchRollupChallengeStarted")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, []uint64) (event.Subscription, error)); ok {
		return rf(opts, sink, challengeIndex)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, []uint64) event.Subscription); ok {
		r0 = rf(opts, sink, challengeIndex)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, []uint64) error); ok {
		r1 = rf(opts, sink, challengeIndex)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_WatchRollupChallengeStarted_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchRollupChallengeStarted'
type ArbRollupCoreInterface_WatchRollupChallengeStarted_Call struct {
	*mock.Call
}

// WatchRollupChallengeStarted is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted
//   - challengeIndex []uint64
func (_e *ArbRollupCoreInterface_Expecter) WatchRollupChallengeStarted(opts interface{}, sink interface{}, challengeIndex interface{}) *ArbRollupCoreInterface_WatchRollupChallengeStarted_Call {
	return &ArbRollupCoreInterface_WatchRollupChallengeStarted_Call{Call: _e.mock.On("WatchRollupChallengeStarted", opts, sink, challengeIndex)}
}

func (_c *ArbRollupCoreInterface_WatchRollupChallengeStarted_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, challengeIndex []uint64)) *ArbRollupCoreInterface_WatchRollupChallengeStarted_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted), args[2].([]uint64))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_WatchRollupChallengeStarted_Call) Return(_a0 event.Subscription, _a1 error) *ArbRollupCoreInterface_WatchRollupChallengeStarted_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_WatchRollupChallengeStarted_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreRollupChallengeStarted, []uint64) (event.Subscription, error)) *ArbRollupCoreInterface_WatchRollupChallengeStarted_Call {
	_c.Call.Return(run)
	return _c
}

// WatchRollupInitialized provides a mock function with given fields: opts, sink
func (_m *ArbRollupCoreInterface) WatchRollupInitialized(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreRollupInitialized) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchRollupInitialized")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreRollupInitialized) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreRollupInitialized) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreRollupInitialized) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_WatchRollupInitialized_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchRollupInitialized'
type ArbRollupCoreInterface_WatchRollupInitialized_Call struct {
	*mock.Call
}

// WatchRollupInitialized is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *arbitrum_rollup_core.ArbRollupCoreRollupInitialized
func (_e *ArbRollupCoreInterface_Expecter) WatchRollupInitialized(opts interface{}, sink interface{}) *ArbRollupCoreInterface_WatchRollupInitialized_Call {
	return &ArbRollupCoreInterface_WatchRollupInitialized_Call{Call: _e.mock.On("WatchRollupInitialized", opts, sink)}
}

func (_c *ArbRollupCoreInterface_WatchRollupInitialized_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreRollupInitialized)) *ArbRollupCoreInterface_WatchRollupInitialized_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *arbitrum_rollup_core.ArbRollupCoreRollupInitialized))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_WatchRollupInitialized_Call) Return(_a0 event.Subscription, _a1 error) *ArbRollupCoreInterface_WatchRollupInitialized_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_WatchRollupInitialized_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreRollupInitialized) (event.Subscription, error)) *ArbRollupCoreInterface_WatchRollupInitialized_Call {
	_c.Call.Return(run)
	return _c
}

// WatchUserStakeUpdated provides a mock function with given fields: opts, sink, user
func (_m *ArbRollupCoreInterface) WatchUserStakeUpdated(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, user []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, user)

	if len(ret) == 0 {
		panic("no return value specified for WatchUserStakeUpdated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, user)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, []common.Address) error); ok {
		r1 = rf(opts, sink, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_WatchUserStakeUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchUserStakeUpdated'
type ArbRollupCoreInterface_WatchUserStakeUpdated_Call struct {
	*mock.Call
}

// WatchUserStakeUpdated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated
//   - user []common.Address
func (_e *ArbRollupCoreInterface_Expecter) WatchUserStakeUpdated(opts interface{}, sink interface{}, user interface{}) *ArbRollupCoreInterface_WatchUserStakeUpdated_Call {
	return &ArbRollupCoreInterface_WatchUserStakeUpdated_Call{Call: _e.mock.On("WatchUserStakeUpdated", opts, sink, user)}
}

func (_c *ArbRollupCoreInterface_WatchUserStakeUpdated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, user []common.Address)) *ArbRollupCoreInterface_WatchUserStakeUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated), args[2].([]common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_WatchUserStakeUpdated_Call) Return(_a0 event.Subscription, _a1 error) *ArbRollupCoreInterface_WatchUserStakeUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_WatchUserStakeUpdated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreUserStakeUpdated, []common.Address) (event.Subscription, error)) *ArbRollupCoreInterface_WatchUserStakeUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// WatchUserWithdrawableFundsUpdated provides a mock function with given fields: opts, sink, user
func (_m *ArbRollupCoreInterface) WatchUserWithdrawableFundsUpdated(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, user []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, user)

	if len(ret) == 0 {
		panic("no return value specified for WatchUserWithdrawableFundsUpdated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, user)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, []common.Address) error); ok {
		r1 = rf(opts, sink, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchUserWithdrawableFundsUpdated'
type ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call struct {
	*mock.Call
}

// WatchUserWithdrawableFundsUpdated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated
//   - user []common.Address
func (_e *ArbRollupCoreInterface_Expecter) WatchUserWithdrawableFundsUpdated(opts interface{}, sink interface{}, user interface{}) *ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call {
	return &ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call{Call: _e.mock.On("WatchUserWithdrawableFundsUpdated", opts, sink, user)}
}

func (_c *ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, user []common.Address)) *ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated), args[2].([]common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call) Return(_a0 event.Subscription, _a1 error) *ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *arbitrum_rollup_core.ArbRollupCoreUserWithdrawableFundsUpdated, []common.Address) (event.Subscription, error)) *ArbRollupCoreInterface_WatchUserWithdrawableFundsUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// WithdrawableFunds provides a mock function with given fields: opts, owner
func (_m *ArbRollupCoreInterface) WithdrawableFunds(opts *bind.CallOpts, owner common.Address) (*big.Int, error) {
	ret := _m.Called(opts, owner)

	if len(ret) == 0 {
		panic("no return value specified for WithdrawableFunds")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (*big.Int, error)); ok {
		return rf(opts, owner)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) *big.Int); ok {
		r0 = rf(opts, owner)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, owner)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_WithdrawableFunds_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithdrawableFunds'
type ArbRollupCoreInterface_WithdrawableFunds_Call struct {
	*mock.Call
}

// WithdrawableFunds is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - owner common.Address
func (_e *ArbRollupCoreInterface_Expecter) WithdrawableFunds(opts interface{}, owner interface{}) *ArbRollupCoreInterface_WithdrawableFunds_Call {
	return &ArbRollupCoreInterface_WithdrawableFunds_Call{Call: _e.mock.On("WithdrawableFunds", opts, owner)}
}

func (_c *ArbRollupCoreInterface_WithdrawableFunds_Call) Run(run func(opts *bind.CallOpts, owner common.Address)) *ArbRollupCoreInterface_WithdrawableFunds_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_WithdrawableFunds_Call) Return(_a0 *big.Int, _a1 error) *ArbRollupCoreInterface_WithdrawableFunds_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_WithdrawableFunds_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (*big.Int, error)) *ArbRollupCoreInterface_WithdrawableFunds_Call {
	_c.Call.Return(run)
	return _c
}

// ZombieAddress provides a mock function with given fields: opts, zombieNum
func (_m *ArbRollupCoreInterface) ZombieAddress(opts *bind.CallOpts, zombieNum *big.Int) (common.Address, error) {
	ret := _m.Called(opts, zombieNum)

	if len(ret) == 0 {
		panic("no return value specified for ZombieAddress")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) (common.Address, error)); ok {
		return rf(opts, zombieNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) common.Address); ok {
		r0 = rf(opts, zombieNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, *big.Int) error); ok {
		r1 = rf(opts, zombieNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ZombieAddress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZombieAddress'
type ArbRollupCoreInterface_ZombieAddress_Call struct {
	*mock.Call
}

// ZombieAddress is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - zombieNum *big.Int
func (_e *ArbRollupCoreInterface_Expecter) ZombieAddress(opts interface{}, zombieNum interface{}) *ArbRollupCoreInterface_ZombieAddress_Call {
	return &ArbRollupCoreInterface_ZombieAddress_Call{Call: _e.mock.On("ZombieAddress", opts, zombieNum)}
}

func (_c *ArbRollupCoreInterface_ZombieAddress_Call) Run(run func(opts *bind.CallOpts, zombieNum *big.Int)) *ArbRollupCoreInterface_ZombieAddress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(*big.Int))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ZombieAddress_Call) Return(_a0 common.Address, _a1 error) *ArbRollupCoreInterface_ZombieAddress_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ZombieAddress_Call) RunAndReturn(run func(*bind.CallOpts, *big.Int) (common.Address, error)) *ArbRollupCoreInterface_ZombieAddress_Call {
	_c.Call.Return(run)
	return _c
}

// ZombieCount provides a mock function with given fields: opts
func (_m *ArbRollupCoreInterface) ZombieCount(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for ZombieCount")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ZombieCount_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZombieCount'
type ArbRollupCoreInterface_ZombieCount_Call struct {
	*mock.Call
}

// ZombieCount is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbRollupCoreInterface_Expecter) ZombieCount(opts interface{}) *ArbRollupCoreInterface_ZombieCount_Call {
	return &ArbRollupCoreInterface_ZombieCount_Call{Call: _e.mock.On("ZombieCount", opts)}
}

func (_c *ArbRollupCoreInterface_ZombieCount_Call) Run(run func(opts *bind.CallOpts)) *ArbRollupCoreInterface_ZombieCount_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ZombieCount_Call) Return(_a0 *big.Int, _a1 error) *ArbRollupCoreInterface_ZombieCount_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ZombieCount_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *ArbRollupCoreInterface_ZombieCount_Call {
	_c.Call.Return(run)
	return _c
}

// ZombieLatestStakedNode provides a mock function with given fields: opts, zombieNum
func (_m *ArbRollupCoreInterface) ZombieLatestStakedNode(opts *bind.CallOpts, zombieNum *big.Int) (uint64, error) {
	ret := _m.Called(opts, zombieNum)

	if len(ret) == 0 {
		panic("no return value specified for ZombieLatestStakedNode")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) (uint64, error)); ok {
		return rf(opts, zombieNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int) uint64); ok {
		r0 = rf(opts, zombieNum)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, *big.Int) error); ok {
		r1 = rf(opts, zombieNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbRollupCoreInterface_ZombieLatestStakedNode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ZombieLatestStakedNode'
type ArbRollupCoreInterface_ZombieLatestStakedNode_Call struct {
	*mock.Call
}

// ZombieLatestStakedNode is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - zombieNum *big.Int
func (_e *ArbRollupCoreInterface_Expecter) ZombieLatestStakedNode(opts interface{}, zombieNum interface{}) *ArbRollupCoreInterface_ZombieLatestStakedNode_Call {
	return &ArbRollupCoreInterface_ZombieLatestStakedNode_Call{Call: _e.mock.On("ZombieLatestStakedNode", opts, zombieNum)}
}

func (_c *ArbRollupCoreInterface_ZombieLatestStakedNode_Call) Run(run func(opts *bind.CallOpts, zombieNum *big.Int)) *ArbRollupCoreInterface_ZombieLatestStakedNode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(*big.Int))
	})
	return _c
}

func (_c *ArbRollupCoreInterface_ZombieLatestStakedNode_Call) Return(_a0 uint64, _a1 error) *ArbRollupCoreInterface_ZombieLatestStakedNode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbRollupCoreInterface_ZombieLatestStakedNode_Call) RunAndReturn(run func(*bind.CallOpts, *big.Int) (uint64, error)) *ArbRollupCoreInterface_ZombieLatestStakedNode_Call {
	_c.Call.Return(run)
	return _c
}

// NewArbRollupCoreInterface creates a new instance of ArbRollupCoreInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewArbRollupCoreInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *ArbRollupCoreInterface {
	mock := &ArbRollupCoreInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
