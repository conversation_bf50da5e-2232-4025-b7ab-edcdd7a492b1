// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_l2_arbitrum_messenger

import (
	big "math/big"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	event "github.com/ethereum/go-ethereum/event"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	l2_arbitrum_messenger "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/liquiditymanager/generated/l2_arbitrum_messenger"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// L2ArbitrumMessengerInterface is an autogenerated mock type for the L2ArbitrumMessengerInterface type
type L2ArbitrumMessengerInterface struct {
	mock.Mock
}

type L2ArbitrumMessengerInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *L2ArbitrumMessengerInterface) EXPECT() *L2ArbitrumMessengerInterface_Expecter {
	return &L2ArbitrumMessengerInterface_Expecter{mock: &_m.Mock}
}

// Address provides a mock function with given fields:
func (_m *L2ArbitrumMessengerInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// L2ArbitrumMessengerInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type L2ArbitrumMessengerInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *L2ArbitrumMessengerInterface_Expecter) Address() *L2ArbitrumMessengerInterface_Address_Call {
	return &L2ArbitrumMessengerInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *L2ArbitrumMessengerInterface_Address_Call) Run(run func()) *L2ArbitrumMessengerInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *L2ArbitrumMessengerInterface_Address_Call) Return(_a0 common.Address) *L2ArbitrumMessengerInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *L2ArbitrumMessengerInterface_Address_Call) RunAndReturn(run func() common.Address) *L2ArbitrumMessengerInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// FilterTxToL1 provides a mock function with given fields: opts, _from, _to, _id
func (_m *L2ArbitrumMessengerInterface) FilterTxToL1(opts *bind.FilterOpts, _from []common.Address, _to []common.Address, _id []*big.Int) (*l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1Iterator, error) {
	ret := _m.Called(opts, _from, _to, _id)

	if len(ret) == 0 {
		panic("no return value specified for FilterTxToL1")
	}

	var r0 *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1Iterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address, []*big.Int) (*l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1Iterator, error)); ok {
		return rf(opts, _from, _to, _id)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address, []*big.Int) *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1Iterator); ok {
		r0 = rf(opts, _from, _to, _id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1Iterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address, []*big.Int) error); ok {
		r1 = rf(opts, _from, _to, _id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// L2ArbitrumMessengerInterface_FilterTxToL1_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterTxToL1'
type L2ArbitrumMessengerInterface_FilterTxToL1_Call struct {
	*mock.Call
}

// FilterTxToL1 is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - _from []common.Address
//   - _to []common.Address
//   - _id []*big.Int
func (_e *L2ArbitrumMessengerInterface_Expecter) FilterTxToL1(opts interface{}, _from interface{}, _to interface{}, _id interface{}) *L2ArbitrumMessengerInterface_FilterTxToL1_Call {
	return &L2ArbitrumMessengerInterface_FilterTxToL1_Call{Call: _e.mock.On("FilterTxToL1", opts, _from, _to, _id)}
}

func (_c *L2ArbitrumMessengerInterface_FilterTxToL1_Call) Run(run func(opts *bind.FilterOpts, _from []common.Address, _to []common.Address, _id []*big.Int)) *L2ArbitrumMessengerInterface_FilterTxToL1_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address), args[3].([]*big.Int))
	})
	return _c
}

func (_c *L2ArbitrumMessengerInterface_FilterTxToL1_Call) Return(_a0 *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1Iterator, _a1 error) *L2ArbitrumMessengerInterface_FilterTxToL1_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *L2ArbitrumMessengerInterface_FilterTxToL1_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address, []*big.Int) (*l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1Iterator, error)) *L2ArbitrumMessengerInterface_FilterTxToL1_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *L2ArbitrumMessengerInterface) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// L2ArbitrumMessengerInterface_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type L2ArbitrumMessengerInterface_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *L2ArbitrumMessengerInterface_Expecter) ParseLog(log interface{}) *L2ArbitrumMessengerInterface_ParseLog_Call {
	return &L2ArbitrumMessengerInterface_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *L2ArbitrumMessengerInterface_ParseLog_Call) Run(run func(log types.Log)) *L2ArbitrumMessengerInterface_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *L2ArbitrumMessengerInterface_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *L2ArbitrumMessengerInterface_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *L2ArbitrumMessengerInterface_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *L2ArbitrumMessengerInterface_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// ParseTxToL1 provides a mock function with given fields: log
func (_m *L2ArbitrumMessengerInterface) ParseTxToL1(log types.Log) (*l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseTxToL1")
	}

	var r0 *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// L2ArbitrumMessengerInterface_ParseTxToL1_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseTxToL1'
type L2ArbitrumMessengerInterface_ParseTxToL1_Call struct {
	*mock.Call
}

// ParseTxToL1 is a helper method to define mock.On call
//   - log types.Log
func (_e *L2ArbitrumMessengerInterface_Expecter) ParseTxToL1(log interface{}) *L2ArbitrumMessengerInterface_ParseTxToL1_Call {
	return &L2ArbitrumMessengerInterface_ParseTxToL1_Call{Call: _e.mock.On("ParseTxToL1", log)}
}

func (_c *L2ArbitrumMessengerInterface_ParseTxToL1_Call) Run(run func(log types.Log)) *L2ArbitrumMessengerInterface_ParseTxToL1_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *L2ArbitrumMessengerInterface_ParseTxToL1_Call) Return(_a0 *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, _a1 error) *L2ArbitrumMessengerInterface_ParseTxToL1_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *L2ArbitrumMessengerInterface_ParseTxToL1_Call) RunAndReturn(run func(types.Log) (*l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, error)) *L2ArbitrumMessengerInterface_ParseTxToL1_Call {
	_c.Call.Return(run)
	return _c
}

// WatchTxToL1 provides a mock function with given fields: opts, sink, _from, _to, _id
func (_m *L2ArbitrumMessengerInterface) WatchTxToL1(opts *bind.WatchOpts, sink chan<- *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, _from []common.Address, _to []common.Address, _id []*big.Int) (event.Subscription, error) {
	ret := _m.Called(opts, sink, _from, _to, _id)

	if len(ret) == 0 {
		panic("no return value specified for WatchTxToL1")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, []common.Address, []common.Address, []*big.Int) (event.Subscription, error)); ok {
		return rf(opts, sink, _from, _to, _id)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, []common.Address, []common.Address, []*big.Int) event.Subscription); ok {
		r0 = rf(opts, sink, _from, _to, _id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, []common.Address, []common.Address, []*big.Int) error); ok {
		r1 = rf(opts, sink, _from, _to, _id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// L2ArbitrumMessengerInterface_WatchTxToL1_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchTxToL1'
type L2ArbitrumMessengerInterface_WatchTxToL1_Call struct {
	*mock.Call
}

// WatchTxToL1 is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1
//   - _from []common.Address
//   - _to []common.Address
//   - _id []*big.Int
func (_e *L2ArbitrumMessengerInterface_Expecter) WatchTxToL1(opts interface{}, sink interface{}, _from interface{}, _to interface{}, _id interface{}) *L2ArbitrumMessengerInterface_WatchTxToL1_Call {
	return &L2ArbitrumMessengerInterface_WatchTxToL1_Call{Call: _e.mock.On("WatchTxToL1", opts, sink, _from, _to, _id)}
}

func (_c *L2ArbitrumMessengerInterface_WatchTxToL1_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, _from []common.Address, _to []common.Address, _id []*big.Int)) *L2ArbitrumMessengerInterface_WatchTxToL1_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1), args[2].([]common.Address), args[3].([]common.Address), args[4].([]*big.Int))
	})
	return _c
}

func (_c *L2ArbitrumMessengerInterface_WatchTxToL1_Call) Return(_a0 event.Subscription, _a1 error) *L2ArbitrumMessengerInterface_WatchTxToL1_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *L2ArbitrumMessengerInterface_WatchTxToL1_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *l2_arbitrum_messenger.L2ArbitrumMessengerTxToL1, []common.Address, []common.Address, []*big.Int) (event.Subscription, error)) *L2ArbitrumMessengerInterface_WatchTxToL1_Call {
	_c.Call.Return(run)
	return _c
}

// NewL2ArbitrumMessengerInterface creates a new instance of L2ArbitrumMessengerInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewL2ArbitrumMessengerInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *L2ArbitrumMessengerInterface {
	mock := &L2ArbitrumMessengerInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
