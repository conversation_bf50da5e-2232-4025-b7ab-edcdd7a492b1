// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_optimism_portal_2

import (
	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	mock "github.com/stretchr/testify/mock"
)

// OptimismPortal2Interface is an autogenerated mock type for the OptimismPortal2Interface type
type OptimismPortal2Interface struct {
	mock.Mock
}

type OptimismPortal2Interface_Expecter struct {
	mock *mock.Mock
}

func (_m *OptimismPortal2Interface) EXPECT() *OptimismPortal2Interface_Expecter {
	return &OptimismPortal2Interface_Expecter{mock: &_m.Mock}
}

// Address provides a mock function with given fields:
func (_m *OptimismPortal2Interface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// OptimismPortal2Interface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type OptimismPortal2Interface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *OptimismPortal2Interface_Expecter) Address() *OptimismPortal2Interface_Address_Call {
	return &OptimismPortal2Interface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *OptimismPortal2Interface_Address_Call) Run(run func()) *OptimismPortal2Interface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *OptimismPortal2Interface_Address_Call) Return(_a0 common.Address) *OptimismPortal2Interface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *OptimismPortal2Interface_Address_Call) RunAndReturn(run func() common.Address) *OptimismPortal2Interface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// DisputeGameFactory provides a mock function with given fields: opts
func (_m *OptimismPortal2Interface) DisputeGameFactory(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for DisputeGameFactory")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OptimismPortal2Interface_DisputeGameFactory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DisputeGameFactory'
type OptimismPortal2Interface_DisputeGameFactory_Call struct {
	*mock.Call
}

// DisputeGameFactory is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *OptimismPortal2Interface_Expecter) DisputeGameFactory(opts interface{}) *OptimismPortal2Interface_DisputeGameFactory_Call {
	return &OptimismPortal2Interface_DisputeGameFactory_Call{Call: _e.mock.On("DisputeGameFactory", opts)}
}

func (_c *OptimismPortal2Interface_DisputeGameFactory_Call) Run(run func(opts *bind.CallOpts)) *OptimismPortal2Interface_DisputeGameFactory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *OptimismPortal2Interface_DisputeGameFactory_Call) Return(_a0 common.Address, _a1 error) *OptimismPortal2Interface_DisputeGameFactory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OptimismPortal2Interface_DisputeGameFactory_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *OptimismPortal2Interface_DisputeGameFactory_Call {
	_c.Call.Return(run)
	return _c
}

// RespectedGameType provides a mock function with given fields: opts
func (_m *OptimismPortal2Interface) RespectedGameType(opts *bind.CallOpts) (uint32, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for RespectedGameType")
	}

	var r0 uint32
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (uint32, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) uint32); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(uint32)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// OptimismPortal2Interface_RespectedGameType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RespectedGameType'
type OptimismPortal2Interface_RespectedGameType_Call struct {
	*mock.Call
}

// RespectedGameType is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *OptimismPortal2Interface_Expecter) RespectedGameType(opts interface{}) *OptimismPortal2Interface_RespectedGameType_Call {
	return &OptimismPortal2Interface_RespectedGameType_Call{Call: _e.mock.On("RespectedGameType", opts)}
}

func (_c *OptimismPortal2Interface_RespectedGameType_Call) Run(run func(opts *bind.CallOpts)) *OptimismPortal2Interface_RespectedGameType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *OptimismPortal2Interface_RespectedGameType_Call) Return(_a0 uint32, _a1 error) *OptimismPortal2Interface_RespectedGameType_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *OptimismPortal2Interface_RespectedGameType_Call) RunAndReturn(run func(*bind.CallOpts) (uint32, error)) *OptimismPortal2Interface_RespectedGameType_Call {
	_c.Call.Return(run)
	return _c
}

// NewOptimismPortal2Interface creates a new instance of OptimismPortal2Interface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewOptimismPortal2Interface(t interface {
	mock.TestingT
	Cleanup(func())
}) *OptimismPortal2Interface {
	mock := &OptimismPortal2Interface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
