// Code generated by mockery v2.43.2. DO NOT EDIT.

package mock_arbitrum_inbox

import (
	big "math/big"

	arbitrum_inbox "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/liquiditymanager/generated/arbitrum_inbox"

	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"

	common "github.com/ethereum/go-ethereum/common"

	event "github.com/ethereum/go-ethereum/event"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// ArbitrumInboxInterface is an autogenerated mock type for the ArbitrumInboxInterface type
type ArbitrumInboxInterface struct {
	mock.Mock
}

type ArbitrumInboxInterface_Expecter struct {
	mock *mock.Mock
}

func (_m *ArbitrumInboxInterface) EXPECT() *ArbitrumInboxInterface_Expecter {
	return &ArbitrumInboxInterface_Expecter{mock: &_m.Mock}
}

// Address provides a mock function with given fields:
func (_m *ArbitrumInboxInterface) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// ArbitrumInboxInterface_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type ArbitrumInboxInterface_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *ArbitrumInboxInterface_Expecter) Address() *ArbitrumInboxInterface_Address_Call {
	return &ArbitrumInboxInterface_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *ArbitrumInboxInterface_Address_Call) Run(run func()) *ArbitrumInboxInterface_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ArbitrumInboxInterface_Address_Call) Return(_a0 common.Address) *ArbitrumInboxInterface_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ArbitrumInboxInterface_Address_Call) RunAndReturn(run func() common.Address) *ArbitrumInboxInterface_Address_Call {
	_c.Call.Return(run)
	return _c
}

// AllowListEnabled provides a mock function with given fields: opts
func (_m *ArbitrumInboxInterface) AllowListEnabled(opts *bind.CallOpts) (bool, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for AllowListEnabled")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (bool, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) bool); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_AllowListEnabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AllowListEnabled'
type ArbitrumInboxInterface_AllowListEnabled_Call struct {
	*mock.Call
}

// AllowListEnabled is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbitrumInboxInterface_Expecter) AllowListEnabled(opts interface{}) *ArbitrumInboxInterface_AllowListEnabled_Call {
	return &ArbitrumInboxInterface_AllowListEnabled_Call{Call: _e.mock.On("AllowListEnabled", opts)}
}

func (_c *ArbitrumInboxInterface_AllowListEnabled_Call) Run(run func(opts *bind.CallOpts)) *ArbitrumInboxInterface_AllowListEnabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_AllowListEnabled_Call) Return(_a0 bool, _a1 error) *ArbitrumInboxInterface_AllowListEnabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_AllowListEnabled_Call) RunAndReturn(run func(*bind.CallOpts) (bool, error)) *ArbitrumInboxInterface_AllowListEnabled_Call {
	_c.Call.Return(run)
	return _c
}

// Bridge provides a mock function with given fields: opts
func (_m *ArbitrumInboxInterface) Bridge(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Bridge")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_Bridge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Bridge'
type ArbitrumInboxInterface_Bridge_Call struct {
	*mock.Call
}

// Bridge is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbitrumInboxInterface_Expecter) Bridge(opts interface{}) *ArbitrumInboxInterface_Bridge_Call {
	return &ArbitrumInboxInterface_Bridge_Call{Call: _e.mock.On("Bridge", opts)}
}

func (_c *ArbitrumInboxInterface_Bridge_Call) Run(run func(opts *bind.CallOpts)) *ArbitrumInboxInterface_Bridge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_Bridge_Call) Return(_a0 common.Address, _a1 error) *ArbitrumInboxInterface_Bridge_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_Bridge_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbitrumInboxInterface_Bridge_Call {
	_c.Call.Return(run)
	return _c
}

// CalculateRetryableSubmissionFee provides a mock function with given fields: opts, dataLength, baseFee
func (_m *ArbitrumInboxInterface) CalculateRetryableSubmissionFee(opts *bind.CallOpts, dataLength *big.Int, baseFee *big.Int) (*big.Int, error) {
	ret := _m.Called(opts, dataLength, baseFee)

	if len(ret) == 0 {
		panic("no return value specified for CalculateRetryableSubmissionFee")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int, *big.Int) (*big.Int, error)); ok {
		return rf(opts, dataLength, baseFee)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, *big.Int, *big.Int) *big.Int); ok {
		r0 = rf(opts, dataLength, baseFee)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, *big.Int, *big.Int) error); ok {
		r1 = rf(opts, dataLength, baseFee)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CalculateRetryableSubmissionFee'
type ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call struct {
	*mock.Call
}

// CalculateRetryableSubmissionFee is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - dataLength *big.Int
//   - baseFee *big.Int
func (_e *ArbitrumInboxInterface_Expecter) CalculateRetryableSubmissionFee(opts interface{}, dataLength interface{}, baseFee interface{}) *ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call {
	return &ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call{Call: _e.mock.On("CalculateRetryableSubmissionFee", opts, dataLength, baseFee)}
}

func (_c *ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call) Run(run func(opts *bind.CallOpts, dataLength *big.Int, baseFee *big.Int)) *ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(*big.Int), args[2].(*big.Int))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call) Return(_a0 *big.Int, _a1 error) *ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call) RunAndReturn(run func(*bind.CallOpts, *big.Int, *big.Int) (*big.Int, error)) *ArbitrumInboxInterface_CalculateRetryableSubmissionFee_Call {
	_c.Call.Return(run)
	return _c
}

// FilterInboxMessageDelivered provides a mock function with given fields: opts, messageNum
func (_m *ArbitrumInboxInterface) FilterInboxMessageDelivered(opts *bind.FilterOpts, messageNum []*big.Int) (*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredIterator, error) {
	ret := _m.Called(opts, messageNum)

	if len(ret) == 0 {
		panic("no return value specified for FilterInboxMessageDelivered")
	}

	var r0 *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []*big.Int) (*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredIterator, error)); ok {
		return rf(opts, messageNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []*big.Int) *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredIterator); ok {
		r0 = rf(opts, messageNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []*big.Int) error); ok {
		r1 = rf(opts, messageNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_FilterInboxMessageDelivered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterInboxMessageDelivered'
type ArbitrumInboxInterface_FilterInboxMessageDelivered_Call struct {
	*mock.Call
}

// FilterInboxMessageDelivered is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - messageNum []*big.Int
func (_e *ArbitrumInboxInterface_Expecter) FilterInboxMessageDelivered(opts interface{}, messageNum interface{}) *ArbitrumInboxInterface_FilterInboxMessageDelivered_Call {
	return &ArbitrumInboxInterface_FilterInboxMessageDelivered_Call{Call: _e.mock.On("FilterInboxMessageDelivered", opts, messageNum)}
}

func (_c *ArbitrumInboxInterface_FilterInboxMessageDelivered_Call) Run(run func(opts *bind.FilterOpts, messageNum []*big.Int)) *ArbitrumInboxInterface_FilterInboxMessageDelivered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]*big.Int))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_FilterInboxMessageDelivered_Call) Return(_a0 *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredIterator, _a1 error) *ArbitrumInboxInterface_FilterInboxMessageDelivered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_FilterInboxMessageDelivered_Call) RunAndReturn(run func(*bind.FilterOpts, []*big.Int) (*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredIterator, error)) *ArbitrumInboxInterface_FilterInboxMessageDelivered_Call {
	_c.Call.Return(run)
	return _c
}

// FilterInboxMessageDeliveredFromOrigin provides a mock function with given fields: opts, messageNum
func (_m *ArbitrumInboxInterface) FilterInboxMessageDeliveredFromOrigin(opts *bind.FilterOpts, messageNum []*big.Int) (*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOriginIterator, error) {
	ret := _m.Called(opts, messageNum)

	if len(ret) == 0 {
		panic("no return value specified for FilterInboxMessageDeliveredFromOrigin")
	}

	var r0 *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOriginIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []*big.Int) (*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOriginIterator, error)); ok {
		return rf(opts, messageNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []*big.Int) *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOriginIterator); ok {
		r0 = rf(opts, messageNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOriginIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []*big.Int) error); ok {
		r1 = rf(opts, messageNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterInboxMessageDeliveredFromOrigin'
type ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call struct {
	*mock.Call
}

// FilterInboxMessageDeliveredFromOrigin is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - messageNum []*big.Int
func (_e *ArbitrumInboxInterface_Expecter) FilterInboxMessageDeliveredFromOrigin(opts interface{}, messageNum interface{}) *ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call {
	return &ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call{Call: _e.mock.On("FilterInboxMessageDeliveredFromOrigin", opts, messageNum)}
}

func (_c *ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call) Run(run func(opts *bind.FilterOpts, messageNum []*big.Int)) *ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]*big.Int))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call) Return(_a0 *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOriginIterator, _a1 error) *ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call) RunAndReturn(run func(*bind.FilterOpts, []*big.Int) (*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOriginIterator, error)) *ArbitrumInboxInterface_FilterInboxMessageDeliveredFromOrigin_Call {
	_c.Call.Return(run)
	return _c
}

// GetProxyAdmin provides a mock function with given fields: opts
func (_m *ArbitrumInboxInterface) GetProxyAdmin(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for GetProxyAdmin")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_GetProxyAdmin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProxyAdmin'
type ArbitrumInboxInterface_GetProxyAdmin_Call struct {
	*mock.Call
}

// GetProxyAdmin is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbitrumInboxInterface_Expecter) GetProxyAdmin(opts interface{}) *ArbitrumInboxInterface_GetProxyAdmin_Call {
	return &ArbitrumInboxInterface_GetProxyAdmin_Call{Call: _e.mock.On("GetProxyAdmin", opts)}
}

func (_c *ArbitrumInboxInterface_GetProxyAdmin_Call) Run(run func(opts *bind.CallOpts)) *ArbitrumInboxInterface_GetProxyAdmin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_GetProxyAdmin_Call) Return(_a0 common.Address, _a1 error) *ArbitrumInboxInterface_GetProxyAdmin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_GetProxyAdmin_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbitrumInboxInterface_GetProxyAdmin_Call {
	_c.Call.Return(run)
	return _c
}

// Initialize provides a mock function with given fields: opts, _bridge, _sequencerInbox
func (_m *ArbitrumInboxInterface) Initialize(opts *bind.TransactOpts, _bridge common.Address, _sequencerInbox common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, _bridge, _sequencerInbox)

	if len(ret) == 0 {
		panic("no return value specified for Initialize")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, _bridge, _sequencerInbox)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address, common.Address) *types.Transaction); ok {
		r0 = rf(opts, _bridge, _sequencerInbox)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address, common.Address) error); ok {
		r1 = rf(opts, _bridge, _sequencerInbox)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_Initialize_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Initialize'
type ArbitrumInboxInterface_Initialize_Call struct {
	*mock.Call
}

// Initialize is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _bridge common.Address
//   - _sequencerInbox common.Address
func (_e *ArbitrumInboxInterface_Expecter) Initialize(opts interface{}, _bridge interface{}, _sequencerInbox interface{}) *ArbitrumInboxInterface_Initialize_Call {
	return &ArbitrumInboxInterface_Initialize_Call{Call: _e.mock.On("Initialize", opts, _bridge, _sequencerInbox)}
}

func (_c *ArbitrumInboxInterface_Initialize_Call) Run(run func(opts *bind.TransactOpts, _bridge common.Address, _sequencerInbox common.Address)) *ArbitrumInboxInterface_Initialize_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address), args[2].(common.Address))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_Initialize_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumInboxInterface_Initialize_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_Initialize_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address, common.Address) (*types.Transaction, error)) *ArbitrumInboxInterface_Initialize_Call {
	_c.Call.Return(run)
	return _c
}

// IsAllowed provides a mock function with given fields: opts, user
func (_m *ArbitrumInboxInterface) IsAllowed(opts *bind.CallOpts, user common.Address) (bool, error) {
	ret := _m.Called(opts, user)

	if len(ret) == 0 {
		panic("no return value specified for IsAllowed")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (bool, error)); ok {
		return rf(opts, user)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) bool); ok {
		r0 = rf(opts, user)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_IsAllowed_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsAllowed'
type ArbitrumInboxInterface_IsAllowed_Call struct {
	*mock.Call
}

// IsAllowed is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - user common.Address
func (_e *ArbitrumInboxInterface_Expecter) IsAllowed(opts interface{}, user interface{}) *ArbitrumInboxInterface_IsAllowed_Call {
	return &ArbitrumInboxInterface_IsAllowed_Call{Call: _e.mock.On("IsAllowed", opts, user)}
}

func (_c *ArbitrumInboxInterface_IsAllowed_Call) Run(run func(opts *bind.CallOpts, user common.Address)) *ArbitrumInboxInterface_IsAllowed_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_IsAllowed_Call) Return(_a0 bool, _a1 error) *ArbitrumInboxInterface_IsAllowed_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_IsAllowed_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (bool, error)) *ArbitrumInboxInterface_IsAllowed_Call {
	_c.Call.Return(run)
	return _c
}

// MaxDataSize provides a mock function with given fields: opts
func (_m *ArbitrumInboxInterface) MaxDataSize(opts *bind.CallOpts) (*big.Int, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for MaxDataSize")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (*big.Int, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) *big.Int); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_MaxDataSize_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MaxDataSize'
type ArbitrumInboxInterface_MaxDataSize_Call struct {
	*mock.Call
}

// MaxDataSize is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbitrumInboxInterface_Expecter) MaxDataSize(opts interface{}) *ArbitrumInboxInterface_MaxDataSize_Call {
	return &ArbitrumInboxInterface_MaxDataSize_Call{Call: _e.mock.On("MaxDataSize", opts)}
}

func (_c *ArbitrumInboxInterface_MaxDataSize_Call) Run(run func(opts *bind.CallOpts)) *ArbitrumInboxInterface_MaxDataSize_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_MaxDataSize_Call) Return(_a0 *big.Int, _a1 error) *ArbitrumInboxInterface_MaxDataSize_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_MaxDataSize_Call) RunAndReturn(run func(*bind.CallOpts) (*big.Int, error)) *ArbitrumInboxInterface_MaxDataSize_Call {
	_c.Call.Return(run)
	return _c
}

// ParseInboxMessageDelivered provides a mock function with given fields: log
func (_m *ArbitrumInboxInterface) ParseInboxMessageDelivered(log types.Log) (*arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseInboxMessageDelivered")
	}

	var r0 *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_inbox.ArbitrumInboxInboxMessageDelivered)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_ParseInboxMessageDelivered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseInboxMessageDelivered'
type ArbitrumInboxInterface_ParseInboxMessageDelivered_Call struct {
	*mock.Call
}

// ParseInboxMessageDelivered is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbitrumInboxInterface_Expecter) ParseInboxMessageDelivered(log interface{}) *ArbitrumInboxInterface_ParseInboxMessageDelivered_Call {
	return &ArbitrumInboxInterface_ParseInboxMessageDelivered_Call{Call: _e.mock.On("ParseInboxMessageDelivered", log)}
}

func (_c *ArbitrumInboxInterface_ParseInboxMessageDelivered_Call) Run(run func(log types.Log)) *ArbitrumInboxInterface_ParseInboxMessageDelivered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_ParseInboxMessageDelivered_Call) Return(_a0 *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, _a1 error) *ArbitrumInboxInterface_ParseInboxMessageDelivered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_ParseInboxMessageDelivered_Call) RunAndReturn(run func(types.Log) (*arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, error)) *ArbitrumInboxInterface_ParseInboxMessageDelivered_Call {
	_c.Call.Return(run)
	return _c
}

// ParseInboxMessageDeliveredFromOrigin provides a mock function with given fields: log
func (_m *ArbitrumInboxInterface) ParseInboxMessageDeliveredFromOrigin(log types.Log) (*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseInboxMessageDeliveredFromOrigin")
	}

	var r0 *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseInboxMessageDeliveredFromOrigin'
type ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call struct {
	*mock.Call
}

// ParseInboxMessageDeliveredFromOrigin is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbitrumInboxInterface_Expecter) ParseInboxMessageDeliveredFromOrigin(log interface{}) *ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call {
	return &ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call{Call: _e.mock.On("ParseInboxMessageDeliveredFromOrigin", log)}
}

func (_c *ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call) Run(run func(log types.Log)) *ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call) Return(_a0 *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, _a1 error) *ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call) RunAndReturn(run func(types.Log) (*arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, error)) *ArbitrumInboxInterface_ParseInboxMessageDeliveredFromOrigin_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *ArbitrumInboxInterface) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type ArbitrumInboxInterface_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *ArbitrumInboxInterface_Expecter) ParseLog(log interface{}) *ArbitrumInboxInterface_ParseLog_Call {
	return &ArbitrumInboxInterface_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *ArbitrumInboxInterface_ParseLog_Call) Run(run func(log types.Log)) *ArbitrumInboxInterface_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *ArbitrumInboxInterface_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *ArbitrumInboxInterface_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// Pause provides a mock function with given fields: opts
func (_m *ArbitrumInboxInterface) Pause(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Pause")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_Pause_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Pause'
type ArbitrumInboxInterface_Pause_Call struct {
	*mock.Call
}

// Pause is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *ArbitrumInboxInterface_Expecter) Pause(opts interface{}) *ArbitrumInboxInterface_Pause_Call {
	return &ArbitrumInboxInterface_Pause_Call{Call: _e.mock.On("Pause", opts)}
}

func (_c *ArbitrumInboxInterface_Pause_Call) Run(run func(opts *bind.TransactOpts)) *ArbitrumInboxInterface_Pause_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_Pause_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumInboxInterface_Pause_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_Pause_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *ArbitrumInboxInterface_Pause_Call {
	_c.Call.Return(run)
	return _c
}

// SendContractTransaction provides a mock function with given fields: opts, gasLimit, maxFeePerGas, to, value, data
func (_m *ArbitrumInboxInterface) SendContractTransaction(opts *bind.TransactOpts, gasLimit *big.Int, maxFeePerGas *big.Int, to common.Address, value *big.Int, data []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, gasLimit, maxFeePerGas, to, value, data)

	if len(ret) == 0 {
		panic("no return value specified for SendContractTransaction")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, *big.Int, *big.Int, common.Address, *big.Int, []byte) (*types.Transaction, error)); ok {
		return rf(opts, gasLimit, maxFeePerGas, to, value, data)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, *big.Int, *big.Int, common.Address, *big.Int, []byte) *types.Transaction); ok {
		r0 = rf(opts, gasLimit, maxFeePerGas, to, value, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, *big.Int, *big.Int, common.Address, *big.Int, []byte) error); ok {
		r1 = rf(opts, gasLimit, maxFeePerGas, to, value, data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_SendContractTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendContractTransaction'
type ArbitrumInboxInterface_SendContractTransaction_Call struct {
	*mock.Call
}

// SendContractTransaction is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - gasLimit *big.Int
//   - maxFeePerGas *big.Int
//   - to common.Address
//   - value *big.Int
//   - data []byte
func (_e *ArbitrumInboxInterface_Expecter) SendContractTransaction(opts interface{}, gasLimit interface{}, maxFeePerGas interface{}, to interface{}, value interface{}, data interface{}) *ArbitrumInboxInterface_SendContractTransaction_Call {
	return &ArbitrumInboxInterface_SendContractTransaction_Call{Call: _e.mock.On("SendContractTransaction", opts, gasLimit, maxFeePerGas, to, value, data)}
}

func (_c *ArbitrumInboxInterface_SendContractTransaction_Call) Run(run func(opts *bind.TransactOpts, gasLimit *big.Int, maxFeePerGas *big.Int, to common.Address, value *big.Int, data []byte)) *ArbitrumInboxInterface_SendContractTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(*big.Int), args[2].(*big.Int), args[3].(common.Address), args[4].(*big.Int), args[5].([]byte))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_SendContractTransaction_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumInboxInterface_SendContractTransaction_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_SendContractTransaction_Call) RunAndReturn(run func(*bind.TransactOpts, *big.Int, *big.Int, common.Address, *big.Int, []byte) (*types.Transaction, error)) *ArbitrumInboxInterface_SendContractTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// SendL2Message provides a mock function with given fields: opts, messageData
func (_m *ArbitrumInboxInterface) SendL2Message(opts *bind.TransactOpts, messageData []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, messageData)

	if len(ret) == 0 {
		panic("no return value specified for SendL2Message")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []byte) (*types.Transaction, error)); ok {
		return rf(opts, messageData)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []byte) *types.Transaction); ok {
		r0 = rf(opts, messageData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []byte) error); ok {
		r1 = rf(opts, messageData)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_SendL2Message_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendL2Message'
type ArbitrumInboxInterface_SendL2Message_Call struct {
	*mock.Call
}

// SendL2Message is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - messageData []byte
func (_e *ArbitrumInboxInterface_Expecter) SendL2Message(opts interface{}, messageData interface{}) *ArbitrumInboxInterface_SendL2Message_Call {
	return &ArbitrumInboxInterface_SendL2Message_Call{Call: _e.mock.On("SendL2Message", opts, messageData)}
}

func (_c *ArbitrumInboxInterface_SendL2Message_Call) Run(run func(opts *bind.TransactOpts, messageData []byte)) *ArbitrumInboxInterface_SendL2Message_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]byte))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_SendL2Message_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumInboxInterface_SendL2Message_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_SendL2Message_Call) RunAndReturn(run func(*bind.TransactOpts, []byte) (*types.Transaction, error)) *ArbitrumInboxInterface_SendL2Message_Call {
	_c.Call.Return(run)
	return _c
}

// SendL2MessageFromOrigin provides a mock function with given fields: opts, messageData
func (_m *ArbitrumInboxInterface) SendL2MessageFromOrigin(opts *bind.TransactOpts, messageData []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, messageData)

	if len(ret) == 0 {
		panic("no return value specified for SendL2MessageFromOrigin")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []byte) (*types.Transaction, error)); ok {
		return rf(opts, messageData)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []byte) *types.Transaction); ok {
		r0 = rf(opts, messageData)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []byte) error); ok {
		r1 = rf(opts, messageData)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_SendL2MessageFromOrigin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendL2MessageFromOrigin'
type ArbitrumInboxInterface_SendL2MessageFromOrigin_Call struct {
	*mock.Call
}

// SendL2MessageFromOrigin is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - messageData []byte
func (_e *ArbitrumInboxInterface_Expecter) SendL2MessageFromOrigin(opts interface{}, messageData interface{}) *ArbitrumInboxInterface_SendL2MessageFromOrigin_Call {
	return &ArbitrumInboxInterface_SendL2MessageFromOrigin_Call{Call: _e.mock.On("SendL2MessageFromOrigin", opts, messageData)}
}

func (_c *ArbitrumInboxInterface_SendL2MessageFromOrigin_Call) Run(run func(opts *bind.TransactOpts, messageData []byte)) *ArbitrumInboxInterface_SendL2MessageFromOrigin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]byte))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_SendL2MessageFromOrigin_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumInboxInterface_SendL2MessageFromOrigin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_SendL2MessageFromOrigin_Call) RunAndReturn(run func(*bind.TransactOpts, []byte) (*types.Transaction, error)) *ArbitrumInboxInterface_SendL2MessageFromOrigin_Call {
	_c.Call.Return(run)
	return _c
}

// SendUnsignedTransaction provides a mock function with given fields: opts, gasLimit, maxFeePerGas, nonce, to, value, data
func (_m *ArbitrumInboxInterface) SendUnsignedTransaction(opts *bind.TransactOpts, gasLimit *big.Int, maxFeePerGas *big.Int, nonce *big.Int, to common.Address, value *big.Int, data []byte) (*types.Transaction, error) {
	ret := _m.Called(opts, gasLimit, maxFeePerGas, nonce, to, value, data)

	if len(ret) == 0 {
		panic("no return value specified for SendUnsignedTransaction")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, *big.Int, *big.Int, *big.Int, common.Address, *big.Int, []byte) (*types.Transaction, error)); ok {
		return rf(opts, gasLimit, maxFeePerGas, nonce, to, value, data)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, *big.Int, *big.Int, *big.Int, common.Address, *big.Int, []byte) *types.Transaction); ok {
		r0 = rf(opts, gasLimit, maxFeePerGas, nonce, to, value, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, *big.Int, *big.Int, *big.Int, common.Address, *big.Int, []byte) error); ok {
		r1 = rf(opts, gasLimit, maxFeePerGas, nonce, to, value, data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_SendUnsignedTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendUnsignedTransaction'
type ArbitrumInboxInterface_SendUnsignedTransaction_Call struct {
	*mock.Call
}

// SendUnsignedTransaction is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - gasLimit *big.Int
//   - maxFeePerGas *big.Int
//   - nonce *big.Int
//   - to common.Address
//   - value *big.Int
//   - data []byte
func (_e *ArbitrumInboxInterface_Expecter) SendUnsignedTransaction(opts interface{}, gasLimit interface{}, maxFeePerGas interface{}, nonce interface{}, to interface{}, value interface{}, data interface{}) *ArbitrumInboxInterface_SendUnsignedTransaction_Call {
	return &ArbitrumInboxInterface_SendUnsignedTransaction_Call{Call: _e.mock.On("SendUnsignedTransaction", opts, gasLimit, maxFeePerGas, nonce, to, value, data)}
}

func (_c *ArbitrumInboxInterface_SendUnsignedTransaction_Call) Run(run func(opts *bind.TransactOpts, gasLimit *big.Int, maxFeePerGas *big.Int, nonce *big.Int, to common.Address, value *big.Int, data []byte)) *ArbitrumInboxInterface_SendUnsignedTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(*big.Int), args[2].(*big.Int), args[3].(*big.Int), args[4].(common.Address), args[5].(*big.Int), args[6].([]byte))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_SendUnsignedTransaction_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumInboxInterface_SendUnsignedTransaction_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_SendUnsignedTransaction_Call) RunAndReturn(run func(*bind.TransactOpts, *big.Int, *big.Int, *big.Int, common.Address, *big.Int, []byte) (*types.Transaction, error)) *ArbitrumInboxInterface_SendUnsignedTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// SequencerInbox provides a mock function with given fields: opts
func (_m *ArbitrumInboxInterface) SequencerInbox(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for SequencerInbox")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_SequencerInbox_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SequencerInbox'
type ArbitrumInboxInterface_SequencerInbox_Call struct {
	*mock.Call
}

// SequencerInbox is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *ArbitrumInboxInterface_Expecter) SequencerInbox(opts interface{}) *ArbitrumInboxInterface_SequencerInbox_Call {
	return &ArbitrumInboxInterface_SequencerInbox_Call{Call: _e.mock.On("SequencerInbox", opts)}
}

func (_c *ArbitrumInboxInterface_SequencerInbox_Call) Run(run func(opts *bind.CallOpts)) *ArbitrumInboxInterface_SequencerInbox_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_SequencerInbox_Call) Return(_a0 common.Address, _a1 error) *ArbitrumInboxInterface_SequencerInbox_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_SequencerInbox_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *ArbitrumInboxInterface_SequencerInbox_Call {
	_c.Call.Return(run)
	return _c
}

// SetAllowList provides a mock function with given fields: opts, user, val
func (_m *ArbitrumInboxInterface) SetAllowList(opts *bind.TransactOpts, user []common.Address, val []bool) (*types.Transaction, error) {
	ret := _m.Called(opts, user, val)

	if len(ret) == 0 {
		panic("no return value specified for SetAllowList")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address, []bool) (*types.Transaction, error)); ok {
		return rf(opts, user, val)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address, []bool) *types.Transaction); ok {
		r0 = rf(opts, user, val)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []common.Address, []bool) error); ok {
		r1 = rf(opts, user, val)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_SetAllowList_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetAllowList'
type ArbitrumInboxInterface_SetAllowList_Call struct {
	*mock.Call
}

// SetAllowList is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - user []common.Address
//   - val []bool
func (_e *ArbitrumInboxInterface_Expecter) SetAllowList(opts interface{}, user interface{}, val interface{}) *ArbitrumInboxInterface_SetAllowList_Call {
	return &ArbitrumInboxInterface_SetAllowList_Call{Call: _e.mock.On("SetAllowList", opts, user, val)}
}

func (_c *ArbitrumInboxInterface_SetAllowList_Call) Run(run func(opts *bind.TransactOpts, user []common.Address, val []bool)) *ArbitrumInboxInterface_SetAllowList_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]common.Address), args[2].([]bool))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_SetAllowList_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumInboxInterface_SetAllowList_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_SetAllowList_Call) RunAndReturn(run func(*bind.TransactOpts, []common.Address, []bool) (*types.Transaction, error)) *ArbitrumInboxInterface_SetAllowList_Call {
	_c.Call.Return(run)
	return _c
}

// SetAllowListEnabled provides a mock function with given fields: opts, _allowListEnabled
func (_m *ArbitrumInboxInterface) SetAllowListEnabled(opts *bind.TransactOpts, _allowListEnabled bool) (*types.Transaction, error) {
	ret := _m.Called(opts, _allowListEnabled)

	if len(ret) == 0 {
		panic("no return value specified for SetAllowListEnabled")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, bool) (*types.Transaction, error)); ok {
		return rf(opts, _allowListEnabled)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, bool) *types.Transaction); ok {
		r0 = rf(opts, _allowListEnabled)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, bool) error); ok {
		r1 = rf(opts, _allowListEnabled)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_SetAllowListEnabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetAllowListEnabled'
type ArbitrumInboxInterface_SetAllowListEnabled_Call struct {
	*mock.Call
}

// SetAllowListEnabled is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _allowListEnabled bool
func (_e *ArbitrumInboxInterface_Expecter) SetAllowListEnabled(opts interface{}, _allowListEnabled interface{}) *ArbitrumInboxInterface_SetAllowListEnabled_Call {
	return &ArbitrumInboxInterface_SetAllowListEnabled_Call{Call: _e.mock.On("SetAllowListEnabled", opts, _allowListEnabled)}
}

func (_c *ArbitrumInboxInterface_SetAllowListEnabled_Call) Run(run func(opts *bind.TransactOpts, _allowListEnabled bool)) *ArbitrumInboxInterface_SetAllowListEnabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(bool))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_SetAllowListEnabled_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumInboxInterface_SetAllowListEnabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_SetAllowListEnabled_Call) RunAndReturn(run func(*bind.TransactOpts, bool) (*types.Transaction, error)) *ArbitrumInboxInterface_SetAllowListEnabled_Call {
	_c.Call.Return(run)
	return _c
}

// Unpause provides a mock function with given fields: opts
func (_m *ArbitrumInboxInterface) Unpause(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Unpause")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_Unpause_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Unpause'
type ArbitrumInboxInterface_Unpause_Call struct {
	*mock.Call
}

// Unpause is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *ArbitrumInboxInterface_Expecter) Unpause(opts interface{}) *ArbitrumInboxInterface_Unpause_Call {
	return &ArbitrumInboxInterface_Unpause_Call{Call: _e.mock.On("Unpause", opts)}
}

func (_c *ArbitrumInboxInterface_Unpause_Call) Run(run func(opts *bind.TransactOpts)) *ArbitrumInboxInterface_Unpause_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_Unpause_Call) Return(_a0 *types.Transaction, _a1 error) *ArbitrumInboxInterface_Unpause_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_Unpause_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *ArbitrumInboxInterface_Unpause_Call {
	_c.Call.Return(run)
	return _c
}

// WatchInboxMessageDelivered provides a mock function with given fields: opts, sink, messageNum
func (_m *ArbitrumInboxInterface) WatchInboxMessageDelivered(opts *bind.WatchOpts, sink chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, messageNum []*big.Int) (event.Subscription, error) {
	ret := _m.Called(opts, sink, messageNum)

	if len(ret) == 0 {
		panic("no return value specified for WatchInboxMessageDelivered")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, []*big.Int) (event.Subscription, error)); ok {
		return rf(opts, sink, messageNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, []*big.Int) event.Subscription); ok {
		r0 = rf(opts, sink, messageNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, []*big.Int) error); ok {
		r1 = rf(opts, sink, messageNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_WatchInboxMessageDelivered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchInboxMessageDelivered'
type ArbitrumInboxInterface_WatchInboxMessageDelivered_Call struct {
	*mock.Call
}

// WatchInboxMessageDelivered is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered
//   - messageNum []*big.Int
func (_e *ArbitrumInboxInterface_Expecter) WatchInboxMessageDelivered(opts interface{}, sink interface{}, messageNum interface{}) *ArbitrumInboxInterface_WatchInboxMessageDelivered_Call {
	return &ArbitrumInboxInterface_WatchInboxMessageDelivered_Call{Call: _e.mock.On("WatchInboxMessageDelivered", opts, sink, messageNum)}
}

func (_c *ArbitrumInboxInterface_WatchInboxMessageDelivered_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, messageNum []*big.Int)) *ArbitrumInboxInterface_WatchInboxMessageDelivered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered), args[2].([]*big.Int))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_WatchInboxMessageDelivered_Call) Return(_a0 event.Subscription, _a1 error) *ArbitrumInboxInterface_WatchInboxMessageDelivered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_WatchInboxMessageDelivered_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDelivered, []*big.Int) (event.Subscription, error)) *ArbitrumInboxInterface_WatchInboxMessageDelivered_Call {
	_c.Call.Return(run)
	return _c
}

// WatchInboxMessageDeliveredFromOrigin provides a mock function with given fields: opts, sink, messageNum
func (_m *ArbitrumInboxInterface) WatchInboxMessageDeliveredFromOrigin(opts *bind.WatchOpts, sink chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, messageNum []*big.Int) (event.Subscription, error) {
	ret := _m.Called(opts, sink, messageNum)

	if len(ret) == 0 {
		panic("no return value specified for WatchInboxMessageDeliveredFromOrigin")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, []*big.Int) (event.Subscription, error)); ok {
		return rf(opts, sink, messageNum)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, []*big.Int) event.Subscription); ok {
		r0 = rf(opts, sink, messageNum)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, []*big.Int) error); ok {
		r1 = rf(opts, sink, messageNum)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchInboxMessageDeliveredFromOrigin'
type ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call struct {
	*mock.Call
}

// WatchInboxMessageDeliveredFromOrigin is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin
//   - messageNum []*big.Int
func (_e *ArbitrumInboxInterface_Expecter) WatchInboxMessageDeliveredFromOrigin(opts interface{}, sink interface{}, messageNum interface{}) *ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call {
	return &ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call{Call: _e.mock.On("WatchInboxMessageDeliveredFromOrigin", opts, sink, messageNum)}
}

func (_c *ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, messageNum []*big.Int)) *ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin), args[2].([]*big.Int))
	})
	return _c
}

func (_c *ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call) Return(_a0 event.Subscription, _a1 error) *ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *arbitrum_inbox.ArbitrumInboxInboxMessageDeliveredFromOrigin, []*big.Int) (event.Subscription, error)) *ArbitrumInboxInterface_WatchInboxMessageDeliveredFromOrigin_Call {
	_c.Call.Return(run)
	return _c
}

// NewArbitrumInboxInterface creates a new instance of ArbitrumInboxInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewArbitrumInboxInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *ArbitrumInboxInterface {
	mock := &ArbitrumInboxInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
