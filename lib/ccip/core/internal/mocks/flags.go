// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	bind "github.com/ethereum/go-ethereum/accounts/abi/bind"
	common "github.com/ethereum/go-ethereum/common"

	event "github.com/ethereum/go-ethereum/event"

	flags_wrapper "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated/flags_wrapper"

	generated "github.com/smartcontractkit/chainlink/v2/core/gethwrappers/generated"

	mock "github.com/stretchr/testify/mock"

	types "github.com/ethereum/go-ethereum/core/types"
)

// Flags is an autogenerated mock type for the FlagsInterface type
type Flags struct {
	mock.Mock
}

type Flags_Expecter struct {
	mock *mock.Mock
}

func (_m *Flags) EXPECT() *Flags_Expecter {
	return &Flags_Expecter{mock: &_m.Mock}
}

// AcceptOwnership provides a mock function with given fields: opts
func (_m *Flags) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for AcceptOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_AcceptOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AcceptOwnership'
type Flags_AcceptOwnership_Call struct {
	*mock.Call
}

// AcceptOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *Flags_Expecter) AcceptOwnership(opts interface{}) *Flags_AcceptOwnership_Call {
	return &Flags_AcceptOwnership_Call{Call: _e.mock.On("AcceptOwnership", opts)}
}

func (_c *Flags_AcceptOwnership_Call) Run(run func(opts *bind.TransactOpts)) *Flags_AcceptOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *Flags_AcceptOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_AcceptOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_AcceptOwnership_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *Flags_AcceptOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// AddAccess provides a mock function with given fields: opts, _user
func (_m *Flags) AddAccess(opts *bind.TransactOpts, _user common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, _user)

	if len(ret) == 0 {
		panic("no return value specified for AddAccess")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, _user)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, _user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, _user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_AddAccess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddAccess'
type Flags_AddAccess_Call struct {
	*mock.Call
}

// AddAccess is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _user common.Address
func (_e *Flags_Expecter) AddAccess(opts interface{}, _user interface{}) *Flags_AddAccess_Call {
	return &Flags_AddAccess_Call{Call: _e.mock.On("AddAccess", opts, _user)}
}

func (_c *Flags_AddAccess_Call) Run(run func(opts *bind.TransactOpts, _user common.Address)) *Flags_AddAccess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *Flags_AddAccess_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_AddAccess_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_AddAccess_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *Flags_AddAccess_Call {
	_c.Call.Return(run)
	return _c
}

// Address provides a mock function with given fields:
func (_m *Flags) Address() common.Address {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Address")
	}

	var r0 common.Address
	if rf, ok := ret.Get(0).(func() common.Address); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	return r0
}

// Flags_Address_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Address'
type Flags_Address_Call struct {
	*mock.Call
}

// Address is a helper method to define mock.On call
func (_e *Flags_Expecter) Address() *Flags_Address_Call {
	return &Flags_Address_Call{Call: _e.mock.On("Address")}
}

func (_c *Flags_Address_Call) Run(run func()) *Flags_Address_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Flags_Address_Call) Return(_a0 common.Address) *Flags_Address_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Flags_Address_Call) RunAndReturn(run func() common.Address) *Flags_Address_Call {
	_c.Call.Return(run)
	return _c
}

// CheckEnabled provides a mock function with given fields: opts
func (_m *Flags) CheckEnabled(opts *bind.CallOpts) (bool, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for CheckEnabled")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (bool, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) bool); ok {
		r0 = rf(opts)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_CheckEnabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckEnabled'
type Flags_CheckEnabled_Call struct {
	*mock.Call
}

// CheckEnabled is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *Flags_Expecter) CheckEnabled(opts interface{}) *Flags_CheckEnabled_Call {
	return &Flags_CheckEnabled_Call{Call: _e.mock.On("CheckEnabled", opts)}
}

func (_c *Flags_CheckEnabled_Call) Run(run func(opts *bind.CallOpts)) *Flags_CheckEnabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *Flags_CheckEnabled_Call) Return(_a0 bool, _a1 error) *Flags_CheckEnabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_CheckEnabled_Call) RunAndReturn(run func(*bind.CallOpts) (bool, error)) *Flags_CheckEnabled_Call {
	_c.Call.Return(run)
	return _c
}

// DisableAccessCheck provides a mock function with given fields: opts
func (_m *Flags) DisableAccessCheck(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for DisableAccessCheck")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_DisableAccessCheck_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DisableAccessCheck'
type Flags_DisableAccessCheck_Call struct {
	*mock.Call
}

// DisableAccessCheck is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *Flags_Expecter) DisableAccessCheck(opts interface{}) *Flags_DisableAccessCheck_Call {
	return &Flags_DisableAccessCheck_Call{Call: _e.mock.On("DisableAccessCheck", opts)}
}

func (_c *Flags_DisableAccessCheck_Call) Run(run func(opts *bind.TransactOpts)) *Flags_DisableAccessCheck_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *Flags_DisableAccessCheck_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_DisableAccessCheck_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_DisableAccessCheck_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *Flags_DisableAccessCheck_Call {
	_c.Call.Return(run)
	return _c
}

// EnableAccessCheck provides a mock function with given fields: opts
func (_m *Flags) EnableAccessCheck(opts *bind.TransactOpts) (*types.Transaction, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for EnableAccessCheck")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) (*types.Transaction, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts) *types.Transaction); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_EnableAccessCheck_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EnableAccessCheck'
type Flags_EnableAccessCheck_Call struct {
	*mock.Call
}

// EnableAccessCheck is a helper method to define mock.On call
//   - opts *bind.TransactOpts
func (_e *Flags_Expecter) EnableAccessCheck(opts interface{}) *Flags_EnableAccessCheck_Call {
	return &Flags_EnableAccessCheck_Call{Call: _e.mock.On("EnableAccessCheck", opts)}
}

func (_c *Flags_EnableAccessCheck_Call) Run(run func(opts *bind.TransactOpts)) *Flags_EnableAccessCheck_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts))
	})
	return _c
}

func (_c *Flags_EnableAccessCheck_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_EnableAccessCheck_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_EnableAccessCheck_Call) RunAndReturn(run func(*bind.TransactOpts) (*types.Transaction, error)) *Flags_EnableAccessCheck_Call {
	_c.Call.Return(run)
	return _c
}

// FilterAddedAccess provides a mock function with given fields: opts
func (_m *Flags) FilterAddedAccess(opts *bind.FilterOpts) (*flags_wrapper.FlagsAddedAccessIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterAddedAccess")
	}

	var r0 *flags_wrapper.FlagsAddedAccessIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*flags_wrapper.FlagsAddedAccessIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *flags_wrapper.FlagsAddedAccessIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsAddedAccessIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_FilterAddedAccess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterAddedAccess'
type Flags_FilterAddedAccess_Call struct {
	*mock.Call
}

// FilterAddedAccess is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *Flags_Expecter) FilterAddedAccess(opts interface{}) *Flags_FilterAddedAccess_Call {
	return &Flags_FilterAddedAccess_Call{Call: _e.mock.On("FilterAddedAccess", opts)}
}

func (_c *Flags_FilterAddedAccess_Call) Run(run func(opts *bind.FilterOpts)) *Flags_FilterAddedAccess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *Flags_FilterAddedAccess_Call) Return(_a0 *flags_wrapper.FlagsAddedAccessIterator, _a1 error) *Flags_FilterAddedAccess_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_FilterAddedAccess_Call) RunAndReturn(run func(*bind.FilterOpts) (*flags_wrapper.FlagsAddedAccessIterator, error)) *Flags_FilterAddedAccess_Call {
	_c.Call.Return(run)
	return _c
}

// FilterCheckAccessDisabled provides a mock function with given fields: opts
func (_m *Flags) FilterCheckAccessDisabled(opts *bind.FilterOpts) (*flags_wrapper.FlagsCheckAccessDisabledIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterCheckAccessDisabled")
	}

	var r0 *flags_wrapper.FlagsCheckAccessDisabledIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*flags_wrapper.FlagsCheckAccessDisabledIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *flags_wrapper.FlagsCheckAccessDisabledIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsCheckAccessDisabledIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_FilterCheckAccessDisabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterCheckAccessDisabled'
type Flags_FilterCheckAccessDisabled_Call struct {
	*mock.Call
}

// FilterCheckAccessDisabled is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *Flags_Expecter) FilterCheckAccessDisabled(opts interface{}) *Flags_FilterCheckAccessDisabled_Call {
	return &Flags_FilterCheckAccessDisabled_Call{Call: _e.mock.On("FilterCheckAccessDisabled", opts)}
}

func (_c *Flags_FilterCheckAccessDisabled_Call) Run(run func(opts *bind.FilterOpts)) *Flags_FilterCheckAccessDisabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *Flags_FilterCheckAccessDisabled_Call) Return(_a0 *flags_wrapper.FlagsCheckAccessDisabledIterator, _a1 error) *Flags_FilterCheckAccessDisabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_FilterCheckAccessDisabled_Call) RunAndReturn(run func(*bind.FilterOpts) (*flags_wrapper.FlagsCheckAccessDisabledIterator, error)) *Flags_FilterCheckAccessDisabled_Call {
	_c.Call.Return(run)
	return _c
}

// FilterCheckAccessEnabled provides a mock function with given fields: opts
func (_m *Flags) FilterCheckAccessEnabled(opts *bind.FilterOpts) (*flags_wrapper.FlagsCheckAccessEnabledIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterCheckAccessEnabled")
	}

	var r0 *flags_wrapper.FlagsCheckAccessEnabledIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*flags_wrapper.FlagsCheckAccessEnabledIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *flags_wrapper.FlagsCheckAccessEnabledIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsCheckAccessEnabledIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_FilterCheckAccessEnabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterCheckAccessEnabled'
type Flags_FilterCheckAccessEnabled_Call struct {
	*mock.Call
}

// FilterCheckAccessEnabled is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *Flags_Expecter) FilterCheckAccessEnabled(opts interface{}) *Flags_FilterCheckAccessEnabled_Call {
	return &Flags_FilterCheckAccessEnabled_Call{Call: _e.mock.On("FilterCheckAccessEnabled", opts)}
}

func (_c *Flags_FilterCheckAccessEnabled_Call) Run(run func(opts *bind.FilterOpts)) *Flags_FilterCheckAccessEnabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *Flags_FilterCheckAccessEnabled_Call) Return(_a0 *flags_wrapper.FlagsCheckAccessEnabledIterator, _a1 error) *Flags_FilterCheckAccessEnabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_FilterCheckAccessEnabled_Call) RunAndReturn(run func(*bind.FilterOpts) (*flags_wrapper.FlagsCheckAccessEnabledIterator, error)) *Flags_FilterCheckAccessEnabled_Call {
	_c.Call.Return(run)
	return _c
}

// FilterFlagLowered provides a mock function with given fields: opts, subject
func (_m *Flags) FilterFlagLowered(opts *bind.FilterOpts, subject []common.Address) (*flags_wrapper.FlagsFlagLoweredIterator, error) {
	ret := _m.Called(opts, subject)

	if len(ret) == 0 {
		panic("no return value specified for FilterFlagLowered")
	}

	var r0 *flags_wrapper.FlagsFlagLoweredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*flags_wrapper.FlagsFlagLoweredIterator, error)); ok {
		return rf(opts, subject)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *flags_wrapper.FlagsFlagLoweredIterator); ok {
		r0 = rf(opts, subject)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsFlagLoweredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, subject)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_FilterFlagLowered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterFlagLowered'
type Flags_FilterFlagLowered_Call struct {
	*mock.Call
}

// FilterFlagLowered is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - subject []common.Address
func (_e *Flags_Expecter) FilterFlagLowered(opts interface{}, subject interface{}) *Flags_FilterFlagLowered_Call {
	return &Flags_FilterFlagLowered_Call{Call: _e.mock.On("FilterFlagLowered", opts, subject)}
}

func (_c *Flags_FilterFlagLowered_Call) Run(run func(opts *bind.FilterOpts, subject []common.Address)) *Flags_FilterFlagLowered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *Flags_FilterFlagLowered_Call) Return(_a0 *flags_wrapper.FlagsFlagLoweredIterator, _a1 error) *Flags_FilterFlagLowered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_FilterFlagLowered_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*flags_wrapper.FlagsFlagLoweredIterator, error)) *Flags_FilterFlagLowered_Call {
	_c.Call.Return(run)
	return _c
}

// FilterFlagRaised provides a mock function with given fields: opts, subject
func (_m *Flags) FilterFlagRaised(opts *bind.FilterOpts, subject []common.Address) (*flags_wrapper.FlagsFlagRaisedIterator, error) {
	ret := _m.Called(opts, subject)

	if len(ret) == 0 {
		panic("no return value specified for FilterFlagRaised")
	}

	var r0 *flags_wrapper.FlagsFlagRaisedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) (*flags_wrapper.FlagsFlagRaisedIterator, error)); ok {
		return rf(opts, subject)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address) *flags_wrapper.FlagsFlagRaisedIterator); ok {
		r0 = rf(opts, subject)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsFlagRaisedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address) error); ok {
		r1 = rf(opts, subject)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_FilterFlagRaised_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterFlagRaised'
type Flags_FilterFlagRaised_Call struct {
	*mock.Call
}

// FilterFlagRaised is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - subject []common.Address
func (_e *Flags_Expecter) FilterFlagRaised(opts interface{}, subject interface{}) *Flags_FilterFlagRaised_Call {
	return &Flags_FilterFlagRaised_Call{Call: _e.mock.On("FilterFlagRaised", opts, subject)}
}

func (_c *Flags_FilterFlagRaised_Call) Run(run func(opts *bind.FilterOpts, subject []common.Address)) *Flags_FilterFlagRaised_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *Flags_FilterFlagRaised_Call) Return(_a0 *flags_wrapper.FlagsFlagRaisedIterator, _a1 error) *Flags_FilterFlagRaised_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_FilterFlagRaised_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address) (*flags_wrapper.FlagsFlagRaisedIterator, error)) *Flags_FilterFlagRaised_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferRequested provides a mock function with given fields: opts, from, to
func (_m *Flags) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*flags_wrapper.FlagsOwnershipTransferRequestedIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferRequested")
	}

	var r0 *flags_wrapper.FlagsOwnershipTransferRequestedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*flags_wrapper.FlagsOwnershipTransferRequestedIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *flags_wrapper.FlagsOwnershipTransferRequestedIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsOwnershipTransferRequestedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_FilterOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferRequested'
type Flags_FilterOwnershipTransferRequested_Call struct {
	*mock.Call
}

// FilterOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *Flags_Expecter) FilterOwnershipTransferRequested(opts interface{}, from interface{}, to interface{}) *Flags_FilterOwnershipTransferRequested_Call {
	return &Flags_FilterOwnershipTransferRequested_Call{Call: _e.mock.On("FilterOwnershipTransferRequested", opts, from, to)}
}

func (_c *Flags_FilterOwnershipTransferRequested_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *Flags_FilterOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *Flags_FilterOwnershipTransferRequested_Call) Return(_a0 *flags_wrapper.FlagsOwnershipTransferRequestedIterator, _a1 error) *Flags_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_FilterOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*flags_wrapper.FlagsOwnershipTransferRequestedIterator, error)) *Flags_FilterOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// FilterOwnershipTransferred provides a mock function with given fields: opts, from, to
func (_m *Flags) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*flags_wrapper.FlagsOwnershipTransferredIterator, error) {
	ret := _m.Called(opts, from, to)

	if len(ret) == 0 {
		panic("no return value specified for FilterOwnershipTransferred")
	}

	var r0 *flags_wrapper.FlagsOwnershipTransferredIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*flags_wrapper.FlagsOwnershipTransferredIterator, error)); ok {
		return rf(opts, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *flags_wrapper.FlagsOwnershipTransferredIterator); ok {
		r0 = rf(opts, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsOwnershipTransferredIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_FilterOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterOwnershipTransferred'
type Flags_FilterOwnershipTransferred_Call struct {
	*mock.Call
}

// FilterOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - from []common.Address
//   - to []common.Address
func (_e *Flags_Expecter) FilterOwnershipTransferred(opts interface{}, from interface{}, to interface{}) *Flags_FilterOwnershipTransferred_Call {
	return &Flags_FilterOwnershipTransferred_Call{Call: _e.mock.On("FilterOwnershipTransferred", opts, from, to)}
}

func (_c *Flags_FilterOwnershipTransferred_Call) Run(run func(opts *bind.FilterOpts, from []common.Address, to []common.Address)) *Flags_FilterOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *Flags_FilterOwnershipTransferred_Call) Return(_a0 *flags_wrapper.FlagsOwnershipTransferredIterator, _a1 error) *Flags_FilterOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_FilterOwnershipTransferred_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*flags_wrapper.FlagsOwnershipTransferredIterator, error)) *Flags_FilterOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// FilterRaisingAccessControllerUpdated provides a mock function with given fields: opts, previous, current
func (_m *Flags) FilterRaisingAccessControllerUpdated(opts *bind.FilterOpts, previous []common.Address, current []common.Address) (*flags_wrapper.FlagsRaisingAccessControllerUpdatedIterator, error) {
	ret := _m.Called(opts, previous, current)

	if len(ret) == 0 {
		panic("no return value specified for FilterRaisingAccessControllerUpdated")
	}

	var r0 *flags_wrapper.FlagsRaisingAccessControllerUpdatedIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) (*flags_wrapper.FlagsRaisingAccessControllerUpdatedIterator, error)); ok {
		return rf(opts, previous, current)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts, []common.Address, []common.Address) *flags_wrapper.FlagsRaisingAccessControllerUpdatedIterator); ok {
		r0 = rf(opts, previous, current)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsRaisingAccessControllerUpdatedIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, previous, current)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_FilterRaisingAccessControllerUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterRaisingAccessControllerUpdated'
type Flags_FilterRaisingAccessControllerUpdated_Call struct {
	*mock.Call
}

// FilterRaisingAccessControllerUpdated is a helper method to define mock.On call
//   - opts *bind.FilterOpts
//   - previous []common.Address
//   - current []common.Address
func (_e *Flags_Expecter) FilterRaisingAccessControllerUpdated(opts interface{}, previous interface{}, current interface{}) *Flags_FilterRaisingAccessControllerUpdated_Call {
	return &Flags_FilterRaisingAccessControllerUpdated_Call{Call: _e.mock.On("FilterRaisingAccessControllerUpdated", opts, previous, current)}
}

func (_c *Flags_FilterRaisingAccessControllerUpdated_Call) Run(run func(opts *bind.FilterOpts, previous []common.Address, current []common.Address)) *Flags_FilterRaisingAccessControllerUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts), args[1].([]common.Address), args[2].([]common.Address))
	})
	return _c
}

func (_c *Flags_FilterRaisingAccessControllerUpdated_Call) Return(_a0 *flags_wrapper.FlagsRaisingAccessControllerUpdatedIterator, _a1 error) *Flags_FilterRaisingAccessControllerUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_FilterRaisingAccessControllerUpdated_Call) RunAndReturn(run func(*bind.FilterOpts, []common.Address, []common.Address) (*flags_wrapper.FlagsRaisingAccessControllerUpdatedIterator, error)) *Flags_FilterRaisingAccessControllerUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// FilterRemovedAccess provides a mock function with given fields: opts
func (_m *Flags) FilterRemovedAccess(opts *bind.FilterOpts) (*flags_wrapper.FlagsRemovedAccessIterator, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for FilterRemovedAccess")
	}

	var r0 *flags_wrapper.FlagsRemovedAccessIterator
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) (*flags_wrapper.FlagsRemovedAccessIterator, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.FilterOpts) *flags_wrapper.FlagsRemovedAccessIterator); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsRemovedAccessIterator)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.FilterOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_FilterRemovedAccess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterRemovedAccess'
type Flags_FilterRemovedAccess_Call struct {
	*mock.Call
}

// FilterRemovedAccess is a helper method to define mock.On call
//   - opts *bind.FilterOpts
func (_e *Flags_Expecter) FilterRemovedAccess(opts interface{}) *Flags_FilterRemovedAccess_Call {
	return &Flags_FilterRemovedAccess_Call{Call: _e.mock.On("FilterRemovedAccess", opts)}
}

func (_c *Flags_FilterRemovedAccess_Call) Run(run func(opts *bind.FilterOpts)) *Flags_FilterRemovedAccess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.FilterOpts))
	})
	return _c
}

func (_c *Flags_FilterRemovedAccess_Call) Return(_a0 *flags_wrapper.FlagsRemovedAccessIterator, _a1 error) *Flags_FilterRemovedAccess_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_FilterRemovedAccess_Call) RunAndReturn(run func(*bind.FilterOpts) (*flags_wrapper.FlagsRemovedAccessIterator, error)) *Flags_FilterRemovedAccess_Call {
	_c.Call.Return(run)
	return _c
}

// GetFlag provides a mock function with given fields: opts, subject
func (_m *Flags) GetFlag(opts *bind.CallOpts, subject common.Address) (bool, error) {
	ret := _m.Called(opts, subject)

	if len(ret) == 0 {
		panic("no return value specified for GetFlag")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) (bool, error)); ok {
		return rf(opts, subject)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address) bool); ok {
		r0 = rf(opts, subject)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address) error); ok {
		r1 = rf(opts, subject)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_GetFlag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFlag'
type Flags_GetFlag_Call struct {
	*mock.Call
}

// GetFlag is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - subject common.Address
func (_e *Flags_Expecter) GetFlag(opts interface{}, subject interface{}) *Flags_GetFlag_Call {
	return &Flags_GetFlag_Call{Call: _e.mock.On("GetFlag", opts, subject)}
}

func (_c *Flags_GetFlag_Call) Run(run func(opts *bind.CallOpts, subject common.Address)) *Flags_GetFlag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *Flags_GetFlag_Call) Return(_a0 bool, _a1 error) *Flags_GetFlag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_GetFlag_Call) RunAndReturn(run func(*bind.CallOpts, common.Address) (bool, error)) *Flags_GetFlag_Call {
	_c.Call.Return(run)
	return _c
}

// GetFlags provides a mock function with given fields: opts, subjects
func (_m *Flags) GetFlags(opts *bind.CallOpts, subjects []common.Address) ([]bool, error) {
	ret := _m.Called(opts, subjects)

	if len(ret) == 0 {
		panic("no return value specified for GetFlags")
	}

	var r0 []bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, []common.Address) ([]bool, error)); ok {
		return rf(opts, subjects)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, []common.Address) []bool); ok {
		r0 = rf(opts, subjects)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]bool)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, []common.Address) error); ok {
		r1 = rf(opts, subjects)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_GetFlags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFlags'
type Flags_GetFlags_Call struct {
	*mock.Call
}

// GetFlags is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - subjects []common.Address
func (_e *Flags_Expecter) GetFlags(opts interface{}, subjects interface{}) *Flags_GetFlags_Call {
	return &Flags_GetFlags_Call{Call: _e.mock.On("GetFlags", opts, subjects)}
}

func (_c *Flags_GetFlags_Call) Run(run func(opts *bind.CallOpts, subjects []common.Address)) *Flags_GetFlags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *Flags_GetFlags_Call) Return(_a0 []bool, _a1 error) *Flags_GetFlags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_GetFlags_Call) RunAndReturn(run func(*bind.CallOpts, []common.Address) ([]bool, error)) *Flags_GetFlags_Call {
	_c.Call.Return(run)
	return _c
}

// HasAccess provides a mock function with given fields: opts, _user, _calldata
func (_m *Flags) HasAccess(opts *bind.CallOpts, _user common.Address, _calldata []byte) (bool, error) {
	ret := _m.Called(opts, _user, _calldata)

	if len(ret) == 0 {
		panic("no return value specified for HasAccess")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, []byte) (bool, error)); ok {
		return rf(opts, _user, _calldata)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts, common.Address, []byte) bool); ok {
		r0 = rf(opts, _user, _calldata)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts, common.Address, []byte) error); ok {
		r1 = rf(opts, _user, _calldata)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_HasAccess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HasAccess'
type Flags_HasAccess_Call struct {
	*mock.Call
}

// HasAccess is a helper method to define mock.On call
//   - opts *bind.CallOpts
//   - _user common.Address
//   - _calldata []byte
func (_e *Flags_Expecter) HasAccess(opts interface{}, _user interface{}, _calldata interface{}) *Flags_HasAccess_Call {
	return &Flags_HasAccess_Call{Call: _e.mock.On("HasAccess", opts, _user, _calldata)}
}

func (_c *Flags_HasAccess_Call) Run(run func(opts *bind.CallOpts, _user common.Address, _calldata []byte)) *Flags_HasAccess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts), args[1].(common.Address), args[2].([]byte))
	})
	return _c
}

func (_c *Flags_HasAccess_Call) Return(_a0 bool, _a1 error) *Flags_HasAccess_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_HasAccess_Call) RunAndReturn(run func(*bind.CallOpts, common.Address, []byte) (bool, error)) *Flags_HasAccess_Call {
	_c.Call.Return(run)
	return _c
}

// LowerFlags provides a mock function with given fields: opts, subjects
func (_m *Flags) LowerFlags(opts *bind.TransactOpts, subjects []common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, subjects)

	if len(ret) == 0 {
		panic("no return value specified for LowerFlags")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address) (*types.Transaction, error)); ok {
		return rf(opts, subjects)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address) *types.Transaction); ok {
		r0 = rf(opts, subjects)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []common.Address) error); ok {
		r1 = rf(opts, subjects)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_LowerFlags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LowerFlags'
type Flags_LowerFlags_Call struct {
	*mock.Call
}

// LowerFlags is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - subjects []common.Address
func (_e *Flags_Expecter) LowerFlags(opts interface{}, subjects interface{}) *Flags_LowerFlags_Call {
	return &Flags_LowerFlags_Call{Call: _e.mock.On("LowerFlags", opts, subjects)}
}

func (_c *Flags_LowerFlags_Call) Run(run func(opts *bind.TransactOpts, subjects []common.Address)) *Flags_LowerFlags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *Flags_LowerFlags_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_LowerFlags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_LowerFlags_Call) RunAndReturn(run func(*bind.TransactOpts, []common.Address) (*types.Transaction, error)) *Flags_LowerFlags_Call {
	_c.Call.Return(run)
	return _c
}

// Owner provides a mock function with given fields: opts
func (_m *Flags) Owner(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for Owner")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_Owner_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Owner'
type Flags_Owner_Call struct {
	*mock.Call
}

// Owner is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *Flags_Expecter) Owner(opts interface{}) *Flags_Owner_Call {
	return &Flags_Owner_Call{Call: _e.mock.On("Owner", opts)}
}

func (_c *Flags_Owner_Call) Run(run func(opts *bind.CallOpts)) *Flags_Owner_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *Flags_Owner_Call) Return(_a0 common.Address, _a1 error) *Flags_Owner_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_Owner_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *Flags_Owner_Call {
	_c.Call.Return(run)
	return _c
}

// ParseAddedAccess provides a mock function with given fields: log
func (_m *Flags) ParseAddedAccess(log types.Log) (*flags_wrapper.FlagsAddedAccess, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseAddedAccess")
	}

	var r0 *flags_wrapper.FlagsAddedAccess
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*flags_wrapper.FlagsAddedAccess, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *flags_wrapper.FlagsAddedAccess); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsAddedAccess)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseAddedAccess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseAddedAccess'
type Flags_ParseAddedAccess_Call struct {
	*mock.Call
}

// ParseAddedAccess is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseAddedAccess(log interface{}) *Flags_ParseAddedAccess_Call {
	return &Flags_ParseAddedAccess_Call{Call: _e.mock.On("ParseAddedAccess", log)}
}

func (_c *Flags_ParseAddedAccess_Call) Run(run func(log types.Log)) *Flags_ParseAddedAccess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseAddedAccess_Call) Return(_a0 *flags_wrapper.FlagsAddedAccess, _a1 error) *Flags_ParseAddedAccess_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseAddedAccess_Call) RunAndReturn(run func(types.Log) (*flags_wrapper.FlagsAddedAccess, error)) *Flags_ParseAddedAccess_Call {
	_c.Call.Return(run)
	return _c
}

// ParseCheckAccessDisabled provides a mock function with given fields: log
func (_m *Flags) ParseCheckAccessDisabled(log types.Log) (*flags_wrapper.FlagsCheckAccessDisabled, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseCheckAccessDisabled")
	}

	var r0 *flags_wrapper.FlagsCheckAccessDisabled
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*flags_wrapper.FlagsCheckAccessDisabled, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *flags_wrapper.FlagsCheckAccessDisabled); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsCheckAccessDisabled)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseCheckAccessDisabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseCheckAccessDisabled'
type Flags_ParseCheckAccessDisabled_Call struct {
	*mock.Call
}

// ParseCheckAccessDisabled is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseCheckAccessDisabled(log interface{}) *Flags_ParseCheckAccessDisabled_Call {
	return &Flags_ParseCheckAccessDisabled_Call{Call: _e.mock.On("ParseCheckAccessDisabled", log)}
}

func (_c *Flags_ParseCheckAccessDisabled_Call) Run(run func(log types.Log)) *Flags_ParseCheckAccessDisabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseCheckAccessDisabled_Call) Return(_a0 *flags_wrapper.FlagsCheckAccessDisabled, _a1 error) *Flags_ParseCheckAccessDisabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseCheckAccessDisabled_Call) RunAndReturn(run func(types.Log) (*flags_wrapper.FlagsCheckAccessDisabled, error)) *Flags_ParseCheckAccessDisabled_Call {
	_c.Call.Return(run)
	return _c
}

// ParseCheckAccessEnabled provides a mock function with given fields: log
func (_m *Flags) ParseCheckAccessEnabled(log types.Log) (*flags_wrapper.FlagsCheckAccessEnabled, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseCheckAccessEnabled")
	}

	var r0 *flags_wrapper.FlagsCheckAccessEnabled
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*flags_wrapper.FlagsCheckAccessEnabled, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *flags_wrapper.FlagsCheckAccessEnabled); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsCheckAccessEnabled)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseCheckAccessEnabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseCheckAccessEnabled'
type Flags_ParseCheckAccessEnabled_Call struct {
	*mock.Call
}

// ParseCheckAccessEnabled is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseCheckAccessEnabled(log interface{}) *Flags_ParseCheckAccessEnabled_Call {
	return &Flags_ParseCheckAccessEnabled_Call{Call: _e.mock.On("ParseCheckAccessEnabled", log)}
}

func (_c *Flags_ParseCheckAccessEnabled_Call) Run(run func(log types.Log)) *Flags_ParseCheckAccessEnabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseCheckAccessEnabled_Call) Return(_a0 *flags_wrapper.FlagsCheckAccessEnabled, _a1 error) *Flags_ParseCheckAccessEnabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseCheckAccessEnabled_Call) RunAndReturn(run func(types.Log) (*flags_wrapper.FlagsCheckAccessEnabled, error)) *Flags_ParseCheckAccessEnabled_Call {
	_c.Call.Return(run)
	return _c
}

// ParseFlagLowered provides a mock function with given fields: log
func (_m *Flags) ParseFlagLowered(log types.Log) (*flags_wrapper.FlagsFlagLowered, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseFlagLowered")
	}

	var r0 *flags_wrapper.FlagsFlagLowered
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*flags_wrapper.FlagsFlagLowered, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *flags_wrapper.FlagsFlagLowered); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsFlagLowered)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseFlagLowered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseFlagLowered'
type Flags_ParseFlagLowered_Call struct {
	*mock.Call
}

// ParseFlagLowered is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseFlagLowered(log interface{}) *Flags_ParseFlagLowered_Call {
	return &Flags_ParseFlagLowered_Call{Call: _e.mock.On("ParseFlagLowered", log)}
}

func (_c *Flags_ParseFlagLowered_Call) Run(run func(log types.Log)) *Flags_ParseFlagLowered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseFlagLowered_Call) Return(_a0 *flags_wrapper.FlagsFlagLowered, _a1 error) *Flags_ParseFlagLowered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseFlagLowered_Call) RunAndReturn(run func(types.Log) (*flags_wrapper.FlagsFlagLowered, error)) *Flags_ParseFlagLowered_Call {
	_c.Call.Return(run)
	return _c
}

// ParseFlagRaised provides a mock function with given fields: log
func (_m *Flags) ParseFlagRaised(log types.Log) (*flags_wrapper.FlagsFlagRaised, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseFlagRaised")
	}

	var r0 *flags_wrapper.FlagsFlagRaised
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*flags_wrapper.FlagsFlagRaised, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *flags_wrapper.FlagsFlagRaised); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsFlagRaised)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseFlagRaised_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseFlagRaised'
type Flags_ParseFlagRaised_Call struct {
	*mock.Call
}

// ParseFlagRaised is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseFlagRaised(log interface{}) *Flags_ParseFlagRaised_Call {
	return &Flags_ParseFlagRaised_Call{Call: _e.mock.On("ParseFlagRaised", log)}
}

func (_c *Flags_ParseFlagRaised_Call) Run(run func(log types.Log)) *Flags_ParseFlagRaised_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseFlagRaised_Call) Return(_a0 *flags_wrapper.FlagsFlagRaised, _a1 error) *Flags_ParseFlagRaised_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseFlagRaised_Call) RunAndReturn(run func(types.Log) (*flags_wrapper.FlagsFlagRaised, error)) *Flags_ParseFlagRaised_Call {
	_c.Call.Return(run)
	return _c
}

// ParseLog provides a mock function with given fields: log
func (_m *Flags) ParseLog(log types.Log) (generated.AbigenLog, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseLog")
	}

	var r0 generated.AbigenLog
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (generated.AbigenLog, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) generated.AbigenLog); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(generated.AbigenLog)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseLog_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseLog'
type Flags_ParseLog_Call struct {
	*mock.Call
}

// ParseLog is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseLog(log interface{}) *Flags_ParseLog_Call {
	return &Flags_ParseLog_Call{Call: _e.mock.On("ParseLog", log)}
}

func (_c *Flags_ParseLog_Call) Run(run func(log types.Log)) *Flags_ParseLog_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseLog_Call) Return(_a0 generated.AbigenLog, _a1 error) *Flags_ParseLog_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseLog_Call) RunAndReturn(run func(types.Log) (generated.AbigenLog, error)) *Flags_ParseLog_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferRequested provides a mock function with given fields: log
func (_m *Flags) ParseOwnershipTransferRequested(log types.Log) (*flags_wrapper.FlagsOwnershipTransferRequested, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferRequested")
	}

	var r0 *flags_wrapper.FlagsOwnershipTransferRequested
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*flags_wrapper.FlagsOwnershipTransferRequested, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *flags_wrapper.FlagsOwnershipTransferRequested); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsOwnershipTransferRequested)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferRequested'
type Flags_ParseOwnershipTransferRequested_Call struct {
	*mock.Call
}

// ParseOwnershipTransferRequested is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseOwnershipTransferRequested(log interface{}) *Flags_ParseOwnershipTransferRequested_Call {
	return &Flags_ParseOwnershipTransferRequested_Call{Call: _e.mock.On("ParseOwnershipTransferRequested", log)}
}

func (_c *Flags_ParseOwnershipTransferRequested_Call) Run(run func(log types.Log)) *Flags_ParseOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseOwnershipTransferRequested_Call) Return(_a0 *flags_wrapper.FlagsOwnershipTransferRequested, _a1 error) *Flags_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseOwnershipTransferRequested_Call) RunAndReturn(run func(types.Log) (*flags_wrapper.FlagsOwnershipTransferRequested, error)) *Flags_ParseOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// ParseOwnershipTransferred provides a mock function with given fields: log
func (_m *Flags) ParseOwnershipTransferred(log types.Log) (*flags_wrapper.FlagsOwnershipTransferred, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseOwnershipTransferred")
	}

	var r0 *flags_wrapper.FlagsOwnershipTransferred
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*flags_wrapper.FlagsOwnershipTransferred, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *flags_wrapper.FlagsOwnershipTransferred); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsOwnershipTransferred)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseOwnershipTransferred'
type Flags_ParseOwnershipTransferred_Call struct {
	*mock.Call
}

// ParseOwnershipTransferred is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseOwnershipTransferred(log interface{}) *Flags_ParseOwnershipTransferred_Call {
	return &Flags_ParseOwnershipTransferred_Call{Call: _e.mock.On("ParseOwnershipTransferred", log)}
}

func (_c *Flags_ParseOwnershipTransferred_Call) Run(run func(log types.Log)) *Flags_ParseOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseOwnershipTransferred_Call) Return(_a0 *flags_wrapper.FlagsOwnershipTransferred, _a1 error) *Flags_ParseOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseOwnershipTransferred_Call) RunAndReturn(run func(types.Log) (*flags_wrapper.FlagsOwnershipTransferred, error)) *Flags_ParseOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// ParseRaisingAccessControllerUpdated provides a mock function with given fields: log
func (_m *Flags) ParseRaisingAccessControllerUpdated(log types.Log) (*flags_wrapper.FlagsRaisingAccessControllerUpdated, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseRaisingAccessControllerUpdated")
	}

	var r0 *flags_wrapper.FlagsRaisingAccessControllerUpdated
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*flags_wrapper.FlagsRaisingAccessControllerUpdated, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *flags_wrapper.FlagsRaisingAccessControllerUpdated); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsRaisingAccessControllerUpdated)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseRaisingAccessControllerUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseRaisingAccessControllerUpdated'
type Flags_ParseRaisingAccessControllerUpdated_Call struct {
	*mock.Call
}

// ParseRaisingAccessControllerUpdated is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseRaisingAccessControllerUpdated(log interface{}) *Flags_ParseRaisingAccessControllerUpdated_Call {
	return &Flags_ParseRaisingAccessControllerUpdated_Call{Call: _e.mock.On("ParseRaisingAccessControllerUpdated", log)}
}

func (_c *Flags_ParseRaisingAccessControllerUpdated_Call) Run(run func(log types.Log)) *Flags_ParseRaisingAccessControllerUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseRaisingAccessControllerUpdated_Call) Return(_a0 *flags_wrapper.FlagsRaisingAccessControllerUpdated, _a1 error) *Flags_ParseRaisingAccessControllerUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseRaisingAccessControllerUpdated_Call) RunAndReturn(run func(types.Log) (*flags_wrapper.FlagsRaisingAccessControllerUpdated, error)) *Flags_ParseRaisingAccessControllerUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// ParseRemovedAccess provides a mock function with given fields: log
func (_m *Flags) ParseRemovedAccess(log types.Log) (*flags_wrapper.FlagsRemovedAccess, error) {
	ret := _m.Called(log)

	if len(ret) == 0 {
		panic("no return value specified for ParseRemovedAccess")
	}

	var r0 *flags_wrapper.FlagsRemovedAccess
	var r1 error
	if rf, ok := ret.Get(0).(func(types.Log) (*flags_wrapper.FlagsRemovedAccess, error)); ok {
		return rf(log)
	}
	if rf, ok := ret.Get(0).(func(types.Log) *flags_wrapper.FlagsRemovedAccess); ok {
		r0 = rf(log)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*flags_wrapper.FlagsRemovedAccess)
		}
	}

	if rf, ok := ret.Get(1).(func(types.Log) error); ok {
		r1 = rf(log)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_ParseRemovedAccess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseRemovedAccess'
type Flags_ParseRemovedAccess_Call struct {
	*mock.Call
}

// ParseRemovedAccess is a helper method to define mock.On call
//   - log types.Log
func (_e *Flags_Expecter) ParseRemovedAccess(log interface{}) *Flags_ParseRemovedAccess_Call {
	return &Flags_ParseRemovedAccess_Call{Call: _e.mock.On("ParseRemovedAccess", log)}
}

func (_c *Flags_ParseRemovedAccess_Call) Run(run func(log types.Log)) *Flags_ParseRemovedAccess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(types.Log))
	})
	return _c
}

func (_c *Flags_ParseRemovedAccess_Call) Return(_a0 *flags_wrapper.FlagsRemovedAccess, _a1 error) *Flags_ParseRemovedAccess_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_ParseRemovedAccess_Call) RunAndReturn(run func(types.Log) (*flags_wrapper.FlagsRemovedAccess, error)) *Flags_ParseRemovedAccess_Call {
	_c.Call.Return(run)
	return _c
}

// RaiseFlag provides a mock function with given fields: opts, subject
func (_m *Flags) RaiseFlag(opts *bind.TransactOpts, subject common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, subject)

	if len(ret) == 0 {
		panic("no return value specified for RaiseFlag")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, subject)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, subject)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, subject)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_RaiseFlag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RaiseFlag'
type Flags_RaiseFlag_Call struct {
	*mock.Call
}

// RaiseFlag is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - subject common.Address
func (_e *Flags_Expecter) RaiseFlag(opts interface{}, subject interface{}) *Flags_RaiseFlag_Call {
	return &Flags_RaiseFlag_Call{Call: _e.mock.On("RaiseFlag", opts, subject)}
}

func (_c *Flags_RaiseFlag_Call) Run(run func(opts *bind.TransactOpts, subject common.Address)) *Flags_RaiseFlag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *Flags_RaiseFlag_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_RaiseFlag_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_RaiseFlag_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *Flags_RaiseFlag_Call {
	_c.Call.Return(run)
	return _c
}

// RaiseFlags provides a mock function with given fields: opts, subjects
func (_m *Flags) RaiseFlags(opts *bind.TransactOpts, subjects []common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, subjects)

	if len(ret) == 0 {
		panic("no return value specified for RaiseFlags")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address) (*types.Transaction, error)); ok {
		return rf(opts, subjects)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, []common.Address) *types.Transaction); ok {
		r0 = rf(opts, subjects)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, []common.Address) error); ok {
		r1 = rf(opts, subjects)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_RaiseFlags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RaiseFlags'
type Flags_RaiseFlags_Call struct {
	*mock.Call
}

// RaiseFlags is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - subjects []common.Address
func (_e *Flags_Expecter) RaiseFlags(opts interface{}, subjects interface{}) *Flags_RaiseFlags_Call {
	return &Flags_RaiseFlags_Call{Call: _e.mock.On("RaiseFlags", opts, subjects)}
}

func (_c *Flags_RaiseFlags_Call) Run(run func(opts *bind.TransactOpts, subjects []common.Address)) *Flags_RaiseFlags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].([]common.Address))
	})
	return _c
}

func (_c *Flags_RaiseFlags_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_RaiseFlags_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_RaiseFlags_Call) RunAndReturn(run func(*bind.TransactOpts, []common.Address) (*types.Transaction, error)) *Flags_RaiseFlags_Call {
	_c.Call.Return(run)
	return _c
}

// RaisingAccessController provides a mock function with given fields: opts
func (_m *Flags) RaisingAccessController(opts *bind.CallOpts) (common.Address, error) {
	ret := _m.Called(opts)

	if len(ret) == 0 {
		panic("no return value specified for RaisingAccessController")
	}

	var r0 common.Address
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) (common.Address, error)); ok {
		return rf(opts)
	}
	if rf, ok := ret.Get(0).(func(*bind.CallOpts) common.Address); ok {
		r0 = rf(opts)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(common.Address)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.CallOpts) error); ok {
		r1 = rf(opts)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_RaisingAccessController_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RaisingAccessController'
type Flags_RaisingAccessController_Call struct {
	*mock.Call
}

// RaisingAccessController is a helper method to define mock.On call
//   - opts *bind.CallOpts
func (_e *Flags_Expecter) RaisingAccessController(opts interface{}) *Flags_RaisingAccessController_Call {
	return &Flags_RaisingAccessController_Call{Call: _e.mock.On("RaisingAccessController", opts)}
}

func (_c *Flags_RaisingAccessController_Call) Run(run func(opts *bind.CallOpts)) *Flags_RaisingAccessController_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.CallOpts))
	})
	return _c
}

func (_c *Flags_RaisingAccessController_Call) Return(_a0 common.Address, _a1 error) *Flags_RaisingAccessController_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_RaisingAccessController_Call) RunAndReturn(run func(*bind.CallOpts) (common.Address, error)) *Flags_RaisingAccessController_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveAccess provides a mock function with given fields: opts, _user
func (_m *Flags) RemoveAccess(opts *bind.TransactOpts, _user common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, _user)

	if len(ret) == 0 {
		panic("no return value specified for RemoveAccess")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, _user)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, _user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, _user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_RemoveAccess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveAccess'
type Flags_RemoveAccess_Call struct {
	*mock.Call
}

// RemoveAccess is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _user common.Address
func (_e *Flags_Expecter) RemoveAccess(opts interface{}, _user interface{}) *Flags_RemoveAccess_Call {
	return &Flags_RemoveAccess_Call{Call: _e.mock.On("RemoveAccess", opts, _user)}
}

func (_c *Flags_RemoveAccess_Call) Run(run func(opts *bind.TransactOpts, _user common.Address)) *Flags_RemoveAccess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *Flags_RemoveAccess_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_RemoveAccess_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_RemoveAccess_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *Flags_RemoveAccess_Call {
	_c.Call.Return(run)
	return _c
}

// SetRaisingAccessController provides a mock function with given fields: opts, racAddress
func (_m *Flags) SetRaisingAccessController(opts *bind.TransactOpts, racAddress common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, racAddress)

	if len(ret) == 0 {
		panic("no return value specified for SetRaisingAccessController")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, racAddress)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, racAddress)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, racAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_SetRaisingAccessController_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetRaisingAccessController'
type Flags_SetRaisingAccessController_Call struct {
	*mock.Call
}

// SetRaisingAccessController is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - racAddress common.Address
func (_e *Flags_Expecter) SetRaisingAccessController(opts interface{}, racAddress interface{}) *Flags_SetRaisingAccessController_Call {
	return &Flags_SetRaisingAccessController_Call{Call: _e.mock.On("SetRaisingAccessController", opts, racAddress)}
}

func (_c *Flags_SetRaisingAccessController_Call) Run(run func(opts *bind.TransactOpts, racAddress common.Address)) *Flags_SetRaisingAccessController_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *Flags_SetRaisingAccessController_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_SetRaisingAccessController_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_SetRaisingAccessController_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *Flags_SetRaisingAccessController_Call {
	_c.Call.Return(run)
	return _c
}

// TransferOwnership provides a mock function with given fields: opts, _to
func (_m *Flags) TransferOwnership(opts *bind.TransactOpts, _to common.Address) (*types.Transaction, error) {
	ret := _m.Called(opts, _to)

	if len(ret) == 0 {
		panic("no return value specified for TransferOwnership")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) (*types.Transaction, error)); ok {
		return rf(opts, _to)
	}
	if rf, ok := ret.Get(0).(func(*bind.TransactOpts, common.Address) *types.Transaction); ok {
		r0 = rf(opts, _to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.TransactOpts, common.Address) error); ok {
		r1 = rf(opts, _to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_TransferOwnership_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransferOwnership'
type Flags_TransferOwnership_Call struct {
	*mock.Call
}

// TransferOwnership is a helper method to define mock.On call
//   - opts *bind.TransactOpts
//   - _to common.Address
func (_e *Flags_Expecter) TransferOwnership(opts interface{}, _to interface{}) *Flags_TransferOwnership_Call {
	return &Flags_TransferOwnership_Call{Call: _e.mock.On("TransferOwnership", opts, _to)}
}

func (_c *Flags_TransferOwnership_Call) Run(run func(opts *bind.TransactOpts, _to common.Address)) *Flags_TransferOwnership_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.TransactOpts), args[1].(common.Address))
	})
	return _c
}

func (_c *Flags_TransferOwnership_Call) Return(_a0 *types.Transaction, _a1 error) *Flags_TransferOwnership_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_TransferOwnership_Call) RunAndReturn(run func(*bind.TransactOpts, common.Address) (*types.Transaction, error)) *Flags_TransferOwnership_Call {
	_c.Call.Return(run)
	return _c
}

// WatchAddedAccess provides a mock function with given fields: opts, sink
func (_m *Flags) WatchAddedAccess(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsAddedAccess) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchAddedAccess")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsAddedAccess) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsAddedAccess) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsAddedAccess) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_WatchAddedAccess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchAddedAccess'
type Flags_WatchAddedAccess_Call struct {
	*mock.Call
}

// WatchAddedAccess is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *flags_wrapper.FlagsAddedAccess
func (_e *Flags_Expecter) WatchAddedAccess(opts interface{}, sink interface{}) *Flags_WatchAddedAccess_Call {
	return &Flags_WatchAddedAccess_Call{Call: _e.mock.On("WatchAddedAccess", opts, sink)}
}

func (_c *Flags_WatchAddedAccess_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsAddedAccess)) *Flags_WatchAddedAccess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *flags_wrapper.FlagsAddedAccess))
	})
	return _c
}

func (_c *Flags_WatchAddedAccess_Call) Return(_a0 event.Subscription, _a1 error) *Flags_WatchAddedAccess_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_WatchAddedAccess_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsAddedAccess) (event.Subscription, error)) *Flags_WatchAddedAccess_Call {
	_c.Call.Return(run)
	return _c
}

// WatchCheckAccessDisabled provides a mock function with given fields: opts, sink
func (_m *Flags) WatchCheckAccessDisabled(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsCheckAccessDisabled) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchCheckAccessDisabled")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsCheckAccessDisabled) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsCheckAccessDisabled) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsCheckAccessDisabled) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_WatchCheckAccessDisabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchCheckAccessDisabled'
type Flags_WatchCheckAccessDisabled_Call struct {
	*mock.Call
}

// WatchCheckAccessDisabled is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *flags_wrapper.FlagsCheckAccessDisabled
func (_e *Flags_Expecter) WatchCheckAccessDisabled(opts interface{}, sink interface{}) *Flags_WatchCheckAccessDisabled_Call {
	return &Flags_WatchCheckAccessDisabled_Call{Call: _e.mock.On("WatchCheckAccessDisabled", opts, sink)}
}

func (_c *Flags_WatchCheckAccessDisabled_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsCheckAccessDisabled)) *Flags_WatchCheckAccessDisabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *flags_wrapper.FlagsCheckAccessDisabled))
	})
	return _c
}

func (_c *Flags_WatchCheckAccessDisabled_Call) Return(_a0 event.Subscription, _a1 error) *Flags_WatchCheckAccessDisabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_WatchCheckAccessDisabled_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsCheckAccessDisabled) (event.Subscription, error)) *Flags_WatchCheckAccessDisabled_Call {
	_c.Call.Return(run)
	return _c
}

// WatchCheckAccessEnabled provides a mock function with given fields: opts, sink
func (_m *Flags) WatchCheckAccessEnabled(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsCheckAccessEnabled) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchCheckAccessEnabled")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsCheckAccessEnabled) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsCheckAccessEnabled) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsCheckAccessEnabled) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_WatchCheckAccessEnabled_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchCheckAccessEnabled'
type Flags_WatchCheckAccessEnabled_Call struct {
	*mock.Call
}

// WatchCheckAccessEnabled is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *flags_wrapper.FlagsCheckAccessEnabled
func (_e *Flags_Expecter) WatchCheckAccessEnabled(opts interface{}, sink interface{}) *Flags_WatchCheckAccessEnabled_Call {
	return &Flags_WatchCheckAccessEnabled_Call{Call: _e.mock.On("WatchCheckAccessEnabled", opts, sink)}
}

func (_c *Flags_WatchCheckAccessEnabled_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsCheckAccessEnabled)) *Flags_WatchCheckAccessEnabled_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *flags_wrapper.FlagsCheckAccessEnabled))
	})
	return _c
}

func (_c *Flags_WatchCheckAccessEnabled_Call) Return(_a0 event.Subscription, _a1 error) *Flags_WatchCheckAccessEnabled_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_WatchCheckAccessEnabled_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsCheckAccessEnabled) (event.Subscription, error)) *Flags_WatchCheckAccessEnabled_Call {
	_c.Call.Return(run)
	return _c
}

// WatchFlagLowered provides a mock function with given fields: opts, sink, subject
func (_m *Flags) WatchFlagLowered(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsFlagLowered, subject []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, subject)

	if len(ret) == 0 {
		panic("no return value specified for WatchFlagLowered")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsFlagLowered, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, subject)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsFlagLowered, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, subject)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsFlagLowered, []common.Address) error); ok {
		r1 = rf(opts, sink, subject)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_WatchFlagLowered_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchFlagLowered'
type Flags_WatchFlagLowered_Call struct {
	*mock.Call
}

// WatchFlagLowered is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *flags_wrapper.FlagsFlagLowered
//   - subject []common.Address
func (_e *Flags_Expecter) WatchFlagLowered(opts interface{}, sink interface{}, subject interface{}) *Flags_WatchFlagLowered_Call {
	return &Flags_WatchFlagLowered_Call{Call: _e.mock.On("WatchFlagLowered", opts, sink, subject)}
}

func (_c *Flags_WatchFlagLowered_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsFlagLowered, subject []common.Address)) *Flags_WatchFlagLowered_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *flags_wrapper.FlagsFlagLowered), args[2].([]common.Address))
	})
	return _c
}

func (_c *Flags_WatchFlagLowered_Call) Return(_a0 event.Subscription, _a1 error) *Flags_WatchFlagLowered_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_WatchFlagLowered_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsFlagLowered, []common.Address) (event.Subscription, error)) *Flags_WatchFlagLowered_Call {
	_c.Call.Return(run)
	return _c
}

// WatchFlagRaised provides a mock function with given fields: opts, sink, subject
func (_m *Flags) WatchFlagRaised(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsFlagRaised, subject []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, subject)

	if len(ret) == 0 {
		panic("no return value specified for WatchFlagRaised")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsFlagRaised, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, subject)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsFlagRaised, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, subject)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsFlagRaised, []common.Address) error); ok {
		r1 = rf(opts, sink, subject)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_WatchFlagRaised_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchFlagRaised'
type Flags_WatchFlagRaised_Call struct {
	*mock.Call
}

// WatchFlagRaised is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *flags_wrapper.FlagsFlagRaised
//   - subject []common.Address
func (_e *Flags_Expecter) WatchFlagRaised(opts interface{}, sink interface{}, subject interface{}) *Flags_WatchFlagRaised_Call {
	return &Flags_WatchFlagRaised_Call{Call: _e.mock.On("WatchFlagRaised", opts, sink, subject)}
}

func (_c *Flags_WatchFlagRaised_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsFlagRaised, subject []common.Address)) *Flags_WatchFlagRaised_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *flags_wrapper.FlagsFlagRaised), args[2].([]common.Address))
	})
	return _c
}

func (_c *Flags_WatchFlagRaised_Call) Return(_a0 event.Subscription, _a1 error) *Flags_WatchFlagRaised_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_WatchFlagRaised_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsFlagRaised, []common.Address) (event.Subscription, error)) *Flags_WatchFlagRaised_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferRequested provides a mock function with given fields: opts, sink, from, to
func (_m *Flags) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferRequested")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsOwnershipTransferRequested, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsOwnershipTransferRequested, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_WatchOwnershipTransferRequested_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferRequested'
type Flags_WatchOwnershipTransferRequested_Call struct {
	*mock.Call
}

// WatchOwnershipTransferRequested is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *flags_wrapper.FlagsOwnershipTransferRequested
//   - from []common.Address
//   - to []common.Address
func (_e *Flags_Expecter) WatchOwnershipTransferRequested(opts interface{}, sink interface{}, from interface{}, to interface{}) *Flags_WatchOwnershipTransferRequested_Call {
	return &Flags_WatchOwnershipTransferRequested_Call{Call: _e.mock.On("WatchOwnershipTransferRequested", opts, sink, from, to)}
}

func (_c *Flags_WatchOwnershipTransferRequested_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsOwnershipTransferRequested, from []common.Address, to []common.Address)) *Flags_WatchOwnershipTransferRequested_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *flags_wrapper.FlagsOwnershipTransferRequested), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *Flags_WatchOwnershipTransferRequested_Call) Return(_a0 event.Subscription, _a1 error) *Flags_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_WatchOwnershipTransferRequested_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsOwnershipTransferRequested, []common.Address, []common.Address) (event.Subscription, error)) *Flags_WatchOwnershipTransferRequested_Call {
	_c.Call.Return(run)
	return _c
}

// WatchOwnershipTransferred provides a mock function with given fields: opts, sink, from, to
func (_m *Flags) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, from, to)

	if len(ret) == 0 {
		panic("no return value specified for WatchOwnershipTransferred")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, from, to)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsOwnershipTransferred, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsOwnershipTransferred, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_WatchOwnershipTransferred_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchOwnershipTransferred'
type Flags_WatchOwnershipTransferred_Call struct {
	*mock.Call
}

// WatchOwnershipTransferred is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *flags_wrapper.FlagsOwnershipTransferred
//   - from []common.Address
//   - to []common.Address
func (_e *Flags_Expecter) WatchOwnershipTransferred(opts interface{}, sink interface{}, from interface{}, to interface{}) *Flags_WatchOwnershipTransferred_Call {
	return &Flags_WatchOwnershipTransferred_Call{Call: _e.mock.On("WatchOwnershipTransferred", opts, sink, from, to)}
}

func (_c *Flags_WatchOwnershipTransferred_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsOwnershipTransferred, from []common.Address, to []common.Address)) *Flags_WatchOwnershipTransferred_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *flags_wrapper.FlagsOwnershipTransferred), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *Flags_WatchOwnershipTransferred_Call) Return(_a0 event.Subscription, _a1 error) *Flags_WatchOwnershipTransferred_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_WatchOwnershipTransferred_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsOwnershipTransferred, []common.Address, []common.Address) (event.Subscription, error)) *Flags_WatchOwnershipTransferred_Call {
	_c.Call.Return(run)
	return _c
}

// WatchRaisingAccessControllerUpdated provides a mock function with given fields: opts, sink, previous, current
func (_m *Flags) WatchRaisingAccessControllerUpdated(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsRaisingAccessControllerUpdated, previous []common.Address, current []common.Address) (event.Subscription, error) {
	ret := _m.Called(opts, sink, previous, current)

	if len(ret) == 0 {
		panic("no return value specified for WatchRaisingAccessControllerUpdated")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsRaisingAccessControllerUpdated, []common.Address, []common.Address) (event.Subscription, error)); ok {
		return rf(opts, sink, previous, current)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsRaisingAccessControllerUpdated, []common.Address, []common.Address) event.Subscription); ok {
		r0 = rf(opts, sink, previous, current)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsRaisingAccessControllerUpdated, []common.Address, []common.Address) error); ok {
		r1 = rf(opts, sink, previous, current)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_WatchRaisingAccessControllerUpdated_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchRaisingAccessControllerUpdated'
type Flags_WatchRaisingAccessControllerUpdated_Call struct {
	*mock.Call
}

// WatchRaisingAccessControllerUpdated is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *flags_wrapper.FlagsRaisingAccessControllerUpdated
//   - previous []common.Address
//   - current []common.Address
func (_e *Flags_Expecter) WatchRaisingAccessControllerUpdated(opts interface{}, sink interface{}, previous interface{}, current interface{}) *Flags_WatchRaisingAccessControllerUpdated_Call {
	return &Flags_WatchRaisingAccessControllerUpdated_Call{Call: _e.mock.On("WatchRaisingAccessControllerUpdated", opts, sink, previous, current)}
}

func (_c *Flags_WatchRaisingAccessControllerUpdated_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsRaisingAccessControllerUpdated, previous []common.Address, current []common.Address)) *Flags_WatchRaisingAccessControllerUpdated_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *flags_wrapper.FlagsRaisingAccessControllerUpdated), args[2].([]common.Address), args[3].([]common.Address))
	})
	return _c
}

func (_c *Flags_WatchRaisingAccessControllerUpdated_Call) Return(_a0 event.Subscription, _a1 error) *Flags_WatchRaisingAccessControllerUpdated_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_WatchRaisingAccessControllerUpdated_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsRaisingAccessControllerUpdated, []common.Address, []common.Address) (event.Subscription, error)) *Flags_WatchRaisingAccessControllerUpdated_Call {
	_c.Call.Return(run)
	return _c
}

// WatchRemovedAccess provides a mock function with given fields: opts, sink
func (_m *Flags) WatchRemovedAccess(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsRemovedAccess) (event.Subscription, error) {
	ret := _m.Called(opts, sink)

	if len(ret) == 0 {
		panic("no return value specified for WatchRemovedAccess")
	}

	var r0 event.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsRemovedAccess) (event.Subscription, error)); ok {
		return rf(opts, sink)
	}
	if rf, ok := ret.Get(0).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsRemovedAccess) event.Subscription); ok {
		r0 = rf(opts, sink)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(event.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsRemovedAccess) error); ok {
		r1 = rf(opts, sink)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Flags_WatchRemovedAccess_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WatchRemovedAccess'
type Flags_WatchRemovedAccess_Call struct {
	*mock.Call
}

// WatchRemovedAccess is a helper method to define mock.On call
//   - opts *bind.WatchOpts
//   - sink chan<- *flags_wrapper.FlagsRemovedAccess
func (_e *Flags_Expecter) WatchRemovedAccess(opts interface{}, sink interface{}) *Flags_WatchRemovedAccess_Call {
	return &Flags_WatchRemovedAccess_Call{Call: _e.mock.On("WatchRemovedAccess", opts, sink)}
}

func (_c *Flags_WatchRemovedAccess_Call) Run(run func(opts *bind.WatchOpts, sink chan<- *flags_wrapper.FlagsRemovedAccess)) *Flags_WatchRemovedAccess_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*bind.WatchOpts), args[1].(chan<- *flags_wrapper.FlagsRemovedAccess))
	})
	return _c
}

func (_c *Flags_WatchRemovedAccess_Call) Return(_a0 event.Subscription, _a1 error) *Flags_WatchRemovedAccess_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Flags_WatchRemovedAccess_Call) RunAndReturn(run func(*bind.WatchOpts, chan<- *flags_wrapper.FlagsRemovedAccess) (event.Subscription, error)) *Flags_WatchRemovedAccess_Call {
	_c.Call.Return(run)
	return _c
}

// NewFlags creates a new instance of Flags. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFlags(t interface {
	mock.TestingT
	Cleanup(func())
}) *Flags {
	mock := &Flags{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
