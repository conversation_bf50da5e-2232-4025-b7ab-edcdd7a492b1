type                = "directrequest"
schemaVersion       = 1
name                = "%s"
contractAddress     = "%s"
externalJobID       = "%s"
evmChainID          = 1337
observationSource   = """
    decode_log   [type=ethabidecodelog
                  abi="OracleRequest(bytes32 indexed specId, address requester, bytes32 requestId, uint256 payment, address callbackAddr, bytes4 callbackFunctionId, uint256 cancelExpiration, uint256 dataVersion, bytes data)"
                  data="$(jobRun.logData)"
                  topics="$(jobRun.logTopics)"]
    decode_cbor  [type=cborparse data="$(decode_log.data)"]
    ds1          [type=http method=GET url="$(decode_cbor.urlUSD)" allowunrestrictednetworkaccess="true"];
    ds1_parse    [type=jsonparse path="$(decode_cbor.pathUSD)"];
    ds1_multiply [type=multiply value="$(ds1_parse)" times=100];
    encode_data [type=ethabiencode abi="(uint256 value)" data=<{"value": $(ds1_multiply)}>]
    encode_tx [type=ethabiencode
            abi="fulfillOracleRequest(bytes32 requestId, uint256 payment, address callbackAddress, bytes4 callbackFunctionId, uint256 expiration, bytes32 data)"
            data=<{"requestId": $(decode_log.requestId),
                   "payment": $(decode_log.payment),
                   "callbackAddress": $(decode_log.callbackAddr),
                   "callbackFunctionId": $(decode_log.callbackFunctionId),
                   "expiration": $(decode_log.cancelExpiration),
                   "data": $(encode_data)}>]
    submit [type=ethtx to="%s" data="$(encode_tx)" minConfirmations="2"]
    decode_log->decode_cbor->ds1 -> ds1_parse -> ds1_multiply->encode_data->encode_tx->submit;
"""
