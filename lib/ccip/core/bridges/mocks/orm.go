// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	auth "github.com/smartcontractkit/chainlink/v2/core/auth"
	bridges "github.com/smartcontractkit/chainlink/v2/core/bridges"

	context "context"

	mock "github.com/stretchr/testify/mock"

	sqlutil "github.com/smartcontractkit/chainlink-common/pkg/sqlutil"

	time "time"
)

// ORM is an autogenerated mock type for the ORM type
type ORM struct {
	mock.Mock
}

type ORM_Expecter struct {
	mock *mock.Mock
}

func (_m *ORM) EXPECT() *ORM_Expecter {
	return &ORM_Expecter{mock: &_m.Mock}
}

// BridgeTypes provides a mock function with given fields: ctx, offset, limit
func (_m *ORM) BridgeTypes(ctx context.Context, offset int, limit int) ([]bridges.BridgeType, int, error) {
	ret := _m.Called(ctx, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for BridgeTypes")
	}

	var r0 []bridges.BridgeType
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) ([]bridges.BridgeType, int, error)); ok {
		return rf(ctx, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) []bridges.BridgeType); ok {
		r0 = rf(ctx, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]bridges.BridgeType)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) int); ok {
		r1 = rf(ctx, offset, limit)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int, int) error); ok {
		r2 = rf(ctx, offset, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ORM_BridgeTypes_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BridgeTypes'
type ORM_BridgeTypes_Call struct {
	*mock.Call
}

// BridgeTypes is a helper method to define mock.On call
//   - ctx context.Context
//   - offset int
//   - limit int
func (_e *ORM_Expecter) BridgeTypes(ctx interface{}, offset interface{}, limit interface{}) *ORM_BridgeTypes_Call {
	return &ORM_BridgeTypes_Call{Call: _e.mock.On("BridgeTypes", ctx, offset, limit)}
}

func (_c *ORM_BridgeTypes_Call) Run(run func(ctx context.Context, offset int, limit int)) *ORM_BridgeTypes_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *ORM_BridgeTypes_Call) Return(_a0 []bridges.BridgeType, _a1 int, _a2 error) *ORM_BridgeTypes_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *ORM_BridgeTypes_Call) RunAndReturn(run func(context.Context, int, int) ([]bridges.BridgeType, int, error)) *ORM_BridgeTypes_Call {
	_c.Call.Return(run)
	return _c
}

// BulkUpsertBridgeResponse provides a mock function with given fields: ctx, responses
func (_m *ORM) BulkUpsertBridgeResponse(ctx context.Context, responses []bridges.BridgeResponse) error {
	ret := _m.Called(ctx, responses)

	if len(ret) == 0 {
		panic("no return value specified for BulkUpsertBridgeResponse")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []bridges.BridgeResponse) error); ok {
		r0 = rf(ctx, responses)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_BulkUpsertBridgeResponse_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BulkUpsertBridgeResponse'
type ORM_BulkUpsertBridgeResponse_Call struct {
	*mock.Call
}

// BulkUpsertBridgeResponse is a helper method to define mock.On call
//   - ctx context.Context
//   - responses []bridges.BridgeResponse
func (_e *ORM_Expecter) BulkUpsertBridgeResponse(ctx interface{}, responses interface{}) *ORM_BulkUpsertBridgeResponse_Call {
	return &ORM_BulkUpsertBridgeResponse_Call{Call: _e.mock.On("BulkUpsertBridgeResponse", ctx, responses)}
}

func (_c *ORM_BulkUpsertBridgeResponse_Call) Run(run func(ctx context.Context, responses []bridges.BridgeResponse)) *ORM_BulkUpsertBridgeResponse_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]bridges.BridgeResponse))
	})
	return _c
}

func (_c *ORM_BulkUpsertBridgeResponse_Call) Return(_a0 error) *ORM_BulkUpsertBridgeResponse_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_BulkUpsertBridgeResponse_Call) RunAndReturn(run func(context.Context, []bridges.BridgeResponse) error) *ORM_BulkUpsertBridgeResponse_Call {
	_c.Call.Return(run)
	return _c
}

// CreateBridgeType provides a mock function with given fields: ctx, bt
func (_m *ORM) CreateBridgeType(ctx context.Context, bt *bridges.BridgeType) error {
	ret := _m.Called(ctx, bt)

	if len(ret) == 0 {
		panic("no return value specified for CreateBridgeType")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *bridges.BridgeType) error); ok {
		r0 = rf(ctx, bt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_CreateBridgeType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateBridgeType'
type ORM_CreateBridgeType_Call struct {
	*mock.Call
}

// CreateBridgeType is a helper method to define mock.On call
//   - ctx context.Context
//   - bt *bridges.BridgeType
func (_e *ORM_Expecter) CreateBridgeType(ctx interface{}, bt interface{}) *ORM_CreateBridgeType_Call {
	return &ORM_CreateBridgeType_Call{Call: _e.mock.On("CreateBridgeType", ctx, bt)}
}

func (_c *ORM_CreateBridgeType_Call) Run(run func(ctx context.Context, bt *bridges.BridgeType)) *ORM_CreateBridgeType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*bridges.BridgeType))
	})
	return _c
}

func (_c *ORM_CreateBridgeType_Call) Return(_a0 error) *ORM_CreateBridgeType_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_CreateBridgeType_Call) RunAndReturn(run func(context.Context, *bridges.BridgeType) error) *ORM_CreateBridgeType_Call {
	_c.Call.Return(run)
	return _c
}

// CreateExternalInitiator provides a mock function with given fields: ctx, externalInitiator
func (_m *ORM) CreateExternalInitiator(ctx context.Context, externalInitiator *bridges.ExternalInitiator) error {
	ret := _m.Called(ctx, externalInitiator)

	if len(ret) == 0 {
		panic("no return value specified for CreateExternalInitiator")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *bridges.ExternalInitiator) error); ok {
		r0 = rf(ctx, externalInitiator)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_CreateExternalInitiator_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateExternalInitiator'
type ORM_CreateExternalInitiator_Call struct {
	*mock.Call
}

// CreateExternalInitiator is a helper method to define mock.On call
//   - ctx context.Context
//   - externalInitiator *bridges.ExternalInitiator
func (_e *ORM_Expecter) CreateExternalInitiator(ctx interface{}, externalInitiator interface{}) *ORM_CreateExternalInitiator_Call {
	return &ORM_CreateExternalInitiator_Call{Call: _e.mock.On("CreateExternalInitiator", ctx, externalInitiator)}
}

func (_c *ORM_CreateExternalInitiator_Call) Run(run func(ctx context.Context, externalInitiator *bridges.ExternalInitiator)) *ORM_CreateExternalInitiator_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*bridges.ExternalInitiator))
	})
	return _c
}

func (_c *ORM_CreateExternalInitiator_Call) Return(_a0 error) *ORM_CreateExternalInitiator_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_CreateExternalInitiator_Call) RunAndReturn(run func(context.Context, *bridges.ExternalInitiator) error) *ORM_CreateExternalInitiator_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteBridgeType provides a mock function with given fields: ctx, bt
func (_m *ORM) DeleteBridgeType(ctx context.Context, bt *bridges.BridgeType) error {
	ret := _m.Called(ctx, bt)

	if len(ret) == 0 {
		panic("no return value specified for DeleteBridgeType")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *bridges.BridgeType) error); ok {
		r0 = rf(ctx, bt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_DeleteBridgeType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteBridgeType'
type ORM_DeleteBridgeType_Call struct {
	*mock.Call
}

// DeleteBridgeType is a helper method to define mock.On call
//   - ctx context.Context
//   - bt *bridges.BridgeType
func (_e *ORM_Expecter) DeleteBridgeType(ctx interface{}, bt interface{}) *ORM_DeleteBridgeType_Call {
	return &ORM_DeleteBridgeType_Call{Call: _e.mock.On("DeleteBridgeType", ctx, bt)}
}

func (_c *ORM_DeleteBridgeType_Call) Run(run func(ctx context.Context, bt *bridges.BridgeType)) *ORM_DeleteBridgeType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*bridges.BridgeType))
	})
	return _c
}

func (_c *ORM_DeleteBridgeType_Call) Return(_a0 error) *ORM_DeleteBridgeType_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_DeleteBridgeType_Call) RunAndReturn(run func(context.Context, *bridges.BridgeType) error) *ORM_DeleteBridgeType_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteExternalInitiator provides a mock function with given fields: ctx, name
func (_m *ORM) DeleteExternalInitiator(ctx context.Context, name string) error {
	ret := _m.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for DeleteExternalInitiator")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_DeleteExternalInitiator_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteExternalInitiator'
type ORM_DeleteExternalInitiator_Call struct {
	*mock.Call
}

// DeleteExternalInitiator is a helper method to define mock.On call
//   - ctx context.Context
//   - name string
func (_e *ORM_Expecter) DeleteExternalInitiator(ctx interface{}, name interface{}) *ORM_DeleteExternalInitiator_Call {
	return &ORM_DeleteExternalInitiator_Call{Call: _e.mock.On("DeleteExternalInitiator", ctx, name)}
}

func (_c *ORM_DeleteExternalInitiator_Call) Run(run func(ctx context.Context, name string)) *ORM_DeleteExternalInitiator_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *ORM_DeleteExternalInitiator_Call) Return(_a0 error) *ORM_DeleteExternalInitiator_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_DeleteExternalInitiator_Call) RunAndReturn(run func(context.Context, string) error) *ORM_DeleteExternalInitiator_Call {
	_c.Call.Return(run)
	return _c
}

// ExternalInitiators provides a mock function with given fields: ctx, offset, limit
func (_m *ORM) ExternalInitiators(ctx context.Context, offset int, limit int) ([]bridges.ExternalInitiator, int, error) {
	ret := _m.Called(ctx, offset, limit)

	if len(ret) == 0 {
		panic("no return value specified for ExternalInitiators")
	}

	var r0 []bridges.ExternalInitiator
	var r1 int
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, int, int) ([]bridges.ExternalInitiator, int, error)); ok {
		return rf(ctx, offset, limit)
	}
	if rf, ok := ret.Get(0).(func(context.Context, int, int) []bridges.ExternalInitiator); ok {
		r0 = rf(ctx, offset, limit)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]bridges.ExternalInitiator)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, int, int) int); ok {
		r1 = rf(ctx, offset, limit)
	} else {
		r1 = ret.Get(1).(int)
	}

	if rf, ok := ret.Get(2).(func(context.Context, int, int) error); ok {
		r2 = rf(ctx, offset, limit)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ORM_ExternalInitiators_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ExternalInitiators'
type ORM_ExternalInitiators_Call struct {
	*mock.Call
}

// ExternalInitiators is a helper method to define mock.On call
//   - ctx context.Context
//   - offset int
//   - limit int
func (_e *ORM_Expecter) ExternalInitiators(ctx interface{}, offset interface{}, limit interface{}) *ORM_ExternalInitiators_Call {
	return &ORM_ExternalInitiators_Call{Call: _e.mock.On("ExternalInitiators", ctx, offset, limit)}
}

func (_c *ORM_ExternalInitiators_Call) Run(run func(ctx context.Context, offset int, limit int)) *ORM_ExternalInitiators_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(int), args[2].(int))
	})
	return _c
}

func (_c *ORM_ExternalInitiators_Call) Return(_a0 []bridges.ExternalInitiator, _a1 int, _a2 error) *ORM_ExternalInitiators_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *ORM_ExternalInitiators_Call) RunAndReturn(run func(context.Context, int, int) ([]bridges.ExternalInitiator, int, error)) *ORM_ExternalInitiators_Call {
	_c.Call.Return(run)
	return _c
}

// FindBridge provides a mock function with given fields: ctx, name
func (_m *ORM) FindBridge(ctx context.Context, name bridges.BridgeName) (bridges.BridgeType, error) {
	ret := _m.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for FindBridge")
	}

	var r0 bridges.BridgeType
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, bridges.BridgeName) (bridges.BridgeType, error)); ok {
		return rf(ctx, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, bridges.BridgeName) bridges.BridgeType); ok {
		r0 = rf(ctx, name)
	} else {
		r0 = ret.Get(0).(bridges.BridgeType)
	}

	if rf, ok := ret.Get(1).(func(context.Context, bridges.BridgeName) error); ok {
		r1 = rf(ctx, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindBridge_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindBridge'
type ORM_FindBridge_Call struct {
	*mock.Call
}

// FindBridge is a helper method to define mock.On call
//   - ctx context.Context
//   - name bridges.BridgeName
func (_e *ORM_Expecter) FindBridge(ctx interface{}, name interface{}) *ORM_FindBridge_Call {
	return &ORM_FindBridge_Call{Call: _e.mock.On("FindBridge", ctx, name)}
}

func (_c *ORM_FindBridge_Call) Run(run func(ctx context.Context, name bridges.BridgeName)) *ORM_FindBridge_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(bridges.BridgeName))
	})
	return _c
}

func (_c *ORM_FindBridge_Call) Return(bt bridges.BridgeType, err error) *ORM_FindBridge_Call {
	_c.Call.Return(bt, err)
	return _c
}

func (_c *ORM_FindBridge_Call) RunAndReturn(run func(context.Context, bridges.BridgeName) (bridges.BridgeType, error)) *ORM_FindBridge_Call {
	_c.Call.Return(run)
	return _c
}

// FindBridges provides a mock function with given fields: ctx, name
func (_m *ORM) FindBridges(ctx context.Context, name []bridges.BridgeName) ([]bridges.BridgeType, error) {
	ret := _m.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for FindBridges")
	}

	var r0 []bridges.BridgeType
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, []bridges.BridgeName) ([]bridges.BridgeType, error)); ok {
		return rf(ctx, name)
	}
	if rf, ok := ret.Get(0).(func(context.Context, []bridges.BridgeName) []bridges.BridgeType); ok {
		r0 = rf(ctx, name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]bridges.BridgeType)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, []bridges.BridgeName) error); ok {
		r1 = rf(ctx, name)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindBridges_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindBridges'
type ORM_FindBridges_Call struct {
	*mock.Call
}

// FindBridges is a helper method to define mock.On call
//   - ctx context.Context
//   - name []bridges.BridgeName
func (_e *ORM_Expecter) FindBridges(ctx interface{}, name interface{}) *ORM_FindBridges_Call {
	return &ORM_FindBridges_Call{Call: _e.mock.On("FindBridges", ctx, name)}
}

func (_c *ORM_FindBridges_Call) Run(run func(ctx context.Context, name []bridges.BridgeName)) *ORM_FindBridges_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]bridges.BridgeName))
	})
	return _c
}

func (_c *ORM_FindBridges_Call) Return(bts []bridges.BridgeType, err error) *ORM_FindBridges_Call {
	_c.Call.Return(bts, err)
	return _c
}

func (_c *ORM_FindBridges_Call) RunAndReturn(run func(context.Context, []bridges.BridgeName) ([]bridges.BridgeType, error)) *ORM_FindBridges_Call {
	_c.Call.Return(run)
	return _c
}

// FindExternalInitiator provides a mock function with given fields: ctx, eia
func (_m *ORM) FindExternalInitiator(ctx context.Context, eia *auth.Token) (*bridges.ExternalInitiator, error) {
	ret := _m.Called(ctx, eia)

	if len(ret) == 0 {
		panic("no return value specified for FindExternalInitiator")
	}

	var r0 *bridges.ExternalInitiator
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *auth.Token) (*bridges.ExternalInitiator, error)); ok {
		return rf(ctx, eia)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *auth.Token) *bridges.ExternalInitiator); ok {
		r0 = rf(ctx, eia)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*bridges.ExternalInitiator)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *auth.Token) error); ok {
		r1 = rf(ctx, eia)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindExternalInitiator_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindExternalInitiator'
type ORM_FindExternalInitiator_Call struct {
	*mock.Call
}

// FindExternalInitiator is a helper method to define mock.On call
//   - ctx context.Context
//   - eia *auth.Token
func (_e *ORM_Expecter) FindExternalInitiator(ctx interface{}, eia interface{}) *ORM_FindExternalInitiator_Call {
	return &ORM_FindExternalInitiator_Call{Call: _e.mock.On("FindExternalInitiator", ctx, eia)}
}

func (_c *ORM_FindExternalInitiator_Call) Run(run func(ctx context.Context, eia *auth.Token)) *ORM_FindExternalInitiator_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*auth.Token))
	})
	return _c
}

func (_c *ORM_FindExternalInitiator_Call) Return(_a0 *bridges.ExternalInitiator, _a1 error) *ORM_FindExternalInitiator_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_FindExternalInitiator_Call) RunAndReturn(run func(context.Context, *auth.Token) (*bridges.ExternalInitiator, error)) *ORM_FindExternalInitiator_Call {
	_c.Call.Return(run)
	return _c
}

// FindExternalInitiatorByName provides a mock function with given fields: ctx, iname
func (_m *ORM) FindExternalInitiatorByName(ctx context.Context, iname string) (bridges.ExternalInitiator, error) {
	ret := _m.Called(ctx, iname)

	if len(ret) == 0 {
		panic("no return value specified for FindExternalInitiatorByName")
	}

	var r0 bridges.ExternalInitiator
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bridges.ExternalInitiator, error)); ok {
		return rf(ctx, iname)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bridges.ExternalInitiator); ok {
		r0 = rf(ctx, iname)
	} else {
		r0 = ret.Get(0).(bridges.ExternalInitiator)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, iname)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_FindExternalInitiatorByName_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindExternalInitiatorByName'
type ORM_FindExternalInitiatorByName_Call struct {
	*mock.Call
}

// FindExternalInitiatorByName is a helper method to define mock.On call
//   - ctx context.Context
//   - iname string
func (_e *ORM_Expecter) FindExternalInitiatorByName(ctx interface{}, iname interface{}) *ORM_FindExternalInitiatorByName_Call {
	return &ORM_FindExternalInitiatorByName_Call{Call: _e.mock.On("FindExternalInitiatorByName", ctx, iname)}
}

func (_c *ORM_FindExternalInitiatorByName_Call) Run(run func(ctx context.Context, iname string)) *ORM_FindExternalInitiatorByName_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *ORM_FindExternalInitiatorByName_Call) Return(exi bridges.ExternalInitiator, err error) *ORM_FindExternalInitiatorByName_Call {
	_c.Call.Return(exi, err)
	return _c
}

func (_c *ORM_FindExternalInitiatorByName_Call) RunAndReturn(run func(context.Context, string) (bridges.ExternalInitiator, error)) *ORM_FindExternalInitiatorByName_Call {
	_c.Call.Return(run)
	return _c
}

// GetCachedResponse provides a mock function with given fields: ctx, dotId, specId, maxElapsed
func (_m *ORM) GetCachedResponse(ctx context.Context, dotId string, specId int32, maxElapsed time.Duration) ([]byte, error) {
	ret := _m.Called(ctx, dotId, specId, maxElapsed)

	if len(ret) == 0 {
		panic("no return value specified for GetCachedResponse")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int32, time.Duration) ([]byte, error)); ok {
		return rf(ctx, dotId, specId, maxElapsed)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int32, time.Duration) []byte); ok {
		r0 = rf(ctx, dotId, specId, maxElapsed)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int32, time.Duration) error); ok {
		r1 = rf(ctx, dotId, specId, maxElapsed)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ORM_GetCachedResponse_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCachedResponse'
type ORM_GetCachedResponse_Call struct {
	*mock.Call
}

// GetCachedResponse is a helper method to define mock.On call
//   - ctx context.Context
//   - dotId string
//   - specId int32
//   - maxElapsed time.Duration
func (_e *ORM_Expecter) GetCachedResponse(ctx interface{}, dotId interface{}, specId interface{}, maxElapsed interface{}) *ORM_GetCachedResponse_Call {
	return &ORM_GetCachedResponse_Call{Call: _e.mock.On("GetCachedResponse", ctx, dotId, specId, maxElapsed)}
}

func (_c *ORM_GetCachedResponse_Call) Run(run func(ctx context.Context, dotId string, specId int32, maxElapsed time.Duration)) *ORM_GetCachedResponse_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int32), args[3].(time.Duration))
	})
	return _c
}

func (_c *ORM_GetCachedResponse_Call) Return(_a0 []byte, _a1 error) *ORM_GetCachedResponse_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *ORM_GetCachedResponse_Call) RunAndReturn(run func(context.Context, string, int32, time.Duration) ([]byte, error)) *ORM_GetCachedResponse_Call {
	_c.Call.Return(run)
	return _c
}

// GetCachedResponseWithFinished provides a mock function with given fields: ctx, dotId, specId, maxElapsed
func (_m *ORM) GetCachedResponseWithFinished(ctx context.Context, dotId string, specId int32, maxElapsed time.Duration) ([]byte, time.Time, error) {
	ret := _m.Called(ctx, dotId, specId, maxElapsed)

	if len(ret) == 0 {
		panic("no return value specified for GetCachedResponseWithFinished")
	}

	var r0 []byte
	var r1 time.Time
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int32, time.Duration) ([]byte, time.Time, error)); ok {
		return rf(ctx, dotId, specId, maxElapsed)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, int32, time.Duration) []byte); ok {
		r0 = rf(ctx, dotId, specId, maxElapsed)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, int32, time.Duration) time.Time); ok {
		r1 = rf(ctx, dotId, specId, maxElapsed)
	} else {
		r1 = ret.Get(1).(time.Time)
	}

	if rf, ok := ret.Get(2).(func(context.Context, string, int32, time.Duration) error); ok {
		r2 = rf(ctx, dotId, specId, maxElapsed)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// ORM_GetCachedResponseWithFinished_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCachedResponseWithFinished'
type ORM_GetCachedResponseWithFinished_Call struct {
	*mock.Call
}

// GetCachedResponseWithFinished is a helper method to define mock.On call
//   - ctx context.Context
//   - dotId string
//   - specId int32
//   - maxElapsed time.Duration
func (_e *ORM_Expecter) GetCachedResponseWithFinished(ctx interface{}, dotId interface{}, specId interface{}, maxElapsed interface{}) *ORM_GetCachedResponseWithFinished_Call {
	return &ORM_GetCachedResponseWithFinished_Call{Call: _e.mock.On("GetCachedResponseWithFinished", ctx, dotId, specId, maxElapsed)}
}

func (_c *ORM_GetCachedResponseWithFinished_Call) Run(run func(ctx context.Context, dotId string, specId int32, maxElapsed time.Duration)) *ORM_GetCachedResponseWithFinished_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int32), args[3].(time.Duration))
	})
	return _c
}

func (_c *ORM_GetCachedResponseWithFinished_Call) Return(_a0 []byte, _a1 time.Time, _a2 error) *ORM_GetCachedResponseWithFinished_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *ORM_GetCachedResponseWithFinished_Call) RunAndReturn(run func(context.Context, string, int32, time.Duration) ([]byte, time.Time, error)) *ORM_GetCachedResponseWithFinished_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateBridgeType provides a mock function with given fields: ctx, bt, btr
func (_m *ORM) UpdateBridgeType(ctx context.Context, bt *bridges.BridgeType, btr *bridges.BridgeTypeRequest) error {
	ret := _m.Called(ctx, bt, btr)

	if len(ret) == 0 {
		panic("no return value specified for UpdateBridgeType")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *bridges.BridgeType, *bridges.BridgeTypeRequest) error); ok {
		r0 = rf(ctx, bt, btr)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_UpdateBridgeType_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateBridgeType'
type ORM_UpdateBridgeType_Call struct {
	*mock.Call
}

// UpdateBridgeType is a helper method to define mock.On call
//   - ctx context.Context
//   - bt *bridges.BridgeType
//   - btr *bridges.BridgeTypeRequest
func (_e *ORM_Expecter) UpdateBridgeType(ctx interface{}, bt interface{}, btr interface{}) *ORM_UpdateBridgeType_Call {
	return &ORM_UpdateBridgeType_Call{Call: _e.mock.On("UpdateBridgeType", ctx, bt, btr)}
}

func (_c *ORM_UpdateBridgeType_Call) Run(run func(ctx context.Context, bt *bridges.BridgeType, btr *bridges.BridgeTypeRequest)) *ORM_UpdateBridgeType_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*bridges.BridgeType), args[2].(*bridges.BridgeTypeRequest))
	})
	return _c
}

func (_c *ORM_UpdateBridgeType_Call) Return(_a0 error) *ORM_UpdateBridgeType_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_UpdateBridgeType_Call) RunAndReturn(run func(context.Context, *bridges.BridgeType, *bridges.BridgeTypeRequest) error) *ORM_UpdateBridgeType_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertBridgeResponse provides a mock function with given fields: ctx, dotId, specId, response
func (_m *ORM) UpsertBridgeResponse(ctx context.Context, dotId string, specId int32, response []byte) error {
	ret := _m.Called(ctx, dotId, specId, response)

	if len(ret) == 0 {
		panic("no return value specified for UpsertBridgeResponse")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int32, []byte) error); ok {
		r0 = rf(ctx, dotId, specId, response)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ORM_UpsertBridgeResponse_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertBridgeResponse'
type ORM_UpsertBridgeResponse_Call struct {
	*mock.Call
}

// UpsertBridgeResponse is a helper method to define mock.On call
//   - ctx context.Context
//   - dotId string
//   - specId int32
//   - response []byte
func (_e *ORM_Expecter) UpsertBridgeResponse(ctx interface{}, dotId interface{}, specId interface{}, response interface{}) *ORM_UpsertBridgeResponse_Call {
	return &ORM_UpsertBridgeResponse_Call{Call: _e.mock.On("UpsertBridgeResponse", ctx, dotId, specId, response)}
}

func (_c *ORM_UpsertBridgeResponse_Call) Run(run func(ctx context.Context, dotId string, specId int32, response []byte)) *ORM_UpsertBridgeResponse_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string), args[2].(int32), args[3].([]byte))
	})
	return _c
}

func (_c *ORM_UpsertBridgeResponse_Call) Return(_a0 error) *ORM_UpsertBridgeResponse_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_UpsertBridgeResponse_Call) RunAndReturn(run func(context.Context, string, int32, []byte) error) *ORM_UpsertBridgeResponse_Call {
	_c.Call.Return(run)
	return _c
}

// WithDataSource provides a mock function with given fields: _a0
func (_m *ORM) WithDataSource(_a0 sqlutil.DataSource) bridges.ORM {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for WithDataSource")
	}

	var r0 bridges.ORM
	if rf, ok := ret.Get(0).(func(sqlutil.DataSource) bridges.ORM); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(bridges.ORM)
		}
	}

	return r0
}

// ORM_WithDataSource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithDataSource'
type ORM_WithDataSource_Call struct {
	*mock.Call
}

// WithDataSource is a helper method to define mock.On call
//   - _a0 sqlutil.DataSource
func (_e *ORM_Expecter) WithDataSource(_a0 interface{}) *ORM_WithDataSource_Call {
	return &ORM_WithDataSource_Call{Call: _e.mock.On("WithDataSource", _a0)}
}

func (_c *ORM_WithDataSource_Call) Run(run func(_a0 sqlutil.DataSource)) *ORM_WithDataSource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(sqlutil.DataSource))
	})
	return _c
}

func (_c *ORM_WithDataSource_Call) Return(_a0 bridges.ORM) *ORM_WithDataSource_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *ORM_WithDataSource_Call) RunAndReturn(run func(sqlutil.DataSource) bridges.ORM) *ORM_WithDataSource_Call {
	_c.Call.Return(run)
	return _c
}

// NewORM creates a new instance of ORM. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewORM(t interface {
	mock.TestingT
	Cleanup(func())
}) *ORM {
	mock := &ORM{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
