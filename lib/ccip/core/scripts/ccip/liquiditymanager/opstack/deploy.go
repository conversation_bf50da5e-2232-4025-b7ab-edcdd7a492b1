package opstack

import (
	"github.com/ethereum/go-ethereum/common"
	chainsel "github.com/smartcontractkit/chain-selectors"
)

var (
	// Optimism Contracts
	// See https://docs.optimism.io/chain/addresses
	OptimismContractsByChainID map[uint64]map[string]common.Address
)

func init() {
	OptimismContractsByChainID = map[uint64]map[string]common.Address{
		chainsel.ETHEREUM_TESTNET_SEPOLIA.EvmChainID: {
			"L1StandardBridge":       common.HexToAddress("******************************************"),
			"L1CrossDomainMessenger": common.HexToAddress("******************************************"),
			"WETH":                   common.HexToAddress("******************************************"),
			"FaucetTestingToken":     common.HexToAddress("******************************************"),
			"OptimismPortalProxy":    common.HexToAddress("******************************************"),
			"L2OutputOracle":         common.HexToAddress("******************************************"), // Removed after FPAC upgrade
		},
		chainsel.ETHEREUM_TESTNET_SEPOLIA_OPTIMISM_1.EvmChainID: {
			"WETH":                common.HexToAddress("******************************************"),
			"FaucetTestingToken":  common.HexToAddress("******************************************"),
			"L2ToL1MessagePasser": common.HexToAddress("******************************************"),
		},
	}
}
