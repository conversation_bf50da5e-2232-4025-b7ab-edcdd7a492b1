package arb

import (
	"github.com/ethereum/go-ethereum/common"
	chainsel "github.com/smartcontractkit/chain-selectors"
)

var (
	// Arbitrum Contracts
	// See https://docs.arbitrum.io/for-devs/useful-addresses
	ArbitrumContracts map[uint64]map[string]common.Address
)

func init() {
	ArbitrumContracts = map[uint64]map[string]common.Address{
		chainsel.ETHEREUM_TESTNET_SEPOLIA.EvmChainID: {
			"L1GatewayRouter": common.HexToAddress("******************************************"),
			"L1Outbox":        common.HexToAddress("******************************************"),
			// labeled "Delayed Inbox" in the arbitrum docs
			"L1Inbox": common.HexToAddress("******************************************"),
			"Rollup":  common.HexToAddress("******************************************"),
			"WETH":    common.HexToAddress("******************************************"),
		},
		chainsel.ETHEREUM_TESTNET_SEPOLIA_ARBITRUM_1.EvmChainID: {
			"L2GatewayRouter": common.HexToAddress("******************************************"),
			"NodeInterface":   common.HexToAddress("******************************************"),
			"WETH":            common.HexToAddress("******************************************"),
		},
	}
}
