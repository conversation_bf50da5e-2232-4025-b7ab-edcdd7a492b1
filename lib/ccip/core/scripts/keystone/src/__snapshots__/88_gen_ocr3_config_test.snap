
[TestGenerateOCR3Config - 1]
{
 "F": 1,
 "OffchainConfig": "<nonemptyvalue>",
 "OffchainConfigVersion": 30,
 "OnchainConfig": "0x",
 "Signers": [
  "011400a2402db8e549f094ea31e1c0edd77623f4ca5b12052000ea551e503b93a1c9ae26262b4db8f66db4cbe5ddcb6039e29d2665a634d48e4a",
  "0114004af19c802b244d1d085492c3946391c965e10519052000ea551e503b93a1c9ae26262b4db8f66db4cbe5ddcb6039e29d2665a634d48e4b",
  "01140061925685d2b80b121537341d063c4e57b2f9323c052000ea551e503b93a1c9ae26262b4db8f66db4cbe5ddcb6039e29d2665a634d48e4c",
  "011400fd97efd53fc20acc098fcd746c04d8d7540d97e0052000ea551e503b93a1c9ae26262b4db8f66db4cbe5ddcb6039e29d2665a634d48e4d",
  "011400a0b67dc5345a71d02b396147ae2cb75dda63cbe9052000ea551e503b93a1c9ae26262b4db8f66db4cbe5ddcb6039e29d2665a634d48e4e"
 ],
 "Transmitters": [
  "0xF4e7e516146c8567F8E8be0ED1f1A92798628d35",
  "0x8B60FDcc9CAC8ea476b31d17011CB204471431d9",
  "0x6620F516F29979B214e2451498a057FDd3a0A85d",
  "0xFeB61E22FCf4F9740c9D96b05199F195bd61A7c2",
  "0x882Fd04D78A7e7D386Dd5b550f19479E5494B0B2"
 ]
}
---
