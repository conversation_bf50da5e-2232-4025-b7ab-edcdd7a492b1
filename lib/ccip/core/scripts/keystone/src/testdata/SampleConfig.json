{"OracleConfig": {"MaxQueryLengthBytes": 1000000, "MaxObservationLengthBytes": 1000000, "MaxReportLengthBytes": 1000000, "MaxRequestBatchSize": 1000, "UniqueReports": true, "DeltaProgressMillis": 5000, "DeltaResendMillis": 5000, "DeltaInitialMillis": 5000, "DeltaRoundMillis": 2000, "DeltaGraceMillis": 500, "DeltaCertifiedCommitRequestMillis": 1000, "DeltaStageMillis": 30000, "MaxRoundsPerEpoch": 10, "TransmissionSchedule": [1, 1, 1, 1], "MaxDurationQueryMillis": 1000, "MaxDurationObservationMillis": 1000, "MaxDurationReportMillis": 1000, "MaxDurationAcceptMillis": 1000, "MaxDurationTransmitMillis": 1000, "MaxFaultyOracles": 1}}