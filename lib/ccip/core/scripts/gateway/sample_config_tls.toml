[UserServerConfig]
Port = 8088
TLSEnabled = true
TLSCertPath = "certificate.pem"
TLSKeyPath = "key.pem"
Path = "/user"
ContentTypeHeader = "application/jsonrpc"
ReadTimeoutMillis = 1000
WriteTimeoutMillis = 1000
RequestTimeoutMillis = 1000
MaxRequestBytes = 10_000

[NodeServerConfig]
Port = 8089
TLSEnabled = true
TLSCertPath = "certificate.pem"
TLSKeyPath = "key.pem"
Path = "/node"
ReadTimeoutMillis = 1000
WriteTimeoutMillis = 1000
RequestTimeoutMillis = 1000
MaxRequestBytes = 10_000
HandshakeTimeoutMillis = 1000

[ConnectionManagerConfig]
AuthGatewayId = "example_gateway"
AuthTimestampToleranceSec = 60
AuthChallengeLen = 32

[[Dons]]
DonId = "example_don"
HandlerName = "dummy"

[[Dons.Members]]
Name = "example_node"
Address = "0x68902d681c28119f9b2531473a417088bf008e59"