// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	ldapauth "github.com/smartcontractkit/chainlink/v2/core/sessions/ldapauth"
	mock "github.com/stretchr/testify/mock"
)

// LDAPClient is an autogenerated mock type for the LDAPClient type
type LDAPClient struct {
	mock.Mock
}

type LDAPClient_Expecter struct {
	mock *mock.Mock
}

func (_m *LDAPClient) EXPECT() *LDAPClient_Expecter {
	return &LDAPClient_Expecter{mock: &_m.Mock}
}

// CreateEphemeralConnection provides a mock function with given fields:
func (_m *LDAPClient) CreateEphemeralConnection() (ldapauth.LDAPConn, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for CreateEphemeralConnection")
	}

	var r0 ldapauth.LDAPConn
	var r1 error
	if rf, ok := ret.Get(0).(func() (ldapauth.LDAPConn, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() ldapauth.LDAPConn); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ldapauth.LDAPConn)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// LDAPClient_CreateEphemeralConnection_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateEphemeralConnection'
type LDAPClient_CreateEphemeralConnection_Call struct {
	*mock.Call
}

// CreateEphemeralConnection is a helper method to define mock.On call
func (_e *LDAPClient_Expecter) CreateEphemeralConnection() *LDAPClient_CreateEphemeralConnection_Call {
	return &LDAPClient_CreateEphemeralConnection_Call{Call: _e.mock.On("CreateEphemeralConnection")}
}

func (_c *LDAPClient_CreateEphemeralConnection_Call) Run(run func()) *LDAPClient_CreateEphemeralConnection_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *LDAPClient_CreateEphemeralConnection_Call) Return(_a0 ldapauth.LDAPConn, _a1 error) *LDAPClient_CreateEphemeralConnection_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *LDAPClient_CreateEphemeralConnection_Call) RunAndReturn(run func() (ldapauth.LDAPConn, error)) *LDAPClient_CreateEphemeralConnection_Call {
	_c.Call.Return(run)
	return _c
}

// NewLDAPClient creates a new instance of LDAPClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewLDAPClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *LDAPClient {
	mock := &LDAPClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
