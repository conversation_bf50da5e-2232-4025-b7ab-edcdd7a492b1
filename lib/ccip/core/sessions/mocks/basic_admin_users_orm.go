// Code generated by mockery v2.43.2. DO NOT EDIT.

package mocks

import (
	context "context"

	sessions "github.com/smartcontractkit/chainlink/v2/core/sessions"
	mock "github.com/stretchr/testify/mock"
)

// BasicAdminUsersORM is an autogenerated mock type for the BasicAdminUsersORM type
type BasicAdminUsersORM struct {
	mock.Mock
}

type BasicAdminUsersORM_Expecter struct {
	mock *mock.Mock
}

func (_m *BasicAdminUsersORM) EXPECT() *BasicAdminUsersORM_Expecter {
	return &BasicAdminUsersORM_Expecter{mock: &_m.Mock}
}

// CreateUser provides a mock function with given fields: ctx, user
func (_m *BasicAdminUsersORM) CreateUser(ctx context.Context, user *sessions.User) error {
	ret := _m.Called(ctx, user)

	if len(ret) == 0 {
		panic("no return value specified for CreateUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *sessions.User) error); ok {
		r0 = rf(ctx, user)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// BasicAdminUsersORM_CreateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUser'
type BasicAdminUsersORM_CreateUser_Call struct {
	*mock.Call
}

// CreateUser is a helper method to define mock.On call
//   - ctx context.Context
//   - user *sessions.User
func (_e *BasicAdminUsersORM_Expecter) CreateUser(ctx interface{}, user interface{}) *BasicAdminUsersORM_CreateUser_Call {
	return &BasicAdminUsersORM_CreateUser_Call{Call: _e.mock.On("CreateUser", ctx, user)}
}

func (_c *BasicAdminUsersORM_CreateUser_Call) Run(run func(ctx context.Context, user *sessions.User)) *BasicAdminUsersORM_CreateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*sessions.User))
	})
	return _c
}

func (_c *BasicAdminUsersORM_CreateUser_Call) Return(_a0 error) *BasicAdminUsersORM_CreateUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *BasicAdminUsersORM_CreateUser_Call) RunAndReturn(run func(context.Context, *sessions.User) error) *BasicAdminUsersORM_CreateUser_Call {
	_c.Call.Return(run)
	return _c
}

// FindUser provides a mock function with given fields: ctx, email
func (_m *BasicAdminUsersORM) FindUser(ctx context.Context, email string) (sessions.User, error) {
	ret := _m.Called(ctx, email)

	if len(ret) == 0 {
		panic("no return value specified for FindUser")
	}

	var r0 sessions.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (sessions.User, error)); ok {
		return rf(ctx, email)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) sessions.User); ok {
		r0 = rf(ctx, email)
	} else {
		r0 = ret.Get(0).(sessions.User)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BasicAdminUsersORM_FindUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUser'
type BasicAdminUsersORM_FindUser_Call struct {
	*mock.Call
}

// FindUser is a helper method to define mock.On call
//   - ctx context.Context
//   - email string
func (_e *BasicAdminUsersORM_Expecter) FindUser(ctx interface{}, email interface{}) *BasicAdminUsersORM_FindUser_Call {
	return &BasicAdminUsersORM_FindUser_Call{Call: _e.mock.On("FindUser", ctx, email)}
}

func (_c *BasicAdminUsersORM_FindUser_Call) Run(run func(ctx context.Context, email string)) *BasicAdminUsersORM_FindUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *BasicAdminUsersORM_FindUser_Call) Return(_a0 sessions.User, _a1 error) *BasicAdminUsersORM_FindUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BasicAdminUsersORM_FindUser_Call) RunAndReturn(run func(context.Context, string) (sessions.User, error)) *BasicAdminUsersORM_FindUser_Call {
	_c.Call.Return(run)
	return _c
}

// ListUsers provides a mock function with given fields: ctx
func (_m *BasicAdminUsersORM) ListUsers(ctx context.Context) ([]sessions.User, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ListUsers")
	}

	var r0 []sessions.User
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]sessions.User, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []sessions.User); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]sessions.User)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// BasicAdminUsersORM_ListUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListUsers'
type BasicAdminUsersORM_ListUsers_Call struct {
	*mock.Call
}

// ListUsers is a helper method to define mock.On call
//   - ctx context.Context
func (_e *BasicAdminUsersORM_Expecter) ListUsers(ctx interface{}) *BasicAdminUsersORM_ListUsers_Call {
	return &BasicAdminUsersORM_ListUsers_Call{Call: _e.mock.On("ListUsers", ctx)}
}

func (_c *BasicAdminUsersORM_ListUsers_Call) Run(run func(ctx context.Context)) *BasicAdminUsersORM_ListUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *BasicAdminUsersORM_ListUsers_Call) Return(_a0 []sessions.User, _a1 error) *BasicAdminUsersORM_ListUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *BasicAdminUsersORM_ListUsers_Call) RunAndReturn(run func(context.Context) ([]sessions.User, error)) *BasicAdminUsersORM_ListUsers_Call {
	_c.Call.Return(run)
	return _c
}

// NewBasicAdminUsersORM creates a new instance of BasicAdminUsersORM. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBasicAdminUsersORM(t interface {
	mock.TestingT
	Cleanup(func())
}) *BasicAdminUsersORM {
	mock := &BasicAdminUsersORM{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
