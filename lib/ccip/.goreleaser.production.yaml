version: 2
project_name: chainlink
env:
    - IMG_PRE={{ if index .Env "IMAGE_PREFIX"  }}{{ .Env.IMAGE_PREFIX }}{{ else }}localhost:5001{{ end }}
    - IMG_TAG={{ if index .Env "IMAGE_TAG" }}{{ .Env.IMAGE_TAG }}{{ else }}develop{{ end }}
    - CGO_ENABLED=1
    - VERSION={{ if index .Env "CHAINLINK_VERSION" }}{{ .Env.CHAINLINK_VERSION }}{{ else }}v0.0.0-local{{ end }}
release:
    disable: "true"
builds:
    - targets:
        - go_first_class
      binary: chainlink
      hooks:
        post:
            - cmd: ./tools/bin/goreleaser_utils build_post_hook {{ dir .Path }}
      no_unique_dist_dir: "true"
      ldflags:
        - -s -w -r=$ORIGIN/libs
        - -X github.com/smartcontractkit/chainlink/v2/core/static.Sha={{ .FullCommit }}
        - |-
          -extldflags "-Wl,--dynamic-linker={{ if contains .Runtime.Goarch "amd64" -}}
          /lib64/ld-linux-x86-64.so.2
          {{- else if contains .Runtime.Goarch "arm64" -}}
          /lib/ld-linux-aarch64.so.1
          {{- end }}"
        - -X github.com/smartcontractkit/chainlink/v2/core/static.Version={{ .Env.VERSION }}
      flags:
        - -trimpath
        - -buildmode=pie
archives:
    - format: tar.gz
snapshot:
    version_template: '{{ .Env.VERSION }}-{{ .ShortCommit }}'
checksum:
    name_template: checksums.txt
dockers:
    - id: linux-amd64-chainlink
      goos: linux
      goarch: amd64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-amd64'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}-amd64'
      skip_push: '{{ contains .Tag "-ccip" }}'
      extra_files:
        - tmp/libs
      build_flag_templates:
        - --platform=linux/amd64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.version={{ .Env.VERSION }}
      use: buildx
    - id: linux-amd64-chainlink-plugins
      goos: linux
      goarch: amd64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins-amd64'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins-amd64'
      skip_push: '{{ contains .Tag "-ccip" }}'
      extra_files:
        - tmp/libs
        - tmp/plugins
      build_flag_templates:
        - --platform=linux/amd64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --build-arg=CL_MEDIAN_CMD=chainlink-feeds
        - --build-arg=CL_MERCURY_CMD=chainlink-mercury
        - --build-arg=CL_SOLANA_CMD=chainlink-solana
        - --build-arg=CL_STARKNET_CMD=chainlink-starknet
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.version={{ .Env.VERSION }}
      use: buildx
    - id: linux-arm64-chainlink
      goos: linux
      goarch: arm64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-arm64'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}-arm64'
      skip_push: '{{ contains .Tag "-ccip" }}'
      extra_files:
        - tmp/libs
      build_flag_templates:
        - --platform=linux/arm64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.version={{ .Env.VERSION }}
      use: buildx
    - id: linux-arm64-chainlink-plugins
      goos: linux
      goarch: arm64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins-arm64'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins-arm64'
      skip_push: '{{ contains .Tag "-ccip" }}'
      extra_files:
        - tmp/libs
        - tmp/plugins
      build_flag_templates:
        - --platform=linux/arm64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --build-arg=CL_MEDIAN_CMD=chainlink-feeds
        - --build-arg=CL_MERCURY_CMD=chainlink-mercury
        - --build-arg=CL_SOLANA_CMD=chainlink-solana
        - --build-arg=CL_STARKNET_CMD=chainlink-starknet
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.version={{ .Env.VERSION }}
      use: buildx
    - id: linux-amd64-ccip
      goos: linux
      goarch: amd64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-amd64'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}-amd64'
      skip_push: '{{ not (contains .Tag "-ccip") }}'
      extra_files:
        - tmp/libs
        - ccip/config
      build_flag_templates:
        - --platform=linux/amd64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --build-arg=CL_CHAIN_DEFAULTS=/chainlink/ccip-config
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.version={{ .Env.VERSION }}
      use: buildx
    - id: linux-amd64-ccip-plugins
      goos: linux
      goarch: amd64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins-amd64'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins-amd64'
      skip_push: '{{ not (contains .Tag "-ccip") }}'
      extra_files:
        - tmp/libs
        - tmp/plugins
        - ccip/config
      build_flag_templates:
        - --platform=linux/amd64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --build-arg=CL_CHAIN_DEFAULTS=/chainlink/ccip-config
        - --build-arg=CL_MEDIAN_CMD=chainlink-feeds
        - --build-arg=CL_MERCURY_CMD=chainlink-mercury
        - --build-arg=CL_SOLANA_CMD=chainlink-solana
        - --build-arg=CL_STARKNET_CMD=chainlink-starknet
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.version={{ .Env.VERSION }}
      use: buildx
    - id: linux-arm64-ccip
      goos: linux
      goarch: arm64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-arm64'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}-arm64'
      skip_push: '{{ not (contains .Tag "-ccip") }}'
      extra_files:
        - tmp/libs
        - ccip/config
      build_flag_templates:
        - --platform=linux/arm64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --build-arg=CL_CHAIN_DEFAULTS=/chainlink/ccip-config
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.version={{ .Env.VERSION }}
      use: buildx
    - id: linux-arm64-ccip-plugins
      goos: linux
      goarch: arm64
      dockerfile: core/chainlink.goreleaser.Dockerfile
      image_templates:
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins-arm64'
        - '{{ .Env.IMG_PRE }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins-arm64'
      skip_push: '{{ not (contains .Tag "-ccip") }}'
      extra_files:
        - tmp/libs
        - tmp/plugins
        - ccip/config
      build_flag_templates:
        - --platform=linux/arm64
        - --pull
        - --build-arg=CHAINLINK_USER=chainlink
        - --build-arg=COMMIT_SHA={{ .FullCommit }}
        - --build-arg=CL_CHAIN_DEFAULTS=/chainlink/ccip-config
        - --build-arg=CL_MEDIAN_CMD=chainlink-feeds
        - --build-arg=CL_MERCURY_CMD=chainlink-mercury
        - --build-arg=CL_SOLANA_CMD=chainlink-solana
        - --build-arg=CL_STARKNET_CMD=chainlink-starknet
        - --label=org.opencontainers.image.created={{ .Date }}
        - --label=org.opencontainers.image.description="node of the decentralized oracle network, bridging on and off-chain computation"
        - --label=org.opencontainers.image.licenses=MIT
        - --label=org.opencontainers.image.revision={{ .FullCommit }}
        - --label=org.opencontainers.image.source=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.title=chainlink
        - --label=org.opencontainers.image.url=https://github.com/smartcontractkit/chainlink
        - --label=org.opencontainers.image.version={{ .Env.VERSION }}
      use: buildx
docker_manifests:
    - id: tagged-chainlink-chainlink-experimental-goreleaser
      name_template: '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}'
      skip_push: '{{ contains .Tag "-ccip" }}'
      image_templates:
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-amd64'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-arm64'
    - id: sha-chainlink-chainlink-experimental-goreleaser
      name_template: '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}'
      skip_push: '{{ contains .Tag "-ccip" }}'
      image_templates:
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}-amd64'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}-arm64'
    - id: tagged-plugins-chainlink-chainlink-experimental-goreleaser
      name_template: '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins'
      skip_push: '{{ contains .Tag "-ccip" }}'
      image_templates:
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins-amd64'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins-arm64'
    - id: sha-plugins-chainlink-chainlink-experimental-goreleaser
      name_template: '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins'
      skip_push: '{{ contains .Tag "-ccip" }}'
      image_templates:
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins-amd64'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins-arm64'
    - id: tagged-chainlink-chainlink-ccip-experimental-goreleaser
      name_template: '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}'
      skip_push: '{{ not (contains .Tag "-ccip") }}'
      image_templates:
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-amd64'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-arm64'
    - id: sha-chainlink-chainlink-ccip-experimental-goreleaser
      name_template: '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}'
      skip_push: '{{ not (contains .Tag "-ccip") }}'
      image_templates:
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}-amd64'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}-arm64'
    - id: tagged-plugins-chainlink-chainlink-ccip-experimental-goreleaser
      name_template: '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins'
      skip_push: '{{ not (contains .Tag "-ccip") }}'
      image_templates:
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins-amd64'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:{{ .Env.IMG_TAG }}-plugins-arm64'
    - id: sha-plugins-chainlink-chainlink-ccip-experimental-goreleaser
      name_template: '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins'
      skip_push: '{{ not (contains .Tag "-ccip") }}'
      image_templates:
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins-amd64'
        - '{{ .Env.IMAGE_PREFIX }}/chainlink/chainlink-ccip-experimental-goreleaser:sha-{{ .ShortCommit }}-plugins-arm64'
changelog:
    filters:
        exclude:
            - '^docs:'
            - '^test:'
    sort: asc
before:
    hooks:
        - cmd: go mod tidy
        - cmd: ./tools/bin/goreleaser_utils before_hook
sboms:
    - artifacts: archive
partial:
    by: target
nightly:
    version_template: '{{ .Env.VERSION }}-{{ .Env.IMG_TAG }}'
