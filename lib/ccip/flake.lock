{"nodes": {"flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1710146030, "narHash": "sha256-SZ5L6eA7HJ/nmkzGG7/ISclqe6oZdOZTNoesiInkXPQ=", "owner": "numtide", "repo": "flake-utils", "rev": "b1d9ab70662946ef0850d488da1c9019f3a9752a", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "foundry": {"inputs": {"flake-utils": ["flake-utils"], "nixpkgs": "nixpkgs"}, "locked": {"lastModified": 1725354688, "narHash": "sha256-KHHFemVt6C/hbGoMzIq7cpxmjdp+KZVZaqbvx02aliY=", "owner": "shazow", "repo": "foundry.nix", "rev": "671672bd60a0d2e5f6757638fdf27e806df755a4", "type": "github"}, "original": {"owner": "shazow", "ref": "monthly", "repo": "foundry.nix", "type": "github"}}, "goreleaser-nur": {"inputs": {"nixpkgs": "nixpkgs_2"}, "locked": {"lastModified": 1726594821, "narHash": "sha256-ORImH+i+zOCMOdztNDqGDbyyFRC/FKmgbX8w50TNbQY=", "owner": "goreleaser", "repo": "nur", "rev": "bd2ee272ddfffbda9377a472131728e83ce2332d", "type": "github"}, "original": {"owner": "goreleaser", "repo": "nur", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1666753130, "narHash": "sha256-Wff1dGPFSneXJLI2c0kkdWTgxnQ416KE6X4KnFkgPYQ=", "owner": "NixOS", "repo": "nixpkgs", "rev": "f540aeda6f677354f1e7144ab04352f61aaa0118", "type": "github"}, "original": {"id": "nixpkgs", "type": "indirect"}}, "nixpkgs_2": {"locked": {"lastModified": 1624561540, "narHash": "sha256-izJ2PYZMGMsSkg+e7c9A1x3t/yOLT+qzUM6WQsc2tqo=", "owner": "NixOS", "repo": "nixpkgs", "rev": "c6a049a3d32293b24c0f894a840872cf67fd7c11", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixpkgs-unstable", "repo": "nixpkgs", "type": "github"}}, "nixpkgs_3": {"locked": {"lastModified": 1725103162, "narHash": "sha256-Ym04C5+qovuQDYL/rKWSR+WESseQBbNAe5DsXNx5trY=", "owner": "nixos", "repo": "nixpkgs", "rev": "12228ff1752d7b7624a54e9c1af4b222b3c1073b", "type": "github"}, "original": {"owner": "nixos", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "nur": {"locked": {"lastModified": 1727912806, "narHash": "sha256-LDOTTOGPaEP233gBrL8dnPGopc1lqcJFe0VB/+K/yWc=", "owner": "nix-community", "repo": "NUR", "rev": "9d9bcd30fec126b08b49020b7af08bc1aad66210", "type": "github"}, "original": {"owner": "nix-community", "repo": "NUR", "type": "github"}}, "root": {"inputs": {"flake-utils": "flake-utils", "foundry": "foundry", "goreleaser-nur": "goreleaser-nur", "nixpkgs": "nixpkgs_3", "nur": "nur"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}