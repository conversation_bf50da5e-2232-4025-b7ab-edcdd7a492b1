name: Setup Go
description: Setup Golang with efficient caching
inputs:
  only-modules:
    description: Set to 'true' to only cache modules
    default: "false"
  cache-version:
    description: Set this to cache bust
    default: "1"
  build-cache-version:
    description: For scoping build caches to certain matrix jobs, takes precedence over cache-version
    default: ""
  go-version-file:
    description: Set where the go version file is located at
    default: "go.mod"
  go-module-file:
    description: Set where the go module file is located at
    default: "go.sum"
  restore-module-cache-only:
    description: |
      Only restore the module cache, don't automatically update it.
      Leave the updating to go-mod-cache.yml.
    default: "true"
  restore-build-cache-only:
    description: |
      Only restore the build cache, don't automatically update/upload it.
    default: "false"

runs:
  using: composite
  steps:
    - name: Get Go Version
      shell: bash
      id: go-version
      run: |
        version=$(sed -ne '/^toolchain /s/^toolchain go//p' ${{ inputs.go-version-file }})
        if [ -z "$version" ]; then
          version=$(sed -ne '/^go /s/^go //p' ${{ inputs.go-version-file }})
          echo "Toolchain version not found in ${{ inputs.go-version-file }}, using go directive instead."
        fi
        echo "Go Version: $version"
        echo "version=$version" >> "$GITHUB_OUTPUT"

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ steps.go-version.outputs.version }}
        cache: false
        check-latest: true

    - name: Get branch name
      if: ${{ inputs.only-modules == 'false' }}
      id: branch-name
      uses: smartcontractkit/.github/actions/branch-names@branch-names/1.0.0

    - name: Set go cache keys
      shell: bash
      id: go-cache-dir
      run: |
        echo "gomodcache=$(go env GOMODCACHE)" >> $GITHUB_OUTPUT
        echo "gobuildcache=$(go env GOCACHE)" >> $GITHUB_OUTPUT

    - name: Set go module path
      id: go-module-path
      shell: bash
      run: echo "path=./${{ inputs.go-module-file }}" >> $GITHUB_OUTPUT

    # By default, restore the cache only.
    # If multiple jobs call actions/cache, then only one will get priority to create upon a cache miss.
    # We will only restore the cache by default (by calling actions/cache/restore) and let the
    # `go-mod-cache.yml` workflow handle the creation.
    - uses: actions/cache/restore@v4
      if: ${{ inputs.restore-module-cache-only == 'true' }}
      name: Cache Go Modules
      with:
        path: |
          ${{ steps.go-cache-dir.outputs.gomodcache }}
        # The lifetime of go modules is much higher than the build outputs, so we increase cache efficiency
        # here by not having the primary key contain the branch name
        key: ${{ runner.os }}-gomod-${{ inputs.cache-version }}-${{ hashFiles(steps.go-module-path.outputs.path) }}
        restore-keys: |
          ${{ runner.os }}-gomod-${{ inputs.cache-version }}-

    # If this is called, then it will create the cache entry upon a cache miss.
    # The cache is created after a cache miss, and after job completes successfully.
    - uses: actions/cache@v4
      if: ${{ inputs.restore-module-cache-only != 'true' }}
      name: Cache Go Modules
      with:
        path: |
          ${{ steps.go-cache-dir.outputs.gomodcache }}
        # The lifetime of go modules is much higher than the build outputs, so we increase cache efficiency
        # here by not having the primary key contain the branch name
        key: ${{ runner.os }}-gomod-${{ inputs.cache-version }}-${{ hashFiles(steps.go-module-path.outputs.path) }}
        restore-keys: |
          ${{ runner.os }}-gomod-${{ inputs.cache-version }}-

    - name: Build Cache Key
      id: build-cache-keys
      shell: bash
      env:
        CACHE_VERSION: ${{ inputs.build-cache-version || inputs.cache-version }}
        CURRENT_BRANCH: ${{ steps.branch-name.outputs.current_branch }}
        RUNNER_OS: ${{ runner.os }}
      # Build the cache keys here so that we can guarantee that the cache keys are the same
      # across the conditional steps below.
      # --
      # We use a SHA and the branch name in the cache key, so that build caches can be "upserted".
      # As a PR progresses, it will continue to use a cache that is relevant to a recent commit.
      #
      # If the cache is only created once and reused over the life of a PR, then it will become stale over time.
      # As the cache would be populated on the first run, and then never updated again.
      run: |
        SHORT_SHA=$(echo $GITHUB_SHA | cut -c1-7)

        DEVELOP_KEY_PREFIX="${RUNNER_OS}-gobuild-${CACHE_VERSION}-develop-"
        KEY_PREFIX="${RUNNER_OS}-gobuild-${CACHE_VERSION}-${CURRENT_BRANCH}-"
        PRIMARY_KEY="${KEY_PREFIX}${SHORT_SHA}"

        echo "primary-key=${PRIMARY_KEY}" >> $GITHUB_OUTPUT
        echo "key-prefix=${KEY_PREFIX}" >> $GITHUB_OUTPUT
        echo "develop-key-prefix=${DEVELOP_KEY_PREFIX}" >> $GITHUB_OUTPUT

    - uses: actions/cache/restore@v4
      name: Cache Go Build Outputs (restore)
      # For certain events, we don't necessarily want to create a build cache, but we will benefit from restoring from one.
      if: ${{ inputs.only-modules == 'false' && (github.event_name == 'merge_group' || inputs.restore-build-cache-only == 'true') }}
      with:
        path: |
          ${{ steps.go-cache-dir.outputs.gobuildcache }}
        key: ${{ steps.build-cache-keys.outputs.primary-key }}
        restore-keys: |
          ${{ steps.build-cache-keys.outputs.key-prefix }}
          ${{ steps.build-cache-keys.outputs.develop-key-prefix }}

    - uses: actions/cache@v4
      # don't save cache on merge queue events
      if: ${{ inputs.only-modules == 'false' && (github.event_name != 'merge_group' && inputs.restore-build-cache-only == 'false') }}
      id: build-cache
      name: Cache Go Build Outputs
      with:
        path: |
          ${{ steps.go-cache-dir.outputs.gobuildcache }}
        key: ${{ steps.build-cache-keys.outputs.primary-key }}
        restore-keys: |
          ${{ steps.build-cache-keys.outputs.key-prefix }}
          ${{ steps.build-cache-keys.outputs.develop-key-prefix }}
