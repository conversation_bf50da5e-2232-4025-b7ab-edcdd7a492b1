name: Build and Publish with Goreleaser
description: A composite action that allows building and publishing signed chainlink artifacts (binaries + images)
inputs:
  goreleaser-version:
    description: The goreleaser version
    default: "~> v2"
    required: false
  goreleaser-key:
    description: The goreleaser key
    required: false
  # publishing inputs
  docker-registry:
    description: The docker registry
    default: localhost:5001
    required: false
  docker-image-tag:
    description: The docker image tag
    default: develop
    required: false
  # goreleaser inputs
  goreleaser-release-type:
    description: The goreleaser release type, it can be either "nightly", "merge", "snapshot", "release"
    default: "snapshot"
    required: false
  goreleaser-config:
    description: "The goreleaser configuration yaml"
    default: ".goreleaser.yaml"
    required: false
  # other inputs
  enable-debug:
    description: |
      Enable debug information for the run (true/false). This includes
      buildkit debug information, and goreleaser debug, etc.
    required: false
    default: "${{ runner.debug == '1' }}"

runs:
  using: composite
  steps:
    # See https://github.com/orgs/community/discussions/25678#discussioncomment-5242449
    - name: Delete unused tools to free up space
      shell: bash
      run: |
        sudo rm -rf /opt/hostedtoolcache/CodeQL
        sudo rm -rf /opt/hostedtoolcache/PyPy
        sudo rm -rf /opt/hostedtoolcache/Python
        sudo rm -rf /opt/hostedtoolcache/Ruby
        sudo rm -rf /opt/hostedtoolcache/Java_*

    - # We need QEMU to test the cross architecture builds after they're built.
      name: Set up QEMU
      uses: docker/setup-qemu-action@4574d27a4764455b42196d70a065bc6853246a25 # v3.4.0

    - name: Setup docker buildx
      uses: docker/setup-buildx-action@f7ce87c1d6bead3e36075b2ce75da1f6cc28aaca # v3.9.0
      with:
        buildkitd-flags: ${{ inputs.enable-debug == 'true' && '--debug' || '' }}

    - name: Set up Go
      uses: ./.github/actions/setup-go
      with:
        go-version-file: 'go.mod'
        only-modules: 'true'

    - name: Setup goreleaser
      uses: goreleaser/goreleaser-action@90a3faa9d0182683851fbfa97ca1a2cb983bfca3 # v6.2.1
      with:
        distribution: goreleaser-pro
        install-only: true
        version: ${{ inputs.goreleaser-version }}
      env:
        GORELEASER_KEY: ${{ inputs.goreleaser-key }}

    - name: Login to docker registry
      uses: docker/login-action@9780b0c442fbb1117ed29e0efdff1e18412f7567 # v3.3.0
      with:
        registry: ${{ inputs.docker-registry }}

    - name: Install syft
      uses: anchore/sbom-action/download-syft@fc46e51fd3cb168ffb36c6d1915723c47db58abb # v0.17.7

    - name: Run goreleaser release
      shell: bash
      env:
        GORELEASER_CONFIG: ${{ inputs.goreleaser-config }}
        RELEASE_TYPE: ${{ inputs.goreleaser-release-type }}
        IMAGE_PREFIX: ${{ inputs.docker-registry }}
        IMAGE_TAG: ${{ inputs.docker-image-tag }}
        GORELEASER_KEY: ${{ inputs.goreleaser-key }}
        GITHUB_TOKEN: ${{ github.token }}
        DEBUG: ${{ inputs.enable-debug }}
      run: |
        # https://github.com/orgs/community/discussions/24950
        ${GITHUB_ACTION_PATH}/release.js
