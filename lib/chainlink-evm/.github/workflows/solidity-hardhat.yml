name: Solidity-Hardhat

on:
  merge_group:
  push:

env:
  NODE_OPTIONS: --max_old_space_size=8192

defaults:
  run:
    shell: bash

jobs:
  changes:
    name: Detect changes
    runs-on: ubuntu-latest
    outputs:
      changes: ${{ steps.changes.outputs.src }}
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
        id: changes
        with:
          filters: |
            src:
              - 'contracts/src/!(v0.8/(functions|keystone|l2ep|llo-feeds|vrf|workflow)/**)/**/*'
              - 'contracts/test/**/*'
              - 'contracts/package.json'
              - 'contracts/pnpm-lock.yaml'
              - 'contracts/hardhat.config.ts'
              - '.github/workflows/solidity-hardhat.yml'

  hardhat-test:
    needs: [changes]
    if: needs.changes.outputs.changes == 'true'
    name: Solidity ${{ fromJSON('["(skipped)", ""]')[needs.changes.outputs.changes == 'true'] }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Setup NodeJS
        uses: ./.github/actions/setup-nodejs
      - name: Setup Hardhat
        uses: ./.github/actions/setup-hardhat
        with:
          namespace: coverage
      - name: Run tests
        working-directory: contracts
        run: pnpm test

  solidity:
    needs: [changes, hardhat-test]
    name: Solidity
    runs-on: ubuntu-latest
    if: always()
    steps:
      - run: echo 'Solidity tests finished!'
      - name: Check test results
        run: |
          if [[ "${{ needs.changes.result }}" = "failure" || "${{ needs.solidity-splits.result }}" = "failure" ]]; then
            echo "One or more changes / solidity-splits jobs failed"
            exit 1
          else
            echo "All test jobs passed successfully"
          fi
