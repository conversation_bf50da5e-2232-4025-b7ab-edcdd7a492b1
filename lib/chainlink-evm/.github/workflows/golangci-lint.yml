name: Run golangci-lint

on: [pull_request]

jobs:
  golangci-lint:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
      actions: read
    steps:
      - name: Setup tar default options
        shell: bash
        # Do not overwrite existing files when extracting files from a cache archive.
        # Since actions/cache does not support this option, we set it here as a default.
        run: echo "TAR_OPTIONS=--skip-old-files" >> $GITHUB_ENV

      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          cache-dependency-path: go.sum
          go-version-file: go.mod

      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@38e1018663fa5173f3968ea0777460d3de38f256 # v5.3.0
        with:
          args: --config=${{ github.workspace }}/.golangci.yml
          only-new-issues: true
          version: v1.64.8
          working-directory: pkg