// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package destination_verifier

import (
	"errors"
	"fmt"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
	"github.com/smartcontractkit/chainlink-evm/gethwrappers/generated"
)

var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

type CommonAddressAndWeight struct {
	Addr   common.Address
	Weight uint64
}

var DestinationVerifierMetaData = &bind.MetaData{
	ABI: "[{\"type\":\"constructor\",\"inputs\":[{\"name\":\"verifierProxy\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"acceptOwnership\",\"inputs\":[],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"i_verifierProxy\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"contractIDestinationVerifierProxy\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"owner\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"removeLatestConfig\",\"inputs\":[],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"s_accessController\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"s_feeManager\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"setAccessController\",\"inputs\":[{\"name\":\"accessController\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"setConfig\",\"inputs\":[{\"name\":\"signers\",\"type\":\"address[]\",\"internalType\":\"address[]\"},{\"name\":\"f\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"recipientAddressesAndWeights\",\"type\":\"tuple[]\",\"internalType\":\"structCommon.AddressAndWeight[]\",\"components\":[{\"name\":\"addr\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"weight\",\"type\":\"uint64\",\"internalType\":\"uint64\"}]}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"setConfigActive\",\"inputs\":[{\"name\":\"donConfigIndex\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"isActive\",\"type\":\"bool\",\"internalType\":\"bool\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"setConfigWithActivationTime\",\"inputs\":[{\"name\":\"signers\",\"type\":\"address[]\",\"internalType\":\"address[]\"},{\"name\":\"f\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"recipientAddressesAndWeights\",\"type\":\"tuple[]\",\"internalType\":\"structCommon.AddressAndWeight[]\",\"components\":[{\"name\":\"addr\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"weight\",\"type\":\"uint64\",\"internalType\":\"uint64\"}]},{\"name\":\"activationTime\",\"type\":\"uint32\",\"internalType\":\"uint32\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"setFeeManager\",\"inputs\":[{\"name\":\"feeManager\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"supportsInterface\",\"inputs\":[{\"name\":\"interfaceId\",\"type\":\"bytes4\",\"internalType\":\"bytes4\"}],\"outputs\":[{\"name\":\"\",\"type\":\"bool\",\"internalType\":\"bool\"}],\"stateMutability\":\"pure\"},{\"type\":\"function\",\"name\":\"transferOwnership\",\"inputs\":[{\"name\":\"to\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"typeAndVersion\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"string\",\"internalType\":\"string\"}],\"stateMutability\":\"pure\"},{\"type\":\"function\",\"name\":\"verify\",\"inputs\":[{\"name\":\"signedReport\",\"type\":\"bytes\",\"internalType\":\"bytes\"},{\"name\":\"parameterPayload\",\"type\":\"bytes\",\"internalType\":\"bytes\"},{\"name\":\"sender\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[{\"name\":\"\",\"type\":\"bytes\",\"internalType\":\"bytes\"}],\"stateMutability\":\"payable\"},{\"type\":\"function\",\"name\":\"verifyBulk\",\"inputs\":[{\"name\":\"signedReports\",\"type\":\"bytes[]\",\"internalType\":\"bytes[]\"},{\"name\":\"parameterPayload\",\"type\":\"bytes\",\"internalType\":\"bytes\"},{\"name\":\"sender\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[{\"name\":\"\",\"type\":\"bytes[]\",\"internalType\":\"bytes[]\"}],\"stateMutability\":\"payable\"},{\"type\":\"event\",\"name\":\"AccessControllerSet\",\"inputs\":[{\"name\":\"oldAccessController\",\"type\":\"address\",\"indexed\":false,\"internalType\":\"address\"},{\"name\":\"newAccessController\",\"type\":\"address\",\"indexed\":false,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"ConfigActivated\",\"inputs\":[{\"name\":\"donConfigId\",\"type\":\"bytes24\",\"indexed\":false,\"internalType\":\"bytes24\"},{\"name\":\"isActive\",\"type\":\"bool\",\"indexed\":false,\"internalType\":\"bool\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"ConfigRemoved\",\"inputs\":[{\"name\":\"donConfigId\",\"type\":\"bytes24\",\"indexed\":false,\"internalType\":\"bytes24\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"ConfigSet\",\"inputs\":[{\"name\":\"donConfigId\",\"type\":\"bytes24\",\"indexed\":true,\"internalType\":\"bytes24\"},{\"name\":\"signers\",\"type\":\"address[]\",\"indexed\":false,\"internalType\":\"address[]\"},{\"name\":\"f\",\"type\":\"uint8\",\"indexed\":false,\"internalType\":\"uint8\"},{\"name\":\"recipientAddressesAndWeights\",\"type\":\"tuple[]\",\"indexed\":false,\"internalType\":\"structCommon.AddressAndWeight[]\",\"components\":[{\"name\":\"addr\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"weight\",\"type\":\"uint64\",\"internalType\":\"uint64\"}]},{\"name\":\"donConfigIndex\",\"type\":\"uint16\",\"indexed\":false,\"internalType\":\"uint16\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"FeeManagerSet\",\"inputs\":[{\"name\":\"oldFeeManager\",\"type\":\"address\",\"indexed\":false,\"internalType\":\"address\"},{\"name\":\"newFeeManager\",\"type\":\"address\",\"indexed\":false,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"OwnershipTransferRequested\",\"inputs\":[{\"name\":\"from\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"OwnershipTransferred\",\"inputs\":[{\"name\":\"from\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"to\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"ReportVerified\",\"inputs\":[{\"name\":\"feedId\",\"type\":\"bytes32\",\"indexed\":true,\"internalType\":\"bytes32\"},{\"name\":\"requester\",\"type\":\"address\",\"indexed\":false,\"internalType\":\"address\"}],\"anonymous\":false},{\"type\":\"error\",\"name\":\"AccessForbidden\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"BadActivationTime\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"BadVerification\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"DonConfigAlreadyExists\",\"inputs\":[{\"name\":\"donConfigId\",\"type\":\"bytes24\",\"internalType\":\"bytes24\"}]},{\"type\":\"error\",\"name\":\"DonConfigDoesNotExist\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"ExcessSigners\",\"inputs\":[{\"name\":\"numSigners\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"maxSigners\",\"type\":\"uint256\",\"internalType\":\"uint256\"}]},{\"type\":\"error\",\"name\":\"FaultToleranceMustBePositive\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"FeeManagerInvalid\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"InsufficientSigners\",\"inputs\":[{\"name\":\"numSigners\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"minSigners\",\"type\":\"uint256\",\"internalType\":\"uint256\"}]},{\"type\":\"error\",\"name\":\"MismatchedSignatures\",\"inputs\":[{\"name\":\"rsLength\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"ssLength\",\"type\":\"uint256\",\"internalType\":\"uint256\"}]},{\"type\":\"error\",\"name\":\"NoSigners\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"NonUniqueSignatures\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"ZeroAddress\",\"inputs\":[]}]",
	Bin: "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",
}

var DestinationVerifierABI = DestinationVerifierMetaData.ABI

var DestinationVerifierBin = DestinationVerifierMetaData.Bin

func DeployDestinationVerifier(auth *bind.TransactOpts, backend bind.ContractBackend, verifierProxy common.Address) (common.Address, *types.Transaction, *DestinationVerifier, error) {
	parsed, err := DestinationVerifierMetaData.GetAbi()
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	if parsed == nil {
		return common.Address{}, nil, nil, errors.New("GetABI returned nil")
	}

	address, tx, contract, err := bind.DeployContract(auth, *parsed, common.FromHex(DestinationVerifierBin), backend, verifierProxy)
	if err != nil {
		return common.Address{}, nil, nil, err
	}
	return address, tx, &DestinationVerifier{address: address, abi: *parsed, DestinationVerifierCaller: DestinationVerifierCaller{contract: contract}, DestinationVerifierTransactor: DestinationVerifierTransactor{contract: contract}, DestinationVerifierFilterer: DestinationVerifierFilterer{contract: contract}}, nil
}

type DestinationVerifier struct {
	address common.Address
	abi     abi.ABI
	DestinationVerifierCaller
	DestinationVerifierTransactor
	DestinationVerifierFilterer
}

type DestinationVerifierCaller struct {
	contract *bind.BoundContract
}

type DestinationVerifierTransactor struct {
	contract *bind.BoundContract
}

type DestinationVerifierFilterer struct {
	contract *bind.BoundContract
}

type DestinationVerifierSession struct {
	Contract     *DestinationVerifier
	CallOpts     bind.CallOpts
	TransactOpts bind.TransactOpts
}

type DestinationVerifierCallerSession struct {
	Contract *DestinationVerifierCaller
	CallOpts bind.CallOpts
}

type DestinationVerifierTransactorSession struct {
	Contract     *DestinationVerifierTransactor
	TransactOpts bind.TransactOpts
}

type DestinationVerifierRaw struct {
	Contract *DestinationVerifier
}

type DestinationVerifierCallerRaw struct {
	Contract *DestinationVerifierCaller
}

type DestinationVerifierTransactorRaw struct {
	Contract *DestinationVerifierTransactor
}

func NewDestinationVerifier(address common.Address, backend bind.ContractBackend) (*DestinationVerifier, error) {
	abi, err := abi.JSON(strings.NewReader(DestinationVerifierABI))
	if err != nil {
		return nil, err
	}
	contract, err := bindDestinationVerifier(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &DestinationVerifier{address: address, abi: abi, DestinationVerifierCaller: DestinationVerifierCaller{contract: contract}, DestinationVerifierTransactor: DestinationVerifierTransactor{contract: contract}, DestinationVerifierFilterer: DestinationVerifierFilterer{contract: contract}}, nil
}

func NewDestinationVerifierCaller(address common.Address, caller bind.ContractCaller) (*DestinationVerifierCaller, error) {
	contract, err := bindDestinationVerifier(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierCaller{contract: contract}, nil
}

func NewDestinationVerifierTransactor(address common.Address, transactor bind.ContractTransactor) (*DestinationVerifierTransactor, error) {
	contract, err := bindDestinationVerifier(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierTransactor{contract: contract}, nil
}

func NewDestinationVerifierFilterer(address common.Address, filterer bind.ContractFilterer) (*DestinationVerifierFilterer, error) {
	contract, err := bindDestinationVerifier(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierFilterer{contract: contract}, nil
}

func bindDestinationVerifier(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := DestinationVerifierMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

func (_DestinationVerifier *DestinationVerifierRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _DestinationVerifier.Contract.DestinationVerifierCaller.contract.Call(opts, result, method, params...)
}

func (_DestinationVerifier *DestinationVerifierRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.DestinationVerifierTransactor.contract.Transfer(opts)
}

func (_DestinationVerifier *DestinationVerifierRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.DestinationVerifierTransactor.contract.Transact(opts, method, params...)
}

func (_DestinationVerifier *DestinationVerifierCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _DestinationVerifier.Contract.contract.Call(opts, result, method, params...)
}

func (_DestinationVerifier *DestinationVerifierTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.contract.Transfer(opts)
}

func (_DestinationVerifier *DestinationVerifierTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.contract.Transact(opts, method, params...)
}

func (_DestinationVerifier *DestinationVerifierCaller) IVerifierProxy(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _DestinationVerifier.contract.Call(opts, &out, "i_verifierProxy")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_DestinationVerifier *DestinationVerifierSession) IVerifierProxy() (common.Address, error) {
	return _DestinationVerifier.Contract.IVerifierProxy(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierCallerSession) IVerifierProxy() (common.Address, error) {
	return _DestinationVerifier.Contract.IVerifierProxy(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _DestinationVerifier.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_DestinationVerifier *DestinationVerifierSession) Owner() (common.Address, error) {
	return _DestinationVerifier.Contract.Owner(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierCallerSession) Owner() (common.Address, error) {
	return _DestinationVerifier.Contract.Owner(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierCaller) SAccessController(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _DestinationVerifier.contract.Call(opts, &out, "s_accessController")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_DestinationVerifier *DestinationVerifierSession) SAccessController() (common.Address, error) {
	return _DestinationVerifier.Contract.SAccessController(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierCallerSession) SAccessController() (common.Address, error) {
	return _DestinationVerifier.Contract.SAccessController(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierCaller) SFeeManager(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _DestinationVerifier.contract.Call(opts, &out, "s_feeManager")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

func (_DestinationVerifier *DestinationVerifierSession) SFeeManager() (common.Address, error) {
	return _DestinationVerifier.Contract.SFeeManager(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierCallerSession) SFeeManager() (common.Address, error) {
	return _DestinationVerifier.Contract.SFeeManager(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierCaller) SupportsInterface(opts *bind.CallOpts, interfaceId [4]byte) (bool, error) {
	var out []interface{}
	err := _DestinationVerifier.contract.Call(opts, &out, "supportsInterface", interfaceId)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

func (_DestinationVerifier *DestinationVerifierSession) SupportsInterface(interfaceId [4]byte) (bool, error) {
	return _DestinationVerifier.Contract.SupportsInterface(&_DestinationVerifier.CallOpts, interfaceId)
}

func (_DestinationVerifier *DestinationVerifierCallerSession) SupportsInterface(interfaceId [4]byte) (bool, error) {
	return _DestinationVerifier.Contract.SupportsInterface(&_DestinationVerifier.CallOpts, interfaceId)
}

func (_DestinationVerifier *DestinationVerifierCaller) TypeAndVersion(opts *bind.CallOpts) (string, error) {
	var out []interface{}
	err := _DestinationVerifier.contract.Call(opts, &out, "typeAndVersion")

	if err != nil {
		return *new(string), err
	}

	out0 := *abi.ConvertType(out[0], new(string)).(*string)

	return out0, err

}

func (_DestinationVerifier *DestinationVerifierSession) TypeAndVersion() (string, error) {
	return _DestinationVerifier.Contract.TypeAndVersion(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierCallerSession) TypeAndVersion() (string, error) {
	return _DestinationVerifier.Contract.TypeAndVersion(&_DestinationVerifier.CallOpts)
}

func (_DestinationVerifier *DestinationVerifierTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "acceptOwnership")
}

func (_DestinationVerifier *DestinationVerifierSession) AcceptOwnership() (*types.Transaction, error) {
	return _DestinationVerifier.Contract.AcceptOwnership(&_DestinationVerifier.TransactOpts)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _DestinationVerifier.Contract.AcceptOwnership(&_DestinationVerifier.TransactOpts)
}

func (_DestinationVerifier *DestinationVerifierTransactor) RemoveLatestConfig(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "removeLatestConfig")
}

func (_DestinationVerifier *DestinationVerifierSession) RemoveLatestConfig() (*types.Transaction, error) {
	return _DestinationVerifier.Contract.RemoveLatestConfig(&_DestinationVerifier.TransactOpts)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) RemoveLatestConfig() (*types.Transaction, error) {
	return _DestinationVerifier.Contract.RemoveLatestConfig(&_DestinationVerifier.TransactOpts)
}

func (_DestinationVerifier *DestinationVerifierTransactor) SetAccessController(opts *bind.TransactOpts, accessController common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "setAccessController", accessController)
}

func (_DestinationVerifier *DestinationVerifierSession) SetAccessController(accessController common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetAccessController(&_DestinationVerifier.TransactOpts, accessController)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) SetAccessController(accessController common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetAccessController(&_DestinationVerifier.TransactOpts, accessController)
}

func (_DestinationVerifier *DestinationVerifierTransactor) SetConfig(opts *bind.TransactOpts, signers []common.Address, f uint8, recipientAddressesAndWeights []CommonAddressAndWeight) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "setConfig", signers, f, recipientAddressesAndWeights)
}

func (_DestinationVerifier *DestinationVerifierSession) SetConfig(signers []common.Address, f uint8, recipientAddressesAndWeights []CommonAddressAndWeight) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetConfig(&_DestinationVerifier.TransactOpts, signers, f, recipientAddressesAndWeights)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) SetConfig(signers []common.Address, f uint8, recipientAddressesAndWeights []CommonAddressAndWeight) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetConfig(&_DestinationVerifier.TransactOpts, signers, f, recipientAddressesAndWeights)
}

func (_DestinationVerifier *DestinationVerifierTransactor) SetConfigActive(opts *bind.TransactOpts, donConfigIndex *big.Int, isActive bool) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "setConfigActive", donConfigIndex, isActive)
}

func (_DestinationVerifier *DestinationVerifierSession) SetConfigActive(donConfigIndex *big.Int, isActive bool) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetConfigActive(&_DestinationVerifier.TransactOpts, donConfigIndex, isActive)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) SetConfigActive(donConfigIndex *big.Int, isActive bool) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetConfigActive(&_DestinationVerifier.TransactOpts, donConfigIndex, isActive)
}

func (_DestinationVerifier *DestinationVerifierTransactor) SetConfigWithActivationTime(opts *bind.TransactOpts, signers []common.Address, f uint8, recipientAddressesAndWeights []CommonAddressAndWeight, activationTime uint32) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "setConfigWithActivationTime", signers, f, recipientAddressesAndWeights, activationTime)
}

func (_DestinationVerifier *DestinationVerifierSession) SetConfigWithActivationTime(signers []common.Address, f uint8, recipientAddressesAndWeights []CommonAddressAndWeight, activationTime uint32) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetConfigWithActivationTime(&_DestinationVerifier.TransactOpts, signers, f, recipientAddressesAndWeights, activationTime)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) SetConfigWithActivationTime(signers []common.Address, f uint8, recipientAddressesAndWeights []CommonAddressAndWeight, activationTime uint32) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetConfigWithActivationTime(&_DestinationVerifier.TransactOpts, signers, f, recipientAddressesAndWeights, activationTime)
}

func (_DestinationVerifier *DestinationVerifierTransactor) SetFeeManager(opts *bind.TransactOpts, feeManager common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "setFeeManager", feeManager)
}

func (_DestinationVerifier *DestinationVerifierSession) SetFeeManager(feeManager common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetFeeManager(&_DestinationVerifier.TransactOpts, feeManager)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) SetFeeManager(feeManager common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.SetFeeManager(&_DestinationVerifier.TransactOpts, feeManager)
}

func (_DestinationVerifier *DestinationVerifierTransactor) TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "transferOwnership", to)
}

func (_DestinationVerifier *DestinationVerifierSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.TransferOwnership(&_DestinationVerifier.TransactOpts, to)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) TransferOwnership(to common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.TransferOwnership(&_DestinationVerifier.TransactOpts, to)
}

func (_DestinationVerifier *DestinationVerifierTransactor) Verify(opts *bind.TransactOpts, signedReport []byte, parameterPayload []byte, sender common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "verify", signedReport, parameterPayload, sender)
}

func (_DestinationVerifier *DestinationVerifierSession) Verify(signedReport []byte, parameterPayload []byte, sender common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.Verify(&_DestinationVerifier.TransactOpts, signedReport, parameterPayload, sender)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) Verify(signedReport []byte, parameterPayload []byte, sender common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.Verify(&_DestinationVerifier.TransactOpts, signedReport, parameterPayload, sender)
}

func (_DestinationVerifier *DestinationVerifierTransactor) VerifyBulk(opts *bind.TransactOpts, signedReports [][]byte, parameterPayload []byte, sender common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.contract.Transact(opts, "verifyBulk", signedReports, parameterPayload, sender)
}

func (_DestinationVerifier *DestinationVerifierSession) VerifyBulk(signedReports [][]byte, parameterPayload []byte, sender common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.VerifyBulk(&_DestinationVerifier.TransactOpts, signedReports, parameterPayload, sender)
}

func (_DestinationVerifier *DestinationVerifierTransactorSession) VerifyBulk(signedReports [][]byte, parameterPayload []byte, sender common.Address) (*types.Transaction, error) {
	return _DestinationVerifier.Contract.VerifyBulk(&_DestinationVerifier.TransactOpts, signedReports, parameterPayload, sender)
}

type DestinationVerifierAccessControllerSetIterator struct {
	Event *DestinationVerifierAccessControllerSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *DestinationVerifierAccessControllerSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(DestinationVerifierAccessControllerSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(DestinationVerifierAccessControllerSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *DestinationVerifierAccessControllerSetIterator) Error() error {
	return it.fail
}

func (it *DestinationVerifierAccessControllerSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type DestinationVerifierAccessControllerSet struct {
	OldAccessController common.Address
	NewAccessController common.Address
	Raw                 types.Log
}

func (_DestinationVerifier *DestinationVerifierFilterer) FilterAccessControllerSet(opts *bind.FilterOpts) (*DestinationVerifierAccessControllerSetIterator, error) {

	logs, sub, err := _DestinationVerifier.contract.FilterLogs(opts, "AccessControllerSet")
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierAccessControllerSetIterator{contract: _DestinationVerifier.contract, event: "AccessControllerSet", logs: logs, sub: sub}, nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) WatchAccessControllerSet(opts *bind.WatchOpts, sink chan<- *DestinationVerifierAccessControllerSet) (event.Subscription, error) {

	logs, sub, err := _DestinationVerifier.contract.WatchLogs(opts, "AccessControllerSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(DestinationVerifierAccessControllerSet)
				if err := _DestinationVerifier.contract.UnpackLog(event, "AccessControllerSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) ParseAccessControllerSet(log types.Log) (*DestinationVerifierAccessControllerSet, error) {
	event := new(DestinationVerifierAccessControllerSet)
	if err := _DestinationVerifier.contract.UnpackLog(event, "AccessControllerSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type DestinationVerifierConfigActivatedIterator struct {
	Event *DestinationVerifierConfigActivated

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *DestinationVerifierConfigActivatedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(DestinationVerifierConfigActivated)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(DestinationVerifierConfigActivated)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *DestinationVerifierConfigActivatedIterator) Error() error {
	return it.fail
}

func (it *DestinationVerifierConfigActivatedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type DestinationVerifierConfigActivated struct {
	DonConfigId [24]byte
	IsActive    bool
	Raw         types.Log
}

func (_DestinationVerifier *DestinationVerifierFilterer) FilterConfigActivated(opts *bind.FilterOpts) (*DestinationVerifierConfigActivatedIterator, error) {

	logs, sub, err := _DestinationVerifier.contract.FilterLogs(opts, "ConfigActivated")
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierConfigActivatedIterator{contract: _DestinationVerifier.contract, event: "ConfigActivated", logs: logs, sub: sub}, nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) WatchConfigActivated(opts *bind.WatchOpts, sink chan<- *DestinationVerifierConfigActivated) (event.Subscription, error) {

	logs, sub, err := _DestinationVerifier.contract.WatchLogs(opts, "ConfigActivated")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(DestinationVerifierConfigActivated)
				if err := _DestinationVerifier.contract.UnpackLog(event, "ConfigActivated", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) ParseConfigActivated(log types.Log) (*DestinationVerifierConfigActivated, error) {
	event := new(DestinationVerifierConfigActivated)
	if err := _DestinationVerifier.contract.UnpackLog(event, "ConfigActivated", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type DestinationVerifierConfigRemovedIterator struct {
	Event *DestinationVerifierConfigRemoved

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *DestinationVerifierConfigRemovedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(DestinationVerifierConfigRemoved)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(DestinationVerifierConfigRemoved)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *DestinationVerifierConfigRemovedIterator) Error() error {
	return it.fail
}

func (it *DestinationVerifierConfigRemovedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type DestinationVerifierConfigRemoved struct {
	DonConfigId [24]byte
	Raw         types.Log
}

func (_DestinationVerifier *DestinationVerifierFilterer) FilterConfigRemoved(opts *bind.FilterOpts) (*DestinationVerifierConfigRemovedIterator, error) {

	logs, sub, err := _DestinationVerifier.contract.FilterLogs(opts, "ConfigRemoved")
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierConfigRemovedIterator{contract: _DestinationVerifier.contract, event: "ConfigRemoved", logs: logs, sub: sub}, nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) WatchConfigRemoved(opts *bind.WatchOpts, sink chan<- *DestinationVerifierConfigRemoved) (event.Subscription, error) {

	logs, sub, err := _DestinationVerifier.contract.WatchLogs(opts, "ConfigRemoved")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(DestinationVerifierConfigRemoved)
				if err := _DestinationVerifier.contract.UnpackLog(event, "ConfigRemoved", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) ParseConfigRemoved(log types.Log) (*DestinationVerifierConfigRemoved, error) {
	event := new(DestinationVerifierConfigRemoved)
	if err := _DestinationVerifier.contract.UnpackLog(event, "ConfigRemoved", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type DestinationVerifierConfigSetIterator struct {
	Event *DestinationVerifierConfigSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *DestinationVerifierConfigSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(DestinationVerifierConfigSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(DestinationVerifierConfigSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *DestinationVerifierConfigSetIterator) Error() error {
	return it.fail
}

func (it *DestinationVerifierConfigSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type DestinationVerifierConfigSet struct {
	DonConfigId                  [24]byte
	Signers                      []common.Address
	F                            uint8
	RecipientAddressesAndWeights []CommonAddressAndWeight
	DonConfigIndex               uint16
	Raw                          types.Log
}

func (_DestinationVerifier *DestinationVerifierFilterer) FilterConfigSet(opts *bind.FilterOpts, donConfigId [][24]byte) (*DestinationVerifierConfigSetIterator, error) {

	var donConfigIdRule []interface{}
	for _, donConfigIdItem := range donConfigId {
		donConfigIdRule = append(donConfigIdRule, donConfigIdItem)
	}

	logs, sub, err := _DestinationVerifier.contract.FilterLogs(opts, "ConfigSet", donConfigIdRule)
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierConfigSetIterator{contract: _DestinationVerifier.contract, event: "ConfigSet", logs: logs, sub: sub}, nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) WatchConfigSet(opts *bind.WatchOpts, sink chan<- *DestinationVerifierConfigSet, donConfigId [][24]byte) (event.Subscription, error) {

	var donConfigIdRule []interface{}
	for _, donConfigIdItem := range donConfigId {
		donConfigIdRule = append(donConfigIdRule, donConfigIdItem)
	}

	logs, sub, err := _DestinationVerifier.contract.WatchLogs(opts, "ConfigSet", donConfigIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(DestinationVerifierConfigSet)
				if err := _DestinationVerifier.contract.UnpackLog(event, "ConfigSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) ParseConfigSet(log types.Log) (*DestinationVerifierConfigSet, error) {
	event := new(DestinationVerifierConfigSet)
	if err := _DestinationVerifier.contract.UnpackLog(event, "ConfigSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type DestinationVerifierFeeManagerSetIterator struct {
	Event *DestinationVerifierFeeManagerSet

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *DestinationVerifierFeeManagerSetIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(DestinationVerifierFeeManagerSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(DestinationVerifierFeeManagerSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *DestinationVerifierFeeManagerSetIterator) Error() error {
	return it.fail
}

func (it *DestinationVerifierFeeManagerSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type DestinationVerifierFeeManagerSet struct {
	OldFeeManager common.Address
	NewFeeManager common.Address
	Raw           types.Log
}

func (_DestinationVerifier *DestinationVerifierFilterer) FilterFeeManagerSet(opts *bind.FilterOpts) (*DestinationVerifierFeeManagerSetIterator, error) {

	logs, sub, err := _DestinationVerifier.contract.FilterLogs(opts, "FeeManagerSet")
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierFeeManagerSetIterator{contract: _DestinationVerifier.contract, event: "FeeManagerSet", logs: logs, sub: sub}, nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) WatchFeeManagerSet(opts *bind.WatchOpts, sink chan<- *DestinationVerifierFeeManagerSet) (event.Subscription, error) {

	logs, sub, err := _DestinationVerifier.contract.WatchLogs(opts, "FeeManagerSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(DestinationVerifierFeeManagerSet)
				if err := _DestinationVerifier.contract.UnpackLog(event, "FeeManagerSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) ParseFeeManagerSet(log types.Log) (*DestinationVerifierFeeManagerSet, error) {
	event := new(DestinationVerifierFeeManagerSet)
	if err := _DestinationVerifier.contract.UnpackLog(event, "FeeManagerSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type DestinationVerifierOwnershipTransferRequestedIterator struct {
	Event *DestinationVerifierOwnershipTransferRequested

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *DestinationVerifierOwnershipTransferRequestedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(DestinationVerifierOwnershipTransferRequested)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(DestinationVerifierOwnershipTransferRequested)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *DestinationVerifierOwnershipTransferRequestedIterator) Error() error {
	return it.fail
}

func (it *DestinationVerifierOwnershipTransferRequestedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type DestinationVerifierOwnershipTransferRequested struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_DestinationVerifier *DestinationVerifierFilterer) FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*DestinationVerifierOwnershipTransferRequestedIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _DestinationVerifier.contract.FilterLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierOwnershipTransferRequestedIterator{contract: _DestinationVerifier.contract, event: "OwnershipTransferRequested", logs: logs, sub: sub}, nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *DestinationVerifierOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _DestinationVerifier.contract.WatchLogs(opts, "OwnershipTransferRequested", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(DestinationVerifierOwnershipTransferRequested)
				if err := _DestinationVerifier.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) ParseOwnershipTransferRequested(log types.Log) (*DestinationVerifierOwnershipTransferRequested, error) {
	event := new(DestinationVerifierOwnershipTransferRequested)
	if err := _DestinationVerifier.contract.UnpackLog(event, "OwnershipTransferRequested", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type DestinationVerifierOwnershipTransferredIterator struct {
	Event *DestinationVerifierOwnershipTransferred

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *DestinationVerifierOwnershipTransferredIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(DestinationVerifierOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(DestinationVerifierOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *DestinationVerifierOwnershipTransferredIterator) Error() error {
	return it.fail
}

func (it *DestinationVerifierOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type DestinationVerifierOwnershipTransferred struct {
	From common.Address
	To   common.Address
	Raw  types.Log
}

func (_DestinationVerifier *DestinationVerifierFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*DestinationVerifierOwnershipTransferredIterator, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _DestinationVerifier.contract.FilterLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierOwnershipTransferredIterator{contract: _DestinationVerifier.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *DestinationVerifierOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error) {

	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var toRule []interface{}
	for _, toItem := range to {
		toRule = append(toRule, toItem)
	}

	logs, sub, err := _DestinationVerifier.contract.WatchLogs(opts, "OwnershipTransferred", fromRule, toRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(DestinationVerifierOwnershipTransferred)
				if err := _DestinationVerifier.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) ParseOwnershipTransferred(log types.Log) (*DestinationVerifierOwnershipTransferred, error) {
	event := new(DestinationVerifierOwnershipTransferred)
	if err := _DestinationVerifier.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

type DestinationVerifierReportVerifiedIterator struct {
	Event *DestinationVerifierReportVerified

	contract *bind.BoundContract
	event    string

	logs chan types.Log
	sub  ethereum.Subscription
	done bool
	fail error
}

func (it *DestinationVerifierReportVerifiedIterator) Next() bool {

	if it.fail != nil {
		return false
	}

	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(DestinationVerifierReportVerified)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}

	select {
	case log := <-it.logs:
		it.Event = new(DestinationVerifierReportVerified)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

func (it *DestinationVerifierReportVerifiedIterator) Error() error {
	return it.fail
}

func (it *DestinationVerifierReportVerifiedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

type DestinationVerifierReportVerified struct {
	FeedId    [32]byte
	Requester common.Address
	Raw       types.Log
}

func (_DestinationVerifier *DestinationVerifierFilterer) FilterReportVerified(opts *bind.FilterOpts, feedId [][32]byte) (*DestinationVerifierReportVerifiedIterator, error) {

	var feedIdRule []interface{}
	for _, feedIdItem := range feedId {
		feedIdRule = append(feedIdRule, feedIdItem)
	}

	logs, sub, err := _DestinationVerifier.contract.FilterLogs(opts, "ReportVerified", feedIdRule)
	if err != nil {
		return nil, err
	}
	return &DestinationVerifierReportVerifiedIterator{contract: _DestinationVerifier.contract, event: "ReportVerified", logs: logs, sub: sub}, nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) WatchReportVerified(opts *bind.WatchOpts, sink chan<- *DestinationVerifierReportVerified, feedId [][32]byte) (event.Subscription, error) {

	var feedIdRule []interface{}
	for _, feedIdItem := range feedId {
		feedIdRule = append(feedIdRule, feedIdItem)
	}

	logs, sub, err := _DestinationVerifier.contract.WatchLogs(opts, "ReportVerified", feedIdRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:

				event := new(DestinationVerifierReportVerified)
				if err := _DestinationVerifier.contract.UnpackLog(event, "ReportVerified", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

func (_DestinationVerifier *DestinationVerifierFilterer) ParseReportVerified(log types.Log) (*DestinationVerifierReportVerified, error) {
	event := new(DestinationVerifierReportVerified)
	if err := _DestinationVerifier.contract.UnpackLog(event, "ReportVerified", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

func (_DestinationVerifier *DestinationVerifier) ParseLog(log types.Log) (generated.AbigenLog, error) {
	switch log.Topics[0] {
	case _DestinationVerifier.abi.Events["AccessControllerSet"].ID:
		return _DestinationVerifier.ParseAccessControllerSet(log)
	case _DestinationVerifier.abi.Events["ConfigActivated"].ID:
		return _DestinationVerifier.ParseConfigActivated(log)
	case _DestinationVerifier.abi.Events["ConfigRemoved"].ID:
		return _DestinationVerifier.ParseConfigRemoved(log)
	case _DestinationVerifier.abi.Events["ConfigSet"].ID:
		return _DestinationVerifier.ParseConfigSet(log)
	case _DestinationVerifier.abi.Events["FeeManagerSet"].ID:
		return _DestinationVerifier.ParseFeeManagerSet(log)
	case _DestinationVerifier.abi.Events["OwnershipTransferRequested"].ID:
		return _DestinationVerifier.ParseOwnershipTransferRequested(log)
	case _DestinationVerifier.abi.Events["OwnershipTransferred"].ID:
		return _DestinationVerifier.ParseOwnershipTransferred(log)
	case _DestinationVerifier.abi.Events["ReportVerified"].ID:
		return _DestinationVerifier.ParseReportVerified(log)

	default:
		return nil, fmt.Errorf("abigen wrapper received unknown log topic: %v", log.Topics[0])
	}
}

func (DestinationVerifierAccessControllerSet) Topic() common.Hash {
	return common.HexToHash("0x953e92b1a6442e9c3242531154a3f6f6eb00b4e9c719ba8118fa6235e4ce89b6")
}

func (DestinationVerifierConfigActivated) Topic() common.Hash {
	return common.HexToHash("0x90186a1e77b498ec417ea88bd026cae00d7043c357cc45221777623bda582dd4")
}

func (DestinationVerifierConfigRemoved) Topic() common.Hash {
	return common.HexToHash("0x970fd8f3ebdd9a271080aacf9807a5c709be0b448e4047a6fc212b8cc165368d")
}

func (DestinationVerifierConfigSet) Topic() common.Hash {
	return common.HexToHash("0xa7d03f81dd1c1d8a55355fd71e1221d5959b7d9fb171fbb6389f207f63598fa5")
}

func (DestinationVerifierFeeManagerSet) Topic() common.Hash {
	return common.HexToHash("0x04628abcaa6b1674651352125cb94b65b289145bc2bc4d67720bb7d966372f03")
}

func (DestinationVerifierOwnershipTransferRequested) Topic() common.Hash {
	return common.HexToHash("0xed8889f560326eb138920d842192f0eb3dd22b4f139c87a2c57538e05bae1278")
}

func (DestinationVerifierOwnershipTransferred) Topic() common.Hash {
	return common.HexToHash("0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0")
}

func (DestinationVerifierReportVerified) Topic() common.Hash {
	return common.HexToHash("0x58ca9502e98a536e06e72d680fcc251e5d10b72291a281665a2c2dc0ac30fcc5")
}

func (_DestinationVerifier *DestinationVerifier) Address() common.Address {
	return _DestinationVerifier.address
}

type DestinationVerifierInterface interface {
	IVerifierProxy(opts *bind.CallOpts) (common.Address, error)

	Owner(opts *bind.CallOpts) (common.Address, error)

	SAccessController(opts *bind.CallOpts) (common.Address, error)

	SFeeManager(opts *bind.CallOpts) (common.Address, error)

	SupportsInterface(opts *bind.CallOpts, interfaceId [4]byte) (bool, error)

	TypeAndVersion(opts *bind.CallOpts) (string, error)

	AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error)

	RemoveLatestConfig(opts *bind.TransactOpts) (*types.Transaction, error)

	SetAccessController(opts *bind.TransactOpts, accessController common.Address) (*types.Transaction, error)

	SetConfig(opts *bind.TransactOpts, signers []common.Address, f uint8, recipientAddressesAndWeights []CommonAddressAndWeight) (*types.Transaction, error)

	SetConfigActive(opts *bind.TransactOpts, donConfigIndex *big.Int, isActive bool) (*types.Transaction, error)

	SetConfigWithActivationTime(opts *bind.TransactOpts, signers []common.Address, f uint8, recipientAddressesAndWeights []CommonAddressAndWeight, activationTime uint32) (*types.Transaction, error)

	SetFeeManager(opts *bind.TransactOpts, feeManager common.Address) (*types.Transaction, error)

	TransferOwnership(opts *bind.TransactOpts, to common.Address) (*types.Transaction, error)

	Verify(opts *bind.TransactOpts, signedReport []byte, parameterPayload []byte, sender common.Address) (*types.Transaction, error)

	VerifyBulk(opts *bind.TransactOpts, signedReports [][]byte, parameterPayload []byte, sender common.Address) (*types.Transaction, error)

	FilterAccessControllerSet(opts *bind.FilterOpts) (*DestinationVerifierAccessControllerSetIterator, error)

	WatchAccessControllerSet(opts *bind.WatchOpts, sink chan<- *DestinationVerifierAccessControllerSet) (event.Subscription, error)

	ParseAccessControllerSet(log types.Log) (*DestinationVerifierAccessControllerSet, error)

	FilterConfigActivated(opts *bind.FilterOpts) (*DestinationVerifierConfigActivatedIterator, error)

	WatchConfigActivated(opts *bind.WatchOpts, sink chan<- *DestinationVerifierConfigActivated) (event.Subscription, error)

	ParseConfigActivated(log types.Log) (*DestinationVerifierConfigActivated, error)

	FilterConfigRemoved(opts *bind.FilterOpts) (*DestinationVerifierConfigRemovedIterator, error)

	WatchConfigRemoved(opts *bind.WatchOpts, sink chan<- *DestinationVerifierConfigRemoved) (event.Subscription, error)

	ParseConfigRemoved(log types.Log) (*DestinationVerifierConfigRemoved, error)

	FilterConfigSet(opts *bind.FilterOpts, donConfigId [][24]byte) (*DestinationVerifierConfigSetIterator, error)

	WatchConfigSet(opts *bind.WatchOpts, sink chan<- *DestinationVerifierConfigSet, donConfigId [][24]byte) (event.Subscription, error)

	ParseConfigSet(log types.Log) (*DestinationVerifierConfigSet, error)

	FilterFeeManagerSet(opts *bind.FilterOpts) (*DestinationVerifierFeeManagerSetIterator, error)

	WatchFeeManagerSet(opts *bind.WatchOpts, sink chan<- *DestinationVerifierFeeManagerSet) (event.Subscription, error)

	ParseFeeManagerSet(log types.Log) (*DestinationVerifierFeeManagerSet, error)

	FilterOwnershipTransferRequested(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*DestinationVerifierOwnershipTransferRequestedIterator, error)

	WatchOwnershipTransferRequested(opts *bind.WatchOpts, sink chan<- *DestinationVerifierOwnershipTransferRequested, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferRequested(log types.Log) (*DestinationVerifierOwnershipTransferRequested, error)

	FilterOwnershipTransferred(opts *bind.FilterOpts, from []common.Address, to []common.Address) (*DestinationVerifierOwnershipTransferredIterator, error)

	WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *DestinationVerifierOwnershipTransferred, from []common.Address, to []common.Address) (event.Subscription, error)

	ParseOwnershipTransferred(log types.Log) (*DestinationVerifierOwnershipTransferred, error)

	FilterReportVerified(opts *bind.FilterOpts, feedId [][32]byte) (*DestinationVerifierReportVerifiedIterator, error)

	WatchReportVerified(opts *bind.WatchOpts, sink chan<- *DestinationVerifierReportVerified, feedId [][32]byte) (event.Subscription, error)

	ParseReportVerified(log types.Log) (*DestinationVerifierReportVerified, error)

	ParseLog(log types.Log) (generated.AbigenLog, error)

	Address() common.Address
}
