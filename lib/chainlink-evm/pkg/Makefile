.PHONY: mockery
mockery: ## Install mockery.
	go install github.com/vektra/mockery/v2@v2.53.3

.PHONY: codecgen
codecgen: ## Install codecgen
	go install github.com/ugorji/go/codec/codecgen@v1.2.10

.PHONY: protoc
protoc: ## Install protoc
	../tools/bin/install-protoc.sh 29.3 /
	go install google.golang.org/protobuf/cmd/protoc-gen-go@`go list -m -json google.golang.org/protobuf | jq -r .Version`

.PHONY: generate
generate: codecgen mockery protoc
	export PATH="$(HOME)/.local/bin:$(PATH)"; go generate ./...
	mockery

.PHONY: rm-mocked
rm-mocked:
	grep -rl "^// Code generated by mockery" | grep .go$ | xargs -r rm

.PHONY: testdb
testdb:
	cd $(shell go mod download -json github.com/smartcontractkit/chainlink/v2@d0bcaaeadbe0268fac24395f29169aa879a653f3 | jq -r .Dir) && \
	go run ./core/store/cmd/preparetest
