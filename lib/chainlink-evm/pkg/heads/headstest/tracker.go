// Code generated by mockery v2.53.3. DO NOT EDIT.

package headstest

import (
	context "context"

	chains "github.com/smartcontractkit/chainlink-framework/chains"

	mock "github.com/stretchr/testify/mock"
)

// Tracker is an autogenerated mock type for the Tracker type
type Tracker[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	mock.Mock
}

type Tracker_Expecter[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	mock *mock.Mock
}

func (_m *Tracker[H, BLOCK_HASH]) EXPECT() *Tracker_Expecter[H, BLOCK_HASH] {
	return &Tracker_Expecter[H, BLOCK_HASH]{mock: &_m.Mock}
}

// Backfill provides a mock function with given fields: ctx, headWithChain, prevHeadWithChain
func (_m *Tracker[H, BLOCK_HASH]) Backfill(ctx context.Context, headWith<PERSON>hain H, prevHeadWith<PERSON>hain H) error {
	ret := _m.Called(ctx, headWith<PERSON>hain, prevHeadWithChain)

	if len(ret) == 0 {
		panic("no return value specified for Backfill")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, H, H) error); ok {
		r0 = rf(ctx, headWithChain, prevHeadWithChain)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Tracker_Backfill_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Backfill'
type Tracker_Backfill_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Backfill is a helper method to define mock.On call
//   - ctx context.Context
//   - headWithChain H
//   - prevHeadWithChain H
func (_e *Tracker_Expecter[H, BLOCK_HASH]) Backfill(ctx interface{}, headWithChain interface{}, prevHeadWithChain interface{}) *Tracker_Backfill_Call[H, BLOCK_HASH] {
	return &Tracker_Backfill_Call[H, BLOCK_HASH]{Call: _e.mock.On("Backfill", ctx, headWithChain, prevHeadWithChain)}
}

func (_c *Tracker_Backfill_Call[H, BLOCK_HASH]) Run(run func(ctx context.Context, headWithChain H, prevHeadWithChain H)) *Tracker_Backfill_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(H), args[2].(H))
	})
	return _c
}

func (_c *Tracker_Backfill_Call[H, BLOCK_HASH]) Return(err error) *Tracker_Backfill_Call[H, BLOCK_HASH] {
	_c.Call.Return(err)
	return _c
}

func (_c *Tracker_Backfill_Call[H, BLOCK_HASH]) RunAndReturn(run func(context.Context, H, H) error) *Tracker_Backfill_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with no fields
func (_m *Tracker[H, BLOCK_HASH]) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Tracker_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Tracker_Close_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *Tracker_Expecter[H, BLOCK_HASH]) Close() *Tracker_Close_Call[H, BLOCK_HASH] {
	return &Tracker_Close_Call[H, BLOCK_HASH]{Call: _e.mock.On("Close")}
}

func (_c *Tracker_Close_Call[H, BLOCK_HASH]) Run(run func()) *Tracker_Close_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Tracker_Close_Call[H, BLOCK_HASH]) Return(_a0 error) *Tracker_Close_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tracker_Close_Call[H, BLOCK_HASH]) RunAndReturn(run func() error) *Tracker_Close_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with no fields
func (_m *Tracker[H, BLOCK_HASH]) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// Tracker_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type Tracker_HealthReport_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *Tracker_Expecter[H, BLOCK_HASH]) HealthReport() *Tracker_HealthReport_Call[H, BLOCK_HASH] {
	return &Tracker_HealthReport_Call[H, BLOCK_HASH]{Call: _e.mock.On("HealthReport")}
}

func (_c *Tracker_HealthReport_Call[H, BLOCK_HASH]) Run(run func()) *Tracker_HealthReport_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Tracker_HealthReport_Call[H, BLOCK_HASH]) Return(_a0 map[string]error) *Tracker_HealthReport_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tracker_HealthReport_Call[H, BLOCK_HASH]) RunAndReturn(run func() map[string]error) *Tracker_HealthReport_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// LatestAndFinalizedBlock provides a mock function with given fields: ctx
func (_m *Tracker[H, BLOCK_HASH]) LatestAndFinalizedBlock(ctx context.Context) (H, H, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for LatestAndFinalizedBlock")
	}

	var r0 H
	var r1 H
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context) (H, H, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) H); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(H)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) H); ok {
		r1 = rf(ctx)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(H)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context) error); ok {
		r2 = rf(ctx)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// Tracker_LatestAndFinalizedBlock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestAndFinalizedBlock'
type Tracker_LatestAndFinalizedBlock_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// LatestAndFinalizedBlock is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Tracker_Expecter[H, BLOCK_HASH]) LatestAndFinalizedBlock(ctx interface{}) *Tracker_LatestAndFinalizedBlock_Call[H, BLOCK_HASH] {
	return &Tracker_LatestAndFinalizedBlock_Call[H, BLOCK_HASH]{Call: _e.mock.On("LatestAndFinalizedBlock", ctx)}
}

func (_c *Tracker_LatestAndFinalizedBlock_Call[H, BLOCK_HASH]) Run(run func(ctx context.Context)) *Tracker_LatestAndFinalizedBlock_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Tracker_LatestAndFinalizedBlock_Call[H, BLOCK_HASH]) Return(latest H, finalized H, err error) *Tracker_LatestAndFinalizedBlock_Call[H, BLOCK_HASH] {
	_c.Call.Return(latest, finalized, err)
	return _c
}

func (_c *Tracker_LatestAndFinalizedBlock_Call[H, BLOCK_HASH]) RunAndReturn(run func(context.Context) (H, H, error)) *Tracker_LatestAndFinalizedBlock_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// LatestChain provides a mock function with no fields
func (_m *Tracker[H, BLOCK_HASH]) LatestChain() H {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for LatestChain")
	}

	var r0 H
	if rf, ok := ret.Get(0).(func() H); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(H)
		}
	}

	return r0
}

// Tracker_LatestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestChain'
type Tracker_LatestChain_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// LatestChain is a helper method to define mock.On call
func (_e *Tracker_Expecter[H, BLOCK_HASH]) LatestChain() *Tracker_LatestChain_Call[H, BLOCK_HASH] {
	return &Tracker_LatestChain_Call[H, BLOCK_HASH]{Call: _e.mock.On("LatestChain")}
}

func (_c *Tracker_LatestChain_Call[H, BLOCK_HASH]) Run(run func()) *Tracker_LatestChain_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Tracker_LatestChain_Call[H, BLOCK_HASH]) Return(_a0 H) *Tracker_LatestChain_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tracker_LatestChain_Call[H, BLOCK_HASH]) RunAndReturn(run func() H) *Tracker_LatestChain_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with no fields
func (_m *Tracker[H, BLOCK_HASH]) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// Tracker_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type Tracker_Name_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *Tracker_Expecter[H, BLOCK_HASH]) Name() *Tracker_Name_Call[H, BLOCK_HASH] {
	return &Tracker_Name_Call[H, BLOCK_HASH]{Call: _e.mock.On("Name")}
}

func (_c *Tracker_Name_Call[H, BLOCK_HASH]) Run(run func()) *Tracker_Name_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Tracker_Name_Call[H, BLOCK_HASH]) Return(_a0 string) *Tracker_Name_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tracker_Name_Call[H, BLOCK_HASH]) RunAndReturn(run func() string) *Tracker_Name_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with no fields
func (_m *Tracker[H, BLOCK_HASH]) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Tracker_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type Tracker_Ready_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *Tracker_Expecter[H, BLOCK_HASH]) Ready() *Tracker_Ready_Call[H, BLOCK_HASH] {
	return &Tracker_Ready_Call[H, BLOCK_HASH]{Call: _e.mock.On("Ready")}
}

func (_c *Tracker_Ready_Call[H, BLOCK_HASH]) Run(run func()) *Tracker_Ready_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Tracker_Ready_Call[H, BLOCK_HASH]) Return(_a0 error) *Tracker_Ready_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tracker_Ready_Call[H, BLOCK_HASH]) RunAndReturn(run func() error) *Tracker_Ready_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *Tracker[H, BLOCK_HASH]) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Tracker_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type Tracker_Start_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *Tracker_Expecter[H, BLOCK_HASH]) Start(_a0 interface{}) *Tracker_Start_Call[H, BLOCK_HASH] {
	return &Tracker_Start_Call[H, BLOCK_HASH]{Call: _e.mock.On("Start", _a0)}
}

func (_c *Tracker_Start_Call[H, BLOCK_HASH]) Run(run func(_a0 context.Context)) *Tracker_Start_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Tracker_Start_Call[H, BLOCK_HASH]) Return(_a0 error) *Tracker_Start_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Tracker_Start_Call[H, BLOCK_HASH]) RunAndReturn(run func(context.Context) error) *Tracker_Start_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// NewTracker creates a new instance of Tracker. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTracker[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable](t interface {
	mock.TestingT
	Cleanup(func())
}) *Tracker[H, BLOCK_HASH] {
	mock := &Tracker[H, BLOCK_HASH]{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
