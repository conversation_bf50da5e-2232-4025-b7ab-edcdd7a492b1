// Code generated by mockery v2.53.3. DO NOT EDIT.

package headstest

import (
	context "context"

	chains "github.com/smartcontractkit/chainlink-framework/chains"

	mock "github.com/stretchr/testify/mock"
)

// Trackable is an autogenerated mock type for the Trackable type
type Trackable[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	mock.Mock
}

type Trackable_Expecter[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	mock *mock.Mock
}

func (_m *Trackable[H, BLOCK_HASH]) EXPECT() *Trackable_Expecter[H, BLOCK_HASH] {
	return &Trackable_Expecter[H, BLOCK_HASH]{mock: &_m.Mock}
}

// OnNewLongestChain provides a mock function with given fields: ctx, head
func (_m *Trackable[H, BLOCK_HASH]) OnNewLongestChain(ctx context.Context, head H) {
	_m.Called(ctx, head)
}

// Trackable_OnNewLongestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'OnNewLongestChain'
type Trackable_OnNewLongestChain_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// OnNewLongestChain is a helper method to define mock.On call
//   - ctx context.Context
//   - head H
func (_e *Trackable_Expecter[H, BLOCK_HASH]) OnNewLongestChain(ctx interface{}, head interface{}) *Trackable_OnNewLongestChain_Call[H, BLOCK_HASH] {
	return &Trackable_OnNewLongestChain_Call[H, BLOCK_HASH]{Call: _e.mock.On("OnNewLongestChain", ctx, head)}
}

func (_c *Trackable_OnNewLongestChain_Call[H, BLOCK_HASH]) Run(run func(ctx context.Context, head H)) *Trackable_OnNewLongestChain_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(H))
	})
	return _c
}

func (_c *Trackable_OnNewLongestChain_Call[H, BLOCK_HASH]) Return() *Trackable_OnNewLongestChain_Call[H, BLOCK_HASH] {
	_c.Call.Return()
	return _c
}

func (_c *Trackable_OnNewLongestChain_Call[H, BLOCK_HASH]) RunAndReturn(run func(context.Context, H)) *Trackable_OnNewLongestChain_Call[H, BLOCK_HASH] {
	_c.Run(run)
	return _c
}

// NewTrackable creates a new instance of Trackable. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTrackable[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable](t interface {
	mock.TestingT
	Cleanup(func())
}) *Trackable[H, BLOCK_HASH] {
	mock := &Trackable[H, BLOCK_HASH]{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
