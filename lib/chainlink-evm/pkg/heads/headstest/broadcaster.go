// Code generated by mockery v2.53.3. DO NOT EDIT.

package headstest

import (
	context "context"

	chains "github.com/smartcontractkit/chainlink-framework/chains"

	heads "github.com/smartcontractkit/chainlink-framework/chains/heads"

	mock "github.com/stretchr/testify/mock"
)

// Broadcaster is an autogenerated mock type for the Broadcaster type
type Broadcaster[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	mock.Mock
}

type Broadcaster_Expecter[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	mock *mock.Mock
}

func (_m *Broadcaster[H, BLOCK_HASH]) EXPECT() *Broadcaster_Expecter[H, BLOCK_HASH] {
	return &Broadcaster_Expecter[H, BLOCK_HASH]{mock: &_m.Mock}
}

// BroadcastNewLongestChain provides a mock function with given fields: _a0
func (_m *Broadcaster[H, BLOCK_HASH]) BroadcastNewLongestChain(_a0 H) {
	_m.Called(_a0)
}

// Broadcaster_BroadcastNewLongestChain_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BroadcastNewLongestChain'
type Broadcaster_BroadcastNewLongestChain_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// BroadcastNewLongestChain is a helper method to define mock.On call
//   - _a0 H
func (_e *Broadcaster_Expecter[H, BLOCK_HASH]) BroadcastNewLongestChain(_a0 interface{}) *Broadcaster_BroadcastNewLongestChain_Call[H, BLOCK_HASH] {
	return &Broadcaster_BroadcastNewLongestChain_Call[H, BLOCK_HASH]{Call: _e.mock.On("BroadcastNewLongestChain", _a0)}
}

func (_c *Broadcaster_BroadcastNewLongestChain_Call[H, BLOCK_HASH]) Run(run func(_a0 H)) *Broadcaster_BroadcastNewLongestChain_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(H))
	})
	return _c
}

func (_c *Broadcaster_BroadcastNewLongestChain_Call[H, BLOCK_HASH]) Return() *Broadcaster_BroadcastNewLongestChain_Call[H, BLOCK_HASH] {
	_c.Call.Return()
	return _c
}

func (_c *Broadcaster_BroadcastNewLongestChain_Call[H, BLOCK_HASH]) RunAndReturn(run func(H)) *Broadcaster_BroadcastNewLongestChain_Call[H, BLOCK_HASH] {
	_c.Run(run)
	return _c
}

// Close provides a mock function with no fields
func (_m *Broadcaster[H, BLOCK_HASH]) Close() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Broadcaster_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Broadcaster_Close_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *Broadcaster_Expecter[H, BLOCK_HASH]) Close() *Broadcaster_Close_Call[H, BLOCK_HASH] {
	return &Broadcaster_Close_Call[H, BLOCK_HASH]{Call: _e.mock.On("Close")}
}

func (_c *Broadcaster_Close_Call[H, BLOCK_HASH]) Run(run func()) *Broadcaster_Close_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_Close_Call[H, BLOCK_HASH]) Return(_a0 error) *Broadcaster_Close_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_Close_Call[H, BLOCK_HASH]) RunAndReturn(run func() error) *Broadcaster_Close_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// HealthReport provides a mock function with no fields
func (_m *Broadcaster[H, BLOCK_HASH]) HealthReport() map[string]error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for HealthReport")
	}

	var r0 map[string]error
	if rf, ok := ret.Get(0).(func() map[string]error); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]error)
		}
	}

	return r0
}

// Broadcaster_HealthReport_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HealthReport'
type Broadcaster_HealthReport_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// HealthReport is a helper method to define mock.On call
func (_e *Broadcaster_Expecter[H, BLOCK_HASH]) HealthReport() *Broadcaster_HealthReport_Call[H, BLOCK_HASH] {
	return &Broadcaster_HealthReport_Call[H, BLOCK_HASH]{Call: _e.mock.On("HealthReport")}
}

func (_c *Broadcaster_HealthReport_Call[H, BLOCK_HASH]) Run(run func()) *Broadcaster_HealthReport_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_HealthReport_Call[H, BLOCK_HASH]) Return(_a0 map[string]error) *Broadcaster_HealthReport_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_HealthReport_Call[H, BLOCK_HASH]) RunAndReturn(run func() map[string]error) *Broadcaster_HealthReport_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// Name provides a mock function with no fields
func (_m *Broadcaster[H, BLOCK_HASH]) Name() string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Name")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// Broadcaster_Name_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Name'
type Broadcaster_Name_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Name is a helper method to define mock.On call
func (_e *Broadcaster_Expecter[H, BLOCK_HASH]) Name() *Broadcaster_Name_Call[H, BLOCK_HASH] {
	return &Broadcaster_Name_Call[H, BLOCK_HASH]{Call: _e.mock.On("Name")}
}

func (_c *Broadcaster_Name_Call[H, BLOCK_HASH]) Run(run func()) *Broadcaster_Name_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_Name_Call[H, BLOCK_HASH]) Return(_a0 string) *Broadcaster_Name_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_Name_Call[H, BLOCK_HASH]) RunAndReturn(run func() string) *Broadcaster_Name_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// Ready provides a mock function with no fields
func (_m *Broadcaster[H, BLOCK_HASH]) Ready() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ready")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Broadcaster_Ready_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Ready'
type Broadcaster_Ready_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Ready is a helper method to define mock.On call
func (_e *Broadcaster_Expecter[H, BLOCK_HASH]) Ready() *Broadcaster_Ready_Call[H, BLOCK_HASH] {
	return &Broadcaster_Ready_Call[H, BLOCK_HASH]{Call: _e.mock.On("Ready")}
}

func (_c *Broadcaster_Ready_Call[H, BLOCK_HASH]) Run(run func()) *Broadcaster_Ready_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Broadcaster_Ready_Call[H, BLOCK_HASH]) Return(_a0 error) *Broadcaster_Ready_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_Ready_Call[H, BLOCK_HASH]) RunAndReturn(run func() error) *Broadcaster_Ready_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// Start provides a mock function with given fields: _a0
func (_m *Broadcaster[H, BLOCK_HASH]) Start(_a0 context.Context) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for Start")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Broadcaster_Start_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Start'
type Broadcaster_Start_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Start is a helper method to define mock.On call
//   - _a0 context.Context
func (_e *Broadcaster_Expecter[H, BLOCK_HASH]) Start(_a0 interface{}) *Broadcaster_Start_Call[H, BLOCK_HASH] {
	return &Broadcaster_Start_Call[H, BLOCK_HASH]{Call: _e.mock.On("Start", _a0)}
}

func (_c *Broadcaster_Start_Call[H, BLOCK_HASH]) Run(run func(_a0 context.Context)) *Broadcaster_Start_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Broadcaster_Start_Call[H, BLOCK_HASH]) Return(_a0 error) *Broadcaster_Start_Call[H, BLOCK_HASH] {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Broadcaster_Start_Call[H, BLOCK_HASH]) RunAndReturn(run func(context.Context) error) *Broadcaster_Start_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// Subscribe provides a mock function with given fields: callback
func (_m *Broadcaster[H, BLOCK_HASH]) Subscribe(callback heads.Trackable[H, BLOCK_HASH]) (H, func()) {
	ret := _m.Called(callback)

	if len(ret) == 0 {
		panic("no return value specified for Subscribe")
	}

	var r0 H
	var r1 func()
	if rf, ok := ret.Get(0).(func(heads.Trackable[H, BLOCK_HASH]) (H, func())); ok {
		return rf(callback)
	}
	if rf, ok := ret.Get(0).(func(heads.Trackable[H, BLOCK_HASH]) H); ok {
		r0 = rf(callback)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(H)
		}
	}

	if rf, ok := ret.Get(1).(func(heads.Trackable[H, BLOCK_HASH]) func()); ok {
		r1 = rf(callback)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(func())
		}
	}

	return r0, r1
}

// Broadcaster_Subscribe_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Subscribe'
type Broadcaster_Subscribe_Call[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable] struct {
	*mock.Call
}

// Subscribe is a helper method to define mock.On call
//   - callback heads.Trackable[H,BLOCK_HASH]
func (_e *Broadcaster_Expecter[H, BLOCK_HASH]) Subscribe(callback interface{}) *Broadcaster_Subscribe_Call[H, BLOCK_HASH] {
	return &Broadcaster_Subscribe_Call[H, BLOCK_HASH]{Call: _e.mock.On("Subscribe", callback)}
}

func (_c *Broadcaster_Subscribe_Call[H, BLOCK_HASH]) Run(run func(callback heads.Trackable[H, BLOCK_HASH])) *Broadcaster_Subscribe_Call[H, BLOCK_HASH] {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(heads.Trackable[H, BLOCK_HASH]))
	})
	return _c
}

func (_c *Broadcaster_Subscribe_Call[H, BLOCK_HASH]) Return(currentLongestChain H, unsubscribe func()) *Broadcaster_Subscribe_Call[H, BLOCK_HASH] {
	_c.Call.Return(currentLongestChain, unsubscribe)
	return _c
}

func (_c *Broadcaster_Subscribe_Call[H, BLOCK_HASH]) RunAndReturn(run func(heads.Trackable[H, BLOCK_HASH]) (H, func())) *Broadcaster_Subscribe_Call[H, BLOCK_HASH] {
	_c.Call.Return(run)
	return _c
}

// NewBroadcaster creates a new instance of Broadcaster. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewBroadcaster[H chains.Head[BLOCK_HASH], BLOCK_HASH chains.Hashable](t interface {
	mock.TestingT
	Cleanup(func())
}) *Broadcaster[H, BLOCK_HASH] {
	mock := &Broadcaster[H, BLOCK_HASH]{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
