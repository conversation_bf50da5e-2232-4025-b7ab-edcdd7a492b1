ChainID = "37111"
ChainType = "zksync"
# finality depth for this chain is very inconsistent due to low network traffic. in testing blocks every ~1-2minutes were seen
# confirmed this value with product
FinalityDepth = 40
LinkContractAddress = '0x7f1b9eE544f9ff9bB521Ab79c205d79C55250a36'
# block rate is dynamic, have seen block times as low as 1s
LogPollInterval = "5s"
# sufficient time for RPC to be labelled out of sync
NoNewHeadsThreshold = "10m"

[GasEstimator]
Mode = 'FeeHistory'
# bump gas aggressively to avoid high amounts of transmit errors
BumpThreshold = 1
BumpPercent = 40

[GasEstimator.FeeHistory]
CacheTimeout = '5s'

[GasEstimator.DAOracle]
OracleType = 'zksync'

[Transactions]
ResendAfterThreshold = '7m0s'

[HeadTracker]
# l1 batching is done every 8hrs with low network activity setting this value to a rough calculation of ~1tx / 2min * 8hrs
HistoryDepth = 250

[NodePool.Errors]
TerminallyUnderpriced = "(?:: |^)(max fee per gas less than block base fee|virtual machine entered unexpected state. (?:P|p)lease contact developers and provide transaction details that caused this error. Error description: (?:The operator included transaction with an unacceptable gas price|Assertion error: Fair pubdata price too high))$"
