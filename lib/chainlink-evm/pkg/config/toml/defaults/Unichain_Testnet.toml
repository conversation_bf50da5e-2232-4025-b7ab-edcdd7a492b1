ChainID = '1301'
# OP stack: https://docs.unichain.org/docs/getting-started/set-up-a-node#overview
ChainType = 'optimismBedrock'
# finality_depth was: ~1900
FinalityDepth = 2000
LinkContractAddress = '0xda40816f278Cd049c137F6612822D181065EBfB4'
# block_time was:  ~1s, adding 1 second buffer 
LogPollInterval = '2s'

# batching_size_finalization_percentage = 30% according to the explorer batching view
# ( batching_size_finalization_percentage * finality_depth) * block_time / 60 secs = ~10  min (finality time)
# After running soak tests using 10m threw issues as there are batchs that take 35m, so we are bumping it to 45m to be sure 
NoNewFinalizedHeadsThreshold = '45m'

FinalityTagEnabled = true

[GasEstimator]
EIP1559DynamicFees = true
Mode = 'FeeHistory'

[GasEstimator.FeeHistory]
# block_time was: 1s, per recommendation skip 1-2 blocks
CacheTimeout = '2s'

[GasEstimator.BlockHistory]
# As we see blocks containing between ~[8-12]tx, to get about ~1000 tx to check we would need to rougly go 100 tx back
BlockHistorySize = 100

[GasEstimator.DAOracle]
OracleType = 'opstack'
OracleAddress = '0x420000000000000000000000000000000000000F'
