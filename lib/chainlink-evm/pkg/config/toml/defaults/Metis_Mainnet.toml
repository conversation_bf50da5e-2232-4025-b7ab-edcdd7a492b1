# Metis is an L2 chain based on Optimism.
ChainID = '1088'
ChainType = 'optimismBedrock'
# Sequencer offers absolute finality
FinalityDepth = 10
FinalityTagEnabled = true
LinkContractAddress = '0xd2FE54D1E5F568eB710ba9d898Bf4bD02C7c0353'
MinIncomingConfirmations = 1
NoNewHeadsThreshold = '0'
OCR.ContractConfirmations = 1

[GasEstimator]
Mode = 'SuggestedPrice'
# Metis uses the SuggestedPrice estimator; we don't want to place any limits on the minimum gas price
PriceMin = '0'

[GasEstimator.BlockHistory]
# Force an error if someone enables the estimator by accident; we never want to run the block history estimator on metisaa
BlockHistorySize = 0

[NodePool]
SyncThreshold = 10

[GasEstimator.DAOracle]
OracleType = 'opstack'
OracleAddress = '0x420000000000000000000000000000000000000F'
