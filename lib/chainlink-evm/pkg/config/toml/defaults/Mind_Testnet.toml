ChainID = '192940'
ChainType = 'arbitrum'
FinalityTagEnabled = true
LinkContractAddress = '0xd8A9246e84903e82CA01e42774b01A7CdD465BFa'
LogPollInterval = '2s' # max 4 blocks per second, adding slight buffer

[GasEstimator]
EIP1559DynamicFees = false
Mode = 'Arbitrum'
PriceDefault = '0.005 gwei' # fixed to 0.005 gwei
PriceMin = '0' # Arbitrum uses the suggested gas price, so we don't want to place any limits on the minimum            

[GasEstimator.DAOracle]
OracleType = 'arbitrum'

[NodePool]
SyncThreshold = 10