ChainID = '1123'
# OP stack from questionnaire https://docs.google.com/spreadsheets/d/1l8dx1GzxEnjgwH5x3vB60FUr5iFALzPcs6W_wOAiuDs/edit?gid=625078687#gid=625078687
ChainType = 'optimismBedrock'
# finality_depth was: ~1900
FinalityDepth = 2000
LinkContractAddress = '0x436a1907D9e6a65E6db73015F08f9C66F6B63E45'
# block_time:  ~2s, adding 1 second buffer  
LogPollInterval = '3s'

# finality_depth * block_time / 60 secs = ~66  min (finality time)
NoNewFinalizedHeadsThreshold = '70m'

FinalityTagEnabled = true

[GasEstimator]
EIP1559DynamicFees = true
Mode = 'FeeHistory'

[GasEstimator.FeeHistory]
# block_time was: 2s, per recommendation skip 1-2 blocks
CacheTimeout = '4s'

[GasEstimator.BlockHistory]
BlockHistorySize = 100

[GasEstimator.DAOracle]
OracleType = 'opstack'
OracleAddress = '0x420000000000000000000000000000000000000F'
