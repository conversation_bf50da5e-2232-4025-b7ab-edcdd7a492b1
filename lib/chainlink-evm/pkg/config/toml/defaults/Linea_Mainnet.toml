ChainID = '59144'
# Block time 12s, finality < 60m
FinalityDepth = 300
LinkContractAddress = '0xa18152629128738a5c081eb226335FEd4B9C95e9'
# Blocks are only emitted when a transaction happens / no empty blocks
NoNewHeadsThreshold = '0'

[GasEstimator]
BumpMin = '500 mwei'
BumpPercent = 40
PriceMin = '400 mwei'

[Transactions]
# increase resend time to align with finality
ResendAfterThreshold = '3m'

# set greater than finality depth
[HeadTracker]
HistoryDepth = 350

[Transactions.AutoPurge]
Enabled = true
Threshold = 50 # 50 blocks at 3s block time ~2.5 minutes
MinAttempts = 3
