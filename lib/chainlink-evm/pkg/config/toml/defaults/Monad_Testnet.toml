ChainID = '10143'
# finality_depth was: 0
FinalityDepth = 120
FinalityTagEnabled = false
LogBackfillBatchSize = 100 #context timeouts with higher values
# No WS Support
LogBroadcasterEnabled = false
# block_time was:  1s, adding 1 second buffer
LogPollInterval = '2s'
NoNewFinalizedHeadsThreshold = '1m'
NoNewHeadsThreshold = '1m'

[GasEstimator]
EIP1559DynamicFees = false
Mode = 'FeeHistory'

[GasEstimator.FeeHistory]
CacheTimeout = '2s'

[GasEstimator.BlockHistory]
BlockHistorySize = 100

[NodePool]
NewHeadsPollInterval = '4s' 
