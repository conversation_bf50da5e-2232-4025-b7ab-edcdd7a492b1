# xDai currently uses AuRa (like Parity) consensus so finality rules will be similar to parity
# See: https://www.poa.network/for-users/whitepaper/poadao-v1/proof-of-authority
# NOTE: xDai is planning to move to Honeybadger BFT which might have different finality guarantees
# https://www.xdaichain.com/for-validators/consensus/honeybadger-bft-consensus
# For worst case re-org depth on AuRa, assume 2n+2 (see: https://github.com/poanetwork/wiki/wiki/Aura-Consensus-Protocol-Audit)
# With xDai's current maximum of 19 validators then 40 blocks is the maximum possible re-org)
# The mainnet default of 50 blocks is ok here
ChainID = '100'
ChainType = 'gnosis'
LinkContractAddress = '0xE2e73A1c69ecF83F464EFCE6A5be353a37cA09b2'
LogPollInterval = '5s'
FinalizedBlockOffset = 2
NoNewFinalizedHeadsThreshold = '2m'

[GasEstimator]
PriceDefault = '1 gwei'
PriceMax = '500 gwei'
# 1 Gwei is the minimum accepted by the validators (unless whitelisted)
PriceMin = '1 gwei'
