// Code generated by mockery v2.53.3. DO NOT EDIT.

package clienttest

import (
	big "math/big"

	assets "github.com/smartcontractkit/chainlink-common/pkg/assets"

	client "github.com/smartcontractkit/chainlink-evm/pkg/client"

	common "github.com/ethereum/go-ethereum/common"

	context "context"

	ethereum "github.com/ethereum/go-ethereum"

	mock "github.com/stretchr/testify/mock"

	multinode "github.com/smartcontractkit/chainlink-framework/multinode"

	pkgtypes "github.com/smartcontractkit/chainlink-evm/pkg/types"

	rpc "github.com/ethereum/go-ethereum/rpc"

	types "github.com/ethereum/go-ethereum/core/types"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.<PERSON>}
}

// BalanceAt provides a mock function with given fields: ctx, account, blockNumber
func (_m *Client) BalanceAt(ctx context.Context, account common.Address, blockNumber *big.Int) (*big.Int, error) {
	ret := _m.Called(ctx, account, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for BalanceAt")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) (*big.Int, error)); ok {
		return rf(ctx, account, blockNumber)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) *big.Int); ok {
		r0 = rf(ctx, account, blockNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, account, blockNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_BalanceAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BalanceAt'
type Client_BalanceAt_Call struct {
	*mock.Call
}

// BalanceAt is a helper method to define mock.On call
//   - ctx context.Context
//   - account common.Address
//   - blockNumber *big.Int
func (_e *Client_Expecter) BalanceAt(ctx interface{}, account interface{}, blockNumber interface{}) *Client_BalanceAt_Call {
	return &Client_BalanceAt_Call{Call: _e.mock.On("BalanceAt", ctx, account, blockNumber)}
}

func (_c *Client_BalanceAt_Call) Run(run func(ctx context.Context, account common.Address, blockNumber *big.Int)) *Client_BalanceAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *Client_BalanceAt_Call) Return(_a0 *big.Int, _a1 error) *Client_BalanceAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_BalanceAt_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) (*big.Int, error)) *Client_BalanceAt_Call {
	_c.Call.Return(run)
	return _c
}

// BatchCallContext provides a mock function with given fields: ctx, b
func (_m *Client) BatchCallContext(ctx context.Context, b []rpc.BatchElem) error {
	ret := _m.Called(ctx, b)

	if len(ret) == 0 {
		panic("no return value specified for BatchCallContext")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []rpc.BatchElem) error); ok {
		r0 = rf(ctx, b)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_BatchCallContext_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCallContext'
type Client_BatchCallContext_Call struct {
	*mock.Call
}

// BatchCallContext is a helper method to define mock.On call
//   - ctx context.Context
//   - b []rpc.BatchElem
func (_e *Client_Expecter) BatchCallContext(ctx interface{}, b interface{}) *Client_BatchCallContext_Call {
	return &Client_BatchCallContext_Call{Call: _e.mock.On("BatchCallContext", ctx, b)}
}

func (_c *Client_BatchCallContext_Call) Run(run func(ctx context.Context, b []rpc.BatchElem)) *Client_BatchCallContext_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]rpc.BatchElem))
	})
	return _c
}

func (_c *Client_BatchCallContext_Call) Return(_a0 error) *Client_BatchCallContext_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_BatchCallContext_Call) RunAndReturn(run func(context.Context, []rpc.BatchElem) error) *Client_BatchCallContext_Call {
	_c.Call.Return(run)
	return _c
}

// BatchCallContextAll provides a mock function with given fields: ctx, b
func (_m *Client) BatchCallContextAll(ctx context.Context, b []rpc.BatchElem) error {
	ret := _m.Called(ctx, b)

	if len(ret) == 0 {
		panic("no return value specified for BatchCallContextAll")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []rpc.BatchElem) error); ok {
		r0 = rf(ctx, b)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_BatchCallContextAll_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BatchCallContextAll'
type Client_BatchCallContextAll_Call struct {
	*mock.Call
}

// BatchCallContextAll is a helper method to define mock.On call
//   - ctx context.Context
//   - b []rpc.BatchElem
func (_e *Client_Expecter) BatchCallContextAll(ctx interface{}, b interface{}) *Client_BatchCallContextAll_Call {
	return &Client_BatchCallContextAll_Call{Call: _e.mock.On("BatchCallContextAll", ctx, b)}
}

func (_c *Client_BatchCallContextAll_Call) Run(run func(ctx context.Context, b []rpc.BatchElem)) *Client_BatchCallContextAll_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].([]rpc.BatchElem))
	})
	return _c
}

func (_c *Client_BatchCallContextAll_Call) Return(_a0 error) *Client_BatchCallContextAll_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_BatchCallContextAll_Call) RunAndReturn(run func(context.Context, []rpc.BatchElem) error) *Client_BatchCallContextAll_Call {
	_c.Call.Return(run)
	return _c
}

// BlockByHash provides a mock function with given fields: ctx, hash
func (_m *Client) BlockByHash(ctx context.Context, hash common.Hash) (*types.Block, error) {
	ret := _m.Called(ctx, hash)

	if len(ret) == 0 {
		panic("no return value specified for BlockByHash")
	}

	var r0 *types.Block
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*types.Block, error)); ok {
		return rf(ctx, hash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *types.Block); ok {
		r0 = rf(ctx, hash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Block)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, hash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_BlockByHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BlockByHash'
type Client_BlockByHash_Call struct {
	*mock.Call
}

// BlockByHash is a helper method to define mock.On call
//   - ctx context.Context
//   - hash common.Hash
func (_e *Client_Expecter) BlockByHash(ctx interface{}, hash interface{}) *Client_BlockByHash_Call {
	return &Client_BlockByHash_Call{Call: _e.mock.On("BlockByHash", ctx, hash)}
}

func (_c *Client_BlockByHash_Call) Run(run func(ctx context.Context, hash common.Hash)) *Client_BlockByHash_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *Client_BlockByHash_Call) Return(_a0 *types.Block, _a1 error) *Client_BlockByHash_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_BlockByHash_Call) RunAndReturn(run func(context.Context, common.Hash) (*types.Block, error)) *Client_BlockByHash_Call {
	_c.Call.Return(run)
	return _c
}

// BlockByNumber provides a mock function with given fields: ctx, number
func (_m *Client) BlockByNumber(ctx context.Context, number *big.Int) (*types.Block, error) {
	ret := _m.Called(ctx, number)

	if len(ret) == 0 {
		panic("no return value specified for BlockByNumber")
	}

	var r0 *types.Block
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) (*types.Block, error)); ok {
		return rf(ctx, number)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) *types.Block); ok {
		r0 = rf(ctx, number)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Block)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, number)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_BlockByNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BlockByNumber'
type Client_BlockByNumber_Call struct {
	*mock.Call
}

// BlockByNumber is a helper method to define mock.On call
//   - ctx context.Context
//   - number *big.Int
func (_e *Client_Expecter) BlockByNumber(ctx interface{}, number interface{}) *Client_BlockByNumber_Call {
	return &Client_BlockByNumber_Call{Call: _e.mock.On("BlockByNumber", ctx, number)}
}

func (_c *Client_BlockByNumber_Call) Run(run func(ctx context.Context, number *big.Int)) *Client_BlockByNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *Client_BlockByNumber_Call) Return(_a0 *types.Block, _a1 error) *Client_BlockByNumber_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_BlockByNumber_Call) RunAndReturn(run func(context.Context, *big.Int) (*types.Block, error)) *Client_BlockByNumber_Call {
	_c.Call.Return(run)
	return _c
}

// CallContext provides a mock function with given fields: ctx, result, method, args
func (_m *Client) CallContext(ctx context.Context, result interface{}, method string, args ...interface{}) error {
	var _ca []interface{}
	_ca = append(_ca, ctx, result, method)
	_ca = append(_ca, args...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CallContext")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, interface{}, string, ...interface{}) error); ok {
		r0 = rf(ctx, result, method, args...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_CallContext_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CallContext'
type Client_CallContext_Call struct {
	*mock.Call
}

// CallContext is a helper method to define mock.On call
//   - ctx context.Context
//   - result interface{}
//   - method string
//   - args ...interface{}
func (_e *Client_Expecter) CallContext(ctx interface{}, result interface{}, method interface{}, args ...interface{}) *Client_CallContext_Call {
	return &Client_CallContext_Call{Call: _e.mock.On("CallContext",
		append([]interface{}{ctx, result, method}, args...)...)}
}

func (_c *Client_CallContext_Call) Run(run func(ctx context.Context, result interface{}, method string, args ...interface{})) *Client_CallContext_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]interface{}, len(args)-3)
		for i, a := range args[3:] {
			if a != nil {
				variadicArgs[i] = a.(interface{})
			}
		}
		run(args[0].(context.Context), args[1].(interface{}), args[2].(string), variadicArgs...)
	})
	return _c
}

func (_c *Client_CallContext_Call) Return(_a0 error) *Client_CallContext_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_CallContext_Call) RunAndReturn(run func(context.Context, interface{}, string, ...interface{}) error) *Client_CallContext_Call {
	_c.Call.Return(run)
	return _c
}

// CallContract provides a mock function with given fields: ctx, msg, blockNumber
func (_m *Client) CallContract(ctx context.Context, msg ethereum.CallMsg, blockNumber *big.Int) ([]byte, error) {
	ret := _m.Called(ctx, msg, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for CallContract")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.CallMsg, *big.Int) ([]byte, error)); ok {
		return rf(ctx, msg, blockNumber)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.CallMsg, *big.Int) []byte); ok {
		r0 = rf(ctx, msg, blockNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ethereum.CallMsg, *big.Int) error); ok {
		r1 = rf(ctx, msg, blockNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_CallContract_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CallContract'
type Client_CallContract_Call struct {
	*mock.Call
}

// CallContract is a helper method to define mock.On call
//   - ctx context.Context
//   - msg ethereum.CallMsg
//   - blockNumber *big.Int
func (_e *Client_Expecter) CallContract(ctx interface{}, msg interface{}, blockNumber interface{}) *Client_CallContract_Call {
	return &Client_CallContract_Call{Call: _e.mock.On("CallContract", ctx, msg, blockNumber)}
}

func (_c *Client_CallContract_Call) Run(run func(ctx context.Context, msg ethereum.CallMsg, blockNumber *big.Int)) *Client_CallContract_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ethereum.CallMsg), args[2].(*big.Int))
	})
	return _c
}

func (_c *Client_CallContract_Call) Return(_a0 []byte, _a1 error) *Client_CallContract_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_CallContract_Call) RunAndReturn(run func(context.Context, ethereum.CallMsg, *big.Int) ([]byte, error)) *Client_CallContract_Call {
	_c.Call.Return(run)
	return _c
}

// CheckTxValidity provides a mock function with given fields: ctx, from, to, data
func (_m *Client) CheckTxValidity(ctx context.Context, from common.Address, to common.Address, data []byte) *client.SendError {
	ret := _m.Called(ctx, from, to, data)

	if len(ret) == 0 {
		panic("no return value specified for CheckTxValidity")
	}

	var r0 *client.SendError
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, common.Address, []byte) *client.SendError); ok {
		r0 = rf(ctx, from, to, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*client.SendError)
		}
	}

	return r0
}

// Client_CheckTxValidity_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckTxValidity'
type Client_CheckTxValidity_Call struct {
	*mock.Call
}

// CheckTxValidity is a helper method to define mock.On call
//   - ctx context.Context
//   - from common.Address
//   - to common.Address
//   - data []byte
func (_e *Client_Expecter) CheckTxValidity(ctx interface{}, from interface{}, to interface{}, data interface{}) *Client_CheckTxValidity_Call {
	return &Client_CheckTxValidity_Call{Call: _e.mock.On("CheckTxValidity", ctx, from, to, data)}
}

func (_c *Client_CheckTxValidity_Call) Run(run func(ctx context.Context, from common.Address, to common.Address, data []byte)) *Client_CheckTxValidity_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(common.Address), args[3].([]byte))
	})
	return _c
}

func (_c *Client_CheckTxValidity_Call) Return(_a0 *client.SendError) *Client_CheckTxValidity_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_CheckTxValidity_Call) RunAndReturn(run func(context.Context, common.Address, common.Address, []byte) *client.SendError) *Client_CheckTxValidity_Call {
	_c.Call.Return(run)
	return _c
}

// Close provides a mock function with no fields
func (_m *Client) Close() {
	_m.Called()
}

// Client_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Client_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *Client_Expecter) Close() *Client_Close_Call {
	return &Client_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *Client_Close_Call) Run(run func()) *Client_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Client_Close_Call) Return() *Client_Close_Call {
	_c.Call.Return()
	return _c
}

func (_c *Client_Close_Call) RunAndReturn(run func()) *Client_Close_Call {
	_c.Run(run)
	return _c
}

// CodeAt provides a mock function with given fields: ctx, account, blockNumber
func (_m *Client) CodeAt(ctx context.Context, account common.Address, blockNumber *big.Int) ([]byte, error) {
	ret := _m.Called(ctx, account, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for CodeAt")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) ([]byte, error)); ok {
		return rf(ctx, account, blockNumber)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) []byte); ok {
		r0 = rf(ctx, account, blockNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, account, blockNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_CodeAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CodeAt'
type Client_CodeAt_Call struct {
	*mock.Call
}

// CodeAt is a helper method to define mock.On call
//   - ctx context.Context
//   - account common.Address
//   - blockNumber *big.Int
func (_e *Client_Expecter) CodeAt(ctx interface{}, account interface{}, blockNumber interface{}) *Client_CodeAt_Call {
	return &Client_CodeAt_Call{Call: _e.mock.On("CodeAt", ctx, account, blockNumber)}
}

func (_c *Client_CodeAt_Call) Run(run func(ctx context.Context, account common.Address, blockNumber *big.Int)) *Client_CodeAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *Client_CodeAt_Call) Return(_a0 []byte, _a1 error) *Client_CodeAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_CodeAt_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) ([]byte, error)) *Client_CodeAt_Call {
	_c.Call.Return(run)
	return _c
}

// ConfiguredChainID provides a mock function with no fields
func (_m *Client) ConfiguredChainID() *big.Int {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for ConfiguredChainID")
	}

	var r0 *big.Int
	if rf, ok := ret.Get(0).(func() *big.Int); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	return r0
}

// Client_ConfiguredChainID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ConfiguredChainID'
type Client_ConfiguredChainID_Call struct {
	*mock.Call
}

// ConfiguredChainID is a helper method to define mock.On call
func (_e *Client_Expecter) ConfiguredChainID() *Client_ConfiguredChainID_Call {
	return &Client_ConfiguredChainID_Call{Call: _e.mock.On("ConfiguredChainID")}
}

func (_c *Client_ConfiguredChainID_Call) Run(run func()) *Client_ConfiguredChainID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Client_ConfiguredChainID_Call) Return(_a0 *big.Int) *Client_ConfiguredChainID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_ConfiguredChainID_Call) RunAndReturn(run func() *big.Int) *Client_ConfiguredChainID_Call {
	_c.Call.Return(run)
	return _c
}

// Dial provides a mock function with given fields: ctx
func (_m *Client) Dial(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Dial")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_Dial_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Dial'
type Client_Dial_Call struct {
	*mock.Call
}

// Dial is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Client_Expecter) Dial(ctx interface{}) *Client_Dial_Call {
	return &Client_Dial_Call{Call: _e.mock.On("Dial", ctx)}
}

func (_c *Client_Dial_Call) Run(run func(ctx context.Context)) *Client_Dial_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Client_Dial_Call) Return(_a0 error) *Client_Dial_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_Dial_Call) RunAndReturn(run func(context.Context) error) *Client_Dial_Call {
	_c.Call.Return(run)
	return _c
}

// EstimateGas provides a mock function with given fields: ctx, call
func (_m *Client) EstimateGas(ctx context.Context, call ethereum.CallMsg) (uint64, error) {
	ret := _m.Called(ctx, call)

	if len(ret) == 0 {
		panic("no return value specified for EstimateGas")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.CallMsg) (uint64, error)); ok {
		return rf(ctx, call)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.CallMsg) uint64); ok {
		r0 = rf(ctx, call)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, ethereum.CallMsg) error); ok {
		r1 = rf(ctx, call)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_EstimateGas_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'EstimateGas'
type Client_EstimateGas_Call struct {
	*mock.Call
}

// EstimateGas is a helper method to define mock.On call
//   - ctx context.Context
//   - call ethereum.CallMsg
func (_e *Client_Expecter) EstimateGas(ctx interface{}, call interface{}) *Client_EstimateGas_Call {
	return &Client_EstimateGas_Call{Call: _e.mock.On("EstimateGas", ctx, call)}
}

func (_c *Client_EstimateGas_Call) Run(run func(ctx context.Context, call ethereum.CallMsg)) *Client_EstimateGas_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ethereum.CallMsg))
	})
	return _c
}

func (_c *Client_EstimateGas_Call) Return(_a0 uint64, _a1 error) *Client_EstimateGas_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_EstimateGas_Call) RunAndReturn(run func(context.Context, ethereum.CallMsg) (uint64, error)) *Client_EstimateGas_Call {
	_c.Call.Return(run)
	return _c
}

// FeeHistory provides a mock function with given fields: ctx, blockCount, lastBlock, rewardPercentiles
func (_m *Client) FeeHistory(ctx context.Context, blockCount uint64, lastBlock *big.Int, rewardPercentiles []float64) (*ethereum.FeeHistory, error) {
	ret := _m.Called(ctx, blockCount, lastBlock, rewardPercentiles)

	if len(ret) == 0 {
		panic("no return value specified for FeeHistory")
	}

	var r0 *ethereum.FeeHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uint64, *big.Int, []float64) (*ethereum.FeeHistory, error)); ok {
		return rf(ctx, blockCount, lastBlock, rewardPercentiles)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uint64, *big.Int, []float64) *ethereum.FeeHistory); ok {
		r0 = rf(ctx, blockCount, lastBlock, rewardPercentiles)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ethereum.FeeHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uint64, *big.Int, []float64) error); ok {
		r1 = rf(ctx, blockCount, lastBlock, rewardPercentiles)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_FeeHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FeeHistory'
type Client_FeeHistory_Call struct {
	*mock.Call
}

// FeeHistory is a helper method to define mock.On call
//   - ctx context.Context
//   - blockCount uint64
//   - lastBlock *big.Int
//   - rewardPercentiles []float64
func (_e *Client_Expecter) FeeHistory(ctx interface{}, blockCount interface{}, lastBlock interface{}, rewardPercentiles interface{}) *Client_FeeHistory_Call {
	return &Client_FeeHistory_Call{Call: _e.mock.On("FeeHistory", ctx, blockCount, lastBlock, rewardPercentiles)}
}

func (_c *Client_FeeHistory_Call) Run(run func(ctx context.Context, blockCount uint64, lastBlock *big.Int, rewardPercentiles []float64)) *Client_FeeHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uint64), args[2].(*big.Int), args[3].([]float64))
	})
	return _c
}

func (_c *Client_FeeHistory_Call) Return(feeHistory *ethereum.FeeHistory, err error) *Client_FeeHistory_Call {
	_c.Call.Return(feeHistory, err)
	return _c
}

func (_c *Client_FeeHistory_Call) RunAndReturn(run func(context.Context, uint64, *big.Int, []float64) (*ethereum.FeeHistory, error)) *Client_FeeHistory_Call {
	_c.Call.Return(run)
	return _c
}

// FilterLogs provides a mock function with given fields: ctx, q
func (_m *Client) FilterLogs(ctx context.Context, q ethereum.FilterQuery) ([]types.Log, error) {
	ret := _m.Called(ctx, q)

	if len(ret) == 0 {
		panic("no return value specified for FilterLogs")
	}

	var r0 []types.Log
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.FilterQuery) ([]types.Log, error)); ok {
		return rf(ctx, q)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.FilterQuery) []types.Log); ok {
		r0 = rf(ctx, q)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]types.Log)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ethereum.FilterQuery) error); ok {
		r1 = rf(ctx, q)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_FilterLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FilterLogs'
type Client_FilterLogs_Call struct {
	*mock.Call
}

// FilterLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - q ethereum.FilterQuery
func (_e *Client_Expecter) FilterLogs(ctx interface{}, q interface{}) *Client_FilterLogs_Call {
	return &Client_FilterLogs_Call{Call: _e.mock.On("FilterLogs", ctx, q)}
}

func (_c *Client_FilterLogs_Call) Run(run func(ctx context.Context, q ethereum.FilterQuery)) *Client_FilterLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ethereum.FilterQuery))
	})
	return _c
}

func (_c *Client_FilterLogs_Call) Return(_a0 []types.Log, _a1 error) *Client_FilterLogs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_FilterLogs_Call) RunAndReturn(run func(context.Context, ethereum.FilterQuery) ([]types.Log, error)) *Client_FilterLogs_Call {
	_c.Call.Return(run)
	return _c
}

// HeadByHash provides a mock function with given fields: ctx, n
func (_m *Client) HeadByHash(ctx context.Context, n common.Hash) (*pkgtypes.Head, error) {
	ret := _m.Called(ctx, n)

	if len(ret) == 0 {
		panic("no return value specified for HeadByHash")
	}

	var r0 *pkgtypes.Head
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*pkgtypes.Head, error)); ok {
		return rf(ctx, n)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *pkgtypes.Head); ok {
		r0 = rf(ctx, n)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pkgtypes.Head)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, n)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_HeadByHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HeadByHash'
type Client_HeadByHash_Call struct {
	*mock.Call
}

// HeadByHash is a helper method to define mock.On call
//   - ctx context.Context
//   - n common.Hash
func (_e *Client_Expecter) HeadByHash(ctx interface{}, n interface{}) *Client_HeadByHash_Call {
	return &Client_HeadByHash_Call{Call: _e.mock.On("HeadByHash", ctx, n)}
}

func (_c *Client_HeadByHash_Call) Run(run func(ctx context.Context, n common.Hash)) *Client_HeadByHash_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *Client_HeadByHash_Call) Return(_a0 *pkgtypes.Head, _a1 error) *Client_HeadByHash_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_HeadByHash_Call) RunAndReturn(run func(context.Context, common.Hash) (*pkgtypes.Head, error)) *Client_HeadByHash_Call {
	_c.Call.Return(run)
	return _c
}

// HeadByNumber provides a mock function with given fields: ctx, n
func (_m *Client) HeadByNumber(ctx context.Context, n *big.Int) (*pkgtypes.Head, error) {
	ret := _m.Called(ctx, n)

	if len(ret) == 0 {
		panic("no return value specified for HeadByNumber")
	}

	var r0 *pkgtypes.Head
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) (*pkgtypes.Head, error)); ok {
		return rf(ctx, n)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) *pkgtypes.Head); ok {
		r0 = rf(ctx, n)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pkgtypes.Head)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, n)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_HeadByNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HeadByNumber'
type Client_HeadByNumber_Call struct {
	*mock.Call
}

// HeadByNumber is a helper method to define mock.On call
//   - ctx context.Context
//   - n *big.Int
func (_e *Client_Expecter) HeadByNumber(ctx interface{}, n interface{}) *Client_HeadByNumber_Call {
	return &Client_HeadByNumber_Call{Call: _e.mock.On("HeadByNumber", ctx, n)}
}

func (_c *Client_HeadByNumber_Call) Run(run func(ctx context.Context, n *big.Int)) *Client_HeadByNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *Client_HeadByNumber_Call) Return(_a0 *pkgtypes.Head, _a1 error) *Client_HeadByNumber_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_HeadByNumber_Call) RunAndReturn(run func(context.Context, *big.Int) (*pkgtypes.Head, error)) *Client_HeadByNumber_Call {
	_c.Call.Return(run)
	return _c
}

// HeaderByHash provides a mock function with given fields: ctx, h
func (_m *Client) HeaderByHash(ctx context.Context, h common.Hash) (*types.Header, error) {
	ret := _m.Called(ctx, h)

	if len(ret) == 0 {
		panic("no return value specified for HeaderByHash")
	}

	var r0 *types.Header
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*types.Header, error)); ok {
		return rf(ctx, h)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *types.Header); ok {
		r0 = rf(ctx, h)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Header)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, h)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_HeaderByHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HeaderByHash'
type Client_HeaderByHash_Call struct {
	*mock.Call
}

// HeaderByHash is a helper method to define mock.On call
//   - ctx context.Context
//   - h common.Hash
func (_e *Client_Expecter) HeaderByHash(ctx interface{}, h interface{}) *Client_HeaderByHash_Call {
	return &Client_HeaderByHash_Call{Call: _e.mock.On("HeaderByHash", ctx, h)}
}

func (_c *Client_HeaderByHash_Call) Run(run func(ctx context.Context, h common.Hash)) *Client_HeaderByHash_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *Client_HeaderByHash_Call) Return(_a0 *types.Header, _a1 error) *Client_HeaderByHash_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_HeaderByHash_Call) RunAndReturn(run func(context.Context, common.Hash) (*types.Header, error)) *Client_HeaderByHash_Call {
	_c.Call.Return(run)
	return _c
}

// HeaderByNumber provides a mock function with given fields: ctx, n
func (_m *Client) HeaderByNumber(ctx context.Context, n *big.Int) (*types.Header, error) {
	ret := _m.Called(ctx, n)

	if len(ret) == 0 {
		panic("no return value specified for HeaderByNumber")
	}

	var r0 *types.Header
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) (*types.Header, error)); ok {
		return rf(ctx, n)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *big.Int) *types.Header); ok {
		r0 = rf(ctx, n)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Header)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *big.Int) error); ok {
		r1 = rf(ctx, n)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_HeaderByNumber_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'HeaderByNumber'
type Client_HeaderByNumber_Call struct {
	*mock.Call
}

// HeaderByNumber is a helper method to define mock.On call
//   - ctx context.Context
//   - n *big.Int
func (_e *Client_Expecter) HeaderByNumber(ctx interface{}, n interface{}) *Client_HeaderByNumber_Call {
	return &Client_HeaderByNumber_Call{Call: _e.mock.On("HeaderByNumber", ctx, n)}
}

func (_c *Client_HeaderByNumber_Call) Run(run func(ctx context.Context, n *big.Int)) *Client_HeaderByNumber_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*big.Int))
	})
	return _c
}

func (_c *Client_HeaderByNumber_Call) Return(_a0 *types.Header, _a1 error) *Client_HeaderByNumber_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_HeaderByNumber_Call) RunAndReturn(run func(context.Context, *big.Int) (*types.Header, error)) *Client_HeaderByNumber_Call {
	_c.Call.Return(run)
	return _c
}

// IsL2 provides a mock function with no fields
func (_m *Client) IsL2() bool {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsL2")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func() bool); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// Client_IsL2_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsL2'
type Client_IsL2_Call struct {
	*mock.Call
}

// IsL2 is a helper method to define mock.On call
func (_e *Client_Expecter) IsL2() *Client_IsL2_Call {
	return &Client_IsL2_Call{Call: _e.mock.On("IsL2")}
}

func (_c *Client_IsL2_Call) Run(run func()) *Client_IsL2_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Client_IsL2_Call) Return(_a0 bool) *Client_IsL2_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_IsL2_Call) RunAndReturn(run func() bool) *Client_IsL2_Call {
	_c.Call.Return(run)
	return _c
}

// LINKBalance provides a mock function with given fields: ctx, address, linkAddress
func (_m *Client) LINKBalance(ctx context.Context, address common.Address, linkAddress common.Address) (*assets.Link, error) {
	ret := _m.Called(ctx, address, linkAddress)

	if len(ret) == 0 {
		panic("no return value specified for LINKBalance")
	}

	var r0 *assets.Link
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, common.Address) (*assets.Link, error)); ok {
		return rf(ctx, address, linkAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, common.Address) *assets.Link); ok {
		r0 = rf(ctx, address, linkAddress)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*assets.Link)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, common.Address) error); ok {
		r1 = rf(ctx, address, linkAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_LINKBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LINKBalance'
type Client_LINKBalance_Call struct {
	*mock.Call
}

// LINKBalance is a helper method to define mock.On call
//   - ctx context.Context
//   - address common.Address
//   - linkAddress common.Address
func (_e *Client_Expecter) LINKBalance(ctx interface{}, address interface{}, linkAddress interface{}) *Client_LINKBalance_Call {
	return &Client_LINKBalance_Call{Call: _e.mock.On("LINKBalance", ctx, address, linkAddress)}
}

func (_c *Client_LINKBalance_Call) Run(run func(ctx context.Context, address common.Address, linkAddress common.Address)) *Client_LINKBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(common.Address))
	})
	return _c
}

func (_c *Client_LINKBalance_Call) Return(_a0 *assets.Link, _a1 error) *Client_LINKBalance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_LINKBalance_Call) RunAndReturn(run func(context.Context, common.Address, common.Address) (*assets.Link, error)) *Client_LINKBalance_Call {
	_c.Call.Return(run)
	return _c
}

// LatestBlockHeight provides a mock function with given fields: ctx
func (_m *Client) LatestBlockHeight(ctx context.Context) (*big.Int, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for LatestBlockHeight")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*big.Int, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *big.Int); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_LatestBlockHeight_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestBlockHeight'
type Client_LatestBlockHeight_Call struct {
	*mock.Call
}

// LatestBlockHeight is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Client_Expecter) LatestBlockHeight(ctx interface{}) *Client_LatestBlockHeight_Call {
	return &Client_LatestBlockHeight_Call{Call: _e.mock.On("LatestBlockHeight", ctx)}
}

func (_c *Client_LatestBlockHeight_Call) Run(run func(ctx context.Context)) *Client_LatestBlockHeight_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Client_LatestBlockHeight_Call) Return(_a0 *big.Int, _a1 error) *Client_LatestBlockHeight_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_LatestBlockHeight_Call) RunAndReturn(run func(context.Context) (*big.Int, error)) *Client_LatestBlockHeight_Call {
	_c.Call.Return(run)
	return _c
}

// LatestFinalizedBlock provides a mock function with given fields: ctx
func (_m *Client) LatestFinalizedBlock(ctx context.Context) (*pkgtypes.Head, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for LatestFinalizedBlock")
	}

	var r0 *pkgtypes.Head
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*pkgtypes.Head, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *pkgtypes.Head); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pkgtypes.Head)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_LatestFinalizedBlock_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'LatestFinalizedBlock'
type Client_LatestFinalizedBlock_Call struct {
	*mock.Call
}

// LatestFinalizedBlock is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Client_Expecter) LatestFinalizedBlock(ctx interface{}) *Client_LatestFinalizedBlock_Call {
	return &Client_LatestFinalizedBlock_Call{Call: _e.mock.On("LatestFinalizedBlock", ctx)}
}

func (_c *Client_LatestFinalizedBlock_Call) Run(run func(ctx context.Context)) *Client_LatestFinalizedBlock_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Client_LatestFinalizedBlock_Call) Return(head *pkgtypes.Head, err error) *Client_LatestFinalizedBlock_Call {
	_c.Call.Return(head, err)
	return _c
}

func (_c *Client_LatestFinalizedBlock_Call) RunAndReturn(run func(context.Context) (*pkgtypes.Head, error)) *Client_LatestFinalizedBlock_Call {
	_c.Call.Return(run)
	return _c
}

// NodeStates provides a mock function with no fields
func (_m *Client) NodeStates() map[string]string {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for NodeStates")
	}

	var r0 map[string]string
	if rf, ok := ret.Get(0).(func() map[string]string); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	return r0
}

// Client_NodeStates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NodeStates'
type Client_NodeStates_Call struct {
	*mock.Call
}

// NodeStates is a helper method to define mock.On call
func (_e *Client_Expecter) NodeStates() *Client_NodeStates_Call {
	return &Client_NodeStates_Call{Call: _e.mock.On("NodeStates")}
}

func (_c *Client_NodeStates_Call) Run(run func()) *Client_NodeStates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Client_NodeStates_Call) Return(_a0 map[string]string) *Client_NodeStates_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_NodeStates_Call) RunAndReturn(run func() map[string]string) *Client_NodeStates_Call {
	_c.Call.Return(run)
	return _c
}

// NonceAt provides a mock function with given fields: ctx, account, blockNumber
func (_m *Client) NonceAt(ctx context.Context, account common.Address, blockNumber *big.Int) (uint64, error) {
	ret := _m.Called(ctx, account, blockNumber)

	if len(ret) == 0 {
		panic("no return value specified for NonceAt")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) (uint64, error)); ok {
		return rf(ctx, account, blockNumber)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, *big.Int) uint64); ok {
		r0 = rf(ctx, account, blockNumber)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, *big.Int) error); ok {
		r1 = rf(ctx, account, blockNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_NonceAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NonceAt'
type Client_NonceAt_Call struct {
	*mock.Call
}

// NonceAt is a helper method to define mock.On call
//   - ctx context.Context
//   - account common.Address
//   - blockNumber *big.Int
func (_e *Client_Expecter) NonceAt(ctx interface{}, account interface{}, blockNumber interface{}) *Client_NonceAt_Call {
	return &Client_NonceAt_Call{Call: _e.mock.On("NonceAt", ctx, account, blockNumber)}
}

func (_c *Client_NonceAt_Call) Run(run func(ctx context.Context, account common.Address, blockNumber *big.Int)) *Client_NonceAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(*big.Int))
	})
	return _c
}

func (_c *Client_NonceAt_Call) Return(_a0 uint64, _a1 error) *Client_NonceAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_NonceAt_Call) RunAndReturn(run func(context.Context, common.Address, *big.Int) (uint64, error)) *Client_NonceAt_Call {
	_c.Call.Return(run)
	return _c
}

// PendingCallContract provides a mock function with given fields: ctx, msg
func (_m *Client) PendingCallContract(ctx context.Context, msg ethereum.CallMsg) ([]byte, error) {
	ret := _m.Called(ctx, msg)

	if len(ret) == 0 {
		panic("no return value specified for PendingCallContract")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.CallMsg) ([]byte, error)); ok {
		return rf(ctx, msg)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.CallMsg) []byte); ok {
		r0 = rf(ctx, msg)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ethereum.CallMsg) error); ok {
		r1 = rf(ctx, msg)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_PendingCallContract_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PendingCallContract'
type Client_PendingCallContract_Call struct {
	*mock.Call
}

// PendingCallContract is a helper method to define mock.On call
//   - ctx context.Context
//   - msg ethereum.CallMsg
func (_e *Client_Expecter) PendingCallContract(ctx interface{}, msg interface{}) *Client_PendingCallContract_Call {
	return &Client_PendingCallContract_Call{Call: _e.mock.On("PendingCallContract", ctx, msg)}
}

func (_c *Client_PendingCallContract_Call) Run(run func(ctx context.Context, msg ethereum.CallMsg)) *Client_PendingCallContract_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ethereum.CallMsg))
	})
	return _c
}

func (_c *Client_PendingCallContract_Call) Return(_a0 []byte, _a1 error) *Client_PendingCallContract_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_PendingCallContract_Call) RunAndReturn(run func(context.Context, ethereum.CallMsg) ([]byte, error)) *Client_PendingCallContract_Call {
	_c.Call.Return(run)
	return _c
}

// PendingCodeAt provides a mock function with given fields: ctx, account
func (_m *Client) PendingCodeAt(ctx context.Context, account common.Address) ([]byte, error) {
	ret := _m.Called(ctx, account)

	if len(ret) == 0 {
		panic("no return value specified for PendingCodeAt")
	}

	var r0 []byte
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) ([]byte, error)); ok {
		return rf(ctx, account)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) []byte); ok {
		r0 = rf(ctx, account)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]byte)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address) error); ok {
		r1 = rf(ctx, account)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_PendingCodeAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PendingCodeAt'
type Client_PendingCodeAt_Call struct {
	*mock.Call
}

// PendingCodeAt is a helper method to define mock.On call
//   - ctx context.Context
//   - account common.Address
func (_e *Client_Expecter) PendingCodeAt(ctx interface{}, account interface{}) *Client_PendingCodeAt_Call {
	return &Client_PendingCodeAt_Call{Call: _e.mock.On("PendingCodeAt", ctx, account)}
}

func (_c *Client_PendingCodeAt_Call) Run(run func(ctx context.Context, account common.Address)) *Client_PendingCodeAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address))
	})
	return _c
}

func (_c *Client_PendingCodeAt_Call) Return(_a0 []byte, _a1 error) *Client_PendingCodeAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_PendingCodeAt_Call) RunAndReturn(run func(context.Context, common.Address) ([]byte, error)) *Client_PendingCodeAt_Call {
	_c.Call.Return(run)
	return _c
}

// PendingNonceAt provides a mock function with given fields: ctx, account
func (_m *Client) PendingNonceAt(ctx context.Context, account common.Address) (uint64, error) {
	ret := _m.Called(ctx, account)

	if len(ret) == 0 {
		panic("no return value specified for PendingNonceAt")
	}

	var r0 uint64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) (uint64, error)); ok {
		return rf(ctx, account)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address) uint64); ok {
		r0 = rf(ctx, account)
	} else {
		r0 = ret.Get(0).(uint64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address) error); ok {
		r1 = rf(ctx, account)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_PendingNonceAt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PendingNonceAt'
type Client_PendingNonceAt_Call struct {
	*mock.Call
}

// PendingNonceAt is a helper method to define mock.On call
//   - ctx context.Context
//   - account common.Address
func (_e *Client_Expecter) PendingNonceAt(ctx interface{}, account interface{}) *Client_PendingNonceAt_Call {
	return &Client_PendingNonceAt_Call{Call: _e.mock.On("PendingNonceAt", ctx, account)}
}

func (_c *Client_PendingNonceAt_Call) Run(run func(ctx context.Context, account common.Address)) *Client_PendingNonceAt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address))
	})
	return _c
}

func (_c *Client_PendingNonceAt_Call) Return(_a0 uint64, _a1 error) *Client_PendingNonceAt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_PendingNonceAt_Call) RunAndReturn(run func(context.Context, common.Address) (uint64, error)) *Client_PendingNonceAt_Call {
	_c.Call.Return(run)
	return _c
}

// SendTransaction provides a mock function with given fields: ctx, tx
func (_m *Client) SendTransaction(ctx context.Context, tx *types.Transaction) error {
	ret := _m.Called(ctx, tx)

	if len(ret) == 0 {
		panic("no return value specified for SendTransaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *types.Transaction) error); ok {
		r0 = rf(ctx, tx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Client_SendTransaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendTransaction'
type Client_SendTransaction_Call struct {
	*mock.Call
}

// SendTransaction is a helper method to define mock.On call
//   - ctx context.Context
//   - tx *types.Transaction
func (_e *Client_Expecter) SendTransaction(ctx interface{}, tx interface{}) *Client_SendTransaction_Call {
	return &Client_SendTransaction_Call{Call: _e.mock.On("SendTransaction", ctx, tx)}
}

func (_c *Client_SendTransaction_Call) Run(run func(ctx context.Context, tx *types.Transaction)) *Client_SendTransaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*types.Transaction))
	})
	return _c
}

func (_c *Client_SendTransaction_Call) Return(_a0 error) *Client_SendTransaction_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Client_SendTransaction_Call) RunAndReturn(run func(context.Context, *types.Transaction) error) *Client_SendTransaction_Call {
	_c.Call.Return(run)
	return _c
}

// SendTransactionReturnCode provides a mock function with given fields: ctx, tx, fromAddress
func (_m *Client) SendTransactionReturnCode(ctx context.Context, tx *types.Transaction, fromAddress common.Address) (multinode.SendTxReturnCode, error) {
	ret := _m.Called(ctx, tx, fromAddress)

	if len(ret) == 0 {
		panic("no return value specified for SendTransactionReturnCode")
	}

	var r0 multinode.SendTxReturnCode
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *types.Transaction, common.Address) (multinode.SendTxReturnCode, error)); ok {
		return rf(ctx, tx, fromAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *types.Transaction, common.Address) multinode.SendTxReturnCode); ok {
		r0 = rf(ctx, tx, fromAddress)
	} else {
		r0 = ret.Get(0).(multinode.SendTxReturnCode)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *types.Transaction, common.Address) error); ok {
		r1 = rf(ctx, tx, fromAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_SendTransactionReturnCode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendTransactionReturnCode'
type Client_SendTransactionReturnCode_Call struct {
	*mock.Call
}

// SendTransactionReturnCode is a helper method to define mock.On call
//   - ctx context.Context
//   - tx *types.Transaction
//   - fromAddress common.Address
func (_e *Client_Expecter) SendTransactionReturnCode(ctx interface{}, tx interface{}, fromAddress interface{}) *Client_SendTransactionReturnCode_Call {
	return &Client_SendTransactionReturnCode_Call{Call: _e.mock.On("SendTransactionReturnCode", ctx, tx, fromAddress)}
}

func (_c *Client_SendTransactionReturnCode_Call) Run(run func(ctx context.Context, tx *types.Transaction, fromAddress common.Address)) *Client_SendTransactionReturnCode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(*types.Transaction), args[2].(common.Address))
	})
	return _c
}

func (_c *Client_SendTransactionReturnCode_Call) Return(_a0 multinode.SendTxReturnCode, _a1 error) *Client_SendTransactionReturnCode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_SendTransactionReturnCode_Call) RunAndReturn(run func(context.Context, *types.Transaction, common.Address) (multinode.SendTxReturnCode, error)) *Client_SendTransactionReturnCode_Call {
	_c.Call.Return(run)
	return _c
}

// SubscribeFilterLogs provides a mock function with given fields: ctx, q, ch
func (_m *Client) SubscribeFilterLogs(ctx context.Context, q ethereum.FilterQuery, ch chan<- types.Log) (ethereum.Subscription, error) {
	ret := _m.Called(ctx, q, ch)

	if len(ret) == 0 {
		panic("no return value specified for SubscribeFilterLogs")
	}

	var r0 ethereum.Subscription
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.FilterQuery, chan<- types.Log) (ethereum.Subscription, error)); ok {
		return rf(ctx, q, ch)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ethereum.FilterQuery, chan<- types.Log) ethereum.Subscription); ok {
		r0 = rf(ctx, q, ch)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ethereum.Subscription)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ethereum.FilterQuery, chan<- types.Log) error); ok {
		r1 = rf(ctx, q, ch)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_SubscribeFilterLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubscribeFilterLogs'
type Client_SubscribeFilterLogs_Call struct {
	*mock.Call
}

// SubscribeFilterLogs is a helper method to define mock.On call
//   - ctx context.Context
//   - q ethereum.FilterQuery
//   - ch chan<- types.Log
func (_e *Client_Expecter) SubscribeFilterLogs(ctx interface{}, q interface{}, ch interface{}) *Client_SubscribeFilterLogs_Call {
	return &Client_SubscribeFilterLogs_Call{Call: _e.mock.On("SubscribeFilterLogs", ctx, q, ch)}
}

func (_c *Client_SubscribeFilterLogs_Call) Run(run func(ctx context.Context, q ethereum.FilterQuery, ch chan<- types.Log)) *Client_SubscribeFilterLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(ethereum.FilterQuery), args[2].(chan<- types.Log))
	})
	return _c
}

func (_c *Client_SubscribeFilterLogs_Call) Return(_a0 ethereum.Subscription, _a1 error) *Client_SubscribeFilterLogs_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_SubscribeFilterLogs_Call) RunAndReturn(run func(context.Context, ethereum.FilterQuery, chan<- types.Log) (ethereum.Subscription, error)) *Client_SubscribeFilterLogs_Call {
	_c.Call.Return(run)
	return _c
}

// SubscribeToHeads provides a mock function with given fields: ctx
func (_m *Client) SubscribeToHeads(ctx context.Context) (<-chan *pkgtypes.Head, ethereum.Subscription, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for SubscribeToHeads")
	}

	var r0 <-chan *pkgtypes.Head
	var r1 ethereum.Subscription
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context) (<-chan *pkgtypes.Head, ethereum.Subscription, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) <-chan *pkgtypes.Head); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(<-chan *pkgtypes.Head)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) ethereum.Subscription); ok {
		r1 = rf(ctx)
	} else {
		if ret.Get(1) != nil {
			r1 = ret.Get(1).(ethereum.Subscription)
		}
	}

	if rf, ok := ret.Get(2).(func(context.Context) error); ok {
		r2 = rf(ctx)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// Client_SubscribeToHeads_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SubscribeToHeads'
type Client_SubscribeToHeads_Call struct {
	*mock.Call
}

// SubscribeToHeads is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Client_Expecter) SubscribeToHeads(ctx interface{}) *Client_SubscribeToHeads_Call {
	return &Client_SubscribeToHeads_Call{Call: _e.mock.On("SubscribeToHeads", ctx)}
}

func (_c *Client_SubscribeToHeads_Call) Run(run func(ctx context.Context)) *Client_SubscribeToHeads_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Client_SubscribeToHeads_Call) Return(_a0 <-chan *pkgtypes.Head, _a1 ethereum.Subscription, _a2 error) *Client_SubscribeToHeads_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *Client_SubscribeToHeads_Call) RunAndReturn(run func(context.Context) (<-chan *pkgtypes.Head, ethereum.Subscription, error)) *Client_SubscribeToHeads_Call {
	_c.Call.Return(run)
	return _c
}

// SuggestGasPrice provides a mock function with given fields: ctx
func (_m *Client) SuggestGasPrice(ctx context.Context) (*big.Int, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for SuggestGasPrice")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*big.Int, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *big.Int); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_SuggestGasPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SuggestGasPrice'
type Client_SuggestGasPrice_Call struct {
	*mock.Call
}

// SuggestGasPrice is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Client_Expecter) SuggestGasPrice(ctx interface{}) *Client_SuggestGasPrice_Call {
	return &Client_SuggestGasPrice_Call{Call: _e.mock.On("SuggestGasPrice", ctx)}
}

func (_c *Client_SuggestGasPrice_Call) Run(run func(ctx context.Context)) *Client_SuggestGasPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Client_SuggestGasPrice_Call) Return(_a0 *big.Int, _a1 error) *Client_SuggestGasPrice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_SuggestGasPrice_Call) RunAndReturn(run func(context.Context) (*big.Int, error)) *Client_SuggestGasPrice_Call {
	_c.Call.Return(run)
	return _c
}

// SuggestGasTipCap provides a mock function with given fields: ctx
func (_m *Client) SuggestGasTipCap(ctx context.Context) (*big.Int, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for SuggestGasTipCap")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*big.Int, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *big.Int); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_SuggestGasTipCap_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SuggestGasTipCap'
type Client_SuggestGasTipCap_Call struct {
	*mock.Call
}

// SuggestGasTipCap is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Client_Expecter) SuggestGasTipCap(ctx interface{}) *Client_SuggestGasTipCap_Call {
	return &Client_SuggestGasTipCap_Call{Call: _e.mock.On("SuggestGasTipCap", ctx)}
}

func (_c *Client_SuggestGasTipCap_Call) Run(run func(ctx context.Context)) *Client_SuggestGasTipCap_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *Client_SuggestGasTipCap_Call) Return(_a0 *big.Int, _a1 error) *Client_SuggestGasTipCap_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_SuggestGasTipCap_Call) RunAndReturn(run func(context.Context) (*big.Int, error)) *Client_SuggestGasTipCap_Call {
	_c.Call.Return(run)
	return _c
}

// TokenBalance provides a mock function with given fields: ctx, address, contractAddress
func (_m *Client) TokenBalance(ctx context.Context, address common.Address, contractAddress common.Address) (*big.Int, error) {
	ret := _m.Called(ctx, address, contractAddress)

	if len(ret) == 0 {
		panic("no return value specified for TokenBalance")
	}

	var r0 *big.Int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, common.Address) (*big.Int, error)); ok {
		return rf(ctx, address, contractAddress)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Address, common.Address) *big.Int); ok {
		r0 = rf(ctx, address, contractAddress)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*big.Int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Address, common.Address) error); ok {
		r1 = rf(ctx, address, contractAddress)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_TokenBalance_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TokenBalance'
type Client_TokenBalance_Call struct {
	*mock.Call
}

// TokenBalance is a helper method to define mock.On call
//   - ctx context.Context
//   - address common.Address
//   - contractAddress common.Address
func (_e *Client_Expecter) TokenBalance(ctx interface{}, address interface{}, contractAddress interface{}) *Client_TokenBalance_Call {
	return &Client_TokenBalance_Call{Call: _e.mock.On("TokenBalance", ctx, address, contractAddress)}
}

func (_c *Client_TokenBalance_Call) Run(run func(ctx context.Context, address common.Address, contractAddress common.Address)) *Client_TokenBalance_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Address), args[2].(common.Address))
	})
	return _c
}

func (_c *Client_TokenBalance_Call) Return(_a0 *big.Int, _a1 error) *Client_TokenBalance_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_TokenBalance_Call) RunAndReturn(run func(context.Context, common.Address, common.Address) (*big.Int, error)) *Client_TokenBalance_Call {
	_c.Call.Return(run)
	return _c
}

// TransactionByHash provides a mock function with given fields: ctx, txHash
func (_m *Client) TransactionByHash(ctx context.Context, txHash common.Hash) (*types.Transaction, error) {
	ret := _m.Called(ctx, txHash)

	if len(ret) == 0 {
		panic("no return value specified for TransactionByHash")
	}

	var r0 *types.Transaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*types.Transaction, error)); ok {
		return rf(ctx, txHash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *types.Transaction); ok {
		r0 = rf(ctx, txHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Transaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, txHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_TransactionByHash_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransactionByHash'
type Client_TransactionByHash_Call struct {
	*mock.Call
}

// TransactionByHash is a helper method to define mock.On call
//   - ctx context.Context
//   - txHash common.Hash
func (_e *Client_Expecter) TransactionByHash(ctx interface{}, txHash interface{}) *Client_TransactionByHash_Call {
	return &Client_TransactionByHash_Call{Call: _e.mock.On("TransactionByHash", ctx, txHash)}
}

func (_c *Client_TransactionByHash_Call) Run(run func(ctx context.Context, txHash common.Hash)) *Client_TransactionByHash_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *Client_TransactionByHash_Call) Return(_a0 *types.Transaction, _a1 error) *Client_TransactionByHash_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_TransactionByHash_Call) RunAndReturn(run func(context.Context, common.Hash) (*types.Transaction, error)) *Client_TransactionByHash_Call {
	_c.Call.Return(run)
	return _c
}

// TransactionReceipt provides a mock function with given fields: ctx, txHash
func (_m *Client) TransactionReceipt(ctx context.Context, txHash common.Hash) (*types.Receipt, error) {
	ret := _m.Called(ctx, txHash)

	if len(ret) == 0 {
		panic("no return value specified for TransactionReceipt")
	}

	var r0 *types.Receipt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) (*types.Receipt, error)); ok {
		return rf(ctx, txHash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, common.Hash) *types.Receipt); ok {
		r0 = rf(ctx, txHash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*types.Receipt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, common.Hash) error); ok {
		r1 = rf(ctx, txHash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Client_TransactionReceipt_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'TransactionReceipt'
type Client_TransactionReceipt_Call struct {
	*mock.Call
}

// TransactionReceipt is a helper method to define mock.On call
//   - ctx context.Context
//   - txHash common.Hash
func (_e *Client_Expecter) TransactionReceipt(ctx interface{}, txHash interface{}) *Client_TransactionReceipt_Call {
	return &Client_TransactionReceipt_Call{Call: _e.mock.On("TransactionReceipt", ctx, txHash)}
}

func (_c *Client_TransactionReceipt_Call) Run(run func(ctx context.Context, txHash common.Hash)) *Client_TransactionReceipt_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(common.Hash))
	})
	return _c
}

func (_c *Client_TransactionReceipt_Call) Return(_a0 *types.Receipt, _a1 error) *Client_TransactionReceipt_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Client_TransactionReceipt_Call) RunAndReturn(run func(context.Context, common.Hash) (*types.Receipt, error)) *Client_TransactionReceipt_Call {
	_c.Call.Return(run)
	return _c
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
