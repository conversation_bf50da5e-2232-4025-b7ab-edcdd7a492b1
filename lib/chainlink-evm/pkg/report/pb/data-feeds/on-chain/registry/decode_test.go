//nolint:govet, testifylint // disable govet, testifylint
package registry

import (
	"encoding/base64"
	"math"
	"math/big"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/smartcontractkit/chainlink-evm/pkg/report/datafeeds"
	wt_msg "github.com/smartcontractkit/chainlink-evm/pkg/report/pb/platform"
)

func TestDecodeAsReportProcessed(t *testing.T) {
	// Base64-encoded report data (example)
	// version | workflow_execution_id | timestamp | don_id | config_version | ... | data
	encoded := "AYFtgPpLuLNQysw6LjlSNrzGuBOwVoth7qC9PmunIY3TZvW/cAAAAAEAAAABvAbzAOeX1ahXVjehSq4T4/hQgAjR/FT0xGEf/xemjLAwMDAwRk9PQkFSAAAAAAAAAAAAAAAAAAAAAAAAAKoAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAAMREREREREREQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEgAAMREREREREREQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZvW/aQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABm9b9pAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAElCUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASUJQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABnBQGpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAElCUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASUJQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABJQlAAMiIiIiIiIiIgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEgAAMiIiIiIiIiIgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZvW/aQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABm9b9pAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAElCUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASUJQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABnBQGpAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAElCUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAASUJQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABJQl"

	// Decode the base64 data
	rawReport, err := base64.StdEncoding.DecodeString(encoded)
	require.NoError(t, err)

	// Define test cases
	tests := []struct {
		name     string
		input    wt_msg.WriteConfirmed
		expected []FeedUpdated
		wantErr  bool
	}{
		{
			name: "Valid input",
			input: wt_msg.WriteConfirmed{
				Node:      "example-node",
				Forwarder: "example-forwarder",
				Receiver:  "example-receiver",

				// Report Info
				ReportId:      123,
				ReportContext: []byte{},
				Report:        rawReport, // Example valid byte slice
				SignersNum:    2,

				// Transmission Info
				Transmitter: "example-transmitter",
				Success:     true,

				// Block Info
				BlockHash:      "0xaa",
				BlockHeight:    "17",
				BlockTimestamp: 0x66f5bf69,
			},
			expected: []FeedUpdated{
				{
					FeedId:                "0x0003111111111111111100000000000000000000000000000000000000000000",
					ObservationsTimestamp: 0x66f5bf69,
					Benchmark:             []uint8{0x4, 0x94, 0x25},
					Report:                []uint8{0x0, 0x3, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x66, 0xf5, 0xbf, 0x69, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x66, 0xf5, 0xbf, 0x69, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x67, 0x5, 0x1, 0xa9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25},

					BenchmarkVal: math.NaN(),

					BlockHash:      "0xaa",
					BlockHeight:    "17",
					BlockTimestamp: 0x66f5bf69,

					TxSender:   "example-transmitter",
					TxReceiver: "example-forwarder",
				},
				{
					FeedId:                "0x0003222222222222222200000000000000000000000000000000000000000000",
					ObservationsTimestamp: 0x66f5bf69,
					Benchmark:             []uint8{0x4, 0x94, 0x25},
					Report:                []uint8{0x0, 0x3, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x66, 0xf5, 0xbf, 0x69, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x66, 0xf5, 0xbf, 0x69, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x67, 0x5, 0x1, 0xa9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x94, 0x25},

					BenchmarkVal: 3000.69,

					BlockHash:      "0xaa",
					BlockHeight:    "17",
					BlockTimestamp: 0x66f5bf69,

					TxSender:   "example-transmitter",
					TxReceiver: "example-forwarder",
				},
			},
			wantErr: false,
		},
		{
			name: "Invalid input",
			input: wt_msg.WriteConfirmed{
				Node:      "example-node",
				Forwarder: "example-forwarder",
				Receiver:  "example-receiver",

				// Report Info
				ReportId:      123,
				ReportContext: []byte{},
				Report:        []byte{0x01, 0x02, 0x03, 0x04}, // Example invalid byte slice
				SignersNum:    2,

				// Transmission Info
				Transmitter: "example-transmitter",
				Success:     true,
			},
			expected: []FeedUpdated{},
			wantErr:  true,
		},
		// Add more test cases as needed
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := DecodeAsFeedUpdated(&tt.input)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, len(tt.expected), len(result))

				for i, m := range tt.expected {
					// Notice: if BenchmarkVal is NaN we can't compare directly
					if math.IsNaN(m.BenchmarkVal) {
						require.True(t, math.IsNaN(result[i].BenchmarkVal))
						// Skip the comparison (nullify the value)
						m.BenchmarkVal = -1
						result[i].BenchmarkVal = -1
					}
					// Finally, compare the values
					require.Equal(t, m, *result[i])
				}
			}
		})
	}
}

func TestToBenchmarkVal(t *testing.T) {
	// Helper function to set a big.Int value (base 10)
	mustSetString := func(s string) *big.Int {
		val, _ := new(big.Int).SetString(s, 10)
		return val
	}

	tests := []struct {
		name             string
		feedID           string
		val              *big.Int
		expected         float64
		expectedDecimals uint8
	}{
		{
			name:             "Number (price value) with 18 decimals",
			feedID:           "018e16c39e000032000000000000000000000000000000000000000000000000",
			val:              big.NewInt(1000000000000000000),
			expected:         1.0,
			expectedDecimals: 18,
		},
		{
			name:             "Number (price value) with 8 decimals",
			feedID:           "01e880c2b3000028000000000000000000000000000000000000000000000000",
			val:              big.NewInt(1000000000000000000),
			expected:         10000000000.0,
			expectedDecimals: 8,
		},
		{
			name:             "Number (price value) with 18 decimals - feed ID #2",
			feedID:           "01e880c2b3000132000000000000000000000000000000000000000000000000",
			val:              big.NewInt(1000000012340000000), // 1 ETH
			expected:         1.00000001234,
			expectedDecimals: 18,
		},
		{
			name:             "Number (24-hour global volume) as integer",
			feedID:           "01e880c2b3000820000000000000000000000000000000000000000000000000",
			val:              big.NewInt(1000000000000000000), // 1 ETH
			expected:         1000000000000000000.0,
			expectedDecimals: 0,
		},
		{
			name:             "Number (price value) with 18 decimals - feed ID #3",
			feedID:           "018933b5e4001032000000000000000000000000000000000000000000000000",
			val:              big.NewInt(1000000000000000087), // 1 ETH
			expected:         1.000000000000000087,
			expectedDecimals: 18,
		},
		{
			name:             "NaN value (NAV issuer name) as a string",
			feedID:           "018933b5e4001101000000000000000000000000000000000000000000000000",
			val:              big.NewInt(1000000000000000000),
			expected:         math.NaN(),
			expectedDecimals: 0,
		},
		{
			name:             "NaN value (NAV registry location) as an address",
			feedID:           "018933b5e4001202000000000000000000000000000000000000000000000000",
			val:              big.NewInt(1000000000000000000),
			expected:         math.NaN(),
			expectedDecimals: 0,
		},
		{
			name:             "Number (price value) with 18 decimals - feed ID #4",
			feedID:           "011e22d6bf000332000000000000000000000000000000000000000000000000",
			val:              mustSetString("9990000000000000009"),
			expected:         9.990000000000000009,
			expectedDecimals: 18,
		},
		{
			name:             "Number (price value) with 8 decimals - feed ID #2",
			feedID:           "01a80ff216000328000000000000000000000000000000000000000000000000",
			val:              mustSetString("9990000000000000009"),
			expected:         99900000000.00000009,
			expectedDecimals: 8,
		},
		{
			name:             "Number (price value) with 8 decimals - feed ID #2 - huge number (overflow)",
			feedID:           "01a80ff216000328000000000000000000000000000000000000000000000000",
			val:              new(big.Int).Exp(big.NewInt(10), big.NewInt(400), nil), // Very large number
			expected:         math.Inf(1),                                            // positive infinity
			expectedDecimals: 8,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			feedID, err := datafeeds.NewFeedIDFromHex(tt.feedID)
			require.NoError(t, err)

			decimals, isNumber := datafeeds.GetDecimals(feedID.GetDataType())

			result := toBenchmarkVal(feedID, tt.val)
			if math.IsNaN(tt.expected) {
				require.False(t, isNumber)
				require.True(t, math.IsNaN(result))
			} else {
				require.True(t, isNumber)
				require.Equal(t, tt.expected, result)
				require.Equal(t, tt.expectedDecimals, decimals)
			}
		})
	}
}
