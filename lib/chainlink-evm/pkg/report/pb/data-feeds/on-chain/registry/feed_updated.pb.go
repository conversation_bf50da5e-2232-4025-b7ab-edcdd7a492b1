// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: feed_updated.proto

package registry

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The on-chain FeedUpdated event which is extracted from the tx/event data or
// the write-target WriteConfirmed event, after a write was confirmed (@see message: write-target.WriteConfirmed).
type FeedUpdated struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Event data
	FeedId                string `protobuf:"bytes,1,opt,name=feed_id,json=feedId,proto3" json:"feed_id,omitempty"` // bytes as hex string for readability
	ObservationsTimestamp uint32 `protobuf:"varint,2,opt,name=observations_timestamp,json=observationsTimestamp,proto3" json:"observations_timestamp,omitempty"`
	Benchmark             []byte `protobuf:"bytes,3,opt,name=benchmark,proto3" json:"benchmark,omitempty"`
	Report                []byte `protobuf:"bytes,4,opt,name=report,proto3" json:"report,omitempty"`
	// Notice: benchmark_val is the benchmark i192 on-chain value decoded as an double (float64), scaled by number of decimals (e.g., 1e-18)
	// This is the largest type Prometheus supports, and this conversion can overflow but so far was sufficient
	// for most use-cases. For big numbers, benchmark bytes should be used instead.
	//
	// Set as `math.NaN()` if report data type not a number, or `+/-Inf` if number doesn't fit in double.
	BenchmarkVal float64 `protobuf:"fixed64,5,opt,name=benchmark_val,json=benchmarkVal,proto3" json:"benchmark_val,omitempty"`
	// Head data - when was the event produced on-chain
	BlockHash      string `protobuf:"bytes,6,opt,name=block_hash,json=blockHash,proto3" json:"block_hash,omitempty"`
	BlockHeight    string `protobuf:"bytes,7,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
	BlockTimestamp uint64 `protobuf:"varint,8,opt,name=block_timestamp,json=blockTimestamp,proto3" json:"block_timestamp,omitempty"`
	// Transaction data - info about the tx that mained the event (optional)
	TxId       string `protobuf:"bytes,10,opt,name=tx_id,json=txId,proto3" json:"tx_id,omitempty"` // TXM ref
	TxHash     string `protobuf:"bytes,11,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	TxSender   string `protobuf:"bytes,12,opt,name=tx_sender,json=txSender,proto3" json:"tx_sender,omitempty"`
	TxReceiver string `protobuf:"bytes,13,opt,name=tx_receiver,json=txReceiver,proto3" json:"tx_receiver,omitempty"`
	TxStatus   string `protobuf:"bytes,14,opt,name=tx_status,json=txStatus,proto3" json:"tx_status,omitempty"`
	// [Execution Context]
	// TODO: replace with a proto reference once supported
	// Execution Context - Source
	MetaSourceId string `protobuf:"bytes,20,opt,name=meta_source_id,json=metaSourceId,proto3" json:"meta_source_id,omitempty"`
	// Execution Context - Chain
	MetaChainFamilyName string `protobuf:"bytes,21,opt,name=meta_chain_family_name,json=metaChainFamilyName,proto3" json:"meta_chain_family_name,omitempty"`
	MetaChainId         string `protobuf:"bytes,22,opt,name=meta_chain_id,json=metaChainId,proto3" json:"meta_chain_id,omitempty"`
	MetaNetworkName     string `protobuf:"bytes,23,opt,name=meta_network_name,json=metaNetworkName,proto3" json:"meta_network_name,omitempty"`
	MetaNetworkNameFull string `protobuf:"bytes,24,opt,name=meta_network_name_full,json=metaNetworkNameFull,proto3" json:"meta_network_name_full,omitempty"`
	// Execution Context - Workflow (capabilities.RequestMetadata)
	MetaWorkflowId               string `protobuf:"bytes,25,opt,name=meta_workflow_id,json=metaWorkflowId,proto3" json:"meta_workflow_id,omitempty"`
	MetaWorkflowOwner            string `protobuf:"bytes,26,opt,name=meta_workflow_owner,json=metaWorkflowOwner,proto3" json:"meta_workflow_owner,omitempty"`
	MetaWorkflowExecutionId      string `protobuf:"bytes,27,opt,name=meta_workflow_execution_id,json=metaWorkflowExecutionId,proto3" json:"meta_workflow_execution_id,omitempty"`
	MetaWorkflowName             string `protobuf:"bytes,28,opt,name=meta_workflow_name,json=metaWorkflowName,proto3" json:"meta_workflow_name,omitempty"`
	MetaWorkflowDonId            uint32 `protobuf:"varint,29,opt,name=meta_workflow_don_id,json=metaWorkflowDonId,proto3" json:"meta_workflow_don_id,omitempty"`
	MetaWorkflowDonConfigVersion uint32 `protobuf:"varint,30,opt,name=meta_workflow_don_config_version,json=metaWorkflowDonConfigVersion,proto3" json:"meta_workflow_don_config_version,omitempty"`
	MetaReferenceId              string `protobuf:"bytes,31,opt,name=meta_reference_id,json=metaReferenceId,proto3" json:"meta_reference_id,omitempty"`
	// Execution Context - Capability
	MetaCapabilityType           string `protobuf:"bytes,32,opt,name=meta_capability_type,json=metaCapabilityType,proto3" json:"meta_capability_type,omitempty"`
	MetaCapabilityId             string `protobuf:"bytes,33,opt,name=meta_capability_id,json=metaCapabilityId,proto3" json:"meta_capability_id,omitempty"`
	MetaCapabilityTimestampStart uint64 `protobuf:"varint,34,opt,name=meta_capability_timestamp_start,json=metaCapabilityTimestampStart,proto3" json:"meta_capability_timestamp_start,omitempty"`
	MetaCapabilityTimestampEmit  uint64 `protobuf:"varint,35,opt,name=meta_capability_timestamp_emit,json=metaCapabilityTimestampEmit,proto3" json:"meta_capability_timestamp_emit,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *FeedUpdated) Reset() {
	*x = FeedUpdated{}
	mi := &file_feed_updated_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeedUpdated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedUpdated) ProtoMessage() {}

func (x *FeedUpdated) ProtoReflect() protoreflect.Message {
	mi := &file_feed_updated_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedUpdated.ProtoReflect.Descriptor instead.
func (*FeedUpdated) Descriptor() ([]byte, []int) {
	return file_feed_updated_proto_rawDescGZIP(), []int{0}
}

func (x *FeedUpdated) GetFeedId() string {
	if x != nil {
		return x.FeedId
	}
	return ""
}

func (x *FeedUpdated) GetObservationsTimestamp() uint32 {
	if x != nil {
		return x.ObservationsTimestamp
	}
	return 0
}

func (x *FeedUpdated) GetBenchmark() []byte {
	if x != nil {
		return x.Benchmark
	}
	return nil
}

func (x *FeedUpdated) GetReport() []byte {
	if x != nil {
		return x.Report
	}
	return nil
}

func (x *FeedUpdated) GetBenchmarkVal() float64 {
	if x != nil {
		return x.BenchmarkVal
	}
	return 0
}

func (x *FeedUpdated) GetBlockHash() string {
	if x != nil {
		return x.BlockHash
	}
	return ""
}

func (x *FeedUpdated) GetBlockHeight() string {
	if x != nil {
		return x.BlockHeight
	}
	return ""
}

func (x *FeedUpdated) GetBlockTimestamp() uint64 {
	if x != nil {
		return x.BlockTimestamp
	}
	return 0
}

func (x *FeedUpdated) GetTxId() string {
	if x != nil {
		return x.TxId
	}
	return ""
}

func (x *FeedUpdated) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *FeedUpdated) GetTxSender() string {
	if x != nil {
		return x.TxSender
	}
	return ""
}

func (x *FeedUpdated) GetTxReceiver() string {
	if x != nil {
		return x.TxReceiver
	}
	return ""
}

func (x *FeedUpdated) GetTxStatus() string {
	if x != nil {
		return x.TxStatus
	}
	return ""
}

func (x *FeedUpdated) GetMetaSourceId() string {
	if x != nil {
		return x.MetaSourceId
	}
	return ""
}

func (x *FeedUpdated) GetMetaChainFamilyName() string {
	if x != nil {
		return x.MetaChainFamilyName
	}
	return ""
}

func (x *FeedUpdated) GetMetaChainId() string {
	if x != nil {
		return x.MetaChainId
	}
	return ""
}

func (x *FeedUpdated) GetMetaNetworkName() string {
	if x != nil {
		return x.MetaNetworkName
	}
	return ""
}

func (x *FeedUpdated) GetMetaNetworkNameFull() string {
	if x != nil {
		return x.MetaNetworkNameFull
	}
	return ""
}

func (x *FeedUpdated) GetMetaWorkflowId() string {
	if x != nil {
		return x.MetaWorkflowId
	}
	return ""
}

func (x *FeedUpdated) GetMetaWorkflowOwner() string {
	if x != nil {
		return x.MetaWorkflowOwner
	}
	return ""
}

func (x *FeedUpdated) GetMetaWorkflowExecutionId() string {
	if x != nil {
		return x.MetaWorkflowExecutionId
	}
	return ""
}

func (x *FeedUpdated) GetMetaWorkflowName() string {
	if x != nil {
		return x.MetaWorkflowName
	}
	return ""
}

func (x *FeedUpdated) GetMetaWorkflowDonId() uint32 {
	if x != nil {
		return x.MetaWorkflowDonId
	}
	return 0
}

func (x *FeedUpdated) GetMetaWorkflowDonConfigVersion() uint32 {
	if x != nil {
		return x.MetaWorkflowDonConfigVersion
	}
	return 0
}

func (x *FeedUpdated) GetMetaReferenceId() string {
	if x != nil {
		return x.MetaReferenceId
	}
	return ""
}

func (x *FeedUpdated) GetMetaCapabilityType() string {
	if x != nil {
		return x.MetaCapabilityType
	}
	return ""
}

func (x *FeedUpdated) GetMetaCapabilityId() string {
	if x != nil {
		return x.MetaCapabilityId
	}
	return ""
}

func (x *FeedUpdated) GetMetaCapabilityTimestampStart() uint64 {
	if x != nil {
		return x.MetaCapabilityTimestampStart
	}
	return 0
}

func (x *FeedUpdated) GetMetaCapabilityTimestampEmit() uint64 {
	if x != nil {
		return x.MetaCapabilityTimestampEmit
	}
	return 0
}

var File_feed_updated_proto protoreflect.FileDescriptor

const file_feed_updated_proto_rawDesc = "" +
	"\n" +
	"\x12feed_updated.proto\x12\x1bdatafeeds.on_chain.registry\"\xe2\t\n" +
	"\vFeedUpdated\x12\x17\n" +
	"\afeed_id\x18\x01 \x01(\tR\x06feedId\x125\n" +
	"\x16observations_timestamp\x18\x02 \x01(\rR\x15observationsTimestamp\x12\x1c\n" +
	"\tbenchmark\x18\x03 \x01(\fR\tbenchmark\x12\x16\n" +
	"\x06report\x18\x04 \x01(\fR\x06report\x12#\n" +
	"\rbenchmark_val\x18\x05 \x01(\x01R\fbenchmarkVal\x12\x1d\n" +
	"\n" +
	"block_hash\x18\x06 \x01(\tR\tblockHash\x12!\n" +
	"\fblock_height\x18\a \x01(\tR\vblockHeight\x12'\n" +
	"\x0fblock_timestamp\x18\b \x01(\x04R\x0eblockTimestamp\x12\x13\n" +
	"\x05tx_id\x18\n" +
	" \x01(\tR\x04txId\x12\x17\n" +
	"\atx_hash\x18\v \x01(\tR\x06txHash\x12\x1b\n" +
	"\ttx_sender\x18\f \x01(\tR\btxSender\x12\x1f\n" +
	"\vtx_receiver\x18\r \x01(\tR\n" +
	"txReceiver\x12\x1b\n" +
	"\ttx_status\x18\x0e \x01(\tR\btxStatus\x12$\n" +
	"\x0emeta_source_id\x18\x14 \x01(\tR\fmetaSourceId\x123\n" +
	"\x16meta_chain_family_name\x18\x15 \x01(\tR\x13metaChainFamilyName\x12\"\n" +
	"\rmeta_chain_id\x18\x16 \x01(\tR\vmetaChainId\x12*\n" +
	"\x11meta_network_name\x18\x17 \x01(\tR\x0fmetaNetworkName\x123\n" +
	"\x16meta_network_name_full\x18\x18 \x01(\tR\x13metaNetworkNameFull\x12(\n" +
	"\x10meta_workflow_id\x18\x19 \x01(\tR\x0emetaWorkflowId\x12.\n" +
	"\x13meta_workflow_owner\x18\x1a \x01(\tR\x11metaWorkflowOwner\x12;\n" +
	"\x1ameta_workflow_execution_id\x18\x1b \x01(\tR\x17metaWorkflowExecutionId\x12,\n" +
	"\x12meta_workflow_name\x18\x1c \x01(\tR\x10metaWorkflowName\x12/\n" +
	"\x14meta_workflow_don_id\x18\x1d \x01(\rR\x11metaWorkflowDonId\x12F\n" +
	" meta_workflow_don_config_version\x18\x1e \x01(\rR\x1cmetaWorkflowDonConfigVersion\x12*\n" +
	"\x11meta_reference_id\x18\x1f \x01(\tR\x0fmetaReferenceId\x120\n" +
	"\x14meta_capability_type\x18  \x01(\tR\x12metaCapabilityType\x12,\n" +
	"\x12meta_capability_id\x18! \x01(\tR\x10metaCapabilityId\x12E\n" +
	"\x1fmeta_capability_timestamp_start\x18\" \x01(\x04R\x1cmetaCapabilityTimestampStart\x12C\n" +
	"\x1emeta_capability_timestamp_emit\x18# \x01(\x04R\x1bmetaCapabilityTimestampEmitB\fZ\n" +
	".;registryb\x06proto3"

var (
	file_feed_updated_proto_rawDescOnce sync.Once
	file_feed_updated_proto_rawDescData []byte
)

func file_feed_updated_proto_rawDescGZIP() []byte {
	file_feed_updated_proto_rawDescOnce.Do(func() {
		file_feed_updated_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_feed_updated_proto_rawDesc), len(file_feed_updated_proto_rawDesc)))
	})
	return file_feed_updated_proto_rawDescData
}

var file_feed_updated_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_feed_updated_proto_goTypes = []any{
	(*FeedUpdated)(nil), // 0: datafeeds.on_chain.registry.FeedUpdated
}
var file_feed_updated_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_feed_updated_proto_init() }
func file_feed_updated_proto_init() {
	if File_feed_updated_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_feed_updated_proto_rawDesc), len(file_feed_updated_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_feed_updated_proto_goTypes,
		DependencyIndexes: file_feed_updated_proto_depIdxs,
		MessageInfos:      file_feed_updated_proto_msgTypes,
	}.Build()
	File_feed_updated_proto = out.File
	file_feed_updated_proto_goTypes = nil
	file_feed_updated_proto_depIdxs = nil
}
