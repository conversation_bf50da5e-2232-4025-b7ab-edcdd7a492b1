syntax="proto3";

package platform.write_target;
option go_package = ".;writetarget";

// WT errored while processing write request
message WriteError {
  uint32 code = 1;
  string summary = 2;
  string cause = 3;

  string node = 4;
  string forwarder = 5;
  string receiver = 6;

  // Report Info
  uint32 report_id = 7;

  // [Execution Context]
  // TODO: replace with a proto reference once supported
  // Execution Context - Source
  string meta_source_id = 20;

  // Execution Context - Chain
  string meta_chain_family_name = 21;
  string meta_chain_id = 22;
  string meta_network_name = 23;
  string meta_network_name_full = 24;

  // Execution Context - Workflow (capabilities.RequestMetadata)
  string meta_workflow_id = 25;
  string meta_workflow_owner = 26;
  string meta_workflow_execution_id = 27;
  string meta_workflow_name = 28;
  uint32 meta_workflow_don_id = 29;
  uint32 meta_workflow_don_config_version = 30;
  string meta_reference_id = 31;

  // Execution Context - Capability
  string meta_capability_type = 32;
  string meta_capability_id = 33;
  uint64 meta_capability_timestamp_start = 34;
  uint64 meta_capability_timestamp_emit = 35;
}
