# Test files run with a different solhint ruleset, ignore them here.
./**/*.t.sol

# Ignore frozen Automation code
./src/v0.8/automation/v1_2
./src/v0.8/automation/interfaces/v1_2
./src/v0.8/automation/v1_3
./src/v0.8/automation/interfaces/v1_3
./src/v0.8/automation/v2_0
./src/v0.8/automation/interfaces/v2_0
./src/v0.8/automation/v2_1
./src/v0.8/automation/interfaces/v2_1/
./src/v0.8/automation/interfaces/MigratableKeeperRegistryInterface.sol
./src/v0.8/automation/upkeeps/UpkeepBalanceMonitor.sol
./src/v0.8/automation/upkeeps/LinkAvailableBalanceMonitor.sol
./src/v0.8/automation/upkeeps/EthBalanceMonitor.sol
./src/v0.8/automation/upkeeps/ERC20BalanceMonitor.sol
./src/v0.8/automation/upkeeps/CronUpkeepFactory.sol
./src/v0.8/automation/upkeeps/CronUpkeepDelegate.sol
./src/v0.8/automation/upkeeps/CronUpkeep.sol
./src/v0.8/automation/libraries/internal/Cron.sol
./src/v0.8/automation/AutomationForwarder.sol
./src/v0.8/automation/AutomationForwarderLogic.sol
./src/v0.8/automation/interfaces/v2_2/IAutomationRegistryMaster.sol
./src/v0.8/automation/interfaces/v2_3/IAutomationRegistryMaster2_3.sol


# Ignore tests / test helpers (for now)
./src/v0.8/automation/mocks
./src/v0.8/automation/testhelpers
./src/v0.8/data-feeds/test/helpers

# Ignore Functions v1.0.0 code that was frozen after audit
./src/v0.8/functions/v1_0_0

# Test helpers
./src/v0.8/vrf/testhelpers
./src/v0.8/tests
./src/v0.8/functions/tests
./src/v0.8/mocks/

# Always ignore vendor
./src/v0.8/vendor
./node_modules/

# Ignore tweaked vendored contracts
./src/v0.8/shared/enumerable/EnumerableSetWithBytes16.sol
