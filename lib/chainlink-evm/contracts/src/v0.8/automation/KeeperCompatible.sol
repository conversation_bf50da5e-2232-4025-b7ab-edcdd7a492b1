// SPDX-License-Identifier: MIT
/**
 * @notice This is a deprecated interface. Please use AutomationCompatible directly.
 */
pragma solidity ^0.8.0;
// solhint-disable-next-line no-unused-import
import {AutomationCompatible as KeeperCompatible} from "./AutomationCompatible.sol";
// solhint-disable-next-line no-unused-import
import {AutomationBase as KeeperBase} from "./AutomationBase.sol";
// solhint-disable-next-line no-unused-import
import {AutomationCompatibleInterface as KeeperCompatibleInterface} from "./interfaces/AutomationCompatibleInterface.sol";
