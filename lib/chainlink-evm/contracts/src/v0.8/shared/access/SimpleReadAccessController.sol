// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {SimpleWriteAccessController} from "./SimpleWriteAccessController.sol";

/// @title SimpleReadAccessController
/// @notice Gives access to:
/// - any externally owned account (note that off-chain actors can always read
/// any contract storage regardless of on-chain access control measures, so this
/// does not weaken the access control while improving usability)
/// - accounts explicitly added to an access list
/// @dev SimpleReadAccessController is not suitable for access controlling writes
/// since it grants any externally owned account access! See
/// SimpleWriteAccessController for that.
contract SimpleReadAccessController is SimpleWriteAccessController {
  /// @notice Returns the access of an address
  /// @param _user The address to query
  function hasAccess(address _user, bytes memory _calldata) public view virtual override returns (bool) {
    // solhint-disable-next-line avoid-tx-origin
    return super.hasAccess(_user, _calldata) || _user == tx.origin;
  }
}
