ArbitrumCrossDomainForwarder_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 37567)
ArbitrumCrossDomainForwarder_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 12954)
ArbitrumCrossDomainForwarder_Constructor:test_InitialState() (gas: 22111)
ArbitrumCrossDomainForwarder_Forward:test_Forward() (gas: 47818)
ArbitrumCrossDomainForwarder_Forward:test_ForwardRevert() (gas: 22147)
ArbitrumCrossDomainForwarder_Forward:test_NotCallableByUnknownAddress() (gas: 16083)
ArbitrumCrossDomainForwarder_TransferL1Ownership:test_CallableByL1Owner() (gas: 41439)
ArbitrumCrossDomainForwarder_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 19274)
ArbitrumCrossDomainForwarder_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 18644)
ArbitrumCrossDomainForwarder_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 13232)
ArbitrumCrossDomainGovernor_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 37567)
ArbitrumCrossDomainGovernor_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 12954)
ArbitrumCrossDomainGovernor_Constructor:test_InitialState() (gas: 22134)
ArbitrumCrossDomainGovernor_Forward:test_CallableByL2Owner() (gas: 49953)
ArbitrumCrossDomainGovernor_Forward:test_Forward() (gas: 47869)
ArbitrumCrossDomainGovernor_Forward:test_ForwardRevert() (gas: 24268)
ArbitrumCrossDomainGovernor_Forward:test_NotCallableByUnknownAddress() (gas: 18216)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_BubbleUpRevert() (gas: 19338)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_CallableByCrossDomainMessengerAddressOrL1Owner() (gas: 60787)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_CallableByL2Owner() (gas: 62915)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_NotCallableByUnknownAddress() (gas: 18271)
ArbitrumCrossDomainGovernor_ForwardDelegate:test_RevertsBatchWhenOneCallFails() (gas: 64276)
ArbitrumCrossDomainGovernor_TransferL1Ownership:test_CallableByL1Owner() (gas: 41439)
ArbitrumCrossDomainGovernor_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 19274)
ArbitrumCrossDomainGovernor_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 18644)
ArbitrumCrossDomainGovernor_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 13232)
ArbitrumSequencerUptimeFeed_AggregatorV3Interface:test_AggregatorV3Interface() (gas: 104766)
ArbitrumSequencerUptimeFeed_AggregatorV3Interface:test_Return0WhenRoundDoesNotExistYet() (gas: 19945)
ArbitrumSequencerUptimeFeed_Constants:test_InitialState() (gas: 8492)
ArbitrumSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceAllowReadsIfConsumingContractIsWhitelisted() (gas: 592246)
ArbitrumSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_AggregatorV2V3InterfaceDisallowReadsIfConsumingContractIsNotWhitelisted() (gas: 562331)
ArbitrumSequencerUptimeFeed_UpdateStatus:test_IgnoreOutOfOrderUpdates() (gas: 99593)
ArbitrumSequencerUptimeFeed_UpdateStatus:test_RevertIfNotL2CrossDomainMessengerAddr() (gas: 15442)
ArbitrumSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndNoTimeChange() (gas: 114527)
ArbitrumSequencerUptimeFeed_UpdateStatus:test_UpdateStatusWhenStatusChangeAndTimeChange() (gas: 114610)
ArbitrumValidator_Validate:test_PostSequencerOffline() (gas: 73248)
BaseSequencerUptimeFeed_AggregatorV3Interface:test_AggregatorV3Interface_ReturnsValidData() (gas: 69197)
BaseSequencerUptimeFeed_AggregatorV3Interface:test_getAnswer_ReturnsValidAnswer() (gas: 57300)
BaseSequencerUptimeFeed_AggregatorV3Interface:test_getTimestamp_ReturnsValidTimestamp() (gas: 57151)
BaseSequencerUptimeFeed_Constructor:test_Constructor_InitialState() (gas: 22050)
BaseSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_ProtectReads_AllowWhen_Whitelisted() (gas: 589517)
BaseSequencerUptimeFeed_ProtectReadsOnAggregatorV2V3InterfaceFunctions:test_ProtectReads_DisallowWhen_NotWhitelisted() (gas: 562342)
BaseSequencerUptimeFeed_UpdateStatus:test_updateStatus_IgnoreOutOfOrderUpdates() (gas: 69490)
BaseSequencerUptimeFeed_UpdateStatus:test_updateStatus_UpdateWhen_NoStatusChangeSameTimestamp() (gas: 77166)
BaseSequencerUptimeFeed_UpdateStatus:test_updateStatus_UpdateWhen_StatusChangeAndNoTimeChange() (gas: 96021)
BaseSequencerUptimeFeed_UpdateStatus:test_updateStatus_UpdateWhen_StatusChangeAndTimeChange() (gas: 96100)
BaseSequencerUptimeFeed_transferL1Sender:test_transferL1Sender_CorrectlyTransfersL1Sender() (gas: 1479310)
BaseValidator_Constructor:test_Constructor_EmitsWhen_ConstructorIsSuccessfull() (gas: 715556)
BaseValidator_GetAndSetGasLimit:test_GetAndSetGasLimit_CorrectlyHandlesGasLimit() (gas: 20119)
OptimismCrossDomainForwarder_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 47110)
OptimismCrossDomainForwarder_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 22135)
OptimismCrossDomainForwarder_Constructor:test_InitialState() (gas: 21947)
OptimismCrossDomainForwarder_Forward:test_Forward() (gas: 58213)
OptimismCrossDomainForwarder_Forward:test_ForwardRevert() (gas: 32533)
OptimismCrossDomainForwarder_Forward:test_NotCallableByUnknownAddress() (gas: 13895)
OptimismCrossDomainForwarder_TransferL1Ownership:test_CallableByL1Owner() (gas: 48912)
OptimismCrossDomainForwarder_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 28733)
OptimismCrossDomainForwarder_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 16456)
OptimismCrossDomainForwarder_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 11044)
OptimismCrossDomainGovernor_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 47110)
OptimismCrossDomainGovernor_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 22135)
OptimismCrossDomainGovernor_Constructor:test_InitialState() (gas: 21970)
OptimismCrossDomainGovernor_Forward:test_CallableByL2Owner() (gas: 47797)
OptimismCrossDomainGovernor_Forward:test_Forward() (gas: 58284)
OptimismCrossDomainGovernor_Forward:test_ForwardRevert() (gas: 32569)
OptimismCrossDomainGovernor_Forward:test_NotCallableByUnknownAddress() (gas: 16031)
OptimismCrossDomainGovernor_ForwardDelegate:test_BubbleUpRevert() (gas: 29128)
OptimismCrossDomainGovernor_ForwardDelegate:test_CallableByCrossDomainMessengerAddressOrL1Owner() (gas: 72836)
OptimismCrossDomainGovernor_ForwardDelegate:test_CallableByL2Owner() (gas: 72841)
OptimismCrossDomainGovernor_ForwardDelegate:test_NotCallableByUnknownAddress() (gas: 16086)
OptimismCrossDomainGovernor_ForwardDelegate:test_RevertsBatchWhenOneCallFails() (gas: 76045)
OptimismCrossDomainGovernor_TransferL1Ownership:test_CallableByL1Owner() (gas: 48912)
OptimismCrossDomainGovernor_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 28733)
OptimismCrossDomainGovernor_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 16456)
OptimismCrossDomainGovernor_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 11044)
OptimismSequencerUptimeFeed_Constructor:test_Constructor_InitialState() (gas: 1530881)
OptimismSequencerUptimeFeed_ValidateSender:test_ValidateSender_UpdateStatusWhen_StatusChangeAndNoTimeChange() (gas: 17874)
OptimismValidator_Validate:test_Validate_PostSequencerOffline() (gas: 79034)
OptimismValidator_Validate:test_Validate_PostSequencerStatus_NoStatusChange() (gas: 79040)
ScrollCrossDomainForwarder_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 47196)
ScrollCrossDomainForwarder_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 22185)
ScrollCrossDomainForwarder_Constructor:test_InitialState() (gas: 21623)
ScrollCrossDomainForwarder_Forward:test_Forward() (gas: 58277)
ScrollCrossDomainForwarder_Forward:test_ForwardRevert() (gas: 32589)
ScrollCrossDomainForwarder_Forward:test_NotCallableByUnknownAddress() (gas: 13895)
ScrollCrossDomainForwarder_TransferL1Ownership:test_CallableByL1Owner() (gas: 48975)
ScrollCrossDomainForwarder_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 28791)
ScrollCrossDomainForwarder_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 16456)
ScrollCrossDomainForwarder_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 11044)
ScrollCrossDomainGovernor_AcceptL1Ownership:test_CallableByPendingL1Owner() (gas: 47196)
ScrollCrossDomainGovernor_AcceptL1Ownership:test_NotCallableByNonPendingOwners() (gas: 22185)
ScrollCrossDomainGovernor_Constructor:test_InitialState() (gas: 21646)
ScrollCrossDomainGovernor_Forward:test_CallableByL2Owner() (gas: 47792)
ScrollCrossDomainGovernor_Forward:test_Forward() (gas: 58343)
ScrollCrossDomainGovernor_Forward:test_ForwardRevert() (gas: 32622)
ScrollCrossDomainGovernor_Forward:test_NotCallableByUnknownAddress() (gas: 16028)
ScrollCrossDomainGovernor_ForwardDelegate:test_BubbleUpRevert() (gas: 29186)
ScrollCrossDomainGovernor_ForwardDelegate:test_CallableByCrossDomainMessengerAddressOrL1Owner() (gas: 72900)
ScrollCrossDomainGovernor_ForwardDelegate:test_CallableByL2Owner() (gas: 72905)
ScrollCrossDomainGovernor_ForwardDelegate:test_NotCallableByUnknownAddress() (gas: 16083)
ScrollCrossDomainGovernor_ForwardDelegate:test_RevertsBatchWhenOneCallFails() (gas: 76110)
ScrollCrossDomainGovernor_TransferL1Ownership:test_CallableByL1Owner() (gas: 48975)
ScrollCrossDomainGovernor_TransferL1Ownership:test_CallableByL1OwnerOrZeroAddress() (gas: 28791)
ScrollCrossDomainGovernor_TransferL1Ownership:test_NotCallableByL2Owner() (gas: 16456)
ScrollCrossDomainGovernor_TransferL1Ownership:test_NotCallableByNonOwners() (gas: 11044)
ScrollSequencerUptimeFeed_Constructor:test_Constructor_InitialState_WhenValidL2XDomainMessenger() (gas: 1511902)
ScrollSequencerUptimeFeed_ValidateSender:test_ValidateSender_UpdateStatusWhen_StatusChangeAndNoTimeChange() (gas: 17864)
ScrollValidator_Validate:test_Validate_PostSequencerOffline() (gas: 82593)
ScrollValidator_Validate:test_Validate_PostSequencerStatus_NoStatusChange() (gas: 82603)
ZKSyncSequencerUptimeFeed_ValidateSender:test_ValidateSender_SuccessWhen_SenderIsValid() (gas: 12611)
ZKSyncValidator_GetChainId:test_GetChainId_CorrectlyGetsTheChainId() (gas: 8369)
ZKSyncValidator_GetSetL2GasPerPubdataByteLimit:test_GetSetL2GasPerPubdataByteLimit_CorrectlyHandlesGasPerPubdataByteLimit() (gas: 18918)
ZKSyncValidator_Validate:test_Validate_PostSequencerOffline() (gas: 56439)
ZKSyncValidator_Validate:test_Validate_PostSequencerStatus_NoStatusChange() (gas: 56503)