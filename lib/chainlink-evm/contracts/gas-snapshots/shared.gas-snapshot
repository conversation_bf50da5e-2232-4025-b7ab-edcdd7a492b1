AuthorizedCallers_applyAuthorizedCallerUpdates:test_AddAndRemove_Success() (gas: 124942)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_OnlyAdd_Success() (gas: 132869)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_OnlyCallableByOwner_Revert() (gas: 12238)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_OnlyRemove_Success() (gas: 44907)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_RemoveThenAdd_Success() (gas: 56991)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_SkipRemove_Success() (gas: 31961)
AuthorizedCallers_applyAuthorizedCallerUpdates:test_ZeroAddressNotAllowed_Revert() (gas: 64413)
AuthorizedCallers_constructor:test_ZeroAddressNotAllowed_Revert() (gas: 64390)
AuthorizedCallers_constructor:test_constructor_Success() (gas: 674931)
BurnMintERC20_approve:test_approve() (gas: 57652)
BurnMintERC20_burn:test_BasicBurn() (gas: 153607)
BurnMintERC20_burnFrom:test_BurnFrom() (gas: 57995)
BurnMintERC20_burnFromAlias:test_burn() (gas: 58039)
BurnMintERC20_constructor:test_Constructor() (gas: 1694831)
BurnMintERC20_getCCIPAdmin:test_getCCIPAdmin() (gas: 10548)
BurnMintERC20_getCCIPAdmin:test_setCCIPAdmin() (gas: 21590)
BurnMintERC20_grantMintAndBurnRoles:test_GrantMintAndBurnRoles() (gas: 79142)
BurnMintERC20_mint:test_mint() (gas: 101849)
BurnMintERC20_supportsInterface:test_SupportsInterface() (gas: 11218)
BurnMintERC20_transfer:test_transfer() (gas: 42338)
BurnMintERC677_approve:testApproveSuccess() (gas: 55512)
BurnMintERC677_approve:testInvalidAddressReverts() (gas: 10663)
BurnMintERC677_burn:testBasicBurnSuccess() (gas: 172100)
BurnMintERC677_burn:testBurnFromZeroAddressReverts() (gas: 47201)
BurnMintERC677_burn:testExceedsBalanceReverts() (gas: 21841)
BurnMintERC677_burn:testSenderNotBurnerReverts() (gas: 13359)
BurnMintERC677_burnFrom:testBurnFromSuccess() (gas: 57959)
BurnMintERC677_burnFrom:testExceedsBalanceReverts() (gas: 35864)
BurnMintERC677_burnFrom:testInsufficientAllowanceReverts() (gas: 21849)
BurnMintERC677_burnFrom:testSenderNotBurnerReverts() (gas: 13359)
BurnMintERC677_burnFromAlias:testBurnFromSuccess() (gas: 57985)
BurnMintERC677_burnFromAlias:testExceedsBalanceReverts() (gas: 35880)
BurnMintERC677_burnFromAlias:testInsufficientAllowanceReverts() (gas: 21869)
BurnMintERC677_burnFromAlias:testSenderNotBurnerReverts() (gas: 13379)
BurnMintERC677_constructor:testConstructorSuccess() (gas: 1672812)
BurnMintERC677_decreaseApproval:testDecreaseApprovalSuccess() (gas: 31069)
BurnMintERC677_grantMintAndBurnRoles:testGrantMintAndBurnRolesSuccess() (gas: 121324)
BurnMintERC677_grantRole:testGrantBurnAccessSuccess() (gas: 53442)
BurnMintERC677_grantRole:testGrantManySuccess() (gas: 937759)
BurnMintERC677_grantRole:testGrantMintAccessSuccess() (gas: 94323)
BurnMintERC677_increaseApproval:testIncreaseApprovalSuccess() (gas: 44076)
BurnMintERC677_mint:testBasicMintSuccess() (gas: 149699)
BurnMintERC677_mint:testMaxSupplyExceededReverts() (gas: 50363)
BurnMintERC677_mint:testSenderNotMinterReverts() (gas: 11195)
BurnMintERC677_supportsInterface:testConstructorSuccess() (gas: 12476)
BurnMintERC677_transfer:testInvalidAddressReverts() (gas: 10639)
BurnMintERC677_transfer:testTransferSuccess() (gas: 42299)
CallWithExactGasZKSync__callWithExactGasSafeReturnData:test__callWithExactGasSafeReturnData_FailsWhen_NotEnoughGasForCall() (gas: 18544)
CallWithExactGasZKSync__callWithExactGasSafeReturnData:test__callWithExactGasSafeReturnData_Success() (gas: 27871)
CallWithExactGasZKSync__callWithExactGasSafeReturnData:test__callWithExactGasSafeReturnData_TruncatesData() (gas: 75408)
CallWithExactGas__callWithExactGas:test_CallWithExactGasReceiverErrorSuccess() (gas: 65949)
CallWithExactGas__callWithExactGas:test_CallWithExactGasSafeReturnDataExactGas() (gas: 18324)
CallWithExactGas__callWithExactGas:test_NoContractReverts() (gas: 11559)
CallWithExactGas__callWithExactGas:test_NoGasForCallExactCheckReverts() (gas: 15788)
CallWithExactGas__callWithExactGas:test_NotEnoughGasForCallReverts() (gas: 16241)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_CallWithExactGasEvenIfTargetIsNoContractExactGasSuccess() (gas: 20073)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_CallWithExactGasEvenIfTargetIsNoContractReceiverErrorSuccess() (gas: 66461)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_NoContractSuccess() (gas: 12962)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_NoGasForCallExactCheckReturnFalseSuccess() (gas: 13005)
CallWithExactGas__callWithExactGasEvenIfTargetIsNoContract:test_NotEnoughGasForCallReturnsFalseSuccess() (gas: 13317)
CallWithExactGas__callWithExactGasSafeReturnData:test_CallWithExactGasSafeReturnDataExactGas() (gas: 20331)
CallWithExactGas__callWithExactGasSafeReturnData:test_NoContractReverts() (gas: 13939)
CallWithExactGas__callWithExactGasSafeReturnData:test_NoGasForCallExactCheckReverts() (gas: 16139)
CallWithExactGas__callWithExactGasSafeReturnData:test_NotEnoughGasForCallReverts() (gas: 16547)
CallWithExactGas__callWithExactGasSafeReturnData:test_callWithExactGasSafeReturnData_ThrowOOGError_Revert() (gas: 36755)
EnumerableMapAddresses_at:testAtSuccess() (gas: 95086)
EnumerableMapAddresses_at:testBytes32AtSuccess() (gas: 94855)
EnumerableMapAddresses_at:testBytesAtSuccess() (gas: 96542)
EnumerableMapAddresses_contains:testBytes32ContainsSuccess() (gas: 93518)
EnumerableMapAddresses_contains:testBytesContainsSuccess() (gas: 93990)
EnumerableMapAddresses_contains:testContainsSuccess() (gas: 93696)
EnumerableMapAddresses_get:testBytes32GetSuccess() (gas: 94256)
EnumerableMapAddresses_get:testBytesGetSuccess() (gas: 95945)
EnumerableMapAddresses_get:testGetSuccess() (gas: 94431)
EnumerableMapAddresses_get_errorMessage:testBytesGetErrorMessageSuccess() (gas: 95878)
EnumerableMapAddresses_get_errorMessage:testGetErrorMessageSuccess() (gas: 94489)
EnumerableMapAddresses_length:testBytes32LengthSuccess() (gas: 72445)
EnumerableMapAddresses_length:testBytesLengthSuccess() (gas: 73064)
EnumerableMapAddresses_length:testLengthSuccess() (gas: 72623)
EnumerableMapAddresses_remove:testBytes32RemoveSuccess() (gas: 73462)
EnumerableMapAddresses_remove:testBytesRemoveSuccess() (gas: 74249)
EnumerableMapAddresses_remove:testRemoveSuccess() (gas: 73686)
EnumerableMapAddresses_set:testBytes32SetSuccess() (gas: 94496)
EnumerableMapAddresses_set:testBytesSetSuccess() (gas: 95428)
EnumerableMapAddresses_set:testSetSuccess() (gas: 94663)
EnumerableMapAddresses_tryGet:testBytes32TryGetSuccess() (gas: 94622)
EnumerableMapAddresses_tryGet:testBytesTryGetSuccess() (gas: 96345)
EnumerableMapAddresses_tryGet:testTryGetSuccess() (gas: 94893)
OpStackBurnMintERC677_constructor:testConstructorSuccess() (gas: 1743682)
OpStackBurnMintERC677_interfaceCompatibility:testBurnCompatibility() (gas: 291483)
OpStackBurnMintERC677_interfaceCompatibility:testMintCompatibility() (gas: 137957)
OpStackBurnMintERC677_interfaceCompatibility:testStaticFunctionsCompatibility() (gas: 13781)
OpStackBurnMintERC677_supportsInterface:testConstructorSuccess() (gas: 12752)
Ownable2Step_acceptOwnership:test_acceptOwnership_MustBeProposedOwner_reverts() (gas: 10360)
Ownable2Step_acceptOwnership:test_acceptOwnership_success() (gas: 31088)
Ownable2Step_constructor:test_constructor_OwnerCannotBeZero_reverts() (gas: 35858)
Ownable2Step_constructor:test_constructor_success() (gas: 10428)
Ownable2Step_onlyOwner:test_onlyOwner_OnlyCallableByOwner_reverts() (gas: 10754)
Ownable2Step_onlyOwner:test_onlyOwner_success() (gas: 7506)
Ownable2Step_transferOwnership:test_transferOwnership_CannotTransferToSelf_reverts() (gas: 10501)
Ownable2Step_transferOwnership:test_transferOwnership_success() (gas: 30140)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_EmptySubset_Reverts() (gas: 5208)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_EmptySuperset_Reverts() (gas: 4535)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_HasDuplicates_Reverts() (gas: 7761)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_NotASubset_Reverts() (gas: 11733)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SingleElementSubset() (gas: 3922)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SingleElementSubsetAndSuperset_Equal() (gas: 1464)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SingleElementSubsetAndSuperset_NotEqual_Reverts() (gas: 6172)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SubsetEqualsSuperset_NoRevert() (gas: 7859)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SubsetLargerThanSuperset_Reverts() (gas: 15410)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_SupersetHasDuplicates_Reverts() (gas: 8790)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_UnsortedSubset_Reverts() (gas: 7128)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_UnsortedSuperset_Reverts() (gas: 8970)
SortedSetValidationUtil_CheckIsValidUniqueSubsetTest:test__checkIsValidUniqueSubset_ValidSubset_Success() (gas: 5671)