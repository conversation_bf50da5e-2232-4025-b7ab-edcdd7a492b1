BundleAggregatorProxyTest:test_aggregator() (gas: 12620)
BundleAggregatorProxyTest:test_bundleDecimals() (gas: 19152)
BundleAggregatorProxyTest:test_confirmAggregatorRevertNotProposed() (gas: 13155)
BundleAggregatorProxyTest:test_confirmAggregatorSuccess() (gas: 32440)
BundleAggregatorProxyTest:test_description() (gas: 20305)
BundleAggregatorProxyTest:test_latestBundle() (gas: 19795)
BundleAggregatorProxyTest:test_latestBundleTimestamp() (gas: 18092)
BundleAggregatorProxyTest:test_proposeAggregator() (gas: 41280)
BundleAggregatorProxyTest:test_version() (gas: 13639)
DataFeedsCacheGasTest:test_bundleDecimals_proxy_gas() (gas: 22286)
DataFeedsCacheGasTest:test_decimals_proxy_gas() (gas: 16912)
DataFeedsCacheGasTest:test_description_proxy_gas() (gas: 20706)
DataFeedsCacheGasTest:test_getAnswer_proxy_gas() (gas: 19151)
DataFeedsCacheGasTest:test_getRoundData_proxy_gas() (gas: 19973)
DataFeedsCacheGasTest:test_getTimestamp_proxy_gas() (gas: 19156)
DataFeedsCacheGasTest:test_latestAnswer_proxy_gas() (gas: 18507)
DataFeedsCacheGasTest:test_latestBundleTimestamp_proxy_gas() (gas: 18484)
DataFeedsCacheGasTest:test_latestBundle_proxy_gas() (gas: 20135)
DataFeedsCacheGasTest:test_latestRoundData_proxy_gas() (gas: 21906)
DataFeedsCacheGasTest:test_latestRound_proxy_gas() (gas: 18628)
DataFeedsCacheGasTest:test_latestTimestamp_proxy_gas() (gas: 18627)
DataFeedsCacheGasTest:test_removeDataIdMappingsForProxies1feed_gas() (gas: 17873)
DataFeedsCacheGasTest:test_removeDataIdMappingsForProxies5feeds_gas() (gas: 43203)
DataFeedsCacheGasTest:test_updateDataIdMappingsForProxies1feed_gas() (gas: 45277)
DataFeedsCacheGasTest:test_updateDataIdMappingsForProxies5feeds_gas() (gas: 97781)
DataFeedsCacheGasTest:test_write_onReport_prices_1_gas() (gas: 74765)
DataFeedsCacheGasTest:test_write_onReport_prices_5_gas() (gas: 336286)
DataFeedsCacheGasTest:test_write_removeFeedConfigs_1_gas() (gas: 52598)
DataFeedsCacheGasTest:test_write_removeFeedConfigs_5_gas() (gas: 232944)
DataFeedsCacheGasTest:test_write_setBundleFeedConfigs_1_gas() (gas: 284044)
DataFeedsCacheGasTest:test_write_setBundleFeedConfigs_5_gas() (gas: 1295817)
DataFeedsCacheGasTest:test_write_setBundleFeedConfigs_with_delete_1_gas() (gas: 91336)
DataFeedsCacheGasTest:test_write_setBundleFeedConfigs_with_delete_5_gas() (gas: 373668)
DataFeedsCacheGasTest:test_write_setDecimalFeedConfigs_1_gas() (gas: 219180)
DataFeedsCacheGasTest:test_write_setDecimalFeedConfigs_5_gas() (gas: 980459)
DataFeedsCacheGasTest:test_write_setDecimalFeedConfigs_with_delete_1_gas() (gas: 74514)
DataFeedsCacheGasTest:test_write_setDecimalFeedConfigs_with_delete_5_gas() (gas: 297007)
DataFeedsCacheTest:testFuzzy_getDataType(bytes16,uint256) (runs: 256, μ: 9492, ~: 9492)
DataFeedsCacheTest:testFuzzy_getDataTypeRevertOutOfBound(bytes16,uint256) (runs: 256, μ: 9275, ~: 9275)
DataFeedsCacheTest:testFuzzy_recoverTokensERC20Success(uint256) (runs: 256, μ: 77137, ~: 77137)
DataFeedsCacheTest:testFuzzy_recoverTokensNativeSuccess(uint256) (runs: 256, μ: 49098, ~: 49098)
DataFeedsCacheTest:test_bundleDecimals() (gas: 282593)
DataFeedsCacheTest:test_decimals() (gas: 221706)
DataFeedsCacheTest:test_description() (gas: 224355)
DataFeedsCacheTest:test_feedCanBeWrittenToByMultipleWorkflows() (gas: 219534)
DataFeedsCacheTest:test_getDataIdForProxy() (gas: 15208)
DataFeedsCacheTest:test_getDataType() (gas: 8885)
DataFeedsCacheTest:test_getFeedMetadata() (gas: 268408)
DataFeedsCacheTest:test_getFeedMetadataRevertFeedNotConfigured() (gas: 13418)
DataFeedsCacheTest:test_getLatestAnswer1() (gas: 307899)
DataFeedsCacheTest:test_getLatestAnswer2() (gas: 578522)
DataFeedsCacheTest:test_getLatestBundle1() (gas: 410719)
DataFeedsCacheTest:test_getLatestBundle2() (gas: 781455)
DataFeedsCacheTest:test_getLatestByFeedId() (gas: 317993)
DataFeedsCacheTest:test_getWorkflowMetaData() (gas: 10183)
DataFeedsCacheTest:test_isFeedAdmin() (gas: 14294)
DataFeedsCacheTest:test_latestAnswer1() (gas: 314064)
DataFeedsCacheTest:test_latestAnswer2() (gas: 589203)
DataFeedsCacheTest:test_latestBundle1() (gas: 420293)
DataFeedsCacheTest:test_latestBundle2() (gas: 798301)
DataFeedsCacheTest:test_onReportInvalidPermission() (gas: 675297)
DataFeedsCacheTest:test_onReportRevertInvalidWorkflowName() (gas: 252356)
DataFeedsCacheTest:test_onReportRevertInvalidWorkflowOwner() (gas: 252386)
DataFeedsCacheTest:test_onReportStaleBundleReport() (gas: 815333)
DataFeedsCacheTest:test_onReportStaleDecimalReport() (gas: 635682)
DataFeedsCacheTest:test_onReportSuccess_BundleReportLength1() (gas: 402715)
DataFeedsCacheTest:test_onReportSuccess_BundleReportLength2() (gas: 765906)
DataFeedsCacheTest:test_onReportSuccess_DecimalReportLength1() (gas: 310085)
DataFeedsCacheTest:test_onReportSuccess_DecimalReportLength2() (gas: 579850)
DataFeedsCacheTest:test_onReportSuccess_EmptyBundleReport() (gas: 251195)
DataFeedsCacheTest:test_onReportSuccess_EmptyDecimalReport() (gas: 241310)
DataFeedsCacheTest:test_onReportSuccess_EmptyReport() (gas: 221787)
DataFeedsCacheTest:test_recoverTokensERC20RevertNoBalance() (gas: 19972)
DataFeedsCacheTest:test_recoverTokensNativeRevertNoBalance() (gas: 11489)
DataFeedsCacheTest:test_recoverTokensRevertUnauthorized() (gas: 13353)
DataFeedsCacheTest:test_removeDataIdMappingsForProxiesSuccess() (gas: 22198)
DataFeedsCacheTest:test_removeDataIdMappingsForProxiesSuccess_and_call_decimals() (gas: 24859)
DataFeedsCacheTest:test_removeFeedAdminSuccess() (gas: 27005)
DataFeedsCacheTest:test_removeFeedsRevertInvalidSender() (gas: 12166)
DataFeedsCacheTest:test_removeFeedsRevertNotConfiguredFeed() (gas: 20111)
DataFeedsCacheTest:test_removeFeedsSuccess() (gas: 193842)
DataFeedsCacheTest:test_setBundleFeedConfigsRevertInvalidConfigsLengthDecimals() (gas: 32379)
DataFeedsCacheTest:test_setBundleFeedConfigs_setAgainWithClear() (gas: 961653)
DataFeedsCacheTest:test_setDecimalFeedConfigs_setAgainWithClear() (gas: 744352)
DataFeedsCacheTest:test_setFeedAdminRevertZeroAddress() (gas: 11343)
DataFeedsCacheTest:test_setFeedConfigsRevertEmptyConfig() (gas: 55621)
DataFeedsCacheTest:test_setFeedConfigsRevertInvalidConfigsLengthDescriptions() (gas: 48286)
DataFeedsCacheTest:test_setFeedConfigsRevertInvalidWorkflowMetadata() (gas: 94046)
DataFeedsCacheTest:test_setFeedConfigsRevertUnauthorizedFeedAdmin() (gas: 43047)
DataFeedsCacheTest:test_setFeedConfigsRevertZeroDataId() (gas: 43199)
DataFeedsCacheTest:test_setFeedConfigsSuccess() (gas: 220710)
DataFeedsCacheTest:test_supportsInterface() (gas: 8637)
DataFeedsCacheTest:test_updateDataIdMappingsForProxiesRevertInvalidLengths() (gas: 12232)
DataFeedsCacheTest:test_updateDataIdMappingsForProxiesRevertUnauthorizedOwner() (gas: 13164)
DataFeedsCacheTest:test_updateDataIdMappingsForProxiesSuccess() (gas: 21337)
DataFeedsCacheTest:test_updateDataIdMappingsForProxies_and_RevertOnWrongCaller() (gas: 25810)
DataFeedsCacheTest:test_updateDataIdMappingsForProxies_and_call_decimals() (gas: 55054)