[profile.default]
auto_detect_solc = true
optimizer = true
optimizer_runs = 1_000_000

src = 'src/v0.8'
test = 'test/v0.8'
out = 'foundry-artifacts'
cache_path = 'foundry-cache'
libs = ['node_modules']
bytecode_hash = "none"
ffi = false

# default is zero, using a non-zero amount enables us to test e.g. billing based on gas prices.
gas_price = 1
block_timestamp = 1234567890
block_number = 12345

[fmt]
tab_width = 2
multiline_func_header = "params_first"
sort_imports = true
single_line_statement_blocks = "preserve"

[profile.functions]
optimizer_runs = 1_000_000
solc_version = '0.8.19'
evm_version = 'paris'
src = 'src/v0.8/functions'
test = 'src/v0.8/functions/tests'
gas_price = 3_000_000_000         # 3 gwei

#Used in tests with different solidity versions.
[profile.vrf]
optimizer_runs = 1_000
src = 'src/v0.8/vrf'
test = 'src/v0.8/vrf/test'

[profile.vrf-compile]
solc_version = '0.8.6'
optimizer_runs = 1_000_000
src = 'src/v0.8/vrf'

[profile.vrfv2plus-compile]
solc_version = '0.8.19'
optimizer_runs = 1_000_000
src = 'src/v0.8/vrf'

[profile.automation]
optimizer_runs = 10_000
src = 'src/v0.8/automation'
test = 'src/v0.8/automation/test'

[profile.automation-compile-12]
solc_version = '0.8.6'
optimizer_runs = 1_000_000
src = 'src/v0.8/automation'

[profile.automation-compile-21]
solc_version = '0.8.16'
optimizer_runs = 1_000_000
src = 'src/v0.8/automation'

[profile.automation-compile-22]
solc_version = '0.8.19'
optimizer_runs = 1_000_000
src = 'src/v0.8/automation'

[profile.l2ep]
solc_version = '0.8.24'
optimizer_runs = 1_000_000
src = 'src/v0.8/l2ep'
test = 'src/v0.8/l2ep/test'

[profile.llo-feeds]
optimizer_runs = 1_000_000
src = 'src/v0.8/llo-feeds'
test = 'src/v0.8/llo-feeds/test'
solc_version = '0.8.19'

[profile.keystone]
optimizer_runs = 1_000_000
solc_version = '0.8.24'
src = 'src/v0.8/keystone'
test = 'src/v0.8/keystone/test'
evm_version = 'paris'

[profile.operatorforwarder]
optimizer_runs = 1_000_000
solc_version = '0.8.19'
src = 'src/v0.8/operatorforwarder'
test = 'src/v0.8/operatorforwarder/test'

[profile.workflow]
optimizer_runs = 1_000_000
solc_version = '0.8.24'
src = 'src/v0.8/workflow'
test = 'src/v0.8/workflow/test'
via_ir = true                   # reconsider using the --via-ir flag if compilation takes too long
evm_version = 'paris'

[profile.data-feeds]
optimizer_runs = 10_000
src = 'src/v0.8/data-feeds'
test = 'src/v0.8/data-feeds/test'
solc_version = '0.8.26'
evm_version = 'paris'

[profile.shared]
optimizer_runs = 1_000_000
src = 'src/v0.8/shared'
test = 'src/v0.8/shared/test'
solc_version = '0.8.24'
evm_version = 'paris'

# See more config options https://github.com/foundry-rs/foundry/tree/master/config
