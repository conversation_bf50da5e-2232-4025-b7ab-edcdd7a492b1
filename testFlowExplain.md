# CrossChainTest Contract Call Sequence Analysis

## Overview
This document provides a detailed breakdown of the call sequence when executing the `CrossChainTest` contract, specifically focusing on the cross-chain rebase token transfer mechanism using Chainlink CCIP.

## Test Execution Flow

### Phase 1: Test Setup (`setUp()` function)

#### 1.1 Fork Creation & Simulator Setup
```
1. vm.createSelectFork("sepolia-eth") → Creates Sepolia testnet fork
2. vm.createFork("arb-sepolia") → Creates Arbitrum Sepolia testnet fork  
3. new CCIPLocalSimulatorFork() → Deploys CCIP message simulator
4. vm.makePersistent(simulator) → Makes simulator persistent across forks
5. ccipLocalSimulatorFork.getNetworkDetails(chainid) → Gets Sepolia network config
```

#### 1.2 Sepolia Chain Deployment
```
6. vm.startPrank(owner) → Impersonate owner for deployments
7. new RebaseToken() → Deploy rebase token on Sepolia
8. new Vault(rebaseToken) → Deploy vault contract on Sepolia
9. new RebaseTokenPool(token, decimals, allowlist, rmn, router) → Deploy token pool
10. sepoliaToken.grantMintAndBurnRole(pool) → Grant pool mint/burn permissions
11. sepoliaToken.grantMintAndBurnRole(vault) → Grant vault mint/burn permissions
```

#### 1.3 Sepolia CCIP Registration
```
12. RegistryModuleOwnerCustom.registerAdminViaOwner(token) → Register as token admin
13. TokenAdminRegistry.acceptAdminRole(token) → Accept admin role
14. TokenAdminRegistry.setPool(token, pool) → Link token to pool in registry
15. vm.stopPrank() → Stop owner impersonation
```

#### 1.4 Arbitrum Chain Deployment
```
16. vm.selectFork(arbSepoliaFork) → Switch to Arbitrum fork
17. ccipLocalSimulatorFork.getNetworkDetails(chainid) → Get Arbitrum network config
18. vm.startPrank(owner) → Impersonate owner on Arbitrum
19. new RebaseToken() → Deploy rebase token on Arbitrum
20. new RebaseTokenPool(token, decimals, allowlist, rmn, router) → Deploy pool
21. arbSepoliaToken.grantMintAndBurnRole(pool) → Grant pool permissions
```

#### 1.5 Arbitrum CCIP Registration
```
22. RegistryModuleOwnerCustom.registerAdminViaOwner(token) → Register admin
23. TokenAdminRegistry.acceptAdminRole(token) → Accept admin role  
24. TokenAdminRegistry.setPool(token, pool) → Link token to pool
25. vm.stopPrank() → Stop owner impersonation
```

#### 1.6 Cross-Chain Pool Configuration
```
26. configureTokenPool(sepoliaFork, sepoliaPool, arbPool, arbToken, arbDetails)
    26.1. vm.selectFork(sepoliaFork) → Switch to Sepolia
    26.2. vm.startPrank(owner) → Impersonate owner
    26.3. Create remotePoolAddresses[] → Encode Arbitrum pool address
    26.4. Create TokenPool.ChainUpdate[] → Configure chain routing
    26.5. TokenPool.applyChainUpdates() → Apply cross-chain configuration
    26.6. vm.stopPrank() → Stop impersonation

27. configureTokenPool(arbFork, arbPool, sepoliaPool, sepoliaToken, sepoliaDetails)
    27.1. vm.selectFork(arbSepoliaFork) → Switch to Arbitrum
    27.2. vm.startPrank(owner) → Impersonate owner
    27.3. Create remotePoolAddresses[] → Encode Sepolia pool address
    27.4. Create TokenPool.ChainUpdate[] → Configure reverse routing
    27.5. TokenPool.applyChainUpdates() → Apply configuration
    27.6. vm.stopPrank() → Stop impersonation
```

### Phase 2: Test Execution (`testBridgeAllTokens()` function)

#### 2.1 Initial User Setup
```
28. vm.selectFork(sepoliaFork) → Switch to Sepolia for test
29. vm.deal(user, SEND_VALUE) → Give user ETH (100,000 wei)
30. vm.prank(user) → Impersonate user
31. vault.deposit{value: SEND_VALUE}() → User deposits ETH to vault
    31.1. vault.deposit() → Vault receives ETH
    31.2. vault → rebaseToken.mint(user, amount, interestRate)
        31.2.1. rebaseToken._mintAccruedInterest(user) → Calculate/mint interest
        31.2.2. rebaseToken.s_userInterestRate[user] = rate → Set user rate
        31.2.3. rebaseToken._mint(user, amount) → Mint principal tokens
32. assertEq(sepoliaToken.balanceOf(user), SEND_VALUE) → Verify user balance
```

#### 2.2 First Bridge: Sepolia → Arbitrum
```
33. bridgeTokens(SEND_VALUE, sepoliaFork, arbFork, sepoliaDetails, arbDetails, sepoliaToken, arbToken)
    
    33.1. _executeBridgeOnSourceChain():
        33.1.1. vm.selectFork(sepoliaFork) → Ensure on source chain
        33.1.2. Create Client.EVMTokenAmount[] → Package token transfer data
        33.1.3. Create Client.EVM2AnyMessage → Build CCIP message:
            - receiver: abi.encode(user) → Destination user address
            - tokenAmounts: [token, amount] → Token transfer details
            - feeToken: LINK address → Payment token
            - extraArgs: gasLimit: 500,000 → Destination gas limit
        33.1.4. IRouterClient.getFee() → Calculate CCIP fees
        33.1.5. ccipLocalSimulatorFork.requestLinkFromFaucet(user, fee) → Fund user
        33.1.6. vm.prank(user) → Impersonate user
        33.1.7. IERC20(LINK).approve(router, fee) → Approve fee payment
        33.1.8. IERC20(token).approve(router, amount) → Approve token transfer
        33.1.9. IRouterClient.ccipSend(chainSelector, message) → Execute bridge
            
            ********. Router → sepoliaPool.lockOrBurn():
                ********.1. sepoliaPool._validateLockOrBurn() → Validate request
                ********.2. sepoliaToken.getUserInterestRate(user) → Get user rate
                ********.3. sepoliaToken.burn(pool, amount) → Burn user tokens
                ********.4. abi.encodePacked(decimals, uint64(rate)) → Pack data
                ********.5. Return LockOrBurnOutV1{destTokenAddress, destPoolData}
            
        33.1.10. assertEq(balance_after, balance_before - amount) → Verify burn
    
    33.2. _verifyBridgeOnDestinationChain():
        33.2.1. expectedRate = localToken.getInterestRate() → Capture rate
        33.2.2. vm.selectFork(remoteFork) → Switch to Arbitrum
        33.2.3. vm.warp(block.timestamp + 20 minutes) → Simulate delay
        33.2.4. balanceBefore = remoteToken.balanceOf(user) → Check balance
        33.2.5. vm.selectFork(localFork) → Switch back to Sepolia
        33.2.6. ccipLocalSimulatorFork.switchChainAndRouteMessage(remoteFork)
            
            ********. Simulator routes message to Arbitrum
            ********. Router → arbPool.releaseOrMint():
                ********.1. arbPool._validateReleaseOrMint() → Validate
                ********.2. decimals = sourcePoolData[0] → Extract decimals
                ********.3. rate = uint64(sourcePoolData[1:9]) → Extract rate
                ********.4. localAmount = _calculateLocalAmount() → Handle decimals
                ********.5. arbToken.mint(user, localAmount, rate) → Mint tokens
                    ********.5.1. arbToken._mintAccruedInterest(user) → Mint interest
                    ********.5.2. arbToken.s_userInterestRate[user] = rate → Set rate
                    ********.5.3. arbToken._mint(user, amount) → Mint principal
                ********.6. Return ReleaseOrMintOutV1{destinationAmount}
        
        33.2.7. assertEq(remoteBalance, balanceBefore + amount) → Verify mint
        33.2.8. assertEq(remoteToken.getUserInterestRate(user), expectedRate) → Verify rate
```

#### 2.3 Second Bridge: Arbitrum → Sepolia (Round-trip)
```
34. vm.selectFork(arbSepoliaFork) → Switch to Arbitrum
35. vm.warp(block.timestamp + 20 minutes) → Fast-forward time
36. bridgeTokens(arbToken.balanceOf(user), arbFork, sepoliaFork, arbDetails, sepoliaDetails, arbToken, sepoliaToken)
    
    [Similar sequence as 33.1-33.2 but in reverse direction]
    36.1. _executeBridgeOnSourceChain() on Arbitrum
    36.2. _verifyBridgeOnDestinationChain() on Sepolia
```

### Phase 3: Critical Data Flow Points

#### 3.1 Data Encoding (Fixed Issue)
```
Original Problem: abi.encode(uint8, uint256) = 64 bytes > 32 byte limit
Fixed Solution: abi.encodePacked(uint8, uint64) = 9 bytes < 32 byte limit

Encoding Process:
- decimals (uint8): 18 → 0x12 (1 byte)
- interestRate (uint64): 50000000000 → 0x0000000ba43b7400 (8 bytes)  
- Combined: 0x120000000ba43b7400 (9 bytes total)
```

#### 3.2 Data Decoding (Fixed Issue)
```
Decoding Process:
- sourcePoolData = 0x120000000ba43b7400
- decimals = uint8(sourcePoolData[0]) = 18
- rateBytes = sourcePoolData[1:9] = 0x0000000ba43b7400
- rate = uint64(bytes8(rateBytes)) = 50000000000
- finalRate = uint256(rate) = 50000000000
```

#### 3.3 Gas Optimization (Fixed Issue)
```
Original Problem: gasLimit: 0 → OutOfGas in _mintAccruedInterest()
Fixed Solution: gasLimit: 500,000 → Sufficient gas for complex operations

Gas Usage Breakdown:
- Basic mint operation: ~50,000 gas
- _mintAccruedInterest calculation: ~200,000 gas  
- Interest rate storage: ~20,000 gas
- Safety buffer: ~230,000 gas
- Total allocated: 500,000 gas
```

## Key Technical Insights

1. **Fork Management**: Foundry's `vm.selectFork()` enables seamless multi-chain testing
2. **CCIP Integration**: Local simulator provides realistic cross-chain message routing
3. **Data Constraints**: CCIP enforces strict 32-byte limits on cross-chain data
4. **Gas Management**: Complex token operations require careful gas limit planning
5. **State Preservation**: User-specific data (interest rates) must survive chain transfers
6. **Bidirectional Testing**: Round-trip tests validate complete cross-chain functionality

This sequence demonstrates the complexity of cross-chain rebase token transfers and highlights the critical importance of efficient data encoding and proper gas management in CCIP applications.
