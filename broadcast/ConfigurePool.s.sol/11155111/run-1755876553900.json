{"transactions": [{"hash": "0xaa7c0c023e093757bec7e42c9942462997f23a7836131f0b0cd75bcb57c2bab5", "transactionType": "CALL", "contractName": null, "contractAddress": "0x344670b152e0e92a5e5bebd890601878b04d01c1", "function": "applyChainUpdates(uint64[],(uint64,bytes[],bytes,(bool,uint128,uint128),(bool,uint128,uint128))[])", "arguments": ["[]", "[(6898391096552792247, [0x000000000000000000000000c9a69727f62338d9642acc0752e34ecdd2066306], 0x0000000000000000000000001f71dcc4db9679b81c677366e24dc0a6b520da32, (false, 0, 0), (false, 0, 0))]"], "transaction": {"from": "0xdd09614b6e7648f66dd3e141343e68dd2f69e0d3", "to": "0x344670b152e0e92a5e5bebd890601878b04d01c1", "gas": "0x6a3e8", "value": "0x0", "input": "0xe8a1da17000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000600000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000005fbc02272fbf20b7000000000000000000000000000000000000000000000000000000000000012000000000000000000000000000000000000000000000000000000000000001a0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000020000000000000000000000000c9a69727f62338d9642acc0752e34ecdd206630600000000000000000000000000000000000000000000000000000000000000200000000000000000000000001f71dcc4db9679b81c677366e24dc0a6b520da32", "nonce": "0x3f", "chainId": "0xaa36a7"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0xa50b0e", "logs": [{"address": "0x344670b152e0e92a5e5bebd890601878b04d01c1", "topics": ["0x7d628c9a1796743d365ab521a8b2a4686e419b3269919dc9145ea2ce853b54ea", "0x0000000000000000000000000000000000000000000000005fbc02272fbf20b7"], "data": "0x00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000020000000000000000000000000c9a69727f62338d9642acc0752e34ecdd2066306", "blockHash": "0xd8559c6e2cd21c9cc2ac3d48936b48448db0599c4f17f8d7cbeee7e0a2d8dadd", "blockNumber": "0x89f294", "blockTimestamp": "0x68a88cc8", "transactionHash": "0xaa7c0c023e093757bec7e42c9942462997f23a7836131f0b0cd75bcb57c2bab5", "transactionIndex": "0x6a", "logIndex": "0x94", "removed": false}, {"address": "0x344670b152e0e92a5e5bebd890601878b04d01c1", "topics": ["0x8d340f17e19058004c20453540862a9c62778504476f6756755cb33bcd6c38c2"], "data": "0x0000000000000000000000000000000000000000000000005fbc02272fbf20b7000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000200000000000000000000000001f71dcc4db9679b81c677366e24dc0a6b520da32", "blockHash": "0xd8559c6e2cd21c9cc2ac3d48936b48448db0599c4f17f8d7cbeee7e0a2d8dadd", "blockNumber": "0x89f294", "blockTimestamp": "0x68a88cc8", "transactionHash": "0xaa7c0c023e093757bec7e42c9942462997f23a7836131f0b0cd75bcb57c2bab5", "transactionIndex": "0x6a", "logIndex": "0x95", "removed": false}], "logsBloom": "0x00000000000000400000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000004000000000004000000000000000000000000040000000000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000400000000000000000000400000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000008000000100000000000000", "type": "0x2", "transactionHash": "0xaa7c0c023e093757bec7e42c9942462997f23a7836131f0b0cd75bcb57c2bab5", "transactionIndex": "0x6a", "blockHash": "0xd8559c6e2cd21c9cc2ac3d48936b48448db0599c4f17f8d7cbeee7e0a2d8dadd", "blockNumber": "0x89f294", "gasUsed": "0x4ceb4", "effectiveGasPrice": "0x2876b4d9", "from": "0xdd09614b6e7648f66dd3e141343e68dd2f69e0d3", "to": "0x344670b152e0e92a5e5bebd890601878b04d01c1", "contractAddress": null}], "libraries": [], "pending": [], "returns": {}, "timestamp": 1755876553900, "chain": 11155111, "commit": "9c0b48d"}