{"transactions": [{"hash": "0x3cf6704b76d4c138e987ec2555fd8b27ef08d882e61d7c1ccd9e499c4812ed92", "transactionType": "CREATE", "contractName": "<PERSON><PERSON>", "contractAddress": "0xc040fc46aa4bac16d455c2c64ecc37edaed2beeb", "function": null, "arguments": ["0x8E84886766991BeC42B803F537928FF1b6174c73"], "transaction": {"from": "0xdd09614b6e7648f66dd3e141343e68dd2f69e0d3", "gas": "0x89653", "value": "0x0", "input": "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", "nonce": "0x6f", "chainId": "0xaa36a7"}, "additionalContracts": [], "isFixedGasLimit": false}, {"hash": "0x0b0375b9b5b7b0bd9828479ad80e4c7934d32e4f45b53a15711b4e6e9680de16", "transactionType": "CALL", "contractName": null, "contractAddress": "0x8e84886766991bec42b803f537928ff1b6174c73", "function": "grantMintAndBurnRole(address)", "arguments": ["0xC040Fc46aA4bAC16d455C2C64EcC37eDaED2BEeB"], "transaction": {"from": "0xdd09614b6e7648f66dd3e141343e68dd2f69e0d3", "to": "0x8e84886766991bec42b803f537928ff1b6174c73", "gas": "0x107e2", "value": "0x0", "input": "0x7cfb3d71000000000000000000000000c040fc46aa4bac16d455c2c64ecc37edaed2beeb", "nonce": "0x70", "chainId": "0xaa36a7"}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [{"status": "0x1", "cumulativeGasUsed": "0x2ec25b0", "logs": [], "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "type": "0x2", "transactionHash": "0x3cf6704b76d4c138e987ec2555fd8b27ef08d882e61d7c1ccd9e499c4812ed92", "transactionIndex": "0x56", "blockHash": "0x539da8fedaa98488cd3b1d3e314b2fc1eedbf831eb02993778c4ed4b803cae8d", "blockNumber": "0x8b932a", "gasUsed": "0x69b05", "effectiveGasPrice": "0x293aa5", "from": "0xdd09614b6e7648f66dd3e141343e68dd2f69e0d3", "to": null, "contractAddress": "0xc040fc46aa4bac16d455c2c64ecc37edaed2beeb"}, {"status": "0x1", "cumulativeGasUsed": "0x2ece4bd", "logs": [{"address": "0x8e84886766991bec42b803f537928ff1b6174c73", "topics": ["0x2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d", "0x7429555b03aa5cf08cc5fc0890c97db295c9cec5e6bb1544bcd7fd1430dae3e2", "0x000000000000000000000000c040fc46aa4bac16d455c2c64ecc37edaed2beeb", "0x000000000000000000000000dd09614b6e7648f66dd3e141343e68dd2f69e0d3"], "data": "0x", "blockHash": "0x539da8fedaa98488cd3b1d3e314b2fc1eedbf831eb02993778c4ed4b803cae8d", "blockNumber": "0x8b932a", "blockTimestamp": "0x68bc6734", "transactionHash": "0x0b0375b9b5b7b0bd9828479ad80e4c7934d32e4f45b53a15711b4e6e9680de16", "transactionIndex": "0x57", "logIndex": "0x23b5", "removed": false}], "logsBloom": "0x00000004000000000000000000000000000000000800000400000000000000020000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000004000000000000000000004001000000000000000000000000000040000000000020000000000000000000100000000000000400000000000000004000000000000000000000000000000000000000000000000", "type": "0x2", "transactionHash": "0x0b0375b9b5b7b0bd9828479ad80e4c7934d32e4f45b53a15711b4e6e9680de16", "transactionIndex": "0x57", "blockHash": "0x539da8fedaa98488cd3b1d3e314b2fc1eedbf831eb02993778c4ed4b803cae8d", "blockNumber": "0x8b932a", "gasUsed": "0xbf0d", "effectiveGasPrice": "0x293aa5", "from": "0xdd09614b6e7648f66dd3e141343e68dd2f69e0d3", "to": "0x8e84886766991bec42b803f537928ff1b6174c73", "contractAddress": null}], "libraries": [], "pending": [], "returns": {"vault": {"internal_type": "contract Vault", "value": "0xC040Fc46aA4bAC16d455C2C64EcC37eDaED2BEeB"}}, "timestamp": 1757177652784, "chain": 11155111, "commit": "9c0b48d"}