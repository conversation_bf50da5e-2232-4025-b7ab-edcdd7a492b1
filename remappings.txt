@openzeppelin/=lib/openzeppelin-contracts/
@chainlink/contracts/=lib/chainlink-evm/contracts/
@chainlink/contracts-ccip/contracts/=lib/chainlink-ccip/chains/evm/contracts/
@openzeppelin-solidity/=lib/chainlink-evm/contracts/src/v0.8/vendor/openzeppelin-solidity/
@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/
@chainlink-local/=lib/chainlink-local/
ccip/=lib/ccip/
chainlink-ccip/=lib/chainlink-ccip/
chainlink-evm/=lib/chainlink-evm/
ds-test/=lib/openzeppelin-contracts/lib/forge-std/lib/ds-test/src/
erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/
forge-std/=lib/forge-std/src/
halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/
openzeppelin-contracts/=lib/openzeppelin-contracts/
