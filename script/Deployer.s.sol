// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import {<PERSON>ript} from "forge-std/Script.sol";

import {CCIPLocalSimulatorFork, Register} from "@chainlink-local/src/ccip/CCIPLocalSimulatorFork.sol";
import {IERC20} from
    "@chainlink/contracts/src/v0.8/vendor/openzeppelin-solidity/v4.8.3/contracts/token/ERC20/IERC20.sol";
import {RegistryModuleOwnerCustom} from
    "lib/ccip/contracts/src/v0.8/ccip/tokenAdminRegistry/RegistryModuleOwnerCustom.sol";
import {TokenAdminRegistry} from "lib/ccip/contracts/src/v0.8/ccip/tokenAdminRegistry/TokenAdminRegistry.sol";

import {Vault} from "../src/Vault.sol";
import {RebaseToken} from "../src/RebaseToken.sol";
import {IRebaseToken} from "../src/Interfaces/IRebaseToken.sol";
import {RebaseTokenPool} from "../src/RebaseTokenPool.sol";
// Doc

contract TokenAndPoolDeployer is Script {
    function run() public returns (RebaseToken token, RebaseTokenPool pool) {
        CCIPLocalSimulatorFork ccipLocalSimulatorFork = new CCIPLocalSimulatorFork();
        // Register: Part of the local simulator, used by CCIPLocalSimulatorFork to provide network details.
        // The Register.NetworkDetails struct holds crucial addresses for CCIP components.
        Register.NetworkDetails memory networkDetails = ccipLocalSimulatorFork.getNetworkDetails(block.chainid);
        vm.startBroadcast();
        // Deploy Token and Pool: // doc
        token = new RebaseToken();
        pool = new RebaseTokenPool(
            IERC20(address(token)), 18, new address[](0), networkDetails.rmnProxyAddress, networkDetails.routerAddress
        );
        vm.stopBroadcast();


        // // Grant pool mint and burn permissions to token:
        // token.grantMintAndBurnRole(address(pool));

        // //  Perform CCIP Configuration Steps // doc

        // // We inform the CCIP RegistryModuleOwnerCustom contract that the deployer of this script (the owner of
        // // the token) will be the administrator for this RebaseToken.
        // RegistryModuleOwnerCustom(networkDetails.registryModuleOwnerCustomAddress).registerAdminViaOwner(address(token));
        // // The designated admin (our deployer account) must then formally accept this administrative role for the
        // // token within the TokenAdminRegistry
        // TokenAdminRegistry(networkDetails.tokenAdminRegistryAddress).acceptAdminRole(address(token));
        // // Finally, we link our RebaseToken to its dedicated RebaseTokenPool in the TokenAdminRegistry.
        // // This tells CCIP which pool contract is responsible for managing our specific token.
        // TokenAdminRegistry(networkDetails.tokenAdminRegistryAddress).setPool(address(token), address(pool));
        // vm.stopBroadcast();
    }
}

contract SetPermissions is Script {
    function grantRole(address token, address pool) public {
        vm.startBroadcast();
        IRebaseToken(token).grantMintAndBurnRole(address(pool));
        vm.stopBroadcast();

    }

    function setAdmin(address token, address pool) public {
        CCIPLocalSimulatorFork ccipLocalSimulatorFork = new CCIPLocalSimulatorFork();
        // Register: Part of the local simulator, used by CCIPLocalSimulatorFork to provide network details.
        // The Register.NetworkDetails struct holds crucial addresses for CCIP components.
        Register.NetworkDetails memory networkDetails = ccipLocalSimulatorFork.getNetworkDetails(block.chainid);
        vm.startBroadcast();

        //  Perform CCIP Configuration Steps // doc

        // We inform the CCIP RegistryModuleOwnerCustom contract that the deployer of this script (the owner of
        // the token) will be the administrator for this RebaseToken.
        // Note: This call must be made by the token owner (msg.sender must be token owner)
        RegistryModuleOwnerCustom(networkDetails.registryModuleOwnerCustomAddress).registerAdminViaOwner(address(token));
        // The designated admin (our deployer account) must then formally accept this administrative role for the
        // token within the TokenAdminRegistry
        TokenAdminRegistry(networkDetails.tokenAdminRegistryAddress).acceptAdminRole(address(token));
        // Finally, we link our RebaseToken to its dedicated RebaseTokenPool in the TokenAdminRegistry.
        // This tells CCIP which pool contract is responsible for managing our specific token.
        TokenAdminRegistry(networkDetails.tokenAdminRegistryAddress).setPool(address(token), address(pool));
        vm.stopBroadcast();

    }
}
    
contract VaultDeployer is Script {
    // The run() function is the main entry point for a Foundry script. Foundry executes this function when you invoke the script.
    function run(address _rebaseToken) public returns (Vault vault) {
        vm.startBroadcast(); // doc
        vault = new Vault(IRebaseToken(_rebaseToken)); // 'vault' is now assigned to the return variable
        // Vault requires the ability to mint and burn tokens
        IRebaseToken(_rebaseToken).grantMintAndBurnRole(address(vault));
        vm.stopBroadcast();
    }
}




