Rationale for Separation:
This separation is strategic due to the differing deployment needs of the contracts:

    The Vault contract is intended for deployment only on the source chain. This is because core functionalities like 
    deposits and redemptions are restricted to the source chain environment.

    The RebaseToken and RebaseTokenPool contracts, on the other hand, need to be deployed on both the source and 
    destination chains to facilitate cross-chain operations.

By splitting the deployment logic, we can execute each deployer script selectively on the appropriate chains.
===================================================================================================================


Using Foundry Cheatcodes for Deployment:
Foundry provides "cheatcodes" (accessible via the vm instance inherited from Script) to interact with the blockchain 
environment during script execution.

    vm.startBroadcast();: This crucial cheatcode tells Foundry to start broadcasting all subsequent state-changing 
                          calls (like contract deployments or function calls that modify state) as actual transactions 
                          to the specified network.

    vm.stopBroadcast();: This signals Foundry to stop broadcasting transactions. All deployments and state changes 
                         should occur between these two calls.
=====================================================================================================================

Tip for Lingering Linter Errors: Sometimes, Solidity linters in IDEs might not immediately pick up changes in imported files. 
                                 If an error persists after you've correctly updated an interface, try cutting and pasting the 
                                 problematic line of code back into your script; this can often force the linter to re-evaluate.
=====================================================================================================================

Returning the Deployed Vault Instance:
To make the address (and instance) of the deployed Vault contract easily accessible to other scripts or for verification 
after the script runs, modify the run function to return the Vault instance.

    - Foundry scripts offer a convenient feature: they can implicitly return the last assigned variable if the returns 
      clause of the function matches its type and name.

    - Update the run function signature to declare Vault vault as a return variable.

    - Remove the Vault type declaration for the vault variable inside the function body, as it's now declared in 
      the returns clause and will be assigned directly.
==============================================================================================================================
Summary of the VaultDeployer Script

At this stage, your VaultDeployer script is configured to perform the following actions:

    1. Accept the address of a pre-deployed RebaseToken contract as an input parameter.

    2. Deploy a new instance of the Vault contract, correctly initializing it by passing the RebaseToken address to its constructor.

    3. Grant the newly deployed Vault contract the necessary MintAndBurnRole on the provided RebaseToken contract.

    4. Return the instance of the successfully deployed Vault contract, making its address and methods available for 
       subsequent operations or verification.






