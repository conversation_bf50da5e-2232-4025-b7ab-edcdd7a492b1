{"security.olympix.detectors.arbitraryAddressSpoofingAttack": true, "security.olympix.detectors.arbitraryTransferFrom": true, "security.olympix.detectors.noAccessControlPayableFallback": true, "security.olympix.detectors.ownerSinglePointOfFailure": true, "security.olympix.detectors.anyTxOrigin": true, "security.olympix.detectors.faultyDiv": true, "security.olympix.detectors.enumConversionOutOfRange": true, "security.olympix.detectors.uintToIntConversion": true, "security.olympix.detectors.downcastOfNumberToAddress": true, "security.olympix.detectors.unsafeDowncast": true, "security.olympix.detectors.swappedShiftParams": true, "security.olympix.detectors.unaryPlusExpression": true, "security.olympix.detectors.uncheckedBlockWithSubtraction": true, "security.olympix.detectors.assemblyReturnInsteadOfLeave": true, "security.olympix.detectors.callsAssemblyReturn": true, "security.olympix.detectors.delegateCallInLoop": true, "security.olympix.detectors.emptyPayableFallback": true, "security.olympix.detectors.lowLevelCallParamsVerified": true, "security.olympix.detectors.improperDiamondPattern": true, "security.olympix.detectors.missingGapVariable": true, "security.olympix.detectors.unenforcedStateMaintenanceKeywords": true, "security.olympix.detectors.unboundedPragma": true, "security.olympix.detectors.directionalOverrideCharacter": true, "security.olympix.detectors.abiEncodePackedDynamicTypes": true, "security.olympix.detectors.callsInLoop": true, "security.olympix.detectors.callWithoutGasBudget": true, "security.olympix.detectors.msgValueReuse": true, "security.olympix.detectors.abiEncoderArray": true, "security.olympix.detectors.blockRandomness": true}